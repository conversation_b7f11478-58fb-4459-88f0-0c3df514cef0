#!/bin/bash
# Go-Kuaidi 后端构建脚本
# 版本: 2.1 (后端专用版本)
# 作者: Go-Kuaidi Team
# 使用方法: ./build.sh
# 说明: 此脚本仅构建Go后端服务，不包含前端构建

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
BUILD_TIME=$(date +"%Y-%m-%d %H:%M:%S")
BUILD_VERSION=$(date +"%Y%m%d_%H%M%S")
PACKAGE_NAME="go-kuaidi-production-${BUILD_VERSION}"

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# 日志函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🚀 $1${NC}"; }

# 错误处理
trap 'log_error "构建过程中发生错误，退出码: $?"' ERR

# 显示构建信息
show_build_info() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                  Go-Kuaidi 后端构建工具                      ║"
    echo "║                    企业级生产环境构建                         ║"
    echo "╠══════════════════════════════════════════════════════════════╣"
    echo "║ 构建时间: ${BUILD_TIME}                           ║"
    echo "║ 构建版本: ${BUILD_VERSION}                        ║"
    echo "║ 项目路径: ${PROJECT_ROOT}                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 环境检查
check_environment() {
    log_step "检查构建环境"
    
    # 检查必要工具
    local tools=("go" "git" "tar" "md5sum")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool 未安装或不在PATH中"
            exit 1
        fi
        log_info "$tool: $(command -v "$tool")"
    done
    
    # 检查Go版本
    local go_version=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go版本: $go_version"
    if [[ $(echo "$go_version" | cut -d. -f2) -lt 21 ]]; then
        log_warning "建议使用Go 1.21+版本"
    fi
    
    # 检查磁盘空间
    local available_space=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 1048576 ]]; then # 1GB
        log_warning "可用磁盘空间不足1GB，可能影响构建"
    fi
    
    log_success "环境检查完成"
}

# 清理构建目录
clean_build() {
    log_step "清理构建目录"
    
    cd "$PROJECT_ROOT"
    
    # 清理Go构建产物
    rm -f kuaidi-server-prod go-kuaidi
    rm -rf bin/kuaidi-server-prod
    
    # 清理旧的部署包
    rm -f go-kuaidi-production-*.tar.gz
    rm -f go-kuaidi-production-*.info
    rm -rf deploy_temp_*
    
    log_success "构建目录清理完成"
}

# 检查并下载Go依赖
prepare_dependencies() {
    log_step "准备Go依赖"
    
    cd "$PROJECT_ROOT"
    
    # 下载依赖
    log_info "下载Go模块依赖..."
    go mod download
    
    # 验证依赖
    log_info "验证依赖完整性..."
    go mod verify
    
    log_success "Go依赖准备完成"
}



# 构建Go后端
build_backend() {
    log_step "构建Go后端服务"

    cd "$PROJECT_ROOT"

    # 检查是否为本地测试构建
    local target_os="linux"
    local target_arch="amd64"
    local binary_name="kuaidi-server-prod"

    if [[ "${1:-}" == "local" ]]; then
        target_os=$(uname -s | tr '[:upper:]' '[:lower:]')
        if [[ "$target_os" == "darwin" ]]; then
            target_arch="amd64"
            if [[ "$(uname -m)" == "arm64" ]]; then
                target_arch="arm64"
            fi
        fi
        binary_name="kuaidi-server-local"
        log_info "本地测试构建模式: ${target_os}/${target_arch}"
    fi

    # 设置构建环境变量
    export CGO_ENABLED=0
    export GOOS="$target_os"
    export GOARCH="$target_arch"
    export ENVIRONMENT=production
    
    # 获取Git信息
    local git_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    local git_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    
    # 构建标志
    local ldflags="-w -s"

    log_info "开始编译Go二进制文件..."
    log_info "目标平台: ${GOOS}/${GOARCH}"
    log_info "Git提交: ${git_commit}"
    log_info "Git分支: ${git_branch}"

    # 构建
    if go build -ldflags="$ldflags" -o kuaidi-server-prod cmd/main.go; then
        local binary_size=$(du -h kuaidi-server-prod | cut -f1)
        log_success "Go后端构建成功 (大小: ${binary_size})"
        
        # 验证二进制文件
        if file kuaidi-server-prod | grep -q "ELF.*executable"; then
            log_success "二进制文件验证通过"
        else
            log_error "二进制文件格式异常"
            exit 1
        fi
        
        # 设置执行权限
        chmod +x kuaidi-server-prod
    else
        log_error "Go后端构建失败"
        exit 1
    fi
}



# 创建启动脚本
create_startup_scripts() {
    local target_dir="$1"
    
    log_info "创建启动脚本..."
    
    # 主启动脚本
    cat > "$target_dir/start.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 生产环境启动脚本

set -e

APP_NAME="kuaidi-server-prod"
PID_FILE="app.pid"
LOG_FILE="app.log"

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查是否已运行
if [[ -f "$PID_FILE" ]]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        log_error "应用已在运行 (PID: $PID)"
        exit 1
    else
        log_warn "发现僵尸PID文件，正在清理..."
        rm -f "$PID_FILE"
    fi
fi

# 设置生产环境
export ENVIRONMENT=production
export GIN_MODE=release

# 检查二进制文件
if [[ ! -f "$APP_NAME" ]]; then
    log_error "应用文件不存在: $APP_NAME"
    exit 1
fi

if [[ ! -x "$APP_NAME" ]]; then
    log_error "应用文件不可执行: $APP_NAME"
    exit 1
fi

# 🚀 创建企业级日志目录结构
log_info "创建日志目录..."
mkdir -p logs
touch logs/go-kuaidi.log
touch logs/error.log
touch logs/access.log
touch logs/audit.log
log_info "✅ 日志目录结构创建完成"

# 检查配置文件
if [[ -f "config/config.yaml" ]]; then
    log_info "✅ 配置文件存在"
else
    log_warn "⚠️  配置文件不存在，应用可能使用默认配置"
fi

# 检查端口是否被占用
if command -v netstat &> /dev/null; then
    if netstat -tuln | grep -q ":8081 "; then
        log_error "端口8081已被占用，请检查是否有其他实例在运行"
        exit 1
    fi
elif command -v ss &> /dev/null; then
    if ss -tuln | grep -q ":8081 "; then
        log_error "端口8081已被占用，请检查是否有其他实例在运行"
        exit 1
    fi
fi

# 启动应用
log_info "启动 Go-Kuaidi 服务..."
log_info "命令: ./$APP_NAME"
log_info "日志文件: $LOG_FILE"

# 启动应用并捕获输出
nohup "./$APP_NAME" > "$LOG_FILE" 2>&1 &
PID=$!

# 保存PID
echo "$PID" > "$PID_FILE"
log_info "进程PID: $PID"

# 等待启动并检查
log_info "等待应用启动..."
for i in {1..10}; do
    sleep 1
    if ! kill -0 "$PID" 2>/dev/null; then
        log_error "❌ 应用进程已退出"
        break
    fi

    # 检查日志文件是否有内容
    if [[ -f "$LOG_FILE" && -s "$LOG_FILE" ]]; then
        # 检查是否有启动成功的标志
        if grep -q "服务启动成功\|Server started\|listening on\|started successfully" "$LOG_FILE" 2>/dev/null; then
            log_info "✅ 应用启动成功 (PID: $PID)"
            log_info "📋 日志文件: $LOG_FILE"
            log_info "🔍 查看日志: tail -f $LOG_FILE"
            log_info "🛑 停止服务: ./stop.sh"
            log_info "🌐 服务地址: http://localhost:8081"
            exit 0
        fi

        # 检查是否有错误信息
        if grep -q "error\|Error\|ERROR\|failed\|Failed\|FAILED\|panic\|fatal" "$LOG_FILE" 2>/dev/null; then
            log_error "❌ 应用启动时发现错误"
            break
        fi
    fi

    echo -n "."
done

echo ""

# 如果到这里说明启动可能失败了
if kill -0 "$PID" 2>/dev/null; then
    log_warn "⚠️  应用进程仍在运行，但可能未完全启动"
    log_info "进程PID: $PID"
else
    log_error "❌ 应用启动失败，进程已退出"
    rm -f "$PID_FILE"
fi

# 显示启动日志的最后几行
if [[ -f "$LOG_FILE" && -s "$LOG_FILE" ]]; then
    log_error "📋 启动日志 (最后10行):"
    echo "----------------------------------------"
    tail -10 "$LOG_FILE"
    echo "----------------------------------------"
    log_info "🔍 查看完整日志: cat $LOG_FILE"
else
    log_error "📋 启动日志文件为空或不存在"
fi

# 提供诊断建议
log_error "🔧 诊断建议:"
echo "1. 检查配置文件: cat config/config.yaml"
echo "2. 检查数据库连接: ping 8.138.252.193"
echo "3. 检查端口占用: netstat -tuln | grep 8081"
echo "4. 手动运行测试: ./$APP_NAME"
echo "5. 查看详细日志: tail -f $LOG_FILE"

exit 1
EOF

    # 停止脚本
    cat > "$target_dir/stop.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 停止脚本

PID_FILE="app.pid"
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

if [[ ! -f "$PID_FILE" ]]; then
    log_error "PID文件不存在，应用可能未运行"
    exit 1
fi

PID=$(cat "$PID_FILE")

if ! kill -0 "$PID" 2>/dev/null; then
    log_error "进程不存在 (PID: $PID)"
    rm -f "$PID_FILE"
    exit 1
fi

log_info "正在停止应用 (PID: $PID)..."

# 优雅停止
kill -TERM "$PID"

# 等待进程结束
for i in {1..10}; do
    if ! kill -0 "$PID" 2>/dev/null; then
        break
    fi
    sleep 1
done

# 强制停止
if kill -0 "$PID" 2>/dev/null; then
    log_info "强制停止进程..."
    kill -KILL "$PID"
    sleep 2
fi

# 清理PID文件
rm -f "$PID_FILE"

log_info "✅ 应用已停止"
EOF

    # 🚀 日志管理脚本
    cat > "$target_dir/logs.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 日志管理脚本

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

show_help() {
    echo -e "${BLUE}Go-Kuaidi 日志管理工具${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  app     查看应用日志 (logs/go-kuaidi.log)"
    echo "  error   查看错误日志 (logs/error.log)"
    echo "  access  查看访问日志 (logs/access.log)"
    echo "  audit   查看审计日志 (logs/audit.log)"
    echo "  start   查看启动日志 (app.log)"
    echo "  all     查看所有日志文件状态"
    echo "  clean   清理旧日志文件"
    echo "  rotate  手动轮转日志文件"
    echo "  size    检查日志文件大小"
    echo "  config  查看配置文件信息"
    echo "  help    显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 app          # 实时查看应用日志"
    echo "  $0 error        # 实时查看错误日志"
    echo "  $0 size         # 检查日志文件大小"
    echo "  $0 clean        # 清理旧日志文件"
    echo "  $0 config       # 查看配置文件信息"
}

case "${1:-help}" in
    "app")
        echo -e "${GREEN}📋 实时查看应用日志...${NC}"
        echo -e "${YELLOW}按 Ctrl+C 退出${NC}"
        tail -f logs/go-kuaidi.log 2>/dev/null || echo "应用日志文件不存在"
        ;;
    "error")
        echo -e "${GREEN}❌ 实时查看错误日志...${NC}"
        echo -e "${YELLOW}按 Ctrl+C 退出${NC}"
        tail -f logs/error.log 2>/dev/null || echo "错误日志文件不存在"
        ;;
    "access")
        echo -e "${GREEN}🌐 实时查看访问日志...${NC}"
        echo -e "${YELLOW}按 Ctrl+C 退出${NC}"
        tail -f logs/access.log 2>/dev/null || echo "访问日志文件不存在"
        ;;
    "audit")
        echo -e "${GREEN}🔍 实时查看审计日志...${NC}"
        echo -e "${YELLOW}按 Ctrl+C 退出${NC}"
        tail -f logs/audit.log 2>/dev/null || echo "审计日志文件不存在"
        ;;
    "start")
        echo -e "${GREEN}🚀 实时查看启动日志...${NC}"
        echo -e "${YELLOW}按 Ctrl+C 退出${NC}"
        tail -f app.log 2>/dev/null || echo "启动日志文件不存在"
        ;;
    "all")
        echo -e "${GREEN}📁 所有日志文件状态:${NC}"
        echo ""
        if [[ -d "logs" ]]; then
            echo -e "${BLUE}企业级日志目录:${NC}"
            ls -lah logs/ 2>/dev/null || echo "日志目录为空"
        else
            echo "日志目录不存在"
        fi
        echo ""
        if [[ -f "app.log" ]]; then
            echo -e "${BLUE}启动日志:${NC}"
            ls -lah app.log
        else
            echo "启动日志文件不存在"
        fi
        ;;
    "clean")
        echo -e "${YELLOW}🧹 清理旧日志文件...${NC}"
        # 清理7天前的日志文件
        find logs/ -name "*.log.*" -mtime +7 -delete 2>/dev/null || true
        # 清理大于100MB的当前日志文件（轮转）
        find logs/ -name "*.log" -size +100M -exec echo "发现大文件: {}" \;
        echo -e "${GREEN}✅ 日志清理完成${NC}"
        ;;
    "rotate")
        echo -e "${YELLOW}🔄 手动轮转日志文件...${NC}"
        if [[ -f "logs/go-kuaidi.log" ]]; then
            mv "logs/go-kuaidi.log" "logs/go-kuaidi.log.$(date +%Y%m%d_%H%M%S)"
            touch "logs/go-kuaidi.log"
            echo -e "${GREEN}✅ 应用日志已轮转${NC}"
        fi
        if [[ -f "logs/error.log" ]]; then
            mv "logs/error.log" "logs/error.log.$(date +%Y%m%d_%H%M%S)"
            touch "logs/error.log"
            echo -e "${GREEN}✅ 错误日志已轮转${NC}"
        fi
        # 发送USR1信号重新打开日志文件（如果应用支持）
        if [[ -f "app.pid" ]]; then
            PID=$(cat app.pid)
            if kill -0 "$PID" 2>/dev/null; then
                kill -USR1 "$PID" 2>/dev/null || true
                echo -e "${GREEN}✅ 已通知应用重新打开日志文件${NC}"
            fi
        fi
        ;;
    "size")
        echo -e "${GREEN}📏 检查日志文件大小...${NC}"
        if [[ -d "logs" ]]; then
            echo -e "${BLUE}日志文件大小统计:${NC}"
            du -h logs/*.log 2>/dev/null | sort -hr || echo "没有日志文件"
            echo ""
            echo -e "${BLUE}总日志目录大小:${NC}"
            du -sh logs/ 2>/dev/null || echo "日志目录不存在"
            echo ""
            # 检查是否有超大日志文件
            large_files=$(find logs/ -name "*.log" -size +50M 2>/dev/null)
            if [[ -n "$large_files" ]]; then
                echo -e "${RED}⚠️  发现大于50MB的日志文件:${NC}"
                echo "$large_files" | while read file; do
                    size=$(du -h "$file" | cut -f1)
                    echo -e "${YELLOW}  $file ($size)${NC}"
                done
                echo -e "${YELLOW}建议运行: $0 rotate${NC}"
            else
                echo -e "${GREEN}✅ 所有日志文件大小正常${NC}"
            fi
        else
            echo "日志目录不存在"
        fi
        ;;
    "config")
        echo -e "${GREEN}🔧 查看配置文件信息...${NC}"
        if [[ -f "config/config.yaml" ]]; then
            echo -e "${BLUE}数据库连接配置:${NC}"
            grep -n "connection_string.*postgresql" config/config.yaml 2>/dev/null || echo "未找到PostgreSQL配置"
            echo ""
            echo -e "${BLUE}Redis连接配置:${NC}"
            grep -n "connection_string.*redis" config/config.yaml 2>/dev/null || echo "未找到Redis配置"
        else
            echo "配置文件不存在"
        fi
        ;;
    "help"|*)
        show_help
        ;;
esac
EOF

    # 重启脚本
    cat > "$target_dir/restart.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 重启脚本

echo "🔄 重启 Go-Kuaidi 服务..."
./stop.sh
sleep 2
./start.sh
EOF

    # 🚀 生产环境优化脚本
    cat > "$target_dir/optimize.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 生产环境优化脚本

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo -e "${BLUE}🚀 Go-Kuaidi 生产环境优化检查${NC}"
echo "================================"

# 1. 检查系统资源
echo ""
echo "💻 系统资源检查..."
if command -v free &> /dev/null; then
    memory_info=$(free -h | grep "Mem:")
    log_info "内存信息: $memory_info"
else
    log_warn "无法获取内存信息"
fi

# 2. 检查磁盘空间
echo ""
echo "💾 磁盘空间检查..."
disk_usage=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
if [[ $disk_usage -gt 80 ]]; then
    log_warn "磁盘使用率过高: ${disk_usage}%"
else
    log_info "磁盘使用率正常: ${disk_usage}%"
fi

# 3. 检查日志文件大小
echo ""
echo "📋 日志文件检查..."
if [[ -d "logs" ]]; then
    total_log_size=$(du -sm logs/ 2>/dev/null | cut -f1)
    if [[ $total_log_size -gt 1000 ]]; then
        log_warn "日志目录过大: ${total_log_size}MB，建议清理"
        echo "  运行: ./logs.sh clean"
    else
        log_info "日志目录大小正常: ${total_log_size}MB"
    fi
else
    log_warn "日志目录不存在"
fi

# 4. 检查进程状态
echo ""
echo "🔍 进程状态检查..."
if [[ -f "app.pid" ]]; then
    PID=$(cat app.pid)
    if kill -0 "$PID" 2>/dev/null; then
        log_info "应用进程运行正常 (PID: $PID)"

        # 检查内存使用
        if command -v ps &> /dev/null; then
            mem_usage=$(ps -p "$PID" -o rss= 2>/dev/null | awk '{print int($1/1024)}')
            if [[ $mem_usage -gt 1000 ]]; then
                log_warn "应用内存使用过高: ${mem_usage}MB"
            else
                log_info "应用内存使用正常: ${mem_usage}MB"
            fi
        fi
    else
        log_error "应用进程未运行"
    fi
else
    log_error "PID文件不存在，应用可能未启动"
fi

# 5. 生产环境建议
echo ""
echo "📝 生产环境优化建议:"
echo "================================"
echo "1. 定期清理日志: ./logs.sh clean"
echo "2. 监控日志大小: ./logs.sh size"
echo "3. 检查应用状态: ./status.sh"
echo "4. 设置定时任务清理日志:"
echo "   0 2 * * * cd $(pwd) && ./logs.sh clean"
echo "5. 监控系统资源使用情况"
echo "6. 配置日志轮转: logrotate"
echo "7. 设置监控告警"

# 6. 性能优化建议
echo ""
echo "⚡ 性能优化建议:"
echo "================================"
echo "1. 数据库连接池已优化 (80个连接)"
echo "2. 日志采样已启用 (降低I/O压力)"
echo "3. 建议监控API响应时间"
echo "4. 建议设置数据库慢查询监控"
echo "5. 考虑使用Redis缓存热点数据"

echo ""
echo -e "${GREEN}✅ 优化检查完成！${NC}"
EOF

    # 🔧 调试脚本
    cat > "$target_dir/debug.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 调试脚本

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

APP_NAME="kuaidi-server-prod"

echo -e "${BLUE}🔧 Go-Kuaidi 启动问题诊断${NC}"
echo "================================"

# 1. 检查二进制文件
echo ""
echo "📦 检查二进制文件..."
if [[ -f "$APP_NAME" ]]; then
    echo -e "${GREEN}✅ 二进制文件存在${NC}"
    ls -la "$APP_NAME"

    if [[ -x "$APP_NAME" ]]; then
        echo -e "${GREEN}✅ 二进制文件可执行${NC}"
    else
        echo -e "${RED}❌ 二进制文件不可执行${NC}"
        echo "修复命令: chmod +x $APP_NAME"
    fi

    # 检查文件类型
    file_type=$(file "$APP_NAME" 2>/dev/null)
    echo "文件类型: $file_type"
else
    echo -e "${RED}❌ 二进制文件不存在${NC}"
    exit 1
fi

# 2. 检查配置文件
echo ""
echo "⚙️  检查配置文件..."
if [[ -f "config/config.yaml" ]]; then
    echo -e "${GREEN}✅ 配置文件存在${NC}"
    echo "配置文件大小: $(du -h config/config.yaml | cut -f1)"

    # 检查数据库配置
    if grep -q "postgresql://.*8.138.252.193" config/config.yaml; then
        echo -e "${YELLOW}⚠️  发现开发环境数据库地址，应该替换为生产环境地址${NC}"
    elif grep -q "1Panel-postgresql-HioR" config/config.yaml; then
        echo -e "${GREEN}✅ 生产环境数据库配置正确${NC}"
    else
        echo -e "${RED}❌ 未找到数据库配置${NC}"
    fi
else
    echo -e "${RED}❌ 配置文件不存在${NC}"
    echo "请确保 config/config.yaml 文件存在"
fi

# 3. 检查网络连接
echo ""
echo "🌐 检查网络连接..."

# 检查数据库连接
echo "检查数据库连接..."
if ping -c 1 -W 3 1Panel-postgresql-HioR >/dev/null 2>&1; then
    echo -e "${GREEN}✅ 数据库主机可达 (1Panel-postgresql-HioR)${NC}"
elif ping -c 1 -W 3 8.138.252.193 >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  开发环境数据库可达 (8.138.252.193)${NC}"
else
    echo -e "${RED}❌ 数据库主机不可达${NC}"
fi

# 检查Redis连接
echo "检查Redis连接..."
if ping -c 1 -W 3 1Panel-redis-dryE >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Redis主机可达 (1Panel-redis-dryE)${NC}"
elif ping -c 1 -W 3 8.138.252.193 >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  开发环境Redis可达 (8.138.252.193)${NC}"
else
    echo -e "${RED}❌ Redis主机不可达${NC}"
fi

# 4. 检查端口占用
echo ""
echo "🔌 检查端口占用..."
if command -v netstat &> /dev/null; then
    if netstat -tuln | grep -q ":8081 "; then
        echo -e "${RED}❌ 端口8081已被占用${NC}"
        netstat -tuln | grep ":8081 "
    else
        echo -e "${GREEN}✅ 端口8081可用${NC}"
    fi
elif command -v ss &> /dev/null; then
    if ss -tuln | grep -q ":8081 "; then
        echo -e "${RED}❌ 端口8081已被占用${NC}"
        ss -tuln | grep ":8081 "
    else
        echo -e "${GREEN}✅ 端口8081可用${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  无法检查端口占用 (netstat/ss不可用)${NC}"
fi

# 5. 尝试手动启动
echo ""
echo "🚀 尝试手动启动 (前台模式)..."
echo "命令: ./$APP_NAME"
echo "按 Ctrl+C 停止"
echo "================================"

# 设置环境变量
export ENVIRONMENT=production
export GIN_MODE=release

# 前台启动，显示所有输出
"./$APP_NAME"
EOF

    # 状态查看脚本
    cat > "$target_dir/status.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 状态查看脚本

PID_FILE="app.pid"
LOG_FILE="app.log"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

if [[ -f "$PID_FILE" ]]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        echo -e "${GREEN}✅ 应用正在运行 (PID: $PID)${NC}"
        echo -e "${GREEN}🌐 服务地址: http://localhost:8081${NC}"
        echo -e "${GREEN}📋 日志文件: $LOG_FILE${NC}"
        
        # 显示进程信息
        if command -v ps &> /dev/null; then
            echo -e "${GREEN}📊 进程信息:${NC}"
            ps -p "$PID" -o pid,ppid,user,cpu,pmem,etime,cmd 2>/dev/null || true
        fi
        
        # 显示日志目录状态
        if [[ -d "logs" ]]; then
            echo -e "${GREEN}📁 日志目录状态:${NC}"
            ls -la logs/ 2>/dev/null || true
        fi

        # 显示最新日志
        if [[ -f "$LOG_FILE" ]]; then
            echo -e "${GREEN}📜 启动日志 (最后10行):${NC}"
            tail -10 "$LOG_FILE"
        fi

        # 显示应用日志
        if [[ -f "logs/go-kuaidi.log" ]]; then
            echo -e "${GREEN}📋 应用日志 (最后5行):${NC}"
            tail -5 "logs/go-kuaidi.log"
        fi
    else
        echo -e "${RED}❌ 应用未运行，但PID文件存在${NC}"
        echo -e "${YELLOW}建议运行: rm -f $PID_FILE${NC}"
    fi
else
    echo -e "${RED}❌ 应用未运行${NC}"
fi
EOF

    # 设置执行权限
    chmod +x "$target_dir"/*.sh

    log_success "启动脚本创建完成"
    log_info "已创建脚本: start.sh, stop.sh, restart.sh, status.sh, logs.sh, optimize.sh, debug.sh"
}

# 创建部署包
create_deployment_package() {
    log_step "创建生产环境部署包"

    cd "$PROJECT_ROOT"

    local temp_dir="deploy_temp_${BUILD_VERSION}"
    local package_file="${PACKAGE_NAME}.tar.gz"

    # 创建临时目录
    mkdir -p "$temp_dir"

    # 复制核心文件
    log_info "复制应用文件..."
    cp kuaidi-server-prod "$temp_dir/"

    # 复制并处理配置文件
    if [[ -d "config" ]]; then
        mkdir -p "$temp_dir/config"

        # 🚀 处理YAML配置文件，替换生产环境连接URL
        for yaml_file in config/*.yaml; do
            if [[ -f "$yaml_file" ]]; then
                local target_file="$temp_dir/config/$(basename "$yaml_file")"
                log_info "处理配置文件: $(basename "$yaml_file")"

                # 复制文件并替换连接URL
                sed -e 's|8\.138\.252\.193:5432|1Panel-postgresql-HioR:5432|g' \
                    -e 's|8\.138\.252\.193:6379|1Panel-redis-dryE:6379|g' \
                    "$yaml_file" > "$target_file"

                log_info "✅ 已替换数据库连接: 8.138.252.193 → 1Panel-postgresql-HioR"
                log_info "✅ 已替换Redis连接: 8.138.252.193 → 1Panel-redis-dryE"
            fi
        done

        # 复制其他配置文件
        cp config/*.json "$temp_dir/config/" 2>/dev/null || true
        log_info "已复制配置文件"
    fi

    # 复制模板文件
    if [[ -d "templates" ]]; then
        cp -r templates "$temp_dir/"
        log_info "已复制模板文件"
    fi

    # 复制数据文件
    if [[ -d "data" ]]; then
        cp -r data "$temp_dir/"
        log_info "已复制数据文件"
    fi

    # 🚀 创建日志目录
    mkdir -p "$temp_dir/logs"
    log_info "已创建日志目录"

    # 创建启动脚本
    create_startup_scripts "$temp_dir"

    # 获取Git信息
    local git_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    local git_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")

    # 创建README文件
    cat > "$temp_dir/README.md" << EOF
# Go-Kuaidi 生产环境部署包

## 📦 构建信息
- **构建版本**: $BUILD_VERSION
- **构建时间**: $BUILD_TIME
- **Git提交**: $git_commit
- **Git分支**: $git_branch
- **Go版本**: $(go version | awk '{print $3}')
- **目标架构**: linux/amd64
- **配置环境**: 生产环境（已自动替换连接地址）

## 🚀 快速部署

### 1. 启动服务
\`\`\`bash
./start.sh
\`\`\`

### 2. 停止服务
\`\`\`bash
./stop.sh
\`\`\`

### 3. 重启服务
\`\`\`bash
./restart.sh
\`\`\`

### 4. 查看状态
\`\`\`bash
./status.sh
\`\`\`

### 5. 日志管理
\`\`\`bash
# 使用日志管理工具
./logs.sh help

# 查看应用日志
./logs.sh app

# 查看错误日志
./logs.sh error

# 查看所有日志状态
./logs.sh all

# 检查日志文件大小
./logs.sh size

# 手动轮转日志
./logs.sh rotate

# 清理旧日志
./logs.sh clean

# 查看配置文件信息
./logs.sh config
\`\`\`

### 6. 生产环境优化
\`\`\`bash
# 运行优化检查
./optimize.sh

# 这将检查:
# - 系统资源使用情况
# - 磁盘空间状态
# - 日志文件大小
# - 进程运行状态
# - 提供优化建议
\`\`\`

### 7. 启动问题调试
\`\`\`bash
# 如果启动失败，运行调试脚本
./debug.sh

# 这将检查:
# - 二进制文件状态
# - 配置文件完整性
# - 网络连接状态
# - 端口占用情况
# - 前台启动测试
\`\`\`

### 8. 手动查看日志
\`\`\`bash
# 查看应用日志
tail -f logs/go-kuaidi.log

# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/access.log

# 查看审计日志
tail -f logs/audit.log

# 查看启动日志
tail -f app.log
\`\`\`

## 📁 目录结构
- \`kuaidi-server-prod\` - Go后端二进制文件
- \`config/\` - 配置文件
- \`templates/\` - 模板文件
- \`data/\` - 数据文件
- \`logs/\` - 企业级日志目录
  - \`go-kuaidi.log\` - 应用日志
  - \`error.log\` - 错误日志
  - \`access.log\` - 访问日志
  - \`audit.log\` - 审计日志
- \`start.sh\` - 启动脚本
- \`stop.sh\` - 停止脚本
- \`restart.sh\` - 重启脚本
- \`status.sh\` - 状态查看脚本
- \`logs.sh\` - 日志管理脚本
- \`optimize.sh\` - 生产环境优化检查脚本
- \`debug.sh\` - 启动问题调试脚本

## ⚠️ 部署注意事项
1. 确保服务器有足够的权限执行二进制文件
2. 确保8081端口未被占用
3. 确保数据库连接配置正确
4. 生产环境建议使用进程管理工具（如systemd、supervisor等）

## 🔧 配置说明
主要配置文件位于 \`config/config.yaml\`，包含：
- 数据库连接配置（已自动替换为生产环境地址）
- Redis连接配置（已自动替换为生产环境地址）
- 服务器端口配置
- 安全配置等

### 🚀 自动配置替换
构建过程中自动将开发环境地址替换为生产环境地址：
- PostgreSQL: \`8.138.252.193:5432\` → \`1Panel-postgresql-HioR:5432\`
- Redis: \`8.138.252.193:6379\` → \`1Panel-redis-dryE:6379\`

## 📞 技术支持
如有问题，请联系技术团队。
EOF

    # 创建部署包
    log_info "打包部署文件..."
    tar -czf "$package_file" -C "$temp_dir" .

    # 清理临时目录
    rm -rf "$temp_dir"

    # 显示包信息
    local package_size=$(du -h "$package_file" | cut -f1)
    local package_md5=$(md5sum "$package_file" | cut -d' ' -f1)

    log_success "部署包创建完成"
    log_info "文件名: $package_file"
    log_info "大小: $package_size"
    log_info "MD5: $package_md5"

    # 保存包信息
    cat > "${PACKAGE_NAME}.info" << EOF
Package: $package_file
Version: $BUILD_VERSION
Build Time: $BUILD_TIME
Size: $package_size
MD5: $package_md5
Git Commit: $git_commit
Git Branch: $git_branch
Go Version: $(go version | awk '{print $3}')
Architecture: linux/amd64
Environment: production
EOF

    # 显示最终结果
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                       构建完成！                             ║"
    echo "╠══════════════════════════════════════════════════════════════╣"
    echo "║ 部署包: $package_file"
    echo "║ 大小: $package_size"
    echo "║ MD5: $package_md5"
    echo "║"
    echo "║ 部署方法:"
    echo "║ 1. tar -xzf $package_file"
    echo "║ 2. cd 解压目录"
    echo "║ 3. ./start.sh"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 主函数
main() {
    show_build_info
    check_environment
    clean_build
    prepare_dependencies
    build_backend
    create_deployment_package

    log_success "🎉 所有构建步骤完成！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 