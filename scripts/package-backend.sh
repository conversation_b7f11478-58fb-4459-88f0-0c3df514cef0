#!/bin/bash
# Go-Kuaidi 后端部署包构建脚本
# 仅包含后端二进制与必要运行文件，不包含前端静态资源
# 用法: ./scripts/package-backend.sh

set -euo pipefail

############################
# 环境与变量
############################
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BUILD_TIME=$(date +"%Y-%m-%d %H:%M:%S")
BUILD_VERSION=$(date +"%Y%m%d_%H%M%S")
PACKAGE_NAME="go-kuaidi-backend-${BUILD_VERSION}"

# 颜色输出
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

log()    { echo -e "${BLUE}ℹ️  $1${NC}"; }
info()   { echo -e "${GREEN}✅ $1${NC}"; }
warn()   { echo -e "${YELLOW}⚠️  $1${NC}"; }
err()    { echo -e "${RED}❌ $1${NC}"; }
trap 'err "构建过程中发生错误，退出码: $?"' ERR

############################
# 函数定义
############################
check_env() {
  log "检查基础依赖..."
  for tool in go git; do
    if ! command -v "$tool" &> /dev/null; then
      err "$tool 未安装或未在 PATH 中"
      exit 1
    fi
  done
  info "环境检查通过"
}

build_backend() {
  log "编译后端二进制..."
  cd "$PROJECT_ROOT"
  export CGO_ENABLED=0
  export GOOS=linux
  export GOARCH=amd64
  go mod download
  go build -ldflags="-w -s" -o kuaidi-server-prod cmd/main.go
  info "后端编译完成: $(du -h kuaidi-server-prod | cut -f1)"
}

create_package() {
  log "准备部署包内容..."
  local TEMP_DIR="deploy_tmp_${BUILD_VERSION}"
  mkdir -p "$TEMP_DIR"

  # 二进制
  cp kuaidi-server-prod "$TEMP_DIR/"

  ###########################
  # 复制配置（仅生产环境相关）
  ###########################
  if [[ -d config ]]; then
    mkdir -p "$TEMP_DIR/config"

    # 仅挑选生产环境所需文件: config.yaml、production.* 以及其他非示例/非开发的资源文件
    for cfg in config/*; do
      fname=$(basename "$cfg")

      # 1) 开发环境文件 2) 示例文件 3) 任何 .env 文件
      if [[ "$fname" == *development* ]] || [[ "$fname" == *.example* ]] || [[ "$fname" == *.env ]]; then
        continue
      fi

      cp -r "$cfg" "$TEMP_DIR/config/"
    done
  fi

  # 模板、迁移等
  [[ -d templates ]]  && cp -r templates "$TEMP_DIR/"
  [[ -d migrations ]] && cp -r migrations "$TEMP_DIR/"

  # 运行脚本: start / stop
  cat > "$TEMP_DIR/start.sh" << 'EOS'
#!/bin/bash
set -e
APP=./kuaidi-server-prod
LOG=app.log
PID=app.pid

echo "[Go-Kuaidi] 启动后端..."
nohup $APP > $LOG 2>&1 &
echo $! > $PID
echo "运行中, PID=$(cat $PID), 日志: $LOG"
EOS
  chmod +x "$TEMP_DIR/start.sh"

  cat > "$TEMP_DIR/stop.sh" << 'EOS'
#!/bin/bash
PID=app.pid
if [[ -f $PID ]]; then
  kill $(cat $PID) && rm $PID && echo "服务已停止" || echo "停止失败"
else
  echo "未找到PID文件, 服务可能未运行"
fi
EOS
  chmod +x "$TEMP_DIR/stop.sh"

  # 打包
  tar -czf "${PACKAGE_NAME}.tar.gz" -C "$TEMP_DIR" .
  info "部署包已生成: ${PACKAGE_NAME}.tar.gz ($(du -h ${PACKAGE_NAME}.tar.gz | cut -f1))"
  rm -rf "$TEMP_DIR"
}

############################
# 主流程
############################
log "======== Go-Kuaidi 后端打包 ========"
log "构建时间: $BUILD_TIME"
check_env
build_backend
create_package
info "🎉 打包完成。请将 ${PACKAGE_NAME}.tar.gz 上传到服务器并解压后执行 ./start.sh" 