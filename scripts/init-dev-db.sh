#!/bin/bash
# 初始化开发环境数据库

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查PostgreSQL是否运行
check_postgres() {
    log_info "检查PostgreSQL服务状态..."
    
    if command -v pg_isready &> /dev/null; then
        if pg_isready -h localhost -p 5432 &> /dev/null; then
            log_success "PostgreSQL服务正在运行"
            return 0
        else
            log_error "PostgreSQL服务未运行"
            return 1
        fi
    else
        log_warning "pg_isready命令未找到，尝试直接连接..."
        if psql -h localhost -p 5432 -U my -d postgres -c "SELECT 1;" &> /dev/null; then
            log_success "PostgreSQL连接成功"
            return 0
        else
            log_error "无法连接到PostgreSQL"
            return 1
        fi
    fi
}

# 创建数据库
create_database() {
    log_info "创建数据库 go_kuaidi..."
    
    # 检查数据库是否已存在
    if psql -h localhost -p 5432 -U my -d postgres -tc "SELECT 1 FROM pg_database WHERE datname = 'go_kuaidi';" | grep -q 1; then
        log_warning "数据库 go_kuaidi 已存在"
    else
        # 创建数据库
        if psql -h localhost -p 5432 -U my -d postgres -c "CREATE DATABASE go_kuaidi;" &> /dev/null; then
            log_success "数据库 go_kuaidi 创建成功"
        else
            log_error "创建数据库失败"
            return 1
        fi
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 加载环境变量
    if [ -f "config/development.env" ]; then
        export $(grep -v '^#' config/development.env | xargs)
    fi
    
    # 检查是否有迁移文件
    if [ -d "migrations" ] && [ "$(ls -A migrations)" ]; then
        log_info "发现迁移文件，运行迁移..."
        # 这里可以添加具体的迁移命令，比如使用golang-migrate
        # migrate -path migrations -database "$DATABASE_URL" up
        log_warning "请手动运行数据库迁移或使用您的迁移工具"
    else
        log_warning "未找到迁移文件目录"
    fi
}

# 检查Redis连接
check_redis() {
    log_info "检查Redis连接..."
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping &> /dev/null; then
            log_success "Redis连接正常"
        else
            log_warning "Redis连接失败，请确保Redis服务正在运行"
            log_info "启动Redis: brew services start redis (macOS) 或 sudo systemctl start redis (Linux)"
        fi
    else
        log_warning "redis-cli命令未找到"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    directories=(
        "logs"
        "uploads"
        "keys"
        "tmp"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_success "创建目录: $dir"
        else
            log_info "目录已存在: $dir"
        fi
    done
}

# 设置文件权限
set_permissions() {
    log_info "设置文件权限..."
    
    # 设置日志目录权限
    if [ -d "logs" ]; then
        chmod 755 logs
    fi
    
    # 设置上传目录权限
    if [ -d "uploads" ]; then
        chmod 755 uploads
    fi
    
    # 设置密钥文件权限
    if [ -f "keys/private.pem" ]; then
        chmod 600 keys/private.pem
        log_success "设置私钥文件权限为600"
    fi
    
    if [ -f "keys/public.pem" ]; then
        chmod 644 keys/public.pem
        log_success "设置公钥文件权限为644"
    fi
}

# 验证配置
validate_config() {
    log_info "验证配置..."
    
    # 加载环境变量
    if [ -f "config/development.env" ]; then
        export $(grep -v '^#' config/development.env | xargs)
    fi
    
    # 检查必要的环境变量
    required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
        "JWT_SECRET"
        "KUAIDI100_API_KEY"
        "YIDA_USERNAME"
        "YUNTONG_BUSINESS_ID"
    )
    
    missing_vars=()
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "缺少必要的环境变量: ${missing_vars[*]}"
        return 1
    else
        log_success "所有必要的环境变量都已设置"
    fi
}

# 测试数据库连接
test_db_connection() {
    log_info "测试数据库连接..."
    
    # 加载环境变量
    if [ -f "config/development.env" ]; then
        export $(grep -v '^#' config/development.env | xargs)
    fi
    
    # 尝试连接数据库
    if psql "$DATABASE_URL" -c "SELECT version();" &> /dev/null; then
        log_success "数据库连接测试成功"
    else
        log_error "数据库连接测试失败"
        log_info "请检查数据库配置: $DATABASE_URL"
        return 1
    fi
}

# 主函数
main() {
    log_info "开始初始化Go-Kuaidi开发环境数据库..."
    
    # 检查PostgreSQL
    if ! check_postgres; then
        log_error "PostgreSQL服务未运行，请先启动PostgreSQL"
        log_info "macOS: brew services start postgresql"
        log_info "Linux: sudo systemctl start postgresql"
        exit 1
    fi
    
    # 创建数据库
    create_database
    
    # 创建必要目录
    create_directories
    
    # 设置权限
    set_permissions
    
    # 验证配置
    validate_config
    
    # 测试数据库连接
    test_db_connection
    
    # 检查Redis
    check_redis
    
    # 运行迁移
    run_migrations
    
    log_success "开发环境数据库初始化完成！"
    log_info "现在可以运行应用程序了："
    log_info "  source config/development.env && go run cmd/main.go"
}

# 显示帮助
show_help() {
    echo "Go-Kuaidi 开发环境数据库初始化脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -f, --force    强制重新创建数据库"
    echo ""
    echo "前置条件:"
    echo "  - PostgreSQL 服务正在运行"
    echo "  - Redis 服务正在运行（可选）"
    echo "  - 用户 'my' 有创建数据库的权限"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--force)
            FORCE_RECREATE=true
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行主函数
main
