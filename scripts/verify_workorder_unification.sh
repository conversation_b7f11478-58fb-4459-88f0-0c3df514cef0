#!/bin/bash

# 工单类型统一化改造验证脚本
# 创建时间: 2025-06-21
# 描述: 验证工单系统统一化改造是否成功完成

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
DB_URL=${DATABASE_URL:-"postgresql://my@localhost:5432/go_kuaidi?sslmode=disable"}
EXPECTED_TYPES=(1 2 12 16 17 19)
EXPECTED_TYPE_NAMES=("催取件" "重量异常" "催派送" "物流停滞" "重新分配快递员" "取消订单")

echo -e "${BLUE}=== 工单类型统一化改造验证 ===${NC}"
echo "验证时间: $(date)"
echo "数据库: $DB_URL"
echo ""

# 1. 验证数据库连接
echo -e "${YELLOW}1. 验证数据库连接...${NC}"
if psql "$DB_URL" -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 数据库连接成功${NC}"
else
    echo -e "${RED}❌ 数据库连接失败${NC}"
    exit 1
fi

# 2. 验证工单类型映射表结构
echo -e "${YELLOW}2. 验证工单类型映射表结构...${NC}"
MAPPING_COUNT=$(psql "$DB_URL" -t -c "SELECT COUNT(*) FROM work_order_type_mappings;" | tr -d ' ')
echo "工单类型映射总数: $MAPPING_COUNT"

if [ "$MAPPING_COUNT" -eq 18 ]; then
    echo -e "${GREEN}✅ 工单类型映射数量正确 (18条)${NC}"
else
    echo -e "${RED}❌ 工单类型映射数量错误，期望18条，实际${MAPPING_COUNT}条${NC}"
fi

# 3. 验证支持的工单类型
echo -e "${YELLOW}3. 验证支持的工单类型...${NC}"
SUPPORTED_COUNT=$(psql "$DB_URL" -t -c "SELECT COUNT(*) FROM work_order_type_mappings WHERE is_supported = true;" | tr -d ' ')
echo "支持的工单类型数量: $SUPPORTED_COUNT"

if [ "$SUPPORTED_COUNT" -ge 6 ]; then
    echo -e "${GREEN}✅ 支持的工单类型数量正确${NC}"
else
    echo -e "${RED}❌ 支持的工单类型数量不足，实际${SUPPORTED_COUNT}条${NC}"
fi

# 4. 验证核心工单类型存在
echo -e "${YELLOW}4. 验证核心工单类型存在...${NC}"
for i in "${!EXPECTED_TYPES[@]}"; do
    TYPE_ID=${EXPECTED_TYPES[$i]}
    TYPE_NAME=${EXPECTED_TYPE_NAMES[$i]}
    
    EXISTS=$(psql "$DB_URL" -t -c "SELECT COUNT(*) FROM work_order_type_mappings WHERE unified_type = $TYPE_ID;" | tr -d ' ')
    
    if [ "$EXISTS" -gt 0 ]; then
        echo -e "${GREEN}✅ 工单类型 $TYPE_ID ($TYPE_NAME) 存在${NC}"
    else
        echo -e "${RED}❌ 工单类型 $TYPE_ID ($TYPE_NAME) 不存在${NC}"
    fi
done

# 5. 验证供应商支持情况
echo -e "${YELLOW}5. 验证供应商支持情况...${NC}"
PROVIDERS=("kuaidi100" "yida" "yuntong")

for PROVIDER in "${PROVIDERS[@]}"; do
    PROVIDER_SUPPORT=$(psql "$DB_URL" -t -c "SELECT COUNT(*) FROM work_order_type_mappings WHERE provider = '$PROVIDER' AND is_supported = true;" | tr -d ' ')
    echo "供应商 $PROVIDER 支持的工单类型数量: $PROVIDER_SUPPORT"
    
    if [ "$PROVIDER_SUPPORT" -gt 0 ]; then
        echo -e "${GREEN}✅ 供应商 $PROVIDER 支持工单类型${NC}"
    else
        echo -e "${YELLOW}⚠️  供应商 $PROVIDER 不支持任何工单类型${NC}"
    fi
done

# 6. 显示详细的工单类型映射
echo -e "${YELLOW}6. 显示详细的工单类型映射...${NC}"
psql "$DB_URL" -c "
SELECT 
    unified_type,
    unified_name,
    provider,
    provider_type,
    provider_name,
    is_supported
FROM work_order_type_mappings 
ORDER BY unified_type, provider;
"

# 7. 验证历史工单数据
echo -e "${YELLOW}7. 验证历史工单数据...${NC}"
TOTAL_ORDERS=$(psql "$DB_URL" -t -c "SELECT COUNT(*) FROM work_orders;" | tr -d ' ')
echo "历史工单总数: $TOTAL_ORDERS"

if [ "$TOTAL_ORDERS" -gt 0 ]; then
    echo "历史工单类型分布:"
    psql "$DB_URL" -c "
    SELECT 
        work_order_type,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / $TOTAL_ORDERS, 2) as percentage
    FROM work_orders 
    GROUP BY work_order_type 
    ORDER BY count DESC;
    "
fi

# 8. 检查是否存在不支持的工单类型
echo -e "${YELLOW}8. 检查不支持的工单类型...${NC}"
UNSUPPORTED_ORDERS=$(psql "$DB_URL" -t -c "
SELECT COUNT(*) 
FROM work_orders wo 
WHERE NOT EXISTS (
    SELECT 1 
    FROM work_order_type_mappings wotm 
    WHERE wotm.unified_type = wo.work_order_type 
    AND wotm.provider = wo.provider 
    AND wotm.is_supported = true
);
" | tr -d ' ')

if [ "$UNSUPPORTED_ORDERS" -eq 0 ]; then
    echo -e "${GREEN}✅ 所有历史工单类型都有对应的映射${NC}"
else
    echo -e "${YELLOW}⚠️  发现 $UNSUPPORTED_ORDERS 个工单使用了不支持的类型${NC}"
fi

# 9. 生成统计报告
echo -e "${YELLOW}9. 生成统计报告...${NC}"
cat << EOF

=== 工单类型统一化改造统计报告 ===
改造时间: $(date)
数据库: $DB_URL

核心指标:
- 工单类型映射总数: $MAPPING_COUNT
- 支持的工单类型数量: $SUPPORTED_COUNT
- 历史工单总数: $TOTAL_ORDERS
- 不支持的工单数量: $UNSUPPORTED_ORDERS

核心工单类型 (6种):
EOF

for i in "${!EXPECTED_TYPES[@]}"; do
    TYPE_ID=${EXPECTED_TYPES[$i]}
    TYPE_NAME=${EXPECTED_TYPE_NAMES[$i]}
    echo "- $TYPE_ID: $TYPE_NAME"
done

echo ""
echo -e "${GREEN}=== 验证完成 ===${NC}"

# 10. 最终验证结果
if [ "$MAPPING_COUNT" -eq 18 ] && [ "$SUPPORTED_COUNT" -ge 6 ]; then
    echo -e "${GREEN}🎉 工单类型统一化改造验证通过！${NC}"
    exit 0
else
    echo -e "${RED}❌ 工单类型统一化改造验证失败！${NC}"
    exit 1
fi
