#!/bin/bash
# Go-Kuaidi 开发环境快速启动脚本

set -e

GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 启动Go-Kuaidi开发环境...${NC}"

# 检查开发环境配置文件
if [ ! -f "config/development.env" ]; then
    echo "❌ 开发环境配置文件不存在"
    exit 1
fi

# 加载环境变量
echo -e "${GREEN}✅ 加载开发环境配置...${NC}"
while IFS= read -r line; do
    # 跳过注释和空行
    if [[ ! "$line" =~ ^[[:space:]]*# ]] && [[ -n "$line" ]]; then
        # 移除行内注释
        line=$(echo "$line" | sed 's/#.*//')
        # 导出变量
        if [[ "$line" =~ ^[A-Za-z_][A-Za-z0-9_]*= ]]; then
            export "$line"
        fi
    fi
done < config/development.env

# 检查数据库连接
echo -e "${BLUE}🔍 检查数据库连接...${NC}"
if ! psql "$DATABASE_URL" -c "SELECT 1;" &>/dev/null; then
    echo "❌ 数据库连接失败，请检查PostgreSQL服务"
    echo "启动PostgreSQL: brew services start postgresql"
    exit 1
fi

# 检查Redis连接（可选）
if command -v redis-cli &> /dev/null; then
    if ! redis-cli ping &>/dev/null; then
        echo "⚠️  Redis连接失败，某些功能可能受限"
        echo "启动Redis: brew services start redis"
    fi
fi

# 启动应用
echo -e "${GREEN}🎯 启动Go-Kuaidi API服务...${NC}"
echo -e "${BLUE}访问地址: http://localhost:8081${NC}"
echo -e "${BLUE}健康检查: http://localhost:8081/health${NC}"
echo -e "${BLUE}按 Ctrl+C 停止服务${NC}"
echo ""

go run cmd/main.go
