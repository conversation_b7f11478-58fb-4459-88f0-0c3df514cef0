#!/bin/bash
# Go-Ku<PERSON>i 企业级生产环境构建脚本
# 版本: 2.0
# 作者: Go-Kuaidi Team
# 使用方法: ./scripts/build-production.sh

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BUILD_TIME=$(date +"%Y-%m-%d %H:%M:%S")
BUILD_VERSION=$(date +"%Y%m%d_%H%M%S")
PACKAGE_NAME="go-kuaidi-production-${BUILD_VERSION}"

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# 日志函数
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🚀 $1${NC}"; }

# 错误处理
trap 'log_error "构建过程中发生错误，退出码: $?"' ERR

# 显示构建信息
show_build_info() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Go-Kuaidi 生产环境构建                    ║"
    echo "║                      企业级打包工具 v2.0                     ║"
    echo "╠══════════════════════════════════════════════════════════════╣"
    echo "║ 构建时间: ${BUILD_TIME}                           ║"
    echo "║ 构建版本: ${BUILD_VERSION}                        ║"
    echo "║ 项目路径: ${PROJECT_ROOT}                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 环境检查
check_environment() {
    log_step "检查构建环境"
    
    # 检查必要工具
    local tools=("go" "node" "pnpm" "git")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool 未安装或不在PATH中"
            exit 1
        fi
        log_info "$tool: $(command -v "$tool")"
    done
    
    # 检查Go版本
    local go_version=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go版本: $go_version"
    if [[ $(echo "$go_version" | cut -d. -f2) -lt 21 ]]; then
        log_warning "建议使用Go 1.21+版本"
    fi
    
    # 检查Node.js版本
    local node_version=$(node -v | sed 's/v//')
    log_info "Node.js版本: $node_version"
    if [[ $(echo "$node_version" | cut -d. -f1) -lt 18 ]]; then
        log_error "Node.js版本过低，需要18+版本"
        exit 1
    fi
    
    # 检查磁盘空间
    local available_space=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 1048576 ]]; then # 1GB
        log_warning "可用磁盘空间不足1GB，可能影响构建"
    fi
    
    log_success "环境检查完成"
}

# 清理构建目录
clean_build() {
    log_step "清理构建目录"
    
    cd "$PROJECT_ROOT"
    
    # 清理Go构建产物
    rm -f kuaidi-server-prod go-kuaidi
    rm -rf bin/kuaidi-server-prod
    
    # 清理前端构建产物
    for frontend in "admin-frontend" "user-frontend"; do
        if [[ -d "$frontend/dist" ]]; then
            log_info "清理 $frontend/dist"
            rm -rf "$frontend/dist"
        fi
        if [[ -d "$frontend/node_modules/.cache" ]]; then
            rm -rf "$frontend/node_modules/.cache"
        fi
    done
    
    # 清理旧的部署包
    rm -f go-kuaidi-production-*.tar.gz
    rm -rf deploy_temp_*
    
    log_success "构建目录清理完成"
}

# 构建Go后端
build_backend() {
    log_step "构建Go后端服务"
    
    cd "$PROJECT_ROOT"
    
    # 设置构建环境变量
    export CGO_ENABLED=0
    export GOOS=linux
    export GOARCH=amd64
    export ENVIRONMENT=production
    
    # 获取Git信息
    local git_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    local git_branch=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    
    # 构建标志
    local ldflags="-w -s"

    log_info "开始编译Go二进制文件..."
    log_info "目标平台: ${GOOS}/${GOARCH}"
    log_info "Git提交: ${git_commit}"
    log_info "Git分支: ${git_branch}"

    # 下载依赖
    go mod download
    go mod verify

    # 构建
    if go build -ldflags="$ldflags" -o kuaidi-server-prod cmd/main.go; then
        local binary_size=$(du -h kuaidi-server-prod | cut -f1)
        log_success "Go后端构建成功 (大小: ${binary_size})"
        
        # 验证二进制文件
        if file kuaidi-server-prod | grep -q "ELF.*executable"; then
            log_success "二进制文件验证通过"
        else
            log_error "二进制文件格式异常"
            exit 1
        fi
    else
        log_error "Go后端构建失败"
        exit 1
    fi
}

# 构建前端应用
build_frontend() {
    local frontend_dir="$1"
    local app_name="$2"
    
    log_step "构建 ${app_name}"
    
    cd "$PROJECT_ROOT/$frontend_dir"
    
    # 检查package.json
    if [[ ! -f "package.json" ]]; then
        log_error "$frontend_dir/package.json 不存在"
        exit 1
    fi
    
    # 安装依赖
    log_info "安装依赖..."
    if ! pnpm install --frozen-lockfile --prod=false; then
        log_error "依赖安装失败"
        exit 1
    fi
    
    # 构建
    log_info "开始构建..."
    export NODE_ENV=production
    
    if [[ -f "vite.config.production.ts" ]]; then
        if ! pnpm vite build --config vite.config.production.ts --mode production; then
            log_error "$app_name 构建失败"
            exit 1
        fi
    else
        if ! pnpm vite build --mode production; then
            log_error "$app_name 构建失败"
            exit 1
        fi
    fi
    
    # 验证构建产物
    if [[ -d "dist" ]]; then
        local dist_size=$(du -sh dist | cut -f1)
        local file_count=$(find dist -type f | wc -l)
        log_success "$app_name 构建成功 (大小: ${dist_size}, 文件数: ${file_count})"
        
        # 检查关键文件
        if [[ ! -f "dist/index.html" ]]; then
            log_warning "$app_name 缺少 index.html"
        fi
    else
        log_error "$app_name 构建产物目录不存在"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# 安全检查和优化
security_optimization() {
    log_step "执行安全检查和优化"

    cd "$PROJECT_ROOT"

    # 删除敏感文件
    local sensitive_patterns=(
        "*.env*"
        "*.key"
        "*.pem"
        "*.log"
        "**/node_modules"
        "**/.git"
        "**/coverage"
        "**/test"
        "**/tests"
        "**/*.test.*"
        "**/*.spec.*"
        "**/cypress"
        "**/e2e"
    )

    log_info "检查敏感文件..."
    for pattern in "${sensitive_patterns[@]}"; do
        if find . -name "$pattern" -type f 2>/dev/null | head -1 | grep -q .; then
            log_warning "发现敏感文件模式: $pattern"
        fi
    done

    # 删除source map文件
    log_info "删除source map文件..."
    find */dist -name "*.map" -type f -delete 2>/dev/null || true

    # 压缩静态资源
    log_info "压缩静态资源..."
    for frontend in "admin-frontend" "user-frontend"; do
        if [[ -d "$frontend/dist" ]]; then
            # 使用gzip压缩
            find "$frontend/dist" -type f \( -name "*.js" -o -name "*.css" -o -name "*.html" -o -name "*.json" \) \
                -exec gzip -k9 {} \; 2>/dev/null || true

            # 生成文件清单
            find "$frontend/dist" -type f -exec stat -c "%n %s %Y" {} \; > "$frontend/dist/manifest.txt"
        fi
    done

    # 验证二进制文件安全性
    log_info "验证二进制文件..."
    if command -v objdump &> /dev/null; then
        if objdump -p kuaidi-server-prod | grep -q "STACK.*NX"; then
            log_success "二进制文件启用了栈保护"
        fi
    fi

    log_success "安全检查和优化完成"
}

# 创建部署包
create_deployment_package() {
    log_step "创建生产环境部署包"

    cd "$PROJECT_ROOT"

    local temp_dir="deploy_temp_${BUILD_VERSION}"
    local package_file="${PACKAGE_NAME}.tar.gz"

    # 创建临时目录
    mkdir -p "$temp_dir"

    # 复制核心文件
    log_info "复制应用文件..."
    cp kuaidi-server-prod "$temp_dir/"

    # 复制前端构建产物
    for frontend in "admin-frontend" "user-frontend"; do
        if [[ -d "$frontend/dist" ]]; then
            cp -r "$frontend/dist" "$temp_dir/$frontend"
            log_info "已复制 $frontend"
        fi
    done

    # 复制配置文件
    if [[ -d "config" ]]; then
        mkdir -p "$temp_dir/config"
        cp config/production.env "$temp_dir/config/" 2>/dev/null || true
        cp config/config.yaml "$temp_dir/config/" 2>/dev/null || true
        cp config/status_mapping.yaml "$temp_dir/config/" 2>/dev/null || true
        log_info "已复制配置文件"
    fi

    # 复制模板文件
    if [[ -d "templates" ]]; then
        cp -r templates "$temp_dir/"
        log_info "已复制模板文件"
    fi

    # 复制数据文件
    if [[ -d "data" ]]; then
        cp -r data "$temp_dir/"
        log_info "已复制数据文件"
    fi

    # 创建启动脚本
    create_startup_scripts "$temp_dir"

    # 创建配置文件
    create_config_files "$temp_dir"

    # 创建部署包
    log_info "打包部署文件..."
    tar -czf "$package_file" -C "$temp_dir" .

    # 清理临时目录
    rm -rf "$temp_dir"

    # 显示包信息
    local package_size=$(du -h "$package_file" | cut -f1)
    local package_md5=$(md5sum "$package_file" | cut -d' ' -f1)

    log_success "部署包创建完成"
    log_info "文件名: $package_file"
    log_info "大小: $package_size"
    log_info "MD5: $package_md5"

    # 保存包信息
    cat > "${PACKAGE_NAME}.info" << EOF
Package: $package_file
Version: $BUILD_VERSION
Build Time: $BUILD_TIME
Size: $package_size
MD5: $package_md5
Git Commit: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
Git Branch: $(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
EOF
}

# 创建启动脚本
create_startup_scripts() {
    local target_dir="$1"

    log_info "创建启动脚本..."

    # 主启动脚本
    cat > "$target_dir/start.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 生产环境启动脚本

set -e

# 配置
APP_NAME="kuaidi-server-prod"
PID_FILE="app.pid"
LOG_FILE="app.log"
CONFIG_FILE="config/production.env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查是否已运行
if [[ -f "$PID_FILE" ]]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        log_error "应用已在运行 (PID: $PID)"
        exit 1
    else
        log_warn "发现僵尸PID文件，正在清理..."
        rm -f "$PID_FILE"
    fi
fi

# 加载环境变量
if [[ -f "$CONFIG_FILE" ]]; then
    log_info "加载配置文件: $CONFIG_FILE"
    set -a
    source "$CONFIG_FILE"
    set +a
else
    log_warn "配置文件不存在: $CONFIG_FILE"
fi

# 设置生产环境
export ENVIRONMENT=production
export GIN_MODE=release

# 检查二进制文件
if [[ ! -f "$APP_NAME" ]]; then
    log_error "应用文件不存在: $APP_NAME"
    exit 1
fi

if [[ ! -x "$APP_NAME" ]]; then
    log_error "应用文件不可执行: $APP_NAME"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动应用
log_info "启动 Go-Kuaidi 服务..."
nohup "./$APP_NAME" > "$LOG_FILE" 2>&1 &
PID=$!

# 保存PID
echo "$PID" > "$PID_FILE"

# 等待启动
sleep 3

# 检查启动状态
if kill -0 "$PID" 2>/dev/null; then
    log_info "✅ 应用启动成功 (PID: $PID)"
    log_info "📋 日志文件: $LOG_FILE"
    log_info "🔍 查看日志: tail -f $LOG_FILE"
    log_info "🛑 停止服务: ./stop.sh"
else
    log_error "❌ 应用启动失败"
    rm -f "$PID_FILE"
    exit 1
fi
EOF

    # 停止脚本
    cat > "$target_dir/stop.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 停止脚本

PID_FILE="app.pid"
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

if [[ ! -f "$PID_FILE" ]]; then
    log_error "PID文件不存在，应用可能未运行"
    exit 1
fi

PID=$(cat "$PID_FILE")

if ! kill -0 "$PID" 2>/dev/null; then
    log_error "进程不存在 (PID: $PID)"
    rm -f "$PID_FILE"
    exit 1
fi

log_info "正在停止应用 (PID: $PID)..."

# 优雅停止
kill -TERM "$PID"

# 等待进程结束
for i in {1..10}; do
    if ! kill -0 "$PID" 2>/dev/null; then
        break
    fi
    sleep 1
done

# 强制停止
if kill -0 "$PID" 2>/dev/null; then
    log_info "强制停止进程..."
    kill -KILL "$PID"
    sleep 2
fi

# 清理PID文件
rm -f "$PID_FILE"

log_info "✅ 应用已停止"
EOF

    # 重启脚本
    cat > "$target_dir/restart.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 重启脚本

echo "🔄 重启 Go-Kuaidi 服务..."

# 停止服务
if [[ -f "stop.sh" ]]; then
    ./stop.sh
fi

# 等待
sleep 2

# 启动服务
if [[ -f "start.sh" ]]; then
    ./start.sh
else
    echo "❌ 启动脚本不存在"
    exit 1
fi
EOF

    # 状态检查脚本
    cat > "$target_dir/status.sh" << 'EOF'
#!/bin/bash
# Go-Kuaidi 状态检查脚本

PID_FILE="app.pid"
LOG_FILE="app.log"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo "🔍 Go-Kuaidi 服务状态检查"
echo "=========================="

# 检查PID文件
if [[ -f "$PID_FILE" ]]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        echo -e "状态: ${GREEN}运行中${NC} (PID: $PID)"

        # 显示进程信息
        echo "进程信息:"
        ps -p "$PID" -o pid,ppid,cmd,etime,pcpu,pmem --no-headers

        # 检查端口
        if command -v netstat &> /dev/null; then
            echo "监听端口:"
            netstat -tlnp 2>/dev/null | grep "$PID" || echo "无监听端口"
        fi

        # 显示最近日志
        if [[ -f "$LOG_FILE" ]]; then
            echo "最近日志 (最后10行):"
            tail -10 "$LOG_FILE"
        fi
    else
        echo -e "状态: ${RED}已停止${NC} (PID文件存在但进程不存在)"
    fi
else
    echo -e "状态: ${RED}已停止${NC} (无PID文件)"
fi

# 系统资源
echo ""
echo "系统资源:"
echo "内存使用: $(free -h | awk 'NR==2{printf "%.1f%%", $3*100/$2}')"
echo "磁盘使用: $(df -h . | awk 'NR==2{print $5}')"
echo "负载平均: $(uptime | awk -F'load average:' '{print $2}')"
EOF

    # 设置执行权限
    chmod +x "$target_dir"/*.sh

    log_success "启动脚本创建完成"
}

# 创建配置文件
create_config_files() {
    local target_dir="$1"

    log_info "创建配置文件..."

    # 创建Nginx配置示例
    cat > "$target_dir/nginx.conf.example" << 'EOF'
# Go-Kuaidi Nginx 配置示例
server {
    listen 80;
    server_name your-domain.com;

    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL配置
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 用户前端
    location / {
        root /path/to/user-frontend;
        try_files $uri $uri/ /index.html;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }

    # 管理后台
    location /admin {
        alias /path/to/admin-frontend;
        try_files $uri $uri/ /index.html;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location /api {
        proxy_pass http://127.0.0.1:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8081;
        access_log off;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }
}
EOF

    # 创建systemd服务文件
    cat > "$target_dir/go-kuaidi.service.example" << 'EOF'
[Unit]
Description=Go-Kuaidi Express API Service
Documentation=https://github.com/your-org/go-kuaidi
After=network.target postgresql.service redis.service
Wants=postgresql.service redis.service

[Service]
Type=simple
User=kuaidi
Group=kuaidi
WorkingDirectory=/opt/go-kuaidi
ExecStart=/opt/go-kuaidi/kuaidi-server-prod
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=go-kuaidi

# 环境变量
Environment=ENVIRONMENT=production
Environment=GIN_MODE=release
EnvironmentFile=-/opt/go-kuaidi/config/production.env

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/go-kuaidi/logs

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF

    # 创建Docker配置
    cat > "$target_dir/Dockerfile.example" << 'EOF'
# Go-Kuaidi 生产环境 Dockerfile
FROM alpine:3.18

# 安装必要工具
RUN apk add --no-cache ca-certificates tzdata

# 创建用户
RUN addgroup -g 1001 kuaidi && \
    adduser -D -u 1001 -G kuaidi kuaidi

# 设置工作目录
WORKDIR /app

# 复制应用文件
COPY kuaidi-server-prod /app/
COPY config/ /app/config/
COPY templates/ /app/templates/
COPY data/ /app/data/
COPY admin-frontend/ /app/admin-frontend/
COPY user-frontend/ /app/user-frontend/

# 设置权限
RUN chown -R kuaidi:kuaidi /app && \
    chmod +x /app/kuaidi-server-prod

# 切换用户
USER kuaidi

# 暴露端口
EXPOSE 8081

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8081/health || exit 1

# 启动命令
CMD ["./kuaidi-server-prod"]
EOF

    # 创建docker-compose配置
    cat > "$target_dir/docker-compose.yml.example" << 'EOF'
version: '3.8'

services:
  go-kuaidi:
    build: .
    ports:
      - "8081:8081"
    environment:
      - ENVIRONMENT=production
      - GIN_MODE=release
    env_file:
      - config/production.env
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: go_kuaidi
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: your-password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - go-kuaidi
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
EOF

    log_success "配置文件创建完成"
}

# 生成部署文档
generate_deployment_docs() {
    log_step "生成部署文档"

    cat > "DEPLOYMENT_GUIDE_${BUILD_VERSION}.md" << EOF
# Go-Kuaidi 生产环境部署指南

**版本**: ${BUILD_VERSION}
**构建时间**: ${BUILD_TIME}
**包文件**: ${PACKAGE_NAME}.tar.gz

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **磁盘**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+ / RHEL 8+)
- **数据库**: PostgreSQL 13+
- **缓存**: Redis 6+
- **反向代理**: Nginx 1.18+ (推荐)

## 🚀 快速部署

### 1. 上传和解压
\`\`\`bash
# 上传部署包到服务器
scp ${PACKAGE_NAME}.tar.gz user@server:/opt/

# 解压
cd /opt
tar -xzf ${PACKAGE_NAME}.tar.gz
cd go-kuaidi
\`\`\`

### 2. 配置环境变量
\`\`\`bash
# 复制并编辑配置文件
cp config/production.env.example config/production.env
vim config/production.env

# 必须配置的环境变量
export DATABASE_URL="********************************/go_kuaidi"
export REDIS_URL="redis://host:6379/0"
export JWT_SECRET="your-super-secret-key"
\`\`\`

### 3. 启动服务
\`\`\`bash
# 启动应用
./start.sh

# 检查状态
./status.sh

# 查看日志
tail -f app.log
\`\`\`

## 🔧 详细配置

### 数据库配置
确保PostgreSQL已安装并运行：
\`\`\`sql
CREATE DATABASE go_kuaidi;
CREATE USER kuaidi_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE go_kuaidi TO kuaidi_user;
\`\`\`

### Redis配置
确保Redis已安装并运行：
\`\`\`bash
# 启动Redis
systemctl start redis
systemctl enable redis
\`\`\`

### Nginx配置
\`\`\`bash
# 复制Nginx配置
cp nginx.conf.example /etc/nginx/sites-available/go-kuaidi
ln -s /etc/nginx/sites-available/go-kuaidi /etc/nginx/sites-enabled/

# 测试配置
nginx -t

# 重载Nginx
systemctl reload nginx
\`\`\`

## 🐳 Docker部署

### 使用Docker Compose
\`\`\`bash
# 复制配置文件
cp docker-compose.yml.example docker-compose.yml
cp Dockerfile.example Dockerfile

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f go-kuaidi
\`\`\`

## 📊 监控和维护

### 服务管理
\`\`\`bash
# 启动服务
./start.sh

# 停止服务
./stop.sh

# 重启服务
./restart.sh

# 查看状态
./status.sh
\`\`\`

### 日志管理
\`\`\`bash
# 查看实时日志
tail -f app.log

# 查看错误日志
grep ERROR app.log

# 日志轮转
logrotate -f /etc/logrotate.d/go-kuaidi
\`\`\`

### 性能监控
- **CPU使用率**: \`top -p \$(cat app.pid)\`
- **内存使用**: \`ps -p \$(cat app.pid) -o pid,vsz,rss,comm\`
- **网络连接**: \`netstat -tlnp | grep 8081\`

## 🔒 安全配置

### 防火墙设置
\`\`\`bash
# 开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 22/tcp
ufw enable
\`\`\`

### SSL证书
\`\`\`bash
# 使用Let's Encrypt
certbot --nginx -d your-domain.com
\`\`\`

## 🚨 故障排除

### 常见问题

1. **应用无法启动**
   - 检查配置文件是否正确
   - 确认数据库和Redis连接
   - 查看错误日志

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接字符串
   - 确认防火墙设置

3. **Redis连接失败**
   - 检查Redis服务状态
   - 验证Redis配置
   - 检查网络连接

### 日志分析
\`\`\`bash
# 查看启动日志
head -50 app.log

# 查看错误日志
grep -i error app.log

# 查看最近的日志
tail -100 app.log
\`\`\`

## 📞 技术支持

如遇到问题，请提供以下信息：
- 部署包版本: ${BUILD_VERSION}
- 错误日志内容
- 系统环境信息
- 配置文件内容（去除敏感信息）

---
**构建信息**
版本: ${BUILD_VERSION}
时间: ${BUILD_TIME}
Git提交: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
EOF

    log_success "部署文档生成完成: DEPLOYMENT_GUIDE_${BUILD_VERSION}.md"
}

# 主函数
main() {
    show_build_info

    # 执行构建流程
    check_environment
    clean_build
    build_backend

    # 构建前端应用
    if [[ -d "admin-frontend" ]]; then
        build_frontend "admin-frontend" "管理后台"
    else
        log_warning "admin-frontend 目录不存在，跳过构建"
    fi

    if [[ -d "user-frontend" ]]; then
        build_frontend "user-frontend" "用户前端"
    else
        log_warning "user-frontend 目录不存在，跳过构建"
    fi

    # 安全检查和优化
    security_optimization

    # 创建部署包
    create_deployment_package

    # 生成部署文档
    generate_deployment_docs

    # 显示构建结果
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                        构建完成                              ║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    log_success "🎉 生产环境构建完成！"
    echo ""
    log_info "📦 部署包: ${PACKAGE_NAME}.tar.gz"
    log_info "📋 包信息: ${PACKAGE_NAME}.info"
    log_info "📚 部署指南: DEPLOYMENT_GUIDE_${BUILD_VERSION}.md"
    echo ""
    log_step "📋 后续步骤:"
    echo "   1. 上传部署包到生产服务器"
    echo "   2. 解压并配置环境变量"
    echo "   3. 配置数据库和Redis"
    echo "   4. 配置反向代理 (Nginx)"
    echo "   5. 启动应用服务"
    echo "   6. 配置监控和日志"
    echo ""
    log_info "🔍 详细说明请查看部署指南文档"
}

# 执行主函数
main "$@"
