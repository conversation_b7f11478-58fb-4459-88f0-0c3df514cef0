#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快递公司映射数据审计脚本
对比配置文件和数据库中的快递公司映射数据一致性
"""

import json
import yaml
import psycopg2
from typing import Dict, List, Set, Tuple
import sys
from datetime import datetime

class ExpressMappingAuditor:
    def __init__(self):
        self.config_companies = {}
        self.db_companies = {}
        self.config_mappings = {}
        self.db_mappings = {}
        
    def load_config_data(self):
        """加载配置文件数据"""
        print("📁 正在加载配置文件数据...")
        
        # 加载JSON配置文件
        try:
            with open('config/backups/express_companies.json', 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            for company in config_data['enabled_companies']:
                code = company['code']
                self.config_companies[code] = {
                    'name': company['name'],
                    'volume_weight_ratio': company['volume_weight_ratio'],
                    'max_weight_kg': company.get('max_weight_kg', 0),
                    'max_volume_cm3': company.get('max_volume_cm3', 0),
                    'support_volume_weight': company.get('support_volume_weight', True)
                }
                
                # 提取映射关系
                for provider, provider_code in company['provider_codes'].items():
                    key = f"{code}:{provider}"
                    self.config_mappings[key] = provider_code
                    
            print(f"✅ 配置文件加载完成: {len(self.config_companies)} 家快递公司")
            
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            sys.exit(1)
    
    def load_database_data(self):
        """加载数据库数据"""
        print("🗄️  正在加载数据库数据...")
        
        try:
            conn = psycopg2.connect(
                host="localhost",
                port=5432,
                database="go_kuaidi",
                user="my",
                password=""
            )
            cursor = conn.cursor()
            
            # 查询快递公司数据
            cursor.execute("""
                SELECT code, name, volume_weight_ratio, max_volume_cm3, support_volume_weight
                FROM express_companies 
                WHERE is_active = true
                ORDER BY code
            """)
            
            for row in cursor.fetchall():
                code, name, volume_weight_ratio, max_volume_cm3, support_volume_weight = row
                self.db_companies[code] = {
                    'name': name,
                    'volume_weight_ratio': volume_weight_ratio or 0,
                    'max_volume_cm3': max_volume_cm3 or 0,
                    'support_volume_weight': support_volume_weight or False
                }
            
            # 查询映射关系
            cursor.execute("""
                SELECT c.code, p.code, m.provider_company_code
                FROM express_company_provider_mappings m
                JOIN express_companies c ON m.company_id = c.id
                JOIN express_providers p ON m.provider_id = p.id
                WHERE m.is_supported = true
                ORDER BY c.code, p.code
            """)
            
            for row in cursor.fetchall():
                company_code, provider_code, provider_company_code = row
                key = f"{company_code}:{provider_code}"
                self.db_mappings[key] = provider_company_code
            
            cursor.close()
            conn.close()
            
            print(f"✅ 数据库加载完成: {len(self.db_companies)} 家快递公司, {len(self.db_mappings)} 条映射关系")
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)
    
    def compare_companies(self):
        """对比快递公司数据"""
        print("\n🔍 开始对比快递公司数据...")
        
        config_codes = set(self.config_companies.keys())
        db_codes = set(self.db_companies.keys())
        
        # 缺失的快递公司
        missing_in_db = config_codes - db_codes
        extra_in_db = db_codes - config_codes
        common_codes = config_codes & db_codes
        
        print(f"\n📊 快递公司数量对比:")
        print(f"   配置文件: {len(config_codes)} 家")
        print(f"   数据库:   {len(db_codes)} 家")
        print(f"   共同:     {len(common_codes)} 家")
        
        if missing_in_db:
            print(f"\n❌ 数据库中缺失的快递公司 ({len(missing_in_db)} 家):")
            for code in sorted(missing_in_db):
                name = self.config_companies[code]['name']
                print(f"   - {code}: {name}")
        
        if extra_in_db:
            print(f"\n➕ 数据库中多余的快递公司 ({len(extra_in_db)} 家):")
            for code in sorted(extra_in_db):
                name = self.db_companies[code]['name']
                print(f"   - {code}: {name}")
        
        # 对比共同快递公司的详细信息
        inconsistencies = []
        for code in common_codes:
            config_company = self.config_companies[code]
            db_company = self.db_companies[code]
            
            issues = []
            
            # 检查名称
            if config_company['name'] != db_company['name']:
                issues.append(f"名称不一致: 配置'{config_company['name']}' vs 数据库'{db_company['name']}'")
            
            # 检查抛比
            if config_company['volume_weight_ratio'] != db_company['volume_weight_ratio']:
                issues.append(f"抛比不一致: 配置{config_company['volume_weight_ratio']} vs 数据库{db_company['volume_weight_ratio']}")
            
            if issues:
                inconsistencies.append((code, issues))
        
        if inconsistencies:
            print(f"\n⚠️  快递公司信息不一致 ({len(inconsistencies)} 家):")
            for code, issues in inconsistencies:
                print(f"   {code}:")
                for issue in issues:
                    print(f"     - {issue}")
        else:
            print(f"\n✅ 所有共同快递公司信息一致")
        
        return missing_in_db, extra_in_db, inconsistencies
    
    def compare_mappings(self):
        """对比映射关系"""
        print(f"\n🔗 开始对比映射关系...")
        
        config_keys = set(self.config_mappings.keys())
        db_keys = set(self.db_mappings.keys())
        
        missing_mappings = config_keys - db_keys
        extra_mappings = db_keys - config_keys
        common_mappings = config_keys & db_keys
        
        print(f"\n📊 映射关系数量对比:")
        print(f"   配置文件: {len(config_keys)} 条")
        print(f"   数据库:   {len(db_keys)} 条")
        print(f"   共同:     {len(common_mappings)} 条")
        
        if missing_mappings:
            print(f"\n❌ 数据库中缺失的映射关系 ({len(missing_mappings)} 条):")
            for key in sorted(missing_mappings):
                company_code, provider = key.split(':')
                provider_code = self.config_mappings[key]
                print(f"   - {company_code} -> {provider}: {provider_code}")
        
        if extra_mappings:
            print(f"\n➕ 数据库中多余的映射关系 ({len(extra_mappings)} 条):")
            for key in sorted(extra_mappings):
                company_code, provider = key.split(':')
                provider_code = self.db_mappings[key]
                print(f"   - {company_code} -> {provider}: {provider_code}")
        
        # 对比映射代码
        mapping_inconsistencies = []
        for key in common_mappings:
            config_code = self.config_mappings[key]
            db_code = self.db_mappings[key]
            
            if config_code != db_code:
                company_code, provider = key.split(':')
                mapping_inconsistencies.append((company_code, provider, config_code, db_code))
        
        if mapping_inconsistencies:
            print(f"\n⚠️  映射代码不一致 ({len(mapping_inconsistencies)} 条):")
            for company_code, provider, config_code, db_code in mapping_inconsistencies:
                print(f"   {company_code} -> {provider}: 配置'{config_code}' vs 数据库'{db_code}'")
        else:
            print(f"\n✅ 所有共同映射关系代码一致")
        
        return missing_mappings, extra_mappings, mapping_inconsistencies
    
    def generate_report(self):
        """生成完整的审计报告"""
        print(f"\n" + "="*80)
        print(f"📋 快递公司映射数据审计报告")
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"="*80)
        
        # 加载数据
        self.load_config_data()
        self.load_database_data()
        
        # 对比数据
        missing_companies, extra_companies, company_inconsistencies = self.compare_companies()
        missing_mappings, extra_mappings, mapping_inconsistencies = self.compare_mappings()
        
        # 生成总结
        print(f"\n📈 审计总结:")
        total_issues = (len(missing_companies) + len(extra_companies) + 
                       len(company_inconsistencies) + len(missing_mappings) + 
                       len(extra_mappings) + len(mapping_inconsistencies))
        
        if total_issues == 0:
            print(f"   ✅ 数据完全一致，无需修复")
        else:
            print(f"   ⚠️  发现 {total_issues} 个问题需要处理")
            
            if missing_companies or extra_companies or company_inconsistencies:
                print(f"   📦 快递公司问题: {len(missing_companies) + len(extra_companies) + len(company_inconsistencies)} 个")
            
            if missing_mappings or extra_mappings or mapping_inconsistencies:
                print(f"   🔗 映射关系问题: {len(missing_mappings) + len(extra_mappings) + len(mapping_inconsistencies)} 个")
        
        print(f"\n" + "="*80)
        return total_issues == 0

if __name__ == "__main__":
    auditor = ExpressMappingAuditor()
    success = auditor.generate_report()
    sys.exit(0 if success else 1)
