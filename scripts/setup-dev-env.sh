#!/bin/bash
# 设置开发环境并运行安全检查

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 设置Go-Kuaidi开发环境...${NC}"

# 加载开发环境变量
if [ -f "config/development.env" ]; then
    echo -e "${GREEN}✅ 加载开发环境配置...${NC}"
    # 更安全的环境变量加载方式
    while IFS= read -r line; do
        # 跳过注释和空行
        if [[ ! "$line" =~ ^[[:space:]]*# ]] && [[ -n "$line" ]]; then
            # 移除行内注释
            line=$(echo "$line" | sed 's/#.*//')
            # 导出变量
            if [[ "$line" =~ ^[A-Za-z_][A-Za-z0-9_]*= ]]; then
                export "$line"
            fi
        fi
    done < config/development.env
else
    echo "❌ 未找到开发环境配置文件"
    exit 1
fi

# 检查密钥文件是否存在
if [ ! -f "keys/private.pem" ] || [ ! -f "keys/public.pem" ]; then
    echo -e "${BLUE}🔑 生成JWT密钥对...${NC}"
    mkdir -p keys
    openssl genrsa -out keys/private.pem 2048
    openssl rsa -in keys/private.pem -pubout -out keys/public.pem
    chmod 600 keys/private.pem
    chmod 644 keys/public.pem
    echo -e "${GREEN}✅ JWT密钥对已生成${NC}"
fi

# 创建日志目录
mkdir -p logs

# 运行安全检查
echo -e "${BLUE}🔍 运行安全检查...${NC}"
./scripts/security-check.sh

echo -e "${GREEN}✅ 开发环境设置完成！${NC}"
