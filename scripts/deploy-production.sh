#!/bin/bash
# Go-<PERSON>aidi 生产环境部署脚本
# 使用方法: ./scripts/deploy-production.sh

set -e

echo "🚀 开始生产环境部署..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查环境
check_environment() {
    echo -e "${YELLOW}检查部署环境...${NC}"
    
    # 检查Node.js版本
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        echo -e "${RED}❌ Node.js 版本过低，需要 >= 16${NC}"
        exit 1
    fi
    
    # 检查pnpm版本
    if ! command -v pnpm &> /dev/null; then
        echo -e "${RED}❌ pnpm 未安装${NC}"
        exit 1
    fi
    
    # 检查Go版本
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go 未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 后端构建
build_backend() {
    echo -e "${YELLOW}构建后端服务...${NC}"
    
    # 当前已经在 go-kuaidi 目录中
    
    # 设置生产环境变量
    export ENVIRONMENT=production
    export CGO_ENABLED=0
    export GOOS=linux
    export GOARCH=amd64
    
    # 构建优化的二进制文件
    go build -ldflags="-w -s" -o kuaidi-server-prod cmd/main.go
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 后端构建成功${NC}"
    else
        echo -e "${RED}❌ 后端构建失败${NC}"
        exit 1
    fi
}

# 前端构建
build_frontend() {
    local frontend_dir=$1
    local app_name=$2
    
    echo -e "${YELLOW}构建 ${app_name}...${NC}"
    
    cd "${frontend_dir}"
    
    # 安装依赖
    pnpm install --frozen-lockfile --prod=false
    
    # 使用生产环境配置构建
    if [ -f "vite.config.production.ts" ]; then
        pnpm vite build --config vite.config.production.ts --mode production
    else
        NODE_ENV=production pnpm vite build --mode production
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ ${app_name} 构建成功${NC}"
        
        # 显示构建信息
        if [ -d "dist" ]; then
            echo "📦 构建产物大小:"
            du -sh dist
            echo "📁 文件数量: $(find dist -type f | wc -l)"
        fi
    else
        echo -e "${RED}❌ ${app_name} 构建失败${NC}"
        exit 1
    fi
    
    # 返回到 go-kuaidi 目录
    cd ..
}

# 安全检查
security_check() {
    echo -e "${YELLOW}执行安全检查...${NC}"
    
    # 检查敏感文件
    SENSITIVE_FILES=(
        ".env"
        ".env.local" 
        ".env.development"
        "config/development.env"
        "keys/private.pem"
    )
    
    for file in "${SENSITIVE_FILES[@]}"; do
        if [ -f "${file}" ]; then
            echo -e "${YELLOW}⚠️  发现敏感文件: ${file}${NC}"
        fi
    done
    
    # 检查前端dist目录是否包含source map
    if find */dist -name "*.map" 2>/dev/null | grep -q .; then
        echo -e "${RED}❌ 发现source map文件，生产环境不应包含${NC}"
        find */dist -name "*.map" -delete
        echo -e "${GREEN}✅ 已删除source map文件${NC}"
    fi
    
    echo -e "${GREEN}✅ 安全检查完成${NC}"
}

# 性能优化
optimize_build() {
    echo -e "${YELLOW}执行性能优化...${NC}"
    
    # 压缩静态文件
    for frontend_dir in "user-frontend" "admin-frontend"; do
        if [ -d "${frontend_dir}/dist" ]; then
            echo "压缩 ${frontend_dir} 静态文件..."
            
            # 使用gzip压缩
            find "${frontend_dir}/dist" -type f \( -name "*.js" -o -name "*.css" -o -name "*.html" \) -exec gzip -k {} \;
            
            # 生成资源清单
            find "${frontend_dir}/dist" -type f -exec stat -c "%n %s" {} \; > "${frontend_dir}/dist/manifest.txt"
        fi
    done
    
    echo -e "${GREEN}✅ 性能优化完成${NC}"
}

# 创建部署包
create_deployment_package() {
    echo -e "${YELLOW}创建部署包...${NC}"
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    PACKAGE_NAME="go-kuaidi-prod-${TIMESTAMP}.tar.gz"
    
    # 创建临时目录
    TEMP_DIR="deploy_temp_${TIMESTAMP}"
    mkdir -p "${TEMP_DIR}"
    
    # 复制生产文件
    cp kuaidi-server-prod "${TEMP_DIR}/"
    cp -r user-frontend/dist "${TEMP_DIR}/user-frontend"
    cp -r admin-frontend/dist "${TEMP_DIR}/admin-frontend"
    cp -r config "${TEMP_DIR}/"
    cp -r templates "${TEMP_DIR}/" 2>/dev/null || echo "templates目录不存在，跳过"
    
    # 创建启动脚本
    cat > "${TEMP_DIR}/start.sh" << 'EOF'
#!/bin/bash
export ENVIRONMENT=production
nohup ./kuaidi-server-prod > app.log 2>&1 &
echo $! > app.pid
echo "应用已启动，PID: $(cat app.pid)"
EOF
    
    chmod +x "${TEMP_DIR}/start.sh"
    
    # 创建停止脚本
    cat > "${TEMP_DIR}/stop.sh" << 'EOF'
#!/bin/bash
if [ -f app.pid ]; then
    PID=$(cat app.pid)
    kill $PID
    rm app.pid
    echo "应用已停止"
else
    echo "应用未运行"
fi
EOF
    
    chmod +x "${TEMP_DIR}/stop.sh"
    
    # 打包
    tar -czf "${PACKAGE_NAME}" -C "${TEMP_DIR}" .
    
    # 清理临时目录
    rm -rf "${TEMP_DIR}"
    
    echo -e "${GREEN}✅ 部署包创建完成: ${PACKAGE_NAME}${NC}"
    
    # 显示包大小
    echo "📦 部署包大小: $(du -sh ${PACKAGE_NAME} | cut -f1)"
}

# 生成部署说明
generate_deployment_guide() {
    cat > "DEPLOYMENT_GUIDE.md" << 'EOF'
# Go-Kuaidi 生产环境部署指南

## 部署步骤

1. 上传部署包到服务器
2. 解压部署包：`tar -xzf go-kuaidi-prod-*.tar.gz`
3. 配置生产环境变量
4. 启动应用：`./start.sh`
5. 检查应用状态：`ps aux | grep kuaidi-server-prod`

## 环境变量配置

确保以下环境变量已正确配置：

```bash
export ENVIRONMENT=production
export DATABASE_URL=postgresql://...
export REDIS_URL=redis://...
export JWT_SECRET=your-production-jwt-secret
export CORS_ALLOWED_ORIGINS=https://yourdomain.com
```

## 反向代理配置 (Nginx)

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    # 用户前端
    location / {
        root /path/to/user-frontend;
        try_files $uri $uri/ /index.html;
    }
    
    # 管理后台
    location /admin {
        root /path/to/admin-frontend;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api {
        proxy_pass http://127.0.0.1:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 性能优化建议

1. 启用gzip压缩
2. 配置静态文件缓存
3. 使用CDN加速静态资源
4. 配置数据库连接池
5. 启用Redis缓存

## 监控检查

- 应用进程：`ps aux | grep kuaidi-server-prod`
- 端口监听：`netstat -tlnp | grep 8081`
- 日志查看：`tail -f app.log`
- 内存使用：`free -h`
- 磁盘空间：`df -h`

EOF

    echo -e "${GREEN}✅ 部署指南已生成: DEPLOYMENT_GUIDE.md${NC}"
}

# 主函数
main() {
    echo "🎯 Go-Kuaidi 生产环境部署"
    echo "========================="
    
    check_environment
    build_backend
    build_frontend "user-frontend" "用户前端"
    build_frontend "admin-frontend" "管理后台"
    security_check
    optimize_build
    create_deployment_package
    generate_deployment_guide
    
    echo ""
    echo -e "${GREEN}🎉 生产环境部署准备完成！${NC}"
    echo ""
    echo "📋 后续步骤:"
    echo "1. 上传部署包到生产服务器"
    echo "2. 配置生产环境变量" 
    echo "3. 配置反向代理 (Nginx/Apache)"
    echo "4. 启动应用服务"
    echo "5. 配置监控和日志"
    echo ""
    echo "📚 详细说明请查看: DEPLOYMENT_GUIDE.md"
}

# 执行主函数
main
