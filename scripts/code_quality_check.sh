#!/bin/bash

# 代码质量检查脚本
# 检查Go代码的编译、格式化、静态分析等

set -e

echo "🔍 开始代码质量检查..."
echo "=================================="

# 1. 检查Go模块
echo "📦 1. 检查Go模块依赖..."
go mod tidy
go mod verify
echo "✅ Go模块检查通过"
echo ""

# 2. 编译检查
echo "🔨 2. 编译检查..."
go build ./...
echo "✅ 编译检查通过"
echo ""

# 3. 格式化检查
echo "📝 3. 代码格式化检查..."
if [ "$(gofmt -l . | wc -l)" -gt 0 ]; then
    echo "❌ 发现格式化问题的文件:"
    gofmt -l .
    echo "运行 'gofmt -w .' 来修复格式化问题"
    exit 1
else
    echo "✅ 代码格式化检查通过"
fi
echo ""

# 4. 静态分析 (如果安装了golint)
echo "🔍 4. 静态分析检查..."
if command -v golint &> /dev/null; then
    echo "运行 golint..."
    golint ./...
else
    echo "⚠️  golint 未安装，跳过静态分析"
fi
echo ""

# 5. 检查未使用的导入和变量 (使用go vet)
echo "🧹 5. 检查未使用的导入和变量..."
go vet ./...
echo "✅ go vet 检查通过"
echo ""

# 6. 安全检查 (如果安装了gosec)
echo "🔒 6. 安全检查..."
if command -v gosec &> /dev/null; then
    echo "运行 gosec..."
    gosec ./...
else
    echo "⚠️  gosec 未安装，跳过安全检查"
fi
echo ""

# 7. 检查循环依赖
echo "🔄 7. 检查循环依赖..."
go list -f '{{.ImportPath}}: {{.Imports}}' ./... | grep -E "循环|cycle" || echo "✅ 无循环依赖"
echo ""

# 8. 构建主程序
echo "🚀 8. 构建主程序..."
go build -o kuaidi-server ./cmd/main.go
echo "✅ 主程序构建成功"
echo ""

# 9. 检查关键文件是否存在
echo "📋 9. 检查关键文件..."
required_files=(
    "cmd/main.go"
    "go.mod"
    "go.sum"
    "README.md"
    "sql/schema.sql"
    "api/handler/balance_handler.go"
    "internal/service/balance_service.go"
    "internal/repository/balance_repository.go"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 缺失"
    fi
done
echo ""

# 10. 检查配置文件
echo "⚙️  10. 检查配置文件..."
if [ -f "config/config.yaml" ] || [ -f "config/config.json" ] || [ -f ".env" ]; then
    echo "✅ 找到配置文件"
else
    echo "⚠️  未找到配置文件，请确保有适当的配置"
fi
echo ""

echo "🎉 代码质量检查完成！"
echo "=================================="

# 显示项目统计信息
echo "📊 项目统计信息:"
echo "Go文件数量: $(find . -name "*.go" -not -path "./vendor/*" | wc -l)"
echo "代码行数: $(find . -name "*.go" -not -path "./vendor/*" -exec wc -l {} + | tail -1 | awk '{print $1}')"
echo "包数量: $(go list ./... | wc -l)"
echo ""

echo "✨ 所有检查完成！项目代码质量良好。"
