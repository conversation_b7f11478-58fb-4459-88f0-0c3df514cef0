#!/bin/bash
# Go-<PERSON><PERSON>i 生产环境安全检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "命令 $1 未找到，请先安装"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始 Go-Kuaidi 生产环境安全检查..."
    
    # 检查必需的命令
    log_info "检查必需的命令..."
    check_command "go"
    check_command "openssl"
    log_success "所有必需命令都已安装"
    
    # 检查环境变量
    check_environment_variables
    
    # 检查文件权限
    check_file_permissions
    
    # 检查SSL证书
    check_ssl_certificates
    
    # 运行Go安全检查工具
    run_go_security_check
    
    # 检查网络配置
    check_network_configuration
    
    # 生成安全报告
    generate_security_report
    
    log_success "安全检查完成！"
}

# 检查环境变量
check_environment_variables() {
    log_info "检查环境变量配置..."
    
    # 必需的环境变量
    required_vars=(
        "ENVIRONMENT"
        "DATABASE_URL"
        "REDIS_URL"
        "GOKUAIDI_SECURITY_SIGNATURE_ENABLED"
        "GOKUAIDI_SECURITY_RATE_LIMIT_ENABLED"
        "GOKUAIDI_SECURITY_AUDIT_ENABLED"
        "GOKUAIDI_SECURITY_HEADERS_ENABLED"
        "GOKUAIDI_SECURITY_FORCE_HTTPS"
        "CORS_ALLOWED_ORIGINS"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
            log_error "缺少必需的环境变量: $var"
        else
            log_success "$var 已设置"
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "发现 ${#missing_vars[@]} 个缺失的环境变量"
        exit 1
    fi
    
    # 检查JWT配置
    if [ -z "$JWT_SECRET" ] && ([ -z "$JWT_PRIVATE_KEY_PATH" ] || [ -z "$JWT_PUBLIC_KEY_PATH" ]); then
        log_error "必须设置 JWT_SECRET 或 JWT_PRIVATE_KEY_PATH/JWT_PUBLIC_KEY_PATH"
        exit 1
    fi
    
    # 检查生产环境特定配置
    if [ "$ENVIRONMENT" = "production" ]; then
        check_production_config
    fi
    
    log_success "环境变量检查通过"
}

# 检查生产环境配置
check_production_config() {
    log_info "检查生产环境特定配置..."
    
    # 检查数据库SSL
    if [[ "$DATABASE_URL" == *"sslmode=disable"* ]]; then
        log_error "生产环境必须启用数据库SSL连接"
        exit 1
    fi
    
    # 检查CORS配置
    if [[ "$CORS_ALLOWED_ORIGINS" == *"localhost"* ]] || [[ "$CORS_ALLOWED_ORIGINS" == *"127.0.0.1"* ]]; then
        log_warning "生产环境CORS配置包含本地地址"
    fi
    
    # 检查安全功能
    security_features=(
        "GOKUAIDI_SECURITY_SIGNATURE_ENABLED"
        "GOKUAIDI_SECURITY_RATE_LIMIT_ENABLED"
        "GOKUAIDI_SECURITY_AUDIT_ENABLED"
        "GOKUAIDI_SECURITY_HEADERS_ENABLED"
        "GOKUAIDI_SECURITY_FORCE_HTTPS"
    )
    
    for feature in "${security_features[@]}"; do
        if [ "${!feature}" != "true" ]; then
            log_error "生产环境必须启用 $feature"
            exit 1
        fi
    done
    
    log_success "生产环境配置检查通过"
}

# 检查文件权限
check_file_permissions() {
    log_info "检查文件权限..."
    
    # 检查私钥文件
    if [ -n "$JWT_PRIVATE_KEY_PATH" ] && [ -f "$JWT_PRIVATE_KEY_PATH" ]; then
        perm=$(stat -c "%a" "$JWT_PRIVATE_KEY_PATH" 2>/dev/null || stat -f "%A" "$JWT_PRIVATE_KEY_PATH" 2>/dev/null)
        if [ "$perm" != "600" ]; then
            log_warning "私钥文件权限不正确: $perm，应该是 600"
            chmod 600 "$JWT_PRIVATE_KEY_PATH"
            log_success "已修复私钥文件权限"
        else
            log_success "私钥文件权限正确"
        fi
    fi
    
    # 检查配置文件
    config_files=("config/config.yaml" "config/production.env" ".env")
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            perm=$(stat -c "%a" "$file" 2>/dev/null || stat -f "%A" "$file" 2>/dev/null)
            if [ "$perm" -gt "640" ]; then
                log_warning "配置文件 $file 权限过宽: $perm"
            else
                log_success "配置文件 $file 权限正确"
            fi
        fi
    done
    
    log_success "文件权限检查完成"
}

# 检查SSL证书
check_ssl_certificates() {
    log_info "检查SSL证书..."
    
    if [ -n "$JWT_PRIVATE_KEY_PATH" ] && [ -f "$JWT_PRIVATE_KEY_PATH" ]; then
        # 验证私钥文件
        if openssl rsa -in "$JWT_PRIVATE_KEY_PATH" -check -noout &>/dev/null; then
            log_success "JWT私钥文件有效"
        else
            log_error "JWT私钥文件无效或损坏"
            exit 1
        fi
    fi
    
    if [ -n "$JWT_PUBLIC_KEY_PATH" ] && [ -f "$JWT_PUBLIC_KEY_PATH" ]; then
        # 验证公钥文件
        if openssl rsa -pubin -in "$JWT_PUBLIC_KEY_PATH" -noout &>/dev/null; then
            log_success "JWT公钥文件有效"
        else
            log_error "JWT公钥文件无效或损坏"
            exit 1
        fi
    fi
    
    log_success "SSL证书检查完成"
}

# 运行Go安全检查工具
run_go_security_check() {
    log_info "运行Go安全检查工具..."
    
    if [ -f "cmd/security-check/main.go" ]; then
        if go run cmd/security-check/main.go -output console; then
            log_success "Go安全检查通过"
        else
            log_error "Go安全检查失败"
            exit 1
        fi
    else
        log_warning "未找到Go安全检查工具"
    fi
}

# 检查网络配置
check_network_configuration() {
    log_info "检查网络配置..."
    
    # 检查端口配置
    port=${SERVER_PORT:-8081}
    if [ "$port" -lt 1024 ] && [ "$(id -u)" -ne 0 ]; then
        log_warning "使用特权端口 $port 但不是root用户"
    fi
    
    log_success "网络配置检查完成"
}

# 生成安全报告
generate_security_report() {
    log_info "生成安全报告..."
    
    report_file="security-report-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "=== Go-Kuaidi 安全检查报告 ==="
        echo "生成时间: $(date)"
        echo "环境: ${ENVIRONMENT:-unknown}"
        echo ""
        echo "✅ 环境变量配置: 通过"
        echo "✅ 文件权限检查: 通过"
        echo "✅ SSL证书验证: 通过"
        echo "✅ 网络配置检查: 通过"
        echo ""
        echo "建议:"
        echo "- 定期轮换API密钥和JWT密钥"
        echo "- 监控安全日志和异常访问"
        echo "- 保持依赖项更新"
        echo "- 定期进行安全审计"
    } > "$report_file"
    
    log_success "安全报告已生成: $report_file"
}

# 显示帮助信息
show_help() {
    echo "Go-Kuaidi 安全检查脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --verbose  详细输出"
    echo ""
    echo "示例:"
    echo "  $0                # 运行完整安全检查"
    echo "  $0 --verbose      # 详细模式运行"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行主函数
main
