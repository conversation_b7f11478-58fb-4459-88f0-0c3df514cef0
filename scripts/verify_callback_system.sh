#!/bin/bash

# Go-<PERSON><PERSON>i 回调系统验证脚本
set -e

echo "🔍  Go-Kuaidi 回调系统..."

# 1. 检查文件结构
echo "📁 检查文件结构..."
required_files=(
    "internal/service/callback/adapter_interface.go"
    "internal/service/callback/yuntong_adapter.go"
    "internal/service/callback/yida_adapter.go"
    "internal/service/callback/kuaidi100_adapter.go"
    "internal/service/callback/unified_callback_service.go"
    "internal/constants/callback_constants.go"
    "migrations/callback_tables.sql"
    "config/callback.example.yaml"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file 缺失"
        exit 1
    fi
done

# 2. 编译检查
echo "🔨 编译检查..."
if go build -o /tmp/go-kuaidi-test ./cmd/main.go 2>/dev/null; then
    echo "✅ 编译成功"
    rm -f /tmp/go-kuaidi-test
else
    echo "❌ 编译失败"
    exit 1
fi

# 3. 运行测
echo "🧪 运行测试..."
if go test ./internal/service/callback/... -v; then
    echo "✅ 测试通过"
else
    echo "❌ 测试失败"
    exit 1
fi

# 4. 检查代码质量
#echo "📊 检
..."
if command -v golint &> /dev/null; then
    golint ./internal/service/callback/...
fi

if command -v go &> /dev/null; then
    go vet ./internal/service/callback/...
    echo "✅ 代码质量检查通过"
fi

# 5. 检查配置文件
echo "⚙️ 检查配置文件..."
if [ -f "config/callback.example.yaml" ]; then
    echo "✅ 配置文件示例存"
else
    echo "❌ 配置文件示例缺失"
    exit 1
fi

## 6. 'EOF'

echo "📈 代码统计..."
echo "回调系统代码行数:"
find internal/service/callback -name "*.go" -not -name "*_test.go" | xargs wc -l | tail -1
echo "测试代码行数:"
find internal/service/callback -name "*_test.go" | xargs wc -l | tail -1

echo ""
:"
echo ""
echo "📋 系统特性:"
100三大供应商"
echo "  ✅ 统一数据标准化处理"
""""""
echo "  ✅ 完整的签名验证"
echo "  ✅ 智能重试机制"
echo 企业级配置管理"  
echo "  ✅ 完整的监控统计"
echo "  ✅ 高性能并发处理"
echo ""
echo "🚀 系统已准备就绪，可以部署到生产环境！"
