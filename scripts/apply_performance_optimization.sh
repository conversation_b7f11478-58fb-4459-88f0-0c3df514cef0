#!/bin/bash

# Go-<PERSON>aidi 数据库性能优化执行脚本
# 基于第一性原理的企业级数据库优化方案
# 创建时间: 2025-06-19
# 版本: v1.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-go_kuaidi}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD:-}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SQL_DIR="${SCRIPT_DIR}/../sql"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v psql &> /dev/null; then
        log_error "psql 命令未找到，请安装 PostgreSQL 客户端"
        exit 1
    fi
    
    if ! command -v pg_isready &> /dev/null; then
        log_error "pg_isready 命令未找到，请安装 PostgreSQL 客户端"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 检查数据库连接
check_database_connection() {
    log_info "检查数据库连接..."
    
    if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" &> /dev/null; then
        log_error "无法连接到数据库 $DB_HOST:$DB_PORT/$DB_NAME"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 执行SQL文件
execute_sql_file() {
    local sql_file="$1"
    local description="$2"
    
    log_info "执行 $description..."
    
    if [ ! -f "$sql_file" ]; then
        log_error "SQL文件不存在: $sql_file"
        return 1
    fi
    
    # 设置PGPASSWORD环境变量
    export PGPASSWORD="$DB_PASSWORD"
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$sql_file" -v ON_ERROR_STOP=1 &> /dev/null; then
        log_success "$description 执行成功"
        return 0
    else
        log_error "$description 执行失败"
        return 1
    fi
}

# 执行SQL命令
execute_sql_command() {
    local sql_command="$1"
    local description="$2"
    
    log_info "执行 $description..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if echo "$sql_command" | psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -v ON_ERROR_STOP=1 &> /dev/null; then
        log_success "$description 执行成功"
        return 0
    else
        log_error "$description 执行失败"
        return 1
    fi
}

# 备份当前索引信息
backup_current_indexes() {
    log_info "备份当前索引信息..."
    
    local backup_file="${SCRIPT_DIR}/index_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    export PGPASSWORD="$DB_PASSWORD"
    
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT
            'CREATE INDEX ' || indexname || ' ON ' || tablename || ' USING ' ||
            COALESCE(indexdef, 'btree') || ';' as index_definition
        FROM pg_indexes
        WHERE schemaname = 'public'
        AND indexname NOT LIKE '%_pkey'
        ORDER BY tablename, indexname;
    " -t -o "$backup_file"
    
    log_success "索引信息已备份到: $backup_file"
}

# 分析当前表统计信息
analyze_current_stats() {
    log_info "分析当前表统计信息..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 获取表大小信息
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
            pg_stat_get_live_tuples(c.oid) as live_tuples,
            pg_stat_get_dead_tuples(c.oid) as dead_tuples
        FROM pg_tables pt
        JOIN pg_class c ON c.relname = pt.tablename
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
    "
    
    # 获取索引使用情况
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT
            schemaname,
            relname as tablename,
            indexrelname as indexname,
            idx_scan,
            idx_tup_read,
            idx_tup_fetch
        FROM pg_stat_user_indexes
        WHERE schemaname = 'public'
        ORDER BY idx_scan DESC;
    "
}

# 应用性能优化
apply_performance_optimization() {
    log_info "开始应用数据库性能优化..."
    
    # 1. 执行索引优化
    if execute_sql_file "$SQL_DIR/performance_optimization.sql" "索引优化"; then
        log_success "索引优化完成"
    else
        log_warning "索引优化部分失败，请检查日志"
    fi
    
    # 2. 更新表统计信息
    log_info "更新表统计信息..."
    execute_sql_command "ANALYZE;" "表统计信息更新"
    
    # 3. 重建统计信息
    log_info "重建关键表统计信息..."
    execute_sql_command "ANALYZE order_records;" "订单表统计信息重建"
    execute_sql_command "ANALYZE users;" "用户表统计信息重建"
    execute_sql_command "ANALYZE express_companies;" "快递公司表统计信息重建"
    execute_sql_command "ANALYZE balance_transactions;" "余额交易表统计信息重建"
    
    log_success "数据库性能优化应用完成"
}

# 验证优化效果
verify_optimization() {
    log_info "验证优化效果..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 检查新创建的索引
    log_info "检查新创建的索引..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT
            indexname,
            tablename,
            pg_size_pretty(pg_relation_size(('public.' || indexname)::regclass)) as index_size
        FROM pg_indexes
        WHERE schemaname = 'public'
        AND indexname LIKE 'idx_order_records_%'
        ORDER BY pg_relation_size(('public.' || indexname)::regclass) DESC;
    "
    
    # 检查表的缓存命中率
    log_info "检查表的缓存命中率..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT
            schemaname,
            relname as tablename,
            heap_blks_read,
            heap_blks_hit,
            CASE
                WHEN heap_blks_hit + heap_blks_read = 0 THEN 0
                ELSE round(heap_blks_hit::numeric / (heap_blks_hit + heap_blks_read) * 100, 2)
            END as cache_hit_ratio
        FROM pg_statio_user_tables
        WHERE schemaname = 'public'
        ORDER BY cache_hit_ratio DESC;
    "
    
    log_success "优化效果验证完成"
}

# 生成优化报告
generate_optimization_report() {
    log_info "生成优化报告..."
    
    local report_file="${SCRIPT_DIR}/optimization_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "Go-Kuaidi 数据库性能优化报告"
        echo "================================"
        echo "优化时间: $(date)"
        echo "数据库: $DB_HOST:$DB_PORT/$DB_NAME"
        echo ""
        
        echo "1. 索引创建情况:"
        echo "----------------"
        export PGPASSWORD="$DB_PASSWORD"
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT COUNT(*) as new_indexes_count
            FROM pg_indexes 
            WHERE schemaname = 'public' 
            AND indexname LIKE 'idx_order_records_%';
        " -t
        
        echo ""
        echo "2. 表大小统计:"
        echo "-------------"
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT
                tablename,
                pg_size_pretty(pg_total_relation_size(('public.' || tablename)::regclass)) as total_size,
                pg_size_pretty(pg_relation_size(('public.' || tablename)::regclass)) as table_size,
                pg_size_pretty(pg_total_relation_size(('public.' || tablename)::regclass) - pg_relation_size(('public.' || tablename)::regclass)) as index_size
            FROM pg_tables
            WHERE schemaname = 'public'
            ORDER BY pg_total_relation_size(('public.' || tablename)::regclass) DESC;
        " -t
        
        echo ""
        echo "3. 优化建议:"
        echo "-----------"
        echo "- 定期执行 VACUUM ANALYZE 维护表统计信息"
        echo "- 监控慢查询日志，及时优化问题查询"
        echo "- 根据业务增长情况调整连接池配置"
        echo "- 考虑实施分区策略处理大表"
        
    } > "$report_file"
    
    log_success "优化报告已生成: $report_file"
}

# 主函数
main() {
    echo "========================================"
    echo "Go-Kuaidi 数据库性能优化工具"
    echo "========================================"
    echo ""
    
    # 检查参数
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        echo "用法: $0 [选项]"
        echo ""
        echo "环境变量:"
        echo "  DB_HOST     数据库主机 (默认: localhost)"
        echo "  DB_PORT     数据库端口 (默认: 5432)"
        echo "  DB_NAME     数据库名称 (默认: go_kuaidi)"
        echo "  DB_USER     数据库用户 (默认: postgres)"
        echo "  DB_PASSWORD 数据库密码"
        echo ""
        echo "选项:"
        echo "  --help, -h  显示此帮助信息"
        echo "  --dry-run   仅分析当前状态，不执行优化"
        echo ""
        exit 0
    fi
    
    # 检查是否为试运行模式
    if [ "$1" = "--dry-run" ]; then
        log_info "试运行模式：仅分析当前状态"
        check_dependencies
        check_database_connection
        analyze_current_stats
        exit 0
    fi
    
    # 执行优化流程
    check_dependencies
    check_database_connection
    
    # 询问用户确认
    echo ""
    log_warning "即将对数据库 $DB_HOST:$DB_PORT/$DB_NAME 执行性能优化"
    read -p "是否继续? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 执行优化
    backup_current_indexes
    analyze_current_stats
    apply_performance_optimization
    verify_optimization
    generate_optimization_report
    
    echo ""
    log_success "数据库性能优化完成！"
    log_info "建议重启应用服务以使配置生效"
}

# 执行主函数
main "$@"
