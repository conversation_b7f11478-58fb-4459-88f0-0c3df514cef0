#!/bin/bash

# Go-Kuaidi 查价数据验证脚本
# 功能: 验证查价返回数据的真实性和有效性

set -e

# 配置参数
BASE_URL="http://localhost:8081"
USERNAME="admin"
PASSWORD="test_secret"
VALIDATION_RESULTS_DIR="./validation_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
VALIDATION_REPORT="$VALIDATION_RESULTS_DIR/data_validation_$TIMESTAMP.txt"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 创建验证结果目录
mkdir -p "$VALIDATION_RESULTS_DIR"

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$VALIDATION_REPORT"
}

success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$VALIDATION_REPORT"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$VALIDATION_REPORT"
}

error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$VALIDATION_REPORT"
}

# 获取认证Token
get_auth_token() {
    local response=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}")
    
    echo "$response" | jq -r '.access_token // empty'
}

# 验证价格数据结构
validate_price_structure() {
    local json_data="$1"
    local test_name="$2"
    
    log "验证价格数据结构: $test_name"
    
    # 检查基本响应结构
    local success=$(echo "$json_data" | jq -r '.success // false')
    local code=$(echo "$json_data" | jq -r '.code // 0')
    local message=$(echo "$json_data" | jq -r '.message // ""')
    local data_exists=$(echo "$json_data" | jq 'has("data")')
    
    if [ "$success" != "true" ]; then
        error "响应success字段不为true: $success"
        return 1
    fi
    
    if [ "$code" != "200" ]; then
        error "响应code不为200: $code"
        return 1
    fi
    
    if [ "$data_exists" != "true" ]; then
        error "响应缺少data字段"
        return 1
    fi
    
    # 检查价格数据数组
    local data_count=$(echo "$json_data" | jq '.data | length')
    if [ "$data_count" -eq "0" ]; then
        warning "返回的价格数据为空"
        return 1
    fi
    
    success "基本数据结构验证通过 (返回 $data_count 条价格)"
    
    # 验证每条价格数据的字段
    local validation_errors=0
    
    for i in $(seq 0 $((data_count - 1))); do
        local price_item=$(echo "$json_data" | jq ".data[$i]")
        
        # 必需字段验证
        local required_fields=("express_code" "express_name" "price" "order_code")
        for field in "${required_fields[@]}"; do
            local value=$(echo "$price_item" | jq -r ".$field // empty")
            if [ -z "$value" ] || [ "$value" = "null" ]; then
                error "价格项 $i 缺少必需字段: $field"
                ((validation_errors++))
            fi
        done
        
        # 价格数值验证
        local price=$(echo "$price_item" | jq -r '.price // 0')
        if (( $(echo "$price <= 0" | bc -l) )); then
            error "价格项 $i 价格无效: $price"
            ((validation_errors++))
        fi
        
        # 快递代码格式验证
        local express_code=$(echo "$price_item" | jq -r '.express_code // ""')
        if [[ ! "$express_code" =~ ^[A-Z0-9]+$ ]]; then
            warning "价格项 $i 快递代码格式可能异常: $express_code"
        fi
        
        # 下单代码验证
        local order_code=$(echo "$price_item" | jq -r '.order_code // ""')
        if [[ ! "$order_code" =~ ^(ORDER_CODE_|ENHANCED_ORDER_CODE_) ]]; then
            warning "价格项 $i 下单代码格式异常: $order_code"
        fi
    done
    
    if [ "$validation_errors" -eq "0" ]; then
        success "所有价格数据字段验证通过"
        return 0
    else
        error "发现 $validation_errors 个数据验证错误"
        return 1
    fi
}

# 验证价格合理性
validate_price_reasonableness() {
    local json_data="$1"
    local weight="$2"
    local test_name="$3"
    
    log "验证价格合理性: $test_name (重量: ${weight}kg)"
    
    local data_count=$(echo "$json_data" | jq '.data | length')
    local reasonable_count=0
    local total_price=0
    
    for i in $(seq 0 $((data_count - 1))); do
        local price_item=$(echo "$json_data" | jq ".data[$i]")
        local express_name=$(echo "$price_item" | jq -r '.express_name')
        local price=$(echo "$price_item" | jq -r '.price')
        local calc_weight=$(echo "$price_item" | jq -r '.calc_weight // 0')
        
        # 价格合理性检查 (基于经验值)
        local min_price=5   # 最低价格5元
        local max_price=100 # 最高价格100元 (对于普通重量)
        
        if (( $(echo "$price >= $min_price && $price <= $max_price" | bc -l) )); then
            ((reasonable_count++))
            total_price=$(echo "$total_price + $price" | bc)
            success "  $express_name: ¥$price (计费重量: ${calc_weight}kg) - 价格合理"
        else
            warning "  $express_name: ¥$price - 价格可能异常"
        fi
        
        # 计费重量合理性检查
        if (( $(echo "$calc_weight < $weight" | bc -l) )); then
            warning "  $express_name: 计费重量($calc_weight kg)小于实际重量($weight kg)"
        fi
    done
    
    local avg_price=$(echo "scale=2; $total_price / $reasonable_count" | bc)
    
    echo "  价格统计:" >> "$VALIDATION_REPORT"
    echo "    合理价格数量: $reasonable_count/$data_count" >> "$VALIDATION_REPORT"
    echo "    平均价格: ¥$avg_price" >> "$VALIDATION_REPORT"
    echo "    价格范围: ¥$(echo "$json_data" | jq -r '[.data[].price] | min') - ¥$(echo "$json_data" | jq -r '[.data[].price] | max')" >> "$VALIDATION_REPORT"
    
    if [ "$reasonable_count" -gt "0" ]; then
        success "价格合理性验证通过 (平均价格: ¥$avg_price)"
        return 0
    else
        error "所有价格都不在合理范围内"
        return 1
    fi
}

# 验证快递公司覆盖度
validate_express_coverage() {
    local json_data="$1"
    local test_name="$2"
    
    log "验证快递公司覆盖度: $test_name"
    
    # 期望的主要快递公司
    local expected_companies=("ZTO" "STO" "YTO" "YD" "JD" "SF" "EMS")
    local found_companies=()
    
    # 获取返回的快递公司
    local express_codes=$(echo "$json_data" | jq -r '.data[].express_code' | sort | uniq)
    
    echo "  返回的快递公司:" >> "$VALIDATION_REPORT"
    while IFS= read -r code; do
        if [ -n "$code" ]; then
            local name=$(echo "$json_data" | jq -r ".data[] | select(.express_code == \"$code\") | .express_name" | head -1)
            echo "    $code: $name" >> "$VALIDATION_REPORT"
            found_companies+=("$code")
        fi
    done <<< "$express_codes"
    
    # 检查主要快递公司覆盖情况
    local coverage_count=0
    for company in "${expected_companies[@]}"; do
        if [[ " ${found_companies[@]} " =~ " $company " ]]; then
            ((coverage_count++))
        fi
    done
    
    local coverage_rate=$(echo "scale=2; $coverage_count * 100 / ${#expected_companies[@]}" | bc)
    
    echo "  覆盖度统计:" >> "$VALIDATION_REPORT"
    echo "    期望快递公司: ${#expected_companies[@]}" >> "$VALIDATION_REPORT"
    echo "    实际覆盖: $coverage_count" >> "$VALIDATION_REPORT"
    echo "    覆盖率: $coverage_rate%" >> "$VALIDATION_REPORT"
    
    if [ "$coverage_count" -ge "3" ]; then
        success "快递公司覆盖度良好 (覆盖率: $coverage_rate%)"
        return 0
    else
        warning "快递公司覆盖度较低 (覆盖率: $coverage_rate%)"
        return 1
    fi
}

# 验证数据一致性
validate_data_consistency() {
    local json_data="$1"
    local test_name="$2"
    
    log "验证数据一致性: $test_name"
    
    local data_count=$(echo "$json_data" | jq '.data | length')
    local consistency_errors=0
    
    # 检查价格排序
    local prices=$(echo "$json_data" | jq -r '.data[].price')
    local sorted_prices=$(echo "$prices" | sort -n)
    
    if [ "$prices" = "$sorted_prices" ]; then
        success "价格排序正确 (从低到高)"
    else
        error "价格排序不正确"
        ((consistency_errors++))
    fi
    
    # 检查重复的快递公司
    local unique_companies=$(echo "$json_data" | jq -r '.data[].express_code' | sort | uniq | wc -l)
    local total_companies=$(echo "$json_data" | jq -r '.data[].express_code' | wc -l)
    
    if [ "$unique_companies" -eq "$total_companies" ]; then
        success "无重复快递公司"
    else
        warning "存在重复快递公司 (总数: $total_companies, 去重后: $unique_companies)"
    fi
    
    # 检查下单代码唯一性
    local unique_codes=$(echo "$json_data" | jq -r '.data[].order_code' | sort | uniq | wc -l)
    local total_codes=$(echo "$json_data" | jq -r '.data[].order_code' | wc -l)
    
    if [ "$unique_codes" -eq "$total_codes" ]; then
        success "下单代码唯一性正确"
    else
        error "下单代码存在重复"
        ((consistency_errors++))
    fi
    
    if [ "$consistency_errors" -eq "0" ]; then
        success "数据一致性验证通过"
        return 0
    else
        error "发现 $consistency_errors 个一致性错误"
        return 1
    fi
}

# 执行完整的数据验证
perform_data_validation() {
    local token="$1"
    local test_name="$2"
    local from_province="$3"
    local from_city="$4"
    local to_province="$5"
    local to_city="$6"
    local weight="$7"
    
    log "执行数据验证: $test_name"
    
    # 发送查价请求
    local response=$(curl -s -X POST "$BASE_URL/api/v1/express/price" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d "{
            \"from_province\": \"$from_province\",
            \"from_city\": \"$from_city\",
            \"to_province\": \"$to_province\",
            \"to_city\": \"$to_city\",
            \"weight\": $weight
        }")
    
    # 保存原始响应
    echo "$response" > "$VALIDATION_RESULTS_DIR/raw_response_${test_name// /_}.json"
    
    # 执行各项验证
    local validation_passed=0
    
    if validate_price_structure "$response" "$test_name"; then
        ((validation_passed++))
    fi
    
    if validate_price_reasonableness "$response" "$weight" "$test_name"; then
        ((validation_passed++))
    fi
    
    if validate_express_coverage "$response" "$test_name"; then
        ((validation_passed++))
    fi
    
    if validate_data_consistency "$response" "$test_name"; then
        ((validation_passed++))
    fi
    
    echo "  验证结果: $validation_passed/4 项通过" >> "$VALIDATION_REPORT"
    echo "" >> "$VALIDATION_REPORT"
    
    return $validation_passed
}

# 主函数
main() {
    echo "========================================" > "$VALIDATION_REPORT"
    echo "Go-Kuaidi 查价数据验证报告" >> "$VALIDATION_REPORT"
    echo "========================================" >> "$VALIDATION_REPORT"
    echo "开始时间: $(date)" >> "$VALIDATION_REPORT"
    echo "" >> "$VALIDATION_REPORT"
    
    log "开始Go-Kuaidi查价数据验证..."
    
    # 获取认证Token
    local token=$(get_auth_token)
    if [ -z "$token" ]; then
        error "无法获取认证Token"
        exit 1
    fi
    
    # 定义验证测试用例
    declare -a test_cases=(
        "深圳到北京,广东省,深圳市,北京市,北京市,1.0"
        "上海到广州,上海市,上海市,广东省,广州市,2.0"
        "北京到杭州,北京市,北京市,浙江省,杭州市,0.5"
        "体积重量测试,浙江省,杭州市,广东省,广州市,0.3"
    )
    
    local total_validations=0
    local passed_validations=0
    
    # 执行验证测试
    for test_case in "${test_cases[@]}"; do
        IFS=',' read -r test_name from_province from_city to_province to_city weight <<< "$test_case"
        
        local result=$(perform_data_validation "$token" "$test_name" "$from_province" "$from_city" "$to_province" "$to_city" "$weight")
        total_validations=$((total_validations + 4))  # 每个测试有4项验证
        passed_validations=$((passed_validations + result))
        
        sleep 1  # 避免请求过于频繁
    done
    
    # 生成总结报告
    echo "========================================" >> "$VALIDATION_REPORT"
    echo "数据验证总结" >> "$VALIDATION_REPORT"
    echo "========================================" >> "$VALIDATION_REPORT"
    echo "总验证项: $total_validations" >> "$VALIDATION_REPORT"
    echo "通过验证: $passed_validations" >> "$VALIDATION_REPORT"
    echo "通过率: $(echo "scale=2; $passed_validations * 100 / $total_validations" | bc)%" >> "$VALIDATION_REPORT"
    echo "验证时间: $(date)" >> "$VALIDATION_REPORT"
    echo "" >> "$VALIDATION_REPORT"
    
    success "数据验证完成！报告文件: $VALIDATION_REPORT"
    
    if [ "$passed_validations" -eq "$total_validations" ]; then
        success "所有数据验证通过！"
        exit 0
    else
        warning "部分数据验证未通过，请查看详细报告"
        exit 1
    fi
}

# 执行主函数
main "$@"
