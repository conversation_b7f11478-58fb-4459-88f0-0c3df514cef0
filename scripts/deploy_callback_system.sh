#!/bin/bash

# Go-Kuaidi 回调系统部署脚本
set -e

echo "🚀 开始部署 Go-Kuaidi 回调系统..."

# 1. 检查环境
echo "📋 检查部署环境..."
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go 1.19+"
    exit 1
fi

if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL 客户端未安装"
    exit 1
fi

# 2. 编
echo "🔨 编译项目..."
go mod tidy
go build -o bin/go-kuaidi ./cmd/server

# 3. 检查配置文件
echo "⚙️ 检查配置文........"
if [ ! -f "config/callback.yaml" ]; then
    echo "📝 创建默认配置文件..."
    cp config/callback.example.yaml config/callback.yaml
    echo "⚠️ 请编辑 config/callback.yaml 配置文件"
fi

# 4. 数据库迁移
echo "🗄️ 执'EOF'..."
if [ -f "migrations/callback_tables.sql" ]; then
    psql $DATABASE_URL -f migrations/callback_tables.sql
    echo "✅ 数据库迁移完成"
else
    echo "⚠️ 数据库迁移文件不存在，请手动创建表结构"
fi

# 5. 运行测试
echo "🧪 ..."
go test ./internal/service/callback/...
echo "✅ 测试通过"

# 6. 启动服务
echo "🎯 启动回调系统..."
echo 'EOF':::::: config/callback.yaml"
echo "日志级别: INFO"
echo "监听端口: 8080"

./bin/go-kuaidi server

echo "🎉 Go-Kuaidi 回调系统部署完成！"
