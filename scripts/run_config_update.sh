#!/bin/bash

# =====================================================
# Go-Kuaidi 预约时间配置更新执行脚本
# 更新日期: 2025-06-30
# =====================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 从环境变量或默认值获取数据库连接信息
    DB_HOST=${DB_HOST:-"gokd-postgresql.ns-wyr9myix.svc"}
    DB_PORT=${DB_PORT:-"5432"}
    DB_NAME=${DB_NAME:-"go_kuaidi"}
    DB_USER=${DB_USER:-"postgres"}
    DB_PASSWORD=${DB_PASSWORD:-"gjx6ngf4"}
    
    # 构建连接字符串
    DB_URL="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
    
    log_info "数据库连接信息:"
    log_info "  主机: ${DB_HOST}:${DB_PORT}"
    log_info "  数据库: ${DB_NAME}"
    log_info "  用户: ${DB_USER}"
    
    # 测试连接
    if command -v psql >/dev/null 2>&1; then
        if psql "${DB_URL}" -c "SELECT 1;" >/dev/null 2>&1; then
            log_success "数据库连接成功"
            return 0
        else
            log_error "数据库连接失败"
            return 1
        fi
    else
        log_warning "psql 命令未找到，跳过连接测试"
        return 0
    fi
}

# 备份当前配置
backup_config() {
    log_info "备份当前配置..."
    
    BACKUP_DIR="./backups"
    BACKUP_FILE="${BACKUP_DIR}/pickup_config_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    mkdir -p "${BACKUP_DIR}"
    
    if command -v psql >/dev/null 2>&1; then
        log_info "导出当前配置到: ${BACKUP_FILE}"
        
        psql "${DB_URL}" -c "
        \copy (SELECT * FROM pickup_time_configs WHERE provider IN ('yida', 'yuntong', 'kuaidi100')) TO '${BACKUP_FILE}_configs.csv' WITH CSV HEADER;
        \copy (SELECT * FROM pickup_business_hours WHERE provider IN ('yida', 'yuntong', 'kuaidi100')) TO '${BACKUP_FILE}_business_hours.csv' WITH CSV HEADER;
        \copy (SELECT * FROM pickup_time_slot_templates WHERE provider IN ('yida', 'yuntong', 'kuaidi100')) TO '${BACKUP_FILE}_time_slots.csv' WITH CSV HEADER;
        " 2>/dev/null || log_warning "备份失败，继续执行更新"
        
        log_success "配置备份完成"
    else
        log_warning "psql 命令未找到，跳过备份"
    fi
}

# 执行配置更新
update_config() {
    log_info "执行配置更新..."
    
    SQL_FILE="./update_pickup_time_configs.sql"
    
    if [ ! -f "${SQL_FILE}" ]; then
        log_error "SQL文件不存在: ${SQL_FILE}"
        return 1
    fi
    
    if command -v psql >/dev/null 2>&1; then
        log_info "执行SQL更新脚本..."
        
        if psql "${DB_URL}" -f "${SQL_FILE}"; then
            log_success "配置更新成功"
            return 0
        else
            log_error "配置更新失败"
            return 1
        fi
    else
        log_error "psql 命令未找到，无法执行更新"
        log_info "请手动执行以下命令:"
        log_info "psql '${DB_URL}' -f '${SQL_FILE}'"
        return 1
    fi
}

# 验证配置
verify_config() {
    log_info "验证配置更新结果..."
    
    if command -v psql >/dev/null 2>&1; then
        log_info "检查配置数量..."
        
        # 检查各供应商配置数量
        YIDA_COUNT=$(psql "${DB_URL}" -t -c "SELECT COUNT(*) FROM pickup_time_configs WHERE provider = 'yida';" 2>/dev/null | tr -d ' ')
        YUNTONG_COUNT=$(psql "${DB_URL}" -t -c "SELECT COUNT(*) FROM pickup_time_configs WHERE provider = 'yuntong';" 2>/dev/null | tr -d ' ')
        KUAIDI100_COUNT=$(psql "${DB_URL}" -t -c "SELECT COUNT(*) FROM pickup_time_configs WHERE provider = 'kuaidi100';" 2>/dev/null | tr -d ' ')
        
        log_info "配置统计:"
        log_info "  易达配置: ${YIDA_COUNT} 条"
        log_info "  云通配置: ${YUNTONG_COUNT} 条"
        log_info "  快递100配置: ${KUAIDI100_COUNT} 条"
        
        # 检查时间段模板
        YIDA_SLOTS=$(psql "${DB_URL}" -t -c "SELECT COUNT(*) FROM pickup_time_slot_templates WHERE provider = 'yida';" 2>/dev/null | tr -d ' ')
        YUNTONG_SLOTS=$(psql "${DB_URL}" -t -c "SELECT COUNT(*) FROM pickup_time_slot_templates WHERE provider = 'yuntong';" 2>/dev/null | tr -d ' ')
        ZTO_SLOTS=$(psql "${DB_URL}" -t -c "SELECT COUNT(*) FROM pickup_time_slot_templates WHERE provider = 'kuaidi100' AND express_code = 'ZTO';" 2>/dev/null | tr -d ' ')
        
        log_info "时间段模板:"
        log_info "  易达时间段: ${YIDA_SLOTS} 个"
        log_info "  云通时间段: ${YUNTONG_SLOTS} 个"
        log_info "  中通时间段: ${ZTO_SLOTS} 个"
        
        # 验证预期结果
        if [ "${YIDA_SLOTS}" = "10" ] && [ "${YUNTONG_SLOTS}" = "10" ] && [ "${ZTO_SLOTS}" = "5" ]; then
            log_success "时间段配置验证通过"
        else
            log_warning "时间段配置数量异常，请检查"
        fi
        
        log_success "配置验证完成"
    else
        log_warning "psql 命令未找到，跳过验证"
    fi
}

# 清理缓存
clear_cache() {
    log_info "清理预约时间缓存..."
    
    if command -v psql >/dev/null 2>&1; then
        psql "${DB_URL}" -c "DELETE FROM pickup_time_cache WHERE provider IN ('yida', 'yuntong', 'kuaidi100');" 2>/dev/null || log_warning "缓存清理失败"
        log_success "缓存清理完成"
    else
        log_warning "psql 命令未找到，跳过缓存清理"
    fi
}

# 主函数
main() {
    log_info "开始执行预约时间配置更新..."
    log_info "脚本位置: $(pwd)"
    log_info "执行时间: $(date)"
    
    # 检查数据库连接
    if ! check_database; then
        log_error "数据库连接检查失败，退出"
        exit 1
    fi
    
    # 备份当前配置
    backup_config
    
    # 执行配置更新
    if ! update_config; then
        log_error "配置更新失败，退出"
        exit 1
    fi
    
    # 验证配置
    verify_config
    
    # 清理缓存
    clear_cache
    
    log_success "预约时间配置更新完成！"
    log_info "请重启应用服务以使配置生效"
    
    # 显示下一步操作建议
    echo ""
    log_info "下一步操作建议:"
    log_info "1. 重启 go-kuaidi 服务"
    log_info "2. 测试预约时间接口"
    log_info "3. 验证各供应商时间段返回"
    echo ""
}

# 执行主函数
main "$@"
