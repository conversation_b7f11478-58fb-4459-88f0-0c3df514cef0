package handler

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// OrderHandler 订单处理器
type OrderHandler struct {
	orderService           *service.OrderService
	priceService           service.PriceServiceInterface  // 使用实时价格服务
	weightTierCacheService service.WeightTierCacheService // 🔥 新增：重量档位缓存服务
	expressCompanyService  express.ExpressCompanyService  // 🔥 新增：快递公司服务
	logger                 *zap.Logger
}

// NewOrderHandler 创建订单处理器
func NewOrderHandler(orderService *service.OrderService, priceService service.PriceServiceInterface, weightTierCacheService service.WeightTierCacheService, expressCompanyService express.ExpressCompanyService, logger *zap.Logger) *OrderHandler {
	return &OrderHandler{
		orderService:           orderService,
		priceService:           priceService,
		weightTierCacheService: weightTierCacheService,
		expressCompanyService:  expressCompanyService,
		logger:                 logger,
	}
}

// 删除标准接口，只保留简化接口

// CancelOrder 取消订单
// @Summary 取消订单
// @Description 取消快递订单
// @Tags 订单
// @Accept json
// @Produce json
// @Param request body model.CancelOrderRequest true "取消订单请求"
// @Success 200 {object} model.CancelOrderResponse "成功"
// @Failure 400 {object} model.ErrorResponse "请求参数错误"
// @Failure 500 {object} model.ErrorResponse "服务器内部错误"
// @Router /api/v1/express/order/cancel [post]
func (h *OrderHandler) CancelOrder(c *gin.Context) {
	var req model.CancelOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 🔥 调试：记录前端传递的原始请求参数
	h.logger.Info("🔍 取消订单请求详情",
		zap.String("order_no", req.OrderNo),
		zap.String("tracking_no", req.TrackingNo),
		zap.String("reason", req.Reason),
		zap.String("raw_request", fmt.Sprintf("%+v", req)))

	// 获取当前用户ID
	userID := getOrderUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Success: false,
			Code:    model.StatusUnauthorized,
			Message: "用户身份验证失败",
		})
		return
	}

	// 🔥 检查是否为管理员（支持多种检测方式）
	isAdmin, _ := c.Get("is_admin")
	isAdminUser := isAdmin != nil && isAdmin.(bool)

	// 🔥 如果通过管理员中间件没有检测到，尝试手动检测管理员权限
	if !isAdminUser {
		// 检查用户是否有管理员权限（通过scope检查）
		scopes, exists := c.Get("scopes")
		if exists {
			if scopeList, ok := scopes.([]string); ok {
				for _, scope := range scopeList {
					if scope == "admin:all" || scope == "admin:super" {
						isAdminUser = true
						break
					}
				}
			}
		}
	}

	// 🔥 调试：记录用户ID和管理员状态
	h.logger.Info("🔍 取消订单用户验证",
		zap.String("user_id", userID),
		zap.Bool("is_admin", isAdminUser),
		zap.String("request_order_no", req.OrderNo),
		zap.String("request_tracking_no", req.TrackingNo))

	// 🔥 管理员可以跨用户取消订单，普通用户只能取消自己的订单
	if isAdminUser {
		// 管理员：不设置用户ID限制，允许跨用户取消
		h.logger.Info("🔑 管理员权限：允许跨用户取消订单",
			zap.String("admin_user_id", userID),
			zap.String("order_no", req.OrderNo))
		// req.UserID 保持为空，表示不进行用户权限验证
	} else {
		// 普通用户：设置用户ID进行权限验证
		req.UserID = userID
	}

	// 取消订单
	resp, err := h.orderService.CancelOrder(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "取消订单失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// DeleteFailedOrder 删除失败订单
// @Summary 删除失败订单
// @Description 删除失败的快递订单记录
// @Tags 订单
// @Accept json
// @Produce json
// @Param order_no query string true "订单号"
// @Success 200 {object} model.BaseResponse "成功"
// @Failure 400 {object} model.ErrorResponse "请求参数错误"
// @Failure 500 {object} model.ErrorResponse "服务器内部错误"
// @Router /api/v1/express/order/failed [delete]
func (h *OrderHandler) DeleteFailedOrder(c *gin.Context) {
	orderNo := c.Query("order_no")
	if orderNo == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "订单号不能为空",
		})
		return
	}

	// 获取当前用户ID
	userID := getOrderUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Success: false,
			Code:    model.StatusUnauthorized,
			Message: "用户身份验证失败",
		})
		return
	}

	// 调用订单服务删除失败订单
	err := h.orderService.DeleteFailedOrder(c.Request.Context(), orderNo, userID)
	if err != nil {
		h.logger.Error("删除失败订单失败",
			zap.String("order_no", orderNo),
			zap.String("user_id", userID),
			zap.Error(err))

		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "删除失败订单失败: " + err.Error(),
		})
		return
	}

	h.logger.Info("删除失败订单成功",
		zap.String("order_no", orderNo),
		zap.String("user_id", userID))

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "失败订单删除成功",
	})
}

// QueryOrder 查询订单
// @Summary 查询订单
// @Description 查询快递订单（注意：易达API不支持直接查询订单详情功能，请使用物流轨迹查询功能获取物流状态，或保存下单时返回的订单信息）
// @Tags 订单
// @Accept json
// @Produce json
// @Param request body model.QueryOrderRequest true "查询订单请求"
// @Success 200 {object} model.QueryOrderResponse "成功"
// @Failure 400 {object} model.ErrorResponse "请求参数错误"
// @Failure 500 {object} model.ErrorResponse "服务器内部错误"
// @Router /api/v1/express/order/query [post]
func (h *OrderHandler) QueryOrder(c *gin.Context) {
	var req model.QueryOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID := getOrderUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Success: false,
			Code:    model.StatusUnauthorized,
			Message: "用户身份验证失败",
		})
		return
	}

	// 设置用户ID进行权限验证
	req.UserID = userID

	// 查询订单
	resp, err := h.orderService.QueryOrder(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "查询订单失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// QueryTrack 查询物流轨迹
// @Summary 查询物流轨迹
// @Description 查询快递物流轨迹
// @Tags 订单
// @Accept json
// @Produce json
// @Param request body model.TrackQueryRequest true "查询物流轨迹请求"
// @Success 200 {object} model.TrackQueryResponse "成功"
// @Failure 400 {object} model.ErrorResponse "请求参数错误"
// @Failure 500 {object} model.ErrorResponse "服务器内部错误"
// @Router /api/v1/express/track [post]
func (h *OrderHandler) QueryTrack(c *gin.Context) {
	var req model.TrackQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID (JWT认证)
	userID := getOrderUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Success: false,
			Code:    model.StatusUnauthorized,
			Message: "用户身份验证失败",
		})
		return
	}

	// 查询物流轨迹
	resp, err := h.orderService.QueryTrack(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "查询物流轨迹失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// SimpleOrderRequest 简化下单请求
type SimpleOrderRequest struct {
	// 订单信息
	UserID          string `json:"user_id"`           // 用户ID (必填)
	CustomerOrderNo string `json:"customer_order_no"` // 🔥 新增：客户自定义订单号（可选）
	OrderCode       string `json:"order_code"`        // 统一下单代码 (从查价接口获取)

	// 寄件人信息 (必填)
	SenderName     string `json:"sender_name"`     // 寄件人姓名
	SenderMobile   string `json:"sender_mobile"`   // 寄件人手机
	SenderProvince string `json:"sender_province"` // 寄件省
	SenderCity     string `json:"sender_city"`     // 寄件市
	SenderDistrict string `json:"sender_district"` // 寄件区/县
	SenderAddress  string `json:"sender_address"`  // 寄件详细地址

	// 收件人信息 (必填)
	ReceiverName     string `json:"receiver_name"`     // 收件人姓名
	ReceiverMobile   string `json:"receiver_mobile"`   // 收件人手机
	ReceiverProvince string `json:"receiver_province"` // 收件省
	ReceiverCity     string `json:"receiver_city"`     // 收件市
	ReceiverDistrict string `json:"receiver_district"` // 收件区/县
	ReceiverAddress  string `json:"receiver_address"`  // 收件详细地址

	// 包裹信息
	Weight    float64 `json:"weight"`     // 重量(kg) (必填)
	Volume    float64 `json:"volume"`     // 体积(m³) (可选)
	Length    float64 `json:"length"`     // 长度(cm) (可选) 🔥 新增
	Width     float64 `json:"width"`      // 宽度(cm) (可选) 🔥 新增
	Height    float64 `json:"height"`     // 高度(cm) (可选) 🔥 新增
	Quantity  int     `json:"quantity"`   // 包裹数量 (可选，默认1)
	GoodsName string  `json:"goods_name"` // 物品名称 (可选，默认"物品")

	// 其他信息
	PayMethod     int     `json:"pay_method"`     // 支付方式：0-寄付，1-到付，2-月结 (可选，默认0)
	Remark        string  `json:"remark"`         // 备注 (可选)
	InsureValue   int     `json:"insure_value"`   // 保价金额(分) (可选)
	ExpectedPrice float64 `json:"expected_price"` // 期望价格 (可选，用于一致性验证)

	// 🔥 新增：菜鸟裹裹专用字段
	OutOrderId string `json:"out_order_id,omitempty"` // 外部订单ID（菜鸟裹裹专用，可选）

	// 预约时间相关字段
	PickupStartTime string `json:"pickup_start_time,omitempty"` // 预约开始时间（ISO 8601格式）
	PickupEndTime   string `json:"pickup_end_time,omitempty"`   // 预约结束时间（ISO 8601格式）
}

// SimpleOrderResponse 简化下单响应
type SimpleOrderResponse struct {
	Success         bool   `json:"success"`           // 是否成功
	Code            int    `json:"code"`              // 状态码
	Message         string `json:"message"`           // 消息
	PlatformOrderNo string `json:"platform_order_no"` // 🔥 平台生成的全局唯一订单号
	CustomerOrderNo string `json:"customer_order_no"` // 🔥 客户订单号
	OrderNo         string `json:"order_no"`          // 🔥 主要展示字段：平台订单号（展示给用户）
	WaybillNo       string `json:"waybill_no"`        // 运单号
	ExpressCode     string `json:"express_code"`      // 快递公司代码
	ExpressName     string `json:"express_name"`      // 快递公司名称
	Price           string `json:"price"`             // 价格
	DeliveryTime    string `json:"delivery_time"`     // 预计送达时间
	// 注意：移除了provider_order_no字段，供应商内部订单号不应展示给用户
}

// CreateSimpleOrder 简化创建订单
// @Summary 简化创建订单
// @Description 使用统一下单代码创建订单，系统会自动处理各供应商之间的差异，用户只需提供基本信息
// @Tags 订单
// @Accept json
// @Produce json
// @Param request body SimpleOrderRequest true "简化下单请求"
// @Success 200 {object} SimpleOrderResponse "成功"
// @Failure 400 {object} model.ErrorResponse "请求参数错误"
// @Failure 500 {object} model.ErrorResponse "服务器内部错误"
// @Router /api/v1/express/order [post]
func (h *OrderHandler) CreateSimpleOrder(c *gin.Context) {
	var req SimpleOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证必填参数
	if err := validateSimpleOrderRequest(req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
		})
		return
	}

	// 解析统一下单代码并进行一致性验证
	expressCode, originalCode, provider, channelID, productCode, enhancedCode, err := parseOrderCodeWithValidation(req.OrderCode)
	if err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "无效的下单代码: " + err.Error(),
		})
		return
	}

	// 验证下单请求与查价请求的一致性
	if enhancedCode != nil {
		if err := validateRequestConsistency(req, enhancedCode); err != nil {
			c.JSON(http.StatusBadRequest, model.ErrorResponse{
				Success: false,
				Code:    model.StatusBadRequest,
				Message: "请求一致性验证失败: " + err.Error(),
			})
			return
		}
	}

	// 获取用户ID（优先使用请求中的user_id）
	userID := req.UserID
	if userID == "" {
		// 如果请求中没有user_id，尝试从上下文获取
		userID = getOrderUserIDFromContext(c)
	}
	if userID == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "用户ID不能为空，请在请求中提供user_id字段",
		})
		return
	}

	// 转换为内部请求格式
	internalReq, err := h.convertToInternalOrderRequest(c.Request.Context(), req, expressCode, originalCode, provider, channelID, productCode, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "生成订单号失败: " + err.Error(),
		})
		return
	}

	// 🔥 强制启用实时价格验证机制：所有订单都必须经过价格验证
	var resp *model.OrderResponse
	if enhancedCode != nil {
		// 使用实时价格验证创建订单
		resp, err = h.createOrderWithRealTimePriceValidation(c.Request.Context(), internalReq, enhancedCode)
	} else {
		// 🔥 修复：旧版本代码也必须经过价格验证，不允许绕过
		h.logger.Warn("检测到旧版本下单代码，强制启用价格验证机制",
			zap.String("order_code", req.OrderCode),
			zap.String("customer_order_no", internalReq.CustomerOrderNo),
			zap.String("provider", provider),
			zap.String("express_type", internalReq.ExpressType))

		// 为旧版本代码创建临时的增强代码结构，确保价格验证
		tempEnhancedCode := &EnhancedOrderCode{
			StandardCode: expressCode,
			OriginalCode: originalCode,
			Provider:     provider,
			ChannelID:    channelID,
			ProductCode:  productCode,
			OriginalRequest: &SimplePriceRequest{
				FromProvince: internalReq.Sender.Province,
				FromCity:     internalReq.Sender.City,
				FromDistrict: internalReq.Sender.District,
				ToProvince:   internalReq.Receiver.Province,
				ToCity:       internalReq.Receiver.City,
				ToDistrict:   internalReq.Receiver.District,
				Weight:       internalReq.Package.Weight,
				Volume:       internalReq.Package.Volume,
				Length:       internalReq.Package.Length,
				Width:        internalReq.Package.Width,
				Height:       internalReq.Package.Height,
				Quantity:     internalReq.Package.Quantity,
				GoodsName:    internalReq.Package.GoodsName,
				PayMethod:    internalReq.PayMethod,
			},
		}

		// 强制使用价格验证流程
		resp, err = h.createOrderWithRealTimePriceValidation(c.Request.Context(), internalReq, tempEnhancedCode)
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: err.Error(),
		})
		return
	}

	// 转换为简化响应格式
	simpleResp := convertToSimpleOrderResponse(resp, expressCode)

	c.JSON(http.StatusOK, simpleResp)
}

// 验证简化下单请求
func validateSimpleOrderRequest(req SimpleOrderRequest) error {
	if req.OrderCode == "" {
		return fmt.Errorf("下单代码不能为空")
	}

	// 🔥 新增：验证客户订单号
	if err := validateCustomerOrderNo(req.CustomerOrderNo); err != nil {
		return err
	}

	if req.SenderName == "" || req.SenderMobile == "" || req.SenderProvince == "" || req.SenderCity == "" || req.SenderAddress == "" {
		return fmt.Errorf("寄件人信息不完整")
	}

	if req.ReceiverName == "" || req.ReceiverMobile == "" || req.ReceiverProvince == "" || req.ReceiverCity == "" || req.ReceiverAddress == "" {
		return fmt.Errorf("收件人信息不完整")
	}

	if req.Weight <= 0 {
		return fmt.Errorf("包裹重量必须大于0")
	}

	// 🚀 验证预约时间参数
	if err := validatePickupTimeParams(req); err != nil {
		return err
	}

	return nil
}

// 🔥 新增：validateCustomerOrderNo 验证客户订单号
func validateCustomerOrderNo(customerOrderNo string) error {
	// 客户订单号是可选的，如果为空则跳过验证
	if customerOrderNo == "" {
		return nil
	}

	// 长度验证：1-64个字符
	if len(customerOrderNo) > 64 {
		return fmt.Errorf("客户订单号长度不能超过64个字符")
	}

	// 字符验证：只允许字母、数字、下划线、连字符
	for _, char := range customerOrderNo {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return fmt.Errorf("客户订单号只能包含字母、数字、下划线和连字符")
		}
	}

	return nil
}

// 🔥 新增：checkCustomerOrderNoUniqueness 检查客户订单号唯一性
func (h *OrderHandler) checkCustomerOrderNoUniqueness(ctx context.Context, customerOrderNo, userID string) error {
	// 使用订单服务的新方法检查唯一性
	existingOrder, err := h.orderService.FindByCustomerOrderNoAndUser(ctx, customerOrderNo, userID)
	if err == nil && existingOrder != nil {
		return fmt.Errorf("客户订单号 %s 已存在，请使用不同的订单号", customerOrderNo)
	}

	// 如果是"未找到记录"或"不属于当前用户"的错误，说明订单号可用
	if err != nil && !strings.Contains(err.Error(), "not found") && !strings.Contains(err.Error(), "no rows") &&
		!strings.Contains(err.Error(), "不存在") && !strings.Contains(err.Error(), "不属于当前用户") {
		h.logger.Warn("检查客户订单号唯一性时发生错误",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("user_id", userID),
			zap.Error(err))
		// 为了安全起见，如果查询出错，仍然允许创建订单
	}

	return nil
}

// 🚀 validatePickupTimeParams 验证预约时间参数
func validatePickupTimeParams(req SimpleOrderRequest) error {
	// 如果没有指定预约时间，跳过验证
	if req.PickupStartTime == "" && req.PickupEndTime == "" {
		return nil
	}

	// 如果提供了开始时间或结束时间，必须同时提供两者
	if (req.PickupStartTime != "" && req.PickupEndTime == "") ||
		(req.PickupStartTime == "" && req.PickupEndTime != "") {
		return fmt.Errorf("预约开始时间和结束时间必须同时提供")
	}

	// 验证时间格式
	if req.PickupStartTime != "" {
		if _, err := time.Parse(time.RFC3339, req.PickupStartTime); err != nil {
			return fmt.Errorf("预约开始时间格式错误，请使用ISO 8601格式 (如: 2025-06-30T09:00:00Z): %v", err)
		}
	}

	if req.PickupEndTime != "" {
		if _, err := time.Parse(time.RFC3339, req.PickupEndTime); err != nil {
			return fmt.Errorf("预约结束时间格式错误，请使用ISO 8601格式 (如: 2025-06-30T11:00:00Z): %v", err)
		}
	}

	// 验证时间逻辑
	if req.PickupStartTime != "" && req.PickupEndTime != "" {
		startTime, _ := time.Parse(time.RFC3339, req.PickupStartTime)
		endTime, _ := time.Parse(time.RFC3339, req.PickupEndTime)

		if !endTime.After(startTime) {
			return fmt.Errorf("预约结束时间必须晚于开始时间")
		}

		// 验证预约时间不能是过去时间
		if startTime.Before(util.NowBeijing()) {
			return fmt.Errorf("预约时间不能是过去时间")
		}
	}

	return nil
}

// 注意：EnhancedOrderCode 和 SimplePriceRequest 结构体定义在 simple_price_handler.go 中

// parseOrderCodeWithValidation 解析下单代码并返回增强代码信息（用于一致性验证）
func parseOrderCodeWithValidation(orderCode string) (expressCode, originalCode, provider, channelID, productCode string, enhancedCode *EnhancedOrderCode, err error) {
	// 检查是否是增强版代码
	if strings.HasPrefix(orderCode, "ENHANCED_ORDER_CODE_") {
		code, err := parseEnhancedOrderCodeWithInfo(orderCode)
		if err != nil {
			return "", "", "", "", "", nil, err
		}
		return code.StandardCode, code.OriginalCode, code.Provider, code.ChannelID, code.ProductCode, code, nil
	}

	// 兼容旧版本代码
	if strings.HasPrefix(orderCode, "ORDER_CODE_") {
		expressCode, originalCode, provider, channelID, productCode, err := parseOldOrderCode(orderCode)
		return expressCode, originalCode, provider, channelID, productCode, nil, err
	}

	return "", "", "", "", "", nil, fmt.Errorf("无效的下单代码格式")
}

// 注意：删除了重复的解析函数，统一使用 parseOrderCodeWithValidation

// parseEnhancedOrderCodeWithInfo 解析增强版下单代码并返回完整信息
func parseEnhancedOrderCodeWithInfo(orderCode string) (*EnhancedOrderCode, error) {
	var encodedCode string
	var isEncrypted bool

	// 检查是否是加密版本
	if strings.HasPrefix(orderCode, "ENHANCED_ORDER_CODE_ENCRYPTED_") {
		encodedCode = strings.TrimPrefix(orderCode, "ENHANCED_ORDER_CODE_ENCRYPTED_")
		isEncrypted = true
	} else {
		encodedCode = strings.TrimPrefix(orderCode, "ENHANCED_ORDER_CODE_")
		isEncrypted = false
	}

	// Base64解码
	decodedBytes, err := base64.StdEncoding.DecodeString(encodedCode)
	if err != nil {
		return nil, fmt.Errorf("解码失败: %v", err)
	}

	var jsonData []byte
	if isEncrypted {
		// 解密数据
		jsonData, err = decryptOrderCodeData(decodedBytes)
		if err != nil {
			return nil, fmt.Errorf("解密失败: %v", err)
		}
	} else {
		jsonData = decodedBytes
	}

	// JSON反序列化
	var enhancedCode EnhancedOrderCode
	if err := json.Unmarshal(jsonData, &enhancedCode); err != nil {
		return nil, fmt.Errorf("反序列化失败: %v", err)
	}

	// 🔥 移除过期时间验证：下单代码永久有效
	// 🔥 移除使用状态验证：简化处理逻辑

	return &enhancedCode, nil
}

// parseOldOrderCode 解析旧版本下单代码（向后兼容）
func parseOldOrderCode(orderCode string) (expressCode, originalCode, provider, channelID, productCode string, err error) {
	// 去除前缀
	encodedCode := strings.TrimPrefix(orderCode, "ORDER_CODE_")

	// Base64解码
	decodedBytes, err := base64.StdEncoding.DecodeString(encodedCode)
	if err != nil {
		return "", "", "", "", "", fmt.Errorf("解码失败: %v", err)
	}

	// 解析字段
	// 格式: 标准代码|原始代码|供应商|渠道ID|产品代码
	parts := strings.Split(string(decodedBytes), "|")
	if len(parts) != 5 {
		return "", "", "", "", "", fmt.Errorf("下单代码格式错误")
	}

	return parts[0], parts[1], parts[2], parts[3], parts[4], nil
}

// decryptOrderCodeData 解密订单代码数据
func decryptOrderCodeData(encryptedData []byte) ([]byte, error) {
	// 使用相同的密钥
	key := []byte("go-kuaidi-secret-key-32-bytes!!")

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("创建AES密码器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM模式失败: %v", err)
	}

	nonceSize := gcm.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, fmt.Errorf("加密数据长度不足")
	}

	nonce, ciphertext := encryptedData[:nonceSize], encryptedData[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("解密失败: %v", err)
	}

	return plaintext, nil
}

// validateRequestConsistency 验证下单请求与查价请求的一致性
func validateRequestConsistency(currentReq SimpleOrderRequest, enhancedCode *EnhancedOrderCode) error {
	if enhancedCode.OriginalRequest == nil {
		return nil // 旧版本代码没有原始请求信息，跳过验证
	}

	// 获取原始请求的地址信息，支持SimplePriceRequest和JDPriceRequest
	var originalSenderProvince, originalSenderCity, originalReceiverProvince, originalReceiverCity string

	// 尝试类型断言为SimplePriceRequest
	if simplePriceReq, ok := enhancedCode.OriginalRequest.(*SimplePriceRequest); ok {
		// 优先使用嵌套格式，降级到扁平格式
		if simplePriceReq.Sender.Province != "" {
			// 使用嵌套格式的地址信息
			originalSenderProvince = simplePriceReq.Sender.Province
			originalSenderCity = simplePriceReq.Sender.City
		} else {
			// 降级到扁平格式
			originalSenderProvince = simplePriceReq.FromProvince
			originalSenderCity = simplePriceReq.FromCity
		}

		if simplePriceReq.Receiver.Province != "" {
			// 使用嵌套格式的地址信息
			originalReceiverProvince = simplePriceReq.Receiver.Province
			originalReceiverCity = simplePriceReq.Receiver.City
		} else {
			// 降级到扁平格式
			originalReceiverProvince = simplePriceReq.ToProvince
			originalReceiverCity = simplePriceReq.ToCity
		}
	} else if realtimePriceReq, ok := enhancedCode.OriginalRequest.(*RealtimePriceRequest); ok {
		// RealtimePriceRequest格式
		originalSenderProvince = realtimePriceReq.Sender.Province
		originalSenderCity = realtimePriceReq.Sender.City
		originalReceiverProvince = realtimePriceReq.Receiver.Province
		originalReceiverCity = realtimePriceReq.Receiver.City
	} else {
		// 未知格式，跳过验证
		return nil
	}

	// 验证路线一致性
	if currentReq.SenderProvince != originalSenderProvince ||
		currentReq.SenderCity != originalSenderCity ||
		currentReq.ReceiverProvince != originalReceiverProvince ||
		currentReq.ReceiverCity != originalReceiverCity {
		return fmt.Errorf("寄收地址与查价时不一致")
	}

	// 验证重量一致性（允许小幅差异）
	var originalWeight float64
	if simplePriceReq, ok := enhancedCode.OriginalRequest.(*SimplePriceRequest); ok {
		originalWeight = simplePriceReq.Weight
	} else if realtimePriceReq, ok := enhancedCode.OriginalRequest.(*RealtimePriceRequest); ok {
		originalWeight = realtimePriceReq.Weight
	} else {
		// 未知格式，跳过重量验证
		return nil
	}

	weightDiff := math.Abs(currentReq.Weight - originalWeight)
	if weightDiff > 0.1 { // 允许0.1kg误差
		return fmt.Errorf("重量与查价时不一致: 查价=%.2fkg, 下单=%.2fkg", originalWeight, currentReq.Weight)
	}

	// 🔥 移除价格锁定验证：价格验证将在下单时进行实时验证
	// 如果用户提供了期望价格，仅作为参考，不进行强制验证
	if currentReq.ExpectedPrice > 0 {
		// 仅记录期望价格，实际价格验证在下单时进行
		// 这里不再进行价格一致性验证
	}

	return nil
}

// 转换为内部请求格式
func (h *OrderHandler) convertToInternalOrderRequest(ctx context.Context, req SimpleOrderRequest, expressCode, originalCode, provider, channelID, productCode, userID string) (*model.OrderRequest, error) {
	// 设置默认值
	quantity := req.Quantity
	if quantity <= 0 {
		quantity = 1
	}

	goodsName := req.GoodsName
	if goodsName == "" {
		goodsName = "物品"
	}

	// 🔥 新增：优先使用用户传入的客户订单号，否则自动生成
	var customerOrderNo string
	if req.CustomerOrderNo != "" {
		// 使用用户传入的客户订单号
		customerOrderNo = req.CustomerOrderNo

		// 🔥 新增：检查客户订单号是否已存在
		if err := h.checkCustomerOrderNoUniqueness(ctx, customerOrderNo, userID); err != nil {
			return nil, err
		}
	} else {
		// 生成供应商前缀的客户订单号
		var err error
		customerOrderNo, err = h.orderService.GenerateCustomerOrderNo(ctx, provider)
		if err != nil {
			// 降级方案：使用时间戳
			customerOrderNo = fmt.Sprintf("gk%d", util.NowBeijing().Unix())
		}
	}

	// 🔥 企业级修复：创建包裹信息，包含长宽高，优先使用长宽高计算体积
	volume := req.Volume
	if req.Length > 0 && req.Width > 0 && req.Height > 0 {
		// 长宽高单位是cm，需要转换为m³
		volumeCm3 := req.Length * req.Width * req.Height
		volume = volumeCm3 / 1000000 // cm³ → m³
	}

	// 🔥 关键修复：计算计费重量，确保下单时使用与缓存查询和价格验证相同的重量
	chargedWeight := h.calculateChargedWeight(req, expressCode)

	h.logger.Info("下单体积重量计算",
		zap.String("express_code", expressCode),
		zap.Float64("original_weight", req.Weight),
		zap.Float64("charged_weight", chargedWeight))

	packageInfo := model.PackageInfo{
		Weight:    chargedWeight, // 🔥 关键修复：使用计费重量而不是原始重量
		Volume:    volume,        // 🔥 修复：使用计算后的体积
		Length:    req.Length,    // 🔥 新增：长度
		Width:     req.Width,     // 🔥 新增：宽度
		Height:    req.Height,    // 🔥 新增：高度
		Quantity:  quantity,
		GoodsName: goodsName,
	}

	// 如果有保价金额，添加到包裹信息
	if req.InsureValue > 0 {
		packageInfo.InsureValue = float64(req.InsureValue)
	}

	// 如果有备注，添加到包裹信息
	if req.Remark != "" {
		packageInfo.Remark = req.Remark
	}

	// 🔥 核心修复：自动从缓存中获取价格，不依赖前端传递
	cachedPrice, err := h.getCachedPriceFromBackend(ctx, req, provider, expressCode)
	if err != nil {
		h.logger.Warn("获取缓存价格失败，将使用实时验证",
			zap.String("provider", provider),
			zap.String("express_code", expressCode),
			zap.Error(err))
		cachedPrice = 0 // 设置为0，触发实时验证
	}

	// 创建订单请求
	orderReq := &model.OrderRequest{
		CustomerOrderNo: customerOrderNo,
		UserID:          userID,       // 设置用户ID
		ExpressType:     originalCode, // 使用原始快递公司代码
		ProductType:     productCode,
		ChannelID:       channelID,
		Provider:        provider, // 设置从订单代码中解析出的供应商信息

		// 🔥 核心修复：自动设置价格验证字段（内部逻辑，用户无感知）
		PriceSource:   "cache",     // 自动标记为缓存价格，触发内部价格验证
		CachedPrice:   cachedPrice, // 🔥 使用后端自动获取的缓存价格
		ExpectedPrice: cachedPrice, // 🔥 使用后端自动获取的缓存价格

		Sender: model.SenderInfo{
			Name:     req.SenderName,
			Mobile:   req.SenderMobile,
			Province: req.SenderProvince,
			City:     req.SenderCity,
			District: req.SenderDistrict,
			Address:  req.SenderAddress,
		},
		Receiver: model.ReceiverInfo{
			Name:     req.ReceiverName,
			Mobile:   req.ReceiverMobile,
			Province: req.ReceiverProvince,
			City:     req.ReceiverCity,
			District: req.ReceiverDistrict,
			Address:  req.ReceiverAddress,
		},
		Package:   packageInfo,
		PayMethod: req.PayMethod,
	}

	// 🚀 添加预约时间信息转换
	if req.PickupStartTime != "" && req.PickupEndTime != "" {
		orderReq.Pickup.StartTime = req.PickupStartTime
		orderReq.Pickup.EndTime = req.PickupEndTime

		h.logger.Info("添加预约时间信息",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("pickup_start_time", req.PickupStartTime),
			zap.String("pickup_end_time", req.PickupEndTime))
	}

	// 🔥 新增：支持用户自定义outOrderId（菜鸟裹裹专用）
	if req.OutOrderId != "" {
		orderReq.OutOrderId = req.OutOrderId
		h.logger.Info("添加自定义outOrderId",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("out_order_id", req.OutOrderId))
	}

	return orderReq, nil
}

// getOrderUserIDFromContext 从上下文中获取用户ID（生产级）
func getOrderUserIDFromContext(c *gin.Context) string {
	// 从上下文获取生产级身份解析服务
	if resolver, exists := c.Get("user_identity_resolver"); exists {
		if identityResolver, ok := resolver.(*service.UserIdentityResolver); ok {
			userID, err := identityResolver.ResolveUserID(c)
			if err == nil && userID != "" {
				return userID
			}
			// 记录错误但不中断流程
			fmt.Printf("身份解析失败: %v\n", err)
		}
	}

	// 降级到传统方式（保持向后兼容）
	userIDKeys := []string{"userID", "user_id", "client_id", "client_id_from_signature"}
	for _, key := range userIDKeys {
		if userID, exists := c.Get(key); exists {
			if id, ok := userID.(string); ok && id != "" {
				return id
			}
		}
	}

	return ""
}

// 转换为简化响应格式
func convertToSimpleOrderResponse(resp *model.OrderResponse, expressCode string) *SimpleOrderResponse {
	if !resp.Success {
		return &SimpleOrderResponse{
			Success: resp.Success,
			Code:    resp.Code,
			Message: resp.Message,
		}
	}

	// 标准化快递公司名称
	standardNameMap := map[string]string{
		"ZTO": "中通快递",
		"STO": "申通快递",
		"YTO": "圆通速递",
		"YD":  "韵达速递",
		"JD":  "京东物流",
		"JT":  "极兔快递",
		"SF":  "顺丰速运",
		"EMS": "EMS快递",
	}

	// 获取标准化的快递公司名称
	expressName := ""
	if name, ok := standardNameMap[expressCode]; ok {
		expressName = name
	} else {
		expressName = expressCode // 如果没有标准化名称，使用代码作为名称
	}

	// 设置预计送达时间
	deliveryTime := "预计2-4天送达"

	return &SimpleOrderResponse{
		Success:         resp.Success,
		Code:            resp.Code,
		Message:         resp.Message,
		PlatformOrderNo: resp.Data.PlatformOrderNo,                                               // 🔥 平台订单号
		CustomerOrderNo: resp.Data.CustomerOrderNo,                                               // 🔥 客户订单号
		OrderNo:         getPrimaryOrderNo(resp.Data.PlatformOrderNo, resp.Data.CustomerOrderNo), // 🔥 主要展示字段：平台订单号
		WaybillNo:       resp.Data.TrackingNo,                                                    // 使用TrackingNo字段
		ExpressCode:     expressCode,
		ExpressName:     expressName,
		Price:           fmt.Sprintf("%.2f", resp.Data.Price),
		DeliveryTime:    deliveryTime,
		// 注意：不再返回供应商内部订单号
	}
}

// getPrimaryOrderNo 获取主要订单号（优先返回平台订单号）
func getPrimaryOrderNo(platformOrderNo, customerOrderNo string) string {
	if platformOrderNo != "" {
		return platformOrderNo
	}
	return customerOrderNo
}

// GetOrderList 获取订单列表
// @Summary 获取订单列表
// @Description 获取订单列表，支持分页、过滤和排序
// @Tags 订单
// @Accept json
// @Produce json
// @Param page query int false "页码，从1开始" default(1)
// @Param page_size query int false "每页大小，最大100" default(20)
// @Param status query string false "订单状态过滤"
// @Param express_type query string false "快递类型过滤"
// @Param provider query string false "供应商过滤"
// @Param customer_order_no query string false "客户订单号过滤"
// @Param order_no query string false "平台订单号过滤"
// @Param tracking_no query string false "运单号过滤"
// @Param start_time query string false "开始时间 (YYYY-MM-DD HH:mm:ss)"
// @Param end_time query string false "结束时间 (YYYY-MM-DD HH:mm:ss)"
// @Param sort_by query string false "排序字段: created_at, updated_at, price" default(created_at)
// @Param sort_order query string false "排序方向: asc, desc" default(desc)
// @Success 200 {object} model.OrderListResponse "成功"
// @Failure 400 {object} model.ErrorResponse "请求参数错误"
// @Failure 500 {object} model.ErrorResponse "服务器内部错误"
// @Router /api/v1/express/orders [get]
func (h *OrderHandler) GetOrderList(c *gin.Context) {
	var req model.OrderListRequest

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 🔥 修复：在绑定后处理批量运单号参数，覆盖可能错误的绑定结果
	h.parseBatchTrackingNos(c, &req)

	// 获取当前用户ID
	userID := getOrderUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Success: false,
			Code:    model.StatusUnauthorized,
			Message: "用户身份验证失败",
		})
		return
	}

	// 强制设置用户ID过滤，确保用户只能查看自己的订单
	req.UserID = userID

	// 🔥 新增：记录批量查询请求日志
	if len(req.TrackingNos) > 0 {
		h.logger.Info("批量运单号查询请求",
			zap.String("user_id", userID),
			zap.Int("tracking_nos_count", len(req.TrackingNos)),
			zap.Strings("tracking_nos", req.TrackingNos),
			zap.String("client_ip", c.ClientIP()))
	}

	// 获取订单列表
	resp, err := h.orderService.GetOrderList(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "获取订单列表失败: " + err.Error(),
		})
		return
	}

	// 🔥 修复：处理 timestamp without time zone 的时区问题
	// 订单表使用 timestamp without time zone，需要明确标记为北京时区
	if resp.Success && resp.Data != nil && len(resp.Data.Items) > 0 {
		for _, item := range resp.Data.Items {
			// 对于 timestamp without time zone，需要明确标记为北京时区
			item.CreatedAt = util.EnsureBeijingTimezone(item.CreatedAt)
			item.UpdatedAt = util.EnsureBeijingTimezone(item.UpdatedAt)
		}
	}

	c.JSON(http.StatusOK, resp)
}

// GetOrderStatistics 获取订单统计信息
// @Summary 获取订单统计信息
// @Description 获取当前用户的订单统计信息，包括总数、今日数量、各状态分布等
// @Tags 订单
// @Accept json
// @Produce json
// @Success 200 {object} model.OrderStatisticsResponse "成功"
// @Failure 401 {object} model.ErrorResponse "用户身份验证失败"
// @Failure 500 {object} model.ErrorResponse "服务器内部错误"
// @Router /api/v1/express/orders/statistics [get]
func (h *OrderHandler) GetOrderStatistics(c *gin.Context) {
	// 获取当前用户ID
	userID := getOrderUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Success: false,
			Code:    model.StatusUnauthorized,
			Message: "用户身份验证失败",
		})
		return
	}

	// 获取当前用户的订单统计
	resp, err := h.orderService.GetOrderStatistics(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "获取订单统计失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// getCachedPriceFromBackend 从后端缓存中获取价格
func (h *OrderHandler) getCachedPriceFromBackend(ctx context.Context, req SimpleOrderRequest, provider, expressCode string) (float64, error) {
	// 检查是否有重量档位缓存服务
	if h.weightTierCacheService == nil {
		return 0, fmt.Errorf("重量档位缓存服务未配置")
	}

	// 🔥 核心修复：计算体积重量，确保与实时验证使用相同的计费重量
	chargedWeight := h.calculateChargedWeight(req, expressCode)

	h.logger.Info("缓存查询体积重量计算",
		zap.String("express_code", expressCode),
		zap.Float64("original_weight", req.Weight),
		zap.Float64("charged_weight", chargedWeight))

	// 构建缓存查询请求
	cacheReq := &model.WeightTierCacheRequest{
		FromProvince: req.SenderProvince,
		ToProvince:   req.ReceiverProvince,
		Provider:     provider,
		ExpressCode:  expressCode,
		Weight:       chargedWeight, // 🔥 使用计费重量而不是原始重量
	}

	// 查询缓存
	cacheResp, err := h.weightTierCacheService.QueryPriceWithCache(ctx, cacheReq)
	if err != nil {
		return 0, fmt.Errorf("查询缓存失败: %w", err)
	}

	if !cacheResp.Success || cacheResp.Data == nil {
		return 0, fmt.Errorf("缓存查询失败: %s", cacheResp.Message)
	}

	// 转换 decimal.Decimal 到 float64
	priceFloat, _ := cacheResp.Data.Price.Float64()

	h.logger.Info("成功从后端缓存获取价格",
		zap.String("provider", provider),
		zap.String("express_code", expressCode),
		zap.Float64("original_weight", req.Weight),
		zap.Float64("charged_weight", chargedWeight),
		zap.Float64("cached_price", priceFloat),
		zap.String("source", cacheResp.Source))

	return priceFloat, nil
}

// calculateChargedWeight 计算计费重量（考虑体积重量）
func (h *OrderHandler) calculateChargedWeight(req SimpleOrderRequest, expressCode string) float64 {
	// 如果没有体积信息，直接返回实际重量
	if req.Length <= 0 || req.Width <= 0 || req.Height <= 0 {
		return req.Weight
	}

	// 计算体积（cm³）
	volumeCm3 := req.Length * req.Width * req.Height

	// 🔥 关键修复：从数据库动态获取快递公司的抛比配置
	volumeRatio := h.getVolumeWeightRatio(expressCode)

	// 使用快递公司特定的抛比计算体积重量
	volumeWeight := volumeCm3 / float64(volumeRatio)

	// 返回实际重量和体积重量的较大值
	chargedWeight := req.Weight
	if volumeWeight > req.Weight {
		chargedWeight = volumeWeight
	}

	h.logger.Debug("体积重量计算详情",
		zap.String("express_code", expressCode),
		zap.Float64("actual_weight", req.Weight),
		zap.Float64("volume_cm3", volumeCm3),
		zap.Int("volume_ratio", volumeRatio),
		zap.Float64("volume_weight", volumeWeight),
		zap.Float64("charged_weight", chargedWeight))

	return chargedWeight
}

// getVolumeWeightRatio 获取快递公司的抛比配置
func (h *OrderHandler) getVolumeWeightRatio(expressCode string) int {
	// 🔥 企业级修复：从数据库获取抛比配置，无默认值
	if h.expressCompanyService == nil {
		h.logger.Error("快递公司服务未初始化")
		panic(fmt.Sprintf("快递公司服务未初始化，无法获取 %s 的抛比配置", expressCode))
	}

	ctx := context.Background()
	company, err := h.expressCompanyService.GetCompanyByCode(ctx, expressCode)
	if err != nil {
		h.logger.Error("获取快递公司配置失败",
			zap.String("express_code", expressCode),
			zap.Error(err))
		panic(fmt.Sprintf("快递公司 %s 配置获取失败: %v。请在数据库 express_companies 表中配置正确的快递公司信息", expressCode, err))
	}

	if !company.IsActive {
		h.logger.Error("快递公司已禁用",
			zap.String("express_code", expressCode),
			zap.String("company_name", company.Name))
		panic(fmt.Sprintf("快递公司 %s 已禁用。请在数据库中启用该快递公司或选择其他快递公司", expressCode))
	}

	if company.VolumeWeightRatio <= 0 {
		h.logger.Error("快递公司抛比配置无效",
			zap.String("express_code", expressCode),
			zap.String("company_name", company.Name),
			zap.Int("volume_ratio", company.VolumeWeightRatio))
		panic(fmt.Sprintf("快递公司 %s 抛比配置无效: %d。请在数据库 express_companies 表中配置正确的 volume_weight_ratio 值（建议值：顺丰5000，其他8000）", expressCode, company.VolumeWeightRatio))
	}

	h.logger.Debug("从数据库获取抛比配置成功",
		zap.String("express_code", expressCode),
		zap.String("company_name", company.Name),
		zap.Int("volume_ratio", company.VolumeWeightRatio))

	return company.VolumeWeightRatio
}

// createOrderWithRealTimePriceValidation 使用实时价格验证创建订单
func (h *OrderHandler) createOrderWithRealTimePriceValidation(ctx context.Context, req *model.OrderRequest, enhancedCode *EnhancedOrderCode) (*model.OrderResponse, error) {
	h.logger.Info("开始实时价格验证下单流程",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("provider", enhancedCode.Provider),
		zap.String("standard_code", enhancedCode.StandardCode),
		zap.String("original_code", enhancedCode.OriginalCode),
		zap.String("express_type", req.ExpressType),
		zap.String("price_source", req.PriceSource),
		zap.Float64("cached_price", req.CachedPrice),
		zap.Float64("expected_price", req.ExpectedPrice))

	// 确保供应商信息一致
	if req.Provider == "" {
		req.Provider = enhancedCode.Provider
	}

	// 调用订单服务创建订单（内部会进行价格验证）
	return h.orderService.CreateOrder(ctx, req)
}

// 🔥 新增：解析批量运单号参数，支持多种格式
func (h *OrderHandler) parseBatchTrackingNos(c *gin.Context, req *model.OrderListRequest) {
	var allTrackingNos []string

	// 🔥 修复：首先检查 Gin 是否已经绑定了 TrackingNos，如果有且包含逗号分隔的字符串，需要重新解析
	if len(req.TrackingNos) > 0 {
		for _, trackingNo := range req.TrackingNos {
			// 检查是否包含逗号（说明 Gin 把逗号分隔的字符串当作单个值绑定了）
			if strings.Contains(trackingNo, ",") {
				// 按逗号分割并清理空白字符
				parts := strings.Split(trackingNo, ",")
				for _, part := range parts {
					if trimmed := strings.TrimSpace(part); trimmed != "" {
						allTrackingNos = append(allTrackingNos, trimmed)
					}
				}
			} else {
				// 正常的运单号
				if trimmed := strings.TrimSpace(trackingNo); trimmed != "" {
					allTrackingNos = append(allTrackingNos, trimmed)
				}
			}
		}
	}

	// 方式1: 处理数组格式 tracking_nos[]=value1&tracking_nos[]=value2
	if trackingNosArray := c.QueryArray("tracking_nos[]"); len(trackingNosArray) > 0 {
		for _, trackingNo := range trackingNosArray {
			if trimmed := strings.TrimSpace(trackingNo); trimmed != "" {
				allTrackingNos = append(allTrackingNos, trimmed)
			}
		}
	}

	// 方式2: 处理逗号分隔格式 tracking_nos=value1,value2,value3
	if trackingNosStr := c.Query("tracking_nos"); trackingNosStr != "" {
		// 检查是否包含逗号（逗号分隔格式）
		if strings.Contains(trackingNosStr, ",") {
			// 按逗号分割并清理空白字符
			parts := strings.Split(trackingNosStr, ",")
			for _, part := range parts {
				if trimmed := strings.TrimSpace(part); trimmed != "" {
					allTrackingNos = append(allTrackingNos, trimmed)
				}
			}
		} else {
			// 单个运单号
			if trimmed := strings.TrimSpace(trackingNosStr); trimmed != "" {
				allTrackingNos = append(allTrackingNos, trimmed)
			}
		}
	}

	// 去重处理
	if len(allTrackingNos) > 0 {
		uniqueTrackingNos := make(map[string]bool)
		var deduplicatedTrackingNos []string

		for _, trackingNo := range allTrackingNos {
			if !uniqueTrackingNos[trackingNo] {
				uniqueTrackingNos[trackingNo] = true
				deduplicatedTrackingNos = append(deduplicatedTrackingNos, trackingNo)
			}
		}

		req.TrackingNos = deduplicatedTrackingNos

		// 🔥 重要：清空单个运单号字段，避免与批量查询冲突
		req.TrackingNo = ""
	}
}
