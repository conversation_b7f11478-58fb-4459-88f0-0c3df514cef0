package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// DocHandler 文档处理器
type DocHandler struct {
}

// NewDocHandler 创建文档处理器
func NewDocHandler() *DocHandler {
	return &DocHandler{}
}

// HandleDocIndex 处理文档首页
// @Summary 文档首页
// @Description API文档首页
// @Tags 文档
// @Accept html
// @Produce html
// @Success 200 {string} string "成功"
// @Router /docs [get]
func (h *DocHandler) HandleDocIndex(c *gin.Context) {
	c.HTML(http.StatusOK, "doc_index.html", gin.H{
		"title": "Go-Kuaidi API 文档",
	})
}

// HandleChineseDoc 处理中文文档
// @Summary 中文文档
// @Description API中文文档
// @Tags 文档
// @Accept html
// @Produce html
// @Success 200 {string} string "成功"
// @Router /docs/zh-cn [get]
func (h *DocHandler) HandleChineseDoc(c *gin.Context) {
	c.HTML(http.StatusOK, "doc_zh_cn.html", gin.H{
		"title": "Go-Kuaidi API 中文文档",
	})
}
