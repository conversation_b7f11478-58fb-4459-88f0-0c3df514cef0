package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/express"
	"go.uber.org/zap"
)

// UpdateProvider 更新供应商
func (h *ExpressCompanyHandler) UpdateProvider(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.<PERSON>(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	providerID := c.Param("id")
	if providerID == "" {
		h.logger.Warn("Provider ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "供应商ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析请求参数
	var req express.UpdateProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	provider, err := h.service.UpdateProvider(c.Request.Context(), providerID, req, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to update express provider",
			zap.String("request_id", requestID),
			zap.String("provider_id", providerID),
			zap.String("operator_id", operatorIDStr),
			zap.Any("request", req),
			zap.Error(err))

		if err.Error() == "供应商不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Express provider updated successfully",
		zap.String("request_id", requestID),
		zap.String("provider_id", providerID),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "供应商更新成功",
		"data":    provider,
	})
}

// DeleteProvider 删除供应商
func (h *ExpressCompanyHandler) DeleteProvider(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	providerID := c.Param("id")
	if providerID == "" {
		h.logger.Warn("Provider ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "供应商ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	err := h.service.DeleteProvider(c.Request.Context(), providerID, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to delete express provider",
			zap.String("request_id", requestID),
			zap.String("provider_id", providerID),
			zap.String("operator_id", operatorIDStr),
			zap.Error(err))

		if err.Error() == "供应商不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Express provider deleted successfully",
		zap.String("request_id", requestID),
		zap.String("provider_id", providerID),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "供应商删除成功",
	})
}

// GetActiveCompanies 获取启用的快递公司列表（普通用户接口）
func (h *ExpressCompanyHandler) GetActiveCompanies(c *gin.Context) {
	requestID := getRequestID(c)

	// 构建过滤条件：只获取启用的快递公司
	filter := express.CompanyFilter{
		IsActive:  &[]bool{true}[0], // 只获取启用的
		SortBy:    "sort_order",
		SortOrder: "DESC",
	}

	pagination := express.Pagination{
		Page:     1,
		PageSize: 100, // 获取所有启用的快递公司
	}

	// 调用服务层
	result, err := h.service.GetCompanies(c.Request.Context(), filter, pagination)
	if err != nil {
		h.logger.Error("Failed to get active express companies",
			zap.String("request_id", requestID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    result.Companies,
	})
}

// GetCompanyByCode 根据代码获取快递公司（普通用户接口）
func (h *ExpressCompanyHandler) GetCompanyByCode(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	companyCode := c.Param("code")
	if companyCode == "" {
		h.logger.Warn("Company code is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司代码不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	company, err := h.service.GetCompanyByCode(c.Request.Context(), companyCode)
	if err != nil {
		h.logger.Error("Failed to get express company by code",
			zap.String("request_id", requestID),
			zap.String("company_code", companyCode),
			zap.Error(err))

		if err.Error() == "快递公司不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	// 检查是否启用
	if !company.IsActive {
		businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, "快递公司未启用")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    company,
	})
}

// GetActiveProviders 获取启用的供应商列表（普通用户接口）
func (h *ExpressCompanyHandler) GetActiveProviders(c *gin.Context) {
	requestID := getRequestID(c)

	// 构建过滤条件：只获取启用的供应商
	filter := express.ProviderFilter{
		IsActive:  &[]bool{true}[0], // 只获取启用的
		SortBy:    "priority",
		SortOrder: "DESC",
	}

	pagination := express.Pagination{
		Page:     1,
		PageSize: 100, // 获取所有启用的供应商
	}

	// 调用服务层
	result, err := h.service.GetProviders(c.Request.Context(), filter, pagination)
	if err != nil {
		h.logger.Error("Failed to get active express providers",
			zap.String("request_id", requestID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    result.Providers,
	})
}

// GetProviderByCode 根据代码获取供应商（普通用户接口）
func (h *ExpressCompanyHandler) GetProviderByCode(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	providerCode := c.Param("code")
	if providerCode == "" {
		h.logger.Warn("Provider code is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "供应商代码不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	provider, err := h.service.GetProviderByCode(c.Request.Context(), providerCode)
	if err != nil {
		h.logger.Error("Failed to get express provider by code",
			zap.String("request_id", requestID),
			zap.String("provider_code", providerCode),
			zap.Error(err))

		if err.Error() == "供应商不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	// 供应商启用状态检查已移除 - 现在通过system_configs.provider_{code}.enabled管理
	// 如果需要检查供应商状态，应该通过ProviderConfigService.IsProviderEnabled()方法
	// if !provider.IsActive {
	//     businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, "供应商未启用")
	//     c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
	//     return
	// }

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    provider,
	})
}
