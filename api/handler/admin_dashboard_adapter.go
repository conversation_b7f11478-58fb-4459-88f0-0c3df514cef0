package handler

import (
	"context"

	"github.com/your-org/go-kuaidi/internal/service"
)

// AdminBalanceServiceAdapter 管理员余额服务适配器
type AdminBalanceServiceAdapter struct {
	service service.AdminBalanceService
}

// NewAdminBalanceServiceAdapter 创建管理员余额服务适配器
func NewAdminBalanceServiceAdapter(service service.AdminBalanceService) AdminBalanceServiceInterface {
	return &AdminBalanceServiceAdapter{
		service: service,
	}
}

// GetBalanceOverview 获取余额总览
func (a *AdminBalanceServiceAdapter) GetBalanceOverview(ctx context.Context) (*AdminBalanceOverview, error) {
	overview, err := a.service.GetBalanceOverview(ctx)
	if err != nil {
		return nil, err
	}

	// 转换数据结构，处理decimal.Decimal到float64的转换
	totalBalance, _ := overview.TotalBalance.Float64()
	avgBalance, _ := overview.AvgBalance.Float64()

	return &AdminBalanceOverview{
		TotalUsers:         overview.TotalUsers,
		ActiveUsers:        overview.ActiveUsers,
		InactiveUsers:      overview.InactiveUsers,
		TotalBalance:       totalBalance,
		AvgBalance:         avgBalance,
		RecentTransactions: overview.RecentTransactions,
	}, nil
}

// GetBalanceStatistics 获取余额统计
func (a *AdminBalanceServiceAdapter) GetBalanceStatistics(ctx context.Context) (*BalanceStatistics, error) {
	stats, err := a.service.GetBalanceStatistics(ctx)
	if err != nil {
		return nil, err
	}

	// 转换数据结构，处理decimal.Decimal到float64的转换
	totalBalance, _ := stats.TotalBalance.Float64()
	avgBalance, _ := stats.AvgBalance.Float64()

	return &BalanceStatistics{
		TotalBalance:     totalBalance,
		TotalUsers:       stats.TotalUsers,
		ActiveUsers:      stats.ActiveUsers,
		AvgBalance:       avgBalance,
		TodayDeposits:    0.0, // 这些字段在model.BalanceStatistics中不存在，设为默认值
		TodayConsumption: 0.0,
		LowBalanceUsers:  stats.ZeroBalanceUsers, // 使用ZeroBalanceUsers作为替代
	}, nil
}

// AdminOrderServiceAdapter 管理员订单服务适配器
type AdminOrderServiceAdapter struct {
	service service.AdminOrderServiceInterface
}

// NewAdminOrderServiceAdapter 创建管理员订单服务适配器
func NewAdminOrderServiceAdapter(service service.AdminOrderServiceInterface) AdminOrderServiceInterface {
	if service == nil {
		panic("AdminOrderService cannot be nil")
	}
	return &AdminOrderServiceAdapter{
		service: service,
	}
}

// GetAdminOrderStatistics 获取管理员订单统计
func (a *AdminOrderServiceAdapter) GetAdminOrderStatistics(ctx context.Context, filter *AdminStatisticsFilter) (*AdminOrderStatistics, error) {
	if a.service == nil {
		panic("AdminOrderService is nil in adapter")
	}
	// 创建一个空的过滤器而不是传nil
	emptyFilter := &service.AdminStatisticsFilter{}
	stats, err := a.service.GetAdminOrderStatistics(ctx, emptyFilter)
	if err != nil {
		return nil, err
	}

	// 转换数据结构，根据实际的model.AdminOrderStatistics字段
	// 从StatusCounts中提取特定状态的订单数
	var pendingOrders, completedOrders, cancelledOrders int64
	if stats.StatusCounts != nil {
		pendingOrders = stats.StatusCounts["pending"] + stats.StatusCounts["submitted"] + stats.StatusCounts["assigned"]
		completedOrders = stats.StatusCounts["delivered"]
		cancelledOrders = stats.StatusCounts["cancelled"]
	}

	return &AdminOrderStatistics{
		TotalOrders:     stats.TotalOrders,
		TodayOrders:     stats.TodayOrders,
		PendingOrders:   pendingOrders,
		CompletedOrders: completedOrders,
		CancelledOrders: cancelledOrders,
		RevenueToday:    stats.TodayAmount, // 使用TodayAmount作为今日收入
		RevenueMonth:    stats.TotalAmount, // 使用TotalAmount作为月收入
	}, nil
}
