package handler

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/express"
	"go.uber.org/zap"
)

// ExpressMappingHandler 快递公司映射处理器
type ExpressMappingHandler struct {
	mappingService express.ExpressMappingService
	logger         *zap.Logger
}

// NewExpressMappingHandler 创建快递公司映射处理器
func NewExpressMappingHandler(mappingService express.ExpressMappingService, logger *zap.Logger) *ExpressMappingHandler {
	return &ExpressMappingHandler{
		mappingService: mappingService,
		logger:         logger,
	}
}

// GetSupportedCompanies 获取供应商支持的快递公司列表
func (h *ExpressMappingHandler) GetSupportedCompanies(c *gin.Context) {
	providerCode := c.Param("provider_code")
	if providerCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    400,
			"message": "供应商代码不能为空",
		})
		return
	}

	companies, err := h.mappingService.GetSupportedCompanies(c.Request.Context(), providerCode)
	if err != nil {
		h.logger.Error("获取支持的快递公司失败",
			zap.String("provider_code", providerCode),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    500,
			"message": "获取支持的快递公司失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    companies,
	})
}

// GetCompanyMapping 获取快递公司映射信息
func (h *ExpressMappingHandler) GetCompanyMapping(c *gin.Context) {
	companyCode := c.Param("company_code")
	providerCode := c.Param("provider_code")

	if companyCode == "" || providerCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    400,
			"message": "快递公司代码和供应商代码不能为空",
		})
		return
	}

	mapping, err := h.mappingService.GetCompanyMapping(c.Request.Context(), companyCode, providerCode)
	if err != nil {
		h.logger.Error("获取快递公司映射失败",
			zap.String("company_code", companyCode),
			zap.String("provider_code", providerCode),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    500,
			"message": "获取快递公司映射失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    mapping,
	})
}

// GetProviderCompanyCode 获取供应商特定的快递公司代码
func (h *ExpressMappingHandler) GetProviderCompanyCode(c *gin.Context) {
	companyCode := c.Param("company_code")
	providerCode := c.Param("provider_code")

	if companyCode == "" || providerCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    400,
			"message": "快递公司代码和供应商代码不能为空",
		})
		return
	}

	providerCompanyCode, err := h.mappingService.GetProviderCompanyCode(c.Request.Context(), companyCode, providerCode)
	if err != nil {
		h.logger.Error("获取供应商快递公司代码失败",
			zap.String("company_code", companyCode),
			zap.String("provider_code", providerCode),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    500,
			"message": "获取供应商快递公司代码失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"company_code":          companyCode,
			"provider_code":         providerCode,
			"provider_company_code": providerCompanyCode,
		},
	})
}

// GetPreferredProvider 获取快递公司的首选供应商
func (h *ExpressMappingHandler) GetPreferredProvider(c *gin.Context) {
	companyCode := c.Param("company_code")
	if companyCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    400,
			"message": "快递公司代码不能为空",
		})
		return
	}

	provider, err := h.mappingService.GetPreferredProvider(c.Request.Context(), companyCode)
	if err != nil {
		h.logger.Error("获取首选供应商失败",
			zap.String("company_code", companyCode),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    500,
			"message": "获取首选供应商失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    provider,
	})
}

// GetVolumeWeightRatio 获取体积重量系数
func (h *ExpressMappingHandler) GetVolumeWeightRatio(c *gin.Context) {
	companyCode := c.Param("company_code")
	if companyCode == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    400,
			"message": "快递公司代码不能为空",
		})
		return
	}

	ratio, err := h.mappingService.GetVolumeWeightRatio(c.Request.Context(), companyCode)
	if err != nil {
		h.logger.Error("获取体积重量系数失败",
			zap.String("company_code", companyCode),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    500,
			"message": "获取体积重量系数失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"company_code":        companyCode,
			"volume_weight_ratio": ratio,
		},
	})
}

// RefreshCache 刷新映射缓存
func (h *ExpressMappingHandler) RefreshCache(c *gin.Context) {
	err := h.mappingService.RefreshCache(context.Background())
	if err != nil {
		h.logger.Error("刷新映射缓存失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    500,
			"message": "刷新映射缓存失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "映射缓存刷新成功",
	})
}
