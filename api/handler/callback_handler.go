package handler

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service/callback"
	"github.com/your-org/go-kuaidi/internal/user"
	"github.com/your-org/go-kuaidi/internal/util"
)

// CallbackHandler 回调处理器
type CallbackHandler struct {
	callbackService *callback.UnifiedCallbackService
	logger          *zap.Logger
}

// NewCallbackHandler 创建回调处理器
func NewCallbackHandler(callbackService *callback.UnifiedCallbackService, logger *zap.Logger) *CallbackHandler {
	return &CallbackHandler{
		callbackService: callbackService,
		logger:          logger,
	}
}

// GetCallbackService 获取回调服务（用于路由器访问）
func (h *CallbackHandler) GetCallbackService() *callback.UnifiedCallbackService {
	return h.callbackService
}

// GetLogger 获取日志器（用于路由器访问）
func (h *CallbackHandler) GetLogger() *zap.Logger {
	return h.logger
}

// getUserIDFromContext 从上下文中获取用户ID的辅助方法
func (h *CallbackHandler) getUserIDFromContext(c *gin.Context) string {
	// 首先尝试从认证中间件获取用户ID
	userID := c.GetString("userID")
	if userID != "" {
		return userID
	}

	h.logger.Warn("用户ID未找到，尝试从客户端ID获取")
	// 如果没有userID，尝试从client_id获取对应的用户
	clientID := c.GetString("client_id")
	if clientID == "" {
		h.logger.Error("客户端ID未找到")
		return ""
	}

	// 从用户存储库获取用户信息
	userRepo, exists := c.Get("user_repository")
	if !exists {
		h.logger.Error("用户存储库未找到")
		return ""
	}

	userRepository := userRepo.(user.UserRepository)
	user, err := userRepository.FindByClientID(clientID)
	if err != nil || user == nil {
		h.logger.Error("根据客户端ID查找用户失败",
			zap.String("client_id", clientID),
			zap.Error(err))
		return ""
	}

	h.logger.Info("成功获取用户ID",
		zap.String("user_id", user.ID),
		zap.String("client_id", clientID))
	return user.ID
}

// decodeRawDataForAdmin 为管理员解码原始数据
func (h *CallbackHandler) decodeRawDataForAdmin(records []*model.UnifiedCallbackRecord) {
	for _, record := range records {
		if len(record.RawData) > 0 {
			// 解析JSON数据
			var rawDataMap map[string]interface{}
			if err := json.Unmarshal(record.RawData, &rawDataMap); err == nil {
				// 检查是否有raw_content字段需要解码
				if rawContent, ok := rawDataMap["raw_content"].(string); ok {
					// 尝试URL解码
					if decoded, err := url.QueryUnescape(rawContent); err == nil {
						// 如果解码成功，更新raw_content
						rawDataMap["raw_content"] = decoded
						// 重新序列化为JSON
						if updatedJSON, err := json.Marshal(rawDataMap); err == nil {
							record.RawData = updatedJSON
						}
					}
				}
			}
		}
	}
}

// HandleProviderCallback 处理供应商回调
func (h *CallbackHandler) HandleProviderCallback(provider string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 读取请求体
		body, err := io.ReadAll(c.Request.Body)
		if err != nil {
			h.logger.Error("读取请求体失败", zap.Error(err))
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "读取请求体失败",
			})
			return
		}

		// 获取请求头
		headers := make(map[string]string)
		for key, values := range c.Request.Header {
			if len(values) > 0 {
				headers[key] = values[0]
			}
		}

		// 记录请求信息
		h.logger.Info("接收到供应商回调",
			zap.String("provider", provider),
			zap.String("remote_ip", c.ClientIP()),
			zap.String("user_agent", c.GetHeader("User-Agent")),
			zap.Int("body_size", len(body)))

		// 处理回调
		response, err := h.callbackService.ProcessCallback(c.Request.Context(), provider, body, headers)
		if err != nil {
			h.logger.Error("处理回调失败",
				zap.String("provider", provider),
				zap.Error(err))

			// 返回错误响应，但仍然是200状态码，避免供应商重复推送
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": err.Error(),
			})
			return
		}

		// 🔥 企业级修复：优化响应头，减少云通重复推送
		c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		c.Header("Pragma", "no-cache")
		c.Header("Expires", "0")
		c.Header("Connection", "close") // 确保连接关闭，避免连接复用问题

		// 🔥 企业级修复：根据DirectReturn标记决定响应格式
		if response.DirectReturn {
			// 直接返回Data内容，不包装（云通等供应商要求的格式）
			c.JSON(http.StatusOK, response.Data)
		} else {
			// 返回包装后的响应格式
			c.JSON(http.StatusOK, response)
		}
	}
}

// GetUserCallbackConfig 获取用户回调配置
func (h *CallbackHandler) GetUserCallbackConfig(c *gin.Context) {
	userID := h.getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 获取用户回调配置
	config, err := h.callbackService.GetUserCallbackConfig(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("获取用户回调配置失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取回调配置失败",
		})
		return
	}

	// 如果配置不存在，返回默认配置
	if config == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"callback_url":      "",
				"enabled":           false,
				"retry_count":       3,
				"timeout_seconds":   30,
				"subscribed_events": []string{},
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// UpdateUserCallbackConfig 更新用户回调配置
func (h *CallbackHandler) UpdateUserCallbackConfig(c *gin.Context) {
	userID := h.getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	var req struct {
		CallbackURL      string   `json:"callback_url" binding:"required"`
		CallbackSecret   string   `json:"callback_secret"`
		Enabled          bool     `json:"enabled"`
		RetryCount       int      `json:"retry_count"`
		TimeoutSeconds   int      `json:"timeout_seconds"`
		SubscribedEvents []string `json:"subscribed_events"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证回调URL格式
	if req.CallbackURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "回调URL不能为空",
		})
		return
	}

	// 设置默认值
	if req.RetryCount <= 0 {
		req.RetryCount = 3
	}
	if req.TimeoutSeconds <= 0 {
		req.TimeoutSeconds = 30
	}
	if len(req.SubscribedEvents) == 0 {
		req.SubscribedEvents = []string{
			model.EventTypeOrderStatusChanged,
			model.EventTypeBillingUpdated,
			model.EventTypeTicketReplied,
		}
	}

	// 创建配置对象
	config := &model.UserCallbackConfig{
		UserID:           userID,
		CallbackURL:      req.CallbackURL,
		CallbackSecret:   req.CallbackSecret,
		Enabled:          req.Enabled,
		RetryCount:       req.RetryCount,
		TimeoutSeconds:   req.TimeoutSeconds,
		SubscribedEvents: req.SubscribedEvents,
	}

	// 尝试更新配置，如果不存在则创建
	err := h.callbackService.UpdateUserCallbackConfig(c.Request.Context(), config)
	if err != nil {
		// 如果更新失败（可能是配置不存在），尝试创建新配置
		if err.Error() == "用户回调配置不存在" {
			err = h.callbackService.SaveUserCallbackConfig(c.Request.Context(), config)
			if err != nil {
				h.logger.Error("保存用户回调配置失败", zap.Error(err))
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "保存回调配置失败",
				})
				return
			}
		} else {
			h.logger.Error("更新用户回调配置失败", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "更新回调配置失败",
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "回调配置更新成功",
	})
}

// GetUserCallbackRecords 获取用户回调记录
// 🔥 优化：支持状态细分筛选和去除缓存
func (h *CallbackHandler) GetUserCallbackRecords(c *gin.Context) {
	// 从认证中间件获取用户ID
	userID := h.getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	// 解析筛选参数
	filters := map[string]string{
		"tracking_no": c.Query("tracking_no"),
		"event_type":  c.Query("event_type"),
		"status":      c.Query("status"),
		"start_time":  c.Query("start_time"),
		"end_time":    c.Query("end_time"),
	}

	// 🔥 新增：检查是否强制刷新（去除缓存）
	forceRefresh := c.Query("_t") != ""

	// 获取增强的转发记录（包含更多用户需要的信息）
	records, err := h.callbackService.GetEnhancedForwardRecordsWithFilters(c.Request.Context(), userID, pageSize, offset, filters, forceRefresh)
	if err != nil {
		h.logger.Error("获取用户转发记录失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取转发记录失败",
		})
		return
	}

	// 获取转发记录总数
	totalCount, err := h.callbackService.GetForwardRecordsCountWithFilters(c.Request.Context(), userID, filters)
	if err != nil {
		h.logger.Error("获取转发记录总数失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取转发记录总数失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"records":   records,
			"total":     totalCount,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetCallbackStatistics 获取回调统计
func (h *CallbackHandler) GetCallbackStatistics(c *gin.Context) {
	// 从认证中间件获取用户ID
	userID := h.getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 解析时间范围参数
	startTimeStr := c.DefaultQuery("start_time", "")
	endTimeStr := c.DefaultQuery("end_time", "")

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse("2006-01-02", startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "开始时间格式错误，请使用 YYYY-MM-DD 格式",
			})
			return
		}
	} else {
		// 默认查询最近30天
		startTime = util.NowBeijing().AddDate(0, 0, -30)
	}

	if endTimeStr != "" {
		endTime, err = time.Parse("2006-01-02", endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "结束时间格式错误，请使用 YYYY-MM-DD 格式",
			})
			return
		}
		// 设置为当天结束时间
		endTime = endTime.Add(24*time.Hour - time.Second)
	} else {
		endTime = util.NowBeijing()
	}

	// 获取转发记录统计信息（用户应该看到的是转发给他们的回调统计）
	stats, err := h.callbackService.GetForwardRecordStatistics(c.Request.Context(), userID, startTime, endTime)
	if err != nil {
		h.logger.Error("获取转发记录统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取统计信息失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// TestUserCallback 测试用户回调
func (h *CallbackHandler) TestUserCallback(c *gin.Context) {
	userID := h.getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 这里可以实现测试回调的逻辑
	// 发送一个测试回调到用户配置的URL
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "测试回调已发送",
	})
}

// RetryCallback 重试回调处理（管理员功能）
func (h *CallbackHandler) RetryCallback(c *gin.Context) {
	recordIDStr := c.Param("id")
	recordID, err := uuid.Parse(recordIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "回调记录ID格式错误",
		})
		return
	}

	err = h.callbackService.RetryCallback(c.Request.Context(), recordID)
	if err != nil {
		h.logger.Error("重试回调失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "重试回调失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "回调重试已启动",
	})
}

// 🔥 新增：用户端回调重推功能
// RetryUserCallback 重试用户自己的回调记录
func (h *CallbackHandler) RetryUserCallback(c *gin.Context) {
	userID := h.getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	recordIDStr := c.Param("id")
	recordID, err := uuid.Parse(recordIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "回调记录ID格式错误",
		})
		return
	}

	// 🔥 修复：前端传递的是转发记录ID，需要先获取转发记录，再找到对应的回调记录
	forwardRecord, err := h.callbackService.GetForwardRecord(c.Request.Context(), recordID)
	if err != nil {
		h.logger.Error("获取转发记录失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取转发记录失败",
		})
		return
	}

	// 验证转发记录是否属于当前用户
	if forwardRecord.UserID != userID {
		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"message": "无权限操作此回调记录",
		})
		return
	}

	// 通过转发记录获取对应的回调记录
	record, err := h.callbackService.GetCallbackRecord(c.Request.Context(), forwardRecord.CallbackRecordID.String())
	if err != nil {
		h.logger.Error("获取回调记录失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取回调记录失败",
		})
		return
	}

	// 检查转发记录的重推次数限制（防止滥用）
	if forwardRecord.RetryCount >= 10 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "回调重试次数已达上限（10次）",
		})
		return
	}

	// 使用回调记录ID进行重推
	err = h.callbackService.RetryCallback(c.Request.Context(), forwardRecord.CallbackRecordID)
	if err != nil {
		h.logger.Error("用户重试回调失败",
			zap.String("user_id", userID),
			zap.String("forward_record_id", recordID.String()),
			zap.String("callback_record_id", forwardRecord.CallbackRecordID.String()),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "重试回调失败: " + err.Error(),
		})
		return
	}

	// 记录审计日志
	h.logger.Info("用户重推回调",
		zap.String("user_id", userID),
		zap.String("record_id", recordID.String()),
		zap.String("order_no", record.OrderNo),
		zap.String("tracking_no", record.TrackingNo),
		zap.Int("retry_count", record.RetryCount+1))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "回调重试已启动",
		"data": gin.H{
			"record_id":   recordID.String(),
			"retry_count": record.RetryCount + 1,
		},
	})
}

// BatchRetryUserCallbacks 批量重试用户自己的回调记录
func (h *CallbackHandler) BatchRetryUserCallbacks(c *gin.Context) {
	userID := h.getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 解析请求体
	var req struct {
		RecordIDs []string `json:"record_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 限制批量操作数量（防止滥用）
	if len(req.RecordIDs) > 50 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "批量操作数量不能超过50条",
		})
		return
	}

	var successCount int
	var failedRecords []gin.H

	for _, recordIDStr := range req.RecordIDs {
		recordID, err := uuid.Parse(recordIDStr)
		if err != nil {
			failedRecords = append(failedRecords, gin.H{
				"record_id": recordIDStr,
				"error":     "ID格式错误",
			})
			continue
		}

		// 🔥 修复：获取转发记录而不是回调记录
		forwardRecord, err := h.callbackService.GetForwardRecord(c.Request.Context(), recordID)
		if err != nil {
			failedRecords = append(failedRecords, gin.H{
				"record_id": recordIDStr,
				"error":     "获取转发记录失败",
			})
			continue
		}

		// 验证转发记录是否属于当前用户
		if forwardRecord.UserID != userID {
			failedRecords = append(failedRecords, gin.H{
				"record_id": recordIDStr,
				"error":     "无权限操作此记录",
			})
			continue
		}

		// 检查转发记录的重推次数限制
		if forwardRecord.RetryCount >= 10 {
			failedRecords = append(failedRecords, gin.H{
				"record_id": recordIDStr,
				"error":     "重试次数已达上限",
			})
			continue
		}

		// 使用回调记录ID执行重推
		err = h.callbackService.RetryCallback(c.Request.Context(), forwardRecord.CallbackRecordID)
		if err != nil {
			failedRecords = append(failedRecords, gin.H{
				"record_id": recordIDStr,
				"error":     "重试失败: " + err.Error(),
			})
			continue
		}

		successCount++
	}

	// 记录审计日志
	h.logger.Info("用户批量重推回调",
		zap.String("user_id", userID),
		zap.Int("total_count", len(req.RecordIDs)),
		zap.Int("success_count", successCount),
		zap.Int("failed_count", len(failedRecords)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": fmt.Sprintf("批量重试完成，成功%d条，失败%d条", successCount, len(failedRecords)),
		"data": gin.H{
			"total_count":    len(req.RecordIDs),
			"success_count":  successCount,
			"failed_count":   len(failedRecords),
			"failed_records": failedRecords,
		},
	})
}

// GetCallbackRecords 获取回调记录（管理员功能）
func (h *CallbackHandler) GetCallbackRecords(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	userID := c.Query("user_id")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize

	// 获取回调记录
	records, err := h.callbackService.GetCallbackRecords(c.Request.Context(), userID, pageSize, offset)
	if err != nil {
		h.logger.Error("获取回调记录失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取回调记录失败",
		})
		return
	}

	// 获取总记录数
	totalCount, err := h.callbackService.GetCallbackRecordsCount(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("获取回调记录总数失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取回调记录总数失败",
		})
		return
	}

	// 管理员可以看到解码后的原始数据
	h.decodeRawDataForAdmin(records)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"records":   records,
			"total":     totalCount,
			"page":      page,
			"page_size": pageSize,
		},
	})
}

// GetAdminCallbackStatistics 获取管理员回调统计（全局统计）
func (h *CallbackHandler) GetAdminCallbackStatistics(c *gin.Context) {
	// 解析时间范围参数
	startTimeStr := c.DefaultQuery("start_time", "")
	endTimeStr := c.DefaultQuery("end_time", "")
	provider := c.DefaultQuery("provider", "")
	eventType := c.DefaultQuery("event_type", "")
	userID := c.DefaultQuery("user_id", "")

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse("2006-01-02 15:04:05", startTimeStr)
		if err != nil {
			// 尝试解析日期格式
			startTime, err = time.Parse("2006-01-02", startTimeStr)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"message": "开始时间格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss 格式",
				})
				return
			}
		}
	} else {
		// 默认查询最近30天
		startTime = util.NowBeijing().AddDate(0, 0, -30)
	}

	if endTimeStr != "" {
		endTime, err = time.Parse("2006-01-02 15:04:05", endTimeStr)
		if err != nil {
			// 尝试解析日期格式
			endTime, err = time.Parse("2006-01-02", endTimeStr)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"message": "结束时间格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:mm:ss 格式",
				})
				return
			}
			// 设置为当天结束时间
			endTime = endTime.Add(24*time.Hour - time.Second)
		}
	} else {
		endTime = util.NowBeijing()
	}

	// 获取管理员统计信息（传空字符串表示查询所有用户）
	adminUserID := ""
	if userID != "" {
		adminUserID = userID // 如果指定了用户ID，则只查询该用户的统计
	}

	stats, err := h.callbackService.GetAdminCallbackStatistics(c.Request.Context(), adminUserID, startTime, endTime, provider, eventType)
	if err != nil {
		h.logger.Error("获取管理员回调统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取统计信息失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}
