package handler

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"
)

// SimplePriceHandler 简化价格查询处理器
type SimplePriceHandler struct {
	priceService      service.PriceServiceInterface // 🚀 修改为接口类型，支持EnhancedPriceService
	mappingService    express.ExpressMappingService
	expressRepository express.ExpressCompanyRepository
}

// NewSimplePriceHandler 创建简化价格查询处理器
func NewSimplePriceHandler(priceService service.PriceServiceInterface, mappingService express.ExpressMappingService, expressRepository express.ExpressCompanyRepository) *SimplePriceHandler {
	return &SimplePriceHandler{
		priceService:      priceService,
		mappingService:    mappingService,
		expressRepository: expressRepository,
	}
}

// 使用共享的类型定义（定义在types.go中）

// QuerySimplePrice API查价接口
// @Summary API查价接口
// @Description API查价接口：实时调用第三方API查询最新价格，数据准确性高，适合实时查价需求。只需提供寄收地址和包裹重量，即可获取所有供应商支持的快递公司价格和下单参数。系统会自动处理各供应商之间的差异，并返回统一格式的结果。
// @Description 返回结果中的快递公司代码已标准化为统一格式，例如：ZTO(中通)、STO(申通)、YTO(圆通)、YD(韵达)、JD(京东)、JT(极兔)、DBL(德邦)、SF(顺丰)、EMS(EMS)。
// @Description 系统会自动去重并按价格从低到高排序，对于同一快递公司的多个价格，只保留最低价格的那个。
// @Description 返回的order_code是一个统一下单代码，用于调用/api/v1/express/order接口创建订单，系统会自动处理供应商差异。
// @Description 对于德邦物流(DBL)，有以下限制：
// @Description 1. 标准快递只支持2.49公斤以下的包裹
// @Description 2. 当包裹重量超过3KG时，系统会自动转换为德邦大件360服务
// @Description 3. 德邦大件360服务可能在某些区域不提供服务，此时会返回相应的错误信息
// @Tags 价格
// @Accept json
// @Produce json
// @Param request body SimplePriceRequest true "价格查询请求"
// @Success 200 {object} SimplePriceResponse "成功"
// @Failure 400 {object} model.ErrorResponse "请求参数错误"
// @Failure 500 {object} model.ErrorResponse "服务器内部错误"
// @Router /api/v1/express/price [post]
func (h *SimplePriceHandler) QuerySimplePrice(c *gin.Context) {
	var req SimplePriceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证必填参数
	if err := validateSimplePriceRequest(req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
		})
		return
	}

	// 转换为内部请求格式
	internalReq := convertToInternalPriceRequest(req)

	// 查询价格
	resp, err := h.priceService.QueryPrice(c.Request.Context(), internalReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "查询价格失败: " + err.Error(),
		})
		return
	}

	// 转换为简化响应格式
	simpleResp := h.ConvertToSimplePriceResponse(resp, &req)

	c.JSON(http.StatusOK, simpleResp)
}

// 验证简化价格查询请求
func validateSimplePriceRequest(req SimplePriceRequest) error {
	// 兼容两种写法：扁平字段或嵌套 sender/receiver
	fromProvince := req.FromProvince
	fromCity := req.FromCity
	toProvince := req.ToProvince
	toCity := req.ToCity

	if fromProvince == "" {
		fromProvince = req.Sender.Province
	}
	if fromCity == "" {
		fromCity = req.Sender.City
	}
	if toProvince == "" {
		toProvince = req.Receiver.Province
	}
	if toCity == "" {
		toCity = req.Receiver.City
	}

	if fromProvince == "" || fromCity == "" {
		return model.NewValidationError("寄件人省市不能为空")
	}

	if toProvince == "" || toCity == "" {
		return model.NewValidationError("收件人省市不能为空")
	}

	if req.Weight <= 0 {
		return model.NewValidationError("包裹重量必须大于0")
	}

	return nil
}

// 转换为内部请求格式
func convertToInternalPriceRequest(req SimplePriceRequest) *model.PriceRequest {
	// 设置默认值
	quantity := req.Quantity
	if quantity <= 0 {
		quantity = 1
	}

	goodsName := req.GoodsName
	if goodsName == "" {
		goodsName = "物品"
	}

	// 🔥 企业级修复：优先使用长宽高计算体积
	volume := req.Volume
	if req.Length > 0 && req.Width > 0 && req.Height > 0 {
		// 长宽高单位是cm，需要转换为m³
		volumeCm3 := req.Length * req.Width * req.Height
		volume = volumeCm3 / 1000000 // cm³ → m³
	}

	// 生成唯一的客户订单号
	customerOrderNo := "SIMPLE_" + util.NowBeijing().Format("20060102150405") + "_" + model.GenerateRandomString(6)

	// 🚀 增强：支持两种请求格式的兼容处理
	// 格式1：平铺格式 {"from_province": "广东省", "to_province": "北京市"}
	// 格式2：嵌套格式 {"sender": {"province": "广东省"}, "receiver": {"province": "北京市"}}
	fromProv := req.FromProvince
	fromCity := req.FromCity
	fromDist := req.FromDistrict

	// 如果平铺格式为空，尝试嵌套格式
	if fromProv == "" && req.Sender.Province != "" {
		fromProv = req.Sender.Province
	}
	if fromCity == "" && req.Sender.City != "" {
		fromCity = req.Sender.City
	}
	if fromDist == "" && req.Sender.District != "" {
		fromDist = req.Sender.District
	}

	toProv := req.ToProvince
	toCity := req.ToCity
	toDist := req.ToDistrict

	// 如果平铺格式为空，尝试嵌套格式
	if toProv == "" && req.Receiver.Province != "" {
		toProv = req.Receiver.Province
	}
	if toCity == "" && req.Receiver.City != "" {
		toCity = req.Receiver.City
	}
	if toDist == "" && req.Receiver.District != "" {
		toDist = req.Receiver.District
	}

	return &model.PriceRequest{
		CustomerOrderNo:   customerOrderNo,
		QueryAllCompanies: true, // 查询所有快递公司
		Sender: model.SenderInfo{
			Province: fromProv,
			City:     fromCity,
			District: fromDist,
			Name:     "寄件人",                   // 使用默认值
			Mobile:   "13800000000",           // 使用默认值
			Address:  req.FromDistrict + "某地", // 使用默认值
		},
		Receiver: model.ReceiverInfo{
			Province: toProv,
			City:     toCity,
			District: toDist,
			Name:     "收件人",                 // 使用默认值
			Mobile:   "13900000000",         // 使用默认值
			Address:  req.ToDistrict + "某地", // 使用默认值
		},
		Package: model.PackageInfo{
			Weight:    req.Weight,
			Length:    req.Length,
			Width:     req.Width,
			Height:    req.Height,
			Volume:    volume,
			Quantity:  quantity,
			GoodsName: goodsName,
		},
		PayMethod: req.PayMethod,
	}
}

// ConvertToSimplePriceResponse 转换为简化响应格式（增强版，支持事务一致性）
// 🚀 公开方法，供统一网关使用
func (h *SimplePriceHandler) ConvertToSimplePriceResponse(resp *model.PriceResponse, originalReq *SimplePriceRequest) *SimplePriceResponse {
	if !resp.Success {
		return &SimplePriceResponse{
			Success: resp.Success,
			Code:    resp.Code,
			Message: resp.Message,
		}
	}

	// 🎯 精简优化：映射服务已在GetSupportedCompanies中应用白名单过滤
	// 转换价格数据
	var pricesWithParams []PriceWithOrderParams

	for _, price := range resp.Data {
		// 🚫 双重过滤：根据 provider 字段或 order_code 前缀识别菜鸟条目
		providerLower := strings.ToLower(price.Provider)
		channelLower := strings.ToLower(price.ChannelID)
		if strings.Contains(providerLower, "cainiao") || strings.Contains(channelLower, "cainiao") {
			continue // 跳过所有菜鸟供应商数据，防止泄露
		}
		// 🎯 根源修复：price.ExpressCode已经是标准快递代码，无需再次映射
		// 缓存响应中的ExpressCode已经是标准代码，直接使用
		standardCode := price.ExpressCode

		// 第二步：通过标准代码获取快递公司信息
		company, err := h.expressRepository.GetCompanyByCode(standardCode)
		if err != nil || company == nil {
			// 无公司信息，跳过该价格
			fmt.Printf("[WARN] 跳过价格，原因: 未找到快递公司信息 standard_code=%s err=%v\n", standardCode, err)
			continue
		}

		companyName := company.Name

		// 生成增强版下单代码（包含事务一致性信息）
		orderCode, expiresAt, err := generateEnhancedOrderCode(standardCode, price.ExpressCode, price.Provider, price.ChannelID, price.ProductCode, price.Price, originalReq)
		if err != nil {
			// 如果生成增强版代码失败，使用旧版本代码作为备选
			orderCode = generateOrderCode(standardCode, price.ExpressCode, price.Provider, price.ChannelID, price.ProductCode)
			expiresAt = util.NowBeijing().Add(30 * time.Minute)
		}

		// 创建价格对象
		priceWithParams := PriceWithOrderParams{
			ExpressCode:          standardCode, // 使用标准化的快递公司代码
			ExpressName:          companyName,  // 使用快递公司名称
			ProductCode:          price.ProductCode,
			ProductName:          price.ProductName,
			Price:                price.Price,
			ContinuedWeightPerKg: price.ContinuedWeightPerKg,
			CalcWeight:           price.CalcWeight,
			OrderCode:            orderCode, // 增强版下单代码
			ExpiresAt:            expiresAt, // 过期时间
			PickupTimeInfo:       price.PickupTimeInfo,
		}

		// 🔥 不进行任何去重，直接添加所有价格
		pricesWithParams = append(pricesWithParams, priceWithParams)
	}

	// 若全部价格因映射或数据缺失被跳过，返回统一错误信息
	if len(pricesWithParams) == 0 {
		return &SimplePriceResponse{
			Success: false,
			Code:    500,
			Message: "所有返回价格均因映射或数据不完整被过滤，请检查快递公司映射配置",
			Data:    []PriceWithOrderParams{},
		}
	}

	// 按价格从低到高排序
	sort.Slice(pricesWithParams, func(i, j int) bool {
		return pricesWithParams[i].Price < pricesWithParams[j].Price
	})

	return &SimplePriceResponse{
		Success: resp.Success,
		Code:    resp.Code,
		Message: resp.Message,
		Data:    pricesWithParams,
	}
}

// generateEnhancedOrderCode 生成增强版下单代码（移除价格锁定机制）
func generateEnhancedOrderCode(standardCode, originalCode, provider, channelID, productCode string, _ float64, originalReq *SimplePriceRequest) (string, time.Time, error) {
	// 🔥 移除价格锁定机制：不再生成唯一ID和过期时间
	// 🔥 下单代码永久有效，不再有过期时间限制

	// 创建增强版代码结构（仅包含路由信息）
	enhancedCode := EnhancedOrderCode{
		StandardCode: standardCode,
		OriginalCode: originalCode,
		Provider:     provider,
		ChannelID:    channelID,
		ProductCode:  productCode,
		// 🔥 移除价格锁定：删除 LockedPrice、CreatedAt、ExpiresAt、CodeID 字段
		OriginalRequest: originalReq, // 存储原始查价请求用于实时价格验证
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(enhancedCode)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("序列化增强代码失败: %v", err)
	}

	// 🔥 移除加密机制：直接使用Base64编码，简化处理
	encodedCode := base64.StdEncoding.EncodeToString(jsonData)

	// 🔥 移除过期时间：返回零值时间，表示永不过期
	return "ENHANCED_ORDER_CODE_" + encodedCode, time.Time{}, nil
}

// 🔥 移除价格锁定相关方法：
// - generateCodeID：不再需要生成唯一ID
// - encryptOrderCode：简化处理，直接使用Base64编码
// 这些方法不再需要，因为我们移除了价格锁定机制

// generateOrderCode 保持向后兼容的旧版本代码生成（备用）
func generateOrderCode(standardCode, originalCode, provider, channelID, productCode string) string {
	// 格式: 标准代码|原始代码|供应商|渠道ID|产品代码
	plainCode := fmt.Sprintf("%s|%s|%s|%s|%s",
		standardCode, originalCode, provider, channelID, productCode)

	// 使用Base64编码
	encodedCode := base64.StdEncoding.EncodeToString([]byte(plainCode))

	// 添加前缀，表示这是一个下单代码
	return "ORDER_CODE_" + encodedCode
}
