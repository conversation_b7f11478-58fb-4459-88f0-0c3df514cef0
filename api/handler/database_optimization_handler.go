package handler

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// DatabaseOptimizationHandler 数据库优化处理器
type DatabaseOptimizationHandler struct {
	optimizationService *service.DatabaseOptimizationService
	logger              *zap.Logger
}

// NewDatabaseOptimizationHandler 创建数据库优化处理器
func NewDatabaseOptimizationHandler(
	optimizationService *service.DatabaseOptimizationService,
	logger *zap.Logger,
) *DatabaseOptimizationHandler {
	return &DatabaseOptimizationHandler{
		optimizationService: optimizationService,
		logger:              logger,
	}
}

// GetPerformanceReport 获取性能报告
// @Summary 获取数据库性能报告
// @Description 获取数据库查询性能、缓存命中率等统计信息
// @Tags 数据库优化
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} map[string]interface{} "性能报告"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /admin/database/performance [get]
func (h *DatabaseOptimizationHandler) GetPerformanceReport(c *gin.Context) {
	report := h.optimizationService.GetPerformanceReport()
	
	h.logger.Info("获取数据库性能报告",
		zap.String("user_id", c.GetString("user_id")),
		zap.Int("metrics_count", len(report)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取性能报告成功",
		"data":    report,
	})
}

// GetOptimizationMetrics 获取优化指标
// @Summary 获取数据库优化指标
// @Description 获取数据库优化配置和运行状态指标
// @Tags 数据库优化
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} map[string]interface{} "优化指标"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /admin/database/metrics [get]
func (h *DatabaseOptimizationHandler) GetOptimizationMetrics(c *gin.Context) {
	metrics := h.optimizationService.GetOptimizationMetrics()
	
	h.logger.Info("获取数据库优化指标",
		zap.String("user_id", c.GetString("user_id")))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取优化指标成功",
		"data":    metrics,
	})
}

// InvalidateCache 使缓存失效
// @Summary 使缓存失效
// @Description 根据类型和标识符使相关缓存失效
// @Tags 数据库优化
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body InvalidateCacheRequest true "缓存失效请求"
// @Success 200 {object} map[string]interface{} "操作成功"
// @Failure 400 {object} map[string]interface{} "请求参数错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /admin/database/cache/invalidate [post]
func (h *DatabaseOptimizationHandler) InvalidateCache(c *gin.Context) {
	var req InvalidateCacheRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数格式错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证参数
	if req.CacheType == "" || req.Identifier == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "缓存类型和标识符不能为空",
		})
		return
	}

	// 使缓存失效
	err := h.optimizationService.InvalidateCache(c.Request.Context(), req.CacheType, req.Identifier)
	if err != nil {
		h.logger.Error("使缓存失效失败",
			zap.String("cache_type", req.CacheType),
			zap.String("identifier", req.Identifier),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "使缓存失效失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("缓存失效成功",
		zap.String("user_id", c.GetString("user_id")),
		zap.String("cache_type", req.CacheType),
		zap.String("identifier", req.Identifier))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "缓存失效成功",
	})
}

// GetCacheStats 获取缓存统计
// @Summary 获取缓存统计信息
// @Description 获取Redis缓存的统计信息
// @Tags 数据库优化
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} map[string]interface{} "缓存统计"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /admin/database/cache/stats [get]
func (h *DatabaseOptimizationHandler) GetCacheStats(c *gin.Context) {
	cacheService := h.optimizationService.GetCacheService()
	stats, err := cacheService.GetCacheStats(c.Request.Context())
	if err != nil {
		h.logger.Error("获取缓存统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取缓存统计失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("获取缓存统计成功",
		zap.String("user_id", c.GetString("user_id")))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取缓存统计成功",
		"data":    stats,
	})
}

// GetSlowQueries 获取慢查询记录
// @Summary 获取慢查询记录
// @Description 获取最近的慢查询记录和优化建议
// @Tags 数据库优化
// @Accept json
// @Produce json
// @Security Bearer
// @Param limit query int false "返回记录数量" default(10)
// @Success 200 {object} map[string]interface{} "慢查询记录"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /admin/database/slow-queries [get]
func (h *DatabaseOptimizationHandler) GetSlowQueries(c *gin.Context) {
	limit := 10
	if l := c.Query("limit"); l != "" {
		if parsedLimit, err := parseIntParam(l); err == nil && parsedLimit > 0 && parsedLimit <= 100 {
			limit = parsedLimit
		}
	}

	performanceMonitor := h.optimizationService.GetPerformanceMonitor()
	slowQueries := performanceMonitor.GetSlowQueries(limit)

	h.logger.Info("获取慢查询记录成功",
		zap.String("user_id", c.GetString("user_id")),
		zap.Int("limit", limit),
		zap.Int("count", len(slowQueries)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取慢查询记录成功",
		"data": gin.H{
			"slow_queries": slowQueries,
			"total":        len(slowQueries),
			"limit":        limit,
		},
	})
}

// InvalidateCacheRequest 缓存失效请求
type InvalidateCacheRequest struct {
	CacheType  string `json:"cache_type" binding:"required" example:"user"`        // 缓存类型：user, order
	Identifier string `json:"identifier" binding:"required" example:"user_123"`    // 标识符
}

// parseIntParam 解析整数参数
func parseIntParam(s string) (int, error) {
	var result int
	for _, r := range s {
		if r < '0' || r > '9' {
			return 0, fmt.Errorf("invalid integer: %s", s)
		}
		result = result*10 + int(r-'0')
	}
	return result, nil
}
