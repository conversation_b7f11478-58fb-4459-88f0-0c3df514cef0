package handler

import (
	stderrors "errors"
	"net/http"
	"slices"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/user"
	"go.uber.org/zap"
)

// AdminUserHandler 管理员用户管理处理器
type AdminUserHandler struct {
	adminUserService user.AdminUserService
	logger           *zap.Logger
}

// NewAdminUserHandler 创建管理员用户管理处理器
func NewAdminUserHandler(adminUserService user.AdminUserService, logger *zap.Logger) *AdminUserHandler {
	return &AdminUserHandler{
		adminUserService: adminUserService,
		logger:           logger,
	}
}

// GetUsers 获取用户列表
// @Summary 获取用户列表
// @Description 管理员获取系统用户列表，支持分页和过滤
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "页码" default(1) minimum(1)
// @Param page_size query int false "每页大小" default(20) minimum(1) maximum(100)
// @Param keyword query string false "关键词搜索（用户名或邮箱）"
// @Param status query string false "用户状态过滤" Enums(active,inactive)
// @Param role_id query string false "角色ID过滤"
// @Param include_deleted query bool false "是否包含软删除用户" default(false)
// @Param order_by query string false "排序字段" Enums(username,email,created_at,updated_at,last_login)
// @Param order query string false "排序方向" Enums(asc,desc)
// @Success 200 {object} user.UserListResult
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/users [get]
func (h *AdminUserHandler) GetUsers(c *gin.Context) {
	requestID := getRequestID(c)

	// 解析分页参数
	pagination := user.Pagination{
		Page:     parseIntQuery(c, "page", 1),
		PageSize: parseIntQuery(c, "page_size", 20),
		OrderBy:  c.DefaultQuery("order_by", "created_at"),
		Order:    c.DefaultQuery("order", "desc"),
	}

	// 设置默认值并验证
	pagination.SetDefaults()
	if err := user.ValidatePagination(pagination); err != nil {
		h.logger.Warn("Invalid pagination parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(err.(errors.BusinessError).HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析过滤条件
	filter := user.UserFilter{
		Keyword:        c.Query("keyword"),
		RoleID:         c.Query("role_id"),
		IncludeDeleted: c.Query("include_deleted") == "true",
	}

	// 解析状态过滤
	if statusStr := c.Query("status"); statusStr != "" {
		switch statusStr {
		case "active":
			active := true
			filter.Status = &active
		case "inactive":
			inactive := false
			filter.Status = &inactive
		default:
			err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "无效的状态参数")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			return
		}
	}

	// 验证过滤条件
	if err := user.ValidateUserFilter(filter); err != nil {
		h.logger.Warn("Invalid filter parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(err.(errors.BusinessError).HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 获取用户列表
	result, err := h.adminUserService.GetUsers(filter, pagination)
	if err != nil {
		h.logger.Error("Failed to get users",
			zap.String("request_id", requestID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "获取用户列表失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully retrieved users",
		zap.String("request_id", requestID),
		zap.Int64("total", result.Total),
		zap.Int("page", result.Page))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取用户列表成功",
		"data":    result,
	})
}

// GetUser 获取用户详情
// @Summary 获取用户详情
// @Description 管理员获取指定用户的详细信息
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "用户ID" format(uuid)
// @Success 200 {object} user.UserWithRoles
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/users/{id} [get]
func (h *AdminUserHandler) GetUser(c *gin.Context) {
	requestID := getRequestID(c)
	userID := c.Param("id")

	if userID == "" {
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 获取用户信息
	userInfo, err := h.adminUserService.GetUserByID(userID)
	if err != nil {
		h.logger.Error("Failed to get user",
			zap.String("request_id", requestID),
			zap.String("user_id", userID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			// 处理标准错误
			if err == user.ErrUserNotFound {
				notFoundErr := errors.NewBusinessError(errors.ErrCodeNotFound, "用户不存在")
				c.JSON(notFoundErr.HTTPStatus(), errors.ToErrorResponse(notFoundErr, requestID))
			} else {
				internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "获取用户信息失败")
				c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
			}
		}
		return
	}

	h.logger.Info("Successfully retrieved user",
		zap.String("request_id", requestID),
		zap.String("user_id", userID),
		zap.String("username", userInfo.Username))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取用户信息成功",
		"data":    userInfo,
	})
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 管理员创建新用户
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body user.CreateUserRequest true "创建用户请求"
// @Success 201 {object} user.UserWithRoles
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 409 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/users [post]
func (h *AdminUserHandler) CreateUser(c *gin.Context) {
	requestID := getRequestID(c)

	var req user.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		validationErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数错误: "+err.Error())
		c.JSON(validationErr.HTTPStatus(), errors.ToErrorResponse(validationErr, requestID))
		return
	}

	// 验证请求参数
	if err := user.ValidateCreateUserRequest(req); err != nil {
		h.logger.Warn("Invalid create user request",
			zap.String("request_id", requestID),
			zap.String("username", req.Username),
			zap.Error(err))
		c.JSON(err.(errors.BusinessError).HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 创建用户
	newUser, err := h.adminUserService.CreateUser(req)
	if err != nil {
		h.logger.Error("Failed to create user",
			zap.String("request_id", requestID),
			zap.String("username", req.Username),
			zap.String("email", req.Email),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "创建用户失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully created user",
		zap.String("request_id", requestID),
		zap.String("user_id", newUser.ID),
		zap.String("username", newUser.Username))

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"code":    http.StatusCreated,
		"message": "用户创建成功",
		"data":    newUser,
	})
}

// 辅助函数
func parseIntQuery(c *gin.Context, key string, defaultValue int) int {
	if value, err := strconv.Atoi(c.Query(key)); err == nil && value > 0 {
		return value
	}
	return defaultValue
}

// UpdateUser 更新用户
// @Summary 更新用户
// @Description 管理员更新指定用户信息
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "用户ID" format(uuid)
// @Param request body user.UpdateUserRequest true "更新用户请求"
// @Success 200 {object} user.UserWithRoles
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 409 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/users/{id} [put]
func (h *AdminUserHandler) UpdateUser(c *gin.Context) {
	requestID := getRequestID(c)
	userID := c.Param("id")

	if userID == "" {
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	var req user.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		validationErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数错误: "+err.Error())
		c.JSON(validationErr.HTTPStatus(), errors.ToErrorResponse(validationErr, requestID))
		return
	}

	// 更新用户
	updatedUser, err := h.adminUserService.UpdateUserByAdmin(userID, req)
	if err != nil {
		h.logger.Error("Failed to update user",
			zap.String("request_id", requestID),
			zap.String("user_id", userID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "更新用户失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully updated user",
		zap.String("request_id", requestID),
		zap.String("user_id", userID),
		zap.String("username", updatedUser.Username))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "用户更新成功",
		"data":    updatedUser,
	})
}

// UpdateUserStatus 更新用户状态
// @Summary 更新用户状态
// @Description 管理员更新用户激活状态
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "用户ID" format(uuid)
// @Param request body object true "状态更新请求" example({"is_active": true})
// @Success 200 {object} object
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/users/{id}/status [patch]
func (h *AdminUserHandler) UpdateUserStatus(c *gin.Context) {
	requestID := getRequestID(c)
	userID := c.Param("id")

	if userID == "" {
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	var req struct {
		IsActive bool `json:"is_active" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		validationErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数错误: "+err.Error())
		c.JSON(validationErr.HTTPStatus(), errors.ToErrorResponse(validationErr, requestID))
		return
	}

	// 更新用户状态
	err := h.adminUserService.UpdateUserStatus(userID, req.IsActive)
	if err != nil {
		h.logger.Error("Failed to update user status",
			zap.String("request_id", requestID),
			zap.String("user_id", userID),
			zap.Bool("is_active", req.IsActive),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "更新用户状态失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully updated user status",
		zap.String("request_id", requestID),
		zap.String("user_id", userID),
		zap.Bool("is_active", req.IsActive))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "用户状态更新成功",
		"data": gin.H{
			"user_id":   userID,
			"is_active": req.IsActive,
		},
	})
}

// ResetPassword 重置用户密码
// @Summary 重置用户密码
// @Description 管理员重置指定用户的密码
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "用户ID" format(uuid)
// @Param request body object true "密码重置请求" example({"new_password": "newpassword123"})
// @Success 200 {object} object
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/users/{id}/password [patch]
func (h *AdminUserHandler) ResetPassword(c *gin.Context) {
	requestID := getRequestID(c)
	userID := c.Param("id")

	if userID == "" {
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	var req struct {
		NewPassword string `json:"new_password" binding:"required,min=8,max=72"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		validationErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数错误: "+err.Error())
		c.JSON(validationErr.HTTPStatus(), errors.ToErrorResponse(validationErr, requestID))
		return
	}

	// 重置密码
	err := h.adminUserService.ResetUserPassword(userID, req.NewPassword)
	if err != nil {
		h.logger.Error("Failed to reset user password",
			zap.String("request_id", requestID),
			zap.String("user_id", userID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "重置密码失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully reset user password",
		zap.String("request_id", requestID),
		zap.String("user_id", userID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "密码重置成功",
		"data": gin.H{
			"user_id": userID,
		},
	})
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 管理员删除指定用户（软删除）
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "用户ID" format(uuid)
// @Success 200 {object} object
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/users/{id} [delete]
func (h *AdminUserHandler) DeleteUser(c *gin.Context) {
	requestID := getRequestID(c)
	userID := c.Param("id")

	if userID == "" {
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 删除用户
	err := h.adminUserService.DeleteUser(userID)
	if err != nil {
		h.logger.Error("Failed to delete user",
			zap.String("request_id", requestID),
			zap.String("user_id", userID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "删除用户失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully deleted user",
		zap.String("request_id", requestID),
		zap.String("user_id", userID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "用户删除成功",
		"data": gin.H{
			"user_id": userID,
		},
	})
}

// GetUserStatistics 获取用户统计信息
// @Summary 获取用户统计信息
// @Description 管理员获取系统用户统计信息
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} user.UserStatistics
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/users/statistics [get]
func (h *AdminUserHandler) GetUserStatistics(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取用户统计信息
	stats, err := h.adminUserService.GetUserStatistics()
	if err != nil {
		h.logger.Error("Failed to get user statistics",
			zap.String("request_id", requestID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "获取用户统计信息失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully retrieved user statistics",
		zap.String("request_id", requestID),
		zap.Int64("total_users", stats.TotalUsers))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取用户统计信息成功",
		"data":    stats,
	})
}

// BatchUpdateUserStatus 批量更新用户状态
// @Summary 批量更新用户状态
// @Description 系统管理员批量更新多个用户的激活状态
// @Tags 系统管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body object true "批量状态更新请求" example({"user_ids": ["uuid1", "uuid2"], "is_active": true})
// @Success 200 {object} object
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/system/admin/users/batch/status [patch]
func (h *AdminUserHandler) BatchUpdateUserStatus(c *gin.Context) {
	requestID := getRequestID(c)

	var req struct {
		UserIDs  []string `json:"user_ids" binding:"required,min=1"`
		IsActive bool     `json:"is_active" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		validationErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数错误: "+err.Error())
		c.JSON(validationErr.HTTPStatus(), errors.ToErrorResponse(validationErr, requestID))
		return
	}

	// 验证用户ID列表
	if len(req.UserIDs) > 100 {
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "批量操作用户数量不能超过100个")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 获取当前用户ID，防止操作自己
	currentUserID, exists := c.Get("userID")
	if exists {
		currentUserIDStr := currentUserID.(string)
		if slices.Contains(req.UserIDs, currentUserIDStr) {
			err := errors.NewBusinessError(errors.ErrCodeForbidden, "不能在批量操作中包含自己的账号")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			return
		}
	}

	// 执行批量更新
	err := h.adminUserService.BatchUpdateStatus(req.UserIDs, req.IsActive)
	if err != nil {
		h.logger.Error("Failed to batch update user status",
			zap.String("request_id", requestID),
			zap.Strings("user_ids", req.UserIDs),
			zap.Bool("is_active", req.IsActive),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "批量更新用户状态失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully batch updated user status",
		zap.String("request_id", requestID),
		zap.Strings("user_ids", req.UserIDs),
		zap.Bool("is_active", req.IsActive))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "批量更新用户状态成功",
		"data": gin.H{
			"updated_count": len(req.UserIDs),
			"is_active":     req.IsActive,
		},
	})
}

// ForceDeleteUser 强制删除用户（硬删除）
// @Summary 强制删除用户
// @Description 系统管理员强制删除用户（硬删除，不可恢复）
// @Tags 系统管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "用户ID" format(uuid)
// @Success 200 {object} object
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/system/admin/users/{id}/force [delete]
func (h *AdminUserHandler) ForceDeleteUser(c *gin.Context) {
	requestID := getRequestID(c)
	userID := c.Param("id")

	if userID == "" {
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 防止删除自己
	currentUserID, exists := c.Get("userID")
	if exists && currentUserID.(string) == userID {
		err := errors.NewBusinessError(errors.ErrCodeForbidden, "不能强制删除自己的账号")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 永久删除用户
	err := h.adminUserService.ForceDeleteUser(userID)
	if err != nil {
		h.logger.Error("Failed to force delete user",
			zap.String("request_id", requestID),
			zap.String("user_id", userID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "永久删除用户失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully force deleted user",
		zap.String("request_id", requestID),
		zap.String("user_id", userID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "用户永久删除成功",
	})
}

// RestoreUser 恢复已删除用户
// @Summary 恢复已删除用户
// @Description 系统管理员恢复已软删除的用户
// @Tags 系统管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "用户ID" format(uuid)
// @Success 200 {object} object
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/system/admin/users/{id}/restore [post]
func (h *AdminUserHandler) RestoreUser(c *gin.Context) {
	requestID := getRequestID(c)
	userID := c.Param("id")

	if userID == "" {
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// TODO: 实现用户恢复逻辑
	// 这里需要实现用户恢复功能，暂时返回未实现错误
	err := errors.NewBusinessError(errors.ErrCodeInternal, "用户恢复功能暂未实现")
	c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
}

// GetDeletedUsers 获取已删除用户列表
// @Summary 获取已删除用户列表
// @Description 系统管理员获取已软删除的用户列表
// @Tags 系统管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "页码" default(1) minimum(1)
// @Param page_size query int false "每页大小" default(20) minimum(1) maximum(100)
// @Success 200 {object} object
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/system/admin/users/deleted [get]
func (h *AdminUserHandler) GetDeletedUsers(c *gin.Context) {
	requestID := getRequestID(c)

	// TODO: 实现获取已删除用户列表逻辑
	// 这里需要实现获取已删除用户功能，暂时返回未实现错误
	err := errors.NewBusinessError(errors.ErrCodeInternal, "获取已删除用户列表功能暂未实现")
	c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
}

// GetAuditLogs 获取审计日志
// @Summary 获取审计日志
// @Description 管理员获取用户操作审计日志
// @Tags 管理员-审计日志
// @Accept json
// @Produce json
// @Security Bearer
// @Param operator_id query string false "操作者ID"
// @Param target_user_id query string false "目标用户ID"
// @Param action query string false "操作类型"
// @Param start_time query string false "开始时间" format(date-time)
// @Param end_time query string false "结束时间" format(date-time)
// @Param page query int false "页码" default(1) minimum(1)
// @Param page_size query int false "每页大小" default(20) minimum(1) maximum(100)
// @Success 200 {object} user.AuditLogListResult
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/audit-logs [get]
func (h *AdminUserHandler) GetAuditLogs(c *gin.Context) {
	requestID := getRequestID(c)

	// 解析查询参数
	var filter user.AuditLogFilter
	filter.OperatorID = c.Query("operator_id")
	filter.TargetUserID = c.Query("target_user_id")
	if action := c.Query("action"); action != "" {
		filter.Action = user.UserAuditAction(action)
	}

	// 解析时间范围
	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if startTime, err := parseTime(startTimeStr); err == nil {
			filter.StartTime = &startTime
		}
	}
	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if endTime, err := parseTime(endTimeStr); err == nil {
			filter.EndTime = &endTime
		}
	}

	// 解析分页参数
	pagination := user.Pagination{
		Page:     parseIntWithDefault(c.Query("page"), 1),
		PageSize: parseIntWithDefault(c.Query("page_size"), 20),
	}

	// 获取审计日志
	result, err := h.adminUserService.GetAuditLogs(filter, pagination)
	if err != nil {
		h.logger.Error("Failed to get audit logs",
			zap.String("request_id", requestID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "获取审计日志失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully retrieved audit logs",
		zap.String("request_id", requestID),
		zap.Int64("total", result.Total))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取审计日志成功",
		"data":    result,
	})
}

// CreateUserWithAudit 创建用户（带审计）
// @Summary 创建用户（带审计）
// @Description 管理员创建用户并记录审计日志
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body user.CreateUserRequest true "用户创建请求"
// @Success 201 {object} user.User
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 409 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/users/with-audit [post]
func (h *AdminUserHandler) CreateUserWithAudit(c *gin.Context) {
	requestID := getRequestID(c)

	var req user.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		validationErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数错误: "+err.Error())
		c.JSON(validationErr.HTTPStatus(), errors.ToErrorResponse(validationErr, requestID))
		return
	}

	// 获取操作者信息
	operatorID, exists := c.Get("userID")
	if !exists {
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "未找到操作者信息")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 获取客户端信息
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 创建用户（带审计）
	ctx := c.Request.Context()
	newUser, err := h.adminUserService.CreateUserWithAudit(ctx, operatorID.(string), req, ipAddress, userAgent)
	if err != nil {
		h.logger.Error("Failed to create user with audit",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID.(string)),
			zap.String("username", req.Username),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "创建用户失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully created user with audit",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID.(string)),
		zap.String("user_id", newUser.ID),
		zap.String("username", newUser.Username))

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"code":    http.StatusCreated,
		"message": "用户创建成功",
		"data":    newUser,
	})
}

// UpdateUserWithAudit 更新用户（带审计）
// @Summary 更新用户（带审计）
// @Description 管理员更新用户信息并记录审计日志
// @Tags 管理员-用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path string true "用户ID" format(uuid)
// @Param request body user.UpdateUserRequest true "用户更新请求"
// @Success 200 {object} user.User
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 409 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/users/{id}/with-audit [put]
func (h *AdminUserHandler) UpdateUserWithAudit(c *gin.Context) {
	requestID := getRequestID(c)
	userID := c.Param("id")

	if userID == "" {
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	var req user.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		validationErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数错误: "+err.Error())
		c.JSON(validationErr.HTTPStatus(), errors.ToErrorResponse(validationErr, requestID))
		return
	}

	// 获取操作者信息
	operatorID, exists := c.Get("userID")
	if !exists {
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "未找到操作者信息")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 防止修改自己的账号
	if operatorID.(string) == userID {
		err := errors.NewBusinessError(errors.ErrCodeForbidden, "不能修改自己的账号")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 获取客户端信息
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 更新用户（带审计）
	ctx := c.Request.Context()
	updatedUser, err := h.adminUserService.UpdateUserByAdminWithAudit(ctx, operatorID.(string), userID, req, ipAddress, userAgent)
	if err != nil {
		h.logger.Error("Failed to update user with audit",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID.(string)),
			zap.String("user_id", userID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "更新用户失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
		}
		return
	}

	h.logger.Info("Successfully updated user with audit",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID.(string)),
		zap.String("user_id", userID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "用户更新成功",
		"data":    updatedUser,
	})
}

func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return "unknown"
}

// parseTime 解析时间字符串
func parseTime(timeStr string) (time.Time, error) {
	// 支持多种时间格式
	formats := []string{
		"2006-01-02T15:04:05Z07:00", // RFC3339
		"2006-01-02 15:04:05",       // 常用格式
		"2006-01-02",                // 日期格式
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, stderrors.New("invalid time format")
}

// parseIntWithDefault 解析整数，如果失败则返回默认值
func parseIntWithDefault(str string, defaultValue int) int {
	if str == "" {
		return defaultValue
	}

	if value, err := strconv.Atoi(str); err == nil && value > 0 {
		return value
	}

	return defaultValue
}
