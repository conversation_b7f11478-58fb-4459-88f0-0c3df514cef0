package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// SystemConfig 系统配置结构
type SystemConfig struct {
	ExpressCompanies []ExpressCompany `json:"express_companies"`
	OrderStatuses    []OrderStatus    `json:"order_statuses"`
	Providers        []Provider       `json:"providers"`
	PaymentMethods   []PaymentMethod  `json:"payment_methods"`
	Regions          []Region         `json:"regions"`
}

// ExpressCompany 快递公司配置
type ExpressCompany struct {
	Code              string     `json:"code"`
	Name              string     `json:"name"`
	Logo              string     `json:"logo,omitempty"`
	IsActive          bool       `json:"is_active"`
	SupportedServices []string   `json:"supported_services"`
	WeightLimit       float64    `json:"weight_limit"`
	SizeLimit         *SizeLimit `json:"size_limit,omitempty"`
}

// SizeLimit 尺寸限制
type SizeLimit struct {
	Length float64 `json:"length"`
	Width  float64 `json:"width"`
	Height float64 `json:"height"`
}

// OrderStatus 订单状态配置
type OrderStatus struct {
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Color       string `json:"color"`
	Icon        string `json:"icon,omitempty"`
	IsFinal     bool   `json:"is_final"`
}

// Provider 供应商配置
type Provider struct {
	Code               string   `json:"code"`
	Name               string   `json:"name"`
	IsActive           bool     `json:"is_active"`
	SupportedCompanies []string `json:"supported_companies"`
	APIVersion         string   `json:"api_version"`
}

// PaymentMethod 支付方式配置
type PaymentMethod struct {
	Code        int    `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
	IsActive    bool   `json:"is_active"`
}

// Region 地区配置
type Region struct {
	Code       string    `json:"code"`
	Name       string    `json:"name"`
	Level      int       `json:"level"`
	ParentCode string    `json:"parent_code,omitempty"`
	Children   []*Region `json:"children,omitempty"`
}

// ConfigHandler 配置处理器
type ConfigHandler struct{}

// NewConfigHandler 创建配置处理器
func NewConfigHandler() *ConfigHandler {
	return &ConfigHandler{}
}

// GetSystemConfig 获取系统配置
func (h *ConfigHandler) GetSystemConfig(c *gin.Context) {
	config := SystemConfig{
		ExpressCompanies: []ExpressCompany{
			{Code: "SF", Name: "顺丰速运", IsActive: true, SupportedServices: []string{"标准快递", "特快专递"}, WeightLimit: 50},
			{Code: "ZTO", Name: "中通快递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
			{Code: "YTO", Name: "圆通速递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
			{Code: "STO", Name: "申通快递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
			{Code: "YD", Name: "韵达速递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
			{Code: "JT", Name: "极兔快递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
			{Code: "JD", Name: "京东物流", IsActive: true, SupportedServices: []string{"标准快递", "次日达"}, WeightLimit: 50},
			{Code: "DBL", Name: "德邦快递", IsActive: true, SupportedServices: []string{"标准快递", "大件快递"}, WeightLimit: 100},
			{Code: "EMS", Name: "中国邮政", IsActive: true, SupportedServices: []string{"标准快递", "特快专递"}, WeightLimit: 50},
			{Code: "HTKY", Name: "百世快递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
		},
		OrderStatuses: []OrderStatus{
			// === 下单阶段 ===
			{Code: "submitted", Name: "已提交", Description: "订单已提交", Color: "primary", IsFinal: false},
			{Code: "submit_failed", Name: "提交失败", Description: "订单提交失败", Color: "danger", IsFinal: true},
			{Code: "print_failed", Name: "面单生成失败", Description: "面单生成失败", Color: "danger", IsFinal: true},

			// === 分配阶段 ===
			{Code: "assigned", Name: "已分配", Description: "已分配网点或快递员", Color: "info", IsFinal: false},
			{Code: "awaiting_pickup", Name: "等待揽收", Description: "等待快递员揽收", Color: "warning", IsFinal: false},

			// === 揽收阶段 ===
			{Code: "picked_up", Name: "已揽收", Description: "快递员已揽收", Color: "primary", IsFinal: false},
			{Code: "pickup_failed", Name: "揽收失败", Description: "快递员揽收失败", Color: "danger", IsFinal: false},

			// === 运输阶段 ===
			{Code: "in_transit", Name: "运输中", Description: "包裹正在运输", Color: "info", IsFinal: false},
			{Code: "out_for_delivery", Name: "派送中", Description: "包裹正在派送", Color: "info", IsFinal: false},

			// === 签收阶段 ===
			{Code: "delivered", Name: "已签收", Description: "包裹已签收", Color: "success", IsFinal: false},
			{Code: "delivered_abnormal", Name: "异常签收", Description: "异常签收", Color: "warning", IsFinal: false},

			// === 计费阶段 ===
			{Code: "billed", Name: "已计费", Description: "订单已计费完成", Color: "success", IsFinal: true},

			// === 异常状态 ===
			{Code: "exception", Name: "异常", Description: "订单异常", Color: "danger", IsFinal: false},
			{Code: "returned", Name: "已退回", Description: "包裹已退回", Color: "warning", IsFinal: false},
			{Code: "forwarded", Name: "已转寄", Description: "包裹已转寄", Color: "info", IsFinal: false},

			// === 取消状态 ===
			{Code: "cancelled", Name: "已取消", Description: "订单已取消", Color: "danger", IsFinal: true},
			{Code: "voided", Name: "已作废", Description: "订单已作废", Color: "danger", IsFinal: true},

			// === 失败状态 ===
			{Code: "failed", Name: "下单失败", Description: "订单下单失败", Color: "danger", IsFinal: true},

			// === 特殊状态 ===
			{Code: "weight_updated", Name: "重量更新", Description: "订单重量已更新", Color: "info", IsFinal: false},
			{Code: "revived", Name: "订单复活", Description: "订单已复活", Color: "primary", IsFinal: false},
		},
		Providers: []Provider{
			{Code: "kuaidi100", Name: "快递100", IsActive: true, SupportedCompanies: []string{"SF", "ZTO", "YTO", "STO", "YD", "JD", "EMS"}, APIVersion: "v1"},
			{Code: "yida", Name: "易达", IsActive: true, SupportedCompanies: []string{"SF", "ZTO", "YTO", "STO"}, APIVersion: "v1"},
			{Code: "yuntong", Name: "云通", IsActive: true, SupportedCompanies: []string{"ZTO", "YTO", "STO", "YD", "HTKY"}, APIVersion: "v1"},
			{Code: "cainiao", Name: "菜鸟裹裹", IsActive: true, SupportedCompanies: []string{"YD", "ZTO", "STO", "YTO", "JT", "JD", "DBL", "SF", "EMS"}, APIVersion: "v1"},
			{Code: "kuaidiniao", Name: "快递鸟", IsActive: true, SupportedCompanies: []string{"SF", "JD", "ZTO", "YTO", "YD", "DBL", "STO", "JTSD", "EMS"}, APIVersion: "v1"},
		},
		PaymentMethods: []PaymentMethod{
			{Code: 0, Name: "寄付", Description: "寄件人付费", IsActive: true},
			{Code: 1, Name: "到付", Description: "收件人付费", IsActive: true},
			{Code: 2, Name: "月结", Description: "月结账户", IsActive: true},
		},
		Regions: []Region{
			{
				Code: "110000", Name: "北京市", Level: 1,
				Children: []*Region{
					{
						Code: "110100", Name: "北京市", Level: 2, ParentCode: "110000",
						Children: []*Region{
							{Code: "110101", Name: "东城区", Level: 3, ParentCode: "110100"},
							{Code: "110102", Name: "西城区", Level: 3, ParentCode: "110100"},
							{Code: "110105", Name: "朝阳区", Level: 3, ParentCode: "110100"},
							{Code: "110106", Name: "丰台区", Level: 3, ParentCode: "110100"},
							{Code: "110107", Name: "石景山区", Level: 3, ParentCode: "110100"},
							{Code: "110108", Name: "海淀区", Level: 3, ParentCode: "110100"},
						},
					},
				},
			},
			{
				Code: "310000", Name: "上海市", Level: 1,
				Children: []*Region{
					{
						Code: "310100", Name: "上海市", Level: 2, ParentCode: "310000",
						Children: []*Region{
							{Code: "310101", Name: "黄浦区", Level: 3, ParentCode: "310100"},
							{Code: "310104", Name: "徐汇区", Level: 3, ParentCode: "310100"},
							{Code: "310105", Name: "长宁区", Level: 3, ParentCode: "310100"},
							{Code: "310106", Name: "静安区", Level: 3, ParentCode: "310100"},
							{Code: "310107", Name: "普陀区", Level: 3, ParentCode: "310100"},
							{Code: "310109", Name: "虹口区", Level: 3, ParentCode: "310100"},
						},
					},
				},
			},
			{
				Code: "330000", Name: "浙江省", Level: 1,
				Children: []*Region{
					{
						Code: "330100", Name: "杭州市", Level: 2, ParentCode: "330000",
						Children: []*Region{
							{Code: "330102", Name: "上城区", Level: 3, ParentCode: "330100"},
							{Code: "330105", Name: "拱墅区", Level: 3, ParentCode: "330100"},
							{Code: "330106", Name: "西湖区", Level: 3, ParentCode: "330100"},
							{Code: "330108", Name: "滨江区", Level: 3, ParentCode: "330100"},
							{Code: "330109", Name: "萧山区", Level: 3, ParentCode: "330100"},
							{Code: "330110", Name: "余杭区", Level: 3, ParentCode: "330100"},
						},
					},
					{
						Code: "330200", Name: "宁波市", Level: 2, ParentCode: "330000",
						Children: []*Region{
							{Code: "330203", Name: "海曙区", Level: 3, ParentCode: "330200"},
							{Code: "330204", Name: "江东区", Level: 3, ParentCode: "330200"},
							{Code: "330205", Name: "江北区", Level: 3, ParentCode: "330200"},
							{Code: "330206", Name: "北仑区", Level: 3, ParentCode: "330200"},
						},
					},
				},
			},
			{
				Code: "440000", Name: "广东省", Level: 1,
				Children: []*Region{
					{
						Code: "440100", Name: "广州市", Level: 2, ParentCode: "440000",
						Children: []*Region{
							{Code: "440103", Name: "荔湾区", Level: 3, ParentCode: "440100"},
							{Code: "440104", Name: "越秀区", Level: 3, ParentCode: "440100"},
							{Code: "440105", Name: "海珠区", Level: 3, ParentCode: "440100"},
							{Code: "440106", Name: "天河区", Level: 3, ParentCode: "440100"},
							{Code: "440111", Name: "白云区", Level: 3, ParentCode: "440100"},
							{Code: "440112", Name: "黄埔区", Level: 3, ParentCode: "440100"},
						},
					},
					{
						Code: "440300", Name: "深圳市", Level: 2, ParentCode: "440000",
						Children: []*Region{
							{Code: "440303", Name: "罗湖区", Level: 3, ParentCode: "440300"},
							{Code: "440304", Name: "福田区", Level: 3, ParentCode: "440300"},
							{Code: "440305", Name: "南山区", Level: 3, ParentCode: "440300"},
							{Code: "440306", Name: "宝安区", Level: 3, ParentCode: "440300"},
							{Code: "440307", Name: "龙岗区", Level: 3, ParentCode: "440300"},
							{Code: "440308", Name: "盐田区", Level: 3, ParentCode: "440300"},
						},
					},
				},
			},
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取系统配置成功",
		"data":    config,
	})
}

// GetExpressCompanies 获取快递公司列表
func (h *ConfigHandler) GetExpressCompanies(c *gin.Context) {
	companies := []ExpressCompany{
		{Code: "SF", Name: "顺丰速运", IsActive: true, SupportedServices: []string{"标准快递", "特快专递"}, WeightLimit: 50},
		{Code: "ZTO", Name: "中通快递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
		{Code: "YTO", Name: "圆通速递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
		{Code: "STO", Name: "申通快递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
		{Code: "YD", Name: "韵达速递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
		{Code: "JT", Name: "极兔快递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
		{Code: "JD", Name: "京东物流", IsActive: true, SupportedServices: []string{"标准快递", "次日达"}, WeightLimit: 50},
		{Code: "DBL", Name: "德邦快递", IsActive: true, SupportedServices: []string{"标准快递", "大件快递"}, WeightLimit: 100},
		{Code: "DBL360", Name: "德邦大件360", IsActive: true, SupportedServices: []string{"大件快递"}, WeightLimit: 500},
		{Code: "EMS", Name: "中国邮政", IsActive: true, SupportedServices: []string{"标准快递", "特快专递"}, WeightLimit: 50},
		{Code: "HTKY", Name: "百世快递", IsActive: true, SupportedServices: []string{"标准快递"}, WeightLimit: 50},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取快递公司列表成功",
		"data":    companies,
	})
}

// GetOrderStatuses 获取订单状态列表
func (h *ConfigHandler) GetOrderStatuses(c *gin.Context) {
	statuses := []OrderStatus{
		// === 下单阶段 ===
		{Code: "submitted", Name: "已提交", Description: "订单已提交", Color: "primary", IsFinal: false},
		{Code: "submit_failed", Name: "提交失败", Description: "订单提交失败", Color: "danger", IsFinal: true},
		{Code: "print_failed", Name: "面单生成失败", Description: "面单生成失败", Color: "danger", IsFinal: true},

		// === 分配阶段 ===
		{Code: "assigned", Name: "已分配", Description: "已分配网点或快递员", Color: "info", IsFinal: false},
		{Code: "awaiting_pickup", Name: "等待揽收", Description: "等待快递员揽收", Color: "warning", IsFinal: false},

		// === 揽收阶段 ===
		{Code: "picked_up", Name: "已揽收", Description: "快递员已揽收", Color: "primary", IsFinal: false},
		{Code: "pickup_failed", Name: "揽收失败", Description: "快递员揽收失败", Color: "danger", IsFinal: false},

		// === 运输阶段 ===
		{Code: "in_transit", Name: "运输中", Description: "包裹正在运输", Color: "info", IsFinal: false},
		{Code: "out_for_delivery", Name: "派送中", Description: "包裹正在派送", Color: "info", IsFinal: false},

		// === 签收阶段 ===
		{Code: "delivered", Name: "已签收", Description: "包裹已签收", Color: "success", IsFinal: false},
		{Code: "delivered_abnormal", Name: "异常签收", Description: "异常签收", Color: "warning", IsFinal: false},

		// === 计费阶段 ===
		{Code: "billed", Name: "已计费", Description: "订单已计费完成", Color: "success", IsFinal: true},

		// === 异常状态 ===
		{Code: "exception", Name: "异常", Description: "订单异常", Color: "danger", IsFinal: false},
		{Code: "returned", Name: "已退回", Description: "包裹已退回", Color: "warning", IsFinal: false},
		{Code: "forwarded", Name: "已转寄", Description: "包裹已转寄", Color: "info", IsFinal: false},

		// === 取消状态 ===
		{Code: "cancelled", Name: "已取消", Description: "订单已取消", Color: "danger", IsFinal: true},
		{Code: "voided", Name: "已作废", Description: "订单已作废", Color: "danger", IsFinal: true},

		// === 特殊状态 ===
		{Code: "weight_updated", Name: "重量更新", Description: "订单重量已更新", Color: "info", IsFinal: false},
		{Code: "revived", Name: "订单复活", Description: "订单已复活", Color: "primary", IsFinal: false},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取订单状态列表成功",
		"data":    statuses,
	})
}

// GetRegions 获取地区列表
func (h *ConfigHandler) GetRegions(c *gin.Context) {
	parentCode := c.Query("parent_code")
	level := c.Query("level")

	// 这里应该从数据库获取，现在返回模拟数据
	var regions []Region

	if parentCode == "" && level == "1" {
		// 返回省级地区
		regions = []Region{
			{Code: "110000", Name: "北京市", Level: 1},
			{Code: "310000", Name: "上海市", Level: 1},
			{Code: "330000", Name: "浙江省", Level: 1},
			{Code: "440000", Name: "广东省", Level: 1},
		}
	} else if parentCode != "" {
		// 返回指定父级下的子地区
		switch parentCode {
		case "110000":
			regions = []Region{
				{Code: "110100", Name: "北京市", Level: 2, ParentCode: "110000"},
			}
		case "330000":
			regions = []Region{
				{Code: "330100", Name: "杭州市", Level: 2, ParentCode: "330000"},
				{Code: "330200", Name: "宁波市", Level: 2, ParentCode: "330000"},
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取地区列表成功",
		"data":    regions,
	})
}
