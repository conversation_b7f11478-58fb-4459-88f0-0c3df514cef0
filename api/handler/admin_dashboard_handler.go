package handler

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"runtime"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/your-org/go-kuaidi/internal/user"
	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// AdminDashboardHandler 管理员仪表盘处理器
type AdminDashboardHandler struct {
	logger              *zap.Logger
	adminUserService    user.AdminUserService
	adminBalanceService AdminBalanceServiceInterface
	adminOrderService   AdminOrderServiceInterface
	performanceHandler  *PerformanceHandler
	db                  *sql.DB
	redis               *redis.Client
}

// AdminBalanceServiceInterface 管理员余额服务接口
type AdminBalanceServiceInterface interface {
	GetBalanceOverview(ctx context.Context) (*AdminBalanceOverview, error)
	GetBalanceStatistics(ctx context.Context) (*BalanceStatistics, error)
}

// AdminOrderServiceInterface 管理员订单服务接口
type AdminOrderServiceInterface interface {
	GetAdminOrderStatistics(ctx context.Context, filter *AdminStatisticsFilter) (*AdminOrderStatistics, error)
}

// 定义需要的数据结构
type AdminBalanceOverview struct {
	TotalUsers         int64   `json:"total_users"`
	ActiveUsers        int64   `json:"active_users"`
	InactiveUsers      int64   `json:"inactive_users"`
	TotalBalance       float64 `json:"total_balance"`
	AvgBalance         float64 `json:"avg_balance"`
	RecentTransactions int64   `json:"recent_transactions"`
}

type BalanceStatistics struct {
	TotalBalance     float64 `json:"total_balance"`
	TotalUsers       int64   `json:"total_users"`
	ActiveUsers      int64   `json:"active_users"`
	AvgBalance       float64 `json:"avg_balance"`
	TodayDeposits    float64 `json:"today_deposits"`
	TodayConsumption float64 `json:"today_consumption"`
	LowBalanceUsers  int64   `json:"low_balance_users"`
}

type AdminOrderStatistics struct {
	TotalOrders     int64   `json:"total_orders"`
	TodayOrders     int64   `json:"today_orders"`
	PendingOrders   int64   `json:"pending_orders"`
	CompletedOrders int64   `json:"completed_orders"`
	CancelledOrders int64   `json:"cancelled_orders"`
	RevenueToday    float64 `json:"revenue_today"`
	RevenueMonth    float64 `json:"revenue_month"`
}

type AdminStatisticsFilter struct {
	StartDate string `json:"start_date,omitempty"`
	EndDate   string `json:"end_date,omitempty"`
}

// NewAdminDashboardHandler 创建管理员仪表盘处理器
func NewAdminDashboardHandler(
	logger *zap.Logger,
	adminUserService user.AdminUserService,
	adminBalanceService AdminBalanceServiceInterface,
	adminOrderService AdminOrderServiceInterface,
	performanceHandler *PerformanceHandler,
	db *sql.DB,
	redis *redis.Client,
	_ interface{}, // 保持兼容性，暂时不使用
) *AdminDashboardHandler {
	return &AdminDashboardHandler{
		logger:              logger,
		adminUserService:    adminUserService,
		adminBalanceService: adminBalanceService,
		adminOrderService:   adminOrderService,
		performanceHandler:  performanceHandler,
		db:                  db,
		redis:               redis,
	}
}

// DashboardOverview 仪表盘概览数据
type DashboardOverview struct {
	// 用户统计
	UserStats *user.UserStatistics `json:"user_stats"`

	// 余额统计
	BalanceOverview interface{} `json:"balance_overview"`

	// 订单统计
	OrderStats interface{} `json:"order_stats"`

	// 系统信息
	SystemInfo interface{} `json:"system_info"`

	// 性能指标
	PerformanceMetrics interface{} `json:"performance_metrics"`

	// 最近活动
	RecentActivities []RecentActivity `json:"recent_activities"`

	// 系统健康状态
	HealthStatus SystemHealthStatus `json:"health_status"`

	// 数据更新时间
	UpdatedAt time.Time `json:"updated_at"`
}

// RecentActivity 最近活动
type RecentActivity struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`   // user, order, balance, system
	Action      string    `json:"action"` // create, update, delete, login
	Description string    `json:"description"`
	UserID      string    `json:"user_id,omitempty"`
	Username    string    `json:"username,omitempty"`
	Timestamp   time.Time `json:"timestamp"`
	IPAddress   string    `json:"ip_address,omitempty"`
}

// SystemHealthStatus 系统健康状态
type SystemHealthStatus struct {
	Overall   string                     `json:"overall"` // healthy, warning, critical
	Database  HealthComponent            `json:"database"`
	Redis     HealthComponent            `json:"redis"`
	API       HealthComponent            `json:"api"`
	Services  map[string]HealthComponent `json:"services"`
	LastCheck time.Time                  `json:"last_check"`
}

// HealthComponent 健康组件
type HealthComponent struct {
	Status       string    `json:"status"` // healthy, warning, critical
	Message      string    `json:"message"`
	ResponseTime string    `json:"response_time,omitempty"`
	LastCheck    time.Time `json:"last_check"`
}

// GetDashboardOverview 获取仪表盘概览
// @Summary 获取管理员仪表盘概览
// @Description 获取管理员仪表盘的所有概览数据，包括用户、订单、余额、系统等统计信息
// @Tags 管理员-仪表盘
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} DashboardOverview
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/dashboard/overview [get]
func (h *AdminDashboardHandler) GetDashboardOverview(c *gin.Context) {
	requestID := getRequestID(c)

	h.logger.Info("Getting dashboard overview", zap.String("request_id", requestID))

	// 并发获取各种统计数据
	ctx := c.Request.Context()

	// 创建响应结构
	overview := &DashboardOverview{
		UpdatedAt: util.NowBeijing(),
	}

	// 并发获取各种统计数据
	var userStats *user.UserStatistics
	var balanceOverview interface{}
	var orderStats interface{}

	// 使用goroutine并发获取数据
	errChan := make(chan error, 3)

	// 获取用户统计
	go func() {
		stats, err := h.adminUserService.GetUserStatistics()
		if err != nil {
			h.logger.Warn("Failed to get user statistics",
				zap.String("request_id", requestID),
				zap.Error(err))
			errChan <- err
		} else {
			userStats = stats
			errChan <- nil
		}
	}()

	// 获取余额概览
	go func() {
		balance, err := h.adminBalanceService.GetBalanceOverview(ctx)
		if err != nil {
			h.logger.Warn("Failed to get balance overview",
				zap.String("request_id", requestID),
				zap.Error(err))
			errChan <- err
		} else {
			balanceOverview = balance
			errChan <- nil
		}
	}()

	// 获取订单统计
	go func() {
		orders, err := h.adminOrderService.GetAdminOrderStatistics(ctx, nil)
		if err != nil {
			h.logger.Warn("Failed to get order statistics",
				zap.String("request_id", requestID),
				zap.Error(err))
			errChan <- err
		} else {
			orderStats = orders
			errChan <- nil
		}
	}()

	// 等待所有goroutine完成
	for i := 0; i < 3; i++ {
		<-errChan
	}

	// 设置获取到的数据
	overview.UserStats = userStats
	overview.BalanceOverview = balanceOverview
	overview.OrderStats = orderStats

	// 获取系统健康状态
	healthStatus := h.getSystemHealthStatus(ctx)
	overview.HealthStatus = healthStatus

	// 获取性能指标
	performanceMetrics := h.getPerformanceMetricsData(ctx)
	overview.PerformanceMetrics = performanceMetrics

	// 获取最近活动（从审计日志获取）
	recentActivities := h.getRecentActivities(ctx, 10)
	overview.RecentActivities = recentActivities

	h.logger.Info("Successfully retrieved dashboard overview",
		zap.String("request_id", requestID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取仪表盘概览成功",
		"data":    overview,
	})
}

// getSystemHealthStatus 获取系统健康状态
func (h *AdminDashboardHandler) getSystemHealthStatus(ctx context.Context) SystemHealthStatus {
	now := util.NowBeijing()

	// 使用context进行超时控制的健康检查
	healthCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 并发检查各个组件的健康状态
	type healthResult struct {
		component string
		health    HealthComponent
	}

	resultChan := make(chan healthResult, 4)

	// 检查数据库健康状态
	go func() {
		start := util.NowBeijing()
		status, message := h.checkDatabaseHealth(healthCtx)
		resultChan <- healthResult{
			component: "database",
			health: HealthComponent{
				Status:       status,
				Message:      message,
				ResponseTime: time.Since(start).String(),
				LastCheck:    now,
			},
		}
	}()

	// 检查Redis健康状态
	go func() {
		start := util.NowBeijing()
		status, message := h.checkRedisHealth(healthCtx)
		resultChan <- healthResult{
			component: "redis",
			health: HealthComponent{
				Status:       status,
				Message:      message,
				ResponseTime: time.Since(start).String(),
				LastCheck:    now,
			},
		}
	}()

	// 检查API健康状态
	go func() {
		start := util.NowBeijing()
		status, message := h.checkAPIHealth(healthCtx)
		resultChan <- healthResult{
			component: "api",
			health: HealthComponent{
				Status:       status,
				Message:      message,
				ResponseTime: time.Since(start).String(),
				LastCheck:    now,
			},
		}
	}()

	// 检查服务健康状态
	go func() {
		start := util.NowBeijing()
		status, message := h.checkServicesHealth(healthCtx)
		resultChan <- healthResult{
			component: "services",
			health: HealthComponent{
				Status:       status,
				Message:      message,
				ResponseTime: time.Since(start).String(),
				LastCheck:    now,
			},
		}
	}()

	// 收集结果
	healthStatus := SystemHealthStatus{
		Services:  make(map[string]HealthComponent),
		LastCheck: now,
	}

	healthyCount := 0
	totalChecks := 4

	for i := 0; i < totalChecks; i++ {
		select {
		case result := <-resultChan:
			switch result.component {
			case "database":
				healthStatus.Database = result.health
			case "redis":
				healthStatus.Redis = result.health
			case "api":
				healthStatus.API = result.health
			case "services":
				// 服务检查结果存储在Services map中
				healthStatus.Services["express_service"] = result.health
				healthStatus.Services["balance_service"] = result.health
			}

			if result.health.Status == "healthy" {
				healthyCount++
			}
		case <-healthCtx.Done():
			h.logger.Warn("Health check timeout", zap.Error(healthCtx.Err()))
			// 设置超时的组件为warning状态
			healthStatus.Overall = "warning"
		}
	}

	// 确定整体健康状态
	if healthyCount == totalChecks {
		healthStatus.Overall = "healthy"
	} else if healthyCount >= totalChecks/2 {
		healthStatus.Overall = "warning"
	} else {
		healthStatus.Overall = "critical"
	}

	return healthStatus
}

// getPerformanceMetricsData 获取性能指标数据
func (h *AdminDashboardHandler) getPerformanceMetricsData(ctx context.Context) interface{} {
	// 使用context进行超时控制
	select {
	case <-ctx.Done():
		h.logger.Warn("性能指标获取超时", zap.Error(ctx.Err()))
		return map[string]interface{}{
			"status":    "timeout",
			"message":   "性能指标获取超时",
			"timestamp": util.NowBeijing().Unix(),
		}
	default:
		// 使用runtime包获取真实的性能指标
		var m runtime.MemStats
		runtime.ReadMemStats(&m)

		// 获取数据库连接池状态（如果可用）
		var dbStats sql.DBStats
		if h.db != nil {
			dbStats = h.db.Stats()
		}

		// 获取真实的系统性能指标
		return map[string]interface{}{
			"memory_usage_mb":     m.Alloc / 1024 / 1024,
			"memory_total_mb":     m.Sys / 1024 / 1024,
			"gc_count":            m.NumGC,
			"goroutine_count":     runtime.NumGoroutine(),
			"cpu_count":           runtime.NumCPU(),
			"heap_objects":        m.HeapObjects,
			"heap_alloc_mb":       m.HeapAlloc / 1024 / 1024,
			"heap_sys_mb":         m.HeapSys / 1024 / 1024,
			"stack_inuse_mb":      m.StackInuse / 1024 / 1024,
			"last_gc_time":        time.Unix(0, int64(m.LastGC)).Format(time.RFC3339),
			"db_open_connections": dbStats.OpenConnections,
			"db_in_use":           dbStats.InUse,
			"db_idle":             dbStats.Idle,
			"timestamp":           util.NowBeijing().Unix(),
			"status":              "healthy",
		}
	}
}

// getRecentActivities 获取最近活动
func (h *AdminDashboardHandler) getRecentActivities(ctx context.Context, limit int) []RecentActivity {
	if h.db == nil {
		h.logger.Warn("数据库连接未初始化，无法获取最近活动")
		return []RecentActivity{}
	}

	// 从数据库查询最近的用户活动
	query := `
		SELECT
			id,
			'user' as type,
			CASE
				WHEN created_at > updated_at THEN 'create'
				ELSE 'update'
			END as action,
			'用户活动: ' || username as description,
			username,
			created_at
		FROM users
		WHERE created_at >= NOW() - INTERVAL '24 hours'
		ORDER BY created_at DESC
		LIMIT $1
	`

	rows, err := h.db.QueryContext(ctx, query, limit)
	if err != nil {
		h.logger.Error("查询最近活动失败", zap.Error(err))
		return []RecentActivity{}
	}
	defer rows.Close()

	var activities []RecentActivity
	for rows.Next() {
		var activity RecentActivity
		err := rows.Scan(
			&activity.ID,
			&activity.Type,
			&activity.Action,
			&activity.Description,
			&activity.Username,
			&activity.Timestamp,
		)
		if err != nil {
			h.logger.Error("扫描活动记录失败", zap.Error(err))
			continue
		}
		activities = append(activities, activity)
	}

	h.logger.Info("获取最近活动成功",
		zap.Int("limit", limit),
		zap.Int("count", len(activities)))

	return activities
}

// GetUserStatsSnapshot 获取用户统计快照
func (h *AdminDashboardHandler) GetUserStatsSnapshot(c *gin.Context) {
	requestID := getRequestID(c)

	userStats, err := h.adminUserService.GetUserStatistics()
	if err != nil {
		h.logger.Error("Failed to get user statistics snapshot",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户统计失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取用户统计成功",
		"data":    userStats,
	})
}

// GetOrderStatsSnapshot 获取订单统计快照
func (h *AdminDashboardHandler) GetOrderStatsSnapshot(c *gin.Context) {
	requestID := getRequestID(c)

	// 调用真实的订单统计服务
	ctx := c.Request.Context()
	orderStats, err := h.adminOrderService.GetAdminOrderStatistics(ctx, nil)
	if err != nil {
		h.logger.Error("Failed to get order statistics snapshot",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取订单统计失败",
		})
		return
	}

	h.logger.Info("Retrieved order statistics snapshot",
		zap.String("request_id", requestID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取订单统计成功",
		"data":    orderStats,
	})
}

// GetBalanceStatsSnapshot 获取余额统计快照
func (h *AdminDashboardHandler) GetBalanceStatsSnapshot(c *gin.Context) {
	requestID := getRequestID(c)

	// 调用真实的余额统计服务
	ctx := c.Request.Context()
	balanceStats, err := h.adminBalanceService.GetBalanceStatistics(ctx)
	if err != nil {
		h.logger.Error("Failed to get balance statistics snapshot",
			zap.String("request_id", requestID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取余额统计失败",
		})
		return
	}

	h.logger.Info("Retrieved balance statistics snapshot",
		zap.String("request_id", requestID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取余额统计成功",
		"data":    balanceStats,
	})
}

// GetSystemHealth 获取系统健康状态
func (h *AdminDashboardHandler) GetSystemHealth(c *gin.Context) {
	requestID := getRequestID(c)

	healthStatus := h.getSystemHealthStatus(c.Request.Context())

	h.logger.Info("Retrieved system health status",
		zap.String("request_id", requestID),
		zap.String("overall_status", healthStatus.Overall))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取系统健康状态成功",
		"data":    healthStatus,
	})
}

// GetPerformanceMetrics 获取性能指标
func (h *AdminDashboardHandler) GetPerformanceMetrics(c *gin.Context) {
	requestID := getRequestID(c)

	// 使用真实的性能监控数据
	performanceMetrics := h.getPerformanceMetricsData(c.Request.Context())

	h.logger.Info("Retrieved performance metrics",
		zap.String("request_id", requestID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取性能指标成功",
		"data":    performanceMetrics,
	})
}

// GetRecentActivities 获取最近活动
func (h *AdminDashboardHandler) GetRecentActivities(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取查询参数
	limitStr := c.DefaultQuery("limit", "20")
	limit := 20
	if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
		limit = l
	}

	activities := h.getRecentActivities(c.Request.Context(), limit)

	h.logger.Info("Retrieved recent activities",
		zap.String("request_id", requestID),
		zap.Int("count", len(activities)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取最近活动成功",
		"data":    activities,
	})
}

// GetRealtimeStats 获取实时统计
func (h *AdminDashboardHandler) GetRealtimeStats(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取真实的实时统计数据
	ctx := c.Request.Context()
	realtimeStats := h.getRealTimeStatistics(ctx)

	h.logger.Info("Retrieved realtime statistics",
		zap.String("request_id", requestID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取实时统计成功",
		"data":    realtimeStats,
	})
}

// getRealTimeStatistics 获取真实的实时统计数据
func (h *AdminDashboardHandler) getRealTimeStatistics(ctx context.Context) map[string]interface{} {
	// 使用context进行超时控制
	select {
	case <-ctx.Done():
		h.logger.Warn("实时统计获取超时", zap.Error(ctx.Err()))
		return map[string]interface{}{
			"status":       "timeout",
			"message":      "实时统计获取超时",
			"last_updated": util.NowBeijing().Unix(),
		}
	default:
		// 获取真实的实时系统统计
		var m runtime.MemStats
		runtime.ReadMemStats(&m)

		// 获取数据库连接池状态
		var dbStats sql.DBStats
		if h.db != nil {
			dbStats = h.db.Stats()
		}

		// 检查Redis连接状态
		redisStatus := "unknown"
		if h.redis != nil {
			if result := h.redis.Ping(ctx); result.Err() == nil {
				redisStatus = "connected"
			} else {
				redisStatus = "disconnected"
			}
		}

		return map[string]interface{}{
			"memory_usage_mb":     m.Alloc / 1024 / 1024,
			"goroutine_count":     runtime.NumGoroutine(),
			"gc_count":            m.NumGC,
			"db_open_connections": dbStats.OpenConnections,
			"db_in_use":           dbStats.InUse,
			"db_idle":             dbStats.Idle,
			"redis_status":        redisStatus,
			"cpu_count":           runtime.NumCPU(),
			"last_updated":        util.NowBeijing().Unix(),
			"status":              "active",
		}
	}
}

// 健康检查方法

// checkDatabaseHealth 检查数据库健康状态
func (h *AdminDashboardHandler) checkDatabaseHealth(ctx context.Context) (string, string) {
	if h.db == nil {
		return "critical", "数据库连接未初始化"
	}

	// 使用context进行超时控制的数据库ping
	err := h.db.PingContext(ctx)
	if err != nil {
		h.logger.Error("数据库健康检查失败", zap.Error(err))
		return "critical", "数据库连接失败: " + err.Error()
	}

	// 检查数据库连接池状态
	stats := h.db.Stats()
	if stats.OpenConnections == 0 {
		return "warning", "数据库连接池无活跃连接"
	}

	return "healthy", "数据库连接正常"
}

// checkRedisHealth 检查Redis健康状态
func (h *AdminDashboardHandler) checkRedisHealth(ctx context.Context) (string, string) {
	if h.redis == nil {
		return "warning", "Redis连接未配置"
	}

	// 使用context进行超时控制的Redis ping
	result := h.redis.Ping(ctx)
	if result.Err() != nil {
		h.logger.Error("Redis健康检查失败", zap.Error(result.Err()))
		return "critical", "Redis连接失败: " + result.Err().Error()
	}

	// 检查Redis响应
	if result.Val() != "PONG" {
		return "warning", "Redis响应异常: " + result.Val()
	}

	return "healthy", "Redis连接正常"
}

// checkAPIHealth 检查API健康状态
func (h *AdminDashboardHandler) checkAPIHealth(ctx context.Context) (string, string) {
	// 使用context进行超时控制
	select {
	case <-ctx.Done():
		return "critical", "API健康检查超时"
	default:
		// 使用runtime包获取真实的系统指标
		var m runtime.MemStats
		runtime.ReadMemStats(&m)

		// 检查内存使用情况
		memUsageMB := m.Alloc / 1024 / 1024 // 字节转MB
		if memUsageMB > 1000 {              // 超过1GB
			return "warning", fmt.Sprintf("内存使用过高: %dMB", memUsageMB)
		}

		// 检查Goroutine数量
		goroutineCount := runtime.NumGoroutine()
		if goroutineCount > 1000 {
			return "warning", fmt.Sprintf("Goroutine数量过多: %d", goroutineCount)
		}

		// 检查GC频率（如果GC过于频繁可能表示内存压力大）
		if m.NumGC > 0 && m.PauseTotalNs > 100*1000*1000 { // 总暂停时间超过100ms
			return "warning", "GC暂停时间过长，可能存在内存压力"
		}

		return "healthy", "API服务正常"
	}
}

// checkServicesHealth 检查服务健康状态
func (h *AdminDashboardHandler) checkServicesHealth(ctx context.Context) (string, string) {
	// 检查各个服务的健康状态
	// 这里可以检查数据库、Redis等核心服务

	// 检查数据库连接
	if h.db != nil {
		if err := h.db.PingContext(ctx); err != nil {
			return "critical", "数据库服务异常"
		}
	}

	// 检查Redis连接
	if h.redis != nil {
		if result := h.redis.Ping(ctx); result.Err() != nil {
			return "warning", "Redis服务异常"
		}
	}

	return "healthy", "所有服务正常"
}
