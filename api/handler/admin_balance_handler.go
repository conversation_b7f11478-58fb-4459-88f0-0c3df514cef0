package handler

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// Context key types to avoid collisions
type contextKey string

const (
	contextKeyClientIP  = contextKey("client_ip")
	contextKeyUserAgent = contextKey("user_agent")
)

// AdminBalanceHandler 管理员余额处理器
type AdminBalanceHandler struct {
	adminBalanceService service.AdminBalanceService
	logger              *zap.Logger
}

// NewAdminBalanceHandler 创建管理员余额处理器
func NewAdminBalanceHandler(adminBalanceService service.AdminBalanceService, logger *zap.Logger) *AdminBalanceHandler {
	return &AdminBalanceHandler{
		adminBalanceService: adminBalanceService,
		logger:              logger,
	}
}

// setNoCacheHeaders 设置无缓存响应头，确保余额数据实时性
func (h *AdminBalanceHandler) setNoCacheHeaders(c *gin.Context) {
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")
}

// GetBalanceOverview 获取余额总览
func (h *AdminBalanceHandler) GetBalanceOverview(c *gin.Context) {
	// 设置无缓存响应头，确保数据实时性
	h.setNoCacheHeaders(c)

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	overview, err := h.adminBalanceService.GetBalanceOverview(ctx)
	if err != nil {
		h.logger.Error("获取余额总览失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取余额总览失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取余额总览成功",
		"data":    overview,
	})
}

// GetUserBalanceList 获取用户余额列表
func (h *AdminBalanceHandler) GetUserBalanceList(c *gin.Context) {
	// 设置无缓存响应头，确保数据实时性
	h.setNoCacheHeaders(c)

	var req model.UserBalanceListRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("绑定查询参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	response, err := h.adminBalanceService.GetUserBalanceList(ctx, &req)
	if err != nil {
		h.logger.Error("获取用户余额列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户余额列表失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取用户余额列表成功",
		"data":    response,
	})
}

// GetUserBalanceDetail 获取用户余额详情
func (h *AdminBalanceHandler) GetUserBalanceDetail(c *gin.Context) {
	// 设置无缓存响应头，确保数据实时性
	h.setNoCacheHeaders(c)

	userID := c.Param("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "用户ID不能为空",
		})
		return
	}

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	detail, err := h.adminBalanceService.GetUserBalanceDetail(ctx, userID)
	if err != nil {
		h.logger.Error("获取用户余额详情失败", zap.String("user_id", userID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户余额详情失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取用户余额详情成功",
		"data":    detail,
	})
}

// GetBalanceAnomalies 获取余额异常
func (h *AdminBalanceHandler) GetBalanceAnomalies(c *gin.Context) {
	// 设置无缓存响应头，确保数据实时性
	h.setNoCacheHeaders(c)

	var req model.AnomalyRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("绑定查询参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	response, err := h.adminBalanceService.GetBalanceAnomalies(ctx, &req)
	if err != nil {
		h.logger.Error("获取余额异常失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取余额异常失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取余额异常成功",
		"data":    response,
	})
}

// ManualDeposit 手动充值
func (h *AdminBalanceHandler) ManualDeposit(c *gin.Context) {
	var req model.AdminDepositRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	adminID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权",
		})
		return
	}
	req.AdminID = adminID.(string)

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	response, err := h.adminBalanceService.ManualDeposit(ctx, &req)
	if err != nil {
		h.logger.Error("手动充值失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "手动充值成功",
		"data":    response,
	})
}

// AdjustBalance 调整余额
func (h *AdminBalanceHandler) AdjustBalance(c *gin.Context) {
	var req model.BalanceAdjustmentRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	adminID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权",
		})
		return
	}
	req.AdminID = adminID.(string)

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	response, err := h.adminBalanceService.AdjustBalance(ctx, &req)
	if err != nil {
		h.logger.Error("调整余额失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "调整余额成功",
		"data":    response,
	})
}

// ForceRefund 强制退款
func (h *AdminBalanceHandler) ForceRefund(c *gin.Context) {
	var req model.ForceRefundRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	adminID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权",
		})
		return
	}
	req.AdminID = adminID.(string)

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	response, err := h.adminBalanceService.ForceRefund(ctx, &req)
	if err != nil {
		h.logger.Error("强制退款失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "强制退款成功",
		"data":    response,
	})
}

// BatchOperation 批量操作
func (h *AdminBalanceHandler) BatchOperation(c *gin.Context) {
	var req model.BatchOperationRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定请求参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	adminID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权",
		})
		return
	}
	req.AdminID = adminID.(string)

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	response, err := h.adminBalanceService.BatchOperation(ctx, &req)
	if err != nil {
		h.logger.Error("批量操作失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "批量操作完成",
		"data":    response,
	})
}

// GetBalanceStatistics 获取余额统计报表
func (h *AdminBalanceHandler) GetBalanceStatistics(c *gin.Context) {
	// 设置无缓存响应头，确保数据实时性
	h.setNoCacheHeaders(c)

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	response, err := h.adminBalanceService.GetBalanceStatistics(ctx)
	if err != nil {
		h.logger.Error("获取余额统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取余额统计成功",
		"data":    response,
	})
}

// GetTransactionStatistics 获取交易统计报表
func (h *AdminBalanceHandler) GetTransactionStatistics(c *gin.Context) {
	// 设置无缓存响应头，确保数据实时性
	h.setNoCacheHeaders(c)

	var req model.TransactionStatisticsRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("绑定查询参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	response, err := h.adminBalanceService.GetTransactionStatistics(ctx, &req)
	if err != nil {
		h.logger.Error("获取交易统计失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取交易统计成功",
		"data":    response,
	})
}

// GetAuditLogs 获取审计日志
func (h *AdminBalanceHandler) GetAuditLogs(c *gin.Context) {
	// 设置无缓存响应头，确保数据实时性
	h.setNoCacheHeaders(c)

	var req model.AuditLogRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("绑定查询参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	response, err := h.adminBalanceService.GetAuditLogs(ctx, &req)
	if err != nil {
		h.logger.Error("获取审计日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取审计日志成功",
		"data":    response,
	})
}

// ExportAuditLogs 导出审计日志
func (h *AdminBalanceHandler) ExportAuditLogs(c *gin.Context) {
	var req model.ExportAuditLogRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("绑定查询参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	adminID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权",
		})
		return
	}
	req.AdminID = adminID.(string)

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	fileData, filename, err := h.adminBalanceService.ExportAuditLogs(ctx, &req)
	if err != nil {
		h.logger.Error("导出审计日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileData)
}

// ExportUserBalances 导出用户余额数据
func (h *AdminBalanceHandler) ExportUserBalances(c *gin.Context) {
	var req model.ExportUserBalanceRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("绑定查询参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	adminID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权",
		})
		return
	}
	req.AdminID = adminID.(string)

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	fileData, filename, err := h.adminBalanceService.ExportUserBalances(ctx, &req)
	if err != nil {
		h.logger.Error("导出用户余额失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileData)
}

// ExportTransactions 导出交易记录
func (h *AdminBalanceHandler) ExportTransactions(c *gin.Context) {
	var req model.ExportTransactionRequest

	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("绑定查询参数失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
		})
		return
	}

	adminID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "未授权",
		})
		return
	}
	req.AdminID = adminID.(string)

	ctx := context.WithValue(c.Request.Context(), contextKeyClientIP, c.ClientIP())
	ctx = context.WithValue(ctx, contextKeyUserAgent, c.GetHeader("User-Agent"))

	fileData, filename, err := h.adminBalanceService.ExportTransactions(ctx, &req)
	if err != nil {
		h.logger.Error("导出交易记录失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileData)
}
