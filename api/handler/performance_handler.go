package handler

import (
	"net/http"
	"runtime"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/middleware"
	"github.com/your-org/go-kuaidi/internal/benchmark"
	"github.com/your-org/go-kuaidi/internal/memory"
	"github.com/your-org/go-kuaidi/internal/pool"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// PerformanceHandler 性能监控处理器
type PerformanceHandler struct {
	performanceMonitor *middleware.PerformanceMonitor
}

// NewPerformanceHandler 创建性能监控处理器
func NewPerformanceHandler() *PerformanceHandler {
	return &PerformanceHandler{
		performanceMonitor: middleware.GlobalPerformanceMonitor,
	}
}

// GetMetrics 获取性能指标
func (h *PerformanceHandler) GetMetrics(c *gin.Context) {
	metrics := h.performanceMonitor.GetMetrics()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取性能指标成功",
		"data":    metrics,
	})
}

// GetSummary 获取性能摘要
func (h *PerformanceHandler) GetSummary(c *gin.Context) {
	summary := h.performanceMonitor.GetSummary()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取性能摘要成功",
		"data":    summary,
	})
}

// GetSystemInfo 获取系统信息
func (h *PerformanceHandler) GetSystemInfo(c *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	systemInfo := gin.H{
		"go_version":      runtime.Version(),
		"go_os":           runtime.GOOS,
		"go_arch":         runtime.GOARCH,
		"cpu_count":       runtime.NumCPU(),
		"goroutine_count": runtime.NumGoroutine(),
		"memory": gin.H{
			"alloc_mb":        bToMb(m.Alloc),
			"total_alloc_mb":  bToMb(m.TotalAlloc),
			"sys_mb":          bToMb(m.Sys),
			"num_gc":          m.NumGC,
			"gc_cpu_fraction": m.GCCPUFraction,
		},
		"uptime": time.Since(startTime).String(),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取系统信息成功",
		"data":    systemInfo,
	})
}

// GetWorkerPoolStats 获取Worker Pool统计信息
func (h *PerformanceHandler) GetWorkerPoolStats(c *gin.Context) {
	var stats interface{}

	if pool.DefaultWorkerPool != nil {
		stats = pool.DefaultWorkerPool.GetStats()
	} else {
		stats = gin.H{
			"status": "Worker Pool not initialized",
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取Worker Pool统计信息成功",
		"data":    stats,
	})
}

// ResetMetrics 重置性能指标
func (h *PerformanceHandler) ResetMetrics(c *gin.Context) {
	h.performanceMonitor.Reset()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "性能指标已重置",
	})
}

// HealthCheck 健康检查
func (h *PerformanceHandler) HealthCheck(c *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 简单的健康检查逻辑
	isHealthy := true
	var issues []string

	// 检查内存使用
	if bToMb(m.Alloc) > 1000 { // 超过1GB
		isHealthy = false
		issues = append(issues, "High memory usage")
	}

	// 检查Goroutine数量
	if runtime.NumGoroutine() > 1000 {
		isHealthy = false
		issues = append(issues, "Too many goroutines")
	}

	status := "healthy"
	if !isHealthy {
		status = "unhealthy"
	}

	response := gin.H{
		"status":     status,
		"timestamp":  util.NowBeijing().Unix(),
		"uptime":     time.Since(startTime).String(),
		"version":    "1.0.0",
		"is_healthy": isHealthy,
	}

	if len(issues) > 0 {
		response["issues"] = issues
	}

	statusCode := http.StatusOK
	if !isHealthy {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, response)
}

// GetCacheStats 获取缓存统计信息
func (h *PerformanceHandler) GetCacheStats(c *gin.Context) {
	// 这里可以添加缓存统计信息的获取逻辑
	// 目前返回一个占位符
	cacheStats := gin.H{
		"l1_cache": gin.H{
			"hits":   0,
			"misses": 0,
			"size":   0,
		},
		"l2_cache": gin.H{
			"hits":   0,
			"misses": 0,
			"size":   0,
		},
		"hit_rate": 0.0,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取缓存统计信息成功",
		"data":    cacheStats,
	})
}

// bToMb 将字节转换为MB
func bToMb(b uint64) uint64 {
	return b / 1024 / 1024
}

// startTime 服务启动时间
var startTime = util.NowBeijing()

// GetPerformanceReport 获取完整的性能报告
func (h *PerformanceHandler) GetPerformanceReport(c *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 获取性能指标
	metrics := h.performanceMonitor.GetMetrics()
	summary := h.performanceMonitor.GetSummary()

	// 获取Worker Pool统计
	var workerPoolStats interface{}
	if pool.DefaultWorkerPool != nil {
		workerPoolStats = pool.DefaultWorkerPool.GetStats()
	}

	report := gin.H{
		"timestamp": util.NowBeijing().Unix(),
		"uptime":    time.Since(startTime).String(),
		"system": gin.H{
			"go_version":      runtime.Version(),
			"cpu_count":       runtime.NumCPU(),
			"goroutine_count": runtime.NumGoroutine(),
			"memory": gin.H{
				"alloc_mb":       bToMb(m.Alloc),
				"total_alloc_mb": bToMb(m.TotalAlloc),
				"sys_mb":         bToMb(m.Sys),
				"num_gc":         m.NumGC,
			},
		},
		"performance": gin.H{
			"summary":     summary,
			"metrics":     metrics,
			"worker_pool": workerPoolStats,
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取性能报告成功",
		"data":    report,
	})
}

// EnablePerformanceMonitoring 启用性能监控
func (h *PerformanceHandler) EnablePerformanceMonitoring(c *gin.Context) {
	// 这里可以添加启用性能监控的逻辑
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "性能监控已启用",
	})
}

// DisablePerformanceMonitoring 禁用性能监控
func (h *PerformanceHandler) DisablePerformanceMonitoring(c *gin.Context) {
	// 这里可以添加禁用性能监控的逻辑
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "性能监控已禁用",
	})
}

// TriggerGC 触发垃圾回收 - 已禁用（生产环境安全）
func (h *PerformanceHandler) TriggerGC(c *gin.Context) {
	c.JSON(http.StatusForbidden, gin.H{
		"success": false,
		"code":    http.StatusForbidden,
		"message": "手动垃圾回收已禁用，系统自动管理内存",
		"error":   "Manual GC disabled for production safety",
	})
}

// 🚀 新增：高级性能监控和优化功能

// GetGCStats 获取GC统计信息
func (h *PerformanceHandler) GetGCStats(c *gin.Context) {
	gcOptimizer := memory.GetGlobalGCOptimizer()
	stats := gcOptimizer.GetStats()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取GC统计成功",
		"data": map[string]interface{}{
			"gc_count":       stats.NumGC,
			"pause_total_ms": stats.PauseTotal.Milliseconds(),
			"pause_avg_ms":   stats.PauseAvg.Milliseconds(),
			"pause_max_ms":   stats.PauseMax.Milliseconds(),
			"heap_alloc_mb":  stats.HeapAlloc / 1024 / 1024,
			"heap_sys_mb":    stats.HeapSys / 1024 / 1024,
			"heap_inuse_mb":  stats.HeapInuse / 1024 / 1024,
			"live_objects":   stats.LiveObjs,
			"pool_hits":      stats.PoolHits,
			"pool_misses":    stats.PoolMisses,
			"last_update":    stats.LastUpdate,
		},
	})
}

// GetJSONStats 获取JSON优化器统计
func (h *PerformanceHandler) GetJSONStats(c *gin.Context) {
	jsonOptimizer := util.GetGlobalJSONOptimizer()
	stats := jsonOptimizer.GetStats()
	performance := jsonOptimizer.GetAveragePerformance()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "获取JSON统计成功",
		"data": map[string]interface{}{
			"marshal_count":       stats.MarshalCount,
			"unmarshal_count":     stats.UnmarshalCount,
			"pool_hits":           stats.PoolHits,
			"pool_misses":         stats.PoolMisses,
			"average_performance": performance,
		},
	})
}

// RunBenchmark 运行性能基准测试
func (h *PerformanceHandler) RunBenchmark(c *gin.Context) {
	logger, _ := zap.NewProduction()

	// 创建基准测试配置
	config := &benchmark.BenchmarkConfig{
		Duration:          2 * time.Minute, // 缩短测试时间
		ConcurrencyLevels: []int{10, 50, 100},
		MonitorInterval:   10 * time.Second,
		Scenarios: []benchmark.TestScenario{
			benchmark.CreatePriceQueryScenario(),
		},
	}

	// 创建基准测试器
	benchmarker := benchmark.NewPerformanceBenchmark(logger, config)

	// 在后台运行基准测试
	go func() {
		results, err := benchmarker.RunBenchmark()
		if err != nil {
			logger.Error("基准测试失败", zap.Error(err))
			return
		}

		logger.Info("基准测试完成",
			zap.Float64("performance_score", results.PerformanceScore),
			zap.Strings("passed_targets", results.PassedTargets),
			zap.Strings("failed_targets", results.FailedTargets))
	}()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "基准测试已启动，请稍后查看日志获取结果",
		"data": map[string]interface{}{
			"duration":           config.Duration.String(),
			"concurrency_levels": config.ConcurrencyLevels,
			"scenarios":          len(config.Scenarios),
		},
	})
}

// OptimizeMemory 优化内存使用
func (h *PerformanceHandler) OptimizeMemory(c *gin.Context) {
	gcOptimizer := memory.GetGlobalGCOptimizer()

	// 强制GC
	gcOptimizer.ForceGC()

	// 获取优化后的统计
	stats := gcOptimizer.GetStats()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "内存优化完成",
		"data": map[string]interface{}{
			"heap_alloc_mb": stats.HeapAlloc / 1024 / 1024,
			"heap_inuse_mb": stats.HeapInuse / 1024 / 1024,
			"gc_count":      stats.NumGC,
			"live_objects":  stats.LiveObjs,
		},
	})
}
