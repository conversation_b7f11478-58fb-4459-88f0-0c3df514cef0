package handler

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/security"
	"github.com/your-org/go-kuaidi/internal/user"
	"go.uber.org/zap"
)

const (
	// 安全常量
	MaxLoginAttempts    = 5                // 最大登录尝试次数
	LockoutDuration     = 30 * time.Minute // 账户锁定时间
	MinPasswordLength   = 8                // 最小密码长度
	MaxPasswordLength   = 128              // 最大密码长度
	TokenValidityPeriod = 1 * time.Hour    // 令牌有效期
)

// AdminAuthHandler 管理员认证处理器
type AdminAuthHandler struct {
	userService     user.UserService
	userRoleService user.UserRoleService
	tokenService    auth.TokenService
	securityService *security.LoginSecurityService
	logger          *zap.Logger
	tokenExpiry     int // JWT token过期时间（秒）
}

// NewAdminAuthHandler 创建管理员认证处理器
func NewAdminAuthHandler(
	userService user.UserService,
	userRoleService user.UserRoleService,
	tokenService auth.TokenService,
	logger *zap.Logger,
	tokenExpirySeconds int,
) *AdminAuthHandler {
	return &AdminAuthHandler{
		userService:     userService,
		userRoleService: userRoleService,
		tokenService:    tokenService,
		securityService: security.NewLoginSecurityService(logger),
		logger:          logger,
		tokenExpiry:     tokenExpirySeconds,
	}
}

// AdminLoginRequest 管理员登录请求
type AdminLoginRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50,alphanum"`
	Password string `json:"password" binding:"required,min=8,max=128"`
}

// AdminLoginResponse 管理员登录响应
type AdminLoginResponse struct {
	Success     bool       `json:"success"`
	Code        int        `json:"code"`
	Message     string     `json:"message"`
	AccessToken string     `json:"access_token,omitempty"`
	TokenType   string     `json:"token_type,omitempty"`
	ExpiresIn   int        `json:"expires_in,omitempty"`
	AdminInfo   *AdminInfo `json:"admin_info,omitempty"`
	Permissions []string   `json:"permissions,omitempty"`
	RequestID   string     `json:"request_id,omitempty"`
}

// AdminInfo 管理员信息
type AdminInfo struct {
	ID       string   `json:"id"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Roles    []string `json:"roles"`
	IsAdmin  bool     `json:"is_admin"`
}

// AdminLogin 管理员登录
// @Summary 管理员登录
// @Description 管理员使用用户名密码登录，获取管理员访问令牌
// @Tags 管理员认证
// @Accept json
// @Produce json
// @Param request body AdminLoginRequest true "登录请求"
// @Success 200 {object} AdminLoginResponse
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/auth/login [post]
func (h *AdminAuthHandler) AdminLogin(c *gin.Context) {
	requestID := getRequestID(c)
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 安全检查：限制请求频率
	if h.securityService.IsRateLimited(clientIP) {
		h.logger.Warn("Rate limit exceeded",
			zap.String("request_id", requestID),
			zap.String("client_ip", clientIP))

		businessErr := errors.NewBusinessError(errors.ErrCodeTooManyRequests, "请求过于频繁，请稍后再试")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	var req AdminLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid login request",
			zap.String("request_id", requestID),
			zap.String("client_ip", clientIP),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 安全检查：输入验证和清理
	if err := h.validateLoginInput(&req); err != nil {
		h.logger.Warn("Invalid login input",
			zap.String("request_id", requestID),
			zap.String("username", req.Username),
			zap.String("client_ip", clientIP),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 安全检查：账户锁定状态
	if h.securityService.IsAccountLocked(req.Username, clientIP) {
		lockInfo := h.securityService.GetLockInfo(req.Username, clientIP)
		remainingTime := ""
		if lockInfo != nil {
			remainingTime = time.Until(lockInfo.UnlockTime).Round(time.Minute).String()
		}

		h.logger.Warn("Account locked due to too many failed attempts",
			zap.String("request_id", requestID),
			zap.String("username", req.Username),
			zap.String("client_ip", clientIP),
			zap.String("remaining_time", remainingTime))

		businessErr := errors.NewBusinessError(errors.ErrCodeForbidden,
			fmt.Sprintf("账户已被锁定，请%s后再试", remainingTime))
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 记录登录尝试
	h.logger.Info("Admin login attempt",
		zap.String("request_id", requestID),
		zap.String("username", req.Username),
		zap.String("client_ip", clientIP),
		zap.String("user_agent", userAgent))

	// 验证用户凭据
	user, err := h.userService.Authenticate(req.Username, req.Password)
	if err != nil {
		// 记录失败尝试并增加失败计数
		h.securityService.RecordFailedAttempt(req.Username, clientIP, userAgent)

		h.logger.Warn("Admin authentication failed",
			zap.String("request_id", requestID),
			zap.String("username", req.Username),
			zap.String("client_ip", clientIP),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户名或密码错误")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 清除失败尝试记录（登录成功）
	h.securityService.ClearFailedAttempts(req.Username, clientIP)

	// 检查用户是否激活
	if !user.IsActive {
		h.logger.Warn("Inactive user attempted admin login",
			zap.String("request_id", requestID),
			zap.String("user_id", user.ID),
			zap.String("username", req.Username))

		businessErr := errors.NewBusinessError(errors.ErrCodeForbidden, "账户已被禁用")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 检查用户是否拥有管理员角色
	hasAdminRole, err := h.userRoleService.HasRole(user.ID, "admin")
	if err != nil {
		h.logger.Error("Failed to check admin role",
			zap.String("request_id", requestID),
			zap.String("user_id", user.ID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, "权限检查失败")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 检查系统管理员角色
	hasSystemAdminRole, _ := h.userRoleService.HasRole(user.ID, "system_admin")

	if !hasAdminRole && !hasSystemAdminRole {
		h.logger.Warn("Non-admin user attempted admin login",
			zap.String("request_id", requestID),
			zap.String("user_id", user.ID),
			zap.String("username", req.Username))

		businessErr := errors.NewBusinessError(errors.ErrCodeForbidden, "您没有管理员权限")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 获取用户角色列表
	userRolesData, err := h.userRoleService.GetUserRoles(user.ID)
	var userRoles []string
	if err != nil {
		h.logger.Error("Failed to get user roles",
			zap.String("request_id", requestID),
			zap.String("user_id", user.ID),
			zap.Error(err))
		userRoles = []string{} // 设置为空数组，不影响登录
	} else {
		// 转换角色数据为字符串数组
		userRoles = make([]string, len(userRolesData))
		for i, role := range userRolesData {
			userRoles[i] = role.Name
		}
	}

	// 获取用户权限列表
	permissions, err := h.getUserPermissions(user.ID)
	if err != nil {
		h.logger.Error("Failed to get user permissions",
			zap.String("request_id", requestID),
			zap.String("user_id", user.ID),
			zap.Error(err))
		permissions = []string{} // 设置为空数组，不影响登录
	}

	// 生成管理员访问令牌（包含完整的管理员权限范围）
	adminScopes := []string{
		"admin:all", "admin:super",
		"express:read", "express:write",
		"user:read", "user:create", "user:update", "user:delete",
		"order:read", "order:write", "order:stats", "order:export",
		"balance:admin_refund", "balance:admin_statistics", "balance:admin_audit", "balance:admin_export",
		"role:read", "role:create", "role:update", "role:delete",
		"permission:read", "permission:create", "permission:update", "permission:delete",
		"audit:read", "system:admin",
	}
	accessToken, err := h.generateAdminToken(user.ID, adminScopes)
	if err != nil {
		h.logger.Error("Failed to generate admin access token",
			zap.String("request_id", requestID),
			zap.String("user_id", user.ID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, "生成访问令牌失败")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 记录成功登录
	h.logger.Info("Admin login successful",
		zap.String("request_id", requestID),
		zap.String("user_id", user.ID),
		zap.String("username", user.Username),
		zap.Strings("roles", userRoles),
		zap.String("client_ip", clientIP))

	// 返回登录成功响应
	response := AdminLoginResponse{
		Success:     true,
		Code:        http.StatusOK,
		Message:     "管理员登录成功",
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   h.tokenExpiry, // 使用实际的token过期时间
		AdminInfo: &AdminInfo{
			ID:       user.ID,
			Username: user.Username,
			Email:    user.Email,
			Roles:    userRoles,
			IsAdmin:  true,
		},
		Permissions: permissions,
		RequestID:   requestID,
	}

	c.JSON(http.StatusOK, response)
}

// generateAdminToken 生成管理员专用令牌
func (h *AdminAuthHandler) generateAdminToken(userID string, scopes []string) (string, error) {
	// 使用用户ID作为subject，而不是client_id
	return h.tokenService.GenerateUserToken(userID, scopes)
}

// getUserPermissions 获取用户权限列表
func (h *AdminAuthHandler) getUserPermissions(userID string) ([]string, error) {
	// 获取用户的所有权限
	permissions := []string{}

	// 检查各种权限
	permissionChecks := map[string]string{
		"user:read":                "查看用户",
		"user:create":              "创建用户",
		"user:update":              "更新用户",
		"user:delete":              "删除用户",
		"order:read":               "查看订单",
		"order:write":              "修改订单",
		"order:stats":              "订单统计",
		"order:export":             "导出订单",
		"balance:admin_refund":     "强制退款",
		"balance:admin_statistics": "余额统计",
		"balance:admin_audit":      "审计日志",
		"balance:admin_export":     "导出数据",
		"role:read":                "查看角色",
		"role:create":              "创建角色",
		"role:update":              "更新角色",
		"role:delete":              "删除角色",
		"permission:read":          "查看权限",
		"permission:create":        "创建权限",
		"permission:update":        "更新权限",
		"permission:delete":        "删除权限",
		"audit:read":               "查看审计日志",
		"system:admin":             "系统管理员",
	}

	for permission := range permissionChecks {
		hasPermission, err := h.userRoleService.HasPermission(userID, permission)
		if err != nil {
			continue // 忽略错误，继续检查其他权限
		}
		if hasPermission {
			permissions = append(permissions, permission)
		}
	}

	return permissions, nil
}

// 安全相关的辅助方法

// validateLoginInput 验证登录输入
func (h *AdminAuthHandler) validateLoginInput(req *AdminLoginRequest) error {
	// 清理输入
	req.Username = strings.TrimSpace(req.Username)
	req.Password = strings.TrimSpace(req.Password)

	// 验证用户名
	if len(req.Username) < 3 || len(req.Username) > 50 {
		return fmt.Errorf("用户名长度必须在3-50个字符之间")
	}

	// 验证密码长度
	if len(req.Password) < MinPasswordLength || len(req.Password) > MaxPasswordLength {
		return fmt.Errorf("密码长度必须在%d-%d个字符之间", MinPasswordLength, MaxPasswordLength)
	}

	// 检查用户名是否包含危险字符
	if strings.ContainsAny(req.Username, "<>\"'&;") {
		return fmt.Errorf("用户名包含非法字符")
	}

	return nil
}

// Close 关闭处理器，清理资源
func (h *AdminAuthHandler) Close() {
	if h.securityService != nil {
		h.securityService.Close()
	}
}
