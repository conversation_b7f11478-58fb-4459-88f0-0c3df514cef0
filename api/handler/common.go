package handler

import (
	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/security"
)

// SignatureValidator 签名验证器
type SignatureValidator struct {
	signatureService security.SignatureService
	clientRepo       auth.ClientRepository
	config           *security.SignatureConfig
}

// NewSignatureValidator 创建签名验证器
func NewSignatureValidator(clientRepo auth.ClientRepository, config *security.SignatureConfig) *SignatureValidator {
	signatureService := security.NewSignatureService(*config)
	return &SignatureValidator{
		signatureService: signatureService,
		clientRepo:       clientRepo,
		config:           config,
	}
}
