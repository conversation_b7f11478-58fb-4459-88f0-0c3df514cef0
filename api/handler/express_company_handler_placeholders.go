package handler

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// 以下是占位符实现，确保项目能够编译
// 这些方法将在后续版本中完整实现

// CreateMapping 创建映射关系
func (h *ExpressCompanyHandler) CreateMapping(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.<PERSON>(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 解析请求参数
	var req express.CreateMappingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 验证请求参数
	if err := req.Validate(); err != nil {
		h.logger.Warn("Request validation failed",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	mapping, err := h.service.CreateMapping(c.Request.Context(), req, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to create express company provider mapping",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorIDStr),
			zap.Any("request", req),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Express company provider mapping created successfully",
		zap.String("request_id", requestID),
		zap.String("mapping_id", mapping.ID),
		zap.String("company_id", mapping.CompanyID),
		zap.String("provider_id", mapping.ProviderID),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "映射关系创建成功",
		"data":    mapping,
	})
}

// GetMappings 获取映射关系列表
func (h *ExpressCompanyHandler) GetMappings(c *gin.Context) {
	requestID := getRequestID(c)

	// 解析查询参数
	var filter express.MappingFilter
	var pagination express.Pagination

	// 分页参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			pagination.Page = page
		} else {
			pagination.Page = 1
		}
	} else {
		pagination.Page = 1
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 && pageSize <= 100 {
			pagination.PageSize = pageSize
		} else {
			pagination.PageSize = 20
		}
	} else {
		pagination.PageSize = 20
	}

	// 过滤参数
	filter.CompanyID = c.Query("company_id")
	filter.ProviderID = c.Query("provider_id")

	if isSupportedStr := c.Query("is_supported"); isSupportedStr != "" {
		if isSupported, err := strconv.ParseBool(isSupportedStr); err == nil {
			filter.IsSupported = &isSupported
		}
	}

	if isPreferredStr := c.Query("is_preferred"); isPreferredStr != "" {
		if isPreferred, err := strconv.ParseBool(isPreferredStr); err == nil {
			filter.IsPreferred = &isPreferred
		}
	}

	filter.SortBy = c.Query("sort_by")
	filter.SortOrder = c.Query("sort_order")

	// 调用服务层
	result, err := h.service.GetMappings(c.Request.Context(), filter, pagination)
	if err != nil {
		h.logger.Error("Failed to get express company provider mappings",
			zap.String("request_id", requestID),
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    result,
	})
}

// GetMapping 获取映射关系详情
func (h *ExpressCompanyHandler) GetMapping(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	mappingID := c.Param("id")
	if mappingID == "" {
		h.logger.Warn("Mapping ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "映射关系ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	mapping, err := h.service.GetMappingByID(c.Request.Context(), mappingID)
	if err != nil {
		h.logger.Error("Failed to get express company provider mapping",
			zap.String("request_id", requestID),
			zap.String("mapping_id", mappingID),
			zap.Error(err))

		if err.Error() == "映射关系不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    mapping,
	})
}

// UpdateMapping 更新映射关系
func (h *ExpressCompanyHandler) UpdateMapping(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	mappingID := c.Param("id")
	if mappingID == "" {
		h.logger.Warn("Mapping ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "映射关系ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析请求参数
	var req express.UpdateMappingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 🔥 新增：获取更新前的映射信息，用于确定需要重载的供应商
	oldMapping, err := h.service.GetMappingByID(c.Request.Context(), mappingID)
	if err != nil {
		h.logger.Error("Failed to get mapping for reload check",
			zap.String("request_id", requestID),
			zap.String("mapping_id", mappingID),
			zap.Error(err))
		// 即使获取失败也继续更新流程，但会跳过热重载
	}

	// 调用服务层
	mapping, err := h.service.UpdateMapping(c.Request.Context(), mappingID, req, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to update express company provider mapping",
			zap.String("request_id", requestID),
			zap.String("mapping_id", mappingID),
			zap.String("operator_id", operatorIDStr),
			zap.Any("request", req),
			zap.Error(err))

		if err.Error() == "映射关系不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	// 🔥 新增：映射更新成功后，自动触发相关供应商的热重载
	if oldMapping != nil {
		go func() {
			// 异步执行热重载，避免阻塞响应
			ctx := context.Background()

			// 通过映射信息获取供应商代码
			providerCode, err := h.getProviderCodeByMapping(ctx, oldMapping)
			if err != nil {
				h.logger.Error("Failed to get provider code for auto reload",
					zap.String("request_id", requestID),
					zap.String("mapping_id", mappingID),
					zap.Error(err))
				return
			}

			// 触发供应商热重载
			if h.dynamicProviderManager != nil {
				if err := h.dynamicProviderManager.ReloadProvider(ctx, providerCode); err != nil {
					h.logger.Error("Failed to auto reload provider after mapping update",
						zap.String("request_id", requestID),
						zap.String("mapping_id", mappingID),
						zap.String("provider_code", providerCode),
						zap.Error(err))
				} else {
					h.logger.Info("Auto reload provider after mapping update completed",
						zap.String("request_id", requestID),
						zap.String("mapping_id", mappingID),
						zap.String("provider_code", providerCode),
						zap.String("operator_id", operatorIDStr))
				}
			}
		}()
	}

	// 返回成功响应
	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: "映射关系更新成功",
		Data:    mapping,
	})
}

// DeleteMapping 删除映射关系
func (h *ExpressCompanyHandler) DeleteMapping(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	mappingID := c.Param("id")
	if mappingID == "" {
		h.logger.Warn("Mapping ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "映射关系ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	err := h.service.DeleteMapping(c.Request.Context(), mappingID, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to delete express company provider mapping",
			zap.String("request_id", requestID),
			zap.String("mapping_id", mappingID),
			zap.String("operator_id", operatorIDStr),
			zap.Error(err))

		if err.Error() == "映射关系不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Express company provider mapping deleted successfully",
		zap.String("request_id", requestID),
		zap.String("mapping_id", mappingID),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "映射关系删除成功",
	})
}

// GetMappingsByCompany 根据快递公司获取映射关系
func (h *ExpressCompanyHandler) GetMappingsByCompany(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	companyID := c.Param("company_id")
	if companyID == "" {
		h.logger.Warn("Company ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	mappings, err := h.service.GetMappingsByCompany(c.Request.Context(), companyID)
	if err != nil {
		h.logger.Error("Failed to get mappings by company",
			zap.String("request_id", requestID),
			zap.String("company_id", companyID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    mappings,
	})
}

// GetMappingsByProvider 根据供应商获取映射关系
func (h *ExpressCompanyHandler) GetMappingsByProvider(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	providerID := c.Param("provider_id")
	if providerID == "" {
		h.logger.Warn("Provider ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "供应商ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	mappings, err := h.service.GetMappingsByProvider(c.Request.Context(), providerID)
	if err != nil {
		h.logger.Error("Failed to get mappings by provider",
			zap.String("request_id", requestID),
			zap.String("provider_id", providerID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    mappings,
	})
}

// CreateService 创建服务
func (h *ExpressCompanyHandler) CreateService(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 解析请求参数
	var req express.CreateServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	service, err := h.service.CreateService(c.Request.Context(), req, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to create express company service",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorIDStr),
			zap.Any("request", req),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Express company service created successfully",
		zap.String("request_id", requestID),
		zap.String("service_id", service.ID),
		zap.String("company_id", service.CompanyID),
		zap.String("service_code", service.ServiceCode),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "服务创建成功",
		"data":    service,
	})
}

// GetServicesByCompany 根据快递公司获取服务列表
func (h *ExpressCompanyHandler) GetServicesByCompany(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	companyID := c.Param("company_id")
	if companyID == "" {
		h.logger.Warn("Company ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	services, err := h.service.GetServicesByCompany(c.Request.Context(), companyID)
	if err != nil {
		h.logger.Error("Failed to get services by company",
			zap.String("request_id", requestID),
			zap.String("company_id", companyID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    services,
	})
}

// UpdateService 更新服务
func (h *ExpressCompanyHandler) UpdateService(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	serviceID := c.Param("id")
	if serviceID == "" {
		h.logger.Warn("Service ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "服务ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析请求参数
	var req express.UpdateServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	service, err := h.service.UpdateService(c.Request.Context(), serviceID, req, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to update express company service",
			zap.String("request_id", requestID),
			zap.String("service_id", serviceID),
			zap.String("operator_id", operatorIDStr),
			zap.Any("request", req),
			zap.Error(err))

		if err.Error() == "服务不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Express company service updated successfully",
		zap.String("request_id", requestID),
		zap.String("service_id", serviceID),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "服务更新成功",
		"data":    service,
	})
}

// DeleteService 删除服务
func (h *ExpressCompanyHandler) DeleteService(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	serviceID := c.Param("id")
	if serviceID == "" {
		h.logger.Warn("Service ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "服务ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	err := h.service.DeleteService(c.Request.Context(), serviceID, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to delete express company service",
			zap.String("request_id", requestID),
			zap.String("service_id", serviceID),
			zap.String("operator_id", operatorIDStr),
			zap.Error(err))

		if err.Error() == "服务不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Express company service deleted successfully",
		zap.String("request_id", requestID),
		zap.String("service_id", serviceID),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "服务删除成功",
	})
}

// CreateConfig 创建配置
func (h *ExpressCompanyHandler) CreateConfig(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 解析请求参数
	var req express.CreateConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	config, err := h.service.CreateConfig(c.Request.Context(), req, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to create express company config",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorIDStr),
			zap.Any("request", req),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Express company config created successfully",
		zap.String("request_id", requestID),
		zap.String("config_id", config.ID),
		zap.String("company_id", config.CompanyID),
		zap.String("config_key", config.ConfigKey),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "配置创建成功",
		"data":    config,
	})
}

// GetConfigsByCompany 根据快递公司获取配置列表
func (h *ExpressCompanyHandler) GetConfigsByCompany(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	companyID := c.Param("company_id")
	if companyID == "" {
		h.logger.Warn("Company ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	configs, err := h.service.GetConfigsByCompany(c.Request.Context(), companyID)
	if err != nil {
		h.logger.Error("Failed to get configs by company",
			zap.String("request_id", requestID),
			zap.String("company_id", companyID),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    configs,
	})
}

// GetConfig 获取特定配置
func (h *ExpressCompanyHandler) GetConfig(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	companyID := c.Param("company_id")
	configKey := c.Param("config_key")

	if companyID == "" || configKey == "" {
		h.logger.Warn("Company ID and config key are required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司ID和配置键不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	config, err := h.service.GetConfig(c.Request.Context(), companyID, configKey)
	if err != nil {
		h.logger.Error("Failed to get express company config",
			zap.String("request_id", requestID),
			zap.String("company_id", companyID),
			zap.String("config_key", configKey),
			zap.Error(err))

		if err.Error() == "配置不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    config,
	})
}

// UpdateConfig 更新配置
func (h *ExpressCompanyHandler) UpdateConfig(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	configID := c.Param("id")
	if configID == "" {
		h.logger.Warn("Config ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "配置ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析请求参数
	var req express.UpdateConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	config, err := h.service.UpdateConfig(c.Request.Context(), configID, req, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to update express company config",
			zap.String("request_id", requestID),
			zap.String("config_id", configID),
			zap.String("operator_id", operatorIDStr),
			zap.Any("request", req),
			zap.Error(err))

		if err.Error() == "配置不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Express company config updated successfully",
		zap.String("request_id", requestID),
		zap.String("config_id", configID),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "配置更新成功",
		"data":    config,
	})
}

// DeleteConfig 删除配置
func (h *ExpressCompanyHandler) DeleteConfig(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	configID := c.Param("id")
	if configID == "" {
		h.logger.Warn("Config ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "配置ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	err := h.service.DeleteConfig(c.Request.Context(), configID, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to delete express company config",
			zap.String("request_id", requestID),
			zap.String("config_id", configID),
			zap.String("operator_id", operatorIDStr),
			zap.Error(err))

		if err.Error() == "配置不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Express company config deleted successfully",
		zap.String("request_id", requestID),
		zap.String("config_id", configID),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "配置删除成功",
	})
}

// GetAuditLogs 获取审计日志列表
func (h *ExpressCompanyHandler) GetAuditLogs(c *gin.Context) {
	requestID := getRequestID(c)

	// 解析查询参数
	var filter express.AuditLogFilter
	var pagination express.Pagination

	// 分页参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			pagination.Page = page
		} else {
			pagination.Page = 1
		}
	} else {
		pagination.Page = 1
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 && pageSize <= 100 {
			pagination.PageSize = pageSize
		} else {
			pagination.PageSize = 20
		}
	} else {
		pagination.PageSize = 20
	}

	// 过滤参数
	filter.OperatorID = c.Query("operator_id")
	filter.TargetCompanyID = c.Query("target_company_id")
	filter.Action = c.Query("action")
	filter.Operation = c.Query("operation")
	filter.Result = c.Query("result")
	filter.StartTime = c.Query("start_time")
	filter.EndTime = c.Query("end_time")
	filter.SortBy = c.Query("sort_by")
	filter.SortOrder = c.Query("sort_order")

	// 调用服务层
	result, err := h.service.GetAuditLogs(c.Request.Context(), filter, pagination)
	if err != nil {
		h.logger.Error("Failed to get express company audit logs",
			zap.String("request_id", requestID),
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    result,
	})
}

// GetProviderCompanyCode 获取供应商特定的快递公司代码
func (h *ExpressCompanyHandler) GetProviderCompanyCode(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	companyCode := c.Param("company_code")
	providerCode := c.Param("provider_code")

	if companyCode == "" || providerCode == "" {
		h.logger.Warn("Company code and provider code are required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司代码和供应商代码不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	providerCompanyCode, err := h.service.GetProviderCompanyCode(c.Request.Context(), companyCode, providerCode)
	if err != nil {
		h.logger.Error("Failed to get provider company code",
			zap.String("request_id", requestID),
			zap.String("company_code", companyCode),
			zap.String("provider_code", providerCode),
			zap.Error(err))

		if err.Error() == "快递公司不存在" || err.Error() == "供应商不存在" || err.Error() == "映射关系不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data": gin.H{
			"company_code":          companyCode,
			"provider_code":         providerCode,
			"provider_company_code": providerCompanyCode,
		},
	})
}

// GetPublicCompanies 获取公开的快递公司列表
func (h *ExpressCompanyHandler) GetPublicCompanies(c *gin.Context) {
	requestID := getRequestID(c)

	// 解析查询参数
	var filter express.CompanyFilter
	var pagination express.Pagination

	// 分页参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			pagination.Page = page
		} else {
			pagination.Page = 1
		}
	} else {
		pagination.Page = 1
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 && pageSize <= 100 {
			pagination.PageSize = pageSize
		} else {
			pagination.PageSize = 20
		}
	} else {
		pagination.PageSize = 20
	}

	// 过滤参数
	filter.Keyword = c.Query("keyword")
	filter.SortBy = c.Query("sort_by")
	filter.SortOrder = c.Query("sort_order")

	// 强制只返回活跃的快递公司
	isActive := true
	filter.IsActive = &isActive

	// 调用服务层
	result, err := h.service.GetCompanies(c.Request.Context(), filter, pagination)
	if err != nil {
		h.logger.Error("Failed to get public express companies",
			zap.String("request_id", requestID),
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    result,
	})
}

// GetPublicCompanyByCode 获取公开的快递公司信息
func (h *ExpressCompanyHandler) GetPublicCompanyByCode(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	companyCode := c.Param("code")
	if companyCode == "" {
		h.logger.Warn("Company code is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司代码不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	company, err := h.service.GetCompanyByCode(c.Request.Context(), companyCode)
	if err != nil {
		h.logger.Error("Failed to get public express company by code",
			zap.String("request_id", requestID),
			zap.String("company_code", companyCode),
			zap.Error(err))

		if err.Error() == "快递公司不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	// 检查快递公司是否活跃
	if !company.IsActive {
		h.logger.Warn("Express company is not active",
			zap.String("request_id", requestID),
			zap.String("company_code", companyCode))
		err := errors.NewBusinessError(errors.ErrCodeNotFound, "快递公司不可用")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    company,
	})
}

// getProviderCodeByMapping 通过映射信息获取供应商代码
func (h *ExpressCompanyHandler) getProviderCodeByMapping(ctx context.Context, mapping *express.ExpressCompanyProviderMapping) (string, error) {
	// 构建查询参数
	filter := express.ProviderFilter{}
	pagination := express.Pagination{
		Page:     1,
		PageSize: 1000,
	}

	// 获取供应商信息
	result, err := h.service.GetProviders(ctx, filter, pagination)
	if err != nil {
		return "", fmt.Errorf("获取供应商列表失败: %w", err)
	}

	// 查找对应的供应商代码
	for _, provider := range result.Providers {
		if provider.ID == mapping.ProviderID {
			return provider.Code, nil
		}
	}

	return "", fmt.Errorf("未找到映射对应的供应商: provider_id=%s", mapping.ProviderID)
}
