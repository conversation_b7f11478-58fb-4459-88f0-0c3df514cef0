package handler

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"
)

// AdminOrderHandler 管理员订单处理器
type AdminOrderHandler struct {
	adminOrderService service.AdminOrderServiceInterface
	logger            *zap.Logger
}

// NewAdminOrderHandler 创建管理员订单处理器
func NewAdminOrderHandler(
	adminOrderService service.AdminOrderServiceInterface,
	logger *zap.Logger,
) *AdminOrderHandler {
	return &AdminOrderHandler{
		adminOrderService: adminOrderService,
		logger:            logger,
	}
}

// GetAdminOrderList 获取管理员订单列表
// @Summary 获取管理员订单列表
// @Description 管理员查询所有用户的订单列表，支持多维度过滤和搜索
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(20)
// @Param user_id query string false "用户ID过滤"
// @Param username query string false "用户名过滤"
// @Param user_email query string false "用户邮箱过滤"
// @Param status query string false "订单状态过滤"
// @Param express_type query string false "快递公司过滤"
// @Param company_code query string false "快递公司代码过滤"
// @Param provider query string false "供应商过滤"
// @Param billing_status query string false "计费状态过滤"
// @Param price_min query number false "最小价格"
// @Param price_max query number false "最大价格"
// @Param weight_min query number false "最小重量"
// @Param weight_max query number false "最大重量"
// @Param search_keyword query string false "搜索关键词"
// @Param search_fields query []string false "搜索字段"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param sort_by query string false "排序字段" default("created_at")
// @Param sort_order query string false "排序方向" default("DESC")
// @Success 200 {object} model.AdminOrderListResponse
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/orders [get]
func (h *AdminOrderHandler) GetAdminOrderList(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	// 解析查询参数
	var req model.AdminOrderListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn("Invalid query parameters",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "查询参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100 // 限制最大页面大小
	}
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc" // 使用小写，符合验证规则
	}

	h.logger.Info("Admin order list query started",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Any("request", req))

	// 调用服务层
	response, err := h.adminOrderService.GetAdminOrderList(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to get admin order list",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		businessErr := errors.WrapError(errors.ErrCodeInternal, "获取订单列表失败", err)
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 检查响应数据是否有效
	if response == nil {
		h.logger.Error("Service returned nil response",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID))
		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, "服务返回空响应")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	if response.Data == nil {
		h.logger.Error("Service returned response with nil data",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID))
		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, "服务返回数据为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 🔥 修复：处理 timestamp without time zone 的时区问题
	// 订单表使用 timestamp without time zone，需要明确标记为北京时区
	if response.Success && response.Data != nil && len(response.Data.Items) > 0 {
		for _, item := range response.Data.Items {
			// 对于 timestamp without time zone，需要明确标记为北京时区
			item.CreatedAt = util.EnsureBeijingTimezone(item.CreatedAt)
			item.UpdatedAt = util.EnsureBeijingTimezone(item.UpdatedAt)
		}
	}

	h.logger.Info("Admin order list query completed",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("total", response.Data.Total),
		zap.Int("page", response.Data.Page))

	c.JSON(http.StatusOK, response)
}

// GetAdminOrderDetail 获取管理员订单详情
// @Summary 获取管理员订单详情
// @Description 管理员查看任意订单的详细信息，包含用户信息、支付信息、物流轨迹等
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param id path int true "订单ID"
// @Success 200 {object} model.AdminOrderDetail
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/orders/{id} [get]
func (h *AdminOrderHandler) GetAdminOrderDetail(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	// 解析订单ID
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		h.logger.Warn("Invalid order ID",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.String("order_id", orderIDStr),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "无效的订单ID")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Admin order detail query started",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("order_id", orderID))

	// 调用服务层
	detail, err := h.adminOrderService.GetAdminOrderDetail(c.Request.Context(), orderID, operatorID)
	if err != nil {
		h.logger.Error("Failed to get admin order detail",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Int64("order_id", orderID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.WrapError(errors.ErrCodeInternal, "获取订单详情失败", err)
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Admin order detail query completed",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("order_id", orderID),
		zap.String("order_no", detail.OrderNo))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "查询成功",
		"data":    detail,
	})
}

// UpdateOrderStatus 更新订单状态
// @Summary 更新订单状态
// @Description 管理员手动更新订单状态，支持状态转换验证和审计记录
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param id path int true "订单ID"
// @Param request body UpdateOrderStatusRequest true "更新请求"
// @Success 200 {object} gin.H
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/orders/{id}/status [put]
func (h *AdminOrderHandler) UpdateOrderStatus(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	// 解析订单ID
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		h.logger.Warn("Invalid order ID",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.String("order_id", orderIDStr),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "无效的订单ID")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 解析请求体
	var req UpdateOrderStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Int64("order_id", orderID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 验证请求参数
	if req.NewStatus == "" {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "新状态不能为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Admin order status update started",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("order_id", orderID),
		zap.String("new_status", req.NewStatus),
		zap.String("reason", req.Reason))

	// 调用服务层
	err = h.adminOrderService.UpdateOrderStatus(c.Request.Context(), orderID, req.NewStatus, operatorID, req.Reason)
	if err != nil {
		h.logger.Error("Failed to update order status",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Int64("order_id", orderID),
			zap.String("new_status", req.NewStatus),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.WrapError(errors.ErrCodeInternal, "更新订单状态失败", err)
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Admin order status update completed",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("order_id", orderID),
		zap.String("new_status", req.NewStatus))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "订单状态更新成功",
		"data": gin.H{
			"order_id":   orderID,
			"new_status": req.NewStatus,
		},
	})
}

// UpdateOrderStatusRequest 更新订单状态请求
type UpdateOrderStatusRequest struct {
	NewStatus string `json:"new_status" binding:"required"`
	Reason    string `json:"reason"`
}

// BatchUpdateOrderStatus 批量更新订单状态
// @Summary 批量更新订单状态
// @Description 管理员批量更新多个订单的状态
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param request body BatchUpdateOrderStatusRequest true "批量更新请求"
// @Success 200 {object} service.BatchOperationResult
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/orders/batch/status [put]
func (h *AdminOrderHandler) BatchUpdateOrderStatus(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	// 解析请求体
	var req BatchUpdateOrderStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 验证请求参数
	if len(req.OrderIDs) == 0 {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "订单ID列表不能为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	if len(req.OrderIDs) > 100 {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "单次批量操作不能超过100个订单")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	if req.NewStatus == "" {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "新状态不能为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Admin batch order status update started",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int("order_count", len(req.OrderIDs)),
		zap.String("new_status", req.NewStatus),
		zap.String("reason", req.Reason))

	// 调用服务层
	result, err := h.adminOrderService.BatchUpdateOrderStatus(c.Request.Context(), req.OrderIDs, req.NewStatus, operatorID, req.Reason)
	if err != nil {
		h.logger.Error("Failed to batch update order status",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Int("order_count", len(req.OrderIDs)),
			zap.String("new_status", req.NewStatus),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.WrapError(errors.ErrCodeInternal, "批量更新订单状态失败", err)
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Admin batch order status update completed",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int("total_items", result.TotalItems),
		zap.Int("success_items", result.SuccessItems),
		zap.Int("failed_items", result.FailedItems),
		zap.Duration("duration", result.Duration))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "批量更新完成",
		"data":    result,
	})
}

// BatchUpdateOrderStatusRequest 批量更新订单状态请求
type BatchUpdateOrderStatusRequest struct {
	OrderIDs  []int64 `json:"order_ids" binding:"required,min=1,max=100"`
	NewStatus string  `json:"new_status" binding:"required"`
	Reason    string  `json:"reason"`
}

// GetAdminOrderStatistics 获取管理员订单统计
// @Summary 获取管理员订单统计
// @Description 获取管理员级别的订单统计数据，包括各种维度的统计信息
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param user_id query string false "用户ID过滤"
// @Param status query string false "订单状态过滤"
// @Param provider query string false "供应商过滤"
// @Param billing_status query string false "计费状态过滤"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Success 200 {object} model.AdminOrderStatistics
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/orders/statistics [get]
func (h *AdminOrderHandler) GetAdminOrderStatistics(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	// 解析查询参数
	var filter service.AdminStatisticsFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		h.logger.Warn("Invalid query parameters",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "查询参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Admin order statistics query started",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Any("filter", filter))

	// 调用服务层
	statistics, err := h.adminOrderService.GetAdminOrderStatistics(c.Request.Context(), &filter)
	if err != nil {
		h.logger.Error("Failed to get admin order statistics",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.WrapError(errors.ErrCodeInternal, "获取统计数据失败", err)
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Admin order statistics query completed",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("total_orders", statistics.TotalOrders),
		zap.Float64("total_amount", statistics.TotalAmount))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "查询成功",
		"data":    statistics,
	})
}

// ExportOrders 导出订单数据
// @Summary 导出订单数据
// @Description 管理员导出订单数据，支持多种格式和过滤条件
// @Tags 管理员订单管理
// @Accept json
// @Produce application/octet-stream
// @Security AdminAuth
// @Param user_id query string false "用户ID过滤"
// @Param username query string false "用户名过滤"
// @Param user_email query string false "用户邮箱过滤"
// @Param status query string false "订单状态过滤"
// @Param express_type query string false "快递公司过滤"
// @Param provider query string false "供应商过滤"
// @Param billing_status query string false "计费状态过滤"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param format query string false "导出格式" default("json") Enums(json,csv,excel)
// @Success 200 {file} file
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/orders/export [get]
func (h *AdminOrderHandler) ExportOrders(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	// 解析查询参数
	var req model.AdminOrderListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn("Invalid query parameters",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "查询参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 设置导出限制
	req.IsExport = true
	if req.PageSize <= 0 || req.PageSize > 10000 {
		req.PageSize = 10000 // 导出限制最大10000条
	}

	format := c.DefaultQuery("format", "json")

	h.logger.Info("Admin order export started",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.String("format", format),
		zap.Any("request", req))

	// 调用服务层
	data, err := h.adminOrderService.ExportOrders(c.Request.Context(), &req, operatorID)
	if err != nil {
		h.logger.Error("Failed to export orders",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.String("format", format),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.WrapError(errors.ErrCodeInternal, "导出订单数据失败", err)
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Admin order export completed",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.String("format", format),
		zap.Int("data_size", len(data)))

	// 设置响应头
	filename := fmt.Sprintf("orders_export_%s.%s", util.NowBeijing().Format("20060102_150405"), format)
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "application/octet-stream")

	c.Data(http.StatusOK, "application/octet-stream", data)
}

// QueryProviderShippingFee 查询供应商运费信息
// @Summary 查询供应商运费信息
// @Description 管理员查询指定订单的供应商运费信息
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param id path int true "订单ID"
// @Success 200 {object} model.ProviderShippingFeeResponse
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 404 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/orders/{id}/shipping-fee [get]
func (h *AdminOrderHandler) QueryProviderShippingFee(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	// 解析订单ID
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		h.logger.Warn("Invalid order ID for shipping fee query",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.String("order_id", orderIDStr),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "无效的订单ID")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Provider shipping fee query started",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("order_id", orderID))

	// 调用服务层
	response, err := h.adminOrderService.QueryProviderShippingFee(c.Request.Context(), orderID, operatorID)
	if err != nil {
		h.logger.Error("Failed to query provider shipping fee",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Int64("order_id", orderID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.WrapError(errors.ErrCodeInternal, "查询供应商运费失败", err)
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Provider shipping fee query completed",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("order_id", orderID),
		zap.Bool("success", response.Success))

	c.JSON(http.StatusOK, response)
}

// BatchQueryProviderShippingFee 批量查询供应商运费信息
// @Summary 批量查询供应商运费信息
// @Description 管理员批量查询多个订单的供应商运费信息
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param request body BatchQueryShippingFeeRequest true "批量查询请求"
// @Success 200 {object} model.BatchProviderShippingFeeResponse
// @Failure 400 {object} errors.ErrorResponse
// @Failure 401 {object} errors.ErrorResponse
// @Failure 403 {object} errors.ErrorResponse
// @Failure 500 {object} errors.ErrorResponse
// @Router /api/v1/admin/orders/batch/shipping-fee [post]
func (h *AdminOrderHandler) BatchQueryProviderShippingFee(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	// 解析请求体
	var req BatchQueryShippingFeeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body for batch shipping fee query",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 验证请求参数
	if len(req.OrderIDs) == 0 {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "订单ID列表不能为空")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	if len(req.OrderIDs) > 50 {
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "单次批量查询不能超过50个订单")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Batch provider shipping fee query started",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int("order_count", len(req.OrderIDs)))

	// 调用服务层
	response, err := h.adminOrderService.BatchQueryProviderShippingFee(c.Request.Context(), req.OrderIDs, operatorID)
	if err != nil {
		h.logger.Error("Failed to batch query provider shipping fee",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Int("order_count", len(req.OrderIDs)),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.WrapError(errors.ErrCodeInternal, "批量查询供应商运费失败", err)
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Batch provider shipping fee query completed",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int("order_count", len(req.OrderIDs)),
		zap.Bool("success", response.Success))

	c.JSON(http.StatusOK, response)
}

// BatchQueryShippingFeeRequest 批量查询运费请求
type BatchQueryShippingFeeRequest struct {
	OrderIDs []int64 `json:"order_ids" binding:"required,min=1,max=50"`
}

// QueryDetailedProviderShippingFee 查询详细的供应商运费信息
// @Summary 查询详细的供应商运费信息
// @Description 查询指定订单的详细供应商运费信息，包括费用明细和重量信息
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param id path int true "订单ID"
// @Success 200 {object} model.DetailedShippingFeeResponse "查询成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 404 {object} errors.ErrorResponse "订单不存在"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/orders/{id}/detailed-shipping-fee [get]
func (h *AdminOrderHandler) QueryDetailedProviderShippingFee(c *gin.Context) {
	// 获取请求ID和操作员ID
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	// 解析订单ID
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		h.logger.Error("Invalid order ID for detailed shipping fee query",
			zap.String("request_id", requestID),
			zap.String("order_id", orderIDStr),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "无效的订单ID")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Detailed shipping fee query started",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("order_id", orderID))

	// 调用服务层查询详细运费信息
	detailedInfo, err := h.adminOrderService.QueryDetailedProviderShippingFee(c.Request.Context(), orderID, operatorID)
	if err != nil {
		h.logger.Error("Failed to query detailed shipping fee",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Int64("order_id", orderID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.WrapError(errors.ErrCodeInternal, "查询详细运费信息失败", err)
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	// 构建响应
	response := &model.DetailedShippingFeeResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "查询成功",
		Data:    detailedInfo,
	}

	h.logger.Info("Detailed shipping fee query completed",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("order_id", orderID),
		zap.Float64("total_fee", detailedInfo.TotalFee),
		zap.Bool("supported", detailedInfo.Supported))

	c.JSON(http.StatusOK, response)
}

// BatchValidatePrices 批量验证订单价格
// @Summary 批量验证订单价格
// @Description 管理员批量验证订单价格，支持按供应商和时间段筛选
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param request body model.BatchPriceValidationRequest true "批量价格验证请求"
// @Success 200 {object} model.BatchPriceValidationResponse "验证成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/orders/batch/validate-prices [post]
func (h *AdminOrderHandler) BatchValidatePrices(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	h.logger.Info("批量价格验证请求",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID))

	// 解析请求参数
	var req model.BatchPriceValidationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	response, err := h.adminOrderService.BatchValidatePrices(c.Request.Context(), &req, operatorID)
	if err != nil {
		h.logger.Error("批量价格验证失败",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		businessErr := errors.WrapError(errors.ErrCodeInternal, "批量价格验证失败", err)
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("批量价格验证成功",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Bool("success", response.Success))

	c.JSON(http.StatusOK, response)
}

// SyncOrderStatus 同步订单状态
// @Summary 同步订单状态
// @Description 管理员手动同步指定订单的状态，从供应商获取最新状态
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param id path int true "订单ID"
// @Success 200 {object} gin.H "同步成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 404 {object} errors.ErrorResponse "订单不存在"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/orders/{id}/sync-status [post]
func (h *AdminOrderHandler) SyncOrderStatus(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	// 获取订单ID
	orderIDStr := c.Param("id")
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		h.logger.Warn("Invalid order ID for status sync",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.String("order_id", orderIDStr),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "无效的订单ID")
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Order status sync started",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("order_id", orderID))

	// 同步订单状态
	result, err := h.adminOrderService.SyncOrderStatus(c.Request.Context(), orderID)
	if err != nil {
		h.logger.Error("同步订单状态失败",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Int64("order_id", orderID),
			zap.Error(err))

		if businessErr, ok := err.(errors.BusinessError); ok {
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.WrapError(errors.ErrCodeInternal, "同步订单状态失败", err)
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Order status sync completed",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Int64("order_id", orderID),
		zap.Bool("status_changed", result.StatusChanged),
		zap.String("old_status", string(result.OldStatus)),
		zap.String("new_status", string(result.NewStatus)))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "订单状态同步成功",
		"data":    result,
	})
}

// BatchSyncOrderStatus 批量同步订单状态
// @Summary 批量同步订单状态
// @Description 管理员批量同步多个订单的状态，从供应商获取最新状态
// @Tags 管理员订单管理
// @Accept json
// @Produce json
// @Security AdminAuth
// @Param request body model.BatchStatusSyncRequest true "批量状态同步请求"
// @Success 200 {object} model.BatchStatusSyncResponse "同步成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/orders/batch/sync-status [post]
func (h *AdminOrderHandler) BatchSyncOrderStatus(c *gin.Context) {
	requestID := c.GetString("request_id")
	operatorID := c.GetString("user_id")

	h.logger.Info("批量订单状态同步请求",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID))

	// 解析请求参数
	var req model.BatchStatusSyncRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request body",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	response, err := h.adminOrderService.BatchSyncOrderStatus(c.Request.Context(), &req, operatorID)
	if err != nil {
		h.logger.Error("批量订单状态同步失败",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		businessErr := errors.WrapError(errors.ErrCodeInternal, "批量订单状态同步失败", err)
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("批量订单状态同步成功",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorID),
		zap.Bool("success", response.Success))

	c.JSON(http.StatusOK, response)
}
