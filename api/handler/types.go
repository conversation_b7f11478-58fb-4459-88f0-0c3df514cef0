package handler

import (
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
)

// SimplePriceRequest 简化价格查询请求
type SimplePriceRequest struct {
	// 寄件地址 (必填)
	FromProvince string `json:"from_province"` // 寄件省
	FromCity     string `json:"from_city"`     // 寄件市
	FromDistrict string `json:"from_district"` // 寄件区/县 (可选)

	// 收件地址 (必填)
	ToProvince string `json:"to_province"` // 收件省
	ToCity     string `json:"to_city"`     // 收件市
	ToDistrict string `json:"to_district"` // 收件区/县 (可选)

	// 快递公司查询 (可选)
	ExpressCode string `json:"express_code"` // 快递公司代码 (可选，不填则查询所有快递公司)

	// 新增：支持嵌套写法 {"sender":{...},"receiver":{...}}
	Sender struct {
		Province string `json:"province"`
		City     string `json:"city"`
		District string `json:"district"`
	} `json:"sender"`
	Receiver struct {
		Province string `json:"province"`
		City     string `json:"city"`
		District string `json:"district"`
	} `json:"receiver"`

	// 包裹信息
	Weight    float64 `json:"weight"`     // 重量(kg) (必填)
	Length    float64 `json:"length"`     // 长(cm) (可选)
	Width     float64 `json:"width"`      // 宽(cm) (可选)
	Height    float64 `json:"height"`     // 高(cm) (可选)
	Volume    float64 `json:"volume"`     // 体积(m³) (可选，如果提供长宽高则自动计算)
	Quantity  int     `json:"quantity"`   // 包裹数量 (可选，默认1)
	GoodsName string  `json:"goods_name"` // 物品名称 (可选，默认"物品")

	// 其他信息
	PayMethod int `json:"pay_method"` // 支付方式：0-寄付，1-到付，2-月结 (可选，默认0)
}

// SimplePriceResponse 简化价格查询响应
type SimplePriceResponse struct {
	Success bool                   `json:"success"`        // 是否成功
	Code    int                    `json:"code"`           // 状态码
	Message string                 `json:"message"`        // 消息
	Data    []PriceWithOrderParams `json:"data,omitempty"` // 价格数据
}

// PriceWithOrderParams 价格和下单参数（增强版，支持事务一致性）
type PriceWithOrderParams struct {
	// 价格信息
	ExpressCode          string  `json:"express_code"`            // 标准化的快递公司代码
	ExpressName          string  `json:"express_name"`            // 标准化的快递公司名称
	ProductCode          string  `json:"product_code"`            // 产品代码
	ProductName          string  `json:"product_name"`            // 产品名称
	Price                float64 `json:"price"`                   // 总价格
	ContinuedWeightPerKg float64 `json:"continued_weight_per_kg"` // 每公斤续重价格
	CalcWeight           float64 `json:"calc_weight"`             // 计费重量

	// 统一下单参数 - 用户只需使用这些标准化参数
	OrderCode string `json:"order_code"` // 统一下单代码 (用于下单API)

	// 事务一致性保证
	ExpiresAt time.Time `json:"expires_at"` // 代码过期时间

	// 取件预约信息
	PickupTimeInfo *model.PickupTimeInfo `json:"pickup_time_info,omitempty"` // 预约时间信息
}

// RealtimePriceRequest 实时查价请求（支持完整地址信息）
type RealtimePriceRequest struct {
	// 寄件人信息 (必填)
	Sender RealtimeAddressInfo `json:"sender"`

	// 收件人信息 (必填)
	Receiver RealtimeAddressInfo `json:"receiver"`

	// 包裹信息
	Weight    float64 `json:"weight"`     // 重量(kg) (必填)
	Length    float64 `json:"length"`     // 长(cm) (可选)
	Width     float64 `json:"width"`      // 宽(cm) (可选)
	Height    float64 `json:"height"`     // 高(cm) (可选)
	Volume    float64 `json:"volume"`     // 体积(m³) (可选，如果提供长宽高则自动计算)
	Quantity  int     `json:"quantity"`   // 包裹数量 (可选，默认1)
	GoodsName string  `json:"goods_name"` // 物品名称 (可选，默认"物品")

	// 其他信息
	PayMethod int `json:"pay_method"` // 支付方式：0-寄付，1-到付，2-月结 (可选，默认0)
}

// RealtimeAddressInfo 实时查价地址信息（包含完整地址）
type RealtimeAddressInfo struct {
	Name     string `json:"name"`     // 姓名 (必填)
	Mobile   string `json:"mobile"`   // 手机号 (必填)
	Province string `json:"province"` // 省份 (必填)
	City     string `json:"city"`     // 城市 (必填)
	District string `json:"district"` // 区/县 (必填)
	Address  string `json:"address"`  // 详细地址 (必填)
}

// RealtimePriceResponse 实时查价响应（标准格式）
type RealtimePriceResponse struct {
	Success bool                  `json:"success"`        // 是否成功
	Code    int                   `json:"code"`           // 状态码
	Message string                `json:"message"`        // 消息
	Data    []RealtimePriceResult `json:"data,omitempty"` // 价格数据列表（标准格式）
}

// RealtimePriceResult 实时查价结果（标准查价格式）
type RealtimePriceResult struct {
	ExpressCode          string  `json:"express_code"`            // 快递公司代码
	ExpressName          string  `json:"express_name"`            // 快递公司名称
	ProductCode          string  `json:"product_code"`            // 产品代码
	ProductName          string  `json:"product_name"`            // 产品名称
	Price                float64 `json:"price"`                   // 总价格
	ContinuedWeightPerKg float64 `json:"continued_weight_per_kg"` // 每公斤续重价格
	CalcWeight           float64 `json:"calc_weight"`             // 计费重量
	OrderCode            string  `json:"order_code"`              // 下单代码（用于后续下单）
	ExpiresAt            string  `json:"expires_at"`              // 价格有效期

	// 🚀 新增：取件预约信息
	PickupTimeInfo *model.PickupTimeInfo `json:"pickup_time_info,omitempty"` // 预约时间信息
}

// EnhancedOrderCode 增强版下单代码（内部使用，包含路由信息）
type EnhancedOrderCode struct {
	// 路由信息
	StandardCode string `json:"standard_code"` // 标准快递代码
	OriginalCode string `json:"original_code"` // 原始快递代码
	Provider     string `json:"provider"`      // 供应商
	ChannelID    string `json:"channel_id"`    // 渠道ID
	ProductCode  string `json:"product_code"`  // 产品代码

	// 🔥 移除价格锁定机制：删除 LockedPrice 字段
	// 🔥 移除过期时间机制：删除 CreatedAt、ExpiresAt、CodeID 字段

	// 原始查价请求（用于实时价格验证）
	OriginalRequest interface{} `json:"original_request"` // 原始查价请求（支持SimplePriceRequest和JDPriceRequest）
}
