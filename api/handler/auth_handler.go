package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/user"
)

// AuthHandler 处理认证相关请求
type AuthHandler struct {
	authController *auth.AuthController
	userService    user.UserService
	clientService  auth.ClientService
	tokenService   auth.TokenService
	tokenExpiry    int // JWT token过期时间（秒）
}

// NewAuthHandler 创建新的认证处理器
func NewAuthHandler(authController *auth.AuthController, userService user.UserService, clientService auth.ClientService, tokenService auth.TokenService, tokenExpirySeconds int) *AuthHandler {
	return &AuthHandler{
		authController: authController,
		userService:    userService,
		clientService:  clientService,
		tokenService:   tokenService,
		tokenExpiry:    tokenExpirySeconds,
	}
}

// HandleTokenRequest 处理令牌请求
func (h *AuthHandler) HandleTokenRequest(c *gin.Context) {
	h.authController.HandleTokenRequest(c)
}

// HandleHealthCheck 处理健康检查请求
func (h *AuthHandler) HandleHealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
	})
}

// HandleRevokeToken 处理令牌撤销请求
func (h *AuthHandler) HandleRevokeToken(c *gin.Context) {
	// 获取令牌ID
	var req struct {
		TokenID string `json:"token_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    400,
			"message": "Invalid request parameters",
		})
		return
	}

	// 撤销令牌
	if err := h.authController.RevokeToken(c, req.TokenID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    500,
			"message": "Failed to revoke token",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "Token revoked successfully",
	})
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	Success     bool      `json:"success"`
	Code        int       `json:"code"`
	Message     string    `json:"message"`
	AccessToken string    `json:"access_token,omitempty"`
	TokenType   string    `json:"token_type,omitempty"`
	ExpiresIn   int       `json:"expires_in,omitempty"`
	UserInfo    *UserInfo `json:"user_info,omitempty"`
}

// UserInfo 用户信息结构
type UserInfo struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	ClientID string `json:"client_id"`
}

// HandleLogin 处理用户登录
func (h *AuthHandler) HandleLogin(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, LoginResponse{
			Success: false,
			Code:    http.StatusBadRequest,
			Message: "Invalid request parameters: " + err.Error(),
		})
		return
	}

	// 验证用户凭据
	user, err := h.userService.Authenticate(req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, LoginResponse{
			Success: false,
			Code:    http.StatusUnauthorized,
			Message: "Invalid username or password",
		})
		return
	}

	// 获取用户的客户端信息
	client, err := h.clientService.FindByID(user.ClientID)
	if err != nil || client == nil {
		// 禁止自动补齐，直接返回错误提示管理员处理
		c.JSON(http.StatusUnauthorized, LoginResponse{
			Success: false,
			Code:    http.StatusUnauthorized,
			Message: "Client credential not found or inactive; please contact administrator.",
		})
		return
	}

	// 确保工单基本权限存在，避免老客户缺scope导致403
	essentialScopes := []string{"workorder:read", "workorder:write"}
	for _, es := range essentialScopes {
		found := false
		for _, s := range client.AllowedScopes {
			if s == es {
				found = true
				break
			}
		}
		if !found {
			client.AllowedScopes = append(client.AllowedScopes, es)
		}
	}
	// 无需持久化；token中包含完整scope

	// 🔥 修复：生成用户访问令牌，使用用户ID而不是客户端ID
	// 这样JWT token的Subject字段将包含用户ID，确保与数据库用户记录一致
	accessToken, err := h.tokenService.GenerateUserToken(user.ID, client.AllowedScopes)
	if err != nil {
		c.JSON(http.StatusInternalServerError, LoginResponse{
			Success: false,
			Code:    http.StatusInternalServerError,
			Message: "Failed to generate access token",
		})
		return
	}

	// 返回登录成功响应
	c.JSON(http.StatusOK, LoginResponse{
		Success:     true,
		Code:        http.StatusOK,
		Message:     "Login successful",
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   h.tokenExpiry, // 使用实际的token过期时间
		UserInfo: &UserInfo{
			ID:       user.ID,
			Username: user.Username,
			Email:    user.Email,
			ClientID: user.ClientID,
		},
	})
}
