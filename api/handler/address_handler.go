package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// AddressHandler 地址处理器
type AddressHandler struct {
	logger                *zap.Logger
	addressParseService   service.AddressParseService
	addressLibraryService service.AddressLibraryService
}

// NewAddressHandler 创建地址处理器
func NewAddressHandler(
	logger *zap.Logger,
	addressParseService service.AddressParseService,
	addressLibraryService service.AddressLibraryService,
) *AddressHandler {
	return &AddressHandler{
		logger:                logger,
		addressParseService:   addressParseService,
		addressLibraryService: addressLibraryService,
	}
}

// ParseAddress 解析地址
// @Summary 解析地址
// @Description 使用AI技术解析地址文本，提取姓名、电话、详细地址等信息
// @Tags 地址解析
// @Accept json
// @Produce json
// @Param request body model.AddressParseRequest true "地址解析请求"
// @Success 200 {object} model.AddressParseResponse "解析成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/address/parse [post]
func (h *AddressHandler) ParseAddress(c *gin.Context) {
	var req model.AddressParseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("参数绑定失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Lat == 0 {
		req.Lat = 30.0
	}
	if req.Lng == 0 {
		req.Lng = 110.0
	}

	h.logger.Info("收到地址解析请求",
		zap.String("text", req.Text),
		zap.Float64("lat", req.Lat),
		zap.Float64("lng", req.Lng))

	// 解析地址
	addressInfo, err := h.addressParseService.ParseAddress(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("地址解析失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    500,
			Message: "地址解析失败: " + err.Error(),
		})
		return
	}

	// 返回成功响应
	response := model.AddressParseResponse{
		Code:    0,
		Success: true,
		Message: "解析成功",
		Data: &model.AddressParseData{
			AddressInfo: addressInfo,
		},
	}

	c.JSON(http.StatusOK, response)
}

// BatchParseAddress 批量解析地址
// @Summary 批量解析地址
// @Description 批量解析多个地址文本
// @Tags 地址解析
// @Accept json
// @Produce json
// @Param request body model.BatchAddressParseRequest true "批量地址解析请求"
// @Success 200 {object} model.BatchAddressParseResponse "解析成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/address/batch-parse [post]
func (h *AddressHandler) BatchParseAddress(c *gin.Context) {
	var req model.BatchAddressParseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("参数绑定失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	h.logger.Info("收到批量地址解析请求",
		zap.Int("count", len(req.Addresses)),
		zap.Int("concurrency", req.Concurrency))

	// 批量解析地址
	data, err := h.addressParseService.BatchParseAddress(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("批量地址解析失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{Success: false,
			Code:    500,
			Message: "批量地址解析失败: " + err.Error(),
		})
		return
	}

	// 返回成功响应
	response := model.BatchAddressParseResponse{
		Code:    0,
		Success: true,
		Message: "批量解析成功",
		Data:    data,
	}

	c.JSON(http.StatusOK, response)
}

// GetAreaCascader 获取地区级联数据
// @Summary 获取地区级联数据
// @Description 获取省市区三级联动数据
// @Tags 地址解析
// @Produce json
// @Success 200 {object} model.AreaCascaderResponse "获取成功"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/address/area-cascader [get]
func (h *AddressHandler) GetAreaCascader(c *gin.Context) {
	h.logger.Info("收到获取地区级联数据请求")

	// 获取地区数据
	areas, err := h.addressLibraryService.GetAreaCascader()
	if err != nil {
		h.logger.Error("获取地区级联数据失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{Success: false,
			Code:    500,
			Message: "获取地区级联数据失败: " + err.Error(),
		})
		return
	}

	// 返回成功响应
	response := model.AreaCascaderResponse{
		Code: 200,
		Msg:  "操作成功",
		Data: areas,
	}

	c.JSON(http.StatusOK, response)
}

// ValidateAddress 验证地址
// @Summary 验证地址
// @Description 验证地址的有效性
// @Tags 地址解析
// @Accept json
// @Produce json
// @Param request body model.AddressValidationRequest true "地址验证请求"
// @Success 200 {object} model.AddressValidationResponse "验证成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/address/validate [post]
func (h *AddressHandler) ValidateAddress(c *gin.Context) {
	var req model.AddressValidationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("参数绑定失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, model.ErrorResponse{Success: false,
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	h.logger.Info("收到地址验证请求",
		zap.String("provinceCode", req.ProvinceCode),
		zap.String("cityCode", req.CityCode),
		zap.String("districtCode", req.DistrictCode))

	// 验证地址
	data, err := h.addressParseService.ValidateAddress(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("地址验证失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{Success: false,
			Code:    500,
			Message: "地址验证失败: " + err.Error(),
		})
		return
	}

	// 返回成功响应
	response := model.AddressValidationResponse{
		Code:    0,
		Success: true,
		Message: "验证成功",
		Data:    data,
	}

	c.JSON(http.StatusOK, response)
}

// SearchAreas 搜索地区
// @Summary 搜索地区
// @Description 根据关键词搜索相似地区
// @Tags 地址解析
// @Produce json
// @Param keyword query string true "搜索关键词"
// @Param limit query int false "限制数量" default(10)
// @Success 200 {object} SuccessResponse "搜索成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/address/search-areas [get]
func (h *AddressHandler) SearchAreas(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{Success: false,
			Code:    400,
			Message: "搜索关键词不能为空",
		})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	h.logger.Info("收到地区搜索请求",
		zap.String("keyword", keyword),
		zap.Int("limit", limit))

	// 搜索地区
	areas, err := h.addressLibraryService.SearchSimilarAreas(keyword, limit)
	if err != nil {
		h.logger.Error("搜索地区失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{Success: false,
			Code:    500,
			Message: "搜索地区失败: " + err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Code:    0,
		Message: "搜索成功",
		Data:    areas,
	})
}

// GetParseHistory 获取解析历史
// @Summary 获取解析历史
// @Description 获取用户的地址解析历史记录
// @Tags 地址解析
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Success 200 {object} SuccessResponse "获取成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/address/history [get]
func (h *AddressHandler) GetParseHistory(c *gin.Context) {
	// 从上下文获取用户ID（需要先实现认证中间件）
	userID := c.GetString("user_id")
	if userID == "" {
		userID = "default_user" // 临时使用默认用户
	}

	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page <= 0 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size <= 0 || size > 100 {
		size = 20
	}

	offset := (page - 1) * size

	h.logger.Info("收到获取解析历史请求",
		zap.String("userID", userID),
		zap.Int("page", page),
		zap.Int("size", size))

	// 获取解析历史
	histories, total, err := h.addressParseService.GetParseHistory(c.Request.Context(), userID, size, offset)
	if err != nil {
		h.logger.Error("获取解析历史失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, model.ErrorResponse{Success: false,
			Code:    500,
			Message: "获取解析历史失败: " + err.Error(),
		})
		return
	}

	// 构建分页响应
	response := map[string]any{
		"list":  histories,
		"total": total,
		"page":  page,
		"size":  size,
	}

	// 返回成功响应
	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Code:    0,
		Message: "获取成功",
		Data:    response,
	})
}
