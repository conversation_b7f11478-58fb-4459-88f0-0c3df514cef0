package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// OptimizedBalanceHandler 优化版余额处理器
type OptimizedBalanceHandler struct {
	*BalanceHandler
	optimizedService *service.OptimizedBalanceService
	logger           *zap.Logger
}

// NewOptimizedBalanceHandler 创建优化版余额处理器
func NewOptimizedBalanceHandler(
	baseHandler *BalanceHandler,
	optimizedService *service.OptimizedBalanceService,
	logger *zap.Logger,
) *OptimizedBalanceHandler {
	return &OptimizedBalanceHandler{
		BalanceHandler:   baseHandler,
		optimizedService: optimizedService,
		logger:           logger,
	}
}

// GetTransactionHistoryOptimized 获取交易历史（优化版）
// @Summary 获取交易历史（优化版）
// @Description 获取用户的交易历史记录（性能优化版本）
// @Tags 余额管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Param type query string false "交易类型筛选"
// @Param status query string false "状态筛选"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param customer_order_no query string false "客户订单号"
// @Param order_no query string false "平台订单号"
// @Param tracking_no query string false "运单号"
// @Success 200 {object} model.TransactionHistoryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/balance/transactions/optimized [get]
func (h *OptimizedBalanceHandler) GetTransactionHistoryOptimized(c *gin.Context) {
	startTime := util.NowBeijing()

	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未认证",
		})
		return
	}

	// 解析分页参数
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	// 参数验证和限制
	if limit <= 0 || limit > 100 {
		limit = 20
	}
	if offset < 0 {
		offset = 0
	}

	// 解析筛选参数
	typeFilter := c.Query("type")
	statusFilter := c.Query("status")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")
	customerOrderNo := c.Query("customer_order_no")
	orderNo := c.Query("order_no")
	trackingNo := c.Query("tracking_no")

	// 计算页码
	page := (offset / limit) + 1

	// 使用优化版服务查询
	transactions, total, err := h.optimizedService.GetTransactionHistoryWithFiltersOptimized(
		c.Request.Context(),
		userID,
		limit,
		offset,
		typeFilter,
		statusFilter,
		startTimeStr,
		endTimeStr,
		customerOrderNo,
		orderNo,
		trackingNo,
	)

	if err != nil {
		h.logger.Error("获取交易历史失败（优化版）",
			zap.String("user_id", userID),
			zap.Error(err),
			zap.Duration("duration", time.Since(startTime)),
		)
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取交易历史失败",
		})
		return
	}

	// 计算总页数
	totalPages := int((total + int64(limit) - 1) / int64(limit))

	response := &model.TransactionHistoryResponse{
		Items:      transactions,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
	}

	// 记录性能指标
	duration := time.Since(startTime)
	h.logger.Info("交易历史查询完成（优化版）",
		zap.String("user_id", userID),
		zap.Int("result_count", len(transactions)),
		zap.Int64("total", total),
		zap.Duration("duration", duration),
		zap.Int("limit", limit),
		zap.Int("offset", offset),
	)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取交易历史成功（优化版）",
		"data":    response,
		"meta": gin.H{
			"query_time": duration.Milliseconds(),
			"cached":     duration.Milliseconds() < 10, // 简单的缓存命中判断
		},
	})
}

// GetTransactionStatistics 获取交易统计信息
// @Summary 获取交易统计信息
// @Description 获取用户的交易统计汇总信息
// @Tags 余额管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param days query int false "统计天数" default(30)
// @Success 200 {object} TransactionStatisticsResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/balance/transactions/statistics [get]
func (h *OptimizedBalanceHandler) GetTransactionStatistics(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未认证",
		})
		return
	}

	days, _ := strconv.Atoi(c.DefaultQuery("days", "30"))
	if days <= 0 || days > 365 {
		days = 30
	}

	// 计算时间范围
	endTime := util.NowBeijing()
	startTime := endTime.AddDate(0, 0, -days)

	startTimeStr := startTime.Format("2006-01-02 15:04:05")
	endTimeStr := endTime.Format("2006-01-02 15:04:05")

	// 获取各种类型的交易统计
	depositTransactions, depositTotal, _ := h.optimizedService.GetTransactionHistoryWithFiltersOptimized(
		c.Request.Context(), userID, 1000, 0, "deposit", "completed", startTimeStr, endTimeStr, "", "", "")

	paymentTransactions, paymentTotal, _ := h.optimizedService.GetTransactionHistoryWithFiltersOptimized(
		c.Request.Context(), userID, 1000, 0, "order_pre_charge", "completed", startTimeStr, endTimeStr, "", "", "")

	refundTransactions, refundTotal, _ := h.optimizedService.GetTransactionHistoryWithFiltersOptimized(
		c.Request.Context(), userID, 1000, 0, "order_cancel_refund", "completed", startTimeStr, endTimeStr, "", "", "")

	// 计算统计数据
	var totalDepositAmount, totalPaymentAmount, totalRefundAmount float64

	for _, tx := range depositTransactions {
		amount, _ := tx.Amount.Float64()
		totalDepositAmount += amount
	}

	for _, tx := range paymentTransactions {
		amount, _ := tx.Amount.Float64()
		totalPaymentAmount += amount
	}

	for _, tx := range refundTransactions {
		amount, _ := tx.Amount.Float64()
		totalRefundAmount += amount
	}

	response := TransactionStatisticsResponse{
		Period: TransactionPeriod{
			StartDate: startTime.Format("2006-01-02"),
			EndDate:   endTime.Format("2006-01-02"),
			Days:      days,
		},
		Deposit: TransactionTypeStats{
			Count:  depositTotal,
			Amount: totalDepositAmount,
		},
		Payment: TransactionTypeStats{
			Count:  paymentTotal,
			Amount: totalPaymentAmount,
		},
		Refund: TransactionTypeStats{
			Count:  refundTotal,
			Amount: totalRefundAmount,
		},
		NetAmount: totalDepositAmount + totalRefundAmount - totalPaymentAmount,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取交易统计成功",
		"data":    response,
	})
}

// TransactionStatisticsResponse 交易统计响应
type TransactionStatisticsResponse struct {
	Period    TransactionPeriod    `json:"period"`
	Deposit   TransactionTypeStats `json:"deposit"`
	Payment   TransactionTypeStats `json:"payment"`
	Refund    TransactionTypeStats `json:"refund"`
	NetAmount float64              `json:"net_amount"`
}

type TransactionPeriod struct {
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
	Days      int    `json:"days"`
}

type TransactionTypeStats struct {
	Count  int64   `json:"count"`
	Amount float64 `json:"amount"`
}

// HealthCheck 健康检查（优化版）
// @Summary 余额服务健康检查
// @Description 检查优化版余额服务的健康状态
// @Tags 系统监控
// @Accept json
// @Produce json
// @Success 200 {object} HealthCheckResponse
// @Router /api/v1/balance/health [get]
func (h *OptimizedBalanceHandler) HealthCheck(c *gin.Context) {
	startTime := util.NowBeijing()

	// 检查缓存状态
	cacheStatus := "healthy"

	// 检查数据库连接（简单查询）
	dbStatus := "healthy"
	_, err := h.optimizedService.GetBalance(c.Request.Context(), "health-check-user")
	if err != nil && err.Error() != "余额记录不存在" {
		dbStatus = "unhealthy"
	}

	response := HealthCheckResponse{
		Status:    "healthy",
		Timestamp: util.NowBeijing().Unix(),
		Version:   "optimized-v1.0",
		Services: map[string]string{
			"cache":    cacheStatus,
			"database": dbStatus,
		},
		ResponseTime: time.Since(startTime).Milliseconds(),
	}

	if dbStatus == "unhealthy" {
		response.Status = "degraded"
	}

	httpStatus := http.StatusOK
	if response.Status == "unhealthy" {
		httpStatus = http.StatusServiceUnavailable
	}

	c.JSON(httpStatus, response)
}

type HealthCheckResponse struct {
	Status       string            `json:"status"`
	Timestamp    int64             `json:"timestamp"`
	Version      string            `json:"version"`
	Services     map[string]string `json:"services"`
	ResponseTime int64             `json:"response_time_ms"`
}

// ClearCache 清理缓存
// @Summary 清理交易记录缓存
// @Description 清理优化版余额服务的缓存数据
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} map[string]interface{}
// @Failure 401 {object} ErrorResponse
// @Router /api/v1/balance/cache/clear [post]
func (h *OptimizedBalanceHandler) ClearCache(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未认证",
		})
		return
	}

	// 触发缓存清理
	// 这里可以实现具体的缓存清理逻辑

	h.logger.Info("用户触发缓存清理", zap.String("user_id", userID))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "缓存清理成功",
		"data": gin.H{
			"cleared_at": util.NowBeijing().Unix(),
		},
	})
}
