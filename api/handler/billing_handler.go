package handler

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"

)

// BillingHandler 计费处理器
type BillingHandler struct {
	billingService service.BillingService
	logger         *zap.Logger
}

// NewBillingHandler 创建计费处理器
func NewBillingHandler(billingService service.BillingService, logger *zap.Logger) *BillingHandler {
	return &BillingHandler{
		billingService: billingService,
		logger:         logger,
	}
}

// GetOrderBillingDetails 获取订单计费详情
// @Summary 获取订单计费详情
// @Description 获取指定订单的计费详情信息
// @Tags 计费管理
// @Accept json
// @Produce json
// @Param order_no path string true "订单号"
// @Security Bearer
// @Success 200 {object} service.OrderBillingInfo
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/billing/order/{order_no} [get]
func (h *BillingHandler) GetOrderBillingDetails(c *gin.Context) {
	orderNo := c.Param("order_no")
	if orderNo == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "订单号不能为空",
		})
		return
	}

	billingInfo, err := h.billingService.GetOrderBillingDetails(c.Request.Context(), orderNo)
	if err != nil {
		h.logger.Error("获取订单计费详情失败",
			zap.String("order_no", orderNo),
			zap.Error(err))

		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "获取计费详情失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "成功",
		"data":    billingInfo,
	})
}

// GetBillingHistory 获取计费历史
// @Summary 获取计费历史
// @Description 获取指定订单的计费变更历史
// @Tags 计费管理
// @Accept json
// @Produce json
// @Param order_no path string true "订单号"
// @Security Bearer
// @Success 200 {object} []model.OrderBillingHistory
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/billing/history/{order_no} [get]
func (h *BillingHandler) GetBillingHistory(c *gin.Context) {
	orderNo := c.Param("order_no")
	if orderNo == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "订单号不能为空",
		})
		return
	}

	history, err := h.billingService.GetBillingHistory(c.Request.Context(), orderNo)
	if err != nil {
		h.logger.Error("获取计费历史失败",
			zap.String("order_no", orderNo),
			zap.Error(err))

		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "获取计费历史失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "成功",
		"data":    history,
	})
}

// GetBillingStatistics 获取计费统计
// @Summary 获取计费统计
// @Description 获取用户的计费统计信息
// @Tags 计费管理
// @Accept json
// @Produce json
// @Param start_time query string false "开始时间 (YYYY-MM-DD)"
// @Param end_time query string false "结束时间 (YYYY-MM-DD)"
// @Security Bearer
// @Success 200 {object} repository.BillingStatistics
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/billing/statistics [get]
func (h *BillingHandler) GetBillingStatistics(c *gin.Context) {
	// 获取用户ID
	userID := getBillingUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, model.ErrorResponse{
			Success: false,
			Code:    model.StatusUnauthorized,
			Message: "用户未认证",
		})
		return
	}

	// 解析时间参数
	startTimeStr := c.DefaultQuery("start_time", "")
	endTimeStr := c.DefaultQuery("end_time", "")

	var startTime, endTime time.Time
	var err error

	if startTimeStr != "" {
		startTime, err = time.Parse("2006-01-02", startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, model.ErrorResponse{
				Success: false,
				Code:    model.StatusBadRequest,
				Message: "开始时间格式错误，应为 YYYY-MM-DD",
			})
			return
		}
	} else {
		// 默认为30天前
		startTime = util.NowBeijing().AddDate(0, 0, -30)
	}

	if endTimeStr != "" {
		endTime, err = time.Parse("2006-01-02", endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, model.ErrorResponse{
				Success: false,
				Code:    model.StatusBadRequest,
				Message: "结束时间格式错误，应为 YYYY-MM-DD",
			})
			return
		}
		// 设置为当天结束时间
		endTime = endTime.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
	} else {
		// 默认为当前时间
		endTime = util.NowBeijing()
	}

	statistics, err := h.billingService.GetBillingStatistics(c.Request.Context(), userID, startTime, endTime)
	if err != nil {
		h.logger.Error("获取计费统计失败",
			zap.String("user_id", userID),
			zap.Error(err))

		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "获取计费统计失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "成功",
		"data":    statistics,
	})
}

// ProcessBillingDifference 处理费用差额
// @Summary 处理费用差额
// @Description 手动处理订单费用差额
// @Tags 计费管理
// @Accept json
// @Produce json
// @Param request body ProcessBillingDifferenceRequest true "费用差额处理请求"
// @Security Bearer
// @Success 200 {object} gin.H
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/billing/process-difference [post]
func (h *BillingHandler) ProcessBillingDifference(c *gin.Context) {
	var req ProcessBillingDifferenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	if req.OrderNo == "" {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "订单号不能为空",
		})
		return
	}

	if req.ActualFee <= 0 {
		c.JSON(http.StatusBadRequest, model.ErrorResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "实际费用必须大于0",
		})
		return
	}

	err := h.billingService.ProcessBillingDifference(c.Request.Context(), req.OrderNo, req.ActualFee, req.Reason)
	if err != nil {
		h.logger.Error("处理费用差额失败",
			zap.String("order_no", req.OrderNo),
			zap.Float64("actual_fee", req.ActualFee),
			zap.Error(err))

		c.JSON(http.StatusInternalServerError, model.ErrorResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "处理费用差额失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "费用差额处理成功",
	})
}

// ProcessBillingDifferenceRequest 费用差额处理请求
type ProcessBillingDifferenceRequest struct {
	OrderNo   string  `json:"order_no" binding:"required"`   // 订单号
	ActualFee float64 `json:"actual_fee" binding:"required"` // 实际费用
	Reason    string  `json:"reason"`                        // 处理原因
}

// getBillingUserIDFromContext 从上下文中获取用户ID
func getBillingUserIDFromContext(c *gin.Context) string {
	// 如果没有服务或服务失败，尝试直接从上下文获取
	if userID, exists := c.Get("userID"); exists {
		if id, ok := userID.(string); ok && id != "" {
			return id
		}
	}

	// 尝试从user_id获取
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok && id != "" {
			return id
		}
	}

	// 尝试从client_id获取对应的用户ID
	if clientID, exists := c.Get("client_id"); exists {
		if id, ok := clientID.(string); ok && id != "" {
			// 这里简化处理，直接返回client_id作为用户ID
			// 在实际生产环境中，应该通过数据库查询获取对应的用户ID
			return id
		}
	}

	return ""
}
