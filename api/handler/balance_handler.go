package handler

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/user"
	"go.uber.org/zap"
)

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// BalanceHandler 余额处理器
type BalanceHandler struct {
	balanceService service.BalanceService
	logger         *zap.Logger
}

// NewBalanceHandler 创建余额处理器
func NewBalanceHandler(balanceService service.BalanceService, logger *zap.Logger) *BalanceHandler {
	if logger == nil {
		logger, _ = zap.NewProduction()
	}
	return &BalanceHandler{
		balanceService: balanceService,
		logger:         logger,
	}
}

// GetBalance 获取用户余额
// @Summary 获取用户余额
// @Description 获取当前用户的余额信息
// @Tags 余额管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} model.BalanceResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/balance [get]
func (h *BalanceHandler) GetBalance(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未认证",
		})
		return
	}

	balance, err := h.balanceService.GetBalance(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("获取余额失败", zap.String("user_id", userID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取余额失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取余额成功",
		"data":    balance,
	})
}

// Deposit 充值
// @Summary 用户充值
// @Description 用户账户充值
// @Tags 余额管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body model.DepositRequest true "充值请求"
// @Success 200 {object} model.TransactionResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/balance/deposit [post]
func (h *BalanceHandler) Deposit(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未认证",
		})
		return
	}

	var req model.DepositRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 设置用户ID
	req.UserID = userID

	// 验证金额
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_AMOUNT",
			Message: "充值金额必须大于0",
		})
		return
	}

	result, err := h.balanceService.Deposit(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("充值失败", zap.String("user_id", userID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "DEPOSIT_FAILED",
			Message: "充值失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// Payment 支付
// @Summary 余额支付
// @Description 使用余额进行支付
// @Tags 余额管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body model.PaymentRequest true "支付请求"
// @Success 200 {object} model.TransactionResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/balance/payment [post]
func (h *BalanceHandler) Payment(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未认证",
		})
		return
	}

	var req model.PaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 设置用户ID
	req.UserID = userID

	// 验证金额
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_AMOUNT",
			Message: "支付金额必须大于0",
		})
		return
	}

	result, err := h.balanceService.Payment(c.Request.Context(), &req)
	if err != nil {
		if err == service.ErrInsufficientBalance {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Code:    "INSUFFICIENT_BALANCE",
				Message: "账户余额不足，请联系客服",
			})
			return
		}
		if err == service.ErrDuplicateTransaction {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Code:    "DUPLICATE_TRANSACTION",
				Message: "重复交易",
			})
			return
		}
		h.logger.Error("支付失败", zap.String("user_id", userID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "PAYMENT_FAILED",
			Message: "支付失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// Refund 退款
// @Summary 退款
// @Description 处理退款
// @Tags 余额管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body model.BalanceRequest true "退款请求"
// @Success 200 {object} model.TransactionResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/balance/refund [post]
func (h *BalanceHandler) Refund(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未认证",
		})
		return
	}

	var req model.BalanceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_REQUEST",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 设置用户ID和操作员ID
	req.UserID = userID
	req.OperatorID = userID

	// 验证金额
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Code:    "INVALID_AMOUNT",
			Message: "退款金额必须大于0",
		})
		return
	}

	result, err := h.balanceService.Refund(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("退款失败", zap.String("user_id", userID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "REFUND_FAILED",
			Message: "退款失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetTransactionHistory 获取交易历史
// @Summary 获取交易历史
// @Description 获取用户的交易历史记录
// @Tags 余额管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} model.TransactionHistoryResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/balance/transactions [get]
func (h *BalanceHandler) GetTransactionHistory(c *gin.Context) {
	userID := getUserIDFromContext(c)
	if userID == "" {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Code:    "UNAUTHORIZED",
			Message: "用户未认证",
		})
		return
	}

	// 解析分页参数
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	if limit <= 0 || limit > 100 {
		limit = 20
	}
	if offset < 0 {
		offset = 0
	}

	// 解析筛选参数
	typeFilter := c.Query("type")
	statusFilter := c.Query("status")
	startTime := c.Query("start_time")
	endTime := c.Query("end_time")
	customerOrderNo := c.Query("customer_order_no")
	orderNo := c.Query("order_no")
	trackingNo := c.Query("tracking_no")

	// 计算页码
	page := (offset / limit) + 1

	transactions, total, err := h.balanceService.GetTransactionHistoryWithFilters(c.Request.Context(), userID, limit, offset, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo)
	if err != nil {
		h.logger.Error("获取交易历史失败", zap.String("user_id", userID), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Code:    "INTERNAL_ERROR",
			Message: "获取交易历史失败",
		})
		return
	}

	// 计算总页数
	totalPages := int((total + int64(limit) - 1) / int64(limit))

	response := &model.TransactionHistoryResponse{
		Items:      transactions,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取交易历史成功",
		"data":    response,
	})
}

// UserIdentityService 用户身份服务
type UserIdentityService struct {
	userRepo user.UserRepository
}

// NewUserIdentityService 创建用户身份服务
func NewUserIdentityService(userRepo user.UserRepository) *UserIdentityService {
	return &UserIdentityService{
		userRepo: userRepo,
	}
}

// GetUserIDFromContext 从上下文中获取用户ID
func (s *UserIdentityService) GetUserIDFromContext(c *gin.Context) (string, error) {
	// 首先尝试从userID获取（认证中间件设置）
	if userID, exists := c.Get("userID"); exists {
		if id, ok := userID.(string); ok && id != "" {
			return id, nil
		}
	}

	// 尝试从user_id获取
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok && id != "" {
			return id, nil
		}
	}

	// 如果没有直接的用户ID，尝试从client_id获取对应的用户ID
	if clientID, exists := c.Get("client_id"); exists {
		if id, ok := clientID.(string); ok && id != "" {
			return s.getUserIDByClientID(id)
		}
	}

	// 尝试从签名验证中获取的client_id
	if clientID, exists := c.Get("client_id_from_signature"); exists {
		if id, ok := clientID.(string); ok && id != "" {
			return s.getUserIDByClientID(id)
		}
	}

	return "", fmt.Errorf("无法获取用户身份信息")
}

// getUserIDByClientID 通过客户端ID获取用户ID
func (s *UserIdentityService) getUserIDByClientID(clientID string) (string, error) {
	foundUser, err := s.userRepo.FindByClientID(clientID)
	if err != nil {
		if err.Error() == "user not found" || err.Error() == "sql: no rows in result set" {
			return "", fmt.Errorf("未找到与客户端ID关联的用户")
		}
		return "", fmt.Errorf("通过客户端ID查找用户失败: %w", err)
	}

	if !foundUser.IsActive {
		return "", fmt.Errorf("用户账户已被禁用")
	}

	return foundUser.ID, nil
}

// getUserIDFromContext 从上下文中获取用户ID（兼容性函数）
func getUserIDFromContext(c *gin.Context) string {
	// 从上下文获取用户身份服务
	service, exists := c.Get("user_identity_service")
	if exists {
		identityService := service.(*UserIdentityService)
		userID, err := identityService.GetUserIDFromContext(c)
		if err == nil {
			return userID
		}
		// 记录错误但不中断流程
		fmt.Printf("获取用户ID失败: %v\n", err)
	}

	// 如果没有服务或服务失败，尝试直接从上下文获取
	if userID, exists := c.Get("userID"); exists {
		if id, ok := userID.(string); ok && id != "" {
			return id
		}
	}

	// 尝试从user_id获取
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok && id != "" {
			return id
		}
	}

	return ""
}
