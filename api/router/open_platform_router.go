package router

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/service"
)

// OpenPlatformRouterConfig 开放平台路由配置
type OpenPlatformRouterConfig struct {
	// 核心服务
	PriceService             service.PriceServiceInterface
	OrderHandler             *handler.OrderHandler
	TokenService             auth.TokenService
	ExpressMappingService    express.ExpressMappingService
	ExpressCompanyRepository express.ExpressCompanyRepository
}

// SetupOpenPlatformRouter 设置开放平台API路由
// 🔒 这些API强制要求OAuth 2.0 + HMAC-SHA256双重认证
func SetupOpenPlatformRouter(engine *gin.Engine, config OpenPlatformRouterConfig) {
	// 🔒 开放平台API组 - 强制签名验证
	// 注意：签名中间件已在全局应用，这里的路径不在跳过列表中，因此会强制验证签名
	openAPI := engine.Group("/api/v1/open")
	
	// 🔒 应用JWT认证中间件
	openAPI.Use(auth.JWTAuthMiddleware(config.TokenService))
	
	{
		// 🚀 快递服务API
		express := openAPI.Group("/express")
		{
			// 🔒 价格查询API - 需要express:read权限 + 签名验证
			simplePriceHandler := handler.NewSimplePriceHandler(
				config.PriceService, 
				config.ExpressMappingService, 
				config.ExpressCompanyRepository,
			)
			express.POST("/price", 
				auth.ScopeRequiredMiddleware("express:read"), 
				simplePriceHandler.QuerySimplePrice)

			// 🔒 批量价格查询API - 需要express:read权限 + 签名验证
			// TODO: 实现批量价格查询功能
			// express.POST("/price/batch",
			//	auth.ScopeRequiredMiddleware("express:read"),
			//	simplePriceHandler.QueryBatchPrice)

			// 🔒 订单创建API - 需要express:write权限 + 签名验证
			express.POST("/order", 
				auth.ScopeRequiredMiddleware("express:write"), 
				config.OrderHandler.CreateSimpleOrder)

			// 🔒 订单查询API - 需要express:read权限 + 签名验证
			// TODO: 实现订单查询功能
			// express.GET("/order/:order_id",
			//	auth.ScopeRequiredMiddleware("express:read"),
			//	config.OrderHandler.GetOrderStatus)

			// 🔒 订单列表API - 需要express:read权限 + 签名验证
			// TODO: 实现订单列表功能
			// express.GET("/orders",
			//	auth.ScopeRequiredMiddleware("express:read"),
			//	config.OrderHandler.GetOrderList)
		}

		// 🚀 系统信息API（开放平台版本 - 简化信息）
		system := openAPI.Group("/system")
		{
			// 🔒 支持的快递公司列表 - 需要express:read权限 + 签名验证
			configHandler := handler.NewConfigHandler()
			system.GET("/express-companies", 
				auth.ScopeRequiredMiddleware("express:read"), 
				configHandler.GetExpressCompanies)

			// 🔒 订单状态说明 - 需要express:read权限 + 签名验证
			system.GET("/order-statuses", 
				auth.ScopeRequiredMiddleware("express:read"), 
				configHandler.GetOrderStatuses)

			// 🔒 地区信息 - 需要express:read权限 + 签名验证
			system.GET("/regions", 
				auth.ScopeRequiredMiddleware("express:read"), 
				configHandler.GetRegions)
		}
	}
}

// SetupWebAPIRouter 设置内部Web API路由
// 🌐 这些API仅需要JWT认证，无需签名验证
func SetupWebAPIRouter(engine *gin.Engine, config OpenPlatformRouterConfig) {
	// 🌐 内部Web API组 - 无需签名验证
	webAPI := engine.Group("/api/v1/web")
	
	// 🌐 应用JWT认证中间件
	webAPI.Use(auth.JWTAuthMiddleware(config.TokenService))
	
	{
		// 🌐 快递服务API（Web版本 - 功能更丰富）
		express := webAPI.Group("/express")
		{
			// 🌐 价格查询API - 仅需JWT认证
			simplePriceHandler := handler.NewSimplePriceHandler(
				config.PriceService, 
				config.ExpressMappingService, 
				config.ExpressCompanyRepository,
			)
			express.POST("/price", 
				auth.ScopeRequiredMiddleware("express:read"), 
				simplePriceHandler.QuerySimplePrice)

			// 🌐 价格比较API - 仅需JWT认证
			// TODO: 实现价格比较功能
			// express.POST("/price/compare",
			//	auth.ScopeRequiredMiddleware("express:read"),
			//	simplePriceHandler.ComparePrices)

			// 🌐 订单创建API - 仅需JWT认证
			express.POST("/order", 
				auth.ScopeRequiredMiddleware("express:write"), 
				config.OrderHandler.CreateSimpleOrder)

			// 🌐 订单管理API - 仅需JWT认证
			// TODO: 实现订单管理功能
			// express.GET("/order/:order_id",
			//	auth.ScopeRequiredMiddleware("express:read"),
			//	config.OrderHandler.GetOrderStatus)

			// express.GET("/orders",
			//	auth.ScopeRequiredMiddleware("express:read"),
			//	config.OrderHandler.GetOrderList)

			// express.PUT("/order/:order_id/cancel",
			//	auth.ScopeRequiredMiddleware("express:write"),
			//	config.OrderHandler.CancelOrder)
		}

		// 🌐 系统配置API（Web版本 - 详细信息）
		system := webAPI.Group("/system")
		{
			configHandler := handler.NewConfigHandler()
			
			// 🌐 系统配置 - 仅需JWT认证
			system.GET("/config", configHandler.GetSystemConfig)
			
			// 🌐 快递公司详细信息 - 仅需JWT认证
			system.GET("/express-companies", configHandler.GetExpressCompanies)
			
			// 🌐 订单状态详细说明 - 仅需JWT认证
			system.GET("/order-statuses", configHandler.GetOrderStatuses)
			
			// 🌐 地区详细信息 - 仅需JWT认证
			system.GET("/regions", configHandler.GetRegions)
		}
	}
}
