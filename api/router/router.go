package router

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/api"
	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/express"
	internal_handler "github.com/your-org/go-kuaidi/internal/handler"
	internal_middleware "github.com/your-org/go-kuaidi/internal/middleware"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/security"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/user"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// SetupRouter 设置路由
func SetupRouter(
	orderHandler *handler.OrderHandler,
	orderService *service.OrderService, // 🚀 订单服务
	authHandler *handler.AuthHandler,
	userController *user.UserController,
	tokenService auth.TokenService,
	db *gorm.DB,
	redisClient *redis.Client,
	clientService auth.ClientService,
	signatureService security.SignatureService,
	nonceManager security.NonceManager, // 🚀 新增nonce管理器
	rateLimitService security.RateLimitService,
	auditService security.AuditService,
	securityConfig *security.SecurityConfig,
	priceService *service.PriceService,
	enhancedPriceService service.PriceServiceInterface, // 🚀 增强价格服务（带缓存）
	providerManager *adapter.ProviderManager, // 🚀 新增：供应商管理器
	roleHandler api.RoleHandlerInterface,
	permissionHandler *api.PermissionHandler,
	userRoleHandler *api.UserRoleHandler,
	permissionMiddleware *internal_middleware.PermissionMiddleware,
	balanceHandler *handler.BalanceHandler,
	optimizedBalanceHandler *handler.OptimizedBalanceHandler, // 优化版余额处理器
	billingHandler *handler.BillingHandler,
	addressHandler *handler.AddressHandler, // 新增地址解析处理器
	platformOrderGenerator *service.PlatformOrderGenerator, // 🔥 新增平台订单号生成器

	callbackHandler *handler.CallbackHandler,
	adminUserHandler *handler.AdminUserHandler,
	adminAuthHandler *handler.AdminAuthHandler,
	adminMiddleware *middleware.AdminMiddleware,
	expressCompanyHandler *handler.ExpressCompanyHandler,
	expressMappingHandler *handler.ExpressMappingHandler,
	adminOrderHandler *handler.AdminOrderHandler,
	adminBalanceHandler *handler.AdminBalanceHandler,
	adminSystemConfigHandler *internal_handler.AdminSystemConfigHandler,
	adminDashboardHandler *handler.AdminDashboardHandler,
	adminProviderReloadHandler *handler.AdminProviderReloadHandler, // 🚀 管理员供应商重载处理器
	regionBlacklistHandler *handler.RegionBlacklistHandler, // 🎯 地区黑名单处理器
	dbOptimizationHandler *handler.DatabaseOptimizationHandler, // 🚀 数据库优化处理器
	workOrderHandler *handler.WorkOrderHandler, // 🚀 工单处理器
	workOrderCallbackForwarder *service.WorkOrderCallbackForwarder, // 🔥 工单回调转发器
	statusHistoryHandler *internal_handler.OrderStatusHistoryHandler, // 🔥 订单状态历史处理器
	expressMappingService express.ExpressMappingService, // 🔥 快递公司映射服务
	expressCompanyRepository express.ExpressCompanyRepository, // 🔥 快递公司仓储
	expressCompanyService express.ExpressCompanyService, // 🚀 新增：快递公司服务
	weightCacheHandler *internal_handler.WeightTierCacheHandler, // 🚀 重量档位缓存处理器
	orderRepository repository.OrderRepository, // 🔥 新增：订单仓储
	systemConfigService service.SystemConfigService, // 🔥 新增：系统配置服务
	logger *zap.Logger, // 🚀 日志记录器
	errorLogger *zap.Logger, // 🚀 错误日志记录器
	accessLogger *zap.Logger, // 🚀 访问日志记录器
) *gin.Engine {

	// 创建认证中间件
	authMiddleware := middleware.NewAuthMiddleware(tokenService)

	// 设置Gin为发布模式以提高性能
	gin.SetMode(gin.ReleaseMode)

	// 创建Gin引擎，禁用默认中间件以提高性能
	r := gin.New()

	// 手动添加必要的中间件
	r.Use(gin.Recovery()) // 恢复中间件

	// 🚀 使用企业级日志中间件
	r.Use(middleware.Cors())
	r.Use(middleware.ErrorCaptureMiddleware(errorLogger)) // 错误捕获中间件
	r.Use(middleware.AccessLogMiddleware(accessLogger))   // 访问日志中间件

	// 添加性能监控中间件
	r.Use(middleware.GlobalPerformanceMonitor.PerformanceMiddleware())
	r.Use(middleware.SlowRequestMiddleware(1 * time.Second))

	// 注入用户存储库和身份服务
	r.Use(func(c *gin.Context) {
		userRepo := userController.GetUserRepository()
		c.Set("user_repository", userRepo)

		// 创建用户身份服务（保持向后兼容）
		userIdentityService := handler.NewUserIdentityService(userRepo)
		c.Set("user_identity_service", userIdentityService)

		// 🔥 注入生产级身份解析服务
		userIdentityResolver := service.NewUserIdentityResolver(userRepo, logger)
		c.Set("user_identity_resolver", userIdentityResolver)

		// 🚀 注入工单服务
		if workOrderHandler != nil {
			c.Set("workorder_service", workOrderHandler.GetService())
		}

		c.Next()
	})

	// 使用安全中间件
	r.Use(middleware.ForceHTTPS(securityConfig.Headers))
	r.Use(middleware.SecurityHeaders(securityConfig.Headers))
	r.Use(middleware.RateLimitMiddleware(rateLimitService))
	r.Use(middleware.AuditMiddleware(auditService))
	// 🚀 使用企业级增强签名验证中间件
	r.Use(middleware.EnhancedSignatureMiddleware(signatureService, clientService, nonceManager, redisClient, securityConfig.Signature, logger))

	// 🚀 静态文件服务 - 提供前端文件（需要在其他路由之前配置）
	// 管理员前端
	r.Static("/admin", "./admin-frontend/dist")
	// 用户前端静态文件（使用StaticFS避免路由冲突）
	r.StaticFS("/static", gin.Dir("./user-frontend/dist", false))

	// 健康检查
	r.GET("/health", authHandler.HandleHealthCheck)

	// 性能监控端点
	performanceHandler := handler.NewPerformanceHandler()
	r.GET("/performance/health", performanceHandler.HealthCheck)
	r.GET("/performance/metrics", performanceHandler.GetMetrics)
	r.GET("/performance/summary", performanceHandler.GetSummary)
	r.GET("/performance/system", performanceHandler.GetSystemInfo)
	r.GET("/performance/worker-pool", performanceHandler.GetWorkerPoolStats)

	r.GET("/performance/report", performanceHandler.GetPerformanceReport)
	r.POST("/performance/gc", performanceHandler.TriggerGC)
	r.POST("/performance/reset", performanceHandler.ResetMetrics)

	// 🚀 新增：高级性能监控API
	r.GET("/performance/gc-stats", performanceHandler.GetGCStats)
	r.GET("/performance/json-stats", performanceHandler.GetJSONStats)
	r.POST("/performance/benchmark", performanceHandler.RunBenchmark)
	r.POST("/performance/optimize-memory", performanceHandler.OptimizeMemory)

	// 🚀 pprof性能分析端点（生产环境建议限制访问）
	pprofGroup := r.Group("/debug/pprof")
	{
		pprofGroup.GET("/", gin.WrapH(http.DefaultServeMux))
		pprofGroup.GET("/cmdline", gin.WrapH(http.DefaultServeMux))
		pprofGroup.GET("/profile", gin.WrapH(http.DefaultServeMux))
		pprofGroup.POST("/symbol", gin.WrapH(http.DefaultServeMux))
		pprofGroup.GET("/symbol", gin.WrapH(http.DefaultServeMux))
		pprofGroup.GET("/trace", gin.WrapH(http.DefaultServeMux))
		pprofGroup.GET("/allocs", gin.WrapH(http.DefaultServeMux))
		pprofGroup.GET("/block", gin.WrapH(http.DefaultServeMux))
		pprofGroup.GET("/goroutine", gin.WrapH(http.DefaultServeMux))
		pprofGroup.GET("/heap", gin.WrapH(http.DefaultServeMux))
		pprofGroup.GET("/mutex", gin.WrapH(http.DefaultServeMux))
		pprofGroup.GET("/threadcreate", gin.WrapH(http.DefaultServeMux))
	}

	// 🚀 Timestamp测试和调试工具（无需认证，帮助用户排查问题）
	timestampTestHandler := handler.NewTimestampTestHandler(logger)
	testGroup := r.Group("/api/test")
	{
		// timestamp验证工具
		testGroup.POST("/timestamp/validate", timestampTestHandler.ValidateTimestamp)
		// 获取服务器时间
		testGroup.GET("/timestamp/server-time", timestampTestHandler.GetServerTime)
		// 生成推荐的timestamp
		testGroup.GET("/timestamp/generate", timestampTestHandler.GenerateTimestamp)
	}

	// 用户相关端点
	r.POST("/api/v1/users/register", userController.HandleRegister)

	// 需要认证的用户端点
	userGroup := r.Group("/api/v1/users")
	userGroup.Use(authMiddleware.RequireAuth())
	{
		userGroup.GET("/profile", userController.HandleGetProfile)
		userGroup.PUT("/callback-url", userController.HandleUpdateCallbackURL)
		userGroup.POST("/reset-client-secret", userController.HandleResetClientSecret)
	}

	// OAuth令牌端点
	r.POST("/oauth/token", authHandler.HandleTokenRequest)
	r.POST("/oauth/revoke", authHandler.HandleRevokeToken)

	// 用户登录端点（不需要认证）
	r.POST("/api/v1/auth/login", authHandler.HandleLogin)

	// 系统配置端点（不需要认证）
	configHandler := handler.NewConfigHandler()
	r.GET("/api/v1/system/config", configHandler.GetSystemConfig)
	r.GET("/api/v1/system/express-companies", configHandler.GetExpressCompanies)
	r.GET("/api/v1/system/order-statuses", configHandler.GetOrderStatuses)
	r.GET("/api/v1/system/regions", configHandler.GetRegions)

	// 前端错误日志收集端点（不需要认证）
	r.POST("/api/v1/system/error-log", func(c *gin.Context) {
		// 简单接收前端错误日志，避免404错误
		var logData map[string]interface{}
		if err := c.ShouldBindJSON(&logData); err != nil {
			c.JSON(200, gin.H{"success": true, "message": "日志接收成功"})
			return
		}

		// 这里可以添加日志记录逻辑，暂时简单返回成功
		// TODO: 如果需要持久化错误日志，可以在这里添加数据库操作
		c.JSON(200, gin.H{"success": true, "message": "日志接收成功"})
	})

	// 🚀 统一网关API - 参考易达设计，单一URL处理所有请求
	userRepository := userController.GetUserRepository()

	var workOrderService service.WorkOrderService
	if workOrderHandler != nil {
		workOrderService = workOrderHandler.GetService()
	}

	// 🔥 创建失败订单服务
	failedOrderService := service.NewFailedOrderService(
		orderRepository,
		systemConfigService,
		logger,
	)

	// 🔥 使用传入的平台订单号生成器（避免重复创建）

	// 🗑️ enhancedOrderQueryService 已删除 - 垃圾服务

	unifiedGatewayHandler := handler.NewUnifiedGatewayHandler(
		enhancedPriceService, // 🚀 修复：使用增强价格服务（带缓存）而不是普通价格服务
		orderHandler,
		orderService,
		// enhancedOrderQueryService 已删除
		workOrderService,
		failedOrderService, // 🔥 新增：失败订单服务
		providerManager,    // 🚀 新增：供应商管理器
		tokenService,
		expressMappingService,
		expressCompanyRepository,
		expressCompanyService, // 🚀 新增：快递公司服务
		systemConfigService,   // 🚀 新增：系统配置服务
		logger,
	)

	// 注入userRepository以支持client_id→user_id映射
	unifiedGatewayHandler.SetUserRepository(userRepository)

	gateway := r.Group("/api/gateway")
	{
		// 🚀 统一执行端点 - 所有功能通过apiMethod参数区分
		gateway.POST("/execute", unifiedGatewayHandler.Execute)
	}

	// 🔒 设置开放平台API路由（强制签名验证）- 保持向后兼容
	SetupOpenPlatformRouter(r, OpenPlatformRouterConfig{
		PriceService:             enhancedPriceService,
		OrderHandler:             orderHandler,
		TokenService:             tokenService,
		ExpressMappingService:    expressMappingService,
		ExpressCompanyRepository: expressCompanyRepository,
	})

	// 🌐 设置内部Web API路由（仅JWT认证）- 保持向后兼容
	SetupWebAPIRouter(r, OpenPlatformRouterConfig{
		PriceService:             enhancedPriceService,
		OrderHandler:             orderHandler,
		TokenService:             tokenService,
		ExpressMappingService:    expressMappingService,
		ExpressCompanyRepository: expressCompanyRepository,
	})

	// 🔄 保持向后兼容 - 原有API路径（逐步迁移到新架构）
	// 注意：这些路径现在会被签名验证拦截，建议客户端迁移到新的API路径
	v1 := r.Group("/api/v1")
	{
		// 快递相关接口（向后兼容，但现在需要签名验证）
		express := v1.Group("/express")
		express.Use(auth.JWTAuthMiddleware(tokenService))
		{
			// 🔒 价格查询接口（现在需要签名验证）
			simplePriceHandler := handler.NewSimplePriceHandler(enhancedPriceService, expressMappingService, expressCompanyRepository)
			express.POST("/price", auth.ScopeRequiredMiddleware("express:read"), simplePriceHandler.QuerySimplePrice)

			// 🔒 下单接口（现在需要签名验证）
			express.POST("/order", auth.ScopeRequiredMiddleware("express:write"), orderHandler.CreateSimpleOrder)

			// 订单管理接口
			express.POST("/order/cancel", auth.ScopeRequiredMiddleware("express:write"), orderHandler.CancelOrder)
			express.DELETE("/order/failed", auth.ScopeRequiredMiddleware("express:write"), orderHandler.DeleteFailedOrder) // 🔥 新增：删除失败订单
			express.POST("/order/query", auth.ScopeRequiredMiddleware("express:read"), orderHandler.QueryOrder)
			express.POST("/track", auth.ScopeRequiredMiddleware("express:read"), orderHandler.QueryTrack)

			// 订单列表和统计接口
			express.GET("/orders", auth.ScopeRequiredMiddleware("express:read"), orderHandler.GetOrderList)
			express.GET("/orders/statistics", auth.ScopeRequiredMiddleware("express:read"), orderHandler.GetOrderStatistics)

			// 🔥 新增：订单状态历史接口
			if statusHistoryHandler != nil {
				express.GET("/orders/:order_no/status-history", auth.ScopeRequiredMiddleware("express:read"), statusHistoryHandler.GetOrderStatusHistory)
				express.GET("/orders/status-history/:customer_order_no", auth.ScopeRequiredMiddleware("express:read"), statusHistoryHandler.GetOrderStatusHistoryByCustomerOrderNo)
				express.GET("/orders/status-history", auth.ScopeRequiredMiddleware("express:read"), statusHistoryHandler.GetUserOrdersStatusHistory)
			}
		}

		// 余额相关接口
		balance := v1.Group("/balance")
		balance.Use(auth.JWTAuthMiddleware(tokenService))
		{
			balance.GET("", balanceHandler.GetBalance)
			balance.POST("/deposit", balanceHandler.Deposit)
			balance.POST("/payment", balanceHandler.Payment)
			balance.POST("/refund", balanceHandler.Refund)
			balance.GET("/transactions", balanceHandler.GetTransactionHistory)

			// 优化版接口
			balance.GET("/transactions/optimized", optimizedBalanceHandler.GetTransactionHistoryOptimized)
			balance.GET("/health", optimizedBalanceHandler.HealthCheck)
			balance.POST("/cache/clear", optimizedBalanceHandler.ClearCache)
		}

		// 计费相关接口
		if billingHandler != nil {
			billing := v1.Group("/billing")
			billing.Use(auth.JWTAuthMiddleware(tokenService))
			{
				billing.GET("/order/:order_no", auth.ScopeRequiredMiddleware("express:read"), billingHandler.GetOrderBillingDetails)
				billing.GET("/history/:order_no", auth.ScopeRequiredMiddleware("express:read"), billingHandler.GetBillingHistory)
				billing.GET("/statistics", auth.ScopeRequiredMiddleware("express:read"), billingHandler.GetBillingStatistics)
				billing.POST("/process-difference", auth.ScopeRequiredMiddleware("express:write"), billingHandler.ProcessBillingDifference)
			}
		}

		// 地址解析相关接口
		if addressHandler != nil {
			address := v1.Group("/address")
			address.Use(auth.JWTAuthMiddleware(tokenService))
			{
				// 地址解析接口
				address.POST("/parse", auth.ScopeRequiredMiddleware("express:read"), addressHandler.ParseAddress)
				address.POST("/batch-parse", auth.ScopeRequiredMiddleware("express:read"), addressHandler.BatchParseAddress)

				// 地址验证接口
				address.POST("/validate", auth.ScopeRequiredMiddleware("express:read"), addressHandler.ValidateAddress)

				// 地区数据接口
				address.GET("/area-cascader", auth.ScopeRequiredMiddleware("express:read"), addressHandler.GetAreaCascader)
				address.GET("/search-areas", auth.ScopeRequiredMiddleware("express:read"), addressHandler.SearchAreas)

				// 解析历史接口
				address.GET("/history", auth.ScopeRequiredMiddleware("express:read"), addressHandler.GetParseHistory)
			}
		}
	}

	// 设置角色和权限相关路由
	SetupRolePermissionRouter(
		r,
		roleHandler,
		permissionHandler,
		userRoleHandler,
		authMiddleware,
		permissionMiddleware,
	)

	// 设置管理员认证路由（不需要认证中间件）
	if adminAuthHandler != nil {
		SetupAdminAuthRouter(r, AdminAuthRouterConfig{
			AdminAuthHandler: adminAuthHandler,
		})
	}

	// 设置管理员用户管理路由
	if adminUserHandler != nil && adminMiddleware != nil {
		SetupAdminUserRouter(r, AdminUserRouterConfig{
			AdminUserHandler: adminUserHandler,
			AuthMiddleware:   authMiddleware,
			AdminMiddleware:  adminMiddleware,
		})

		// 设置系统管理员用户管理路由（超级管理员功能）
		SetupSystemAdminUserRouter(r, AdminUserRouterConfig{
			AdminUserHandler: adminUserHandler,
			AuthMiddleware:   authMiddleware,
			AdminMiddleware:  adminMiddleware,
		})
	}

	// 🔥 设置统一回调路由（物流回调 + 工单回调）
	if callbackHandler != nil && authMiddleware != nil && adminMiddleware != nil {
		SetupCallbackRoutes(r, CallbackRouterConfig{
			CallbackHandler:            callbackHandler,
			WorkOrderCallbackForwarder: workOrderCallbackForwarder, // 🔥 添加工单回调转发器
			AuthMiddleware:             authMiddleware,
			AdminMiddleware:            adminMiddleware,
			TokenService:               tokenService,
		})
	}

	// 设置快递公司管理路由
	if expressCompanyHandler != nil && adminMiddleware != nil {
		SetupExpressCompanyRouter(r, ExpressCompanyRouterConfig{
			ExpressCompanyHandler: expressCompanyHandler,
			ExpressMappingHandler: expressMappingHandler,
			AuthMiddleware:        authMiddleware,
			AdminMiddleware:       adminMiddleware,
			TokenService:          tokenService,
		})
	}

	// 设置管理员订单管理路由
	if adminOrderHandler != nil && adminMiddleware != nil {
		// 管理员订单路由组
		adminOrderGroup := r.Group("/api/v1/admin/orders")

		// 应用认证中间件
		adminOrderGroup.Use(authMiddleware.RequireAuth())

		// 应用管理员权限中间件
		adminOrderGroup.Use(adminMiddleware.RequireAdmin())

		// 订单查询接口（需要读权限）
		readGroup := adminOrderGroup.Group("")
		readGroup.Use(adminMiddleware.RequireResourcePermission("order", "read"))
		readGroup.GET("", adminOrderHandler.GetAdminOrderList)
		readGroup.GET("/:id", adminOrderHandler.GetAdminOrderDetail)
		readGroup.GET("/:id/shipping-fee", adminOrderHandler.QueryProviderShippingFee)
		readGroup.GET("/:id/detailed-shipping-fee", adminOrderHandler.QueryDetailedProviderShippingFee)
		readGroup.POST("/batch/shipping-fee", adminOrderHandler.BatchQueryProviderShippingFee)

		// 订单状态管理接口（需要写权限）
		writeGroup := adminOrderGroup.Group("")
		writeGroup.Use(adminMiddleware.RequireResourcePermission("order", "write"))
		writeGroup.PUT("/:id/status", adminOrderHandler.UpdateOrderStatus)
		writeGroup.PUT("/batch/status", adminOrderHandler.BatchUpdateOrderStatus)
		writeGroup.POST("/batch/validate-prices", adminOrderHandler.BatchValidatePrices)
		writeGroup.POST("/:id/sync-status", adminOrderHandler.SyncOrderStatus)
		writeGroup.POST("/batch/sync-status", adminOrderHandler.BatchSyncOrderStatus)

		// 统计接口（需要统计权限）
		statsGroup := adminOrderGroup.Group("")
		statsGroup.Use(adminMiddleware.RequireResourcePermission("order", "stats"))
		statsGroup.GET("/statistics", adminOrderHandler.GetAdminOrderStatistics)

		// 导出接口（需要导出权限）
		exportGroup := adminOrderGroup.Group("")
		exportGroup.Use(adminMiddleware.RequireResourcePermission("order", "export"))
		exportGroup.GET("/export", adminOrderHandler.ExportOrders)
	}

	// 设置管理员余额管理路由
	if adminBalanceHandler != nil && adminMiddleware != nil {
		SetupAdminBalanceRouter(r, AdminBalanceRouterConfig{
			AdminBalanceHandler: adminBalanceHandler,
			AuthMiddleware:      authMiddleware,
			AdminMiddleware:     adminMiddleware,
		})
	}

	// 设置管理员系统配置管理路由
	if adminSystemConfigHandler != nil && adminMiddleware != nil {
		SetupAdminSystemConfigRouter(r, AdminSystemConfigRouterConfig{
			AdminSystemConfigHandler: adminSystemConfigHandler,
			AuthMiddleware:           authMiddleware,
			AdminMiddleware:          adminMiddleware,
		})
	}

	// 设置管理员仪表盘路由
	if adminDashboardHandler != nil && adminMiddleware != nil {
		SetupAdminDashboardRoutes(r, AdminDashboardRouterConfig{
			AdminDashboardHandler: adminDashboardHandler,
			AuthMiddleware:        authMiddleware,
			AdminMiddleware:       adminMiddleware,
		})
	}

	// 🚀 设置管理员供应商重载路由
	if adminProviderReloadHandler != nil && adminMiddleware != nil {
		SetupAdminProviderReloadRouter(r, AdminProviderReloadRouterConfig{
			AdminProviderReloadHandler: adminProviderReloadHandler,
			AuthMiddleware:             authMiddleware,
			AdminMiddleware:            adminMiddleware,
		})
	}

	// 🎯 设置管理员地区黑名单路由
	if regionBlacklistHandler != nil && adminMiddleware != nil {
		SetupAdminRegionBlacklistRouter(r, AdminRegionBlacklistRouterConfig{
			RegionBlacklistHandler: regionBlacklistHandler,
			AuthMiddleware:         authMiddleware,
			AdminMiddleware:        adminMiddleware,
		})
		logger.Info("✅ 管理员地区黑名单路由注册成功")
	}

	// 注意：所有价格表管理功能已废弃，改为直接使用实时API查询

	// 🚀 设置重量档位缓存路由
	if weightCacheHandler != nil {
		// 创建API v1路由组
		v1 := r.Group("/api/v1")

		// 注册重量档位缓存路由
		weightCacheHandler.RegisterRoutes(v1)

		logger.Info("✅ 重量档位缓存路由注册成功")
	}

	// 🚀 设置数据库优化管理路由
	if dbOptimizationHandler != nil && adminMiddleware != nil {
		// 数据库优化管理路由组
		dbOptimizationGroup := r.Group("/api/v1/admin/database")

		// 应用认证中间件
		dbOptimizationGroup.Use(authMiddleware.RequireAuth())

		// 应用管理员权限中间件
		dbOptimizationGroup.Use(adminMiddleware.RequireAdmin())

		// 性能监控接口
		dbOptimizationGroup.GET("/performance", dbOptimizationHandler.GetPerformanceReport)
		dbOptimizationGroup.GET("/metrics", dbOptimizationHandler.GetOptimizationMetrics)

		// 缓存管理接口
		dbOptimizationGroup.GET("/cache/stats", dbOptimizationHandler.GetCacheStats)
		dbOptimizationGroup.POST("/cache/invalidate", dbOptimizationHandler.InvalidateCache)

		// 慢查询监控接口
		dbOptimizationGroup.GET("/slow-queries", dbOptimizationHandler.GetSlowQueries)
	}

	// 🚀 设置工单管理路由（集成到统一回调系统中）
	if workOrderHandler != nil && authMiddleware != nil {
		// 工单管理路由组
		workOrderGroup := r.Group("/api/v1/workorders")

		// 应用认证中间件
		workOrderGroup.Use(authMiddleware.RequireAuth())

		// 应用JWT认证中间件
		workOrderGroup.Use(auth.JWTAuthMiddleware(tokenService))

		{
			// 创建工单（统一使用智能创建方式）
			workOrderGroup.POST("", auth.ScopeRequiredMiddleware("workorder:write"), workOrderHandler.CreateWorkOrder)

			// 获取工单详情
			workOrderGroup.GET("/:id", auth.ScopeRequiredMiddleware("workorder:read"), workOrderHandler.GetWorkOrder)

			// 获取工单列表
			workOrderGroup.GET("", auth.ScopeRequiredMiddleware("workorder:read"), workOrderHandler.ListWorkOrders)

			// 删除工单
			workOrderGroup.DELETE("/:id", auth.ScopeRequiredMiddleware("workorder:write"), workOrderHandler.DeleteWorkOrder)

			// 回复工单
			workOrderGroup.POST("/:id/replies", auth.ScopeRequiredMiddleware("workorder:write"), workOrderHandler.ReplyWorkOrder)

			// 上传附件
			workOrderGroup.POST("/attachments", auth.ScopeRequiredMiddleware("workorder:write"), workOrderHandler.UploadAttachment)

			// 获取支持的工单类型
			workOrderGroup.GET("/types", auth.ScopeRequiredMiddleware("workorder:read"), workOrderHandler.GetSupportedTypes)
		}
	}

	// 设置回调测试路由

	return r
}
