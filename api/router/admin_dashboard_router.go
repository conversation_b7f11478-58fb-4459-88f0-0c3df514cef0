package router

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
)

// AdminDashboardRouterConfig 管理员仪表盘路由配置
type AdminDashboardRouterConfig struct {
	AdminDashboardHandler *handler.AdminDashboardHandler
	AuthMiddleware        *middleware.AuthMiddleware
	AdminMiddleware       *middleware.AdminMiddleware
}

// SetupAdminDashboardRoutes 设置管理员仪表盘路由
func SetupAdminDashboardRoutes(router *gin.Engine, config AdminDashboardRouterConfig) {
	if config.AdminDashboardHandler == nil || config.AdminMiddleware == nil || config.AuthMiddleware == nil {
		return
	}

	// 管理员仪表盘路由组
	adminGroup := router.Group("/api/v1/admin")

	// 先应用认证中间件，再应用管理员权限中间件
	adminGroup.Use(config.AuthMiddleware.RequireAuth())
	adminGroup.Use(config.AdminMiddleware.RequireAdmin())

	// 仪表盘路由组
	dashboardGroup := adminGroup.Group("/dashboard")
	{
		// 获取仪表盘概览 - 需要基本管理员权限
		dashboardGroup.GET("/overview", config.AdminDashboardHandler.GetDashboardOverview)

		// 获取用户统计快照 - 需要用户读权限
		dashboardGroup.GET("/users/stats",
			config.AdminMiddleware.RequireResourcePermission("user", "read"),
			config.AdminDashboardHandler.GetUserStatsSnapshot)

		// 获取订单统计快照 - 需要订单读权限
		dashboardGroup.GET("/orders/stats",
			config.AdminMiddleware.RequireResourcePermission("order", "read"),
			config.AdminDashboardHandler.GetOrderStatsSnapshot)

		// 获取余额统计快照 - 需要余额读权限
		dashboardGroup.GET("/balance/stats",
			config.AdminMiddleware.RequireResourcePermission("balance", "admin_statistics"),
			config.AdminDashboardHandler.GetBalanceStatsSnapshot)

		// 获取系统健康状态 - 需要系统监控权限
		dashboardGroup.GET("/health",
			config.AdminMiddleware.RequireResourcePermission("system", "monitor"),
			config.AdminDashboardHandler.GetSystemHealth)

		// 获取性能指标 - 需要系统监控权限
		dashboardGroup.GET("/performance",
			config.AdminMiddleware.RequireResourcePermission("system", "monitor"),
			config.AdminDashboardHandler.GetPerformanceMetrics)

		// 获取最近活动 - 需要审计权限
		dashboardGroup.GET("/activities",
			config.AdminMiddleware.RequireResourcePermission("audit", "read"),
			config.AdminDashboardHandler.GetRecentActivities)

		// 获取实时统计 - 需要基本管理员权限
		dashboardGroup.GET("/realtime",
			config.AdminDashboardHandler.GetRealtimeStats)
	}
}

// AdminDashboardPermissions 管理员仪表盘权限定义
var AdminDashboardPermissions = map[string]string{
	"dashboard:view":  "查看仪表盘",
	"dashboard:stats": "查看统计数据",
	"system:monitor":  "系统监控",
	"audit:read":      "查看审计日志",
}

// RegisterAdminDashboardPermissions 注册管理员仪表盘权限
func RegisterAdminDashboardPermissions() map[string]string {
	return AdminDashboardPermissions
}
