package router

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
	"github.com/your-org/go-kuaidi/internal/security"
)

// AdminAuthRouterConfig 管理员认证路由配置
type AdminAuthRouterConfig struct {
	AdminAuthHandler *handler.AdminAuthHandler
}

// SetupAdminAuthRouter 设置管理员认证路由
func SetupAdminAuthRouter(engine *gin.Engine, config AdminAuthRouterConfig) {
	// 安全配置
	securityConfig := security.HeadersConfig{
		Enabled:               true,
		HSTSMaxAgeSeconds:     31536000, // 1年
		HSTSIncludeSubdomains: true,
		XFrameOptions:         "DENY",
		ContentSecurityPolicy: "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'",
		ReferrerPolicy:        "strict-origin-when-cross-origin",
		FeaturePolicy:         "geolocation 'none'; microphone 'none'; camera 'none'",
	}

	// 管理员认证API组 - 添加安全中间件但不需要认证中间件
	adminAuthGroup := engine.Group("/api/v1/admin/auth")
	adminAuthGroup.Use(middleware.SecurityHeaders(securityConfig))
	adminAuthGroup.Use(middleware.AdminLoginRateLimitMiddleware())
	{
		// 管理员登录接口
		adminAuthGroup.POST("/login", config.AdminAuthHandler.AdminLogin)
	}
}
