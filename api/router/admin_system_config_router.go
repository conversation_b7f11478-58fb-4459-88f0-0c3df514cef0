package router

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/middleware"
	"github.com/your-org/go-kuaidi/internal/handler"
)

// AdminSystemConfigRouterConfig 管理员系统配置路由配置
type AdminSystemConfigRouterConfig struct {
	AdminSystemConfigHandler *handler.AdminSystemConfigHandler
	AuthMiddleware           *middleware.AuthMiddleware
	AdminMiddleware          *middleware.AdminMiddleware
}

// SetupAdminSystemConfigRouter 设置管理员系统配置管理路由
func SetupAdminSystemConfigRouter(engine *gin.Engine, config AdminSystemConfigRouterConfig) {
	// 管理员系统配置管理API组
	adminGroup := engine.Group("/api/v1/admin")

	// 应用认证中间件
	adminGroup.Use(config.AuthMiddleware.RequireAuth())

	// 应用管理员权限中间件
	adminGroup.Use(config.AdminMiddleware.RequireAdmin())

	// 注册系统配置管理路由
	config.AdminSystemConfigHandler.RegisterRoutes(adminGroup)
}
