package router

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
)

// AdminBalanceRouterConfig 管理员余额路由配置
type AdminBalanceRouterConfig struct {
	AdminBalanceHandler *handler.AdminBalanceHandler
	AuthMiddleware      *middleware.AuthMiddleware
	AdminMiddleware     *middleware.AdminMiddleware
}

// SetupAdminBalanceRouter 设置管理员余额管理路由
func SetupAdminBalanceRouter(engine *gin.Engine, config AdminBalanceRouterConfig) {
	// 管理员余额管理API组
	adminGroup := engine.Group("/api/v1/admin")

	// 应用认证中间件
	adminGroup.Use(config.AuthMiddleware.RequireAuth())

	// 应用管理员权限中间件
	adminGroup.Use(config.AdminMiddleware.RequireAdmin())

	// 余额管理路由组
	balanceGroup := adminGroup.Group("/balance")
	{
		// 查询接口 - 基本管理员权限即可
		// 获取余额总览
		balanceGroup.GET("/overview", config.AdminBalanceHandler.GetBalanceOverview)

		// 获取用户余额列表
		balanceGroup.GET("/users", config.AdminBalanceHandler.GetUserBalanceList)

		// 获取用户余额详情
		balanceGroup.GET("/users/:user_id", config.AdminBalanceHandler.GetUserBalanceDetail)

		// 获取余额异常记录
		balanceGroup.GET("/anomalies", config.AdminBalanceHandler.GetBalanceAnomalies)

		// 操作接口 - 基本管理员权限即可
		// 手动充值
		balanceGroup.POST("/manual-deposit", config.AdminBalanceHandler.ManualDeposit)

		// 余额调整
		balanceGroup.POST("/adjustment", config.AdminBalanceHandler.AdjustBalance)

		// 批量操作
		balanceGroup.POST("/batch-operation", config.AdminBalanceHandler.BatchOperation)

		// 退款权限组 - 需要余额退款权限
		refundGroup := balanceGroup.Group("")
		refundGroup.Use(config.AdminMiddleware.RequireResourcePermission("balance", "admin_refund"))
		{
			// 强制退款
			refundGroup.POST("/force-refund", config.AdminBalanceHandler.ForceRefund)
		}

		// 统计权限组 - 需要余额统计权限
		statisticsGroup := balanceGroup.Group("")
		statisticsGroup.Use(config.AdminMiddleware.RequireResourcePermission("balance", "admin_statistics"))
		{
			// 余额统计报表
			statisticsGroup.GET("/statistics", config.AdminBalanceHandler.GetBalanceStatistics)

			// 交易统计报表
			statisticsGroup.GET("/transaction-statistics", config.AdminBalanceHandler.GetTransactionStatistics)
		}

		// 审计权限组 - 需要余额审计权限
		auditGroup := balanceGroup.Group("")
		auditGroup.Use(config.AdminMiddleware.RequireResourcePermission("balance", "admin_audit"))
		{
			// 获取审计日志
			auditGroup.GET("/audit-logs", config.AdminBalanceHandler.GetAuditLogs)

			// 导出审计日志
			auditGroup.GET("/audit-logs/export", config.AdminBalanceHandler.ExportAuditLogs)
		}

		// 导出权限组 - 需要余额导出权限
		exportGroup := balanceGroup.Group("")
		exportGroup.Use(config.AdminMiddleware.RequireResourcePermission("balance", "admin_export"))
		{
			// 导出用户余额数据
			exportGroup.GET("/users/export", config.AdminBalanceHandler.ExportUserBalances)

			// 导出交易记录
			exportGroup.GET("/transactions/export", config.AdminBalanceHandler.ExportTransactions)
		}
	}
}
