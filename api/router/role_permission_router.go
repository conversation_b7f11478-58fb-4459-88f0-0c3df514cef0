package router

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/middleware"
	"github.com/your-org/go-kuaidi/internal/api"
	internal_middleware "github.com/your-org/go-kuaidi/internal/middleware"
)

// SetupRolePermissionRouter 设置角色和权限相关路由
func SetupRolePermissionRouter(
	engine *gin.Engine,
	roleHandler api.RoleHandlerInterface,
	permissionHandler *api.PermissionHandler,
	userRoleHandler *api.UserRoleHandler,
	authMiddleware *middleware.AuthMiddleware,
	permissionMiddleware *internal_middleware.PermissionMiddleware,
) {
	// 角色和权限管理API
	adminGroup := engine.Group("/api/v1/admin")
	adminGroup.Use(authMiddleware.RequireAuth())
	{
		// 角色管理API
		roleGroup := adminGroup.Group("/roles")
		{
			// 创建角色 - 需要角色创建权限
			roleGroup.POST("", permissionMiddleware.RequireResourcePermission("role", "create"), roleHandler.CreateRole)
			// 获取所有角色 - 需要角色读取权限
			roleGroup.GET("", permissionMiddleware.RequireResourcePermission("role", "read"), roleHandler.GetAllRoles)
			// 获取角色 - 需要角色读取权限
			roleGroup.GET("/:id", permissionMiddleware.RequireResourcePermission("role", "read"), roleHandler.GetRole)
			// 更新角色 - 需要角色更新权限
			roleGroup.PUT("/:id", permissionMiddleware.RequireResourcePermission("role", "update"), roleHandler.UpdateRole)
			// 删除角色 - 需要角色删除权限
			roleGroup.DELETE("/:id", permissionMiddleware.RequireResourcePermission("role", "delete"), roleHandler.DeleteRole)
			// 获取拥有指定角色的所有用户 - 暂时移除权限验证
			engine.GET("/api/v1/admin/roles-users/:roleId", userRoleHandler.GetUsersByRole)

			// 为角色添加权限 - 需要角色更新权限
			roleGroup.POST("/:id/permissions", permissionMiddleware.RequireResourcePermission("role", "update"), func(c *gin.Context) {
				roleID := c.Param("id")
				var req struct {
					PermissionID string `json:"permission_id" binding:"required"`
				}
				if err := c.ShouldBindJSON(&req); err != nil {
					c.JSON(http.StatusBadRequest, gin.H{
						"error": "无效的请求参数: " + err.Error(),
					})
					return
				}

				err := roleHandler.AddPermissionToRole(c, roleID, req.PermissionID)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{
						"error": "添加权限失败: " + err.Error(),
					})
					return
				}

				c.Status(http.StatusNoContent)
			})

			// 从角色中移除权限 - 需要角色更新权限
			roleGroup.DELETE("/:id/permissions/:permissionId", permissionMiddleware.RequireResourcePermission("role", "update"), func(c *gin.Context) {
				roleID := c.Param("id")
				permissionID := c.Param("permissionId")

				err := roleHandler.RemovePermissionFromRole(c, roleID, permissionID)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{
						"error": "移除权限失败: " + err.Error(),
					})
					return
				}

				c.Status(http.StatusNoContent)
			})
		}

		// 权限管理API
		permissionGroup := adminGroup.Group("/permissions")
		{
			// 创建权限 - 需要权限创建权限
			permissionGroup.POST("", permissionMiddleware.RequireResourcePermission("permission", "create"), permissionHandler.CreatePermission)
			// 获取所有权限 - 需要权限读取权限
			permissionGroup.GET("", permissionMiddleware.RequireResourcePermission("permission", "read"), permissionHandler.GetAllPermissions)
			// 获取权限 - 需要权限读取权限
			permissionGroup.GET("/:id", permissionMiddleware.RequireResourcePermission("permission", "read"), permissionHandler.GetPermission)
			// 更新权限 - 需要权限更新权限
			permissionGroup.PUT("/:id", permissionMiddleware.RequireResourcePermission("permission", "update"), permissionHandler.UpdatePermission)
			// 删除权限 - 需要权限删除权限
			permissionGroup.DELETE("/:id", permissionMiddleware.RequireResourcePermission("permission", "delete"), permissionHandler.DeletePermission)
		}
	}

	// 用户角色管理API
	userGroup := engine.Group("/api/v1/users")
	userGroup.Use(authMiddleware.RequireAuth())
	{
		// 获取用户的所有角色 - 需要用户读取权限
		userGroup.GET("/:userId/roles", permissionMiddleware.RequireResourcePermission("user", "read"), userRoleHandler.GetUserRoles)

		// 以下API需要用户更新权限
		userRoleGroup := userGroup.Group("", permissionMiddleware.RequireResourcePermission("user", "update"))
		{
			// 为用户添加角色
			userRoleGroup.POST("/:userId/roles/:roleId", userRoleHandler.AddRoleToUser)
			// 从用户中移除角色
			userRoleGroup.DELETE("/:userId/roles/:roleId", userRoleHandler.RemoveRoleFromUser)
			// 设置用户的默认角色
			userRoleGroup.PUT("/:userId/default-role/:roleId", userRoleHandler.SetDefaultRoleForUser)
		}
	}
}
