package router

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/api/middleware"
)

// AdminUserRouterConfig 管理员用户路由配置
type AdminUserRouterConfig struct {
	AdminUserHandler *handler.AdminUserHandler
	AuthMiddleware   *middleware.AuthMiddleware
	AdminMiddleware  *middleware.AdminMiddleware
}

// SetupAdminUserRouter 设置管理员用户管理路由
func SetupAdminUserRouter(engine *gin.Engine, config AdminUserRouterConfig) {
	// 管理员用户管理API组
	adminGroup := engine.Group("/api/v1/admin")

	// 应用认证中间件
	adminGroup.Use(config.AuthMiddleware.RequireAuth())

	// 应用管理员权限中间件
	adminGroup.Use(config.AdminMiddleware.RequireAdmin())

	// 用户管理路由组
	userGroup := adminGroup.Group("/users")
	{
		// 获取用户列表 - 需要用户管理权限
		userGroup.GET("",
			config.AdminMiddleware.RequireResourcePermission("user", "read"),
			config.AdminUserHandler.GetUsers)

		// 获取用户统计信息 - 需要用户管理权限
		userGroup.GET("/statistics",
			config.AdminMiddleware.RequireResourcePermission("user", "read"),
			config.AdminUserHandler.GetUserStatistics)

		// 创建用户 - 需要用户创建权限
		userGroup.POST("",
			config.AdminMiddleware.RequireResourcePermission("user", "create"),
			config.AdminUserHandler.CreateUser)

		// 创建用户（带审计） - 需要用户创建权限
		userGroup.POST("/with-audit",
			config.AdminMiddleware.RequireResourcePermission("user", "create"),
			config.AdminUserHandler.CreateUserWithAudit)

		// 获取用户详情 - 需要用户读取权限
		userGroup.GET("/:id",
			config.AdminMiddleware.RequireResourcePermission("user", "read"),
			config.AdminUserHandler.GetUser)

		// 更新用户信息 - 需要用户更新权限，防止自我修改
		userGroup.PUT("/:id",
			config.AdminMiddleware.RequireResourcePermission("user", "update"),
			config.AdminMiddleware.PreventSelfModification(),
			config.AdminUserHandler.UpdateUser)

		// 更新用户信息（带审计） - 需要用户更新权限，防止自我修改
		userGroup.PUT("/:id/with-audit",
			config.AdminMiddleware.RequireResourcePermission("user", "update"),
			config.AdminMiddleware.PreventSelfModification(),
			config.AdminUserHandler.UpdateUserWithAudit)

		// 更新用户状态 - 需要用户更新权限，防止自我修改
		userGroup.PATCH("/:id/status",
			config.AdminMiddleware.RequireResourcePermission("user", "update"),
			config.AdminMiddleware.PreventSelfModification(),
			config.AdminUserHandler.UpdateUserStatus)

		// 重置用户密码 - 需要用户更新权限，防止自我修改
		userGroup.PATCH("/:id/password",
			config.AdminMiddleware.RequireResourcePermission("user", "update"),
			config.AdminMiddleware.PreventSelfModification(),
			config.AdminUserHandler.ResetPassword)

		// 重置用户密码 - POST方式（兼容测试脚本）
		userGroup.POST("/:id/reset-password",
			config.AdminMiddleware.RequireResourcePermission("user", "update"),
			config.AdminMiddleware.PreventSelfModification(),
			config.AdminUserHandler.ResetPassword)

		// 删除用户 - 需要用户删除权限，防止自我删除
		userGroup.DELETE("/:id",
			config.AdminMiddleware.RequireResourcePermission("user", "delete"),
			config.AdminMiddleware.PreventSelfModification(),
			config.AdminUserHandler.DeleteUser)

		// 强制删除用户（硬删除） - 需要用户删除权限，防止自我删除
		userGroup.DELETE("/:id/force",
			config.AdminMiddleware.RequireResourcePermission("user", "delete"),
			config.AdminMiddleware.PreventSelfModification(),
			config.AdminUserHandler.ForceDeleteUser)
	}

	// 审计日志路由组
	auditGroup := adminGroup.Group("/audit-logs")
	{
		// 获取审计日志 - 需要审计读取权限
		auditGroup.GET("",
			config.AdminMiddleware.RequireResourcePermission("audit", "read"),
			config.AdminUserHandler.GetAuditLogs)
	}
}

// SetupSystemAdminUserRouter 设置系统管理员用户管理路由（超级管理员功能）
func SetupSystemAdminUserRouter(engine *gin.Engine, config AdminUserRouterConfig) {
	// 系统管理员API组
	systemAdminGroup := engine.Group("/api/v1/system/admin")

	// 应用认证中间件
	systemAdminGroup.Use(config.AuthMiddleware.RequireAuth())

	// 应用系统管理员权限中间件
	systemAdminGroup.Use(config.AdminMiddleware.RequireSystemAdmin())

	// 系统级用户管理路由组
	userGroup := systemAdminGroup.Group("/users")
	{
		// 批量操作用户状态 - 仅系统管理员
		userGroup.PATCH("/batch/status", config.AdminUserHandler.BatchUpdateUserStatus)

		// 强制删除用户（硬删除） - 仅系统管理员
		userGroup.DELETE("/:id/force", config.AdminUserHandler.ForceDeleteUser)

		// 恢复已删除用户 - 仅系统管理员
		userGroup.POST("/:id/restore", config.AdminUserHandler.RestoreUser)

		// 获取已删除用户列表 - 仅系统管理员
		userGroup.GET("/deleted", config.AdminUserHandler.GetDeletedUsers)
	}
}

// SetupUserManagementAPIDoc 设置用户管理API文档路由
func SetupUserManagementAPIDoc(engine *gin.Engine) {
	// API文档路由
	docGroup := engine.Group("/api/docs")
	{
		// 用户管理API文档
		docGroup.GET("/user-management", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"title":       "用户管理API文档",
				"version":     "v1.0.0",
				"description": "企业级用户管理系统API接口文档",
				"endpoints": map[string]interface{}{
					"user_management": map[string]interface{}{
						"base_path": "/api/v1/admin/users",
						"endpoints": []map[string]interface{}{
							{
								"method":      "GET",
								"path":        "",
								"description": "获取用户列表",
								"permissions": []string{"user:read"},
								"parameters": map[string]string{
									"page":      "页码",
									"page_size": "每页大小",
									"keyword":   "关键词搜索",
									"status":    "状态过滤",
									"role_id":   "角色过滤",
									"order_by":  "排序字段",
									"order":     "排序方向",
								},
							},
							{
								"method":      "POST",
								"path":        "",
								"description": "创建用户",
								"permissions": []string{"user:create"},
								"body": map[string]string{
									"username":  "用户名",
									"email":     "邮箱",
									"password":  "密码",
									"role_id":   "角色ID",
									"is_active": "是否激活",
								},
							},
							{
								"method":      "GET",
								"path":        "/{id}",
								"description": "获取用户详情",
								"permissions": []string{"user:read"},
							},
							{
								"method":       "PUT",
								"path":         "/{id}",
								"description":  "更新用户信息",
								"permissions":  []string{"user:update"},
								"restrictions": []string{"不能修改自己的账号"},
							},
							{
								"method":       "PATCH",
								"path":         "/{id}/status",
								"description":  "更新用户状态",
								"permissions":  []string{"user:update"},
								"restrictions": []string{"不能修改自己的账号状态"},
							},
							{
								"method":       "PATCH",
								"path":         "/{id}/password",
								"description":  "重置用户密码",
								"permissions":  []string{"user:update"},
								"restrictions": []string{"不能重置自己的密码"},
							},
							{
								"method":       "DELETE",
								"path":         "/{id}",
								"description":  "删除用户",
								"permissions":  []string{"user:delete"},
								"restrictions": []string{"不能删除自己的账号", "不能删除管理员账号"},
							},
							{
								"method":      "GET",
								"path":        "/statistics",
								"description": "获取用户统计信息",
								"permissions": []string{"user:read"},
							},
						},
					},
					"system_admin": map[string]interface{}{
						"base_path":   "/api/v1/system/admin/users",
						"description": "系统管理员专用功能",
						"permissions": []string{"system:admin"},
						"endpoints": []map[string]interface{}{
							{
								"method":      "PATCH",
								"path":        "/batch/status",
								"description": "批量更新用户状态",
							},
							{
								"method":      "DELETE",
								"path":        "/{id}/force",
								"description": "强制删除用户（硬删除）",
							},
							{
								"method":      "POST",
								"path":        "/{id}/restore",
								"description": "恢复已删除用户",
							},
							{
								"method":      "GET",
								"path":        "/deleted",
								"description": "获取已删除用户列表",
							},
						},
					},
				},
				"security": map[string]interface{}{
					"authentication": "Bearer Token",
					"authorization":  "基于RBAC的权限控制",
					"permissions": map[string]string{
						"user:read":    "用户读取权限",
						"user:create":  "用户创建权限",
						"user:update":  "用户更新权限",
						"user:delete":  "用户删除权限",
						"system:admin": "系统管理员权限",
					},
				},
				"error_codes": map[string]interface{}{
					"400": "请求参数错误",
					"401": "未认证",
					"403": "权限不足",
					"404": "资源不存在",
					"409": "资源冲突",
					"500": "服务器内部错误",
				},
			})
		})
	}
}
