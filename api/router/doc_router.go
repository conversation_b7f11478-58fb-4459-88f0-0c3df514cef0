package router

import (
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"github.com/your-org/go-kuaidi/api/handler"
)

// SetupDocRouter 设置文档路由
func SetupDocRouter(engine *gin.Engine) {
	// 创建文档处理器
	docHandler := handler.NewDocHandler()

	// 加载HTML模板
	engine.LoadHTMLGlob("templates/*")

	// Swagger文档
	engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 文档路由
	engine.GET("/docs", docHandler.HandleDocIndex)
	engine.GET("/docs/zh-cn", docHandler.HandleChineseDoc)
}
