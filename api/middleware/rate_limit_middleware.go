package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/security"
	"github.com/your-org/go-kuaidi/internal/util"

)

// RateLimitMiddleware 速率限制中间件
func RateLimitMiddleware(rateLimitService security.RateLimitService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求路径
		path := c.Request.URL.Path

		// 🔒 登录相关接口始终启用限速保护（安全要求）
		isLoginEndpoint := path == "/oauth/token" ||
			path == "/api/v1/users/register" ||
			path == "/api/v1/users/reset-client-secret"

		// 如果未启用速率限制且不是登录接口，直接跳过
		if !rateLimitService.IsEnabled() && !isLoginEndpoint {
			c.Next()
			return
		}

		// 允许 /api/v1/callbacks 下的所有请求不限速，直接放行
		if strings.HasPrefix(path, "/api/v1/callbacks") {
			c.Next()
			return
		}

		// 获取客户端IP
		ip := getClientIP(c)

		// 获取客户端ID和用户ID
		clientID := getClientID(c)
		userID := c.GetString("user_id")

		// 创建上下文
		ctx := context.Background()

		// 检查IP限制
		// 对于登录接口，强制进行限速检查（即使全局限速被禁用）
		var ipResult *security.RateLimitResult
		var err error
		if isLoginEndpoint {
			ipResult, err = forceCheckRateLimit(rateLimitService, ctx, security.DimensionIP, ip, path)
		} else {
			ipResult, err = rateLimitService.CheckRateLimit(ctx, security.DimensionIP, ip, path)
		}
		if err != nil {
			// 如果是速率限制错误，返回429状态码
			if err == security.ErrRateLimitExceeded {
				setRateLimitHeaders(c, ipResult)
				c.JSON(http.StatusTooManyRequests, gin.H{
					"success":     false,
					"code":        http.StatusTooManyRequests,
					"message":     "IP rate limit exceeded",
					"error":       fmt.Sprintf("Too many requests: %d/%d. Try again in %v", ipResult.CurrentCount, ipResult.Limit, ipResult.RetryAfter),
					"retry_after": int(ipResult.RetryAfter.Seconds()),
				})
				c.Abort()
				return
			}

			// 其他错误，记录但继续处理
			c.Set("rate_limit_error", err.Error())
		} else if !ipResult.Allowed {
			setRateLimitHeaders(c, ipResult)
			c.JSON(http.StatusTooManyRequests, gin.H{
				"success":     false,
				"code":        http.StatusTooManyRequests,
				"message":     "IP rate limit exceeded",
				"error":       fmt.Sprintf("Too many requests: %d/%d. Try again in %v", ipResult.CurrentCount, ipResult.Limit, ipResult.RetryAfter),
				"retry_after": int(ipResult.RetryAfter.Seconds()),
			})
			c.Abort()
			return
		}

		// 如果有客户端ID，检查客户端限制
		if clientID != "" {
			var clientResult *security.RateLimitResult
			if isLoginEndpoint {
				clientResult, err = forceCheckRateLimit(rateLimitService, ctx, security.DimensionClient, clientID, path)
			} else {
				clientResult, err = rateLimitService.CheckRateLimit(ctx, security.DimensionClient, clientID, path)
			}
			if err != nil {
				// 如果是速率限制错误，返回429状态码
				if err == security.ErrRateLimitExceeded {
					setRateLimitHeaders(c, clientResult)
					c.JSON(http.StatusTooManyRequests, gin.H{
						"success":     false,
						"code":        http.StatusTooManyRequests,
						"message":     "Client rate limit exceeded",
						"error":       fmt.Sprintf("Too many requests: %d/%d. Try again in %v", clientResult.CurrentCount, clientResult.Limit, clientResult.RetryAfter),
						"retry_after": int(clientResult.RetryAfter.Seconds()),
					})
					c.Abort()
					return
				}

				// 其他错误，记录但继续处理
				c.Set("rate_limit_error", err.Error())
			} else if !clientResult.Allowed {
				setRateLimitHeaders(c, clientResult)
				c.JSON(http.StatusTooManyRequests, gin.H{
					"success":     false,
					"code":        http.StatusTooManyRequests,
					"message":     "Client rate limit exceeded",
					"error":       fmt.Sprintf("Too many requests: %d/%d. Try again in %v", clientResult.CurrentCount, clientResult.Limit, clientResult.RetryAfter),
					"retry_after": int(clientResult.RetryAfter.Seconds()),
				})
				c.Abort()
				return
			}
		}

		// 如果有用户ID，检查用户限制
		if userID != "" {
			var userResult *security.RateLimitResult
			if isLoginEndpoint {
				userResult, err = forceCheckRateLimit(rateLimitService, ctx, security.DimensionUser, userID, path)
			} else {
				userResult, err = rateLimitService.CheckRateLimit(ctx, security.DimensionUser, userID, path)
			}
			if err != nil {
				// 如果是速率限制错误，返回429状态码
				if err == security.ErrRateLimitExceeded {
					setRateLimitHeaders(c, userResult)
					c.JSON(http.StatusTooManyRequests, gin.H{
						"success":     false,
						"code":        http.StatusTooManyRequests,
						"message":     "User rate limit exceeded",
						"error":       fmt.Sprintf("Too many requests: %d/%d. Try again in %v", userResult.CurrentCount, userResult.Limit, userResult.RetryAfter),
						"retry_after": int(userResult.RetryAfter.Seconds()),
					})
					c.Abort()
					return
				}

				// 其他错误，记录但继续处理
				c.Set("rate_limit_error", err.Error())
			} else if !userResult.Allowed {
				setRateLimitHeaders(c, userResult)
				c.JSON(http.StatusTooManyRequests, gin.H{
					"success":     false,
					"code":        http.StatusTooManyRequests,
					"message":     "User rate limit exceeded",
					"error":       fmt.Sprintf("Too many requests: %d/%d. Try again in %v", userResult.CurrentCount, userResult.Limit, userResult.RetryAfter),
					"retry_after": int(userResult.RetryAfter.Seconds()),
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// setRateLimitHeaders 设置速率限制相关的HTTP头
func setRateLimitHeaders(c *gin.Context, result *security.RateLimitResult) {
	// 设置Retry-After头
	if result.RetryAfter > 0 {
		c.Header("Retry-After", fmt.Sprintf("%d", int(result.RetryAfter.Seconds())))
	}

	// 设置RateLimit头（符合RFC标准）
	c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", result.Limit))
	c.Header("X-RateLimit-Remaining", fmt.Sprintf("%d", result.Remaining))
	c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", util.NowBeijing().Add(result.RetryAfter).Unix()))
}

// getClientIP 获取客户端真实IP
func getClientIP(c *gin.Context) string {
	// 首先检查X-Forwarded-For头
	xForwardedFor := c.Request.Header.Get("X-Forwarded-For")
	if xForwardedFor != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(xForwardedFor, ",")
		if len(ips) > 0 {
			clientIP := strings.TrimSpace(ips[0])
			if clientIP != "" {
				return clientIP
			}
		}
	}

	// 检查X-Real-IP头
	xRealIP := c.Request.Header.Get("X-Real-IP")
	if xRealIP != "" {
		return xRealIP
	}

	// 使用Gin的ClientIP方法
	return c.ClientIP()
}

// getClientID 获取客户端ID
func getClientID(c *gin.Context) string {
	// 首先尝试从签名中获取
	clientID := c.GetString("client_id_from_signature")
	if clientID != "" {
		return clientID
	}

	// 然后尝试从认证中获取
	clientID = c.GetString("client_id")
	if clientID != "" {
		return clientID
	}

	// 最后尝试从请求头中获取
	return c.Request.Header.Get("X-Client-ID")
}

// forceCheckRateLimit 强制检查限速（忽略全局启用状态，用于登录接口）
func forceCheckRateLimit(rateLimitService security.RateLimitService, ctx context.Context, dimension security.RateLimitDimension, id string, path string) (*security.RateLimitResult, error) {
	// 获取底层的DefaultRateLimitService
	if defaultService, ok := rateLimitService.(*security.DefaultRateLimitService); ok {
		// 直接调用限速逻辑，跳过IsEnabled检查
		return defaultService.ForceCheckRateLimit(ctx, dimension, id, path)
	}

	// 如果不是DefaultRateLimitService，回退到正常检查
	return rateLimitService.CheckRateLimit(ctx, dimension, id, path)
}
