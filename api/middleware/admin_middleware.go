package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/user"
	"go.uber.org/zap"
)

// AdminMiddleware 管理员权限中间件
type AdminMiddleware struct {
	userRoleService user.UserRoleService
	configManager   *config.ConfigManager
	logger          *zap.Logger
}

// NewAdminMiddleware 创建管理员权限中间件
func NewAdminMiddleware(userRoleService user.UserRoleService, configManager *config.ConfigManager, logger *zap.Logger) *AdminMiddleware {
	return &AdminMiddleware{
		userRoleService: userRoleService,
		configManager:   configManager,
		logger:          logger,
	}
}

// RequireAdmin 要求管理员权限
func (m *AdminMiddleware) RequireAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := getRequestID(c)

		// 从上下文中获取用户ID
		userID, exists := c.Get("userID")
		if !exists {
			m.logger.Warn("User ID not found in context", zap.String("request_id", requestID))
			err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		userIDStr, ok := userID.(string)
		if !ok || userIDStr == "" {
			m.logger.Warn("Invalid user ID in context",
				zap.String("request_id", requestID),
				zap.Any("user_id", userID))
			err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "无效的用户ID")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		// 检查用户是否拥有管理员角色
		adminRoleID := "admin"
		if adminRole, exists := m.configManager.GetRole("admin"); exists {
			adminRoleID = adminRole.ID
		}

		hasAdminRole, err := m.userRoleService.HasRole(userIDStr, adminRoleID)
		if err != nil {
			m.logger.Error("Failed to check admin role",
				zap.String("request_id", requestID),
				zap.String("user_id", userIDStr),
				zap.Error(err))
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal,
				m.configManager.GetErrorMessage("internal_error", "zh-CN"))
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
			c.Abort()
			return
		}

		if !hasAdminRole {
			m.logger.Warn("User attempted to access admin resource without admin role",
				zap.String("request_id", requestID),
				zap.String("user_id", userIDStr),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method))
			err := errors.NewBusinessError(errors.ErrCodeForbidden,
				m.configManager.GetErrorMessage("permission_denied", "zh-CN"))
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		// 将管理员标识存储到上下文中
		c.Set("is_admin", true)
		c.Set("admin_user_id", userIDStr)
		c.Set("user_id", userIDStr) // 为了兼容handler中的检查

		m.logger.Info("Admin access granted",
			zap.String("request_id", requestID),
			zap.String("user_id", userIDStr),
			zap.String("path", c.Request.URL.Path),
			zap.String("method", c.Request.Method))

		c.Next()
	}
}

// RequireUserManagementPermission 要求用户管理权限
func (m *AdminMiddleware) RequireUserManagementPermission() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := getRequestID(c)

		// 从上下文中获取用户ID
		userID, exists := c.Get("userID")
		if !exists {
			m.logger.Warn("User ID not found in context", zap.String("request_id", requestID))
			err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		userIDStr := userID.(string)

		// 检查用户管理权限
		hasPermission, err := m.userRoleService.HasPermission(userIDStr, "user:manage")
		if err != nil {
			m.logger.Error("Failed to check user management permission",
				zap.String("request_id", requestID),
				zap.String("user_id", userIDStr),
				zap.Error(err))
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "权限检查失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
			c.Abort()
			return
		}

		if !hasPermission {
			m.logger.Warn("User attempted to access user management without permission",
				zap.String("request_id", requestID),
				zap.String("user_id", userIDStr),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method))
			err := errors.NewBusinessError(errors.ErrCodeForbidden, "需要用户管理权限")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireResourcePermission 要求特定资源权限
func (m *AdminMiddleware) RequireResourcePermission(resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := getRequestID(c)

		// 从上下文中获取用户ID
		userID, exists := c.Get("userID")
		if !exists {
			m.logger.Warn("User ID not found in context", zap.String("request_id", requestID))
			err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		userIDStr := userID.(string)

		// 检查资源权限
		hasPermission, err := m.userRoleService.HasResourcePermission(userIDStr, resource, action)
		if err != nil {
			m.logger.Error("Failed to check resource permission",
				zap.String("request_id", requestID),
				zap.String("user_id", userIDStr),
				zap.String("resource", resource),
				zap.String("action", action),
				zap.Error(err))
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal, "权限检查失败")
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
			c.Abort()
			return
		}

		if !hasPermission {
			m.logger.Warn("User attempted to access resource without permission",
				zap.String("request_id", requestID),
				zap.String("user_id", userIDStr),
				zap.String("resource", resource),
				zap.String("action", action),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method))
			err := errors.NewBusinessError(errors.ErrCodeForbidden, "权限不足")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		c.Next()
	}
}

// PreventSelfModification 防止用户修改自己的账号
func (m *AdminMiddleware) PreventSelfModification() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := getRequestID(c)

		// 从上下文中获取当前用户ID
		currentUserID, exists := c.Get("userID")
		if !exists {
			m.logger.Warn("User ID not found in context", zap.String("request_id", requestID))
			err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		// 从路径参数中获取目标用户ID
		targetUserID := c.Param("id")
		if targetUserID == "" {
			targetUserID = c.Param("user_id")
		}

		// 检查是否尝试修改自己的账号
		if currentUserID.(string) == targetUserID {
			m.logger.Warn("User attempted to modify their own account",
				zap.String("request_id", requestID),
				zap.String("user_id", currentUserID.(string)),
				zap.String("target_user_id", targetUserID),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method))
			err := errors.NewBusinessError(errors.ErrCodeForbidden, "不能修改自己的账号")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireSystemAdmin 要求系统管理员权限（超级管理员）
func (m *AdminMiddleware) RequireSystemAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := getRequestID(c)

		// 从上下文中获取用户ID
		userID, exists := c.Get("userID")
		if !exists {
			m.logger.Warn("User ID not found in context", zap.String("request_id", requestID))
			err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		userIDStr := userID.(string)

		// 检查是否拥有系统管理员角色
		systemAdminRoleID := "system_admin"
		if systemAdminRole, exists := m.configManager.GetRole("system_admin"); exists {
			systemAdminRoleID = systemAdminRole.ID
		}

		hasSystemAdminRole, err := m.userRoleService.HasRole(userIDStr, systemAdminRoleID)
		if err != nil {
			m.logger.Error("Failed to check system admin role",
				zap.String("request_id", requestID),
				zap.String("user_id", userIDStr),
				zap.Error(err))
			internalErr := errors.NewBusinessError(errors.ErrCodeInternal,
				m.configManager.GetErrorMessage("internal_error", "zh-CN"))
			c.JSON(internalErr.HTTPStatus(), errors.ToErrorResponse(internalErr, requestID))
			c.Abort()
			return
		}

		if !hasSystemAdminRole {
			m.logger.Warn("User attempted to access system admin resource without system admin role",
				zap.String("request_id", requestID),
				zap.String("user_id", userIDStr),
				zap.String("path", c.Request.URL.Path),
				zap.String("method", c.Request.Method))
			err := errors.NewBusinessError(errors.ErrCodeForbidden, "需要系统管理员权限")
			c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
			c.Abort()
			return
		}

		// 将系统管理员标识存储到上下文中
		c.Set("is_system_admin", true)

		m.logger.Info("System admin access granted",
			zap.String("request_id", requestID),
			zap.String("user_id", userIDStr),
			zap.String("path", c.Request.URL.Path),
			zap.String("method", c.Request.Method))

		c.Next()
	}
}

// getRequestID 获取请求ID
func getRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		return requestID.(string)
	}
	return "unknown"
}
