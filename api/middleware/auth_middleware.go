package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/user"
)

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	tokenService auth.TokenService
}

// NewAuthMiddleware 创建新的认证中间件
func NewAuthMiddleware(tokenService auth.TokenService) *AuthMiddleware {
	return &AuthMiddleware{
		tokenService: tokenService,
	}
}

// RequireAuth 要求用户认证
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 从请求头中获取认证信息
		authHeader := ctx.GetHeader("Authorization")
		if authHeader == "" {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    http.StatusUnauthorized,
				"message": "Authorization header is required",
			})
			return
		}

		// 解析认证头
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    http.StatusUnauthorized,
				"message": "Authorization header format must be Bearer {token}",
			})
			return
		}

		// 验证令牌
		token := parts[1]
		claims, err := m.tokenService.ValidateToken(token)
		if err != nil {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    http.StatusUnauthorized,
				"message": "Invalid or expired token",
				"error":   err.Error(),
			})
			return
		}

		// 将客户端ID存储在上下文中
		ctx.Set("client_id", claims.ClientID)
		ctx.Set("token_id", claims.ID)
		ctx.Set("scopes", claims.Scopes)

		// 从数据库中获取用户ID
		userRepo, exists := ctx.Get("user_repository")
		if exists {
			userRepository := userRepo.(user.UserRepository)

			// 首先尝试通过用户ID直接查找（管理员令牌）
			if user, err := userRepository.FindByID(claims.Subject); err == nil && user != nil {
				ctx.Set("userID", user.ID)
				fmt.Printf("设置用户ID (通过Subject): %s\n", user.ID)
			} else {
				// 如果通过Subject找不到，尝试通过ClientID查找（普通用户令牌）
				if user, err := userRepository.FindByClientID(claims.ClientID); err == nil && user != nil {
					ctx.Set("userID", user.ID)
					fmt.Printf("设置用户ID (通过ClientID): %s\n", user.ID)
				} else if err != nil {
					fmt.Printf("获取用户失败: %s\n", err.Error())
				} else {
					fmt.Println("未找到用户")
				}
			}
		} else {
			fmt.Println("未找到用户存储库")
		}

		ctx.Next()
	}
}
