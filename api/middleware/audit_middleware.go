package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/your-org/go-kuaidi/internal/security"
	"github.com/your-org/go-kuaidi/internal/util"
)

// 注意：ResponseRecorder已在access_log_middleware.go中定义
// 这里复用该定义以避免重复声明

// AuditMiddleware 审计中间件
func AuditMiddleware(auditService security.AuditService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否应该记录审计日志
		if !auditService.ShouldAudit(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 记录开始时间
		startTime := util.NowBeijing()

		// 生成请求ID
		requestID := uuid.New().String()
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)

		// 读取请求体（限制大小）
		var requestBody []byte
		if c.Request.Body != nil {
			// 限制读取的请求体大小
			limitedReader := io.LimitReader(c.Request.Body, 1024*1024) // 1MB限制
			requestBody, _ = io.ReadAll(limitedReader)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 创建响应记录器
		responseRecorder := &ResponseRecorder{
			ResponseWriter: c.Writer,
			body:           &bytes.Buffer{},
			status:         200, // 默认状态码
		}
		c.Writer = responseRecorder

		// 处理请求
		c.Next()

		// 记录结束时间
		duration := time.Since(startTime).Milliseconds()

		// 获取用户ID和客户端ID
		userID := c.GetString("user_id")
		clientID := getClientID(c)

		// 获取请求体
		requestBodyStr := string(requestBody)

		// 获取响应体
		responseBody := responseRecorder.body.String()

		// 创建元数据
		metadata := make(map[string]interface{})

		// 添加错误信息
		if redisError := c.GetString("redis_error"); redisError != "" {
			metadata["redis_error"] = redisError
		}

		if rateLimitError := c.GetString("rate_limit_error"); rateLimitError != "" {
			metadata["rate_limit_error"] = rateLimitError
		}

		// 添加请求头信息
		headers := make(map[string]string)
		for k, v := range c.Request.Header {
			if len(v) > 0 {
				headers[k] = v[0]
			}
		}
		metadata["headers"] = headers

		// 序列化元数据
		metadataJSON, _ := json.Marshal(metadata)

		// 创建审计日志
		log := security.AuditLog{
			ID:            requestID,
			UserID:        userID,
			ClientID:      clientID,
			Action:        c.Request.Method,
			Resource:      c.Request.URL.Path,
			IP:            getClientIP(c),
			UserAgent:     c.Request.UserAgent(),
			RequestMethod: c.Request.Method,
			RequestPath:   c.Request.URL.Path,
			RequestParams: c.Request.URL.RawQuery,
			RequestBody:   requestBodyStr,
			ResponseCode:  responseRecorder.status,
			ResponseBody:  responseBody,
			Duration:      duration,
			Status:        security.GetStatusFromCode(responseRecorder.status),
			ErrorMessage:  c.Errors.String(),
			Metadata:      metadataJSON,
			CreatedAt:     util.NowBeijing(),
		}

		// 记录审计日志
		err := auditService.LogAudit(log)
		if err != nil {
			// 记录错误，但不中断请求处理
			c.Error(err)
		}
	}
}
