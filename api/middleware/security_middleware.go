package middleware

import (
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/security"
	"github.com/your-org/go-kuaidi/internal/util"

)

// SecurityHeaders 安全头部中间件
func SecurityHeaders(config security.HeadersConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果未启用安全头，直接跳过
		if !config.Enabled {
			c.Next()
			return
		}

		// 添加安全相关的HTTP头

		// HTTP严格传输安全
		if config.HSTSMaxAgeSeconds > 0 {
			hstsValue := fmt.Sprintf("max-age=%d", config.HSTSMaxAgeSeconds)
			if config.HSTSIncludeSubdomains {
				hstsValue += "; includeSubDomains"
			}
			if config.HSTSPreload {
				hstsValue += "; preload"
			}
			c.<PERSON>er("Strict-Transport-Security", hstsValue)
		}

		// 防止点击劫持
		if config.XFrameOptions != "" {
			c.Header("X-Frame-Options", config.XFrameOptions)
		} else {
			c.Header("X-Frame-Options", "DENY")
		}

		// 防止MIME类型嗅探
		c.Header("X-Content-Type-Options", "nosniff")

		// 启用XSS过滤器
		c.Header("X-XSS-Protection", "1; mode=block")

		// 内容安全策略
		if config.ContentSecurityPolicy != "" {
			c.Header("Content-Security-Policy", config.ContentSecurityPolicy)
		}

		// 引荐来源政策
		if config.ReferrerPolicy != "" {
			c.Header("Referrer-Policy", config.ReferrerPolicy)
		}

		// 特性策略
		if config.FeaturePolicy != "" {
			c.Header("Feature-Policy", config.FeaturePolicy)
			// 新版浏览器使用Permissions-Policy
			c.Header("Permissions-Policy", config.FeaturePolicy)
		}

		c.Next()
	}
}

// ForceHTTPS 强制HTTPS中间件
func ForceHTTPS(config security.HeadersConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果未启用强制HTTPS，直接跳过
		if !config.ForceHTTPS {
			c.Next()
			return
		}

		// 检查是否是HTTPS请求
		isHTTPS := c.Request.TLS != nil

		// 检查常见的代理头
		if !isHTTPS {
			// 检查X-Forwarded-Proto头
			forwardedProto := c.Request.Header.Get("X-Forwarded-Proto")
			if forwardedProto != "" {
				isHTTPS = forwardedProto == "https"
			}

			// 检查X-Forwarded-Ssl头
			forwardedSsl := c.Request.Header.Get("X-Forwarded-Ssl")
			if forwardedSsl != "" {
				isHTTPS = forwardedSsl == "on"
			}

			// 检查Front-End-Https头（Azure使用）
			frontEndHttps := c.Request.Header.Get("Front-End-Https")
			if frontEndHttps != "" {
				isHTTPS = frontEndHttps == "on"
			}
		}

		// 如果不是HTTPS请求，重定向到HTTPS
		if !isHTTPS {
			// 跳过本地开发环境
			if !isDevelopmentHost(c.Request.Host) {
				target := "https://" + c.Request.Host + c.Request.URL.Path
				if c.Request.URL.RawQuery != "" {
					target += "?" + c.Request.URL.RawQuery
				}
				c.Redirect(301, target)
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// isDevelopmentHost 检查是否是开发环境主机
func isDevelopmentHost(host string) bool {
	// 检查常见的开发环境主机名
	devHosts := []string{
		"localhost",
		"127.0.0.1",
		"0.0.0.0",
		"::1",
		"***********", // Sealos内网IP
	}

	// 移除端口号
	hostWithoutPort := host
	if colonIndex := strings.Index(host, ":"); colonIndex != -1 {
		hostWithoutPort = host[:colonIndex]
	}

	// 检查是否匹配开发环境主机名
	for _, devHost := range devHosts {
		if hostWithoutPort == devHost {
			return true
		}
	}

	// 检查是否是内网IP (10.0.0.0/8, **********/12, ***********/16)
	if strings.HasPrefix(hostWithoutPort, "10.") ||
		strings.HasPrefix(hostWithoutPort, "172.16.") ||
		strings.HasPrefix(hostWithoutPort, "172.17.") ||
		strings.HasPrefix(hostWithoutPort, "172.18.") ||
		strings.HasPrefix(hostWithoutPort, "172.19.") ||
		strings.HasPrefix(hostWithoutPort, "172.20.") ||
		strings.HasPrefix(hostWithoutPort, "172.21.") ||
		strings.HasPrefix(hostWithoutPort, "172.22.") ||
		strings.HasPrefix(hostWithoutPort, "172.23.") ||
		strings.HasPrefix(hostWithoutPort, "172.24.") ||
		strings.HasPrefix(hostWithoutPort, "172.25.") ||
		strings.HasPrefix(hostWithoutPort, "172.26.") ||
		strings.HasPrefix(hostWithoutPort, "172.27.") ||
		strings.HasPrefix(hostWithoutPort, "172.28.") ||
		strings.HasPrefix(hostWithoutPort, "172.29.") ||
		strings.HasPrefix(hostWithoutPort, "172.30.") ||
		strings.HasPrefix(hostWithoutPort, "172.31.") ||
		strings.HasPrefix(hostWithoutPort, "192.168.") {
		return true
	}

	return false
}

// AdminLoginRateLimitMiddleware 管理员登录专用限流中间件
func AdminLoginRateLimitMiddleware() gin.HandlerFunc {
	// 更宽松的限流：每1分钟最多10次尝试（便于测试）
	attempts := make(map[string][]time.Time)

	return gin.HandlerFunc(func(c *gin.Context) {
		clientIP := c.ClientIP()
		now := util.NowBeijing()

		// 清理过期记录
		if times, exists := attempts[clientIP]; exists {
			var validTimes []time.Time
			for _, t := range times {
				if now.Sub(t) < 1*time.Minute { // 改为1分钟
					validTimes = append(validTimes, t)
				}
			}
			attempts[clientIP] = validTimes
		}

		// 检查登录尝试频率
		if len(attempts[clientIP]) >= 10 { // 改为10次
			c.JSON(429, gin.H{
				"success": false,
				"code":    429,
				"message": "登录尝试过于频繁，请1分钟后再试", // 更新提示信息
			})
			c.Abort()
			return
		}

		// 记录当前尝试
		attempts[clientIP] = append(attempts[clientIP], now)

		c.Next()
	})
}
