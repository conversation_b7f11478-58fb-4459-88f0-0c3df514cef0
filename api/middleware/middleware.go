package middleware

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// Cors 跨域中间件 - 支持环境变量配置
func Cors() gin.HandlerFunc {
	// 从环境变量获取允许的源，生产环境必须配置
	allowedOrigins := getConfiguredOrigins()

	return cors.New(cors.Config{
		AllowOrigins:     allowedOrigins,
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Content-Length", "Accept-Encoding", "X-CSRF-Token", "Authorization", "X-Request-ID", "x-request-id", "X-Requested-With", "Accept", "Cache-Control", "Pragma", "Expires", "X-Timestamp", "X-Nonce", "X-Signature", "X-Client-ID"},
		ExposeHeaders:    []string{"Content-Length", "X-Request-ID"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	})
}

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s | %d | %s | %s | %s | %s | %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.ClientIP,
			param.StatusCode,
			param.Latency,
			param.Method,
			param.Path,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	})
}

// Recovery 恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.Recovery()
}

// getConfiguredOrigins 获取配置的允许源
func getConfiguredOrigins() []string {
	// 需要导入os和strings包
	// 优先使用环境变量
	if origins := os.Getenv("CORS_ALLOWED_ORIGINS"); origins != "" {
		return strings.Split(origins, ",")
	}

	// 根据环境返回不同的默认值
	env := os.Getenv("ENVIRONMENT")
	if env == "production" {
		// 生产环境：返回生产域名
		return []string{
			"https://mywl.py258.com",
			"https://my.py258.com",
			"http://mywl.py258.com",
			"http://my.py258.com",
		}
	}

	// 开发环境：允许本地开发域名和Sealos域名
	return []string{
		"http://localhost:3006",
		"http://localhost:3007",
		"http://localhost:3008",
		"http://127.0.0.1:3006",
		"http://127.0.0.1:3007",
		"http://127.0.0.1:3008",
		"http://***********:3006",
		"https://sdbjgqtfpakl.sealosgzg.site",
		"https://lhnizjbonwll.sealosgzg.site",
	}
}
