package middleware

import (
	"bytes"
	"compress/gzip"
	"io"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/util"
)

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	RequestCount    int64         `json:"request_count"`
	TotalDuration   time.Duration `json:"total_duration"`
	AverageDuration time.Duration `json:"average_duration"`
	MinDuration     time.Duration `json:"min_duration"`
	MaxDuration     time.Duration `json:"max_duration"`
	ErrorCount      int64         `json:"error_count"`
	mu              sync.RWMutex
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	metrics map[string]*PerformanceMetrics
	mu      sync.RWMutex

	// 🚀 新增：JSON优化器
	jsonOptimizer *util.JSONOptimizer

	// 🚀 新增：响应缓冲池
	responsePool sync.Pool

	// 🚀 新增：Gzip压缩器池
	gzipPool sync.Pool
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor() *PerformanceMonitor {
	pm := &PerformanceMonitor{
		metrics:       make(map[string]*PerformanceMetrics),
		jsonOptimizer: util.GetGlobalJSONOptimizer(),
	}

	// 🚀 初始化响应缓冲池
	pm.responsePool = sync.Pool{
		New: func() interface{} {
			return bytes.NewBuffer(make([]byte, 0, 4096)) // 预分配4KB
		},
	}

	// 🚀 初始化Gzip压缩器池
	pm.gzipPool = sync.Pool{
		New: func() interface{} {
			return gzip.NewWriter(io.Discard)
		},
	}

	return pm
}

// PerformanceMiddleware 性能监控中间件
func (pm *PerformanceMonitor) PerformanceMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := util.NowBeijing()
		path := c.Request.URL.Path
		method := c.Request.Method
		key := method + " " + path

		// 处理请求
		c.Next()

		// 计算响应时间
		duration := time.Since(start)
		statusCode := c.Writer.Status()

		// 更新指标
		pm.updateMetrics(key, duration, statusCode >= 400)

		// 添加性能头
		c.Header("X-Response-Time", duration.String())
		c.Header("X-Request-ID", c.GetString("request_id"))
	}
}

// updateMetrics 更新性能指标
func (pm *PerformanceMonitor) updateMetrics(key string, duration time.Duration, isError bool) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	metrics, exists := pm.metrics[key]
	if !exists {
		metrics = &PerformanceMetrics{
			MinDuration: duration,
			MaxDuration: duration,
		}
		pm.metrics[key] = metrics
	}

	metrics.mu.Lock()
	defer metrics.mu.Unlock()

	metrics.RequestCount++
	metrics.TotalDuration += duration

	if duration < metrics.MinDuration {
		metrics.MinDuration = duration
	}
	if duration > metrics.MaxDuration {
		metrics.MaxDuration = duration
	}

	if isError {
		metrics.ErrorCount++
	}

	// 计算平均响应时间
	metrics.AverageDuration = metrics.TotalDuration / time.Duration(metrics.RequestCount)
}

// GetMetrics 获取性能指标
func (pm *PerformanceMonitor) GetMetrics() map[string]*PerformanceMetrics {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	result := make(map[string]*PerformanceMetrics)
	for key, metrics := range pm.metrics {
		metrics.mu.RLock()
		result[key] = &PerformanceMetrics{
			RequestCount:    metrics.RequestCount,
			TotalDuration:   metrics.TotalDuration,
			AverageDuration: metrics.AverageDuration,
			MinDuration:     metrics.MinDuration,
			MaxDuration:     metrics.MaxDuration,
			ErrorCount:      metrics.ErrorCount,
		}
		metrics.mu.RUnlock()
	}

	return result
}

// GetSummary 获取性能摘要
func (pm *PerformanceMonitor) GetSummary() PerformanceSummary {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	var totalRequests int64
	var totalErrors int64
	var totalDuration time.Duration
	var maxDuration time.Duration
	var minDuration time.Duration = time.Hour // 初始化为一个大值

	for _, metrics := range pm.metrics {
		metrics.mu.RLock()
		totalRequests += metrics.RequestCount
		totalErrors += metrics.ErrorCount
		totalDuration += metrics.TotalDuration

		if metrics.MaxDuration > maxDuration {
			maxDuration = metrics.MaxDuration
		}
		if metrics.MinDuration < minDuration {
			minDuration = metrics.MinDuration
		}
		metrics.mu.RUnlock()
	}

	var avgDuration time.Duration
	if totalRequests > 0 {
		avgDuration = totalDuration / time.Duration(totalRequests)
	}

	var errorRate float64
	if totalRequests > 0 {
		errorRate = float64(totalErrors) / float64(totalRequests) * 100
	}

	return PerformanceSummary{
		TotalRequests:   totalRequests,
		TotalErrors:     totalErrors,
		ErrorRate:       errorRate,
		AverageDuration: avgDuration,
		MinDuration:     minDuration,
		MaxDuration:     maxDuration,
	}
}

// PerformanceSummary 性能摘要
type PerformanceSummary struct {
	TotalRequests   int64         `json:"total_requests"`
	TotalErrors     int64         `json:"total_errors"`
	ErrorRate       float64       `json:"error_rate"`
	AverageDuration time.Duration `json:"average_duration"`
	MinDuration     time.Duration `json:"min_duration"`
	MaxDuration     time.Duration `json:"max_duration"`
}

// Reset 重置性能指标
func (pm *PerformanceMonitor) Reset() {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.metrics = make(map[string]*PerformanceMetrics)
}

// SlowRequestMiddleware 慢请求监控中间件
func SlowRequestMiddleware(threshold time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := util.NowBeijing()

		c.Next()

		duration := time.Since(start)
		if duration > threshold {
			// 记录慢请求
			c.Header("X-Slow-Request", "true")
			c.Header("X-Slow-Duration", duration.String())

			// 这里可以添加日志记录或监控告警
			// log.Printf("Slow request detected: %s %s took %v",
			//     c.Request.Method, c.Request.URL.Path, duration)
		}
	}
}

// CompressionMiddleware 响应压缩中间件
func CompressionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查客户端是否支持gzip压缩
		if !shouldCompress(c) {
			c.Next()
			return
		}

		// 设置压缩头
		c.Header("Content-Encoding", "gzip")
		c.Header("Vary", "Accept-Encoding")

		c.Next()
	}
}

// shouldCompress 检查是否应该压缩响应
func shouldCompress(c *gin.Context) bool {
	// 检查Accept-Encoding头
	acceptEncoding := c.GetHeader("Accept-Encoding")
	if acceptEncoding == "" {
		return false
	}

	// 简单检查是否包含gzip
	return contains(acceptEncoding, "gzip")
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			s[:len(substr)] == substr ||
			s[len(s)-len(substr):] == substr ||
			indexOf(s, substr) >= 0)
}

// indexOf 查找子字符串的位置
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// CacheControlMiddleware 缓存控制中间件
func CacheControlMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 根据路径设置不同的缓存策略
		path := c.Request.URL.Path

		if isStaticResource(path) {
			// 静态资源设置长期缓存
			c.Header("Cache-Control", "public, max-age=31536000") // 1年
		} else if isOrderRelatedAPI(path) {
			// 订单相关API不缓存，确保实时性
			c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		} else if isUserManagementAPI(path) {
			// 用户管理API不缓存，确保实时性
			c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		} else if isAPIEndpoint(path) {
			// 其他API端点设置短期缓存
			c.Header("Cache-Control", "public, max-age=300") // 5分钟
		} else {
			// 默认不缓存
			c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
		}

		c.Next()
	}
}

// isStaticResource 检查是否为静态资源
func isStaticResource(path string) bool {
	staticExtensions := []string{".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg"}
	for _, ext := range staticExtensions {
		if len(path) >= len(ext) && path[len(path)-len(ext):] == ext {
			return true
		}
	}
	return false
}

// isOrderRelatedAPI 检查是否为订单相关API
func isOrderRelatedAPI(path string) bool {
	orderPaths := []string{
		"/api/v1/express/orders", // 订单列表
		"/api/v1/express/order",  // 订单操作
		"/api/v1/express/track",  // 物流轨迹
		"/api/v1/billing",        // 计费相关
		"/api/v1/balance",        // 余额相关
	}

	for _, orderPath := range orderPaths {
		if len(path) >= len(orderPath) && path[:len(orderPath)] == orderPath {
			return true
		}
	}
	return false
}

// isUserManagementAPI 检查是否为用户管理API
func isUserManagementAPI(path string) bool {
	userManagementPaths := []string{
		"/api/v1/admin/users",
		"/api/v1/admin/auth",
		"/api/v1/admin/roles",
		"/api/v1/admin/balance", // 管理员余额管理API
		"/api/v1/admin/orders",  // 管理员订单管理API
	}

	for _, userPath := range userManagementPaths {
		if len(path) >= len(userPath) && path[:len(userPath)] == userPath {
			return true
		}
	}
	return false
}

// isAPIEndpoint 检查是否为API端点
func isAPIEndpoint(path string) bool {
	return len(path) >= 4 && path[:4] == "/api"
}

// 全局性能监控器实例
var GlobalPerformanceMonitor = NewPerformanceMonitor()
