package middleware

import (
	"fmt"
	"net/http"
	"os"
	"runtime/debug"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// ErrorResponse 统一错误响应格式
type ErrorResponse struct {
	Success   bool   `json:"success"`
	Code      int    `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"request_id,omitempty"`
	Details   string `json:"details,omitempty"` // 仅在开发环境显示
}

// ErrorHandler 错误处理中间件
func ErrorHandler(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// 获取请求ID
				requestID, _ := c.Get("request_id")
				requestIDStr, _ := requestID.(string)

				// 记录panic详情
				logger.Error("Panic recovered",
					zap.String("request_id", requestIDStr),
					zap.String("method", c.Request.Method),
					zap.String("path", c.Request.URL.Path),
					zap.String("client_ip", c.ClientIP()),
					zap.Any("error", err),
					zap.String("stack", string(debug.Stack())))

				// 返回安全的错误响应
				response := ErrorResponse{
					Success:   false,
					Code:      http.StatusInternalServerError,
					Message:   "服务器内部错误",
					RequestID: requestIDStr,
				}

				// 开发环境显示详细错误信息
				if isDevelopmentMode() {
					response.Details = fmt.Sprintf("%v", err)
				}

				c.JSON(http.StatusInternalServerError, response)
				c.Abort()
			}
		}()

		c.Next()

		// 处理业务错误
		if len(c.Errors) > 0 {
			handleBusinessErrors(c, logger)
		}
	}
}

// handleBusinessErrors 处理业务错误
func handleBusinessErrors(c *gin.Context, logger *zap.Logger) {
	requestID, _ := c.Get("request_id")
	requestIDStr, _ := requestID.(string)

	// 获取最后一个错误
	lastError := c.Errors.Last()

	// 记录错误日志
	logger.Error("Business error",
		zap.String("request_id", requestIDStr),
		zap.String("method", c.Request.Method),
		zap.String("path", c.Request.URL.Path),
		zap.String("client_ip", c.ClientIP()),
		zap.Error(lastError.Err))

	// 根据错误类型返回适当的HTTP状态码和消息
	statusCode, message := categorizeError(lastError.Err)

	response := ErrorResponse{
		Success:   false,
		Code:      statusCode,
		Message:   message,
		RequestID: requestIDStr,
	}

	// 开发环境显示详细错误信息
	if isDevelopmentMode() {
		response.Details = lastError.Error()
	}

	c.JSON(statusCode, response)
}

// categorizeError 根据错误内容分类错误
func categorizeError(err error) (int, string) {
	// 处理特定错误类型
	switch err.(type) {
	case *model.ProviderNotSupportedError:
		return http.StatusBadRequest, "该快递公司不支持当前线路，请选择其他快递公司"
	case *model.CapacityError:
		return http.StatusBadRequest, "当前线路暂时不可用，请选择其他快递公司或稍后重试"
	case *model.NetworkError:
		return http.StatusServiceUnavailable, "网络连接异常，请稍后重试"
	}

	errMsg := strings.ToLower(err.Error())

	// 认证相关错误
	if strings.Contains(errMsg, "unauthorized") ||
		strings.Contains(errMsg, "invalid token") ||
		strings.Contains(errMsg, "token expired") {
		return http.StatusUnauthorized, "认证失败，请重新登录"
	}

	// 权限相关错误
	if strings.Contains(errMsg, "forbidden") ||
		strings.Contains(errMsg, "permission denied") ||
		strings.Contains(errMsg, "access denied") {
		return http.StatusForbidden, "权限不足，无法访问该资源"
	}

	// 参数验证错误
	if strings.Contains(errMsg, "validation") ||
		strings.Contains(errMsg, "invalid parameter") ||
		strings.Contains(errMsg, "bad request") {
		return http.StatusBadRequest, "请求参数错误"
	}

	// 资源不存在错误
	if strings.Contains(errMsg, "not found") ||
		strings.Contains(errMsg, "no rows") {
		return http.StatusNotFound, "请求的资源不存在"
	}

	// 冲突错误
	if strings.Contains(errMsg, "conflict") ||
		strings.Contains(errMsg, "duplicate") ||
		strings.Contains(errMsg, "already exists") {
		return http.StatusConflict, "资源冲突，请检查后重试"
	}

	// 限流错误
	if strings.Contains(errMsg, "rate limit") ||
		strings.Contains(errMsg, "too many requests") {
		return http.StatusTooManyRequests, "请求过于频繁，请稍后重试"
	}

	// 外部服务错误
	if strings.Contains(errMsg, "service unavailable") ||
		strings.Contains(errMsg, "timeout") ||
		strings.Contains(errMsg, "connection") {
		return http.StatusServiceUnavailable, "服务暂时不可用，请稍后重试"
	}

	// 默认服务器错误
	return http.StatusInternalServerError, "服务器内部错误"
}

// isDevelopmentMode 检查是否为开发模式
func isDevelopmentMode() bool {
	env := os.Getenv("ENVIRONMENT")
	return env == "development" || env == "dev" || env == ""
}

// ValidationErrorHandler 验证错误处理器
func ValidationErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有验证错误
		if validationErrors, exists := c.Get("validation_errors"); exists {
			requestID, _ := c.Get("request_id")
			requestIDStr, _ := requestID.(string)

			response := ErrorResponse{
				Success:   false,
				Code:      http.StatusBadRequest,
				Message:   "请求参数验证失败",
				RequestID: requestIDStr,
			}

			// 开发环境显示详细验证错误
			if isDevelopmentMode() {
				if errors, ok := validationErrors.([]string); ok {
					response.Details = strings.Join(errors, "; ")
				}
			}

			c.JSON(http.StatusBadRequest, response)
			c.Abort()
		}
	}
}

// SecurityErrorHandler 安全错误处理器
func SecurityErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有安全相关错误
		if securityError, exists := c.Get("security_error"); exists {
			requestID, _ := c.Get("request_id")
			requestIDStr, _ := requestID.(string)

			// 安全错误不暴露具体信息
			response := ErrorResponse{
				Success:   false,
				Code:      http.StatusForbidden,
				Message:   "安全验证失败",
				RequestID: requestIDStr,
			}

			// 记录安全事件但不在响应中暴露
			if logger, exists := c.Get("logger"); exists {
				if zapLogger, ok := logger.(*zap.Logger); ok {
					zapLogger.Warn("Security error detected",
						zap.String("request_id", requestIDStr),
						zap.String("client_ip", c.ClientIP()),
						zap.String("user_agent", c.Request.UserAgent()),
						zap.Any("security_error", securityError))
				}
			}

			c.JSON(http.StatusForbidden, response)
			c.Abort()
		}
	}
}

// NotFoundHandler 404错误处理器
func NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID, _ := c.Get("request_id")
		requestIDStr, _ := requestID.(string)

		response := ErrorResponse{
			Success:   false,
			Code:      http.StatusNotFound,
			Message:   "请求的接口不存在",
			RequestID: requestIDStr,
		}

		c.JSON(http.StatusNotFound, response)
	}
}

// MethodNotAllowedHandler 405错误处理器
func MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID, _ := c.Get("request_id")
		requestIDStr, _ := requestID.(string)

		response := ErrorResponse{
			Success:   false,
			Code:      http.StatusMethodNotAllowed,
			Message:   "请求方法不被允许",
			RequestID: requestIDStr,
		}

		c.JSON(http.StatusMethodNotAllowed, response)
	}
}
