package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/service"
)

// ServiceInjectionMiddleware 服务注入中间件
type ServiceInjectionMiddleware struct {
	workOrderCallbackForwarder *service.WorkOrderCallbackForwarder
}

// NewServiceInjectionMiddleware 创建服务注入中间件
func NewServiceInjectionMiddleware(
	workOrderCallbackForwarder *service.WorkOrderCallbackForwarder,
) *ServiceInjectionMiddleware {
	return &ServiceInjectionMiddleware{
		workOrderCallbackForwarder: workOrderCallbackForwarder,
	}
}

// InjectServices 注入服务到请求上下文
func (m *ServiceInjectionMiddleware) InjectServices() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 注入工单回调转发器
		if m.workOrderCallbackForwarder != nil {
			c.Set("workorder_callback_forwarder", m.workOrderCallbackForwarder)
		}

		c.Next()
	}
}
