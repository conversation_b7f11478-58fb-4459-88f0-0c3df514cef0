package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/security"
)

// CLIENT_SECRET_MAP 存储客户端ID和密钥的映射
// 在生产环境中，这应该从数据库中加载
var CLIENT_SECRET_MAP = make(map[string]string)
var clientSecretMutex sync.RWMutex

// RegisterClientSecret 注册客户端密钥
func RegisterClientSecret(clientID, clientSecret string) {
	clientSecretMutex.Lock()
	defer clientSecretMutex.Unlock()
	CLIENT_SECRET_MAP[clientID] = clientSecret
}

// SignatureMiddleware 签名验证中间件 - 🔒 企业级强制签名验证
func SignatureMiddleware(signatureService security.SignatureService, clientService auth.ClientService, redisClient *redis.Client, config security.SignatureConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 🔒 强制启用签名验证 - 移除所有可能绕过的逻辑
		// 注释掉原有的禁用逻辑，确保签名验证始终执行

		// 🔒 只允许特定路径跳过签名验证（如健康检查、OAuth token获取）
		if signatureService.ShouldSkipSignature(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 🔒 记录签名验证开始
		// 注意：不记录敏感信息，只记录验证过程

		// 🚀 统一网关特殊处理：从请求体中提取签名参数
		var timestamp, nonce, signature, clientID string

		if c.Request.URL.Path == "/api/gateway/execute" {
			// 统一网关：从JSON请求体中提取签名参数
			timestamp, nonce, signature, clientID = extractSignatureFromGatewayRequest(c)
		} else {
			// 传统API：从HTTP头中提取签名参数
			timestamp = c.GetHeader("X-Timestamp")
			nonce = c.GetHeader("X-Nonce")
			signature = c.GetHeader("X-Signature")
			clientID = c.GetHeader("X-Client-ID")
		}

		// 🚀 统一网关特殊处理：如果所有签名参数都为空，说明是Web客户端，跳过签名验证
		if timestamp == "" && nonce == "" && signature == "" && clientID == "" {
			c.Next()
			return
		}

		// 验证必要参数并进行基本格式检查
		if timestamp == "" || signature == "" || clientID == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    http.StatusUnauthorized,
				"message": "Missing signature parameters",
				"error":   "Required signature headers (X-Timestamp, X-Nonce, X-Signature, X-Client-ID) are missing",
			})
			c.Abort()
			return
		}

		// 🚀 统一网关：nonce可以为空（使用timestamp作为防重放机制）
		if nonce == "" {
			nonce = timestamp // 使用timestamp作为nonce
		}

		// 验证参数长度，防止过长参数攻击
		if len(timestamp) > 20 || len(nonce) > 64 || len(signature) > 512 || len(clientID) > 64 {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"code":    http.StatusBadRequest,
				"message": "Invalid parameter length",
				"error":   "Parameter length exceeds maximum allowed",
			})
			c.Abort()
			return
		}

		// 验证参数格式，防止注入攻击
		if !isValidClientID(clientID) || !isValidNonce(nonce) {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"code":    http.StatusBadRequest,
				"message": "Invalid parameter format",
				"error":   "Parameters contain invalid characters",
			})
			c.Abort()
			return
		}

		// 验证时间戳
		if !signatureService.IsTimestampValid(timestamp) {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"code":    http.StatusBadRequest,
				"message": "Invalid timestamp",
				"error":   "Timestamp is invalid or expired",
			})
			c.Abort()
			return
		}

		// 创建带超时的上下文（用于Redis操作）
		timeoutCtx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()

		// 构建nonce键
		nonceKey := "nonce:" + clientID + ":" + nonce

		// 🚀 检查是否禁用nonce验证
		if !config.DisableNonceValidation {
			// 验证nonce是否已使用（防止重放攻击）
			exists, err := redisClient.Exists(timeoutCtx, nonceKey).Result()
			if err != nil {
				// Redis错误时应该拒绝请求，确保安全性
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"success": false,
					"code":    http.StatusServiceUnavailable,
					"message": "Service temporarily unavailable",
					"error":   "Unable to verify request uniqueness",
				})
				c.Abort()
				return
			} else if exists > 0 {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"code":    http.StatusBadRequest,
					"message": "Nonce already used",
					"error":   "Duplicate request detected",
				})
				c.Abort()
				return
			}
		}

		// 在开发环境中，我们可以跳过客户端验证
		// 在生产环境中，我们应该验证客户端
		// 这里我们简单地检查客户端ID是否存在
		if clientID == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    http.StatusUnauthorized,
				"message": "Invalid client",
				"error":   "Client ID is required",
			})
			c.Abort()
			return
		}

		// 检查请求体大小限制（在读取之前）
		if c.Request.ContentLength > config.MaxRequestBodySize {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"success": false,
				"code":    http.StatusRequestEntityTooLarge,
				"message": "Request body too large",
				"error":   "Maximum allowed size is " + strconv.FormatInt(config.MaxRequestBodySize, 10) + " bytes",
			})
			c.Abort()
			return
		}

		// 读取请求体（限制大小）
		var rawBody []byte
		var err error
		if c.Request.Body != nil {
			// 限制读取的请求体大小
			limitedReader := io.LimitReader(c.Request.Body, config.MaxRequestBodySize)
			rawBody, err = io.ReadAll(limitedReader)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"code":    http.StatusBadRequest,
					"message": "Failed to read request body",
					"error":   err.Error(),
				})
				c.Abort()
				return
			}

			// 重置请求体以供后续处理器使用
			c.Request.Body = io.NopCloser(bytes.NewBuffer(rawBody))
		}

		// 构建参数映射
		params := make(map[string]string)
		params["timestamp"] = timestamp
		params["nonce"] = nonce
		params["client_id"] = clientID
		params["path"] = c.Request.URL.Path

		// 添加查询参数（处理多值参数）
		for key, values := range c.Request.URL.Query() {
			if len(values) > 0 {
				// 对所有值进行排序以确保一致性
				sort.Strings(values)
				// 将多个值用逗号连接
				params["query_"+key] = strings.Join(values, ",")
			}
		}

		// 从数据库获取客户端密钥
		client, err := clientService.FindByID(clientID)
		if err != nil || client == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    http.StatusUnauthorized,
				"message": "Invalid client",
				"error":   "Client not found",
			})
			c.Abort()
			return
		}

		clientSecret := client.Secret
		if clientSecret == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    http.StatusUnauthorized,
				"message": "Invalid client",
				"error":   "Client secret not configured",
			})
			c.Abort()
			return
		}

		// 如果是加密形式，尝试解密
		if dec, decErr := security.DecryptSecret(clientSecret); decErr == nil {
			clientSecret = dec
		}

		// 🔧 修复统一网关签名验证：对于统一网关请求，需要使用不包含sign字段的请求体
		var bodyForSignature []byte
		if c.Request.URL.Path == "/api/gateway/execute" {
			// 统一网关：从原始JSON中移除sign字段
			bodyForSignature = removeSignFieldFromJSON(rawBody)
		} else {
			// 传统API：直接使用原始请求体
			bodyForSignature = rawBody
		}

		// 🔍 添加调试日志 - 签名验证详细信息
		fmt.Printf("🔐 签名验证调试信息:\n")
		fmt.Printf("   客户端ID: %s\n", clientID)
		fmt.Printf("   时间戳: %s\n", timestamp)
		fmt.Printf("   Nonce: %s\n", nonce)
		fmt.Printf("   接收到的签名: %s\n", signature)
		fmt.Printf("   客户端密钥: %s\n", clientSecret)
		fmt.Printf("   请求路径: %s\n", c.Request.URL.Path)
		fmt.Printf("   参数映射: %+v\n", params)
		fmt.Printf("   用于签名的请求体长度: %d\n", len(bodyForSignature))
		if len(bodyForSignature) < 500 {
			fmt.Printf("   用于签名的请求体内容: %s\n", string(bodyForSignature))
		} else {
			fmt.Printf("   用于签名的请求体内容(前500字符): %s...\n", string(bodyForSignature[:500]))
		}

		// 验证签名
		err = signatureService.VerifySignature(params, bodyForSignature, signature, clientSecret)
		if err != nil {
			var statusCode int
			var errorMessage string

			if err == security.ErrRequestBodyTooLarge {
				statusCode = http.StatusRequestEntityTooLarge
				errorMessage = "Request body too large"
			} else {
				statusCode = http.StatusUnauthorized
				errorMessage = "Signature verification failed"
			}

			fmt.Printf("❌ 签名验证失败: %s\n", err.Error())

			c.JSON(statusCode, gin.H{
				"success": false,
				"code":    statusCode,
				"message": errorMessage,
				"error":   err.Error(),
			})
			c.Abort()
			return
		}

		fmt.Printf("✅ 签名验证成功\n")

		// 存储nonce（仅在启用nonce验证时）
		if !config.DisableNonceValidation {
			nonceValidityDuration := time.Duration(config.NonceValiditySeconds) * time.Second
			redisClient.Set(timeoutCtx, nonceKey, "1", nonceValidityDuration)
		}

		// 将客户端ID存储在上下文中
		c.Set("client_id_from_signature", clientID)

		c.Next()
	}
}

// isValidClientID 验证客户端ID格式
func isValidClientID(clientID string) bool {
	// 客户端ID应该只包含字母、数字、连字符和下划线
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, clientID)
	return matched
}

// isValidNonce 验证nonce格式
func isValidNonce(nonce string) bool {
	// nonce应该只包含字母、数字
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9]+$`, nonce)
	return matched
}

// UnifiedGatewayRequest 统一网关请求结构（用于提取签名参数）
type UnifiedGatewayRequest struct {
	ClientType     string      `json:"clientType"`
	Username       string      `json:"username"`
	Timestamp      string      `json:"timestamp"`
	Sign           string      `json:"sign"`
	APIMethod      string      `json:"apiMethod"`
	BusinessParams interface{} `json:"businessParams"`
}

// extractSignatureFromGatewayRequest 从统一网关请求中提取签名参数
func extractSignatureFromGatewayRequest(c *gin.Context) (timestamp, nonce, signature, clientID string) {
	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return "", "", "", ""
	}

	// 恢复请求体，以便后续处理
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	// 解析JSON
	var req UnifiedGatewayRequest
	if err := json.Unmarshal(body, &req); err != nil {
		return "", "", "", ""
	}

	// 检查是否为API客户端
	if strings.ToLower(req.ClientType) != "api" && req.ClientType != "" {
		// 非API客户端，返回空值（跳过签名验证）
		return "", "", "", ""
	}

	// 返回签名参数
	return req.Timestamp, "", req.Sign, req.Username // nonce暂时为空，因为统一网关请求中没有nonce
}

// removeSignFieldFromJSON 从原始JSON中移除sign字段，保持其他字段的原始顺序和格式
func removeSignFieldFromJSON(originalBody []byte) []byte {
	// 🔧 使用字符串操作移除sign字段，保持原始JSON的字段顺序
	bodyStr := string(originalBody)

	// 通用正则：匹配任意空白后紧跟逗号或位于开头的 sign 字段
	// 1) 位于中间：,<可选空白>"sign"<可选空白>:<可选空白>"..."
	// 2) 位于开头："sign"<可选空白>:<可选空白>"...",<可选空白>

	reMiddle := regexp.MustCompile(`,\s*"sign"\s*:\s*"[^"]*"`)
	reStart := regexp.MustCompile(`^\s*"sign"\s*:\s*"[^"]*"\s*,?\s*`)

	// 先移除位于开头的情况
	result := reStart.ReplaceAllString(bodyStr, "")
	// 再移除位于中间的情况
	result = reMiddle.ReplaceAllString(result, "")

	return []byte(result)
}
