package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// WeightTierQueryLog 重量档位缓存查询日志表 (避免与现有price_query_logs冲突)
type WeightTierQueryLog struct {
	ID             uuid.UUID       `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	FromProvince   string          `gorm:"column:from_province;type:varchar(20);not null" json:"from_province"`
	ToProvince     string          `gorm:"column:to_province;type:varchar(20);not null" json:"to_province"`
	Provider       string          `gorm:"column:provider;type:varchar(50);not null" json:"provider"`
	ExpressCode    string          `gorm:"column:express_code;type:varchar(20);not null" json:"express_code"`
	WeightKg       int             `gorm:"column:weight_kg;not null" json:"weight_kg"`
	Price          decimal.Decimal `gorm:"column:price;type:decimal(10,2)" json:"price"`
	Source         string          `gorm:"column:source;type:varchar(20);not null" json:"source"` // "cache" or "realtime"
	ResponseTimeMs int             `gorm:"column:response_time_ms;not null" json:"response_time_ms"`
	Success        bool            `gorm:"column:success;not null" json:"success"`
	ErrorMessage   string          `gorm:"column:error_message;type:text" json:"error_message"`
	CreatedAt      time.Time       `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
}

func (WeightTierQueryLog) TableName() string {
	return "weight_tier_query_logs"
}

// WeightTierQueryLogRequest 重量档位缓存查询日志记录请求
type WeightTierQueryLogRequest struct {
	FromProvince   string
	ToProvince     string
	Provider       string
	ExpressCode    string
	WeightKg       int
	Price          decimal.Decimal
	Source         string
	ResponseTimeMs int
	Success        bool
	ErrorMessage   string
}
