package model

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
)

// SystemConfig 系统配置模型
type SystemConfig struct {
	ID             string    `json:"id" db:"id"`
	ConfigGroup    string    `json:"config_group" db:"config_group"`
	ConfigKey      string    `json:"config_key" db:"config_key"`
	ConfigValue    string    `json:"config_value" db:"config_value"`
	ConfigType     string    `json:"config_type" db:"config_type"`
	Description    string    `json:"description" db:"description"`
	IsEncrypted    bool      `json:"is_encrypted" db:"is_encrypted"`
	IsReadonly     bool      `json:"is_readonly" db:"is_readonly"`
	IsSystem       bool      `json:"is_system" db:"is_system"`
	ValidationRule *string   `json:"validation_rule" db:"validation_rule"`
	DefaultValue   *string   `json:"default_value" db:"default_value"`
	DisplayOrder   int       `json:"display_order" db:"display_order"`
	CreatedAt      time.Time `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time `json:"updated_at" db:"updated_at"`
	CreatedBy      *string   `json:"created_by" db:"created_by"`
	UpdatedBy      *string   `json:"updated_by" db:"updated_by"`
}

// ConfigChangeLog 配置变更日志模型
type ConfigChangeLog struct {
	ID           string    `json:"id" db:"id"`
	ConfigID     string    `json:"config_id" db:"config_id"`
	ConfigGroup  string    `json:"config_group" db:"config_group"`
	ConfigKey    string    `json:"config_key" db:"config_key"`
	OldValue     string    `json:"old_value" db:"old_value"`
	NewValue     string    `json:"new_value" db:"new_value"`
	ChangeType   string    `json:"change_type" db:"change_type"`
	ChangedBy    string    `json:"changed_by" db:"changed_by"`
	ChangeReason string    `json:"change_reason" db:"change_reason"`
	IPAddress    string    `json:"ip_address" db:"ip_address"`
	UserAgent    string    `json:"user_agent" db:"user_agent"`
	RequestID    string    `json:"request_id" db:"request_id"`
	SessionID    string    `json:"session_id" db:"session_id"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
}

// ConfigTemplate 配置模板模型
type ConfigTemplate struct {
	ID                  string          `json:"id" db:"id"`
	TemplateName        string          `json:"template_name" db:"template_name"`
	TemplateDescription string          `json:"template_description" db:"template_description"`
	TemplateData        json.RawMessage `json:"template_data" db:"template_data"`
	IsSystem            bool            `json:"is_system" db:"is_system"`
	CreatedAt           time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time       `json:"updated_at" db:"updated_at"`
	CreatedBy           string          `json:"created_by" db:"created_by"`
	UpdatedBy           string          `json:"updated_by" db:"updated_by"`
}

// ConfigBackup 配置备份模型
type ConfigBackup struct {
	ID                string          `json:"id" db:"id"`
	BackupName        string          `json:"backup_name" db:"backup_name"`
	BackupDescription string          `json:"backup_description" db:"backup_description"`
	BackupData        json.RawMessage `json:"backup_data" db:"backup_data"`
	BackupType        string          `json:"backup_type" db:"backup_type"`
	CreatedBy         string          `json:"created_by" db:"created_by"`
	CreatedAt         time.Time       `json:"created_at" db:"created_at"`
}

// 配置类型常量
const (
	ConfigTypeString  = "string"
	ConfigTypeInteger = "integer"
	ConfigTypeFloat   = "float"
	ConfigTypeBoolean = "boolean"
	ConfigTypeJSON    = "json"
	ConfigTypeArray   = "array"
)

// 变更类型常量
const (
	ChangeTypeCreate  = "create"
	ChangeTypeUpdate  = "update"
	ChangeTypeDelete  = "delete"
	ChangeTypeRestore = "restore"
)

// 备份类型常量
const (
	BackupTypeManual    = "manual"
	BackupTypeAuto      = "auto"
	BackupTypeScheduled = "scheduled"
)

// GetTypedValue 获取类型化的配置值
func (c *SystemConfig) GetTypedValue() (interface{}, error) {
	switch c.ConfigType {
	case ConfigTypeString:
		return c.ConfigValue, nil
	case ConfigTypeInteger:
		return strconv.Atoi(c.ConfigValue)
	case ConfigTypeFloat:
		return strconv.ParseFloat(c.ConfigValue, 64)
	case ConfigTypeBoolean:
		return strconv.ParseBool(c.ConfigValue)
	case ConfigTypeJSON:
		var result interface{}
		err := json.Unmarshal([]byte(c.ConfigValue), &result)
		return result, err
	case ConfigTypeArray:
		var result []interface{}
		err := json.Unmarshal([]byte(c.ConfigValue), &result)
		return result, err
	default:
		return c.ConfigValue, nil
	}
}

// SetTypedValue 设置类型化的配置值
func (c *SystemConfig) SetTypedValue(value interface{}) error {
	switch c.ConfigType {
	case ConfigTypeString:
		if str, ok := value.(string); ok {
			c.ConfigValue = str
		} else {
			c.ConfigValue = fmt.Sprintf("%v", value)
		}
	case ConfigTypeInteger:
		if i, ok := value.(int); ok {
			c.ConfigValue = strconv.Itoa(i)
		} else if i64, ok := value.(int64); ok {
			c.ConfigValue = strconv.FormatInt(i64, 10)
		} else if str, ok := value.(string); ok {
			if _, err := strconv.Atoi(str); err != nil {
				return fmt.Errorf("invalid integer value: %s", str)
			}
			c.ConfigValue = str
		} else {
			return fmt.Errorf("invalid integer value type: %T", value)
		}
	case ConfigTypeFloat:
		if f, ok := value.(float64); ok {
			c.ConfigValue = strconv.FormatFloat(f, 'f', -1, 64)
		} else if d, ok := value.(decimal.Decimal); ok {
			c.ConfigValue = d.String()
		} else if str, ok := value.(string); ok {
			if _, err := strconv.ParseFloat(str, 64); err != nil {
				return fmt.Errorf("invalid float value: %s", str)
			}
			c.ConfigValue = str
		} else {
			return fmt.Errorf("invalid float value type: %T", value)
		}
	case ConfigTypeBoolean:
		if b, ok := value.(bool); ok {
			c.ConfigValue = strconv.FormatBool(b)
		} else if str, ok := value.(string); ok {
			if _, err := strconv.ParseBool(str); err != nil {
				return fmt.Errorf("invalid boolean value: %s", str)
			}
			c.ConfigValue = str
		} else {
			return fmt.Errorf("invalid boolean value type: %T", value)
		}
	case ConfigTypeJSON, ConfigTypeArray:
		bytes, err := json.Marshal(value)
		if err != nil {
			return fmt.Errorf("failed to marshal JSON value: %w", err)
		}
		c.ConfigValue = string(bytes)
	default:
		c.ConfigValue = fmt.Sprintf("%v", value)
	}
	return nil
}

// ValidateValue 验证配置值
func (c *SystemConfig) ValidateValue() error {
	if c.ValidationRule == nil || *c.ValidationRule == "" {
		return nil
	}

	// 解析验证规则 - 使用分号分隔多个规则
	rules := strings.Split(*c.ValidationRule, ";")
	for _, rule := range rules {
		rule = strings.TrimSpace(rule)
		if err := c.validateSingleRule(rule); err != nil {
			return err
		}
	}
	return nil
}

// validateSingleRule 验证单个规则
func (c *SystemConfig) validateSingleRule(rule string) error {
	if rule == "" {
		return nil
	}

	// 长度验证: length:min,max
	if strings.HasPrefix(rule, "length:") {
		return c.validateLength(rule[7:])
	}

	// 范围验证: range:min,max
	if strings.HasPrefix(rule, "range:") {
		return c.validateRange(rule[6:])
	}

	// 枚举验证: enum:value1,value2,value3
	if strings.HasPrefix(rule, "enum:") {
		return c.validateEnum(rule[5:])
	}

	// 正则验证: regex:pattern
	if strings.HasPrefix(rule, "regex:") {
		return c.validateRegex(rule[6:])
	}

	return nil
}

// validateLength 验证字符串长度
func (c *SystemConfig) validateLength(rule string) error {
	parts := strings.Split(rule, ",")
	if len(parts) != 2 {
		return fmt.Errorf("invalid length rule format: %s", rule)
	}

	min, err := strconv.Atoi(strings.TrimSpace(parts[0]))
	if err != nil {
		return fmt.Errorf("invalid min length: %s", parts[0])
	}

	max, err := strconv.Atoi(strings.TrimSpace(parts[1]))
	if err != nil {
		return fmt.Errorf("invalid max length: %s", parts[1])
	}

	length := len(c.ConfigValue)
	if length < min || length > max {
		return fmt.Errorf("value length %d is not between %d and %d", length, min, max)
	}

	return nil
}

// validateRange 验证数值范围
func (c *SystemConfig) validateRange(rule string) error {
	parts := strings.Split(rule, ",")
	if len(parts) != 2 {
		return fmt.Errorf("invalid range rule format: %s", rule)
	}

	minStr := strings.TrimSpace(parts[0])
	maxStr := strings.TrimSpace(parts[1])

	if c.ConfigType == ConfigTypeInteger {
		value, err := strconv.Atoi(c.ConfigValue)
		if err != nil {
			return fmt.Errorf("invalid integer value: %s", c.ConfigValue)
		}

		min, err := strconv.Atoi(minStr)
		if err != nil {
			return fmt.Errorf("invalid min value: %s", minStr)
		}

		max, err := strconv.Atoi(maxStr)
		if err != nil {
			return fmt.Errorf("invalid max value: %s", maxStr)
		}

		if value < min || value > max {
			return fmt.Errorf("value %d is not between %d and %d", value, min, max)
		}
	} else if c.ConfigType == ConfigTypeFloat {
		value, err := strconv.ParseFloat(c.ConfigValue, 64)
		if err != nil {
			return fmt.Errorf("invalid float value: %s", c.ConfigValue)
		}

		min, err := strconv.ParseFloat(minStr, 64)
		if err != nil {
			return fmt.Errorf("invalid min value: %s", minStr)
		}

		max, err := strconv.ParseFloat(maxStr, 64)
		if err != nil {
			return fmt.Errorf("invalid max value: %s", maxStr)
		}

		if value < min || value > max {
			return fmt.Errorf("value %f is not between %f and %f", value, min, max)
		}
	}

	return nil
}

// validateEnum 验证枚举值
func (c *SystemConfig) validateEnum(rule string) error {
	validValues := strings.Split(rule, ",")
	for i, v := range validValues {
		validValues[i] = strings.TrimSpace(v)
	}

	for _, validValue := range validValues {
		if c.ConfigValue == validValue {
			return nil
		}
	}

	return fmt.Errorf("value '%s' is not in allowed values: %s", c.ConfigValue, strings.Join(validValues, ", "))
}

// validateRegex 验证正则表达式
func (c *SystemConfig) validateRegex(pattern string) error {
	// 这里可以添加正则表达式验证逻辑
	// 为了简化，暂时跳过正则验证
	return nil
}

// SystemConfigRequest 系统配置请求模型
type SystemConfigRequest struct {
	ConfigGroup    string `json:"config_group" binding:"required" validate:"required,min=1,max=50"`
	ConfigKey      string `json:"config_key" binding:"required" validate:"required,min=1,max=100"`
	ConfigValue    string `json:"config_value" binding:"required"`
	ConfigType     string `json:"config_type" binding:"required" validate:"required,oneof=string integer float boolean json array"`
	Description    string `json:"description" validate:"max=500"`
	ValidationRule string `json:"validation_rule" validate:"max=200"`
	DefaultValue   string `json:"default_value" validate:"max=500"`
	DisplayOrder   int    `json:"display_order" validate:"min=0"`
	ChangeReason   string `json:"change_reason" validate:"max=200"`
}

// SystemConfigListRequest 系统配置列表请求
type SystemConfigListRequest struct {
	Page          int    `json:"page" form:"page" binding:"omitempty,min=1"`
	PageSize      int    `json:"page_size" form:"page_size" binding:"omitempty,min=1,max=1000"`
	ConfigGroup   string `json:"config_group" form:"config_group"`
	ConfigKey     string `json:"config_key" form:"config_key"`
	ConfigType    string `json:"config_type" form:"config_type"`
	IsSystem      *bool  `json:"is_system" form:"is_system"`
	IsReadonly    *bool  `json:"is_readonly" form:"is_readonly"`
	IsEncrypted   *bool  `json:"is_encrypted" form:"is_encrypted"`
	SearchKeyword string `json:"search_keyword" form:"search_keyword"`
	OrderBy       string `json:"order_by" form:"order_by"`
}

// SystemConfigListResponse 系统配置列表响应
type SystemConfigListResponse struct {
	Items      []*SystemConfig `json:"items"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	PageSize   int             `json:"page_size"`
	TotalPages int             `json:"total_pages"`
}

// ConfigChangeLogListRequest 配置变更日志列表请求
type ConfigChangeLogListRequest struct {
	Page        int    `json:"page" form:"page" binding:"omitempty,min=1"`
	PageSize    int    `json:"page_size" form:"page_size" binding:"omitempty,min=1,max=1000"`
	ConfigGroup string `json:"config_group" form:"config_group"`
	ConfigKey   string `json:"config_key" form:"config_key"`
	ChangeType  string `json:"change_type" form:"change_type"`
	ChangedBy   string `json:"changed_by" form:"changed_by"`
	StartTime   string `json:"start_time" form:"start_time"`
	EndTime     string `json:"end_time" form:"end_time"`
	OrderBy     string `json:"order_by" form:"order_by"`
}

// ConfigChangeLogListResponse 配置变更日志列表响应
type ConfigChangeLogListResponse struct {
	Items      []*ConfigChangeLog `json:"items"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"page_size"`
	TotalPages int                `json:"total_pages"`
}
