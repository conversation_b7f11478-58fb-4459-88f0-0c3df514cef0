package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// AdminBalanceOverview 管理员余额总览
type AdminBalanceOverview struct {
	TotalUsers         int64           `json:"total_users"`
	ActiveUsers        int64           `json:"active_users"`
	InactiveUsers      int64           `json:"inactive_users"`
	TotalBalance       decimal.Decimal `json:"total_balance"`
	AvgBalance         decimal.Decimal `json:"avg_balance"`
	RecentTransactions int64           `json:"recent_transactions"`
	UpdatedAt          time.Time       `json:"updated_at"`
}

// UserBalanceListItem 用户余额列表项
type UserBalanceListItem struct {
	UserID            string          `json:"user_id"`
	Username          string          `json:"username"`
	Email             string          `json:"email"`
	Balance           decimal.Decimal `json:"balance"`
	AvailableBalance  decimal.Decimal `json:"available_balance"`
	Currency          string          `json:"currency"`
	Status            string          `json:"status"`
	LastTransactionAt *time.Time      `json:"last_transaction_at"`
	TransactionCount  int64           `json:"transaction_count"`
	CreatedAt         time.Time       `json:"created_at"`
	UpdatedAt         time.Time       `json:"updated_at"`
}

// UserBalanceListRequest 用户余额列表请求
type UserBalanceListRequest struct {
	Page     int    `json:"page" form:"page" binding:"omitempty,min=1"`
	PageSize int    `json:"page_size" form:"page_size" binding:"omitempty,min=1,max=1000"`
	Keyword  string `json:"keyword" form:"keyword"`
	Status   string `json:"status" form:"status"`
	OrderBy  string `json:"order_by" form:"order_by"`
	Order    string `json:"order" form:"order"`
}

// UserBalanceListResponse 用户余额列表响应
type UserBalanceListResponse struct {
	Items      []*UserBalanceListItem `json:"items"`
	Total      int64                  `json:"total"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int                    `json:"total_pages"`
}

// UserBalanceDetail 用户余额详情
type UserBalanceDetail struct {
	UserID             string                `json:"user_id"`
	Username           string                `json:"username"`
	Email              string                `json:"email"`
	Balance            decimal.Decimal       `json:"balance"`
	AvailableBalance   decimal.Decimal       `json:"available_balance"`
	Currency           string                `json:"currency"`
	Status             string                `json:"status"`
	LastTransactionAt  *time.Time            `json:"last_transaction_at"`
	TransactionCount   int64                 `json:"transaction_count"`
	RecentTransactions []*BalanceTransaction `json:"recent_transactions"`
	CreatedAt          time.Time             `json:"created_at"`
	UpdatedAt          time.Time             `json:"updated_at"`
}

// AdminDepositRequest 管理员充值请求
type AdminDepositRequest struct {
	UserID      string          `json:"user_id" binding:"required"`
	Amount      decimal.Decimal `json:"amount" binding:"required"`
	Reason      string          `json:"reason" binding:"required"`
	Description string          `json:"description"`
	AdminID     string          `json:"-"` // 从上下文获取
}

// BalanceAdjustmentRequest 余额调整请求
type BalanceAdjustmentRequest struct {
	UserID         string          `json:"user_id" binding:"required"`
	Amount         decimal.Decimal `json:"amount" binding:"required"`
	AdjustmentType string          `json:"adjustment_type" binding:"required"` // "increase" or "decrease"
	Reason         string          `json:"reason" binding:"required"`
	Description    string          `json:"description"`
	AdminID        string          `json:"-"`
}

// ForceRefundRequest 强制退款请求
type ForceRefundRequest struct {
	UserID        string          `json:"user_id" binding:"required"`
	Amount        decimal.Decimal `json:"amount" binding:"required"`
	OrderNo       string          `json:"order_no"`
	TransactionID string          `json:"transaction_id"`
	Reason        string          `json:"reason" binding:"required"`
	Description   string          `json:"description"`
	AdminID       string          `json:"-"`
}

// BatchOperationRequest 批量操作请求
type BatchOperationRequest struct {
	OperationType string               `json:"operation_type" binding:"required"`
	UserIDs       []string             `json:"user_ids" binding:"required"`
	Amount        decimal.Decimal      `json:"amount"`
	Reason        string               `json:"reason" binding:"required"`
	Operations    []BatchOperationItem `json:"operations"`
	AdminID       string               `json:"-"`
}

// BatchOperationItem 批量操作项
type BatchOperationItem struct {
	UserID      string          `json:"user_id"`
	Amount      decimal.Decimal `json:"amount"`
	Description string          `json:"description"`
}

// AdminOperationResponse 管理员操作响应
type AdminOperationResponse struct {
	Success       bool            `json:"success"`
	TransactionID string          `json:"transaction_id,omitempty"`
	NewBalance    decimal.Decimal `json:"new_balance,omitempty"`
	Message       string          `json:"message"`
}

// BatchOperationResponse 批量操作响应
type BatchOperationResponse struct {
	TotalCount   int                     `json:"total_count"`
	SuccessCount int                     `json:"success_count"`
	FailureCount int                     `json:"failure_count"`
	Results      []*BatchOperationResult `json:"results"`
}

// BatchOperationResult 批量操作结果
type BatchOperationResult struct {
	UserID        string          `json:"user_id"`
	Success       bool            `json:"success"`
	TransactionID string          `json:"transaction_id,omitempty"`
	NewBalance    decimal.Decimal `json:"new_balance,omitempty"`
	Error         string          `json:"error,omitempty"`
}

// AdminBalanceAuditLog 管理员余额操作审计日志
type AdminBalanceAuditLog struct {
	ID            string          `json:"id" db:"id"`
	AdminID       string          `json:"admin_id" db:"admin_id"`
	TargetUserID  string          `json:"target_user_id" db:"target_user_id"`
	OperationType string          `json:"operation_type" db:"operation_type"`
	Amount        decimal.Decimal `json:"amount" db:"amount"`
	Reason        string          `json:"reason" db:"reason"`
	BeforeBalance decimal.Decimal `json:"before_balance" db:"before_balance"`
	AfterBalance  decimal.Decimal `json:"after_balance" db:"after_balance"`
	Metadata      map[string]any  `json:"metadata" db:"metadata"`
	IPAddress     string          `json:"ip_address" db:"ip_address"`
	UserAgent     string          `json:"user_agent" db:"user_agent"`
	CreatedAt     time.Time       `json:"created_at" db:"created_at"`
}

// AdminTransactionListRequest 管理员交易列表请求
type AdminTransactionListRequest struct {
	Page      int    `json:"page" form:"page" binding:"omitempty,min=1"`
	PageSize  int    `json:"page_size" form:"page_size" binding:"omitempty,min=1,max=1000"`
	UserID    string `json:"user_id" form:"user_id"`
	Type      string `json:"type" form:"type"`
	Status    string `json:"status" form:"status"`
	StartTime string `json:"start_time" form:"start_time"`
	EndTime   string `json:"end_time" form:"end_time"`
	OrderBy   string `json:"order_by" form:"order_by"`
	Order     string `json:"order" form:"order"`
}

// AdminTransactionListResponse 管理员交易列表响应
type AdminTransactionListResponse struct {
	Items      []*BalanceTransaction `json:"items"`
	Total      int64                 `json:"total"`
	Page       int                   `json:"page"`
	PageSize   int                   `json:"page_size"`
	TotalPages int                   `json:"total_pages"`
}

// UpdateTransactionStatusRequest 更新交易状态请求
type UpdateTransactionStatusRequest struct {
	TransactionID string `json:"transaction_id" binding:"required"`
	Status        string `json:"status" binding:"required"`
	Reason        string `json:"reason" binding:"required"`
	AdminID       string `json:"-"`
}

// BalanceStatistics 余额统计
type BalanceStatistics struct {
	TotalBalance     decimal.Decimal `json:"total_balance"`
	AvgBalance       decimal.Decimal `json:"avg_balance"`
	MedianBalance    decimal.Decimal `json:"median_balance"`
	MaxBalance       decimal.Decimal `json:"max_balance"`
	MinBalance       decimal.Decimal `json:"min_balance"`
	TotalUsers       int64           `json:"total_users"`
	ActiveUsers      int64           `json:"active_users"`
	ZeroBalanceUsers int64           `json:"zero_balance_users"`
	HighBalanceUsers int64           `json:"high_balance_users"`
	UpdatedAt        time.Time       `json:"updated_at"`
}

// BalanceAnomaly 余额异常
type BalanceAnomaly struct {
	UserID      string          `json:"user_id"`
	Username    string          `json:"username"`
	Email       string          `json:"email"`
	Balance     decimal.Decimal `json:"balance"`
	AnomalyType string          `json:"anomaly_type"`
	Amount      decimal.Decimal `json:"amount"`
	Description string          `json:"description"`
	Severity    string          `json:"severity"`
	DetectedAt  time.Time       `json:"detected_at"`
	CreatedAt   time.Time       `json:"created_at"`
}

// AnomalyRequest 异常查询请求
type AnomalyRequest struct {
	Page        int    `json:"page" form:"page" binding:"omitempty,min=1"`
	PageSize    int    `json:"page_size" form:"page_size" binding:"omitempty,min=1,max=1000"`
	Type        string `json:"type" form:"type"`
	AnomalyType string `json:"anomaly_type" form:"anomaly_type"`
	Severity    string `json:"severity" form:"severity"`
	UserID      string `json:"user_id" form:"user_id"`
}

// AnomalyResponse 异常查询响应
type AnomalyResponse struct {
	Items      []*BalanceAnomaly `json:"items"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}

// BalanceStatisticsRequest 余额统计请求
type BalanceStatisticsRequest struct {
	StartDate string `form:"start_date" binding:"required"`
	EndDate   string `form:"end_date" binding:"required"`
	GroupBy   string `form:"group_by" binding:"omitempty,oneof=day week month"`
}

// TransactionStatisticsRequest 交易统计请求
type TransactionStatisticsRequest struct {
	StartDate       string `form:"start_date" binding:"required"`
	EndDate         string `form:"end_date" binding:"required"`
	TransactionType string `form:"transaction_type" binding:"omitempty"`
	GroupBy         string `form:"group_by" binding:"omitempty,oneof=day week month"`
}

// AuditLogRequest 审计日志请求
type AuditLogRequest struct {
	Page          int    `form:"page" binding:"omitempty,min=1"`
	PageSize      int    `form:"page_size" binding:"omitempty,min=1,max=1000"`
	AdminID       string `form:"admin_id" binding:"omitempty"`
	TargetUserID  string `form:"target_user_id" binding:"omitempty"`
	OperationType string `form:"operation_type" binding:"omitempty"`
	StartDate     string `form:"start_date" binding:"omitempty"`
	EndDate       string `form:"end_date" binding:"omitempty"`
}

// ExportAuditLogRequest 导出审计日志请求
type ExportAuditLogRequest struct {
	TargetUserID  string `form:"target_user_id" binding:"omitempty"`
	OperationType string `form:"operation_type" binding:"omitempty"`
	StartDate     string `form:"start_date" binding:"required"`
	EndDate       string `form:"end_date" binding:"required"`
	AdminID       string `json:"-"` // 从context获取
}

// ExportUserBalanceRequest 导出用户余额请求
type ExportUserBalanceRequest struct {
	Status    string `form:"status" binding:"omitempty"`
	MinAmount string `form:"min_amount" binding:"omitempty"`
	MaxAmount string `form:"max_amount" binding:"omitempty"`
	AdminID   string `json:"-"` // 从context获取
}

// ExportTransactionRequest 导出交易记录请求
type ExportTransactionRequest struct {
	UserID          string `form:"user_id" binding:"omitempty"`
	TransactionType string `form:"transaction_type" binding:"omitempty"`
	Status          string `form:"status" binding:"omitempty"`
	StartDate       string `form:"start_date" binding:"required"`
	EndDate         string `form:"end_date" binding:"required"`
	AdminID         string `json:"-"` // 从context获取
}

// TransactionStatistics 交易统计
type TransactionStatistics struct {
	TotalTransactions    int64           `json:"total_transactions"`
	TotalAmount          decimal.Decimal `json:"total_amount"`
	DepositCount         int64           `json:"deposit_count"`
	DepositAmount        decimal.Decimal `json:"deposit_amount"`
	WithdrawCount        int64           `json:"withdraw_count"`
	WithdrawAmount       decimal.Decimal `json:"withdraw_amount"`
	RefundCount          int64           `json:"refund_count"`
	RefundAmount         decimal.Decimal `json:"refund_amount"`
	AdjustmentCount      int64           `json:"adjustment_count"`
	AdjustmentAmount     decimal.Decimal `json:"adjustment_amount"`
	AvgTransactionAmount decimal.Decimal `json:"avg_transaction_amount"`
	UpdatedAt            time.Time       `json:"updated_at"`
}

// AuditLogResponse 审计日志响应
type AuditLogResponse struct {
	Items      []*AdminBalanceAuditLog `json:"items"`
	Total      int64                   `json:"total"`
	Page       int                     `json:"page"`
	PageSize   int                     `json:"page_size"`
	TotalPages int                     `json:"total_pages"`
}
