package model

import (
	"time"

	"github.com/shopspring/decimal"
)

// BalanceStatus 余额状态
type BalanceStatus string

const (
	BalanceStatusActive    BalanceStatus = "active"
	BalanceStatusFrozen    BalanceStatus = "frozen"
	BalanceStatusSuspended BalanceStatus = "suspended"
)

// TransactionType 交易类型
type TransactionType string

// 🔥 企业级重构：9种核心交易类型，对应明确业务场景
const (
	// ==================== 充值类 DEPOSIT ====================
	TransactionTypeAdminDeposit TransactionType = "admin_deposit" // 管理员充值
	TransactionTypeUserDeposit  TransactionType = "user_deposit"  // 用户充值

	// ==================== 支付类 PAYMENT ====================
	TransactionTypeOrderPreCharge       TransactionType = "order_pre_charge"       // 下单预收
	TransactionTypeBillingDifference    TransactionType = "billing_difference"     // 费用差额补收
	TransactionTypeOrderInterceptCharge TransactionType = "order_intercept_charge" // 订单拦截_补收
	TransactionTypeReturnCharge         TransactionType = "return_charge"          // 退回收费
	TransactionTypeOrderReviveRecharge  TransactionType = "order_revive_recharge"  // 订单复活重计费

	// ==================== 退款类 REFUND ====================
	TransactionTypeOrderCancelRefund       TransactionType = "order_cancel_refund"       // 订单取消_退款
	TransactionTypeBillingDifferenceRefund TransactionType = "billing_difference_refund" // 费用差额退款

	// ==================== 调整类 ADJUSTMENT ====================
	TransactionTypeBalanceAdjustment TransactionType = "balance_adjustment" // 调账_多退少补

)

// TransactionStatus 交易状态
type TransactionStatus string

const (
	TransactionStatusPending   TransactionStatus = "pending"   // 待处理
	TransactionStatusCompleted TransactionStatus = "completed" // 已完成
	TransactionStatusFailed    TransactionStatus = "failed"    // 失败
	TransactionStatusCancelled TransactionStatus = "cancelled" // 已取消
)

// PaymentMethod 支付方式
type PaymentMethod string

const (
	PaymentMethodBalance      PaymentMethod = "balance"       // 余额支付
	PaymentMethodAlipay       PaymentMethod = "alipay"        // 支付宝
	PaymentMethodWechat       PaymentMethod = "wechat"        // 微信支付
	PaymentMethodBankTransfer PaymentMethod = "bank_transfer" // 银行转账
	PaymentMethodManual       PaymentMethod = "manual"        // 手动充值
)

// UserBalance 用户余额
type UserBalance struct {
	ID        string          `json:"id" db:"id"`
	UserID    string          `json:"user_id" db:"user_id"`
	Balance   decimal.Decimal `json:"balance" db:"balance"`
	Currency  string          `json:"currency" db:"currency"`
	Status    BalanceStatus   `json:"status" db:"status"`
	Version   int64           `json:"version" db:"version"`
	CreatedAt time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt time.Time       `json:"updated_at" db:"updated_at"`
}

// AvailableBalance 获取可用余额（不再使用冻结逻辑，可用余额等于总余额）
func (b *UserBalance) AvailableBalance() decimal.Decimal {
	return b.Balance
}

// TotalBalance 获取总余额（不再使用冻结逻辑，总余额等于余额）
func (b *UserBalance) TotalBalance() decimal.Decimal {
	return b.Balance
}

// BalanceTransaction 余额交易记录
// 🔥 企业级改造：增强交易记录模型，提供完整的业务上下文
type BalanceTransaction struct {
	// 基础信息
	ID            string          `json:"id" db:"id"`
	UserID        string          `json:"user_id" db:"user_id"`
	Type          TransactionType `json:"transaction_type" db:"transaction_type"`
	Amount        decimal.Decimal `json:"amount" db:"amount"`
	Currency      string          `json:"currency" db:"currency"`
	BalanceBefore decimal.Decimal `json:"balance_before" db:"balance_before"`
	BalanceAfter  decimal.Decimal `json:"balance_after" db:"balance_after"`

	// 订单关联信息（新增）
	OrderNo         string `json:"order_no" db:"order_no"`                   // 供应商订单号
	PlatformOrderNo string `json:"platform_order_no" db:"platform_order_no"` // 平台订单号
	CustomerOrderNo string `json:"customer_order_no" db:"customer_order_no"` // 客户订单号
	TrackingNo      string `json:"tracking_no" db:"tracking_no"`             // 运单号

	// 交易分类信息（新增）
	Category string `json:"transaction_category" db:"transaction_category"` // 交易大类
	SubType  string `json:"transaction_sub_type" db:"transaction_sub_type"` // 交易子类型

	// 描述信息（增强）
	ReferenceID       string `json:"reference_id" db:"reference_id"`             // 外部参考ID
	Description       string `json:"description" db:"description"`               // 基础描述
	DetailDescription string `json:"detail_description" db:"detail_description"` // 详细描述
	UserFriendlyDesc  string `json:"user_friendly_desc" db:"user_friendly_desc"` // 用户友好描述

	// 业务上下文（增强）
	Metadata             map[string]interface{} `json:"metadata" db:"metadata"`                             // 元数据
	BusinessContext      map[string]interface{} `json:"business_context" db:"business_context"`             // 业务上下文
	RelatedTransactionID string                 `json:"related_transaction_id" db:"related_transaction_id"` // 关联交易ID

	// 操作信息
	OperatorID string            `json:"operator_id" db:"operator_id"`
	Status     TransactionStatus `json:"status" db:"status"`
	CreatedAt  time.Time         `json:"created_at" db:"created_at"`
}

// 🔥 企业级改造：交易类型分类和描述生成系统

// TransactionCategory 交易大类
type TransactionCategory string

const (
	CategoryDeposit    TransactionCategory = "DEPOSIT"    // 充值类
	CategoryPayment    TransactionCategory = "PAYMENT"    // 支付类
	CategoryRefund     TransactionCategory = "REFUND"     // 退款类
	CategoryAdjustment TransactionCategory = "ADJUSTMENT" // 调整类
)

// GetTransactionCategory 获取交易类型的大类
func (t TransactionType) GetCategory() TransactionCategory {
	switch t {
	// 充值类
	case TransactionTypeUserDeposit, TransactionTypeAdminDeposit:
		return CategoryDeposit

	// 支付类
	case TransactionTypeOrderPreCharge, TransactionTypeBillingDifference,
		TransactionTypeOrderInterceptCharge, TransactionTypeReturnCharge,
		TransactionTypeOrderReviveRecharge:
		return CategoryPayment

	// 退款类
	case TransactionTypeOrderCancelRefund, TransactionTypeBillingDifferenceRefund:
		return CategoryRefund

	// 调整类
	case TransactionTypeBalanceAdjustment:
		return CategoryAdjustment

	default:
		// 默认归类为调整类
		return CategoryAdjustment
	}
}

// GetDisplayName 获取交易类型的显示名称
func (t TransactionType) GetDisplayName() string {
	displayNames := map[TransactionType]string{
		// 充值类
		TransactionTypeUserDeposit:  "用户充值",
		TransactionTypeAdminDeposit: "管理员充值",

		// 支付类
		TransactionTypeOrderPreCharge:       "下单预收",
		TransactionTypeBillingDifference:    "费用差额补收",
		TransactionTypeOrderInterceptCharge: "订单拦截_补收",
		TransactionTypeReturnCharge:         "退回收费",
		TransactionTypeOrderReviveRecharge:  "订单复活重计费",

		// 退款类
		TransactionTypeOrderCancelRefund:       "订单取消_退款",
		TransactionTypeBillingDifferenceRefund: "费用差额退款",

		// 调整类
		TransactionTypeBalanceAdjustment: "调账_多退少补",
	}

	if name, exists := displayNames[t]; exists {
		return name
	}
	return string(t)
}

// Deposit 充值记录
type Deposit struct {
	ID            string                 `json:"id" db:"id"`
	UserID        string                 `json:"user_id" db:"user_id"`
	Amount        decimal.Decimal        `json:"amount" db:"amount"`
	Currency      string                 `json:"currency" db:"currency"`
	PaymentMethod PaymentMethod          `json:"payment_method" db:"payment_method"`
	Status        TransactionStatus      `json:"status" db:"status"`
	TransactionID string                 `json:"transaction_id" db:"transaction_id"`
	PaymentData   map[string]interface{} `json:"payment_data" db:"payment_data"`
	CreatedAt     time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at" db:"updated_at"`
}

// OrderPayment 订单支付记录
type OrderPayment struct {
	ID            string            `json:"id" db:"id"`
	OrderNo       string            `json:"order_no" db:"order_no"`
	UserID        string            `json:"user_id" db:"user_id"`
	Amount        decimal.Decimal   `json:"amount" db:"amount"`
	Currency      string            `json:"currency" db:"currency"`
	PaymentMethod PaymentMethod     `json:"payment_method" db:"payment_method"`
	Status        TransactionStatus `json:"status" db:"status"`
	TransactionID string            `json:"transaction_id" db:"transaction_id"`
	CreatedAt     time.Time         `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time         `json:"updated_at" db:"updated_at"`
}

// BalanceRequest 余额操作请求
type BalanceRequest struct {
	UserID      string                 `json:"user_id" validate:"required"`
	Amount      decimal.Decimal        `json:"amount" validate:"required,gt=0"`
	Currency    string                 `json:"currency" validate:"required"`
	Type        TransactionType        `json:"type" validate:"required"`
	OrderNo     string                 `json:"order_no,omitempty"`
	ReferenceID string                 `json:"reference_id,omitempty"`
	Description string                 `json:"description,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	OperatorID  string                 `json:"operator_id" validate:"required"`
}

// PaymentRequest 支付请求
type PaymentRequest struct {
	UserID        string                 `json:"user_id" validate:"required"`
	OrderNo       string                 `json:"order_no" validate:"required"`
	Amount        decimal.Decimal        `json:"amount" validate:"required,gt=0"`
	Currency      string                 `json:"currency" validate:"required"`
	PaymentMethod PaymentMethod          `json:"payment_method" validate:"required"`
	Description   string                 `json:"description,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// DepositRequest 充值请求
type DepositRequest struct {
	UserID        string                 `json:"user_id" validate:"required"`
	Amount        decimal.Decimal        `json:"amount" validate:"required,gt=0"`
	Currency      string                 `json:"currency" validate:"required"`
	PaymentMethod PaymentMethod          `json:"payment_method" validate:"required"`
	PaymentData   map[string]interface{} `json:"payment_data,omitempty"`
}

// TransferRequest 转账请求
type TransferRequest struct {
	FromUserID  string          `json:"from_user_id" validate:"required"`
	ToUserID    string          `json:"to_user_id" validate:"required"`
	Amount      decimal.Decimal `json:"amount" validate:"required,gt=0"`
	Currency    string          `json:"currency" validate:"required"`
	Description string          `json:"description,omitempty"`
	OperatorID  string          `json:"operator_id" validate:"required"`
}

// BalanceResponse 余额响应
type BalanceResponse struct {
	UserID  string          `json:"user_id"`
	Balance decimal.Decimal `json:"balance"`

	AvailableBalance decimal.Decimal `json:"available_balance"`
	Currency         string          `json:"currency"`
	Status           BalanceStatus   `json:"status"`
	Version          int64           `json:"version"`
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`
}

// TransactionResponse 交易响应 - 🔥 企业级增强版本
type TransactionResponse struct {
	ID            string          `json:"id"`
	UserID        string          `json:"user_id"`
	Type          TransactionType `json:"type"` // 保持向后兼容
	Amount        decimal.Decimal `json:"amount"`
	Currency      string          `json:"currency"`
	BalanceBefore decimal.Decimal `json:"balance_before"`
	BalanceAfter  decimal.Decimal `json:"balance_after"`

	// 订单关联信息（增强）
	OrderNo         string `json:"order_no"`          // 供应商订单号（保持向后兼容）
	PlatformOrderNo string `json:"platform_order_no"` // 平台订单号
	CustomerOrderNo string `json:"customer_order_no"` // 客户订单号
	TrackingNo      string `json:"tracking_no"`       // 运单号

	// 交易分类信息（新增）
	TransactionCategory string `json:"transaction_category"` // 交易大类
	TransactionSubType  string `json:"transaction_sub_type"` // 交易子类型

	// 描述信息（增强）
	ReferenceID       string `json:"reference_id"`
	Description       string `json:"description"`
	DetailDescription string `json:"detail_description"` // 详细描述
	UserFriendlyDesc  string `json:"user_friendly_desc"` // 用户友好描述

	// 业务上下文（新增）
	Metadata             map[string]interface{} `json:"metadata,omitempty"`
	BusinessContext      map[string]interface{} `json:"business_context,omitempty"`
	RelatedTransactionID string                 `json:"related_transaction_id,omitempty"`

	// 操作信息 - 🔥 修复：添加缺失的状态和时间字段
	OperatorID string            `json:"operator_id"`
	Status     TransactionStatus `json:"status"`
	CreatedAt  time.Time         `json:"created_at"`
}

// BalanceCheckResult 余额检查结果 - 🔥 新增：修复余额不足却创建订单成功的BUG
type BalanceCheckResult struct {
	UserID           string          `json:"user_id"`           // 用户ID
	RequestedAmount  decimal.Decimal `json:"requested_amount"`  // 请求金额
	CurrentBalance   decimal.Decimal `json:"current_balance"`   // 当前余额
	AvailableBalance decimal.Decimal `json:"available_balance"` // 可用余额
	IsSufficient     bool            `json:"is_sufficient"`     // 余额是否充足
	Shortage         decimal.Decimal `json:"shortage"`          // 不足金额（如果余额不足）
	CheckTime        time.Time       `json:"check_time"`        // 检查时间
	Message          string          `json:"message"`           // 检查结果消息
}

// TransactionHistoryResponse 交易历史分页响应
type TransactionHistoryResponse struct {
	Items      []*TransactionResponse `json:"items"`
	Total      int64                  `json:"total"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int                    `json:"total_pages"`
}
