package model

import (
	"sync"
)

// StatusManager 线程安全的状态管理器
type StatusManager struct {
	once         sync.Once
	statusGroups map[string][]string
	transitions  map[string][]string
	priorities   map[string]int
}

var (
	statusManager *StatusManager
	managerOnce   sync.Once
)

// GetStatusManager 获取状态管理器单例
func GetStatusManager() *StatusManager {
	managerOnce.Do(func() {
		statusManager = &StatusManager{}
		statusManager.init()
	})
	return statusManager
}

// init 初始化状态管理器
func (sm *StatusManager) init() {
	sm.once.Do(func() {
		sm.statusGroups = initStatusGroups()
		sm.transitions = initValidTransitions()
		sm.priorities = initStatusPriorities()
	})
}

// GetStatusGroups 获取状态分组
func (sm *StatusManager) GetStatusGroups() map[string][]string {
	sm.init()
	// 返回副本以避免并发修改
	result := make(map[string][]string)
	for k, v := range sm.statusGroups {
		result[k] = make([]string, len(v))
		copy(result[k], v)
	}
	return result
}

// GetValidTransitions 获取有效转换
func (sm *StatusManager) GetValidTransitions() map[string][]string {
	sm.init()
	// 返回副本以避免并发修改
	result := make(map[string][]string)
	for k, v := range sm.transitions {
		result[k] = make([]string, len(v))
		copy(result[k], v)
	}
	return result
}

// GetStatusPriorities 获取状态优先级
func (sm *StatusManager) GetStatusPriorities() map[string]int {
	sm.init()
	// 返回副本以避免并发修改
	result := make(map[string]int)
	for k, v := range sm.priorities {
		result[k] = v
	}
	return result
}

// IsValidTransition 检查状态转换是否合法（线程安全）
func (sm *StatusManager) IsValidTransition(fromStatus, toStatus string) bool {
	sm.init()

	if !sm.isValidStatus(fromStatus) || !sm.isValidStatus(toStatus) {
		return false
	}

	validNextStates, exists := sm.transitions[fromStatus]
	if !exists {
		return false
	}

	for _, validStatus := range validNextStates {
		if validStatus == toStatus {
			return true
		}
	}

	return false
}

// GetStatusGroup 获取状态所属分组（线程安全）
func (sm *StatusManager) GetStatusGroup(status string) string {
	sm.init()

	for group, statuses := range sm.statusGroups {
		for _, s := range statuses {
			if s == status {
				return group
			}
		}
	}
	return "unknown"
}

// GetStatusPriority 获取状态优先级（线程安全）
func (sm *StatusManager) GetStatusPriority(status string) int {
	sm.init()

	if priority, exists := sm.priorities[status]; exists {
		return priority
	}
	return 999 // 未知状态的最低优先级
}

// isValidStatus 检查状态是否有效
func (sm *StatusManager) isValidStatus(status string) bool {
	_, ok := sm.priorities[status]
	return ok
}

// initStatusGroups 初始化状态分组
func initStatusGroups() map[string][]string {
	return map[string][]string{
		"active": {
			OrderStatusSubmitted, OrderStatusAssigned, OrderStatusAwaitingPickup,
			OrderStatusPickedUp, OrderStatusInTransit, OrderStatusOutForDelivery,
		},
		"completed": {
			OrderStatusDelivered, OrderStatusDeliveredAbnormal, OrderStatusBilled,
		},
		"failed": {
			OrderStatusSubmitFailed, OrderStatusPrintFailed, OrderStatusPickupFailed,
		},
		"cancelled": {
			OrderStatusCancelling, OrderStatusCancelled, OrderStatusVoided,
		},
		"exception": {
			OrderStatusException, OrderStatusReturned, OrderStatusForwarded,
		},
		"special": {
			OrderStatusWeightUpdated, OrderStatusRevived,
		},
	}
}

// initValidTransitions 初始化有效转换
func initValidTransitions() map[string][]string {
	return map[string][]string{
		OrderStatusSubmitted: {
			OrderStatusAssigned, OrderStatusSubmitFailed, OrderStatusPrintFailed, OrderStatusCancelling, OrderStatusCancelled,
		},
		OrderStatusAssigned: {
			OrderStatusAwaitingPickup, OrderStatusCancelled,
		},
		OrderStatusAwaitingPickup: {
			OrderStatusPickedUp, OrderStatusPickupFailed, OrderStatusCancelled,
		},
		OrderStatusPickedUp: {
			OrderStatusInTransit, OrderStatusBilled, OrderStatusException, OrderStatusCancelled,
		},
		OrderStatusInTransit: {
			OrderStatusOutForDelivery, OrderStatusDelivered, OrderStatusException, OrderStatusReturned, OrderStatusForwarded,
		},
		OrderStatusOutForDelivery: {
			OrderStatusDelivered, OrderStatusDeliveredAbnormal, OrderStatusException, OrderStatusReturned,
		},
		OrderStatusDelivered: {
			OrderStatusBilled,
		},
		OrderStatusDeliveredAbnormal: {
			OrderStatusBilled,
		},
		OrderStatusException: {
			OrderStatusInTransit, OrderStatusReturned, OrderStatusCancelled,
		},
		OrderStatusPickupFailed: {
			OrderStatusAwaitingPickup, OrderStatusCancelled,
		},
		OrderStatusReturned: {
			OrderStatusCancelled,
		},
		OrderStatusCancelling: {
			OrderStatusCancelled, OrderStatusVoided,
		},
		OrderStatusCancelled: {
			OrderStatusRevived, // 快递100特有
		},
		OrderStatusWeightUpdated: {
			OrderStatusInTransit, OrderStatusBilled, OrderStatusCancelled, OrderStatusException,
		},
		// 终态：OrderStatusBilled, OrderStatusVoided, OrderStatusSubmitFailed, OrderStatusPrintFailed
	}
}

// initStatusPriorities 初始化状态优先级
func initStatusPriorities() map[string]int {
	return map[string]int{
		OrderStatusSubmitFailed:      1,
		OrderStatusPrintFailed:       2,
		OrderStatusCancelling:        3,
		OrderStatusCancelled:         4,
		OrderStatusVoided:            5,
		OrderStatusPickupFailed:      6,
		OrderStatusException:         7,
		OrderStatusReturned:          8,
		OrderStatusForwarded:         9,
		OrderStatusWeightUpdated:     10,
		OrderStatusRevived:           11,
		OrderStatusSubmitted:         12,
		OrderStatusAssigned:          13,
		OrderStatusAwaitingPickup:    14,
		OrderStatusPickedUp:          15,
		OrderStatusInTransit:         16,
		OrderStatusOutForDelivery:    17,
		OrderStatusDelivered:         18,
		OrderStatusDeliveredAbnormal: 19,
		OrderStatusBilled:            20,
	}
}
