package model

import (
	"time"
)

// ChangeSource 状态变更来源
type ChangeSource string

const (
	ChangeSourceCallback ChangeSource = "callback" // 供应商回调
	ChangeSourceManual   ChangeSource = "manual"   // 手动操作
	ChangeSourceSystem   ChangeSource = "system"   // 系统自动
	ChangeSourceAPI      ChangeSource = "api"      // API调用
)

// OrderStatusHistory 订单状态历史记录
type OrderStatusHistory struct {
	ID              int64                  `json:"id" db:"id" gorm:"primaryKey;autoIncrement"`
	OrderNo         string                 `json:"order_no" db:"order_no" gorm:"column:order_no;not null;index"`
	FromStatus      string                 `json:"from_status" db:"from_status" gorm:"column:from_status;not null"`
	ToStatus        string                 `json:"to_status" db:"to_status" gorm:"column:to_status;not null"`
	Provider        string                 `json:"provider" db:"provider" gorm:"column:provider;not null"`
	RawStatus       string                 `json:"raw_status" db:"raw_status" gorm:"column:raw_status;default:''"`
	ChangeSource    string                 `json:"change_source" db:"change_source" gorm:"column:change_source;default:'callback'"`
	OperatorID      string                 `json:"operator_id" db:"operator_id" gorm:"column:operator_id"`
	OperatorName    string                 `json:"operator_name" db:"operator_name" gorm:"column:operator_name"`
	ChangeReason    string                 `json:"change_reason" db:"change_reason" gorm:"column:change_reason"`
	UserID          string                 `json:"user_id" db:"user_id" gorm:"column:user_id;index"`
	CustomerOrderNo string                 `json:"customer_order_no" db:"customer_order_no" gorm:"column:customer_order_no;index"`
	Extra           map[string]interface{} `json:"extra" db:"extra" gorm:"column:extra;type:jsonb;default:'{}'"`
	CreatedAt       time.Time              `json:"created_at" db:"created_at" gorm:"column:created_at;autoCreateTime"`
	UpdatedAt       time.Time              `json:"updated_at" db:"updated_at" gorm:"column:updated_at;autoUpdateTime"`
}

// TableName 指定表名
func (OrderStatusHistory) TableName() string {
	return "order_status_history"
}

// OrderStatusUpdateRequest 订单状态更新请求
type OrderStatusUpdateRequest struct {
	OrderNo    string                 `json:"order_no"`
	NewStatus  string                 `json:"new_status"`
	UpdateTime time.Time              `json:"update_time"`
	Provider   string                 `json:"provider"`
	Extra      map[string]interface{} `json:"extra"`
}

// StatusTransitionValidation 状态转换验证结果
type StatusTransitionValidation struct {
	IsValid    bool   `json:"is_valid"`
	FromStatus string `json:"from_status"`
	ToStatus   string `json:"to_status"`
	Reason     string `json:"reason"`
}

// StatusStatistics 状态统计信息
type StatusStatistics struct {
	Status      string  `json:"status"`
	Count       int64   `json:"count"`
	Percentage  float64 `json:"percentage"`
	Description string  `json:"description"`
}

// OrderStatusSummary 订单状态汇总
type OrderStatusSummary struct {
	OrderNo         string                 `json:"order_no"`
	CurrentStatus   string                 `json:"current_status"`
	StatusDesc      string                 `json:"status_desc"`
	Provider        string                 `json:"provider"`
	LastUpdateTime  time.Time              `json:"last_update_time"`
	StatusHistory   []OrderStatusHistory   `json:"status_history"`
	TransitionCount int                    `json:"transition_count"`
	IsTerminal      bool                   `json:"is_terminal"`
	StatusGroup     string                 `json:"status_group"`
	Extra           map[string]interface{} `json:"extra"`
}

// StatusMetrics 状态指标
type StatusMetrics struct {
	TotalOrders        int64              `json:"total_orders"`
	StatusDistribution []StatusStatistics `json:"status_distribution"`
	ProviderStats      map[string]int64   `json:"provider_stats"`
	TransitionStats    map[string]int64   `json:"transition_stats"`
	AverageTransitions float64            `json:"average_transitions"`
	TerminalRate       float64            `json:"terminal_rate"`
	GeneratedAt        time.Time          `json:"generated_at"`
}

// GetStatusTransitionKey 获取状态转换键
func GetStatusTransitionKey(fromStatus, toStatus string) string {
	return fromStatus + "->" + toStatus
}

// IsValidStatusTransition 验证状态转换
func IsValidStatusTransition(fromStatus, toStatus string) StatusTransitionValidation {
	if !IsValidStatus(fromStatus) {
		return StatusTransitionValidation{
			IsValid:    false,
			FromStatus: fromStatus,
			ToStatus:   toStatus,
			Reason:     "源状态无效",
		}
	}

	if !IsValidStatus(toStatus) {
		return StatusTransitionValidation{
			IsValid:    false,
			FromStatus: fromStatus,
			ToStatus:   toStatus,
			Reason:     "目标状态无效",
		}
	}

	if !IsValidTransition(fromStatus, toStatus) {
		return StatusTransitionValidation{
			IsValid:    false,
			FromStatus: fromStatus,
			ToStatus:   toStatus,
			Reason:     "状态转换不合法",
		}
	}

	return StatusTransitionValidation{
		IsValid:    true,
		FromStatus: fromStatus,
		ToStatus:   toStatus,
		Reason:     "状态转换合法",
	}
}

// GetStatusPriority 获取状态优先级
func GetStatusPriority(status string) int {
	manager := GetStatusManager()
	return manager.GetStatusPriority(status)
}

// CompareStatusPriority 比较状态优先级
func CompareStatusPriority(status1, status2 string) int {
	priority1 := GetStatusPriority(status1)
	priority2 := GetStatusPriority(status2)

	if priority1 < priority2 {
		return -1
	} else if priority1 > priority2 {
		return 1
	}
	return 0
}

// GetHigherPriorityStatus 获取优先级更高的状态
func GetHigherPriorityStatus(status1, status2 string) string {
	if CompareStatusPriority(status1, status2) <= 0 {
		return status1
	}
	return status2
}

// BuildStatusSummary 构建状态汇总
func BuildStatusSummary(orderNo, currentStatus, provider string, lastUpdateTime time.Time, history []OrderStatusHistory, extra map[string]interface{}) OrderStatusSummary {
	return OrderStatusSummary{
		OrderNo:         orderNo,
		CurrentStatus:   currentStatus,
		StatusDesc:      GetOrderStatusDesc(currentStatus),
		Provider:        provider,
		LastUpdateTime:  lastUpdateTime,
		StatusHistory:   history,
		TransitionCount: len(history),
		IsTerminal:      IsTerminalStatus(currentStatus),
		StatusGroup:     GetStatusGroup(currentStatus),
		Extra:           extra,
	}
}

// CalculateStatusMetrics 计算状态指标
func CalculateStatusMetrics(orders []OrderStatusSummary) StatusMetrics {
	if len(orders) == 0 {
		return StatusMetrics{
			TotalOrders: 0,
			GeneratedAt: time.Now(),
		}
	}

	totalOrders := int64(len(orders))
	statusCount := make(map[string]int64)
	providerCount := make(map[string]int64)
	transitionCount := make(map[string]int64)
	totalTransitions := 0
	terminalCount := 0

	for _, order := range orders {
		// 统计状态分布
		statusCount[order.CurrentStatus]++

		// 统计供应商分布
		providerCount[order.Provider]++

		// 统计转换次数
		totalTransitions += order.TransitionCount

		// 统计终态订单
		if order.IsTerminal {
			terminalCount++
		}

		// 统计状态转换
		for i := 1; i < len(order.StatusHistory); i++ {
			transitionKey := GetStatusTransitionKey(
				order.StatusHistory[i-1].FromStatus,
				order.StatusHistory[i].ToStatus,
			)
			transitionCount[transitionKey]++
		}
	}

	// 构建状态分布
	var statusDistribution []StatusStatistics
	for status, count := range statusCount {
		percentage := float64(count) / float64(totalOrders) * 100
		statusDistribution = append(statusDistribution, StatusStatistics{
			Status:      status,
			Count:       count,
			Percentage:  percentage,
			Description: GetOrderStatusDesc(status),
		})
	}

	// 计算平均转换次数
	averageTransitions := float64(totalTransitions) / float64(totalOrders)

	// 计算终态比例
	terminalRate := float64(terminalCount) / float64(totalOrders) * 100

	return StatusMetrics{
		TotalOrders:        totalOrders,
		StatusDistribution: statusDistribution,
		ProviderStats:      providerCount,
		TransitionStats:    transitionCount,
		AverageTransitions: averageTransitions,
		TerminalRate:       terminalRate,
		GeneratedAt:        time.Now(),
	}
}

// RecordStatusChangeRequest 记录状态变更请求
type RecordStatusChangeRequest struct {
	OrderNo         string      `json:"order_no" validate:"required"`
	FromStatus      string      `json:"from_status" validate:"required"`
	ToStatus        string      `json:"to_status" validate:"required"`
	Provider        string      `json:"provider" validate:"required"`
	RawStatus       string      `json:"raw_status"`
	ChangeSource    string      `json:"change_source" validate:"required"`
	OperatorID      string      `json:"operator_id"`
	OperatorName    string      `json:"operator_name"`
	ChangeReason    string      `json:"change_reason"`
	UserID          string      `json:"user_id"`
	CustomerOrderNo string      `json:"customer_order_no"`
	Extra           interface{} `json:"extra"`
}

// StatusHistoryItem 状态历史项（用于API响应）
type StatusHistoryItem struct {
	ID               int64     `json:"id"`
	OrderNo          string    `json:"order_no"`
	CustomerOrderNo  string    `json:"customer_order_no"`
	FromStatus       string    `json:"from_status"`
	FromStatusDesc   string    `json:"from_status_desc"`
	ToStatus         string    `json:"to_status"`
	ToStatusDesc     string    `json:"to_status_desc"`
	Provider         string    `json:"provider"`
	ProviderName     string    `json:"provider_name"`
	ChangeSource     string    `json:"change_source"`
	ChangeSourceDesc string    `json:"change_source_desc"`
	OperatorName     string    `json:"operator_name"`
	ChangeReason     string    `json:"change_reason"`
	CreatedAt        time.Time `json:"created_at"`
	Duration         string    `json:"duration"` // 状态持续时间
}

// GetStatusHistoryRequest 获取状态历史请求
type GetStatusHistoryRequest struct {
	OrderNo         string `json:"order_no" form:"order_no"`
	CustomerOrderNo string `json:"customer_order_no" form:"customer_order_no"`
	UserID          string `json:"user_id" form:"user_id"`
	Page            int    `json:"page" form:"page" validate:"min=1"`
	Limit           int    `json:"limit" form:"limit" validate:"min=1,max=100"`
}

// GetStatusHistoryResponse 获取状态历史响应
type GetStatusHistoryResponse struct {
	Items []StatusHistoryItem `json:"items"`
	Total int64               `json:"total"`
	Page  int                 `json:"page"`
	Limit int                 `json:"limit"`
}

// GetChangeSourceDesc 获取变更来源描述
func GetChangeSourceDesc(source string) string {
	switch ChangeSource(source) {
	case ChangeSourceCallback:
		return "供应商回调"
	case ChangeSourceManual:
		return "手动操作"
	case ChangeSourceSystem:
		return "系统自动"
	case ChangeSourceAPI:
		return "API调用"
	default:
		return "未知来源"
	}
}
