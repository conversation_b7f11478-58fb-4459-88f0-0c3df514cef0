package model

// SimplePriceRequest 简化价格查询请求 - 统一定义避免重复声明
type SimplePriceRequest struct {
	// 寄件地址 (必填)
	FromProvince string `json:"from_province" binding:"required" example:"广东省"` // 寄件省
	FromCity     string `json:"from_city" binding:"required" example:"深圳市"`     // 寄件市
	FromDistrict string `json:"from_district" example:"南山区"`                    // 寄件区/县 (可选)

	// 收件地址 (必填)
	ToProvince string `json:"to_province" binding:"required" example:"北京市"` // 收件省
	ToCity     string `json:"to_city" binding:"required" example:"北京市"`     // 收件市
	ToDistrict string `json:"to_district" example:"朝阳区"`                    // 收件区/县 (可选)

	// 快递公司查询 (可选)
	ExpressCode string `json:"express_code" example:"SF"` // 快递公司代码 (可选，不填则查询所有快递公司)

	// 包裹信息
	Weight    float64 `json:"weight" binding:"required,gt=0" example:"1.5"` // 重量(kg) (必填)
	Length    float64 `json:"length" example:"30"`                          // 长(cm) (可选)
	Width     float64 `json:"width" example:"20"`                           // 宽(cm) (可选)
	Height    float64 `json:"height" example:"10"`                          // 高(cm) (可选)
	Volume    float64 `json:"volume" example:"0.01"`                        // 体积(m³) (可选，如果提供长宽高则自动计算)
	Quantity  int     `json:"quantity" binding:"min=1" example:"1"`         // 包裹数量 (可选，默认1)
	GoodsName string  `json:"goods_name" example:"电子产品"`                    // 物品名称 (可选，默认"物品")

	// 其他信息
	PayMethod int `json:"pay_method" example:"0"` // 支付方式：0-寄付，1-到付，2-月结 (可选，默认0)
}

// Validate 验证请求参数
func (r *SimplePriceRequest) Validate() error {
	if r.FromProvince == "" {
		return NewValidationError("发件省份不能为空")
	}
	if r.FromCity == "" {
		return NewValidationError("发件城市不能为空")
	}
	if r.ToProvince == "" {
		return NewValidationError("收件省份不能为空")
	}
	if r.ToCity == "" {
		return NewValidationError("收件城市不能为空")
	}
	if r.Weight <= 0 {
		return NewValidationError("重量必须大于0")
	}
	if r.Quantity <= 0 {
		return NewValidationError("数量必须大于0")
	}
	return nil
}

// SetDefaults 设置默认值
func (r *SimplePriceRequest) SetDefaults() {
	if r.FromDistrict == "" {
		r.FromDistrict = "市辖区"
	}
	if r.ToDistrict == "" {
		r.ToDistrict = "市辖区"
	}
	if r.GoodsName == "" {
		r.GoodsName = "普通货物"
	}
	if r.Quantity == 0 {
		r.Quantity = 1
	}
}

// ToStandardRequest 转换为标准请求格式
func (r *SimplePriceRequest) ToStandardRequest() *PriceRequest {
	// 确保设置了默认值
	r.SetDefaults()

	// 🔥 企业级修复：优先使用长宽高计算体积
	volume := r.Volume
	if r.Length > 0 && r.Width > 0 && r.Height > 0 {
		// 长宽高单位是cm，需要转换为m³
		volumeCm3 := r.Length * r.Width * r.Height
		volume = volumeCm3 / 1000000 // cm³ → m³
	}

	// 🚀 新增：根据是否指定快递公司代码决定查询模式
	queryAllCompanies := r.ExpressCode == ""
	expressType := r.ExpressCode

	return &PriceRequest{
		ExpressType:       expressType,       // 设置快递公司代码
		QueryAllCompanies: queryAllCompanies, // 如果没有指定快递公司，则查询所有
		Sender: SenderInfo{
			Province: r.FromProvince,
			City:     r.FromCity,
			District: r.FromDistrict,
			Name:     "寄件人",
			Mobile:   "13800000000",
			Address:  r.FromDistrict + "某地",
		},
		Receiver: ReceiverInfo{
			Province: r.ToProvince,
			City:     r.ToCity,
			District: r.ToDistrict,
			Name:     "收件人",
			Mobile:   "13900000000",
			Address:  r.ToDistrict + "某地",
		},
		Package: PackageInfo{
			Weight:    r.Weight,
			Length:    r.Length,
			Width:     r.Width,
			Height:    r.Height,
			Volume:    volume,
			Quantity:  r.Quantity,
			GoodsName: r.GoodsName,
		},
		PayMethod: r.PayMethod,
	}
}
