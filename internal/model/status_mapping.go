package model

import (
	"fmt"
	"time"
)

// SystemOrderStatus 系统统一订单状态枚举
type SystemOrderStatus string

const (
	// === 下单阶段 ===
	StatusSubmitted    SystemOrderStatus = "submitted"     // 已提交
	StatusSubmitFailed SystemOrderStatus = "submit_failed" // 提交失败
	StatusPrintFailed  SystemOrderStatus = "print_failed"  // 面单生成失败

	// === 分配阶段 ===
	StatusAssigned       SystemOrderStatus = "assigned"        // 已分配
	StatusAwaitingPickup SystemOrderStatus = "awaiting_pickup" // 等待揽收

	// === 揽收阶段 ===
	StatusPickedUp     SystemOrderStatus = "picked_up"     // 已揽收
	StatusPickupFailed SystemOrderStatus = "pickup_failed" // 揽收失败

	// === 运输阶段 ===
	StatusInTransit      SystemOrderStatus = "in_transit"       // 运输中
	StatusOutForDelivery SystemOrderStatus = "out_for_delivery" // 派送中

	// === 签收阶段 ===
	StatusDelivered         SystemOrderStatus = "delivered"          // 已签收
	StatusDeliveredAbnormal SystemOrderStatus = "delivered_abnormal" // 异常签收

	// === 计费阶段 ===
	StatusBilled SystemOrderStatus = "billed" // 已计费

	// === 异常状态 ===
	StatusException SystemOrderStatus = "exception" // 异常
	StatusReturned  SystemOrderStatus = "returned"  // 已退回
	StatusForwarded SystemOrderStatus = "forwarded" // 已转寄

	// === 取消状态 ===
	StatusCancelling SystemOrderStatus = "cancelling" // 取消中
	StatusCancelled  SystemOrderStatus = "cancelled"  // 已取消
	StatusVoided     SystemOrderStatus = "voided"     // 已作废

	// === 特殊状态 ===
	StatusWeightUpdated SystemOrderStatus = "weight_updated" // 重量更新
	StatusRevived       SystemOrderStatus = "revived"        // 订单复活

	// === 未知状态 ===
	StatusUnknown SystemOrderStatus = "unknown" // 未知状态
)

// ProviderStatusMapping 供应商状态映射
type ProviderStatusMapping struct {
	ProviderCode   string                          `json:"provider_code"`   // 供应商代码
	ProviderName   string                          `json:"provider_name"`   // 供应商名称
	StatusMappings map[string]SystemOrderStatus    `json:"status_mappings"` // 状态映射表
	DefaultStatus  SystemOrderStatus               `json:"default_status"`  // 默认状态
	SupportedOps   []string                        `json:"supported_ops"`   // 支持的操作
	StatusRules    map[string]StatusTransitionRule `json:"status_rules"`    // 状态转换规则
}

// StatusTransitionRule 状态转换规则
type StatusTransitionRule struct {
	FromStatus    []SystemOrderStatus `json:"from_status"`    // 允许的源状态
	ToStatus      SystemOrderStatus   `json:"to_status"`      // 目标状态
	RequiredField []string            `json:"required_field"` // 必需字段
	Description   string              `json:"description"`    // 规则描述
	Priority      int                 `json:"priority"`       // 优先级
}

// StatusSyncResult 状态同步结果
type StatusSyncResult struct {
	OrderID            int64             `json:"order_id"`
	OrderNo            string            `json:"order_no"`
	Provider           string            `json:"provider"`
	OldStatus          SystemOrderStatus `json:"old_status"`
	NewStatus          SystemOrderStatus `json:"new_status"`
	ProviderStatus     string            `json:"provider_status"`
	ProviderStatusDesc string            `json:"provider_status_desc"`
	SyncTime           time.Time         `json:"sync_time"`
	Success            bool              `json:"success"`
	ErrorMessage       string            `json:"error_message,omitempty"`
	StatusChanged      bool              `json:"status_changed"`
	ValidationPassed   bool              `json:"validation_passed"`
	ValidationErrors   []string          `json:"validation_errors,omitempty"`
	// 🔥 新增：退款相关字段
	RefundTriggered bool   `json:"refund_triggered"` // 是否触发退款
	RefundAmount    string `json:"refund_amount"`    // 退款金额
}

// BatchStatusSyncRequest 批量状态同步请求
type BatchStatusSyncRequest struct {
	OrderIDs    []int64 `json:"order_ids,omitempty"`    // 指定订单ID列表
	Provider    string  `json:"provider,omitempty"`     // 供应商筛选
	StartTime   string  `json:"start_time,omitempty"`   // 开始时间
	EndTime     string  `json:"end_time,omitempty"`     // 结束时间
	MaxCount    int     `json:"max_count,omitempty"`    // 最大处理数量
	ForceSync   bool    `json:"force_sync,omitempty"`   // 强制同步（忽略时间限制）
	DryRun      bool    `json:"dry_run,omitempty"`      // 试运行模式
	OnlyFailed  bool    `json:"only_failed,omitempty"`  // 只同步失败的订单
	SkipRecent  bool    `json:"skip_recent,omitempty"`  // 跳过最近同步的订单
	RecentHours int     `json:"recent_hours,omitempty"` // 最近同步时间阈值（小时）
}

// BatchStatusSyncResponse 批量状态同步响应
type BatchStatusSyncResponse struct {
	Success bool                 `json:"success"`
	Code    int                  `json:"code"`
	Message string               `json:"message"`
	Data    *BatchStatusSyncData `json:"data,omitempty"`
}

// BatchStatusSyncData 批量状态同步数据
type BatchStatusSyncData struct {
	TotalOrders     int                `json:"total_orders"`     // 总订单数
	ProcessedOrders int                `json:"processed_orders"` // 已处理订单数
	SuccessOrders   int                `json:"success_orders"`   // 成功订单数
	FailedOrders    int                `json:"failed_orders"`    // 失败订单数
	ChangedOrders   int                `json:"changed_orders"`   // 状态变更订单数
	SkippedOrders   int                `json:"skipped_orders"`   // 跳过订单数
	Results         []StatusSyncResult `json:"results"`          // 同步结果列表
	Summary         *StatusSyncSummary `json:"summary"`          // 汇总信息
	Duration        string             `json:"duration"`         // 处理耗时
}

// StatusSyncSummary 状态同步汇总
type StatusSyncSummary struct {
	ProviderStats     map[string]int            `json:"provider_stats"`      // 按供应商统计
	StatusChangeStats map[string]map[string]int `json:"status_change_stats"` // 状态变更统计
	ErrorStats        map[string]int            `json:"error_stats"`         // 错误统计
	ProcessingTime    map[string]float64        `json:"processing_time"`     // 处理时间统计
}

// GetSystemStatusLabel 获取系统状态的中文标签
func (s SystemOrderStatus) GetLabel() string {
	labels := map[SystemOrderStatus]string{
		StatusSubmitted:         "已提交",
		StatusSubmitFailed:      "提交失败",
		StatusPrintFailed:       "面单生成失败",
		StatusAssigned:          "已分配",
		StatusAwaitingPickup:    "等待揽收",
		StatusPickedUp:          "已揽收",
		StatusPickupFailed:      "揽收失败",
		StatusInTransit:         "运输中",
		StatusOutForDelivery:    "派送中",
		StatusDelivered:         "已签收",
		StatusDeliveredAbnormal: "异常签收",
		StatusBilled:            "已计费",
		StatusException:         "异常",
		StatusReturned:          "已退回",
		StatusForwarded:         "已转寄",
		StatusCancelling:        "取消中",
		StatusCancelled:         "已取消",
		StatusVoided:            "已作废",
		StatusWeightUpdated:     "重量更新",
		StatusRevived:           "订单复活",
		StatusUnknown:           "未知状态",
	}
	if label, exists := labels[s]; exists {
		return label
	}
	return string(s)
}

// IsValidTransition 检查状态转换是否有效
func (s SystemOrderStatus) IsValidTransition(to SystemOrderStatus) bool {
	// 定义有效的状态转换规则（允许合理的状态跳转）
	validTransitions := map[SystemOrderStatus][]SystemOrderStatus{
		StatusSubmitted: {
			StatusAssigned, StatusSubmitFailed, StatusPrintFailed, StatusCancelled,
			StatusAwaitingPickup, StatusPickedUp, StatusInTransit, // 允许跳转
		},
		StatusAssigned: {
			StatusAwaitingPickup, StatusPickedUp, StatusCancelled,
			StatusInTransit, StatusOutForDelivery, StatusDelivered, // 🔥 修复：允许跳转到运输中等状态
		},
		StatusAwaitingPickup: {
			StatusPickedUp, StatusPickupFailed, StatusCancelled,
			StatusInTransit, StatusOutForDelivery, StatusDelivered, // 允许跳转
		},
		StatusPickedUp: {
			StatusInTransit, StatusException, StatusReturned,
			StatusOutForDelivery, StatusDelivered, // 允许跳转
		},
		StatusInTransit: {
			StatusOutForDelivery, StatusDelivered, StatusException, StatusReturned, StatusForwarded,
			StatusDeliveredAbnormal, StatusBilled, // 允许跳转
		},
		StatusOutForDelivery: {
			StatusDelivered, StatusDeliveredAbnormal, StatusException, StatusReturned,
			StatusBilled, // 允许跳转
		},
		StatusDelivered: {
			StatusBilled, StatusException,
		},
		StatusDeliveredAbnormal: {
			StatusBilled, StatusException, StatusReturned,
		},
		StatusException: {
			StatusInTransit, StatusReturned, StatusCancelled, StatusRevived,
			StatusAwaitingPickup, StatusPickedUp, StatusOutForDelivery, StatusDelivered, // 允许恢复到各种状态
		},
		StatusReturned: {
			StatusRevived, StatusCancelled,
		},
		StatusCancelling: {
			StatusCancelled, StatusRevived,
		},
		StatusWeightUpdated: {
			StatusInTransit, StatusOutForDelivery, StatusDelivered,
			StatusAwaitingPickup, StatusPickedUp, // 允许跳转
		},
	}

	// 特殊情况：相同状态总是允许的（用于状态确认）
	if s == to {
		return true
	}

	// 检查是否在允许的转换列表中
	if allowedTransitions, exists := validTransitions[s]; exists {
		for _, allowed := range allowedTransitions {
			if allowed == to {
				return true
			}
		}
	}

	return false
}

// GetStatusPriority 获取状态优先级（用于冲突解决）
func (s SystemOrderStatus) GetPriority() int {
	priorities := map[SystemOrderStatus]int{
		StatusCancelled:         100, // 最高优先级
		StatusVoided:            95,
		StatusDelivered:         90,
		StatusDeliveredAbnormal: 85,
		StatusBilled:            80,
		StatusReturned:          75,
		StatusException:         70,
		StatusOutForDelivery:    60,
		StatusInTransit:         50,
		StatusPickedUp:          40,
		StatusPickupFailed:      35,
		StatusAwaitingPickup:    30,
		StatusAssigned:          20,
		StatusSubmitted:         10,
		StatusSubmitFailed:      5,
		StatusPrintFailed:       5,
		StatusUnknown:           0, // 最低优先级
	}
	if priority, exists := priorities[s]; exists {
		return priority
	}
	return 0
}

// Validate 验证状态同步结果
func (r *StatusSyncResult) Validate() error {
	if r.OrderID <= 0 {
		return fmt.Errorf("invalid order ID: %d", r.OrderID)
	}
	if r.OrderNo == "" {
		return fmt.Errorf("order number cannot be empty")
	}
	if r.Provider == "" {
		return fmt.Errorf("provider cannot be empty")
	}
	if r.NewStatus == "" {
		return fmt.Errorf("new status cannot be empty")
	}
	return nil
}
