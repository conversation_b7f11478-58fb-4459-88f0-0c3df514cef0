package model

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// UnifiedWorkOrderCallbackData 统一工单回调数据
// 🔥 企业级标准：完全统一的回调格式，供应商无关
type UnifiedWorkOrderCallbackData struct {
	// 基础信息
	EventType string `json:"event_type"` // 事件类型：workorder.created, workorder.replied, workorder.completed
	EventTime int64  `json:"event_time"` // 事件时间戳
	Version   string `json:"version"`    // 回调版本，固定为"3.0"

	// 工单信息
	WorkOrderID         string `json:"work_order_id"`          // 平台工单ID
	ProviderWorkOrderID string `json:"provider_work_order_id"` // 供应商工单ID
	WorkOrderType       int    `json:"work_order_type"`        // 工单类型
	WorkOrderTypeName   string `json:"work_order_type_name"`   // 工单类型名称

	// 订单信息
	OrderNo         string `json:"order_no"`          // 平台订单号
	CustomerOrderNo string `json:"customer_order_no"` // 客户订单号
	TrackingNo      string `json:"tracking_no"`       // 运单号

	// 供应商信息（隐藏具体供应商细节）
	Provider string `json:"provider"` // 供应商标识

	// 状态信息
	Status     int    `json:"status"`      // 工单状态
	StatusName string `json:"status_name"` // 状态名称

	// 回复内容
	Content        string   `json:"content"`         // 回复内容
	Committer      string   `json:"committer"`       // 回复人
	AttachmentURLs []string `json:"attachment_urls"` // 附件URL列表

	// 时间信息
	CreatedAt int64 `json:"created_at"` // 工单创建时间
	UpdatedAt int64 `json:"updated_at"` // 更新时间

	// 扩展信息
	Extra map[string]interface{} `json:"extra,omitempty"` // 扩展字段
}

// WorkOrderCallbackConfig 工单回调配置
type WorkOrderCallbackConfig struct {
	ID               uuid.UUID `json:"id" db:"id"`
	UserID           string    `json:"user_id" db:"user_id"`
	Enabled          bool      `json:"enabled" db:"enabled"`
	CallbackURL      string    `json:"callback_url" db:"callback_url"`
	CallbackSecret   string    `json:"callback_secret" db:"callback_secret"`
	TimeoutSeconds   int       `json:"timeout_seconds" db:"timeout_seconds"`
	MaxRetries       int       `json:"max_retries" db:"max_retries"`
	SubscribedEvents []string  `json:"subscribed_events" db:"subscribed_events"`
	CreatedAt        time.Time `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time `json:"updated_at" db:"updated_at"`
}

// WorkOrderForwardRecord 工单转发记录
type WorkOrderForwardRecord struct {
	ID           uuid.UUID       `json:"id" db:"id"`
	WorkOrderID  uuid.UUID       `json:"work_order_id" db:"work_order_id"`
	UserID       string          `json:"user_id" db:"user_id"`
	CallbackURL  string          `json:"callback_url" db:"callback_url"`
	EventType    string          `json:"event_type" db:"event_type"`
	RequestData  json.RawMessage `json:"request_data" db:"request_data"`   // 🔥 修复：使用json.RawMessage替代[]byte
	ResponseData json.RawMessage `json:"response_data" db:"response_data"` // 🔥 修复：使用json.RawMessage替代[]byte
	HTTPStatus   int             `json:"http_status" db:"http_status"`
	Status       string          `json:"status" db:"status"`
	ErrorMessage string          `json:"error_message" db:"error_message"`
	RetryCount   int             `json:"retry_count" db:"retry_count"`
	RequestAt    *time.Time      `json:"request_at" db:"request_at"`
	ResponseAt   *time.Time      `json:"response_at" db:"response_at"`
	CreatedAt    time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at" db:"updated_at"`
}

// WorkOrderCallbackMetrics 工单回调指标
type WorkOrderCallbackMetrics struct {
	UserID         string    `json:"user_id"`
	TotalCallbacks int64     `json:"total_callbacks"`
	SuccessCount   int64     `json:"success_count"`
	FailureCount   int64     `json:"failure_count"`
	SuccessRate    float64   `json:"success_rate"`
	AvgLatencyMs   int64     `json:"avg_latency_ms"`
	LastCallbackAt time.Time `json:"last_callback_at"`
	CircuitBreaks  int64     `json:"circuit_breaks"`
	RateLimitHits  int64     `json:"rate_limit_hits"`
}

// WorkOrderCallbackEvent 工单回调事件
type WorkOrderCallbackEvent struct {
	Type         string                        `json:"type"`
	WorkOrder    *WorkOrder                    `json:"work_order"`
	CallbackData *UnifiedWorkOrderCallbackData `json:"callback_data"`
	Provider     string                        `json:"provider"`
	Timestamp    time.Time                     `json:"timestamp"`
}

// 工单回调事件类型常量
const (
	WorkOrderEventCreated   = "workorder.created"
	WorkOrderEventReplied   = "workorder.replied"
	WorkOrderEventUpdated   = "workorder.updated"
	WorkOrderEventCompleted = "workorder.completed"
	WorkOrderEventCancelled = "workorder.cancelled"
)

// WorkOrderCallbackRequest 工单回调请求
type WorkOrderCallbackRequest struct {
	WorkOrderID  uuid.UUID                     `json:"work_order_id"`
	CallbackData *UnifiedWorkOrderCallbackData `json:"callback_data"`
	Provider     string                        `json:"provider"`
	EventType    string                        `json:"event_type"`
	ForceForward bool                          `json:"force_forward"` // 强制转发，忽略配置
	AsyncForward bool                          `json:"async_forward"` // 异步转发
}

// WorkOrderCallbackResponse 工单回调响应
type WorkOrderCallbackResponse struct {
	Success     bool                      `json:"success"`
	Message     string                    `json:"message"`
	RequestID   string                    `json:"request_id"`
	ForwardedAt time.Time                 `json:"forwarded_at"`
	Metrics     *WorkOrderCallbackMetrics `json:"metrics,omitempty"`
}

// ValidateWorkOrderCallbackData 验证工单回调数据
func (data *UnifiedWorkOrderCallbackData) Validate() error {
	if data.EventType == "" {
		return fmt.Errorf("事件类型不能为空")
	}
	if data.WorkOrderID == "" {
		return fmt.Errorf("工单ID不能为空")
	}
	if data.EventTime <= 0 {
		return fmt.Errorf("事件时间不能为空")
	}
	return nil
}

// GetEventDisplayName 获取事件显示名称
func (data *UnifiedWorkOrderCallbackData) GetEventDisplayName() string {
	eventNames := map[string]string{
		WorkOrderEventCreated:   "工单创建",
		WorkOrderEventReplied:   "工单回复",
		WorkOrderEventUpdated:   "工单更新",
		WorkOrderEventCompleted: "工单完结",
		WorkOrderEventCancelled: "工单取消",
	}

	if name, exists := eventNames[data.EventType]; exists {
		return name
	}
	return data.EventType
}

// IsRetryableStatus 检查状态是否可重试
func (record *WorkOrderForwardRecord) IsRetryableStatus() bool {
	// HTTP 5xx 错误和网络错误可重试
	return record.HTTPStatus >= 500 || record.HTTPStatus == 0
}

// ShouldRetry 检查是否应该重试
func (record *WorkOrderForwardRecord) ShouldRetry(maxRetries int) bool {
	return record.Status == "failed" &&
		record.RetryCount < maxRetries &&
		record.IsRetryableStatus()
}

// CalculateSuccessRate 计算成功率
func (metrics *WorkOrderCallbackMetrics) CalculateSuccessRate() {
	if metrics.TotalCallbacks > 0 {
		metrics.SuccessRate = float64(metrics.SuccessCount) / float64(metrics.TotalCallbacks)
	} else {
		metrics.SuccessRate = 0
	}
}
