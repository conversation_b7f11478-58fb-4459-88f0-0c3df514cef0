package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// WeightTierPriceCache 重量档位价格缓存表 (优化版 - 支持续重价格和产品信息)
type WeightTierPriceCache struct {
	ID           uuid.UUID       `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	FromProvince string          `gorm:"column:from_province;type:varchar(20);not null" json:"from_province"`
	ToProvince   string          `gorm:"column:to_province;type:varchar(20);not null" json:"to_province"`
	RouteKey     string          `gorm:"column:route_key;type:varchar(50);not null" json:"route_key"`
	Provider     string          `gorm:"column:provider;type:varchar(50);not null" json:"provider"`
	ExpressCode  string          `gorm:"column:express_code;type:varchar(20);not null" json:"express_code"`
	ExpressName  string          `gorm:"column:express_name;type:varchar(50);not null" json:"express_name"`
	WeightKg     int             `gorm:"column:weight_kg;type:integer;not null;check:weight_kg >= 1 AND weight_kg <= 20" json:"weight_kg"`
	Price        decimal.Decimal `gorm:"column:price;type:decimal(10,2);not null" json:"price"`
	Currency     string          `gorm:"column:currency;type:varchar(10);default:'CNY'" json:"currency"`

	// 🚀 新增：续重价格和产品信息
	ContinuedWeightPerKg decimal.Decimal `gorm:"column:continued_weight_per_kg;type:decimal(10,2);default:0" json:"continued_weight_per_kg"`
	ProductCode          string          `gorm:"column:product_code;type:varchar(50)" json:"product_code"`
	ProductName          string          `gorm:"column:product_name;type:varchar(100)" json:"product_name"`
	ChannelID            string          `gorm:"column:channel_id;type:varchar(50)" json:"channel_id"`

	// 服务信息
	EstimatedDays int             `gorm:"column:estimated_days" json:"estimated_days"`
	ServiceType   string          `gorm:"column:service_type;type:varchar(50);default:'standard'" json:"service_type"`
	MaxWeight     decimal.Decimal `gorm:"column:max_weight;type:decimal(8,2)" json:"max_weight"`

	// 缓存管理
	CacheHitCount     int64      `gorm:"column:cache_hit_count;default:0" json:"cache_hit_count"`
	LastHitTime       *time.Time `gorm:"column:last_hit_time" json:"last_hit_time"`
	LastValidatedTime *time.Time `gorm:"column:last_validated_time" json:"last_validated_time"`
	ValidationCount   int64      `gorm:"column:validation_count;default:0" json:"validation_count"`
	CreatedAt         time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time  `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 数据质量
	IsValid bool   `gorm:"column:is_valid;default:true" json:"is_valid"`
	Source  string `gorm:"column:source;type:varchar(50);default:'api'" json:"source"`
}

func (WeightTierPriceCache) TableName() string {
	return "weight_tier_price_cache"
}

// RouteDefinition 路线定义表
type RouteDefinition struct {
	ID           uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	FromProvince string    `gorm:"column:from_province;type:varchar(20);not null" json:"from_province"`
	ToProvince   string    `gorm:"column:to_province;type:varchar(20);not null" json:"to_province"`
	RouteKey     string    `gorm:"column:route_key;type:varchar(50);not null;uniqueIndex" json:"route_key"`
	RouteType    string    `gorm:"column:route_type;type:varchar(20);default:'standard'" json:"route_type"`
	Priority     int       `gorm:"column:priority;default:3" json:"priority"` // 1-高, 2-中, 3-低
	DistanceKm   int       `gorm:"column:distance_km" json:"distance_km"`
	IsActive     bool      `gorm:"column:is_active;default:true" json:"is_active"`
	CreatedAt    time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
}

func (RouteDefinition) TableName() string {
	return "route_definitions"
}

// WeightCacheStatistics 缓存统计表
type WeightCacheStatistics struct {
	ID                 uuid.UUID       `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	Date               time.Time       `gorm:"column:date;type:date;not null" json:"date"`
	Provider           string          `gorm:"column:provider;type:varchar(50);not null" json:"provider"`
	TotalQueries       int64           `gorm:"column:total_queries;default:0" json:"total_queries"`
	CacheHits          int64           `gorm:"column:cache_hits;default:0" json:"cache_hits"`
	CacheMisses        int64           `gorm:"column:cache_misses;default:0" json:"cache_misses"`
	RealtimeQueries    int64           `gorm:"column:realtime_queries;default:0" json:"realtime_queries"`
	PriceValidations   int64           `gorm:"column:price_validations;default:0" json:"price_validations"`
	ValidationFailures int64           `gorm:"column:validation_failures;default:0" json:"validation_failures"`
	AvgResponseTimeMs  decimal.Decimal `gorm:"column:average_response_time_ms;type:decimal(10,2)" json:"avg_response_time_ms"`
	CreatedAt          time.Time       `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`

	// 计算字段
	HitRate          float64 `gorm:"-" json:"hit_rate"`          // 命中率 (%)
	TotalValidations int64   `gorm:"-" json:"total_validations"` // 总验证次数
	ExactMatches     int64   `gorm:"-" json:"exact_matches"`     // 精确匹配次数
	OrdersRejected   int64   `gorm:"-" json:"orders_rejected"`   // 订单拒绝次数
}

func (WeightCacheStatistics) TableName() string {
	return "weight_cache_statistics"
}

// OrderPriceValidation 订单价格验证记录表
type OrderPriceValidation struct {
	ID           uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	OrderID      string    `gorm:"column:order_id;type:varchar(100)" json:"order_id"`
	FromProvince string    `gorm:"column:from_province;type:varchar(20);not null" json:"from_province"`
	ToProvince   string    `gorm:"column:to_province;type:varchar(20);not null" json:"to_province"`
	Provider     string    `gorm:"column:provider;type:varchar(50);not null" json:"provider"`
	ExpressCode  string    `gorm:"column:express_code;type:varchar(20);not null" json:"express_code"`
	WeightKg     int       `gorm:"column:weight_kg;not null" json:"weight_kg"`

	// 价格对比
	CachedPrice     decimal.Decimal `gorm:"column:cached_price;type:decimal(10,2)" json:"cached_price"`
	RealtimePrice   decimal.Decimal `gorm:"column:realtime_price;type:decimal(10,2)" json:"realtime_price"`
	PriceDifference decimal.Decimal `gorm:"column:price_difference;type:decimal(10,2)" json:"price_difference"`
	IsPriceMatch    bool            `gorm:"column:is_price_match" json:"is_price_match"`

	// 验证结果
	ValidationResult string    `gorm:"column:validation_result;type:varchar(20)" json:"validation_result"` // 'pass', 'fail', 'cache_miss'
	ActionTaken      string    `gorm:"column:action_taken;type:varchar(50)" json:"action_taken"`           // 'order_proceed', 'order_reject', 'cache_update'
	CreatedAt        time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
}

func (OrderPriceValidation) TableName() string {
	return "order_price_validations"
}

// WeightTierCacheRequest 重量档位缓存查询请求
type WeightTierCacheRequest struct {
	FromProvince string  `json:"from_province" binding:"required"`
	ToProvince   string  `json:"to_province" binding:"required"`
	Provider     string  `json:"provider" binding:"required"`
	ExpressCode  string  `json:"express_code" binding:"required"`
	Weight       float64 `json:"weight" binding:"required,gt=0"`
}

// WeightTierCacheResponse 重量档位缓存查询响应
type WeightTierCacheResponse struct {
	Success      bool             `json:"success"`
	Code         int              `json:"code"`
	Message      string           `json:"message"`
	Data         *CachedPriceInfo `json:"data,omitempty"`
	Source       string           `json:"source"`        // "cache" or "realtime"
	ResponseTime string           `json:"response_time"` // "<10ms" or "~800ms"
}

// CachedPriceInfo 缓存价格信息 (优化版 - 支持续重价格和产品信息)
type CachedPriceInfo struct {
	Price                decimal.Decimal `json:"price"`
	ContinuedWeightPerKg decimal.Decimal `json:"continued_weight_per_kg"`
	ProductCode          string          `json:"product_code"`
	ProductName          string          `json:"product_name"`
	ChannelID            string          `json:"channel_id"`
	EstimatedDays        int             `json:"estimated_days"`
	WeightKg             int             `json:"weight_kg"`
	ExpressCode          string          `json:"express_code"`
	ExpressName          string          `json:"express_name"`
	Provider             string          `json:"provider"`
	IsFromCache          bool            `json:"is_from_cache"`
	CacheHitTime         *time.Time      `json:"cache_hit_time,omitempty"`
}

// OrderValidationRequest 订单价格验证请求
type OrderValidationRequest struct {
	OrderID      string  `json:"order_id" binding:"required"`
	FromProvince string  `json:"from_province" binding:"required"`
	ToProvince   string  `json:"to_province" binding:"required"`
	Provider     string  `json:"provider" binding:"required"`
	ExpressCode  string  `json:"express_code" binding:"required"`
	Weight       float64 `json:"weight" binding:"required,gt=0"`
	PriceSource  string  `json:"price_source"` // "cache" or "realtime"
}

// OrderValidationResponse 订单价格验证响应
type OrderValidationResponse struct {
	Success          bool            `json:"success"`
	Code             int             `json:"code"`
	Message          string          `json:"message"`
	IsValid          bool            `json:"is_valid"`
	CachedPrice      decimal.Decimal `json:"cached_price"`
	RealtimePrice    decimal.Decimal `json:"realtime_price"`
	PriceDifference  decimal.Decimal `json:"price_difference"`
	ValidationResult string          `json:"validation_result"`
	ActionTaken      string          `json:"action_taken"`
}

// WeightCacheOverview 缓存概览视图
type WeightCacheOverview struct {
	Route                  string  `json:"route"`
	Provider               string  `json:"provider"`
	ExpressCode            string  `json:"expressCode"`
	WeightKg               int     `json:"weightKg"`
	CacheCount             int     `json:"cacheCount"`
	ValidCacheCount        int     `json:"validCacheCount"`
	InvalidCacheCount      int     `json:"invalidCacheCount"`
	LastUpdateTime         string  `json:"lastUpdateTime"`
	CacheHitCount          int64   `json:"cacheHitCount"`
	ValidationCount        int64   `json:"validationCount"`
	ValidationFailureCount int64   `json:"validationFailureCount"`
	AvgPrice               float64 `json:"avgPrice"`
	MinPrice               float64 `json:"minPrice"`
	MaxPrice               float64 `json:"maxPrice"`
	RefreshedAt            string  `json:"refreshed_at,omitempty"` // 数据刷新时间
}

// ProviderGroup 供应商分组数据
type ProviderGroup struct {
	Provider                    string                 `json:"provider"`
	ProviderName                string                 `json:"provider_name"`
	TotalCacheCount             int                    `json:"total_cache_count"`
	ValidCacheCount             int                    `json:"valid_cache_count"`
	InvalidCacheCount           int                    `json:"invalid_cache_count"`
	TotalHitCount               int64                  `json:"total_hit_count"`
	TotalValidationCount        int64                  `json:"total_validation_count"`
	TotalValidationFailureCount int64                  `json:"total_validation_failure_count"`
	AvgPrice                    float64                `json:"avg_price"`
	MinPrice                    float64                `json:"min_price"`
	MaxPrice                    float64                `json:"max_price"`
	LastUpdateTime              string                 `json:"last_update_time"`
	RefreshedAt                 string                 `json:"refreshed_at,omitempty"` // 数据刷新时间
	ExpressCompanies            []*ExpressCompanyCache `json:"express_companies"`
}

// ExpressCompanyCache 快递公司缓存信息
type ExpressCompanyCache struct {
	ExpressCode            string            `json:"express_code"`
	ExpressName            string            `json:"express_name"`
	CacheCount             int               `json:"cache_count"`
	ValidCacheCount        int               `json:"valid_cache_count"`
	InvalidCacheCount      int               `json:"invalid_cache_count"`
	HitCount               int64             `json:"hit_count"`
	ValidationCount        int64             `json:"validation_count"`
	ValidationFailureCount int64             `json:"validation_failure_count"`
	AvgPrice               float64           `json:"avg_price"`
	MinPrice               float64           `json:"min_price"`
	MaxPrice               float64           `json:"max_price"`
	LastUpdateTime         string            `json:"last_update_time"`
	Routes                 []*RouteCacheInfo `json:"routes"`
}

// RouteCacheInfo 路线缓存信息
type RouteCacheInfo struct {
	Route                  string          `json:"route"`
	FromProvince           string          `json:"from_province"`
	ToProvince             string          `json:"to_province"`
	WeightKg               int             `json:"weight_kg"`
	Price                  decimal.Decimal `json:"price"`
	ContinuedWeightPerKg   decimal.Decimal `json:"continued_weight_per_kg"`
	ProductCode            string          `json:"product_code"`
	ProductName            string          `json:"product_name"`
	ChannelID              string          `json:"channel_id"`
	EstimatedDays          int             `json:"estimated_days"`
	CacheHitCount          int64           `json:"cache_hit_count"`
	ValidationCount        int64           `json:"validation_count"`
	ValidationFailureCount int64           `json:"validation_failure_count"`
	IsValid                bool            `json:"is_valid"`
	LastHitTime            *string         `json:"last_hit_time"`
	LastValidatedTime      *string         `json:"last_validated_time"`
	CreatedAt              string          `json:"created_at"`
	UpdatedAt              string          `json:"updated_at"`
	// 新增：失败记录统计
	FailedQueries int64 `json:"failed_queries"` // 失败查询次数
	TotalQueries  int64 `json:"total_queries"`  // 总查询次数

	// 新增：失败原因信息
	FailureReasons []*FailureReasonInfo `json:"failure_reasons,omitempty"` // 失败原因列表
}

// FailureReasonInfo 失败原因信息
type FailureReasonInfo struct {
	ErrorMessage string `json:"error_message"` // 错误信息
	Count        int64  `json:"count"`         // 出现次数
	LastOccurred string `json:"last_occurred"` // 最后出现时间
	Source       string `json:"source"`        // 来源：cache/realtime
}

// CacheDetailRequest 缓存详情查询请求
type CacheDetailRequest struct {
	Provider     string `form:"provider"`
	ExpressCode  string `form:"express_code"`
	Route        string `form:"route"`
	FromProvince string `form:"from_province"`
	ToProvince   string `form:"to_province"`
	WeightKg     *int   `form:"weight_kg"`
	IsValid      *bool  `form:"is_valid"`
	SortBy       string `form:"sort_by"`
	SortOrder    string `form:"sort_order"`
	Page         int    `form:"page"`
	PageSize     int    `form:"page_size"`
}

// CacheDetailResponse 缓存详情查询响应
type CacheDetailResponse struct {
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Records  []*RouteCacheInfo `json:"records"`
		Total    int64             `json:"total"`
		Page     int               `json:"page"`
		PageSize int               `json:"page_size"`
	} `json:"data"`
}

// PriceValidationStats 价格验证统计视图
type PriceValidationStats struct {
	ValidationDate    time.Time       `json:"validation_date"`
	Provider          string          `json:"provider"`
	TotalValidations  int64           `json:"total_validations"`
	PassedValidations int64           `json:"passed_validations"`
	FailedValidations int64           `json:"failed_validations"`
	AvgPriceDiff      decimal.Decimal `json:"avg_price_diff"`
	PassRate          decimal.Decimal `json:"pass_rate"`
}

// CacheStatisticsRequest 缓存统计查询请求
type CacheStatisticsRequest struct {
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
	Provider  string    `json:"provider,omitempty"`
}

// CacheStatisticsResponse 缓存统计查询响应
type CacheStatisticsResponse struct {
	Success bool                 `json:"success"`
	Code    int                  `json:"code"`
	Message string               `json:"message"`
	Data    *CacheStatisticsData `json:"data,omitempty"`
}

// CacheStatisticsData 缓存统计数据
type CacheStatisticsData struct {
	TotalQueries       int64                     `json:"total_queries"`
	CacheHitRate       decimal.Decimal           `json:"cache_hit_rate"`
	ValidationPassRate decimal.Decimal           `json:"validation_pass_rate"`
	AvgResponseTime    decimal.Decimal           `json:"avg_response_time"`
	ProviderStats      map[string]*ProviderCache `json:"provider_stats"`
	DailyStats         []*DailyCacheStats        `json:"daily_stats"`
}

// ProviderCache 供应商缓存统计
type ProviderCache struct {
	TotalQueries          int64           `json:"total_queries"`
	CacheHits             int64           `json:"cache_hits"`
	CacheHitRate          decimal.Decimal `json:"cache_hit_rate"`
	ValidationFailures    int64           `json:"validation_failures"`
	ValidationFailureRate decimal.Decimal `json:"validation_failure_rate"`
}

// DailyCacheStats 每日缓存统计
type DailyCacheStats struct {
	Date            string  `json:"date"`
	TotalQueries    int     `json:"total_queries"`
	CacheHits       int     `json:"cache_hits"`
	CacheMisses     int     `json:"cache_misses"`
	CacheHitRate    float64 `json:"cache_hit_rate"`
	AvgResponseTime float64 `json:"avg_response_time"`
}

// CacheOverviewRequest 缓存概览分页查询请求
type CacheOverviewRequest struct {
	Page        int    `form:"page" json:"page"`                 // 页码，从1开始
	PageSize    int    `form:"page_size" json:"page_size"`       // 每页大小
	Provider    string `form:"provider" json:"provider"`         // 供应商筛选
	ExpressCode string `form:"express_code" json:"express_code"` // 快递公司筛选
	Route       string `form:"route" json:"route"`               // 路线筛选
}

// CacheOverviewResponse 缓存概览分页查询响应
type CacheOverviewResponse struct {
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Data       []*WeightCacheOverview `json:"data"`        // 数据列表
		TotalCount int64                  `json:"total_count"` // 总记录数
		Page       int                    `json:"page"`        // 当前页码
		PageSize   int                    `json:"page_size"`   // 每页大小
		TotalPages int                    `json:"total_pages"` // 总页数
	} `json:"data"`
}

// QuickStatsResponse 快速统计响应
type QuickStatsResponse struct {
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		TotalProviders    int     `json:"total_providers"`     // 总供应商数
		TotalCacheEntries int64   `json:"total_cache_entries"` // 总缓存条目数
		ValidCacheCount   int64   `json:"valid_cache_count"`   // 有效缓存数量
		CacheHitRate      float64 `json:"cache_hit_rate"`      // 缓存命中率
		LastUpdated       string  `json:"last_updated"`        // 最后更新时间
	} `json:"data"`
}
