package model

import "time"

// UnifiedAPIResponse 统一API响应格式（开放平台标准）
type UnifiedAPIResponse struct {
	Success   bool        `json:"success"`         // 请求是否成功
	Code      int         `json:"code"`            // 状态码
	Message   string      `json:"message"`         // 响应消息
	Data      interface{} `json:"data,omitempty"`  // 响应数据
	Error     *APIError   `json:"error,omitempty"` // 错误信息
	Meta      *APIMeta    `json:"meta,omitempty"`  // 元数据信息
	Timestamp int64       `json:"timestamp"`       // 响应时间戳
	RequestID string      `json:"request_id"`      // 请求ID
}

// APIError 错误信息结构
type APIError struct {
	Code        string            `json:"code"`                  // 错误代码
	Message     string            `json:"message"`               // 错误消息
	Details     string            `json:"details,omitempty"`     // 错误详情
	Field       string            `json:"field,omitempty"`       // 错误字段
	Suggestions []string          `json:"suggestions,omitempty"` // 解决建议
	MoreInfo    string            `json:"more_info,omitempty"`   // 更多信息链接
	Context     map[string]string `json:"context,omitempty"`     // 错误上下文
}

// BalanceInsufficientError 余额不足错误详情 - 🔥 新增：优化用户体验
type BalanceInsufficientError struct {
	CurrentBalance  float64 `json:"current_balance"`   // 当前余额
	RequiredAmount  float64 `json:"required_amount"`   // 所需金额
	ShortageAmount  float64 `json:"shortage_amount"`   // 不足金额
	RechargeURL     string  `json:"recharge_url"`      // 充值链接
	RechargeGuide   string  `json:"recharge_guide"`    // 充值指引
	SuggestedAmount float64 `json:"suggested_amount"`  // 建议充值金额
	UserFriendlyMsg string  `json:"user_friendly_msg"` // 用户友好提示
}

// APIMeta 元数据信息
type APIMeta struct {
	Version      string            `json:"version"`              // API版本
	ResponseTime int               `json:"response_time_ms"`     // 响应时间（毫秒）
	CacheHit     bool              `json:"cache_hit"`            // 是否缓存命中
	RateLimit    *RateLimitInfo    `json:"rate_limit,omitempty"` // 限流信息
	Pagination   *PaginationInfo   `json:"pagination,omitempty"` // 分页信息
	Debug        map[string]string `json:"debug,omitempty"`      // 调试信息（仅开发环境）
}

// RateLimitInfo 限流信息
type RateLimitInfo struct {
	Limit     int   `json:"limit"`     // 限制次数
	Remaining int   `json:"remaining"` // 剩余次数
	Reset     int64 `json:"reset"`     // 重置时间戳
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Page       int  `json:"page"`        // 当前页码
	PageSize   int  `json:"page_size"`   // 每页大小
	Total      int  `json:"total"`       // 总记录数
	TotalPages int  `json:"total_pages"` // 总页数
	HasNext    bool `json:"has_next"`    // 是否有下一页
	HasPrev    bool `json:"has_prev"`    // 是否有上一页
}

// PriceQueryResponse 价格查询统一响应
type PriceQueryResponse struct {
	Success   bool            `json:"success"`
	Code      int             `json:"code"`
	Message   string          `json:"message"`
	Data      *PriceQueryData `json:"data,omitempty"`
	Error     *APIError       `json:"error,omitempty"`
	Meta      *APIMeta        `json:"meta,omitempty"`
	Timestamp int64           `json:"timestamp"`
	RequestID string          `json:"request_id"`
}

// PriceQueryData 价格查询数据
type PriceQueryData struct {
	Prices      []StandardizedPrice `json:"prices"`                // 价格列表
	Summary     *PriceSummary       `json:"summary"`               // 价格汇总
	Suggestions *PriceSuggestions   `json:"suggestions,omitempty"` // 推荐建议
}

// PriceSummary 价格汇总信息
type PriceSummary struct {
	TotalCount    int     `json:"total_count"`    // 总价格数量
	LowestPrice   float64 `json:"lowest_price"`   // 最低价格
	HighestPrice  float64 `json:"highest_price"`  // 最高价格
	AveragePrice  float64 `json:"average_price"`  // 平均价格
	RecommendedID string  `json:"recommended_id"` // 推荐选项ID
}

// PriceSuggestions 价格推荐建议
type PriceSuggestions struct {
	BestValue      *StandardizedPrice `json:"best_value,omitempty"`      // 性价比最高
	Fastest        *StandardizedPrice `json:"fastest,omitempty"`         // 最快送达
	MostReliable   *StandardizedPrice `json:"most_reliable,omitempty"`   // 最可靠
	EarliestPickup *StandardizedPrice `json:"earliest_pickup,omitempty"` // 最早取件
}

// PickupTimeQueryResponse 取件时间查询统一响应
type PickupTimeQueryResponse struct {
	Success   bool                 `json:"success"`
	Code      int                  `json:"code"`
	Message   string               `json:"message"`
	Data      *PickupTimeQueryData `json:"data,omitempty"`
	Error     *APIError            `json:"error,omitempty"`
	Meta      *APIMeta             `json:"meta,omitempty"`
	Timestamp int64                `json:"timestamp"`
	RequestID string               `json:"request_id"`
}

// PickupTimeQueryData 取件时间查询数据
type PickupTimeQueryData struct {
	ExpressCode  string            `json:"express_code"` // 快递公司代码
	ExpressName  string            `json:"express_name"` // 快递公司名称
	Provider     string            `json:"provider"`     // 供应商
	Location     *LocationInfo     `json:"location"`     // 地址信息
	PickupInfo   *PickupTimeInfo   `json:"pickup_info"`  // 取件时间信息
	Availability *AvailabilityInfo `json:"availability"` // 可用性信息
}

// LocationInfo 地址信息
type LocationInfo struct {
	Province string `json:"province"` // 省份
	City     string `json:"city"`     // 城市
	District string `json:"district"` // 区县
}

// AvailabilityInfo 可用性信息
type AvailabilityInfo struct {
	IsAvailable       bool     `json:"is_available"`                  // 是否可用
	UnavailableReason string   `json:"unavailable_reason,omitempty"`  // 不可用原因
	NextAvailableTime string   `json:"next_available_time,omitempty"` // 下次可用时间
	Alternatives      []string `json:"alternatives,omitempty"`        // 替代方案
}

// 常量定义 - API状态码
const (
	// 成功状态码
	APICodeSuccess = 200

	// 客户端错误状态码
	APICodeBadRequest       = 400 // 请求参数错误
	APICodeUnauthorized     = 401 // 未授权
	APICodeForbidden        = 403 // 禁止访问
	APICodeNotFound         = 404 // 资源不存在
	APICodeMethodNotAllowed = 405 // 方法不允许
	APICodeTooManyRequests  = 429 // 请求过于频繁
	APICodeValidationFailed = 422 // 参数验证失败

	// 服务器错误状态码
	APICodeInternalError       = 500 // 服务器内部错误
	APICodeServiceUnavailable  = 503 // 服务不可用
	APICodeTimeout             = 504 // 请求超时
	APICodeInsufficientStorage = 507 // 存储空间不足

	// 业务错误状态码
	APICodeBusinessError = 600 // 业务逻辑错误
	APICodeDataNotFound  = 601 // 数据不存在
	APICodeConfigError   = 602 // 配置错误
	APICodeProviderError = 603 // 供应商错误
	APICodeCacheError    = 604 // 缓存错误
)

// 常量定义 - 错误代码
const (
	// 通用错误代码
	ErrorCodeInvalidRequest     = "INVALID_REQUEST"
	ErrorCodeMissingParameter   = "MISSING_PARAMETER"
	ErrorCodeInvalidParameter   = "INVALID_PARAMETER"
	ErrorCodeUnauthorized       = "UNAUTHORIZED"
	ErrorCodeForbidden          = "FORBIDDEN"
	ErrorCodeNotFound           = "NOT_FOUND"
	ErrorCodeTooManyRequests    = "TOO_MANY_REQUESTS"
	ErrorCodeInternalError      = "INTERNAL_ERROR"
	ErrorCodeServiceUnavailable = "SERVICE_UNAVAILABLE"

	// 业务错误代码
	ErrorCodeExpressNotSupported  = "EXPRESS_NOT_SUPPORTED"
	ErrorCodeProviderNotAvailable = "PROVIDER_NOT_AVAILABLE"
	ErrorCodeLocationNotSupported = "LOCATION_NOT_SUPPORTED"
	ErrorCodePickupTimeInvalid    = "PICKUP_TIME_INVALID"
	ErrorCodeBusinessHoursClosed  = "BUSINESS_HOURS_CLOSED"
	ErrorCodeConfigNotFound       = "CONFIG_NOT_FOUND"
	ErrorCodeCacheExpired         = "CACHE_EXPIRED"
)

// NewUnifiedAPIResponse 创建统一API响应
func NewUnifiedAPIResponse(success bool, code int, message string, data interface{}) *UnifiedAPIResponse {
	return &UnifiedAPIResponse{
		Success:   success,
		Code:      code,
		Message:   message,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// NewAPIError 创建API错误
func NewAPIError(code, message, details string) *APIError {
	return &APIError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// NewAPIMeta 创建API元数据
func NewAPIMeta(version string, responseTime int, cacheHit bool) *APIMeta {
	return &APIMeta{
		Version:      version,
		ResponseTime: responseTime,
		CacheHit:     cacheHit,
	}
}

// WithError 添加错误信息
func (r *UnifiedAPIResponse) WithError(err *APIError) *UnifiedAPIResponse {
	r.Error = err
	r.Success = false
	return r
}

// WithMeta 添加元数据
func (r *UnifiedAPIResponse) WithMeta(meta *APIMeta) *UnifiedAPIResponse {
	r.Meta = meta
	return r
}

// WithRequestID 添加请求ID
func (r *UnifiedAPIResponse) WithRequestID(requestID string) *UnifiedAPIResponse {
	r.RequestID = requestID
	return r
}
