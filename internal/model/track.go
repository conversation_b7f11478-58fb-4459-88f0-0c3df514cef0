package model

import "time"

// TrackQueryRequest 物流轨迹查询请求
type TrackQueryRequest struct {
	OrderNo    string `json:"order_no"`    // 平台订单号
	TrackingNo string `json:"tracking_no"` // 运单号
	Phone      string `json:"phone"`       // 收、寄件人的电话号码（手机和固定电话均可，只能填写一个）
	From       string `json:"from"`        // 出发地城市
	To         string `json:"to"`          // 目的地城市
	PollToken  string `json:"poll_token"`  // 查询密钥，仅通过商家寄件下单后调用实时快递查询时入参该字段可免扣费查询
}

// TrackQueryResponse 物流轨迹查询响应
type TrackQueryResponse struct {
	Success bool       `json:"success"`        // 是否成功
	Code    int        `json:"code"`           // 状态码
	Message string     `json:"message"`        // 消息
	Data    *TrackInfo `json:"data,omitempty"` // 物流轨迹信息
}

// TrackInfo 物流轨迹信息
type TrackInfo struct {
	OrderNo     string       `json:"order_no"`             // 平台订单号
	TrackingNo  string       `json:"tracking_no"`          // 运单号
	ExpressType string       `json:"express_type"`         // 快递类型
	State       string       `json:"state"`                // 物流状态
	StateDesc   string       `json:"state_desc"`           // 物流状态描述
	IsCheck     string       `json:"is_check"`             // 是否签收
	Tracks      []*TrackItem `json:"tracks"`               // 物流轨迹
	RouteInfo   *RouteInfo   `json:"route_info,omitempty"` // 路由信息
}

// TrackItem 物流轨迹项
type TrackItem struct {
	Context    string    `json:"context"`     // 内容
	Time       time.Time `json:"time"`        // 时间
	Status     string    `json:"status"`      // 状态
	StatusCode string    `json:"status_code"` // 状态码
	AreaCode   string    `json:"area_code"`   // 区域编码
	AreaName   string    `json:"area_name"`   // 区域名称
	Location   string    `json:"location"`    // 位置
}

// RouteInfo 路由信息
type RouteInfo struct {
	From *RouteCity `json:"from,omitempty"` // 出发地
	Cur  *RouteCity `json:"cur,omitempty"`  // 当前位置
	To   *RouteCity `json:"to,omitempty"`   // 目的地
}

// RouteCity 路由城市
type RouteCity struct {
	Number string `json:"number"` // 编码
	Name   string `json:"name"`   // 名称
}

// 物流状态映射
var StateMap = map[string]string{
	"0":    "在途",
	"1":    "揽收",
	"2":    "疑难",
	"3":    "签收",
	"4":    "退签",
	"5":    "派件",
	"6":    "退回",
	"7":    "转投",
	"8":    "清关",
	"10":   "待清关",
	"11":   "清关中",
	"12":   "已清关",
	"13":   "清关异常",
	"14":   "拒签",
	"101":  "已下单",
	"102":  "待揽收",
	"103":  "已揽收",
	"1001": "到达派件城市",
	"1002": "干线",
	"1003": "转递",
	"501":  "投柜或驿站",
	"301":  "本人签收",
	"302":  "派件异常后签收",
	"303":  "代签",
	"304":  "投柜或站签收",
	"401":  "已销单",
	"201":  "超时未签收",
	"202":  "超时未更新",
	"203":  "拒收",
	"204":  "派件异常",
	"205":  "柜或驿站超时未取",
	"206":  "无法联系",
	"207":  "超区",
	"208":  "滞留",
	"209":  "破损",
	"210":  "销单",
}
