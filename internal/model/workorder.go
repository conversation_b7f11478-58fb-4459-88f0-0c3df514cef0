package model

import (
	"time"

	"github.com/google/uuid"
)

// WorkOrder 工单模型
type WorkOrder struct {
	ID                   uuid.UUID       `json:"id" db:"id"`
	UserID               string          `json:"user_id" db:"user_id"`
	CustomerWorkOrderID  *string         `json:"customer_work_order_id,omitempty" db:"customer_work_order_id"` // 🔥 新增：用户自定义工单ID
	OrderNo              *string         `json:"order_no,omitempty" db:"order_no"`
	TrackingNo           *string         `json:"tracking_no,omitempty" db:"tracking_no"`
	Provider             string          `json:"provider" db:"provider"`
	ProviderWorkOrderID  *string         `json:"provider_work_order_id,omitempty" db:"provider_work_order_id"`
	WorkOrderType        int             `json:"work_order_type" db:"work_order_type"`
	Title                string          `json:"title" db:"title"`
	Content              string          `json:"content" db:"content"`
	Status               WorkOrderStatus `json:"status" db:"status"`
	Priority             int             `json:"priority" db:"priority"`
	Version              int             `json:"version" db:"version"` // 🔥 新增：乐观锁版本字段
	FeedbackWeight       *float64        `json:"feedback_weight,omitempty" db:"feedback_weight"`
	GoodsValue           *float64        `json:"goods_value,omitempty" db:"goods_value"`
	OverweightAmount     *float64        `json:"overweight_amount,omitempty" db:"overweight_amount"`
	CallbackURL          *string         `json:"callback_url,omitempty" db:"callback_url"`
	MessageCallbackURL   *string         `json:"message_callback_url,omitempty" db:"message_callback_url"`
	RawRequestData       *string         `json:"raw_request_data,omitempty" db:"raw_request_data"`
	ProviderResponseData *string         `json:"provider_response_data,omitempty" db:"provider_response_data"`
	CreatedAt            time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt            time.Time       `json:"updated_at" db:"updated_at"`
	CompletedAt          *time.Time      `json:"completed_at,omitempty" db:"completed_at"`
	DeletedAt            *time.Time      `json:"deleted_at,omitempty" db:"deleted_at"` // 🔥 新增：软删除字段

	// 关联数据（不存储在数据库中）
	Replies     []WorkOrderReply      `json:"replies,omitempty" db:"-"`
	Attachments []WorkOrderAttachment `json:"attachments,omitempty" db:"-"`
	TypeName    string                `json:"type_name,omitempty" db:"-"`
	StatusName  string                `json:"status_name,omitempty" db:"-"`
}

// WorkOrderReply 工单回复模型
type WorkOrderReply struct {
	ID          uuid.UUID `json:"id" db:"id"`
	WorkOrderID uuid.UUID `json:"work_order_id" db:"work_order_id"`
	ReplyType   int       `json:"reply_type" db:"reply_type"`
	Committer   *string   `json:"committer,omitempty" db:"committer"`
	Content     string    `json:"content" db:"content"`
	RawData     *string   `json:"raw_data,omitempty" db:"raw_data"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`

	// 关联数据
	Attachments []WorkOrderAttachment `json:"attachments,omitempty" db:"-"`
}

// WorkOrderAttachment 工单附件模型
type WorkOrderAttachment struct {
	ID          uuid.UUID  `json:"id" db:"id"`
	WorkOrderID *uuid.UUID `json:"work_order_id,omitempty" db:"work_order_id"`
	ReplyID     *uuid.UUID `json:"reply_id,omitempty" db:"reply_id"`
	FileName    string     `json:"file_name" db:"file_name"`
	FileURL     string     `json:"file_url" db:"file_url"`
	FileType    *string    `json:"file_type,omitempty" db:"file_type"`
	FileSize    *int64     `json:"file_size,omitempty" db:"file_size"`
	UploadType  int        `json:"upload_type" db:"upload_type"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
}

// WorkOrderTypeMapping 工单类型映射模型
type WorkOrderTypeMapping struct {
	ID           int       `json:"id" db:"id"`
	UnifiedType  int       `json:"unified_type" db:"unified_type"`
	UnifiedName  string    `json:"unified_name" db:"unified_name"`
	Provider     string    `json:"provider" db:"provider"`
	ProviderType *int      `json:"provider_type,omitempty" db:"provider_type"`
	ProviderName *string   `json:"provider_name,omitempty" db:"provider_name"`
	IsSupported  bool      `json:"is_supported" db:"is_supported"`
	Description  *string   `json:"description,omitempty" db:"description"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// WorkOrderStatusMapping 工单状态映射模型
type WorkOrderStatusMapping struct {
	ID             int       `json:"id" db:"id"`
	UnifiedStatus  int       `json:"unified_status" db:"unified_status"`
	UnifiedName    string    `json:"unified_name" db:"unified_name"`
	Provider       string    `json:"provider" db:"provider"`
	ProviderStatus *int      `json:"provider_status,omitempty" db:"provider_status"`
	ProviderName   *string   `json:"provider_name,omitempty" db:"provider_name"`
	Description    *string   `json:"description,omitempty" db:"description"`
	CreatedAt      time.Time `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time `json:"updated_at" db:"updated_at"`
}

// WorkOrderStatus 工单状态类型
type WorkOrderStatus int

// 工单状态常量
const (
	WorkOrderStatusPending    WorkOrderStatus = 1 // 待处理
	WorkOrderStatusProcessing WorkOrderStatus = 2 // 处理中
	WorkOrderStatusReplied    WorkOrderStatus = 3 // 已回复
	WorkOrderStatusCompleted  WorkOrderStatus = 4 // 已完结
)

// String 返回状态名称
func (s WorkOrderStatus) String() string {
	switch s {
	case WorkOrderStatusPending:
		return "待处理"
	case WorkOrderStatusProcessing:
		return "处理中"
	case WorkOrderStatusReplied:
		return "已回复"
	case WorkOrderStatusCompleted:
		return "已完结"
	default:
		return "未知状态"
	}
}

// IsValid 验证状态是否有效
func (s WorkOrderStatus) IsValid() bool {
	return s >= WorkOrderStatusPending && s <= WorkOrderStatusCompleted
}

// CanTransitionTo 检查是否可以转换到目标状态
func (s WorkOrderStatus) CanTransitionTo(target WorkOrderStatus) bool {
	validTransitions := map[WorkOrderStatus][]WorkOrderStatus{
		WorkOrderStatusPending: {
			WorkOrderStatusProcessing,
			WorkOrderStatusCompleted, // 可以直接完结
		},
		WorkOrderStatusProcessing: {
			WorkOrderStatusReplied,
			WorkOrderStatusCompleted,
		},
		WorkOrderStatusReplied: {
			WorkOrderStatusProcessing, // 可以重新处理
			WorkOrderStatusCompleted,
		},
		// 已完结状态不能转换到其他状态
		WorkOrderStatusCompleted: {},
	}

	allowedTargets, exists := validTransitions[s]
	if !exists {
		return false
	}

	for _, allowed := range allowedTargets {
		if allowed == target {
			return true
		}
	}
	return false
}

// 工单回复类型常量
const (
	ReplyTypeUser     = 1 // 用户回复
	ReplyTypeProvider = 2 // 供应商回复
	ReplyTypeSystem   = 3 // 系统回复
)

// 工单优先级常量
const (
	PriorityHigh   = 1 // 高优先级
	PriorityMedium = 2 // 中优先级
	PriorityLow    = 3 // 低优先级
)

// 附件上传类型常量
const (
	UploadTypeWorkOrder = 1 // 工单创建时上传
	UploadTypeReply     = 2 // 工单回复时上传
)

// 统一工单类型常量（统一化改造：调整为6种核心类型）
const (
	WorkOrderTypePickupReminder   = 1  // 催取件
	WorkOrderTypeWeightException  = 2  // 重量异常
	WorkOrderTypeDeliveryReminder = 12 // 催派送
	WorkOrderTypeLogisticsStall   = 16 // 物流停滞
	WorkOrderTypeReassignCourier  = 17 // 重新分配快递员
	WorkOrderTypeCancelOrder      = 19 // 取消订单
)

// 工单类型名称映射
var WorkOrderTypeNames = map[int]string{
	WorkOrderTypePickupReminder:   "催取件",
	WorkOrderTypeWeightException:  "重量异常",
	WorkOrderTypeDeliveryReminder: "催派送",
	WorkOrderTypeLogisticsStall:   "物流停滞",
	WorkOrderTypeReassignCourier:  "重新分配快递员",
	WorkOrderTypeCancelOrder:      "取消订单",
}

// 工单类型描述映射
var WorkOrderTypeDescriptions = map[int]string{
	WorkOrderTypePickupReminder:   "催促快递员取件",
	WorkOrderTypeWeightException:  "反馈包裹重量异常",
	WorkOrderTypeDeliveryReminder: "催促快递员派送",
	WorkOrderTypeLogisticsStall:   "物流信息长时间未更新",
	WorkOrderTypeReassignCourier:  "申请重新分配快递员",
	WorkOrderTypeCancelOrder:      "取消快递订单",
}

// 获取支持的工单类型列表
func GetSupportedWorkOrderTypes() []int {
	return []int{
		WorkOrderTypePickupReminder,
		WorkOrderTypeWeightException,
		WorkOrderTypeDeliveryReminder,
		WorkOrderTypeLogisticsStall,
		WorkOrderTypeReassignCourier,
		WorkOrderTypeCancelOrder,
	}
}

// 验证工单类型是否有效
func IsValidWorkOrderType(workOrderType int) bool {
	_, exists := WorkOrderTypeNames[workOrderType]
	return exists
}

// CreateWorkOrderRequest 创建工单请求（统一使用智能创建方式）
type CreateWorkOrderRequest struct {
	// 必填字段
	WorkOrderType int    `json:"work_order_type" binding:"required"` // 6种核心工单类型之一
	Content       string `json:"content" binding:"required"`         // 问题描述

	// 订单标识（二选一即可）
	OrderNo    *string `json:"order_no,omitempty"`    // 客户订单号
	TrackingNo *string `json:"tracking_no,omitempty"` // 运单号

	// 🔥 新增：用户自定义工单ID（可选）
	CustomerWorkOrderID *string `json:"customer_work_order_id,omitempty"` // 用户自定义工单ID

	// 可选字段（根据工单类型自动显示）
	FeedbackWeight   *float64 `json:"feedback_weight,omitempty"`   // 反馈重量（重量异常时使用）
	GoodsValue       *float64 `json:"goods_value,omitempty"`       // 商品价值
	OverweightAmount *float64 `json:"overweight_amount,omitempty"` // 超重金额
	AttachmentURLs   []string `json:"attachment_urls,omitempty"`   // 附件URL列表

	// 系统自动处理字段（用户无需填写）
	Provider             string                 `json:"-"` // 系统自动识别
	CallbackURL          *string                `json:"-"` // 系统自动配置
	MessageCallbackURL   *string                `json:"-"` // 系统自动配置
	ProviderSpecificData map[string]interface{} `json:"-"` // 系统自动处理
}

// 类型别名：为了保持向后兼容性
type SmartCreateWorkOrderRequest = CreateWorkOrderRequest

// WorkOrderResponse 工单响应
type WorkOrderResponse struct {
	Success bool        `json:"success"`
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// WorkOrderListRequest 工单列表请求
type WorkOrderListRequest struct {
	Page          int     `json:"page" form:"page"`
	PageSize      int     `json:"page_size" form:"page_size"`
	Provider      *string `json:"provider,omitempty" form:"provider"`
	Status        *int    `json:"status,omitempty" form:"status"`
	WorkOrderType *int    `json:"work_order_type,omitempty" form:"work_order_type"`
	OrderNo       *string `json:"order_no,omitempty" form:"order_no"`
	TrackingNo    *string `json:"tracking_no,omitempty" form:"tracking_no"`
	StartDate     *string `json:"start_date,omitempty" form:"start_date"`
	EndDate       *string `json:"end_date,omitempty" form:"end_date"`
}

// WorkOrderListResponse 工单列表响应
type WorkOrderListResponse struct {
	Items      []WorkOrder `json:"items"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
}

// ReplyWorkOrderRequest 回复工单请求
type ReplyWorkOrderRequest struct {
	Content        string   `json:"content" binding:"required"`
	AttachmentURLs []string `json:"attachment_urls,omitempty"`
}

// UploadAttachmentResponse 上传附件响应
type UploadAttachmentResponse struct {
	FileURL  string `json:"file_url"`
	FileName string `json:"file_name"`
	FileSize int64  `json:"file_size"`
}
