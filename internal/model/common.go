package model

import (
	"math/rand"
)

// SenderInfo 寄件人信息
type SenderInfo struct {
	Name     string `json:"name"`     // 寄件人姓名
	Province string `json:"province"` // 寄件省
	City     string `json:"city"`     // 寄件市
	District string `json:"district"` // 寄件区/县
	Address  string `json:"address"`  // 详细地址
	Mobile   string `json:"mobile"`   // 手机号
	Tel      string `json:"tel"`      // 电话号码
}

// ReceiverInfo 收件人信息
type ReceiverInfo struct {
	Name     string `json:"name"`     // 收件人姓名
	Province string `json:"province"` // 收件省
	City     string `json:"city"`     // 收件市
	District string `json:"district"` // 收件区/县
	Address  string `json:"address"`  // 详细地址
	Mobile   string `json:"mobile"`   // 手机号
	Tel      string `json:"tel"`      // 电话号码
}

// PackageInfo 包裹信息
type PackageInfo struct {
	Weight      float64 `json:"weight"`       // 重量(kg)
	Quantity    int     `json:"quantity"`     // 包裹数量
	GoodsName   string  `json:"goods_name"`   // 物品名称
	Length      float64 `json:"length"`       // 长(cm)
	Width       float64 `json:"width"`        // 宽(cm)
	Height      float64 `json:"height"`       // 高(cm)
	Volume      float64 `json:"volume"`       // 体积(m³)
	InsureValue float64 `json:"insure_value"` // 保价金额
	Remark      string  `json:"remark"`       // 备注
}

// StandardAddressInfo 标准地址信息
type StandardAddressInfo struct {
	Province string `json:"province"` // 省份
	City     string `json:"city"`     // 城市
	District string `json:"district"` // 区县
	Address  string `json:"address"`  // 详细地址
}

// APIResponse 通用API响应
type APIResponse struct {
	Success bool        `json:"success"`        // 是否成功
	Code    int         `json:"code"`           // 状态码
	Message string      `json:"message"`        // 消息
	Data    interface{} `json:"data,omitempty"` // 数据
}

// ValidationError 验证错误
type ValidationError struct {
	Message string
}

// Error 实现error接口
func (e ValidationError) Error() string {
	return e.Message
}

// NewValidationError 创建验证错误
func NewValidationError(message string) ValidationError {
	return ValidationError{
		Message: message,
	}
}

// GenerateRandomString 生成随机字符串
func GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// CalculateChargedWeight 计算计费重量（含体积重量）
// 根据包裹的实际重量和体积，计算最终的计费重量
// 四通一达等使用8000系数，德邦使用6000系数
func (pkg *PackageInfo) CalculateChargedWeight() float64 {
	chargedWeight := pkg.Weight
	
	// 如果有体积信息，计算体积重量
	if pkg.Length > 0 && pkg.Width > 0 && pkg.Height > 0 {
		// 计算体积重量（cm³ / 8000）- 四通一达标准
		volumeWeight := float64(pkg.Length*pkg.Width*pkg.Height) / 8000.0
		
		// 取体积重量和实际重量的较大值
		if volumeWeight > chargedWeight {
			chargedWeight = volumeWeight
		}
	}
	
	return chargedWeight
}

// CalculateVolumeWeight 计算体积重量
func (pkg *PackageInfo) CalculateVolumeWeight() float64 {
	if pkg.Length <= 0 || pkg.Width <= 0 || pkg.Height <= 0 {
		return 0
	}
	// 默认使用8000系数（四通一达标准）
	return float64(pkg.Length*pkg.Width*pkg.Height) / 8000.0
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Success bool   `json:"success"` // 是否成功
	Code    int    `json:"code"`    // 状态码
	Message string `json:"message"` // 消息
}

// StatusCode 状态码
const (
	StatusSuccess             = 200 // 成功
	StatusBadRequest          = 400 // 请求参数错误
	StatusUnauthorized        = 401 // 未授权
	StatusForbidden           = 403 // 禁止访问
	StatusNotFound            = 404 // 资源不存在
	StatusMethodNotAllowed    = 405 // 方法不允许
	StatusConflict            = 409 // 冲突（价格不一致）
	StatusInternalServerError = 500 // 服务器内部错误
	StatusServiceUnavailable  = 503 // 服务不可用
)

// ExpressCompany 快递公司
const (
	ExpressKuaidi100 = "kuaidi100" // 快递100
	ExpressYida      = "yida"      // 易达
	ExpressYuntong   = "yuntong"   // 云通
)

// ExpressCode 快递公司代码
const (
	ExpressCodeSF  = "SF"  // 顺丰
	ExpressCodeYTO = "YTO" // 圆通
	ExpressCodeSTO = "STO" // 申通
	ExpressCodeYD  = "YD"  // 韵达
	ExpressCodeZTO = "ZTO" // 中通
	ExpressCodeJD  = "JD"  // 京东
	ExpressCodeDB  = "DB"  // 德邦
	ExpressCodeJT  = "JT"  // 极兔
)

// PayMethod 支付方式
const (
	PayMethodSender   = 0 // 寄付
	PayMethodReceiver = 1 // 到付
	PayMethodMonthly  = 2 // 月结
)

// === 特定错误类型定义 ===

// ProviderNotSupportedError 供应商不支持错误
type ProviderNotSupportedError struct {
	Provider string
	Message  string
}

// Error 实现error接口
func (e *ProviderNotSupportedError) Error() string {
	return e.Message
}

// CapacityError 运力异常错误
type CapacityError struct {
	Provider string
	Message  string
}

// Error 实现error接口
func (e *CapacityError) Error() string {
	return e.Message
}

// NetworkError 网络连接错误
type NetworkError struct {
	Provider string
	Message  string
}

// Error 实现error接口
func (e *NetworkError) Error() string {
	return e.Message
}
