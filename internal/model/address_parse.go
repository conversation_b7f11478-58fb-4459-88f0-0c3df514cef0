package model

import (
	"time"
)

// AddressParseRequest 地址解析请求
type AddressParseRequest struct {
	Text        string  `json:"text" binding:"required" example:"李健\n17099916606\n湖南省永州市冷水滩区珊瑚街道 园丁山庄8栋19号门面妈妈驿站"`
	AddressType int     `json:"addressType" example:"0"`
	QueryType   int     `json:"queryType" example:"0"`
	Lat         float64 `json:"lat" example:"30.0"`
	Lng         float64 `json:"lng" example:"110.0"`
}

// AddressParseResponse 地址解析响应
type AddressParseResponse struct {
	Code    int               `json:"code"`
	Success bool              `json:"success"`
	Message string            `json:"message"`
	Data    *AddressParseData `json:"data"`
}

// AddressParseData 地址解析数据
type AddressParseData struct {
	AddressInfo *AddressInfo `json:"addressInfo"`
	Name        string       `json:"name"`        // 姓名（在根级别）
	MobilePhone string       `json:"mobilePhone"` // 手机号（在根级别）
	Phone       string       `json:"phone"`       // 电话（在根级别）
	Text        string       `json:"text"`        // 原始文本
}

// AddressInfo 地址信息
type AddressInfo struct {
	// 基本信息
	Name   string `json:"name"`   // 姓名
	Mobile string `json:"mobile"` // 手机号
	Phone  string `json:"phone"`  // 电话号码

	// 地址信息
	ProvinceCode string `json:"provinceCode"` // 省份代码
	ProvinceName string `json:"provinceName"` // 省份名称
	CityCode     string `json:"cityCode"`     // 城市代码
	CityName     string `json:"cityName"`     // 城市名称
	DistrictCode string `json:"districtCode"` // 区县代码
	DistrictName string `json:"districtName"` // 区县名称
	TownCode     string `json:"townCode"`     // 乡镇代码
	TownName     string `json:"townName"`     // 乡镇名称

	// 详细地址
	DetailAddress string `json:"detailAddress"` // 详细地址
	FullAddress   string `json:"fullAddress"`   // 完整地址

	// 坐标信息
	Latitude  float64 `json:"latitude"`  // 纬度
	Longitude float64 `json:"longitude"` // 经度

	// 置信度
	Confidence float64 `json:"confidence"` // 识别置信度
}

// BatchAddressParseRequest 批量地址解析请求
type BatchAddressParseRequest struct {
	Addresses   []AddressParseRequest `json:"addresses" binding:"required,min=1,max=100"`
	Concurrency int                   `json:"concurrency" example:"5"` // 并发数，默认5
}

// BatchAddressParseResponse 批量地址解析响应
type BatchAddressParseResponse struct {
	Code    int                    `json:"code"`
	Success bool                   `json:"success"`
	Message string                 `json:"message"`
	Data    *BatchAddressParseData `json:"data"`
}

// BatchAddressParseData 批量地址解析数据
type BatchAddressParseData struct {
	Results     []AddressParseResult `json:"results"`
	Statistics  *ParseStatistics     `json:"statistics"`
	ProcessTime float64              `json:"processTime"` // 处理时间（秒）
}

// AddressParseResult 单个地址解析结果
type AddressParseResult struct {
	Index       int          `json:"index"`       // 索引
	Success     bool         `json:"success"`     // 是否成功
	AddressInfo *AddressInfo `json:"addressInfo"` // 地址信息
	Error       string       `json:"error"`       // 错误信息
	ProcessTime float64      `json:"processTime"` // 处理时间（秒）
}

// ParseStatistics 解析统计信息
type ParseStatistics struct {
	Total       int     `json:"total"`       // 总数
	Success     int     `json:"success"`     // 成功数
	Failed      int     `json:"failed"`      // 失败数
	SuccessRate float64 `json:"successRate"` // 成功率
}

// AreaNode 地区节点
type AreaNode struct {
	Value    string      `json:"value"`    // 地区代码
	Label    string      `json:"label"`    // 地区名称
	Children []*AreaNode `json:"children"` // 子节点
}

// AreaCascaderResponse 地区级联响应
type AreaCascaderResponse struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data []*AreaNode `json:"data"`
}

// AddressParseHistory 地址解析历史记录
type AddressParseHistory struct {
	ID           string    `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	UserID       string    `json:"userId" gorm:"type:varchar(36);not null;index"`
	OriginalText string    `json:"originalText" gorm:"type:text;not null"`
	ParsedInfo   string    `json:"parsedInfo" gorm:"type:jsonb"` // JSON格式存储解析结果
	Success      bool      `json:"success" gorm:"not null"`
	ErrorMsg     string    `json:"errorMsg" gorm:"type:text"`
	ProcessTime  float64   `json:"processTime"` // 处理时间（毫秒）
	CreatedAt    time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (AddressParseHistory) TableName() string {
	return "address_parse_histories"
}

// AddressTemplate 地址模板
type AddressTemplate struct {
	ID          string    `json:"id" gorm:"primaryKey;type:uuid;default:gen_random_uuid()"`
	UserID      string    `json:"userId" gorm:"type:varchar(36);not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(100);not null"`
	Template    string    `json:"template" gorm:"type:text;not null"`
	Description string    `json:"description" gorm:"type:text"`
	IsDefault   bool      `json:"isDefault" gorm:"default:false"`
	UsageCount  int       `json:"usageCount" gorm:"default:0"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (AddressTemplate) TableName() string {
	return "address_templates"
}

// AddressValidationRequest 地址验证请求
type AddressValidationRequest struct {
	ProvinceCode  string `json:"provinceCode" binding:"required"`
	CityCode      string `json:"cityCode" binding:"required"`
	DistrictCode  string `json:"districtCode" binding:"required"`
	DetailAddress string `json:"detailAddress" binding:"required"`
}

// AddressValidationResponse 地址验证响应
type AddressValidationResponse struct {
	Code    int                    `json:"code"`
	Success bool                   `json:"success"`
	Message string                 `json:"message"`
	Data    *AddressValidationData `json:"data"`
}

// AddressValidationData 地址验证数据
type AddressValidationData struct {
	IsValid             bool     `json:"isValid"`             // 是否有效
	Confidence          float64  `json:"confidence"`          // 置信度
	Suggestions         []string `json:"suggestions"`         // 建议
	StandardizedAddress string   `json:"standardizedAddress"` // 标准化地址
}
