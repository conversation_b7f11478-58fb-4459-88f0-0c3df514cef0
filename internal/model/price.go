package model

import "time"

// PriceRequest 价格查询请求
type PriceRequest struct {
	// 基本信息
	CustomerOrderNo string   `json:"customer_order_no"` // 客户订单号
	ExpressType     string   `json:"express_code"`      // 快递类型（可选）- 兼容前端字段名
	ExpressCodes    []string `json:"express_codes"`     // 多个快递类型（可选，新增字段）
	ProductType     string   `json:"product_type"`      // 产品类型（可选）
	Provider        string   `json:"provider"`          // 供应商（可选）

	// 寄件人信息
	Sender SenderInfo `json:"sender"`

	// 收件人信息
	Receiver ReceiverInfo `json:"receiver"`

	// 包裹信息
	Package PackageInfo `json:"package"`

	// 其他信息
	PayMethod         int  `json:"pay_method"`    // 支付方式：0-寄付，1-到付，2-月结
	IsCompare         bool `json:"is_compare"`    // 是否比较所有供应商价格
	QueryAllCompanies bool `json:"query_all"`     // 是否查询所有快递公司
	DisableCache      bool `json:"disable_cache"` // 是否禁用缓存（用于性能测试）
}

// StandardizedPrice 标准化价格结构
type StandardizedPrice struct {
	ExpressCode          string            `json:"express_code"`            // 快递公司代码
	ExpressName          string            `json:"express_name"`            // 快递公司名称
	ProductCode          string            `json:"product_code"`            // 产品代码
	ProductName          string            `json:"product_name"`            // 产品名称
	Price                float64           `json:"price"`                   // 总价格
	ContinuedWeightPerKg float64           `json:"continued_weight_per_kg"` // 每公斤续重价格
	CalcWeight           float64           `json:"calc_weight"`             // 计费重量
	Provider             string            `json:"provider"`                // 供应商(内部使用)
	ChannelID            string            `json:"channel_id"`              // 渠道ID
	OrderCode            string            `json:"order_code"`              // 订单代码，用于前端单选和后续下单

	// 🔥 新增：取件时间相关信息
	PickupTimeInfo       *PickupTimeInfo   `json:"pickup_time_info,omitempty"`       // 取件时间信息
	EstimatedDays        int               `json:"estimated_days,omitempty"`         // 预计送达天数
	ExpiresAt            string            `json:"expires_at,omitempty"`             // 价格有效期
}

// PriceResponse 价格查询响应
type PriceResponse struct {
	Success bool                `json:"success"`        // 是否成功
	Code    int                 `json:"code"`           // 状态码
	Message string              `json:"message"`        // 消息
	Data    []StandardizedPrice `json:"data,omitempty"` // 价格数据
}

// PriceRule 价格规则
type PriceRule struct {
	Start      int     `json:"start"`      // 起始重量
	End        int     `json:"end"`        // 结束重量
	First      float64 `json:"first"`      // 首重价格
	Additional float64 `json:"additional"` // 续重价格
}

// DiscountPriceRule 折扣价格规则
type DiscountPriceRule struct {
	Discount float64 `json:"discount"` // 折扣比例
	PerAdd   float64 `json:"per_add"`  // 单笔加收
}

// InsuranceRule 保价规则
type InsuranceRule struct {
	Start float64 `json:"start"` // 保额区间-起
	End   float64 `json:"end"`   // 保额区间-止
	Price float64 `json:"price"` // 固定保价收费
	Rate  float64 `json:"rate"`  // 比例保价收费
}

// PriceComparisonResponse 价格比较响应
type PriceComparisonResponse struct {
	Success bool                   `json:"success"`
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    []*PriceComparisonData `json:"data,omitempty"`
}

// PriceComparisonData 价格比较数据
type PriceComparisonData struct {
	Provider    string  `json:"provider"`
	ExpressType string  `json:"express_type"`
	Price       float64 `json:"price"`
	TimeLimit   string  `json:"time_limit"`
	Ranking     int     `json:"ranking"`
	Savings     float64 `json:"savings"`
}

// PriceInfo 价格信息
type PriceInfo struct {
	Provider    string  `json:"provider"`
	ExpressType string  `json:"express_type"`
	Price       float64 `json:"price"`
	TimeLimit   string  `json:"time_limit"`
	ChannelID   string  `json:"channel_id"`
	Available   bool    `json:"available"`
}

// PriceStatisticsRequest 价格统计请求
type PriceStatisticsRequest struct {
	Provider    string    `json:"provider"`
	ExpressType string    `json:"express_type"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	GroupBy     string    `json:"group_by"` // day, week, month
}

// PriceStatisticsResponse 价格统计响应
type PriceStatisticsResponse struct {
	Success bool                 `json:"success"`
	Code    int                  `json:"code"`
	Message string               `json:"message"`
	Data    *PriceStatisticsData `json:"data,omitempty"`
}

// PriceStatisticsData 价格统计数据
type PriceStatisticsData struct {
	TotalQueries   int64                    `json:"total_queries"`
	AveragePrice   float64                  `json:"average_price"`
	MinPrice       float64                  `json:"min_price"`
	MaxPrice       float64                  `json:"max_price"`
	ProviderStats  map[string]*ProviderStat `json:"provider_stats"`
	TimeSeriesData []*TimeSeriesPoint       `json:"time_series_data"`
}

// ProviderStat 供应商统计
type ProviderStat struct {
	QueryCount   int64   `json:"query_count"`
	AveragePrice float64 `json:"average_price"`
	SuccessRate  float64 `json:"success_rate"`
}

// TimeSeriesPoint 时间序列点
type TimeSeriesPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
	Count     int64     `json:"count"`
}

// PickupTimeInfo 取件时间信息（简化版本）
type PickupTimeInfo struct {
	PickupRequired     bool              `json:"pickup_required"`      // 是否必须预约取件
	SupportsPickupCode bool              `json:"supports_pickup_code"` // 是否支持取件码
	MinAdvanceHours    int               `json:"min_advance_hours"`    // 最小提前预约小时数
	TimeFormat         string            `json:"time_format"`          // 时间格式
	AvailableSlots     []PickupTimeSlotSimple  `json:"available_slots"`      // 可用时间段列表
}

// PickupTimeSlotSimple 简化的可预约时间段（用于API响应）
type PickupTimeSlotSimple struct {
	SlotID    string `json:"slot_id"`    // 时间段ID，用于下单时指定
	SlotName  string `json:"slot_name"`  // 时间段名称，如"09:00-11:00"
	StartTime string `json:"start_time"` // 开始时间 "2025-06-30 09:00:00"
	EndTime   string `json:"end_time"`   // 结束时间 "2025-06-30 11:00:00"
}
