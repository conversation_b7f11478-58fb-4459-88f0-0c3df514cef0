package model

import (
	"fmt"
	"strings"
	"time"
)

// OrderRequest 下单请求
type OrderRequest struct {
	// 基本信息
	PlatformOrderNo string `json:"platform_order_no"` // 🔥 新增：平台生成的全局唯一订单号
	CustomerOrderNo string `json:"customer_order_no"` // 客户订单号
	ExpressType     string `json:"express_type"`      // 快递类型
	ProductType     string `json:"product_type"`      // 产品类型
	ChannelID       string `json:"channel_id"`        // 渠道ID（可选，从查价接口获取）
	Provider        string `json:"provider"`          // 供应商（从订单代码中解析出的）
	UserID          string `json:"user_id"`           // 用户ID
	EstimatedPrice  string `json:"estimated_price"`   // 预估价格

	// 寄件人信息
	Sender SenderInfo `json:"sender"`

	// 收件人信息
	Receiver ReceiverInfo `json:"receiver"`

	// 包裹信息
	Package PackageInfo `json:"package"`

	// 取件信息
	Pickup PickupInfo `json:"pickup"`

	// 价格验证信息
	PriceSource   string  `json:"price_source,omitempty"`   // 价格来源：cache-缓存价格，realtime-实时价格
	CachedPrice   float64 `json:"cached_price,omitempty"`   // 用户选择的缓存价格（当price_source=cache时必填）
	ExpectedPrice float64 `json:"expected_price,omitempty"` // 用户期望价格（用于验证）

	// 其他信息
	PayMethod   int          `json:"pay_method"`   // 支付方式：0-寄付，1-到付，2-月结
	AddServices []AddService `json:"add_services"` // 增值服务

	// 🔥 新增：菜鸟裹裹专用字段
	OutOrderId string `json:"out_order_id,omitempty"` // 外部订单ID（菜鸟裹裹专用，可选）
}

// AddService 增值服务
type AddService struct {
	Type  string `json:"type"`  // 增值服务类型
	Value string `json:"value"` // 增值服务值
}

// PickupInfo 取件信息
type PickupInfo struct {
	StartTime string `json:"start_time"` // 预约取件开始时间
	EndTime   string `json:"end_time"`   // 预约取件结束时间
}

// PriceValidationConfig 价格验证配置
type PriceValidationConfig struct {
	EnableValidation      bool    `json:"enable_validation"`        // 是否启用价格验证
	PriceTolerance        float64 `json:"price_tolerance"`          // 价格容差（元）
	ValidationTimeout     int     `json:"validation_timeout"`       // 验证超时时间（秒）
	MaxRetryAttempts      int     `json:"max_retry_attempts"`       // 最大重试次数
	CacheInvalidateOnFail bool    `json:"cache_invalidate_on_fail"` // 验证失败时是否失效缓存
}

// OrderPriceValidationResult 订单价格验证结果
type OrderPriceValidationResult struct {
	IsValid          bool    `json:"is_valid"`          // 验证是否通过
	CachedPrice      float64 `json:"cached_price"`      // 缓存价格
	RealtimePrice    float64 `json:"realtime_price"`    // 实时价格
	PriceDifference  float64 `json:"price_difference"`  // 价格差异（绝对值）
	ValidationResult string  `json:"validation_result"` // 验证结果：pass-通过，fail-失败，skip-跳过，error-错误
	ActionTaken      string  `json:"action_taken"`      // 采取的行动：order_proceed-继续下单，order_reject-拒绝订单，cache_invalidate-失效缓存
	ErrorMessage     string  `json:"error_message"`     // 错误信息
	ValidationTime   string  `json:"validation_time"`   // 验证时间
}

// OrderResult 下单结果
type OrderResult struct {
	PlatformOrderNo  string      `json:"platform_order_no"`            // 🔥 新增：平台生成的全局唯一订单号
	CustomerOrderNo  string      `json:"customer_order_no"`            // 🔥 新增：客户订单号
	OrderNo          string      `json:"order_no"`                     // 🔥 重新定义：供应商返回的订单号
	TrackingNo       string      `json:"tracking_no"`                  // 运单号
	TaskId           string      `json:"task_id,omitempty"`            // 任务ID
	PollToken        string      `json:"poll_token,omitempty"`         // 轮询令牌，用于查询物流轨迹
	PickupCode       string      `json:"pickup_code"`                  // 取件码
	ChildTrackingNos []string    `json:"child_tracking_nos,omitempty"` // 子单号
	Price            float64     `json:"price"`                        // 价格
	PrintData        interface{} `json:"print_data,omitempty"`         // 打印数据
}

// OrderResponse 下单响应
type OrderResponse struct {
	Success bool         `json:"success"`        // 是否成功
	Code    int          `json:"code"`           // 状态码
	Message string       `json:"message"`        // 消息
	Data    *OrderResult `json:"data,omitempty"` // 订单结果
}

// OrderRecord 订单记录
type OrderRecord struct {
	ID              int64  `json:"id"`
	PlatformOrderNo string `json:"platform_order_no"` // 🔥 新增：平台生成的全局唯一订单号
	CustomerOrderNo string `json:"customer_order_no"` // 客户订单号
	OrderNo         string `json:"order_no"`          // 🔥 重新定义：供应商返回的订单号
	TrackingNo      string `json:"tracking_no"`       // 运单号
	ExpressType     string `json:"express_type"`      // 快递类型
	ProductType     string `json:"product_type"`      // 产品类型
	Provider        string `json:"provider"`          // 供应商
	Status          string `json:"status"`            // 订单状态

	// 失败订单相关字段
	FailureReason  string `json:"failure_reason,omitempty"`  // 失败原因类型
	FailureMessage string `json:"failure_message,omitempty"` // 失败详细信息
	FailureStage   string `json:"failure_stage,omitempty"`   // 失败阶段
	FailureSource  string `json:"failure_source,omitempty"`  // 失败来源：provider-供应商, system-系统
	FailureTime    string `json:"failure_time,omitempty"`    // 失败时间
	CanRetry       bool   `json:"can_retry"`                 // 是否可重试

	// 兼容性字段（保留原有字段）
	Weight float64 `json:"weight"` // 重量（兼容性，建议使用OrderWeight）
	Price  float64 `json:"price"`  // 价格（主要价格字段）

	// 费用相关字段
	ActualFee              float64 `json:"actual_fee"`               // 实收费用
	InsuranceFee           float64 `json:"insurance_fee"`            // 保价费
	OverweightFee          float64 `json:"overweight_fee"`           // 超重费用
	UnderweightFee         float64 `json:"underweight_fee"`          // 超轻费用
	WeightAdjustmentReason string  `json:"weight_adjustment_reason"` // 重量调整原因
	BillingStatus          string  `json:"billing_status"`           // 计费状态: pending, confirmed (settled已废弃)

	// 重量体积相关字段
	OrderVolume   float64 `json:"order_volume"`   // 下单体积(m³)
	ActualWeight  float64 `json:"actual_weight"`  // 实际重量(kg)
	ActualVolume  float64 `json:"actual_volume"`  // 实际体积(m³)
	ChargedWeight float64 `json:"charged_weight"` // 计费重量(kg)

	// 其他字段
	SenderInfo   string    `json:"sender_info"`   // 寄件人信息(JSON)
	ReceiverInfo string    `json:"receiver_info"` // 收件人信息(JSON)
	PackageInfo  string    `json:"package_info"`  // 包裹信息(JSON)
	RequestData  string    `json:"request_data"`  // 请求数据(JSON)
	ResponseData string    `json:"response_data"` // 响应数据(JSON)
	OrderCode    string    `json:"order_code"`    // 🔥 新增：查价接口返回的order_code，用于下单时验证价格一致性
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time `json:"updated_at"`    // 更新时间
	TaskId       string    `json:"task_id"`       // 任务ID
	PollToken    string    `json:"poll_token"`    // 轮询令牌
	UserID       string    `json:"user_id"`       // 用户ID

	// 揽件员信息字段
	CourierName  string `json:"courier_name"`  // 快递员姓名
	CourierPhone string `json:"courier_phone"` // 快递员电话
	CourierCode  string `json:"courier_code"`  // 快递员工号
	StationName  string `json:"station_name"`  // 网点名称
	PickupCode   string `json:"pickup_code"`   // 取件码

	// 价格验证字段
	PriceValidationProviderPrice *float64   `json:"price_validation_provider_price,omitempty"` // 供应商实际价格
	PriceValidationSystemPrice   *float64   `json:"price_validation_system_price,omitempty"`   // 系统订单价格
	PriceValidationProfitStatus  *string    `json:"price_validation_profit_status,omitempty"`  // 盈亏状态：profit, loss, break_even, unknown
	PriceValidationQueryStatus   *string    `json:"price_validation_query_status,omitempty"`   // 查询状态：pending, success, failed, cached
	PriceValidationQueryTime     *time.Time `json:"price_validation_query_time,omitempty"`     // 查询时间
	PriceValidationErrorMessage  *string    `json:"price_validation_error_message,omitempty"`  // 错误信息
	PriceValidationSupported     *bool      `json:"price_validation_supported,omitempty"`      // 供应商是否支持查询
}

// OrderInfo 订单信息（供应商查询返回的信息）
type OrderInfo struct {
	OrderNo        string    `json:"order_no"`        // 平台订单号
	TrackingNo     string    `json:"tracking_no"`     // 运单号
	ExpressType    string    `json:"express_type"`    // 快递类型
	Status         string    `json:"status"`          // 订单状态
	StatusDesc     string    `json:"status_desc"`     // 状态描述
	Weight         float64   `json:"weight"`          // 重量
	Price          float64   `json:"price"`           // 价格
	CreatedAt      time.Time `json:"created_at"`      // 创建时间
	CourierName    string    `json:"courier_name"`    // 快递员姓名
	CourierPhone   string    `json:"courier_phone"`   // 快递员电话
	CourierCode    string    `json:"courier_code"`    // 快递员工号
	StationName    string    `json:"station_name"`    // 网点名称
	StationCode    string    `json:"station_code"`    // 网点编号
	StationAddress string    `json:"station_address"` // 网点地址
	StationPhone   string    `json:"station_phone"`   // 网点电话
	PickupCode     string    `json:"pickup_code"`     // 取件码
}

// UserOrderDetail 用户订单详情（系统内部信息）
type UserOrderDetail struct {
	// 基本信息
	OrderNo         string `json:"order_no"`          // 平台订单号
	CustomerOrderNo string `json:"customer_order_no"` // 客户订单号
	TrackingNo      string `json:"tracking_no"`       // 运单号
	ExpressType     string `json:"express_type"`      // 快递类型
	ExpressName     string `json:"express_name"`      // 快递公司名称
	ProductType     string `json:"product_type"`      // 产品类型
	Provider        string `json:"provider"`          // 供应商
	ProviderName    string `json:"provider_name"`     // 供应商名称

	// 状态信息
	Status     string    `json:"status"`      // 订单状态
	StatusDesc string    `json:"status_desc"` // 状态描述
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
	UpdatedAt  time.Time `json:"updated_at"`  // 更新时间

	// 费用信息
	Price                  float64 `json:"price"`                    // 价格（主要价格字段）
	ActualFee              float64 `json:"actual_fee"`               // 实际费用
	InsuranceFee           float64 `json:"insurance_fee"`            // 保价费
	OverweightFee          float64 `json:"overweight_fee"`           // 超重费用
	UnderweightFee         float64 `json:"underweight_fee"`          // 超轻费用
	WeightAdjustmentReason string  `json:"weight_adjustment_reason"` // 重量调整原因
	BillingStatus          string  `json:"billing_status"`           // 计费状态

	// 重量体积信息
	Weight        float64 `json:"weight"`         // 下单重量(kg)
	OrderVolume   float64 `json:"order_volume"`   // 下单体积(m³)
	ActualWeight  float64 `json:"actual_weight"`  // 实际重量(kg)
	ActualVolume  float64 `json:"actual_volume"`  // 实际体积(m³)
	ChargedWeight float64 `json:"charged_weight"` // 计费重量(kg)

	// 地址信息（解析后的结构化数据）
	SenderInfo   string `json:"sender_info"`   // 寄件人信息（JSON字符串）
	ReceiverInfo string `json:"receiver_info"` // 收件人信息（JSON字符串）
	PackageInfo  string `json:"package_info"`  // 包裹信息（JSON字符串）

	// 系统信息
	TaskId    string `json:"task_id"`    // 任务ID
	PollToken string `json:"poll_token"` // 轮询令牌
	UserID    string `json:"user_id"`    // 用户ID

	// 🔥 新增：失败订单相关字段
	FailureReason  string `json:"failure_reason,omitempty"`  // 失败原因类型
	FailureMessage string `json:"failure_message,omitempty"` // 失败详细信息
	FailureStage   string `json:"failure_stage,omitempty"`   // 失败阶段
	FailureSource  string `json:"failure_source,omitempty"`  // 失败来源：provider-供应商, system-系统
	FailureTime    string `json:"failure_time,omitempty"`    // 失败时间
	CanRetry       bool   `json:"can_retry,omitempty"`       // 是否可重试
}

// CancelOrderRequest 取消订单请求
type CancelOrderRequest struct {
	OrderNo         string `json:"order_no"`          // 🔥 智能订单号：支持平台订单号、客户订单号、供应商订单号
	TrackingNo      string `json:"tracking_no"`       // 运单号
	PlatformOrderNo string `json:"platform_order_no"` // 🔥 新增：明确指定平台订单号
	Reason          string `json:"reason"`            // 取消原因
	UserID          string `json:"-"`                 // 用户ID，用于权限控制（内部使用，不从前端传入）
}

// CancelOrderResponse 取消订单响应
type CancelOrderResponse struct {
	Success bool   `json:"success"` // 是否成功
	Code    int    `json:"code"`    // 状态码
	Message string `json:"message"` // 消息
}

// QueryOrderRequest 查询订单请求
type QueryOrderRequest struct {
	OrderNo    string `json:"order_no"`    // 平台订单号
	TrackingNo string `json:"tracking_no"` // 运单号
	UserID     string `json:"-"`           // 用户ID，用于权限控制（内部使用，不从前端传入）
}

// QueryOrderResponse 查询订单响应
type QueryOrderResponse struct {
	Success bool        `json:"success"`        // 是否成功
	Code    int         `json:"code"`           // 状态码
	Message string      `json:"message"`        // 消息
	Data    interface{} `json:"data,omitempty"` // 订单信息（可以是OrderInfo或UserOrderDetail）
}

// OrderBillingDetail 订单计费详情
type OrderBillingDetail struct {
	ID          int64  `json:"id"`
	OrderID     int64  `json:"order_id"`
	OrderNo     string `json:"order_no"`
	BillingType string `json:"billing_type"` // estimate, actual, adjustment

	// 费用明细
	FreightFee   float64 `json:"freight_fee"`   // 运费
	InsuranceFee float64 `json:"insurance_fee"` // 保价费
	PackageFee   float64 `json:"package_fee"`   // 包装费
	PickupFee    float64 `json:"pickup_fee"`    // 上门取件费
	DeliveryFee  float64 `json:"delivery_fee"`  // 送货上门费
	CodFee       float64 `json:"cod_fee"`       // 代收货款手续费
	OtherFee     float64 `json:"other_fee"`     // 其他费用
	TotalFee     float64 `json:"total_fee"`     // 总费用

	// 重量体积信息
	Weight        float64 `json:"weight"`         // 重量(kg)
	Volume        float64 `json:"volume"`         // 体积(m³)
	ChargedWeight float64 `json:"charged_weight"` // 计费重量(kg)

	// 元数据
	Provider  string    `json:"provider"`
	Source    string    `json:"source"`   // system, callback, manual
	RawData   string    `json:"raw_data"` // 原始数据(JSON)
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// OrderBillingHistory 订单计费历史
type OrderBillingHistory struct {
	ID               int64     `json:"id"`
	OrderID          int64     `json:"order_id"`
	OrderNo          string    `json:"order_no"`
	ChangeType       string    `json:"change_type"`       // fee_update, weight_update, status_change
	OldValues        string    `json:"old_values"`        // 变更前的值(JSON)
	NewValues        string    `json:"new_values"`        // 变更后的值(JSON)
	DifferenceAmount float64   `json:"difference_amount"` // 费用差额
	Reason           string    `json:"reason"`            // 变更原因
	Source           string    `json:"source"`            // 变更来源
	Provider         string    `json:"provider"`
	OperatorID       string    `json:"operator_id"` // 操作员ID
	CreatedAt        time.Time `json:"created_at"`
}

// WeightFeeAdjustment 重量费用调整记录
type WeightFeeAdjustment struct {
	ID      int64  `json:"id"`
	OrderID int64  `json:"order_id"`
	OrderNo string `json:"order_no"`

	// 重量信息
	OriginalWeight float64 `json:"original_weight"` // 原始重量(kg)
	ActualWeight   float64 `json:"actual_weight"`   // 实际重量(kg)
	ChargedWeight  float64 `json:"charged_weight"`  // 计费重量(kg)

	// 费用信息
	OriginalFee    float64 `json:"original_fee"`    // 原始费用
	AdjustedFee    float64 `json:"adjusted_fee"`    // 调整后费用
	OverweightFee  float64 `json:"overweight_fee"`  // 超重费用
	UnderweightFee float64 `json:"underweight_fee"` // 超轻费用

	// 调整信息
	AdjustmentType   string `json:"adjustment_type"`   // 调整类型: overweight, underweight, normal
	AdjustmentReason string `json:"adjustment_reason"` // 调整原因
	Provider         string `json:"provider"`          // 供应商
	Source           string `json:"source"`            // 来源: callback, manual, system

	// 元数据
	RawData    string    `json:"raw_data"`    // 原始回调数据(JSON)
	OperatorID string    `json:"operator_id"` // 操作员ID
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// OrderStatus 订单状态常量（失败状态）
const (
	OrderStatusFailed = "failed" // 下单失败
)

// FailureReason 失败原因常量
const (
	FailureReasonValidation = "validation_failed"       // 参数验证失败
	FailureReasonBalance    = "balance_insufficient"    // 余额不足
	FailureReasonProvider   = "provider_error"          // 供应商错误
	FailureReasonPrice      = "price_validation_failed" // 价格验证失败
	FailureReasonNetwork    = "network_error"           // 网络错误
	FailureReasonSystem     = "system_error"            // 系统错误
	FailureReasonTimeout    = "timeout_error"           // 超时错误
	FailureReasonUnknown    = "unknown_error"           // 未知错误
)

// BillingStatus 计费状态常量
const (
	BillingStatusPending   = "pending"   // 待计费
	BillingStatusConfirmed = "confirmed" // 已确认
	BillingStatusFailed    = "failed"    // 计费失败
	// 🚨 已删除：BillingStatusSettled - 结算功能已废弃
)

// BillingType 计费类型常量
const (
	BillingTypeEstimate   = "estimate"   // 预估计费
	BillingTypeActual     = "actual"     // 实际计费
	BillingTypeAdjustment = "adjustment" // 费用调整
)

// ChangeType 变更类型常量
const (
	ChangeTypeFeeUpdate    = "fee_update"    // 费用更新
	ChangeTypeWeightUpdate = "weight_update" // 重量更新
	ChangeTypeStatusChange = "status_change" // 状态变更
)

// BillingSource 计费来源常量
const (
	BillingSourceSystem    = "system"    // 系统计算
	BillingSourceCallback  = "callback"  // 回调更新
	BillingSourceManual    = "manual"    // 手动调整
	BillingSourceMigration = "migration" // 数据迁移
)

// ProviderShippingFeeInfo 供应商运费信息
type ProviderShippingFeeInfo struct {
	OrderID     int64   `json:"order_id"`        // 订单ID
	Provider    string  `json:"provider"`        // 供应商名称
	ShippingFee float64 `json:"shipping_fee"`    // 运费
	Currency    string  `json:"currency"`        // 货币单位
	QueryTime   string  `json:"query_time"`      // 查询时间
	Supported   bool    `json:"supported"`       // 是否支持查询
	Error       string  `json:"error,omitempty"` // 错误信息
}

// ProviderShippingFeeResponse 供应商运费查询响应
type ProviderShippingFeeResponse struct {
	Success bool                     `json:"success"`
	Code    int                      `json:"code"`
	Message string                   `json:"message"`
	Data    *ProviderShippingFeeInfo `json:"data,omitempty"`
}

// BatchProviderShippingFeeResponse 批量供应商运费查询响应
type BatchProviderShippingFeeResponse struct {
	Success bool                       `json:"success"`
	Code    int                        `json:"code"`
	Message string                     `json:"message"`
	Data    []*ProviderShippingFeeInfo `json:"data,omitempty"`
}

// DetailedShippingFeeInfo 详细运费信息
type DetailedShippingFeeInfo struct {
	OrderID     int64  `json:"order_id"`     // 订单ID
	OrderNo     string `json:"order_no"`     // 订单号
	TrackingNo  string `json:"tracking_no"`  // 运单号
	Provider    string `json:"provider"`     // 供应商名称
	ExpressType string `json:"express_type"` // 快递公司代码

	// 基础运费信息
	TotalFee float64 `json:"total_fee"` // 总运费
	Currency string  `json:"currency"`  // 货币单位

	// 费用明细
	FeeDetails []ShippingFeeDetail `json:"fee_details,omitempty"` // 费用明细列表

	// 重量信息
	WeightInfo *ShippingWeightInfo `json:"weight_info,omitempty"` // 重量信息

	// 查询信息
	QueryTime string `json:"query_time"`      // 查询时间
	Supported bool   `json:"supported"`       // 是否支持查询
	Error     string `json:"error,omitempty"` // 错误信息

	// 原始数据
	RawData string `json:"raw_data,omitempty"` // 供应商返回的原始数据
}

// ShippingFeeDetail 运费明细
type ShippingFeeDetail struct {
	Type        string  `json:"type"`        // 费用类型：freight, insurance, fuel, package, pickup, delivery, cod, other
	TypeName    string  `json:"type_name"`   // 费用类型名称
	Amount      float64 `json:"amount"`      // 费用金额
	Description string  `json:"description"` // 费用描述
	Unit        string  `json:"unit"`        // 计费单位
}

// ShippingWeightInfo 运费相关重量信息
type ShippingWeightInfo struct {
	OrderWeight   float64 `json:"order_weight"`   // 下单重量(kg)
	ActualWeight  float64 `json:"actual_weight"`  // 实际重量(kg)
	ChargedWeight float64 `json:"charged_weight"` // 计费重量(kg)
	VolumeWeight  float64 `json:"volume_weight"`  // 体积重量(kg)
	Volume        float64 `json:"volume"`         // 体积(m³)
}

// DetailedShippingFeeResponse 详细运费查询响应
type DetailedShippingFeeResponse struct {
	Success bool                     `json:"success"`
	Code    int                      `json:"code"`
	Message string                   `json:"message"`
	Data    *DetailedShippingFeeInfo `json:"data,omitempty"`
}

// OrderFailureInfo 订单失败信息
type OrderFailureInfo struct {
	FailureReason  string                 `json:"failure_reason"`  // 失败原因类型
	FailureMessage string                 `json:"failure_message"` // 失败详细信息
	FailureStage   string                 `json:"failure_stage"`   // 失败阶段
	FailureSource  string                 `json:"failure_source"`  // 失败来源：provider-供应商, system-系统
	ErrorCode      string                 `json:"error_code"`      // 错误代码
	FailureTime    time.Time              `json:"failure_time"`    // 失败时间
	CanRetry       bool                   `json:"can_retry"`       // 是否可重试
	AttemptData    map[string]interface{} `json:"attempt_data"`    // 尝试数据
}

// FailedOrderCreateRequest 创建失败订单请求
type FailedOrderCreateRequest struct {
	OrderRequest   *OrderRequest     `json:"order_request"`   // 原始订单请求
	FailureInfo    *OrderFailureInfo `json:"failure_info"`    // 失败信息
	EstimatedPrice float64           `json:"estimated_price"` // 预估价格
}

// FailureStatistics 失败订单统计
type FailureStatistics struct {
	TotalOrders   int64                  `json:"total_orders"`   // 总订单数
	FailedOrders  int64                  `json:"failed_orders"`  // 失败订单数
	FailureRate   float64                `json:"failure_rate"`   // 失败率
	ReasonStats   []*FailureReasonStat   `json:"reason_stats"`   // 失败原因统计
	ProviderStats []*FailureProviderStat `json:"provider_stats"` // 供应商失败统计
	StageStats    []*FailureStageStat    `json:"stage_stats"`    // 失败阶段统计
	TrendStats    []*FailureTrendStat    `json:"trend_stats"`    // 失败趋势统计
	TimeWindow    string                 `json:"time_window"`    // 统计时间窗口
	LastUpdated   time.Time              `json:"last_updated"`   // 最后更新时间
}

// FailureReasonStat 失败原因统计
type FailureReasonStat struct {
	Reason      string  `json:"reason"`      // 失败原因
	Count       int64   `json:"count"`       // 失败次数
	Percentage  float64 `json:"percentage"`  // 失败占比
	Description string  `json:"description"` // 原因描述
}

// FailureProviderStat 供应商失败统计
type FailureProviderStat struct {
	Provider    string  `json:"provider"`     // 供应商
	Count       int64   `json:"count"`        // 失败次数
	Percentage  float64 `json:"percentage"`   // 失败占比
	FailureRate float64 `json:"failure_rate"` // 该供应商失败率
}

// FailureStageStat 失败阶段统计
type FailureStageStat struct {
	Stage       string  `json:"stage"`       // 失败阶段
	Count       int64   `json:"count"`       // 失败次数
	Percentage  float64 `json:"percentage"`  // 失败占比
	Description string  `json:"description"` // 阶段描述
}

// FailureTrendStat 失败趋势统计
type FailureTrendStat struct {
	TimePoint   string  `json:"time_point"`   // 时间点
	FailedCount int64   `json:"failed_count"` // 失败数量
	TotalCount  int64   `json:"total_count"`  // 总数量
	FailureRate float64 `json:"failure_rate"` // 失败率
}

// =====================================================
// 🔥 平台订单号兼容性方法
// =====================================================

// GetPrimaryOrderNo 获取主要订单号（优先返回平台订单号）
func (o *OrderRecord) GetPrimaryOrderNo() string {
	if o.PlatformOrderNo != "" {
		return o.PlatformOrderNo
	}
	return o.OrderNo
}

// GetProviderOrderNo 获取供应商订单号
func (o *OrderRecord) GetProviderOrderNo() string {
	return o.OrderNo
}

// SetPlatformOrderNo 设置平台订单号
func (o *OrderRecord) SetPlatformOrderNo(platformOrderNo string) {
	o.PlatformOrderNo = platformOrderNo
}

// SetProviderOrderNo 设置供应商订单号
func (o *OrderRecord) SetProviderOrderNo(providerOrderNo string) {
	o.OrderNo = providerOrderNo
}

// HasPlatformOrderNo 检查是否有平台订单号
func (o *OrderRecord) HasPlatformOrderNo() bool {
	return o.PlatformOrderNo != ""
}

// GetOrderIdentifiers 获取所有订单标识符
func (o *OrderRecord) GetOrderIdentifiers() map[string]string {
	identifiers := make(map[string]string)

	if o.PlatformOrderNo != "" {
		identifiers["platform_order_no"] = o.PlatformOrderNo
	}
	if o.CustomerOrderNo != "" {
		identifiers["customer_order_no"] = o.CustomerOrderNo
	}
	if o.OrderNo != "" {
		identifiers["provider_order_no"] = o.OrderNo
	}
	if o.TrackingNo != "" {
		identifiers["tracking_no"] = o.TrackingNo
	}

	return identifiers
}

// ValidateOrderIdentifiers 验证订单标识符
func (o *OrderRecord) ValidateOrderIdentifiers() error {
	if o.CustomerOrderNo == "" {
		return fmt.Errorf("客户订单号不能为空")
	}

	// 平台订单号格式验证（如果存在）
	if o.PlatformOrderNo != "" {
		if len(o.PlatformOrderNo) != 19 {
			return fmt.Errorf("平台订单号格式不正确，长度应为19位")
		}
		if !strings.HasPrefix(o.PlatformOrderNo, "GK") {
			return fmt.Errorf("平台订单号格式不正确，应以GK开头")
		}
	}

	return nil
}
