package model

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// CallbackProvider 回调供应商常量
const (
	CallbackProviderYuntong    = "yuntong"
	CallbackProviderYida       = "yida"
	CallbackProviderKuaidi100  = "kuaidi100"
	CallbackProviderCainiao    = "cainiao"
	CallbackProviderKuaidiniao = "kuaidiniao"
)

// CallbackEventType 回调事件类型常量
const (
	EventTypeOrderStatusChanged = "order_status_changed"
	EventTypeBillingUpdated     = "billing_updated"
	EventTypeTicketReplied      = "ticket_replied"

	// 🔥 新增：工单专用事件类型
	EventTypeWorkOrderCreated = "workorder_created"
	EventTypeWorkOrderReplied = "workorder_replied"
	EventTypeWorkOrderClosed  = "workorder_closed"
	EventTypeWorkOrderUpdated = "workorder_updated"
)

// 🔥 新增：易达风格的推送类型常量
const (
	PushTypeStatusUpdate = 1 // 状态推送
	PushTypeBilling      = 2 // 计费推送
	PushTypePickup       = 3 // 揽收推送
	PushTypeException    = 4 // 异常推送
	PushTypeOrderChange  = 5 // 订单变更推送
)

// CallbackStatus 回调处理状态常量
const (
	CallbackStatusPending    = "pending"
	CallbackStatusProcessing = "processing"
	CallbackStatusSuccess    = "success"
	CallbackStatusFailed     = "failed"
)

// UserCallbackConfig 用户回调配置
type UserCallbackConfig struct {
	ID               uuid.UUID `json:"id" db:"id"`
	UserID           string    `json:"user_id" db:"user_id"`
	CallbackURL      string    `json:"callback_url" db:"callback_url"`
	CallbackSecret   string    `json:"callback_secret,omitempty" db:"callback_secret"`
	Enabled          bool      `json:"enabled" db:"enabled"`
	RetryCount       int       `json:"retry_count" db:"retry_count"`
	TimeoutSeconds   int       `json:"timeout_seconds" db:"timeout_seconds"`
	SubscribedEvents []string  `json:"subscribed_events" db:"subscribed_events"`
	CreatedAt        time.Time `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time `json:"updated_at" db:"updated_at"`
}

// UnifiedCallbackRecord 统一回调记录
type UnifiedCallbackRecord struct {
	ID                  uuid.UUID       `json:"id" db:"id"`
	Provider            string          `json:"provider" db:"provider"`
	CallbackType        string          `json:"callback_type" db:"callback_type"`
	OrderNo             string          `json:"order_no" db:"order_no"`
	CustomerOrderNo     string          `json:"customer_order_no" db:"customer_order_no"`
	TrackingNo          string          `json:"tracking_no" db:"tracking_no"`
	UserID              string          `json:"user_id" db:"user_id"`
	Username            string          `json:"username,omitempty" db:"-"` // 用户名，仅在管理员查询时填充
	RawData             json.RawMessage `json:"raw_data" db:"raw_data"`
	RawSignature        string          `json:"raw_signature" db:"raw_signature"`
	StandardizedData    json.RawMessage `json:"standardized_data" db:"standardized_data"`
	EventType           string          `json:"event_type" db:"event_type"`
	InternalStatus      string          `json:"internal_status" db:"internal_status"`
	ExternalStatus      string          `json:"external_status" db:"external_status"`
	ReceivedAt          time.Time       `json:"received_at" db:"received_at"`
	InternalProcessedAt *time.Time      `json:"internal_processed_at" db:"internal_processed_at"`
	ExternalProcessedAt *time.Time      `json:"external_processed_at" db:"external_processed_at"`
	InternalError       string          `json:"internal_error" db:"internal_error"`
	ExternalError       string          `json:"external_error" db:"external_error"`
	RetryCount          int             `json:"retry_count" db:"retry_count"`
	RetryEnabled        bool            `json:"retry_enabled" db:"retry_enabled"`
	RetryStoppedAt      *time.Time      `json:"retry_stopped_at" db:"retry_stopped_at"`
	RetryStopReason     string          `json:"retry_stop_reason" db:"retry_stop_reason"`
	CreatedAt           time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time       `json:"updated_at" db:"updated_at"`
}

// CallbackForwardRecord 外部转发记录
type CallbackForwardRecord struct {
	ID               uuid.UUID        `json:"id" db:"id"`
	CallbackRecordID uuid.UUID        `json:"callback_record_id" db:"callback_record_id"`
	UserID           string           `json:"user_id" db:"user_id"`
	CallbackURL      string           `json:"callback_url" db:"callback_url"`
	RequestData      json.RawMessage  `json:"request_data" db:"request_data"`
	ResponseData     *json.RawMessage `json:"response_data" db:"response_data"`
	HTTPStatus       int              `json:"http_status" db:"http_status"`
	Status           string           `json:"status" db:"status"`
	RetryCount       int              `json:"retry_count" db:"retry_count"`
	RetryEnabled     bool             `json:"retry_enabled" db:"retry_enabled"`
	LastRetryAt      *time.Time       `json:"last_retry_at" db:"last_retry_at"`
	NextRetryAt      *time.Time       `json:"next_retry_at" db:"next_retry_at"`
	ErrorMessage     string           `json:"error_message" db:"error_message"`
	RequestAt        *time.Time       `json:"request_at" db:"request_at"`
	ResponseAt       *time.Time       `json:"response_at" db:"response_at"`
	CreatedAt        time.Time        `json:"created_at" db:"created_at"`
}

// CallbackRetryRecord 回调重试记录
type CallbackRetryRecord struct {
	ID                  uuid.UUID  `json:"id" db:"id"`
	ForwardRecordID     uuid.UUID  `json:"forward_record_id" db:"forward_record_id"`
	CallbackRecordID    uuid.UUID  `json:"callback_record_id" db:"callback_record_id"`
	UserID              string     `json:"user_id" db:"user_id"`
	RetryAttempt        int        `json:"retry_attempt" db:"retry_attempt"`
	ScheduledAt         time.Time  `json:"scheduled_at" db:"scheduled_at"`
	ExecutedAt          *time.Time `json:"executed_at" db:"executed_at"`
	Status              string     `json:"status" db:"status"`
	ErrorMessage        string     `json:"error_message" db:"error_message"`
	HTTPStatus          int        `json:"http_status" db:"http_status"`
	ResponseData        *string    `json:"response_data" db:"response_data"`
	ExecutionDurationMs int        `json:"execution_duration_ms" db:"execution_duration_ms"`
	NextRetryAt         *time.Time `json:"next_retry_at" db:"next_retry_at"`
	CreatedAt           time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at" db:"updated_at"`
}

// 重试状态常量
const (
	RetryStatusPending   = "pending"
	RetryStatusExecuting = "executing"
	RetryStatusSuccess   = "success"
	RetryStatusFailed    = "failed"
	RetryStatusCancelled = "cancelled"
)

// StandardizedCallbackData 标准化回调数据结构
type StandardizedCallbackData struct {
	EventType       string      `json:"event_type"`
	OrderNo         string      `json:"order_no"`          // 供应商订单号
	PlatformOrderNo string      `json:"platform_order_no"` // 🔥 新增：平台订单号
	CustomerOrderNo string      `json:"customer_order_no"`
	TrackingNo      string      `json:"tracking_no"`
	Provider        string      `json:"provider"`
	Timestamp       time.Time   `json:"timestamp"`
	Data            interface{} `json:"data"`
}

// OrderStatusChangedData 订单状态变更事件数据
type OrderStatusChangedData struct {
	OldStatus   string                 `json:"old_status,omitempty"`
	NewStatus   string                 `json:"new_status"`
	StatusDesc  string                 `json:"status_desc"`
	UpdateTime  time.Time              `json:"update_time"`
	CourierInfo *CourierInfo           `json:"courier_info,omitempty"`
	Extra       map[string]interface{} `json:"extra,omitempty"`
}

// BillingUpdatedData 计费信息更新事件数据
type BillingUpdatedData struct {
	Weight        float64      `json:"weight"`                   // 实际重量(kg)
	Volume        float64      `json:"volume,omitempty"`         // 体积(m³)
	Cost          float64      `json:"cost"`                     // 运费成本
	TotalFee      float64      `json:"total_fee"`                // 总费用
	OfficialFee   float64      `json:"official_fee,omitempty"`   // 官方原价
	PackageCount  int          `json:"package_count,omitempty"`  // 包裹数量
	FeeDetails    []FeeDetail  `json:"fee_details,omitempty"`    // 费用明细
	PricingInfo   *PricingInfo `json:"pricing_info,omitempty"`   // 计费信息
	UpdateTime    time.Time    `json:"update_time"`              // 更新时间
	ChargedWeight float64      `json:"charged_weight,omitempty"` // 计费重量
}

// TicketRepliedData 工单回复事件数据
type TicketRepliedData struct {
	TicketID    string    `json:"ticket_id"`
	Status      string    `json:"status"`
	Reply       string    `json:"reply"`
	ReplyTime   time.Time `json:"reply_time"`
	TicketType  string    `json:"ticket_type,omitempty"`
	ResultType  string    `json:"result_type,omitempty"`
	Attachments []string  `json:"attachments,omitempty"`
}

// 🔥 新增：工单回调数据结构
// WorkOrderCallbackData 工单回调事件数据
type WorkOrderCallbackData struct {
	WorkOrderID         string    `json:"workorder_id"`
	ProviderWorkOrderID string    `json:"provider_workorder_id"`
	Status              int       `json:"status"`
	StatusName          string    `json:"status_name"`
	Content             string    `json:"content"`
	Committer           string    `json:"committer"`
	AttachmentURLs      []string  `json:"attachment_urls"`
	UpdatedAt           time.Time `json:"updated_at"`
	WorkOrderType       int       `json:"workorder_type"`
	WorkOrderTypeName   string    `json:"workorder_type_name"`
}

// PickupInfoUpdatedData 揽收信息更新事件数据
type PickupInfoUpdatedData struct {
	CourierInfo *CourierInfo           `json:"courier_info,omitempty"`
	PickupCode  string                 `json:"pickup_code,omitempty"`
	UpdateTime  time.Time              `json:"update_time"`
	Extra       map[string]interface{} `json:"extra,omitempty"`
}

// CallbackData 回调数据
type CallbackData struct {
	EventType       string                 `json:"event_type"`
	OrderNo         string                 `json:"order_no"`
	CustomerOrderNo string                 `json:"customer_order_no"` // 🔥 新增：客户订单号
	TrackingNo      string                 `json:"tracking_no"`
	Provider        string                 `json:"provider"`
	Timestamp       time.Time              `json:"timestamp"`
	Data            map[string]interface{} `json:"data"`
	RawData         string                 `json:"raw_data"`
}

// CallbackListRequest 回调列表请求
type CallbackListRequest struct {
	Provider   string    `json:"provider"`
	EventType  string    `json:"event_type"`
	OrderNo    string    `json:"order_no"`
	TrackingNo string    `json:"tracking_no"`
	Status     string    `json:"status"`
	StartTime  time.Time `json:"start_time"`
	EndTime    time.Time `json:"end_time"`
	Limit      int       `json:"limit"`
	Offset     int       `json:"offset"`
}

// CallbackListResponse 回调列表响应
type CallbackListResponse struct {
	Success bool                     `json:"success"`
	Code    int                      `json:"code"`
	Message string                   `json:"message"`
	Data    []*UnifiedCallbackRecord `json:"data"`
	Total   int64                    `json:"total"`
}

// CourierInfo 快递员信息
type CourierInfo struct {
	Name       string `json:"name,omitempty"`
	Phone      string `json:"phone,omitempty"`
	Code       string `json:"code,omitempty"`
	Station    string `json:"station,omitempty"`
	PickupCode string `json:"pickup_code,omitempty"` // 🔥 新增：取件码
}

// FeeDetail 费用明细
type FeeDetail struct {
	FeeType   string  `json:"fee_type"`             // 费用类型 (freight, insurance, package, other)
	FeeDesc   string  `json:"fee_desc"`             // 费用描述
	Amount    float64 `json:"amount"`               // 费用金额
	PayStatus string  `json:"pay_status,omitempty"` // 支付状态 (pending, paid, failed, refunded)
	// 兼容旧字段
	Type string `json:"type,omitempty"` // 兼容旧版本
	Name string `json:"name,omitempty"` // 兼容旧版本
	Desc string `json:"desc,omitempty"` // 兼容旧版本
}

// PricingInfo 计费信息
type PricingInfo struct {
	FirstWeight   float64 `json:"first_weight"`   // 首重重量(kg)
	FirstPrice    float64 `json:"first_price"`    // 首重价格
	ContinuePrice float64 `json:"continue_price"` // 续重价格
	Discount      float64 `json:"discount"`       // 折扣率(%)
	DiscountFee   float64 `json:"discount_fee"`   // 折扣金额
}

// ParsedCallbackData 解析后的回调数据
type ParsedCallbackData struct {
	Type            string      `json:"type"`
	OrderNo         string      `json:"order_no"`
	CustomerOrderNo string      `json:"customer_order_no"`
	TrackingNo      string      `json:"tracking_no"`
	UserID          string      `json:"user_id"`
	Data            interface{} `json:"data"`
	Timestamp       time.Time   `json:"timestamp"`
}

// CallbackResponse 回调响应
type CallbackResponse struct {
	Success      bool        `json:"success"`
	Code         string      `json:"code"`
	Message      string      `json:"message"`
	Data         interface{} `json:"data,omitempty"`
	DirectReturn bool        `json:"-"` // 标记是否直接返回Data，不包装
}

// UserCallbackRequest 用户回调请求数据
type UserCallbackRequest struct {
	EventType       string      `json:"event_type"`
	OrderNo         string      `json:"order_no"`
	CustomerOrderNo string      `json:"customer_order_no"`
	TrackingNo      string      `json:"tracking_no"`
	Provider        string      `json:"provider"`
	Timestamp       string      `json:"timestamp"`
	Data            interface{} `json:"data"`
	Signature       string      `json:"-"` // 在header中传递
}

// UserCallbackResponse 用户回调响应
type UserCallbackResponse struct {
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// EnhancedForwardRecord 增强的转发记录（用户视图）
type EnhancedForwardRecord struct {
	ID              uuid.UUID        `json:"id"`
	EventType       string           `json:"event_type"`
	OrderNo         string           `json:"order_no"`
	CustomerOrderNo string           `json:"customer_order_no"`
	TrackingNo      string           `json:"tracking_no"`
	CallbackURL     string           `json:"callback_url"`
	RequestData     json.RawMessage  `json:"request_data"`
	ResponseData    *json.RawMessage `json:"response_data"`
	HTTPStatus      int              `json:"http_status"`
	Status          string           `json:"status"`
	RetryCount      int              `json:"retry_count"`
	ErrorMessage    string           `json:"error_message"`
	RequestAt       *time.Time       `json:"request_at"`
	ResponseAt      *time.Time       `json:"response_at"`
	CreatedAt       time.Time        `json:"created_at"`
}

// 🔥 新增：易达风格的用户回调数据结构

// YidaStyleCallbackData 易达风格的回调数据（新格式）
type YidaStyleCallbackData struct {
	OrderNo         string      `json:"order_no"`
	CustomerOrderNo string      `json:"customer_order_no,omitempty"`
	TrackingNo      string      `json:"tracking_no,omitempty"`
	Provider        string      `json:"provider"`
	Timestamp       string      `json:"timestamp"`
	PushType        int         `json:"push_type"`
	Context         interface{} `json:"context"`
}

// StatusUpdateContext 状态更新推送上下文 (push_type: 1)
type StatusUpdateContext struct {
	Status         StatusInfo             `json:"status"`
	UpdateTime     string                 `json:"update_time"`
	ProviderStatus *ProviderStatusInfo    `json:"provider_status,omitempty"`
	Extra          map[string]interface{} `json:"extra,omitempty"`
}

// BillingContext 计费推送上下文 (push_type: 2)
type BillingContext struct {
	Weight       WeightInfo `json:"weight"`
	PackageCount int        `json:"package_count"`
	Fees         []FeeInfo  `json:"fees"`
	TotalFee     float64    `json:"total_fee"`
	UpdateTime   string     `json:"update_time"`
}

// PickupContext 揽收推送上下文 (push_type: 3)
type PickupContext struct {
	Courier    CourierDetail `json:"courier"`
	PickupCode string        `json:"pickup_code,omitempty"`
	PickupTime string        `json:"pickup_time"`
}

// ExceptionContext 异常推送上下文 (push_type: 4)
type ExceptionContext struct {
	ExceptionType string `json:"exception_type"`
	Description   string `json:"description"`
	OccurredTime  string `json:"occurred_time"`
	Resolution    string `json:"resolution,omitempty"`
}

// OrderChangeContext 订单变更推送上下文 (push_type: 5)
type OrderChangeContext struct {
	ChangeType     string `json:"change_type"`
	NewTrackingNo  string `json:"new_tracking_no,omitempty"`
	NewProductType string `json:"new_product_type,omitempty"`
	ChangeTime     string `json:"change_time"`
	ChangeReason   string `json:"change_reason,omitempty"`
}

// StatusInfo 状态信息
type StatusInfo struct {
	Code        string `json:"code"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// ProviderStatusInfo 供应商状态信息
type ProviderStatusInfo struct {
	Code        interface{} `json:"code"` // 可能是数字或字符串
	Description string      `json:"description"`
}

// WeightInfo 重量信息
type WeightInfo struct {
	Actual  float64 `json:"actual"`           // 实际重量(kg)
	Charged float64 `json:"charged"`          // 计费重量(kg)
	Volume  float64 `json:"volume,omitempty"` // 体积(m³)
}

// FeeInfo 费用信息
type FeeInfo struct {
	Type   int     `json:"type"`   // 费用类型：0-运费，1-保价费，2-春节费，3-耗材费，10-逆向费，100-其他费
	Name   string  `json:"name"`   // 费用名称
	Amount float64 `json:"amount"` // 费用金额
}

// CourierDetail 快递员详细信息
type CourierDetail struct {
	Name    string       `json:"name,omitempty"`
	Phone   string       `json:"phone,omitempty"`
	Station *StationInfo `json:"station,omitempty"`
}

// StationInfo 站点信息
type StationInfo struct {
	Name    string `json:"name,omitempty"`
	Address string `json:"address,omitempty"`
	Phone   string `json:"phone,omitempty"`
}

// 🔥 新设计：完全统一的回调格式（供应商无关）
// ================================================================

// UnifiedCallbackData 统一的回调数据结构
// 遵循第一性原理：完全统一的格式，供应商无关，语义清晰
type UnifiedCallbackData struct {
	CustomerOrderNo string      `json:"customer_order_no"`       // 🔥 新增：客户订单号
	OrderNo         string      `json:"order_no"`                // 订单号（当前为供应商订单号，逐步迁移）
	PlatformOrderNo string      `json:"platform_order_no"`       // 🔥 新增：平台订单号（明确标识）
	TrackingNo      string      `json:"tracking_no"`             // 运单号
	Timestamp       string      `json:"timestamp"`               // 时间戳 (RFC3339格式)
	EventType       int         `json:"event_type"`              // 事件类型
	SupplierCode    string      `json:"supplier_code,omitempty"` // 🔥 新增：供应商标识字段（可选）
	Data            interface{} `json:"data"`                    // 事件数据
}

// RetryRecordListResponse 重试记录列表响应
type RetryRecordListResponse struct {
	Records    []*CallbackRetryRecord `json:"records"`
	Total      int64                  `json:"total"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int64                  `json:"total_pages"`
}

// RetryStatusResponse 重试状态响应
type RetryStatusResponse struct {
	IsRunning bool          `json:"is_running"`
	Metrics   *RetryMetrics `json:"metrics"`
}

// RetryMetrics 重试指标（从retry_scheduler.go复制）
type RetryMetrics struct {
	TotalScheduled   int64     `json:"total_scheduled"`
	TotalExecuted    int64     `json:"total_executed"`
	TotalSucceeded   int64     `json:"total_succeeded"`
	TotalFailed      int64     `json:"total_failed"`
	TotalCancelled   int64     `json:"total_cancelled"`
	AverageLatencyMs int64     `json:"average_latency_ms"`
	LastExecuteTime  time.Time `json:"last_execute_time"`
	ActiveWorkers    int       `json:"active_workers"`
}

// 🔥 修复：统一事件类型常量（避免与旧常量冲突）
const (
	UnifiedEventTypeStatusUpdate = 1 // 状态更新事件
	UnifiedEventTypeBilling      = 2 // 计费事件
	UnifiedEventTypePickup       = 3 // 揽收事件
	UnifiedEventTypeException    = 4 // 异常事件
	UnifiedEventTypeOrderChange  = 5 // 订单变更事件
)

// UnifiedStatusData 统一状态更新数据 (event_type: 1)
type UnifiedStatusData struct {
	Status      UnifiedStatus `json:"status"`      // 状态信息
	UpdateTime  string        `json:"update_time"` // 更新时间
	Description string        `json:"description"` // 状态描述
}

// UnifiedBillingData 统一计费数据 (event_type: 2)
type UnifiedBillingData struct {
	Weight       UnifiedWeight `json:"weight"`        // 重量信息
	PackageCount int           `json:"package_count"` // 包裹数量
	Fees         []UnifiedFee  `json:"fees"`          // 费用列表
	TotalAmount  float64       `json:"total_amount"`  // 总金额
	BillingTime  string        `json:"billing_time"`  // 计费时间
}

// UnifiedPickupData 统一揽收数据 (event_type: 3)
type UnifiedPickupData struct {
	Courier    UnifiedCourier `json:"courier"`     // 快递员信息
	PickupCode string         `json:"pickup_code"` // 取件码
	PickupTime string         `json:"pickup_time"` // 揽收时间
}

// UnifiedExceptionData 统一异常数据 (event_type: 4)
type UnifiedExceptionData struct {
	Type        string `json:"type"`        // 异常类型
	Description string `json:"description"` // 异常描述
	OccurTime   string `json:"occur_time"`  // 发生时间
	Solution    string `json:"solution"`    // 解决方案
}

// UnifiedOrderChangeData 统一订单变更数据 (event_type: 5)
type UnifiedOrderChangeData struct {
	ChangeType    string `json:"change_type"`     // 变更类型
	NewTrackingNo string `json:"new_tracking_no"` // 新运单号
	ChangeTime    string `json:"change_time"`     // 变更时间
	Reason        string `json:"reason"`          // 变更原因
}

// 统一的基础数据结构
// ================================================================

// UnifiedStatus 统一状态信息
type UnifiedStatus struct {
	Code string `json:"code"` // 状态代码 (delivered, in_transit, picked_up, etc.)
	Name string `json:"name"` // 状态名称 (已签收, 运输中, 已揽收, etc.)
}

// UnifiedWeight 统一重量信息
type UnifiedWeight struct {
	Actual  float64 `json:"actual"`  // 实际重量(kg)
	Charged float64 `json:"charged"` // 计费重量(kg)
	Volume  float64 `json:"volume"`  // 体积重量(kg)
}

// UnifiedFee 统一费用信息
type UnifiedFee struct {
	Type   string  `json:"type"`   // 费用类型 (freight, insurance, fuel, etc.)
	Name   string  `json:"name"`   // 费用名称
	Amount float64 `json:"amount"` // 费用金额
}

// UnifiedCourier 统一快递员信息
type UnifiedCourier struct {
	Name    string          `json:"name"`    // 快递员姓名
	Phone   string          `json:"phone"`   // 快递员电话
	Station *UnifiedStation `json:"station"` // 站点信息
}

// UnifiedStation 统一站点信息
type UnifiedStation struct {
	Name    string `json:"name"`    // 站点名称
	Address string `json:"address"` // 站点地址
	Phone   string `json:"phone"`   // 站点电话
}
