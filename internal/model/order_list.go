package model

import (
	"fmt"
	"strings"
	"time"
)

// OrderListRequest 订单列表查询请求
type OrderListRequest struct {
	// 分页参数
	Page     int `json:"page" form:"page"`           // 页码，从1开始
	PageSize int `json:"page_size" form:"page_size"` // 每页大小，最大100

	// 用户权限过滤（内部使用，不从前端传入）
	UserID string `json:"-" form:"-"` // 用户ID，用于权限控制

	// 过滤参数
	Status          string `json:"status" form:"status"`                       // 订单状态过滤
	ExpressType     string `json:"express_type" form:"express_type"`           // 快递类型过滤
	Provider        string `json:"provider" form:"provider"`                   // 供应商过滤
	CustomerOrderNo string `json:"customer_order_no" form:"customer_order_no"` // 客户订单号过滤
	OrderNo         string `json:"order_no" form:"order_no"`                   // 平台订单号过滤
	TrackingNo      string `json:"tracking_no" form:"tracking_no"`             // 运单号过滤

	// 🔥 新增：批量运单号查询支持
	TrackingNos []string `json:"tracking_nos" form:"tracking_nos"` // 批量运单号列表，支持逗号分隔或数组格式
	BatchMode   bool     `json:"batch_mode" form:"batch_mode"`     // 批量查询模式标识

	// 重量异常筛选
	WeightAnomaly string `json:"weight_anomaly" form:"weight_anomaly"` // 重量异常类型: overweight, underweight

	// 时间范围过滤
	StartTime string `json:"start_time" form:"start_time"` // 开始时间 (YYYY-MM-DD HH:mm:ss)
	EndTime   string `json:"end_time" form:"end_time"`     // 结束时间 (YYYY-MM-DD HH:mm:ss)

	// 排序参数
	SortBy    string `json:"sort_by" form:"sort_by"`       // 排序字段: created_at, updated_at, price
	SortOrder string `json:"sort_order" form:"sort_order"` // 排序方向: asc, desc

	// 🔥 新增：批量查询内部配置（不从前端传入）
	MaxBatchSize int `json:"-" form:"-"` // 最大批量查询数量限制，默认50
}

// OrderListItem 订单列表项
type OrderListItem struct {
	ID              int64   `json:"id"`                // 订单ID
	PlatformOrderNo string  `json:"platform_order_no"` // 🔥 新增：平台生成的全局唯一订单号
	CustomerOrderNo string  `json:"customer_order_no"` // 客户订单号
	OrderNo         string  `json:"order_no"`          // 🔥 兼容性字段：主要订单号（优先显示平台订单号）
	ProviderOrderNo string  `json:"provider_order_no"` // 🔥 新增：供应商返回的订单号
	TrackingNo      string  `json:"tracking_no"`       // 运单号
	ExpressType     string  `json:"express_type"`      // 快递类型
	ExpressName     string  `json:"express_name"`      // 快递公司名称
	ProductType     string  `json:"product_type"`      // 产品类型
	Provider        string  `json:"provider"`          // 供应商
	ProviderName    string  `json:"provider_name"`     // 供应商名称
	Status          string  `json:"status"`            // 订单状态
	StatusDesc      string  `json:"status_desc"`       // 状态描述
	Weight          float64 `json:"weight"`            // 重量
	Price           float64 `json:"price"`             // 价格
	ActualFee       float64 `json:"actual_fee"`        // 实收费用
	InsuranceFee    float64 `json:"insurance_fee"`     // 保价费
	BillingStatus   string  `json:"billing_status"`    // 计费状态
	SenderInfo      string  `json:"sender_info"`       // 寄件人信息摘要
	ReceiverInfo    string  `json:"receiver_info"`     // 收件人信息摘要

	// 重量体积信息
	OrderVolume   float64 `json:"order_volume"`   // 下单体积(m³)
	ActualWeight  float64 `json:"actual_weight"`  // 实际重量(kg)
	ActualVolume  float64 `json:"actual_volume"`  // 实际体积(m³)
	ChargedWeight float64 `json:"charged_weight"` // 计费重量(kg)

	// 揽件员信息
	CourierName  string `json:"courier_name"`  // 快递员姓名
	CourierPhone string `json:"courier_phone"` // 快递员电话
	CourierCode  string `json:"courier_code"`  // 快递员工号
	StationName  string `json:"station_name"`  // 网点名称
	PickupCode   string `json:"pickup_code"`   // 取件码

	// 🔥 企业级修复：添加供应商相关字段
	TaskId    string `json:"task_id"`    // 供应商任务ID（如快递100的TaskId）
	PollToken string `json:"poll_token"` // 供应商轮询令牌

	// 🔥 新增：预约时间信息
	PickupStartTime string `json:"pickup_start_time"` // 预约取件开始时间
	PickupEndTime   string `json:"pickup_end_time"`   // 预约取件结束时间

	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// OrderListResponse 订单列表响应
type OrderListResponse struct {
	Success bool           `json:"success"`
	Code    int            `json:"code"`
	Message string         `json:"message"`
	Data    *OrderListData `json:"data,omitempty"`
}

// OrderListData 订单列表数据
type OrderListData struct {
	Items      []*OrderListItem `json:"items"`       // 订单列表
	Total      int64            `json:"total"`       // 总数量
	Page       int              `json:"page"`        // 当前页码
	PageSize   int              `json:"page_size"`   // 每页大小
	TotalPages int              `json:"total_pages"` // 总页数
	HasNext    bool             `json:"has_next"`    // 是否有下一页
	HasPrev    bool             `json:"has_prev"`    // 是否有上一页

	// 🔥 新增：批量查询统计信息
	BatchStats *BatchQueryStats `json:"batch_stats,omitempty"` // 批量查询统计，仅在批量查询时返回
}

// BatchQueryStats 批量查询统计信息
type BatchQueryStats struct {
	TotalQueried int      `json:"total_queried"` // 查询的运单号总数
	FoundCount   int      `json:"found_count"`   // 找到的订单数量
	NotFound     []string `json:"not_found"`     // 未找到的运单号列表
	QueryTime    string   `json:"query_time"`    // 查询耗时（毫秒）
}

// OrderStatistics 订单统计信息（用户版本，不包含敏感信息）
type OrderStatistics struct {
	TotalOrders  int64            `json:"total_orders"`  // 总订单数
	TodayOrders  int64            `json:"today_orders"`  // 今日订单数
	StatusCounts map[string]int64 `json:"status_counts"` // 各状态订单数量
}

// OrderStatisticsResponse 订单统计响应
type OrderStatisticsResponse struct {
	Success bool             `json:"success"`
	Code    int              `json:"code"`
	Message string           `json:"message"`
	Data    *OrderStatistics `json:"data,omitempty"`
}

// 统一订单状态常量 - 基于三家供应商官方文档设计
const (
	// === 下单阶段 ===
	OrderStatusSubmitted    = "submitted"     // 已提交（快递100:0,200, 云通:100）
	OrderStatusSubmitFailed = "submit_failed" // 提交失败（快递100:610, 云通:400）
	OrderStatusPrintFailed  = "print_failed"  // 面单生成失败（快递100:201）

	// === 分配阶段 ===
	OrderStatusAssigned       = "assigned"        // 已分配（快递100:1, 云通:102,103）
	OrderStatusAwaitingPickup = "awaiting_pickup" // 等待揽收（快递100:2, 易达:1）

	// === 揽收阶段 ===
	OrderStatusPickedUp     = "picked_up"     // 已揽收（快递100:10, 易达:11, 云通:104）
	OrderStatusPickupFailed = "pickup_failed" // 揽收失败（快递100:11, 云通:204）

	// === 运输阶段 ===
	OrderStatusInTransit      = "in_transit"       // 运输中（快递100:101, 易达:2, 云通:2）
	OrderStatusOutForDelivery = "out_for_delivery" // 派送中（快递100:400）

	// === 签收阶段 ===
	OrderStatusDelivered         = "delivered"          // 已签收（快递100:13, 易达:3, 云通:3）
	OrderStatusDeliveredAbnormal = "delivered_abnormal" // 异常签收（快递100:14）

	// === 计费阶段 ===
	OrderStatusBilled = "billed" // 已计费（快递100:15, 云通:301）

	// === 异常状态 ===
	OrderStatusException = "exception" // 异常（易达:6, 云通:500）
	OrderStatusReturned  = "returned"  // 已退回（快递100:12）
	OrderStatusForwarded = "forwarded" // 已转寄（云通:501）

	// === 取消状态 ===
	OrderStatusCancelling = "cancelling" // 取消中（用户发起取消，等待供应商确认）
	OrderStatusCancelled  = "cancelled"  // 已取消（快递100:9,99, 易达:10, 云通:203）
	OrderStatusVoided     = "voided"     // 已作废（云通:205）

	// === 特殊状态 ===
	OrderStatusWeightUpdated = "weight_updated" // 重量更新（快递100:155, 云通:208）
	OrderStatusRevived       = "revived"        // 订单复活（快递100:166）
)

// 状态分组
var StatusGroups = map[string][]string{
	"active": {
		OrderStatusSubmitted, OrderStatusAssigned, OrderStatusAwaitingPickup,
		OrderStatusPickedUp, OrderStatusInTransit, OrderStatusOutForDelivery,
	},
	"completed": {
		OrderStatusDelivered, OrderStatusDeliveredAbnormal, OrderStatusBilled,
	},
	"failed": {
		OrderStatusSubmitFailed, OrderStatusPrintFailed, OrderStatusPickupFailed,
	},
	"cancelled": {
		OrderStatusCancelling, OrderStatusCancelled, OrderStatusVoided,
	},
	"exception": {
		OrderStatusException, OrderStatusReturned, OrderStatusForwarded,
	},
	"special": {
		OrderStatusWeightUpdated, OrderStatusRevived,
	},
}

// 状态优先级（用于状态冲突时的处理）
var StatusPriority = map[string]int{
	OrderStatusSubmitFailed:      1,
	OrderStatusPrintFailed:       2,
	OrderStatusCancelled:         3,
	OrderStatusCancelling:        4,
	OrderStatusVoided:            5,
	OrderStatusPickupFailed:      6,
	OrderStatusException:         7,
	OrderStatusReturned:          8,
	OrderStatusForwarded:         9,
	OrderStatusWeightUpdated:     10,
	OrderStatusRevived:           11,
	OrderStatusSubmitted:         12,
	OrderStatusAssigned:          13,
	OrderStatusAwaitingPickup:    14,
	OrderStatusPickedUp:          15,
	OrderStatusInTransit:         16,
	OrderStatusOutForDelivery:    17,
	OrderStatusDelivered:         18,
	OrderStatusDeliveredAbnormal: 19,
	OrderStatusBilled:            20,
}

// 合法的状态转换路径
var ValidTransitions = map[string][]string{
	OrderStatusSubmitted: {
		OrderStatusAssigned, OrderStatusSubmitFailed, OrderStatusPrintFailed, OrderStatusCancelled,
	},
	OrderStatusAssigned: {
		OrderStatusAwaitingPickup, OrderStatusCancelled,
	},
	OrderStatusAwaitingPickup: {
		OrderStatusPickedUp, OrderStatusPickupFailed, OrderStatusCancelled,
	},
	OrderStatusPickedUp: {
		OrderStatusInTransit, OrderStatusBilled, OrderStatusException, OrderStatusCancelled,
	},
	OrderStatusInTransit: {
		OrderStatusOutForDelivery, OrderStatusDelivered, OrderStatusException, OrderStatusReturned, OrderStatusForwarded,
	},
	OrderStatusOutForDelivery: {
		OrderStatusDelivered, OrderStatusDeliveredAbnormal, OrderStatusException, OrderStatusReturned,
	},
	OrderStatusDelivered: {
		OrderStatusBilled,
	},
	OrderStatusDeliveredAbnormal: {
		OrderStatusBilled,
	},
	OrderStatusException: {
		OrderStatusInTransit, OrderStatusReturned, OrderStatusCancelled,
	},
	OrderStatusPickupFailed: {
		OrderStatusAwaitingPickup, OrderStatusCancelled,
	},
	OrderStatusReturned: {
		OrderStatusCancelled,
	},
	OrderStatusCancelled: {
		OrderStatusRevived, // 快递100特有
	},
	OrderStatusWeightUpdated: {
		OrderStatusInTransit, OrderStatusBilled, OrderStatusCancelled, OrderStatusException,
	},
	// 终态：OrderStatusBilled, OrderStatusVoided, OrderStatusSubmitFailed, OrderStatusPrintFailed
}

// GetOrderStatusDesc 获取状态描述
func GetOrderStatusDesc(status string) string {
	statusMap := map[string]string{
		OrderStatusSubmitted:         "已提交",
		OrderStatusSubmitFailed:      "提交失败",
		OrderStatusPrintFailed:       "面单生成失败",
		OrderStatusAssigned:          "已分配",
		OrderStatusAwaitingPickup:    "等待揽收",
		OrderStatusPickedUp:          "已揽收",
		OrderStatusPickupFailed:      "揽收失败",
		OrderStatusInTransit:         "运输中",
		OrderStatusOutForDelivery:    "派送中",
		OrderStatusDelivered:         "已签收",
		OrderStatusDeliveredAbnormal: "异常签收",
		OrderStatusBilled:            "已计费",
		OrderStatusException:         "异常",
		OrderStatusReturned:          "已退回",
		OrderStatusForwarded:         "已转寄",
		OrderStatusCancelling:        "取消中",
		OrderStatusCancelled:         "已取消",
		OrderStatusVoided:            "已作废",
		OrderStatusWeightUpdated:     "重量更新",
		OrderStatusRevived:           "订单复活",
	}

	if desc, ok := statusMap[status]; ok {
		return desc
	}
	return status
}

// IsValidStatus 检查状态是否有效
func IsValidStatus(status string) bool {
	manager := GetStatusManager()
	return manager.GetStatusPriority(status) != 999
}

// IsValidTransition 检查状态转换是否合法
func IsValidTransition(fromStatus, toStatus string) bool {
	manager := GetStatusManager()
	return manager.IsValidTransition(fromStatus, toStatus)
}

// GetStatusGroup 获取状态所属分组
func GetStatusGroup(status string) string {
	manager := GetStatusManager()
	return manager.GetStatusGroup(status)
}

// IsTerminalStatus 检查是否为终态
func IsTerminalStatus(status string) bool {
	terminalStatuses := []string{
		OrderStatusBilled, OrderStatusVoided, OrderStatusSubmitFailed, OrderStatusPrintFailed,
	}

	for _, terminalStatus := range terminalStatuses {
		if status == terminalStatus {
			return true
		}
	}
	return false
}

// 获取供应商名称
func GetProviderName(provider string) string {
	providerMap := map[string]string{
		ExpressKuaidi100: "快递100",
		ExpressYida:      "易达",
		ExpressYuntong:   "云通",
	}

	if name, ok := providerMap[provider]; ok {
		return name
	}
	return provider
}

// ValidateOrderListRequest 验证订单列表请求参数
func ValidateOrderListRequest(req *OrderListRequest) error {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 🔥 新增：设置批量查询默认配置
	if req.MaxBatchSize <= 0 {
		req.MaxBatchSize = 50 // 默认最大批量查询50个运单号
	}

	// 🔥 新增：批量查询验证逻辑
	if len(req.TrackingNos) > 0 {
		// 自动启用批量模式
		req.BatchMode = true

		// 验证批量查询数量限制
		if len(req.TrackingNos) > req.MaxBatchSize {
			return NewValidationError(fmt.Sprintf("批量查询运单号数量不能超过%d个", req.MaxBatchSize))
		}

		// 验证运单号格式和去重
		uniqueTrackingNos := make(map[string]bool)
		validTrackingNos := make([]string, 0, len(req.TrackingNos))

		for _, trackingNo := range req.TrackingNos {
			// 清理空白字符
			trackingNo = strings.TrimSpace(trackingNo)
			if trackingNo == "" {
				continue // 跳过空运单号
			}

			// 验证运单号长度（一般在8-30位之间）
			if len(trackingNo) < 8 || len(trackingNo) > 30 {
				return NewValidationError(fmt.Sprintf("运单号格式不正确: %s", trackingNo))
			}

			// 去重处理
			if !uniqueTrackingNos[trackingNo] {
				uniqueTrackingNos[trackingNo] = true
				validTrackingNos = append(validTrackingNos, trackingNo)
			}
		}

		// 更新去重后的运单号列表
		req.TrackingNos = validTrackingNos

		// 检查去重后是否还有有效的运单号
		if len(req.TrackingNos) == 0 {
			return NewValidationError("没有有效的运单号")
		}

		// 批量查询时，清空单个运单号查询字段，避免冲突
		req.TrackingNo = ""
	}

	// 设置默认排序
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}

	// 验证排序字段
	validSortFields := map[string]bool{
		"created_at": true,
		"updated_at": true,
		"price":      true,
		"id":         true,
	}
	if !validSortFields[req.SortBy] {
		return NewValidationError("无效的排序字段")
	}

	// 验证排序方向
	if req.SortOrder != "asc" && req.SortOrder != "desc" {
		return NewValidationError("无效的排序方向")
	}

	return nil
}

// ===== 管理员订单管理模型 =====

// BatchPriceValidationRequest 批量价格验证请求
type BatchPriceValidationRequest struct {
	Provider  string  `json:"provider" form:"provider"`     // 供应商筛选
	StartTime string  `json:"start_time" form:"start_time"` // 开始时间
	EndTime   string  `json:"end_time" form:"end_time"`     // 结束时间
	OrderIDs  []int64 `json:"order_ids,omitempty"`          // 指定订单ID列表（可选）
	MaxCount  int     `json:"max_count" form:"max_count"`   // 最大处理数量，默认100
}

// BatchPriceValidationResponse 批量价格验证响应
type BatchPriceValidationResponse struct {
	Success bool                      `json:"success"`
	Code    int                       `json:"code"`
	Message string                    `json:"message"`
	Data    *BatchPriceValidationData `json:"data,omitempty"`
}

// BatchPriceValidationData 批量价格验证数据
type BatchPriceValidationData struct {
	TotalOrders     int                      `json:"total_orders"`     // 总订单数
	ProcessedOrders int                      `json:"processed_orders"` // 已处理订单数
	SuccessOrders   int                      `json:"success_orders"`   // 成功订单数
	FailedOrders    int                      `json:"failed_orders"`    // 失败订单数
	SkippedOrders   int                      `json:"skipped_orders"`   // 跳过订单数
	Results         []*PriceValidationResult `json:"results"`          // 验证结果列表
	Summary         *PriceValidationSummary  `json:"summary"`          // 汇总信息
}

// PriceValidationResult 单个价格验证结果
type PriceValidationResult struct {
	OrderID       int64   `json:"order_id"`
	OrderNo       string  `json:"order_no"`
	Provider      string  `json:"provider"`
	SystemPrice   float64 `json:"system_price"`
	ProviderPrice float64 `json:"provider_price"`
	ProfitStatus  string  `json:"profit_status"`
	QueryStatus   string  `json:"query_status"`
	ErrorMessage  string  `json:"error_message,omitempty"`
	Supported     bool    `json:"supported"`
	QueryTime     string  `json:"query_time"`
}

// PriceValidationSummary 价格验证汇总
type PriceValidationSummary struct {
	TotalProfit      float64 `json:"total_profit"`      // 总盈利金额
	TotalLoss        float64 `json:"total_loss"`        // 总亏损金额
	ProfitCount      int     `json:"profit_count"`      // 盈利订单数
	LossCount        int     `json:"loss_count"`        // 亏损订单数
	BreakEvenCount   int     `json:"break_even_count"`  // 持平订单数
	UnsupportedCount int     `json:"unsupported_count"` // 不支持订单数
}

// AdminOrderListRequest 管理员订单列表查询请求
type AdminOrderListRequest struct {
	// 继承基础订单列表请求
	OrderListRequest

	// 管理员特有过滤条件
	UserID        string `json:"user_id" form:"user_id"`               // 用户ID过滤
	Username      string `json:"username" form:"username"`             // 用户名过滤
	UserEmail     string `json:"user_email" form:"user_email"`         // 用户邮箱过滤
	CompanyCode   string `json:"company_code" form:"company_code"`     // 快递公司代码过滤
	BillingStatus string `json:"billing_status" form:"billing_status"` // 计费状态过滤

	// 价格范围过滤
	PriceMin *float64 `json:"price_min" form:"price_min"` // 最小价格
	PriceMax *float64 `json:"price_max" form:"price_max"` // 最大价格

	// 重量范围过滤
	WeightMin *float64 `json:"weight_min" form:"weight_min"` // 最小重量
	WeightMax *float64 `json:"weight_max" form:"weight_max"` // 最大重量

	// 高级搜索
	SearchKeyword string   `json:"search_keyword" form:"search_keyword"` // 关键词搜索
	SearchFields  []string `json:"search_fields" form:"search_fields"`   // 搜索字段

	// 时间范围扩展
	CreatedStartTime string `json:"created_start_time" form:"created_start_time"` // 创建开始时间
	CreatedEndTime   string `json:"created_end_time" form:"created_end_time"`     // 创建结束时间
	UpdatedStartTime string `json:"updated_start_time" form:"updated_start_time"` // 更新开始时间
	UpdatedEndTime   string `json:"updated_end_time" form:"updated_end_time"`     // 更新结束时间

	// 数据导出标识
	IsExport bool `json:"is_export" form:"is_export"` // 是否为导出请求
}

// AdminOrderListItem 管理员订单列表项
type AdminOrderListItem struct {
	// 继承基础订单列表项
	OrderListItem

	// 用户信息
	User struct {
		ID       string `json:"id"`
		Username string `json:"username"`
		Email    string `json:"email"`
		IsActive bool   `json:"is_active"`
	} `json:"user"`

	// 费用详情
	Billing struct {
		EstimatedFee  float64 `json:"estimated_fee"`  // 预估费用
		ActualFee     float64 `json:"actual_fee"`     // 实际费用
		InsuranceFee  float64 `json:"insurance_fee"`  // 保价费
		BillingStatus string  `json:"billing_status"` // 计费状态
		FeeDifference float64 `json:"fee_difference"` // 费用差额
	} `json:"billing"`

	// 价格验证信息（简化版）
	PriceValidation struct {
		ProviderPrice float64 `json:"provider_price"` // 供应商实际价格
		SystemPrice   float64 `json:"system_price"`   // 系统订单价格
		ProfitStatus  string  `json:"profit_status"`  // 盈亏状态：profit, loss, break_even
		QueryStatus   string  `json:"query_status"`   // 查询状态：pending, success, failed, cached
		QueryTime     string  `json:"query_time"`     // 查询时间
		ErrorMessage  string  `json:"error_message"`  // 错误信息
		Supported     bool    `json:"supported"`      // 供应商是否支持查询
	} `json:"price_validation,omitempty"`

	// 价格对比信息
	PriceComparison struct {
		ProviderActualPrice  float64             `json:"provider_actual_price"`   // 供应商实际价格
		SystemEstimatedPrice float64             `json:"system_estimated_price"`  // 系统预估价格
		PriceDifference      float64             `json:"price_difference"`        // 价格差异（供应商价格 - 系统价格）
		DifferencePercent    float64             `json:"difference_percent"`      // 差异百分比
		QueryStatus          string              `json:"query_status"`            // 查询状态：pending, success, failed, cached
		QueryTime            *time.Time          `json:"query_time,omitempty"`    // 查询时间
		ErrorMessage         string              `json:"error_message,omitempty"` // 错误信息
		CacheExpiry          *time.Time          `json:"cache_expiry,omitempty"`  // 缓存过期时间
		FeeDetails           []ShippingFeeDetail `json:"fee_details,omitempty"`   // 费用明细
		Supported            bool                `json:"supported"`               // 供应商是否支持查询
	} `json:"price_comparison,omitempty"`

	// 重量体积详情
	WeightVolume struct {
		OrderWeight   float64 `json:"order_weight"`   // 下单重量
		OrderVolume   float64 `json:"order_volume"`   // 下单体积
		ActualWeight  float64 `json:"actual_weight"`  // 实际重量
		ActualVolume  float64 `json:"actual_volume"`  // 实际体积
		ChargedWeight float64 `json:"charged_weight"` // 计费重量
	} `json:"weight_volume"`

	// 快递公司信息
	ExpressCompany struct {
		Code        string `json:"code"`
		Name        string `json:"name"`
		EnglishName string `json:"english_name"`
	} `json:"express_company"`

	// 操作信息
	Operations struct {
		CanCancel       bool `json:"can_cancel"`        // 是否可取消
		CanUpdateStatus bool `json:"can_update_status"` // 是否可更新状态
		CanRefund       bool `json:"can_refund"`        // 是否可退款
	} `json:"operations"`
}

// AdminOrderListResponse 管理员订单列表响应
type AdminOrderListResponse struct {
	Success bool                `json:"success"`
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Data    *AdminOrderListData `json:"data,omitempty"`
}

// AdminOrderListData 管理员订单列表数据
type AdminOrderListData struct {
	Items      []*AdminOrderListItem `json:"items"`                // 订单列表
	Total      int64                 `json:"total"`                // 总数量
	Page       int                   `json:"page"`                 // 当前页码
	PageSize   int                   `json:"page_size"`            // 每页大小
	TotalPages int                   `json:"total_pages"`          // 总页数
	HasNext    bool                  `json:"has_next"`             // 是否有下一页
	HasPrev    bool                  `json:"has_prev"`             // 是否有上一页
	Statistics *AdminOrderStatistics `json:"statistics,omitempty"` // 统计信息
}

// AdminOrderStatistics 管理员订单统计信息
type AdminOrderStatistics struct {
	// 基础统计
	TotalOrders int64   `json:"total_orders"` // 总订单数
	TodayOrders int64   `json:"today_orders"` // 今日订单数
	TotalAmount float64 `json:"total_amount"` // 总金额
	TodayAmount float64 `json:"today_amount"` // 今日金额

	// 状态统计
	StatusCounts   map[string]int64 `json:"status_counts"`   // 各状态订单数量
	ProviderCounts map[string]int64 `json:"provider_counts"` // 各供应商订单数量
	CompanyCounts  map[string]int64 `json:"company_counts"`  // 各快递公司订单数量

	// 计费统计
	BillingCounts map[string]int64 `json:"billing_counts"` // 各计费状态订单数量

	// 用户统计
	ActiveUsers int64 `json:"active_users"` // 活跃用户数
	NewUsers    int64 `json:"new_users"`    // 新用户数

	// 异常统计
	ExceptionOrders int64 `json:"exception_orders"` // 异常订单数
	RefundOrders    int64 `json:"refund_orders"`    // 退款订单数

	// 时间范围
	StatisticsTime struct {
		StartTime time.Time `json:"start_time"`
		EndTime   time.Time `json:"end_time"`
	} `json:"statistics_time"`
}

// ValidateAdminOrderListRequest 验证管理员订单列表请求参数
func ValidateAdminOrderListRequest(req *AdminOrderListRequest) error {
	// 先验证基础订单列表请求
	if err := ValidateOrderListRequest(&req.OrderListRequest); err != nil {
		return err
	}

	// 验证价格范围
	if req.PriceMin != nil && *req.PriceMin < 0 {
		return NewValidationError("最小价格不能为负数")
	}
	if req.PriceMax != nil && *req.PriceMax < 0 {
		return NewValidationError("最大价格不能为负数")
	}
	if req.PriceMin != nil && req.PriceMax != nil && *req.PriceMin > *req.PriceMax {
		return NewValidationError("最小价格不能大于最大价格")
	}

	// 验证重量范围
	if req.WeightMin != nil && *req.WeightMin < 0 {
		return NewValidationError("最小重量不能为负数")
	}
	if req.WeightMax != nil && *req.WeightMax < 0 {
		return NewValidationError("最大重量不能为负数")
	}
	if req.WeightMin != nil && req.WeightMax != nil && *req.WeightMin > *req.WeightMax {
		return NewValidationError("最小重量不能大于最大重量")
	}

	// 验证搜索字段
	if len(req.SearchFields) > 0 {
		validSearchFields := map[string]bool{
			"customer_order_no": true,
			"order_no":          true,
			"tracking_no":       true,
			"sender_info":       true,
			"receiver_info":     true,
			"username":          true,
			"user_email":        true,
		}
		for _, field := range req.SearchFields {
			if !validSearchFields[field] {
				return NewValidationError(fmt.Sprintf("无效的搜索字段: %s", field))
			}
		}
	}

	// 验证计费状态
	if req.BillingStatus != "" {
		validBillingStatuses := []string{"pending", "confirmed", "refunded"} // 已删除settled
		isValid := false
		for _, status := range validBillingStatuses {
			if req.BillingStatus == status {
				isValid = true
				break
			}
		}
		if !isValid {
			return NewValidationError("无效的计费状态")
		}
	}

	// 验证导出请求的特殊限制
	if req.IsExport {
		if req.PageSize > 10000 {
			req.PageSize = 10000 // 导出限制最大10000条
		}
	}

	return nil
}

// GetAdminOrderOperationPermissions 获取管理员订单操作权限
func GetAdminOrderOperationPermissions(status string) map[string]bool {
	permissions := map[string]bool{
		"can_cancel":        false,
		"can_update_status": false,
		"can_refund":        false,
	}

	switch status {
	case OrderStatusSubmitted, OrderStatusAssigned, OrderStatusAwaitingPickup:
		permissions["can_cancel"] = true
		permissions["can_update_status"] = true
	case OrderStatusPickedUp, OrderStatusInTransit:
		permissions["can_update_status"] = true
	case OrderStatusDelivered, OrderStatusDeliveredAbnormal:
		permissions["can_refund"] = true
		permissions["can_update_status"] = true
	case OrderStatusException:
		permissions["can_cancel"] = true
		permissions["can_update_status"] = true
		permissions["can_refund"] = true
	}

	return permissions
}

// GetValidAdminStatusTransitions 获取管理员可执行的状态转换
func GetValidAdminStatusTransitions(currentStatus string) []string {
	adminTransitions := map[string][]string{
		OrderStatusSubmitted: {
			OrderStatusAssigned, OrderStatusCancelled, OrderStatusException,
		},
		OrderStatusAssigned: {
			OrderStatusAwaitingPickup, OrderStatusPickedUp, OrderStatusCancelled, OrderStatusException,
		},
		OrderStatusAwaitingPickup: {
			OrderStatusPickedUp, OrderStatusPickupFailed, OrderStatusCancelled, OrderStatusException,
		},
		OrderStatusPickedUp: {
			OrderStatusInTransit, OrderStatusException, OrderStatusCancelled,
		},
		OrderStatusInTransit: {
			OrderStatusOutForDelivery, OrderStatusDelivered, OrderStatusException, OrderStatusReturned,
		},
		OrderStatusOutForDelivery: {
			OrderStatusDelivered, OrderStatusDeliveredAbnormal, OrderStatusException, OrderStatusReturned,
		},
		OrderStatusException: {
			OrderStatusInTransit, OrderStatusCancelled, OrderStatusReturned,
		},
		OrderStatusPickupFailed: {
			OrderStatusAwaitingPickup, OrderStatusCancelled,
		},
		OrderStatusReturned: {
			OrderStatusCancelled,
		},
	}

	if transitions, exists := adminTransitions[currentStatus]; exists {
		return transitions
	}
	return []string{}
}

// AdminOrderDetail 管理员订单详情
type AdminOrderDetail struct {
	// 基础订单信息
	OrderRecord

	// 用户详细信息
	User struct {
		ID          string    `json:"id"`
		Username    string    `json:"username"`
		Email       string    `json:"email"`
		IsActive    bool      `json:"is_active"`
		CreatedAt   time.Time `json:"created_at"`
		DefaultRole string    `json:"default_role"`
	} `json:"user"`

	// 快递公司详细信息
	ExpressCompany struct {
		ID                   string `json:"id"`
		Code                 string `json:"code"`
		Name                 string `json:"name"`
		EnglishName          string `json:"english_name"`
		LogoURL              string `json:"logo_url"`
		OfficialWebsite      string `json:"official_website"`
		CustomerServicePhone string `json:"customer_service_phone"`
	} `json:"express_company"`

	// 支付信息
	Payment struct {
		Method        string     `json:"method"`
		Status        string     `json:"status"`
		Amount        float64    `json:"amount"`
		TransactionID string     `json:"transaction_id"`
		PaidAt        *time.Time `json:"paid_at"`
	} `json:"payment"`

	// 操作历史
	OperationHistory []AdminOrderOperation `json:"operation_history"`

	// 物流轨迹
	TrackingHistory []AdminTrackingEvent `json:"tracking_history"`

	// 审计信息
	AuditInfo struct {
		CreatedBy         string    `json:"created_by"`
		UpdatedBy         string    `json:"updated_by"`
		LastModified      time.Time `json:"last_modified"`
		ModificationCount int       `json:"modification_count"`
	} `json:"audit_info"`
}

// AdminOrderOperation 管理员订单操作记录
type AdminOrderOperation struct {
	ID         string                 `json:"id"`
	OrderID    int64                  `json:"order_id"`
	OperatorID string                 `json:"operator_id"`
	Operator   string                 `json:"operator"`
	Operation  string                 `json:"operation"`
	OldStatus  string                 `json:"old_status"`
	NewStatus  string                 `json:"new_status"`
	Reason     string                 `json:"reason"`
	Details    map[string]interface{} `json:"details"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	CreatedAt  time.Time              `json:"created_at"`
}

// AdminTrackingEvent 管理员物流轨迹事件
type AdminTrackingEvent struct {
	ID          string    `json:"id"`
	OrderID     int64     `json:"order_id"`
	Status      string    `json:"status"`
	Description string    `json:"description"`
	Location    string    `json:"location"`
	Timestamp   time.Time `json:"timestamp"`
	Source      string    `json:"source"` // system, callback, manual
	RawData     string    `json:"raw_data"`
	CreatedAt   time.Time `json:"created_at"`
}
