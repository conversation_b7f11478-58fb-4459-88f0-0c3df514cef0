package utils

import (
	"fmt"
	"math"

	"github.com/your-org/go-kuaidi/internal/express"
	"go.uber.org/zap"
)

// VolumeWeightCalculator 企业级体积重量计算器
// 支持各快递公司不同的抛比配置，确保计费准确性
type VolumeWeightCalculator struct {
	logger             *zap.Logger
	expressCompanyRepo express.ExpressCompanyRepository
}

// NewVolumeWeightCalculator 创建体积重量计算器
func NewVolumeWeightCalculator(
	logger *zap.Logger,
	expressCompanyRepo express.ExpressCompanyRepository,
) *VolumeWeightCalculator {
	return &VolumeWeightCalculator{
		logger:             logger,
		expressCompanyRepo: expressCompanyRepo,
	}
}

// VolumeWeightResult 体积重量计算结果
type VolumeWeightResult struct {
	ActualWeight    float64 `json:"actual_weight"`    // 实际重量(kg)
	VolumeWeight    float64 `json:"volume_weight"`    // 体积重量(kg)
	ChargedWeight   float64 `json:"charged_weight"`   // 计费重量(kg)
	VolumeRatio     int     `json:"volume_ratio"`     // 使用的抛比
	ExpressCode     string  `json:"express_code"`     // 快递公司代码
	CalculationDesc string  `json:"calculation_desc"` // 计算说明
}

// CalculateChargedWeight 计算计费重量（考虑体积重量）
func (calc *VolumeWeightCalculator) CalculateChargedWeight(
	expressCode string,
	actualWeight, volume float64,
) (float64, error) {
	// 如果没有体积信息，直接返回实际重量（向上取整）
	if volume <= 0 {
		result := math.Ceil(actualWeight)
		calc.logger.Debug("无体积信息，使用实际重量",
			zap.String("express_code", expressCode),
			zap.Float64("actual_weight", actualWeight),
			zap.Float64("charged_weight", result))
		return result, nil
	}

	// 获取快递公司抛比配置
	volumeRatio, err := calc.GetVolumeWeightRatio(expressCode)
	if err != nil {
		return 0, fmt.Errorf("获取抛比配置失败: %w", err)
	}

	// 计算体积重量（将 m³ 转换为 cm³）
	volumeCm3 := volume * 1000000
	volumeWeight := volumeCm3 / float64(volumeRatio)

	// 取实际重量和体积重量的较大值
	chargedWeight := math.Max(actualWeight, volumeWeight)

	// 向上取整到最近的整数（快递行业标准）
	result := math.Ceil(chargedWeight)

	calc.logger.Debug("体积重量计算完成",
		zap.String("express_code", expressCode),
		zap.Int("volume_ratio", volumeRatio),
		zap.Float64("actual_weight", actualWeight),
		zap.Float64("volume_m3", volume),
		zap.Float64("volume_weight", volumeWeight),
		zap.Float64("charged_weight", result))

	return result, nil
}

// ExpressCompanyConfig 快递公司配置简化结构
type ExpressCompanyConfig struct {
	Code                string
	Name                string
	VolumeWeightRatio   int
	SupportVolumeWeight bool
}

// CalculateVolumeFromDimensions 根据长宽高计算体积
// 输入单位：厘米，输出单位：立方厘米
func (calc *VolumeWeightCalculator) CalculateVolumeFromDimensions(length, width, height float64) float64 {
	if length <= 0 || width <= 0 || height <= 0 {
		return 0
	}
	return length * width * height
}

// ValidateVolumeWeight 验证体积重量计算结果
func (calc *VolumeWeightCalculator) ValidateVolumeWeight(result *VolumeWeightResult) error {
	if result == nil {
		return fmt.Errorf("计算结果不能为空")
	}

	if result.ActualWeight <= 0 {
		return fmt.Errorf("实际重量必须大于0")
	}

	if result.ChargedWeight < result.ActualWeight {
		return fmt.Errorf("计费重量不能小于实际重量")
	}

	if result.VolumeWeight > 0 && result.ChargedWeight < result.VolumeWeight {
		return fmt.Errorf("计费重量不能小于体积重量")
	}

	return nil
}

// GetSupportedExpressCompanies 获取支持体积重量计算的快递公司列表
func (calc *VolumeWeightCalculator) GetSupportedExpressCompanies() []ExpressCompanyConfig {
	// 构建查询条件：只查询活跃的快递公司
	isActive := true
	filter := express.CompanyFilter{
		IsActive: &isActive,
	}
	pagination := express.Pagination{
		Page:     1,
		PageSize: 100, // 足够大的页面大小，获取所有活跃快递公司
	}

	result, err := calc.expressCompanyRepo.GetCompanies(filter, pagination)
	if err != nil {
		calc.logger.Error("获取支持体积重量计算的快递公司失败", zap.Error(err))
		return []ExpressCompanyConfig{}
	}

	var supported []ExpressCompanyConfig
	for _, company := range result.Companies {
		// 从数据库获取体积重量支持配置，不使用默认值
		supported = append(supported, ExpressCompanyConfig{
			Code:                company.Code,
			Name:                company.Name,
			VolumeWeightRatio:   company.VolumeWeightRatio,
			SupportVolumeWeight: company.SupportVolumeWeight, // 从数据库获取，不使用默认值
		})
	}

	calc.logger.Debug("获取支持体积重量计算的快递公司",
		zap.Int("total_count", len(supported)))

	return supported
}

// GetVolumeWeightRatio 获取快递公司体积重量抛比
func (calc *VolumeWeightCalculator) GetVolumeWeightRatio(expressCode string) (int, error) {
	if calc.expressCompanyRepo == nil {
		return 0, fmt.Errorf("ExpressCompanyRepository 未初始化")
	}

	company, err := calc.expressCompanyRepo.GetCompanyByCode(expressCode)
	if err != nil {
		return 0, fmt.Errorf("获取快递公司配置失败: %w", err)
	}

	if !company.IsActive {
		return 0, fmt.Errorf("快递公司 %s 已禁用", expressCode)
	}

	if company.VolumeWeightRatio <= 0 {
		return 0, fmt.Errorf("快递公司 %s 抛比配置无效: %d", expressCode, company.VolumeWeightRatio)
	}

	calc.logger.Debug("获取抛比配置成功",
		zap.String("express_code", expressCode),
		zap.String("company_name", company.Name),
		zap.Int("volume_ratio", company.VolumeWeightRatio))

	return company.VolumeWeightRatio, nil
}
