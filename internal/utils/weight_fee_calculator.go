package utils

import (
	"fmt"
	"math"

	"github.com/your-org/go-kuaidi/internal/express"
	"go.uber.org/zap"
)

// WeightFeeCalculator 重量费用计算器
// 实现正确的计费重量向上取整和跨越整数区间判定逻辑
type WeightFeeCalculator struct {
	logger           *zap.Logger
	volumeWeightCalc *VolumeWeightCalculator
}

// NewWeightFeeCalculator 创建重量费用计算器
func NewWeightFeeCalculator(
	logger *zap.Logger,
	expressCompanyRepo express.ExpressCompanyRepository,
) *WeightFeeCalculator {
	return &WeightFeeCalculator{
		logger:           logger,
		volumeWeightCalc: NewVolumeWeightCalculator(logger, expressCompanyRepo),
	}
}

// CalculateChargedWeight 计算计费重量（向上取整）
// 使用企业级体积重量计算器，根据快递公司的抛比配置计算正确的计费重量
func (wfc *WeightFeeCalculator) CalculateChargedWeight(actualWeight, volumeCm3 float64) float64 {
	// 🔥 企业级修复：使用默认抛比8000进行计算（兼容旧接口）
	// 注意：建议使用 CalculateChargedWeightWithExpressCode 方法获得更准确的结果
	return wfc.CalculateChargedWeightWithExpressCode("", actualWeight, volumeCm3)
}

// CalculateChargedWeightWithExpressCode 根据快递公司计算计费重量
// 这是推荐的方法，能够根据不同快递公司的抛比配置计算准确的计费重量
func (wfc *WeightFeeCalculator) CalculateChargedWeightWithExpressCode(
	expressCode string,
	actualWeight,
	volumeCm3 float64,
) float64 {
	// 将volumeCm3转换为m³后传递给VolumeWeightCalculator
	volumeM3 := volumeCm3 / 1000000

	// 使用企业级体积重量计算器
	result, err := wfc.volumeWeightCalc.CalculateChargedWeight(expressCode, actualWeight, volumeM3)
	if err != nil {
		wfc.logger.Error("体积重量计算失败，使用实际重量",
			zap.String("express_code", expressCode),
			zap.Float64("actual_weight", actualWeight),
			zap.Float64("volume_cm3", volumeCm3),
			zap.Error(err))
		return math.Ceil(actualWeight)
	}

	wfc.logger.Debug("体积重量计算成功",
		zap.String("express_code", expressCode),
		zap.Float64("charged_weight", result))

	return result
}

// WeightFeeResult 重量费用计算结果
type WeightFeeResult struct {
	IsOverweight     bool    `json:"is_overweight"`     // 是否超重
	IsUnderweight    bool    `json:"is_underweight"`    // 是否超轻
	OverweightFee    float64 `json:"overweight_fee"`    // 超重费用
	UnderweightFee   float64 `json:"underweight_fee"`   // 超轻费用
	OrderChargedKg   float64 `json:"order_charged_kg"`  // 下单计费重量(整数KG)
	ActualChargedKg  float64 `json:"actual_charged_kg"` // 实际计费重量(整数KG)
	WeightDifference float64 `json:"weight_difference"` // 重量差异(KG)
	Reason           string  `json:"reason"`            // 计算原因
}

// CalculateWeightFees 计算超重/超轻费用
// 实现正确的跨越整数重量区间判定逻辑
func (wfc *WeightFeeCalculator) CalculateWeightFees(
	orderWeight float64, // 下单重量
	actualWeight float64, // 实际重量
	orderVolume float64, // 下单体积
	actualVolume float64, // 实际体积
	overweightFeeFromAPI float64, // API返回的超重费用
	underweightFeeFromAPI float64, // API返回的超轻费用
) *WeightFeeResult {
	// 1. 计算下单时的计费重量（向上取整）
	orderChargedWeight := wfc.CalculateChargedWeight(orderWeight, orderVolume)

	// 2. 计算实际的计费重量（向上取整）
	actualChargedWeight := wfc.CalculateChargedWeight(actualWeight, actualVolume)

	// 3. 计算重量差异
	weightDifference := actualChargedWeight - orderChargedWeight

	result := &WeightFeeResult{
		OrderChargedKg:   orderChargedWeight,
		ActualChargedKg:  actualChargedWeight,
		WeightDifference: weightDifference,
	}

	wfc.logger.Info("重量费用计算详情",
		zap.Float64("order_weight", orderWeight),
		zap.Float64("actual_weight", actualWeight),
		zap.Float64("order_charged_weight", orderChargedWeight),
		zap.Float64("actual_charged_weight", actualChargedWeight),
		zap.Float64("weight_difference", weightDifference),
		zap.Float64("api_overweight_fee", overweightFeeFromAPI),
		zap.Float64("api_underweight_fee", underweightFeeFromAPI))

	// 4. 判定超重/超轻逻辑
	if weightDifference > 0 {
		// 实际重量大于下单重量，可能超重
		result.IsOverweight = wfc.isValidOverweight(orderChargedWeight, actualChargedWeight)
		if result.IsOverweight {
			result.OverweightFee = overweightFeeFromAPI
			result.Reason = wfc.buildOverweightReason(orderChargedWeight, actualChargedWeight, overweightFeeFromAPI)
		} else {
			result.Reason = wfc.buildNoOverweightReason(orderChargedWeight, actualChargedWeight)
		}
	} else if weightDifference < 0 {
		// 实际重量小于下单重量，可能超轻
		result.IsUnderweight = wfc.isValidUnderweight(orderChargedWeight, actualChargedWeight)
		if result.IsUnderweight {
			result.UnderweightFee = underweightFeeFromAPI
			result.Reason = wfc.buildUnderweightReason(orderChargedWeight, actualChargedWeight, underweightFeeFromAPI)
		} else {
			result.Reason = wfc.buildNoUnderweightReason(orderChargedWeight, actualChargedWeight)
		}
	} else {
		// 重量相同，无费用调整
		result.Reason = "计费重量相同，无需调整"
	}

	wfc.logger.Info("重量费用计算结果",
		zap.Bool("is_overweight", result.IsOverweight),
		zap.Bool("is_underweight", result.IsUnderweight),
		zap.Float64("overweight_fee", result.OverweightFee),
		zap.Float64("underweight_fee", result.UnderweightFee),
		zap.String("reason", result.Reason))

	return result
}

// isValidOverweight 判断是否为有效的超重
// 规则：只有跨越整数重量区间时才算超重
func (wfc *WeightFeeCalculator) isValidOverweight(orderChargedKg, actualChargedKg float64) bool {
	// 必须跨越整数区间才算超重
	// 例如：下单1.01KG(计费2KG) → 回调2.01KG(计费3KG) = 超重
	// 例如：下单1.01KG(计费2KG) → 回调1.5KG(计费2KG) = 不算超重
	return actualChargedKg > orderChargedKg
}

// isValidUnderweight 判断是否为有效的超轻
// 规则：只有跨越整数重量区间才算超轻，但1KG以内的调整无效
func (wfc *WeightFeeCalculator) isValidUnderweight(orderChargedKg, actualChargedKg float64) bool {
	// 1. 必须跨越整数区间
	if actualChargedKg >= orderChargedKg {
		return false
	}

	// 2. 如果实际计费重量降到1KG以内，需要特殊判断
	if actualChargedKg <= 1.0 {
		// 只有当下单重量大于1KG时，降到1KG才算超轻
		// 例如：下单2KG → 回调1KG = 超轻 ✅
		// 例如：下单0.9KG(计费1KG) → 回调0.5KG(计费1KG) = 不算超轻 ❌
		return orderChargedKg > 1.0
	}

	// 3. 其他情况：必须跨越整数区间才算超轻
	// 例如：下单3KG → 回调2KG = 超轻 ✅
	// 例如：下单2KG → 回调1.01KG(计费2KG) = 不算超轻 ❌
	return true
}

// buildOverweightReason 构建超重原因
func (wfc *WeightFeeCalculator) buildOverweightReason(orderChargedKg, actualChargedKg, fee float64) string {
	return fmt.Sprintf("超重：%.0fKG → %.0fKG，超重费用¥%.2f", orderChargedKg, actualChargedKg, fee)
}

// buildUnderweightReason 构建超轻原因
func (wfc *WeightFeeCalculator) buildUnderweightReason(orderChargedKg, actualChargedKg, fee float64) string {
	return fmt.Sprintf("超轻：%.0fKG → %.0fKG，超轻费用¥%.2f", orderChargedKg, actualChargedKg, fee)
}

// buildNoOverweightReason 构建不算超重的原因
func (wfc *WeightFeeCalculator) buildNoOverweightReason(orderChargedKg, actualChargedKg float64) string {
	return fmt.Sprintf("重量调整：%.0fKG → %.0fKG，未跨越整数区间，不算超重", orderChargedKg, actualChargedKg)
}

// buildNoUnderweightReason 构建不算超轻的原因
func (wfc *WeightFeeCalculator) buildNoUnderweightReason(orderChargedKg, actualChargedKg float64) string {
	if actualChargedKg <= 1.0 {
		return fmt.Sprintf("重量调整：%.0fKG → %.0fKG，1KG以内无效，不算超轻", orderChargedKg, actualChargedKg)
	}
	return fmt.Sprintf("重量调整：%.0fKG → %.0fKG，未跨越整数区间，不算超轻", orderChargedKg, actualChargedKg)
}

// ValidateWeightFeeScenarios 验证重量费用场景（用于测试）
func (wfc *WeightFeeCalculator) ValidateWeightFeeScenarios() []TestScenario {
	return []TestScenario{
		// 超重场景
		{
			Name:         "有效超重：1.01KG → 2.01KG",
			OrderWeight:  1.01,
			ActualWeight: 2.01,
			Expected:     ScenarioResult{IsOverweight: true, IsUnderweight: false},
		},
		{
			Name:         "无效超重：1.01KG → 1.5KG",
			OrderWeight:  1.01,
			ActualWeight: 1.5,
			Expected:     ScenarioResult{IsOverweight: false, IsUnderweight: false},
		},

		// 超轻场景
		{
			Name:         "有效超轻：2KG → 1KG",
			OrderWeight:  2.0,
			ActualWeight: 1.0,
			Expected:     ScenarioResult{IsOverweight: false, IsUnderweight: true},
		},
		{
			Name:         "无效超轻：0.9KG → 0.5KG",
			OrderWeight:  0.9,
			ActualWeight: 0.5,
			Expected:     ScenarioResult{IsOverweight: false, IsUnderweight: false},
		},
		{
			Name:         "无效超轻：2KG → 1.01KG",
			OrderWeight:  2.0,
			ActualWeight: 1.01,
			Expected:     ScenarioResult{IsOverweight: false, IsUnderweight: false},
		},
	}
}

// TestScenario 测试场景
type TestScenario struct {
	Name         string
	OrderWeight  float64
	ActualWeight float64
	Expected     ScenarioResult
}

// ScenarioResult 场景结果
type ScenarioResult struct {
	IsOverweight  bool
	IsUnderweight bool
}
