package utils

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/your-org/go-kuaidi/internal/model"
)

// 配置常量
const (
	// 费用相关常量
	MaxFeeAmount          = 999999.99 // 最大费用金额
	FeePrecisionThreshold = 0.01      // 费用精度阈值

	// 重量相关常量
	MaxWeightKg = 99999.999 // 最大重量(kg)

	// 体积相关常量
	MaxVolumeCubicM = 999.999999 // 最大体积(m³)

	// 转换常量
	KgToGramRatio        = 1000.0       // 千克到克的转换比例
	PoundToKgRatio       = 0.453592     // 磅到千克的转换比例
	CmToMRatio           = 100.0        // 厘米到米的转换比例
	CubicCmToCubicMRatio = 1000000.0    // 立方厘米到立方米的转换比例
	CubicMmToCubicMRatio = 1000000000.0 // 立方毫米到立方米的转换比例
)

// BillingCalculator 计费计算器
type BillingCalculator struct{}

// NewBillingCalculator 创建计费计算器
func NewBillingCalculator() *BillingCalculator {
	return &BillingCalculator{}
}

// CalculateTotalFee 计算总费用
func (bc *BillingCalculator) CalculateTotalFee(details []model.FeeDetail) float64 {
	var total float64
	for _, detail := range details {
		total += detail.Amount
	}
	return RoundToTwoDecimals(total)
}

// CalculateFeeByType 按类型计算费用
func (bc *BillingCalculator) CalculateFeeByType(details []model.FeeDetail) map[string]float64 {
	feeMap := make(map[string]float64)

	for _, detail := range details {
		feeMap[detail.Type] += detail.Amount
	}

	// 四舍五入到两位小数
	for feeType, amount := range feeMap {
		feeMap[feeType] = RoundToTwoDecimals(amount)
	}

	return feeMap
}

// CalculateFeeDifference 计算费用差额
func (bc *BillingCalculator) CalculateFeeDifference(estimatedFee, actualFee float64) float64 {
	return RoundToTwoDecimals(actualFee - estimatedFee)
}

// CalculateWeightDifference 计算重量差异
func (bc *BillingCalculator) CalculateWeightDifference(orderWeight, actualWeight float64) float64 {
	return RoundToThreeDecimals(actualWeight - orderWeight)
}

// CalculateVolumeDifference 计算体积差异
func (bc *BillingCalculator) CalculateVolumeDifference(orderVolume, actualVolume float64) float64 {
	return RoundToSixDecimals(actualVolume - orderVolume)
}

// WeightConverter 重量转换器
type WeightConverter struct{}

// NewWeightConverter 创建重量转换器
func NewWeightConverter() *WeightConverter {
	return &WeightConverter{}
}

// KgToGram 千克转克
func (wc *WeightConverter) KgToGram(kg float64) float64 {
	return kg * KgToGramRatio
}

// GramToKg 克转千克
func (wc *WeightConverter) GramToKg(gram float64) float64 {
	return gram / KgToGramRatio
}

// PoundToKg 磅转千克
func (wc *WeightConverter) PoundToKg(pound float64) float64 {
	return pound * PoundToKgRatio
}

// KgToPound 千克转磅
func (wc *WeightConverter) KgToPound(kg float64) float64 {
	return kg / PoundToKgRatio
}

// NormalizeWeight 标准化重量（统一转换为千克）
func (wc *WeightConverter) NormalizeWeight(weight float64, unit string) (float64, error) {
	unit = strings.ToLower(strings.TrimSpace(unit))

	switch unit {
	case "kg", "千克", "公斤":
		return RoundToThreeDecimals(weight), nil
	case "g", "gram", "克":
		return RoundToThreeDecimals(wc.GramToKg(weight)), nil
	case "lb", "pound", "磅":
		return RoundToThreeDecimals(wc.PoundToKg(weight)), nil
	default:
		return 0, fmt.Errorf("不支持的重量单位: %s", unit)
	}
}

// VolumeConverter 体积转换器
type VolumeConverter struct{}

// NewVolumeConverter 创建体积转换器
func NewVolumeConverter() *VolumeConverter {
	return &VolumeConverter{}
}

// CmToM 厘米转米
func (vc *VolumeConverter) CmToM(cm float64) float64 {
	return cm / 100
}

// MToCm 米转厘米
func (vc *VolumeConverter) MToCm(m float64) float64 {
	return m * 100
}

// CalculateVolume 计算体积（长×宽×高）
func (vc *VolumeConverter) CalculateVolume(length, width, height float64, unit string) (float64, error) {
	unit = strings.ToLower(strings.TrimSpace(unit))

	volume := length * width * height

	switch unit {
	case "m", "米":
		return RoundToSixDecimals(volume), nil
	case "cm", "厘米":
		// 立方厘米转立方米
		return RoundToSixDecimals(volume / 1000000), nil
	case "mm", "毫米":
		// 立方毫米转立方米
		return RoundToSixDecimals(volume / 1000000000), nil
	default:
		return 0, fmt.Errorf("不支持的长度单位: %s", unit)
	}
}

// DataValidator 数据验证器
type DataValidator struct{}

// NewDataValidator 创建数据验证器
func NewDataValidator() *DataValidator {
	return &DataValidator{}
}

// ValidateFee 验证费用
func (dv *DataValidator) ValidateFee(fee float64) error {
	if fee < 0 {
		return fmt.Errorf("费用不能为负数: %.2f", fee)
	}
	if fee > MaxFeeAmount {
		return fmt.Errorf("费用超过最大限制: %.2f", fee)
	}
	return nil
}

// ValidateWeight 验证重量
func (dv *DataValidator) ValidateWeight(weight float64) error {
	if weight < 0 {
		return fmt.Errorf("重量不能为负数: %.3f", weight)
	}
	if weight > MaxWeightKg {
		return fmt.Errorf("重量超过最大限制: %.3f", weight)
	}
	return nil
}

// ValidateVolume 验证体积
func (dv *DataValidator) ValidateVolume(volume float64) error {
	if volume < 0 {
		return fmt.Errorf("体积不能为负数: %.6f", volume)
	}
	if volume > MaxVolumeCubicM {
		return fmt.Errorf("体积超过最大限制: %.6f", volume)
	}
	return nil
}

// ValidateFeeDetails 验证费用明细
func (dv *DataValidator) ValidateFeeDetails(details []model.FeeDetail) error {
	if len(details) == 0 {
		return fmt.Errorf("费用明细不能为空")
	}

	totalFee := 0.0

	for i, detail := range details {
		// 验证费用金额
		if err := dv.ValidateFee(detail.Amount); err != nil {
			return fmt.Errorf("费用明细[%d]金额无效: %w", i, err)
		}

		// 验证费用类型
		if detail.Type == "" {
			return fmt.Errorf("费用明细[%d]类型不能为空", i)
		}

		// 允许相同类型的费用明细（例如多个其他费用）
		// 只检查名称是否为空
		if detail.Name == "" {
			return fmt.Errorf("费用明细[%d]名称不能为空", i)
		}

		totalFee += detail.Amount
	}

	// 验证总费用
	if err := dv.ValidateFee(totalFee); err != nil {
		return fmt.Errorf("费用明细总和无效: %w", err)
	}

	return nil
}

// ValidateBillingData 验证计费数据
func (dv *DataValidator) ValidateBillingData(data *model.BillingUpdatedData) error {
	// 验证重量
	if err := dv.ValidateWeight(data.Weight); err != nil {
		return fmt.Errorf("重量验证失败: %w", err)
	}

	if err := dv.ValidateWeight(data.ChargedWeight); err != nil {
		return fmt.Errorf("计费重量验证失败: %w", err)
	}

	// 验证体积
	if err := dv.ValidateVolume(data.Volume); err != nil {
		return fmt.Errorf("体积验证失败: %w", err)
	}

	// 验证总费用
	if err := dv.ValidateFee(data.TotalFee); err != nil {
		return fmt.Errorf("总费用验证失败: %w", err)
	}

	// 验证费用明细
	if err := dv.ValidateFeeDetails(data.FeeDetails); err != nil {
		return fmt.Errorf("费用明细验证失败: %w", err)
	}

	// 验证费用明细总和与总费用是否一致
	calculator := NewBillingCalculator()
	calculatedTotal := calculator.CalculateTotalFee(data.FeeDetails)
	if math.Abs(calculatedTotal-data.TotalFee) > 0.01 {
		return fmt.Errorf("费用明细总和(%.2f)与总费用(%.2f)不一致", calculatedTotal, data.TotalFee)
	}

	return nil
}

// 数学工具函数

// RoundToTwoDecimals 四舍五入到两位小数
func RoundToTwoDecimals(value float64) float64 {
	return math.Round(value*100) / 100
}

// RoundToThreeDecimals 四舍五入到三位小数
func RoundToThreeDecimals(value float64) float64 {
	return math.Round(value*1000) / 1000
}

// RoundToSixDecimals 四舍五入到六位小数
func RoundToSixDecimals(value float64) float64 {
	return math.Round(value*1000000) / 1000000
}

// FormatFee 格式化费用显示
func FormatFee(fee float64) string {
	return fmt.Sprintf("%.2f", fee)
}

// FormatWeight 格式化重量显示
func FormatWeight(weight float64) string {
	return fmt.Sprintf("%.3f", weight)
}

// FormatVolume 格式化体积显示
func FormatVolume(volume float64) string {
	return fmt.Sprintf("%.6f", volume)
}

// ParseFee 解析费用字符串
func ParseFee(feeStr string) (float64, error) {
	// 移除货币符号和空格
	feeStr = strings.TrimSpace(feeStr)
	feeStr = strings.ReplaceAll(feeStr, "¥", "")
	feeStr = strings.ReplaceAll(feeStr, "$", "")
	feeStr = strings.ReplaceAll(feeStr, "元", "")
	feeStr = strings.TrimSpace(feeStr)

	fee, err := strconv.ParseFloat(feeStr, 64)
	if err != nil {
		return 0, fmt.Errorf("费用格式错误: %s", feeStr)
	}

	return RoundToTwoDecimals(fee), nil
}

// ParseWeight 解析重量字符串
func ParseWeight(weightStr string) (float64, error) {
	// 移除单位和空格
	weightStr = strings.TrimSpace(weightStr)
	weightStr = strings.ReplaceAll(weightStr, "kg", "")
	weightStr = strings.ReplaceAll(weightStr, "千克", "")
	weightStr = strings.ReplaceAll(weightStr, "公斤", "")
	weightStr = strings.TrimSpace(weightStr)

	weight, err := strconv.ParseFloat(weightStr, 64)
	if err != nil {
		return 0, fmt.Errorf("重量格式错误: %s", weightStr)
	}

	return RoundToThreeDecimals(weight), nil
}

// FeeTypeMapper 费用类型映射器
type FeeTypeMapper struct {
	typeMap map[string]string
}

// NewFeeTypeMapper 创建费用类型映射器
func NewFeeTypeMapper() *FeeTypeMapper {
	return &FeeTypeMapper{
		typeMap: map[string]string{
			"运费":    "freight",
			"快递费":   "freight",
			"运输费":   "freight",
			"保价费":   "insurance",
			"保险费":   "insurance",
			"包装费":   "package",
			"耗材费":   "package",
			"上门取件费": "pickup",
			"取件费":   "pickup",
			"送货上门费": "delivery",
			"派送费":   "delivery",
			"代收货款费": "cod",
			"代收费":   "cod",
			"其他费用":  "other",
			"杂费":    "other",
		},
	}
}

// MapFeeType 映射费用类型
func (ftm *FeeTypeMapper) MapFeeType(chineseName string) string {
	if englishType, exists := ftm.typeMap[chineseName]; exists {
		return englishType
	}
	return "other" // 默认为其他费用
}

// GetAllMappings 获取所有映射关系
func (ftm *FeeTypeMapper) GetAllMappings() map[string]string {
	result := make(map[string]string)
	for k, v := range ftm.typeMap {
		result[k] = v
	}
	return result
}
