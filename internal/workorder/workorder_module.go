package workorder

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/your-org/go-kuaidi/api/handler"
	"github.com/your-org/go-kuaidi/internal/adapter/workorder"
	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// WorkOrderModule 工单模块
type WorkOrderModule struct {
	Repository                 repository.WorkOrderRepository
	Service                    service.WorkOrderService
	Handler                    *handler.WorkOrderHandler
	serviceInjectionMiddleware interface{} // 服务注入中间件
}

// WorkOrderConfig 工单配置
type WorkOrderConfig struct {
	// 快递100配置
	Kuaidi100 struct {
		APIKey   string `yaml:"api_key" json:"api_key"`
		Secret   string `yaml:"secret" json:"secret"`
		Customer string `yaml:"customer" json:"customer"`
	} `yaml:"kuaidi100" json:"kuaidi100"`

	// 易达配置
	Yida struct {
		Username   string `yaml:"username" json:"username"`
		PrivateKey string `yaml:"private_key" json:"private_key"`
	} `yaml:"yida" json:"yida"`

	// 云通配置
	Yuntong struct {
		BusinessID string `yaml:"business_id" json:"business_id"`
		APIKey     string `yaml:"api_key" json:"api_key"`
	} `yaml:"yuntong" json:"yuntong"`

	// 文件上传配置
	FileUpload struct {
		Type      string `yaml:"type" json:"type"`             // local, oss, mock
		UploadDir string `yaml:"upload_dir" json:"upload_dir"` // 本地上传目录
		BaseURL   string `yaml:"base_url" json:"base_url"`     // 访问基础URL

		// OSS配置
		OSS struct {
			Endpoint        string `yaml:"endpoint" json:"endpoint"`
			AccessKeyID     string `yaml:"access_key_id" json:"access_key_id"`
			AccessKeySecret string `yaml:"access_key_secret" json:"access_key_secret"`
			BucketName      string `yaml:"bucket_name" json:"bucket_name"`
		} `yaml:"oss" json:"oss"`
	} `yaml:"file_upload" json:"file_upload"`
}

// NewWorkOrderModule 创建工单模块
// TODO: 待迁移到数据库配置管理，当前仍使用YAML配置
func NewWorkOrderModule(
	db *sql.DB,
	orderRepo repository.OrderRepository,
	config *WorkOrderConfig,
	logger *zap.Logger,
) (*WorkOrderModule, error) {
	// 1. 创建仓储层
	workOrderRepo := repository.NewPostgresWorkOrderRepository(db, logger)

	// 2. 创建文件上传服务
	var fileUploadService service.FileUploadService
	switch config.FileUpload.Type {
	case "oss":
		fileUploadService = service.NewOSSFileUploadService(
			config.FileUpload.OSS.Endpoint,
			config.FileUpload.OSS.AccessKeyID,
			config.FileUpload.OSS.AccessKeySecret,
			config.FileUpload.OSS.BucketName,
			logger,
		)
	case "local":
		fileUploadService = service.NewLocalFileUploadService(
			config.FileUpload.UploadDir,
			config.FileUpload.BaseURL,
			logger,
		)
	default:
		// 默认使用模拟服务
		fileUploadService = service.NewMockFileUploadService(
			config.FileUpload.BaseURL,
			logger,
		)
	}

	// 3. 创建服务层（不带回调转发）
	workOrderService := service.NewDefaultWorkOrderService(
		workOrderRepo,
		orderRepo,
		nil, // TODO: 传入SmartOrderFinder - 临时使用nil
		fileUploadService,
		nil, // 不使用回调转发
		logger,
	)

	// 4. 注册供应商适配器
	defaultService := workOrderService.(*service.DefaultWorkOrderService)

	// 注册快递100适配器
	if config.Kuaidi100.APIKey != "" {
		kuaidi100Adapter := workorder.NewKuaidi100WorkOrderAdapter(
			config.Kuaidi100.APIKey,
			config.Kuaidi100.Secret,
			config.Kuaidi100.Customer,
			workOrderRepo,
			logger,
		)
		defaultService.RegisterProviderAdapter("kuaidi100", kuaidi100Adapter)
		logger.Info("已注册快递100工单适配器")
	}

	// 注册易达适配器
	if config.Yida.Username != "" && config.Yida.PrivateKey != "" {
		yidaAdapter := workorder.NewYidaWorkOrderAdapter(
			config.Yida.Username,
			config.Yida.PrivateKey,
			workOrderRepo,
			logger,
		)
		defaultService.RegisterProviderAdapter("yida", yidaAdapter)
		logger.Info("已注册易达工单适配器")
	}

	// 注册云通适配器
	if config.Yuntong.BusinessID != "" && config.Yuntong.APIKey != "" {
		yuntongAdapter := workorder.NewYuntongWorkOrderAdapter(
			config.Yuntong.BusinessID,
			config.Yuntong.APIKey,
			workOrderRepo,
			logger,
		)
		defaultService.RegisterProviderAdapter("yuntong", yuntongAdapter)
		logger.Info("已注册云通工单适配器")
	}

	// 5. 创建处理器
	workOrderHandler := handler.NewWorkOrderHandler(workOrderService, logger)

	return &WorkOrderModule{
		Repository: workOrderRepo,
		Service:    workOrderService,
		Handler:    workOrderHandler,
	}, nil
}

// NewWorkOrderModuleWithCallback 创建工单模块（支持回调转发）
// 🔥 新增：支持统一回调转发的工单模块构造函数
func NewWorkOrderModuleWithCallback(
	db *sql.DB,
	orderRepo repository.OrderRepository,
	callbackService service.WorkOrderCallbackService,
	config *WorkOrderConfig,
	logger *zap.Logger,
) (*WorkOrderModule, error) {
	// 1. 创建仓储层
	workOrderRepo := repository.NewPostgresWorkOrderRepository(db, logger)

	// 2. 创建文件上传服务
	var fileUploadService service.FileUploadService
	switch config.FileUpload.Type {
	case "oss":
		fileUploadService = service.NewOSSFileUploadService(
			config.FileUpload.OSS.Endpoint,
			config.FileUpload.OSS.AccessKeyID,
			config.FileUpload.OSS.AccessKeySecret,
			config.FileUpload.OSS.BucketName,
			logger,
		)
	case "local":
		fileUploadService = service.NewLocalFileUploadService(
			config.FileUpload.UploadDir,
			config.FileUpload.BaseURL,
			logger,
		)
	default:
		// 默认使用模拟服务
		fileUploadService = service.NewMockFileUploadService(
			config.FileUpload.BaseURL,
			logger,
		)
	}

	// 3. 创建服务层（集成回调转发）
	workOrderService := service.NewDefaultWorkOrderService(
		workOrderRepo,
		orderRepo,
		nil, // TODO: 传入SmartOrderFinder - 临时使用nil
		fileUploadService,
		callbackService, // 🔥 注入回调服务
		logger,
	)

	// 4. 注册供应商适配器（使用数据库配置）
	defaultService := workOrderService.(*service.DefaultWorkOrderService)

	// 创建供应商配置服务
	ctx := context.Background()
	systemConfigRepository := repository.NewPostgresSystemConfigRepository(db)
	systemConfigService := service.NewDefaultSystemConfigService(systemConfigRepository, logger)
	providerConfigService := service.NewProviderConfigService(systemConfigService, logger)

	// 注册快递100工单适配器（从数据库获取配置）
	if kuaidi100Config, err := providerConfigService.GetKuaidi100WorkorderConfig(ctx); err == nil {
		kuaidi100Adapter := workorder.NewKuaidi100WorkOrderAdapter(
			kuaidi100Config.Key,
			kuaidi100Config.Secret,
			kuaidi100Config.Customer,
			workOrderRepo,
			logger,
		)
		defaultService.RegisterProviderAdapter("kuaidi100", kuaidi100Adapter)
		logger.Info("✅ 已注册快递100工单适配器（数据库配置）",
			zap.String("base_url", kuaidi100Config.BaseURL),
			zap.Int("timeout", kuaidi100Config.Timeout))
	} else {
		logger.Warn("❌ 快递100工单适配器注册失败", zap.Error(err))
	}

	// 注册易达工单适配器（从数据库获取配置）
	if yidaConfig, err := providerConfigService.GetYidaWorkorderConfig(ctx); err == nil {
		yidaAdapter := workorder.NewYidaWorkOrderAdapter(
			yidaConfig.AppKey,
			yidaConfig.AppSecret,
			workOrderRepo,
			logger,
		)
		defaultService.RegisterProviderAdapter("yida", yidaAdapter)
		logger.Info("✅ 已注册易达工单适配器（数据库配置）",
			zap.String("base_url", yidaConfig.BaseURL),
			zap.Int("timeout", yidaConfig.Timeout))
	} else {
		logger.Warn("❌ 易达工单适配器注册失败", zap.Error(err))
	}

	// 注册云通工单适配器（从数据库获取配置）
	if yuntongConfig, err := providerConfigService.GetYuntongWorkorderConfig(ctx); err == nil {
		yuntongAdapter := workorder.NewYuntongWorkOrderAdapter(
			yuntongConfig.EBusinessID,
			yuntongConfig.ApiKey,
			workOrderRepo,
			logger,
		)
		defaultService.RegisterProviderAdapter("yuntong", yuntongAdapter)
		logger.Info("✅ 已注册云通工单适配器（数据库配置）",
			zap.String("base_url", yuntongConfig.BaseURL),
			zap.Int("timeout", yuntongConfig.Timeout))
	} else {
		logger.Warn("❌ 云通工单适配器注册失败", zap.Error(err))
	}

	// 注册快递鸟工单适配器（从数据库获取配置）
	if kuaidiniaoConfig, err := providerConfigService.GetKuaidiniaoWorkorderConfig(ctx); err == nil {
		kuaidiniaoAdapter := workorder.NewKuaidiNiaoWorkOrderAdapter(
			kuaidiniaoConfig.EBusinessID,
			kuaidiniaoConfig.ApiKey,
			kuaidiniaoConfig.BaseURL,
			workOrderRepo,
			orderRepo, // 🔥 新增：传递订单仓储用于查询真实用户信息
			logger,
		)
		defaultService.RegisterProviderAdapter("kuaidiniao", kuaidiniaoAdapter)
		logger.Info("✅ 已注册快递鸟工单适配器（数据库配置）",
			zap.String("base_url", kuaidiniaoConfig.BaseURL),
			zap.String("environment", kuaidiniaoConfig.Environment),
			zap.Int("timeout", kuaidiniaoConfig.Timeout))
	} else {
		logger.Warn("❌ 快递鸟工单适配器注册失败", zap.Error(err))
	}

	// 5. 创建处理器
	workOrderHandler := handler.NewWorkOrderHandler(workOrderService, logger)

	return &WorkOrderModule{
		Repository: workOrderRepo,
		Service:    workOrderService,
		Handler:    workOrderHandler,
	}, nil
}

// LoadWorkOrderConfig 加载工单配置
func LoadWorkOrderConfig(cfg *config.Config) *WorkOrderConfig {
	workOrderConfig := &WorkOrderConfig{}

	// 从供应商配置中读取工单API密钥
	if cfg.Providers != nil {
		// 快递100配置
		if kuaidi100, exists := cfg.Providers["kuaidi100"]; exists {
			if kuaidi100Map, ok := kuaidi100.(map[string]interface{}); ok {
				if apiKey, ok := kuaidi100Map["api_key"].(string); ok {
					workOrderConfig.Kuaidi100.APIKey = apiKey
				}
				if secret, ok := kuaidi100Map["secret"].(string); ok {
					workOrderConfig.Kuaidi100.Secret = secret
				}
				if customer, ok := kuaidi100Map["customer"].(string); ok {
					workOrderConfig.Kuaidi100.Customer = customer
				}
			}
		}

		// 易达配置
		if yida, exists := cfg.Providers["yida"]; exists {
			if yidaMap, ok := yida.(map[string]interface{}); ok {
				if username, ok := yidaMap["username"].(string); ok {
					workOrderConfig.Yida.Username = username
				}
				if privateKey, ok := yidaMap["private_key"].(string); ok {
					workOrderConfig.Yida.PrivateKey = privateKey
				}
			}
		}

		// 云通配置
		if yuntong, exists := cfg.Providers["yuntong"]; exists {
			if yuntongMap, ok := yuntong.(map[string]interface{}); ok {
				if businessID, ok := yuntongMap["business_id"].(string); ok {
					workOrderConfig.Yuntong.BusinessID = businessID
				}
				if apiKey, ok := yuntongMap["api_key"].(string); ok {
					workOrderConfig.Yuntong.APIKey = apiKey
				}
			}
		}
	}

	// 文件上传配置
	workOrderConfig.FileUpload.Type = getStringFromConfig(cfg, "file_upload.type", "mock")
	workOrderConfig.FileUpload.UploadDir = getStringFromConfig(cfg, "file_upload.upload_dir", "./uploads")
	workOrderConfig.FileUpload.BaseURL = getStringFromConfig(cfg, "file_upload.base_url", "http://localhost:8080")

	// OSS配置
	workOrderConfig.FileUpload.OSS.Endpoint = getStringFromConfig(cfg, "file_upload.oss.endpoint", "")
	workOrderConfig.FileUpload.OSS.AccessKeyID = getStringFromConfig(cfg, "file_upload.oss.access_key_id", "")
	workOrderConfig.FileUpload.OSS.AccessKeySecret = getStringFromConfig(cfg, "file_upload.oss.access_key_secret", "")
	workOrderConfig.FileUpload.OSS.BucketName = getStringFromConfig(cfg, "file_upload.oss.bucket_name", "")

	return workOrderConfig
}

// getStringFromConfig 从配置中获取字符串值（简化版）
func getStringFromConfig(cfg *config.Config, key, defaultValue string) string {
	// 这里可以实现更复杂的配置读取逻辑
	// 暂时返回默认值
	return defaultValue
}

// ValidateWorkOrderConfig 验证工单配置
func ValidateWorkOrderConfig(config *WorkOrderConfig) error {
	// 验证至少配置了一个供应商
	hasProvider := false

	if config.Kuaidi100.APIKey != "" {
		hasProvider = true
	}

	if config.Yida.Username != "" && config.Yida.PrivateKey != "" {
		hasProvider = true
	}

	if config.Yuntong.BusinessID != "" && config.Yuntong.APIKey != "" {
		hasProvider = true
	}

	if !hasProvider {
		return fmt.Errorf("至少需要配置一个工单供应商")
	}

	// 验证文件上传配置
	if config.FileUpload.Type == "oss" {
		if config.FileUpload.OSS.Endpoint == "" ||
			config.FileUpload.OSS.AccessKeyID == "" ||
			config.FileUpload.OSS.AccessKeySecret == "" ||
			config.FileUpload.OSS.BucketName == "" {
			return fmt.Errorf("OSS文件上传配置不完整")
		}
	}

	if config.FileUpload.Type == "local" {
		if config.FileUpload.UploadDir == "" {
			return fmt.Errorf("本地文件上传目录不能为空")
		}
	}

	return nil
}

// GetSupportedProviders 获取支持的供应商列表
func (m *WorkOrderModule) GetSupportedProviders() []string {
	var providers []string

	// 这里可以通过检查已注册的适配器来动态获取
	// 为了简化，直接返回固定列表
	providers = append(providers, "kuaidi100", "yida", "yuntong")

	return providers
}

// GetProviderDisplayName 获取供应商显示名称
func GetProviderDisplayName(provider string) string {
	displayNames := map[string]string{
		"kuaidi100": "快递100",
		"yida":      "易达",
		"yuntong":   "云通",
	}

	if name, exists := displayNames[provider]; exists {
		return name
	}

	return provider
}

// SetServiceInjectionMiddleware 设置服务注入中间件
func (m *WorkOrderModule) SetServiceInjectionMiddleware(middleware interface{}) {
	m.serviceInjectionMiddleware = middleware
}

// GetServiceInjectionMiddleware 获取服务注入中间件
func (m *WorkOrderModule) GetServiceInjectionMiddleware() interface{} {
	return m.serviceInjectionMiddleware
}
