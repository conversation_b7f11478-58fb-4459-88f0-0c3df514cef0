package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	_ "github.com/lib/pq"
	"go.uber.org/zap"
)

// PerformanceOptimizer 数据库性能优化器
type PerformanceOptimizer struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewPerformanceOptimizer 创建性能优化器
func NewPerformanceOptimizer(db *sql.DB, logger *zap.Logger) *PerformanceOptimizer {
	return &PerformanceOptimizer{
		db:     db,
		logger: logger,
	}
}

// OptimizeConnectionPool 优化连接池配置
func (p *PerformanceOptimizer) OptimizeConnectionPool() error {
	// 设置最大打开连接数
	p.db.SetMaxOpenConns(100)

	// 设置最大空闲连接数
	p.db.SetMaxIdleConns(50)

	// 设置连接最大生存时间
	p.db.SetConnMaxLifetime(30 * time.Minute)

	// 设置连接最大空闲时间
	p.db.SetConnMaxIdleTime(5 * time.Minute)

	p.logger.Info("数据库连接池优化完成",
		zap.Int("max_open_conns", 100),
		zap.Int("max_idle_conns", 50),
		zap.Duration("conn_max_lifetime", 30*time.Minute),
		zap.Duration("conn_max_idle_time", 5*time.Minute))

	return nil
}

// CreateOptimizedIndexes 创建优化索引
func (p *PerformanceOptimizer) CreateOptimizedIndexes() error {
	indexes := []string{
		// 快递公司表索引
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_express_companies_active_sort 
		 ON express_companies (is_active, sort_order DESC) WHERE is_active = true`,

		// 价格表索引（如果存在）
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prices_route_weight 
		 ON prices (from_province, from_city, to_province, to_city, weight) 
		 WHERE created_at > NOW() - INTERVAL '7 days'`,

		// 供应商配置索引
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_provider_configs_active 
		 ON provider_configs (provider_name, is_active) WHERE is_active = true`,

		// 快递代码映射索引
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_express_mappings_provider_code 
		 ON express_code_mappings (provider_name, standard_code)`,
	}

	for _, indexSQL := range indexes {
		if err := p.executeWithTimeout(indexSQL, 30*time.Second); err != nil {
			p.logger.Warn("创建索引失败", zap.String("sql", indexSQL), zap.Error(err))
		} else {
			p.logger.Info("索引创建成功", zap.String("sql", indexSQL))
		}
	}

	return nil
}

// OptimizeQueries 优化查询语句
func (p *PerformanceOptimizer) OptimizeQueries() error {
	// 分析查询计划
	queries := []string{
		`ANALYZE express_companies`,
		`ANALYZE provider_configs`,
		`ANALYZE express_code_mappings`,
	}

	for _, query := range queries {
		if err := p.executeWithTimeout(query, 10*time.Second); err != nil {
			p.logger.Warn("查询优化失败", zap.String("sql", query), zap.Error(err))
		}
	}

	return nil
}

// SetOptimalPostgreSQLSettings 设置PostgreSQL最优配置
func (p *PerformanceOptimizer) SetOptimalPostgreSQLSettings() error {
	settings := map[string]string{
		// 内存设置
		"shared_buffers":       "256MB",
		"effective_cache_size": "1GB",
		"work_mem":             "4MB",
		"maintenance_work_mem": "64MB",

		// 检查点设置
		"checkpoint_completion_target": "0.9",
		"wal_buffers":                  "16MB",

		// 查询优化
		"random_page_cost":         "1.1",
		"effective_io_concurrency": "200",

		// 连接设置
		"max_connections": "200",
	}

	for setting, value := range settings {
		query := fmt.Sprintf("ALTER SYSTEM SET %s = '%s'", setting, value)
		if err := p.executeWithTimeout(query, 5*time.Second); err != nil {
			p.logger.Warn("设置PostgreSQL参数失败",
				zap.String("setting", setting),
				zap.String("value", value),
				zap.Error(err))
		} else {
			p.logger.Info("PostgreSQL参数设置成功",
				zap.String("setting", setting),
				zap.String("value", value))
		}
	}

	// 重新加载配置
	if err := p.executeWithTimeout("SELECT pg_reload_conf()", 5*time.Second); err != nil {
		p.logger.Warn("重新加载PostgreSQL配置失败", zap.Error(err))
	}

	return nil
}

// CreateMaterializedViews 创建物化视图
func (p *PerformanceOptimizer) CreateMaterializedViews() error {
	views := []string{
		// 活跃快递公司视图
		`CREATE MATERIALIZED VIEW IF NOT EXISTS mv_active_express_companies AS
		 SELECT code, name, english_name, sort_order
		 FROM express_companies 
		 WHERE is_active = true
		 ORDER BY sort_order DESC`,

		// 供应商快递映射视图
		`CREATE MATERIALIZED VIEW IF NOT EXISTS mv_provider_express_mappings AS
		 SELECT p.provider_name, p.is_active as provider_active,
		        m.standard_code, m.provider_code,
		        e.name as express_name
		 FROM provider_configs p
		 JOIN express_code_mappings m ON p.provider_name = m.provider_name
		 JOIN express_companies e ON m.standard_code = e.code
		 WHERE p.is_active = true AND e.is_active = true`,
	}

	for _, viewSQL := range views {
		if err := p.executeWithTimeout(viewSQL, 30*time.Second); err != nil {
			p.logger.Warn("创建物化视图失败", zap.String("sql", viewSQL), zap.Error(err))
		} else {
			p.logger.Info("物化视图创建成功", zap.String("sql", viewSQL))
		}
	}

	return nil
}

// RefreshMaterializedViews 刷新物化视图
func (p *PerformanceOptimizer) RefreshMaterializedViews() error {
	views := []string{
		"REFRESH MATERIALIZED VIEW CONCURRENTLY mv_active_express_companies",
		"REFRESH MATERIALIZED VIEW CONCURRENTLY mv_provider_express_mappings",
	}

	for _, refreshSQL := range views {
		if err := p.executeWithTimeout(refreshSQL, 10*time.Second); err != nil {
			p.logger.Warn("刷新物化视图失败", zap.String("sql", refreshSQL), zap.Error(err))
		}
	}

	return nil
}

// StartPerformanceMonitoring 启动性能监控
func (p *PerformanceOptimizer) StartPerformanceMonitoring() {
	go func() {
		ticker := time.NewTicker(5 * time.Minute)
		defer ticker.Stop()

		for range ticker.C {
			p.collectPerformanceMetrics()
		}
	}()
}

// collectPerformanceMetrics 收集性能指标
func (p *PerformanceOptimizer) collectPerformanceMetrics() {
	// 查询活跃连接数
	var activeConns int
	err := p.db.QueryRow("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'").Scan(&activeConns)
	if err == nil {
		p.logger.Info("数据库活跃连接数", zap.Int("active_connections", activeConns))
	}

	// 查询慢查询
	slowQueries := `
		SELECT query, mean_exec_time, calls 
		FROM pg_stat_statements 
		WHERE mean_exec_time > 100 
		ORDER BY mean_exec_time DESC 
		LIMIT 5
	`

	rows, err := p.db.Query(slowQueries)
	if err == nil {
		defer rows.Close()

		slowQueryCount := 0
		for rows.Next() {
			var query string
			var meanTime float64
			var calls int64

			if err := rows.Scan(&query, &meanTime, &calls); err == nil {
				slowQueryCount++
				p.logger.Warn("发现慢查询",
					zap.String("query", query[:100]+"..."),
					zap.Float64("mean_exec_time_ms", meanTime),
					zap.Int64("calls", calls))
			}
		}

		if slowQueryCount == 0 {
			p.logger.Info("没有发现慢查询")
		}
	}

	// 查询缓存命中率
	var cacheHitRatio float64
	cacheQuery := `
		SELECT round(
			(sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read))) * 100, 2
		) as cache_hit_ratio
		FROM pg_statio_user_tables
	`

	err = p.db.QueryRow(cacheQuery).Scan(&cacheHitRatio)
	if err == nil {
		p.logger.Info("数据库缓存命中率", zap.Float64("cache_hit_ratio_percent", cacheHitRatio))
	}
}

// executeWithTimeout 带超时的SQL执行
func (p *PerformanceOptimizer) executeWithTimeout(query string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	_, err := p.db.ExecContext(ctx, query)
	return err
}

// GetPerformanceReport 获取性能报告
func (p *PerformanceOptimizer) GetPerformanceReport() (map[string]interface{}, error) {
	report := make(map[string]interface{})

	// 连接池状态
	stats := p.db.Stats()
	report["connection_pool"] = map[string]interface{}{
		"open_connections":    stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration":       stats.WaitDuration.String(),
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}

	// 数据库大小
	var dbSize string
	err := p.db.QueryRow("SELECT pg_size_pretty(pg_database_size(current_database()))").Scan(&dbSize)
	if err == nil {
		report["database_size"] = dbSize
	}

	// 表统计
	tableStats := `
		SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del, n_live_tup
		FROM pg_stat_user_tables
		ORDER BY n_live_tup DESC
		LIMIT 10
	`

	rows, err := p.db.Query(tableStats)
	if err == nil {
		defer rows.Close()

		var tables []map[string]interface{}
		for rows.Next() {
			var schema, table string
			var inserts, updates, deletes, liveTuples int64

			if err := rows.Scan(&schema, &table, &inserts, &updates, &deletes, &liveTuples); err == nil {
				tables = append(tables, map[string]interface{}{
					"schema":      schema,
					"table":       table,
					"inserts":     inserts,
					"updates":     updates,
					"deletes":     deletes,
					"live_tuples": liveTuples,
				})
			}
		}
		report["table_statistics"] = tables
	}

	return report, nil
}
