package database

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// QueryPerformanceMonitor 查询性能监控器
// 基于第一性原理设计：实时监控 + 性能分析 + 自动优化建议
type QueryPerformanceMonitor struct {
	db     *sql.DB
	logger *zap.Logger
	config *MonitorConfig
	
	// 性能统计
	stats      *PerformanceStats
	statsMutex sync.RWMutex
	
	// 慢查询记录
	slowQueries      []SlowQuery
	slowQueriesMutex sync.RWMutex
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	// 慢查询阈值
	SlowQueryThreshold time.Duration
	
	// 监控间隔
	MonitorInterval time.Duration
	
	// 最大慢查询记录数
	MaxSlowQueries int
	
	// 是否启用自动优化建议
	EnableOptimizationSuggestions bool
	
	// 性能报告间隔
	ReportInterval time.Duration
}

// PerformanceStats 性能统计
type PerformanceStats struct {
	TotalQueries     int64         `json:"total_queries"`
	SlowQueries      int64         `json:"slow_queries"`
	AverageTime      time.Duration `json:"average_time"`
	MaxTime          time.Duration `json:"max_time"`
	MinTime          time.Duration `json:"min_time"`
	CacheHitRate     float64       `json:"cache_hit_rate"`
	ActiveConnections int           `json:"active_connections"`
	LastUpdated      time.Time     `json:"last_updated"`
}

// SlowQuery 慢查询记录
type SlowQuery struct {
	Query       string        `json:"query"`
	Duration    time.Duration `json:"duration"`
	Timestamp   time.Time     `json:"timestamp"`
	Parameters  []interface{} `json:"parameters,omitempty"`
	StackTrace  string        `json:"stack_trace,omitempty"`
	Suggestion  string        `json:"suggestion,omitempty"`
}

// QueryMetrics 查询指标
type QueryMetrics struct {
	Operation    string        `json:"operation"`
	Duration     time.Duration `json:"duration"`
	RowsAffected int64         `json:"rows_affected"`
	RowsReturned int64         `json:"rows_returned"`
	CacheHit     bool          `json:"cache_hit"`
	Timestamp    time.Time     `json:"timestamp"`
}

// NewQueryPerformanceMonitor 创建查询性能监控器
func NewQueryPerformanceMonitor(db *sql.DB, logger *zap.Logger) *QueryPerformanceMonitor {
	config := &MonitorConfig{
		SlowQueryThreshold:            1 * time.Second,
		MonitorInterval:               30 * time.Second,
		MaxSlowQueries:                100,
		EnableOptimizationSuggestions: true,
		ReportInterval:                5 * time.Minute,
	}

	monitor := &QueryPerformanceMonitor{
		db:     db,
		logger: logger,
		config: config,
		stats: &PerformanceStats{
			MinTime:     time.Hour, // 初始化为一个大值
			LastUpdated: time.Now(),
		},
		slowQueries: make([]SlowQuery, 0, config.MaxSlowQueries),
	}

	return monitor
}

// Start 启动性能监控
func (m *QueryPerformanceMonitor) Start(ctx context.Context) {
	m.logger.Info("启动查询性能监控器")

	// 启动监控协程
	go m.monitorLoop(ctx)
	
	// 启动报告协程
	go m.reportLoop(ctx)
}

// RecordQuery 记录查询性能
func (m *QueryPerformanceMonitor) RecordQuery(metrics *QueryMetrics) {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()

	// 更新统计信息
	m.stats.TotalQueries++
	
	// 更新平均时间
	if m.stats.TotalQueries == 1 {
		m.stats.AverageTime = metrics.Duration
	} else {
		// 计算移动平均
		totalTime := time.Duration(float64(m.stats.AverageTime) * float64(m.stats.TotalQueries-1))
		m.stats.AverageTime = (totalTime + metrics.Duration) / time.Duration(m.stats.TotalQueries)
	}
	
	// 更新最大最小时间
	if metrics.Duration > m.stats.MaxTime {
		m.stats.MaxTime = metrics.Duration
	}
	if metrics.Duration < m.stats.MinTime {
		m.stats.MinTime = metrics.Duration
	}
	
	m.stats.LastUpdated = time.Now()

	// 检查是否为慢查询
	if metrics.Duration > m.config.SlowQueryThreshold {
		m.recordSlowQuery(metrics)
	}

	// 记录详细日志
	if metrics.Duration > m.config.SlowQueryThreshold {
		m.logger.Warn("慢查询检测",
			zap.String("operation", metrics.Operation),
			zap.Duration("duration", metrics.Duration),
			zap.Int64("rows_affected", metrics.RowsAffected),
			zap.Int64("rows_returned", metrics.RowsReturned),
			zap.Bool("cache_hit", metrics.CacheHit))
	} else {
		m.logger.Debug("查询性能记录",
			zap.String("operation", metrics.Operation),
			zap.Duration("duration", metrics.Duration),
			zap.Bool("cache_hit", metrics.CacheHit))
	}
}

// recordSlowQuery 记录慢查询
func (m *QueryPerformanceMonitor) recordSlowQuery(metrics *QueryMetrics) {
	m.slowQueriesMutex.Lock()
	defer m.slowQueriesMutex.Unlock()

	m.stats.SlowQueries++

	slowQuery := SlowQuery{
		Query:     metrics.Operation,
		Duration:  metrics.Duration,
		Timestamp: metrics.Timestamp,
	}

	// 生成优化建议
	if m.config.EnableOptimizationSuggestions {
		slowQuery.Suggestion = m.generateOptimizationSuggestion(metrics)
	}

	// 添加到慢查询列表
	if len(m.slowQueries) >= m.config.MaxSlowQueries {
		// 移除最旧的记录
		m.slowQueries = m.slowQueries[1:]
	}
	m.slowQueries = append(m.slowQueries, slowQuery)
}

// generateOptimizationSuggestion 生成优化建议
func (m *QueryPerformanceMonitor) generateOptimizationSuggestion(metrics *QueryMetrics) string {
	suggestions := []string{}

	// 基于查询时间的建议
	if metrics.Duration > 5*time.Second {
		suggestions = append(suggestions, "查询时间过长，建议检查索引和查询条件")
	}

	// 基于返回行数的建议
	if metrics.RowsReturned > 1000 {
		suggestions = append(suggestions, "返回行数过多，建议使用分页查询")
	}

	// 基于缓存命中的建议
	if !metrics.CacheHit && metrics.Duration > 1*time.Second {
		suggestions = append(suggestions, "查询未命中缓存，建议添加缓存策略")
	}

	// 基于操作类型的建议
	if metrics.Operation == "GetOptimizedAdminOrderList" {
		if metrics.Duration > 2*time.Second {
			suggestions = append(suggestions, "订单列表查询慢，建议优化WHERE条件和索引")
		}
	}

	if len(suggestions) == 0 {
		return "查询性能正常"
	}

	return fmt.Sprintf("优化建议: %v", suggestions)
}

// monitorLoop 监控循环
func (m *QueryPerformanceMonitor) monitorLoop(ctx context.Context) {
	ticker := time.NewTicker(m.config.MonitorInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			m.collectDatabaseMetrics(ctx)
		}
	}
}

// collectDatabaseMetrics 收集数据库指标
func (m *QueryPerformanceMonitor) collectDatabaseMetrics(ctx context.Context) {
	// 查询活跃连接数
	var activeConns int
	err := m.db.QueryRowContext(ctx, "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'").Scan(&activeConns)
	if err == nil {
		m.statsMutex.Lock()
		m.stats.ActiveConnections = activeConns
		m.statsMutex.Unlock()
	}

	// 查询缓存命中率
	var cacheHitRate float64
	cacheQuery := `
		SELECT round(
			(sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read))) * 100, 2
		) as cache_hit_ratio
		FROM pg_statio_user_tables
		WHERE schemaname = 'public'
	`
	err = m.db.QueryRowContext(ctx, cacheQuery).Scan(&cacheHitRate)
	if err == nil {
		m.statsMutex.Lock()
		m.stats.CacheHitRate = cacheHitRate
		m.statsMutex.Unlock()
	}
}

// reportLoop 报告循环
func (m *QueryPerformanceMonitor) reportLoop(ctx context.Context) {
	ticker := time.NewTicker(m.config.ReportInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			m.generatePerformanceReport()
		}
	}
}

// generatePerformanceReport 生成性能报告
func (m *QueryPerformanceMonitor) generatePerformanceReport() {
	m.statsMutex.RLock()
	stats := *m.stats
	m.statsMutex.RUnlock()

	m.slowQueriesMutex.RLock()
	slowQueryCount := len(m.slowQueries)
	m.slowQueriesMutex.RUnlock()

	m.logger.Info("数据库性能报告",
		zap.Int64("total_queries", stats.TotalQueries),
		zap.Int64("slow_queries", stats.SlowQueries),
		zap.Duration("average_time", stats.AverageTime),
		zap.Duration("max_time", stats.MaxTime),
		zap.Duration("min_time", stats.MinTime),
		zap.Float64("cache_hit_rate", stats.CacheHitRate),
		zap.Int("active_connections", stats.ActiveConnections),
		zap.Int("slow_query_records", slowQueryCount))

	// 如果慢查询过多，发出警告
	if stats.SlowQueries > 0 {
		slowQueryRate := float64(stats.SlowQueries) / float64(stats.TotalQueries) * 100
		if slowQueryRate > 5.0 { // 慢查询率超过5%
			m.logger.Warn("慢查询率过高",
				zap.Float64("slow_query_rate", slowQueryRate),
				zap.String("suggestion", "建议检查数据库索引和查询优化"))
		}
	}
}

// GetStats 获取性能统计
func (m *QueryPerformanceMonitor) GetStats() *PerformanceStats {
	m.statsMutex.RLock()
	defer m.statsMutex.RUnlock()
	
	statsCopy := *m.stats
	return &statsCopy
}

// GetSlowQueries 获取慢查询记录
func (m *QueryPerformanceMonitor) GetSlowQueries(limit int) []SlowQuery {
	m.slowQueriesMutex.RLock()
	defer m.slowQueriesMutex.RUnlock()
	
	if limit <= 0 || limit > len(m.slowQueries) {
		limit = len(m.slowQueries)
	}
	
	// 返回最近的慢查询
	start := len(m.slowQueries) - limit
	if start < 0 {
		start = 0
	}
	
	result := make([]SlowQuery, limit)
	copy(result, m.slowQueries[start:])
	
	return result
}

// GetPerformanceReport 获取性能报告
func (m *QueryPerformanceMonitor) GetPerformanceReport() map[string]interface{} {
	stats := m.GetStats()
	slowQueries := m.GetSlowQueries(10)
	
	return map[string]interface{}{
		"stats":        stats,
		"slow_queries": slowQueries,
		"generated_at": time.Now(),
	}
}

// ExportMetrics 导出指标（用于Prometheus等监控系统）
func (m *QueryPerformanceMonitor) ExportMetrics() ([]byte, error) {
	report := m.GetPerformanceReport()
	return json.Marshal(report)
}
