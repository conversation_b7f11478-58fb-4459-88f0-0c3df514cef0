package database

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"go.uber.org/zap"
)

// TransactionManager 事务管理器
type TransactionManager struct {
	db      *sql.DB
	logger  *zap.Logger
	metrics *prometheus.CounterVec
}

// TransactionOptions 事务选项
type TransactionOptions struct {
	IsolationLevel sql.IsolationLevel
	ReadOnly       bool
	Timeout        time.Duration
}

// NewTransactionManager 创建事务管理器
func NewTransactionManager(db *sql.DB, logger *zap.Logger) *TransactionManager {
	// 创建Prometheus指标
	metrics := prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "database_transactions_total",
			Help: "Total number of database transactions",
		},
		[]string{"status"},
	)
	prometheus.MustRegister(metrics)

	return &TransactionManager{
		db:      db,
		logger:  logger,
		metrics: metrics,
	}
}

// ExecuteInTransaction 在事务中执行函数
func (tm *TransactionManager) ExecuteInTransaction(
	ctx context.Context,
	opts *TransactionOptions,
	fn func(context.Context, *sql.Tx) error,
) error {
	// 设置默认选项
	if opts == nil {
		opts = &TransactionOptions{
			IsolationLevel: sql.LevelDefault,
			ReadOnly:       false,
			Timeout:        30 * time.Second,
		}
	}

	// 设置超时
	if opts.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, opts.Timeout)
		defer cancel()
	}

	// 开始事务
	txOpts := &sql.TxOptions{
		Isolation: opts.IsolationLevel,
		ReadOnly:  opts.ReadOnly,
	}

	tx, err := tm.db.BeginTx(ctx, txOpts)
	if err != nil {
		tm.metrics.WithLabelValues("begin_error").Inc()
		tm.logger.Error("开始事务失败",
			zap.Error(err),
			zap.String("isolation_level", opts.IsolationLevel.String()),
			zap.Bool("read_only", opts.ReadOnly))
		return fmt.Errorf("begin transaction failed: %w", err)
	}

	// 记录事务开始
	start := time.Now()
	tm.logger.Debug("事务开始",
		zap.String("isolation_level", opts.IsolationLevel.String()),
		zap.Bool("read_only", opts.ReadOnly))

	// 执行业务逻辑
	err = fn(ctx, tx)

	// 记录事务执行时间
	duration := time.Since(start)
	tm.logger.Debug("事务执行完成",
		zap.Duration("duration", duration),
		zap.Error(err))

	// 提交或回滚
	if err != nil {
		if rollbackErr := tx.Rollback(); rollbackErr != nil {
			tm.logger.Error("事务回滚失败",
				zap.Error(rollbackErr),
				zap.Error(err),
				zap.Duration("duration", duration))
			tm.metrics.WithLabelValues("rollback_error").Inc()
			return fmt.Errorf("rollback failed: %w (original error: %v)", rollbackErr, err)
		}
		tm.metrics.WithLabelValues("rollback").Inc()
		tm.logger.Info("事务已回滚",
			zap.Error(err),
			zap.Duration("duration", duration))
		return err
	}

	if err := tx.Commit(); err != nil {
		tm.metrics.WithLabelValues("commit_error").Inc()
		tm.logger.Error("事务提交失败",
			zap.Error(err),
			zap.Duration("duration", duration))
		return fmt.Errorf("commit transaction failed: %w", err)
	}

	tm.metrics.WithLabelValues("success").Inc()
	tm.logger.Info("事务提交成功",
		zap.Duration("duration", duration))
	return nil
}

// ExecuteInReadOnlyTransaction 在只读事务中执行函数
func (tm *TransactionManager) ExecuteInReadOnlyTransaction(
	ctx context.Context,
	fn func(context.Context, *sql.Tx) error,
) error {
	opts := &TransactionOptions{
		IsolationLevel: sql.LevelReadCommitted,
		ReadOnly:       true,
		Timeout:        10 * time.Second,
	}
	return tm.ExecuteInTransaction(ctx, opts, fn)
}

// ExecuteInSerializableTransaction 在可序列化事务中执行函数
func (tm *TransactionManager) ExecuteInSerializableTransaction(
	ctx context.Context,
	fn func(context.Context, *sql.Tx) error,
) error {
	opts := &TransactionOptions{
		IsolationLevel: sql.LevelSerializable,
		ReadOnly:       false,
		Timeout:        30 * time.Second,
	}
	return tm.ExecuteInTransaction(ctx, opts, fn)
}

// ExecuteWithRetry 带重试的事务执行
func (tm *TransactionManager) ExecuteWithRetry(
	ctx context.Context,
	opts *TransactionOptions,
	maxRetries int,
	fn func(context.Context, *sql.Tx) error,
) error {
	var lastErr error
	
	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避
			backoff := time.Duration(attempt*attempt) * 100 * time.Millisecond
			tm.logger.Info("重试事务",
				zap.Int("attempt", attempt),
				zap.Duration("backoff", backoff))
			
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(backoff):
			}
		}
		
		err := tm.ExecuteInTransaction(ctx, opts, fn)
		if err == nil {
			if attempt > 0 {
				tm.logger.Info("事务重试成功",
					zap.Int("attempts", attempt+1))
			}
			return nil
		}
		
		lastErr = err
		
		// 检查是否应该重试
		if !isRetryableError(err) {
			tm.logger.Error("事务错误不可重试",
				zap.Error(err),
				zap.Int("attempt", attempt))
			break
		}
		
		tm.logger.Warn("事务失败，准备重试",
			zap.Error(err),
			zap.Int("attempt", attempt),
			zap.Int("max_retries", maxRetries))
	}
	
	tm.logger.Error("事务重试失败",
		zap.Error(lastErr),
		zap.Int("max_retries", maxRetries))
	return lastErr
}

// isRetryableError 检查错误是否可重试
func isRetryableError(err error) bool {
	if err == nil {
		return false
	}
	
	errStr := err.Error()
	
	// 可重试的错误类型
	retryableErrors := []string{
		"serialization failure",
		"deadlock detected",
		"could not serialize access",
		"connection reset",
		"connection refused",
		"timeout",
	}
	
	for _, retryableErr := range retryableErrors {
		if contains(errStr, retryableErr) {
			return true
		}
	}
	
	return false
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(s) > len(substr) && 
			(s[:len(substr)] == substr || 
			 s[len(s)-len(substr):] == substr || 
			 containsSubstring(s, substr))))
}

// containsSubstring 检查字符串是否包含子字符串
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// GetMetrics 获取事务指标
func (tm *TransactionManager) GetMetrics() *prometheus.CounterVec {
	return tm.metrics
}

// Close 关闭事务管理器
func (tm *TransactionManager) Close() error {
	if tm.db != nil {
		return tm.db.Close()
	}
	return nil
}
