package http

import (
	"context"
	"crypto/tls"
	"net"
	"net/http"
	"sync"
	"time"

	"go.uber.org/zap"
)

// HighPerformanceHTTPClient 高性能HTTP客户端
type HighPerformanceHTTPClient struct {
	client *http.Client
	logger *zap.Logger
	stats  *ClientStats
}

// ClientStats HTTP客户端统计信息
type ClientStats struct {
	mu                sync.RWMutex
	TotalRequests     int64
	SuccessRequests   int64
	FailedRequests    int64
	TimeoutRequests   int64
	TotalResponseTime time.Duration
	MinResponseTime   time.Duration
	MaxResponseTime   time.Duration
}

// NewHighPerformanceHTTPClient 创建高性能HTTP客户端
func NewHighPerformanceHTTPClient(logger *zap.Logger) *HighPerformanceHTTPClient {
	// 自定义Transport，优化性能
	transport := &http.Transport{
		// 连接池配置 - 增强版
		MaxIdleConns:        300, // 增加最大空闲连接数
		MaxIdleConnsPerHost: 100, // 增加每个主机最大空闲连接数
		MaxConnsPerHost:     200, // 增加每个主机最大连接数

		// 超时配置 - 优化版
		IdleConnTimeout:       60 * time.Second,       // 延长空闲连接超时
		TLSHandshakeTimeout:   3 * time.Second,        // 减少TLS握手超时
		ExpectContinueTimeout: 500 * time.Millisecond, // 减少Expect超时
		ResponseHeaderTimeout: 3 * time.Second,        // 减少响应头超时

		// 拨号配置 - 快速版
		DialContext: (&net.Dialer{
			Timeout:   1500 * time.Millisecond, // 减少连接超时
			KeepAlive: 60 * time.Second,        // 延长Keep-Alive时间
			DualStack: true,                    // 支持IPv4和IPv6
		}).DialContext,

		// TLS配置
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
			MinVersion:         tls.VersionTLS12,
		},

		// 启用压缩以减少传输时间（适合查价场景）
		DisableCompression: false,

		// 强制HTTP/2
		ForceAttemptHTTP2: true,
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   800 * time.Millisecond, // 平衡超时时间
	}

	return &HighPerformanceHTTPClient{
		client: client,
		logger: logger,
		stats:  &ClientStats{},
	}
}

// DoRequest 执行HTTP请求
func (c *HighPerformanceHTTPClient) DoRequest(ctx context.Context, req *http.Request) (*http.Response, error) {
	startTime := time.Now()

	// 更新统计
	c.stats.mu.Lock()
	c.stats.TotalRequests++
	c.stats.mu.Unlock()

	// 执行请求
	resp, err := c.client.Do(req.WithContext(ctx))

	duration := time.Since(startTime)

	// 更新统计信息
	c.updateStats(err, duration)

	if err != nil {
		// 检查是否是超时错误
		if ctx.Err() == context.DeadlineExceeded {
			c.stats.mu.Lock()
			c.stats.TimeoutRequests++
			c.stats.mu.Unlock()
		}

		c.stats.mu.Lock()
		c.stats.FailedRequests++
		c.stats.mu.Unlock()

		return nil, err
	}

	c.stats.mu.Lock()
	c.stats.SuccessRequests++
	c.stats.mu.Unlock()

	return resp, nil
}

// updateStats 更新统计信息
func (c *HighPerformanceHTTPClient) updateStats(err error, duration time.Duration) {
	c.stats.mu.Lock()
	defer c.stats.mu.Unlock()

	c.stats.TotalResponseTime += duration

	if c.stats.MinResponseTime == 0 || duration < c.stats.MinResponseTime {
		c.stats.MinResponseTime = duration
	}

	if duration > c.stats.MaxResponseTime {
		c.stats.MaxResponseTime = duration
	}
}

// GetStats 获取客户端统计信息
func (c *HighPerformanceHTTPClient) GetStats() ClientStats {
	c.stats.mu.RLock()
	defer c.stats.mu.RUnlock()

	return ClientStats{
		TotalRequests:     c.stats.TotalRequests,
		SuccessRequests:   c.stats.SuccessRequests,
		FailedRequests:    c.stats.FailedRequests,
		TimeoutRequests:   c.stats.TimeoutRequests,
		TotalResponseTime: c.stats.TotalResponseTime,
		MinResponseTime:   c.stats.MinResponseTime,
		MaxResponseTime:   c.stats.MaxResponseTime,
	}
}

// GetAverageResponseTime 获取平均响应时间
func (c *HighPerformanceHTTPClient) GetAverageResponseTime() time.Duration {
	c.stats.mu.RLock()
	defer c.stats.mu.RUnlock()

	if c.stats.TotalRequests == 0 {
		return 0
	}

	return time.Duration(int64(c.stats.TotalResponseTime) / c.stats.TotalRequests)
}

// GetSuccessRate 获取成功率
func (c *HighPerformanceHTTPClient) GetSuccessRate() float64 {
	c.stats.mu.RLock()
	defer c.stats.mu.RUnlock()

	if c.stats.TotalRequests == 0 {
		return 0
	}

	return float64(c.stats.SuccessRequests) / float64(c.stats.TotalRequests) * 100
}

// ResetStats 重置统计信息
func (c *HighPerformanceHTTPClient) ResetStats() {
	c.stats.mu.Lock()
	defer c.stats.mu.Unlock()

	c.stats.TotalRequests = 0
	c.stats.SuccessRequests = 0
	c.stats.FailedRequests = 0
	c.stats.TimeoutRequests = 0
	c.stats.TotalResponseTime = 0
	c.stats.MinResponseTime = 0
	c.stats.MaxResponseTime = 0
}

// WarmupConnections 预热连接池
func (c *HighPerformanceHTTPClient) WarmupConnections(urls []string) {
	var wg sync.WaitGroup

	for _, url := range urls {
		wg.Add(1)
		go func(targetURL string) {
			defer wg.Done()

			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			req, err := http.NewRequestWithContext(ctx, "HEAD", targetURL, nil)
			if err != nil {
				return
			}

			resp, err := c.client.Do(req)
			if err == nil && resp != nil {
				resp.Body.Close()
			}
		}(url)
	}

	wg.Wait()
	c.logger.Info("HTTP连接池预热完成", zap.Int("urls_count", len(urls)))
}

// OptimizeForProvider 为特定供应商优化客户端
func (c *HighPerformanceHTTPClient) OptimizeForProvider(providerName string) *http.Client {
	var transport *http.Transport
	var timeout time.Duration

	switch providerName {
	case "kuaidi100":
		// 快递100优化配置 - 通常响应较快
		transport = &http.Transport{
			MaxIdleConns:          150,
			MaxIdleConnsPerHost:   50,
			MaxConnsPerHost:       100,
			IdleConnTimeout:       45 * time.Second,
			TLSHandshakeTimeout:   2 * time.Second,
			ResponseHeaderTimeout: 2 * time.Second,
			DialContext: (&net.Dialer{
				Timeout:   1 * time.Second,
				KeepAlive: 45 * time.Second,
			}).DialContext,
			DisableCompression: false,
			ForceAttemptHTTP2:  true,
		}
		timeout = 600 * time.Millisecond

	case "yida":
		// 易达优化配置 - 中等响应速度
		transport = &http.Transport{
			MaxIdleConns:          120,
			MaxIdleConnsPerHost:   40,
			MaxConnsPerHost:       80,
			IdleConnTimeout:       30 * time.Second,
			TLSHandshakeTimeout:   2500 * time.Millisecond,
			ResponseHeaderTimeout: 2500 * time.Millisecond,
			DialContext: (&net.Dialer{
				Timeout:   1200 * time.Millisecond,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			DisableCompression: false,
			ForceAttemptHTTP2:  true,
		}
		timeout = 800 * time.Millisecond

	case "yuntong":
		// 云通优化配置 - 较慢但稳定
		transport = &http.Transport{
			MaxIdleConns:          100,
			MaxIdleConnsPerHost:   30,
			MaxConnsPerHost:       60,
			IdleConnTimeout:       20 * time.Second,
			TLSHandshakeTimeout:   3 * time.Second,
			ResponseHeaderTimeout: 3 * time.Second,
			DialContext: (&net.Dialer{
				Timeout:   1500 * time.Millisecond,
				KeepAlive: 20 * time.Second,
			}).DialContext,
			DisableCompression: false,
			ForceAttemptHTTP2:  true,
		}
		timeout = 1000 * time.Millisecond

	default:
		// 默认配置
		transport = c.client.Transport.(*http.Transport)
		timeout = 800 * time.Millisecond
	}

	return &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}
}

// StartPerformanceMonitoring 启动性能监控
func (c *HighPerformanceHTTPClient) StartPerformanceMonitoring() {
	go func() {
		ticker := time.NewTicker(1 * time.Minute)
		defer ticker.Stop()

		for range ticker.C {
			stats := c.GetStats()
			avgResponseTime := c.GetAverageResponseTime()
			successRate := c.GetSuccessRate()

			c.logger.Info("HTTP客户端性能统计",
				zap.Int64("total_requests", stats.TotalRequests),
				zap.Int64("success_requests", stats.SuccessRequests),
				zap.Int64("failed_requests", stats.FailedRequests),
				zap.Int64("timeout_requests", stats.TimeoutRequests),
				zap.Duration("avg_response_time", avgResponseTime),
				zap.Duration("min_response_time", stats.MinResponseTime),
				zap.Duration("max_response_time", stats.MaxResponseTime),
				zap.Float64("success_rate_percent", successRate))

			// 如果成功率低于90%，记录警告
			if successRate < 90 && stats.TotalRequests > 10 {
				c.logger.Warn("HTTP客户端成功率较低",
					zap.Float64("success_rate", successRate),
					zap.Int64("total_requests", stats.TotalRequests))
			}

			// 如果平均响应时间超过50ms，记录警告
			if avgResponseTime > 50*time.Millisecond && stats.TotalRequests > 10 {
				c.logger.Warn("HTTP客户端响应时间较慢",
					zap.Duration("avg_response_time", avgResponseTime),
					zap.Int64("total_requests", stats.TotalRequests))
			}
		}
	}()
}

// GetDetailedReport 获取详细性能报告
func (c *HighPerformanceHTTPClient) GetDetailedReport() map[string]interface{} {
	stats := c.GetStats()
	avgResponseTime := c.GetAverageResponseTime()
	successRate := c.GetSuccessRate()

	return map[string]interface{}{
		"total_requests":         stats.TotalRequests,
		"success_requests":       stats.SuccessRequests,
		"failed_requests":        stats.FailedRequests,
		"timeout_requests":       stats.TimeoutRequests,
		"success_rate_percent":   successRate,
		"avg_response_time_ms":   avgResponseTime.Milliseconds(),
		"min_response_time_ms":   stats.MinResponseTime.Milliseconds(),
		"max_response_time_ms":   stats.MaxResponseTime.Milliseconds(),
		"total_response_time_ms": stats.TotalResponseTime.Milliseconds(),
	}
}
