package constants

// 供应商常量定义
const (
	// 供应商标识
	ProviderYuntong    = "yuntong"
	ProviderYida       = "yida"
	ProviderKuaidi100  = "kuaidi100"
	ProviderCainiao    = "cainiao"
	ProviderKuaidiNiao = "kuaidiniao"
)

// ProviderNames 供应商名称映射
var ProviderNames = map[string]string{
	ProviderYuntong:    "云通",
	ProviderYida:       "易达",
	ProviderKuaidi100:  "快递100",
	ProviderCainiao:    "菜鸟裹裹",
	ProviderKuaidiNiao: "快递鸟",
}

// SupportedProviders 支持的供应商列表
var SupportedProviders = []string{
	ProviderYuntong,
	ProviderYida,
	ProviderKuaidi100,
	ProviderCainiao,
	ProviderKuaidiNiao,
}

// GetProviderName 获取供应商中文名称
func GetProviderName(provider string) string {
	if name, ok := ProviderNames[provider]; ok {
		return name
	}
	return provider
}

// IsValidProvider 检查是否为有效的供应商
func IsValidProvider(provider string) bool {
	for _, p := range SupportedProviders {
		if p == provider {
			return true
		}
	}
	return false
}

// 状态映射相关常量
const (
	// 状态转换验证模式
	ValidationModeStrict = "strict" // 严格模式：非法转换直接报错
	ValidationModeWarn   = "warn"   // 警告模式：非法转换记录警告但继续
	ValidationModeSkip   = "skip"   // 跳过模式：不进行转换验证
)

// 默认配置
const (
	DefaultValidationMode = ValidationModeStrict
	DefaultPageSize       = 20
	MaxPageSize           = 100
)

// 业务常量
const (
	// 状态历史保留天数
	StatusHistoryRetentionDays = 90

	// 状态转换超时时间（小时）
	StatusTransitionTimeoutHours = 72

	// 异常状态告警阈值
	ExceptionAlertThreshold = 10
)
