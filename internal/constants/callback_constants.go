package constants

// 云通回调类型常量
const (
	YuntongRequestTypeOrderPush   = "103" // 订单推送
	YuntongRequestTypeBillingPush = "104" // 到付月结计费推送
	YuntongRequestTypeTicketReply = "105" // 工单回复推送
)

// 易达回调类型常量
const (
	YidaPushTypeStatusPush  = 1 // 状态推送
	YidaPushTypeBillingPush = 2 // 计费推送
	YidaPushTypePickupPush  = 3 // 揽收推送
	YidaPushTypeOrderChange = 5 // 订单变更
)

// 快递100回调状态常量
const (
	Kuaidi100StatusPolling  = "polling"  // 轮询中
	Kuaidi100StatusShutdown = "shutdown" // 轮询结束
	Kuaidi100StatusAbort    = "abort"    // 轮询中止
)

// 回调类型常量
const (
	CallbackTypeOrderPush        = "order_push"
	CallbackTypeBillingPush      = "billing_push"
	CallbackTypeTicketReply      = "ticket_reply"
	CallbackTypeStatusPush       = "status_push"
	CallbackTypePickupPush       = "pickup_push"
	CallbackTypeOrderChange      = "order_change"
	CallbackTypeStatusUpdate     = "status_update"
	CallbackTypeTrackingComplete = "tracking_complete"
	CallbackTypeTrackingAbort    = "tracking_abort"
	CallbackTypeOrderCallback    = "order_callback"
)

// 标准状态常量
const (
	StatusCreated            = "created"
	StatusFailed             = "failed"
	StatusAssigned           = "assigned"
	StatusPendingPickup      = "pending_pickup"
	StatusPickedUp           = "picked_up"
	StatusPickupFailed       = "pickup_failed"
	StatusAccepted           = "accepted"
	StatusCollecting         = "collecting"
	StatusInTransit          = "in_transit"
	StatusDelivering         = "delivering"
	StatusDelivered          = "delivered"
	StatusExceptionDelivered = "exception_delivered"
	StatusException          = "exception"
	StatusCanceled           = "canceled"
	StatusReturned           = "returned"
	StatusForwarded          = "forwarded"
	// 🚨 已删除：StatusSettled - 结算功能已废弃
	StatusWeightUpdated = "weight_updated"
	StatusRevived       = "revived"
	StatusPrinted       = "printed"
	StatusPrintFailed   = "print_failed"
	StatusUnknown       = "unknown"
)

// 云通状态码常量
const (
	YuntongStateOrderSuccess  = 100 // 下单成功
	YuntongStateOrderFailed   = 400 // 下单失败
	YuntongStateAssignNetwork = 102 // 分配网点
	YuntongStateAssignCourier = 103 // 分配快递员
	YuntongStatePickedUp      = 104 // 已取件
	YuntongStateBilling       = 301 // 计费/已揽件
	YuntongStateUpdateWeight  = 208 // 更新重量
	YuntongStateCancelOrder   = 203 // 取消订单
	YuntongStatePickupFailed  = 204 // 揽收失败
	YuntongStateVoid          = 205 // 作废
	YuntongStateInTransit     = 2   // 在途中
	YuntongStateDelivered     = 3   // 签收
	YuntongStateException     = 500 // 异常
	YuntongStateForwarded     = 501 // 已转寄
	YuntongStateTicketReply   = 401 // 工单回复（固定值）
)

// 云通操作类型常量
const (
	YuntongOperateTypeTicketReply = 1 // 工单回复操作类型（固定值）
)

// 易达状态常量
const (
	YidaStatusPendingPickup = "1"  // 待取件
	YidaStatusPickedUp      = "11" // 已取件
	YidaStatusInTransit     = "2"  // 运输中
	YidaStatusDelivered     = "3"  // 已签收
	YidaStatusException     = "6"  // 异常
	YidaStatusCancelled     = "10" // 已取消
)

// 易达工单状态常量
const (
	YidaTicketStatusPending   = 1 // 待回复
	YidaTicketStatusReplied   = 2 // 已回复
	YidaTicketStatusCompleted = 3 // 已完结
)

// 易达工单类型常量（统一化改造：只保留5种核心类型）
const (
	YidaTicketTypePickupUrge      = 1  // 催揽收
	YidaTicketTypeWeightException = 2  // 重量异常
	YidaTicketTypeDeliveryUrge    = 10 // 催派送
	YidaTicketTypeReassignCourier = 15 // 重新分配快递员
	YidaTicketTypeCancelOrder     = 16 // 取消运单
)

// 易达费用类型常量
const (
	YidaFeeTypeFreight   = 0   // 快递运费
	YidaFeeTypeInsurance = 1   // 保价费
	YidaFeeTypeSpringFee = 2   // 春节加派费
	YidaFeeTypeMaterial  = 3   // 耗材费
	YidaFeeTypeReverse   = 10  // 逆向费
	YidaFeeTypeOther     = 100 // 其他费用
)

// 快递100状态码常量
const (
	Kuaidi100StatusCreated            = 0   // 下单成功
	Kuaidi100StatusAccepted           = 1   // 已接单
	Kuaidi100StatusCollecting         = 2   // 收件中
	Kuaidi100StatusUserCancel         = 9   // 用户主动取消
	Kuaidi100StatusPickedUp           = 10  // 已取件
	Kuaidi100StatusPickupFailed       = 11  // 揽货失败
	Kuaidi100StatusReturned           = 12  // 已退回
	Kuaidi100StatusDelivered          = 13  // 已签收
	Kuaidi100StatusExceptionDelivered = 14  // 异常签收
	Kuaidi100StatusSettled            = 15  // 已结算
	Kuaidi100StatusCancelled          = 99  // 订单已取消
	Kuaidi100StatusInTransit          = 101 // 运输中
	Kuaidi100StatusWeightUpdated      = 155 // 修改重量
	Kuaidi100StatusRevived            = 166 // 订单复活
	Kuaidi100StatusPrinted            = 200 // 已出单
	Kuaidi100StatusPrintFailed        = 201 // 出单失败
	Kuaidi100StatusDelivering         = 400 // 派送中
	Kuaidi100StatusOrderFailed        = 610 // 下单失败
)

// HTTP响应码常量
const (
	HTTPStatusOK                  = "200"
	HTTPStatusBadRequest          = "400"
	HTTPStatusUnauthorized        = "401"
	HTTPStatusForbidden           = "403"
	HTTPStatusNotFound            = "404"
	HTTPStatusInternalServerError = "500"
)

// 响应消息常量
const (
	MessageSuccess          = "成功"
	MessageReceived         = "接收成功"
	MessageFailed           = "失败"
	MessageInvalidSignature = "签名验证失败"
	MessageInvalidData      = "数据格式错误"
	MessageUnknownProvider  = "未知供应商"
	MessageUnknownType      = "未知回调类型"
)

// 快递100响应字段常量
const (
	Kuaidi100ResponseFieldResult     = "result"
	Kuaidi100ResponseFieldReturnCode = "returnCode"
	Kuaidi100ResponseFieldMessage    = "message"
)

// 易达响应字段常量
const (
	YidaResponseFieldCode    = "code"
	YidaResponseFieldSuccess = "success"
)

// 云通响应字段常量
const (
	YuntongResponseFieldReason  = "Reason"
	YuntongResponseFieldSuccess = "Success"
)

// 响应值常量
const (
	ResponseValueTrue  = true
	ResponseValueFalse = false
)

// 回调重试策略常量
const (
	// 重试间隔（秒）
	RetryIntervalLevel1 = 30   // 第1次重试：30秒后
	RetryIntervalLevel2 = 300  // 第2次重试：5分钟后
	RetryIntervalLevel3 = 1800 // 第3次重试：30分钟后
	RetryIntervalLevel4 = 3600 // 第4次重试：1小时后
	RetryIntervalLevel5 = 7200 // 第5次重试：2小时后

	// 最大重试次数
	MaxRetryCount = 5

	// 重试触发条件
	RetryableHTTPStatus500 = 500 // 服务器内部错误
	RetryableHTTPStatus502 = 502 // 网关错误
	RetryableHTTPStatus503 = 503 // 服务不可用
	RetryableHTTPStatus504 = 504 // 网关超时
	RetryableHTTPStatus429 = 429 // 请求过多
)

// 回调优先级常量
const (
	CallbackPriorityHigh   = "high"   // 高优先级（订单状态变更）
	CallbackPriorityMedium = "medium" // 中优先级（计费更新）
	CallbackPriorityLow    = "low"    // 低优先级（工单回复）
)

// 回调队列常量
const (
	CallbackQueueHigh   = "callback:queue:high"
	CallbackQueueMedium = "callback:queue:medium"
	CallbackQueueLow    = "callback:queue:low"
	CallbackQueueRetry  = "callback:queue:retry"
)

// 时间格式常量
const (
	TimeFormatYuntong = "2006-01-02 15:04:05"
	TimeFormatRFC3339 = "2006-01-02T15:04:05Z07:00"
	TimeFormatDate    = "2006-01-02"
)

// 回调默认配置常量
const (
	DefaultTimeout        = 30   // 默认超时时间（秒）
	DefaultRetryCount     = 3    // 默认重试次数
	DefaultMaxRetryCount  = 5    // 最大重试次数
	DefaultMaxPageSize    = 100  // 最大分页大小
	DefaultMaxWorkers     = 10   // 默认工作协程数
	DefaultBufferSize     = 1000 // 默认缓冲区大小
	DefaultMaxConcurrency = 50   // 默认最大并发数
)

// 事件类型常量
const (
	EventTypeOrderStatusChanged = "order_status_changed"
	EventTypeBillingUpdated     = "billing_updated"
	EventTypeTicketReplied      = "ticket_replied"
	EventTypePickupInfoUpdated  = "pickup_info_updated"
	EventTypeOrderChanged       = "order_changed"
	EventTypeTrackingUpdated    = "tracking_updated"
	EventTypeTrackingCompleted  = "tracking_completed"
	EventTypeTrackingAborted    = "tracking_aborted"
)

// 回调状态常量
const (
	CallbackStatusPending    = "pending"
	CallbackStatusProcessing = "processing"
	CallbackStatusSuccess    = "success"
	CallbackStatusFailed     = "failed"
)

// 费用类型常量
const (
	FeeTypeFreight   = "freight"   // 运费
	FeeTypeInsurance = "insurance" // 保价费
	FeeTypePackage   = "package"   // 包装费
	FeeTypeOther     = "other"     // 其他费用
)

// 状态分类常量
const (
	StatusCategorySuccess    = "success"
	StatusCategoryFailure    = "failure"
	StatusCategoryTerminal   = "terminal"
	StatusCategoryProcessing = "processing"
)

// 队列类型常量
const (
	QueueTypeRedis    = "redis"
	QueueTypeRabbitMQ = "rabbitmq"
	QueueTypeKafka    = "kafka"
)

// 签名算法常量
const (
	SignatureAlgorithmMD5        = "md5"
	SignatureAlgorithmSHA256     = "sha256"
	SignatureAlgorithmHMACSHA256 = "hmac-sha256"
	SignatureAlgorithmRSA        = "rsa"
)

// 编码常量
const (
	EncodingUTF8   = "utf-8"
	EncodingBase64 = "base64"
	EncodingHex    = "hex"
	EncodingURL    = "url"
)

// 内容类型常量
const (
	ContentTypeJSON = "application/json"
	ContentTypeForm = "application/x-www-form-urlencoded"
	ContentTypeXML  = "application/xml"
)

// 请求头常量
const (
	HeaderContentType   = "Content-Type"
	HeaderUserAgent     = "User-Agent"
	HeaderSignature     = "X-Signature"
	HeaderTimestamp     = "X-Timestamp"
	HeaderAuthorization = "Authorization"
)

// 用户代理常量
const (
	UserAgentGoKuaidi = "Go-Kuaidi-Callback/1.0"
)

// 数据库表名常量
const (
	TableUnifiedCallbackRecords = "unified_callback_records"
	TableCallbackForwardRecords = "callback_forward_records"
	TableUserCallbackConfigs    = "user_callback_configs"
	TableOrderRecords           = "order_records"
)

// 缓存键前缀常量
const (
	CacheKeyPrefixCallback     = "callback:"
	CacheKeyPrefixUserConfig   = "user_config:"
	CacheKeyPrefixOrderMapping = "order_mapping:"
	CacheKeyPrefixStatistics   = "statistics:"
)

// 日志级别常量
const (
	LogLevelDebug = "debug"
	LogLevelInfo  = "info"
	LogLevelWarn  = "warn"
	LogLevelError = "error"
)

// 环境常量
const (
	EnvDevelopment = "development"
	EnvTesting     = "testing"
	EnvStaging     = "staging"
	EnvProduction  = "production"
)
