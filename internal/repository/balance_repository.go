package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/infrastructure/database"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// BalanceRepository 余额仓储接口
type BalanceRepository interface {
	// 余额操作
	GetBalance(ctx context.Context, userID string) (*model.UserBalance, error)
	CreateBalance(ctx context.Context, balance *model.UserBalance) error
	UpdateBalance(ctx context.Context, balance *model.UserBalance) error
	UpdateBalanceWithVersion(ctx context.Context, userID string, balance, frozenBalance decimal.Decimal, version int64) error

	// 交易记录
	CreateTransaction(ctx context.Context, tx *model.BalanceTransaction) error
	GetTransactionsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.BalanceTransaction, error)
	GetTransactionsByUserIDWithFilters(ctx context.Context, userID string, limit, offset int, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo string) ([]*model.BalanceTransaction, error)
	GetTransactionCountByUserID(ctx context.Context, userID string) (int64, error)
	GetTransactionCountByUserIDWithFilters(ctx context.Context, userID string, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo string) (int64, error)
	GetTransactionByID(ctx context.Context, id string) (*model.BalanceTransaction, error)
	GetTransactionByReferenceID(ctx context.Context, referenceID string) (*model.BalanceTransaction, error)
	GetTransactionByOrderNo(ctx context.Context, orderNo string) (*model.BalanceTransaction, error)
	GetTransactionsByOrderNo(ctx context.Context, userID, orderNo string, limit, offset int) ([]*model.BalanceTransaction, error)
	GetTransactionsByCustomerOrderNo(ctx context.Context, userID, customerOrderNo string, limit, offset int) ([]*model.BalanceTransaction, error)
	GetPreChargeTransactionByOrderNo(ctx context.Context, orderNo string) (*model.BalanceTransaction, error)
	UpdateTransactionStatus(ctx context.Context, transactionID string, status model.TransactionStatus) error

	// 充值记录
	CreateDeposit(ctx context.Context, deposit *model.Deposit) error
	GetDepositByID(ctx context.Context, id string) (*model.Deposit, error)
	GetDepositsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.Deposit, error)
	UpdateDeposit(ctx context.Context, deposit *model.Deposit) error

	// 支付记录
	CreateOrderPayment(ctx context.Context, payment *model.OrderPayment) error
	GetOrderPaymentByOrderNo(ctx context.Context, orderNo string) (*model.OrderPayment, error)
	GetOrderPaymentsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.OrderPayment, error)
}

// PostgresBalanceRepository PostgreSQL余额仓储实现
type PostgresBalanceRepository struct {
	db *sql.DB
}

// NewPostgresBalanceRepository 创建PostgreSQL余额仓储
func NewPostgresBalanceRepository(db *sql.DB) BalanceRepository {
	return &PostgresBalanceRepository{
		db: db,
	}
}

// ---- 事务感知 SQL 执行辅助 ----
// 如果 ctx 中携带 *sql.Tx，则在该事务内执行；否则走连接池。
func (r *PostgresBalanceRepository) exec(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	if tx := database.TxFromContext(ctx); tx != nil {
		return tx.ExecContext(ctx, query, args...)
	}
	return r.db.ExecContext(ctx, query, args...)
}

// GetBalance 获取用户余额
func (r *PostgresBalanceRepository) GetBalance(ctx context.Context, userID string) (*model.UserBalance, error) {
	query := `
		SELECT id, user_id, balance, currency, status, version, created_at, updated_at
		FROM user_balances
		WHERE user_id = $1
	`

	var balance model.UserBalance
	var balanceStr string

	err := r.db.QueryRowContext(ctx, query, userID).Scan(
		&balance.ID,
		&balance.UserID,
		&balanceStr,
		&balance.Currency,
		&balance.Status,
		&balance.Version,
		&balance.CreatedAt,
		&balance.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("用户余额不存在")
		}
		return nil, fmt.Errorf("查询用户余额失败: %w", err)
	}

	// 转换decimal
	balance.Balance, _ = decimal.NewFromString(balanceStr)

	return &balance, nil
}

// CreateBalance 创建用户余额
func (r *PostgresBalanceRepository) CreateBalance(ctx context.Context, balance *model.UserBalance) error {
	if balance.ID == "" {
		balance.ID = uuid.New().String()
	}

	now := util.NowBeijing()
	balance.CreatedAt = now
	balance.UpdatedAt = now

	query := `
		INSERT INTO user_balances (id, user_id, balance, currency, status, version, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	_, err := r.exec(ctx, query,
		balance.ID,
		balance.UserID,
		balance.Balance.String(),
		balance.Currency,
		balance.Status,
		balance.Version,
		balance.CreatedAt,
		balance.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("创建用户余额失败: %w", err)
	}

	return nil
}

// UpdateBalance 更新用户余额
func (r *PostgresBalanceRepository) UpdateBalance(ctx context.Context, balance *model.UserBalance) error {
	balance.UpdatedAt = util.NowBeijing()

	query := `
		UPDATE user_balances
		SET balance = $1, status = $2, version = $3, updated_at = $4
		WHERE user_id = $5
	`

	result, err := r.exec(ctx, query,
		balance.Balance.String(),
		balance.Status,
		balance.Version,
		balance.UpdatedAt,
		balance.UserID,
	)

	if err != nil {
		return fmt.Errorf("更新用户余额失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("用户余额不存在或版本冲突")
	}

	return nil
}

// UpdateBalanceWithVersion 使用乐观锁更新用户余额（frozenBalance参数保留以兼容，但不使用）
func (r *PostgresBalanceRepository) UpdateBalanceWithVersion(ctx context.Context, userID string, balance, frozenBalance decimal.Decimal, version int64) error {
	query := `
		UPDATE user_balances
		SET balance = $1, version = version + 1, updated_at = NOW()
		WHERE user_id = $2 AND version = $3
	`

	result, err := r.exec(ctx, query,
		balance.String(),
		userID,
		version,
	)

	if err != nil {
		return fmt.Errorf("更新用户余额失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("余额版本冲突，请重试")
	}

	return nil
}

// CreateTransaction 创建交易记录
// 🔥 企业级改造：支持增强的交易记录字段
func (r *PostgresBalanceRepository) CreateTransaction(ctx context.Context, tx *model.BalanceTransaction) error {
	if tx.ID == "" {
		tx.ID = uuid.New().String()
	}

	tx.CreatedAt = util.NowBeijing()

	// 自动设置交易分类信息
	if tx.Category == "" {
		tx.Category = string(tx.Type.GetCategory())
	}
	if tx.SubType == "" {
		tx.SubType = string(tx.Type)
	}

	// 序列化metadata
	metadataJSON, err := json.Marshal(tx.Metadata)
	if err != nil {
		return fmt.Errorf("序列化metadata失败: %w", err)
	}

	// 序列化business_context
	businessContextJSON, err := json.Marshal(tx.BusinessContext)
	if err != nil {
		return fmt.Errorf("序列化business_context失败: %w", err)
	}

	query := `
		INSERT INTO balance_transactions (
			id, user_id, transaction_type, amount, currency, balance_before, balance_after,
			order_no, platform_order_no, customer_order_no, tracking_no, transaction_category, transaction_sub_type,
			reference_id, description, detail_description, user_friendly_desc,
			metadata, business_context, related_transaction_id, operator_id, status, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23)
	`

	// 🔥 企业级修复：处理UUID字段的NULL值
	var relatedTransactionID interface{}
	if tx.RelatedTransactionID != "" {
		relatedTransactionID = tx.RelatedTransactionID
	} else {
		relatedTransactionID = nil
	}

	_, err = r.exec(ctx, query,
		tx.ID,                     // $1
		tx.UserID,                 // $2
		string(tx.Type),           // $3
		tx.Amount.String(),        // $4
		tx.Currency,               // $5
		tx.BalanceBefore.String(), // $6
		tx.BalanceAfter.String(),  // $7
		tx.OrderNo,                // $8
		tx.PlatformOrderNo,        // $9
		tx.CustomerOrderNo,        // $10
		tx.TrackingNo,             // $11
		tx.Category,               // $12
		tx.SubType,                // $13
		tx.ReferenceID,            // $14
		tx.Description,            // $15
		tx.DetailDescription,      // $16
		tx.UserFriendlyDesc,       // $17
		metadataJSON,              // $18
		businessContextJSON,       // $19
		relatedTransactionID,      // $20 - 处理NULL值
		tx.OperatorID,             // $21
		string(tx.Status),         // $22
		tx.CreatedAt,              // $23
	)

	if err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}

	return nil
}

// GetTransactionsByUserID 根据用户ID获取交易记录
func (r *PostgresBalanceRepository) GetTransactionsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.BalanceTransaction, error) {
	query := `
		SELECT id, user_id, transaction_type, amount, currency, balance_before, balance_after,
			   order_no, reference_id, description, metadata, operator_id, status, created_at
		FROM balance_transactions
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询交易记录失败: %w", err)
	}
	defer rows.Close()

	var transactions []*model.BalanceTransaction
	for rows.Next() {
		tx := &model.BalanceTransaction{}
		var amountStr, balanceBeforeStr, balanceAfterStr string
		var metadataJSON []byte

		err := rows.Scan(
			&tx.ID,
			&tx.UserID,
			&tx.Type,
			&amountStr,
			&tx.Currency,
			&balanceBeforeStr,
			&balanceAfterStr,
			&tx.OrderNo,
			&tx.ReferenceID,
			&tx.Description,
			&metadataJSON,
			&tx.OperatorID,
			&tx.Status,
			&tx.CreatedAt,
		)

		if err != nil {
			return nil, fmt.Errorf("扫描交易记录失败: %w", err)
		}

		// 转换decimal
		tx.Amount, _ = decimal.NewFromString(amountStr)
		tx.BalanceBefore, _ = decimal.NewFromString(balanceBeforeStr)
		tx.BalanceAfter, _ = decimal.NewFromString(balanceAfterStr)

		// 反序列化metadata
		if len(metadataJSON) > 0 {
			json.Unmarshal(metadataJSON, &tx.Metadata)
		}

		transactions = append(transactions, tx)
	}

	return transactions, nil
}

// GetTransactionCountByUserID 获取用户交易记录总数
func (r *PostgresBalanceRepository) GetTransactionCountByUserID(ctx context.Context, userID string) (int64, error) {
	query := `
		SELECT COUNT(*)
		FROM balance_transactions
		WHERE user_id = $1
	`

	var count int64
	err := r.db.QueryRowContext(ctx, query, userID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("查询交易记录总数失败: %w", err)
	}

	return count, nil
}

// GetTransactionsByUserIDWithFilters 根据用户ID和筛选条件获取交易记录
// 🔥 企业级改造：支持增强的交易记录字段查询
func (r *PostgresBalanceRepository) GetTransactionsByUserIDWithFilters(ctx context.Context, userID string, limit, offset int, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo string) ([]*model.BalanceTransaction, error) {
	query := `
		SELECT bt.id, bt.user_id, bt.transaction_type, bt.amount, bt.currency, bt.balance_before, bt.balance_after,
			   bt.order_no,
			   COALESCE(NULLIF(bt.platform_order_no, ''), ord.platform_order_no, '') as platform_order_no,
			   COALESCE(bt.customer_order_no, '') as customer_order_no,
			   COALESCE(NULLIF(bt.tracking_no, ''), ord.tracking_no, '') as tracking_no,
			   COALESCE(bt.transaction_category, '') as transaction_category,
			   COALESCE(bt.transaction_sub_type, '') as transaction_sub_type,
			   bt.reference_id, bt.description,
			   COALESCE(bt.detail_description, '') as detail_description,
			   COALESCE(bt.user_friendly_desc, '') as user_friendly_desc,
			   bt.metadata,
			   COALESCE(bt.business_context, '{}') as business_context,
			   bt.related_transaction_id,
			   bt.operator_id, bt.status, bt.created_at
		FROM balance_transactions bt
		LEFT JOIN order_records ord ON (
			bt.order_no = ord.customer_order_no 
			OR bt.order_no = ord.order_no
			OR bt.customer_order_no = ord.customer_order_no
			OR bt.customer_order_no = ord.order_no
		)
		WHERE bt.user_id = $1`

	args := []interface{}{userID}
	argIndex := 2

	// 添加类型筛选
	if typeFilter != "" {
		query += fmt.Sprintf(" AND bt.transaction_type = $%d", argIndex)
		args = append(args, typeFilter)
		argIndex++
	}

	// 添加状态筛选
	if statusFilter != "" {
		query += fmt.Sprintf(" AND bt.status = $%d", argIndex)
		args = append(args, statusFilter)
		argIndex++
	}

	// 添加时间范围筛选
	if startTime != "" {
		query += fmt.Sprintf(" AND bt.created_at >= $%d", argIndex)
		args = append(args, startTime)
		argIndex++
	}

	if endTime != "" {
		query += fmt.Sprintf(" AND bt.created_at <= $%d", argIndex)
		args = append(args, endTime)
		argIndex++
	}

	// 🔥 企业级修复：确保无论使用哪个订单号都能查到所有相关记录
	// 通过订单表关联查询，建立订单号之间的关联关系
	if customerOrderNo != "" || orderNo != "" {
		// 构建关联查询，确保能查到所有相关的交易记录
		query = `
			SELECT DISTINCT bt.id, bt.user_id, bt.transaction_type, bt.amount, bt.currency, bt.balance_before, bt.balance_after,
				   bt.order_no,
				   COALESCE(NULLIF(bt.platform_order_no, ''), ord.platform_order_no, '') as platform_order_no,
				   COALESCE(bt.customer_order_no, '') as customer_order_no,
				   COALESCE(NULLIF(bt.tracking_no, ''), ord.tracking_no, '') as tracking_no,
				   COALESCE(bt.transaction_category, '') as transaction_category,
				   COALESCE(bt.transaction_sub_type, '') as transaction_sub_type,
				   bt.reference_id, bt.description,
				   COALESCE(bt.detail_description, '') as detail_description,
				   COALESCE(bt.user_friendly_desc, '') as user_friendly_desc,
				   bt.metadata,
				   COALESCE(bt.business_context, '{}') as business_context,
				   bt.related_transaction_id,
				   bt.operator_id, bt.status, bt.created_at
			FROM balance_transactions bt
			LEFT JOIN order_records ord ON (
				-- 🚀 性能优化：优先匹配platform_order_no，减少OR条件
				bt.platform_order_no = ord.platform_order_no
				OR (bt.platform_order_no IS NULL AND (
					bt.order_no = ord.customer_order_no
					OR bt.order_no = ord.order_no
					OR bt.customer_order_no = ord.customer_order_no  
					OR bt.customer_order_no = ord.order_no
				))
			)
			WHERE bt.user_id = $1`

		// 添加订单号筛选条件
		var orderConditions []string
		if customerOrderNo != "" {
			orderConditions = append(orderConditions, fmt.Sprintf("(ord.customer_order_no = $%d OR ord.order_no = $%d OR bt.order_no = $%d OR bt.customer_order_no = $%d)", argIndex, argIndex, argIndex, argIndex))
			args = append(args, customerOrderNo)
			argIndex++
		}
		if orderNo != "" {
			// 🔥 修复：添加platform_order_no字段的查询，支持平台订单号筛选
			orderConditions = append(orderConditions, fmt.Sprintf("(ord.platform_order_no = $%d OR bt.platform_order_no = $%d OR ord.customer_order_no = $%d OR ord.order_no = $%d OR bt.order_no = $%d OR bt.customer_order_no = $%d)", argIndex, argIndex, argIndex, argIndex, argIndex, argIndex))
			args = append(args, orderNo)
			argIndex++
		}

		if len(orderConditions) > 0 {
			query += " AND (" + strings.Join(orderConditions, " OR ") + ")"
		}

		// 添加其他筛选条件
		if typeFilter != "" {
			query += fmt.Sprintf(" AND bt.transaction_type = $%d", argIndex)
			args = append(args, typeFilter)
			argIndex++
		}
		if statusFilter != "" {
			query += fmt.Sprintf(" AND bt.status = $%d", argIndex)
			args = append(args, statusFilter)
			argIndex++
		}
		if startTime != "" {
			query += fmt.Sprintf(" AND bt.created_at >= $%d", argIndex)
			args = append(args, startTime)
			argIndex++
		}
		if endTime != "" {
			query += fmt.Sprintf(" AND bt.created_at <= $%d", argIndex)
			args = append(args, endTime)
			argIndex++
		}

		query += " ORDER BY bt.created_at DESC"
		query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
		args = append(args, limit, offset)

		// 直接执行查询并返回
		rows, err := r.db.QueryContext(ctx, query, args...)
		if err != nil {
			return nil, fmt.Errorf("查询交易记录失败: %w", err)
		}
		defer rows.Close()

		// 扫描结果
		var transactions []*model.BalanceTransaction
		for rows.Next() {
			tx := &model.BalanceTransaction{}
			var amountStr, balanceBeforeStr, balanceAfterStr string
			var metadataJSON, businessContextJSON []byte
			var orderNo, platformOrderNo, referenceID, description sql.NullString
			var relatedTransactionID sql.NullString

			err := rows.Scan(
				&tx.ID,
				&tx.UserID,
				&tx.Type,
				&amountStr,
				&tx.Currency,
				&balanceBeforeStr,
				&balanceAfterStr,
				&orderNo,
				&platformOrderNo,
				&tx.CustomerOrderNo,
				&tx.TrackingNo,
				&tx.Category,
				&tx.SubType,
				&referenceID,
				&description,
				&tx.DetailDescription,
				&tx.UserFriendlyDesc,
				&metadataJSON,
				&businessContextJSON,
				&relatedTransactionID,
				&tx.OperatorID,
				&tx.Status,
				&tx.CreatedAt,
			)
			if err != nil {
				return nil, fmt.Errorf("扫描交易记录失败: %w", err)
			}

			// 转换decimal字段
			tx.Amount, _ = decimal.NewFromString(amountStr)
			tx.BalanceBefore, _ = decimal.NewFromString(balanceBeforeStr)
			tx.BalanceAfter, _ = decimal.NewFromString(balanceAfterStr)

			// 处理可空字段
			if orderNo.Valid {
				tx.OrderNo = orderNo.String
			}
			if platformOrderNo.Valid {
				tx.PlatformOrderNo = platformOrderNo.String
			}
			if referenceID.Valid {
				tx.ReferenceID = referenceID.String
			}
			if description.Valid {
				tx.Description = description.String
			}
			if relatedTransactionID.Valid {
				tx.RelatedTransactionID = relatedTransactionID.String
			}

			// 反序列化JSON字段
			if len(metadataJSON) > 0 {
				json.Unmarshal(metadataJSON, &tx.Metadata)
			}
			if len(businessContextJSON) > 0 {
				json.Unmarshal(businessContextJSON, &tx.BusinessContext)
			}

			transactions = append(transactions, tx)
		}

		if err = rows.Err(); err != nil {
			return nil, fmt.Errorf("遍历交易记录失败: %w", err)
		}

		return transactions, nil
	}

	// 添加运单号筛选（需要通过订单表关联查询）
	if trackingNo != "" {
		query = `
			SELECT bt.id, bt.user_id, bt.transaction_type, bt.amount, bt.currency, bt.balance_before, bt.balance_after,
				   bt.order_no,
				   COALESCE(NULLIF(bt.platform_order_no, ''), ord.platform_order_no, '') as platform_order_no,
				   COALESCE(bt.customer_order_no, '') as customer_order_no,
				   COALESCE(NULLIF(bt.tracking_no, ''), ord.tracking_no, '') as tracking_no,
				   COALESCE(bt.transaction_category, '') as transaction_category,
				   COALESCE(bt.transaction_sub_type, '') as transaction_sub_type,
				   bt.reference_id, bt.description,
				   COALESCE(bt.detail_description, '') as detail_description,
				   COALESCE(bt.user_friendly_desc, '') as user_friendly_desc,
				   bt.metadata,
				   COALESCE(bt.business_context, '{}') as business_context,
				   bt.related_transaction_id,
				   bt.operator_id, bt.status, bt.created_at
			FROM balance_transactions bt
			LEFT JOIN order_records ord ON (bt.order_no = ord.customer_order_no OR bt.order_no = ord.order_no)
			WHERE bt.user_id = $1 AND ord.tracking_no = $` + fmt.Sprintf("%d", argIndex)
		args = append(args, trackingNo)
		argIndex++
	}

	query += " ORDER BY bt.created_at DESC"
	query += fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1)
	args = append(args, limit, offset)

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询交易记录失败: %w", err)
	}
	defer rows.Close()

	var transactions []*model.BalanceTransaction
	for rows.Next() {
		tx := &model.BalanceTransaction{}
		var amountStr, balanceBeforeStr, balanceAfterStr string
		var metadataJSON, businessContextJSON []byte
		var orderNo, platformOrderNo, referenceID, description sql.NullString
		var relatedTransactionID sql.NullString // 🔥 修复：使用NullString处理UUID字段

		err := rows.Scan(
			&tx.ID,
			&tx.UserID,
			&tx.Type,
			&amountStr,
			&tx.Currency,
			&balanceBeforeStr,
			&balanceAfterStr,
			&orderNo,
			&platformOrderNo,    // 新增字段
			&tx.CustomerOrderNo, // 新增字段
			&tx.TrackingNo,      // 新增字段
			&tx.Category,        // 新增字段
			&tx.SubType,         // 新增字段
			&referenceID,
			&description,
			&tx.DetailDescription, // 新增字段
			&tx.UserFriendlyDesc,  // 新增字段
			&metadataJSON,
			&businessContextJSON,  // 新增字段
			&relatedTransactionID, // 🔥 修复：扫描到NullString
			&tx.OperatorID,
			&tx.Status,
			&tx.CreatedAt,
		)

		if err != nil {
			return nil, fmt.Errorf("扫描交易记录失败: %w", err)
		}

		// 处理NULL值
		if orderNo.Valid {
			tx.OrderNo = orderNo.String
		}
		if platformOrderNo.Valid {
			tx.PlatformOrderNo = platformOrderNo.String
		}
		if referenceID.Valid {
			tx.ReferenceID = referenceID.String
		}
		if description.Valid {
			tx.Description = description.String
		}
		// 🔥 修复：处理关联交易ID的NULL值
		if relatedTransactionID.Valid {
			tx.RelatedTransactionID = relatedTransactionID.String
		}

		// 转换decimal
		tx.Amount, _ = decimal.NewFromString(amountStr)
		tx.BalanceBefore, _ = decimal.NewFromString(balanceBeforeStr)
		tx.BalanceAfter, _ = decimal.NewFromString(balanceAfterStr)

		// 反序列化metadata
		if len(metadataJSON) > 0 {
			json.Unmarshal(metadataJSON, &tx.Metadata)
		}

		// 反序列化business_context
		if len(businessContextJSON) > 0 {
			json.Unmarshal(businessContextJSON, &tx.BusinessContext)
		}

		transactions = append(transactions, tx)
	}

	return transactions, nil
}

// GetTransactionCountByUserIDWithFilters 获取用户交易记录总数（带筛选）
func (r *PostgresBalanceRepository) GetTransactionCountByUserIDWithFilters(ctx context.Context, userID string, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo string) (int64, error) {
	query := `
		SELECT COUNT(*)
		FROM balance_transactions
		WHERE user_id = $1`

	args := []interface{}{userID}
	argIndex := 2

	// 添加类型筛选
	if typeFilter != "" {
		query += fmt.Sprintf(" AND transaction_type = $%d", argIndex)
		args = append(args, typeFilter)
		argIndex++
	}

	// 添加状态筛选
	if statusFilter != "" {
		query += fmt.Sprintf(" AND status = $%d", argIndex)
		args = append(args, statusFilter)
		argIndex++
	}

	// 添加时间范围筛选
	if startTime != "" {
		query += fmt.Sprintf(" AND created_at >= $%d", argIndex)
		args = append(args, startTime)
		argIndex++
	}

	if endTime != "" {
		query += fmt.Sprintf(" AND created_at <= $%d", argIndex)
		args = append(args, endTime)
		argIndex++
	}

	// 🔥 企业级修复：确保计数查询与主查询逻辑一致
	// 通过订单表关联查询，建立订单号之间的关联关系
	if customerOrderNo != "" || orderNo != "" {
		// 使用关联查询确保计数准确
		query = `
			SELECT COUNT(DISTINCT bt.id)
			FROM balance_transactions bt
			LEFT JOIN order_records ord ON (
				-- 🚀 性能优化：优先匹配platform_order_no，减少OR条件
				bt.platform_order_no = ord.platform_order_no
				OR (bt.platform_order_no IS NULL AND (
					bt.order_no = ord.customer_order_no
					OR bt.order_no = ord.order_no
					OR bt.customer_order_no = ord.customer_order_no  
					OR bt.customer_order_no = ord.order_no
				))
			)
			WHERE bt.user_id = $1`

		// 添加订单号筛选条件
		var orderConditions []string
		if customerOrderNo != "" {
			orderConditions = append(orderConditions, fmt.Sprintf("(ord.customer_order_no = $%d OR ord.order_no = $%d OR bt.order_no = $%d OR bt.customer_order_no = $%d)", argIndex, argIndex, argIndex, argIndex))
			args = append(args, customerOrderNo)
			argIndex++
		}
		if orderNo != "" {
			// 🔥 修复：添加platform_order_no字段的查询，支持平台订单号筛选
			orderConditions = append(orderConditions, fmt.Sprintf("(ord.platform_order_no = $%d OR bt.platform_order_no = $%d OR ord.customer_order_no = $%d OR ord.order_no = $%d OR bt.order_no = $%d OR bt.customer_order_no = $%d)", argIndex, argIndex, argIndex, argIndex, argIndex, argIndex))
			args = append(args, orderNo)
			argIndex++
		}

		if len(orderConditions) > 0 {
			query += " AND (" + strings.Join(orderConditions, " OR ") + ")"
		}

		// 添加其他筛选条件
		if typeFilter != "" {
			query += fmt.Sprintf(" AND bt.transaction_type = $%d", argIndex)
			args = append(args, typeFilter)
			argIndex++
		}
		if statusFilter != "" {
			query += fmt.Sprintf(" AND bt.status = $%d", argIndex)
			args = append(args, statusFilter)
			argIndex++
		}
		if startTime != "" {
			query += fmt.Sprintf(" AND bt.created_at >= $%d", argIndex)
			args = append(args, startTime)
			argIndex++
		}
		if endTime != "" {
			query += fmt.Sprintf(" AND bt.created_at <= $%d", argIndex)
			args = append(args, endTime)
			argIndex++
		}

		// 直接执行查询并返回
		var count int64
		err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
		if err != nil {
			return 0, fmt.Errorf("查询交易记录总数失败: %w", err)
		}
		return count, nil
	}

	// 添加运单号筛选（需要通过订单表关联查询）
	if trackingNo != "" {
		query = `
			SELECT COUNT(*)
			FROM balance_transactions bt
			LEFT JOIN order_records ord ON (bt.order_no = ord.customer_order_no OR bt.order_no = ord.order_no)
			WHERE bt.user_id = $1 AND ord.tracking_no = $` + fmt.Sprintf("%d", argIndex)
		args = append(args, trackingNo)
		argIndex++
	}

	var count int64
	err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("查询交易记录总数失败: %w", err)
	}

	return count, nil
}

// GetTransactionsByOrderNo 根据订单号获取交易记录列表
func (r *PostgresBalanceRepository) GetTransactionsByOrderNo(ctx context.Context, userID, orderNo string, limit, offset int) ([]*model.BalanceTransaction, error) {
	query := `
		SELECT id, user_id, transaction_type, amount, currency, balance_before, balance_after,
		       order_no, COALESCE(customer_order_no, '') as customer_order_no,
		       COALESCE(tracking_no, '') as tracking_no, category, sub_type,
		       COALESCE(reference_id, '') as reference_id,
		       description, detail_description, user_friendly_desc,
		       COALESCE(metadata, '{}') as metadata,
		       COALESCE(business_context, '{}') as business_context,
		       COALESCE(related_transaction_id, '') as related_transaction_id,
		       operator_id, status, created_at, updated_at
		FROM balance_transactions
		WHERE user_id = $1 AND order_no = $2
		ORDER BY created_at DESC
		LIMIT $3 OFFSET $4
	`

	rows, err := r.db.QueryContext(ctx, query, userID, orderNo, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询订单号交易记录失败: %w", err)
	}
	defer rows.Close()

	var transactions []*model.BalanceTransaction
	for rows.Next() {
		tx := &model.BalanceTransaction{}
		var amountStr, balanceBeforeStr, balanceAfterStr string
		var metadataJSON, businessContextJSON []byte

		err := rows.Scan(
			&tx.ID, &tx.UserID, &tx.Type, &amountStr, &tx.Currency,
			&balanceBeforeStr, &balanceAfterStr, &tx.OrderNo, &tx.CustomerOrderNo,
			&tx.TrackingNo, &tx.Category, &tx.SubType, &tx.ReferenceID,
			&tx.Description, &tx.DetailDescription, &tx.UserFriendlyDesc,
			&metadataJSON, &businessContextJSON, &tx.RelatedTransactionID,
			&tx.OperatorID, &tx.Status, &tx.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描交易记录失败: %w", err)
		}

		// 解析decimal字段
		if tx.Amount, err = decimal.NewFromString(amountStr); err != nil {
			return nil, fmt.Errorf("解析金额失败: %w", err)
		}
		if tx.BalanceBefore, err = decimal.NewFromString(balanceBeforeStr); err != nil {
			return nil, fmt.Errorf("解析余额前失败: %w", err)
		}
		if tx.BalanceAfter, err = decimal.NewFromString(balanceAfterStr); err != nil {
			return nil, fmt.Errorf("解析余额后失败: %w", err)
		}

		// 解析JSON字段
		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &tx.Metadata); err != nil {
				return nil, fmt.Errorf("解析metadata失败: %w", err)
			}
		}
		if len(businessContextJSON) > 0 {
			if err := json.Unmarshal(businessContextJSON, &tx.BusinessContext); err != nil {
				return nil, fmt.Errorf("解析business_context失败: %w", err)
			}
		}

		transactions = append(transactions, tx)
	}

	return transactions, nil
}

// GetTransactionsByCustomerOrderNo 根据客户订单号获取交易记录列表
func (r *PostgresBalanceRepository) GetTransactionsByCustomerOrderNo(ctx context.Context, userID, customerOrderNo string, limit, offset int) ([]*model.BalanceTransaction, error) {
	query := `
		SELECT id, user_id, transaction_type, amount, currency, balance_before, balance_after,
		       order_no, COALESCE(customer_order_no, '') as customer_order_no,
		       COALESCE(tracking_no, '') as tracking_no, category, sub_type,
		       COALESCE(reference_id, '') as reference_id,
		       description, detail_description, user_friendly_desc,
		       COALESCE(metadata, '{}') as metadata,
		       COALESCE(business_context, '{}') as business_context,
		       COALESCE(related_transaction_id, '') as related_transaction_id,
		       operator_id, status, created_at, updated_at
		FROM balance_transactions
		WHERE user_id = $1 AND customer_order_no = $2
		ORDER BY created_at DESC
		LIMIT $3 OFFSET $4
	`

	rows, err := r.db.QueryContext(ctx, query, userID, customerOrderNo, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询客户订单号交易记录失败: %w", err)
	}
	defer rows.Close()

	var transactions []*model.BalanceTransaction
	for rows.Next() {
		tx := &model.BalanceTransaction{}
		var amountStr, balanceBeforeStr, balanceAfterStr string
		var metadataJSON, businessContextJSON []byte

		err := rows.Scan(
			&tx.ID, &tx.UserID, &tx.Type, &amountStr, &tx.Currency,
			&balanceBeforeStr, &balanceAfterStr, &tx.OrderNo, &tx.CustomerOrderNo,
			&tx.TrackingNo, &tx.Category, &tx.SubType, &tx.ReferenceID,
			&tx.Description, &tx.DetailDescription, &tx.UserFriendlyDesc,
			&metadataJSON, &businessContextJSON, &tx.RelatedTransactionID,
			&tx.OperatorID, &tx.Status, &tx.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描交易记录失败: %w", err)
		}

		// 解析decimal字段
		if tx.Amount, err = decimal.NewFromString(amountStr); err != nil {
			return nil, fmt.Errorf("解析金额失败: %w", err)
		}
		if tx.BalanceBefore, err = decimal.NewFromString(balanceBeforeStr); err != nil {
			return nil, fmt.Errorf("解析余额前失败: %w", err)
		}
		if tx.BalanceAfter, err = decimal.NewFromString(balanceAfterStr); err != nil {
			return nil, fmt.Errorf("解析余额后失败: %w", err)
		}

		// 解析JSON字段
		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &tx.Metadata); err != nil {
				return nil, fmt.Errorf("解析metadata失败: %w", err)
			}
		}
		if len(businessContextJSON) > 0 {
			if err := json.Unmarshal(businessContextJSON, &tx.BusinessContext); err != nil {
				return nil, fmt.Errorf("解析business_context失败: %w", err)
			}
		}

		transactions = append(transactions, tx)
	}

	return transactions, nil
}

// GetTransactionByID 根据ID获取交易记录
func (r *PostgresBalanceRepository) GetTransactionByID(ctx context.Context, id string) (*model.BalanceTransaction, error) {
	query := `
		SELECT id, user_id, transaction_type, amount, currency, balance_before, balance_after,
			   order_no,
			   COALESCE(customer_order_no, '') as customer_order_no,
			   COALESCE(tracking_no, '') as tracking_no,
			   COALESCE(transaction_category, '') as transaction_category,
			   COALESCE(transaction_sub_type, '') as transaction_sub_type,
			   reference_id, description,
			   COALESCE(detail_description, '') as detail_description,
			   COALESCE(user_friendly_desc, '') as user_friendly_desc,
			   metadata,
			   COALESCE(business_context, '{}') as business_context,
			   related_transaction_id,
			   operator_id, status, created_at
		FROM balance_transactions
		WHERE id = $1
	`

	tx := &model.BalanceTransaction{}
	var amountStr, balanceBeforeStr, balanceAfterStr string
	var metadataJSON, businessContextJSON []byte
	var orderNo, referenceID, description sql.NullString
	var relatedTransactionID sql.NullString

	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&tx.ID,
		&tx.UserID,
		&tx.Type,
		&amountStr,
		&tx.Currency,
		&balanceBeforeStr,
		&balanceAfterStr,
		&orderNo,
		&tx.CustomerOrderNo,
		&tx.TrackingNo,
		&tx.Category,
		&tx.SubType,
		&referenceID,
		&description,
		&tx.DetailDescription,
		&tx.UserFriendlyDesc,
		&metadataJSON,
		&businessContextJSON,
		&relatedTransactionID,
		&tx.OperatorID,
		&tx.Status,
		&tx.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("交易记录不存在")
		}
		return nil, fmt.Errorf("查询交易记录失败: %w", err)
	}

	// 处理NULL值
	if orderNo.Valid {
		tx.OrderNo = orderNo.String
	}
	if referenceID.Valid {
		tx.ReferenceID = referenceID.String
	}
	if description.Valid {
		tx.Description = description.String
	}
	if relatedTransactionID.Valid {
		tx.RelatedTransactionID = relatedTransactionID.String
	}

	// 转换decimal
	tx.Amount, _ = decimal.NewFromString(amountStr)
	tx.BalanceBefore, _ = decimal.NewFromString(balanceBeforeStr)
	tx.BalanceAfter, _ = decimal.NewFromString(balanceAfterStr)

	// 反序列化metadata
	if len(metadataJSON) > 0 {
		json.Unmarshal(metadataJSON, &tx.Metadata)
	}

	// 反序列化business_context
	if len(businessContextJSON) > 0 {
		json.Unmarshal(businessContextJSON, &tx.BusinessContext)
	}

	return tx, nil
}

// GetTransactionByReferenceID 根据参考ID获取交易记录
func (r *PostgresBalanceRepository) GetTransactionByReferenceID(ctx context.Context, referenceID string) (*model.BalanceTransaction, error) {
	query := `
		SELECT id, user_id, transaction_type, amount, currency, balance_before, balance_after,
			   order_no,
			   COALESCE(customer_order_no, '') as customer_order_no,
			   COALESCE(tracking_no, '') as tracking_no,
			   COALESCE(transaction_category, '') as transaction_category,
			   COALESCE(transaction_sub_type, '') as transaction_sub_type,
			   reference_id, description,
			   COALESCE(detail_description, '') as detail_description,
			   COALESCE(user_friendly_desc, '') as user_friendly_desc,
			   metadata,
			   COALESCE(business_context, '{}') as business_context,
			   related_transaction_id,
			   operator_id, status, created_at
		FROM balance_transactions
		WHERE reference_id = $1
	`

	tx := &model.BalanceTransaction{}
	var amountStr, balanceBeforeStr, balanceAfterStr string
	var metadataJSON, businessContextJSON []byte
	var orderNo, refID, description sql.NullString
	var relatedTransactionID sql.NullString

	err := r.db.QueryRowContext(ctx, query, referenceID).Scan(
		&tx.ID,
		&tx.UserID,
		&tx.Type,
		&amountStr,
		&tx.Currency,
		&balanceBeforeStr,
		&balanceAfterStr,
		&orderNo,
		&tx.CustomerOrderNo,
		&tx.TrackingNo,
		&tx.Category,
		&tx.SubType,
		&refID,
		&description,
		&tx.DetailDescription,
		&tx.UserFriendlyDesc,
		&metadataJSON,
		&businessContextJSON,
		&relatedTransactionID,
		&tx.OperatorID,
		&tx.Status,
		&tx.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("交易记录不存在")
		}
		return nil, fmt.Errorf("查询交易记录失败: %w", err)
	}

	// 处理NULL值
	if orderNo.Valid {
		tx.OrderNo = orderNo.String
	}
	if refID.Valid {
		tx.ReferenceID = refID.String
	}
	if description.Valid {
		tx.Description = description.String
	}
	if relatedTransactionID.Valid {
		tx.RelatedTransactionID = relatedTransactionID.String
	}

	// 转换decimal
	tx.Amount, _ = decimal.NewFromString(amountStr)
	tx.BalanceBefore, _ = decimal.NewFromString(balanceBeforeStr)
	tx.BalanceAfter, _ = decimal.NewFromString(balanceAfterStr)

	// 反序列化metadata
	if len(metadataJSON) > 0 {
		json.Unmarshal(metadataJSON, &tx.Metadata)
	}

	// 反序列化business_context
	if len(businessContextJSON) > 0 {
		json.Unmarshal(businessContextJSON, &tx.BusinessContext)
	}

	return tx, nil
}

// GetTransactionByOrderNo 根据订单号获取交易记录
func (r *PostgresBalanceRepository) GetTransactionByOrderNo(ctx context.Context, orderNo string) (*model.BalanceTransaction, error) {
	query := `
		SELECT id, user_id, transaction_type, amount, currency, balance_before, balance_after,
			   order_no,
			   COALESCE(customer_order_no, '') as customer_order_no,
			   COALESCE(tracking_no, '') as tracking_no,
			   COALESCE(transaction_category, '') as transaction_category,
			   COALESCE(transaction_sub_type, '') as transaction_sub_type,
			   reference_id, description,
			   COALESCE(detail_description, '') as detail_description,
			   COALESCE(user_friendly_desc, '') as user_friendly_desc,
			   metadata,
			   COALESCE(business_context, '{}') as business_context,
			   related_transaction_id,
			   operator_id, status, created_at
		FROM balance_transactions
		WHERE order_no = $1
		ORDER BY created_at DESC
		LIMIT 1
	`

	var transaction model.BalanceTransaction
	var amountStr, balanceBeforeStr, balanceAfterStr string
	var metadataJSON, businessContextJSON []byte
	var orderNoField, referenceID, description sql.NullString
	var relatedTransactionID sql.NullString

	err := r.db.QueryRowContext(ctx, query, orderNo).Scan(
		&transaction.ID,
		&transaction.UserID,
		&transaction.Type,
		&amountStr,
		&transaction.Currency,
		&balanceBeforeStr,
		&balanceAfterStr,
		&orderNoField,
		&transaction.CustomerOrderNo,
		&transaction.TrackingNo,
		&transaction.Category,
		&transaction.SubType,
		&referenceID,
		&description,
		&transaction.DetailDescription,
		&transaction.UserFriendlyDesc,
		&metadataJSON,
		&businessContextJSON,
		&relatedTransactionID,
		&transaction.OperatorID,
		&transaction.Status,
		&transaction.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("交易记录不存在")
		}
		return nil, fmt.Errorf("查询交易记录失败: %w", err)
	}

	// 处理NULL值
	if orderNoField.Valid {
		transaction.OrderNo = orderNoField.String
	}
	if referenceID.Valid {
		transaction.ReferenceID = referenceID.String
	}
	if description.Valid {
		transaction.Description = description.String
	}
	if relatedTransactionID.Valid {
		transaction.RelatedTransactionID = relatedTransactionID.String
	}

	// 解析decimal字段
	if transaction.Amount, err = decimal.NewFromString(amountStr); err != nil {
		return nil, fmt.Errorf("解析金额失败: %w", err)
	}
	if transaction.BalanceBefore, err = decimal.NewFromString(balanceBeforeStr); err != nil {
		return nil, fmt.Errorf("解析余额前失败: %w", err)
	}
	if transaction.BalanceAfter, err = decimal.NewFromString(balanceAfterStr); err != nil {
		return nil, fmt.Errorf("解析余额后失败: %w", err)
	}

	// 解析metadata
	if len(metadataJSON) > 0 {
		if err := json.Unmarshal(metadataJSON, &transaction.Metadata); err != nil {
			return nil, fmt.Errorf("解析metadata失败: %w", err)
		}
	}

	// 解析business_context
	if len(businessContextJSON) > 0 {
		if err := json.Unmarshal(businessContextJSON, &transaction.BusinessContext); err != nil {
			return nil, fmt.Errorf("解析business_context失败: %w", err)
		}
	}

	return &transaction, nil
}

// GetPreChargeTransactionByOrderNo 根据订单号获取预扣费交易记录
func (r *PostgresBalanceRepository) GetPreChargeTransactionByOrderNo(ctx context.Context, orderNo string) (*model.BalanceTransaction, error) {
	query := `
		SELECT id, user_id, transaction_type, amount, currency, balance_before, balance_after,
			   order_no,
			   COALESCE(customer_order_no, '') as customer_order_no,
			   COALESCE(tracking_no, '') as tracking_no,
			   COALESCE(transaction_category, '') as transaction_category,
			   COALESCE(transaction_sub_type, '') as transaction_sub_type,
			   reference_id, description,
			   COALESCE(detail_description, '') as detail_description,
			   COALESCE(user_friendly_desc, '') as user_friendly_desc,
			   metadata,
			   COALESCE(business_context, '{}') as business_context,
			   related_transaction_id,
			   operator_id, status, created_at
		FROM balance_transactions
		WHERE order_no = $1 AND transaction_type = $2
		ORDER BY created_at DESC
		LIMIT 1
	`

	var transaction model.BalanceTransaction
	var amountStr, balanceBeforeStr, balanceAfterStr string
	var metadataJSON, businessContextJSON []byte
	var orderNoField, referenceID, description sql.NullString
	var relatedTransactionID sql.NullString

	err := r.db.QueryRowContext(ctx, query, orderNo, model.TransactionTypeOrderPreCharge).Scan(
		&transaction.ID,
		&transaction.UserID,
		&transaction.Type,
		&amountStr,
		&transaction.Currency,
		&balanceBeforeStr,
		&balanceAfterStr,
		&orderNoField,
		&transaction.CustomerOrderNo,
		&transaction.TrackingNo,
		&transaction.Category,
		&transaction.SubType,
		&referenceID,
		&description,
		&transaction.DetailDescription,
		&transaction.UserFriendlyDesc,
		&metadataJSON,
		&businessContextJSON,
		&relatedTransactionID,
		&transaction.OperatorID,
		&transaction.Status,
		&transaction.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("预扣费记录不存在")
		}
		return nil, fmt.Errorf("查询预扣费记录失败: %w", err)
	}

	// 处理NULL值
	if orderNoField.Valid {
		transaction.OrderNo = orderNoField.String
	}
	if referenceID.Valid {
		transaction.ReferenceID = referenceID.String
	}
	if description.Valid {
		transaction.Description = description.String
	}
	if relatedTransactionID.Valid {
		transaction.RelatedTransactionID = relatedTransactionID.String
	}

	// 解析decimal字段
	if transaction.Amount, err = decimal.NewFromString(amountStr); err != nil {
		return nil, fmt.Errorf("解析金额失败: %w", err)
	}
	if transaction.BalanceBefore, err = decimal.NewFromString(balanceBeforeStr); err != nil {
		return nil, fmt.Errorf("解析余额前失败: %w", err)
	}
	if transaction.BalanceAfter, err = decimal.NewFromString(balanceAfterStr); err != nil {
		return nil, fmt.Errorf("解析余额后失败: %w", err)
	}

	// 解析metadata
	if len(metadataJSON) > 0 {
		if err := json.Unmarshal(metadataJSON, &transaction.Metadata); err != nil {
			return nil, fmt.Errorf("解析metadata失败: %w", err)
		}
	}

	// 解析business_context
	if len(businessContextJSON) > 0 {
		if err := json.Unmarshal(businessContextJSON, &transaction.BusinessContext); err != nil {
			return nil, fmt.Errorf("解析business_context失败: %w", err)
		}
	}

	return &transaction, nil
}

// CreateDeposit 创建充值记录
func (r *PostgresBalanceRepository) CreateDeposit(ctx context.Context, deposit *model.Deposit) error {
	if deposit.ID == "" {
		deposit.ID = uuid.New().String()
	}

	now := util.NowBeijing()
	deposit.CreatedAt = now
	deposit.UpdatedAt = now

	query := `
		INSERT INTO deposits (id, user_id, amount, currency, payment_method, status, transaction_id, payment_data, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
	`

	// 序列化payment_data
	paymentDataJSON, err := json.Marshal(deposit.PaymentData)
	if err != nil {
		return fmt.Errorf("序列化payment_data失败: %w", err)
	}

	_, err = r.exec(ctx, query,
		deposit.ID,
		deposit.UserID,
		deposit.Amount.String(),
		deposit.Currency,
		deposit.PaymentMethod,
		deposit.Status,
		deposit.TransactionID,
		paymentDataJSON,
		deposit.CreatedAt,
		deposit.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("创建充值记录失败: %w", err)
	}

	return nil
}

// GetDepositByID 根据ID获取充值记录
func (r *PostgresBalanceRepository) GetDepositByID(ctx context.Context, id string) (*model.Deposit, error) {
	query := `
		SELECT id, user_id, amount, currency, payment_method, status, transaction_id, payment_data, created_at, updated_at
		FROM deposits
		WHERE id = $1
	`

	var deposit model.Deposit
	var amountStr string
	var paymentDataJSON []byte

	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&deposit.ID,
		&deposit.UserID,
		&amountStr,
		&deposit.Currency,
		&deposit.PaymentMethod,
		&deposit.Status,
		&deposit.TransactionID,
		&paymentDataJSON,
		&deposit.CreatedAt,
		&deposit.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("充值记录不存在")
		}
		return nil, fmt.Errorf("查询充值记录失败: %w", err)
	}

	// 转换decimal
	deposit.Amount, _ = decimal.NewFromString(amountStr)

	// 反序列化payment_data
	if len(paymentDataJSON) > 0 {
		json.Unmarshal(paymentDataJSON, &deposit.PaymentData)
	}

	return &deposit, nil
}

// GetDepositsByUserID 根据用户ID获取充值记录
func (r *PostgresBalanceRepository) GetDepositsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.Deposit, error) {
	query := `
		SELECT id, user_id, amount, currency, payment_method, status, transaction_id, payment_data, created_at, updated_at
		FROM deposits
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询充值记录失败: %w", err)
	}
	defer rows.Close()

	var deposits []*model.Deposit
	for rows.Next() {
		deposit := &model.Deposit{}
		var amountStr string
		var paymentDataJSON []byte

		err := rows.Scan(
			&deposit.ID,
			&deposit.UserID,
			&amountStr,
			&deposit.Currency,
			&deposit.PaymentMethod,
			&deposit.Status,
			&deposit.TransactionID,
			&paymentDataJSON,
			&deposit.CreatedAt,
			&deposit.UpdatedAt,
		)

		if err != nil {
			return nil, fmt.Errorf("扫描充值记录失败: %w", err)
		}

		// 转换decimal
		deposit.Amount, _ = decimal.NewFromString(amountStr)

		// 反序列化payment_data
		if len(paymentDataJSON) > 0 {
			json.Unmarshal(paymentDataJSON, &deposit.PaymentData)
		}

		deposits = append(deposits, deposit)
	}

	return deposits, nil
}

// UpdateDeposit 更新充值记录
func (r *PostgresBalanceRepository) UpdateDeposit(ctx context.Context, deposit *model.Deposit) error {
	deposit.UpdatedAt = util.NowBeijing()

	// 序列化payment_data
	paymentDataJSON, err := json.Marshal(deposit.PaymentData)
	if err != nil {
		return fmt.Errorf("序列化payment_data失败: %w", err)
	}

	query := `
		UPDATE deposits
		SET status = $1, transaction_id = $2, payment_data = $3, updated_at = $4
		WHERE id = $5
	`

	result, err := r.exec(ctx, query,
		deposit.Status,
		deposit.TransactionID,
		paymentDataJSON,
		deposit.UpdatedAt,
		deposit.ID,
	)

	if err != nil {
		return fmt.Errorf("更新充值记录失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("充值记录不存在")
	}

	return nil
}

// CreateOrderPayment 创建订单支付记录
func (r *PostgresBalanceRepository) CreateOrderPayment(ctx context.Context, payment *model.OrderPayment) error {
	if payment.ID == "" {
		payment.ID = uuid.New().String()
	}

	now := util.NowBeijing()
	payment.CreatedAt = now
	payment.UpdatedAt = now

	query := `
		INSERT INTO order_payments (id, user_id, order_no, amount, currency, payment_method,
								   status, transaction_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
	`

	_, err := r.exec(ctx, query,
		payment.ID,
		payment.UserID,
		payment.OrderNo,
		payment.Amount.String(),
		payment.Currency,
		payment.PaymentMethod,
		payment.Status,
		payment.TransactionID,
		payment.CreatedAt,
		payment.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("创建订单支付记录失败: %w", err)
	}

	return nil
}

// GetOrderPaymentByOrderNo 根据订单号获取支付记录
func (r *PostgresBalanceRepository) GetOrderPaymentByOrderNo(ctx context.Context, orderNo string) (*model.OrderPayment, error) {
	query := `
		SELECT id, user_id, order_no, amount, currency, payment_method, status, transaction_id, created_at, updated_at
		FROM order_payments
		WHERE order_no = $1
	`

	var payment model.OrderPayment
	var amountStr string

	err := r.db.QueryRowContext(ctx, query, orderNo).Scan(
		&payment.ID,
		&payment.UserID,
		&payment.OrderNo,
		&amountStr,
		&payment.Currency,
		&payment.PaymentMethod,
		&payment.Status,
		&payment.TransactionID,
		&payment.CreatedAt,
		&payment.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("支付记录不存在")
		}
		return nil, fmt.Errorf("查询支付记录失败: %w", err)
	}

	// 转换decimal
	payment.Amount, _ = decimal.NewFromString(amountStr)

	return &payment, nil
}

// GetOrderPaymentsByUserID 根据用户ID获取支付记录
func (r *PostgresBalanceRepository) GetOrderPaymentsByUserID(ctx context.Context, userID string, limit, offset int) ([]*model.OrderPayment, error) {
	query := `
		SELECT id, user_id, order_no, amount, currency, payment_method, status, transaction_id, created_at, updated_at
		FROM order_payments
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询支付记录失败: %w", err)
	}
	defer rows.Close()

	var payments []*model.OrderPayment
	for rows.Next() {
		payment := &model.OrderPayment{}
		var amountStr string

		err := rows.Scan(
			&payment.ID,
			&payment.UserID,
			&payment.OrderNo,
			&amountStr,
			&payment.Currency,
			&payment.PaymentMethod,
			&payment.Status,
			&payment.TransactionID,
			&payment.CreatedAt,
			&payment.UpdatedAt,
		)

		if err != nil {
			return nil, fmt.Errorf("扫描支付记录失败: %w", err)
		}

		// 转换decimal
		payment.Amount, _ = decimal.NewFromString(amountStr)

		payments = append(payments, payment)
	}

	return payments, nil
}

// UpdateTransactionStatus 更新交易状态
func (r *PostgresBalanceRepository) UpdateTransactionStatus(ctx context.Context, transactionID string, status model.TransactionStatus) error {
	query := `
		UPDATE balance_transactions
		SET status = $1
		WHERE id = $2
	`

	result, err := r.exec(ctx, query, status, transactionID)
	if err != nil {
		return fmt.Errorf("更新交易状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("交易记录不存在")
	}

	return nil
}
