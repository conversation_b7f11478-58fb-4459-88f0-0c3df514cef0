package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// 业务配置常量
const (
	// 大额交易阈值（元）
	LARGE_TRANSACTION_THRESHOLD = 1000
	// 高余额用户阈值（元）
	HIGH_BALANCE_THRESHOLD = 1000
)

// AdminBalanceRepository 管理员余额仓储接口
type AdminBalanceRepository interface {
	// 余额查询
	GetBalanceOverview(ctx context.Context) (*model.AdminBalanceOverview, error)
	GetUserBalanceList(ctx context.Context, req *model.UserBalanceListRequest) (*model.UserBalanceListResponse, error)
	GetUserBalanceDetail(ctx context.Context, userID string) (*model.UserBalanceDetail, error)
	GetBalanceAnomalies(ctx context.Context, req *model.AnomalyRequest) (*model.AnomalyResponse, error)

	// 审计日志
	CreateAuditLog(ctx context.Context, log *model.AdminBalanceAuditLog) error
	GetAuditLogs(ctx context.Context, userID string, limit, offset int) ([]*model.AdminBalanceAuditLog, error)

	// 交易管理
	GetAdminTransactionList(ctx context.Context, req *model.AdminTransactionListRequest) (*model.AdminTransactionListResponse, error)

	// 统计分析
	GetBalanceStatistics(ctx context.Context) (*model.BalanceStatistics, error)
}

// PostgresAdminBalanceRepository PostgreSQL管理员余额仓储实现
type PostgresAdminBalanceRepository struct {
	db *sql.DB
}

// NewPostgresAdminBalanceRepository 创建PostgreSQL管理员余额仓储
func NewPostgresAdminBalanceRepository(db *sql.DB) AdminBalanceRepository {
	return &PostgresAdminBalanceRepository{
		db: db,
	}
}

// GetBalanceOverview 获取余额总览
func (r *PostgresAdminBalanceRepository) GetBalanceOverview(ctx context.Context) (*model.AdminBalanceOverview, error) {
	query := `
		SELECT 
			COUNT(*) as total_users,
			COUNT(CASE WHEN ub.balance > 0 THEN 1 END) as active_users,
			COUNT(CASE WHEN ub.balance = 0 THEN 1 END) as inactive_users,
			COALESCE(SUM(ub.balance), 0) as total_balance,
			COALESCE(AVG(ub.balance), 0) as avg_balance,
			(SELECT COUNT(*) FROM balance_transactions WHERE created_at >= NOW() - INTERVAL '24 hours') as recent_transactions
		FROM user_balances ub
		JOIN users u ON ub.user_id = u.id
	`

	var overview model.AdminBalanceOverview
	var totalBalanceStr, avgBalanceStr string

	err := r.db.QueryRowContext(ctx, query).Scan(
		&overview.TotalUsers,
		&overview.ActiveUsers,
		&overview.InactiveUsers,
		&totalBalanceStr,
		&avgBalanceStr,
		&overview.RecentTransactions,
	)

	if err != nil {
		return nil, fmt.Errorf("查询余额总览失败: %w", err)
	}

	// 转换decimal
	overview.TotalBalance, _ = decimal.NewFromString(totalBalanceStr)
	overview.AvgBalance, _ = decimal.NewFromString(avgBalanceStr)
	overview.UpdatedAt = util.NowBeijing()

	return &overview, nil
}

// GetUserBalanceList 获取用户余额列表
func (r *PostgresAdminBalanceRepository) GetUserBalanceList(ctx context.Context, req *model.UserBalanceListRequest) (*model.UserBalanceListResponse, error) {
	// 🔒 安全验证：验证输入参数
	if err := util.ValidatePaginationParamsGlobal(req.Page, req.PageSize); err != nil {
		return nil, fmt.Errorf("分页参数验证失败: %w", err)
	}

	if err := util.ValidateSearchKeywordGlobal(req.Keyword); err != nil {
		return nil, fmt.Errorf("搜索关键词验证失败: %w", err)
	}

	if err := util.ValidateStatusFilter(req.Status); err != nil {
		return nil, fmt.Errorf("状态参数验证失败: %w", err)
	}

	// 构建查询条件
	whereClause := "WHERE 1=1"
	args := []interface{}{}
	argIndex := 1

	if req.Keyword != "" {
		// 🔒 清理和转义输入
		safeKeyword := util.SanitizeInputGlobal(req.Keyword)
		whereClause += fmt.Sprintf(" AND (u.username ILIKE $%d OR u.email ILIKE $%d)", argIndex, argIndex+1)
		keyword := "%" + safeKeyword + "%"
		args = append(args, keyword, keyword)
		argIndex += 2
	}

	if req.Status != "" {
		whereClause += fmt.Sprintf(" AND ub.status = $%d", argIndex)
		args = append(args, req.Status)
		argIndex++
	}

	// 构建排序
	orderBy := "ub.updated_at"
	if req.OrderBy != "" {
		switch req.OrderBy {
		case "balance":
			orderBy = "ub.balance"
		case "username":
			orderBy = "u.username"
		case "created_at":
			orderBy = "ub.created_at"
		}
	}

	order := "DESC"
	if req.Order == "asc" {
		order = "ASC"
	}

	// 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	// 查询总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM user_balances ub
		JOIN users u ON ub.user_id = u.id
		%s
	`, whereClause)

	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("查询用户余额总数失败: %w", err)
	}

	// 🔥 修复N+1查询：使用窗口函数替代子查询
	listQuery := fmt.Sprintf(`
		SELECT 
			ub.user_id,
			u.username,
			u.email,
			ub.balance,
			ub.currency,
			ub.status,
			bt_stats.last_transaction_at,
			COALESCE(bt_stats.transaction_count, 0) as transaction_count,
			ub.created_at,
			ub.updated_at
		FROM user_balances ub
		JOIN users u ON ub.user_id = u.id
		LEFT JOIN (
			SELECT 
				user_id,
				COUNT(*) as transaction_count,
				MAX(created_at) as last_transaction_at
			FROM balance_transactions 
			GROUP BY user_id
		) bt_stats ON ub.user_id = bt_stats.user_id
		%s
		ORDER BY %s %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, order, argIndex, argIndex+1)

	args = append(args, req.PageSize, offset)

	rows, err := r.db.QueryContext(ctx, listQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询用户余额列表失败: %w", err)
	}
	defer rows.Close()

	var items []*model.UserBalanceListItem
	for rows.Next() {
		var item model.UserBalanceListItem
		var balanceStr string
		var lastTransactionAt sql.NullTime

		err := rows.Scan(
			&item.UserID,
			&item.Username,
			&item.Email,
			&balanceStr,
			&item.Currency,
			&item.Status,
			&lastTransactionAt,
			&item.TransactionCount,
			&item.CreatedAt,
			&item.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描用户余额数据失败: %w", err)
		}

		// 转换decimal
		item.Balance, _ = decimal.NewFromString(balanceStr)
		item.AvailableBalance = item.Balance

		if lastTransactionAt.Valid {
			item.LastTransactionAt = &lastTransactionAt.Time
		}

		items = append(items, &item)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历用户余额数据失败: %w", err)
	}

	// 计算总页数
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &model.UserBalanceListResponse{
		Items:      items,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetUserBalanceDetail 获取用户余额详情
func (r *PostgresAdminBalanceRepository) GetUserBalanceDetail(ctx context.Context, userID string) (*model.UserBalanceDetail, error) {
	// 🔥 修复N+1查询：使用JOIN替代子查询
	query := `
		SELECT 
			ub.user_id,
			u.username,
			u.email,
			ub.balance,
			ub.currency,
			ub.status,
			bt_stats.last_transaction_at,
			COALESCE(bt_stats.transaction_count, 0) as transaction_count,
			ub.created_at,
			ub.updated_at
		FROM user_balances ub
		JOIN users u ON ub.user_id = u.id
		LEFT JOIN (
			SELECT 
				user_id,
				COUNT(*) as transaction_count,
				MAX(created_at) as last_transaction_at
			FROM balance_transactions 
			WHERE user_id = $1
			GROUP BY user_id
		) bt_stats ON ub.user_id = bt_stats.user_id
		WHERE ub.user_id = $1
	`

	var detail model.UserBalanceDetail
	var balanceStr string
	var lastTransactionAt sql.NullTime

	err := r.db.QueryRowContext(ctx, query, userID).Scan(
		&detail.UserID,
		&detail.Username,
		&detail.Email,
		&balanceStr,
		&detail.Currency,
		&detail.Status,
		&lastTransactionAt,
		&detail.TransactionCount,
		&detail.CreatedAt,
		&detail.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("用户余额不存在")
		}
		return nil, fmt.Errorf("查询用户余额详情失败: %w", err)
	}

	// 转换decimal
	detail.Balance, _ = decimal.NewFromString(balanceStr)
	detail.AvailableBalance = detail.Balance

	if lastTransactionAt.Valid {
		detail.LastTransactionAt = &lastTransactionAt.Time
	}

	// 获取最近的交易记录
	recentTransactions, err := r.getRecentTransactions(ctx, userID, 10)
	if err != nil {
		return nil, fmt.Errorf("获取最近交易记录失败: %w", err)
	}
	detail.RecentTransactions = recentTransactions

	return &detail, nil
}

// getRecentTransactions 获取最近的交易记录
func (r *PostgresAdminBalanceRepository) getRecentTransactions(ctx context.Context, userID string, limit int) ([]*model.BalanceTransaction, error) {
	query := `
		SELECT id, user_id, transaction_type, amount, currency, balance_before, balance_after,
			   order_no, description, operator_id, status, metadata, created_at
		FROM balance_transactions
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit)
	if err != nil {
		return nil, fmt.Errorf("查询最近交易记录失败: %w", err)
	}
	defer rows.Close()

	var transactions []*model.BalanceTransaction
	for rows.Next() {
		var tx model.BalanceTransaction
		var amountStr, balanceBeforeStr, balanceAfterStr string
		var metadataBytes []byte
		var orderNo, description, operatorID sql.NullString

		err := rows.Scan(
			&tx.ID,
			&tx.UserID,
			&tx.Type,
			&amountStr,
			&tx.Currency,
			&balanceBeforeStr,
			&balanceAfterStr,
			&orderNo,
			&description,
			&operatorID,
			&tx.Status,
			&metadataBytes,
			&tx.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描交易记录失败: %w", err)
		}

		// 转换decimal
		tx.Amount, _ = decimal.NewFromString(amountStr)
		tx.BalanceBefore, _ = decimal.NewFromString(balanceBeforeStr)
		tx.BalanceAfter, _ = decimal.NewFromString(balanceAfterStr)

		// 处理可空字段
		if orderNo.Valid {
			tx.OrderNo = orderNo.String
		}
		if description.Valid {
			tx.Description = description.String
		}
		if operatorID.Valid {
			tx.OperatorID = operatorID.String
		}

		// 解析metadata
		if len(metadataBytes) > 0 {
			json.Unmarshal(metadataBytes, &tx.Metadata)
		}

		transactions = append(transactions, &tx)
	}

	return transactions, nil
}

// GetBalanceAnomalies 获取余额异常
func (r *PostgresAdminBalanceRepository) GetBalanceAnomalies(ctx context.Context, req *model.AnomalyRequest) (*model.AnomalyResponse, error) {
	var allAnomalies []*model.BalanceAnomaly

	// 1. 检测负余额异常
	negativeBalanceQuery := `
		SELECT
			ub.user_id,
			u.username,
			u.email,
			ub.balance,
			0 as frozen_balance,
			ub.updated_at,
			'negative_balance' as anomaly_type,
			'用户余额为负数' as description,
			'high' as severity
		FROM user_balances ub
		JOIN users u ON ub.user_id = u.id
		WHERE ub.balance < 0 AND ub.status = 'active'
	`

	rows1, err := r.db.QueryContext(ctx, negativeBalanceQuery)
	if err != nil {
		return nil, fmt.Errorf("查询负余额异常失败: %w", err)
	}
	defer rows1.Close()

	for rows1.Next() {
		var anomaly model.BalanceAnomaly
		var balanceStr, frozenBalanceStr string

		err := rows1.Scan(
			&anomaly.UserID,
			&anomaly.Username,
			&anomaly.Email,
			&balanceStr,
			&frozenBalanceStr,
			&anomaly.DetectedAt,
			&anomaly.AnomalyType,
			&anomaly.Description,
			&anomaly.Severity,
		)
		if err != nil {
			continue
		}

		anomaly.Balance, _ = decimal.NewFromString(balanceStr)
		// 忽略冻结余额，因为已删除该字段
		allAnomalies = append(allAnomalies, &anomaly)
	}

	// 2. 检测大额交易异常（24小时内单笔交易超过配置阈值）
	largeTransactionQuery := `
		SELECT DISTINCT
			bt.user_id,
			u.username,
			u.email,
			ub.balance,
			0 as frozen_balance,
			bt.created_at,
			'large_transaction' as anomaly_type,
			CONCAT('大额交易: ', bt.amount::text, '元') as description,
			'medium' as severity
		FROM balance_transactions bt
		JOIN users u ON bt.user_id = u.id
		JOIN user_balances ub ON bt.user_id = ub.user_id
		WHERE bt.amount > $1  -- 大额交易阈值
		AND bt.created_at > NOW() - INTERVAL '24 hours'
		AND ub.status = 'active'
	`

	rows2, err := r.db.QueryContext(ctx, largeTransactionQuery, LARGE_TRANSACTION_THRESHOLD)
	if err != nil {
		return nil, fmt.Errorf("查询大额交易异常失败: %w", err)
	}
	defer rows2.Close()

	for rows2.Next() {
		var anomaly model.BalanceAnomaly
		var balanceStr, frozenBalanceStr string

		err := rows2.Scan(
			&anomaly.UserID,
			&anomaly.Username,
			&anomaly.Email,
			&balanceStr,
			&frozenBalanceStr,
			&anomaly.DetectedAt,
			&anomaly.AnomalyType,
			&anomaly.Description,
			&anomaly.Severity,
		)
		if err != nil {
			continue
		}

		anomaly.Balance, _ = decimal.NewFromString(balanceStr)
		allAnomalies = append(allAnomalies, &anomaly)
	}

	// 应用过滤条件
	filteredAnomalies := []*model.BalanceAnomaly{}
	for _, anomaly := range allAnomalies {
		if req.AnomalyType != "" && anomaly.AnomalyType != req.AnomalyType {
			continue
		}
		if req.Severity != "" && anomaly.Severity != req.Severity {
			continue
		}
		if req.UserID != "" && anomaly.UserID != req.UserID {
			continue
		}
		filteredAnomalies = append(filteredAnomalies, anomaly)
	}

	// 分页处理
	total := int64(len(filteredAnomalies))
	offset := (req.Page - 1) * req.PageSize
	end := offset + req.PageSize
	if end > len(filteredAnomalies) {
		end = len(filteredAnomalies)
	}

	var pageItems []*model.BalanceAnomaly
	if offset < len(filteredAnomalies) {
		pageItems = filteredAnomalies[offset:end]
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &model.AnomalyResponse{
		Items:      pageItems,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// CreateAuditLog 创建审计日志
func (r *PostgresAdminBalanceRepository) CreateAuditLog(ctx context.Context, log *model.AdminBalanceAuditLog) error {
	if log.ID == "" {
		log.ID = uuid.New().String()
	}

	now := util.NowBeijing()
	log.CreatedAt = now

	// 序列化metadata
	metadataBytes, err := json.Marshal(log.Metadata)
	if err != nil {
		return fmt.Errorf("序列化metadata失败: %w", err)
	}

	query := `
		INSERT INTO admin_balance_audit_logs (
			id, admin_id, target_user_id, operation_type, amount, reason,
			before_balance, after_balance, metadata, ip_address, user_agent, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
	`

	_, err = r.db.ExecContext(ctx, query,
		log.ID,
		log.AdminID,
		log.TargetUserID,
		log.OperationType,
		log.Amount.String(),
		log.Reason,
		log.BeforeBalance.String(),
		log.AfterBalance.String(),
		metadataBytes,
		log.IPAddress,
		log.UserAgent,
		log.CreatedAt,
	)

	if err != nil {
		return fmt.Errorf("创建审计日志失败: %w", err)
	}

	return nil
}

// GetAuditLogs 获取审计日志
func (r *PostgresAdminBalanceRepository) GetAuditLogs(ctx context.Context, userID string, limit, offset int) ([]*model.AdminBalanceAuditLog, error) {
	query := `
		SELECT id, admin_id, target_user_id, operation_type, amount, reason,
			   before_balance, after_balance, metadata, ip_address, user_agent, created_at
		FROM admin_balance_audit_logs
		WHERE target_user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询审计日志失败: %w", err)
	}
	defer rows.Close()

	var logs []*model.AdminBalanceAuditLog
	for rows.Next() {
		var log model.AdminBalanceAuditLog
		var amountStr, beforeBalanceStr, afterBalanceStr string
		var metadataBytes []byte

		err := rows.Scan(
			&log.ID,
			&log.AdminID,
			&log.TargetUserID,
			&log.OperationType,
			&amountStr,
			&log.Reason,
			&beforeBalanceStr,
			&afterBalanceStr,
			&metadataBytes,
			&log.IPAddress,
			&log.UserAgent,
			&log.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描审计日志失败: %w", err)
		}

		// 转换decimal
		log.Amount, _ = decimal.NewFromString(amountStr)
		log.BeforeBalance, _ = decimal.NewFromString(beforeBalanceStr)
		log.AfterBalance, _ = decimal.NewFromString(afterBalanceStr)

		// 解析metadata
		if len(metadataBytes) > 0 {
			json.Unmarshal(metadataBytes, &log.Metadata)
		}

		logs = append(logs, &log)
	}

	return logs, nil
}

// GetAdminTransactionList 获取管理员交易列表
func (r *PostgresAdminBalanceRepository) GetAdminTransactionList(ctx context.Context, req *model.AdminTransactionListRequest) (*model.AdminTransactionListResponse, error) {
	// 构建查询条件
	whereClause := "WHERE 1=1"
	args := []interface{}{}
	argIndex := 1

	if req.UserID != "" {
		whereClause += fmt.Sprintf(" AND bt.user_id = $%d", argIndex)
		args = append(args, req.UserID)
		argIndex++
	}

	if req.Type != "" {
		whereClause += fmt.Sprintf(" AND bt.type = $%d", argIndex)
		args = append(args, req.Type)
		argIndex++
	}

	if req.Status != "" {
		whereClause += fmt.Sprintf(" AND bt.status = $%d", argIndex)
		args = append(args, req.Status)
		argIndex++
	}

	if req.StartTime != "" {
		whereClause += fmt.Sprintf(" AND bt.created_at >= $%d", argIndex)
		args = append(args, req.StartTime)
		argIndex++
	}

	if req.EndTime != "" {
		whereClause += fmt.Sprintf(" AND bt.created_at <= $%d", argIndex)
		args = append(args, req.EndTime)
		argIndex++
	}

	// 构建排序
	orderBy := "bt.created_at"
	if req.OrderBy != "" {
		switch req.OrderBy {
		case "amount":
			orderBy = "bt.amount"
		case "type":
			orderBy = "bt.type"
		case "status":
			orderBy = "bt.status"
		}
	}

	order := "DESC"
	if req.Order == "asc" {
		order = "ASC"
	}

	// 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	// 查询总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM balance_transactions bt
		%s
	`, whereClause)

	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("查询交易总数失败: %w", err)
	}

	// 查询列表数据
	listQuery := fmt.Sprintf(`
		SELECT
			bt.id, bt.user_id, bt.transaction_type, bt.amount, bt.currency,
			bt.balance_before, bt.balance_after, bt.order_no, bt.description,
			bt.operator_id, bt.status, bt.metadata, bt.created_at
		FROM balance_transactions bt
		%s
		ORDER BY %s %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, order, argIndex, argIndex+1)

	args = append(args, req.PageSize, offset)

	rows, err := r.db.QueryContext(ctx, listQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询交易列表失败: %w", err)
	}
	defer rows.Close()

	var items []*model.BalanceTransaction
	for rows.Next() {
		var tx model.BalanceTransaction
		var amountStr, balanceBeforeStr, balanceAfterStr string
		var metadataBytes []byte
		var orderNo, description, operatorID sql.NullString

		err := rows.Scan(
			&tx.ID,
			&tx.UserID,
			&tx.Type,
			&amountStr,
			&tx.Currency,
			&balanceBeforeStr,
			&balanceAfterStr,
			&orderNo,
			&description,
			&operatorID,
			&tx.Status,
			&metadataBytes,
			&tx.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描交易数据失败: %w", err)
		}

		// 转换decimal
		tx.Amount, _ = decimal.NewFromString(amountStr)
		tx.BalanceBefore, _ = decimal.NewFromString(balanceBeforeStr)
		tx.BalanceAfter, _ = decimal.NewFromString(balanceAfterStr)

		// 处理可空字段
		if orderNo.Valid {
			tx.OrderNo = orderNo.String
		}
		if description.Valid {
			tx.Description = description.String
		}
		if operatorID.Valid {
			tx.OperatorID = operatorID.String
		}

		// 解析metadata
		if len(metadataBytes) > 0 {
			json.Unmarshal(metadataBytes, &tx.Metadata)
		}

		items = append(items, &tx)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历交易数据失败: %w", err)
	}

	// 计算总页数
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &model.AdminTransactionListResponse{
		Items:      items,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetBalanceStatistics 获取余额统计
func (r *PostgresAdminBalanceRepository) GetBalanceStatistics(ctx context.Context) (*model.BalanceStatistics, error) {
	query := `
		SELECT
			COALESCE(SUM(balance), 0) as total_balance,
			COALESCE(AVG(balance), 0) as avg_balance,
			COALESCE(MAX(balance), 0) as max_balance,
			COALESCE(MIN(balance), 0) as min_balance,
			COUNT(*) as total_users,
			COUNT(CASE WHEN balance > 0 THEN 1 END) as active_users,
			COUNT(CASE WHEN balance = 0 THEN 1 END) as zero_balance_users,
			COUNT(CASE WHEN balance > $1 THEN 1 END) as high_balance_users  -- 高余额用户阈值
		FROM user_balances
	`

	var stats model.BalanceStatistics
	var totalBalanceStr, avgBalanceStr, maxBalanceStr, minBalanceStr string

	err := r.db.QueryRowContext(ctx, query, HIGH_BALANCE_THRESHOLD).Scan(
		&totalBalanceStr,
		&avgBalanceStr,
		&maxBalanceStr,
		&minBalanceStr,
		&stats.TotalUsers,
		&stats.ActiveUsers,
		&stats.ZeroBalanceUsers,
		&stats.HighBalanceUsers,
	)

	if err != nil {
		return nil, fmt.Errorf("查询余额统计失败: %w", err)
	}

	// 转换decimal
	stats.TotalBalance, _ = decimal.NewFromString(totalBalanceStr)
	stats.AvgBalance, _ = decimal.NewFromString(avgBalanceStr)
	stats.MaxBalance, _ = decimal.NewFromString(maxBalanceStr)
	stats.MinBalance, _ = decimal.NewFromString(minBalanceStr)
	// TODO: 实现中位数计算
	stats.MedianBalance = decimal.Zero
	stats.UpdatedAt = util.NowBeijing()

	return &stats, nil
}
