package repository

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/google/uuid"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"

)

// SystemConfigRepository 系统配置仓储接口
type SystemConfigRepository interface {
	// 配置管理
	CreateConfig(ctx context.Context, config *model.SystemConfig) error
	GetConfigByID(ctx context.Context, id string) (*model.SystemConfig, error)
	GetConfigByKey(ctx context.Context, group, key string) (*model.SystemConfig, error)
	UpdateConfig(ctx context.Context, config *model.SystemConfig) error
	DeleteConfig(ctx context.Context, id string) error
	ListConfigs(ctx context.Context, req *model.SystemConfigListRequest) (*model.SystemConfigListResponse, error)
	GetConfigsByGroup(ctx context.Context, group string) ([]*model.SystemConfig, error)
	BatchUpdateConfigs(ctx context.Context, configs []*model.SystemConfig, operatorID string) error

	// 配置变更日志
	CreateChangeLog(ctx context.Context, log *model.ConfigChangeLog) error
	ListChangeLogs(ctx context.Context, req *model.ConfigChangeLogListRequest) (*model.ConfigChangeLogListResponse, error)

	// 配置模板
	CreateTemplate(ctx context.Context, template *model.ConfigTemplate) error
	GetTemplateByName(ctx context.Context, name string) (*model.ConfigTemplate, error)
	ListTemplates(ctx context.Context) ([]*model.ConfigTemplate, error)
	UpdateTemplate(ctx context.Context, template *model.ConfigTemplate) error
	DeleteTemplate(ctx context.Context, id string) error

	// 配置备份
	CreateBackup(ctx context.Context, backup *model.ConfigBackup) error
	GetBackupByID(ctx context.Context, id string) (*model.ConfigBackup, error)
	ListBackups(ctx context.Context, page, pageSize int) ([]*model.ConfigBackup, int64, error)
	DeleteBackup(ctx context.Context, id string) error

	// 工具方法
	GetConfigGroups(ctx context.Context) ([]string, error)
	ValidateConfigKey(ctx context.Context, group, key, excludeID string) error
}

// PostgresSystemConfigRepository PostgreSQL系统配置仓储实现
type PostgresSystemConfigRepository struct {
	db *sql.DB
}

// NewPostgresSystemConfigRepository 创建PostgreSQL系统配置仓储
func NewPostgresSystemConfigRepository(db *sql.DB) SystemConfigRepository {
	return &PostgresSystemConfigRepository{
		db: db,
	}
}

// CreateConfig 创建配置
func (r *PostgresSystemConfigRepository) CreateConfig(ctx context.Context, config *model.SystemConfig) error {
	if config.ID == "" {
		config.ID = uuid.New().String()
	}

	now := util.NowBeijing()
	config.CreatedAt = now
	config.UpdatedAt = now

	query := `
		INSERT INTO system_configs (
			id, config_group, config_key, config_value, config_type, description,
			is_encrypted, is_readonly, is_system, validation_rule, default_value,
			display_order, created_at, updated_at, created_by, updated_by
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
	`

	_, err := r.db.ExecContext(ctx, query,
		config.ID, config.ConfigGroup, config.ConfigKey, config.ConfigValue,
		config.ConfigType, config.Description, config.IsEncrypted, config.IsReadonly,
		config.IsSystem, config.ValidationRule, config.DefaultValue, config.DisplayOrder,
		config.CreatedAt, config.UpdatedAt, config.CreatedBy, config.UpdatedBy,
	)

	if err != nil {
		return fmt.Errorf("创建配置失败: %w", err)
	}

	return nil
}

// GetConfigByID 根据ID获取配置
func (r *PostgresSystemConfigRepository) GetConfigByID(ctx context.Context, id string) (*model.SystemConfig, error) {
	query := `
		SELECT id, config_group, config_key, config_value, config_type, description,
			   is_encrypted, is_readonly, is_system, validation_rule, default_value,
			   display_order, created_at, updated_at, created_by, updated_by
		FROM system_configs
		WHERE id = $1
	`

	var config model.SystemConfig
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&config.ID, &config.ConfigGroup, &config.ConfigKey, &config.ConfigValue,
		&config.ConfigType, &config.Description, &config.IsEncrypted, &config.IsReadonly,
		&config.IsSystem, &config.ValidationRule, &config.DefaultValue, &config.DisplayOrder,
		&config.CreatedAt, &config.UpdatedAt, &config.CreatedBy, &config.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("配置不存在")
		}
		return nil, fmt.Errorf("查询配置失败: %w", err)
	}

	return &config, nil
}

// GetConfigByKey 根据组和键获取配置
func (r *PostgresSystemConfigRepository) GetConfigByKey(ctx context.Context, group, key string) (*model.SystemConfig, error) {
	query := `
		SELECT id, config_group, config_key, config_value, config_type, description,
			   is_encrypted, is_readonly, is_system, validation_rule, default_value,
			   display_order, created_at, updated_at, created_by, updated_by
		FROM system_configs
		WHERE config_group = $1 AND config_key = $2
	`

	var config model.SystemConfig
	err := r.db.QueryRowContext(ctx, query, group, key).Scan(
		&config.ID, &config.ConfigGroup, &config.ConfigKey, &config.ConfigValue,
		&config.ConfigType, &config.Description, &config.IsEncrypted, &config.IsReadonly,
		&config.IsSystem, &config.ValidationRule, &config.DefaultValue, &config.DisplayOrder,
		&config.CreatedAt, &config.UpdatedAt, &config.CreatedBy, &config.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("配置不存在")
		}
		return nil, fmt.Errorf("查询配置失败: %w", err)
	}

	return &config, nil
}

// UpdateConfig 更新配置
func (r *PostgresSystemConfigRepository) UpdateConfig(ctx context.Context, config *model.SystemConfig) error {
	config.UpdatedAt = util.NowBeijing()

	query := `
		UPDATE system_configs SET
			config_value = $1, config_type = $2, description = $3,
			is_encrypted = $4, is_readonly = $5, validation_rule = $6,
			default_value = $7, display_order = $8, updated_at = $9, updated_by = $10
		WHERE id = $11
	`

	result, err := r.db.ExecContext(ctx, query,
		config.ConfigValue, config.ConfigType, config.Description,
		config.IsEncrypted, config.IsReadonly, config.ValidationRule,
		config.DefaultValue, config.DisplayOrder, config.UpdatedAt, config.UpdatedBy,
		config.ID,
	)

	if err != nil {
		return fmt.Errorf("更新配置失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("配置不存在")
	}

	return nil
}

// DeleteConfig 删除配置
func (r *PostgresSystemConfigRepository) DeleteConfig(ctx context.Context, id string) error {
	query := `DELETE FROM system_configs WHERE id = $1 AND is_system = false`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("删除配置失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("配置不存在或为系统配置不可删除")
	}

	return nil
}

// ListConfigs 获取配置列表
func (r *PostgresSystemConfigRepository) ListConfigs(ctx context.Context, req *model.SystemConfigListRequest) (*model.SystemConfigListResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 构建查询条件
	whereClause := "WHERE 1=1"
	args := []interface{}{}
	argIndex := 1

	if req.ConfigGroup != "" {
		whereClause += fmt.Sprintf(" AND config_group = $%d", argIndex)
		args = append(args, req.ConfigGroup)
		argIndex++
	}

	if req.ConfigKey != "" {
		whereClause += fmt.Sprintf(" AND config_key ILIKE $%d", argIndex)
		args = append(args, "%"+req.ConfigKey+"%")
		argIndex++
	}

	if req.ConfigType != "" {
		whereClause += fmt.Sprintf(" AND config_type = $%d", argIndex)
		args = append(args, req.ConfigType)
		argIndex++
	}

	if req.IsSystem != nil {
		whereClause += fmt.Sprintf(" AND is_system = $%d", argIndex)
		args = append(args, *req.IsSystem)
		argIndex++
	}

	if req.IsReadonly != nil {
		whereClause += fmt.Sprintf(" AND is_readonly = $%d", argIndex)
		args = append(args, *req.IsReadonly)
		argIndex++
	}

	if req.IsEncrypted != nil {
		whereClause += fmt.Sprintf(" AND is_encrypted = $%d", argIndex)
		args = append(args, *req.IsEncrypted)
		argIndex++
	}

	if req.SearchKeyword != "" {
		whereClause += fmt.Sprintf(" AND (config_key ILIKE $%d OR description ILIKE $%d)", argIndex, argIndex+1)
		keyword := "%" + req.SearchKeyword + "%"
		args = append(args, keyword, keyword)
		argIndex += 2
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM system_configs %s", whereClause)
	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("查询配置总数失败: %w", err)
	}

	// 构建排序
	orderBy := "config_group, display_order, config_key"
	if req.OrderBy != "" {
		switch req.OrderBy {
		case "created_at":
			orderBy = "created_at DESC"
		case "updated_at":
			orderBy = "updated_at DESC"
		case "config_key":
			orderBy = "config_key"
		case "config_group":
			orderBy = "config_group, config_key"
		}
	}

	// 查询数据
	offset := (req.Page - 1) * req.PageSize
	dataQuery := fmt.Sprintf(`
		SELECT id, config_group, config_key, config_value, config_type, description,
			   is_encrypted, is_readonly, is_system, validation_rule, default_value,
			   display_order, created_at, updated_at, created_by, updated_by
		FROM system_configs
		%s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, req.PageSize, offset)

	rows, err := r.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询配置数据失败: %w", err)
	}
	defer rows.Close()

	var configs []*model.SystemConfig
	for rows.Next() {
		var config model.SystemConfig
		err := rows.Scan(
			&config.ID, &config.ConfigGroup, &config.ConfigKey, &config.ConfigValue,
			&config.ConfigType, &config.Description, &config.IsEncrypted, &config.IsReadonly,
			&config.IsSystem, &config.ValidationRule, &config.DefaultValue, &config.DisplayOrder,
			&config.CreatedAt, &config.UpdatedAt, &config.CreatedBy, &config.UpdatedBy,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描配置数据失败: %w", err)
		}
		configs = append(configs, &config)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历配置数据失败: %w", err)
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &model.SystemConfigListResponse{
		Items:      configs,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetConfigsByGroup 根据组获取配置列表
func (r *PostgresSystemConfigRepository) GetConfigsByGroup(ctx context.Context, group string) ([]*model.SystemConfig, error) {
	query := `
		SELECT id, config_group, config_key, config_value, config_type, description,
			   is_encrypted, is_readonly, is_system, validation_rule, default_value,
			   display_order, created_at, updated_at, created_by, updated_by
		FROM system_configs
		WHERE config_group = $1
		ORDER BY display_order, config_key
	`

	rows, err := r.db.QueryContext(ctx, query, group)
	if err != nil {
		return nil, fmt.Errorf("查询配置组失败: %w", err)
	}
	defer rows.Close()

	var configs []*model.SystemConfig
	for rows.Next() {
		var config model.SystemConfig
		err := rows.Scan(
			&config.ID, &config.ConfigGroup, &config.ConfigKey, &config.ConfigValue,
			&config.ConfigType, &config.Description, &config.IsEncrypted, &config.IsReadonly,
			&config.IsSystem, &config.ValidationRule, &config.DefaultValue, &config.DisplayOrder,
			&config.CreatedAt, &config.UpdatedAt, &config.CreatedBy, &config.UpdatedBy,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描配置数据失败: %w", err)
		}
		configs = append(configs, &config)
	}

	return configs, nil
}

// BatchUpdateConfigs 批量更新配置
func (r *PostgresSystemConfigRepository) BatchUpdateConfigs(ctx context.Context, configs []*model.SystemConfig, operatorID string) error {
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	updateQuery := `
		UPDATE system_configs SET
			config_value = $1, updated_at = $2, updated_by = $3
		WHERE id = $4
	`

	now := util.NowBeijing()
	for _, config := range configs {
		_, err := tx.ExecContext(ctx, updateQuery,
			config.ConfigValue, now, operatorID, config.ID)
		if err != nil {
			return fmt.Errorf("批量更新配置失败: %w", err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// CreateChangeLog 创建配置变更日志
func (r *PostgresSystemConfigRepository) CreateChangeLog(ctx context.Context, log *model.ConfigChangeLog) error {
	if log.ID == "" {
		log.ID = uuid.New().String()
	}

	log.CreatedAt = util.NowBeijing()

	query := `
		INSERT INTO config_change_logs (
			id, config_id, config_group, config_key, old_value, new_value,
			change_type, changed_by, change_reason, ip_address, user_agent,
			request_id, session_id, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
	`

	_, err := r.db.ExecContext(ctx, query,
		log.ID, log.ConfigID, log.ConfigGroup, log.ConfigKey, log.OldValue, log.NewValue,
		log.ChangeType, log.ChangedBy, log.ChangeReason, log.IPAddress, log.UserAgent,
		log.RequestID, log.SessionID, log.CreatedAt,
	)

	if err != nil {
		return fmt.Errorf("创建配置变更日志失败: %w", err)
	}

	return nil
}

// GetConfigGroups 获取所有配置组
func (r *PostgresSystemConfigRepository) GetConfigGroups(ctx context.Context) ([]string, error) {
	query := `SELECT DISTINCT config_group FROM system_configs ORDER BY config_group`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询配置组失败: %w", err)
	}
	defer rows.Close()

	var groups []string
	for rows.Next() {
		var group string
		if err := rows.Scan(&group); err != nil {
			return nil, fmt.Errorf("扫描配置组失败: %w", err)
		}
		groups = append(groups, group)
	}

	return groups, nil
}

// ValidateConfigKey 验证配置键是否唯一
func (r *PostgresSystemConfigRepository) ValidateConfigKey(ctx context.Context, group, key, excludeID string) error {
	query := `SELECT COUNT(*) FROM system_configs WHERE config_group = $1 AND config_key = $2`
	args := []interface{}{group, key}

	if excludeID != "" {
		query += " AND id != $3"
		args = append(args, excludeID)
	}

	var count int
	err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
	if err != nil {
		return fmt.Errorf("验证配置键失败: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("配置键 %s.%s 已存在", group, key)
	}

	return nil
}

// ListChangeLogs 获取配置变更日志列表
func (r *PostgresSystemConfigRepository) ListChangeLogs(ctx context.Context, req *model.ConfigChangeLogListRequest) (*model.ConfigChangeLogListResponse, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 构建查询条件
	whereClause := "WHERE 1=1"
	args := []interface{}{}
	argIndex := 1

	if req.ConfigGroup != "" {
		whereClause += fmt.Sprintf(" AND config_group = $%d", argIndex)
		args = append(args, req.ConfigGroup)
		argIndex++
	}

	if req.ConfigKey != "" {
		whereClause += fmt.Sprintf(" AND config_key = $%d", argIndex)
		args = append(args, req.ConfigKey)
		argIndex++
	}

	if req.ChangeType != "" {
		whereClause += fmt.Sprintf(" AND change_type = $%d", argIndex)
		args = append(args, req.ChangeType)
		argIndex++
	}

	if req.ChangedBy != "" {
		whereClause += fmt.Sprintf(" AND changed_by = $%d", argIndex)
		args = append(args, req.ChangedBy)
		argIndex++
	}

	if req.StartTime != "" {
		whereClause += fmt.Sprintf(" AND created_at >= $%d", argIndex)
		args = append(args, req.StartTime)
		argIndex++
	}

	if req.EndTime != "" {
		whereClause += fmt.Sprintf(" AND created_at <= $%d", argIndex)
		args = append(args, req.EndTime)
		argIndex++
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM config_change_logs %s", whereClause)
	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("查询变更日志总数失败: %w", err)
	}

	// 构建排序
	orderBy := "created_at DESC"
	if req.OrderBy != "" {
		switch req.OrderBy {
		case "created_at":
			orderBy = "created_at DESC"
		case "config_group":
			orderBy = "config_group, config_key, created_at DESC"
		case "change_type":
			orderBy = "change_type, created_at DESC"
		}
	}

	// 查询数据
	offset := (req.Page - 1) * req.PageSize
	dataQuery := fmt.Sprintf(`
		SELECT id, config_id, config_group, config_key, old_value, new_value,
			   change_type, changed_by, change_reason, ip_address, user_agent,
			   request_id, session_id, created_at
		FROM config_change_logs
		%s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, req.PageSize, offset)

	rows, err := r.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询变更日志数据失败: %w", err)
	}
	defer rows.Close()

	var logs []*model.ConfigChangeLog
	for rows.Next() {
		var log model.ConfigChangeLog
		err := rows.Scan(
			&log.ID, &log.ConfigID, &log.ConfigGroup, &log.ConfigKey, &log.OldValue, &log.NewValue,
			&log.ChangeType, &log.ChangedBy, &log.ChangeReason, &log.IPAddress, &log.UserAgent,
			&log.RequestID, &log.SessionID, &log.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描变更日志数据失败: %w", err)
		}
		logs = append(logs, &log)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历变更日志数据失败: %w", err)
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &model.ConfigChangeLogListResponse{
		Items:      logs,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// CreateTemplate 创建配置模板
func (r *PostgresSystemConfigRepository) CreateTemplate(ctx context.Context, template *model.ConfigTemplate) error {
	if template.ID == "" {
		template.ID = uuid.New().String()
	}

	now := util.NowBeijing()
	template.CreatedAt = now
	template.UpdatedAt = now

	query := `
		INSERT INTO config_templates (
			id, template_name, template_description, template_data,
			is_system, created_at, updated_at, created_by, updated_by
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	_, err := r.db.ExecContext(ctx, query,
		template.ID, template.TemplateName, template.TemplateDescription, template.TemplateData,
		template.IsSystem, template.CreatedAt, template.UpdatedAt, template.CreatedBy, template.UpdatedBy,
	)

	if err != nil {
		return fmt.Errorf("创建配置模板失败: %w", err)
	}

	return nil
}

// GetTemplateByName 根据名称获取配置模板
func (r *PostgresSystemConfigRepository) GetTemplateByName(ctx context.Context, name string) (*model.ConfigTemplate, error) {
	query := `
		SELECT id, template_name, template_description, template_data,
			   is_system, created_at, updated_at, created_by, updated_by
		FROM config_templates
		WHERE template_name = $1
	`

	var template model.ConfigTemplate
	err := r.db.QueryRowContext(ctx, query, name).Scan(
		&template.ID, &template.TemplateName, &template.TemplateDescription, &template.TemplateData,
		&template.IsSystem, &template.CreatedAt, &template.UpdatedAt, &template.CreatedBy, &template.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("配置模板不存在")
		}
		return nil, fmt.Errorf("查询配置模板失败: %w", err)
	}

	return &template, nil
}

// ListTemplates 获取配置模板列表
func (r *PostgresSystemConfigRepository) ListTemplates(ctx context.Context) ([]*model.ConfigTemplate, error) {
	query := `
		SELECT id, template_name, template_description, template_data,
			   is_system, created_at, updated_at, created_by, updated_by
		FROM config_templates
		ORDER BY is_system DESC, template_name
	`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询配置模板失败: %w", err)
	}
	defer rows.Close()

	var templates []*model.ConfigTemplate
	for rows.Next() {
		var template model.ConfigTemplate
		err := rows.Scan(
			&template.ID, &template.TemplateName, &template.TemplateDescription, &template.TemplateData,
			&template.IsSystem, &template.CreatedAt, &template.UpdatedAt, &template.CreatedBy, &template.UpdatedBy,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描配置模板数据失败: %w", err)
		}
		templates = append(templates, &template)
	}

	return templates, nil
}

// UpdateTemplate 更新配置模板
func (r *PostgresSystemConfigRepository) UpdateTemplate(ctx context.Context, template *model.ConfigTemplate) error {
	template.UpdatedAt = util.NowBeijing()

	query := `
		UPDATE config_templates SET
			template_description = $1, template_data = $2, updated_at = $3, updated_by = $4
		WHERE id = $5 AND is_system = false
	`

	result, err := r.db.ExecContext(ctx, query,
		template.TemplateDescription, template.TemplateData, template.UpdatedAt, template.UpdatedBy, template.ID,
	)

	if err != nil {
		return fmt.Errorf("更新配置模板失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("配置模板不存在或为系统模板不可修改")
	}

	return nil
}

// DeleteTemplate 删除配置模板
func (r *PostgresSystemConfigRepository) DeleteTemplate(ctx context.Context, id string) error {
	query := `DELETE FROM config_templates WHERE id = $1 AND is_system = false`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("删除配置模板失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("配置模板不存在或为系统模板不可删除")
	}

	return nil
}

// CreateBackup 创建配置备份
func (r *PostgresSystemConfigRepository) CreateBackup(ctx context.Context, backup *model.ConfigBackup) error {
	if backup.ID == "" {
		backup.ID = uuid.New().String()
	}

	backup.CreatedAt = util.NowBeijing()

	query := `
		INSERT INTO config_backups (
			id, backup_name, backup_description, backup_data,
			backup_type, created_by, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7)
	`

	_, err := r.db.ExecContext(ctx, query,
		backup.ID, backup.BackupName, backup.BackupDescription, backup.BackupData,
		backup.BackupType, backup.CreatedBy, backup.CreatedAt,
	)

	if err != nil {
		return fmt.Errorf("创建配置备份失败: %w", err)
	}

	return nil
}

// GetBackupByID 根据ID获取配置备份
func (r *PostgresSystemConfigRepository) GetBackupByID(ctx context.Context, id string) (*model.ConfigBackup, error) {
	query := `
		SELECT id, backup_name, backup_description, backup_data,
			   backup_type, created_by, created_at
		FROM config_backups
		WHERE id = $1
	`

	var backup model.ConfigBackup
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&backup.ID, &backup.BackupName, &backup.BackupDescription, &backup.BackupData,
		&backup.BackupType, &backup.CreatedBy, &backup.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("配置备份不存在")
		}
		return nil, fmt.Errorf("查询配置备份失败: %w", err)
	}

	return &backup, nil
}

// ListBackups 获取配置备份列表
func (r *PostgresSystemConfigRepository) ListBackups(ctx context.Context, page, pageSize int) ([]*model.ConfigBackup, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	// 查询总数
	countQuery := `SELECT COUNT(*) FROM config_backups`
	var total int64
	err := r.db.QueryRowContext(ctx, countQuery).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("查询配置备份总数失败: %w", err)
	}

	// 查询数据
	offset := (page - 1) * pageSize
	dataQuery := `
		SELECT id, backup_name, backup_description, backup_data,
			   backup_type, created_by, created_at
		FROM config_backups
		ORDER BY created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := r.db.QueryContext(ctx, dataQuery, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("查询配置备份数据失败: %w", err)
	}
	defer rows.Close()

	var backups []*model.ConfigBackup
	for rows.Next() {
		var backup model.ConfigBackup
		err := rows.Scan(
			&backup.ID, &backup.BackupName, &backup.BackupDescription, &backup.BackupData,
			&backup.BackupType, &backup.CreatedBy, &backup.CreatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描配置备份数据失败: %w", err)
		}
		backups = append(backups, &backup)
	}

	return backups, total, nil
}

// DeleteBackup 删除配置备份
func (r *PostgresSystemConfigRepository) DeleteBackup(ctx context.Context, id string) error {
	query := `DELETE FROM config_backups WHERE id = $1`

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("删除配置备份失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("配置备份不存在")
	}

	return nil
}
