package repository

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"

)

// BillingRepository 计费仓库接口
type BillingRepository interface {
	// 计费详情管理
	SaveBillingDetail(ctx context.Context, detail *model.OrderBillingDetail) error
	GetBillingDetailsByOrderNo(ctx context.Context, orderNo string) ([]*model.OrderBillingDetail, error)
	GetLatestBillingDetail(ctx context.Context, orderNo string, billingType string) (*model.OrderBillingDetail, error)
	UpdateBillingDetail(ctx context.Context, detail *model.OrderBillingDetail) error

	// 计费历史管理
	SaveBillingHistory(ctx context.Context, history *model.OrderBillingHistory) error
	GetBillingHistoryByOrderNo(ctx context.Context, orderNo string) ([]*model.OrderBillingHistory, error)

	// 批量操作
	BatchSaveBillingDetails(ctx context.Context, details []*model.OrderBillingDetail) error

	// 统计查询
	GetBillingStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (*BillingStatistics, error)
}

// BillingStatistics 计费统计信息
type BillingStatistics struct {
	TotalOrders         int64   `json:"total_orders"`
	TotalPrice          float64 `json:"total_price"` // 使用price替代estimated_fee
	TotalActualFee      float64 `json:"total_actual_fee"`
	TotalDifference     float64 `json:"total_difference"`
	AverageWeightDiff   float64 `json:"average_weight_diff"`
	PendingBillingCount int64   `json:"pending_billing_count"`
}

// PostgresBillingRepository PostgreSQL实现的计费仓库
type PostgresBillingRepository struct {
	db *sql.DB
}

// NewPostgresBillingRepository 创建PostgreSQL计费仓库
func NewPostgresBillingRepository(db *sql.DB) *PostgresBillingRepository {
	return &PostgresBillingRepository{
		db: db,
	}
}

// SaveBillingDetail 保存计费详情
func (r *PostgresBillingRepository) SaveBillingDetail(ctx context.Context, detail *model.OrderBillingDetail) error {
	query := `
		INSERT INTO order_billing_details (
			order_id, order_no, billing_type, freight_fee, insurance_fee, package_fee,
			pickup_fee, delivery_fee, cod_fee, other_fee, total_fee, weight, volume,
			charged_weight, provider, source, raw_data, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
		RETURNING id
	`

	var id int64
	err := r.db.QueryRowContext(
		ctx, query,
		detail.OrderID, detail.OrderNo, detail.BillingType,
		detail.FreightFee, detail.InsuranceFee, detail.PackageFee,
		detail.PickupFee, detail.DeliveryFee, detail.CodFee, detail.OtherFee,
		detail.TotalFee, detail.Weight, detail.Volume, detail.ChargedWeight,
		detail.Provider, detail.Source, detail.RawData,
		detail.CreatedAt, detail.UpdatedAt,
	).Scan(&id)

	if err != nil {
		return fmt.Errorf("保存计费详情失败: %w", err)
	}

	detail.ID = id
	return nil
}

// GetBillingDetailsByOrderNo 根据订单号获取计费详情
func (r *PostgresBillingRepository) GetBillingDetailsByOrderNo(ctx context.Context, orderNo string) ([]*model.OrderBillingDetail, error) {
	query := `
		SELECT id, order_id, order_no, billing_type, freight_fee, insurance_fee, package_fee,
			   pickup_fee, delivery_fee, cod_fee, other_fee, total_fee, weight, volume,
			   charged_weight, provider, source, COALESCE(raw_data, '{}'), created_at, updated_at
		FROM order_billing_details
		WHERE order_no = $1
		ORDER BY created_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, orderNo)
	if err != nil {
		return nil, fmt.Errorf("查询计费详情失败: %w", err)
	}
	defer rows.Close()

	var details []*model.OrderBillingDetail
	for rows.Next() {
		var detail model.OrderBillingDetail
		err := rows.Scan(
			&detail.ID, &detail.OrderID, &detail.OrderNo, &detail.BillingType,
			&detail.FreightFee, &detail.InsuranceFee, &detail.PackageFee,
			&detail.PickupFee, &detail.DeliveryFee, &detail.CodFee, &detail.OtherFee,
			&detail.TotalFee, &detail.Weight, &detail.Volume, &detail.ChargedWeight,
			&detail.Provider, &detail.Source, &detail.RawData,
			&detail.CreatedAt, &detail.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描计费详情失败: %w", err)
		}
		details = append(details, &detail)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代计费详情失败: %w", err)
	}

	return details, nil
}

// GetLatestBillingDetail 获取最新的计费详情
func (r *PostgresBillingRepository) GetLatestBillingDetail(ctx context.Context, orderNo string, billingType string) (*model.OrderBillingDetail, error) {
	query := `
		SELECT id, order_id, order_no, billing_type, freight_fee, insurance_fee, package_fee,
			   pickup_fee, delivery_fee, cod_fee, other_fee, total_fee, weight, volume,
			   charged_weight, provider, source, COALESCE(raw_data, '{}'), created_at, updated_at
		FROM order_billing_details
		WHERE order_no = $1 AND billing_type = $2
		ORDER BY created_at DESC
		LIMIT 1
	`

	var detail model.OrderBillingDetail
	err := r.db.QueryRowContext(ctx, query, orderNo, billingType).Scan(
		&detail.ID, &detail.OrderID, &detail.OrderNo, &detail.BillingType,
		&detail.FreightFee, &detail.InsuranceFee, &detail.PackageFee,
		&detail.PickupFee, &detail.DeliveryFee, &detail.CodFee, &detail.OtherFee,
		&detail.TotalFee, &detail.Weight, &detail.Volume, &detail.ChargedWeight,
		&detail.Provider, &detail.Source, &detail.RawData,
		&detail.CreatedAt, &detail.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("计费详情不存在")
		}
		return nil, fmt.Errorf("查询计费详情失败: %w", err)
	}

	return &detail, nil
}

// UpdateBillingDetail 更新计费详情
func (r *PostgresBillingRepository) UpdateBillingDetail(ctx context.Context, detail *model.OrderBillingDetail) error {
	query := `
		UPDATE order_billing_details SET
			freight_fee = $1, insurance_fee = $2, package_fee = $3,
			pickup_fee = $4, delivery_fee = $5, cod_fee = $6, other_fee = $7,
			total_fee = $8, weight = $9, volume = $10, charged_weight = $11,
			raw_data = $12, updated_at = $13
		WHERE id = $14
	`

	result, err := r.db.ExecContext(ctx, query,
		detail.FreightFee, detail.InsuranceFee, detail.PackageFee,
		detail.PickupFee, detail.DeliveryFee, detail.CodFee, detail.OtherFee,
		detail.TotalFee, detail.Weight, detail.Volume, detail.ChargedWeight,
		detail.RawData, util.NowBeijing(), detail.ID,
	)
	if err != nil {
		return fmt.Errorf("更新计费详情失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("计费详情不存在或未变更")
	}

	return nil
}

// SaveBillingHistory 保存计费历史
func (r *PostgresBillingRepository) SaveBillingHistory(ctx context.Context, history *model.OrderBillingHistory) error {
	query := `
		INSERT INTO order_billing_history (
			order_id, order_no, change_type, old_values, new_values, difference_amount,
			reason, source, provider, operator_id, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
		RETURNING id
	`

	var id int64
	err := r.db.QueryRowContext(
		ctx, query,
		history.OrderID, history.OrderNo, history.ChangeType,
		history.OldValues, history.NewValues, history.DifferenceAmount,
		history.Reason, history.Source, history.Provider,
		history.OperatorID, history.CreatedAt,
	).Scan(&id)

	if err != nil {
		return fmt.Errorf("保存计费历史失败: %w", err)
	}

	history.ID = id
	return nil
}

// GetBillingHistoryByOrderNo 根据订单号获取计费历史
func (r *PostgresBillingRepository) GetBillingHistoryByOrderNo(ctx context.Context, orderNo string) ([]*model.OrderBillingHistory, error) {
	query := `
		SELECT id, order_id, order_no, change_type, COALESCE(old_values, '{}'),
			   COALESCE(new_values, '{}'), difference_amount, COALESCE(reason, ''),
			   source, provider, COALESCE(operator_id, ''), created_at
		FROM order_billing_history
		WHERE order_no = $1
		ORDER BY created_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, orderNo)
	if err != nil {
		return nil, fmt.Errorf("查询计费历史失败: %w", err)
	}
	defer rows.Close()

	var histories []*model.OrderBillingHistory
	for rows.Next() {
		var history model.OrderBillingHistory
		err := rows.Scan(
			&history.ID, &history.OrderID, &history.OrderNo, &history.ChangeType,
			&history.OldValues, &history.NewValues, &history.DifferenceAmount,
			&history.Reason, &history.Source, &history.Provider,
			&history.OperatorID, &history.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描计费历史失败: %w", err)
		}
		histories = append(histories, &history)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代计费历史失败: %w", err)
	}

	return histories, nil
}

// BatchSaveBillingDetails 批量保存计费详情
func (r *PostgresBillingRepository) BatchSaveBillingDetails(ctx context.Context, details []*model.OrderBillingDetail) error {
	if len(details) == 0 {
		return nil
	}

	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, `
		INSERT INTO order_billing_details (
			order_id, order_no, billing_type, freight_fee, insurance_fee, package_fee,
			pickup_fee, delivery_fee, cod_fee, other_fee, total_fee, weight, volume,
			charged_weight, provider, source, raw_data, created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
	`)
	if err != nil {
		return fmt.Errorf("准备语句失败: %w", err)
	}
	defer stmt.Close()

	for _, detail := range details {
		_, err := stmt.ExecContext(ctx,
			detail.OrderID, detail.OrderNo, detail.BillingType,
			detail.FreightFee, detail.InsuranceFee, detail.PackageFee,
			detail.PickupFee, detail.DeliveryFee, detail.CodFee, detail.OtherFee,
			detail.TotalFee, detail.Weight, detail.Volume, detail.ChargedWeight,
			detail.Provider, detail.Source, detail.RawData,
			detail.CreatedAt, detail.UpdatedAt,
		)
		if err != nil {
			return fmt.Errorf("批量插入计费详情失败: %w", err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// GetBillingStatistics 获取计费统计信息
func (r *PostgresBillingRepository) GetBillingStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (*BillingStatistics, error) {
	query := `
		SELECT
			COUNT(*) as total_orders,
			COALESCE(SUM(price), 0) as total_estimated_fee,
			COALESCE(SUM(actual_fee), 0) as total_actual_fee,
			COALESCE(SUM(actual_fee - price), 0) as total_difference,
			COALESCE(AVG(actual_weight - order_weight), 0) as average_weight_diff,
			COUNT(CASE WHEN billing_status = 'pending' THEN 1 END) as pending_billing_count
		FROM order_records
		WHERE user_id = $1 AND created_at BETWEEN $2 AND $3
	`

	var stats BillingStatistics
	err := r.db.QueryRowContext(ctx, query, userID, startTime, endTime).Scan(
		&stats.TotalOrders,
		&stats.TotalPrice,
		&stats.TotalActualFee,
		&stats.TotalDifference,
		&stats.AverageWeightDiff,
		&stats.PendingBillingCount,
	)

	if err != nil {
		return nil, fmt.Errorf("查询计费统计信息失败: %w", err)
	}

	return &stats, nil
}
