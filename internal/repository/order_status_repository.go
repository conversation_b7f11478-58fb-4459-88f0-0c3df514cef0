package repository

import (
	"context"
	"database/sql"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// OrderStatusRepository 订单状态仓库接口
type OrderStatusRepository interface {
	// CreateStatusHistory 创建状态历史记录
	CreateStatusHistory(ctx context.Context, history *model.OrderStatusHistory) error

	// GetStatusHistory 获取订单状态历史
	GetStatusHistory(ctx context.Context, orderNo string) ([]model.OrderStatusHistory, error)

	// UpdateOrderStatus 更新订单状态
	UpdateOrderStatus(ctx context.Context, req *model.OrderStatusUpdateRequest) error

	// GetOrderCurrentStatus 获取订单当前状态
	GetOrderCurrentStatus(ctx context.Context, orderNo string) (string, error)

	// ValidateStatusTransition 验证状态转换
	ValidateStatusTransition(ctx context.Context, orderNo, newStatus string) (*model.StatusTransitionValidation, error)

	// GetOrdersNeedingAttention 获取需要关注的订单（异常状态等）
	GetOrdersNeedingAttention(ctx context.Context, limit, offset int) ([]model.OrderStatusSummary, error)

	// BatchUpdateOrderStatus 批量更新订单状态
	BatchUpdateOrderStatus(ctx context.Context, requests []*model.OrderStatusUpdateRequest) error
}

// OrderStatusRepositoryImpl 订单状态仓库实现
type OrderStatusRepositoryImpl struct {
	db *sql.DB
}

// NewOrderStatusRepository 创建订单状态仓库
func NewOrderStatusRepository(db *sql.DB) OrderStatusRepository {
	return &OrderStatusRepositoryImpl{
		db: db,
	}
}

// CreateStatusHistory 创建状态历史记录
func (r *OrderStatusRepositoryImpl) CreateStatusHistory(ctx context.Context, history *model.OrderStatusHistory) error {
	query := `
		INSERT INTO order_status_history (
			order_no, from_status, to_status, provider, raw_status, extra, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8
		)`

	now := util.NowBeijing()
	history.CreatedAt = now
	history.UpdatedAt = now

	_, err := r.db.ExecContext(ctx, query,
		history.OrderNo,
		history.FromStatus,
		history.ToStatus,
		history.Provider,
		history.RawStatus,
		history.Extra,
		history.CreatedAt,
		history.UpdatedAt,
	)

	return err
}

// GetStatusHistory 获取订单状态历史
func (r *OrderStatusRepositoryImpl) GetStatusHistory(ctx context.Context, orderNo string) ([]model.OrderStatusHistory, error) {
	query := `
		SELECT id, order_no, from_status, to_status, provider, raw_status, extra, created_at, updated_at
		FROM order_status_history
		WHERE order_no = $1
		ORDER BY created_at ASC`

	rows, err := r.db.QueryContext(ctx, query, orderNo)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var histories []model.OrderStatusHistory
	for rows.Next() {
		var history model.OrderStatusHistory
		err := rows.Scan(
			&history.ID,
			&history.OrderNo,
			&history.FromStatus,
			&history.ToStatus,
			&history.Provider,
			&history.RawStatus,
			&history.Extra,
			&history.CreatedAt,
			&history.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		histories = append(histories, history)
	}

	return histories, rows.Err()
}

// UpdateOrderStatus 更新订单状态
func (r *OrderStatusRepositoryImpl) UpdateOrderStatus(ctx context.Context, req *model.OrderStatusUpdateRequest) error {
	// 开始事务
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 获取当前状态
	var currentStatus string
	err = tx.QueryRowContext(ctx, "SELECT status FROM order_records WHERE order_no = $1", req.OrderNo).Scan(&currentStatus)
	if err != nil {
		return err
	}

	// 更新订单状态
	updateQuery := `
		UPDATE order_records
		SET status = $1, updated_at = $2
		WHERE order_no = $3`

	_, err = tx.ExecContext(ctx, updateQuery, req.NewStatus, req.UpdateTime, req.OrderNo)
	if err != nil {
		return err
	}

	// 创建状态历史记录
	historyQuery := `
		INSERT INTO order_status_history (
			order_no, from_status, to_status, provider, raw_status, extra, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8
		)`

	now := util.NowBeijing()
	_, err = tx.ExecContext(ctx, historyQuery,
		req.OrderNo,
		currentStatus,
		req.NewStatus,
		req.Provider,
		"", // raw_status 可以从 extra 中获取
		req.Extra,
		now,
		now,
	)
	if err != nil {
		return err
	}

	return tx.Commit()
}

// GetOrderCurrentStatus 获取订单当前状态
func (r *OrderStatusRepositoryImpl) GetOrderCurrentStatus(ctx context.Context, orderNo string) (string, error) {
	var status string
	query := "SELECT status FROM order_records WHERE order_no = $1"
	err := r.db.QueryRowContext(ctx, query, orderNo).Scan(&status)
	return status, err
}

// ValidateStatusTransition 验证状态转换
func (r *OrderStatusRepositoryImpl) ValidateStatusTransition(ctx context.Context, orderNo, newStatus string) (*model.StatusTransitionValidation, error) {
	currentStatus, err := r.GetOrderCurrentStatus(ctx, orderNo)
	if err != nil {
		return nil, err
	}

	validation := model.IsValidStatusTransition(currentStatus, newStatus)
	return &validation, nil
}

// GetOrdersNeedingAttention 获取需要关注的订单
func (r *OrderStatusRepositoryImpl) GetOrdersNeedingAttention(ctx context.Context, limit, offset int) ([]model.OrderStatusSummary, error) {
	// 获取异常状态的订单
	exceptionStatuses := []string{
		model.OrderStatusException,
		model.OrderStatusPickupFailed,
		model.OrderStatusSubmitFailed,
		model.OrderStatusPrintFailed,
	}

	query := `
		SELECT order_no, status, provider, updated_at
		FROM order_records
		WHERE status = ANY($1)
		ORDER BY updated_at DESC
		LIMIT $2 OFFSET $3`

	rows, err := r.db.QueryContext(ctx, query, exceptionStatuses, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var summaries []model.OrderStatusSummary
	for rows.Next() {
		var summary model.OrderStatusSummary
		var provider string
		var lastUpdateTime time.Time

		err := rows.Scan(
			&summary.OrderNo,
			&summary.CurrentStatus,
			&provider,
			&lastUpdateTime,
		)
		if err != nil {
			return nil, err
		}

		// 获取状态历史
		history, _ := r.GetStatusHistory(ctx, summary.OrderNo)

		summary = model.BuildStatusSummary(
			summary.OrderNo,
			summary.CurrentStatus,
			provider,
			lastUpdateTime,
			history,
			nil,
		)

		summaries = append(summaries, summary)
	}

	return summaries, rows.Err()
}

// BatchUpdateOrderStatus 批量更新订单状态
func (r *OrderStatusRepositoryImpl) BatchUpdateOrderStatus(ctx context.Context, requests []*model.OrderStatusUpdateRequest) error {
	if len(requests) == 0 {
		return nil
	}

	// 开始事务
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	for _, req := range requests {
		// 获取当前状态
		var currentStatus string
		err = tx.QueryRowContext(ctx, "SELECT status FROM order_records WHERE order_no = $1", req.OrderNo).Scan(&currentStatus)
		if err != nil {
			continue // 跳过不存在的订单
		}

		// 更新订单状态
		updateQuery := `
			UPDATE order_records
			SET status = $1, updated_at = $2
			WHERE order_no = $3`

		_, err = tx.ExecContext(ctx, updateQuery, req.NewStatus, req.UpdateTime, req.OrderNo)
		if err != nil {
			continue // 跳过更新失败的订单
		}

		// 创建状态历史记录
		historyQuery := `
			INSERT INTO order_status_history (
				order_no, from_status, to_status, provider, raw_status, extra, created_at, updated_at
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8
			)`

		now := util.NowBeijing()
		_, _ = tx.ExecContext(ctx, historyQuery,
			req.OrderNo,
			currentStatus,
			req.NewStatus,
			req.Provider,
			"",
			req.Extra,
			now,
			now,
		)
		// 忽略历史记录插入错误，不影响主流程
	}

	return tx.Commit()
}
