package validator

import (
	"fmt"
	"net/mail"
	"regexp"
	"strings"
	"unicode"
	"unicode/utf8"
)

// InputValidator 输入验证器
type InputValidator struct {
	// 预编译的正则表达式
	phoneRegex        *regexp.Regexp
	orderNoRegex      *regexp.Regexp
	trackingRegex     *regexp.Regexp
	sqlInjectionRegex *regexp.Regexp
	xssRegex          *regexp.Regexp
}

// NewInputValidator 创建新的输入验证器
func NewInputValidator() *InputValidator {
	return &InputValidator{
		// 🔥 修复：放宽手机号验证，支持手机号和固定电话
		phoneRegex:        regexp.MustCompile(`^[\d\s\-\+\(\)]{7,20}$`),
		orderNoRegex:      regexp.MustCompile(`^[A-Za-z0-9_-]{1,50}$`),
		trackingRegex:     regexp.MustCompile(`^[A-Za-z0-9]{1,50}$`),
		sqlInjectionRegex: regexp.MustCompile(`(?i)(union|select|insert|update|delete|drop|create|alter|exec|script|javascript|vbscript|onload|onerror)`),
		xssRegex:          regexp.MustCompile(`(?i)(<script|javascript:|vbscript:|onload=|onerror=|<iframe|<object|<embed)`),
	}
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("validation error in field '%s': %s", e.Field, e.Message)
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid  bool              `json:"valid"`
	Errors []ValidationError `json:"errors,omitempty"`
}

// AddError 添加验证错误
func (vr *ValidationResult) AddError(field, message, code string) {
	vr.Valid = false
	vr.Errors = append(vr.Errors, ValidationError{
		Field:   field,
		Message: message,
		Code:    code,
	})
}

// ValidateString 验证字符串基本规则
func (v *InputValidator) ValidateString(value, fieldName string, minLen, maxLen int, required bool) *ValidationResult {
	result := &ValidationResult{Valid: true}

	// 检查是否必填
	if required && strings.TrimSpace(value) == "" {
		result.AddError(fieldName, "字段不能为空", "required")
		return result
	}

	// 如果不是必填且为空，则跳过其他验证
	if !required && strings.TrimSpace(value) == "" {
		return result
	}

	// 检查长度
	if utf8.RuneCountInString(value) < minLen {
		result.AddError(fieldName, fmt.Sprintf("长度不能少于%d个字符", minLen), "min_length")
	}

	if utf8.RuneCountInString(value) > maxLen {
		result.AddError(fieldName, fmt.Sprintf("长度不能超过%d个字符", maxLen), "max_length")
	}

	// 检查SQL注入
	if v.sqlInjectionRegex.MatchString(value) {
		result.AddError(fieldName, "包含不安全的字符", "sql_injection")
	}

	// 检查XSS
	if v.xssRegex.MatchString(value) {
		result.AddError(fieldName, "包含不安全的脚本内容", "xss")
	}

	return result
}

// ValidateEmail 验证邮箱地址
func (v *InputValidator) ValidateEmail(email, fieldName string, required bool) *ValidationResult {
	result := &ValidationResult{Valid: true}

	// 检查是否必填
	if required && strings.TrimSpace(email) == "" {
		result.AddError(fieldName, "邮箱地址不能为空", "required")
		return result
	}

	// 如果不是必填且为空，则跳过验证
	if !required && strings.TrimSpace(email) == "" {
		return result
	}

	// 验证邮箱格式
	if _, err := mail.ParseAddress(email); err != nil {
		result.AddError(fieldName, "邮箱地址格式不正确", "invalid_email")
	}

	// 检查长度
	if len(email) > 254 {
		result.AddError(fieldName, "邮箱地址过长", "max_length")
	}

	return result
}

// ValidatePhone 验证手机号码
func (v *InputValidator) ValidatePhone(phone, fieldName string, required bool) *ValidationResult {
	result := &ValidationResult{Valid: true}

	// 检查是否必填
	if required && strings.TrimSpace(phone) == "" {
		result.AddError(fieldName, "手机号码不能为空", "required")
		return result
	}

	// 如果不是必填且为空，则跳过验证
	if !required && strings.TrimSpace(phone) == "" {
		return result
	}

	// 🔥 修复：验证电话号码格式（支持手机号和固定电话）
	if !v.phoneRegex.MatchString(phone) {
		result.AddError(fieldName, "电话号码格式不正确（支持手机号和固定电话）", "invalid_phone")
	}

	return result
}

// ValidateOrderNo 验证订单号
func (v *InputValidator) ValidateOrderNo(orderNo, fieldName string, required bool) *ValidationResult {
	result := &ValidationResult{Valid: true}

	// 检查是否必填
	if required && strings.TrimSpace(orderNo) == "" {
		result.AddError(fieldName, "订单号不能为空", "required")
		return result
	}

	// 如果不是必填且为空，则跳过验证
	if !required && strings.TrimSpace(orderNo) == "" {
		return result
	}

	// 验证订单号格式
	if !v.orderNoRegex.MatchString(orderNo) {
		result.AddError(fieldName, "订单号格式不正确，只能包含字母、数字、下划线和连字符", "invalid_order_no")
	}

	return result
}

// ValidateTrackingNo 验证快递单号
func (v *InputValidator) ValidateTrackingNo(trackingNo, fieldName string, required bool) *ValidationResult {
	result := &ValidationResult{Valid: true}

	// 检查是否必填
	if required && strings.TrimSpace(trackingNo) == "" {
		result.AddError(fieldName, "快递单号不能为空", "required")
		return result
	}

	// 如果不是必填且为空，则跳过验证
	if !required && strings.TrimSpace(trackingNo) == "" {
		return result
	}

	// 验证快递单号格式
	if !v.trackingRegex.MatchString(trackingNo) {
		result.AddError(fieldName, "快递单号格式不正确，只能包含字母和数字", "invalid_tracking_no")
	}

	return result
}

// SanitizeSearchKeyword 清理搜索关键词
func (v *InputValidator) SanitizeSearchKeyword(keyword string) string {
	// 限制长度
	if utf8.RuneCountInString(keyword) > 100 {
		runes := []rune(keyword)
		keyword = string(runes[:100])
	}

	// 移除危险字符，保留安全字符
	var result strings.Builder
	for _, char := range keyword {
		if v.isSafeChar(char) {
			result.WriteRune(char)
		}
	}

	return strings.TrimSpace(result.String())
}

// isSafeChar 检查字符是否安全
func (v *InputValidator) isSafeChar(char rune) bool {
	return unicode.IsLetter(char) ||
		unicode.IsDigit(char) ||
		unicode.Is(unicode.Han, char) || // 中文字符
		char == '-' || char == '_' || char == '@' || char == '.' || char == ' '
}

// ValidateNumericRange 验证数值范围
func (v *InputValidator) ValidateNumericRange(value float64, fieldName string, min, max float64) *ValidationResult {
	result := &ValidationResult{Valid: true}

	if value < min {
		result.AddError(fieldName, fmt.Sprintf("数值不能小于%.2f", min), "min_value")
	}

	if value > max {
		result.AddError(fieldName, fmt.Sprintf("数值不能大于%.2f", max), "max_value")
	}

	return result
}

// CombineResults 合并多个验证结果
func CombineResults(results ...*ValidationResult) *ValidationResult {
	combined := &ValidationResult{Valid: true}

	for _, result := range results {
		if !result.Valid {
			combined.Valid = false
			combined.Errors = append(combined.Errors, result.Errors...)
		}
	}

	return combined
}
