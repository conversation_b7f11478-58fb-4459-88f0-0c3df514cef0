package errors

import (
	"fmt"
	"net/http"
	"time"
)

// ErrorCode 错误代码类型
type ErrorCode string

// 业务错误代码定义
const (
	// 通用错误
	ErrCodeInternal        ErrorCode = "INTERNAL_ERROR"
	ErrCodeInvalidRequest  ErrorCode = "INVALID_REQUEST"
	ErrCodeUnauthorized    ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden       ErrorCode = "FORBIDDEN"
	ErrCodeNotFound        ErrorCode = "NOT_FOUND"
	ErrCodeConflict        ErrorCode = "CONFLICT"
	ErrCodeTooManyRequests ErrorCode = "TOO_MANY_REQUESTS"
	ErrCodeNotImplemented  ErrorCode = "NOT_IMPLEMENTED"

	// 订单相关错误
	ErrCodeOrderNotFound      ErrorCode = "ORDER_NOT_FOUND"
	ErrCodeOrderAlreadyExists ErrorCode = "ORDER_ALREADY_EXISTS"
	ErrCodeOrderCancelled     ErrorCode = "ORDER_CANCELLED"
	ErrCodeOrderExpired       ErrorCode = "ORDER_EXPIRED"
	ErrCodeInvalidOrderStatus ErrorCode = "INVALID_ORDER_STATUS"
	ErrCodeOrderLimitExceeded ErrorCode = "ORDER_LIMIT_EXCEEDED"

	// 余额相关错误
	ErrCodeInsufficientBalance  ErrorCode = "INSUFFICIENT_BALANCE"
	ErrCodeBalanceNotFound      ErrorCode = "BALANCE_NOT_FOUND"
	ErrCodeBalanceFrozen        ErrorCode = "BALANCE_FROZEN"
	ErrCodeInvalidAmount        ErrorCode = "INVALID_AMOUNT"
	ErrCodeDuplicateTransaction ErrorCode = "DUPLICATE_TRANSACTION"
	ErrCodeTransactionFailed    ErrorCode = "TRANSACTION_FAILED"

	// 计费相关错误
	ErrCodeBillingNotFound      ErrorCode = "BILLING_NOT_FOUND"
	ErrCodeInvalidFeeAmount     ErrorCode = "INVALID_FEE_AMOUNT"
	ErrCodeBillingProcessFailed ErrorCode = "BILLING_PROCESS_FAILED"
	ErrCodeFeeDifferenceTooBig  ErrorCode = "FEE_DIFFERENCE_TOO_BIG"

	// 价格验证相关错误
	ErrCodePriceChanged          ErrorCode = "PRICE_CHANGED"
	ErrCodePriceValidationFailed ErrorCode = "PRICE_VALIDATION_FAILED"
	ErrCodeInvalidPriceSource    ErrorCode = "INVALID_PRICE_SOURCE"
	ErrCodeCachedPriceExpired    ErrorCode = "CACHED_PRICE_EXPIRED"

	// 回调相关错误
	ErrCodeCallbackNotFound      ErrorCode = "CALLBACK_NOT_FOUND"
	ErrCodeInvalidSignature      ErrorCode = "INVALID_SIGNATURE"
	ErrCodeCallbackTimeout       ErrorCode = "CALLBACK_TIMEOUT"
	ErrCodeCallbackProcessFailed ErrorCode = "CALLBACK_PROCESS_FAILED"

	// 供应商相关错误
	ErrCodeProviderNotFound    ErrorCode = "PROVIDER_NOT_FOUND"
	ErrCodeProviderUnavailable ErrorCode = "PROVIDER_UNAVAILABLE"
	ErrCodeProviderTimeout     ErrorCode = "PROVIDER_TIMEOUT"
	ErrCodeProviderRateLimit   ErrorCode = "PROVIDER_RATE_LIMIT"

	// 配置相关错误
	ErrCodeConfigNotFound ErrorCode = "CONFIG_NOT_FOUND"
	ErrCodeInvalidConfig  ErrorCode = "INVALID_CONFIG"

	// 数据库相关错误
	ErrCodeDatabaseConnection ErrorCode = "DATABASE_CONNECTION_ERROR"
	ErrCodeDatabaseTimeout    ErrorCode = "DATABASE_TIMEOUT"
	ErrCodeDatabaseConstraint ErrorCode = "DATABASE_CONSTRAINT_VIOLATION"
	ErrCodeOptimisticLock     ErrorCode = "OPTIMISTIC_LOCK_FAILURE"
)

// BusinessError 业务错误接口
type BusinessError interface {
	error
	Code() ErrorCode
	Message() string
	Details() map[string]interface{}
	HTTPStatus() int
	IsRetryable() bool
	Timestamp() time.Time
}

// businessError 业务错误实现
type businessError struct {
	code       ErrorCode
	message    string
	details    map[string]interface{}
	httpStatus int
	retryable  bool
	timestamp  time.Time
	cause      error
}

// Error 实现error接口
func (e *businessError) Error() string {
	if e.cause != nil {
		return fmt.Sprintf("[%s] %s: %v", e.code, e.message, e.cause)
	}
	return fmt.Sprintf("[%s] %s", e.code, e.message)
}

// Code 返回错误代码
func (e *businessError) Code() ErrorCode {
	return e.code
}

// Message 返回错误消息
func (e *businessError) Message() string {
	return e.message
}

// Details 返回错误详情
func (e *businessError) Details() map[string]interface{} {
	if e.details == nil {
		return make(map[string]interface{})
	}
	return e.details
}

// HTTPStatus 返回HTTP状态码
func (e *businessError) HTTPStatus() int {
	return e.httpStatus
}

// IsRetryable 返回是否可重试
func (e *businessError) IsRetryable() bool {
	return e.retryable
}

// Timestamp 返回错误时间戳
func (e *businessError) Timestamp() time.Time {
	return e.timestamp
}

// Unwrap 返回原始错误
func (e *businessError) Unwrap() error {
	return e.cause
}

// NewBusinessError 创建业务错误
func NewBusinessError(code ErrorCode, message string) BusinessError {
	return &businessError{
		code:       code,
		message:    message,
		details:    make(map[string]interface{}),
		httpStatus: getDefaultHTTPStatus(code),
		retryable:  isRetryableByDefault(code),
		timestamp:  time.Now(),
	}
}

// NewBusinessErrorWithDetails 创建带详情的业务错误
func NewBusinessErrorWithDetails(code ErrorCode, message string, details map[string]interface{}) BusinessError {
	return &businessError{
		code:       code,
		message:    message,
		details:    details,
		httpStatus: getDefaultHTTPStatus(code),
		retryable:  isRetryableByDefault(code),
		timestamp:  time.Now(),
	}
}

// NewBusinessErrorWithCause 创建带原因的业务错误
func NewBusinessErrorWithCause(code ErrorCode, message string, cause error) BusinessError {
	return &businessError{
		code:       code,
		message:    message,
		details:    make(map[string]interface{}),
		httpStatus: getDefaultHTTPStatus(code),
		retryable:  isRetryableByDefault(code),
		timestamp:  time.Now(),
		cause:      cause,
	}
}

// WrapError 包装错误
func WrapError(code ErrorCode, message string, cause error) BusinessError {
	return NewBusinessErrorWithCause(code, message, cause)
}

// getDefaultHTTPStatus 获取默认HTTP状态码
func getDefaultHTTPStatus(code ErrorCode) int {
	switch code {
	case ErrCodeInvalidRequest, ErrCodeInvalidAmount, ErrCodeInvalidFeeAmount, ErrCodeInvalidConfig:
		return http.StatusBadRequest
	case ErrCodeUnauthorized:
		return http.StatusUnauthorized
	case ErrCodeForbidden:
		return http.StatusForbidden
	case ErrCodeNotFound, ErrCodeOrderNotFound, ErrCodeBalanceNotFound, ErrCodeBillingNotFound,
		ErrCodeCallbackNotFound, ErrCodeProviderNotFound, ErrCodeConfigNotFound:
		return http.StatusNotFound
	case ErrCodeConflict, ErrCodeOrderAlreadyExists, ErrCodeDuplicateTransaction:
		return http.StatusConflict
	case ErrCodeTooManyRequests, ErrCodeProviderRateLimit:
		return http.StatusTooManyRequests
	case ErrCodeProviderUnavailable:
		return http.StatusServiceUnavailable
	case ErrCodeProviderTimeout, ErrCodeCallbackTimeout, ErrCodeDatabaseTimeout:
		return http.StatusGatewayTimeout
	case ErrCodeNotImplemented:
		return http.StatusNotImplemented
	default:
		return http.StatusInternalServerError
	}
}

// isRetryableByDefault 判断错误是否默认可重试
func isRetryableByDefault(code ErrorCode) bool {
	switch code {
	case ErrCodeProviderTimeout, ErrCodeProviderUnavailable, ErrCodeDatabaseTimeout,
		ErrCodeDatabaseConnection, ErrCodeCallbackTimeout, ErrCodeTooManyRequests,
		ErrCodeProviderRateLimit:
		return true
	default:
		return false
	}
}

// 预定义的常用错误
var (
	ErrInsufficientBalance  = NewBusinessError(ErrCodeInsufficientBalance, "账户余额不足，请联系客服")
	ErrInvalidAmount        = NewBusinessError(ErrCodeInvalidAmount, "金额无效")
	ErrDuplicateTransaction = NewBusinessError(ErrCodeDuplicateTransaction, "重复交易")
	ErrOrderNotFound        = NewBusinessError(ErrCodeOrderNotFound, "订单不存在")
	ErrProviderUnavailable  = NewBusinessError(ErrCodeProviderUnavailable, "供应商服务不可用")
	ErrInvalidSignature     = NewBusinessError(ErrCodeInvalidSignature, "签名验证失败")
	ErrOptimisticLock       = NewBusinessError(ErrCodeOptimisticLock, "并发冲突，请重试")
)

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Success   bool                   `json:"success"`
	Code      ErrorCode              `json:"code"`
	Message   string                 `json:"message"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	RequestID string                 `json:"request_id,omitempty"`
}

// ToErrorResponse 转换为错误响应
func ToErrorResponse(err error, requestID string) *ErrorResponse {
	if businessErr, ok := err.(BusinessError); ok {
		return &ErrorResponse{
			Success:   false,
			Code:      businessErr.Code(),
			Message:   businessErr.Message(),
			Details:   businessErr.Details(),
			Timestamp: businessErr.Timestamp(),
			RequestID: requestID,
		}
	}

	// 处理非业务错误
	return &ErrorResponse{
		Success:   false,
		Code:      ErrCodeInternal,
		Message:   "内部服务错误",
		Details:   map[string]interface{}{"original_error": err.Error()},
		Timestamp: time.Now(),
		RequestID: requestID,
	}
}

// IsBusinessError 判断是否为业务错误
func IsBusinessError(err error) bool {
	_, ok := err.(BusinessError)
	return ok
}

// GetErrorCode 获取错误代码
func GetErrorCode(err error) ErrorCode {
	if businessErr, ok := err.(BusinessError); ok {
		return businessErr.Code()
	}
	return ErrCodeInternal
}

// IsRetryable 判断错误是否可重试
func IsRetryable(err error) bool {
	if businessErr, ok := err.(BusinessError); ok {
		return businessErr.IsRetryable()
	}
	return false
}
