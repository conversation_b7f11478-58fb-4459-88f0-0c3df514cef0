package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/user"
)

// RoleHandlerInterface 角色处理器接口
type RoleHandlerInterface interface {
	CreateRole(c *gin.Context)
	GetRole(c *gin.Context)
	GetAllRoles(c *gin.Context)
	UpdateRole(c *gin.Context)
	DeleteRole(c *gin.Context)
	AddPermissionToRole(c *gin.Context, roleID, permissionID string) error
	RemovePermissionFromRole(c *gin.Context, roleID, permissionID string) error
}

// RoleHandler 角色处理器
type RoleHandler struct {
	roleService user.RoleService
}

// NewRoleHandler 创建新的角色处理器
func NewRoleHandler(roleService user.RoleService) RoleHandlerInterface {
	return &RoleHandler{
		roleService: roleService,
	}
}

// CreateRole 创建新角色
// @Summary 创建新角色
// @Description 创建新角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param role body CreateRoleRequest true "角色信息"
// @Success 201 {object} user.Role
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/roles [post]
func (h *RoleHandler) CreateRole(c *gin.Context) {
	var req CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求参数: " + err.Error(),
		})
		return
	}

	// 创建角色
	role, err := h.roleService.CreateRole(req.Name, req.Description, req.IsSystem)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建角色失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, role)
}

// GetRole 获取角色
// @Summary 获取角色
// @Description 获取角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path string true "角色ID"
// @Success 200 {object} user.Role
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/roles/{id} [get]
func (h *RoleHandler) GetRole(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "角色ID不能为空",
		})
		return
	}

	// 获取角色
	role, err := h.roleService.GetRoleByID(id)
	if err != nil {
		if err == user.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "角色不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取角色失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, role)
}

// GetAllRoles 获取所有角色
// @Summary 获取所有角色
// @Description 获取所有角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Success 200 {array} user.Role
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/roles [get]
func (h *RoleHandler) GetAllRoles(c *gin.Context) {
	// 获取所有角色
	roles, err := h.roleService.GetAllRoles()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取所有角色失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, roles)
}

// UpdateRole 更新角色
// @Summary 更新角色
// @Description 更新角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path string true "角色ID"
// @Param role body UpdateRoleRequest true "角色信息"
// @Success 200 {object} user.Role
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/roles/{id} [put]
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "角色ID不能为空",
		})
		return
	}

	var req UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求参数: " + err.Error(),
		})
		return
	}

	// 获取角色
	role, err := h.roleService.GetRoleByID(id)
	if err != nil {
		if err == user.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "角色不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取角色失败: " + err.Error(),
		})
		return
	}

	// 更新角色
	role.Name = req.Name
	role.Description = req.Description
	role.IsSystem = req.IsSystem

	err = h.roleService.UpdateRole(role)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新角色失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, role)
}

// DeleteRole 删除角色
// @Summary 删除角色
// @Description 删除角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path string true "角色ID"
// @Success 204 {object} nil
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/roles/{id} [delete]
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "角色ID不能为空",
		})
		return
	}

	// 删除角色
	err := h.roleService.DeleteRole(id)
	if err != nil {
		if err == user.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "角色不存在",
			})
			return
		}
		if err == user.ErrSystemRoleCannotBeDeleted {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "系统角色不能被删除",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "删除角色失败: " + err.Error(),
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	IsSystem    bool   `json:"is_system"`
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	IsSystem    bool   `json:"is_system"`
}

// AddPermissionToRole 为角色添加权限
func (h *RoleHandler) AddPermissionToRole(c *gin.Context, roleID, permissionID string) error {
	return h.roleService.AddPermissionToRole(roleID, permissionID)
}

// RemovePermissionFromRole 从角色中移除权限
func (h *RoleHandler) RemovePermissionFromRole(c *gin.Context, roleID, permissionID string) error {
	return h.roleService.RemovePermissionFromRole(roleID, permissionID)
}
