package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/user"
)

// UserRoleHandler 用户角色处理器
type UserRoleHandler struct {
	userService user.UserRoleService
	roleService user.RoleService
}

// NewUserRoleHandler 创建新的用户角色处理器
func NewUserRoleHandler(userService user.UserRoleService, roleService user.RoleService) *UserRoleHandler {
	return &UserRoleHandler{
		userService: userService,
		roleService: roleService,
	}
}

// AddRoleToUser 为用户添加角色
// @Summary 为用户添加角色
// @Description 为用户添加角色
// @Tags 用户角色管理
// @Accept json
// @Produce json
// @Param userId path string true "用户ID"
// @Param roleId path string true "角色ID"
// @Success 204 {object} nil
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/users/{userId}/roles/{roleId} [post]
func (h *UserRoleHandler) AddRoleToUser(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "用户ID不能为空",
		})
		return
	}

	roleID := c.Param("roleId")
	if roleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "角色ID不能为空",
		})
		return
	}

	// 为用户添加角色
	err := h.userService.AddRoleToUser(userID, roleID)
	if err != nil {
		if err == user.ErrUserNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "用户不存在",
			})
			return
		}
		if err == user.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "角色不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "为用户添加角色失败: " + err.Error(),
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// RemoveRoleFromUser 从用户中移除角色
// @Summary 从用户中移除角色
// @Description 从用户中移除角色
// @Tags 用户角色管理
// @Accept json
// @Produce json
// @Param userId path string true "用户ID"
// @Param roleId path string true "角色ID"
// @Success 204 {object} nil
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/users/{userId}/roles/{roleId} [delete]
func (h *UserRoleHandler) RemoveRoleFromUser(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "用户ID不能为空",
		})
		return
	}

	roleID := c.Param("roleId")
	if roleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "角色ID不能为空",
		})
		return
	}

	// 从用户中移除角色
	err := h.userService.RemoveRoleFromUser(userID, roleID)
	if err != nil {
		if err == user.ErrUserNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "用户不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "从用户中移除角色失败: " + err.Error(),
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// SetDefaultRoleForUser 设置用户的默认角色
// @Summary 设置用户的默认角色
// @Description 设置用户的默认角色
// @Tags 用户角色管理
// @Accept json
// @Produce json
// @Param userId path string true "用户ID"
// @Param roleId path string true "角色ID"
// @Success 204 {object} nil
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/users/{userId}/default-role/{roleId} [put]
func (h *UserRoleHandler) SetDefaultRoleForUser(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "用户ID不能为空",
		})
		return
	}

	roleID := c.Param("roleId")
	if roleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "角色ID不能为空",
		})
		return
	}

	// 设置用户的默认角色
	err := h.userService.SetDefaultRoleForUser(userID, roleID)
	if err != nil {
		if err == user.ErrUserNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "用户不存在",
			})
			return
		}
		if err == user.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "角色不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "设置用户默认角色失败: " + err.Error(),
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetUserRoles 获取用户的所有角色
// @Summary 获取用户的所有角色
// @Description 获取用户的所有角色
// @Tags 用户角色管理
// @Accept json
// @Produce json
// @Param userId path string true "用户ID"
// @Success 200 {array} user.Role
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/users/{userId}/roles [get]
func (h *UserRoleHandler) GetUserRoles(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "用户ID不能为空",
		})
		return
	}

	// 获取用户的所有角色
	roles, err := h.userService.GetUserRoles(userID)
	if err != nil {
		if err == user.ErrUserNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "用户不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取用户角色失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, roles)
}

// GetUsersByRole 获取拥有指定角色的所有用户
// @Summary 获取拥有指定角色的所有用户
// @Description 获取拥有指定角色的所有用户
// @Tags 用户角色管理
// @Accept json
// @Produce json
// @Param roleId path string true "角色ID"
// @Success 200 {array} user.User
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/roles/{roleId}/users [get]
func (h *UserRoleHandler) GetUsersByRole(c *gin.Context) {
	roleID := c.Param("roleId")
	if roleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "角色ID不能为空",
		})
		return
	}

	// 获取拥有指定角色的所有用户
	users, err := h.userService.GetUsersByRole(roleID)
	if err != nil {
		if err == user.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "角色不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取拥有角色的用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, users)
}
