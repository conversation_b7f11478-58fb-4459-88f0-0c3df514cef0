package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/user"
)

// PermissionHandler 权限处理器
type PermissionHandler struct {
	permissionService user.PermissionService
}

// NewPermissionHandler 创建新的权限处理器
func NewPermissionHandler(permissionService user.PermissionService) *PermissionHandler {
	return &PermissionHandler{
		permissionService: permissionService,
	}
}

// CreatePermission 创建新权限
// @Summary 创建新权限
// @Description 创建新权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param permission body CreatePermissionRequest true "权限信息"
// @Success 201 {object} user.Permission
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/permissions [post]
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	var req CreatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求参数: " + err.Error(),
		})
		return
	}

	// 创建权限
	permission, err := h.permissionService.CreatePermission(req.Name, req.Description, req.Resource, req.Action, req.IsSystem)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "创建权限失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, permission)
}

// GetPermission 获取权限
// @Summary 获取权限
// @Description 获取权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param id path string true "权限ID"
// @Success 200 {object} user.Permission
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/permissions/{id} [get]
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "权限ID不能为空",
		})
		return
	}

	// 获取权限
	permission, err := h.permissionService.GetPermissionByID(id)
	if err != nil {
		if err == user.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "权限不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取权限失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, permission)
}

// GetAllPermissions 获取所有权限
// @Summary 获取所有权限
// @Description 获取所有权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Success 200 {array} user.Permission
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/permissions [get]
func (h *PermissionHandler) GetAllPermissions(c *gin.Context) {
	// 获取所有权限
	permissions, err := h.permissionService.GetAllPermissions()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取所有权限失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, permissions)
}

// UpdatePermission 更新权限
// @Summary 更新权限
// @Description 更新权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param id path string true "权限ID"
// @Param permission body UpdatePermissionRequest true "权限信息"
// @Success 200 {object} user.Permission
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/permissions/{id} [put]
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "权限ID不能为空",
		})
		return
	}

	var req UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的请求参数: " + err.Error(),
		})
		return
	}

	// 获取权限
	permission, err := h.permissionService.GetPermissionByID(id)
	if err != nil {
		if err == user.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "权限不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取权限失败: " + err.Error(),
		})
		return
	}

	// 更新权限
	permission.Name = req.Name
	permission.Description = req.Description
	permission.Resource = req.Resource
	permission.Action = req.Action
	permission.IsSystem = req.IsSystem

	err = h.permissionService.UpdatePermission(permission)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "更新权限失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, permission)
}

// DeletePermission 删除权限
// @Summary 删除权限
// @Description 删除权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param id path string true "权限ID"
// @Success 204 {object} nil
// @Failure 400 {object} model.ErrorResponse
// @Failure 401 {object} model.ErrorResponse
// @Failure 403 {object} model.ErrorResponse
// @Failure 404 {object} model.ErrorResponse
// @Failure 500 {object} model.ErrorResponse
// @Router /api/v1/permissions/{id} [delete]
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "权限ID不能为空",
		})
		return
	}

	// 删除权限
	err := h.permissionService.DeletePermission(id)
	if err != nil {
		if err == user.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "权限不存在",
			})
			return
		}
		if err == user.ErrSystemPermissionCannotBeDeleted {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "系统权限不能被删除",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "删除权限失败: " + err.Error(),
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Resource    string `json:"resource" binding:"required"`
	Action      string `json:"action" binding:"required"`
	IsSystem    bool   `json:"is_system"`
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Resource    string `json:"resource" binding:"required"`
	Action      string `json:"action" binding:"required"`
	IsSystem    bool   `json:"is_system"`
}
