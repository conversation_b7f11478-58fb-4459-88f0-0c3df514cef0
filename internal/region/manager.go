package region

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"github.com/your-org/go-kuaidi/internal/config"
	"go.uber.org/zap"
)

// RegionManager 地区管理器
type RegionManager struct {
	configManager *config.ConfigManager
	logger        *zap.Logger
	regions       *RegionData
	mu            sync.RWMutex
}

// AreaCascaderResponse 地区级联响应（匹配现有JSON格式）
type AreaCascaderResponse struct {
	Code int            `json:"code"`
	Msg  string         `json:"msg"`
	Data []AreaProvince `json:"data"`
	Tid  any            `json:"tid"`
}

// AreaProvince 省份（匹配现有JSON格式）
type AreaProvince struct {
	Value    string     `json:"value"`
	Label    string     `json:"label"`
	Children []AreaCity `json:"children"`
	Priority int        `json:"priority,omitempty"` // 优先级：1-一线，2-二线，3-三线
}

// AreaCity 城市（匹配现有JSON格式）
type AreaCity struct {
	Value    string         `json:"value"`
	Label    string         `json:"label"`
	Children []AreaDistrict `json:"children"`
	Priority int            `json:"priority,omitempty"` // 优先级：1-一线，2-二线，3-三线
}

// AreaDistrict 区县（匹配现有JSON格式）
type AreaDistrict struct {
	Value    string `json:"value"`
	Label    string `json:"label"`
	Children any    `json:"children"` // 通常为null
}

// RegionData 内部使用的地区数据结构
type RegionData struct {
	Provinces []Province `json:"provinces"`
}

// Province 省份
type Province struct {
	Code     string `json:"code"`
	Name     string `json:"name"`
	Cities   []City `json:"cities"`
	Priority int    `json:"priority"` // 优先级：1-一线，2-二线，3-三线
}

// City 城市
type City struct {
	Code      string     `json:"code"`
	Name      string     `json:"name"`
	Districts []District `json:"districts"`
	Priority  int        `json:"priority"` // 优先级：1-一线，2-二线，3-三线
}

// District 区县
type District struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

// RouteTemplate 路线模板（省市级别）
type RouteTemplate struct {
	FromProvince string  `json:"from_province"`
	FromCity     string  `json:"from_city"`
	ToProvince   string  `json:"to_province"`
	ToCity       string  `json:"to_city"`
	Weight       float64 `json:"weight"`
	Quantity     int     `json:"quantity"`
	Priority     int     `json:"priority"`   // 路线优先级
	RouteType    string  `json:"route_type"` // 路线类型：tier1_to_tier1, tier1_to_tier2, etc.
}

// WarmupConfig 预热配置
type WarmupConfig struct {
	// 预热策略
	Strategy WarmupStrategy `mapstructure:"strategy"`

	// 并发控制
	Concurrency ConcurrencyConfig `mapstructure:"concurrency"`

	// 路线配置
	Routes RouteConfig `mapstructure:"routes"`

	// 调度配置
	Schedule ScheduleConfig `mapstructure:"schedule"`
}

// WarmupStrategy 预热策略
type WarmupStrategy struct {
	// 预热模式：full-全量，priority-优先级，smart-智能
	Mode string `mapstructure:"mode"`

	// 批次大小
	BatchSize int `mapstructure:"batch_size"`

	// 批次间隔（毫秒）
	BatchIntervalMs int `mapstructure:"batch_interval_ms"`

	// 最大预热时间（分钟）
	MaxDurationMinutes int `mapstructure:"max_duration_minutes"`

	// 错误重试次数
	MaxRetries int `mapstructure:"max_retries"`
}

// ConcurrencyConfig 并发配置
type ConcurrencyConfig struct {
	// 工作协程数
	WorkerCount int `mapstructure:"worker_count"`

	// 任务队列大小
	QueueSize int `mapstructure:"queue_size"`

	// 单个任务超时（秒）
	TaskTimeoutSeconds int `mapstructure:"task_timeout_seconds"`
}

// RouteConfig 路线配置
type RouteConfig struct {
	// 权重配置
	Weights []WeightConfig `mapstructure:"weights"`

	// 优先级城市
	PriorityCities []string `mapstructure:"priority_cities"`

	// 热门路线
	PopularRoutes []string `mapstructure:"popular_routes"`

	// 排除的路线模式
	ExcludePatterns []string `mapstructure:"exclude_patterns"`
}

// WeightConfig 重量配置
type WeightConfig struct {
	Weight   float64 `mapstructure:"weight"`
	Priority int     `mapstructure:"priority"`
}

// ScheduleConfig 调度配置
type ScheduleConfig struct {
	// 是否启用定时预热
	Enabled bool `mapstructure:"enabled"`

	// 预热时间（cron表达式）
	CronExpression string `mapstructure:"cron_expression"`

	// 预热窗口（小时）
	WindowHours int `mapstructure:"window_hours"`
}

// NewRegionManager 创建地区管理器
func NewRegionManager(configManager *config.ConfigManager, logger *zap.Logger) *RegionManager {
	return &RegionManager{
		configManager: configManager,
		logger:        logger,
	}
}

// LoadRegions 加载地区数据
func (rm *RegionManager) LoadRegions() error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	// 获取地区数据文件路径
	regionDataPath := rm.configManager.GetString("region.data_path")
	if regionDataPath == "" {
		regionDataPath = "config/getAreaCascaderVo.json"
	}

	// 确保路径是绝对路径
	if !filepath.IsAbs(regionDataPath) {
		workDir, err := os.Getwd()
		if err == nil {
			regionDataPath = filepath.Join(workDir, regionDataPath)
		}
	}

	// 读取地区数据文件
	data, err := os.ReadFile(regionDataPath)
	if err != nil {
		// 如果文件不存在，生成默认地区数据
		rm.logger.Warn("Region data file not found, generating default data",
			zap.String("path", regionDataPath))
		rm.regions = rm.generateDefaultRegions()
		return nil
	}

	// 解析地区数据
	var areaResponse AreaCascaderResponse
	if err := json.Unmarshal(data, &areaResponse); err != nil {
		return fmt.Errorf("failed to parse region data: %w", err)
	}

	// 转换为内部数据结构
	regions := rm.convertAreaDataToRegions(&areaResponse)
	rm.regions = regions

	rm.logger.Info("Region data loaded successfully",
		zap.Int("provinces", len(regions.Provinces)),
		zap.String("path", regionDataPath))

	return nil
}

// convertAreaDataToRegions 转换地区数据格式
func (rm *RegionManager) convertAreaDataToRegions(areaResponse *AreaCascaderResponse) *RegionData {
	var provinces []Province

	// 定义城市优先级映射
	tier1Cities := map[string]int{
		"北京市": 1, "上海市": 1, "广州市": 1, "深圳市": 1,
	}
	tier2Cities := map[string]int{
		"杭州市": 2, "南京市": 2, "苏州市": 2, "成都市": 2, "武汉市": 2, "西安市": 2,
		"天津市": 2, "重庆市": 2, "青岛市": 2, "大连市": 2, "宁波市": 2, "厦门市": 2,
	}

	// 定义省份优先级映射
	tier1Provinces := map[string]int{
		"北京市": 1, "上海市": 1, "广东省": 1,
	}
	tier2Provinces := map[string]int{
		"江苏省": 2, "浙江省": 2, "山东省": 2, "四川省": 2, "湖北省": 2, "陕西省": 2,
		"天津市": 2, "重庆市": 2, "福建省": 2, "辽宁省": 2,
	}

	for _, areaProvince := range areaResponse.Data {
		// 确定省份优先级
		provincePriority := 3 // 默认三线
		if priority, exists := tier1Provinces[areaProvince.Label]; exists {
			provincePriority = priority
		} else if priority, exists := tier2Provinces[areaProvince.Label]; exists {
			provincePriority = priority
		}

		var cities []City
		for _, areaCity := range areaProvince.Children {
			// 确定城市优先级
			cityPriority := 3 // 默认三线
			if priority, exists := tier1Cities[areaCity.Label]; exists {
				cityPriority = priority
			} else if priority, exists := tier2Cities[areaCity.Label]; exists {
				cityPriority = priority
			}

			var districts []District
			for _, areaDistrict := range areaCity.Children {
				districts = append(districts, District{
					Code: areaDistrict.Value,
					Name: areaDistrict.Label,
				})
			}

			cities = append(cities, City{
				Code:      areaCity.Value,
				Name:      areaCity.Label,
				Districts: districts,
				Priority:  cityPriority,
			})
		}

		provinces = append(provinces, Province{
			Code:     areaProvince.Value,
			Name:     areaProvince.Label,
			Cities:   cities,
			Priority: provincePriority,
		})
	}

	return &RegionData{
		Provinces: provinces,
	}
}

// generateDefaultRegions 生成默认地区数据
func (rm *RegionManager) generateDefaultRegions() *RegionData {
	return &RegionData{
		Provinces: []Province{
			{
				Code: "110000", Name: "北京市", Priority: 1,
				Cities: []City{
					{
						Code: "110100", Name: "北京市", Priority: 1,
						Districts: []District{
							{Code: "110101", Name: "东城区"},
							{Code: "110102", Name: "西城区"},
							{Code: "110105", Name: "朝阳区"},
							{Code: "110106", Name: "丰台区"},
							{Code: "110107", Name: "石景山区"},
							{Code: "110108", Name: "海淀区"},
						},
					},
				},
			},
			{
				Code: "310000", Name: "上海市", Priority: 1,
				Cities: []City{
					{
						Code: "310100", Name: "上海市", Priority: 1,
						Districts: []District{
							{Code: "310101", Name: "黄浦区"},
							{Code: "310104", Name: "徐汇区"},
							{Code: "310105", Name: "长宁区"},
							{Code: "310106", Name: "静安区"},
							{Code: "310107", Name: "普陀区"},
							{Code: "310115", Name: "浦东新区"},
						},
					},
				},
			},
			{
				Code: "440000", Name: "广东省", Priority: 1,
				Cities: []City{
					{
						Code: "440100", Name: "广州市", Priority: 1,
						Districts: []District{
							{Code: "440103", Name: "荔湾区"},
							{Code: "440104", Name: "越秀区"},
							{Code: "440105", Name: "海珠区"},
							{Code: "440106", Name: "天河区"},
						},
					},
					{
						Code: "440300", Name: "深圳市", Priority: 1,
						Districts: []District{
							{Code: "440303", Name: "罗湖区"},
							{Code: "440304", Name: "福田区"},
							{Code: "440305", Name: "南山区"},
							{Code: "440307", Name: "龙岗区"},
						},
					},
				},
			},
			{
				Code: "320000", Name: "江苏省", Priority: 2,
				Cities: []City{
					{
						Code: "320100", Name: "南京市", Priority: 2,
						Districts: []District{
							{Code: "320102", Name: "玄武区"},
							{Code: "320104", Name: "秦淮区"},
							{Code: "320105", Name: "建邺区"},
							{Code: "320106", Name: "鼓楼区"},
						},
					},
					{
						Code: "320500", Name: "苏州市", Priority: 2,
						Districts: []District{
							{Code: "320505", Name: "虎丘区"},
							{Code: "320506", Name: "吴中区"},
							{Code: "320507", Name: "相城区"},
						},
					},
				},
			},
			{
				Code: "330000", Name: "浙江省", Priority: 2,
				Cities: []City{
					{
						Code: "330100", Name: "杭州市", Priority: 2,
						Districts: []District{
							{Code: "330102", Name: "上城区"},
							{Code: "330105", Name: "拱墅区"},
							{Code: "330106", Name: "西湖区"},
							{Code: "330108", Name: "滨江区"},
						},
					},
				},
			},
		},
	}
}

// GetWarmupConfig 获取预热配置
func (rm *RegionManager) GetWarmupConfig() *WarmupConfig {
	perfConfig := rm.configManager.GetEnhancedPerformanceConfig()

	return &WarmupConfig{
		Strategy: WarmupStrategy{
			Mode:               rm.configManager.GetString("warmup.strategy.mode"),
			BatchSize:          rm.configManager.GetInt("warmup.strategy.batch_size"),
			BatchIntervalMs:    rm.configManager.GetInt("warmup.strategy.batch_interval_ms"),
			MaxDurationMinutes: rm.configManager.GetInt("warmup.strategy.max_duration_minutes"),
			MaxRetries:         rm.configManager.GetInt("warmup.strategy.max_retries"),
		},
		Concurrency: ConcurrencyConfig{
			WorkerCount:        perfConfig.Concurrency.WorkerPoolSize,
			QueueSize:          perfConfig.Concurrency.TaskQueueSize,
			TaskTimeoutSeconds: perfConfig.Timeout.TotalQueryMs / 1000,
		},
		Routes: RouteConfig{
			Weights: []WeightConfig{
				{Weight: 1.0, Priority: 1},
				{Weight: 2.0, Priority: 2},
				{Weight: 5.0, Priority: 3},
			},
			PriorityCities: rm.configManager.GetStringSlice("warmup.routes.priority_cities"),
			PopularRoutes:  rm.configManager.GetStringSlice("warmup.routes.popular_routes"),
		},
		Schedule: ScheduleConfig{
			Enabled:        rm.configManager.GetBool("warmup.schedule.enabled"),
			CronExpression: rm.configManager.GetString("warmup.schedule.cron_expression"),
			WindowHours:    rm.configManager.GetInt("warmup.schedule.window_hours"),
		},
	}
}

// GenerateRouteTemplates 生成路线模板
func (rm *RegionManager) GenerateRouteTemplates(strategy string) ([]RouteTemplate, error) {
	rm.mu.RLock()
	defer rm.mu.RUnlock()

	if rm.regions == nil {
		return nil, fmt.Errorf("regions not loaded")
	}

	config := rm.GetWarmupConfig()
	var templates []RouteTemplate

	switch strategy {
	case "full":
		templates = rm.generateFullRoutes(config)
	case "priority":
		templates = rm.generatePriorityRoutes(config)
	case "smart":
		templates = rm.generateSmartRoutes(config)
	default:
		templates = rm.generatePriorityRoutes(config)
	}

	rm.logger.Info("Generated route templates",
		zap.String("strategy", strategy),
		zap.Int("count", len(templates)))

	return templates, nil
}

// generatePriorityRoutes 生成优先级路线（省市级别，双向预热）
func (rm *RegionManager) generatePriorityRoutes(config *WarmupConfig) []RouteTemplate {
	var templates []RouteTemplate

	// 一线城市间的路线（双向预热）
	tier1Cities := rm.getTierCities(1)
	for _, fromCity := range tier1Cities {
		for _, toCity := range tier1Cities {
			if fromCity.Code != toCity.Code {
				for _, weight := range config.Routes.Weights {
					// A -> B 方向
					template1 := RouteTemplate{
						FromProvince: fromCity.Province,
						FromCity:     fromCity.Name,
						ToProvince:   toCity.Province,
						ToCity:       toCity.Name,
						Weight:       weight.Weight,
						Quantity:     1,
						Priority:     1,
						RouteType:    "tier1_to_tier1",
					}
					templates = append(templates, template1)

					// B -> A 方向（双向预热，因为价格可能不同）
					template2 := RouteTemplate{
						FromProvince: toCity.Province,
						FromCity:     toCity.Name,
						ToProvince:   fromCity.Province,
						ToCity:       fromCity.Name,
						Weight:       weight.Weight,
						Quantity:     1,
						Priority:     1,
						RouteType:    "tier1_to_tier1_reverse",
					}
					templates = append(templates, template2)
				}
			}
		}
	}

	// 一线到二线城市的路线（双向预热）
	tier2Cities := rm.getTierCities(2)
	for _, fromCity := range tier1Cities {
		for _, toCity := range tier2Cities {
			// 一线 -> 二线
			template1 := RouteTemplate{
				FromProvince: fromCity.Province,
				FromCity:     fromCity.Name,
				ToProvince:   toCity.Province,
				ToCity:       toCity.Name,
				Weight:       1.0,
				Quantity:     1,
				Priority:     2,
				RouteType:    "tier1_to_tier2",
			}
			templates = append(templates, template1)

			// 二线 -> 一线（反向，价格可能不同）
			template2 := RouteTemplate{
				FromProvince: toCity.Province,
				FromCity:     toCity.Name,
				ToProvince:   fromCity.Province,
				ToCity:       fromCity.Name,
				Weight:       1.0,
				Quantity:     1,
				Priority:     2,
				RouteType:    "tier2_to_tier1",
			}
			templates = append(templates, template2)
		}
	}

	// 二线城市间的重要路线
	for i, fromCity := range tier2Cities {
		for j, toCity := range tier2Cities {
			if i != j && fromCity.Province != toCity.Province { // 跨省的二线城市
				template := RouteTemplate{
					FromProvince: fromCity.Province,
					FromCity:     fromCity.Name,
					ToProvince:   toCity.Province,
					ToCity:       toCity.Name,
					Weight:       1.0,
					Quantity:     1,
					Priority:     3,
					RouteType:    "tier2_to_tier2",
				}
				templates = append(templates, template)
			}
		}
	}

	return templates
}

// CityInfo 城市信息
type CityInfo struct {
	Code      string
	Name      string
	Province  string
	Districts []District
}

// getTierCities 获取指定等级的城市
func (rm *RegionManager) getTierCities(tier int) []CityInfo {
	var cities []CityInfo

	for _, province := range rm.regions.Provinces {
		if province.Priority <= tier {
			for _, city := range province.Cities {
				if city.Priority <= tier {
					cities = append(cities, CityInfo{
						Code:      city.Code,
						Name:      city.Name,
						Province:  province.Name,
						Districts: city.Districts,
					})
				}
			}
		}
	}

	return cities
}

// generateFullRoutes 生成全量路线（所有省市组合）
func (rm *RegionManager) generateFullRoutes(config *WarmupConfig) []RouteTemplate {
	var templates []RouteTemplate

	// 获取所有城市
	allCities := rm.getAllCities()

	// 生成所有城市间的路线组合
	for _, fromCity := range allCities {
		for _, toCity := range allCities {
			if fromCity.Code != toCity.Code && fromCity.Province != toCity.Province { // 跨省路线
				template := RouteTemplate{
					FromProvince: fromCity.Province,
					FromCity:     fromCity.Name,
					ToProvince:   toCity.Province,
					ToCity:       toCity.Name,
					Weight:       1.0, // 标准重量
					Quantity:     1,
					Priority:     4, // 全量路线优先级较低
					RouteType:    "full_coverage",
				}
				templates = append(templates, template)
			}
		}
	}

	rm.logger.Info("Generated full routes", zap.Int("count", len(templates)))
	return templates
}

// generateSmartRoutes 生成智能路线（基于热门路线和经济发达地区）
func (rm *RegionManager) generateSmartRoutes(config *WarmupConfig) []RouteTemplate {
	var templates []RouteTemplate

	// 获取热门城市（一二线城市）
	hotCities := append(rm.getTierCities(1), rm.getTierCities(2)...)

	// 热门城市间的路线
	for _, fromCity := range hotCities {
		for _, toCity := range hotCities {
			if fromCity.Code != toCity.Code {
				// 多重量预热
				weights := []float64{1.0, 2.0, 5.0}
				for _, weight := range weights {
					template := RouteTemplate{
						FromProvince: fromCity.Province,
						FromCity:     fromCity.Name,
						ToProvince:   toCity.Province,
						ToCity:       toCity.Name,
						Weight:       weight,
						Quantity:     1,
						Priority:     1,
						RouteType:    "smart_hot",
					}
					templates = append(templates, template)
				}
			}
		}
	}

	// 添加经济发达省份的省会城市路线
	economicProvinces := []string{"广东省", "江苏省", "浙江省", "山东省", "河南省", "四川省", "湖北省", "福建省", "湖南省", "安徽省"}
	capitalCities := rm.getCapitalCities(economicProvinces)

	for _, fromCity := range capitalCities {
		for _, toCity := range capitalCities {
			if fromCity.Code != toCity.Code && fromCity.Province != toCity.Province {
				template := RouteTemplate{
					FromProvince: fromCity.Province,
					FromCity:     fromCity.Name,
					ToProvince:   toCity.Province,
					ToCity:       toCity.Name,
					Weight:       1.0,
					Quantity:     1,
					Priority:     2,
					RouteType:    "smart_economic",
				}
				templates = append(templates, template)
			}
		}
	}

	rm.logger.Info("Generated smart routes", zap.Int("count", len(templates)))
	return templates
}

// getAllCities 获取所有城市
func (rm *RegionManager) getAllCities() []CityInfo {
	var cities []CityInfo

	for _, province := range rm.regions.Provinces {
		for _, city := range province.Cities {
			cities = append(cities, CityInfo{
				Code:      city.Code,
				Name:      city.Name,
				Province:  province.Name,
				Districts: city.Districts,
			})
		}
	}

	return cities
}

// getCapitalCities 获取指定省份的省会城市
func (rm *RegionManager) getCapitalCities(provinceNames []string) []CityInfo {
	var cities []CityInfo

	// 省会城市映射
	capitalMap := map[string]string{
		"广东省": "广州市",
		"江苏省": "南京市",
		"浙江省": "杭州市",
		"山东省": "济南市",
		"河南省": "郑州市",
		"四川省": "成都市",
		"湖北省": "武汉市",
		"福建省": "福州市",
		"湖南省": "长沙市",
		"安徽省": "合肥市",
	}

	for _, province := range rm.regions.Provinces {
		if capitalName, exists := capitalMap[province.Name]; exists {
			for _, city := range province.Cities {
				if city.Name == capitalName {
					cities = append(cities, CityInfo{
						Code:      city.Code,
						Name:      city.Name,
						Province:  province.Name,
						Districts: city.Districts,
					})
					break
				}
			}
		}
	}

	return cities
}
