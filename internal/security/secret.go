package security

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"os"
)

var encKey []byte

func init() {
	keyStr := os.Getenv("CLIENT_ENC_KEY")
	if len(keyStr) == 0 {
		// 开发环境使用固定演示密钥(32字节)。生产必须显式配置。
		keyStr = "0123456789abcdef0123456789abcdef"
	}
	if len(keyStr) != 32 {
		panic("CLIENT_ENC_KEY 必须是32字节长度的Base64或明文字符串")
	}
	encKey = []byte(keyStr)
}

// EncryptSecret AES-GCM 加密并返回 base64 值
func EncryptSecret(plain string) (string, error) {
	block, err := aes.NewCipher(encKey)
	if err != nil {
		return "", err
	}
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}
	cipherText := gcm.Seal(nonce, nonce, []byte(plain), nil)
	return base64.StdEncoding.EncodeToString(cipherText), nil
}

// DecryptSecret 解密 base64 编码密文
func DecryptSecret(cipherB64 string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(cipherB64)
	if err != nil {
		return "", err
	}
	block, err := aes.NewCipher(encKey)
	if err != nil {
		return "", err
	}
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	if len(data) < gcm.NonceSize() {
		return "", fmt.Errorf("cipher too short")
	}
	nonce, ciphertext := data[:gcm.NonceSize()], data[gcm.NonceSize():]
	plain, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}
	return string(plain), nil
}
