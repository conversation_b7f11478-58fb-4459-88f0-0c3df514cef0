package security

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

var (
	// ErrRateLimitExceeded 速率限制超出错误
	ErrRateLimitExceeded = errors.New("rate limit exceeded")

	// ErrRedisTimeout Redis超时错误
	ErrRedisTimeout = errors.New("redis operation timeout")
)

// RateLimitResult 速率限制结果
type RateLimitResult struct {
	// 是否允许请求
	Allowed bool

	// 当前计数
	CurrentCount int64

	// 限制
	Limit int

	// 重试等待时间
	RetryAfter time.Duration

	// 剩余请求数
	Remaining int
}

// RateLimitDimension 速率限制维度
type RateLimitDimension string

const (
	// DimensionIP IP维度
	DimensionIP RateLimitDimension = "ip"

	// DimensionClient 客户端维度
	DimensionClient RateLimitDimension = "client"

	// DimensionUser 用户维度
	DimensionUser RateLimitDimension = "user"
)

// RateLimitService 速率限制服务
type RateLimitService interface {
	// CheckRateLimit 检查速率限制
	CheckRateLimit(ctx context.Context, dimension RateLimitDimension, id string, path string) (*RateLimitResult, error)

	// GetPathConfig 获取路径配置
	GetPathConfig(path string) *PathRateLimit

	// IsEnabled 检查是否启用速率限制
	IsEnabled() bool
}

// PathRateLimit 路径速率限制
type PathRateLimit struct {
	// 限制次数
	Limit int

	// 时间周期
	Period time.Duration
}

// DefaultRateLimitService 默认速率限制服务实现
type DefaultRateLimitService struct {
	redisClient *redis.Client
	config      RateLimitConfig
	pathLimits  map[string]*PathRateLimit
}

// NewRateLimitService 创建新的速率限制服务
func NewRateLimitService(redisClient *redis.Client, config RateLimitConfig) RateLimitService {
	// 创建路径限制映射
	pathLimits := make(map[string]*PathRateLimit)
	for path, limit := range config.PathLimits {
		pathLimits[path] = &PathRateLimit{
			Limit:  limit.Limit,
			Period: time.Duration(limit.PeriodSeconds) * time.Second,
		}
	}

	return &DefaultRateLimitService{
		redisClient: redisClient,
		config:      config,
		pathLimits:  pathLimits,
	}
}

// IsEnabled 检查是否启用速率限制
func (s *DefaultRateLimitService) IsEnabled() bool {
	return s.config.Enabled
}

// GetPathConfig 获取路径配置
func (s *DefaultRateLimitService) GetPathConfig(path string) *PathRateLimit {
	// 检查是否有特定路径配置
	if limit, exists := s.pathLimits[path]; exists {
		return limit
	}

	// 返回默认配置
	return &PathRateLimit{
		Limit:  s.config.DefaultLimit,
		Period: time.Duration(s.config.DefaultPeriodSeconds) * time.Second,
	}
}

// CheckRateLimit 检查速率限制
func (s *DefaultRateLimitService) CheckRateLimit(ctx context.Context, dimension RateLimitDimension, id string, path string) (*RateLimitResult, error) {
	// 如果未启用速率限制，直接放行
	if !s.IsEnabled() {
		return &RateLimitResult{
			Allowed:      true,
			CurrentCount: 0,
			Limit:        0,
			RetryAfter:   0,
			Remaining:    0,
		}, nil
	}

	// 获取路径配置
	pathConfig := s.GetPathConfig(path)

	// 根据维度调整限制
	limit := pathConfig.Limit
	if dimension == DimensionClient {
		limit = int(float64(limit) * s.config.ClientMultiplier)
	} else if dimension == DimensionUser {
		limit = int(float64(limit) * s.config.UserMultiplier)
	}

	// 构建Redis键
	key := fmt.Sprintf("%s%s:%s:%s", s.config.RedisKeyPrefix, dimension, id, path)

	// 创建带超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(s.config.RedisTimeoutSeconds)*time.Second)
	defer cancel()

	// 使用Redis的INCR和EXPIRE原子操作
	pipe := s.redisClient.Pipeline()
	incr := pipe.Incr(timeoutCtx, key)
	pipe.Expire(timeoutCtx, key, pathConfig.Period)

	// 执行管道操作
	_, err := pipe.Exec(timeoutCtx)
	if err != nil {
		// 检查是否是超时错误
		if err == context.DeadlineExceeded {
			return nil, ErrRedisTimeout
		}

		// 其他Redis错误，放行请求
		return &RateLimitResult{
			Allowed:      true,
			CurrentCount: 0,
			Limit:        limit,
			RetryAfter:   0,
			Remaining:    limit,
		}, nil
	}

	// 获取当前计数
	count := incr.Val()

	// 计算剩余请求数
	remaining := limit - int(count)
	if remaining < 0 {
		remaining = 0
	}

	// 检查是否超过限制
	if count > int64(limit) {
		// 获取键的剩余生存时间
		ttl, err := s.redisClient.TTL(timeoutCtx, key).Result()
		if err != nil {
			ttl = pathConfig.Period // 如果获取TTL失败，使用配置的周期
		}

		// 返回限制结果
		return &RateLimitResult{
			Allowed:      false,
			CurrentCount: count,
			Limit:        limit,
			RetryAfter:   ttl,
			Remaining:    0,
		}, ErrRateLimitExceeded
	}

	// 请求未超过限制
	return &RateLimitResult{
		Allowed:      true,
		CurrentCount: count,
		Limit:        limit,
		RetryAfter:   0,
		Remaining:    remaining,
	}, nil
}

// ForceCheckRateLimit 强制检查速率限制（忽略全局启用状态，用于登录接口等安全敏感接口）
func (s *DefaultRateLimitService) ForceCheckRateLimit(ctx context.Context, dimension RateLimitDimension, id string, path string) (*RateLimitResult, error) {
	// 获取路径配置
	pathConfig := s.GetPathConfig(path)

	// 根据维度调整限制
	limit := pathConfig.Limit
	if dimension == DimensionClient {
		limit = int(float64(limit) * s.config.ClientMultiplier)
	} else if dimension == DimensionUser {
		limit = int(float64(limit) * s.config.UserMultiplier)
	}

	// 构建Redis键
	key := fmt.Sprintf("%s%s:%s:%s", s.config.RedisKeyPrefix, dimension, id, path)

	// 创建带超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(s.config.RedisTimeoutSeconds)*time.Second)
	defer cancel()

	// 使用Redis的INCR和EXPIRE原子操作
	pipe := s.redisClient.Pipeline()
	incr := pipe.Incr(timeoutCtx, key)
	pipe.Expire(timeoutCtx, key, pathConfig.Period)

	// 执行管道操作
	_, err := pipe.Exec(timeoutCtx)
	if err != nil {
		// 检查是否是超时错误
		if err == context.DeadlineExceeded {
			return nil, ErrRedisTimeout
		}

		// 其他Redis错误，放行请求
		return &RateLimitResult{
			Allowed:      true,
			CurrentCount: 0,
			Limit:        limit,
			RetryAfter:   0,
			Remaining:    limit,
		}, nil
	}

	// 获取当前计数
	count := incr.Val()

	// 计算剩余请求数
	remaining := limit - int(count)
	if remaining < 0 {
		remaining = 0
	}

	// 检查是否超过限制
	if count > int64(limit) {
		// 获取键的剩余生存时间
		ttl, err := s.redisClient.TTL(timeoutCtx, key).Result()
		if err != nil {
			ttl = pathConfig.Period // 如果获取TTL失败，使用配置的周期
		}

		// 返回限制结果
		return &RateLimitResult{
			Allowed:      false,
			CurrentCount: count,
			Limit:        limit,
			RetryAfter:   ttl,
			Remaining:    0,
		}, ErrRateLimitExceeded
	}

	// 请求未超过限制
	return &RateLimitResult{
		Allowed:      true,
		CurrentCount: count,
		Limit:        limit,
		RetryAfter:   0,
		Remaining:    remaining,
	}, nil
}
