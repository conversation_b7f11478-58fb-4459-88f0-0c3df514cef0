package security

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

const (
	// 安全常量
	MaxLoginAttempts    = 5                // 最大登录尝试次数
	LockoutDuration     = 30 * time.Minute // 账户锁定时间
	CleanupInterval     = 10 * time.Minute // 清理过期记录的间隔
	RateLimitWindow     = 5 * time.Minute  // 限流时间窗口
	RateLimitMaxReqs    = 5                // 限流时间窗口内最大请求数
)

// LoginAttempt 登录尝试记录
type LoginAttempt struct {
	Username  string    `json:"username"`
	ClientIP  string    `json:"client_ip"`
	Timestamp time.Time `json:"timestamp"`
	Success   bool      `json:"success"`
	UserAgent string    `json:"user_agent"`
}

// AccountLockInfo 账户锁定信息
type AccountLockInfo struct {
	Username     string    `json:"username"`
	ClientIP     string    `json:"client_ip"`
	LockTime     time.Time `json:"lock_time"`
	UnlockTime   time.Time `json:"unlock_time"`
	FailedCount  int       `json:"failed_count"`
	LastAttempt  time.Time `json:"last_attempt"`
}

// LoginSecurityService 登录安全服务
type LoginSecurityService struct {
	logger        *zap.Logger
	attempts      map[string][]LoginAttempt // key: username:clientIP
	locks         map[string]*AccountLockInfo
	rateLimits    map[string][]time.Time // key: clientIP
	mutex         sync.RWMutex
	cleanupTicker *time.Ticker
	ctx           context.Context
	cancel        context.CancelFunc
}

// NewLoginSecurityService 创建登录安全服务
func NewLoginSecurityService(logger *zap.Logger) *LoginSecurityService {
	ctx, cancel := context.WithCancel(context.Background())
	
	service := &LoginSecurityService{
		logger:     logger,
		attempts:   make(map[string][]LoginAttempt),
		locks:      make(map[string]*AccountLockInfo),
		rateLimits: make(map[string][]time.Time),
		ctx:        ctx,
		cancel:     cancel,
	}

	// 启动清理协程
	service.startCleanupRoutine()
	
	return service
}

// IsRateLimited 检查IP是否被限流
func (s *LoginSecurityService) IsRateLimited(clientIP string) bool {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()
	key := clientIP

	// 清理过期记录
	if times, exists := s.rateLimits[key]; exists {
		var validTimes []time.Time
		for _, t := range times {
			if now.Sub(t) < RateLimitWindow {
				validTimes = append(validTimes, t)
			}
		}
		s.rateLimits[key] = validTimes
	}

	// 检查是否超过限制
	if len(s.rateLimits[key]) >= RateLimitMaxReqs {
		s.logger.Warn("Rate limit exceeded",
			zap.String("client_ip", clientIP),
			zap.Int("attempts", len(s.rateLimits[key])))
		return true
	}

	// 记录当前请求
	s.rateLimits[key] = append(s.rateLimits[key], now)
	return false
}

// IsAccountLocked 检查账户是否被锁定
func (s *LoginSecurityService) IsAccountLocked(username, clientIP string) bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	key := s.getLockKey(username, clientIP)
	lockInfo, exists := s.locks[key]
	
	if !exists {
		return false
	}

	// 检查锁定是否已过期
	if time.Now().After(lockInfo.UnlockTime) {
		// 锁定已过期，异步清理
		go s.clearLock(key)
		return false
	}

	s.logger.Info("Account is locked",
		zap.String("username", username),
		zap.String("client_ip", clientIP),
		zap.Time("unlock_time", lockInfo.UnlockTime),
		zap.Int("failed_count", lockInfo.FailedCount))

	return true
}

// RecordFailedAttempt 记录失败尝试
func (s *LoginSecurityService) RecordFailedAttempt(username, clientIP, userAgent string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()
	key := s.getAttemptKey(username, clientIP)
	lockKey := s.getLockKey(username, clientIP)

	// 记录失败尝试
	attempt := LoginAttempt{
		Username:  username,
		ClientIP:  clientIP,
		Timestamp: now,
		Success:   false,
		UserAgent: userAgent,
	}

	s.attempts[key] = append(s.attempts[key], attempt)

	// 清理过期的尝试记录
	s.cleanupExpiredAttempts(key)

	// 检查是否需要锁定账户
	failedCount := len(s.attempts[key])
	if failedCount >= MaxLoginAttempts {
		lockInfo := &AccountLockInfo{
			Username:    username,
			ClientIP:    clientIP,
			LockTime:    now,
			UnlockTime:  now.Add(LockoutDuration),
			FailedCount: failedCount,
			LastAttempt: now,
		}

		s.locks[lockKey] = lockInfo

		s.logger.Warn("Account locked due to too many failed attempts",
			zap.String("username", username),
			zap.String("client_ip", clientIP),
			zap.Int("failed_count", failedCount),
			zap.Time("unlock_time", lockInfo.UnlockTime))

		// 清理失败尝试记录（已锁定）
		delete(s.attempts, key)
	} else {
		s.logger.Info("Failed login attempt recorded",
			zap.String("username", username),
			zap.String("client_ip", clientIP),
			zap.Int("failed_count", failedCount),
			zap.Int("remaining_attempts", MaxLoginAttempts-failedCount))
	}
}

// ClearFailedAttempts 清除失败尝试记录
func (s *LoginSecurityService) ClearFailedAttempts(username, clientIP string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	key := s.getAttemptKey(username, clientIP)
	delete(s.attempts, key)

	s.logger.Info("Cleared failed login attempts",
		zap.String("username", username),
		zap.String("client_ip", clientIP))
}

// GetFailedAttemptCount 获取失败尝试次数
func (s *LoginSecurityService) GetFailedAttemptCount(username, clientIP string) int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	key := s.getAttemptKey(username, clientIP)
	return len(s.attempts[key])
}

// GetLockInfo 获取锁定信息
func (s *LoginSecurityService) GetLockInfo(username, clientIP string) *AccountLockInfo {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	key := s.getLockKey(username, clientIP)
	if lockInfo, exists := s.locks[key]; exists {
		// 返回副本以避免并发修改
		info := *lockInfo
		return &info
	}
	return nil
}

// Close 关闭服务
func (s *LoginSecurityService) Close() {
	if s.cancel != nil {
		s.cancel()
	}
	if s.cleanupTicker != nil {
		s.cleanupTicker.Stop()
	}
}

// 私有方法

func (s *LoginSecurityService) getAttemptKey(username, clientIP string) string {
	return fmt.Sprintf("%s:%s", username, clientIP)
}

func (s *LoginSecurityService) getLockKey(username, clientIP string) string {
	return fmt.Sprintf("lock:%s:%s", username, clientIP)
}

func (s *LoginSecurityService) clearLock(key string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	delete(s.locks, key)
}

func (s *LoginSecurityService) cleanupExpiredAttempts(key string) {
	now := time.Now()
	attempts := s.attempts[key]
	
	var validAttempts []LoginAttempt
	for _, attempt := range attempts {
		if now.Sub(attempt.Timestamp) < LockoutDuration {
			validAttempts = append(validAttempts, attempt)
		}
	}
	
	s.attempts[key] = validAttempts
}

func (s *LoginSecurityService) startCleanupRoutine() {
	s.cleanupTicker = time.NewTicker(CleanupInterval)
	
	go func() {
		for {
			select {
			case <-s.ctx.Done():
				return
			case <-s.cleanupTicker.C:
				s.performCleanup()
			}
		}
	}()
}

func (s *LoginSecurityService) performCleanup() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now()

	// 清理过期的失败尝试记录
	for key := range s.attempts {
		s.cleanupExpiredAttempts(key)
		if len(s.attempts[key]) == 0 {
			delete(s.attempts, key)
		}
	}

	// 清理过期的锁定记录
	for key, lockInfo := range s.locks {
		if now.After(lockInfo.UnlockTime) {
			delete(s.locks, key)
		}
	}

	// 清理过期的限流记录
	for key, times := range s.rateLimits {
		var validTimes []time.Time
		for _, t := range times {
			if now.Sub(t) < RateLimitWindow {
				validTimes = append(validTimes, t)
			}
		}
		if len(validTimes) == 0 {
			delete(s.rateLimits, key)
		} else {
			s.rateLimits[key] = validTimes
		}
	}

	s.logger.Debug("Security cleanup completed",
		zap.Int("active_attempts", len(s.attempts)),
		zap.Int("active_locks", len(s.locks)),
		zap.Int("active_rate_limits", len(s.rateLimits)))
}
