package security

// SecurityConfig 安全配置
type SecurityConfig struct {
	// 签名配置
	Signature SignatureConfig `json:"signature"`

	// 速率限制配置
	RateLimit RateLimitConfig `json:"rate_limit"`

	// 审计日志配置
	Audit AuditConfig `json:"audit"`

	// HTTP安全头配置
	Headers HeadersConfig `json:"headers"`
}

// SignatureConfig 签名配置
type SignatureConfig struct {
	// 是否启用签名验证
	Enabled bool `json:"enabled"`

	// 时间戳有效期
	TimestampValiditySeconds int `json:"timestamp_validity_seconds"`

	// Nonce有效期
	NonceValiditySeconds int `json:"nonce_validity_seconds"`

	// 最大请求体大小（字节）
	MaxRequestBodySize int64 `json:"max_request_body_size"`

	// 跳过签名验证的路径
	SkipPaths []string `json:"skip_paths"`

	// 是否在开发环境中禁用签名验证
	DisableInDevelopment bool `json:"disable_in_development"`

	// 🚀 新增：是否禁用nonce验证（保留签名验证但跳过nonce检查）
	DisableNonceValidation bool `json:"disable_nonce_validation"`
}

// RateLimitConfig 速率限制配置
type RateLimitConfig struct {
	// 是否启用速率限制
	Enabled bool `json:"enabled"`

	// Redis键前缀
	RedisKeyPrefix string `json:"redis_key_prefix"`

	// Redis操作超时时间
	RedisTimeoutSeconds int `json:"redis_timeout_seconds"`

	// 默认限制（每分钟请求数）
	DefaultLimit int `json:"default_limit"`

	// 默认时间窗口
	DefaultPeriodSeconds int `json:"default_period_seconds"`

	// 客户端限制倍数（相对于IP限制）
	ClientMultiplier float64 `json:"client_multiplier"`

	// 用户限制倍数（相对于IP限制）
	UserMultiplier float64 `json:"user_multiplier"`

	// 路径特定限制
	PathLimits map[string]PathLimit `json:"path_limits"`
}

// PathLimit 路径特定的速率限制
type PathLimit struct {
	// 限制（每时间窗口请求数）
	Limit int `json:"limit"`

	// 时间窗口（秒）
	PeriodSeconds int `json:"period_seconds"`
}

// AuditConfig 审计日志配置
type AuditConfig struct {
	// 是否启用审计日志
	Enabled bool `json:"enabled"`

	// 异步队列大小
	QueueSize int `json:"queue_size"`

	// 批量保存大小
	BatchSize int `json:"batch_size"`

	// 批量保存间隔（秒）
	BatchIntervalSeconds int `json:"batch_interval_seconds"`

	// 最大请求体记录大小（字节）
	MaxRequestBodySize int `json:"max_request_body_size"`

	// 最大响应体记录大小（字节）
	MaxResponseBodySize int `json:"max_response_body_size"`

	// 敏感字段列表（将被脱敏）
	SensitiveFields []string `json:"sensitive_fields"`

	// 采样率（0-100）
	SamplingRate int `json:"sampling_rate"`

	// 跳过审计的路径
	SkipPaths []string `json:"skip_paths"`

	// 数据保留天数（0表示永久保留）
	RetentionDays int `json:"retention_days"`
}

// HeadersConfig HTTP安全头配置
type HeadersConfig struct {
	// 是否启用安全头
	Enabled bool `json:"enabled"`

	// 是否强制HTTPS
	ForceHTTPS bool `json:"force_https"`

	// HSTS最大年龄（秒）
	HSTSMaxAgeSeconds int `json:"hsts_max_age_seconds"`

	// 是否包含子域名
	HSTSIncludeSubdomains bool `json:"hsts_include_subdomains"`

	// 是否预加载
	HSTSPreload bool `json:"hsts_preload"`

	// X-Frame-Options值
	XFrameOptions string `json:"x_frame_options"`

	// Content-Security-Policy值
	ContentSecurityPolicy string `json:"content_security_policy"`

	// 引荐来源政策
	ReferrerPolicy string `json:"referrer_policy"`

	// 特性策略
	FeaturePolicy string `json:"feature_policy"`
}

// DefaultSecurityConfig 返回默认安全配置 - 🔒 企业级强制安全策略
func DefaultSecurityConfig() *SecurityConfig {
	return &SecurityConfig{
		Signature: SignatureConfig{
			Enabled:                  true,        // 🔒 强制启用签名验证
			TimestampValiditySeconds: 1800,        // 🚀 优化：30分钟，适应高并发和网络延迟
			NonceValiditySeconds:     1800,        // 🚀 优化：30分钟
			MaxRequestBodySize:       1024 * 1024, // 1MB
			SkipPaths: []string{
				"/health",      // 健康检查
				"/oauth/token", // OAuth token获取（使用client_secret验证）
			},
			DisableInDevelopment:   false, // 🔒 所有环境都强制启用签名验证
			DisableNonceValidation: false, // 🔒 默认启用nonce验证
		},
		RateLimit: RateLimitConfig{
			Enabled:              true,
			RedisKeyPrefix:       "ratelimit:",
			RedisTimeoutSeconds:  3,
			DefaultLimit:         60,
			DefaultPeriodSeconds: 60,
			ClientMultiplier:     2.0,
			UserMultiplier:       3.0,
			PathLimits: map[string]PathLimit{
				"/oauth/token": {
					Limit:         10,
					PeriodSeconds: 60,
				},
				"/api/v1/users/register": {
					Limit:         5,
					PeriodSeconds: 60,
				},
				"/api/v1/users/reset-client-secret": {
					Limit:         3,
					PeriodSeconds: 60,
				},
				"/api/v1/express/price": {
					Limit:         30,
					PeriodSeconds: 60,
				},
				"/api/v1/express/order": {
					Limit:         20,
					PeriodSeconds: 60,
				},
			},
		},
		Audit: AuditConfig{
			Enabled:              true,
			QueueSize:            1000,
			BatchSize:            100,
			BatchIntervalSeconds: 5,
			MaxRequestBodySize:   4096,
			MaxResponseBodySize:  4096,
			SamplingRate:         100,
			RetentionDays:        90,
			SensitiveFields:      []string{"password", "client_secret", "token", "access_token", "refresh_token"},
			SkipPaths:            []string{"/health"},
		},
		Headers: HeadersConfig{
			Enabled:               true,
			ForceHTTPS:            true,
			HSTSMaxAgeSeconds:     31536000, // 1年
			HSTSIncludeSubdomains: true,
			HSTSPreload:           false,
			XFrameOptions:         "DENY",
			ContentSecurityPolicy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; font-src 'self'; frame-src 'none'; connect-src 'self'",
			ReferrerPolicy:        "strict-origin-when-cross-origin",
			FeaturePolicy:         "camera 'none'; microphone 'none'; geolocation 'none'",
		},
	}
}
