package security

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/util"
)

var (
	// ErrRequestBodyTooLarge 请求体过大错误
	ErrRequestBodyTooLarge = errors.New("request body too large")

	// ErrInvalidSignature 无效签名错误
	ErrInvalidSignature = errors.New("invalid signature")

	// ErrMissingSignatureParams 缺少签名参数错误
	ErrMissingSignatureParams = errors.New("missing signature parameters")

	// ErrInvalidTimestamp 无效时间戳错误
	ErrInvalidTimestamp = errors.New("invalid timestamp")

	// ErrInvalidNonce 无效随机数错误
	ErrInvalidNonce = errors.New("invalid nonce")

	// ErrClientNotFound 客户端未找到错误
	ErrClientNotFound = errors.New("client not found")
)

// SignatureService 签名服务接口
type SignatureService interface {
	// GenerateSignature 生成签名
	GenerateSignature(params map[string]string, body []byte, secret string) (string, error)

	// VerifySignature 验证签名
	VerifySignature(params map[string]string, body []byte, signature, secret string) error

	// GenerateNonce 生成随机字符串
	GenerateNonce() string

	// GenerateTimestamp 生成当前时间戳
	GenerateTimestamp() string

	// IsTimestampValid 检查时间戳是否有效
	IsTimestampValid(timestamp string) bool

	// ShouldSkipSignature 检查是否应该跳过签名验证
	ShouldSkipSignature(path string) bool
}

// DefaultSignatureService 默认签名服务实现
type DefaultSignatureService struct {
	config SignatureConfig
}

// NewSignatureService 创建新的签名服务
func NewSignatureService(config SignatureConfig) SignatureService {
	return &DefaultSignatureService{
		config: config,
	}
}

// GenerateSignature 生成签名
func (s *DefaultSignatureService) GenerateSignature(params map[string]string, body []byte, secret string) (string, error) {
	// 检查请求体大小
	if int64(len(body)) > s.config.MaxRequestBodySize {
		return "", ErrRequestBodyTooLarge
	}

	// 按字典序排序参数
	var keys []string
	for k := range params {
		if k != "sign" { // 排除签名参数
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	fmt.Printf("🔍 签名生成过程:\n")
	fmt.Printf("   参数键(排序后): %v\n", keys)

	// 构建待签名字符串
	var parts []string
	for _, k := range keys {
		// 对参数值进行URL编码以防止签名绕过
		encodedValue := url.QueryEscape(params[k])
		parts = append(parts, fmt.Sprintf("%s=%s", k, encodedValue))
		fmt.Printf("   参数: %s = %s (编码后: %s)\n", k, params[k], encodedValue)
	}
	stringToSign := strings.Join(parts, "&")

	// 如果有请求体，添加到待签名字符串
	if len(body) > 0 {
		// 对请求体进行Base64编码以安全处理二进制数据
		bodyEncoded := base64.StdEncoding.EncodeToString(body)
		stringToSign = stringToSign + "&body=" + bodyEncoded
		fmt.Printf("   请求体长度: %d\n", len(body))
		if len(bodyEncoded) < 200 {
			fmt.Printf("   请求体Base64: %s\n", bodyEncoded)
		} else {
			fmt.Printf("   请求体Base64(前200字符): %s...\n", bodyEncoded[:200])
		}
	}

	fmt.Printf("   待签名字符串长度: %d\n", len(stringToSign))
	if len(stringToSign) < 500 {
		fmt.Printf("   待签名字符串: %s\n", stringToSign)
	} else {
		fmt.Printf("   待签名字符串(前500字符): %s...\n", stringToSign[:500])
	}

	// 使用HMAC-SHA256计算签名
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	fmt.Printf("   生成的签名: %s\n", signature)

	return signature, nil
}

// VerifySignature 验证签名
func (s *DefaultSignatureService) VerifySignature(params map[string]string, body []byte, signature, secret string) error {
	// 检查请求体大小
	if int64(len(body)) > s.config.MaxRequestBodySize {
		return ErrRequestBodyTooLarge
	}

	expectedSignature, err := s.GenerateSignature(params, body, secret)
	if err != nil {
		fmt.Printf("❌ 生成期望签名失败: %s\n", err.Error())
		return err
	}

	fmt.Printf("🔍 签名对比:\n")
	fmt.Printf("   接收到的签名: %s\n", signature)
	fmt.Printf("   期望的签名:   %s\n", expectedSignature)
	fmt.Printf("   签名是否匹配: %t\n", hmac.Equal([]byte(signature), []byte(expectedSignature)))

	if !hmac.Equal([]byte(signature), []byte(expectedSignature)) {
		return ErrInvalidSignature
	}

	return nil
}

// GenerateNonce 生成随机字符串
func (s *DefaultSignatureService) GenerateNonce() string {
	// 使用加密安全的随机数生成器
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 32 // 增加长度提高安全性

	// 生成随机字节
	randomBytes := make([]byte, length)
	_, err := io.ReadFull(rand.Reader, randomBytes)
	if err != nil {
		// 更安全的回退机制：使用SHA256哈希
		hash := sha256.Sum256([]byte(fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Unix())))
		return fmt.Sprintf("%x", hash[:16]) // 返回32字符的十六进制字符串
	}

	// 将随机字节映射到字符集
	result := make([]byte, length)
	for i, b := range randomBytes {
		result[i] = charset[b%byte(len(charset))]
	}

	return string(result)
}

// GenerateTimestamp 生成当前时间戳（北京时间）
func (s *DefaultSignatureService) GenerateTimestamp() string {
	// 使用北京时间生成时间戳
	return fmt.Sprintf("%d", util.NowBeijing().Unix())
}

// IsTimestampValid 检查时间戳是否有效（使用北京时间，支持秒级和毫秒级）
func (s *DefaultSignatureService) IsTimestampValid(timestamp string) bool {
	var ts time.Time
	var err error

	// 使用当前时区（应用程序已设置为北京时间）

	// 首先尝试解析Unix时间戳
	if unixTime, parseErr := strconv.ParseInt(timestamp, 10, 64); parseErr == nil {
		// 检查时间戳范围（防止负数和过大值）
		if unixTime < 0 {
			return false
		}

		// 🚀 支持毫秒级和秒级时间戳
		now := util.NowBeijing()
		if unixTime > 1e12 {
			// 毫秒级时间戳
			ts = time.UnixMilli(unixTime).In(util.Beijing)
			// 检查毫秒级时间戳范围
			if unixTime > now.UnixMilli()+86400*1000 { // 不允许超过当前时间24小时
				return false
			}
		} else {
			// 秒级时间戳
			ts = time.Unix(unixTime, 0).In(util.Beijing)
			// 检查秒级时间戳范围
			if unixTime > now.Unix()+86400 { // 不允许超过当前时间24小时
				return false
			}
		}
	} else {
		// 尝试解析RFC3339格式
		ts, err = time.Parse(time.RFC3339, timestamp)
		if err != nil {
			return false
		}
	}

	// 检查时间戳是否在有效期内（使用北京时间）
	now := time.Now()
	validityDuration := time.Duration(s.config.TimestampValiditySeconds) * time.Second

	// 检查时间戳是否太旧
	if now.Sub(ts) > validityDuration {
		return false
	}

	// 🚀 优化：检查时间戳是否来自未来（允许更大的时钟偏差，适应分布式环境）
	if ts.Sub(now) > 2*time.Minute {
		return false
	}

	return true
}

// ShouldSkipSignature 检查是否应该跳过签名验证 - 🚀 统一网关智能认证策略
func (s *DefaultSignatureService) ShouldSkipSignature(path string) bool {
	// 🔒 基础设施端点 - 无需签名验证
	infrastructurePaths := []string{
		"/health",      // 健康检查
		"/oauth/token", // OAuth token获取（使用client_secret验证）
	}

	// 🌐 内部Web API - 无需签名验证（仅JWT认证）
	// 这些API专为Web前端设计，不需要复杂的签名验证
	webAPIPrefixes := []string{
		"/api/v1/auth/",       // 用户认证
		"/api/v1/admin/auth/", // 管理员认证
		"/api/v1/users/",      // 用户管理
		"/api/v1/admin/",      // 管理员功能
		"/api/v1/system/",     // 系统配置
		"/api/v1/web/",        // 明确的Web API前缀
	}

	// 🔥 供应商回调端点 - 无需签名验证
	// 供应商回调是第三方主动推送，不使用我们的签名体系
	callbackPaths := []string{
		"/api/v1/callbacks/kuaidi100",  // 快递100回调
		"/api/v1/callbacks/yida",       // 易达回调
		"/api/v1/callbacks/yuntong",    // 云通回调
		"/api/v1/callbacks/cainiao",    // 菜鸟回调
		"/api/v1/callbacks/kuaidiniao", // 快递鸟回调
	}

	// 🔥 供应商回调子路径 - 无需签名验证
	callbackPrefixes := []string{
		"/api/v1/callbacks/kuaidi100/",  // 快递100回调子路径
		"/api/v1/callbacks/yida/",       // 易达回调子路径
		"/api/v1/callbacks/yuntong/",    // 云通回调子路径
		"/api/v1/callbacks/cainiao/",    // 菜鸟回调子路径
		"/api/v1/callbacks/kuaidiniao/", // 快递鸟回调子路径
	}

	// 检查基础设施端点
	for _, allowedPath := range infrastructurePaths {
		if path == allowedPath {
			return true
		}
	}

	// 检查Web API前缀
	for _, prefix := range webAPIPrefixes {
		if strings.HasPrefix(path, prefix) {
			return true
		}
	}

	// 检查供应商回调端点
	for _, callbackPath := range callbackPaths {
		if path == callbackPath {
			return true
		}
	}

	// 检查供应商回调前缀
	for _, prefix := range callbackPrefixes {
		if strings.HasPrefix(path, prefix) {
			return true
		}
	}

	// 🚀 统一网关特殊处理 - 需要在handler内部进行签名验证
	// 这里不跳过签名验证，让签名中间件正常处理
	// 统一网关会根据clientType决定是否需要签名验证

	// 🔒 开放平台API - 强制签名验证
	// 所有其他API路径（特别是 /api/v1/open/* 和 /api/v1/express/*）都需要签名验证
	return false
}
