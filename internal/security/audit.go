package security

import (
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

var (
	// ErrAuditQueueFull 审计队列已满错误
	ErrAuditQueueFull = errors.New("audit queue is full")

	// ErrAuditDisabled 审计功能已禁用错误
	ErrAuditDisabled = errors.New("audit logging is disabled")
)

// AuditLog 审计日志结构
type AuditLog struct {
	ID            string          `json:"id" gorm:"primaryKey"`
	UserID        string          `json:"user_id"`
	ClientID      string          `json:"client_id"`
	Action        string          `json:"action"`
	Resource      string          `json:"resource"`
	IP            string          `json:"ip"`
	UserAgent     string          `json:"user_agent"`
	RequestMethod string          `json:"request_method"`
	RequestPath   string          `json:"request_path"`
	RequestParams string          `json:"request_params"`
	RequestBody   string          `json:"request_body"`
	ResponseCode  int             `json:"response_code"`
	ResponseBody  string          `json:"response_body"`
	Duration      int64           `json:"duration"` // 毫秒
	Status        string          `json:"status"`
	ErrorMessage  string          `json:"error_message"`
	Metadata      json.RawMessage `json:"metadata"`
	CreatedAt     time.Time       `json:"created_at"`
}

// AuditService 审计日志服务接口
type AuditService interface {
	// LogAudit 记录审计日志
	LogAudit(log AuditLog) error

	// ShouldAudit 检查是否应该记录审计日志
	ShouldAudit(path string) bool

	// IsEnabled 检查是否启用审计日志
	IsEnabled() bool

	// Shutdown 关闭审计服务
	Shutdown() error
}

// DefaultAuditService 默认审计日志服务实现
type DefaultAuditService struct {
	db               *gorm.DB
	config           AuditConfig
	asyncQueue       chan AuditLog
	shutdownChan     chan struct{}
	wg               sync.WaitGroup
	sensitiveRegexps []*regexp.Regexp
}

// NewAuditService 创建新的审计服务
func NewAuditService(db *gorm.DB, config AuditConfig) AuditService {
	// 编译敏感字段正则表达式
	sensitiveRegexps := make([]*regexp.Regexp, 0, len(config.SensitiveFields))
	for _, field := range config.SensitiveFields {
		// 创建匹配敏感字段的正则表达式
		pattern := fmt.Sprintf(`"%s"\s*:\s*"[^"]*"`, field)
		re, err := regexp.Compile(pattern)
		if err == nil {
			sensitiveRegexps = append(sensitiveRegexps, re)
		}

		// 创建匹配表单字段的正则表达式
		formPattern := fmt.Sprintf(`%s=[^&]*`, field)
		formRe, err := regexp.Compile(formPattern)
		if err == nil {
			sensitiveRegexps = append(sensitiveRegexps, formRe)
		}
	}

	service := &DefaultAuditService{
		db:               db,
		config:           config,
		asyncQueue:       make(chan AuditLog, config.QueueSize),
		shutdownChan:     make(chan struct{}),
		sensitiveRegexps: sensitiveRegexps,
	}

	// 启动异步处理
	if config.Enabled {
		service.wg.Add(1)
		go service.processQueue()

		// 如果配置了数据保留期限，启动清理任务
		if config.RetentionDays > 0 {
			service.wg.Add(1)
			go service.startCleanupTask()
		}
	}

	return service
}

// IsEnabled 检查是否启用审计日志
func (s *DefaultAuditService) IsEnabled() bool {
	return s.config.Enabled
}

// ShouldAudit 检查是否应该记录审计日志
func (s *DefaultAuditService) ShouldAudit(path string) bool {
	// 如果未启用审计日志，返回false
	if !s.IsEnabled() {
		return false
	}

	// 检查是否在跳过路径列表中
	for _, skipPath := range s.config.SkipPaths {
		if path == skipPath {
			return false
		}
	}

	// 应用采样率
	if s.config.SamplingRate < 100 {
		// 生成0-99的随机数
		randomValue := rand.Intn(100)
		// 如果随机数大于等于采样率，跳过记录
		if randomValue >= s.config.SamplingRate {
			return false
		}
	}

	return true
}

// sanitizeData 脱敏数据
func (s *DefaultAuditService) sanitizeData(data string) string {
	if data == "" {
		return data
	}

	result := data

	// 应用所有敏感字段正则表达式
	for _, re := range s.sensitiveRegexps {
		result = re.ReplaceAllStringFunc(result, func(match string) string {
			// 找到冒号或等号的位置
			colonPos := strings.Index(match, ":")
			equalPos := strings.Index(match, "=")

			var pos int

			if colonPos > 0 {
				pos = colonPos
			} else if equalPos > 0 {
				pos = equalPos
			} else {
				return match
			}

			// 提取字段名部分
			fieldPart := match[:pos+1]

			// 替换值部分为 "******"
			return fieldPart + " \"******\""
		})
	}

	return result
}

// processQueue 处理队列
func (s *DefaultAuditService) processQueue() {
	defer s.wg.Done()

	batch := make([]AuditLog, 0, s.config.BatchSize)
	ticker := time.NewTicker(time.Duration(s.config.BatchIntervalSeconds) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case log := <-s.asyncQueue:
			// 脱敏处理
			log.RequestBody = s.sanitizeData(log.RequestBody)
			log.RequestParams = s.sanitizeData(log.RequestParams)
			log.ResponseBody = s.sanitizeData(log.ResponseBody)

			batch = append(batch, log)
			if len(batch) >= s.config.BatchSize {
				s.saveBatch(batch)
				batch = make([]AuditLog, 0, s.config.BatchSize)
			}
		case <-ticker.C:
			if len(batch) > 0 {
				s.saveBatch(batch)
				batch = make([]AuditLog, 0, s.config.BatchSize)
			}
		case <-s.shutdownChan:
			// 保存剩余的日志
			if len(batch) > 0 {
				s.saveBatch(batch)
			}
			return
		}
	}
}

// startCleanupTask 启动清理任务
func (s *DefaultAuditService) startCleanupTask() {
	defer s.wg.Done()

	// 每天运行一次清理任务
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	// 立即运行一次清理
	s.cleanupOldLogs()

	for {
		select {
		case <-ticker.C:
			s.cleanupOldLogs()
		case <-s.shutdownChan:
			return
		}
	}
}

// cleanupOldLogs 清理旧日志
func (s *DefaultAuditService) cleanupOldLogs() {
	if s.config.RetentionDays <= 0 {
		return
	}

	// 检查数据库连接是否可用
	if s.db == nil {
		fmt.Println("Warning: Database connection is nil, skipping audit log cleanup")
		return
	}

	// 计算截止日期
	cutoffDate := time.Now().AddDate(0, 0, -s.config.RetentionDays)

	// 删除旧日志
	s.db.Where("created_at < ?", cutoffDate).Delete(&AuditLog{})
}

// saveBatch 批量保存
func (s *DefaultAuditService) saveBatch(logs []AuditLog) {
	if len(logs) == 0 {
		return
	}

	// 检查数据库连接是否可用
	if s.db == nil {
		fmt.Println("Warning: Database connection is nil, skipping audit log save")
		return
	}

	// 使用事务批量保存
	err := s.db.Transaction(func(tx *gorm.DB) error {
		return tx.CreateInBatches(logs, s.config.BatchSize).Error
	})

	if err != nil {
		// 记录错误，但不中断处理
		fmt.Printf("Error saving audit logs: %v\n", err)
	}
}

// LogAudit 记录审计日志
func (s *DefaultAuditService) LogAudit(log AuditLog) error {
	// 如果未启用审计日志，返回错误
	if !s.IsEnabled() {
		return ErrAuditDisabled
	}

	// 如果没有ID，生成一个
	if log.ID == "" {
		log.ID = uuid.New().String()
	}

	// 如果没有创建时间，设置为当前时间
	if log.CreatedAt.IsZero() {
		log.CreatedAt = time.Now()
	}

	// 限制请求体和响应体大小
	if len(log.RequestBody) > s.config.MaxRequestBodySize {
		log.RequestBody = log.RequestBody[:s.config.MaxRequestBodySize] + "... (truncated)"
	}

	if len(log.ResponseBody) > s.config.MaxResponseBodySize {
		log.ResponseBody = log.ResponseBody[:s.config.MaxResponseBodySize] + "... (truncated)"
	}

	// 尝试将日志加入队列
	select {
	case s.asyncQueue <- log:
		// 成功加入队列
		return nil
	default:
		// 队列已满，直接保存
		go func() {
			// 脱敏处理
			log.RequestBody = s.sanitizeData(log.RequestBody)
			log.RequestParams = s.sanitizeData(log.RequestParams)
			log.ResponseBody = s.sanitizeData(log.ResponseBody)

			// 检查数据库连接是否可用
			if s.db == nil {
				fmt.Println("Warning: Database connection is nil, skipping audit log save")
				return
			}

			s.db.Create(&log)
		}()
		return ErrAuditQueueFull
	}
}

// Shutdown 关闭审计服务
func (s *DefaultAuditService) Shutdown() error {
	if !s.IsEnabled() {
		return nil
	}

	// 发送关闭信号
	close(s.shutdownChan)

	// 等待所有goroutine完成
	s.wg.Wait()

	return nil
}

// GetStatusFromCode 根据状态码获取状态
func GetStatusFromCode(code int) string {
	if code >= 200 && code < 300 {
		return "success"
	} else if code >= 400 && code < 500 {
		return "client_error"
	} else if code >= 500 {
		return "server_error"
	}
	return "unknown"
}
