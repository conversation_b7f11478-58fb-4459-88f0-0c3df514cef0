package security

import (
	"fmt"
	"net/url"
	"os"
	"strings"

	"go.uber.org/zap"
)

// ConfigValidator 安全配置验证器
type ConfigValidator struct {
	logger *zap.Logger
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator(logger *zap.Logger) *ConfigValidator {
	return &ConfigValidator{
		logger: logger,
	}
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid    bool     `json:"valid"`
	Errors   []string `json:"errors,omitempty"`
	Warnings []string `json:"warnings,omitempty"`
}

// AddError 添加错误
func (vr *ValidationResult) AddError(message string) {
	vr.Valid = false
	vr.Errors = append(vr.Errors, message)
}

// AddWarning 添加警告
func (vr *ValidationResult) AddWarning(message string) {
	vr.Warnings = append(vr.Warnings, message)
}

// ValidateProductionConfig 验证生产环境配置
func (cv *ConfigValidator) ValidateProductionConfig() *ValidationResult {
	result := &ValidationResult{Valid: true}

	// 检查环境变量
	cv.validateEnvironment(result)

	// 检查数据库配置
	cv.validateDatabaseConfig(result)

	// 检查Redis配置
	cv.validateRedisConfig(result)

	// 检查JWT配置
	cv.validateJWTConfig(result)

	// 检查安全配置
	cv.validateSecurityConfig(result)

	// 检查CORS配置
	cv.validateCORSConfig(result)

	// 检查文件权限
	cv.validateFilePermissions(result)

	return result
}

// validateEnvironment 验证环境配置
func (cv *ConfigValidator) validateEnvironment(result *ValidationResult) {
	env := os.Getenv("ENVIRONMENT")
	if env == "" {
		result.AddWarning("ENVIRONMENT 环境变量未设置，默认为开发环境")
	} else if env == "production" {
		cv.logger.Info("检测到生产环境配置")
	}
}

// validateDatabaseConfig 验证数据库配置
func (cv *ConfigValidator) validateDatabaseConfig(result *ValidationResult) {
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		result.AddError("DATABASE_URL 环境变量未设置")
		return
	}

	// 解析数据库URL
	parsedURL, err := url.Parse(dbURL)
	if err != nil {
		result.AddError(fmt.Sprintf("DATABASE_URL 格式错误: %v", err))
		return
	}

	// 检查SSL模式
	sslMode := parsedURL.Query().Get("sslmode")
	if sslMode == "disable" {
		env := os.Getenv("ENVIRONMENT")
		if env == "production" {
			result.AddError("生产环境必须启用数据库SSL连接")
		} else {
			result.AddWarning("建议启用数据库SSL连接")
		}
	}

	// 检查数据库类型
	if parsedURL.Scheme != "postgresql" {
		result.AddWarning("建议使用PostgreSQL数据库")
	}
}

// validateRedisConfig 验证Redis配置
func (cv *ConfigValidator) validateRedisConfig(result *ValidationResult) {
	redisURL := os.Getenv("REDIS_URL")
	if redisURL == "" {
		result.AddError("REDIS_URL 环境变量未设置")
		return
	}

	// 解析Redis URL
	parsedURL, err := url.Parse(redisURL)
	if err != nil {
		result.AddError(fmt.Sprintf("REDIS_URL 格式错误: %v", err))
		return
	}

	// 检查是否有密码
	if parsedURL.User == nil || parsedURL.User.Username() == "" {
		env := os.Getenv("ENVIRONMENT")
		if env == "production" {
			result.AddError("生产环境Redis必须设置密码")
		} else {
			result.AddWarning("建议为Redis设置密码")
		}
	}
}

// validateJWTConfig 验证JWT配置
func (cv *ConfigValidator) validateJWTConfig(result *ValidationResult) {
	jwtSecret := os.Getenv("JWT_SECRET")
	privateKeyPath := os.Getenv("JWT_PRIVATE_KEY_PATH")
	publicKeyPath := os.Getenv("JWT_PUBLIC_KEY_PATH")

	// 检查JWT密钥配置
	if jwtSecret == "" && (privateKeyPath == "" || publicKeyPath == "") {
		result.AddError("必须设置 JWT_SECRET 或 JWT_PRIVATE_KEY_PATH/JWT_PUBLIC_KEY_PATH")
		return
	}

	// 检查JWT密钥强度
	if jwtSecret != "" {
		if len(jwtSecret) < 32 {
			result.AddError("JWT_SECRET 长度不能少于32个字符")
		}
		if strings.Contains(strings.ToLower(jwtSecret), "secret") ||
			strings.Contains(strings.ToLower(jwtSecret), "password") ||
			strings.Contains(strings.ToLower(jwtSecret), "123456") {
			result.AddError("JWT_SECRET 不能包含常见的弱密钥模式")
		}
	}

	// 检查密钥文件
	if privateKeyPath != "" {
		if !cv.fileExists(privateKeyPath) {
			result.AddError(fmt.Sprintf("JWT私钥文件不存在: %s", privateKeyPath))
		} else {
			cv.checkFilePermissions(privateKeyPath, 0600, result)
		}
	}

	if publicKeyPath != "" {
		if !cv.fileExists(publicKeyPath) {
			result.AddError(fmt.Sprintf("JWT公钥文件不存在: %s", publicKeyPath))
		}
	}
}

// validateSecurityConfig 验证安全配置
func (cv *ConfigValidator) validateSecurityConfig(result *ValidationResult) {
	env := os.Getenv("ENVIRONMENT")

	// 检查签名验证
	signatureEnabled := os.Getenv("GOKUAIDI_SECURITY_SIGNATURE_ENABLED")
	if env == "production" && signatureEnabled != "true" {
		result.AddError("生产环境必须启用签名验证")
	}

	// 检查速率限制
	rateLimitEnabled := os.Getenv("GOKUAIDI_SECURITY_RATE_LIMIT_ENABLED")
	if env == "production" && rateLimitEnabled != "true" {
		result.AddError("生产环境必须启用速率限制")
	}

	// 检查审计日志
	auditEnabled := os.Getenv("GOKUAIDI_SECURITY_AUDIT_ENABLED")
	if env == "production" && auditEnabled != "true" {
		result.AddError("生产环境必须启用审计日志")
	}

	// 检查HTTPS强制
	forceHTTPS := os.Getenv("GOKUAIDI_SECURITY_FORCE_HTTPS")
	if env == "production" && forceHTTPS != "true" {
		result.AddError("生产环境必须强制使用HTTPS")
	}
}

// validateCORSConfig 验证CORS配置
func (cv *ConfigValidator) validateCORSConfig(result *ValidationResult) {
	corsOrigins := os.Getenv("CORS_ALLOWED_ORIGINS")
	env := os.Getenv("ENVIRONMENT")

	if env == "production" {
		if corsOrigins == "" {
			result.AddError("生产环境必须明确配置CORS_ALLOWED_ORIGINS")
			return
		}

		// 检查是否包含不安全的通配符
		if strings.Contains(corsOrigins, "*") {
			result.AddError("生产环境CORS配置不能使用通配符")
		}

		// 检查是否包含localhost
		if strings.Contains(strings.ToLower(corsOrigins), "localhost") ||
			strings.Contains(corsOrigins, "127.0.0.1") {
			result.AddWarning("生产环境CORS配置包含本地地址")
		}
	}
}

// validateFilePermissions 验证文件权限
func (cv *ConfigValidator) validateFilePermissions(result *ValidationResult) {
	// 检查配置文件权限
	configFiles := []string{
		"config/config.yaml",
		"config/production.env",
		".env",
	}

	for _, file := range configFiles {
		if cv.fileExists(file) {
			cv.checkFilePermissions(file, 0640, result)
		}
	}

	// 检查密钥文件权限
	keyFiles := []string{
		os.Getenv("JWT_PRIVATE_KEY_PATH"),
		"keys/private.pem",
	}

	for _, file := range keyFiles {
		if file != "" && cv.fileExists(file) {
			cv.checkFilePermissions(file, 0600, result)
		}
	}
}

// fileExists 检查文件是否存在
func (cv *ConfigValidator) fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

// checkFilePermissions 检查文件权限
func (cv *ConfigValidator) checkFilePermissions(filename string, expectedPerm os.FileMode, result *ValidationResult) {
	info, err := os.Stat(filename)
	if err != nil {
		result.AddWarning(fmt.Sprintf("无法检查文件权限: %s", filename))
		return
	}

	actualPerm := info.Mode().Perm()
	if actualPerm != expectedPerm {
		result.AddWarning(fmt.Sprintf("文件 %s 权限为 %o，建议设置为 %o",
			filename, actualPerm, expectedPerm))
	}
}

// ValidateAPIKeys 验证API密钥配置
func (cv *ConfigValidator) ValidateAPIKeys() *ValidationResult {
	result := &ValidationResult{Valid: true}

	apiKeys := map[string]string{
		"KUAIDI100_API_KEY":   os.Getenv("KUAIDI100_API_KEY"),
		"KUAIDI100_SECRET":    os.Getenv("KUAIDI100_SECRET"),
		"YIDA_USERNAME":       os.Getenv("YIDA_USERNAME"),
		"YIDA_PRIVATE_KEY":    os.Getenv("YIDA_PRIVATE_KEY"),
		"YUNTONG_BUSINESS_ID": os.Getenv("YUNTONG_BUSINESS_ID"),
		"YUNTONG_API_KEY":     os.Getenv("YUNTONG_API_KEY"),
		"YUNTONG_SECRET_KEY":  os.Getenv("YUNTONG_SECRET_KEY"),
	}

	env := os.Getenv("ENVIRONMENT")
	for keyName, keyValue := range apiKeys {
		if keyValue == "" {
			if env == "production" {
				result.AddError(fmt.Sprintf("生产环境必须设置 %s", keyName))
			} else {
				result.AddWarning(fmt.Sprintf("建议设置 %s", keyName))
			}
		} else if len(keyValue) < 8 {
			result.AddWarning(fmt.Sprintf("%s 长度过短，可能不安全", keyName))
		}
	}

	return result
}

// GenerateSecurityReport 生成安全报告
func (cv *ConfigValidator) GenerateSecurityReport() string {
	configResult := cv.ValidateProductionConfig()
	apiKeyResult := cv.ValidateAPIKeys()

	var report strings.Builder
	report.WriteString("=== Go-Kuaidi 安全配置报告 ===\n\n")

	// 配置验证结果
	report.WriteString("## 配置验证结果\n")
	if configResult.Valid {
		report.WriteString("✅ 配置验证通过\n")
	} else {
		report.WriteString("❌ 配置验证失败\n")
		for _, err := range configResult.Errors {
			report.WriteString(fmt.Sprintf("  - 错误: %s\n", err))
		}
	}

	if len(configResult.Warnings) > 0 {
		report.WriteString("\n⚠️ 警告:\n")
		for _, warning := range configResult.Warnings {
			report.WriteString(fmt.Sprintf("  - %s\n", warning))
		}
	}

	// API密钥验证结果
	report.WriteString("\n## API密钥验证结果\n")
	if apiKeyResult.Valid {
		report.WriteString("✅ API密钥配置正常\n")
	} else {
		report.WriteString("❌ API密钥配置有问题\n")
		for _, err := range apiKeyResult.Errors {
			report.WriteString(fmt.Sprintf("  - 错误: %s\n", err))
		}
	}

	if len(apiKeyResult.Warnings) > 0 {
		report.WriteString("\n⚠️ API密钥警告:\n")
		for _, warning := range apiKeyResult.Warnings {
			report.WriteString(fmt.Sprintf("  - %s\n", warning))
		}
	}

	// 安全建议
	report.WriteString("\n## 安全建议\n")
	report.WriteString("1. 定期轮换API密钥和JWT密钥\n")
	report.WriteString("2. 监控异常访问模式\n")
	report.WriteString("3. 定期备份审计日志\n")
	report.WriteString("4. 使用密钥管理服务存储敏感信息\n")
	report.WriteString("5. 定期进行安全扫描和渗透测试\n")

	return report.String()
}
