package interfaces

import (
	"context"

	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/model"
)

// CallbackServiceInterface 回调服务接口
type CallbackServiceInterface interface {
	// 回调处理
	ProcessCallback(ctx context.Context, provider string, rawData []byte, headers map[string]string) (*model.CallbackResponse, error)
	ProcessCallbackAsync(ctx context.Context, provider string, rawData []byte, headers map[string]string) error

	// 回调查询
	GetCallbackRecord(ctx context.Context, recordID string) (*model.UnifiedCallbackRecord, error)
	ListCallbackRecords(ctx context.Context, req *model.CallbackListRequest) (*model.CallbackListResponse, error)

	// 回调重试
	RetryCallback(ctx context.Context, recordID string) error
	RetryFailedCallbacks(ctx context.Context, maxRetries int) error

	// 适配器管理
	RegisterProviderAdapter(provider string, adapter CallbackAdapterInterface) error
	GetProviderAdapter(provider string) (CallbackAdapterInterface, bool)
}

// CallbackAdapterInterface 回调适配器接口
type CallbackAdapterInterface interface {
	// 签名验证
	ValidateSignature(rawData []byte, headers map[string]string) error

	// 数据解析
	ParseCallbackData(rawData []byte) (*model.CallbackData, error)

	// 数据标准化
	StandardizeData(data *model.CallbackData) (*model.StandardizedCallbackData, error)

	// 获取供应商名称
	GetProviderName() string

	// 获取支持的事件类型
	GetSupportedEventTypes() []string
}

// BillingService 计费服务接口
type BillingService interface {
	UpdateOrderBilling(ctx context.Context, req *UpdateBillingRequest) error
	ProcessBillingDifference(ctx context.Context, orderNo string, actualFee float64, reason string) error
}

type BalanceService interface {
	RefundForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error
	GetOrderNetPayment(ctx context.Context, userID, orderNo, customerOrderNo string) (decimal.Decimal, error)
}

// UpdateBillingRequest 更新计费请求
type UpdateBillingRequest struct {
	OrderNo       string
	BillingType   string
	FreightFee    float64
	InsuranceFee  float64
	PackageFee    float64
	PickupFee     float64
	DeliveryFee   float64
	CodFee        float64
	OtherFee      float64
	TotalFee      float64
	Weight        float64
	Volume        float64
	ChargedWeight float64
	Provider      string
	Source        string
	Reason        string
	RawData       map[string]any
}
