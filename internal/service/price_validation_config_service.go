package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// PriceValidationConfigService 价格验证配置服务接口
type PriceValidationConfigService interface {
	GetPriceValidationConfig(ctx context.Context) (*model.PriceValidationConfig, error)
	RefreshConfig(ctx context.Context) error
}

// DefaultPriceValidationConfigService 默认价格验证配置服务实现
type DefaultPriceValidationConfigService struct {
	configService SystemConfigService
	logger        *zap.Logger
	cachedConfig  *model.PriceValidationConfig
	lastRefresh   time.Time
	cacheExpiry   time.Duration
}

// NewPriceValidationConfigService 创建价格验证配置服务
func NewPriceValidationConfigService(configService SystemConfigService, logger *zap.Logger) PriceValidationConfigService {
	return &DefaultPriceValidationConfigService{
		configService: configService,
		logger:        logger,
		cacheExpiry:   5 * time.Minute, // 配置缓存5分钟
	}
}

// GetPriceValidationConfig 获取价格验证配置
func (s *DefaultPriceValidationConfigService) GetPriceValidationConfig(ctx context.Context) (*model.PriceValidationConfig, error) {
	// 检查缓存是否有效
	if s.cachedConfig != nil && time.Since(s.lastRefresh) < s.cacheExpiry {
		return s.cachedConfig, nil
	}

	// 刷新配置
	if err := s.RefreshConfig(ctx); err != nil {
		// 如果刷新失败但有缓存配置，使用缓存配置
		if s.cachedConfig != nil {
			s.logger.Warn("刷新价格验证配置失败，使用缓存配置", zap.Error(err))
			return s.cachedConfig, nil
		}
		return nil, fmt.Errorf("获取价格验证配置失败: %w", err)
	}

	return s.cachedConfig, nil
}

// RefreshConfig 刷新配置
func (s *DefaultPriceValidationConfigService) RefreshConfig(ctx context.Context) error {
	config := &model.PriceValidationConfig{}

	// 从数据库获取各项配置
	enableValidation, err := s.getBoolConfig(ctx, "price_validation", "enable_validation", true)
	if err != nil {
		return fmt.Errorf("获取价格验证开关配置失败: %w", err)
	}
	config.EnableValidation = enableValidation

	priceTolerance, err := s.getFloatConfig(ctx, "price_validation", "price_tolerance", 0.0)
	if err != nil {
		return fmt.Errorf("获取价格容差配置失败: %w", err)
	}
	config.PriceTolerance = priceTolerance

	validationTimeout, err := s.getIntConfig(ctx, "price_validation", "validation_timeout", 30)
	if err != nil {
		return fmt.Errorf("获取验证超时配置失败: %w", err)
	}
	config.ValidationTimeout = validationTimeout

	maxRetryAttempts, err := s.getIntConfig(ctx, "price_validation", "max_retry_attempts", 3)
	if err != nil {
		return fmt.Errorf("获取最大重试次数配置失败: %w", err)
	}
	config.MaxRetryAttempts = maxRetryAttempts

	cacheInvalidateOnFail, err := s.getBoolConfig(ctx, "price_validation", "cache_invalidate_on_fail", true)
	if err != nil {
		return fmt.Errorf("获取缓存失效配置失败: %w", err)
	}
	config.CacheInvalidateOnFail = cacheInvalidateOnFail

	// 更新缓存
	s.cachedConfig = config
	s.lastRefresh = util.NowBeijing()

	s.logger.Info("价格验证配置刷新成功",
		zap.Bool("enable_validation", config.EnableValidation),
		zap.Float64("price_tolerance", config.PriceTolerance),
		zap.Int("validation_timeout", config.ValidationTimeout),
		zap.Int("max_retry_attempts", config.MaxRetryAttempts),
		zap.Bool("cache_invalidate_on_fail", config.CacheInvalidateOnFail))

	return nil
}

// getBoolConfig 获取布尔配置
func (s *DefaultPriceValidationConfigService) getBoolConfig(ctx context.Context, group, key string, defaultValue bool) (bool, error) {
	configValue, err := s.configService.GetConfigByKey(ctx, group, key)
	if err != nil {
		s.logger.Error("获取配置失败，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.Bool("default_value", defaultValue),
			zap.Error(err))
		return defaultValue, nil
	}

	if configValue.ConfigType != model.ConfigTypeBoolean {
		return false, fmt.Errorf("配置类型不匹配，期望boolean，实际%s", configValue.ConfigType)
	}

	value, err := strconv.ParseBool(configValue.ConfigValue)
	if err != nil {
		return false, fmt.Errorf("配置值转换失败: %w", err)
	}

	return value, nil
}

// getFloatConfig 获取浮点数配置
func (s *DefaultPriceValidationConfigService) getFloatConfig(ctx context.Context, group, key string, defaultValue float64) (float64, error) {
	configValue, err := s.configService.GetConfigByKey(ctx, group, key)
	if err != nil {
		s.logger.Error("获取配置失败，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.Float64("default_value", defaultValue),
			zap.Error(err))
		return defaultValue, nil
	}

	if configValue.ConfigType != model.ConfigTypeFloat {
		return 0, fmt.Errorf("配置类型不匹配，期望float，实际%s", configValue.ConfigType)
	}

	value, err := strconv.ParseFloat(configValue.ConfigValue, 64)
	if err != nil {
		return 0, fmt.Errorf("配置值转换失败: %w", err)
	}

	return value, nil
}

// getIntConfig 获取整数配置
func (s *DefaultPriceValidationConfigService) getIntConfig(ctx context.Context, group, key string, defaultValue int) (int, error) {
	configValue, err := s.configService.GetConfigByKey(ctx, group, key)
	if err != nil {
		s.logger.Error("获取配置失败，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.Int("default_value", defaultValue),
			zap.Error(err))
		return defaultValue, nil
	}

	if configValue.ConfigType != model.ConfigTypeInteger {
		return 0, fmt.Errorf("配置类型不匹配，期望integer，实际%s", configValue.ConfigType)
	}

	value, err := strconv.Atoi(configValue.ConfigValue)
	if err != nil {
		return 0, fmt.Errorf("配置值转换失败: %w", err)
	}

	return value, nil
}
