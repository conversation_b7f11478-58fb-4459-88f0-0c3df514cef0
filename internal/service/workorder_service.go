package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// WorkOrderService 工单服务接口
type WorkOrderService interface {
	// 创建工单（统一使用智能创建方式）
	CreateWorkOrder(ctx context.Context, userID string, req *model.SmartCreateWorkOrderRequest) (*model.WorkOrder, error)
	// 获取工单详情
	GetWorkOrder(ctx context.Context, userID string, workOrderID uuid.UUID) (*model.WorkOrder, error)
	// 获取工单列表
	ListWorkOrders(ctx context.Context, userID string, req *model.WorkOrderListRequest) (*model.WorkOrderListResponse, error)
	// 回复工单
	ReplyWorkOrder(ctx context.Context, userID string, workOrderID uuid.UUID, req *model.ReplyWorkOrderRequest) (*model.WorkOrderReply, error)
	// 删除工单
	DeleteWorkOrder(ctx context.Context, userID string, workOrderID uuid.UUID) error
	// 上传附件
	UploadAttachment(ctx context.Context, userID string, fileName string, fileContent []byte) (*model.UploadAttachmentResponse, error)
	// 获取支持的工单类型
	GetSupportedTypes(ctx context.Context, provider string) ([]model.WorkOrderTypeMapping, error)
	// 处理供应商回调
	HandleProviderCallback(ctx context.Context, provider string, callbackData interface{}) error
}

// DefaultWorkOrderService 默认工单服务实现
type DefaultWorkOrderService struct {
	workOrderRepo     repository.WorkOrderRepository
	orderRepo         repository.OrderRepository
	smartOrderFinder  *SmartOrderFinder // 🔥 新增：智能订单查找服务
	providerAdapters  map[string]WorkOrderProviderAdapter
	fileUploadService FileUploadService
	callbackService   WorkOrderCallbackService // 🔥 工单回调服务
	logger            *zap.Logger
}

// WorkOrderProviderAdapter 工单供应商适配器接口
type WorkOrderProviderAdapter interface {
	// 创建工单
	CreateWorkOrder(ctx context.Context, req *model.CreateWorkOrderRequest) (*ProviderWorkOrderResponse, error)
	// 查询工单
	QueryWorkOrder(ctx context.Context, providerWorkOrderID string) (*ProviderWorkOrderResponse, error)
	// 回复工单
	ReplyWorkOrder(ctx context.Context, providerWorkOrderID string, content string, attachmentURLs []string) error
	// 删除/取消工单
	DeleteWorkOrder(ctx context.Context, providerWorkOrderID string, reason string) error
	// 上传附件
	UploadAttachment(ctx context.Context, fileName string, fileContent []byte) (string, error)
	// 解析回调数据
	ParseCallback(ctx context.Context, callbackData interface{}) (*model.WorkOrderCallbackData, error)
}

// ProviderWorkOrderResponse 供应商工单响应
type ProviderWorkOrderResponse struct {
	ProviderWorkOrderID string                 `json:"provider_work_order_id"`
	Status              int                    `json:"status"`
	Message             string                 `json:"message"`
	Data                map[string]interface{} `json:"data,omitempty"`
}

// WorkOrderCallbackData 工单回调数据 (使用model包中的定义)
// 注意：这个类型现在引用 model.WorkOrderCallbackData

// 注意：FileUploadService 接口已在 file_upload_service.go 中定义
// 注意：CallbackServiceInterface 接口已在 interfaces.go 中定义

// WorkOrderCallbackService 工单回调服务接口（简化版）
// 🔥 新增：专门用于工单回调转发的简化接口
type WorkOrderCallbackService interface {
	ProcessCallback(ctx context.Context, provider string, rawData []byte, headers map[string]string) (*model.CallbackResponse, error)
}

// NewDefaultWorkOrderService 创建默认工单服务
func NewDefaultWorkOrderService(
	workOrderRepo repository.WorkOrderRepository,
	orderRepo repository.OrderRepository,
	smartOrderFinder *SmartOrderFinder, // 🔥 新增：智能订单查找服务
	fileUploadService FileUploadService,
	callbackService WorkOrderCallbackService, // 🔥 工单回调服务
	logger *zap.Logger,
) WorkOrderService {
	service := &DefaultWorkOrderService{
		workOrderRepo:     workOrderRepo,
		orderRepo:         orderRepo,
		smartOrderFinder:  smartOrderFinder, // 🔥 新增：设置智能订单查找服务
		providerAdapters:  make(map[string]WorkOrderProviderAdapter),
		fileUploadService: fileUploadService,
		callbackService:   callbackService, // 🔥 注入回调服务
		logger:            logger,
	}

	return service
}

// Stop 停止工单服务
func (s *DefaultWorkOrderService) Stop() error {
	// 现在使用 EnhancedWorkOrderCallbackService，不需要手动停止回调池
	return nil
}

// RegisterProviderAdapter 注册供应商适配器
func (s *DefaultWorkOrderService) RegisterProviderAdapter(provider string, adapter WorkOrderProviderAdapter) {
	s.providerAdapters[provider] = adapter
}

// CreateWorkOrder 创建工单（统一使用智能创建方式）
func (s *DefaultWorkOrderService) CreateWorkOrder(ctx context.Context, userID string, req *model.SmartCreateWorkOrderRequest) (*model.WorkOrder, error) {
	// 1. 验证创建请求参数
	if err := s.validateCreateRequest(req); err != nil {
		return nil, fmt.Errorf("请求参数验证失败: %w", err)
	}

	// 2. 根据订单号或运单号自动查询订单信息
	order, err := s.findOrderByIdentifier(ctx, userID, req.OrderNo, req.TrackingNo)
	if err != nil {
		return nil, fmt.Errorf("查询订单信息失败: %w", err)
	}

	// 3. 从订单中自动识别供应商
	provider := order.Provider
	if provider == "" {
		return nil, fmt.Errorf("无法从订单中识别供应商")
	}

	// 4. 验证供应商是否支持该工单类型
	typeMapping, err := s.workOrderRepo.GetTypeMapping(ctx, req.WorkOrderType, provider)
	if err != nil {
		return nil, fmt.Errorf("获取工单类型映射失败: %w", err)
	}

	if !typeMapping.IsSupported {
		return nil, fmt.Errorf("供应商 %s 不支持工单类型 %s", provider, typeMapping.UnifiedName)
	}

	// 5. 自动配置回调URL（如果系统有配置）
	callbackURL := s.generateCallbackURL(fmt.Sprintf("%d", order.ID))
	messageCallbackURL := s.generateMessageCallbackURL(fmt.Sprintf("%d", order.ID))

	// 6. 生成工单标题
	title := s.generateWorkOrderTitle(typeMapping.UnifiedName, req.OrderNo, req.TrackingNo)

	// 🔥 新增：生成或使用用户自定义工单ID
	var customerWorkOrderID string
	if req.CustomerWorkOrderID != nil && *req.CustomerWorkOrderID != "" {
		// 使用用户提供的自定义工单ID
		customerWorkOrderID = *req.CustomerWorkOrderID

		// 验证同一用户下自定义工单ID的唯一性
		if err := s.validateCustomerWorkOrderIDUniqueness(ctx, userID, customerWorkOrderID); err != nil {
			return nil, fmt.Errorf("用户自定义工单ID验证失败: %w", err)
		}
	} else {
		// 自动生成工单ID
		customerWorkOrderID = fmt.Sprintf("wo%d", util.NowBeijing().Unix())
	}

	// 7. 创建本地工单记录
	workOrder := &model.WorkOrder{
		ID:                  uuid.New(),
		UserID:              userID,
		CustomerWorkOrderID: &customerWorkOrderID, // 🔥 新增：用户自定义工单ID
		OrderNo:             req.OrderNo,
		TrackingNo:          req.TrackingNo,
		Provider:            provider, // 自动识别的供应商
		WorkOrderType:       req.WorkOrderType,
		Title:               title,
		Content:             req.Content,
		Status:              model.WorkOrderStatusPending,
		Priority:            model.PriorityMedium,
		FeedbackWeight:      req.FeedbackWeight,
		GoodsValue:          req.GoodsValue,
		OverweightAmount:    req.OverweightAmount,
		CallbackURL:         callbackURL,
		MessageCallbackURL:  messageCallbackURL,
	}

	// 8. 序列化原始请求数据
	if rawData, err := json.Marshal(req); err == nil {
		rawDataStr := string(rawData)
		workOrder.RawRequestData = &rawDataStr
	}

	// 9. 保存到数据库
	if err := s.workOrderRepo.Create(ctx, workOrder); err != nil {
		return nil, fmt.Errorf("保存工单失败: %w", err)
	}

	// 10. 构建供应商API请求（转换工单类型）
	providerWorkOrderType := *typeMapping.ProviderType // 使用供应商特定的工单类型
	providerReq := &model.CreateWorkOrderRequest{
		OrderNo:              req.OrderNo,
		TrackingNo:           req.TrackingNo,
		Provider:             provider,
		WorkOrderType:        providerWorkOrderType, // 🔥 使用转换后的供应商类型
		Content:              req.Content,
		FeedbackWeight:       req.FeedbackWeight,
		GoodsValue:           req.GoodsValue,
		OverweightAmount:     req.OverweightAmount,
		CallbackURL:          callbackURL,
		MessageCallbackURL:   messageCallbackURL,
		AttachmentURLs:       req.AttachmentURLs,
		ProviderSpecificData: s.generateProviderSpecificData(provider, req.WorkOrderType, order),
	}

	s.logger.Info("工单类型转换",
		zap.String("provider", provider),
		zap.Int("unified_type", req.WorkOrderType),
		zap.String("unified_name", typeMapping.UnifiedName),
		zap.Int("provider_type", providerWorkOrderType),
		zap.String("provider_name", *typeMapping.ProviderName))

	// 11. 获取供应商适配器
	adapter, exists := s.providerAdapters[provider]
	if !exists {
		return nil, fmt.Errorf("不支持的供应商: %s", provider)
	}

	// 12. 调用供应商API创建工单
	providerResp, err := adapter.CreateWorkOrder(ctx, providerReq)
	if err != nil {
		s.logger.Error("调用供应商API创建工单失败",
			zap.Error(err),
			zap.String("work_order_id", workOrder.ID.String()),
			zap.String("provider", provider))

		// 🔥 删除本地工单
		if deleteErr := s.workOrderRepo.Delete(ctx, workOrder.ID); deleteErr != nil {
			s.logger.Error("删除失败的工单记录失败", zap.Error(deleteErr))
		}

		// 🔥 提取供应商的具体业务错误信息并直接返回失败
		businessError := s.extractBusinessError(err.Error(), provider)
		return nil, fmt.Errorf("创建工单失败: %s", businessError)
	}

	// 更新供应商工单ID和响应数据
	workOrder.ProviderWorkOrderID = &providerResp.ProviderWorkOrderID
	workOrder.Status = model.WorkOrderStatusPending
	if respData, err := json.Marshal(providerResp); err == nil {
		respDataStr := string(respData)
		workOrder.ProviderResponseData = &respDataStr
	}

	// 更新数据库
	if err := s.workOrderRepo.Update(ctx, workOrder); err != nil {
		s.logger.Error("更新工单供应商信息失败", zap.Error(err))
		return nil, fmt.Errorf("更新工单信息失败: %w", err)
	}

	// 13. 处理附件
	if len(req.AttachmentURLs) > 0 {
		if err := s.createAttachments(ctx, &workOrder.ID, nil, req.AttachmentURLs, model.UploadTypeWorkOrder); err != nil {
			s.logger.Error("创建工单附件失败", zap.Error(err))
		}
	}

	// 14. 加载完整的工单信息
	return s.loadWorkOrderDetails(ctx, workOrder)
}

// GetWorkOrder 获取工单详情
func (s *DefaultWorkOrderService) GetWorkOrder(ctx context.Context, userID string, workOrderID uuid.UUID) (*model.WorkOrder, error) {
	// 🔥 修复：输入验证
	if userID == "" {
		return nil, fmt.Errorf("用户ID不能为空")
	}

	// 1. 获取工单基础信息
	workOrder, err := s.workOrderRepo.GetByID(ctx, workOrderID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("工单不存在")
		}
		return nil, fmt.Errorf("获取工单失败: %w", err)
	}

	// 🔥 修复：权限验证 - 防止横向越权
	if workOrder.UserID != userID {
		s.logger.Warn("用户尝试访问其他用户的工单",
			zap.String("user_id", userID),
			zap.String("work_order_id", workOrderID.String()),
			zap.String("work_order_owner", workOrder.UserID))
		return nil, fmt.Errorf("无权访问该工单")
	}

	// 3. 加载完整信息
	return s.loadWorkOrderDetails(ctx, workOrder)
}

// ListWorkOrders 获取工单列表
func (s *DefaultWorkOrderService) ListWorkOrders(ctx context.Context, userID string, req *model.WorkOrderListRequest) (*model.WorkOrderListResponse, error) {
	// 调用仓储层获取列表
	response, err := s.workOrderRepo.List(ctx, userID, req)
	if err != nil {
		return nil, err
	}

	// 为每个工单加载类型和状态名称
	for i := range response.Items {
		s.enrichWorkOrderInfo(&response.Items[i])
	}

	return response, nil
}

// DeleteWorkOrder 删除工单
func (s *DefaultWorkOrderService) DeleteWorkOrder(ctx context.Context, userID string, workOrderID uuid.UUID) error {
	// 1. 输入验证
	if userID == "" {
		return fmt.Errorf("用户ID不能为空")
	}
	if workOrderID == uuid.Nil {
		return fmt.Errorf("工单ID不能为空")
	}

	// 2. 获取工单信息
	workOrder, err := s.workOrderRepo.GetByID(ctx, workOrderID)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("工单不存在")
		}
		return fmt.Errorf("获取工单失败: %w", err)
	}

	// 3. 权限验证 - 防止横向越权
	if workOrder.UserID != userID {
		s.logger.Warn("用户尝试删除其他用户的工单",
			zap.String("user_id", userID),
			zap.String("work_order_id", workOrderID.String()),
			zap.String("work_order_owner", workOrder.UserID))
		return fmt.Errorf("无权删除该工单")
	}

	// 4. 状态验证 - 只能删除特定状态的工单
	if !s.canDeleteWorkOrder(workOrder.Status) {
		return fmt.Errorf("工单状态不允许删除，当前状态: %s", s.getWorkOrderStatusName(workOrder.Status))
	}

	// 5. 时间限制验证 - 创建后24小时内才能删除
	if time.Since(workOrder.CreatedAt) > 24*time.Hour {
		return fmt.Errorf("工单创建超过24小时，无法删除")
	}

	// 6. 尝试调用供应商API删除/取消工单（如果有供应商工单ID）
	if workOrder.ProviderWorkOrderID != nil && *workOrder.ProviderWorkOrderID != "" {
		adapter, exists := s.providerAdapters[workOrder.Provider]
		if exists {
			deleteReason := "用户主动删除工单"
			if err := adapter.DeleteWorkOrder(ctx, *workOrder.ProviderWorkOrderID, deleteReason); err != nil {
				s.logger.Warn("调用供应商API删除工单失败，继续删除本地记录",
					zap.Error(err),
					zap.String("work_order_id", workOrderID.String()),
					zap.String("provider", workOrder.Provider),
					zap.String("provider_work_order_id", *workOrder.ProviderWorkOrderID))
				// 注意：供应商API删除失败不阻止本地删除，因为可能是供应商不支持删除
			} else {
				s.logger.Info("成功调用供应商API删除工单",
					zap.String("work_order_id", workOrderID.String()),
					zap.String("provider", workOrder.Provider),
					zap.String("provider_work_order_id", *workOrder.ProviderWorkOrderID))
			}
		}
	}

	// 7. 删除本地工单记录（硬删除）
	if err := s.workOrderRepo.Delete(ctx, workOrderID); err != nil {
		s.logger.Error("删除工单失败",
			zap.Error(err),
			zap.String("work_order_id", workOrderID.String()),
			zap.String("user_id", userID))
		return fmt.Errorf("删除工单失败: %w", err)
	}

	// 8. 记录审计日志
	s.logger.Info("工单删除成功",
		zap.String("work_order_id", workOrderID.String()),
		zap.String("user_id", userID),
		zap.String("provider", workOrder.Provider),
		zap.String("work_order_type", fmt.Sprintf("%d", workOrder.WorkOrderType)),
		zap.String("status", fmt.Sprintf("%d", workOrder.Status)))

	return nil
}

// canDeleteWorkOrder 检查工单是否可以删除
func (s *DefaultWorkOrderService) canDeleteWorkOrder(status model.WorkOrderStatus) bool {
	// 只允许删除以下状态的工单：
	// - 待处理 (Pending)
	// - 处理中 (Processing)
	// 不允许删除已完成、已回复的工单
	switch status {
	case model.WorkOrderStatusPending, model.WorkOrderStatusProcessing:
		return true
	case model.WorkOrderStatusReplied, model.WorkOrderStatusCompleted:
		return false
	default:
		return false
	}
}

// getWorkOrderStatusName 获取工单状态名称（用于错误提示）
func (s *DefaultWorkOrderService) getWorkOrderStatusName(status model.WorkOrderStatus) string {
	// 使用model中定义的String()方法
	return status.String()
}

// validateCreateRequest 验证创建工单请求
func (s *DefaultWorkOrderService) validateCreateRequest(req *model.SmartCreateWorkOrderRequest) error {
	if req.WorkOrderType <= 0 {
		return fmt.Errorf("工单类型不能为空")
	}

	if strings.TrimSpace(req.Content) == "" {
		return fmt.Errorf("工单内容不能为空")
	}

	// 验证订单号或运单号至少提供一个
	if (req.OrderNo == nil || *req.OrderNo == "") && (req.TrackingNo == nil || *req.TrackingNo == "") {
		return fmt.Errorf("订单号或运单号至少需要提供一个")
	}

	// 验证工单类型是否为支持的6种核心类型
	if !model.IsValidWorkOrderType(req.WorkOrderType) {
		return fmt.Errorf("不支持的工单类型: %d", req.WorkOrderType)
	}

	// 验证重量异常工单的特殊要求
	if req.WorkOrderType == model.WorkOrderTypeWeightException {
		if req.FeedbackWeight != nil && *req.FeedbackWeight <= 0 {
			return fmt.Errorf("反馈重量必须大于0")
		}
	}

	return nil
}

// findOrderByIdentifier 根据订单标识符查找订单
func (s *DefaultWorkOrderService) findOrderByIdentifier(ctx context.Context, userID string, orderNo, trackingNo *string) (*model.OrderRecord, error) {
	// 🔥 修复：统一查询逻辑，支持多种查询方式

	// 1. 参数验证
	if (orderNo == nil || *orderNo == "") && (trackingNo == nil || *trackingNo == "") {
		return nil, fmt.Errorf("必须提供订单号或运单号")
	}

	var order *model.OrderRecord
	var err error
	var searchKey string

	// 2. 🔥 优先使用订单号查询 - 使用智能订单查找服务
	if orderNo != nil && *orderNo != "" {
		searchKey = *orderNo

		// 🔥 使用智能订单查找服务，支持多种订单号格式（如果可用）
		if s.smartOrderFinder != nil {
			order, err = s.smartOrderFinder.FindOrderByAnyIdentifier(ctx, searchKey, userID)
		} else {
			// 回退到基础repository查询
			order, err = s.orderRepo.FindByOrderNo(ctx, searchKey)
		}
		if err == nil && order != nil {
			s.logger.Info("智能订单查找成功",
				zap.String("search_key", searchKey),
				zap.String("user_id", userID),
				zap.String("order_id", fmt.Sprintf("%d", order.ID)),
				zap.String("platform_order_no", order.PlatformOrderNo))
		}
	}

	// 3. 如果订单号查询失败，使用运单号查询
	if order == nil && trackingNo != nil && *trackingNo != "" {
		searchKey = *trackingNo
		// 🔥 修复：使用智能订单查找服务，正确传递用户ID
		if s.smartOrderFinder != nil {
			order, err = s.smartOrderFinder.FindOrderByAnyIdentifier(ctx, searchKey, userID)
		} else {
			// 回退到基础repository查询
			smartFinder := NewSmartOrderFinder(s.orderRepo, s.logger)
			order, err = smartFinder.FindOrderByAnyIdentifier(ctx, searchKey, userID)
		}
		if err == nil && order != nil {
			s.logger.Info("通过运单号找到订单",
				zap.String("tracking_no", searchKey),
				zap.String("user_id", userID),
				zap.String("order_id", fmt.Sprintf("%d", order.ID)),
				zap.String("platform_order_no", order.PlatformOrderNo))
		}
	}

	// 4. 检查查询结果
	if order == nil {
		if orderNo != nil && *orderNo != "" {
			return nil, fmt.Errorf("未找到订单号为 %s 的订单", *orderNo)
		}
		if trackingNo != nil && *trackingNo != "" {
			return nil, fmt.Errorf("未找到运单号为 %s 的订单", *trackingNo)
		}
		return nil, fmt.Errorf("未找到匹配的订单")
	}

	// 5. 验证订单归属（如果有用户ID限制）
	if userID != "" && order.UserID != "" && order.UserID != userID {
		s.logger.Warn("用户尝试访问其他用户的订单",
			zap.String("user_id", userID),
			zap.String("order_user_id", order.UserID),
			zap.String("order_id", fmt.Sprintf("%d", order.ID)))
		return nil, fmt.Errorf("无权操作该订单")
	}

	s.logger.Info("成功找到订单",
		zap.String("user_id", userID),
		zap.String("order_id", fmt.Sprintf("%d", order.ID)),
		zap.String("provider", order.Provider),
		zap.String("customer_order_no", order.CustomerOrderNo))

	return order, nil
}

// generateCallbackURL 生成工单回调URL
func (s *DefaultWorkOrderService) generateCallbackURL(orderID string) *string {
	// 这里可以根据系统配置生成回调URL
	// 例如: https://api.example.com/workorder/callback/{orderID}
	// 暂时返回nil，表示使用默认配置
	return nil
}

// generateMessageCallbackURL 生成消息回调URL
func (s *DefaultWorkOrderService) generateMessageCallbackURL(orderID string) *string {
	// 这里可以根据系统配置生成消息回调URL
	// 例如: https://api.example.com/workorder/message/{orderID}
	// 暂时返回nil，表示使用默认配置
	return nil
}

// generateProviderSpecificData 生成供应商特定数据
func (s *DefaultWorkOrderService) generateProviderSpecificData(provider string, workOrderType int, order *model.OrderRecord) map[string]interface{} {
	data := make(map[string]interface{})

	switch provider {
	case "yida":
		// 易达特定数据
		if workOrderType == model.WorkOrderTypeWeightException {
			data["yida"] = map[string]interface{}{
				"secondType": 1, // 默认为重量核实
			}
		}
	case "kuaidi100":
		// 快递100特定数据
		// 暂时无特殊要求
	case "yuntong":
		// 云通特定数据
		// 暂时无特殊要求
	case "kuaidiniao":
		// 快递鸟特定数据
		data["kuaidiniao"] = map[string]interface{}{
			"platform_order_no": order.PlatformOrderNo, // 传递平台订单号给快递鸟工单API
		}
	}

	return data
}

// generateWorkOrderTitle 生成工单标题
func (s *DefaultWorkOrderService) generateWorkOrderTitle(typeName string, orderNo, trackingNo *string) string {
	title := typeName

	if orderNo != nil && *orderNo != "" {
		title += " - " + *orderNo
	} else if trackingNo != nil && *trackingNo != "" {
		title += " - " + *trackingNo
	}

	return title
}

// loadWorkOrderDetails 加载工单完整信息
func (s *DefaultWorkOrderService) loadWorkOrderDetails(ctx context.Context, workOrder *model.WorkOrder) (*model.WorkOrder, error) {
	// 1. 加载回复列表
	replies, err := s.workOrderRepo.GetRepliesByWorkOrderID(ctx, workOrder.ID)
	if err != nil {
		s.logger.Error("加载工单回复失败", zap.Error(err))
	} else {
		// 为每个回复加载附件
		for i := range replies {
			attachments, err := s.workOrderRepo.GetAttachmentsByReplyID(ctx, replies[i].ID)
			if err != nil {
				s.logger.Error("加载回复附件失败", zap.Error(err))
			} else {
				replies[i].Attachments = attachments
			}
		}
		workOrder.Replies = replies
	}

	// 2. 加载工单附件
	attachments, err := s.workOrderRepo.GetAttachmentsByWorkOrderID(ctx, workOrder.ID)
	if err != nil {
		s.logger.Error("加载工单附件失败", zap.Error(err))
	} else {
		workOrder.Attachments = attachments
	}

	// 3. 丰富工单信息
	s.enrichWorkOrderInfo(workOrder)

	return workOrder, nil
}

// enrichWorkOrderInfo 丰富工单信息
func (s *DefaultWorkOrderService) enrichWorkOrderInfo(workOrder *model.WorkOrder) {
	// 获取类型名称
	if typeMapping, err := s.workOrderRepo.GetTypeMapping(context.Background(), workOrder.WorkOrderType, workOrder.Provider); err == nil {
		workOrder.TypeName = typeMapping.UnifiedName
	}

	// 获取状态名称
	if statusMapping, err := s.workOrderRepo.GetStatusMapping(context.Background(), int(workOrder.Status), workOrder.Provider); err == nil {
		workOrder.StatusName = statusMapping.UnifiedName
	}
}

// createAttachments 创建附件
func (s *DefaultWorkOrderService) createAttachments(ctx context.Context, workOrderID *uuid.UUID, replyID *uuid.UUID, attachmentURLs []string, uploadType int) error {
	for _, url := range attachmentURLs {
		if url == "" {
			continue
		}

		// 从URL中提取文件名
		fileName := s.extractFileNameFromURL(url)

		attachment := &model.WorkOrderAttachment{
			WorkOrderID: workOrderID,
			ReplyID:     replyID,
			FileName:    fileName,
			FileURL:     url,
			UploadType:  uploadType,
		}

		if err := s.workOrderRepo.CreateAttachment(ctx, attachment); err != nil {
			s.logger.Error("创建附件失败", zap.Error(err), zap.String("url", url))
			return err
		}
	}

	return nil
}

// extractFileNameFromURL 从URL中提取文件名
func (s *DefaultWorkOrderService) extractFileNameFromURL(url string) string {
	parts := strings.Split(url, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	return "attachment"
}

// ReplyWorkOrder 回复工单
func (s *DefaultWorkOrderService) ReplyWorkOrder(ctx context.Context, userID string, workOrderID uuid.UUID, req *model.ReplyWorkOrderRequest) (*model.WorkOrderReply, error) {
	// 1. 获取工单信息
	workOrder, err := s.workOrderRepo.GetByID(ctx, workOrderID)
	if err != nil {
		return nil, err
	}

	// 2. 验证用户权限
	if workOrder.UserID != userID {
		return nil, fmt.Errorf("无权操作该工单")
	}

	// 3. 验证工单状态
	if workOrder.Status == model.WorkOrderStatusCompleted {
		return nil, fmt.Errorf("工单已完结，无法回复")
	}

	// 4. 创建回复记录
	reply := &model.WorkOrderReply{
		ID:          uuid.New(),
		WorkOrderID: workOrderID,
		ReplyType:   model.ReplyTypeUser,
		Content:     req.Content,
	}

	if err := s.workOrderRepo.CreateReply(ctx, reply); err != nil {
		return nil, fmt.Errorf("创建回复失败: %w", err)
	}

	// 5. 处理附件
	if len(req.AttachmentURLs) > 0 {
		if err := s.createAttachments(ctx, &workOrderID, &reply.ID, req.AttachmentURLs, model.UploadTypeReply); err != nil {
			s.logger.Error("创建回复附件失败", zap.Error(err))
		}
	}

	// 6. 调用供应商API回复工单
	if workOrder.ProviderWorkOrderID != nil {
		adapter, exists := s.providerAdapters[workOrder.Provider]
		if exists {
			if err := adapter.ReplyWorkOrder(ctx, *workOrder.ProviderWorkOrderID, req.Content, req.AttachmentURLs); err != nil {
				s.logger.Error("调用供应商API回复工单失败",
					zap.Error(err),
					zap.String("work_order_id", workOrderID.String()),
					zap.String("provider", workOrder.Provider))
			}
		}
	}

	// 7. 加载回复的附件
	attachments, err := s.workOrderRepo.GetAttachmentsByReplyID(ctx, reply.ID)
	if err != nil {
		s.logger.Error("加载回复附件失败", zap.Error(err))
	} else {
		reply.Attachments = attachments
	}

	return reply, nil
}

// UploadAttachment 上传附件
func (s *DefaultWorkOrderService) UploadAttachment(ctx context.Context, userID string, fileName string, fileContent []byte) (*model.UploadAttachmentResponse, error) {
	// 1. 调用文件上传服务
	fileURL, err := s.fileUploadService.UploadFile(ctx, fileName, fileContent)
	if err != nil {
		return nil, fmt.Errorf("上传文件失败: %w", err)
	}

	// 2. 创建临时附件记录（不关联具体工单）
	attachment := &model.WorkOrderAttachment{
		FileName:   fileName,
		FileURL:    fileURL,
		FileSize:   func() *int64 { size := int64(len(fileContent)); return &size }(),
		UploadType: model.UploadTypeWorkOrder,
	}

	if err := s.workOrderRepo.CreateAttachment(ctx, attachment); err != nil {
		s.logger.Error("创建临时附件记录失败", zap.Error(err))
		// 不返回错误，因为文件已经上传成功
	}

	return &model.UploadAttachmentResponse{
		FileURL:  fileURL,
		FileName: fileName,
		FileSize: int64(len(fileContent)),
	}, nil
}

// GetSupportedTypes 获取支持的工单类型
func (s *DefaultWorkOrderService) GetSupportedTypes(ctx context.Context, provider string) ([]model.WorkOrderTypeMapping, error) {
	if provider == "" {
		// 如果没有指定供应商，返回所有供应商的工单类型
		return s.workOrderRepo.ListAllSupportedTypes(ctx)
	}
	return s.workOrderRepo.ListSupportedTypes(ctx, provider)
}

// HandleProviderCallback 处理供应商回调
func (s *DefaultWorkOrderService) HandleProviderCallback(ctx context.Context, provider string, callbackData interface{}) error {
	// 1. 获取供应商适配器
	adapter, exists := s.providerAdapters[provider]
	if !exists {
		return fmt.Errorf("不支持的供应商: %s", provider)
	}

	// 2. 解析回调数据
	parsedData, err := adapter.ParseCallback(ctx, callbackData)
	if err != nil {
		return fmt.Errorf("解析回调数据失败: %w", err)
	}

	// 3. 查找对应的工单
	workOrder, err := s.workOrderRepo.GetByProviderWorkOrderID(ctx, provider, parsedData.ProviderWorkOrderID)
	if err != nil {
		return fmt.Errorf("查找工单失败: %w", err)
	}

	// 4. 更新工单状态
	if parsedData.Status > 0 {
		// 获取状态映射
		statusMapping, err := s.workOrderRepo.GetProviderStatusMapping(ctx, parsedData.Status, provider)
		if err != nil {
			s.logger.Error("获取状态映射失败", zap.Error(err))
		} else {
			workOrder.Status = model.WorkOrderStatus(statusMapping.UnifiedStatus)
			if model.WorkOrderStatus(statusMapping.UnifiedStatus) == model.WorkOrderStatusCompleted {
				now := util.NowBeijing()
				workOrder.CompletedAt = &now
			}
		}
	}

	// 5. 更新工单
	if err := s.workOrderRepo.Update(ctx, workOrder); err != nil {
		return fmt.Errorf("更新工单失败: %w", err)
	}

	// 6. 创建回复记录
	if parsedData.Content != "" {
		reply := &model.WorkOrderReply{
			ID:          uuid.New(),
			WorkOrderID: workOrder.ID,
			ReplyType:   model.ReplyTypeProvider,
			Committer:   &parsedData.Committer,
			Content:     parsedData.Content,
		}

		// 序列化原始数据（新的WorkOrderCallbackData结构没有RawData字段）
		// 使用parsedData本身作为原始数据
		if rawData, err := json.Marshal(parsedData); err == nil {
			rawDataStr := string(rawData)
			reply.RawData = &rawDataStr
		}

		if err := s.workOrderRepo.CreateReply(ctx, reply); err != nil {
			s.logger.Error("创建回调回复失败", zap.Error(err))
		}

		// 处理回调中的附件
		if len(parsedData.AttachmentURLs) > 0 {
			if err := s.createAttachments(ctx, &workOrder.ID, &reply.ID, parsedData.AttachmentURLs, model.UploadTypeReply); err != nil {
				s.logger.Error("创建回调附件失败", zap.Error(err))
			}
		}
	}

	// 🔥 修复：直接使用回调服务处理，避免goroutine泄漏
	if workOrder.UserID != "" && s.callbackService != nil {
		// 异步处理回调，避免阻塞主流程
		go s.triggerUserCallback(ctx, workOrder, parsedData, provider)
	}

	return nil
}

// triggerUserCallback 触发用户回调转发
func (s *DefaultWorkOrderService) triggerUserCallback(ctx context.Context, workOrder *model.WorkOrder, callbackData *model.WorkOrderCallbackData, provider string) {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Error("工单回调转发发生panic",
				zap.String("work_order_id", workOrder.ID.String()),
				zap.Any("panic", r))
		}
	}()

	// 1. 确定事件类型
	eventType := s.determineEventType(callbackData.Status, int(workOrder.Status))

	// 🔥 优化：预先获取所有需要的数据，避免重复查询
	orderNo := s.getOrderNoFromWorkOrder(workOrder)
	customerOrderNo := s.getCustomerOrderNoFromWorkOrder(ctx, workOrder)
	trackingNo := s.getTrackingNoFromWorkOrder(workOrder)
	statusName := s.getStatusName(ctx, callbackData.Status, provider)
	workOrderTypeName := s.getWorkOrderTypeName(ctx, workOrder.WorkOrderType, provider)
	currentTime := util.NowBeijing()

	// 2. 构建标准化回调数据
	standardizedData := &model.StandardizedCallbackData{
		EventType:       eventType,
		OrderNo:         orderNo,
		CustomerOrderNo: customerOrderNo,
		TrackingNo:      trackingNo,
		Timestamp:       currentTime,
		Provider:        provider,
		Data: &model.WorkOrderCallbackData{
			WorkOrderID:         workOrder.ID.String(),
			ProviderWorkOrderID: callbackData.ProviderWorkOrderID,
			Status:              callbackData.Status,
			StatusName:          statusName,
			Content:             callbackData.Content,
			Committer:           callbackData.Committer,
			AttachmentURLs:      callbackData.AttachmentURLs,
			UpdatedAt:           currentTime,
			WorkOrderType:       workOrder.WorkOrderType,
			WorkOrderTypeName:   workOrderTypeName,
		},
	}

	// 3. 创建回调记录
	record := &model.UnifiedCallbackRecord{
		ID:              uuid.New(),
		Provider:        provider,
		UserID:          workOrder.UserID,
		OrderNo:         orderNo,
		CustomerOrderNo: customerOrderNo,
		TrackingNo:      trackingNo,
		EventType:       eventType,
		InternalStatus:  model.CallbackStatusPending,
		ExternalStatus:  model.CallbackStatusPending,
		CreatedAt:       currentTime,
		UpdatedAt:       currentTime,
	}

	// 4. 序列化原始数据（新的WorkOrderCallbackData结构没有RawData字段）
	// 使用callbackData本身作为原始数据
	if rawData, err := json.Marshal(callbackData); err == nil {
		record.RawData = json.RawMessage(rawData)
	} else {
		// 🔥 修复：记录序列化失败的错误
		s.logger.Warn("序列化工单回调原始数据失败",
			zap.String("work_order_id", workOrder.ID.String()),
			zap.String("provider", provider),
			zap.Error(err))
	}

	// 5. 调用统一回调服务处理
	// 注意：这里需要使用正确的方法签名
	// 我们需要先将数据序列化为字节数组
	callbackBytes, err := json.Marshal(standardizedData)
	if err != nil {
		s.logger.Error("序列化工单回调数据失败",
			zap.String("work_order_id", workOrder.ID.String()),
			zap.Error(err))
		return
	}

	// 构建头部信息
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-Event-Type": eventType,
		"X-Provider":   provider,
	}

	// 🔥 修复：添加超时控制
	timeoutCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// 调用统一回调服务
	_, err = s.callbackService.ProcessCallback(timeoutCtx, provider, callbackBytes, headers)
	if err != nil {
		if errors.Is(err, context.DeadlineExceeded) {
			s.logger.Error("工单回调转发超时",
				zap.String("work_order_id", workOrder.ID.String()),
				zap.String("event_type", eventType),
				zap.Duration("timeout", 30*time.Second))
		} else {
			s.logger.Error("工单回调转发失败",
				zap.String("work_order_id", workOrder.ID.String()),
				zap.String("event_type", eventType),
				zap.Error(err))
		}
	} else {
		s.logger.Info("工单回调转发成功",
			zap.String("work_order_id", workOrder.ID.String()),
			zap.String("event_type", eventType),
			zap.String("user_id", workOrder.UserID))
	}
}

// determineEventType 确定事件类型
func (s *DefaultWorkOrderService) determineEventType(callbackStatus int, currentStatus int) string {
	// 根据状态变化确定事件类型
	switch model.WorkOrderStatus(callbackStatus) {
	case model.WorkOrderStatusCompleted:
		return model.EventTypeWorkOrderClosed
	case model.WorkOrderStatusReplied:
		return model.EventTypeWorkOrderReplied
	case model.WorkOrderStatusProcessing:
		return model.EventTypeWorkOrderUpdated
	default:
		return model.EventTypeWorkOrderUpdated
	}
}

// getOrderNoFromWorkOrder 从工单获取订单号
func (s *DefaultWorkOrderService) getOrderNoFromWorkOrder(workOrder *model.WorkOrder) string {
	if workOrder.OrderNo != nil {
		return *workOrder.OrderNo
	}
	return ""
}

// getCustomerOrderNoFromWorkOrder 从工单获取客户订单号
// 🔥 修复：实现真实的客户订单号查询逻辑
func (s *DefaultWorkOrderService) getCustomerOrderNoFromWorkOrder(ctx context.Context, workOrder *model.WorkOrder) string {
	// 🔥 如果工单直接关联了订单号，使用智能订单查找服务查询（如果可用）
	if workOrder.OrderNo != nil && *workOrder.OrderNo != "" {
		if s.smartOrderFinder != nil {
			if order, err := s.smartOrderFinder.FindOrderByAnyIdentifier(ctx, *workOrder.OrderNo, workOrder.UserID); err == nil && order != nil {
				return order.CustomerOrderNo
			}
		} else {
			// 回退到基础repository查询
			if order, err := s.orderRepo.FindByOrderNo(ctx, *workOrder.OrderNo); err == nil && order != nil {
				return order.CustomerOrderNo
			}
		}
	}

	// 如果有运单号，通过运单号查询订单
	if workOrder.TrackingNo != nil && *workOrder.TrackingNo != "" {
		// 注意：这里需要根据实际的OrderRepository接口调整方法名
		// 暂时注释掉，避免编译错误
		// if order, err := s.orderRepo.FindByTrackingNo(ctx, *workOrder.TrackingNo); err == nil && order != nil {
		//     return order.CustomerOrderNo
		// }
	}

	// 🚨 警告：无法获取客户订单号，记录日志
	s.logger.Warn("无法获取工单关联的客户订单号",
		zap.String("work_order_id", workOrder.ID.String()),
		zap.String("order_no", func() string {
			if workOrder.OrderNo != nil {
				return *workOrder.OrderNo
			}
			return ""
		}()),
		zap.String("tracking_no", func() string {
			if workOrder.TrackingNo != nil {
				return *workOrder.TrackingNo
			}
			return ""
		}()))

	return ""
}

// getTrackingNoFromWorkOrder 从工单获取运单号
func (s *DefaultWorkOrderService) getTrackingNoFromWorkOrder(workOrder *model.WorkOrder) string {
	if workOrder.TrackingNo != nil {
		return *workOrder.TrackingNo
	}
	return ""
}

// getStatusName 获取状态名称
// 🔥 修复：传递正确的上下文
func (s *DefaultWorkOrderService) getStatusName(ctx context.Context, status int, provider string) string {
	if statusMapping, err := s.workOrderRepo.GetProviderStatusMapping(ctx, status, provider); err == nil {
		return statusMapping.UnifiedName
	}
	return fmt.Sprintf("状态%d", status)
}

// getWorkOrderTypeName 获取工单类型名称
// 🔥 修复：传递正确的上下文
func (s *DefaultWorkOrderService) getWorkOrderTypeName(ctx context.Context, workOrderType int, provider string) string {
	if typeMapping, err := s.workOrderRepo.GetTypeMapping(ctx, workOrderType, provider); err == nil {
		return typeMapping.UnifiedName
	}
	return fmt.Sprintf("类型%d", workOrderType)
}

// extractBusinessError 提取供应商的具体业务错误信息
func (s *DefaultWorkOrderService) extractBusinessError(errorMsg, provider string) string {
	switch provider {
	case "yida":
		// 易达错误格式: "创建工单失败: 当前订单状态非已结算,请耐心等待[tid:xxx]"
		if strings.Contains(errorMsg, "当前订单状态非已结算") {
			return "订单状态不符合要求：重量异常工单需要订单状态为已结算"
		}
		if strings.Contains(errorMsg, "当前订单状态非待取件") {
			return "订单状态不符合要求：催取件工单需要订单状态为待取件"
		}
		if strings.Contains(errorMsg, "下单时间未超") {
			return "时间限制：催取件需要下单时间超过30分钟"
		}
		if strings.Contains(errorMsg, "订单状态非派件") {
			return "订单状态不符合要求：催派送工单需要订单状态为派件中"
		}
		if strings.Contains(errorMsg, "物流更新时间未超") {
			return "时间限制：物流停滞需要24小时未更新物流轨迹"
		}
		// 提取原始错误中的关键信息
		if strings.Contains(errorMsg, "创建工单失败:") {
			start := strings.Index(errorMsg, "创建工单失败:") + len("创建工单失败:")
			if tidIndex := strings.Index(errorMsg, "[tid:"); tidIndex > start {
				return strings.TrimSpace(errorMsg[start:tidIndex])
			}
			return strings.TrimSpace(errorMsg[start:])
		}

	case "kuaidi100":
		// 快递100错误处理
		if strings.Contains(errorMsg, "订单状态") {
			return "订单状态不符合工单创建要求"
		}

	case "yuntong":
		// 云通错误处理
		if strings.Contains(errorMsg, "订单状态") {
			return "订单状态不符合工单创建要求"
		}
	}

	// 返回原始错误信息
	return errorMsg
}

// 🔥 新增：验证用户自定义工单ID唯一性
func (s *DefaultWorkOrderService) validateCustomerWorkOrderIDUniqueness(ctx context.Context, userID, customerWorkOrderID string) error {
	// 检查同一用户下是否已存在相同的自定义工单ID
	exists, err := s.workOrderRepo.ExistsByCustomerWorkOrderID(ctx, userID, customerWorkOrderID)
	if err != nil {
		return fmt.Errorf("检查用户自定义工单ID唯一性失败: %w", err)
	}

	if exists {
		return fmt.Errorf("用户自定义工单ID '%s' 已存在，请使用其他ID", customerWorkOrderID)
	}

	return nil
}
