package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// FileUploadService 文件上传服务接口
type FileUploadService interface {
	UploadFile(ctx context.Context, fileName string, fileContent []byte) (string, error)
}

// LocalFileUploadService 本地文件上传服务实现
type LocalFileUploadService struct {
	uploadDir string
	baseURL   string
	logger    *zap.Logger
}

// NewLocalFileUploadService 创建本地文件上传服务
func NewLocalFileUploadService(uploadDir, baseURL string, logger *zap.Logger) FileUploadService {
	return &LocalFileUploadService{
		uploadDir: uploadDir,
		baseURL:   baseURL,
		logger:    logger,
	}
}

// UploadFile 上传文件
func (s *LocalFileUploadService) UploadFile(ctx context.Context, fileName string, fileContent []byte) (string, error) {
	// 1. 验证文件类型
	if !s.isAllowedFileType(fileName) {
		return "", fmt.Errorf("不支持的文件类型")
	}

	// 2. 生成唯一文件名
	uniqueFileName := s.generateUniqueFileName(fileName, fileContent)

	// 3. 创建目录结构（按日期分组）
	dateDir := util.NowBeijing().Format("2006/01/02")
	fullDir := filepath.Join(s.uploadDir, "workorders", dateDir)
	if err := os.MkdirAll(fullDir, 0755); err != nil {
		s.logger.Error("创建上传目录失败", zap.Error(err), zap.String("dir", fullDir))
		return "", fmt.Errorf("创建上传目录失败: %w", err)
	}

	// 4. 写入文件
	filePath := filepath.Join(fullDir, uniqueFileName)
	file, err := os.Create(filePath)
	if err != nil {
		s.logger.Error("创建文件失败", zap.Error(err), zap.String("path", filePath))
		return "", fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	if _, err := file.Write(fileContent); err != nil {
		s.logger.Error("写入文件失败", zap.Error(err), zap.String("path", filePath))
		return "", fmt.Errorf("写入文件失败: %w", err)
	}

	// 5. 生成访问URL
	fileURL := fmt.Sprintf("%s/uploads/workorders/%s/%s",
		strings.TrimRight(s.baseURL, "/"),
		dateDir,
		uniqueFileName)

	s.logger.Info("文件上传成功",
		zap.String("original_name", fileName),
		zap.String("unique_name", uniqueFileName),
		zap.String("url", fileURL),
		zap.Int("size", len(fileContent)))

	return fileURL, nil
}

// isAllowedFileType 检查是否为允许的文件类型
func (s *LocalFileUploadService) isAllowedFileType(fileName string) bool {
	allowedExtensions := []string{
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", // 图片
		".pdf", ".doc", ".docx", ".txt", ".rtf", // 文档
		".zip", ".rar", ".7z", // 压缩包
	}

	ext := strings.ToLower(filepath.Ext(fileName))
	for _, allowedExt := range allowedExtensions {
		if ext == allowedExt {
			return true
		}
	}

	return false
}

// generateUniqueFileName 生成唯一文件名
func (s *LocalFileUploadService) generateUniqueFileName(originalName string, content []byte) string {
	// 使用时间戳 + 文件内容MD5 + 原始扩展名生成唯一文件名
	ext := filepath.Ext(originalName)
	timestamp := util.NowBeijing().Format("20060102150405")

	// 计算文件内容的MD5
	hash := md5.Sum(content)
	hashStr := fmt.Sprintf("%x", hash)[:8] // 取前8位

	return fmt.Sprintf("%s_%s%s", timestamp, hashStr, ext)
}

// OSSFileUploadService OSS文件上传服务实现（示例）
type OSSFileUploadService struct {
	endpoint        string
	accessKeyID     string
	accessKeySecret string
	bucketName      string
	logger          *zap.Logger
}

// NewOSSFileUploadService 创建OSS文件上传服务
func NewOSSFileUploadService(endpoint, accessKeyID, accessKeySecret, bucketName string, logger *zap.Logger) FileUploadService {
	return &OSSFileUploadService{
		endpoint:        endpoint,
		accessKeyID:     accessKeyID,
		accessKeySecret: accessKeySecret,
		bucketName:      bucketName,
		logger:          logger,
	}
}

// UploadFile OSS上传文件实现
func (s *OSSFileUploadService) UploadFile(ctx context.Context, fileName string, fileContent []byte) (string, error) {
	// TODO: 实现OSS上传逻辑
	// 这里是示例代码，实际需要集成阿里云OSS SDK

	// 1. 验证文件类型
	if !s.isAllowedFileType(fileName) {
		return "", fmt.Errorf("不支持的文件类型")
	}

	// 2. 生成对象键
	objectKey := s.generateObjectKey(fileName, fileContent)

	// 3. 上传到OSS
	// client, err := oss.New(s.endpoint, s.accessKeyID, s.accessKeySecret)
	// if err != nil {
	//     return "", fmt.Errorf("创建OSS客户端失败: %w", err)
	// }
	//
	// bucket, err := client.Bucket(s.bucketName)
	// if err != nil {
	//     return "", fmt.Errorf("获取OSS存储桶失败: %w", err)
	// }
	//
	// err = bucket.PutObject(objectKey, bytes.NewReader(fileContent))
	// if err != nil {
	//     return "", fmt.Errorf("上传文件到OSS失败: %w", err)
	// }

	// 4. 生成访问URL
	fileURL := fmt.Sprintf("https://%s.%s/%s", s.bucketName, s.endpoint, objectKey)

	s.logger.Info("文件上传到OSS成功",
		zap.String("original_name", fileName),
		zap.String("object_key", objectKey),
		zap.String("url", fileURL),
		zap.Int("size", len(fileContent)))

	return fileURL, nil
}

// isAllowedFileType OSS版本的文件类型检查
func (s *OSSFileUploadService) isAllowedFileType(fileName string) bool {
	allowedExtensions := []string{
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", // 图片
		".pdf", ".doc", ".docx", ".txt", ".rtf", // 文档
		".zip", ".rar", ".7z", // 压缩包
	}

	ext := strings.ToLower(filepath.Ext(fileName))
	for _, allowedExt := range allowedExtensions {
		if ext == allowedExt {
			return true
		}
	}

	return false
}

// generateObjectKey 生成OSS对象键
func (s *OSSFileUploadService) generateObjectKey(originalName string, content []byte) string {
	// 生成对象键：workorders/2025/06/20/timestamp_hash.ext
	ext := filepath.Ext(originalName)
	dateDir := util.NowBeijing().Format("2006/01/02")
	timestamp := util.NowBeijing().Format("20060102150405")

	// 计算文件内容的MD5
	hash := md5.Sum(content)
	hashStr := fmt.Sprintf("%x", hash)[:8] // 取前8位

	return fmt.Sprintf("workorders/%s/%s_%s%s", dateDir, timestamp, hashStr, ext)
}

// MockFileUploadService 模拟文件上传服务（用于测试）
type MockFileUploadService struct {
	baseURL string
	logger  *zap.Logger
}

// NewMockFileUploadService 创建模拟文件上传服务
func NewMockFileUploadService(baseURL string, logger *zap.Logger) FileUploadService {
	return &MockFileUploadService{
		baseURL: baseURL,
		logger:  logger,
	}
}

// UploadFile 模拟上传文件
func (s *MockFileUploadService) UploadFile(ctx context.Context, fileName string, fileContent []byte) (string, error) {
	// 生成模拟URL
	timestamp := util.NowBeijing().Format("20060102150405")
	hash := md5.Sum(fileContent)
	hashStr := fmt.Sprintf("%x", hash)[:8]

	ext := filepath.Ext(fileName)
	mockFileName := fmt.Sprintf("%s_%s%s", timestamp, hashStr, ext)

	fileURL := fmt.Sprintf("%s/mock/uploads/workorders/%s",
		strings.TrimRight(s.baseURL, "/"),
		mockFileName)

	s.logger.Info("模拟文件上传成功",
		zap.String("original_name", fileName),
		zap.String("mock_url", fileURL),
		zap.Int("size", len(fileContent)))

	return fileURL, nil
}
