package service

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// 监控配置常量
const (
	DefaultMetricsInterval    = 30 * time.Second
	DefaultAlertingInterval   = 1 * time.Minute
	DefaultMemoryThreshold    = 1024 // MB
	DefaultGoroutineThreshold = 1000
	MaxFeeDifferencesHistory  = 1000
)

// MetricsCollector 指标收集器
type MetricsCollector struct {
	mutex sync.RWMutex

	// 业务指标
	TotalRequests      int64            `json:"total_requests"`
	SuccessRequests    int64            `json:"success_requests"`
	FailedRequests     int64            `json:"failed_requests"`
	AverageLatency     time.Duration    `json:"average_latency"`
	RequestsByEndpoint map[string]int64 `json:"requests_by_endpoint"`

	// 系统指标
	MemoryUsage    uint64 `json:"memory_usage"`
	GoroutineCount int    `json:"goroutine_count"`
	GCCount        uint32 `json:"gc_count"`

	// 数据库指标
	DBConnections int   `json:"db_connections"`
	DBQueries     int64 `json:"db_queries"`
	DBErrors      int64 `json:"db_errors"`

	// 缓存指标
	CacheHits    int64   `json:"cache_hits"`
	CacheMisses  int64   `json:"cache_misses"`
	CacheHitRate float64 `json:"cache_hit_rate"`

	// 计费指标
	BillingUpdates int64     `json:"billing_updates"`
	BillingErrors  int64     `json:"billing_errors"`
	FeeDifferences []float64 `json:"fee_differences"`

	startTime time.Time
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector() *MetricsCollector {
	return &MetricsCollector{
		RequestsByEndpoint: make(map[string]int64),
		FeeDifferences:     make([]float64, 0),
		startTime:          util.NowBeijing(),
	}
}

// RecordRequest 记录请求
func (mc *MetricsCollector) RecordRequest(endpoint string, success bool, latency time.Duration) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	mc.TotalRequests++
	mc.RequestsByEndpoint[endpoint]++

	if success {
		mc.SuccessRequests++
	} else {
		mc.FailedRequests++
	}

	// 计算平均延迟（简化版）
	if mc.TotalRequests == 1 {
		mc.AverageLatency = latency
	} else {
		mc.AverageLatency = (mc.AverageLatency + latency) / 2
	}
}

// RecordBillingUpdate 记录计费更新
func (mc *MetricsCollector) RecordBillingUpdate(success bool, feeDifference float64) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	mc.BillingUpdates++
	if !success {
		mc.BillingErrors++
	}

	if feeDifference != 0 {
		mc.FeeDifferences = append(mc.FeeDifferences, feeDifference)
		// 保持最近N条记录，避免内存无限增长
		if len(mc.FeeDifferences) > MaxFeeDifferencesHistory {
			mc.FeeDifferences = mc.FeeDifferences[len(mc.FeeDifferences)-MaxFeeDifferencesHistory:]
		}
	}
}

// RecordCacheOperation 记录缓存操作
func (mc *MetricsCollector) RecordCacheOperation(hit bool) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	if hit {
		mc.CacheHits++
	} else {
		mc.CacheMisses++
	}

	total := mc.CacheHits + mc.CacheMisses
	if total > 0 {
		mc.CacheHitRate = float64(mc.CacheHits) / float64(total)
	}
}

// RecordDBOperation 记录数据库操作
func (mc *MetricsCollector) RecordDBOperation(success bool) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	mc.DBQueries++
	if !success {
		mc.DBErrors++
	}
}

// UpdateSystemMetrics 更新系统指标
func (mc *MetricsCollector) UpdateSystemMetrics() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	mc.MemoryUsage = m.Alloc
	mc.GoroutineCount = runtime.NumGoroutine()
	mc.GCCount = m.NumGC
}

// GetMetrics 获取指标快照
func (mc *MetricsCollector) GetMetrics() map[string]interface{} {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	// 计算成功率
	var successRate float64
	if mc.TotalRequests > 0 {
		successRate = float64(mc.SuccessRequests) / float64(mc.TotalRequests)
	}

	// 计算平均费用差额
	var avgFeeDifference float64
	if len(mc.FeeDifferences) > 0 {
		sum := 0.0
		for _, diff := range mc.FeeDifferences {
			sum += diff
		}
		avgFeeDifference = sum / float64(len(mc.FeeDifferences))
	}

	// 计算运行时间
	uptime := time.Since(mc.startTime)

	return map[string]interface{}{
		"uptime":                uptime.String(),
		"total_requests":        mc.TotalRequests,
		"success_requests":      mc.SuccessRequests,
		"failed_requests":       mc.FailedRequests,
		"success_rate":          successRate,
		"average_latency":       mc.AverageLatency.String(),
		"requests_by_endpoint":  mc.RequestsByEndpoint,
		"memory_usage_mb":       mc.MemoryUsage / 1024 / 1024,
		"goroutine_count":       mc.GoroutineCount,
		"gc_count":              mc.GCCount,
		"db_connections":        mc.DBConnections,
		"db_queries":            mc.DBQueries,
		"db_errors":             mc.DBErrors,
		"cache_hits":            mc.CacheHits,
		"cache_misses":          mc.CacheMisses,
		"cache_hit_rate":        mc.CacheHitRate,
		"billing_updates":       mc.BillingUpdates,
		"billing_errors":        mc.BillingErrors,
		"avg_fee_difference":    avgFeeDifference,
		"fee_differences_count": len(mc.FeeDifferences),
	}
}

// MonitoringService 监控服务
type MonitoringService struct {
	collector *MetricsCollector
	logger    *zap.Logger
	stopCh    chan struct{}
	wg        sync.WaitGroup
}

// NewMonitoringService 创建监控服务
func NewMonitoringService(logger *zap.Logger) *MonitoringService {
	return &MonitoringService{
		collector: NewMetricsCollector(),
		logger:    logger,
		stopCh:    make(chan struct{}),
	}
}

// Start 启动监控服务
func (ms *MonitoringService) Start(ctx context.Context) {
	ms.wg.Add(1)
	go ms.metricsCollectionLoop(ctx)

	ms.wg.Add(1)
	go ms.alertingLoop(ctx)

	ms.logger.Info("监控服务已启动")
}

// Stop 停止监控服务
func (ms *MonitoringService) Stop() {
	close(ms.stopCh)
	ms.wg.Wait()
	ms.logger.Info("监控服务已停止")
}

// GetCollector 获取指标收集器
func (ms *MonitoringService) GetCollector() *MetricsCollector {
	return ms.collector
}

// metricsCollectionLoop 指标收集循环
func (ms *MonitoringService) metricsCollectionLoop(ctx context.Context) {
	defer ms.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ms.stopCh:
			return
		case <-ticker.C:
			ms.collector.UpdateSystemMetrics()
			ms.logMetrics()
		}
	}
}

// alertingLoop 告警循环
func (ms *MonitoringService) alertingLoop(ctx context.Context) {
	defer ms.wg.Done()

	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ms.stopCh:
			return
		case <-ticker.C:
			ms.checkAlerts()
		}
	}
}

// logMetrics 记录指标日志
func (ms *MonitoringService) logMetrics() {
	metrics := ms.collector.GetMetrics()

	ms.logger.Info("系统指标",
		zap.Any("memory_usage_mb", metrics["memory_usage_mb"]),
		zap.Any("goroutine_count", metrics["goroutine_count"]),
		zap.Any("total_requests", metrics["total_requests"]),
		zap.Any("success_rate", metrics["success_rate"]),
		zap.Any("cache_hit_rate", metrics["cache_hit_rate"]))
}

// checkAlerts 检查告警
func (ms *MonitoringService) checkAlerts() {
	metrics := ms.collector.GetMetrics()

	// 内存使用告警
	if memUsage, ok := metrics["memory_usage_mb"].(uint64); ok && memUsage > DefaultMemoryThreshold {
		ms.logger.Warn("内存使用过高", zap.Uint64("memory_mb", memUsage))
	}

	// Goroutine数量告警
	if goroutines, ok := metrics["goroutine_count"].(int); ok && goroutines > DefaultGoroutineThreshold {
		ms.logger.Warn("Goroutine数量过多", zap.Int("count", goroutines))
	}

	// 成功率告警
	if successRate, ok := metrics["success_rate"].(float64); ok && successRate < 0.95 {
		ms.logger.Warn("请求成功率过低", zap.Float64("success_rate", successRate))
	}

	// 缓存命中率告警
	if hitRate, ok := metrics["cache_hit_rate"].(float64); ok && hitRate < 0.8 {
		ms.logger.Warn("缓存命中率过低", zap.Float64("hit_rate", hitRate))
	}

	// 计费错误率告警
	billingUpdates := metrics["billing_updates"].(int64)
	billingErrors := metrics["billing_errors"].(int64)
	if billingUpdates > 0 {
		errorRate := float64(billingErrors) / float64(billingUpdates)
		if errorRate > 0.05 {
			ms.logger.Warn("计费错误率过高",
				zap.Float64("error_rate", errorRate),
				zap.Int64("errors", billingErrors),
				zap.Int64("total", billingUpdates))
		}
	}
}

// HealthCheck 健康检查
func (ms *MonitoringService) HealthCheck() map[string]interface{} {
	metrics := ms.collector.GetMetrics()

	health := map[string]interface{}{
		"status": "healthy",
		"checks": map[string]interface{}{},
	}

	checks := health["checks"].(map[string]interface{})

	// 内存检查
	if memUsage, ok := metrics["memory_usage_mb"].(uint64); ok {
		if memUsage > 2048 {
			checks["memory"] = map[string]interface{}{
				"status":  "unhealthy",
				"message": "内存使用过高",
				"value":   memUsage,
			}
			health["status"] = "unhealthy"
		} else {
			checks["memory"] = map[string]interface{}{
				"status": "healthy",
				"value":  memUsage,
			}
		}
	}

	// 成功率检查
	if successRate, ok := metrics["success_rate"].(float64); ok {
		if successRate < 0.9 {
			checks["success_rate"] = map[string]interface{}{
				"status":  "unhealthy",
				"message": "请求成功率过低",
				"value":   successRate,
			}
			health["status"] = "unhealthy"
		} else {
			checks["success_rate"] = map[string]interface{}{
				"status": "healthy",
				"value":  successRate,
			}
		}
	}

	return health
}

// ExportMetrics 导出指标（Prometheus格式）
func (ms *MonitoringService) ExportMetrics() string {
	metrics := ms.collector.GetMetrics()

	// 简化的Prometheus格式导出
	result := ""

	if totalRequests, ok := metrics["total_requests"].(int64); ok {
		result += fmt.Sprintf("kuaidi_total_requests %d\n", totalRequests)
	}

	if successRequests, ok := metrics["success_requests"].(int64); ok {
		result += fmt.Sprintf("kuaidi_success_requests %d\n", successRequests)
	}

	if failedRequests, ok := metrics["failed_requests"].(int64); ok {
		result += fmt.Sprintf("kuaidi_failed_requests %d\n", failedRequests)
	}

	if memUsage, ok := metrics["memory_usage_mb"].(uint64); ok {
		result += fmt.Sprintf("kuaidi_memory_usage_mb %d\n", memUsage)
	}

	if goroutines, ok := metrics["goroutine_count"].(int); ok {
		result += fmt.Sprintf("kuaidi_goroutines %d\n", goroutines)
	}

	if cacheHitRate, ok := metrics["cache_hit_rate"].(float64); ok {
		result += fmt.Sprintf("kuaidi_cache_hit_rate %.2f\n", cacheHitRate)
	}

	return result
}
