package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/user"
	"github.com/your-org/go-kuaidi/internal/util"
)

// AdminOrderService 管理员订单服务实现
type AdminOrderService struct {
	orderRepository         repository.OrderRepository
	userRepository          user.UserRepository
	expressRepository       express.ExpressCompanyRepository
	auditService            AuditServiceInterface
	configService           SystemConfigServiceInterface
	shippingFeeService      ProviderShippingFeeServiceInterface
	priceValidationService  PriceValidationServiceInterface
	statusMappingService    *StatusMappingService
	providerManager         *adapter.ProviderManager
	statusSyncRefundService *StatusSyncRefundService // 🔥 新增：状态同步退款服务
	logger                  *zap.Logger
}

// NewAdminOrderService 创建管理员订单服务
func NewAdminOrderService(
	orderRepository repository.OrderRepository,
	userRepository user.UserRepository,
	expressRepository express.ExpressCompanyRepository,
	auditService AuditServiceInterface,
	configService SystemConfigServiceInterface,
	shippingFeeService ProviderShippingFeeServiceInterface,
	priceValidationService PriceValidationServiceInterface,
	statusMappingService *StatusMappingService,
	providerManager *adapter.ProviderManager,
	balanceService BalanceServiceInterface, // 🔥 新增：余额服务
	db *sql.DB, // 🔥 新增：数据库连接
	logger *zap.Logger,
) AdminOrderServiceInterface {
	// 🔥 创建状态同步退款服务
	statusSyncRefundService := NewStatusSyncRefundService(
		db,
		orderRepository,
		balanceService,
		auditService,
		logger,
	)

	return &AdminOrderService{
		orderRepository:         orderRepository,
		userRepository:          userRepository,
		expressRepository:       expressRepository,
		auditService:            auditService,
		configService:           configService,
		shippingFeeService:      shippingFeeService,
		priceValidationService:  priceValidationService,
		statusMappingService:    statusMappingService,
		providerManager:         providerManager,
		statusSyncRefundService: statusSyncRefundService,
		logger:                  logger,
	}
}

// GetAdminOrderList 获取管理员订单列表
func (s *AdminOrderService) GetAdminOrderList(ctx context.Context, req *model.AdminOrderListRequest) (*model.AdminOrderListResponse, error) {
	// 验证请求参数
	if err := s.validateAdminOrderListRequest(req); err != nil {
		s.logger.Error("Admin order list request validation failed",
			zap.Error(err),
			zap.Any("request", req))
		return &model.AdminOrderListResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
			Data: &model.AdminOrderListData{
				Items:      []*model.AdminOrderListItem{},
				Total:      0,
				Page:       req.Page,
				PageSize:   req.PageSize,
				TotalPages: 0,
				HasNext:    false,
				HasPrev:    false,
				Statistics: nil,
			},
		}, nil
	}

	// 记录操作日志
	s.logger.Info("Admin order list query started",
		zap.String("user_id", req.UserID),
		zap.String("username", req.Username),
		zap.Int("page", req.Page),
		zap.Int("page_size", req.PageSize))

	// 查询订单列表
	s.logger.Info("Calling AdminListWithFilter",
		zap.Any("request", req))

	items, total, err := s.orderRepository.AdminListWithFilter(ctx, req)
	if err != nil {
		s.logger.Error("Failed to query admin order list",
			zap.Error(err),
			zap.Any("request", req))
		return &model.AdminOrderListResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("查询订单列表失败: %s", err.Error()),
			Data: &model.AdminOrderListData{
				Items:      []*model.AdminOrderListItem{},
				Total:      0,
				Page:       req.Page,
				PageSize:   req.PageSize,
				TotalPages: 0,
				HasNext:    false,
				HasPrev:    false,
				Statistics: nil,
			},
		}, nil
	}

	s.logger.Info("AdminListWithFilter completed",
		zap.Int64("total", total),
		zap.Int("items_count", len(items)))

	// 计算分页信息
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	hasNext := req.Page < totalPages
	hasPrev := req.Page > 1

	// 获取统计信息（如果需要）
	var statistics *model.AdminOrderStatistics
	// 暂时跳过统计信息获取，避免可能的循环调用问题
	// if !req.IsExport {
	// 	statisticsFilter := &AdminStatisticsFilter{
	// 		UserID:        stringToPointer(req.UserID),
	// 		Status:        stringToPointer(req.Status),
	// 		Provider:      stringToPointer(req.Provider),
	// 		BillingStatus: stringToPointer(req.BillingStatus),
	// 		StartTime:     stringToTimePointer(req.StartTime),
	// 		EndTime:       stringToTimePointer(req.EndTime),
	// 	}
	// 	statistics, _ = s.GetAdminOrderStatistics(ctx, statisticsFilter)
	// }

	return &model.AdminOrderListResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "查询成功",
		Data: &model.AdminOrderListData{
			Items:      items,
			Total:      total,
			Page:       req.Page,
			PageSize:   req.PageSize,
			TotalPages: totalPages,
			HasNext:    hasNext,
			HasPrev:    hasPrev,
			Statistics: statistics,
		},
	}, nil
}

// GetAdminOrderDetail 获取管理员订单详情
func (s *AdminOrderService) GetAdminOrderDetail(ctx context.Context, orderID int64, operatorID string) (*model.AdminOrderDetail, error) {
	// 查询订单基础信息
	orderRecord, err := s.orderRepository.FindByID(ctx, orderID)
	if err != nil {
		s.logger.Error("Failed to find order by ID",
			zap.Int64("order_id", orderID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeNotFound, "订单不存在")
	}

	// 查询用户信息
	user, err := s.userRepository.FindByID(orderRecord.UserID)
	if err != nil {
		s.logger.Warn("Failed to find user for order",
			zap.Int64("order_id", orderID),
			zap.String("user_id", orderRecord.UserID),
			zap.Error(err))
	}

	// 查询快递公司信息
	var expressCompany *express.ExpressCompany
	if orderRecord.ExpressType != "" {
		expressCompany, err = s.expressRepository.GetCompanyByCode(orderRecord.ExpressType)
		if err != nil {
			s.logger.Warn("Failed to find express company for order",
				zap.Int64("order_id", orderID),
				zap.String("express_type", orderRecord.ExpressType),
				zap.Error(err))
		}
	}

	// 构建详情响应
	detail := &model.AdminOrderDetail{
		OrderRecord: *orderRecord,
	}

	// 填充用户信息
	if user != nil {
		detail.User.ID = user.ID
		detail.User.Username = user.Username
		detail.User.Email = user.Email
		detail.User.IsActive = user.IsActive
		detail.User.CreatedAt = user.CreatedAt
		detail.User.DefaultRole = user.DefaultRoleID
	}

	// 填充快递公司信息
	if expressCompany != nil {
		detail.ExpressCompany.ID = expressCompany.ID
		detail.ExpressCompany.Code = expressCompany.Code
		detail.ExpressCompany.Name = expressCompany.Name
		if expressCompany.EnglishName != nil {
			detail.ExpressCompany.EnglishName = *expressCompany.EnglishName
		}
		if expressCompany.OfficialWebsite != nil {
			detail.ExpressCompany.OfficialWebsite = *expressCompany.OfficialWebsite
		}
	}

	// 记录访问日志
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, orderRecord.OrderNo, "view_detail", operatorID, map[string]interface{}{
			"order_id": orderID,
			"action":   "admin_view_detail",
		})
	}

	s.logger.Info("Admin order detail retrieved",
		zap.Int64("order_id", orderID),
		zap.String("operator_id", operatorID),
		zap.String("order_no", orderRecord.OrderNo))

	return detail, nil
}

// UpdateOrderStatus 更新订单状态
func (s *AdminOrderService) UpdateOrderStatus(ctx context.Context, orderID int64, newStatus, operatorID, reason string) error {
	// 查询当前订单
	orderRecord, err := s.orderRepository.FindByID(ctx, orderID)
	if err != nil {
		return errors.NewBusinessError(errors.ErrCodeNotFound, "订单不存在")
	}

	oldStatus := orderRecord.Status

	// 验证状态转换是否合法
	if !s.configService.IsStatusTransitionValid(oldStatus, newStatus, "admin") {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest,
			fmt.Sprintf("不允许从状态 %s 转换到 %s", oldStatus, newStatus))
	}

	// 更新订单状态（使用企业级状态更新方法，包含历史记录）
	updateReq := &repository.OrderStatusUpdateRequest{
		OrderNo:    orderRecord.OrderNo,
		NewStatus:  newStatus,
		UpdateTime: util.NowBeijing(),
		Provider:   orderRecord.Provider,
		Extra: map[string]interface{}{
			"change_source": "manual",
			"operator_id":   operatorID,
			"operator_name": operatorID, // 可以后续从用户服务获取真实姓名
			"change_reason": reason,
		},
	}

	if err := s.orderRepository.UpdateOrderStatus(ctx, updateReq); err != nil {
		return errors.WrapError(errors.ErrCodeInternal, "更新订单状态失败", err)
	}

	// 记录审计日志
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, orderRecord.OrderNo, "status_update", operatorID, map[string]interface{}{
			"order_id":   orderID,
			"old_status": oldStatus,
			"new_status": newStatus,
			"reason":     reason,
			"action":     "admin_status_update",
		})
	}

	s.logger.Info("Order status updated by admin",
		zap.Int64("order_id", orderID),
		zap.String("operator_id", operatorID),
		zap.String("old_status", oldStatus),
		zap.String("new_status", newStatus),
		zap.String("reason", reason))

	return nil
}

// BatchUpdateOrderStatus 批量更新订单状态
func (s *AdminOrderService) BatchUpdateOrderStatus(ctx context.Context, orderIDs []int64, newStatus, operatorID, reason string) (*BatchOperationResult, error) {
	// 验证批量操作限制
	maxBatchItems := s.configService.GetBatchOperationMaxItems()
	if len(orderIDs) > maxBatchItems {
		return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest,
			fmt.Sprintf("批量操作不能超过 %d 个订单", maxBatchItems))
	}

	start := util.NowBeijing()
	result := &BatchOperationResult{
		TotalItems:   len(orderIDs),
		SuccessItems: 0,
		FailedItems:  0,
		Errors:       make([]string, 0),
		Duration:     0,
	}

	for _, orderID := range orderIDs {
		err := s.UpdateOrderStatus(ctx, orderID, newStatus, operatorID, reason)
		if err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("订单ID %d: %s", orderID, err.Error()))
			result.FailedItems++
		} else {
			result.SuccessItems++
		}
	}

	result.Duration = time.Since(start)

	s.logger.Info("Batch order status update completed",
		zap.String("operator_id", operatorID),
		zap.String("new_status", newStatus),
		zap.Int("total_items", result.TotalItems),
		zap.Int("success_items", result.SuccessItems),
		zap.Int("failed_items", result.FailedItems),
		zap.Duration("duration", result.Duration))

	return result, nil
}

// GetAdminOrderStatistics 获取管理员订单统计数据
func (s *AdminOrderService) GetAdminOrderStatistics(ctx context.Context, filter *AdminStatisticsFilter) (*model.AdminOrderStatistics, error) {
	// 转换过滤器类型
	repoFilter := &repository.AdminStatisticsFilter{
		UserID:        pointerToString(filter.UserID),
		Status:        pointerToString(filter.Status),
		Provider:      pointerToString(filter.Provider),
		BillingStatus: pointerToString(filter.BillingStatus),
		StartTime:     timePointerToString(filter.StartTime),
		EndTime:       timePointerToString(filter.EndTime),
	}

	// 调用仓储层获取统计数据
	stats, err := s.orderRepository.GetAdminStatistics(ctx, repoFilter)
	if err != nil {
		s.logger.Error("Failed to get admin order statistics",
			zap.Error(err),
			zap.Any("filter", filter))
		return nil, errors.WrapError(errors.ErrCodeInternal, "获取统计数据失败", err)
	}

	s.logger.Debug("Admin order statistics retrieved",
		zap.Any("filter", filter),
		zap.Int64("total_orders", stats.TotalOrders),
		zap.Float64("total_amount", stats.TotalAmount))

	return stats, nil
}

// ExportOrders 导出订单数据
func (s *AdminOrderService) ExportOrders(ctx context.Context, req *model.AdminOrderListRequest, operatorID string) ([]byte, error) {
	// 设置导出标识
	req.IsExport = true

	// 获取订单列表
	response, err := s.GetAdminOrderList(ctx, req)
	if err != nil {
		return nil, err
	}

	if !response.Success {
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, response.Message)
	}

	// 这里应该实现具体的导出逻辑（CSV、Excel等）
	// 为了简化，这里返回JSON格式
	data, err := json.Marshal(response.Data.Items)
	if err != nil {
		s.logger.Error("Failed to marshal export data",
			zap.Error(err),
			zap.String("operator_id", operatorID))
		return nil, errors.WrapError(errors.ErrCodeInternal, "导出数据序列化失败", err)
	}

	// 记录导出操作
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, "", "export_orders", operatorID, map[string]interface{}{
			"export_count": len(response.Data.Items),
			"filter":       req,
			"action":       "admin_export_orders",
		})
	}

	s.logger.Info("Orders exported by admin",
		zap.String("operator_id", operatorID),
		zap.Int("export_count", len(response.Data.Items)))

	return data, nil
}

// 辅助函数：指针转字符串
func pointerToString(p *string) string {
	if p == nil {
		return ""
	}
	return *p
}

// 辅助函数：时间指针转字符串
func timePointerToString(p *time.Time) string {
	if p == nil {
		return ""
	}
	return p.Format(time.RFC3339)
}

// validateAdminOrderListRequest 使用配置服务验证管理员订单列表请求
func (s *AdminOrderService) validateAdminOrderListRequest(req *model.AdminOrderListRequest) error {
	if req == nil {
		return fmt.Errorf("请求不能为空")
	}

	// 基础验证
	if err := model.ValidateAdminOrderListRequest(req); err != nil {
		return err
	}

	// 使用配置服务验证分页参数
	maxPageSize := s.configService.GetConfigAsIntWithDefault("max_page_size", 100)
	defaultPageSize := s.configService.GetConfigAsIntWithDefault("default_page_size", 20)

	if req.PageSize <= 0 {
		req.PageSize = defaultPageSize
	}
	if req.PageSize > maxPageSize {
		req.PageSize = maxPageSize
	}

	// 验证搜索字段
	if len(req.SearchFields) > 0 {
		validFields, err := s.configService.GetValidSearchFields("admin")
		if err != nil {
			s.logger.Warn("Failed to get valid search fields from config", zap.Error(err))
			// 使用默认验证逻辑
		} else {
			validFieldsMap := make(map[string]bool)
			for _, field := range validFields {
				validFieldsMap[field] = true
			}

			for _, field := range req.SearchFields {
				if !validFieldsMap[field] {
					return fmt.Errorf("无效的搜索字段: %s", field)
				}
			}
		}
	}

	// 验证计费状态
	if req.BillingStatus != "" {
		if !s.configService.IsBillingStatusValid(req.BillingStatus) {
			return fmt.Errorf("无效的计费状态: %s", req.BillingStatus)
		}
	}

	// 验证排序字段
	if req.SortBy != "" {
		if !s.configService.IsSortFieldValid(req.SortBy, "admin") {
			return fmt.Errorf("无效的排序字段: %s", req.SortBy)
		}
	}

	// 设置默认排序
	if req.SortBy == "" {
		req.SortBy = s.configService.GetDefaultSortField()
	}
	if req.SortOrder == "" {
		req.SortOrder = s.configService.GetDefaultSortOrder()
	}

	// 验证批量操作限制
	if req.IsExport {
		maxExportItems := s.configService.GetExportMaxItems()
		if req.PageSize > maxExportItems {
			req.PageSize = maxExportItems
		}
	}

	return nil
}

// QueryProviderShippingFee 查询供应商运费信息
func (s *AdminOrderService) QueryProviderShippingFee(ctx context.Context, orderID int64, operatorID string) (*model.ProviderShippingFeeResponse, error) {
	s.logger.Info("开始查询供应商运费信息",
		zap.Int64("order_id", orderID),
		zap.String("operator_id", operatorID))

	// 查询订单信息
	orderRecord, err := s.orderRepository.FindByID(ctx, orderID)
	if err != nil {
		s.logger.Error("Failed to find order by ID for shipping fee query",
			zap.Int64("order_id", orderID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		return &model.ProviderShippingFeeResponse{
			Success: false,
			Code:    model.StatusNotFound,
			Message: "订单不存在",
		}, nil
	}

	// 使用新的运费查询服务
	detailedInfo, err := s.shippingFeeService.QueryShippingFee(ctx, orderRecord)
	if err != nil {
		s.logger.Error("Failed to query shipping fee from service",
			zap.Int64("order_id", orderID),
			zap.String("provider", orderRecord.Provider),
			zap.Error(err))
		return &model.ProviderShippingFeeResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("查询运费失败: %s", err.Error()),
		}, nil
	}

	// 转换为简化的响应格式（保持向后兼容）
	shippingFeeInfo := &model.ProviderShippingFeeInfo{
		OrderID:     detailedInfo.OrderID,
		Provider:    detailedInfo.Provider,
		ShippingFee: detailedInfo.TotalFee,
		Currency:    detailedInfo.Currency,
		QueryTime:   detailedInfo.QueryTime,
		Supported:   detailedInfo.Supported,
		Error:       detailedInfo.Error,
	}

	// 记录审计日志
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, orderRecord.OrderNo, "query_shipping_fee", operatorID, map[string]interface{}{
			"order_id":     orderID,
			"provider":     orderRecord.Provider,
			"shipping_fee": detailedInfo.TotalFee,
			"supported":    detailedInfo.Supported,
			"action":       "admin_query_shipping_fee",
		})
	}

	s.logger.Info("供应商运费查询完成",
		zap.Int64("order_id", orderID),
		zap.String("provider", orderRecord.Provider),
		zap.Float64("total_fee", detailedInfo.TotalFee),
		zap.Bool("supported", detailedInfo.Supported))

	return &model.ProviderShippingFeeResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "查询成功",
		Data:    shippingFeeInfo,
	}, nil
}

// BatchQueryProviderShippingFee 批量查询供应商运费信息
func (s *AdminOrderService) BatchQueryProviderShippingFee(ctx context.Context, orderIDs []int64, operatorID string) (*model.BatchProviderShippingFeeResponse, error) {
	// 验证批量操作限制
	maxBatchItems := s.configService.GetBatchOperationMaxItems()
	if len(orderIDs) > maxBatchItems {
		return &model.BatchProviderShippingFeeResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: fmt.Sprintf("批量操作不能超过 %d 个订单", maxBatchItems),
		}, nil
	}

	results := make([]*model.ProviderShippingFeeInfo, 0, len(orderIDs))

	// 查询所有订单记录
	orderRecords := make([]*model.OrderRecord, 0, len(orderIDs))
	for _, orderID := range orderIDs {
		orderRecord, err := s.orderRepository.FindByID(ctx, orderID)
		if err != nil {
			s.logger.Warn("Failed to find order for batch shipping fee query",
				zap.Int64("order_id", orderID),
				zap.Error(err))
			// 创建一个错误记录
			orderRecords = append(orderRecords, &model.OrderRecord{
				ID:       orderID,
				Provider: "unknown",
			})
		} else {
			orderRecords = append(orderRecords, orderRecord)
		}
	}

	// 使用新的批量运费查询服务
	detailedInfos, err := s.shippingFeeService.BatchQueryShippingFee(ctx, orderRecords)
	if err != nil {
		s.logger.Error("Failed to batch query shipping fees from service",
			zap.Int("order_count", len(orderIDs)),
			zap.Error(err))
		return &model.BatchProviderShippingFeeResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("批量查询运费失败: %s", err.Error()),
		}, nil
	}

	// 转换为简化的响应格式（保持向后兼容）
	for _, detailedInfo := range detailedInfos {
		if detailedInfo != nil {
			results = append(results, &model.ProviderShippingFeeInfo{
				OrderID:     detailedInfo.OrderID,
				Provider:    detailedInfo.Provider,
				ShippingFee: detailedInfo.TotalFee,
				Currency:    detailedInfo.Currency,
				QueryTime:   detailedInfo.QueryTime,
				Supported:   detailedInfo.Supported,
				Error:       detailedInfo.Error,
			})
		}
	}

	// 记录批量查询审计日志
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, "", "batch_query_shipping_fee", operatorID, map[string]interface{}{
			"order_count":   len(orderIDs),
			"success_count": len(results),
			"action":        "admin_batch_query_shipping_fee",
		})
	}

	s.logger.Info("批量查询供应商运费完成",
		zap.Int("total_count", len(orderIDs)),
		zap.Int("success_count", len(results)))

	return &model.BatchProviderShippingFeeResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "批量查询完成",
		Data:    results,
	}, nil
}

// QueryDetailedProviderShippingFee 查询详细的供应商运费信息
func (s *AdminOrderService) QueryDetailedProviderShippingFee(ctx context.Context, orderID int64, operatorID string) (*model.DetailedShippingFeeInfo, error) {
	s.logger.Info("开始查询详细供应商运费信息",
		zap.Int64("order_id", orderID),
		zap.String("operator_id", operatorID))

	// 查询订单信息
	orderRecord, err := s.orderRepository.FindByID(ctx, orderID)
	if err != nil {
		s.logger.Error("Failed to find order by ID for detailed shipping fee query",
			zap.Int64("order_id", orderID),
			zap.String("operator_id", operatorID),
			zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeNotFound, "订单不存在")
	}

	// 使用运费查询服务获取详细信息
	detailedInfo, err := s.shippingFeeService.QueryShippingFee(ctx, orderRecord)
	if err != nil {
		s.logger.Error("Failed to query detailed shipping fee from service",
			zap.Int64("order_id", orderID),
			zap.String("provider", orderRecord.Provider),
			zap.Error(err))
		return nil, errors.WrapError(errors.ErrCodeInternal, "查询详细运费失败", err)
	}

	// 记录审计日志
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, orderRecord.OrderNo, "query_detailed_shipping_fee", operatorID, map[string]interface{}{
			"order_id":    orderID,
			"provider":    orderRecord.Provider,
			"total_fee":   detailedInfo.TotalFee,
			"supported":   detailedInfo.Supported,
			"fee_details": len(detailedInfo.FeeDetails),
			"action":      "admin_query_detailed_shipping_fee",
		})
	}

	s.logger.Info("详细供应商运费查询完成",
		zap.Int64("order_id", orderID),
		zap.String("provider", orderRecord.Provider),
		zap.Float64("total_fee", detailedInfo.TotalFee),
		zap.Bool("supported", detailedInfo.Supported),
		zap.Int("fee_details_count", len(detailedInfo.FeeDetails)))

	return detailedInfo, nil
}

// BatchValidatePrices 批量验证订单价格
func (s *AdminOrderService) BatchValidatePrices(ctx context.Context, req *model.BatchPriceValidationRequest, operatorID string) (*model.BatchPriceValidationResponse, error) {
	s.logger.Info("开始批量价格验证",
		zap.String("operator_id", operatorID),
		zap.String("provider", req.Provider),
		zap.String("start_time", req.StartTime),
		zap.String("end_time", req.EndTime),
		zap.Int("max_count", req.MaxCount))

	// 验证请求参数
	if err := s.validateBatchPriceValidationRequest(req); err != nil {
		return &model.BatchPriceValidationResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
		}, nil
	}

	// 构建订单查询条件
	orderListReq := &model.AdminOrderListRequest{
		OrderListRequest: model.OrderListRequest{
			Page:      1,
			PageSize:  req.MaxCount,
			SortBy:    "created_at",
			SortOrder: "desc",
			Provider:  req.Provider,
			StartTime: req.StartTime,
			EndTime:   req.EndTime,
		},
	}

	// 如果指定了订单ID，直接查询这些订单
	var orders []*model.AdminOrderListItem
	var total int64
	var err error

	if len(req.OrderIDs) > 0 {
		// 根据订单ID查询
		orders, err = s.getOrdersByIDs(ctx, req.OrderIDs)
		if err != nil {
			s.logger.Error("根据订单ID查询失败", zap.Error(err))
			return &model.BatchPriceValidationResponse{
				Success: false,
				Code:    model.StatusInternalServerError,
				Message: "查询订单失败",
			}, nil
		}
		total = int64(len(orders))
	} else {
		// 根据条件查询订单
		orders, total, err = s.orderRepository.AdminListWithFilter(ctx, orderListReq)
		if err != nil {
			s.logger.Error("查询订单列表失败", zap.Error(err))
			return &model.BatchPriceValidationResponse{
				Success: false,
				Code:    model.StatusInternalServerError,
				Message: "查询订单列表失败",
			}, nil
		}
	}

	if len(orders) == 0 {
		return &model.BatchPriceValidationResponse{
			Success: true,
			Code:    model.StatusSuccess,
			Message: "没有找到符合条件的订单",
			Data: &model.BatchPriceValidationData{
				TotalOrders:     0,
				ProcessedOrders: 0,
				SuccessOrders:   0,
				FailedOrders:    0,
				SkippedOrders:   0,
				Results:         []*model.PriceValidationResult{},
				Summary:         &model.PriceValidationSummary{},
			},
		}, nil
	}

	// 执行价格验证
	results, summary := s.performBatchPriceValidation(ctx, orders)

	// 🔥 企业级修复：将验证结果持久化到数据库
	if len(results) > 0 {
		if err := s.orderRepository.BatchUpdatePriceValidation(ctx, results); err != nil {
			s.logger.Error("批量更新价格验证结果失败", zap.Error(err))
			// 不返回错误，因为验证已经完成，只是持久化失败
		} else {
			s.logger.Info("批量价格验证结果已持久化到数据库",
				zap.Int("updated_count", len(results)))
		}
	}

	// 记录审计日志
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, "", "batch_price_validation", operatorID, map[string]interface{}{
			"total_orders":     len(orders),
			"processed_orders": len(results),
			"success_orders":   summary.ProfitCount + summary.LossCount + summary.BreakEvenCount,
			"failed_orders":    len(results) - (summary.ProfitCount + summary.LossCount + summary.BreakEvenCount),
			"provider":         req.Provider,
			"start_time":       req.StartTime,
			"end_time":         req.EndTime,
		})
	}

	s.logger.Info("批量价格验证完成",
		zap.String("operator_id", operatorID),
		zap.Int("total_orders", len(orders)),
		zap.Int("processed_orders", len(results)))

	return &model.BatchPriceValidationResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "批量价格验证完成",
		Data: &model.BatchPriceValidationData{
			TotalOrders:     int(total),
			ProcessedOrders: len(results),
			SuccessOrders:   summary.ProfitCount + summary.LossCount + summary.BreakEvenCount,
			FailedOrders:    len(results) - (summary.ProfitCount + summary.LossCount + summary.BreakEvenCount),
			SkippedOrders:   summary.UnsupportedCount,
			Results:         results,
			Summary:         summary,
		},
	}, nil
}

// validateBatchPriceValidationRequest 验证批量价格验证请求
func (s *AdminOrderService) validateBatchPriceValidationRequest(req *model.BatchPriceValidationRequest) error {
	if req == nil {
		return fmt.Errorf("请求不能为空")
	}

	// 设置默认最大数量
	if req.MaxCount <= 0 {
		req.MaxCount = 100
	}

	// 限制最大数量
	maxAllowed := s.configService.GetBatchOperationMaxItems()
	if req.MaxCount > maxAllowed {
		req.MaxCount = maxAllowed
	}

	// 验证时间范围
	if req.StartTime != "" && req.EndTime != "" {
		startTime, err := time.Parse(time.RFC3339, req.StartTime)
		if err != nil {
			return fmt.Errorf("开始时间格式无效: %s", req.StartTime)
		}

		endTime, err := time.Parse(time.RFC3339, req.EndTime)
		if err != nil {
			return fmt.Errorf("结束时间格式无效: %s", req.EndTime)
		}

		if startTime.After(endTime) {
			return fmt.Errorf("开始时间不能晚于结束时间")
		}

		// 限制时间范围不超过30天
		if endTime.Sub(startTime) > 30*24*time.Hour {
			return fmt.Errorf("时间范围不能超过30天")
		}
	}

	return nil
}

// getOrdersByIDs 根据订单ID列表查询订单
func (s *AdminOrderService) getOrdersByIDs(ctx context.Context, orderIDs []int64) ([]*model.AdminOrderListItem, error) {
	var orders []*model.AdminOrderListItem

	for _, orderID := range orderIDs {
		orderRecord, err := s.orderRepository.FindByID(ctx, orderID)
		if err != nil {
			s.logger.Warn("查询订单失败", zap.Int64("order_id", orderID), zap.Error(err))
			continue
		}

		// 转换为AdminOrderListItem
		item := &model.AdminOrderListItem{}
		item.ID = orderRecord.ID
		item.OrderNo = orderRecord.OrderNo
		item.CustomerOrderNo = orderRecord.CustomerOrderNo
		item.TrackingNo = orderRecord.TrackingNo
		item.ExpressType = orderRecord.ExpressType
		item.ProductType = orderRecord.ProductType
		item.Provider = orderRecord.Provider
		item.Status = orderRecord.Status
		item.Weight = orderRecord.Weight
		item.Price = orderRecord.Price
		item.ActualFee = orderRecord.ActualFee
		item.InsuranceFee = orderRecord.InsuranceFee
		item.BillingStatus = orderRecord.BillingStatus
		item.SenderInfo = orderRecord.SenderInfo
		item.ReceiverInfo = orderRecord.ReceiverInfo
		item.CreatedAt = orderRecord.CreatedAt
		item.UpdatedAt = orderRecord.UpdatedAt
		// 🔥 企业级修复：添加缺失的TaskId和PollToken字段映射
		item.TaskId = orderRecord.TaskId
		item.PollToken = orderRecord.PollToken

		// 初始化价格验证信息
		item.PriceValidation.SystemPrice = orderRecord.Price
		item.PriceValidation.QueryStatus = "pending"
		item.PriceValidation.ProfitStatus = "unknown"
		item.PriceValidation.Supported = s.shippingFeeService.IsProviderSupported(orderRecord.Provider)

		orders = append(orders, item)
	}

	return orders, nil
}

// performBatchPriceValidation 执行批量价格验证
func (s *AdminOrderService) performBatchPriceValidation(ctx context.Context, orders []*model.AdminOrderListItem) ([]*model.PriceValidationResult, *model.PriceValidationSummary) {
	results := make([]*model.PriceValidationResult, 0, len(orders))
	summary := &model.PriceValidationSummary{}

	for _, order := range orders {
		result := s.validateSingleOrderPrice(ctx, order)
		results = append(results, result)

		// 更新汇总信息
		switch result.ProfitStatus {
		case "profit":
			summary.ProfitCount++
			if result.ProviderPrice > 0 {
				summary.TotalProfit += result.SystemPrice - result.ProviderPrice
			}
		case "loss":
			summary.LossCount++
			if result.ProviderPrice > 0 {
				summary.TotalLoss += result.ProviderPrice - result.SystemPrice
			}
		case "break_even":
			summary.BreakEvenCount++
		default:
			if !result.Supported {
				summary.UnsupportedCount++
			}
		}
	}

	return results, summary
}

// validateSingleOrderPrice 验证单个订单价格
func (s *AdminOrderService) validateSingleOrderPrice(ctx context.Context, order *model.AdminOrderListItem) *model.PriceValidationResult {
	result := &model.PriceValidationResult{
		OrderID:     order.ID,
		OrderNo:     order.OrderNo,
		Provider:    order.Provider,
		SystemPrice: order.Price,
		QueryTime:   util.NowBeijing().Format(time.RFC3339),
	}

	// 检查供应商是否支持
	if !s.shippingFeeService.IsProviderSupported(order.Provider) {
		result.QueryStatus = "failed"
		result.ErrorMessage = fmt.Sprintf("供应商 %s 不支持价格查询", order.Provider)
		result.Supported = false
		result.ProfitStatus = "unknown"
		return result
	}

	result.Supported = true

	// 构建订单记录用于查询
	orderRecord := &model.OrderRecord{
		ID:              order.ID,
		OrderNo:         order.OrderNo,
		CustomerOrderNo: order.CustomerOrderNo,
		TrackingNo:      order.TrackingNo,
		ExpressType:     order.ExpressType,
		ProductType:     order.ProductType,
		Provider:        order.Provider,
		Status:          order.Status,
		Weight:          order.Weight,
		Price:           order.Price,
		SenderInfo:      order.SenderInfo,
		ReceiverInfo:    order.ReceiverInfo,
		// 🔥 企业级修复：添加缺失的TaskId和PollToken字段
		TaskId:    order.TaskId,
		PollToken: order.PollToken,
	}

	// 查询供应商价格
	detailedInfo, err := s.shippingFeeService.QueryShippingFee(ctx, orderRecord)
	if err != nil {
		result.QueryStatus = "failed"
		result.ErrorMessage = err.Error()
		result.ProfitStatus = "unknown"
		return result
	}

	result.ProviderPrice = detailedInfo.TotalFee
	result.QueryStatus = "success"

	// 计算盈亏状态
	result.ProfitStatus = s.calculateProfitStatus(result.SystemPrice, result.ProviderPrice)

	return result
}

// calculateProfitStatus 计算盈亏状态
func (s *AdminOrderService) calculateProfitStatus(systemPrice, providerPrice float64) string {
	if providerPrice <= 0 {
		return "unknown"
	}

	if systemPrice <= 0 {
		return "unknown"
	}

	difference := systemPrice - providerPrice
	tolerance := 0.01 // 1分钱容差

	if difference > tolerance {
		return "profit"
	} else if difference < -tolerance {
		return "loss"
	} else {
		return "break_even"
	}
}

// SyncOrderStatus 同步单个订单状态
func (s *AdminOrderService) SyncOrderStatus(ctx context.Context, orderID int64) (*model.StatusSyncResult, error) {
	startTime := util.NowBeijing()

	// 查询订单记录
	orderRecord, err := s.orderRepository.FindByID(ctx, orderID)
	if err != nil {
		return &model.StatusSyncResult{
			OrderID:      orderID,
			Success:      false,
			ErrorMessage: fmt.Sprintf("查询订单失败: %v", err),
			SyncTime:     startTime,
		}, fmt.Errorf("查询订单失败: %w", err)
	}

	result := &model.StatusSyncResult{
		OrderID:   orderRecord.ID,
		OrderNo:   orderRecord.OrderNo,
		Provider:  orderRecord.Provider,
		OldStatus: model.SystemOrderStatus(orderRecord.Status),
		SyncTime:  startTime,
		Success:   false,
	}

	// 🔥 修复：订单管理操作不应该检查供应商启用状态，只检查是否支持状态同步功能
	// 供应商禁用只影响新的查价和下单，不影响现有订单的管理操作
	if !s.statusMappingService.IsProviderSupported(orderRecord.Provider) {
		result.ErrorMessage = fmt.Sprintf("供应商 %s 不支持状态同步功能", orderRecord.Provider)
		s.logger.Warn("供应商不支持状态同步功能",
			zap.Int64("order_id", orderID),
			zap.String("provider", orderRecord.Provider))
		return result, fmt.Errorf("供应商 %s 不支持状态同步功能", orderRecord.Provider)
	}

	// 获取供应商适配器
	providerAdapter, exists := s.providerManager.Get(orderRecord.Provider)
	if !exists {
		result.ErrorMessage = fmt.Sprintf("未找到供应商适配器: %s", orderRecord.Provider)
		return result, fmt.Errorf("未找到供应商适配器: %s", orderRecord.Provider)
	}

	// 查询最新的订单信息
	orderInfo, err := providerAdapter.QueryOrder(ctx, orderRecord.TaskId, orderRecord.TrackingNo)
	if err != nil {
		// 检查是否是状态异常错误
		if s.shippingFeeService.IsProviderOrderStatusError(err.Error()) {
			s.logger.Info("检测到订单状态异常，映射为已取消状态",
				zap.Int64("order_id", orderID),
				zap.String("order_no", orderRecord.OrderNo),
				zap.String("error", err.Error()))

			// 将错误状态映射为已取消
			result.NewStatus = model.StatusCancelled
			result.ProviderStatus = "error"
			result.ProviderStatusDesc = err.Error()

			// 验证状态转换
			valid, validationErrors := s.statusMappingService.ValidateStatusTransition(
				ctx, orderRecord.Provider, result.OldStatus, result.NewStatus)

			result.ValidationPassed = valid
			result.ValidationErrors = validationErrors

			if valid {
				// 更新订单状态
				if updateErr := s.orderRepository.UpdateStatus(ctx, orderID, string(result.NewStatus)); updateErr != nil {
					result.ErrorMessage = fmt.Sprintf("更新订单状态失败: %v", updateErr)
					s.logger.Error("更新订单状态失败", zap.Error(updateErr))
					return result, fmt.Errorf("更新订单状态失败: %w", updateErr)
				}

				result.Success = true
				result.StatusChanged = true

				// 🔥 状态更新成功后，检查是否需要退款
				refundResult, refundErr := s.statusSyncRefundService.ProcessStatusSyncRefund(
					ctx, orderID, result.OldStatus, result.NewStatus, "system_sync")
				if refundErr != nil {
					s.logger.Error("状态异常退款处理失败",
						zap.Int64("order_id", orderID),
						zap.String("order_no", orderRecord.OrderNo),
						zap.Error(refundErr))
				} else if refundResult.RefundTriggered {
					result.RefundTriggered = true
					result.RefundAmount = refundResult.RefundAmount.String()
					s.logger.Info("状态异常触发自动退款",
						zap.Int64("order_id", orderID),
						zap.String("order_no", orderRecord.OrderNo),
						zap.String("refund_amount", result.RefundAmount))
				}

				// 记录审计日志
				s.logStatusSyncAudit(ctx, result, "系统自动同步：订单状态异常")
			} else {
				result.ErrorMessage = fmt.Sprintf("状态转换验证失败: %v", validationErrors)
				s.logger.Warn("状态转换验证失败",
					zap.Int64("order_id", orderID),
					zap.Strings("validation_errors", validationErrors))
			}

			return result, nil
		}

		result.ErrorMessage = fmt.Sprintf("查询订单信息失败: %v", err)
		return result, fmt.Errorf("查询订单信息失败: %w", err)
	}

	// 映射供应商状态到系统状态
	newSystemStatus, err := s.statusMappingService.MapProviderStatusToSystem(
		ctx, orderRecord.Provider, orderInfo.Status)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("状态映射失败: %v", err)
		s.logger.Error("状态映射失败",
			zap.Int64("order_id", orderID),
			zap.String("provider", orderRecord.Provider),
			zap.String("provider_status", orderInfo.Status),
			zap.Error(err))
		return result, fmt.Errorf("状态映射失败: %w", err)
	}

	result.NewStatus = newSystemStatus
	result.ProviderStatus = orderInfo.Status
	result.ProviderStatusDesc = orderInfo.StatusDesc

	// 检查状态是否发生变化
	if result.OldStatus == result.NewStatus {
		result.Success = true
		result.StatusChanged = false
		result.ValidationPassed = true
		s.logger.Debug("订单状态无变化",
			zap.Int64("order_id", orderID),
			zap.String("status", string(result.NewStatus)))
		return result, nil
	}

	// 验证状态转换
	valid, validationErrors := s.statusMappingService.ValidateStatusTransition(
		ctx, orderRecord.Provider, result.OldStatus, result.NewStatus)

	result.ValidationPassed = valid
	result.ValidationErrors = validationErrors

	if !valid {
		result.ErrorMessage = fmt.Sprintf("状态转换验证失败: %v", validationErrors)
		s.logger.Warn("状态转换验证失败",
			zap.Int64("order_id", orderID),
			zap.String("old_status", string(result.OldStatus)),
			zap.String("new_status", string(result.NewStatus)),
			zap.Strings("validation_errors", validationErrors))
		return result, nil
	}

	// 解决状态冲突（如果需要）
	resolvedStatus := s.statusMappingService.ResolveStatusConflict(
		ctx, result.OldStatus, result.NewStatus, orderRecord.Provider)

	if resolvedStatus != result.NewStatus {
		s.logger.Info("状态冲突已解决",
			zap.Int64("order_id", orderID),
			zap.String("original_new_status", string(result.NewStatus)),
			zap.String("resolved_status", string(resolvedStatus)))
		result.NewStatus = resolvedStatus
	}

	// 更新订单状态（使用企业级状态更新方法，包含历史记录）
	updateReq := &repository.OrderStatusUpdateRequest{
		OrderNo:    orderRecord.OrderNo,
		NewStatus:  string(result.NewStatus),
		UpdateTime: util.NowBeijing(),
		Provider:   orderRecord.Provider,
		Extra: map[string]interface{}{
			"change_source":   "system",
			"change_reason":   "状态同步",
			"sync_source":     "admin_sync",
			"provider_status": result.ProviderStatus,
		},
	}

	if err := s.orderRepository.UpdateOrderStatus(ctx, updateReq); err != nil {
		result.ErrorMessage = fmt.Sprintf("更新订单状态失败: %v", err)
		return result, fmt.Errorf("更新订单状态失败: %w", err)
	}

	result.Success = true
	result.StatusChanged = true

	// 🔥 状态更新成功后，检查是否需要退款（不管状态是否变化）
	refundResult, err := s.statusSyncRefundService.ProcessStatusSyncRefund(
		ctx, orderID, result.OldStatus, result.NewStatus, "system_sync")
	if err != nil {
		s.logger.Error("状态同步退款处理失败",
			zap.Int64("order_id", orderID),
			zap.String("order_no", orderRecord.OrderNo),
			zap.Error(err))
		// 不返回错误，因为状态更新已经成功
	} else if refundResult.RefundTriggered {
		result.RefundTriggered = true
		result.RefundAmount = refundResult.RefundAmount.String()
		s.logger.Info("状态同步触发自动退款",
			zap.Int64("order_id", orderID),
			zap.String("order_no", orderRecord.OrderNo),
			zap.String("refund_amount", result.RefundAmount))
	}

	// 记录审计日志
	s.logStatusSyncAudit(ctx, result, "供应商状态同步")

	s.logger.Info("订单状态同步成功",
		zap.Int64("order_id", orderID),
		zap.String("order_no", orderRecord.OrderNo),
		zap.String("old_status", string(result.OldStatus)),
		zap.String("new_status", string(result.NewStatus)),
		zap.String("provider_status", orderInfo.Status),
		zap.Bool("refund_triggered", result.RefundTriggered),
		zap.String("refund_amount", result.RefundAmount),
		zap.Duration("duration", time.Since(startTime)))

	return result, nil
}

// BatchSyncOrderStatus 批量同步订单状态
func (s *AdminOrderService) BatchSyncOrderStatus(ctx context.Context, req *model.BatchStatusSyncRequest, operatorID string) (*model.BatchStatusSyncResponse, error) {
	startTime := util.NowBeijing()

	s.logger.Info("开始批量订单状态同步",
		zap.String("operator_id", operatorID),
		zap.Any("request", req))

	// 验证请求参数
	if err := s.validateBatchStatusSyncRequest(req); err != nil {
		return &model.BatchStatusSyncResponse{
			Success: false,
			Code:    400,
			Message: fmt.Sprintf("请求参数无效: %v", err),
		}, err
	}

	// 查询需要同步的订单
	orders, err := s.getOrdersForStatusSync(ctx, req)
	if err != nil {
		return &model.BatchStatusSyncResponse{
			Success: false,
			Code:    500,
			Message: fmt.Sprintf("查询订单失败: %v", err),
		}, err
	}

	if len(orders) == 0 {
		return &model.BatchStatusSyncResponse{
			Success: true,
			Code:    200,
			Message: "没有找到需要同步的订单",
			Data: &model.BatchStatusSyncData{
				TotalOrders:     0,
				ProcessedOrders: 0,
				SuccessOrders:   0,
				FailedOrders:    0,
				ChangedOrders:   0,
				SkippedOrders:   0,
				Results:         []model.StatusSyncResult{},
				Duration:        time.Since(startTime).String(),
			},
		}, nil
	}

	// 执行批量同步
	results := s.performBatchStatusSync(ctx, orders, req.DryRun)

	// 统计结果
	summary := s.calculateStatusSyncSummary(results)

	data := &model.BatchStatusSyncData{
		TotalOrders:     len(orders),
		ProcessedOrders: len(results),
		SuccessOrders:   summary.ErrorStats["success"],
		FailedOrders:    summary.ErrorStats["failed"],
		ChangedOrders:   summary.ErrorStats["changed"],
		SkippedOrders:   summary.ErrorStats["skipped"],
		Results:         results,
		Summary:         summary,
		Duration:        time.Since(startTime).String(),
	}

	// 记录审计日志
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, "", "batch_status_sync", operatorID, map[string]interface{}{
			"total_orders":     data.TotalOrders,
			"processed_orders": data.ProcessedOrders,
			"success_orders":   data.SuccessOrders,
			"failed_orders":    data.FailedOrders,
			"changed_orders":   data.ChangedOrders,
			"provider":         req.Provider,
			"dry_run":          req.DryRun,
			"duration":         data.Duration,
		})
	}

	s.logger.Info("批量订单状态同步完成",
		zap.String("operator_id", operatorID),
		zap.Int("total_orders", data.TotalOrders),
		zap.Int("success_orders", data.SuccessOrders),
		zap.Int("failed_orders", data.FailedOrders),
		zap.Int("changed_orders", data.ChangedOrders),
		zap.Duration("duration", time.Since(startTime)))

	return &model.BatchStatusSyncResponse{
		Success: true,
		Code:    200,
		Message: "批量状态同步完成",
		Data:    data,
	}, nil
}

// logStatusSyncAudit 记录状态同步审计日志
func (s *AdminOrderService) logStatusSyncAudit(ctx context.Context, result *model.StatusSyncResult, reason string) {
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, fmt.Sprintf("%d", result.OrderID), "status_sync", "system", map[string]interface{}{
			"old_status":           string(result.OldStatus),
			"new_status":           string(result.NewStatus),
			"provider_status":      result.ProviderStatus,
			"provider_status_desc": result.ProviderStatusDesc,
			"status_changed":       result.StatusChanged,
			"validation_passed":    result.ValidationPassed,
			"validation_errors":    result.ValidationErrors,
			"reason":               reason,
			"sync_time":            result.SyncTime,
		})
	}
}

// validateBatchStatusSyncRequest 验证批量状态同步请求
func (s *AdminOrderService) validateBatchStatusSyncRequest(req *model.BatchStatusSyncRequest) error {
	if req == nil {
		return fmt.Errorf("请求不能为空")
	}

	// 设置默认值
	if req.MaxCount <= 0 {
		req.MaxCount = 100
	}

	// 限制最大数量
	maxAllowed := s.configService.GetBatchOperationMaxItems()
	if req.MaxCount > maxAllowed {
		req.MaxCount = maxAllowed
	}

	// 验证时间范围
	if req.StartTime != "" && req.EndTime != "" {
		startTime, err := time.Parse(time.RFC3339, req.StartTime)
		if err != nil {
			return fmt.Errorf("开始时间格式无效: %s", req.StartTime)
		}

		endTime, err := time.Parse(time.RFC3339, req.EndTime)
		if err != nil {
			return fmt.Errorf("结束时间格式无效: %s", req.EndTime)
		}

		if startTime.After(endTime) {
			return fmt.Errorf("开始时间不能晚于结束时间")
		}

		// 限制时间范围不超过30天
		if endTime.Sub(startTime) > 30*24*time.Hour {
			return fmt.Errorf("时间范围不能超过30天")
		}
	}

	// 验证供应商
	if req.Provider != "" && !s.statusMappingService.IsProviderSupported(req.Provider) {
		return fmt.Errorf("供应商 %s 不支持状态同步", req.Provider)
	}

	return nil
}

// getOrdersForStatusSync 获取需要状态同步的订单
func (s *AdminOrderService) getOrdersForStatusSync(ctx context.Context, req *model.BatchStatusSyncRequest) ([]*model.OrderRecord, error) {
	// 构建查询条件
	filter := &model.AdminOrderListRequest{
		OrderListRequest: model.OrderListRequest{
			Page:      1,
			PageSize:  req.MaxCount,
			Provider:  req.Provider,
			StartTime: req.StartTime,
			EndTime:   req.EndTime,
		},
	}

	// 如果指定了订单ID列表，直接查询这些订单
	if len(req.OrderIDs) > 0 {
		var orders []*model.OrderRecord
		for _, orderID := range req.OrderIDs {
			if len(orders) >= req.MaxCount {
				break
			}
			orderRecord, err := s.orderRepository.FindByID(ctx, orderID)
			if err != nil {
				s.logger.Warn("查询订单失败", zap.Int64("order_id", orderID), zap.Error(err))
				continue
			}
			orders = append(orders, orderRecord)
		}
		return orders, nil
	}

	// 查询符合条件的订单
	response, err := s.GetAdminOrderList(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("查询订单列表失败: %w", err)
	}

	// 转换为OrderRecord
	var orders []*model.OrderRecord
	for _, item := range response.Data.Items {
		// 检查供应商是否支持状态同步
		if !s.statusMappingService.IsProviderSupported(item.Provider) {
			continue
		}

		// 如果只处理失败的订单，跳过成功的订单
		if req.OnlyFailed && item.Status != "failed" && item.Status != "exception" {
			continue
		}

		// 如果跳过最近同步的订单，检查最后同步时间
		if req.SkipRecent && req.RecentHours > 0 {
			// 这里需要从数据库查询最后同步时间，暂时跳过
			// TODO: 实现最后同步时间检查
		}

		orderRecord := &model.OrderRecord{
			ID:              item.ID,
			OrderNo:         item.OrderNo,
			CustomerOrderNo: item.CustomerOrderNo,
			TrackingNo:      item.TrackingNo,
			ExpressType:     item.ExpressType,
			ProductType:     item.ProductType,
			Provider:        item.Provider,
			Status:          item.Status,
			Weight:          item.Weight,
			Price:           item.Price,
			SenderInfo:      item.SenderInfo,
			ReceiverInfo:    item.ReceiverInfo,
			CreatedAt:       item.CreatedAt,
			UpdatedAt:       item.UpdatedAt,
		}

		orders = append(orders, orderRecord)
	}

	return orders, nil
}

// performBatchStatusSync 执行批量状态同步
func (s *AdminOrderService) performBatchStatusSync(ctx context.Context, orders []*model.OrderRecord, dryRun bool) []model.StatusSyncResult {
	results := make([]model.StatusSyncResult, 0, len(orders))

	// 使用并发控制
	semaphore := make(chan struct{}, 10) // 最大并发数
	var wg sync.WaitGroup
	var mu sync.Mutex

	for _, order := range orders {
		wg.Add(1)
		go func(orderRecord *model.OrderRecord) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			var result *model.StatusSyncResult
			var err error

			if dryRun {
				// 试运行模式：只检查状态映射，不实际更新
				result = s.dryRunStatusSync(ctx, orderRecord)
			} else {
				// 实际同步
				result, err = s.SyncOrderStatus(ctx, orderRecord.ID)
				if err != nil && result == nil {
					result = &model.StatusSyncResult{
						OrderID:      orderRecord.ID,
						OrderNo:      orderRecord.OrderNo,
						Provider:     orderRecord.Provider,
						OldStatus:    model.SystemOrderStatus(orderRecord.Status),
						Success:      false,
						ErrorMessage: err.Error(),
						SyncTime:     util.NowBeijing(),
					}
				}
			}

			// 线程安全地添加结果
			mu.Lock()
			results = append(results, *result)
			mu.Unlock()
		}(order)
	}

	// 等待所有同步完成
	wg.Wait()

	return results
}

// dryRunStatusSync 试运行状态同步
func (s *AdminOrderService) dryRunStatusSync(ctx context.Context, orderRecord *model.OrderRecord) *model.StatusSyncResult {
	result := &model.StatusSyncResult{
		OrderID:   orderRecord.ID,
		OrderNo:   orderRecord.OrderNo,
		Provider:  orderRecord.Provider,
		OldStatus: model.SystemOrderStatus(orderRecord.Status),
		SyncTime:  util.NowBeijing(),
		Success:   true, // 试运行总是成功的
	}

	// 检查供应商是否支持
	if !s.statusMappingService.IsProviderSupported(orderRecord.Provider) {
		result.Success = false
		result.ErrorMessage = fmt.Sprintf("供应商 %s 不支持状态同步", orderRecord.Provider)
		return result
	}

	// 模拟状态映射（这里可以添加更复杂的逻辑）
	result.NewStatus = result.OldStatus // 试运行时保持状态不变
	result.StatusChanged = false
	result.ValidationPassed = true

	return result
}

// calculateStatusSyncSummary 计算状态同步汇总
func (s *AdminOrderService) calculateStatusSyncSummary(results []model.StatusSyncResult) *model.StatusSyncSummary {
	summary := &model.StatusSyncSummary{
		ProviderStats:     make(map[string]int),
		StatusChangeStats: make(map[string]map[string]int),
		ErrorStats:        make(map[string]int),
		ProcessingTime:    make(map[string]float64),
	}

	for _, result := range results {
		// 供应商统计
		summary.ProviderStats[result.Provider]++

		// 错误统计
		if result.Success {
			summary.ErrorStats["success"]++
			if result.StatusChanged {
				summary.ErrorStats["changed"]++
			}
		} else {
			summary.ErrorStats["failed"]++
		}

		// 状态变更统计
		if result.StatusChanged {
			oldStatus := string(result.OldStatus)
			newStatus := string(result.NewStatus)

			if summary.StatusChangeStats[oldStatus] == nil {
				summary.StatusChangeStats[oldStatus] = make(map[string]int)
			}
			summary.StatusChangeStats[oldStatus][newStatus]++
		}
	}

	return summary
}
