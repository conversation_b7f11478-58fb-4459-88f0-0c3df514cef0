package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// WorkOrderMonitoringConfig 监控配置
type WorkOrderMonitoringConfig struct {
	// 监控间隔
	MonitorInterval time.Duration
	// 告警阈值
	FailureRateThreshold   float64 // 失败率阈值（0.0-1.0）
	ResponseTimeThreshold  time.Duration // 响应时间阈值
	CircuitBreakerThreshold int     // 熔断器触发阈值
	RateLimitThreshold     float64 // 限流阈值（0.0-1.0）
	
	// 告警冷却时间
	AlertCooldown time.Duration
	
	// 启用的监控项
	EnableFailureRateAlert   bool
	EnableResponseTimeAlert  bool
	EnableCircuitBreakerAlert bool
	EnableRateLimitAlert     bool
}

// WorkOrderMonitoringMetrics 监控指标
type WorkOrderMonitoringMetrics struct {
	// 成功/失败统计
	TotalRequests    int64
	SuccessRequests  int64
	FailedRequests   int64
	FailureRate      float64
	
	// 响应时间统计
	TotalResponseTime time.Duration
	AverageResponseTime time.Duration
	MaxResponseTime   time.Duration
	MinResponseTime   time.Duration
	
	// 熔断器状态
	CircuitBreakerState string
	CircuitBreakerFailures int
	
	// 限流状态
	RateLimitHits    int64
	RateLimitRate    float64
	
	// 时间窗口
	WindowStart time.Time
	WindowEnd   time.Time
}

// AlertType 告警类型
type AlertType string

const (
	AlertTypeFailureRate     AlertType = "failure_rate"
	AlertTypeResponseTime    AlertType = "response_time"
	AlertTypeCircuitBreaker  AlertType = "circuit_breaker"
	AlertTypeRateLimit       AlertType = "rate_limit"
)

// Alert 告警信息
type Alert struct {
	Type        AlertType
	Level       string // info, warning, error, critical
	Message     string
	Timestamp   time.Time
	UserID      string
	Metrics     *WorkOrderMonitoringMetrics
	Resolved    bool
	ResolvedAt  *time.Time
}

// AlertHandler 告警处理器接口
type AlertHandler interface {
	HandleAlert(ctx context.Context, alert *Alert) error
}

// WorkOrderMonitoring 工单回调监控系统
type WorkOrderMonitoring struct {
	config           WorkOrderMonitoringConfig
	circuitBreaker   *WorkOrderCircuitBreaker
	rateLimiter      *WorkOrderRateLimiter
	alertHandlers    []AlertHandler
	metrics          *WorkOrderMonitoringMetrics
	alerts           map[string]*Alert // key: alertType_userID
	lastAlertTime    map[string]time.Time
	mu               sync.RWMutex
	logger           *zap.Logger
	
	// 统计数据
	requestTimes     []time.Duration
	requestTimestamps []time.Time
	rateLimitHits    int64
	
	ctx    context.Context
	cancel context.CancelFunc
}

// NewWorkOrderMonitoring 创建工单回调监控系统
func NewWorkOrderMonitoring(
	config WorkOrderMonitoringConfig,
	circuitBreaker *WorkOrderCircuitBreaker,
	rateLimiter *WorkOrderRateLimiter,
	logger *zap.Logger,
) *WorkOrderMonitoring {
	// 设置默认值
	if config.MonitorInterval <= 0 {
		config.MonitorInterval = 30 * time.Second
	}
	if config.FailureRateThreshold <= 0 {
		config.FailureRateThreshold = 0.1 // 10%失败率
	}
	if config.ResponseTimeThreshold <= 0 {
		config.ResponseTimeThreshold = 5 * time.Second
	}
	if config.CircuitBreakerThreshold <= 0 {
		config.CircuitBreakerThreshold = 3
	}
	if config.RateLimitThreshold <= 0 {
		config.RateLimitThreshold = 0.8 // 80%使用率
	}
	if config.AlertCooldown <= 0 {
		config.AlertCooldown = 5 * time.Minute
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	return &WorkOrderMonitoring{
		config:         config,
		circuitBreaker: circuitBreaker,
		rateLimiter:    rateLimiter,
		alertHandlers:  make([]AlertHandler, 0),
		metrics:        &WorkOrderMonitoringMetrics{
			MinResponseTime: time.Hour, // 初始化为一个大值
			WindowStart:     util.NowBeijing(),
		},
		alerts:        make(map[string]*Alert),
		lastAlertTime: make(map[string]time.Time),
		logger:        logger,
		requestTimes:  make([]time.Duration, 0),
		requestTimestamps: make([]time.Time, 0),
		ctx:           ctx,
		cancel:        cancel,
	}
}

// AddAlertHandler 添加告警处理器
func (m *WorkOrderMonitoring) AddAlertHandler(handler AlertHandler) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.alertHandlers = append(m.alertHandlers, handler)
}

// Start 启动监控
func (m *WorkOrderMonitoring) Start() {
	m.logger.Info("启动工单回调监控系统",
		zap.Duration("monitor_interval", m.config.MonitorInterval))
	
	go m.monitorLoop()
}

// Stop 停止监控
func (m *WorkOrderMonitoring) Stop() {
	m.logger.Info("停止工单回调监控系统")
	m.cancel()
}

// RecordRequest 记录请求
func (m *WorkOrderMonitoring) RecordRequest(userID string, responseTime time.Duration, success bool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	now := util.NowBeijing()
	
	// 更新总体统计
	m.metrics.TotalRequests++
	if success {
		m.metrics.SuccessRequests++
	} else {
		m.metrics.FailedRequests++
	}
	
	// 更新失败率
	if m.metrics.TotalRequests > 0 {
		m.metrics.FailureRate = float64(m.metrics.FailedRequests) / float64(m.metrics.TotalRequests)
	}
	
	// 记录响应时间
	m.requestTimes = append(m.requestTimes, responseTime)
	m.requestTimestamps = append(m.requestTimestamps, now)
	
	// 更新响应时间统计
	m.metrics.TotalResponseTime += responseTime
	m.metrics.AverageResponseTime = time.Duration(int64(m.metrics.TotalResponseTime) / m.metrics.TotalRequests)
	
	if responseTime > m.metrics.MaxResponseTime {
		m.metrics.MaxResponseTime = responseTime
	}
	if responseTime < m.metrics.MinResponseTime {
		m.metrics.MinResponseTime = responseTime
	}
	
	// 清理过期数据（保留最近1小时的数据）
	m.cleanupOldData(now)
}

// RecordRateLimitHit 记录限流命中
func (m *WorkOrderMonitoring) RecordRateLimitHit(userID string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.rateLimitHits++
	
	// 计算限流率
	if m.metrics.TotalRequests > 0 {
		m.metrics.RateLimitRate = float64(m.rateLimitHits) / float64(m.metrics.TotalRequests)
	}
}

// monitorLoop 监控循环
func (m *WorkOrderMonitoring) monitorLoop() {
	ticker := time.NewTicker(m.config.MonitorInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.checkAlerts()
		}
	}
}

// checkAlerts 检查告警条件
func (m *WorkOrderMonitoring) checkAlerts() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	now := util.NowBeijing()
	m.metrics.WindowEnd = now
	
	// 1. 检查失败率告警
	if m.config.EnableFailureRateAlert && m.metrics.FailureRate > m.config.FailureRateThreshold {
		m.triggerAlert(AlertTypeFailureRate, "error", 
			fmt.Sprintf("工单回调失败率过高: %.2f%% (阈值: %.2f%%)", 
				m.metrics.FailureRate*100, m.config.FailureRateThreshold*100), "", now)
	}
	
	// 2. 检查响应时间告警
	if m.config.EnableResponseTimeAlert && m.metrics.AverageResponseTime > m.config.ResponseTimeThreshold {
		m.triggerAlert(AlertTypeResponseTime, "warning",
			fmt.Sprintf("工单回调平均响应时间过长: %v (阈值: %v)",
				m.metrics.AverageResponseTime, m.config.ResponseTimeThreshold), "", now)
	}
	
	// 3. 检查熔断器告警
	if m.config.EnableCircuitBreakerAlert {
		cbMetrics := m.circuitBreaker.GetMetrics()
		state := cbMetrics["state"].(string)
		failureCount := cbMetrics["failure_count"].(int)
		
		m.metrics.CircuitBreakerState = state
		m.metrics.CircuitBreakerFailures = failureCount
		
		if state == "open" || state == "half-open" {
			level := "warning"
			if state == "open" {
				level = "error"
			}
			m.triggerAlert(AlertTypeCircuitBreaker, level,
				fmt.Sprintf("工单回调熔断器状态异常: %s (失败次数: %d)", state, failureCount), "", now)
		}
	}
	
	// 4. 检查限流告警
	if m.config.EnableRateLimitAlert && m.metrics.RateLimitRate > m.config.RateLimitThreshold {
		m.triggerAlert(AlertTypeRateLimit, "warning",
			fmt.Sprintf("工单回调限流率过高: %.2f%% (阈值: %.2f%%)",
				m.metrics.RateLimitRate*100, m.config.RateLimitThreshold*100), "", now)
	}
}

// triggerAlert 触发告警
func (m *WorkOrderMonitoring) triggerAlert(alertType AlertType, level, message, userID string, timestamp time.Time) {
	alertKey := fmt.Sprintf("%s_%s", alertType, userID)
	
	// 检查告警冷却时间
	if lastTime, exists := m.lastAlertTime[alertKey]; exists {
		if timestamp.Sub(lastTime) < m.config.AlertCooldown {
			return // 在冷却时间内，跳过告警
		}
	}
	
	alert := &Alert{
		Type:      alertType,
		Level:     level,
		Message:   message,
		Timestamp: timestamp,
		UserID:    userID,
		Metrics:   m.copyMetrics(),
	}
	
	m.alerts[alertKey] = alert
	m.lastAlertTime[alertKey] = timestamp
	
	// 异步处理告警
	go m.handleAlert(alert)
	
	m.logger.Warn("触发工单回调告警",
		zap.String("type", string(alertType)),
		zap.String("level", level),
		zap.String("message", message),
		zap.String("user_id", userID))
}

// handleAlert 处理告警
func (m *WorkOrderMonitoring) handleAlert(alert *Alert) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	for _, handler := range m.alertHandlers {
		if err := handler.HandleAlert(ctx, alert); err != nil {
			m.logger.Error("处理告警失败",
				zap.String("alert_type", string(alert.Type)),
				zap.Error(err))
		}
	}
}

// copyMetrics 复制监控指标
func (m *WorkOrderMonitoring) copyMetrics() *WorkOrderMonitoringMetrics {
	return &WorkOrderMonitoringMetrics{
		TotalRequests:       m.metrics.TotalRequests,
		SuccessRequests:     m.metrics.SuccessRequests,
		FailedRequests:      m.metrics.FailedRequests,
		FailureRate:         m.metrics.FailureRate,
		TotalResponseTime:   m.metrics.TotalResponseTime,
		AverageResponseTime: m.metrics.AverageResponseTime,
		MaxResponseTime:     m.metrics.MaxResponseTime,
		MinResponseTime:     m.metrics.MinResponseTime,
		CircuitBreakerState: m.metrics.CircuitBreakerState,
		CircuitBreakerFailures: m.metrics.CircuitBreakerFailures,
		RateLimitHits:       m.rateLimitHits,
		RateLimitRate:       m.metrics.RateLimitRate,
		WindowStart:         m.metrics.WindowStart,
		WindowEnd:           m.metrics.WindowEnd,
	}
}

// cleanupOldData 清理过期数据
func (m *WorkOrderMonitoring) cleanupOldData(now time.Time) {
	// 保留最近1小时的数据
	cutoff := now.Add(-time.Hour)
	
	// 清理响应时间数据
	var newTimes []time.Duration
	var newTimestamps []time.Time
	
	for i, timestamp := range m.requestTimestamps {
		if timestamp.After(cutoff) {
			newTimes = append(newTimes, m.requestTimes[i])
			newTimestamps = append(newTimestamps, timestamp)
		}
	}
	
	m.requestTimes = newTimes
	m.requestTimestamps = newTimestamps
}

// GetMetrics 获取监控指标
func (m *WorkOrderMonitoring) GetMetrics() *WorkOrderMonitoringMetrics {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.copyMetrics()
}

// GetAlerts 获取活跃告警
func (m *WorkOrderMonitoring) GetAlerts() []*Alert {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	alerts := make([]*Alert, 0, len(m.alerts))
	for _, alert := range m.alerts {
		if !alert.Resolved {
			alerts = append(alerts, alert)
		}
	}
	
	return alerts
}

// ResolveAlert 解决告警
func (m *WorkOrderMonitoring) ResolveAlert(alertType AlertType, userID string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	alertKey := fmt.Sprintf("%s_%s", alertType, userID)
	if alert, exists := m.alerts[alertKey]; exists {
		now := util.NowBeijing()
		alert.Resolved = true
		alert.ResolvedAt = &now
		
		m.logger.Info("告警已解决",
			zap.String("type", string(alertType)),
			zap.String("user_id", userID))
	}
}

// Reset 重置监控数据
func (m *WorkOrderMonitoring) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.metrics = &WorkOrderMonitoringMetrics{
		MinResponseTime: time.Hour,
		WindowStart:     util.NowBeijing(),
	}
	m.alerts = make(map[string]*Alert)
	m.lastAlertTime = make(map[string]time.Time)
	m.requestTimes = make([]time.Duration, 0)
	m.requestTimestamps = make([]time.Time, 0)
	m.rateLimitHits = 0
	
	m.logger.Info("工单回调监控数据已重置")
} 