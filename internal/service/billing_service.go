package service

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"math"
	"time"

	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"
	"github.com/your-org/go-kuaidi/internal/utils"
	"go.uber.org/zap"
)

// BillingHistoryRecord 费用历史记录结构
type BillingHistoryRecord struct {
	AmountAfter      float64   `json:"amount_after"`
	Reason           string    `json:"reason"`
	CreatedAt        time.Time `json:"created_at"`
	AdjustmentAmount float64   `json:"adjustment_amount"`
}

// BillingService 计费服务接口
type BillingService interface {
	// 计费更新
	UpdateOrderBilling(ctx context.Context, req *UpdateBillingRequest) error

	// 计费查询
	GetOrderBillingDetails(ctx context.Context, orderNo string) (*OrderBillingInfo, error)
	GetBillingHistory(ctx context.Context, orderNo string) ([]*model.OrderBillingHistory, error)

	// 费用差额处理
	ProcessBillingDifference(ctx context.Context, orderNo string, actualFee float64, reason string) error

	// 统计查询
	GetBillingStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (*repository.BillingStatistics, error)
}

// UpdateBillingRequest 更新计费请求
type UpdateBillingRequest struct {
	OrderNo     string `json:"order_no"`
	BillingType string `json:"billing_type"` // estimate, actual, adjustment

	// 费用明细
	FreightFee   float64 `json:"freight_fee"`
	InsuranceFee float64 `json:"insurance_fee"`
	PackageFee   float64 `json:"package_fee"`
	PickupFee    float64 `json:"pickup_fee"`
	DeliveryFee  float64 `json:"delivery_fee"`
	CodFee       float64 `json:"cod_fee"`
	OtherFee     float64 `json:"other_fee"`
	TotalFee     float64 `json:"total_fee"`

	// 重量体积信息
	Weight        float64 `json:"weight"`
	Volume        float64 `json:"volume"`
	ChargedWeight float64 `json:"charged_weight"`

	// 元数据
	Provider   string                 `json:"provider"`
	Source     string                 `json:"source"`
	Reason     string                 `json:"reason"`
	OperatorID string                 `json:"operator_id"`
	RawData    map[string]interface{} `json:"raw_data"`
}

// OrderBillingInfo 订单计费信息
type OrderBillingInfo struct {
	OrderNo       string `json:"order_no"`
	BillingStatus string `json:"billing_status"`

	// 费用汇总
	Price        float64 `json:"price"` // 价格（主要价格字段）
	ActualFee    float64 `json:"actual_fee"`
	InsuranceFee float64 `json:"insurance_fee"`
	Difference   float64 `json:"difference"`

	// 重量汇总
	Weight        float64 `json:"weight"` // 下单重量
	ActualWeight  float64 `json:"actual_weight"`
	ChargedWeight float64 `json:"charged_weight"`
	OrderVolume   float64 `json:"order_volume"`
	ActualVolume  float64 `json:"actual_volume"`

	// 详细信息
	Details     []*model.OrderBillingDetail `json:"details"`
	LastUpdated time.Time                   `json:"last_updated"`
}

// BillingServiceImpl 计费服务实现
type BillingServiceImpl struct {
	orderRepo      repository.OrderRepository
	billingRepo    repository.BillingRepository
	balanceService BalanceService
	calculator     *utils.BillingCalculator
	validator      *utils.DataValidator
	logger         *zap.Logger
}

// NewBillingService 创建计费服务
func NewBillingService(
	orderRepo repository.OrderRepository,
	billingRepo repository.BillingRepository,
	balanceService BalanceService,
	logger *zap.Logger,
) BillingService {
	return &BillingServiceImpl{
		orderRepo:      orderRepo,
		billingRepo:    billingRepo,
		balanceService: balanceService,
		calculator:     utils.NewBillingCalculator(),
		validator:      utils.NewDataValidator(),
		logger:         logger,
	}
}

// UpdateOrderBilling 更新订单计费信息
func (s *BillingServiceImpl) UpdateOrderBilling(ctx context.Context, req *UpdateBillingRequest) error {
	s.logger.Info("开始更新订单计费信息",
		zap.String("order_no", req.OrderNo),
		zap.String("billing_type", req.BillingType),
		zap.Float64("total_fee", req.TotalFee))

	// 1. 🚀 使用智能查询服务获取订单信息
	smartFinder := NewSmartOrderFinder(s.orderRepo, s.logger)
	order, err := smartFinder.FindOrderByAnyIdentifier(ctx, req.OrderNo, "")
	if err != nil {
		s.logger.Error("智能查询服务获取订单信息失败",
			zap.String("order_no", req.OrderNo),
			zap.Error(err))
		return fmt.Errorf("获取订单信息失败: %w", err)
	}

	// 2. 记录变更前的值
	oldValues := map[string]interface{}{
		"price":          order.Price,
		"actual_fee":     order.ActualFee,
		"insurance_fee":  order.InsuranceFee,
		"weight":         order.Weight, // 使用weight替代order_weight
		"actual_weight":  order.ActualWeight,
		"charged_weight": order.ChargedWeight,
		"billing_status": order.BillingStatus,
	}

	// 3. 创建计费详情记录
	rawDataBytes, _ := json.Marshal(req.RawData)
	detail := &model.OrderBillingDetail{
		OrderID:       order.ID,
		OrderNo:       req.OrderNo,
		BillingType:   req.BillingType,
		FreightFee:    req.FreightFee,
		InsuranceFee:  req.InsuranceFee,
		PackageFee:    req.PackageFee,
		PickupFee:     req.PickupFee,
		DeliveryFee:   req.DeliveryFee,
		CodFee:        req.CodFee,
		OtherFee:      req.OtherFee,
		TotalFee:      req.TotalFee,
		Weight:        req.Weight,
		Volume:        req.Volume,
		ChargedWeight: req.ChargedWeight,
		Provider:      req.Provider,
		Source:        req.Source,
		RawData:       string(rawDataBytes),
		CreatedAt:     util.NowBeijing(),
		UpdatedAt:     util.NowBeijing(),
	}

	if err := s.billingRepo.SaveBillingDetail(ctx, detail); err != nil {
		return fmt.Errorf("保存计费详情失败: %w", err)
	}

	// 4. 更新订单主表
	if err := s.updateOrderMainFields(ctx, order, req); err != nil {
		return fmt.Errorf("更新订单主表失败: %w", err)
	}

	// 5. 记录变更历史
	newValues := map[string]interface{}{
		"price":          order.Price,
		"actual_fee":     order.ActualFee,
		"insurance_fee":  order.InsuranceFee,
		"weight":         order.Weight, // 使用weight替代order_weight
		"actual_weight":  order.ActualWeight,
		"charged_weight": order.ChargedWeight,
		"billing_status": order.BillingStatus,
	}

	if err := s.recordBillingHistory(ctx, order, req, oldValues, newValues); err != nil {
		s.logger.Warn("记录计费历史失败", zap.Error(err))
		// 不阻断主流程
	}

	s.logger.Info("订单计费信息更新成功",
		zap.String("order_no", req.OrderNo),
		zap.String("billing_type", req.BillingType))

	return nil
}

// updateOrderMainFields 更新订单主表字段
func (s *BillingServiceImpl) updateOrderMainFields(ctx context.Context, order *model.OrderRecord, req *UpdateBillingRequest) error {
	// 根据计费类型更新不同字段
	switch req.BillingType {
	case model.BillingTypeEstimate:
		order.Price = req.TotalFee
		order.Weight = req.Weight // 下单重量
		order.OrderVolume = req.Volume
		order.BillingStatus = model.BillingStatusPending

	case model.BillingTypeActual:
		order.ActualFee = req.TotalFee
		order.ActualWeight = req.Weight
		order.ActualVolume = req.Volume
		order.ChargedWeight = req.ChargedWeight
		order.BillingStatus = model.BillingStatusConfirmed
		// 注意：实际计费不应该覆盖下单重量和预收费用

	case model.BillingTypeAdjustment:
		order.ActualFee = req.TotalFee
		order.ActualWeight = req.Weight
		order.ActualVolume = req.Volume
		order.ChargedWeight = req.ChargedWeight
		// 注意：调整计费不应该覆盖下单重量和预收费用
	}

	// 更新保价费
	order.InsuranceFee = req.InsuranceFee

	order.UpdatedAt = util.NowBeijing()

	return s.orderRepo.Update(ctx, order)
}

// recordBillingHistory 记录计费历史
func (s *BillingServiceImpl) recordBillingHistory(
	ctx context.Context,
	order *model.OrderRecord,
	req *UpdateBillingRequest,
	oldValues, newValues map[string]interface{},
) error {
	oldValuesBytes, _ := json.Marshal(oldValues)
	newValuesBytes, _ := json.Marshal(newValues)

	// 计算费用差额
	var differenceAmount float64
	if oldActualFee, ok := oldValues["actual_fee"].(float64); ok {
		if newActualFee, ok := newValues["actual_fee"].(float64); ok {
			differenceAmount = newActualFee - oldActualFee
		}
	}

	history := &model.OrderBillingHistory{
		OrderID:          order.ID,
		OrderNo:          req.OrderNo,
		ChangeType:       model.ChangeTypeFeeUpdate,
		OldValues:        string(oldValuesBytes),
		NewValues:        string(newValuesBytes),
		DifferenceAmount: differenceAmount,
		Reason:           req.Reason,
		Source:           req.Source,
		Provider:         req.Provider,
		OperatorID:       req.OperatorID,
		CreatedAt:        util.NowBeijing(),
	}

	return s.billingRepo.SaveBillingHistory(ctx, history)
}

// GetOrderBillingDetails 获取订单计费详情
func (s *BillingServiceImpl) GetOrderBillingDetails(ctx context.Context, orderNo string) (*OrderBillingInfo, error) {
	// 🚀 使用智能查询服务获取订单基本信息
	smartFinder := NewSmartOrderFinder(s.orderRepo, s.logger)
	order, err := smartFinder.FindOrderByAnyIdentifier(ctx, orderNo, "")
	if err != nil {
		s.logger.Error("智能查询服务获取订单信息失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return nil, fmt.Errorf("获取订单信息失败: %w", err)
	}

	// 获取计费详情
	details, err := s.billingRepo.GetBillingDetailsByOrderNo(ctx, orderNo)
	if err != nil {
		return nil, fmt.Errorf("获取计费详情失败: %w", err)
	}

	// 构建返回信息
	info := &OrderBillingInfo{
		OrderNo:       orderNo,
		BillingStatus: order.BillingStatus,
		Price:         order.Price, // 使用Price作为主要价格字段
		ActualFee:     order.ActualFee,
		InsuranceFee:  order.InsuranceFee,
		Difference:    order.ActualFee - order.Price,
		Weight:        order.Weight, // 使用weight替代order_weight
		ActualWeight:  order.ActualWeight,
		ChargedWeight: order.ChargedWeight,
		OrderVolume:   order.OrderVolume,
		ActualVolume:  order.ActualVolume,
		Details:       details,
		LastUpdated:   order.UpdatedAt,
	}

	return info, nil
}

// GetBillingHistory 获取计费历史
func (s *BillingServiceImpl) GetBillingHistory(ctx context.Context, orderNo string) ([]*model.OrderBillingHistory, error) {
	return s.billingRepo.GetBillingHistoryByOrderNo(ctx, orderNo)
}

// ProcessBillingDifference 处理费用差额 - 企业级实现（只扣差额，考虑实收金额）
func (s *BillingServiceImpl) ProcessBillingDifference(ctx context.Context, orderNo string, actualFee float64, reason string) error {
	// 🚀 使用智能查询服务获取订单信息
	smartFinder := NewSmartOrderFinder(s.orderRepo, s.logger)
	order, err := smartFinder.FindOrderByAnyIdentifier(ctx, orderNo, "")
	if err != nil {
		s.logger.Error("智能查询服务获取订单信息失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return fmt.Errorf("获取订单信息失败: %w", err)
	}

	// 记录变更前的值用于审计
	oldActualFee := order.ActualFee

	// 🔥 核心防护：检查是否已处理过相同费用调整
	if err := s.checkBillingAdjustmentIdempotency(ctx, orderNo, actualFee, reason); err != nil {
		s.logger.Warn("🚨 检测到重复费用调整，跳过处理",
			zap.String("order_no", orderNo),
			zap.Float64("actual_fee", actualFee),
			zap.String("reason", reason),
			zap.Error(err))
		return nil // 不返回错误，避免重试
	}

	s.logger.Debug("🔥 开始处理费用差额 - 增强防护模式", // 改为Debug级别
		zap.String("order_no", orderNo),
		zap.String("status", order.Status),
		zap.Float64("order_price", order.Price),       // 用户下单时的预估费用
		zap.Float64("supplier_actual_fee", actualFee), // 供应商推送的实际总费用
		zap.Float64("old_actual_fee", oldActualFee))

	// 🔥 核心修复：查询该客户订单号目前实收了用户多少钱
	userActualPaid, err := s.calculateActualPaidAmount(ctx, order)
	if err != nil {
		s.logger.Error("获取用户实际支付金额失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return fmt.Errorf("获取用户实际支付金额失败: %w", err)
	}

	// 供应商回调总费用
	supplierTotalFee := actualFee

	// 🔥 核心修复：计算费用差额
	// 差额 = 供应商总费用 - 用户实际已付金额
	feeDifference := supplierTotalFee - userActualPaid

	s.logger.Debug("费用差额计算详情", // 改为Debug级别
		zap.String("order_no", orderNo),
		zap.Float64("user_actual_paid", userActualPaid),     // 用户实际已付金额
		zap.Float64("supplier_total_fee", supplierTotalFee), // 供应商总费用
		zap.Float64("fee_difference", feeDifference),        // 费用差额
		zap.String("difference_type", s.getDifferenceType(feeDifference)))

	// 🔥 关键修复：无论如何都要更新actual_fee字段（用于对账和审计）
	updateReq := &repository.OrderBillingUpdateRequest{
		OrderNo:  orderNo,
		TotalFee: actualFee,
		Weight:   0, // 不更新重量字段
	}

	if err := s.orderRepo.UpdateOrderActualFeeOnly(ctx, updateReq); err != nil {
		s.logger.Error("更新订单actual_fee失败",
			zap.String("order_no", orderNo),
			zap.Float64("actual_fee", actualFee),
			zap.Error(err))
		return fmt.Errorf("更新订单actual_fee失败: %w", err)
	}

	s.logger.Info("订单actual_fee更新成功",
		zap.String("order_no", orderNo),
		zap.Float64("old_actual_fee", oldActualFee),
		zap.Float64("new_actual_fee", actualFee))

	// 记录计费历史
	if err := s.recordBillingDifferenceHistory(ctx, order, oldActualFee, actualFee, feeDifference, reason); err != nil {
		s.logger.Warn("记录费用差额历史失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
	}

	// 🔥 核心修复：只有真正的差额才需要处理余额调整
	if feeDifference > utils.FeePrecisionThreshold {
		// 供应商费用高于用户支付，需要补收差额
		s.logger.Info("需要补收费用差额",
			zap.String("order_no", orderNo),
			zap.String("status", order.Status),
			zap.Float64("user_actual_paid", userActualPaid),
			zap.Float64("supplier_total_fee", supplierTotalFee),
			zap.Float64("difference_amount", feeDifference),
			zap.String("transaction_type", "费用差额补收"))

		if s.balanceService != nil {
			// 修复：必须使用ChargeForBillingDifference，不能用PreChargeForOrder
			err := s.balanceService.ChargeForBillingDifference(ctx, order.UserID, order.OrderNo, decimal.NewFromFloat(feeDifference))
			if err != nil {
				s.logger.Error("费用差额补收失败",
					zap.String("order_no", orderNo),
					zap.String("status", order.Status),
					zap.Float64("amount", feeDifference),
					zap.Error(err))
				return err
			}
		}

	} else if feeDifference < -utils.FeePrecisionThreshold {
		// 供应商费用低于用户支付，需要退还差额
		refundAmount := -feeDifference
		s.logger.Info("需要退还费用差额",
			zap.String("order_no", orderNo),
			zap.Float64("user_actual_paid", userActualPaid),
			zap.Float64("supplier_total_fee", supplierTotalFee),
			zap.Float64("refund_amount", refundAmount),
			zap.String("transaction_type", "费用差额退款"))

		if s.balanceService != nil {
			err := s.processRefund(ctx, order, refundAmount, "供应商费用低于预付费用，退还差额")
			if err != nil {
				s.logger.Error("费用差额退款失败",
					zap.String("order_no", orderNo),
					zap.Float64("amount", refundAmount),
					zap.Error(err))
				return err
			}
		}
	} else {
		// 差额在精度范围内，无需操作
		s.logger.Info("费用差额在精度范围内，无需操作",
			zap.String("order_no", orderNo),
			zap.Float64("difference", feeDifference),
			zap.Float64("threshold", utils.FeePrecisionThreshold))
	}

	// 🚀 记录费用调整审计信息（防止重复处理）
	if err := s.recordBillingAdjustmentAudit(ctx, orderNo, order, actualFee, feeDifference, reason); err != nil {
		s.logger.Warn("记录费用调整审计失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		// 不返回错误，避免影响主流程
	}

	s.logger.Info("✅ 费用差额处理完成",
		zap.String("order_no", orderNo),
		zap.Float64("fee_difference", feeDifference))

	return nil
}

// recordBillingAdjustmentAudit 记录费用调整审计信息
func (s *BillingServiceImpl) recordBillingAdjustmentAudit(ctx context.Context, orderNo string, order *model.OrderRecord, actualFee, feeDifference float64, reason string) error {
	// 🚀 生成费用内容指纹用于防重复
	feeFingerprint := s.generateFeeContentFingerprint(orderNo, actualFee, reason)

	// 插入审计记录
	insertQuery := `
		INSERT INTO billing_adjustment_audit (
			order_no, user_id, adjustment_type, amount_before, amount_after,
			adjustment_amount, reason, auto_approved, requires_manual_review
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	// 判断是否需要人工审核（大额调整）
	requiresManualReview := math.Abs(feeDifference) > 50.0 // 超过50元需要人工审核

	err := s.orderRepo.ExecRawSQL(ctx, insertQuery,
		orderNo, order.UserID, "billing_difference",
		order.ActualFee, actualFee, feeDifference,
		reason, !requiresManualReview, requiresManualReview)

	if err != nil {
		s.logger.Error("记录费用调整审计失败",
			zap.String("order_no", orderNo),
			zap.String("fee_fingerprint", feeFingerprint),
			zap.Error(err))
		return err
	}

	s.logger.Info("✅ 费用调整审计记录成功",
		zap.String("order_no", orderNo),
		zap.Float64("actual_fee", actualFee),
		zap.Float64("fee_difference", feeDifference),
		zap.String("fee_fingerprint", feeFingerprint),
		zap.Bool("requires_manual_review", requiresManualReview))

	return nil
}

// calculateActualPaidAmount 计算用户实际支付金额（高并发优化版）
func (s *BillingServiceImpl) calculateActualPaidAmount(ctx context.Context, order *model.OrderRecord) (float64, error) {
	// 🚀 高并发优化：使用直接SQL查询，避免复杂的GetOrderNetPayment方法
	// 这个方法在高并发下性能更好
	actualPaid, err := s.calculateActualPaidAmountDirect(ctx, order)
	if err != nil {
		s.logger.Warn("直接查询失败，回退到标准方法",
			zap.String("order_no", order.OrderNo),
			zap.Error(err))

		// 回退到原方法
		netPayment, err := s.balanceService.GetOrderNetPayment(ctx, order.UserID, order.OrderNo, order.CustomerOrderNo)
		if err != nil {
			s.logger.Warn("获取订单净支付金额失败，使用订单价格作为回退",
				zap.String("order_no", order.OrderNo),
				zap.Float64("order_price", order.Price),
				zap.Error(err))
			return order.Price, nil
		}
		actualPaid, _ = netPayment.Float64()
	}

	s.logger.Debug("获取本订单实际支付金额成功", // 改为Debug级别减少日志量
		zap.String("order_no", order.OrderNo),
		zap.Float64("actual_paid_amount", actualPaid),
		zap.Float64("order_price", order.Price),
		zap.String("calculation_method", "高并发优化查询"))

	return actualPaid, nil
}

// calculateActualPaidAmountDirect 直接SQL查询计算实际支付金额（高并发优化）
func (s *BillingServiceImpl) calculateActualPaidAmountDirect(ctx context.Context, order *model.OrderRecord) (float64, error) {
	// 🚀 高并发优化：使用单个聚合查询，避免复杂的行扫描
	// 🔥 修复：同时查询供应商订单号和客户订单号，确保能找到交易记录
	query := `
		SELECT
			COALESCE(SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END), 0) as total_paid,
			COALESCE(SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END), 0) as total_refunded
		FROM balance_transactions
		WHERE user_id = $1
		AND (order_no = $2 OR customer_order_no = $2 OR order_no = $3 OR customer_order_no = $3)
		AND status = 'completed'
	`

	rows, err := s.orderRepo.QueryRawSQL(ctx, query, order.UserID, order.OrderNo, order.CustomerOrderNo)
	if err != nil {
		return 0, fmt.Errorf("查询订单支付金额失败: %w", err)
	}
	defer rows.Close()

	var totalPaid, totalRefunded float64
	if rows.Next() {
		err = rows.Scan(&totalPaid, &totalRefunded)
		if err != nil {
			return 0, fmt.Errorf("扫描支付金额失败: %w", err)
		}
	}

	netPayment := totalPaid - totalRefunded
	return netPayment, nil
}

// getDifferenceType 获取差额类型描述
func (s *BillingServiceImpl) getDifferenceType(difference float64) string {
	if difference > utils.FeePrecisionThreshold {
		return "需要补收"
	} else if difference < -utils.FeePrecisionThreshold {
		return "需要退款"
	}
	return "无需调整"
}

// processRefund 处理退款 - 使用超轻退款交易类型
func (s *BillingServiceImpl) processRefund(ctx context.Context, order *model.OrderRecord, amount float64, reason string) error {
	s.logger.Info("开始处理退款",
		zap.String("order_no", order.OrderNo),
		zap.String("customer_order_no", order.CustomerOrderNo),
		zap.String("user_id", order.UserID),
		zap.Float64("refund_amount", amount),
		zap.String("reason", reason))

	// 转换为decimal类型
	refundAmount := decimal.NewFromFloat(amount)

	// 创建费用差额退款交易记录
	err := s.createBillingDifferenceRefund(ctx, order, refundAmount, reason)
	if err != nil {
		return fmt.Errorf("费用差额退款失败: %w", err)
	}

	s.logger.Info("费用差额退款处理成功",
		zap.String("order_no", order.OrderNo),
		zap.String("customer_order_no", order.CustomerOrderNo),
		zap.Float64("refund_amount", amount),
		zap.String("transaction_type", "billing_difference_refund"))

	return nil
}

// recordBillingDifferenceHistory 记录费用差额处理历史
func (s *BillingServiceImpl) recordBillingDifferenceHistory(
	ctx context.Context,
	order *model.OrderRecord,
	oldActualFee, newActualFee, difference float64,
	reason string,
) error {
	// 构建详细的变更记录
	oldValues := map[string]interface{}{
		"actual_fee": oldActualFee,
		"status":     order.Status,
		"price":      order.Price,
	}

	newValues := map[string]interface{}{
		"actual_fee": newActualFee,
		"status":     order.Status,
		"price":      order.Price,
	}

	oldValuesBytes, _ := json.Marshal(oldValues)
	newValuesBytes, _ := json.Marshal(newValues)

	// 根据订单状态确定变更类型和详细原因
	changeType := model.ChangeTypeFeeUpdate
	detailedReason := reason

	if order.Status == model.OrderStatusCancelled || order.Status == "canceled" {
		detailedReason = fmt.Sprintf("已取消订单费用差额处理: %s (差额: %.2f元)", reason, difference)
	} else {
		detailedReason = fmt.Sprintf("费用差额处理: %s (差额: %.2f元)", reason, difference)
	}

	history := &model.OrderBillingHistory{
		OrderID:          order.ID,
		OrderNo:          order.OrderNo,
		ChangeType:       changeType,
		OldValues:        string(oldValuesBytes),
		NewValues:        string(newValuesBytes),
		DifferenceAmount: newActualFee - oldActualFee,
		Reason:           detailedReason,
		Source:           model.BillingSourceCallback,
		Provider:         order.Provider,
		OperatorID:       "system",
		CreatedAt:        util.NowBeijing(),
	}

	return s.billingRepo.SaveBillingHistory(ctx, history)
}

// TransactionRecord 交易记录结构（简化保留，可能其他地方使用）
type TransactionRecord struct {
	OrderNo string  `json:"order_no"`
	Type    string  `json:"type"`
	Amount  float64 `json:"amount"`
}

// GetBillingStatistics 获取计费统计信息
func (s *BillingServiceImpl) GetBillingStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (*repository.BillingStatistics, error) {
	return s.billingRepo.GetBillingStatistics(ctx, userID, startTime, endTime)
}

// 🚀 企业级智能幂等性检查：防止重复扣费但允许真正的费用变更
func (s *BillingServiceImpl) checkBillingAdjustmentIdempotency(ctx context.Context, orderNo string, actualFee float64, reason string) error {
	// 🔥 智能机制1：基于费用内容的精确指纹识别
	feeContentFingerprint := s.generateFeeContentFingerprint(orderNo, actualFee, reason)

	// 🔥 智能机制2：查询该订单的所有历史费用调整记录（无时间限制）
	historyQuery := `
		SELECT amount_after, reason, created_at, adjustment_amount
		FROM billing_adjustment_audit
		WHERE order_no = $1
		ORDER BY created_at DESC
		LIMIT 10
	`

	rows, err := s.orderRepo.QueryRawSQL(ctx, historyQuery, orderNo)
	if err != nil {
		s.logger.Warn("智能幂等性检查失败，使用回退逻辑", zap.Error(err))
		return s.checkBillingAdjustmentIdempotencyEnhanced(ctx, orderNo, actualFee, reason)
	}
	defer rows.Close()

	var historyRecords []BillingHistoryRecord
	for rows.Next() {
		var record BillingHistoryRecord
		var amountAfter, adjustmentAmount float64
		var createdAt time.Time
		var reasonStr string

		err = rows.Scan(&amountAfter, &reasonStr, &createdAt, &adjustmentAmount)
		if err != nil {
			s.logger.Error("扫描费用历史记录失败", zap.Error(err))
			continue
		}

		record.AmountAfter = amountAfter
		record.Reason = reasonStr
		record.CreatedAt = createdAt
		record.AdjustmentAmount = adjustmentAmount
		historyRecords = append(historyRecords, record)
	}

	// 🔥 智能机制3：检查是否为完全相同的费用内容
	for _, record := range historyRecords {
		historicalFingerprint := s.generateFeeContentFingerprint(orderNo, record.AmountAfter, record.Reason)
		if historicalFingerprint == feeContentFingerprint {
			s.logger.Warn("🚨 检测到完全相同的费用内容，拒绝重复处理",
				zap.String("order_no", orderNo),
				zap.Float64("actual_fee", actualFee),
				zap.Float64("historical_fee", record.AmountAfter),
				zap.String("fee_fingerprint", feeContentFingerprint),
				zap.Time("historical_time", record.CreatedAt))
			return fmt.Errorf("检测到重复的费用调整：相同费用内容 %.2f 元", actualFee)
		}
	}

	// 🔥 智能机制4：费用变更合理性检查
	if len(historyRecords) > 0 {
		latestRecord := historyRecords[0]
		feeChange := actualFee - latestRecord.AmountAfter

		s.logger.Info("检测到费用变更",
			zap.String("order_no", orderNo),
			zap.Float64("previous_fee", latestRecord.AmountAfter),
			zap.Float64("new_fee", actualFee),
			zap.Float64("fee_change", feeChange),
			zap.String("change_type", s.getFeeChangeType(feeChange)))

		// 检查是否为异常的费用变更
		if math.Abs(feeChange) > 100 { // 超过100元的变更需要特别关注
			s.logger.Warn("⚠️ 检测到异常大的费用变更，建议人工审核",
				zap.String("order_no", orderNo),
				zap.Float64("fee_change", feeChange))
		}

		// 检查频繁变更（24小时内超过3次）
		recentChanges := 0
		cutoffTime := time.Now().Add(-24 * time.Hour)
		for _, record := range historyRecords {
			if record.CreatedAt.After(cutoffTime) {
				recentChanges++
			}
		}

		if recentChanges >= 3 {
			s.logger.Error("🚨 检测到频繁的费用变更，可能存在异常",
				zap.String("order_no", orderNo),
				zap.Int("recent_changes", recentChanges))
			return fmt.Errorf("订单费用变更过于频繁，24小时内已有 %d 次变更", recentChanges)
		}
	}

	s.logger.Info("✅ 智能幂等性检查通过，允许费用调整",
		zap.String("order_no", orderNo),
		zap.Float64("actual_fee", actualFee),
		zap.String("fee_fingerprint", feeContentFingerprint))

	return nil
}

// createBillingDifferenceRefund 创建费用差额退款交易记录
func (s *BillingServiceImpl) createBillingDifferenceRefund(ctx context.Context, order *model.OrderRecord, amount decimal.Decimal, reason string) error {
	// 🔥 修复：使用专门的费用差额退款方法，确保交易类型正确
	return s.balanceService.RefundForBillingDifference(ctx, order.UserID, order.OrderNo, amount)
}

// 🚀 智能幂等性辅助方法

// generateFeeContentFingerprint 生成费用内容指纹
func (s *BillingServiceImpl) generateFeeContentFingerprint(orderNo string, actualFee float64, reason string) string {
	// 🔥 核心修复：只基于订单号+费用金额生成唯一指纹
	// 移除重量和原因，只关注真正影响扣费的核心要素：费用金额
	content := fmt.Sprintf("%s|%.2f", orderNo, actualFee)

	hash := sha256.Sum256([]byte(content))
	fingerprint := fmt.Sprintf("%x", hash)[:16] // 取前16位作为指纹

	s.logger.Debug("🔍 生成费用内容指纹",
		zap.String("order_no", orderNo),
		zap.Float64("total_fee", actualFee),
		zap.String("fingerprint", fingerprint),
		zap.String("content", content))

	return fingerprint
}

// getFeeChangeType 获取费用变更类型描述
func (s *BillingServiceImpl) getFeeChangeType(feeChange float64) string {
	if feeChange > 0 {
		return "费用增加"
	} else if feeChange < 0 {
		return "费用减少"
	}
	return "费用不变"
}

// checkBillingAdjustmentIdempotencyEnhanced 增强型幂等性检查（回退方法）
func (s *BillingServiceImpl) checkBillingAdjustmentIdempotencyEnhanced(ctx context.Context, orderNo string, actualFee float64, reason string) error {
	// 🔥 增强型检查：基于费用金额的精确匹配（无时间限制）
	checkQuery := `
		SELECT amount_after, created_at, reason
		FROM billing_adjustment_audit
		WHERE order_no = $1 AND ABS(amount_after - $2) < 0.01
		ORDER BY created_at DESC
		LIMIT 5
	`

	rows, err := s.orderRepo.QueryRawSQL(ctx, checkQuery, orderNo, actualFee)
	if err != nil {
		s.logger.Error("增强型幂等性检查失败", zap.Error(err))
		return nil // 检查失败时允许继续处理，但记录错误
	}
	defer rows.Close()

	var duplicateRecords []BillingHistoryRecord
	for rows.Next() {
		var record BillingHistoryRecord
		var amountAfter float64
		var createdAt time.Time
		var reasonStr string

		err = rows.Scan(&amountAfter, &createdAt, &reasonStr)
		if err != nil {
			s.logger.Error("扫描增强型幂等性记录失败", zap.Error(err))
			continue
		}

		record.AmountAfter = amountAfter
		record.CreatedAt = createdAt
		record.Reason = reasonStr
		duplicateRecords = append(duplicateRecords, record)
	}

	// 检查是否存在完全相同的费用和原因
	for _, record := range duplicateRecords {
		if math.Abs(record.AmountAfter-actualFee) < 0.01 && record.Reason == reason {
			s.logger.Warn("🚨 增强型检查：检测到重复的费用调整",
				zap.String("order_no", orderNo),
				zap.Float64("actual_fee", actualFee),
				zap.String("reason", reason),
				zap.Time("previous_time", record.CreatedAt))
			return fmt.Errorf("检测到重复的费用调整：%.2f 元", actualFee)
		}
	}

	return nil
}
