package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
)

// EnhancedAuditService 增强审计服务接口
type EnhancedAuditService interface {
	// 记录管理员操作审计日志
	LogAdminOperation(ctx context.Context, log *EnhancedAuditLog) error
	// 查询审计日志
	QueryAuditLogs(ctx context.Context, req *AuditLogQueryRequest) (*AuditLogQueryResponse, error)
	// 获取操作统计
	GetOperationStatistics(ctx context.Context, req *OperationStatsRequest) (*OperationStatsResponse, error)
	// 检测异常操作
	DetectAnomalousOperations(ctx context.Context, adminID string, duration time.Duration) ([]*AnomalousOperation, error)
}

// EnhancedAuditLog 增强审计日志
type EnhancedAuditLog struct {
	ID               string                 `json:"id"`
	AdminID          string                 `json:"admin_id"`
	TargetUserID     string                 `json:"target_user_id,omitempty"`
	OperationType    string                 `json:"operation_type"`
	OperationDetails map[string]interface{} `json:"operation_details"`
	Amount           *decimal.Decimal       `json:"amount,omitempty"`
	BeforeBalance    *decimal.Decimal       `json:"before_balance,omitempty"`
	AfterBalance     *decimal.Decimal       `json:"after_balance,omitempty"`
	IPAddress        string                 `json:"ip_address"`
	UserAgent        string                 `json:"user_agent"`
	RequestID        string                 `json:"request_id"`
	SessionID        string                 `json:"session_id"`
	Result           string                 `json:"result"` // success, failed, pending
	ErrorMessage     string                 `json:"error_message,omitempty"`
	ExecutionTimeMs  int                    `json:"execution_time_ms"`
	CreatedAt        time.Time              `json:"created_at"`
}

// AuditLogQueryRequest 审计日志查询请求
type AuditLogQueryRequest struct {
	AdminID       string    `json:"admin_id,omitempty"`
	TargetUserID  string    `json:"target_user_id,omitempty"`
	OperationType string    `json:"operation_type,omitempty"`
	Result        string    `json:"result,omitempty"`
	StartTime     time.Time `json:"start_time,omitempty"`
	EndTime       time.Time `json:"end_time,omitempty"`
	Page          int       `json:"page"`
	PageSize      int       `json:"page_size"`
}

// AuditLogQueryResponse 审计日志查询响应
type AuditLogQueryResponse struct {
	Items      []*EnhancedAuditLog `json:"items"`
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	PageSize   int                 `json:"page_size"`
	TotalPages int                 `json:"total_pages"`
}

// OperationStatsRequest 操作统计请求
type OperationStatsRequest struct {
	AdminID   string    `json:"admin_id,omitempty"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

// OperationStatsResponse 操作统计响应
type OperationStatsResponse struct {
	TotalOperations    int64                      `json:"total_operations"`
	SuccessOperations  int64                      `json:"success_operations"`
	FailedOperations   int64                      `json:"failed_operations"`
	OperationsByType   map[string]int64           `json:"operations_by_type"`
	OperationsByAdmin  map[string]int64           `json:"operations_by_admin"`
	AverageExecutionMs float64                    `json:"average_execution_ms"`
	TotalAmount        decimal.Decimal            `json:"total_amount"`
	AmountByType       map[string]decimal.Decimal `json:"amount_by_type"`
}

// AnomalousOperation 异常操作
type AnomalousOperation struct {
	AdminID       string          `json:"admin_id"`
	OperationType string          `json:"operation_type"`
	Count         int64           `json:"count"`
	TotalAmount   decimal.Decimal `json:"total_amount"`
	AverageAmount decimal.Decimal `json:"average_amount"`
	Reason        string          `json:"reason"`
	Severity      string          `json:"severity"` // low, medium, high, critical
}

// DefaultEnhancedAuditService 默认增强审计服务实现
type DefaultEnhancedAuditService struct {
	db            *sql.DB
	configService SystemConfigService
	logger        *zap.Logger
}

// NewEnhancedAuditService 创建增强审计服务
func NewEnhancedAuditService(
	db *sql.DB,
	configService SystemConfigService,
	logger *zap.Logger,
) EnhancedAuditService {
	return &DefaultEnhancedAuditService{
		db:            db,
		configService: configService,
		logger:        logger,
	}
}

// LogAdminOperation 记录管理员操作审计日志
func (s *DefaultEnhancedAuditService) LogAdminOperation(ctx context.Context, log *EnhancedAuditLog) error {
	// 检查是否启用详细审计
	detailedAuditEnabled := s.configService.GetConfigAsBoolWithDefault("admin_balance.detailed_audit_enabled", true)
	if !detailedAuditEnabled {
		return nil
	}

	// 序列化操作详情
	operationDetailsJSON, err := json.Marshal(log.OperationDetails)
	if err != nil {
		s.logger.Error("序列化操作详情失败", zap.Error(err))
		operationDetailsJSON = []byte("{}")
	}

	// 解析IP地址
	var ipAddr net.IP
	if log.IPAddress != "" {
		ipAddr = net.ParseIP(log.IPAddress)
	}

	query := `
		INSERT INTO admin_balance_audit_logs_enhanced (
			id, admin_id, target_user_id, operation_type, operation_details,
			amount, before_balance, after_balance, ip_address, user_agent,
			request_id, session_id, result, error_message, execution_time_ms, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
	`

	_, err = s.db.ExecContext(ctx, query,
		log.ID, log.AdminID, log.TargetUserID, log.OperationType, operationDetailsJSON,
		log.Amount, log.BeforeBalance, log.AfterBalance, ipAddr, log.UserAgent,
		log.RequestID, log.SessionID, log.Result, log.ErrorMessage, log.ExecutionTimeMs, log.CreatedAt,
	)

	if err != nil {
		s.logger.Error("记录增强审计日志失败",
			zap.String("admin_id", log.AdminID),
			zap.String("operation_type", log.OperationType),
			zap.Error(err))
		return fmt.Errorf("记录增强审计日志失败: %w", err)
	}

	s.logger.Debug("记录增强审计日志成功",
		zap.String("admin_id", log.AdminID),
		zap.String("operation_type", log.OperationType),
		zap.String("result", log.Result))

	return nil
}

// QueryAuditLogs 查询审计日志
func (s *DefaultEnhancedAuditService) QueryAuditLogs(ctx context.Context, req *AuditLogQueryRequest) (*AuditLogQueryResponse, error) {
	// 构建查询条件
	whereClause := "WHERE 1=1"
	args := []interface{}{}
	argIndex := 1

	if req.AdminID != "" {
		whereClause += fmt.Sprintf(" AND admin_id = $%d", argIndex)
		args = append(args, req.AdminID)
		argIndex++
	}

	if req.TargetUserID != "" {
		whereClause += fmt.Sprintf(" AND target_user_id = $%d", argIndex)
		args = append(args, req.TargetUserID)
		argIndex++
	}

	if req.OperationType != "" {
		whereClause += fmt.Sprintf(" AND operation_type = $%d", argIndex)
		args = append(args, req.OperationType)
		argIndex++
	}

	if req.Result != "" {
		whereClause += fmt.Sprintf(" AND result = $%d", argIndex)
		args = append(args, req.Result)
		argIndex++
	}

	if !req.StartTime.IsZero() {
		whereClause += fmt.Sprintf(" AND created_at >= $%d", argIndex)
		args = append(args, req.StartTime)
		argIndex++
	}

	if !req.EndTime.IsZero() {
		whereClause += fmt.Sprintf(" AND created_at <= $%d", argIndex)
		args = append(args, req.EndTime)
		argIndex++
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM admin_balance_audit_logs_enhanced %s", whereClause)
	var total int64
	err := s.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		s.logger.Error("查询审计日志总数失败", zap.Error(err))
		return nil, fmt.Errorf("查询审计日志总数失败: %w", err)
	}

	// 查询数据
	offset := (req.Page - 1) * req.PageSize
	dataQuery := fmt.Sprintf(`
		SELECT id, admin_id, COALESCE(target_user_id, ''), operation_type, operation_details,
			   COALESCE(amount, 0), COALESCE(before_balance, 0), COALESCE(after_balance, 0),
			   COALESCE(ip_address::text, ''), COALESCE(user_agent, ''),
			   COALESCE(request_id, ''), COALESCE(session_id, ''),
			   result, COALESCE(error_message, ''), execution_time_ms, created_at
		FROM admin_balance_audit_logs_enhanced %s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, req.PageSize, offset)

	rows, err := s.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		s.logger.Error("查询审计日志数据失败", zap.Error(err))
		return nil, fmt.Errorf("查询审计日志数据失败: %w", err)
	}
	defer rows.Close()

	var logs []*EnhancedAuditLog
	for rows.Next() {
		log := &EnhancedAuditLog{}
		var operationDetailsJSON []byte
		var amount, beforeBalance, afterBalance decimal.Decimal

		err := rows.Scan(
			&log.ID, &log.AdminID, &log.TargetUserID, &log.OperationType, &operationDetailsJSON,
			&amount, &beforeBalance, &afterBalance,
			&log.IPAddress, &log.UserAgent, &log.RequestID, &log.SessionID,
			&log.Result, &log.ErrorMessage, &log.ExecutionTimeMs, &log.CreatedAt,
		)
		if err != nil {
			s.logger.Error("扫描审计日志数据失败", zap.Error(err))
			continue
		}

		// 反序列化操作详情
		if err := json.Unmarshal(operationDetailsJSON, &log.OperationDetails); err != nil {
			s.logger.Error("反序列化操作详情失败", zap.Error(err))
			log.OperationDetails = make(map[string]interface{})
		}

		// 设置金额字段
		if !amount.IsZero() {
			log.Amount = &amount
		}
		if !beforeBalance.IsZero() {
			log.BeforeBalance = &beforeBalance
		}
		if !afterBalance.IsZero() {
			log.AfterBalance = &afterBalance
		}

		logs = append(logs, log)
	}

	if err := rows.Err(); err != nil {
		s.logger.Error("遍历审计日志数据失败", zap.Error(err))
		return nil, fmt.Errorf("遍历审计日志数据失败: %w", err)
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	return &AuditLogQueryResponse{
		Items:      logs,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// GetOperationStatistics 获取操作统计
func (s *DefaultEnhancedAuditService) GetOperationStatistics(ctx context.Context, req *OperationStatsRequest) (*OperationStatsResponse, error) {
	// 构建查询条件
	whereClause := "WHERE created_at >= $1 AND created_at <= $2"
	args := []interface{}{req.StartTime, req.EndTime}
	argIndex := 3

	if req.AdminID != "" {
		whereClause += fmt.Sprintf(" AND admin_id = $%d", argIndex)
		args = append(args, req.AdminID)
		argIndex++
	}

	// 查询基本统计
	statsQuery := fmt.Sprintf(`
		SELECT
			COUNT(*) as total_operations,
			COUNT(CASE WHEN result = 'success' THEN 1 END) as success_operations,
			COUNT(CASE WHEN result = 'failed' THEN 1 END) as failed_operations,
			AVG(execution_time_ms) as avg_execution_ms,
			COALESCE(SUM(amount), 0) as total_amount
		FROM admin_balance_audit_logs_enhanced %s
	`, whereClause)

	var stats OperationStatsResponse
	stats.OperationsByType = make(map[string]int64)
	stats.OperationsByAdmin = make(map[string]int64)
	stats.AmountByType = make(map[string]decimal.Decimal)

	err := s.db.QueryRowContext(ctx, statsQuery, args...).Scan(
		&stats.TotalOperations, &stats.SuccessOperations, &stats.FailedOperations,
		&stats.AverageExecutionMs, &stats.TotalAmount,
	)
	if err != nil {
		s.logger.Error("查询操作统计失败", zap.Error(err))
		return nil, fmt.Errorf("查询操作统计失败: %w", err)
	}

	// 查询按操作类型分组的统计
	typeStatsQuery := fmt.Sprintf(`
		SELECT operation_type, COUNT(*) as count, COALESCE(SUM(amount), 0) as total_amount
		FROM admin_balance_audit_logs_enhanced %s
		GROUP BY operation_type
	`, whereClause)

	rows, err := s.db.QueryContext(ctx, typeStatsQuery, args...)
	if err != nil {
		s.logger.Error("查询按类型分组的操作统计失败", zap.Error(err))
	} else {
		defer rows.Close()
		for rows.Next() {
			var operationType string
			var count int64
			var totalAmount decimal.Decimal
			if err := rows.Scan(&operationType, &count, &totalAmount); err != nil {
				s.logger.Error("扫描类型统计数据失败", zap.Error(err))
				continue
			}
			stats.OperationsByType[operationType] = count
			stats.AmountByType[operationType] = totalAmount
		}
	}

	// 查询按管理员分组的统计
	if req.AdminID == "" {
		adminStatsQuery := fmt.Sprintf(`
			SELECT admin_id, COUNT(*) as count
			FROM admin_balance_audit_logs_enhanced %s
			GROUP BY admin_id
		`, whereClause)

		rows, err := s.db.QueryContext(ctx, adminStatsQuery, args...)
		if err != nil {
			s.logger.Error("查询按管理员分组的操作统计失败", zap.Error(err))
		} else {
			defer rows.Close()
			for rows.Next() {
				var adminID string
				var count int64
				if err := rows.Scan(&adminID, &count); err != nil {
					s.logger.Error("扫描管理员统计数据失败", zap.Error(err))
					continue
				}
				stats.OperationsByAdmin[adminID] = count
			}
		}
	}

	return &stats, nil
}

// DetectAnomalousOperations 检测异常操作
func (s *DefaultEnhancedAuditService) DetectAnomalousOperations(ctx context.Context, adminID string, duration time.Duration) ([]*AnomalousOperation, error) {
	// 获取配置的阈值
	largeAmountThreshold := s.configService.GetConfigWithDefault("admin_balance.large_amount_threshold", "1000.00")
	maxOperationsPerMin := s.configService.GetConfigAsIntWithDefault("admin_balance.max_operations_per_minute", 10)

	largeAmountDecimal, _ := decimal.NewFromString(largeAmountThreshold)

	var anomalies []*AnomalousOperation

	// 检测大额操作异常
	largeAmountQuery := `
		SELECT operation_type, COUNT(*) as count, SUM(amount) as total_amount, AVG(amount) as avg_amount
		FROM admin_balance_audit_logs_enhanced
		WHERE admin_id = $1 AND created_at >= NOW() - INTERVAL '%d minutes'
			AND amount >= $2 AND result = 'success'
		GROUP BY operation_type
	`

	minutes := int(duration.Minutes())
	rows, err := s.db.QueryContext(ctx, fmt.Sprintf(largeAmountQuery, minutes), adminID, largeAmountDecimal)
	if err != nil {
		s.logger.Error("查询大额操作异常失败", zap.Error(err))
	} else {
		defer rows.Close()
		for rows.Next() {
			var operationType string
			var count int64
			var totalAmount, avgAmount decimal.Decimal

			if err := rows.Scan(&operationType, &count, &totalAmount, &avgAmount); err != nil {
				s.logger.Error("扫描大额操作数据失败", zap.Error(err))
				continue
			}

			severity := "medium"
			if totalAmount.GreaterThan(largeAmountDecimal.Mul(decimal.NewFromInt(10))) {
				severity = "high"
			}
			if totalAmount.GreaterThan(largeAmountDecimal.Mul(decimal.NewFromInt(50))) {
				severity = "critical"
			}

			anomalies = append(anomalies, &AnomalousOperation{
				AdminID:       adminID,
				OperationType: operationType,
				Count:         count,
				TotalAmount:   totalAmount,
				AverageAmount: avgAmount,
				Reason:        fmt.Sprintf("检测到大额操作，总金额: %s", totalAmount.String()),
				Severity:      severity,
			})
		}
	}

	// 检测高频操作异常
	highFreqQuery := `
		SELECT operation_type, COUNT(*) as count
		FROM admin_balance_audit_logs_enhanced
		WHERE admin_id = $1 AND created_at >= NOW() - INTERVAL '1 minute'
		GROUP BY operation_type
		HAVING COUNT(*) > $2
	`

	rows, err = s.db.QueryContext(ctx, highFreqQuery, adminID, maxOperationsPerMin)
	if err != nil {
		s.logger.Error("查询高频操作异常失败", zap.Error(err))
	} else {
		defer rows.Close()
		for rows.Next() {
			var operationType string
			var count int64

			if err := rows.Scan(&operationType, &count); err != nil {
				s.logger.Error("扫描高频操作数据失败", zap.Error(err))
				continue
			}

			severity := "medium"
			if count > int64(maxOperationsPerMin*2) {
				severity = "high"
			}
			if count > int64(maxOperationsPerMin*5) {
				severity = "critical"
			}

			anomalies = append(anomalies, &AnomalousOperation{
				AdminID:       adminID,
				OperationType: operationType,
				Count:         count,
				TotalAmount:   decimal.Zero,
				AverageAmount: decimal.Zero,
				Reason:        fmt.Sprintf("检测到高频操作，1分钟内操作%d次", count),
				Severity:      severity,
			})
		}
	}

	return anomalies, nil
}
