package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
)

// 辅助函数：将字符串转换为字符串指针
func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// SystemConfigService 系统配置服务接口
type SystemConfigService interface {
	// 配置管理
	CreateConfig(ctx context.Context, req *model.SystemConfigRequest, operatorID string) (*model.SystemConfig, error)
	GetConfigByID(ctx context.Context, id string) (*model.SystemConfig, error)
	GetConfigByKey(ctx context.Context, group, key string) (*model.SystemConfig, error)
	UpdateConfig(ctx context.Context, id string, req *model.SystemConfigRequest, operatorID string) (*model.SystemConfig, error)
	DeleteConfig(ctx context.Context, id string, operatorID string) error
	ListConfigs(ctx context.Context, req *model.SystemConfigListRequest) (*model.SystemConfigListResponse, error)
	GetConfigsByGroup(ctx context.Context, group string) ([]*model.SystemConfig, error)
	BatchUpdateConfigs(ctx context.Context, configs []*model.SystemConfig, operatorID string) error

	// 配置值获取（带缓存）
	GetStringConfig(ctx context.Context, group, key string, defaultValue string) (string, error)
	GetIntConfig(ctx context.Context, group, key string, defaultValue int) (int, error)
	GetFloatConfig(ctx context.Context, group, key string, defaultValue float64) (float64, error)
	GetBoolConfig(ctx context.Context, group, key string, defaultValue bool) (bool, error)
	GetJSONConfig(ctx context.Context, group, key string, result interface{}) error

	// 配置变更日志
	ListChangeLogs(ctx context.Context, req *model.ConfigChangeLogListRequest) (*model.ConfigChangeLogListResponse, error)

	// 配置模板
	CreateTemplate(ctx context.Context, template *model.ConfigTemplate, operatorID string) (*model.ConfigTemplate, error)
	GetTemplateByName(ctx context.Context, name string) (*model.ConfigTemplate, error)
	ListTemplates(ctx context.Context) ([]*model.ConfigTemplate, error)
	UpdateTemplate(ctx context.Context, id string, template *model.ConfigTemplate, operatorID string) (*model.ConfigTemplate, error)
	DeleteTemplate(ctx context.Context, id string, operatorID string) error
	ApplyTemplate(ctx context.Context, templateName string, operatorID string) error

	// 配置备份
	CreateBackup(ctx context.Context, backupName, description string, operatorID string) (*model.ConfigBackup, error)
	GetBackupByID(ctx context.Context, id string) (*model.ConfigBackup, error)
	ListBackups(ctx context.Context, page, pageSize int) ([]*model.ConfigBackup, int64, error)
	RestoreFromBackup(ctx context.Context, backupID string, operatorID string) error
	DeleteBackup(ctx context.Context, id string, operatorID string) error

	// 工具方法
	GetConfigGroups(ctx context.Context) ([]string, error)
	RefreshCache() error
	ClearCache()
	ValidateConfig(ctx context.Context, config *model.SystemConfig) error

	// 兼容旧接口的方法
	GetConfig(key string) (string, error)
	GetConfigWithDefault(key, defaultValue string) string
	GetConfigAsInt(key string) (int, error)
	GetConfigAsIntWithDefault(key string, defaultValue int) int
	GetConfigAsBool(key string) (bool, error)
	GetConfigAsBoolWithDefault(key string, defaultValue bool) bool
	GetConfigAsJSON(key string, result interface{}) error

	// 计费状态相关
	GetValidBillingStatuses() ([]string, error)
	IsBillingStatusValid(status string) bool

	// 搜索字段相关
	GetValidSearchFields(userType string) ([]string, error)
	IsSearchFieldValid(field, userType string) bool
	GetDefaultSearchFields() ([]string, error)

	// 排序字段相关
	GetValidSortFields(userType string) (map[string]string, error)
	IsSortFieldValid(field, userType string) bool
	GetDefaultSortField() string
	GetDefaultSortOrder() string

	// 状态转换相关
	GetValidStatusTransitions(fromStatus, userType string) ([]string, error)
	IsStatusTransitionValid(fromStatus, toStatus, userType string) bool

	// 操作权限相关
	GetOperationPermissions(status, userType string) (map[string]bool, error)
	IsOperationAllowed(status, operation, userType string) bool

	// 批量操作限制
	GetBatchOperationMaxItems() int
	GetExportMaxItems() int
}

// DefaultSystemConfigService 默认系统配置服务实现
type DefaultSystemConfigService struct {
	repository repository.SystemConfigRepository
	logger     *zap.Logger
	cacheTTL   time.Duration
}

// NewDefaultSystemConfigService 创建默认系统配置服务
func NewDefaultSystemConfigService(
	repository repository.SystemConfigRepository,
	logger *zap.Logger,
) SystemConfigService {
	return &DefaultSystemConfigService{
		repository: repository,
		logger:     logger,
		cacheTTL:   0, // 禁用缓存，实现实时配置生效
	}
}

// CreateConfig 创建配置
func (s *DefaultSystemConfigService) CreateConfig(ctx context.Context, req *model.SystemConfigRequest, operatorID string) (*model.SystemConfig, error) {
	// 验证配置键是否唯一
	if err := s.repository.ValidateConfigKey(ctx, req.ConfigGroup, req.ConfigKey, ""); err != nil {
		return nil, fmt.Errorf("配置键验证失败: %w", err)
	}

	// 创建配置对象
	config := &model.SystemConfig{
		ConfigGroup:    req.ConfigGroup,
		ConfigKey:      req.ConfigKey,
		ConfigValue:    req.ConfigValue,
		ConfigType:     req.ConfigType,
		Description:    req.Description,
		ValidationRule: stringPtr(req.ValidationRule),
		DefaultValue:   stringPtr(req.DefaultValue),
		DisplayOrder:   req.DisplayOrder,
		CreatedBy:      stringPtr(operatorID),
		UpdatedBy:      stringPtr(operatorID),
	}

	// 验证配置值
	if err := s.ValidateConfig(ctx, config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 创建配置
	if err := s.repository.CreateConfig(ctx, config); err != nil {
		s.logger.Error("创建配置失败", zap.Error(err), zap.String("operator", operatorID))
		return nil, fmt.Errorf("创建配置失败: %w", err)
	}

	// 记录变更日志
	changeLog := &model.ConfigChangeLog{
		ConfigID:     config.ID,
		ConfigGroup:  config.ConfigGroup,
		ConfigKey:    config.ConfigKey,
		NewValue:     config.ConfigValue,
		ChangeType:   model.ChangeTypeCreate,
		ChangedBy:    operatorID,
		ChangeReason: req.ChangeReason,
	}

	if err := s.repository.CreateChangeLog(ctx, changeLog); err != nil {
		s.logger.Warn("记录配置变更日志失败", zap.Error(err))
	}

	// 清除缓存
	s.clearCache()

	s.logger.Info("配置创建成功",
		zap.String("config_group", config.ConfigGroup),
		zap.String("config_key", config.ConfigKey),
		zap.String("operator", operatorID))

	return config, nil
}

// GetConfigByID 根据ID获取配置
func (s *DefaultSystemConfigService) GetConfigByID(ctx context.Context, id string) (*model.SystemConfig, error) {
	config, err := s.repository.GetConfigByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}
	return config, nil
}

// GetConfigByKey 根据组和键获取配置
func (s *DefaultSystemConfigService) GetConfigByKey(ctx context.Context, group, key string) (*model.SystemConfig, error) {
	// 直接从数据库获取，不使用缓存
	config, err := s.repository.GetConfigByKey(ctx, group, key)
	if err != nil {
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}

	return config, nil
}

// ListConfigs 获取配置列表
func (s *DefaultSystemConfigService) ListConfigs(ctx context.Context, req *model.SystemConfigListRequest) (*model.SystemConfigListResponse, error) {
	return s.repository.ListConfigs(ctx, req)
}

// GetConfigsByGroup 根据组获取配置列表
func (s *DefaultSystemConfigService) GetConfigsByGroup(ctx context.Context, group string) ([]*model.SystemConfig, error) {
	return s.repository.GetConfigsByGroup(ctx, group)
}

// GetStringConfig 获取字符串配置
func (s *DefaultSystemConfigService) GetStringConfig(ctx context.Context, group, key string, defaultValue string) (string, error) {
	config, err := s.GetConfigByKey(ctx, group, key)
	if err != nil {
		s.logger.Warn("获取配置失败，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.String("default", defaultValue),
			zap.Error(err))
		return defaultValue, nil
	}

	if config.ConfigType != model.ConfigTypeString {
		return defaultValue, fmt.Errorf("配置类型不匹配，期望string，实际%s", config.ConfigType)
	}

	return config.ConfigValue, nil
}

// GetIntConfig 获取整数配置
func (s *DefaultSystemConfigService) GetIntConfig(ctx context.Context, group, key string, defaultValue int) (int, error) {
	config, err := s.GetConfigByKey(ctx, group, key)
	if err != nil {
		s.logger.Warn("获取配置失败，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.Int("default", defaultValue),
			zap.Error(err))
		return defaultValue, nil
	}

	if config.ConfigType != model.ConfigTypeInteger {
		return defaultValue, fmt.Errorf("配置类型不匹配，期望integer，实际%s", config.ConfigType)
	}

	value, err := strconv.Atoi(config.ConfigValue)
	if err != nil {
		s.logger.Warn("配置值转换失败，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.String("value", config.ConfigValue),
			zap.Int("default", defaultValue),
			zap.Error(err))
		return defaultValue, nil
	}

	return value, nil
}

// GetIntConfigStrict 获取整数配置（严格模式，无默认值）
// 用于关键配置如抛比等，确保从数据库获取
func (s *DefaultSystemConfigService) GetIntConfigStrict(ctx context.Context, group, key string) (int, error) {
	config, err := s.GetConfigByKey(ctx, group, key)
	if err != nil {
		s.logger.Error("获取关键配置失败",
			zap.String("group", group),
			zap.String("key", key),
			zap.Error(err))
		return 0, fmt.Errorf("关键配置 %s.%s 获取失败: %w。请在数据库 system_configs 表中配置该项", group, key, err)
	}

	if config.ConfigType != model.ConfigTypeInteger {
		return 0, fmt.Errorf("配置类型不匹配，期望integer，实际%s。请检查数据库中 %s.%s 的配置类型", config.ConfigType, group, key)
	}

	value, err := strconv.Atoi(config.ConfigValue)
	if err != nil {
		s.logger.Error("关键配置值转换失败",
			zap.String("group", group),
			zap.String("key", key),
			zap.String("value", config.ConfigValue),
			zap.Error(err))
		return 0, fmt.Errorf("配置值转换失败: %w。请检查数据库中 %s.%s 的配置值格式", err, group, key)
	}

	return value, nil
}

// GetFloatConfig 获取浮点数配置
func (s *DefaultSystemConfigService) GetFloatConfig(ctx context.Context, group, key string, defaultValue float64) (float64, error) {
	config, err := s.GetConfigByKey(ctx, group, key)
	if err != nil {
		s.logger.Warn("获取配置失败，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.Float64("default", defaultValue),
			zap.Error(err))
		return defaultValue, nil
	}

	if config.ConfigType != model.ConfigTypeFloat {
		return defaultValue, fmt.Errorf("配置类型不匹配，期望float，实际%s", config.ConfigType)
	}

	value, err := strconv.ParseFloat(config.ConfigValue, 64)
	if err != nil {
		s.logger.Warn("配置值转换失败，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.String("value", config.ConfigValue),
			zap.Float64("default", defaultValue),
			zap.Error(err))
		return defaultValue, nil
	}

	return value, nil
}

// GetBoolConfig 获取布尔配置
func (s *DefaultSystemConfigService) GetBoolConfig(ctx context.Context, group, key string, defaultValue bool) (bool, error) {
	config, err := s.GetConfigByKey(ctx, group, key)
	if err != nil {
		s.logger.Warn("获取配置失败，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.Bool("default", defaultValue),
			zap.Error(err))
		return defaultValue, nil
	}

	if config.ConfigType != model.ConfigTypeBoolean {
		return defaultValue, fmt.Errorf("配置类型不匹配，期望boolean，实际%s", config.ConfigType)
	}

	value, err := strconv.ParseBool(config.ConfigValue)
	if err != nil {
		s.logger.Warn("配置值转换失败，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.String("value", config.ConfigValue),
			zap.Bool("default", defaultValue),
			zap.Error(err))
		return defaultValue, nil
	}

	return value, nil
}

// GetJSONConfig 获取JSON配置
func (s *DefaultSystemConfigService) GetJSONConfig(ctx context.Context, group, key string, result interface{}) error {
	config, err := s.GetConfigByKey(ctx, group, key)
	if err != nil {
		return fmt.Errorf("获取配置失败: %w", err)
	}

	if config.ConfigType != model.ConfigTypeJSON && config.ConfigType != model.ConfigTypeArray {
		return fmt.Errorf("配置类型不匹配，期望json/array，实际%s", config.ConfigType)
	}

	if err := json.Unmarshal([]byte(config.ConfigValue), result); err != nil {
		return fmt.Errorf("JSON解析失败: %w", err)
	}

	return nil
}

// ValidateConfig 验证配置
func (s *DefaultSystemConfigService) ValidateConfig(ctx context.Context, config *model.SystemConfig) error {
	return config.ValidateValue()
}

// 缓存相关的私有方法

func (s *DefaultSystemConfigService) clearCache() {
	// 禁用缓存，什么都不做
}

// UpdateConfig 更新配置
func (s *DefaultSystemConfigService) UpdateConfig(ctx context.Context, id string, req *model.SystemConfigRequest, operatorID string) (*model.SystemConfig, error) {
	// 获取原配置
	oldConfig, err := s.repository.GetConfigByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("获取原配置失败: %w", err)
	}

	// 检查是否为系统配置
	if oldConfig.IsSystem && oldConfig.IsReadonly {
		return nil, fmt.Errorf("系统只读配置不可修改")
	}

	// 更新配置对象
	config := &model.SystemConfig{
		ID:             id,
		ConfigGroup:    oldConfig.ConfigGroup, // 组不允许修改
		ConfigKey:      oldConfig.ConfigKey,   // 键不允许修改
		ConfigValue:    req.ConfigValue,
		ConfigType:     req.ConfigType,
		Description:    req.Description,
		IsEncrypted:    oldConfig.IsEncrypted, // 加密标志不允许修改
		IsReadonly:     oldConfig.IsReadonly,  // 只读标志不允许修改
		IsSystem:       oldConfig.IsSystem,    // 系统标志不允许修改
		ValidationRule: stringPtr(req.ValidationRule),
		DefaultValue:   stringPtr(req.DefaultValue),
		DisplayOrder:   req.DisplayOrder,
		UpdatedBy:      stringPtr(operatorID),
	}

	// 验证配置值
	if err := s.ValidateConfig(ctx, config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 更新配置
	if err := s.repository.UpdateConfig(ctx, config); err != nil {
		s.logger.Error("更新配置失败", zap.Error(err), zap.String("operator", operatorID))
		return nil, fmt.Errorf("更新配置失败: %w", err)
	}

	// 记录变更日志
	changeLog := &model.ConfigChangeLog{
		ConfigID:     config.ID,
		ConfigGroup:  config.ConfigGroup,
		ConfigKey:    config.ConfigKey,
		OldValue:     oldConfig.ConfigValue,
		NewValue:     config.ConfigValue,
		ChangeType:   model.ChangeTypeUpdate,
		ChangedBy:    operatorID,
		ChangeReason: req.ChangeReason,
	}

	if err := s.repository.CreateChangeLog(ctx, changeLog); err != nil {
		s.logger.Warn("记录配置变更日志失败", zap.Error(err))
	}

	// 清除缓存
	s.clearCache()

	s.logger.Info("配置更新成功",
		zap.String("config_group", config.ConfigGroup),
		zap.String("config_key", config.ConfigKey),
		zap.String("old_value", oldConfig.ConfigValue),
		zap.String("new_value", config.ConfigValue),
		zap.String("operator", operatorID))

	return config, nil
}

// DeleteConfig 删除配置
func (s *DefaultSystemConfigService) DeleteConfig(ctx context.Context, id string, operatorID string) error {
	// 获取配置信息用于日志
	config, err := s.repository.GetConfigByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取配置失败: %w", err)
	}

	// 删除配置
	if err := s.repository.DeleteConfig(ctx, id); err != nil {
		s.logger.Error("删除配置失败", zap.Error(err), zap.String("operator", operatorID))
		return fmt.Errorf("删除配置失败: %w", err)
	}

	// 记录变更日志
	changeLog := &model.ConfigChangeLog{
		ConfigID:    config.ID,
		ConfigGroup: config.ConfigGroup,
		ConfigKey:   config.ConfigKey,
		OldValue:    config.ConfigValue,
		ChangeType:  model.ChangeTypeDelete,
		ChangedBy:   operatorID,
	}

	if err := s.repository.CreateChangeLog(ctx, changeLog); err != nil {
		s.logger.Warn("记录配置变更日志失败", zap.Error(err))
	}

	// 清除缓存
	s.clearCache()

	s.logger.Info("配置删除成功",
		zap.String("config_group", config.ConfigGroup),
		zap.String("config_key", config.ConfigKey),
		zap.String("operator", operatorID))

	return nil
}

// BatchUpdateConfigs 批量更新配置
func (s *DefaultSystemConfigService) BatchUpdateConfigs(ctx context.Context, configs []*model.SystemConfig, operatorID string) error {
	// 验证所有配置
	for _, config := range configs {
		if err := s.ValidateConfig(ctx, config); err != nil {
			return fmt.Errorf("配置验证失败 [%s.%s]: %w", config.ConfigGroup, config.ConfigKey, err)
		}
	}

	// 批量更新
	if err := s.repository.BatchUpdateConfigs(ctx, configs, operatorID); err != nil {
		s.logger.Error("批量更新配置失败", zap.Error(err), zap.String("operator", operatorID))
		return fmt.Errorf("批量更新配置失败: %w", err)
	}

	// 清除缓存
	s.clearCache()

	s.logger.Info("批量更新配置成功",
		zap.Int("count", len(configs)),
		zap.String("operator", operatorID))

	return nil
}

// ListChangeLogs 获取配置变更日志列表
func (s *DefaultSystemConfigService) ListChangeLogs(ctx context.Context, req *model.ConfigChangeLogListRequest) (*model.ConfigChangeLogListResponse, error) {
	return s.repository.ListChangeLogs(ctx, req)
}

// GetConfigGroups 获取配置组列表
func (s *DefaultSystemConfigService) GetConfigGroups(ctx context.Context) ([]string, error) {
	return s.repository.GetConfigGroups(ctx)
}

// RefreshCache 刷新缓存
func (s *DefaultSystemConfigService) RefreshCache() error {
	s.clearCache()
	s.logger.Info("配置缓存已刷新")
	return nil
}

// ClearCache 清除缓存
func (s *DefaultSystemConfigService) ClearCache() {
	s.clearCache()
}

// CreateTemplate 创建配置模板
func (s *DefaultSystemConfigService) CreateTemplate(ctx context.Context, template *model.ConfigTemplate, operatorID string) (*model.ConfigTemplate, error) {
	template.CreatedBy = operatorID
	template.UpdatedBy = operatorID

	if err := s.repository.CreateTemplate(ctx, template); err != nil {
		s.logger.Error("创建配置模板失败", zap.Error(err), zap.String("operator", operatorID))
		return nil, fmt.Errorf("创建配置模板失败: %w", err)
	}

	s.logger.Info("配置模板创建成功",
		zap.String("template_name", template.TemplateName),
		zap.String("operator", operatorID))

	return template, nil
}

// GetTemplateByName 根据名称获取配置模板
func (s *DefaultSystemConfigService) GetTemplateByName(ctx context.Context, name string) (*model.ConfigTemplate, error) {
	return s.repository.GetTemplateByName(ctx, name)
}

// ListTemplates 获取配置模板列表
func (s *DefaultSystemConfigService) ListTemplates(ctx context.Context) ([]*model.ConfigTemplate, error) {
	return s.repository.ListTemplates(ctx)
}

// UpdateTemplate 更新配置模板
func (s *DefaultSystemConfigService) UpdateTemplate(ctx context.Context, id string, template *model.ConfigTemplate, operatorID string) (*model.ConfigTemplate, error) {
	template.ID = id
	template.UpdatedBy = operatorID

	if err := s.repository.UpdateTemplate(ctx, template); err != nil {
		s.logger.Error("更新配置模板失败", zap.Error(err), zap.String("operator", operatorID))
		return nil, fmt.Errorf("更新配置模板失败: %w", err)
	}

	s.logger.Info("配置模板更新成功",
		zap.String("template_name", template.TemplateName),
		zap.String("operator", operatorID))

	return template, nil
}

// DeleteTemplate 删除配置模板
func (s *DefaultSystemConfigService) DeleteTemplate(ctx context.Context, id string, operatorID string) error {
	if err := s.repository.DeleteTemplate(ctx, id); err != nil {
		s.logger.Error("删除配置模板失败", zap.Error(err), zap.String("operator", operatorID))
		return fmt.Errorf("删除配置模板失败: %w", err)
	}

	s.logger.Info("配置模板删除成功",
		zap.String("template_id", id),
		zap.String("operator", operatorID))

	return nil
}

// ApplyTemplate 应用配置模板
func (s *DefaultSystemConfigService) ApplyTemplate(ctx context.Context, templateName string, operatorID string) error {
	// TODO: 实现模板应用逻辑
	return fmt.Errorf("模板应用功能暂未实现")
}

// CreateBackup 创建配置备份
func (s *DefaultSystemConfigService) CreateBackup(ctx context.Context, backupName, description string, operatorID string) (*model.ConfigBackup, error) {
	// TODO: 实现备份创建逻辑
	return nil, fmt.Errorf("配置备份功能暂未实现")
}

// GetBackupByID 根据ID获取配置备份
func (s *DefaultSystemConfigService) GetBackupByID(ctx context.Context, id string) (*model.ConfigBackup, error) {
	return s.repository.GetBackupByID(ctx, id)
}

// ListBackups 获取配置备份列表
func (s *DefaultSystemConfigService) ListBackups(ctx context.Context, page, pageSize int) ([]*model.ConfigBackup, int64, error) {
	return s.repository.ListBackups(ctx, page, pageSize)
}

// RestoreFromBackup 从备份恢复配置
func (s *DefaultSystemConfigService) RestoreFromBackup(ctx context.Context, backupID string, operatorID string) error {
	// TODO: 实现备份恢复逻辑
	return fmt.Errorf("配置恢复功能暂未实现")
}

// DeleteBackup 删除配置备份
func (s *DefaultSystemConfigService) DeleteBackup(ctx context.Context, id string, operatorID string) error {
	if err := s.repository.DeleteBackup(ctx, id); err != nil {
		s.logger.Error("删除配置备份失败", zap.Error(err), zap.String("operator", operatorID))
		return fmt.Errorf("删除配置备份失败: %w", err)
	}

	s.logger.Info("配置备份删除成功",
		zap.String("backup_id", id),
		zap.String("operator", operatorID))

	return nil
}

// 兼容旧接口的方法实现
// GetConfig 获取配置值（兼容旧接口）
func (s *DefaultSystemConfigService) GetConfig(key string) (string, error) {
	// 假设key格式为 "group.key"
	parts := strings.Split(key, ".")
	if len(parts) != 2 {
		return "", fmt.Errorf("配置键格式错误，应为 group.key: %s", key)
	}

	ctx := context.Background()
	return s.GetStringConfig(ctx, parts[0], parts[1], "")
}

// GetConfigWithDefault 获取配置值，如果不存在则返回默认值（兼容旧接口）
func (s *DefaultSystemConfigService) GetConfigWithDefault(key, defaultValue string) string {
	value, err := s.GetConfig(key)
	if err != nil {
		return defaultValue
	}
	return value
}

// GetConfigAsInt 获取整数配置值（兼容旧接口）
func (s *DefaultSystemConfigService) GetConfigAsInt(key string) (int, error) {
	parts := strings.Split(key, ".")
	if len(parts) != 2 {
		return 0, fmt.Errorf("配置键格式错误，应为 group.key: %s", key)
	}

	ctx := context.Background()
	return s.GetIntConfig(ctx, parts[0], parts[1], 0)
}

// GetConfigAsIntWithDefault 获取整数配置值，如果不存在或无效则返回默认值（兼容旧接口）
func (s *DefaultSystemConfigService) GetConfigAsIntWithDefault(key string, defaultValue int) int {
	value, err := s.GetConfigAsInt(key)
	if err != nil {
		return defaultValue
	}
	return value
}

// GetConfigAsBool 获取布尔配置值（兼容旧接口）
func (s *DefaultSystemConfigService) GetConfigAsBool(key string) (bool, error) {
	parts := strings.Split(key, ".")
	if len(parts) != 2 {
		return false, fmt.Errorf("配置键格式错误，应为 group.key: %s", key)
	}

	ctx := context.Background()
	return s.GetBoolConfig(ctx, parts[0], parts[1], false)
}

// GetConfigAsBoolWithDefault 获取布尔配置值，如果不存在或无效则返回默认值（兼容旧接口）
func (s *DefaultSystemConfigService) GetConfigAsBoolWithDefault(key string, defaultValue bool) bool {
	value, err := s.GetConfigAsBool(key)
	if err != nil {
		return defaultValue
	}
	return value
}

// GetConfigAsJSON 获取JSON配置值并解析到指定结构（兼容旧接口）
func (s *DefaultSystemConfigService) GetConfigAsJSON(key string, result interface{}) error {
	parts := strings.Split(key, ".")
	if len(parts) != 2 {
		return fmt.Errorf("配置键格式错误，应为 group.key: %s", key)
	}

	ctx := context.Background()
	return s.GetJSONConfig(ctx, parts[0], parts[1], result)
}

// 计费状态相关方法
// GetValidBillingStatuses 获取有效的计费状态列表
func (s *DefaultSystemConfigService) GetValidBillingStatuses() ([]string, error) {
	// 返回默认的计费状态
	return []string{"pending", "confirmed"}, nil // 已删除settled
}

// IsBillingStatusValid 检查计费状态是否有效
func (s *DefaultSystemConfigService) IsBillingStatusValid(status string) bool {
	validStatuses, _ := s.GetValidBillingStatuses()
	for _, validStatus := range validStatuses {
		if validStatus == status {
			return true
		}
	}
	return false
}

// 搜索字段相关方法
// GetValidSearchFields 获取有效的搜索字段列表
func (s *DefaultSystemConfigService) GetValidSearchFields(userType string) ([]string, error) {
	// 根据用户类型返回不同的搜索字段
	switch userType {
	case "admin":
		return []string{
			"customer_order_no", "order_no", "tracking_no", "user_id",
			"username", "user_email", "express_type", "provider",
		}, nil
	default:
		return []string{"customer_order_no", "order_no", "tracking_no"}, nil
	}
}

// IsSearchFieldValid 检查搜索字段是否有效
func (s *DefaultSystemConfigService) IsSearchFieldValid(field, userType string) bool {
	fields, err := s.GetValidSearchFields(userType)
	if err != nil {
		return false
	}

	for _, validField := range fields {
		if validField == field {
			return true
		}
	}
	return false
}

// GetDefaultSearchFields 获取默认搜索字段
func (s *DefaultSystemConfigService) GetDefaultSearchFields() ([]string, error) {
	ctx := context.Background()
	var fields []string
	if err := s.GetJSONConfig(ctx, "system", "default_search_fields", &fields); err != nil {
		return []string{"customer_order_no", "order_no", "tracking_no"}, nil
	}
	return fields, nil
}

// 排序字段相关方法
// GetValidSortFields 获取有效的排序字段映射
func (s *DefaultSystemConfigService) GetValidSortFields(userType string) (map[string]string, error) {
	// 根据用户类型返回不同的排序字段
	switch userType {
	case "admin":
		return map[string]string{
			"created_at":   "order_records.created_at",
			"updated_at":   "order_records.updated_at",
			"order_no":     "order_records.order_no",
			"tracking_no":  "order_records.tracking_no",
			"status":       "order_records.status",
			"express_type": "order_records.express_type",
			"provider":     "order_records.provider",
			"price":        "order_records.price",
			"weight":       "order_records.weight",
			"user_id":      "order_records.user_id",
		}, nil
	default:
		return map[string]string{
			"created_at":   "order_records.created_at",
			"updated_at":   "order_records.updated_at",
			"order_no":     "order_records.order_no",
			"tracking_no":  "order_records.tracking_no",
			"status":       "order_records.status",
			"express_type": "order_records.express_type",
			"price":        "order_records.price",
		}, nil
	}
}

// IsSortFieldValid 检查排序字段是否有效
func (s *DefaultSystemConfigService) IsSortFieldValid(field, userType string) bool {
	fields, err := s.GetValidSortFields(userType)
	if err != nil {
		return false
	}

	_, exists := fields[field]
	return exists
}

// GetDefaultSortField 获取默认排序字段
func (s *DefaultSystemConfigService) GetDefaultSortField() string {
	ctx := context.Background()
	value, _ := s.GetStringConfig(ctx, "system", "default_sort_field", "created_at")
	return value
}

// GetDefaultSortOrder 获取默认排序方向
func (s *DefaultSystemConfigService) GetDefaultSortOrder() string {
	ctx := context.Background()
	value, _ := s.GetStringConfig(ctx, "system", "default_sort_order", "desc")
	return value
}

// 状态转换相关方法
// GetValidStatusTransitions 获取有效的状态转换列表
func (s *DefaultSystemConfigService) GetValidStatusTransitions(fromStatus, userType string) ([]string, error) {
	// 使用硬编码的状态转换规则
	transitions := map[string][]string{
		"submitted":          {"assigned", "submit_failed", "print_failed", "cancelled"},
		"assigned":           {"awaiting_pickup", "cancelled"},
		"awaiting_pickup":    {"picked_up", "pickup_failed", "cancelled"},
		"picked_up":          {"in_transit", "billed", "exception", "cancelled"},
		"in_transit":         {"out_for_delivery", "delivered", "exception", "returned", "forwarded"},
		"out_for_delivery":   {"delivered", "delivered_abnormal", "exception", "returned"},
		"delivered":          {"billed"},
		"delivered_abnormal": {"billed"},
		"exception":          {"in_transit", "returned", "cancelled"},
		"pickup_failed":      {"awaiting_pickup", "cancelled"},
		"returned":           {"cancelled"},
		"cancelled":          {"revived"},
	}

	if validTransitions, exists := transitions[fromStatus]; exists {
		return validTransitions, nil
	}

	return []string{}, nil
}

// IsStatusTransitionValid 检查状态转换是否有效
func (s *DefaultSystemConfigService) IsStatusTransitionValid(fromStatus, toStatus, userType string) bool {
	transitions, err := s.GetValidStatusTransitions(fromStatus, userType)
	if err != nil {
		return false
	}

	for _, validTransition := range transitions {
		if validTransition == toStatus {
			return true
		}
	}
	return false
}

// 操作权限相关方法
// GetOperationPermissions 获取操作权限
func (s *DefaultSystemConfigService) GetOperationPermissions(status, userType string) (map[string]bool, error) {
	// 根据状态和用户类型返回操作权限
	permissions := map[string]bool{
		"can_cancel":        false,
		"can_update_status": false,
		"can_refund":        false,
		"can_export":        false,
		"can_view_detail":   true,
	}

	// 管理员有更多权限
	if userType == "admin" {
		permissions["can_export"] = true
		permissions["can_update_status"] = true

		switch status {
		case "submitted", "assigned", "awaiting_pickup":
			permissions["can_cancel"] = true
		case "picked_up", "in_transit":
			permissions["can_update_status"] = true
		case "delivered", "delivered_abnormal":
			permissions["can_refund"] = true
		case "exception":
			permissions["can_cancel"] = true
			permissions["can_refund"] = true
		}
	}

	return permissions, nil
}

// IsOperationAllowed 检查操作是否被允许
func (s *DefaultSystemConfigService) IsOperationAllowed(status, operation, userType string) bool {
	permissions, err := s.GetOperationPermissions(status, userType)
	if err != nil {
		return false
	}

	allowed, exists := permissions[operation]
	return exists && allowed
}

// 批量操作限制方法
// GetBatchOperationMaxItems 获取批量操作最大项目数
func (s *DefaultSystemConfigService) GetBatchOperationMaxItems() int {
	ctx := context.Background()
	value, _ := s.GetIntConfig(ctx, "system", "batch_operation_max_items", 100)
	return value
}

// GetExportMaxItems 获取导出最大项目数
func (s *DefaultSystemConfigService) GetExportMaxItems() int {
	ctx := context.Background()
	value, _ := s.GetIntConfig(ctx, "system", "export_max_items", 10000)
	return value
}
