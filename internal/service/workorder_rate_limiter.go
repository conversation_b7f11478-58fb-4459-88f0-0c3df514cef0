package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// WorkOrderRateLimiterConfig 限流器配置
type WorkOrderRateLimiterConfig struct {
	// 每秒令牌数
	TokensPerSecond float64
	// 桶容量
	BucketCapacity int
	// 用户级限流（每个用户独立限流）
	PerUserLimit bool
	// 全局限流配置
	GlobalTokensPerSecond float64
	GlobalBucketCapacity  int
	// 禁用限流（当为 true 时，AllowRequest 永远放行）
	Disabled bool
}

// TokenBucket 令牌桶
type TokenBucket struct {
	capacity        int
	tokens          float64
	tokensPerSecond float64
	lastRefillTime  time.Time
	mu              sync.Mutex
}

// WorkOrderRateLimiter 工单回调限流器
type WorkOrderRateLimiter struct {
	config       WorkOrderRateLimiterConfig
	userBuckets  map[string]*TokenBucket
	globalBucket *TokenBucket
	mu           sync.RWMutex
	logger       *zap.Logger
}

// NewWorkOrderRateLimiter 创建工单回调限流器
func NewWorkOrderRateLimiter(config WorkOrderRateLimiterConfig, logger *zap.Logger) *WorkOrderRateLimiter {
	// 设置默认值
	if config.TokensPerSecond <= 0 {
		config.TokensPerSecond = 10.0 // 默认每秒10个令牌
	}
	if config.BucketCapacity <= 0 {
		config.BucketCapacity = 100 // 默认桶容量100
	}
	if config.GlobalTokensPerSecond <= 0 {
		config.GlobalTokensPerSecond = 100.0 // 默认全局每秒100个令牌
	}
	if config.GlobalBucketCapacity <= 0 {
		config.GlobalBucketCapacity = 1000 // 默认全局桶容量1000
	}

	limiter := &WorkOrderRateLimiter{
		config:      config,
		userBuckets: make(map[string]*TokenBucket),
		logger:      logger,
	}

	// 创建全局令牌桶
	limiter.globalBucket = newTokenBucket(
		config.GlobalBucketCapacity,
		config.GlobalTokensPerSecond,
	)

	return limiter
}

// newTokenBucket 创建令牌桶
func newTokenBucket(capacity int, tokensPerSecond float64) *TokenBucket {
	return &TokenBucket{
		capacity:        capacity,
		tokens:          float64(capacity), // 初始满桶
		tokensPerSecond: tokensPerSecond,
		lastRefillTime:  util.NowBeijing(),
	}
}

// AllowRequest 检查是否允许请求
func (rl *WorkOrderRateLimiter) AllowRequest(ctx context.Context, userID string) error {
	// 如已禁用限流，则直接放行
	if rl.config.Disabled {
		return nil
	}

	// 1. 检查全局限流
	if !rl.globalBucket.consume(1) {
		rl.logger.Warn("工单回调全局限流触发",
			zap.String("user_id", userID))
		return fmt.Errorf("全局限流：请求过于频繁")
	}

	// 2. 检查用户级限流（如果启用）
	if rl.config.PerUserLimit {
		userBucket := rl.getUserBucket(userID)
		if !userBucket.consume(1) {
			rl.logger.Warn("工单回调用户限流触发",
				zap.String("user_id", userID))
			return fmt.Errorf("用户限流：请求过于频繁")
		}
	}

	return nil
}

// getUserBucket 获取用户令牌桶
func (rl *WorkOrderRateLimiter) getUserBucket(userID string) *TokenBucket {
	rl.mu.RLock()
	bucket, exists := rl.userBuckets[userID]
	rl.mu.RUnlock()

	if exists {
		return bucket
	}

	// 创建新的用户令牌桶
	rl.mu.Lock()
	defer rl.mu.Unlock()

	// 双重检查
	if bucket, exists := rl.userBuckets[userID]; exists {
		return bucket
	}

	bucket = newTokenBucket(
		rl.config.BucketCapacity,
		rl.config.TokensPerSecond,
	)
	rl.userBuckets[userID] = bucket

	rl.logger.Debug("创建用户令牌桶",
		zap.String("user_id", userID),
		zap.Int("capacity", rl.config.BucketCapacity),
		zap.Float64("tokens_per_second", rl.config.TokensPerSecond))

	return bucket
}

// consume 消费令牌
func (tb *TokenBucket) consume(tokens int) bool {
	tb.mu.Lock()
	defer tb.mu.Unlock()

	// 补充令牌
	tb.refill()

	// 检查是否有足够的令牌
	if tb.tokens >= float64(tokens) {
		tb.tokens -= float64(tokens)
		return true
	}

	return false
}

// refill 补充令牌
func (tb *TokenBucket) refill() {
	now := util.NowBeijing()
	elapsed := now.Sub(tb.lastRefillTime).Seconds()

	// 计算应该补充的令牌数
	tokensToAdd := elapsed * tb.tokensPerSecond

	// 更新令牌数（不超过桶容量）
	tb.tokens = min(tb.tokens+tokensToAdd, float64(tb.capacity))
	tb.lastRefillTime = now
}

// min 返回两个float64中的较小值
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

// GetMetrics 获取限流器指标
func (rl *WorkOrderRateLimiter) GetMetrics() map[string]interface{} {
	rl.mu.RLock()
	defer rl.mu.RUnlock()

	userMetrics := make(map[string]interface{})
	for userID, bucket := range rl.userBuckets {
		bucket.mu.Lock()
		userMetrics[userID] = map[string]interface{}{
			"available_tokens":  bucket.tokens,
			"capacity":          bucket.capacity,
			"tokens_per_second": bucket.tokensPerSecond,
			"utilization":       (float64(bucket.capacity) - bucket.tokens) / float64(bucket.capacity),
		}
		bucket.mu.Unlock()
	}

	rl.globalBucket.mu.Lock()
	globalMetrics := map[string]interface{}{
		"available_tokens":  rl.globalBucket.tokens,
		"capacity":          rl.globalBucket.capacity,
		"tokens_per_second": rl.globalBucket.tokensPerSecond,
		"utilization":       (float64(rl.globalBucket.capacity) - rl.globalBucket.tokens) / float64(rl.globalBucket.capacity),
	}
	rl.globalBucket.mu.Unlock()

	return map[string]interface{}{
		"config": map[string]interface{}{
			"tokens_per_second":        rl.config.TokensPerSecond,
			"bucket_capacity":          rl.config.BucketCapacity,
			"per_user_limit":           rl.config.PerUserLimit,
			"global_tokens_per_second": rl.config.GlobalTokensPerSecond,
			"global_bucket_capacity":   rl.config.GlobalBucketCapacity,
			"disabled":                 rl.config.Disabled,
		},
		"global_bucket": globalMetrics,
		"user_buckets":  userMetrics,
		"total_users":   len(rl.userBuckets),
	}
}

// Reset 重置限流器
func (rl *WorkOrderRateLimiter) Reset() {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	// 重置所有用户令牌桶
	for userID, bucket := range rl.userBuckets {
		bucket.mu.Lock()
		bucket.tokens = float64(bucket.capacity)
		bucket.lastRefillTime = util.NowBeijing()
		bucket.mu.Unlock()

		rl.logger.Debug("重置用户令牌桶",
			zap.String("user_id", userID))
	}

	// 重置全局令牌桶
	rl.globalBucket.mu.Lock()
	rl.globalBucket.tokens = float64(rl.globalBucket.capacity)
	rl.globalBucket.lastRefillTime = util.NowBeijing()
	rl.globalBucket.mu.Unlock()

	rl.logger.Info("工单回调限流器已重置")
}

// CleanupUserBuckets 清理不活跃的用户令牌桶
func (rl *WorkOrderRateLimiter) CleanupUserBuckets(inactiveThreshold time.Duration) {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := util.NowBeijing()
	var cleanedCount int

	for userID, bucket := range rl.userBuckets {
		bucket.mu.Lock()
		inactive := now.Sub(bucket.lastRefillTime) > inactiveThreshold
		bucket.mu.Unlock()

		if inactive {
			delete(rl.userBuckets, userID)
			cleanedCount++
		}
	}

	if cleanedCount > 0 {
		rl.logger.Info("清理不活跃的用户令牌桶",
			zap.Int("cleaned_count", cleanedCount),
			zap.Duration("inactive_threshold", inactiveThreshold))
	}
}

// StartCleanupWorker 启动清理工作者
func (rl *WorkOrderRateLimiter) StartCleanupWorker(ctx context.Context, cleanupInterval time.Duration, inactiveThreshold time.Duration) {
	ticker := time.NewTicker(cleanupInterval)
	defer ticker.Stop()

	rl.logger.Info("启动限流器清理工作者",
		zap.Duration("cleanup_interval", cleanupInterval),
		zap.Duration("inactive_threshold", inactiveThreshold))

	for {
		select {
		case <-ctx.Done():
			rl.logger.Info("限流器清理工作者停止")
			return
		case <-ticker.C:
			rl.CleanupUserBuckets(inactiveThreshold)
		}
	}
}

// GetUserTokens 获取用户可用令牌数
func (rl *WorkOrderRateLimiter) GetUserTokens(userID string) float64 {
	if !rl.config.PerUserLimit {
		return -1 // 表示未启用用户级限流
	}

	bucket := rl.getUserBucket(userID)
	bucket.mu.Lock()
	defer bucket.mu.Unlock()

	bucket.refill()
	return bucket.tokens
}

// GetGlobalTokens 获取全局可用令牌数
func (rl *WorkOrderRateLimiter) GetGlobalTokens() float64 {
	rl.globalBucket.mu.Lock()
	defer rl.globalBucket.mu.Unlock()

	rl.globalBucket.refill()
	return rl.globalBucket.tokens
}
