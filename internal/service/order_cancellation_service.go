package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"
)

// OrderCancellationService 订单取消服务 - 单一职责原则
// 专门处理订单取消的复杂业务逻辑，包括状态管理和退款处理
type OrderCancellationService struct {
	orderRepository    repository.OrderRepository
	balanceService     BalanceServiceInterface
	transactionManager TransactionManagerInterface
	auditService       AuditServiceInterface
	providerManager    *adapter.ProviderManager
	identityResolver   *UserIdentityResolver
	logger             *zap.Logger
}

// NewOrderCancellationService 创建订单取消服务
func NewOrderCancellationService(
	orderRepository repository.OrderRepository,
	balanceService BalanceServiceInterface,
	transactionManager TransactionManagerInterface,
	auditService AuditServiceInterface,
	providerManager *adapter.ProviderManager,
	identityResolver *UserIdentityResolver,
	logger *zap.Logger,
) *OrderCancellationService {
	return &OrderCancellationService{
		orderRepository:    orderRepository,
		balanceService:     balanceService,
		transactionManager: transactionManager,
		auditService:       auditService,
		providerManager:    providerManager,
		identityResolver:   identityResolver,
		logger:             logger,
	}
}

// CancellationRequest 取消请求
type CancellationRequest struct {
	OrderNo         string `json:"order_no"`          // 🔥 智能订单号：支持平台订单号、客户订单号、供应商订单号
	TrackingNo      string `json:"tracking_no"`       // 运单号
	PlatformOrderNo string `json:"platform_order_no"` // 🔥 新增：明确指定平台订单号
	UserID          string `json:"user_id"`           // 用户ID
	Reason          string `json:"reason"`            // 取消原因
}

// CancellationResponse 取消响应
type CancellationResponse struct {
	Success   bool   `json:"success"`
	Code      int    `json:"code"`
	Message   string `json:"message"`
	OrderNo   string `json:"order_no"`
	Status    string `json:"status"`
	RequestID string `json:"request_id"`
}

// InitiateCancellation 发起取消订单 - 第一阶段：请求供应商取消
// 遵循KISS原则：只做一件事，发起取消请求并标记状态
func (s *OrderCancellationService) InitiateCancellation(ctx context.Context, req *CancellationRequest) (*CancellationResponse, error) {
	// 1. 验证和获取订单
	order, err := s.validateAndGetOrder(ctx, req)
	if err != nil {
		return &CancellationResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
		}, nil
	}

	// 2. 检查订单是否可以取消
	// 🔥 修改：快递鸟订单现在直接变为已取消状态，不再有"取消中"状态
	// 如果快递鸟订单已经是已取消状态，直接返回成功
	if order.Status == model.OrderStatusCancelled && order.Provider == "kuaidiniao" {
		s.logger.Info("🔍 [快递鸟特殊处理] 订单已经是已取消状态",
			zap.String("order_no", order.OrderNo),
			zap.String("provider", order.Provider),
			zap.String("status", order.Status))

		return &CancellationResponse{
			Success: true,
			Code:    model.StatusSuccess,
			Message: "订单已经是已取消状态",
			OrderNo: order.OrderNo,
			Status:  model.OrderStatusCancelled,
		}, nil
	}

	if err := s.validateCancellationEligibility(order); err != nil {
		return &CancellationResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
		}, nil
	}

	// 3. 获取供应商适配器
	providerAdapter, err := s.getProviderAdapter(order.Provider)
	if err != nil {
		return &CancellationResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: err.Error(),
		}, nil
	}

	// 4. 生成取消请求ID用于追踪
	requestID := s.generateCancellationRequestID(order.OrderNo)

	// 5. 在事务中执行取消发起逻辑
	err = s.transactionManager.ExecuteInTransaction(ctx, func(txCtx context.Context) error {
		// 5.1 调用供应商API发起取消
		if err := s.callProviderCancelOrder(txCtx, providerAdapter, order, req.Reason); err != nil {
			return fmt.Errorf("调用供应商取消API失败: %w", err)
		}

		// 🔥 快递鸟特殊处理：直接设置为已取消状态，跳过"取消中"状态
		if order.Provider == "kuaidiniao" {
			s.logger.Info("🚀 [快递鸟特殊处理] 直接设置为已取消状态，跳过取消中状态",
				zap.String("order_no", order.OrderNo),
				zap.String("user_id", order.UserID),
				zap.String("provider", order.Provider))

			// 直接设置为已取消状态
			order.Status = model.OrderStatusCancelled
			order.UpdatedAt = util.NowBeijing()

			if err := s.orderRepository.Update(txCtx, order); err != nil {
				return fmt.Errorf("更新订单状态失败: %w", err)
			}

			// 立即处理退款
			if err := s.processCancellationSuccess(txCtx, order, req.Reason); err != nil {
				return fmt.Errorf("处理取消退款失败: %w", err)
			}

			s.logger.Info("✅ [快递鸟特殊处理] 订单已直接取消并完成退款",
				zap.String("order_no", order.OrderNo),
				zap.String("user_id", order.UserID))

			// 🔥 新增：触发外部回调转发
			if err := s.triggerCancellationCallback(txCtx, order, req.Reason); err != nil {
				s.logger.Error("触发取消回调转发失败",
					zap.String("order_no", order.OrderNo),
					zap.Error(err))
				// 不阻断主流程，只记录错误
			}
		} else {
			// 其他供应商：等待供应商回调确认后处理退款
			s.logger.Info("⚠️ 等待供应商回调确认后处理退款",
				zap.String("order_no", order.OrderNo),
				zap.String("user_id", order.UserID),
				zap.String("provider", order.Provider))

			// 设置为"取消中"状态
			order.Status = model.OrderStatusCancelling
			order.UpdatedAt = util.NowBeijing()

			if err := s.orderRepository.Update(txCtx, order); err != nil {
				return fmt.Errorf("更新订单状态失败: %w", err)
			}
		}

		// 5.3 记录取消请求审计日志
		if s.auditService != nil {
			s.auditService.LogOrderOperation(txCtx, order.OrderNo, "initiate_cancellation", req.UserID, map[string]any{
				"reason":     req.Reason,
				"request_id": requestID,
				"provider":   order.Provider,
			})
		}

		return nil
	})

	if err != nil {
		s.logger.Error("发起订单取消失败",
			zap.String("order_no", order.OrderNo),
			zap.String("user_id", req.UserID),
			zap.Error(err))

		return &CancellationResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("发起取消失败: %s", err.Error()),
		}, nil
	}

	// 🔥 根据供应商类型返回不同的响应
	if order.Provider == "kuaidiniao" {
		s.logger.Info("快递鸟订单取消已完成",
			zap.String("order_no", order.OrderNo),
			zap.String("user_id", req.UserID),
			zap.String("request_id", requestID),
			zap.String("provider", order.Provider))

		return &CancellationResponse{
			Success:   true,
			Code:      model.StatusSuccess,
			Message:   "订单取消成功，退款已处理",
			OrderNo:   order.OrderNo,
			Status:    model.OrderStatusCancelled,
			RequestID: requestID,
		}, nil
	} else {
		s.logger.Info("订单取消请求已发起，等待供应商确认",
			zap.String("order_no", order.OrderNo),
			zap.String("user_id", req.UserID),
			zap.String("request_id", requestID),
			zap.String("provider", order.Provider))

		return &CancellationResponse{
			Success:   true,
			Code:      model.StatusSuccess,
			Message:   "取消请求已发起，等待供应商确认",
			OrderNo:   order.OrderNo,
			Status:    model.OrderStatusCancelling,
			RequestID: requestID,
		}, nil
	}
}

// handleKuaidiniaoActiveCancellationVerification 处理快递鸟主动查询确认机制
// 🔥 解决快递鸟不推送已取消状态的问题
func (s *OrderCancellationService) handleKuaidiniaoActiveCancellationVerification(ctx context.Context, order *model.OrderRecord, reason string) (*CancellationResponse, error) {
	s.logger.Info("🔍 [快递鸟主动确认] 开始处理快递鸟主动查询确认机制",
		zap.String("order_no", order.OrderNo),
		zap.String("provider", order.Provider),
		zap.String("status", order.Status),
		zap.String("reason", reason))

	// 1. 获取快递鸟适配器
	providerAdapter, err := s.getProviderAdapter(order.Provider)
	if err != nil {
		s.logger.Error("❌ [快递鸟主动确认] 获取快递鸟适配器失败",
			zap.String("order_no", order.OrderNo),
			zap.Error(err))
		return &CancellationResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("获取供应商适配器失败: %s", err.Error()),
		}, nil
	}

	// 2. 检查是否为快递鸟适配器
	kuaidiniaoAdapter, ok := providerAdapter.(*adapter.KuaidiNiaoAdapter)
	if !ok {
		s.logger.Error("❌ [快递鸟主动确认] 适配器类型不匹配",
			zap.String("order_no", order.OrderNo),
			zap.String("provider", order.Provider))
		return &CancellationResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "供应商适配器类型不匹配",
		}, nil
	}

	// 3. 启动异步主动查询确认机制
	// 使用goroutine避免阻塞用户请求
	go func() {
		// 创建新的上下文，避免原请求上下文取消影响后台处理
		bgCtx := context.Background()

		s.logger.Info("🚀 [快递鸟主动确认] 启动异步主动查询确认",
			zap.String("order_no", order.OrderNo))

		// 调用快递鸟适配器的主动查询确认机制
		err := kuaidiniaoAdapter.VerifyCancellationStatusAsync(bgCtx, order.OrderNo)
		if err != nil {
			s.logger.Error("❌ [快递鸟主动确认] 异步主动查询确认失败",
				zap.String("order_no", order.OrderNo),
				zap.Error(err))
		} else {
			s.logger.Info("✅ [快递鸟主动确认] 异步主动查询确认完成",
				zap.String("order_no", order.OrderNo))
		}
	}()

	// 4. 立即返回成功响应
	s.logger.Info("✅ [快递鸟主动确认] 主动查询确认机制已启动",
		zap.String("order_no", order.OrderNo))

	return &CancellationResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "快递鸟主动查询确认机制已启动，正在验证取消状态",
		OrderNo: order.OrderNo,
		Status:  model.OrderStatusCancelling,
	}, nil
}

// ProcessCancellationCallback 处理取消回调 - 第二阶段：基于供应商回调执行退款
// 遵循DRY原则：统一的回调处理逻辑
func (s *OrderCancellationService) ProcessCancellationCallback(ctx context.Context, orderNo string, success bool, reason string) error {
	// 1. 获取订单信息
	order, err := s.orderRepository.FindByOrderNo(ctx, orderNo)
	if err != nil {
		return fmt.Errorf("获取订单信息失败: %w", err)
	}

	// 2. 验证订单状态
	if order.Status != model.OrderStatusCancelling {
		s.logger.Warn("收到取消回调但订单状态不是取消中",
			zap.String("order_no", orderNo),
			zap.String("current_status", order.Status),
			zap.Bool("callback_success", success))
		return nil // 不返回错误，避免重复处理
	}

	// 3. 在事务中处理回调结果
	return s.transactionManager.ExecuteInTransaction(ctx, func(txCtx context.Context) error {
		if success {
			// 取消成功，执行退款逻辑
			return s.processCancellationSuccess(txCtx, order, reason)
		} else {
			// 取消失败，恢复订单状态
			return s.processCancellationFailure(txCtx, order, reason)
		}
	})
}

// processCancellationSuccess 处理取消成功 - 执行退款
func (s *OrderCancellationService) processCancellationSuccess(ctx context.Context, order *model.OrderRecord, reason string) error {
	s.logger.Info("供应商确认取消成功，开始执行退款",
		zap.String("order_no", order.OrderNo),
		zap.String("user_id", order.UserID))

	// 1. 计算退款金额
	refundAmount, err := s.calculateRefundAmount(ctx, order)
	if err != nil {
		s.logger.Error("计算退款金额失败",
			zap.String("order_no", order.OrderNo),
			zap.Error(err))
		// 使用订单价格作为备选方案
		refundAmount = decimal.NewFromFloat(order.Price)
	}

	// 2. 执行退款
	if refundAmount.GreaterThan(decimal.Zero) {
		if err := s.balanceService.RefundForOrder(ctx, order.UserID, order.OrderNo, refundAmount); err != nil {
			return fmt.Errorf("退款失败: %w", err)
		}
		s.logger.Info("订单取消退款成功",
			zap.String("order_no", order.OrderNo),
			zap.String("user_id", order.UserID),
			zap.String("refund_amount", refundAmount.String()))
	}

	// 3. 更新订单状态为已取消
	order.Status = model.OrderStatusCancelled
	order.UpdatedAt = util.NowBeijing()

	if err := s.orderRepository.Update(ctx, order); err != nil {
		return fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 4. 记录审计日志
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, order.OrderNo, "cancellation_confirmed", order.UserID, map[string]any{
			"refund_amount": refundAmount,
			"reason":        reason,
			"status":        "success",
		})
	}

	return nil
}

// processCancellationFailure 处理取消失败 - 恢复订单状态
func (s *OrderCancellationService) processCancellationFailure(ctx context.Context, order *model.OrderRecord, reason string) error {
	s.logger.Warn("供应商取消失败，恢复订单状态",
		zap.String("order_no", order.OrderNo),
		zap.String("reason", reason))

	// 1. 恢复到之前的状态（通常是submitted或awaiting_pickup）
	previousStatus := s.determinePreviousStatus(order)
	order.Status = previousStatus
	order.UpdatedAt = util.NowBeijing()

	if err := s.orderRepository.Update(ctx, order); err != nil {
		return fmt.Errorf("恢复订单状态失败: %w", err)
	}

	// 2. 记录审计日志
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, order.OrderNo, "cancellation_failed", order.UserID, map[string]any{
			"reason":          reason,
			"restored_status": previousStatus,
		})
	}

	return nil
}

// validateAndGetOrder 验证并获取订单
func (s *OrderCancellationService) validateAndGetOrder(ctx context.Context, req *CancellationRequest) (*model.OrderRecord, error) {
	// 🔐 企业级参数验证：至少需要一个查询条件
	if req.OrderNo == "" && req.TrackingNo == "" && req.PlatformOrderNo == "" {
		return nil, fmt.Errorf("订单号、运单号和平台订单号不能同时为空")
	}

	s.logger.Info("🔍 订单查找开始",
		zap.String("request_order_no", req.OrderNo),
		zap.String("request_tracking_no", req.TrackingNo),
		zap.String("request_platform_order_no", req.PlatformOrderNo),
		zap.String("user_id", req.UserID))

	var order *model.OrderRecord
	var err error

	// 🔥 使用高并发安全的智能订单查找服务
	smartFinder := NewSmartOrderFinder(s.orderRepository, s.logger)

	// 🔥 优先使用明确指定的平台订单号查找
	if req.PlatformOrderNo != "" {
		order, err = smartFinder.FindOrderByAnyIdentifier(ctx, req.PlatformOrderNo, req.UserID)
		if err == nil {
			s.logger.Info("✅ 通过平台订单号查询成功",
				zap.String("platform_order_no", req.PlatformOrderNo),
				zap.String("customer_order_no", order.CustomerOrderNo),
				zap.String("order_user_id", order.UserID))
			return order, nil
		}
		s.logger.Debug("平台订单号查询失败，尝试其他方式", zap.Error(err))
	}

	// 使用智能订单号查找（支持多种订单号格式）
	if req.OrderNo != "" {
		order, err = smartFinder.FindOrderByAnyIdentifier(ctx, req.OrderNo, req.UserID)
		if err == nil {
			s.logger.Info("✅ 通过订单号查询成功",
				zap.String("order_no", req.OrderNo),
				zap.String("customer_order_no", order.CustomerOrderNo),
				zap.String("order_user_id", order.UserID))
			return order, nil
		}
		s.logger.Debug("订单号查询失败，尝试运单号", zap.Error(err))
	}

	// 使用运单号查找
	if req.TrackingNo != "" {
		order, err = smartFinder.FindOrderByAnyIdentifier(ctx, req.TrackingNo, req.UserID)
		if err == nil {
			s.logger.Info("✅ 通过运单号查询成功",
				zap.String("tracking_no", req.TrackingNo),
				zap.String("order_user_id", order.UserID))
			return order, nil
		}
		s.logger.Debug("运单号查询失败", zap.Error(err))
	}

	s.logger.Error("❌ 所有查询方式都失败",
		zap.String("request_order_no", req.OrderNo),
		zap.String("request_tracking_no", req.TrackingNo),
		zap.String("request_platform_order_no", req.PlatformOrderNo),
		zap.String("user_id", req.UserID))

	return nil, fmt.Errorf("订单不存在")
}

// validateCancellationEligibility 验证取消资格
func (s *OrderCancellationService) validateCancellationEligibility(order *model.OrderRecord) error {
	// 不可取消的状态
	nonCancelableStatuses := []string{
		model.OrderStatusPickedUp,
		model.OrderStatusInTransit,
		model.OrderStatusOutForDelivery,
		model.OrderStatusDelivered,
		model.OrderStatusDeliveredAbnormal,
		model.OrderStatusBilled,
		model.OrderStatusCancelled,
		model.OrderStatusCancelling, // 已在取消中
		model.OrderStatusVoided,
		model.OrderStatusReturned,
		model.OrderStatusForwarded,
		model.OrderStatusFailed, // 🔥 新增：失败的订单不能取消（因为没有真正的供应商订单号）
	}

	for _, status := range nonCancelableStatuses {
		if order.Status == status {
			statusDesc := model.GetOrderStatusDesc(order.Status)
			if statusDesc == "" {
				statusDesc = order.Status
			}
			return fmt.Errorf("订单状态为 %s，无法取消", statusDesc)
		}
	}

	return nil
}

// callProviderCancelOrder 调用供应商取消订单API，为菜鸟供应商特殊处理
func (s *OrderCancellationService) callProviderCancelOrder(ctx context.Context, providerAdapter adapter.ProviderAdapter, order *model.OrderRecord, reason string) error {
	// 🔥 菜鸟供应商特殊处理：需要设置用户手机号
	if order.Provider == "cainiao" {
		// 从订单记录中解析寄件人信息
		senderInfo, err := s.parseSenderInfoFromOrder(order)
		if err != nil {
			s.logger.Error("解析菜鸟订单寄件人信息失败",
				zap.String("order_no", order.OrderNo),
				zap.Error(err))
			return fmt.Errorf("解析寄件人信息失败: %w", err)
		}

		// 🔥 临时解决方案：修改菜鸟适配器的取消订单方法，传递用户手机号
		if cainiaoAdapter, ok := providerAdapter.(*adapter.CainiaoAdapter); ok {
			return cainiaoAdapter.CancelOrderWithUserInfo(ctx, order.TaskId, order.OrderNo, reason, senderInfo.Mobile)
		}
	}

	// 其他供应商使用标准接口
	return providerAdapter.CancelOrder(ctx, order.TaskId, order.OrderNo, reason)
}

// parseSenderInfoFromOrder 从订单记录中解析寄件人信息
func (s *OrderCancellationService) parseSenderInfoFromOrder(order *model.OrderRecord) (*model.SenderInfo, error) {
	if order.SenderInfo == "" {
		return nil, fmt.Errorf("订单寄件人信息为空")
	}

	var senderInfo model.SenderInfo
	if err := json.Unmarshal([]byte(order.SenderInfo), &senderInfo); err != nil {
		return nil, fmt.Errorf("解析寄件人信息JSON失败: %w", err)
	}

	if senderInfo.Mobile == "" {
		return nil, fmt.Errorf("寄件人手机号为空")
	}

	return &senderInfo, nil
}

// getProviderAdapter 获取供应商适配器用于订单取消（不检查启用状态）
// 🔥 修复：订单取消操作不应该检查供应商启用状态，因为用户有权取消已下的订单
func (s *OrderCancellationService) getProviderAdapter(provider string) (adapter.ProviderAdapter, error) {
	providerAdapter, ok := s.providerManager.GetForTracking(provider)
	if !ok {
		return nil, fmt.Errorf("供应商 %s 不存在", provider)
	}
	return providerAdapter, nil
}

// calculateRefundAmount 计算退款金额
func (s *OrderCancellationService) calculateRefundAmount(ctx context.Context, order *model.OrderRecord) (decimal.Decimal, error) {
	// 获取用户实际支付的金额
	if s.balanceService != nil {
		actualPaidAmount, err := s.balanceService.GetOrderNetPayment(ctx, order.UserID, order.OrderNo, order.CustomerOrderNo)
		if err != nil {
			s.logger.Error("获取实际支付金额失败",
				zap.String("order_no", order.OrderNo),
				zap.Error(err))
			return decimal.Zero, err
		}
		return actualPaidAmount, nil
	}

	// 备选方案：使用订单价格
	return decimal.NewFromFloat(order.Price), nil
}

// generateCancellationRequestID 生成取消请求ID
func (s *OrderCancellationService) generateCancellationRequestID(orderNo string) string {
	return fmt.Sprintf("CANCEL_%s_%d", orderNo, util.NowBeijing().Unix())
}

// determinePreviousStatus 确定之前的状态
func (s *OrderCancellationService) determinePreviousStatus(order *model.OrderRecord) string {
	// 根据订单创建时间和其他信息推断之前的状态
	// 简化实现：返回submitted状态
	return model.OrderStatusSubmitted
}

// triggerCancellationCallback 触发取消回调转发
// 🔥 新增：为快递鸟直接取消的订单生成外部回调转发
func (s *OrderCancellationService) triggerCancellationCallback(ctx context.Context, order *model.OrderRecord, reason string) error {
	s.logger.Info("🔄 触发取消回调转发",
		zap.String("order_no", order.OrderNo),
		zap.String("user_id", order.UserID),
		zap.String("provider", order.Provider),
		zap.String("reason", reason))

	// 🔥 解决方案：模拟快递鸟的取消回调，发送到本地回调接口
	// 这样可以触发完整的回调处理流程，包括外部转发

	// 构建快递鸟格式的取消回调数据
	kuaidiniaoCallbackData := map[string]interface{}{
		"PushTime":    util.NowBeijing().Format("2006-01-02 15:04:05"),
		"EBusinessID": "1778716", // 快递鸟用户ID
		"Data": []map[string]interface{}{
			{
				"ShipperCode":     "STO", // 申通快递代码
				"State":           "203", // 已取消状态码
				"CreateTime":      util.NowBeijing().Format("2006-01-02 15:04:05"),
				"KDNOrderCode":    order.TrackingNo,                   // 运单号
				"OrderCode":       order.OrderNo,                      // 订单号
				"Reason":          fmt.Sprintf("快递鸟直接取消: %s", reason), // 取消原因
				"OperateType":     1,                                  // 操作类型
				"CallRequestType": "1801",                             // 请求类型
			},
		},
		"Count": 1,
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(kuaidiniaoCallbackData)
	if err != nil {
		return fmt.Errorf("序列化快递鸟回调数据失败: %w", err)
	}

	// 🔥 构建快递鸟回调的表单数据格式
	// 快递鸟回调需要URL编码的RequestData参数
	encodedData := url.QueryEscape(string(jsonData))
	formData := fmt.Sprintf("RequestData=%s&DataSign=INTERNAL_CALLBACK&RequestType=103", encodedData)
	rawData := []byte(formData)

	s.logger.Info("📤 生成快递鸟格式的取消回调数据",
		zap.String("order_no", order.OrderNo),
		zap.String("user_id", order.UserID),
		zap.String("json_data", string(jsonData)),
		zap.String("form_data", formData))

	// 🔥 异步发送到本地快递鸟回调接口，触发完整的回调处理流程
	go s.sendInternalCallback(context.Background(), rawData)

	return nil
}

// sendInternalCallback 发送内部回调到本地接口
func (s *OrderCancellationService) sendInternalCallback(ctx context.Context, rawData []byte) {
	s.logger.Info("🔄 异步发送内部回调到本地快递鸟接口",
		zap.Int("data_size", len(rawData)))

	// 🔥 实现HTTP客户端调用本地回调接口
	// 这会触发完整的回调处理流程，包括外部转发

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 构建请求URL - 本地快递鸟回调接口
	callbackURL := "http://localhost:8081/api/v1/callbacks/kuaidiniao"

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", callbackURL, bytes.NewBuffer(rawData))
	if err != nil {
		s.logger.Error("创建内部回调请求失败",
			zap.String("url", callbackURL),
			zap.Error(err))
		return
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
	req.Header.Set("User-Agent", "KuaidiNiao-Internal-Callback/1.0")
	req.Header.Set("X-Internal-Callback", "true") // 标记为内部回调

	s.logger.Info("📤 发送内部回调请求",
		zap.String("url", callbackURL),
		zap.String("method", "POST"),
		zap.Int("body_size", len(rawData)))

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		s.logger.Error("发送内部回调请求失败",
			zap.String("url", callbackURL),
			zap.Error(err))
		return
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode == 200 {
		s.logger.Info("✅ 内部回调请求成功",
			zap.String("url", callbackURL),
			zap.Int("status_code", resp.StatusCode))
	} else {
		s.logger.Warn("⚠️ 内部回调请求返回非200状态",
			zap.String("url", callbackURL),
			zap.Int("status_code", resp.StatusCode))
	}

	s.logger.Info("🎉 内部回调处理完成，已触发完整的回调处理流程",
		zap.String("url", callbackURL),
		zap.Int("status_code", resp.StatusCode))
}
