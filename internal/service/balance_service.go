package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

var (
	ErrInsufficientBalance  = errors.New("余额不足")
	ErrBalanceNotFound      = errors.New("余额账户不存在")
	ErrInvalidAmount        = errors.New("金额无效")
	ErrDuplicateTransaction = errors.New("重复交易")
	ErrInvalidConfig        = errors.New("无效配置")
	ErrInvalidUserID        = errors.New("用户ID无效")
)

// BalanceService 余额服务接口
type BalanceService interface {
	// 余额查询
	GetBalance(ctx context.Context, userID string) (*model.BalanceResponse, error)

	// 余额操作
	Deposit(ctx context.Context, req *model.DepositRequest) (*model.TransactionResponse, error)
	Payment(ctx context.Context, req *model.PaymentRequest) (*model.TransactionResponse, error)
	Refund(ctx context.Context, req *model.BalanceRequest) (*model.TransactionResponse, error)
	Transfer(ctx context.Context, req *model.TransferRequest) (*model.TransactionResponse, error)

	// 交易记录
	GetTransactionHistory(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, error)
	GetTransactionHistoryWithCount(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, int64, error)
	GetTransactionHistoryWithFilters(ctx context.Context, userID string, limit, offset int, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo string) ([]*model.TransactionResponse, int64, error)
	GetTransactionByID(ctx context.Context, id string) (*model.TransactionResponse, error)
	GetTransactionByOrderNo(ctx context.Context, orderNo string) (*model.TransactionResponse, error)

	// 充值和支付记录
	GetDepositHistory(ctx context.Context, userID string, limit, offset int) ([]*model.Deposit, error)
	GetPaymentHistory(ctx context.Context, userID string, limit, offset int) ([]*model.OrderPayment, error)

	PreChargeForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error
	PreChargeForOrderWithDetails(ctx context.Context, userID, customerOrderNo, platformOrderNo string, amount decimal.Decimal) error
	ChargeForBillingDifference(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error
	RefundForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error
	RefundForBillingDifference(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error
	GetOrderNetPayment(ctx context.Context, userID, orderNo, customerOrderNo string) (decimal.Decimal, error)

	// 余额预检查方法 - 🔥 新增：修复余额不足却创建订单成功的BUG
	CheckBalanceForOrder(ctx context.Context, userID string, amount decimal.Decimal) (*model.BalanceCheckResult, error)
	CheckAndReserveBalance(ctx context.Context, userID string, amount decimal.Decimal, reservationID string) error
	ReleaseReservedBalance(ctx context.Context, userID string, reservationID string) error

	// 内部方法
	CreateBalanceIfNotExists(ctx context.Context, userID string) error
	ProcessPaymentCallback(ctx context.Context, depositID string, success bool, transactionID string) error
}

// DefaultBalanceService 默认余额服务实现
// 🔥 企业级改造：集成智能描述生成器
type DefaultBalanceService struct {
	repository           repository.BalanceRepository
	orderRepository      repository.OrderRepository
	db                   *sql.DB
	logger               *zap.Logger
	descriptionGenerator *TransactionDescriptionGenerator // 新增：描述生成器
	monitor              *BalanceCheckMonitor             // 🔥 新增：余额检查监控器
}

// NewBalanceService 创建余额服务
// 🔥 企业级改造：初始化描述生成器
func NewBalanceService(repository repository.BalanceRepository, orderRepository repository.OrderRepository, db *sql.DB, logger *zap.Logger) BalanceService {
	if logger == nil {
		logger, _ = zap.NewProduction()
	}
	// 🔥 修复：暂时禁用监控器，避免Prometheus重复注册问题
	// TODO: 后续可以通过配置开关来控制是否启用监控
	return &DefaultBalanceService{
		repository:           repository,
		orderRepository:      orderRepository,
		db:                   db,
		logger:               logger,
		descriptionGenerator: NewTransactionDescriptionGenerator(), // 初始化描述生成器
		monitor:              nil,                                  // 🔥 暂时禁用监控器
	}
}

// GetBalance 获取用户余额
func (s *DefaultBalanceService) GetBalance(ctx context.Context, userID string) (*model.BalanceResponse, error) {
	balance, err := s.repository.GetBalance(ctx, userID)
	if err != nil {
		// 如果余额不存在，自动创建
		if err := s.CreateBalanceIfNotExists(ctx, userID); err != nil {
			return nil, fmt.Errorf("创建余额账户失败: %w", err)
		}

		// 重新获取余额
		balance, err = s.repository.GetBalance(ctx, userID)
		if err != nil {
			return nil, fmt.Errorf("获取余额失败: %w", err)
		}
	}

	return &model.BalanceResponse{
		UserID:           balance.UserID,
		Balance:          balance.Balance,
		AvailableBalance: balance.Balance, // 可用余额等于总余额
		Currency:         balance.Currency,
		UpdatedAt:        balance.UpdatedAt,
	}, nil
}

// CreateBalanceIfNotExists 如果余额账户不存在则创建
func (s *DefaultBalanceService) CreateBalanceIfNotExists(ctx context.Context, userID string) error {
	// 先检查余额账户是否已存在
	_, err := s.repository.GetBalance(ctx, userID)
	if err == nil {
		// 账户已存在，直接返回
		return nil
	}

	// 账户不存在，创建新账户
	balance := &model.UserBalance{
		UserID:   userID,
		Balance:  decimal.Zero,
		Currency: "CNY",
		Version:  0,
	}

	return s.repository.CreateBalance(ctx, balance)
}

// CheckBalanceForOrder 检查用户余额是否足够支付订单 - 🔥 新增：修复余额不足却创建订单成功的BUG
func (s *DefaultBalanceService) CheckBalanceForOrder(ctx context.Context, userID string, amount decimal.Decimal) (*model.BalanceCheckResult, error) {
	// 🔥 监控：记录检查开始时间
	startTime := time.Now()

	// 🔥 调试：记录基础余额服务开始执行
	s.logger.Info("🔥 基础余额服务：开始执行余额检查",
		zap.String("user_id", userID),
		zap.String("amount", amount.String()))

	if userID == "" {
		return nil, ErrInvalidUserID
	}
	if amount.LessThanOrEqual(decimal.Zero) {
		return nil, ErrInvalidAmount
	}

	// 确保余额账户存在
	if err := s.CreateBalanceIfNotExists(ctx, userID); err != nil {
		duration := time.Since(startTime)
		if s.monitor != nil {
			s.monitor.RecordBalanceCheck(ctx, userID, amount, nil, duration, err)
		}
		return nil, fmt.Errorf("创建余额账户失败: %w", err)
	}

	// 获取当前余额
	balance, err := s.repository.GetBalance(ctx, userID)
	if err != nil {
		duration := time.Since(startTime)
		if s.monitor != nil {
			s.monitor.RecordBalanceCheck(ctx, userID, amount, nil, duration, err)
		}
		return nil, fmt.Errorf("获取余额失败: %w", err)
	}

	// 计算检查结果
	currentBalance := balance.Balance
	availableBalance := balance.AvailableBalance()
	isSufficient := availableBalance.GreaterThanOrEqual(amount)

	result := &model.BalanceCheckResult{
		UserID:           userID,
		RequestedAmount:  amount,
		CurrentBalance:   currentBalance,
		AvailableBalance: availableBalance,
		IsSufficient:     isSufficient,
		CheckTime:        util.NowBeijing(),
	}

	if isSufficient {
		result.Message = "余额充足"
		result.Shortage = decimal.Zero
	} else {
		shortage := amount.Sub(availableBalance)
		result.Shortage = shortage
		result.Message = "账户余额不足，请联系客服"
	}

	// 🔥 调试：记录余额检查结果
	s.logger.Info("🔥 基础余额服务：余额检查完成",
		zap.String("user_id", userID),
		zap.String("amount", amount.String()),
		zap.String("current_balance", currentBalance.String()),
		zap.String("available_balance", availableBalance.String()),
		zap.Bool("is_sufficient", isSufficient),
		zap.String("message", result.Message))

	// 🔥 监控：记录检查完成（如果监控器可用）
	duration := time.Since(startTime)
	if s.monitor != nil {
		s.monitor.RecordBalanceCheck(ctx, userID, amount, result, duration, nil)
	}

	return result, nil
}

// CheckAndReserveBalance 检查并预留余额 - 🔥 新增：原子化余额检查和预留
func (s *DefaultBalanceService) CheckAndReserveBalance(ctx context.Context, userID string, amount decimal.Decimal, reservationID string) error {
	// TODO: 实现余额预留机制（可选的高级功能）
	// 当前先使用简单的余额检查
	result, err := s.CheckBalanceForOrder(ctx, userID, amount)
	if err != nil {
		return err
	}

	if !result.IsSufficient {
		return ErrInsufficientBalance
	}

	return nil
}

// ReleaseReservedBalance 释放预留余额 - 🔥 新增：释放预留的余额
func (s *DefaultBalanceService) ReleaseReservedBalance(ctx context.Context, userID string, reservationID string) error {
	// TODO: 实现余额预留释放机制（可选的高级功能）
	// 当前为空实现
	return nil
}

// Deposit 充值
func (s *DefaultBalanceService) Deposit(ctx context.Context, req *model.DepositRequest) (*model.TransactionResponse, error) {
	// 验证金额
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return nil, ErrInvalidAmount
	}

	// 确保余额账户存在
	if err := s.CreateBalanceIfNotExists(ctx, req.UserID); err != nil {
		return nil, fmt.Errorf("创建余额账户失败: %w", err)
	}

	// 创建充值记录
	deposit := &model.Deposit{
		UserID:        req.UserID,
		Amount:        req.Amount,
		Currency:      req.Currency,
		PaymentMethod: req.PaymentMethod,

		PaymentData: req.PaymentData,
	}

	if err := s.repository.CreateDeposit(ctx, deposit); err != nil {
		return nil, fmt.Errorf("创建充值记录失败: %w", err)
	}

	// 如果是手动充值，直接完成
	if req.PaymentMethod == model.PaymentMethodManual {
		return s.processDeposit(ctx, deposit, req.UserID)
	}

	// 其他支付方式需要等待回调
	return &model.TransactionResponse{
		ID:          deposit.ID,
		Type:        model.TransactionTypeUserDeposit, // 🔥 企业级修复：使用新的细分交易类型
		Amount:      deposit.Amount,
		Currency:    deposit.Currency,
		Description: "充值待处理",
	}, nil
}

// processDeposit 处理充值
func (s *DefaultBalanceService) processDeposit(ctx context.Context, deposit *model.Deposit, operatorID string) (*model.TransactionResponse, error) {
	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 获取当前余额
	balance, err := s.repository.GetBalance(ctx, deposit.UserID)
	if err != nil {
		return nil, fmt.Errorf("获取余额失败: %w", err)
	}

	// 计算新余额
	newBalance := balance.Balance.Add(deposit.Amount)

	// 先尝试更新余额（使用乐观锁，冻结余额保持为0）
	if err := s.repository.UpdateBalanceWithVersion(ctx, deposit.UserID, newBalance, decimal.Zero, balance.Version); err != nil {
		return nil, fmt.Errorf("更新余额失败: %w", err)
	}

	// 创建交易记录
	transaction := &model.BalanceTransaction{
		UserID:        deposit.UserID,
		Type:          model.TransactionTypeUserDeposit, // 🔥 企业级修复：使用新的细分交易类型
		Amount:        deposit.Amount,
		Currency:      deposit.Currency,
		BalanceBefore: balance.Balance,
		BalanceAfter:  newBalance,
		ReferenceID:   deposit.ID,
		Description:   fmt.Sprintf("充值 %s %s", deposit.Amount.String(), deposit.Currency),
		OperatorID:    operatorID,
	}

	// 保存交易记录
	if err := s.repository.CreateTransaction(ctx, transaction); err != nil {
		return nil, fmt.Errorf("创建交易记录失败: %w", err)
	}

	// 更新充值记录状态
	deposit.Status = model.TransactionStatusCompleted
	deposit.TransactionID = transaction.ID
	if err := s.repository.UpdateDeposit(ctx, deposit); err != nil {
		return nil, fmt.Errorf("更新充值记录失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("充值成功",
		zap.String("user_id", deposit.UserID),
		zap.String("amount", deposit.Amount.String()),
		zap.String("transaction_id", transaction.ID),
	)

	return &model.TransactionResponse{
		ID:            transaction.ID,
		Type:          transaction.Type,
		Amount:        transaction.Amount,
		Currency:      transaction.Currency,
		BalanceBefore: transaction.BalanceBefore,
		BalanceAfter:  transaction.BalanceAfter,
		Description:   transaction.Description,
	}, nil
}

// Payment 支付
func (s *DefaultBalanceService) Payment(ctx context.Context, req *model.PaymentRequest) (*model.TransactionResponse, error) {
	// 验证金额
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return nil, ErrInvalidAmount
	}

	// 检查是否重复支付
	existingPayment, err := s.repository.GetOrderPaymentByOrderNo(ctx, req.OrderNo)
	if err == nil && existingPayment != nil {
		return nil, ErrDuplicateTransaction
	}

	// 如果是余额支付，处理余额扣减
	if req.PaymentMethod == model.PaymentMethodBalance {
		return s.processBalancePayment(ctx, req)
	}

	// 其他支付方式创建支付记录
	payment := &model.OrderPayment{
		OrderNo:       req.OrderNo,
		UserID:        req.UserID,
		Amount:        req.Amount,
		Currency:      req.Currency,
		PaymentMethod: req.PaymentMethod,
	}

	if err := s.repository.CreateOrderPayment(ctx, payment); err != nil {
		return nil, fmt.Errorf("创建支付记录失败: %w", err)
	}

	return &model.TransactionResponse{
		ID:       payment.ID,
		Type:     model.TransactionTypeOrderPreCharge, // 🔥 使用新的9种核心类型
		Amount:   payment.Amount,
		Currency: payment.Currency,

		Description: req.Description,
	}, nil
}

// processBalancePayment 处理余额支付
func (s *DefaultBalanceService) processBalancePayment(ctx context.Context, req *model.PaymentRequest) (*model.TransactionResponse, error) {
	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 获取当前余额
	balance, err := s.repository.GetBalance(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("获取余额失败: %w", err)
	}

	// 检查余额是否足够
	if balance.AvailableBalance().LessThan(req.Amount) {
		return nil, ErrInsufficientBalance
	}

	// 计算新余额
	newBalance := balance.Balance.Sub(req.Amount)

	// 🔥 彻底重构：创建交易记录，使用新的9种核心类型
	transaction := &model.BalanceTransaction{
		UserID:            req.UserID,
		Type:              model.TransactionTypeOrderPreCharge, // 🔥 使用新的9种核心类型
		Amount:            req.Amount,
		Currency:          req.Currency,
		BalanceBefore:     balance.Balance,
		BalanceAfter:      newBalance,
		OrderNo:           req.OrderNo,
		Description:       req.Description,
		DetailDescription: fmt.Sprintf("用户%s支付订单%s，金额%s%s", req.UserID, req.OrderNo, req.Amount.String(), req.Currency),
		UserFriendlyDesc:  fmt.Sprintf("您支付了订单%s，金额%s元", req.OrderNo, req.Amount.String()),
		Metadata:          req.Metadata,                     // 🔥 企业级修复：包含metadata信息
		Status:            model.TransactionStatusCompleted, // 🔥 修复：设置交易状态为已完成
		BusinessContext: map[string]interface{}{
			"transaction_time": util.NowBeijing(),
			"payment_method":   string(req.PaymentMethod),
			"order_info": map[string]interface{}{
				"order_no": req.OrderNo,
				"amount":   req.Amount.String(),
				"currency": req.Currency,
			},
		},
		OperatorID: req.UserID,
	}

	// 保存交易记录
	if err := s.repository.CreateTransaction(ctx, transaction); err != nil {
		return nil, fmt.Errorf("创建交易记录失败: %w", err)
	}

	// 更新余额（使用乐观锁）
	if err := s.repository.UpdateBalanceWithVersion(ctx, req.UserID, newBalance, decimal.Zero, balance.Version); err != nil {
		return nil, fmt.Errorf("更新余额失败: %w", err)
	}

	// 创建订单支付记录
	payment := &model.OrderPayment{
		OrderNo:       req.OrderNo,
		UserID:        req.UserID,
		Amount:        req.Amount,
		Currency:      req.Currency,
		PaymentMethod: req.PaymentMethod,

		TransactionID: transaction.ID,
	}

	if err := s.repository.CreateOrderPayment(ctx, payment); err != nil {
		return nil, fmt.Errorf("创建支付记录失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("余额支付成功",
		zap.String("user_id", req.UserID),
		zap.String("order_no", req.OrderNo),
		zap.String("amount", req.Amount.String()),
		zap.String("transaction_id", transaction.ID),
	)

	return &model.TransactionResponse{
		ID:            transaction.ID,
		UserID:        transaction.UserID,
		Type:          transaction.Type,
		Amount:        transaction.Amount,
		Currency:      transaction.Currency,
		BalanceBefore: transaction.BalanceBefore,
		BalanceAfter:  transaction.BalanceAfter,

		// 订单关联信息
		OrderNo:         transaction.OrderNo,
		CustomerOrderNo: transaction.CustomerOrderNo,
		TrackingNo:      transaction.TrackingNo,

		// 交易分类信息
		TransactionCategory: transaction.Category,
		TransactionSubType:  transaction.SubType,

		// 描述信息
		ReferenceID:       transaction.ReferenceID,
		Description:       transaction.Description,
		DetailDescription: transaction.DetailDescription,
		UserFriendlyDesc:  transaction.UserFriendlyDesc,

		// 业务上下文
		Metadata:             transaction.Metadata,
		BusinessContext:      transaction.BusinessContext,
		RelatedTransactionID: transaction.RelatedTransactionID,

		// 操作信息

	}, nil
}

// Refund 退款
func (s *DefaultBalanceService) Refund(ctx context.Context, req *model.BalanceRequest) (*model.TransactionResponse, error) {
	// 验证金额
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return nil, ErrInvalidAmount
	}

	// 检查是否重复退款
	if req.ReferenceID != "" {
		existingTx, err := s.repository.GetTransactionByReferenceID(ctx, req.ReferenceID)
		if err == nil && existingTx != nil {
			return nil, ErrDuplicateTransaction
		}
	}

	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 获取当前余额
	balance, err := s.repository.GetBalance(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("获取余额失败: %w", err)
	}

	// 计算新余额
	newBalance := balance.Balance.Add(req.Amount)

	// 创建交易记录
	transaction := &model.BalanceTransaction{
		UserID:        req.UserID,
		Type:          model.TransactionTypeOrderCancelRefund,
		Amount:        req.Amount,
		Currency:      req.Currency,
		BalanceBefore: balance.Balance,
		BalanceAfter:  newBalance,
		OrderNo:       req.OrderNo,
		ReferenceID:   req.ReferenceID,
		Description:   req.Description,
		Status:        model.TransactionStatusCompleted, // 🔥 修复：设置交易状态为已完成
	}

	// 保存交易记录
	if err := s.repository.CreateTransaction(ctx, transaction); err != nil {
		return nil, fmt.Errorf("创建交易记录失败: %w", err)
	}

	// 更新余额（使用乐观锁）
	if err := s.repository.UpdateBalanceWithVersion(ctx, req.UserID, newBalance, decimal.Zero, balance.Version); err != nil {
		return nil, fmt.Errorf("更新余额失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("退款成功",
		zap.String("user_id", req.UserID),
		zap.String("amount", req.Amount.String()),
		zap.String("transaction_id", transaction.ID),
	)

	return &model.TransactionResponse{
		ID:            transaction.ID,
		Type:          transaction.Type,
		Amount:        transaction.Amount,
		Currency:      transaction.Currency,
		BalanceBefore: transaction.BalanceBefore,
		BalanceAfter:  transaction.BalanceAfter,
		Description:   transaction.Description,
	}, nil
}

// 注意：已删除 Freeze 和 Unfreeze 方法，系统不再使用冻结逻辑

// Transfer 转账
func (s *DefaultBalanceService) Transfer(ctx context.Context, req *model.TransferRequest) (*model.TransactionResponse, error) {
	// 验证金额
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return nil, ErrInvalidAmount
	}

	// 验证转账双方不能是同一人
	if req.FromUserID == req.ToUserID {
		return nil, fmt.Errorf("不能向自己转账")
	}

	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 获取转出方余额
	fromBalance, err := s.repository.GetBalance(ctx, req.FromUserID)
	if err != nil {
		return nil, fmt.Errorf("获取转出方余额失败: %w", err)
	}

	// 检查余额是否足够
	if fromBalance.AvailableBalance().LessThan(req.Amount) {
		return nil, ErrInsufficientBalance
	}

	// 确保转入方余额账户存在
	if err := s.CreateBalanceIfNotExists(ctx, req.ToUserID); err != nil {
		return nil, fmt.Errorf("创建转入方余额账户失败: %w", err)
	}

	// 获取转入方余额
	toBalance, err := s.repository.GetBalance(ctx, req.ToUserID)
	if err != nil {
		return nil, fmt.Errorf("获取转入方余额失败: %w", err)
	}

	// 计算新余额
	newFromBalance := fromBalance.Balance.Sub(req.Amount)
	newToBalance := toBalance.Balance.Add(req.Amount)

	// 创建转出交易记录
	transferOutTx := &model.BalanceTransaction{
		UserID:        req.FromUserID,
		Type:          model.TransactionTypeBalanceAdjustment,
		Amount:        req.Amount,
		Currency:      req.Currency,
		BalanceBefore: fromBalance.Balance,
		BalanceAfter:  newFromBalance,
		Description:   req.Description,
	}

	// 创建转入交易记录
	transferInTx := &model.BalanceTransaction{
		UserID:        req.ToUserID,
		Type:          model.TransactionTypeAdminDeposit,
		Amount:        req.Amount,
		Currency:      req.Currency,
		BalanceBefore: toBalance.Balance,
		BalanceAfter:  newToBalance,
		Description:   req.Description,
	}

	// 保存交易记录
	if err := s.repository.CreateTransaction(ctx, transferOutTx); err != nil {
		return nil, fmt.Errorf("创建转出交易记录失败: %w", err)
	}

	if err := s.repository.CreateTransaction(ctx, transferInTx); err != nil {
		return nil, fmt.Errorf("创建转入交易记录失败: %w", err)
	}

	// 更新转出方余额
	if err := s.repository.UpdateBalanceWithVersion(ctx, req.FromUserID, newFromBalance, decimal.Zero, fromBalance.Version); err != nil {
		return nil, fmt.Errorf("更新转出方余额失败: %w", err)
	}

	// 更新转入方余额
	if err := s.repository.UpdateBalanceWithVersion(ctx, req.ToUserID, newToBalance, decimal.Zero, toBalance.Version); err != nil {
		return nil, fmt.Errorf("更新转入方余额失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("转账成功",
		zap.String("from_user_id", req.FromUserID),
		zap.String("to_user_id", req.ToUserID),
		zap.String("amount", req.Amount.String()),
		zap.String("transaction_id", transferOutTx.ID),
	)

	return &model.TransactionResponse{
		ID:            transferOutTx.ID,
		Type:          transferOutTx.Type,
		Amount:        transferOutTx.Amount,
		Currency:      transferOutTx.Currency,
		BalanceBefore: transferOutTx.BalanceBefore,
		BalanceAfter:  transferOutTx.BalanceAfter,
		Description:   transferOutTx.Description,
	}, nil
}

// GetTransactionHistory 获取交易历史
func (s *DefaultBalanceService) GetTransactionHistory(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, error) {
	transactions, err := s.repository.GetTransactionsByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("获取交易历史失败: %w", err)
	}

	var responses []*model.TransactionResponse
	for _, tx := range transactions {
		trackingNo := s.getTrackingNoByOrderNo(ctx, tx.OrderNo)
		responses = append(responses, &model.TransactionResponse{
			ID:            tx.ID,
			Type:          tx.Type,
			Amount:        tx.Amount,
			Currency:      tx.Currency,
			BalanceBefore: tx.BalanceBefore,
			BalanceAfter:  tx.BalanceAfter,
			OrderNo:       tx.OrderNo,
			TrackingNo:    trackingNo,
			ReferenceID:   tx.ReferenceID,
			Description:   tx.Description,
		})
	}

	return responses, nil
}

// GetTransactionHistoryWithCount 获取交易历史和总数
func (s *DefaultBalanceService) GetTransactionHistoryWithCount(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, int64, error) {
	// 获取交易记录
	transactions, err := s.repository.GetTransactionsByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("获取交易历史失败: %w", err)
	}

	// 获取总数
	count, err := s.repository.GetTransactionCountByUserID(ctx, userID)
	if err != nil {
		return nil, 0, fmt.Errorf("获取交易记录总数失败: %w", err)
	}

	var responses []*model.TransactionResponse
	for _, tx := range transactions {
		trackingNo := s.getTrackingNoByOrderNo(ctx, tx.OrderNo)
		responses = append(responses, &model.TransactionResponse{
			ID:            tx.ID,
			UserID:        tx.UserID,
			Type:          tx.Type,
			Amount:        tx.Amount,
			Currency:      tx.Currency,
			BalanceBefore: tx.BalanceBefore,
			BalanceAfter:  tx.BalanceAfter,

			// 订单关联信息
			OrderNo:         tx.OrderNo,
			CustomerOrderNo: tx.CustomerOrderNo,
			TrackingNo:      trackingNo,

			// 交易分类信息
			TransactionCategory: tx.Category,
			TransactionSubType:  tx.SubType,

			// 描述信息
			ReferenceID:       tx.ReferenceID,
			Description:       tx.Description,
			DetailDescription: tx.DetailDescription,
			UserFriendlyDesc:  tx.UserFriendlyDesc,

			// 业务上下文
			Metadata:             tx.Metadata,
			BusinessContext:      tx.BusinessContext,
			RelatedTransactionID: tx.RelatedTransactionID,

			// 操作信息 - 🔥 修复：添加缺失的字段映射
			OperatorID: tx.OperatorID,
			Status:     tx.Status,
			CreatedAt:  tx.CreatedAt,
		})
	}

	return responses, count, nil
}

// getTrackingNoByOrderNo 通过订单号获取运单号
func (s *DefaultBalanceService) getTrackingNoByOrderNo(ctx context.Context, orderNo string) string {
	if orderNo == "" {
		return ""
	}

	// 如果orderRepository为nil，直接返回空字符串
	if s.orderRepository == nil {
		s.logger.Debug("orderRepository为nil，无法获取运单号", zap.String("order_no", orderNo))
		return ""
	}

	// 🚀 使用智能查询服务进行高效查询
	smartFinder := NewSmartOrderFinder(s.orderRepository, s.logger)
	order, err := smartFinder.FindOrderByAnyIdentifier(ctx, orderNo, "")
	if err != nil {
		// 如果查不到，记录日志但不影响主流程
		s.logger.Debug("智能查询服务获取运单号失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return ""
	}

	if order == nil {
		return ""
	}

	return order.TrackingNo
}

// getOrderNumbers 根据订单号获取客户订单号和平台订单号
func (s *DefaultBalanceService) getOrderNumbers(ctx context.Context, orderNo string) (customerOrderNo, platformOrderNo string) {
	if orderNo == "" {
		return "", ""
	}

	// 如果orderRepository为nil，尝试根据格式判断
	if s.orderRepository == nil {
		// 🔥 修复：更准确的订单号格式判断
		return s.classifyOrderNumberByFormat(orderNo)
	}

	// 🚀 使用智能查询服务查询订单记录
	smartFinder := NewSmartOrderFinder(s.orderRepository, s.logger)
	order, err := smartFinder.FindOrderByAnyIdentifier(ctx, orderNo, "")
	if err != nil {
		// 如果查不到，使用格式判断作为降级方案
		s.logger.Debug("智能查询服务获取订单号分类失败，使用格式判断降级",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return s.classifyOrderNumberByFormat(orderNo)
	}

	return order.CustomerOrderNo, order.PlatformOrderNo
}

// classifyOrderNumberByFormat 根据订单号格式进行分类
// 🔥 修复：更准确的订单号格式判断逻辑
func (s *DefaultBalanceService) classifyOrderNumberByFormat(orderNo string) (customerOrderNo, platformOrderNo string) {
	if orderNo == "" {
		return "", ""
	}

	// 🔥 修复1：平台订单号格式 - GK开头的为平台订单号
	if len(orderNo) >= 3 && orderNo[:2] == "GK" {
		return "", orderNo // platformOrderNo
	}

	// 🔥 修复2：客户订单号格式判断
	// 包含下划线的数字格式（如 8820200_157）通常是客户订单号
	if strings.Contains(orderNo, "_") {
		// 进一步验证是否是数字_数字格式
		parts := strings.Split(orderNo, "_")
		if len(parts) == 2 {
			// 检查两部分是否都是数字
			if isNumeric(parts[0]) && isNumeric(parts[1]) {
				return orderNo, "" // customerOrderNo
			}
		}
	}

	// 🔥 修复3：其他已知的客户订单号格式
	if len(orderNo) >= 3 {
		prefix := orderNo[:2]
		if prefix == "yd" || prefix == "yt" || prefix == "kd" {
			return orderNo, "" // customerOrderNo
		}
	}

	// 🔥 修复4：纯数字格式的进一步判断
	if isNumeric(orderNo) {
		// 根据长度判断：通常平台订单号更长
		if len(orderNo) >= 15 {
			return "", orderNo // 可能是平台订单号
		} else {
			return orderNo, "" // 可能是客户订单号
		}
	}

	// 🔥 默认情况：无法确定格式，返回为客户订单号（更安全的默认值）
	s.logger.Warn("无法确定订单号格式，默认作为客户订单号处理",
		zap.String("order_no", orderNo))
	return orderNo, ""
}

// isNumeric 检查字符串是否为纯数字
func isNumeric(s string) bool {
	if s == "" {
		return false
	}
	for _, r := range s {
		if r < '0' || r > '9' {
			return false
		}
	}
	return true
}

// GetTransactionHistoryWithFilters 获取交易历史和总数（带筛选）
func (s *DefaultBalanceService) GetTransactionHistoryWithFilters(ctx context.Context, userID string, limit, offset int, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo string) ([]*model.TransactionResponse, int64, error) {
	// 获取交易记录
	transactions, err := s.repository.GetTransactionsByUserIDWithFilters(ctx, userID, limit, offset, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo)
	if err != nil {
		return nil, 0, fmt.Errorf("获取交易历史失败: %w", err)
	}

	// 获取总数
	count, err := s.repository.GetTransactionCountByUserIDWithFilters(ctx, userID, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo)
	if err != nil {
		return nil, 0, fmt.Errorf("获取交易记录总数失败: %w", err)
	}

	var responses []*model.TransactionResponse
	for _, tx := range transactions {
		// 🔥 企业级修复：优先从metadata中提取订单信息，然后再从数据库查询
		var trackingNo, customerOrderNo, platformOrderNo string

		// 1. 尝试从metadata中提取信息
		if tx.Metadata != nil {
			if originalOrderNo, ok := tx.Metadata["original_order_no"].(string); ok {
				platformOrderNo = originalOrderNo
			}
			if custOrderNo, ok := tx.Metadata["customer_order_no"].(string); ok {
				customerOrderNo = custOrderNo
			}
			if tNo, ok := tx.Metadata["tracking_no"].(string); ok {
				trackingNo = tNo
			}
		}

		// 2. 如果metadata中没有信息，则从数据库查询
		if customerOrderNo == "" || platformOrderNo == "" || trackingNo == "" {
			dbTrackingNo := s.getTrackingNoByOrderNo(ctx, tx.OrderNo)
			dbCustomerOrderNo, dbPlatformOrderNo := s.getOrderNumbers(ctx, tx.OrderNo)

			if trackingNo == "" {
				trackingNo = dbTrackingNo
			}
			if customerOrderNo == "" {
				customerOrderNo = dbCustomerOrderNo
			}
			if platformOrderNo == "" {
				platformOrderNo = dbPlatformOrderNo
			}
		}

		// 3. 如果还是没有平台订单号，使用原始订单号
		if platformOrderNo == "" {
			platformOrderNo = tx.OrderNo
		}

		responses = append(responses, &model.TransactionResponse{
			ID:            tx.ID,
			UserID:        tx.UserID,
			Type:          tx.Type,
			Amount:        tx.Amount,
			Currency:      tx.Currency,
			BalanceBefore: tx.BalanceBefore,
			BalanceAfter:  tx.BalanceAfter,

			// 订单关联信息（增强）
			OrderNo:         tx.OrderNo,      // 供应商订单号（保持向后兼容）
			PlatformOrderNo: platformOrderNo, // 平台订单号
			CustomerOrderNo: customerOrderNo, // 客户订单号
			TrackingNo:      trackingNo,      // 运单号

			// 交易分类信息（新增）
			TransactionCategory: tx.Category,
			TransactionSubType:  tx.SubType,

			// 描述信息（增强）
			ReferenceID:       tx.ReferenceID,
			Description:       tx.Description,
			DetailDescription: tx.DetailDescription,
			UserFriendlyDesc:  tx.UserFriendlyDesc,

			// 业务上下文（新增）
			Metadata:             tx.Metadata,
			BusinessContext:      tx.BusinessContext,
			RelatedTransactionID: tx.RelatedTransactionID,

			// 操作信息 - 🔥 修复：添加缺失的字段映射
			OperatorID: tx.OperatorID,
			Status:     tx.Status,
			CreatedAt:  tx.CreatedAt,
		})
	}

	return responses, count, nil
}

// GetTransactionByID 根据ID获取交易
func (s *DefaultBalanceService) GetTransactionByID(ctx context.Context, id string) (*model.TransactionResponse, error) {
	tx, err := s.repository.GetTransactionByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("获取交易记录失败: %w", err)
	}

	trackingNo := s.getTrackingNoByOrderNo(ctx, tx.OrderNo)
	return &model.TransactionResponse{
		ID:            tx.ID,
		UserID:        tx.UserID,
		Type:          tx.Type,
		Amount:        tx.Amount,
		Currency:      tx.Currency,
		BalanceBefore: tx.BalanceBefore,
		BalanceAfter:  tx.BalanceAfter,

		// 订单关联信息
		OrderNo:         tx.OrderNo,
		CustomerOrderNo: tx.CustomerOrderNo,
		TrackingNo:      trackingNo,

		// 交易分类信息
		TransactionCategory: tx.Category,
		TransactionSubType:  tx.SubType,

		// 描述信息
		ReferenceID:       tx.ReferenceID,
		Description:       tx.Description,
		DetailDescription: tx.DetailDescription,
		UserFriendlyDesc:  tx.UserFriendlyDesc,

		// 业务上下文
		Metadata:             tx.Metadata,
		BusinessContext:      tx.BusinessContext,
		RelatedTransactionID: tx.RelatedTransactionID,

		// 操作信息 - 🔥 修复：添加缺失的字段映射
		OperatorID: tx.OperatorID,
		Status:     tx.Status,
		CreatedAt:  tx.CreatedAt,
	}, nil
}

// GetTransactionByOrderNo 根据订单号获取交易记录
func (s *DefaultBalanceService) GetTransactionByOrderNo(ctx context.Context, orderNo string) (*model.TransactionResponse, error) {
	tx, err := s.repository.GetTransactionByOrderNo(ctx, orderNo)
	if err != nil {
		return nil, fmt.Errorf("获取订单交易记录失败: %w", err)
	}

	trackingNo := s.getTrackingNoByOrderNo(ctx, tx.OrderNo)
	return &model.TransactionResponse{
		ID:            tx.ID,
		UserID:        tx.UserID,
		Type:          tx.Type,
		Amount:        tx.Amount,
		Currency:      tx.Currency,
		BalanceBefore: tx.BalanceBefore,
		BalanceAfter:  tx.BalanceAfter,

		// 订单关联信息
		OrderNo:         tx.OrderNo,
		CustomerOrderNo: tx.CustomerOrderNo,
		TrackingNo:      trackingNo,

		// 交易分类信息
		TransactionCategory: tx.Category,
		TransactionSubType:  tx.SubType,

		// 描述信息
		ReferenceID:       tx.ReferenceID,
		Description:       tx.Description,
		DetailDescription: tx.DetailDescription,
		UserFriendlyDesc:  tx.UserFriendlyDesc,

		// 业务上下文
		Metadata:             tx.Metadata,
		BusinessContext:      tx.BusinessContext,
		RelatedTransactionID: tx.RelatedTransactionID,

		// 操作信息
	}, nil
}

// GetDepositHistory 获取充值历史
func (s *DefaultBalanceService) GetDepositHistory(ctx context.Context, userID string, limit, offset int) ([]*model.Deposit, error) {
	return s.repository.GetDepositsByUserID(ctx, userID, limit, offset)
}

// GetPaymentHistory 获取支付历史
func (s *DefaultBalanceService) GetPaymentHistory(ctx context.Context, userID string, limit, offset int) ([]*model.OrderPayment, error) {
	return s.repository.GetOrderPaymentsByUserID(ctx, userID, limit, offset)
}

// ProcessPaymentCallback 处理支付回调
func (s *DefaultBalanceService) ProcessPaymentCallback(ctx context.Context, depositID string, success bool, transactionID string) error {
	// 获取充值记录
	deposit, err := s.repository.GetDepositByID(ctx, depositID)
	if err != nil {
		return fmt.Errorf("获取充值记录失败: %w", err)
	}

	if success {
		// 支付成功，处理充值
		_, err := s.processDeposit(ctx, deposit, deposit.UserID)
		if err != nil {
			return fmt.Errorf("处理充值失败: %w", err)
		}
	} else {
		// 支付失败，更新状态
		deposit.Status = model.TransactionStatusFailed
		deposit.TransactionID = transactionID
		if err := s.repository.UpdateDeposit(ctx, deposit); err != nil {
			return fmt.Errorf("更新充值记录失败: %w", err)
		}
	}

	return nil
}

// GetBalanceHistory 获取余额历史记录
func (s *DefaultBalanceService) GetBalanceHistory(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, error) {
	transactions, err := s.repository.GetTransactionsByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("获取交易记录失败: %w", err)
	}

	var responses []*model.TransactionResponse
	for _, tx := range transactions {
		responses = append(responses, &model.TransactionResponse{
			ID:            tx.ID,
			UserID:        tx.UserID,
			Type:          tx.Type,
			Amount:        tx.Amount,
			Currency:      tx.Currency,
			BalanceBefore: tx.BalanceBefore,
			BalanceAfter:  tx.BalanceAfter,

			// 订单关联信息
			OrderNo:         tx.OrderNo,
			CustomerOrderNo: tx.CustomerOrderNo,
			TrackingNo:      tx.TrackingNo,

			// 交易分类信息
			TransactionCategory: tx.Category,
			TransactionSubType:  tx.SubType,

			// 描述信息
			ReferenceID:       tx.ReferenceID,
			Description:       tx.Description,
			DetailDescription: tx.DetailDescription,
			UserFriendlyDesc:  tx.UserFriendlyDesc,

			// 业务上下文
			Metadata:             tx.Metadata,
			BusinessContext:      tx.BusinessContext,
			RelatedTransactionID: tx.RelatedTransactionID,

			// 操作信息
		})
	}

	return responses, nil
}

// PreChargeForOrderWithDetails 为订单预收费用（推荐方法） - 传入完整订单信息
// 🚀 性能优化：避免数据库查询，直接使用传入的订单信息
func (s *DefaultBalanceService) PreChargeForOrderWithDetails(ctx context.Context, userID, customerOrderNo, platformOrderNo string, amount decimal.Decimal) error {
	if userID == "" {
		return ErrInvalidUserID
	}
	if customerOrderNo == "" {
		return fmt.Errorf("客户订单号不能为空")
	}
	if amount.LessThanOrEqual(decimal.Zero) {
		return ErrInvalidAmount
	}

	// 确保余额账户存在
	if err := s.CreateBalanceIfNotExists(ctx, userID); err != nil {
		return fmt.Errorf("创建余额账户失败: %w", err)
	}

	// 🔥 性能优化：直接使用传入的订单信息，无需数据库查询
	return s.executePreChargeLogicWithDetails(ctx, userID, customerOrderNo, platformOrderNo, amount)
}

// PreChargeForOrder 为订单直接扣费（不再使用冻结逻辑）
// 🚨 紧急修复：移除嵌套事务，避免下单失败不退钱的严重问题
func (s *DefaultBalanceService) PreChargeForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	if amount.LessThanOrEqual(decimal.Zero) {
		return ErrInvalidAmount
	}

	// 确保余额账户存在
	if err := s.CreateBalanceIfNotExists(ctx, userID); err != nil {
		return fmt.Errorf("创建余额账户失败: %w", err)
	}

	// 检查是否已经扣费
	existingTx, err := s.repository.GetTransactionByOrderNo(ctx, orderNo)
	if err == nil && existingTx != nil && existingTx.Type == model.TransactionTypeOrderPreCharge {
		s.logger.Warn("订单已扣费，跳过重复操作",
			zap.String("order_no", orderNo),
			zap.String("user_id", userID))
		return nil
	}

	// 🚨 关键修复：不创建独立事务，直接在调用方事务中执行
	// 这样当订单创建失败时，余额扣费会随着外层事务一起回滚
	s.logger.Info("🔄 在外层事务中执行预扣费逻辑",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()))

	// 直接执行预收费用逻辑，不创建新事务
	if err := s.executePreChargeLogic(ctx, userID, orderNo, amount); err != nil {
		s.logger.Error("❌ 预扣费逻辑执行失败",
			zap.String("user_id", userID),
			zap.String("order_no", orderNo),
			zap.Error(err))
		return err
	}

	s.logger.Info("✅ 预扣费逻辑执行成功",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()))

	return nil
}

// executePreChargeLogicWithDetails 执行预收费用的核心逻辑（优化版）
// 🚀 性能优化：直接使用传入的订单信息，避免数据库查询
func (s *DefaultBalanceService) executePreChargeLogicWithDetails(ctx context.Context, userID, customerOrderNo, platformOrderNo string, amount decimal.Decimal) error {
	// 获取当前余额
	balance, err := s.repository.GetBalance(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取余额失败: %w", err)
	}

	// 检查余额是否足够（直接扣费，不考虑冻结）
	if balance.Balance.LessThan(amount) {
		return ErrInsufficientBalance
	}

	// 计算新的余额
	newBalance := balance.Balance.Sub(amount)

	// 🚀 性能优化：直接使用传入的订单信息，无需查询
	// 确保客户订单号不为空
	if customerOrderNo == "" {
		return fmt.Errorf("客户订单号不能为空")
	}

	// 生成智能描述
	shortDesc := fmt.Sprintf("订单%s下单预收 %s%s", customerOrderNo, amount.String(), "CNY")
	detailDesc := fmt.Sprintf("订单%s下单时预收运费%s%s", customerOrderNo, amount.String(), "CNY")
	friendlyDesc := fmt.Sprintf("订单%s预收费用%s元", customerOrderNo, amount.String())

	// 创建交易记录
	transaction := &model.BalanceTransaction{
		UserID:            userID,
		Type:              model.TransactionTypeOrderPreCharge,
		Amount:            amount.Neg(), // 负数表示扣费
		Currency:          "CNY",
		BalanceBefore:     balance.Balance,
		BalanceAfter:      newBalance,
		OrderNo:           customerOrderNo,
		PlatformOrderNo:   platformOrderNo, // 🔥 优化：直接使用传入的平台订单号
		CustomerOrderNo:   customerOrderNo, // 🔥 优化：直接使用传入的客户订单号
		Description:       shortDesc,
		DetailDescription: detailDesc,
		UserFriendlyDesc:  friendlyDesc,
		BusinessContext: map[string]interface{}{
			"transaction_time": util.NowBeijing(),
			"charge_type":      "order_pre_charge",
			"order_info": map[string]interface{}{
				"customer_order_no": customerOrderNo,
				"platform_order_no": platformOrderNo, // 🔥 优化：使用正确的平台订单号
				"amount":            amount.String(),
				"currency":          "CNY",
			},
		},
		OperatorID: userID,
		Status:     model.TransactionStatusCompleted, // 🔥 修复：设置交易状态为已完成
	}

	// 保存交易记录
	if err := s.repository.CreateTransaction(ctx, transaction); err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}

	// 🔥 修复：更新余额时增加重试机制处理版本冲突
	// 🚨 关键修复：预扣费时传入负数，确保减少余额
	if err := s.updateBalanceWithRetryLogic(ctx, userID, amount.Neg(), 3); err != nil {
		return fmt.Errorf("更新余额失败: %w", err)
	}

	s.logger.Info("订单扣费成功（优化版）",
		zap.String("user_id", userID),
		zap.String("customer_order_no", customerOrderNo),
		zap.String("platform_order_no", platformOrderNo),
		zap.String("amount", amount.String()),
		zap.String("new_balance", newBalance.String()))

	return nil
}

// executePreChargeLogic 执行预收费用的核心逻辑
func (s *DefaultBalanceService) executePreChargeLogic(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {

	// 获取当前余额
	balance, err := s.repository.GetBalance(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取余额失败: %w", err)
	}

	// 检查余额是否足够（直接扣费，不考虑冻结）
	if balance.Balance.LessThan(amount) {
		return ErrInsufficientBalance
	}

	// 计算新的余额
	newBalance := balance.Balance.Sub(amount)

	// 获取客户订单号和平台订单号用于描述
	customerOrderNo, platformOrderNo := s.getOrderNumbers(ctx, orderNo)
	if customerOrderNo == "" {
		customerOrderNo = orderNo // 降级方案
	}

	// 🔥 使用智能查询服务获取完整的订单标识（如果没有获取到真正的平台订单号）
	if (platformOrderNo == "" || platformOrderNo == orderNo || platformOrderNo == customerOrderNo) && s.orderRepository != nil {
		// 🚀 使用智能查询服务通过客户订单号查询订单信息
		smartFinder := NewSmartOrderFinder(s.orderRepository, s.logger)
		if orderRecord, err := smartFinder.FindOrderByAnyIdentifier(ctx, customerOrderNo, ""); err == nil && orderRecord != nil && orderRecord.PlatformOrderNo != "" {
			platformOrderNo = orderRecord.PlatformOrderNo
			s.logger.Debug("智能查询服务通过客户订单号查询到平台订单号",
				zap.String("customer_order_no", customerOrderNo),
				zap.String("platform_order_no", platformOrderNo))
		}
	}

	// 最后的降级方案
	if platformOrderNo == "" {
		platformOrderNo = orderNo
	}

	// 🔥 使用描述生成器创建交易记录上下文
	txContext := TransactionContext{
		Amount:          amount,
		Currency:        "CNY",
		OrderNo:         orderNo,
		PlatformOrderNo: platformOrderNo, // 🔥 新增：设置平台订单号
		CustomerOrderNo: customerOrderNo,
		Timestamp:       util.NowBeijing(),
	}

	// 生成智能描述
	shortDesc, detailDesc, friendlyDesc := s.descriptionGenerator.GenerateDescriptions(
		model.TransactionTypeOrderPreCharge, txContext,
	)

	// 🔥 彻底重构：创建扣费交易记录，使用新的9种核心类型
	// 🚨 关键修复：扣费时amount应该为负数
	chargeAmount := amount.Neg() // 扣费为负数

	transaction := &model.BalanceTransaction{
		UserID:            userID,
		Type:              model.TransactionTypeOrderPreCharge, // 🔥 修复：下单时应该使用预收类型
		Amount:            chargeAmount,                        // 🚨 修复：扣费为负数
		Currency:          "CNY",
		BalanceBefore:     balance.Balance,
		BalanceAfter:      newBalance,
		OrderNo:           orderNo,         // 供应商订单号
		PlatformOrderNo:   platformOrderNo, // 🔥 新增：设置平台订单号
		CustomerOrderNo:   customerOrderNo, // 🔥 新增：设置客户订单号
		Description:       shortDesc,
		DetailDescription: detailDesc,
		UserFriendlyDesc:  friendlyDesc,
		BusinessContext: map[string]interface{}{
			"transaction_time": util.NowBeijing(),
			"charge_type":      "order_pre_charge",
			"order_info": map[string]interface{}{
				"customer_order_no": customerOrderNo,
				"platform_order_no": platformOrderNo, // 🔥 修复：使用正确的平台订单号
				"amount":            amount.String(),
				"currency":          "CNY",
			},
		},
		OperatorID: userID,
		Status:     model.TransactionStatusCompleted, // 🔥 修复：设置交易状态为已完成
	}

	// 保存交易记录
	if err := s.repository.CreateTransaction(ctx, transaction); err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}

	// 🔥 修复：更新余额时增加重试机制处理版本冲突
	// 🚨 关键修复：预扣费时传入负数，确保减少余额
	if err := s.updateBalanceWithRetryLogic(ctx, userID, amount.Neg(), 3); err != nil {
		return fmt.Errorf("更新余额失败: %w", err)
	}

	s.logger.Info("订单扣费成功",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()),
		zap.String("new_balance", newBalance.String()))

	return nil
}

// AutoRefundFailedOrder 自动退款失败的订单 - 企业级数据修复方法
// 🔥 关键修复：当订单创建失败但预收费用已扣除时，自动执行退款
func (s *DefaultBalanceService) AutoRefundFailedOrder(ctx context.Context, userID, orderNo string) error {
	s.logger.Info("🔍 开始自动退款失败订单",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo))

	// 1. 查找预收费用交易记录
	preChargeTx, err := s.repository.GetPreChargeTransactionByOrderNo(ctx, orderNo)
	if err != nil {
		s.logger.Error("❌ 未找到预收费用记录",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return fmt.Errorf("未找到预收费用记录: %w", err)
	}

	// 2. 检查是否已经有退款记录
	existingRefunds, err := s.repository.GetTransactionsByUserIDWithFilters(
		ctx, userID, 10, 0,
		string(model.TransactionTypeOrderCancelRefund), "", "", "", "", orderNo, "",
	)
	if err == nil && len(existingRefunds) > 0 {
		s.logger.Info("⚠️ 订单已有退款记录，跳过自动退款",
			zap.String("order_no", orderNo),
			zap.String("existing_refund_id", existingRefunds[0].ID))
		return nil
	}

	// 3. 执行退款
	refundAmount := preChargeTx.Amount
	if err := s.RefundForOrder(ctx, userID, orderNo, refundAmount); err != nil {
		s.logger.Error("❌ 自动退款失败",
			zap.String("order_no", orderNo),
			zap.String("refund_amount", refundAmount.String()),
			zap.Error(err))
		return fmt.Errorf("自动退款失败: %w", err)
	}

	s.logger.Info("✅ 失败订单自动退款成功",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("refund_amount", refundAmount.String()))

	return nil
}

// ChargeForBillingDifference 费用差额补收 - 专门用于回调费用差额处理
func (s *DefaultBalanceService) ChargeForBillingDifference(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	if amount.LessThanOrEqual(decimal.Zero) {
		return ErrInvalidAmount
	}

	// 确保余额账户存在
	if err := s.CreateBalanceIfNotExists(ctx, userID); err != nil {
		return fmt.Errorf("创建余额账户失败: %w", err)
	}

	s.logger.Info("🔄 执行费用差额补收",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()))

	// 执行费用差额补收逻辑
	if err := s.executeBillingDifferenceLogic(ctx, userID, orderNo, amount); err != nil {
		s.logger.Error("❌ 费用差额补收失败",
			zap.String("user_id", userID),
			zap.String("order_no", orderNo),
			zap.Error(err))
		return err
	}

	s.logger.Info("✅ 费用差额补收成功",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()))

	return nil
}

// executeBillingDifferenceLogic 执行费用差额补收的核心逻辑
func (s *DefaultBalanceService) executeBillingDifferenceLogic(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	// 获取当前余额
	balance, err := s.repository.GetBalance(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取余额失败: %w", err)
	}

	// 检查余额是否足够
	if balance.Balance.LessThan(amount) {
		return ErrInsufficientBalance
	}

	// 计算新的余额
	newBalance := balance.Balance.Sub(amount)

	// 获取客户订单号和平台订单号用于描述
	customerOrderNo, platformOrderNo := s.getOrderNumbers(ctx, orderNo)
	if customerOrderNo == "" {
		customerOrderNo = orderNo // 降级方案
	}

	// 🔥 使用智能查询服务获取完整的订单标识（如果没有获取到真正的平台订单号）
	if (platformOrderNo == "" || platformOrderNo == orderNo || platformOrderNo == customerOrderNo) && s.orderRepository != nil {
		// 🚀 使用智能查询服务通过客户订单号查询订单信息
		smartFinder := NewSmartOrderFinder(s.orderRepository, s.logger)
		if orderRecord, err := smartFinder.FindOrderByAnyIdentifier(ctx, customerOrderNo, ""); err == nil && orderRecord != nil && orderRecord.PlatformOrderNo != "" {
			platformOrderNo = orderRecord.PlatformOrderNo
		}
	}

	// 最后的降级方案
	if platformOrderNo == "" {
		platformOrderNo = orderNo
	}

	// 🔥 使用描述生成器创建交易记录上下文
	txContext := TransactionContext{
		Amount:          amount,
		Currency:        "CNY",
		OrderNo:         orderNo,
		PlatformOrderNo: platformOrderNo, // 🔥 新增：设置平台订单号
		CustomerOrderNo: customerOrderNo,
		Timestamp:       util.NowBeijing(),
	}

	// 生成智能描述
	shortDesc, detailDesc, friendlyDesc := s.descriptionGenerator.GenerateDescriptions(
		model.TransactionTypeBillingDifference, txContext,
	)

	// 🔥 创建费用差额补收交易记录
	chargeAmount := amount.Neg() // 扣费为负数

	transaction := &model.BalanceTransaction{
		UserID:            userID,
		Type:              model.TransactionTypeBillingDifference, // 🔥 关键：使用费用差额补收类型
		Amount:            chargeAmount,
		Currency:          "CNY",
		BalanceBefore:     balance.Balance,
		BalanceAfter:      newBalance,
		OrderNo:           orderNo,         // 供应商订单号
		PlatformOrderNo:   platformOrderNo, // 🔥 新增：设置平台订单号
		CustomerOrderNo:   customerOrderNo, // 客户订单号
		Description:       shortDesc,
		DetailDescription: detailDesc,
		UserFriendlyDesc:  friendlyDesc,
		Status:            model.TransactionStatusCompleted, // 🔥 修复：设置交易状态为已完成
		BusinessContext: map[string]interface{}{
			"transaction_time": util.NowBeijing(),
			"charge_type":      "billing_difference",
			"order_info": map[string]interface{}{
				"customer_order_no": customerOrderNo,
				"platform_order_no": platformOrderNo, // 🔥 修复：使用正确的平台订单号
				"amount":            amount.String(),
				"currency":          "CNY",
			},
		},
		OperatorID: "system",
	}

	// 保存交易记录
	if err := s.repository.CreateTransaction(ctx, transaction); err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}

	// 更新余额
	if err := s.repository.UpdateBalanceWithVersion(ctx, userID, newBalance, decimal.Zero, balance.Version); err != nil {
		return fmt.Errorf("更新余额失败: %w", err)
	}

	return nil
}

// RefundForOrder 为订单退款 - 企业级生产环境标准实现
// 🚨 关键修复：增加严格的安全检查，防止重复退款和异常退款
// 🔥 重要修复：统一使用客户订单号，确保扣费和退费记录能够正确关联
func (s *DefaultBalanceService) RefundForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	s.logger.Info("🔍 开始订单退款处理 [企业级标准]",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("refund_amount", amount.String()))

	// 🚨 企业级验证1：金额有效性检查
	if amount.LessThanOrEqual(decimal.Zero) {
		// 🔥 企业级修复：对于0金额，记录信息但不报错，这是正常的重复退款防护
		if amount.IsZero() {
			s.logger.Info("✅ 退款金额为0，订单无需退款",
				zap.String("order_no", orderNo),
				zap.String("amount", amount.String()),
				zap.String("reason", "订单净支付金额为0，无需退款"))
			return nil // 返回nil表示成功，无需退款
		}

		// 负数金额仍然报错
		s.logger.Error("❌ 退款金额无效",
			zap.String("order_no", orderNo),
			zap.String("amount", amount.String()))
		return ErrInvalidAmount
	}

	// 🔥 核心修复：获取客户订单号，确保与扣费记录使用相同的订单号
	customerOrderNo, platformOrderNo := s.getOrderNumbers(ctx, orderNo)

	// 确定用于交易记录的订单号：优先使用客户订单号
	transactionOrderNo := customerOrderNo
	if transactionOrderNo == "" {
		transactionOrderNo = orderNo // 降级方案
	}

	s.logger.Info("🔍 订单号解析结果",
		zap.String("input_order_no", orderNo),
		zap.String("customer_order_no", customerOrderNo),
		zap.String("platform_order_no", platformOrderNo),
		zap.String("transaction_order_no", transactionOrderNo))

	// 🚨 企业级验证2：计算订单实际净支付金额
	// 🔥 修复：获取订单的客户订单号，确保查询条件正确
	var refundCustomerOrderNo string
	if s.orderRepository != nil {
		order, err := s.orderRepository.FindByOrderNo(ctx, orderNo)
		if err == nil && order != nil {
			refundCustomerOrderNo = order.CustomerOrderNo
		}
	}

	actualNetPayment, err := s.GetOrderNetPayment(ctx, userID, orderNo, refundCustomerOrderNo)
	if err != nil {
		s.logger.Error("❌ 获取订单净支付金额失败",
			zap.String("order_no", orderNo),
			zap.String("user_id", userID),
			zap.String("customer_order_no", refundCustomerOrderNo),
			zap.Error(err))
		return fmt.Errorf("获取订单净支付金额失败: %w", err)
	}

	// 🚨 企业级验证3：退款金额不能超过实际支付金额
	if amount.GreaterThan(actualNetPayment) {
		s.logger.Error("❌ 退款金额超过实际支付金额，拒绝退款",
			zap.String("order_no", orderNo),
			zap.String("user_id", userID),
			zap.String("refund_amount", amount.String()),
			zap.String("actual_net_payment", actualNetPayment.String()),
			zap.String("excess_amount", amount.Sub(actualNetPayment).String()))
		amountFloat, _ := amount.Float64()
		netPaymentFloat, _ := actualNetPayment.Float64()
		return fmt.Errorf("退款金额(%.2f)超过实际支付金额(%.2f)，拒绝退款",
			amountFloat, netPaymentFloat)
	}

	// 🚨 企业级验证4：检查是否已经退款
	// 🔥 修复：使用客户订单号查询已有退款记录，确保能找到所有相关记录
	existingRefunds, err := s.repository.GetTransactionsByUserIDWithFilters(
		ctx, userID, 100, 0,
		"", "", "", "", customerOrderNo, transactionOrderNo, "",
	)
	if err != nil {
		s.logger.Error("❌ 查询现有退款记录失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return fmt.Errorf("查询现有退款记录失败: %w", err)
	}

	var totalExistingRefunds decimal.Decimal = decimal.Zero
	for _, tx := range existingRefunds {
		if tx.Type == model.TransactionTypeOrderCancelRefund {
			totalExistingRefunds = totalExistingRefunds.Add(tx.Amount)
		}
	}

	// 检查总退款金额是否会超过实际支付金额
	totalRefundAfter := totalExistingRefunds.Add(amount)
	if totalRefundAfter.GreaterThan(actualNetPayment) {
		s.logger.Error("❌ 累计退款金额将超过实际支付金额，拒绝退款",
			zap.String("order_no", orderNo),
			zap.String("user_id", userID),
			zap.String("current_refund", amount.String()),
			zap.String("existing_refunds", totalExistingRefunds.String()),
			zap.String("total_refund_after", totalRefundAfter.String()),
			zap.String("actual_net_payment", actualNetPayment.String()))
		totalRefundFloat, _ := totalRefundAfter.Float64()
		netPaymentFloat2, _ := actualNetPayment.Float64()
		return fmt.Errorf("累计退款金额(%.2f)将超过实际支付金额(%.2f)，拒绝退款",
			totalRefundFloat, netPaymentFloat2)
	}

	// 如果已有退款且金额相等，跳过重复操作
	if totalExistingRefunds.Equal(amount) {
		s.logger.Warn("⚠️ 订单已退款相同金额，跳过重复操作",
			zap.String("order_no", orderNo),
			zap.String("user_id", userID),
			zap.String("existing_refund_amount", totalExistingRefunds.String()))
		return nil
	}

	// 🚨 企业级事务处理：开始数据库事务
	s.logger.Info("🔄 开始退款事务处理",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("refund_amount", amount.String()))

	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		s.logger.Error("❌ 开始事务失败", zap.Error(err))
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 获取当前余额（使用乐观锁）
	balance, err := s.repository.GetBalance(ctx, userID)
	if err != nil {
		s.logger.Error("❌ 获取用户余额失败",
			zap.String("user_id", userID),
			zap.Error(err))
		return fmt.Errorf("获取余额失败: %w", err)
	}

	// 计算退款后余额
	newBalance := balance.Balance.Add(amount)

	s.logger.Info("💰 余额计算完成",
		zap.String("user_id", userID),
		zap.String("balance_before", balance.Balance.String()),
		zap.String("refund_amount", amount.String()),
		zap.String("balance_after", newBalance.String()),
		zap.Int64("balance_version", balance.Version))

	// 客户订单号已在前面获取，这里不需要重复获取

	// 🚨 企业级交易记录：创建详细的退款记录
	// 🔥 核心修复：使用客户订单号作为主要订单号，确保与扣费记录一致
	transaction := &model.BalanceTransaction{
		UserID:          userID,
		Type:            model.TransactionTypeOrderCancelRefund,
		Amount:          amount,
		Currency:        "CNY",
		BalanceBefore:   balance.Balance,
		BalanceAfter:    newBalance,
		OrderNo:         transactionOrderNo, // 🔥 修复：使用统一的客户订单号
		CustomerOrderNo: customerOrderNo,
		Description:     fmt.Sprintf("订单 %s 取消退款 [企业级安全退款]", customerOrderNo),
		OperatorID:      "system_refund",
		Status:          model.TransactionStatusCompleted, // 🔥 修复：设置交易状态为已完成
	}

	// 保存交易记录
	if err := s.repository.CreateTransaction(ctx, transaction); err != nil {
		s.logger.Error("❌ 创建退款交易记录失败",
			zap.String("user_id", userID),
			zap.String("order_no", orderNo),
			zap.Error(err))
		return fmt.Errorf("创建交易记录失败: %w", err)
	}

	s.logger.Info("✅ 退款交易记录创建成功",
		zap.String("transaction_id", transaction.ID),
		zap.String("user_id", userID),
		zap.String("transaction_order_no", transactionOrderNo),
		zap.String("customer_order_no", customerOrderNo),
		zap.String("platform_order_no", platformOrderNo))

	// 🚨 企业级余额更新：使用乐观锁确保数据一致性
	if err := s.repository.UpdateBalanceWithVersion(ctx, userID, newBalance, decimal.Zero, balance.Version); err != nil {
		s.logger.Error("❌ 更新用户余额失败",
			zap.String("user_id", userID),
			zap.String("new_balance", newBalance.String()),
			zap.Int64("version", balance.Version),
			zap.Error(err))
		return fmt.Errorf("更新余额失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		s.logger.Error("❌ 提交退款事务失败",
			zap.String("user_id", userID),
			zap.String("order_no", orderNo),
			zap.Error(err))
		return fmt.Errorf("提交事务失败: %w", err)
	}

	s.logger.Info("🎉 订单退款成功 [企业级标准]",
		zap.String("user_id", userID),
		zap.String("transaction_order_no", transactionOrderNo),
		zap.String("customer_order_no", customerOrderNo),
		zap.String("platform_order_no", platformOrderNo),
		zap.String("refund_amount", amount.String()),
		zap.String("balance_before", balance.Balance.String()),
		zap.String("balance_after", newBalance.String()),
		zap.String("transaction_id", transaction.ID))

	return nil
}

// RefundForBillingDifference 费用差额退款 - 专门用于回调费用差额退款处理
func (s *DefaultBalanceService) RefundForBillingDifference(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	if amount.LessThanOrEqual(decimal.Zero) {
		return ErrInvalidAmount
	}

	// 确保余额账户存在
	if err := s.CreateBalanceIfNotExists(ctx, userID); err != nil {
		return fmt.Errorf("创建余额账户失败: %w", err)
	}

	s.logger.Info("🔄 执行费用差额退款",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()))

	// 执行费用差额退款逻辑
	if err := s.executeBillingDifferenceRefundLogic(ctx, userID, orderNo, amount); err != nil {
		s.logger.Error("❌ 费用差额退款失败",
			zap.String("user_id", userID),
			zap.String("order_no", orderNo),
			zap.Error(err))
		return err
	}

	s.logger.Info("✅ 费用差额退款成功",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("amount", amount.String()))

	return nil
}

// executeBillingDifferenceRefundLogic 执行费用差额退款的核心逻辑
func (s *DefaultBalanceService) executeBillingDifferenceRefundLogic(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	// 获取当前余额
	balance, err := s.repository.GetBalance(ctx, userID)
	if err != nil {
		return fmt.Errorf("获取余额失败: %w", err)
	}

	// 计算退款后余额
	newBalance := balance.Balance.Add(amount)

	// 获取客户订单号和平台订单号用于描述
	customerOrderNo, platformOrderNo := s.getOrderNumbers(ctx, orderNo)
	if customerOrderNo == "" {
		customerOrderNo = orderNo // 降级方案
	}

	// 🔥 查询订单信息以获取完整的订单标识（如果没有获取到真正的平台订单号）
	if (platformOrderNo == "" || platformOrderNo == orderNo || platformOrderNo == customerOrderNo) && s.orderRepository != nil {
		// 尝试通过客户订单号查询订单信息
		if orderRecord, err := s.orderRepository.FindByCustomerOrderNo(ctx, customerOrderNo); err == nil && orderRecord != nil && orderRecord.PlatformOrderNo != "" {
			platformOrderNo = orderRecord.PlatformOrderNo
		}
	}

	// 最后的降级方案
	if platformOrderNo == "" {
		platformOrderNo = orderNo
	}

	// 🔥 使用描述生成器创建交易记录上下文
	txContext := TransactionContext{
		Amount:          amount,
		Currency:        "CNY",
		OrderNo:         orderNo,
		PlatformOrderNo: platformOrderNo, // 🔥 新增：设置平台订单号
		CustomerOrderNo: customerOrderNo,
		Timestamp:       util.NowBeijing(),
	}

	// 生成智能描述
	shortDesc, detailDesc, friendlyDesc := s.descriptionGenerator.GenerateDescriptions(
		model.TransactionTypeBillingDifferenceRefund, txContext,
	)

	// 🔥 创建费用差额退款交易记录
	transaction := &model.BalanceTransaction{
		UserID:            userID,
		Type:              model.TransactionTypeBillingDifferenceRefund, // 🔥 关键：使用费用差额退款类型
		Amount:            amount,
		Currency:          "CNY",
		BalanceBefore:     balance.Balance,
		BalanceAfter:      newBalance,
		OrderNo:           orderNo,
		CustomerOrderNo:   customerOrderNo,
		Description:       shortDesc,
		DetailDescription: detailDesc,
		UserFriendlyDesc:  friendlyDesc,
		Status:            model.TransactionStatusCompleted, // 🔥 修复：设置交易状态为已完成
		BusinessContext: map[string]interface{}{
			"transaction_time": util.NowBeijing(),
			"refund_type":      "billing_difference",
			"order_info": map[string]interface{}{
				"customer_order_no": customerOrderNo,
				"platform_order_no": platformOrderNo, // 🔥 修复：使用正确的平台订单号
				"amount":            amount.String(),
				"currency":          "CNY",
			},
		},
		OperatorID: "system",
	}

	// 保存交易记录
	if err := s.repository.CreateTransaction(ctx, transaction); err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}

	// 更新余额
	if err := s.repository.UpdateBalanceWithVersion(ctx, userID, newBalance, decimal.Zero, balance.Version); err != nil {
		return fmt.Errorf("更新余额失败: %w", err)
	}

	return nil
}

// GetOrderNetPayment 获取订单净支付金额 - 企业级生产环境标准实现
// 🚨 关键修复：防止重复计算，确保财务数据100%准确
func (s *DefaultBalanceService) GetOrderNetPayment(ctx context.Context, userID, orderNo, customerOrderNo string) (decimal.Decimal, error) {
	s.logger.Info("🔍 开始计算订单净支付金额 [企业级标准]",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("customer_order_no", customerOrderNo))

	// 🚨 企业级修复：使用精确的SQL查询，避免重复计算和错误匹配
	// 🔥 关键修复：通过订单记录查找正确的客户订单号，确保能匹配到交易记录
	var actualCustomerOrderNo string
	if s.orderRepository != nil {
		smartFinder := NewSmartOrderFinder(s.orderRepository, s.logger)
		order, err := smartFinder.FindOrderByAnyIdentifier(ctx, orderNo, "")
		if err == nil && order != nil {
			actualCustomerOrderNo = order.CustomerOrderNo
			s.logger.Debug("通过智能查询找到实际客户订单号",
				zap.String("input_order_no", orderNo),
				zap.String("actual_customer_order_no", actualCustomerOrderNo))
		}
	}

	// 如果没有找到，使用传入的客户订单号
	if actualCustomerOrderNo == "" {
		actualCustomerOrderNo = customerOrderNo
	}

	// 直接在数据库层面进行精确匹配，移除危险的LIKE查询
	query := `
		SELECT
			id, transaction_type, amount, order_no, customer_order_no,
			tracking_no, description, created_at
		FROM balance_transactions
		WHERE user_id = $1
		AND (
			-- 精确匹配订单号
			order_no = $2
			OR customer_order_no = $2
			-- 如果找到了实际客户订单号，也进行匹配
			OR (CASE WHEN $3 != '' THEN order_no = $3 OR customer_order_no = $3 ELSE FALSE END)
		)
		AND status = 'completed'
		ORDER BY created_at ASC
	`

	// 构建查询参数 - 使用实际找到的客户订单号
	customerOrderNoParam := actualCustomerOrderNo

	rows, err := s.db.QueryContext(ctx, query, userID, orderNo, customerOrderNoParam)
	if err != nil {
		s.logger.Error("查询订单交易记录失败",
			zap.String("user_id", userID),
			zap.String("order_no", orderNo),
			zap.Error(err))
		return decimal.Zero, fmt.Errorf("查询交易记录失败: %w", err)
	}
	defer rows.Close()

	// 解析交易记录
	var transactions []*model.BalanceTransaction
	transactionMap := make(map[string]*model.BalanceTransaction) // 使用map防止重复

	for rows.Next() {
		tx := &model.BalanceTransaction{}
		var amountStr string
		var orderNoPtr, customerOrderNoPtr, trackingNoPtr *string

		err := rows.Scan(
			&tx.ID,
			&tx.Type,
			&amountStr,
			&orderNoPtr,
			&customerOrderNoPtr,
			&trackingNoPtr,
			&tx.Description,
			&tx.CreatedAt,
		)
		if err != nil {
			s.logger.Error("扫描交易记录失败", zap.Error(err))
			return decimal.Zero, fmt.Errorf("扫描交易记录失败: %w", err)
		}

		// 转换金额
		tx.Amount, err = decimal.NewFromString(amountStr)
		if err != nil {
			s.logger.Error("转换金额失败",
				zap.String("amount_str", amountStr),
				zap.Error(err))
			return decimal.Zero, fmt.Errorf("转换金额失败: %w", err)
		}

		// 处理可空字段
		if orderNoPtr != nil {
			tx.OrderNo = *orderNoPtr
		}
		if customerOrderNoPtr != nil {
			tx.CustomerOrderNo = *customerOrderNoPtr
		}
		if trackingNoPtr != nil {
			tx.TrackingNo = *trackingNoPtr
		}

		// 🚨 关键修复：使用交易ID作为唯一键，防止重复计算
		if _, exists := transactionMap[tx.ID]; !exists {
			transactionMap[tx.ID] = tx
			transactions = append(transactions, tx)
		}
	}

	if err = rows.Err(); err != nil {
		s.logger.Error("遍历交易记录失败", zap.Error(err))
		return decimal.Zero, fmt.Errorf("遍历交易记录失败: %w", err)
	}

	// 🚨 企业级计算：分类统计，确保逻辑清晰
	var totalPaid decimal.Decimal = decimal.Zero
	var totalRefunded decimal.Decimal = decimal.Zero
	var paymentCount, refundCount int

	for _, tx := range transactions {
		s.logger.Debug("处理交易记录",
			zap.String("transaction_id", tx.ID),
			zap.String("type", string(tx.Type)),
			zap.String("amount", tx.Amount.String()),
			zap.String("order_no", tx.OrderNo),
			zap.String("description", tx.Description))

		switch tx.Type {
		case model.TransactionTypeOrderPreCharge,
			model.TransactionTypeBillingDifference,
			model.TransactionTypeOrderInterceptCharge,
			model.TransactionTypeReturnCharge,
			model.TransactionTypeOrderReviveRecharge:
			// 🔥 兼容历史补收交易类型（超重补收）
			// 🔥 关键修复：支付类型记录的金额是负数（扣费），需要取绝对值计算用户实际支付金额
			paidAmount := tx.Amount.Abs()
			totalPaid = totalPaid.Add(paidAmount)
			paymentCount++
			s.logger.Debug("✅ 支付记录",
				zap.String("transaction_id", tx.ID),
				zap.String("original_amount", tx.Amount.String()),
				zap.String("paid_amount", paidAmount.String()),
				zap.String("description", tx.Description))

		case model.TransactionTypeOrderCancelRefund,
			model.TransactionTypeBillingDifferenceRefund:
			// 退款类型：增加总退款金额
			totalRefunded = totalRefunded.Add(tx.Amount)
			refundCount++
			s.logger.Debug("💰 退款记录",
				zap.String("transaction_id", tx.ID),
				zap.String("amount", tx.Amount.String()),
				zap.String("description", tx.Description))

		// 🚨 重要：检查是否有已废弃的pickup_actual_charge交易（使用字符串比较）
		default:
			if string(tx.Type) == "pickup_actual_charge" {
				s.logger.Warn("⚠️ 发现已废弃的pickup_actual_charge交易，跳过统计",
					zap.String("transaction_id", tx.ID),
					zap.String("amount", tx.Amount.String()))
			} else {
				s.logger.Warn("⚠️ 未识别的交易类型",
					zap.String("transaction_id", tx.ID),
					zap.String("type", string(tx.Type)),
					zap.String("amount", tx.Amount.String()))
			}
		}
	}

	// 计算净支付金额 = 总支付 - 总退款
	netPayment := totalPaid.Sub(totalRefunded)

	// 🚨 企业级验证：检查计算结果的合理性
	if netPayment.LessThan(decimal.Zero) {
		s.logger.Warn("⚠️ 净支付金额为负数，可能存在异常",
			zap.String("user_id", userID),
			zap.String("order_no", orderNo),
			zap.String("net_payment", netPayment.String()),
			zap.String("total_paid", totalPaid.String()),
			zap.String("total_refunded", totalRefunded.String()))
	}

	s.logger.Info("✅ 订单净支付金额计算完成 [企业级标准]",
		zap.String("user_id", userID),
		zap.String("order_no", orderNo),
		zap.String("customer_order_no", customerOrderNo),
		zap.String("total_paid", totalPaid.String()),
		zap.String("total_refunded", totalRefunded.String()),
		zap.String("net_payment", netPayment.String()),
		zap.Int("total_transactions", len(transactions)),
		zap.Int("payment_count", paymentCount),
		zap.Int("refund_count", refundCount))

	return netPayment, nil
}

// CheckDuplicateWeightFeeTransaction 检查重复的重量费用交易 - 公开方法
func (s *DefaultBalanceService) CheckDuplicateWeightFeeTransaction(ctx context.Context, userID, orderNo, transactionType string, amount decimal.Decimal) (bool, error) {
	// 查询最近24小时内是否有相同的交易
	transactions, err := s.repository.GetTransactionsByUserIDWithFilters(
		ctx, userID, 50, 0, // 查询最近50条记录
		transactionType, "", "", "", "", orderNo, "", // 按交易类型和订单号筛选
	)
	if err != nil {
		return false, fmt.Errorf("查询交易记录失败: %w", err)
	}

	// 检查是否有相同金额的交易
	for _, tx := range transactions {
		if tx.Amount.Equal(amount) && tx.Status == model.TransactionStatusCompleted {
			// 检查时间间隔（24小时内认为是重复）
			if time.Since(tx.CreatedAt) < 24*time.Hour {
				s.logger.Warn("发现重复的重量费用交易",
					zap.String("user_id", userID),
					zap.String("order_no", orderNo),
					zap.String("transaction_type", transactionType),
					zap.String("amount", amount.String()),
					zap.String("existing_tx_id", tx.ID),
					zap.Time("existing_tx_time", tx.CreatedAt))
				return true, nil
			}
		}
	}

	return false, nil
}

// updateBalanceWithRetryLogic 带重试机制的余额更新逻辑
// 🔥 修复：处理余额版本冲突问题，增加重试机制
func (s *DefaultBalanceService) updateBalanceWithRetryLogic(ctx context.Context, userID string, changeAmount decimal.Decimal, maxRetries int) error {
	var err error

	for retryCount := 0; retryCount < maxRetries; retryCount++ {
		// 获取当前余额
		balance, getErr := s.repository.GetBalance(ctx, userID)
		if getErr != nil {
			return fmt.Errorf("获取余额失败: %w", getErr)
		}

		// 检查余额是否足够（如果是扣费操作）
		if changeAmount.IsNegative() && balance.Balance.LessThan(changeAmount.Abs()) {
			return ErrInsufficientBalance
		}

		// 计算新余额
		newBalance := balance.Balance.Add(changeAmount)

		// 尝试更新余额
		err = s.repository.UpdateBalanceWithVersion(ctx, userID, newBalance, decimal.Zero, balance.Version)
		if err == nil {
			// 更新成功
			s.logger.Info("余额更新成功",
				zap.String("user_id", userID),
				zap.String("change_amount", changeAmount.String()),
				zap.String("new_balance", newBalance.String()),
				zap.Int("retry_count", retryCount))
			return nil
		}

		// 检查是否是版本冲突错误
		if strings.Contains(err.Error(), "余额版本冲突") {
			// 版本冲突，准备重试
			s.logger.Warn("余额版本冲突，准备重试",
				zap.String("user_id", userID),
				zap.String("change_amount", changeAmount.String()),
				zap.Int("retry_count", retryCount+1),
				zap.Int("max_retries", maxRetries),
				zap.Int64("version", balance.Version))

			// 指数退避延迟后重试
			backoffMs := 50 * (1 << retryCount) // 50ms, 100ms, 200ms, 400ms...
			if backoffMs > 1000 {
				backoffMs = 1000 // 最大1秒
			}
			time.Sleep(time.Duration(backoffMs) * time.Millisecond)
			continue
		}

		// 其他错误直接返回
		return err
	}

	// 达到最大重试次数
	return fmt.Errorf("更新余额失败，达到最大重试次数(%d): %w", maxRetries, err)
}
