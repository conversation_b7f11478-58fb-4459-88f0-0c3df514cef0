package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"

)

// PriceValidationService 价格验证服务
type PriceValidationService struct {
	shippingFeeService ProviderShippingFeeServiceInterface
	logger             *zap.Logger
	
	// 缓存相关
	cache      map[string]*CachedPriceValidation
	cacheMutex sync.RWMutex
	cacheExpiry time.Duration
	
	// 并发控制
	maxConcurrency int
}

// CachedPriceValidation 缓存的价格验证结果
type CachedPriceValidation struct {
	ProviderPrice float64
	SystemPrice   float64
	ProfitStatus  string
	QueryStatus   string
	QueryTime     time.Time
	ErrorMessage  string
	Supported     bool
	ExpiryTime    time.Time
}

// NewPriceValidationService 创建价格验证服务
func NewPriceValidationService(
	shippingFeeService ProviderShippingFeeServiceInterface,
	logger *zap.Logger,
) *PriceValidationService {
	return &PriceValidationService{
		shippingFeeService: shippingFeeService,
		logger:             logger,
		cache:              make(map[string]*CachedPriceValidation),
		cacheExpiry:        1 * time.Hour, // 缓存1小时
		maxConcurrency:     3,             // 降低最大并发数，减少对供应商API的压力
	}
}

// ValidateOrderPrices 批量验证订单价格
func (s *PriceValidationService) ValidateOrderPrices(ctx context.Context, orders []*model.AdminOrderListItem) error {
	if len(orders) == 0 {
		return nil
	}

	// 智能过滤：只对需要查询的订单进行验证
	ordersToValidate := s.filterOrdersNeedingValidation(orders)

	if len(ordersToValidate) == 0 {
		s.logger.Debug("没有需要验证的订单，跳过价格验证")
		return nil
	}

	s.logger.Info("开始智能批量验证订单价格",
		zap.Int("total_orders", len(orders)),
		zap.Int("orders_to_validate", len(ordersToValidate)))

	// 使用并发控制
	semaphore := make(chan struct{}, s.maxConcurrency)
	var wg sync.WaitGroup

	for _, order := range ordersToValidate {
		wg.Add(1)
		go func(o *model.AdminOrderListItem) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 验证单个订单价格
			s.validateSingleOrderPrice(ctx, o)
		}(order)
	}

	// 等待所有验证完成
	wg.Wait()

	s.logger.Info("智能批量价格验证完成",
		zap.Int("total_orders", len(orders)),
		zap.Int("validated_orders", len(ordersToValidate)))

	return nil
}

// validateSingleOrderPrice 验证单个订单价格
func (s *PriceValidationService) validateSingleOrderPrice(ctx context.Context, order *model.AdminOrderListItem) {
	cacheKey := s.getCacheKey(order.ID)
	
	// 检查缓存
	if cached := s.getFromCache(cacheKey); cached != nil {
		s.logger.Debug("使用缓存的价格验证结果",
			zap.Int64("order_id", order.ID))
		
		order.PriceValidation.ProviderPrice = cached.ProviderPrice
		order.PriceValidation.SystemPrice = cached.SystemPrice
		order.PriceValidation.ProfitStatus = cached.ProfitStatus
		order.PriceValidation.QueryStatus = "cached"
		order.PriceValidation.QueryTime = cached.QueryTime.Format(time.RFC3339)
		order.PriceValidation.ErrorMessage = cached.ErrorMessage
		order.PriceValidation.Supported = cached.Supported
		return
	}

	// 设置初始状态
	order.PriceValidation.QueryStatus = "pending"
	order.PriceValidation.SystemPrice = order.Price
	order.PriceValidation.Supported = s.shippingFeeService.IsProviderSupported(order.Provider)

	if !order.PriceValidation.Supported {
		order.PriceValidation.QueryStatus = "failed"
		order.PriceValidation.ErrorMessage = fmt.Sprintf("供应商 %s 不支持价格查询", order.Provider)
		order.PriceValidation.ProfitStatus = "unknown"
		return
	}

	// 创建订单记录用于查询
	orderRecord := &model.OrderRecord{
		ID:          order.ID,
		OrderNo:     order.OrderNo,
		TrackingNo:  order.TrackingNo,
		Provider:    order.Provider,
		ExpressType: order.ExpressType,
		TaskId:      order.OrderNo, // 使用订单号作为TaskId
		Weight:      order.WeightVolume.OrderWeight,
		Price:       order.Price,
	}

	// 查询供应商价格
	detailedInfo, err := s.shippingFeeService.QueryShippingFee(ctx, orderRecord)
	if err != nil {
		s.logger.Error("查询供应商价格失败",
			zap.Int64("order_id", order.ID),
			zap.String("provider", order.Provider),
			zap.Error(err))
		
		order.PriceValidation.QueryStatus = "failed"
		order.PriceValidation.ErrorMessage = fmt.Sprintf("查询失败: %s", err.Error())
		order.PriceValidation.ProfitStatus = "unknown"
		return
	}

	// 设置查询结果
	order.PriceValidation.ProviderPrice = detailedInfo.TotalFee
	order.PriceValidation.QueryStatus = "success"
	order.PriceValidation.QueryTime = util.NowBeijing().Format(time.RFC3339)
	order.PriceValidation.ErrorMessage = detailedInfo.Error

	if detailedInfo.Error != "" {
		order.PriceValidation.QueryStatus = "failed"
		order.PriceValidation.ProfitStatus = "unknown"
	} else {
		// 计算盈亏状态
		order.PriceValidation.ProfitStatus = s.calculateProfitStatus(
			order.PriceValidation.SystemPrice,
			order.PriceValidation.ProviderPrice,
		)
	}

	// 缓存结果
	s.setCache(cacheKey, &CachedPriceValidation{
		ProviderPrice: order.PriceValidation.ProviderPrice,
		SystemPrice:   order.PriceValidation.SystemPrice,
		ProfitStatus:  order.PriceValidation.ProfitStatus,
		QueryStatus:   order.PriceValidation.QueryStatus,
		QueryTime:     util.NowBeijing(),
		ErrorMessage:  order.PriceValidation.ErrorMessage,
		Supported:     order.PriceValidation.Supported,
		ExpiryTime:    util.NowBeijing().Add(s.cacheExpiry),
	})

	s.logger.Debug("订单价格验证完成",
		zap.Int64("order_id", order.ID),
		zap.Float64("system_price", order.PriceValidation.SystemPrice),
		zap.Float64("provider_price", order.PriceValidation.ProviderPrice),
		zap.String("profit_status", order.PriceValidation.ProfitStatus))
}

// calculateProfitStatus 计算盈亏状态
func (s *PriceValidationService) calculateProfitStatus(systemPrice, providerPrice float64) string {
	// 如果供应商价格为0或无效，返回未知状态
	if providerPrice <= 0 {
		s.logger.Debug("供应商价格无效，无法计算盈亏状态",
			zap.Float64("system_price", systemPrice),
			zap.Float64("provider_price", providerPrice))
		return "unknown"
	}

	// 如果系统价格为0或无效，返回未知状态
	if systemPrice <= 0 {
		s.logger.Debug("系统价格无效，无法计算盈亏状态",
			zap.Float64("system_price", systemPrice),
			zap.Float64("provider_price", providerPrice))
		return "unknown"
	}

	// 计算价格差异（系统价格 - 供应商价格）
	difference := systemPrice - providerPrice

	// 设置容差值（0.01元）来处理浮点数精度问题
	tolerance := 0.01

	s.logger.Debug("计算盈亏状态",
		zap.Float64("system_price", systemPrice),
		zap.Float64("provider_price", providerPrice),
		zap.Float64("difference", difference),
		zap.Float64("tolerance", tolerance))

	if difference > tolerance {
		return "profit"    // 盈利：系统价格 > 供应商价格
	} else if difference < -tolerance {
		return "loss"      // 亏损：系统价格 < 供应商价格
	} else {
		return "break_even" // 持平：价格差异在容差范围内
	}
}

// getCacheKey 生成缓存键
func (s *PriceValidationService) getCacheKey(orderID int64) string {
	return fmt.Sprintf("price_validation_%d", orderID)
}

// getFromCache 从缓存获取结果
func (s *PriceValidationService) getFromCache(key string) *CachedPriceValidation {
	s.cacheMutex.RLock()
	defer s.cacheMutex.RUnlock()
	
	cached, exists := s.cache[key]
	if !exists {
		return nil
	}
	
	// 检查是否过期
	if util.NowBeijing().After(cached.ExpiryTime) {
		// 异步清理过期缓存
		go func() {
			s.cacheMutex.Lock()
			delete(s.cache, key)
			s.cacheMutex.Unlock()
		}()
		return nil
	}
	
	return cached
}

// setCache 设置缓存
func (s *PriceValidationService) setCache(key string, value *CachedPriceValidation) {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()
	
	s.cache[key] = value
}

// ClearExpiredCache 清理过期缓存
func (s *PriceValidationService) ClearExpiredCache() {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()
	
	now := util.NowBeijing()
	for key, cached := range s.cache {
		if now.After(cached.ExpiryTime) {
			delete(s.cache, key)
		}
	}
}

// GetCacheStats 获取缓存统计信息
func (s *PriceValidationService) GetCacheStats() map[string]interface{} {
	s.cacheMutex.RLock()
	defer s.cacheMutex.RUnlock()

	return map[string]interface{}{
		"cache_size":   len(s.cache),
		"cache_expiry": s.cacheExpiry.String(),
	}
}

// filterOrdersNeedingValidation 过滤需要验证的订单
func (s *PriceValidationService) filterOrdersNeedingValidation(orders []*model.AdminOrderListItem) []*model.AdminOrderListItem {
	var ordersToValidate []*model.AdminOrderListItem
	now := util.NowBeijing()

	for _, order := range orders {
		cacheKey := s.getCacheKey(order.ID)

		// 检查缓存
		if cached := s.getFromCache(cacheKey); cached != nil {
			// 如果缓存未过期，使用缓存数据
			s.applyCachedValidationResult(order, cached)
			continue
		}

		// 检查订单是否需要验证
		if s.shouldValidateOrder(order, now) {
			ordersToValidate = append(ordersToValidate, order)
		} else {
			// 设置默认状态
			s.setDefaultValidationState(order)
		}
	}

	return ordersToValidate
}

// shouldValidateOrder 判断订单是否需要验证
func (s *PriceValidationService) shouldValidateOrder(order *model.AdminOrderListItem, now time.Time) bool {
	// 检查供应商是否支持查询
	if !s.shippingFeeService.IsProviderSupported(order.Provider) {
		// 不支持的供应商，设置不支持状态
		order.PriceValidation.Supported = false
		order.PriceValidation.QueryStatus = "failed"
		order.PriceValidation.ErrorMessage = fmt.Sprintf("供应商 %s 不支持价格查询", order.Provider)
		order.PriceValidation.ProfitStatus = "unknown"
		return false
	}

	// 检查订单创建时间，只对最近创建的订单或长时间未验证的订单进行查询
	orderCreatedAt := order.CreatedAt
	if orderCreatedAt.IsZero() {
		s.logger.Warn("订单创建时间为空", zap.Time("created_at", order.CreatedAt))
		orderCreatedAt = now // 如果时间为空，当作新订单处理
	}

	// 如果订单是新创建的（1小时内）或者从未验证过，则需要验证
	isNewOrder := now.Sub(orderCreatedAt) < s.cacheExpiry
	hasNoValidation := order.PriceValidation.QueryStatus == "" || order.PriceValidation.QueryStatus == "pending"

	return isNewOrder || hasNoValidation
}

// applyCachedValidationResult 应用缓存的验证结果
func (s *PriceValidationService) applyCachedValidationResult(order *model.AdminOrderListItem, cached *CachedPriceValidation) {
	order.PriceValidation.ProviderPrice = cached.ProviderPrice
	order.PriceValidation.SystemPrice = cached.SystemPrice
	order.PriceValidation.ProfitStatus = cached.ProfitStatus
	order.PriceValidation.QueryStatus = "cached"
	order.PriceValidation.QueryTime = cached.QueryTime.Format(time.RFC3339)
	order.PriceValidation.ErrorMessage = cached.ErrorMessage
	order.PriceValidation.Supported = cached.Supported
}

// setDefaultValidationState 设置默认验证状态
func (s *PriceValidationService) setDefaultValidationState(order *model.AdminOrderListItem) {
	if order.PriceValidation.QueryStatus == "" {
		order.PriceValidation.SystemPrice = order.Price
		order.PriceValidation.QueryStatus = "pending"
		order.PriceValidation.ProfitStatus = "unknown"
		order.PriceValidation.Supported = s.shippingFeeService.IsProviderSupported(order.Provider)
	}
}
