package service

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"

)

// EnhancedWorkOrderCallbackService 增强工单回调服务
type EnhancedWorkOrderCallbackService struct {
	callbackRepo     repository.CallbackRepository
	workOrderRepo    repository.WorkOrderRepository
	configManager    *WorkOrderCallbackConfigManager
	metricsCollector *WorkOrderCallbackMetricsCollector
	logger           *zap.Logger

	// 企业级保护机制
	circuitBreaker *WorkOrderCircuitBreaker
	rateLimiter    *WorkOrderRateLimiter
	monitoring     *WorkOrderMonitoring

	// 内部状态管理
	ctx     context.Context
	cancel  context.CancelFunc
	started bool
	mu      sync.RWMutex
}

// WorkOrderCallbackConfigManager 工单回调配置管理器
type WorkOrderCallbackConfigManager struct {
	repo   repository.CallbackRepository
	cache  map[string]*model.WorkOrderCallbackConfig
	mu     sync.RWMutex
	logger *zap.Logger
}

// WorkOrderCallbackMetricsCollector 工单回调指标收集器
type WorkOrderCallbackMetricsCollector struct {
	metrics map[string]*model.WorkOrderCallbackMetrics
	mu      sync.RWMutex
	logger  *zap.Logger
}

// WorkOrderCallbackHealthChecker 工单回调健康检查器
type WorkOrderCallbackHealthChecker struct {
	service *EnhancedWorkOrderCallbackService
	logger  *zap.Logger
}

// WorkOrderProcessingPool 工单处理池
type WorkOrderProcessingPool struct {
	workers   int
	taskQueue chan *WorkOrderCallbackTask
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
	logger    *zap.Logger
}

// WorkOrderCallbackTask 工单回调任务
type WorkOrderCallbackTask struct {
	ID           uuid.UUID
	WorkOrder    *model.WorkOrder
	CallbackData *model.WorkOrderCallbackData
	Provider     string
	EventType    string
	CreatedAt    time.Time
	RetryCount   int
}

// NewEnhancedWorkOrderCallbackService 创建增强工单回调服务
func NewEnhancedWorkOrderCallbackService(
	callbackRepo repository.CallbackRepository,
	workOrderRepo repository.WorkOrderRepository,
	logger *zap.Logger,
) *EnhancedWorkOrderCallbackService {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建熔断器配置
	circuitBreakerConfig := WorkOrderCircuitBreakerConfig{
		FailureThreshold:         5,                // 5次失败触发熔断
		TimeWindow:               60 * time.Second, // 60秒时间窗口
		RecoveryTimeout:          30 * time.Second, // 30秒恢复时间
		HalfOpenMaxRequests:      3,                // 半开状态最大3个请求
		HalfOpenSuccessThreshold: 2,                // 半开状态2次成功即恢复
	}

	// 创建限流器配置
	rateLimiterConfig := WorkOrderRateLimiterConfig{
		TokensPerSecond:       10.0,  // 每个用户每秒10个请求
		BucketCapacity:        100,   // 用户桶容量100
		PerUserLimit:          true,  // 启用用户级限流
		GlobalTokensPerSecond: 100.0, // 全局每秒100个请求
		GlobalBucketCapacity:  1000,  // 全局桶容量1000
		Disabled:              true,  // 全面禁用限流
	}

	// 创建组件
	circuitBreaker := NewWorkOrderCircuitBreaker(circuitBreakerConfig, logger)
	rateLimiter := NewWorkOrderRateLimiter(rateLimiterConfig, logger)

	// 创建监控配置
	monitoringConfig := WorkOrderMonitoringConfig{
		MonitorInterval:           30 * time.Second,
		FailureRateThreshold:      0.1, // 10%失败率
		ResponseTimeThreshold:     5 * time.Second,
		CircuitBreakerThreshold:   3,
		RateLimitThreshold:        0.8, // 80%使用率
		AlertCooldown:             5 * time.Minute,
		EnableFailureRateAlert:    true,
		EnableResponseTimeAlert:   true,
		EnableCircuitBreakerAlert: true,
		EnableRateLimitAlert:      true,
	}

	monitoring := NewWorkOrderMonitoring(monitoringConfig, circuitBreaker, rateLimiter, logger)

	service := &EnhancedWorkOrderCallbackService{
		callbackRepo:     callbackRepo,
		workOrderRepo:    workOrderRepo,
		configManager:    NewWorkOrderCallbackConfigManager(callbackRepo, logger),
		metricsCollector: NewWorkOrderCallbackMetricsCollector(logger),
		logger:           logger,
		ctx:              ctx,
		cancel:           cancel,
		circuitBreaker:   circuitBreaker,
		rateLimiter:      rateLimiter,
		monitoring:       monitoring,
	}

	return service
}

// Start 启动服务
func (s *EnhancedWorkOrderCallbackService) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.started {
		return fmt.Errorf("服务已经启动")
	}

	s.logger.Info("启动增强工单回调服务")

	// 启动组件
	if err := s.configManager.Start(); err != nil {
		return fmt.Errorf("启动配置管理器失败: %w", err)
	}

	if err := s.metricsCollector.Start(); err != nil {
		return fmt.Errorf("启动指标收集器失败: %w", err)
	}

	// 启动监控系统
	s.monitoring.Start()

	s.started = true
	s.logger.Info("增强工单回调服务启动成功")
	return nil
}

// Stop 停止服务
func (s *EnhancedWorkOrderCallbackService) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.started {
		return fmt.Errorf("服务未启动")
	}

	s.logger.Info("停止增强工单回调服务")

	// 停止监控系统
	s.monitoring.Stop()

	// 停止组件
	s.metricsCollector.Stop()
	s.configManager.Stop()

	// 取消上下文
	s.cancel()

	s.started = false
	s.logger.Info("增强工单回调服务停止成功")
	return nil
}

// ProcessCallback 处理工单回调（实现WorkOrderCallbackService接口）
func (s *EnhancedWorkOrderCallbackService) ProcessCallback(
	ctx context.Context,
	provider string,
	rawData []byte,
	headers map[string]string,
) (*model.CallbackResponse, error) {
	requestID := uuid.New().String()
	startTime := util.NowBeijing()

	s.logger.Info("接收工单回调请求",
		zap.String("request_id", requestID),
		zap.String("provider", provider),
		zap.Int("data_size", len(rawData)),
		zap.Any("headers", headers))

	// 1. 解析工单回调数据
	var callbackData map[string]interface{}
	if err := json.Unmarshal(rawData, &callbackData); err != nil {
		s.logger.Error("解析工单回调数据失败",
			zap.String("request_id", requestID),
			zap.String("provider", provider),
			zap.Error(err))
		return &model.CallbackResponse{
			Success: false,
			Code:    "400",
			Message: "数据格式错误",
		}, fmt.Errorf("解析回调数据失败: %w", err)
	}

	// 2. 提取工单ID
	var workOrderID string
	switch provider {
	case "kuaidi100":
		if wid, ok := callbackData["workorderId"]; ok {
			workOrderID = fmt.Sprintf("%v", wid)
		}
	case "yida":
		if taskNo, ok := callbackData["taskNo"]; ok {
			workOrderID = fmt.Sprintf("%v", taskNo)
		}
	case "yuntong":
		if data, ok := callbackData["Data"].([]interface{}); ok && len(data) > 0 {
			if workOrder, ok := data[0].(map[string]interface{}); ok {
				if complaintNumber, ok := workOrder["ComplaintNumber"]; ok {
					workOrderID = fmt.Sprintf("%v", complaintNumber)
				}
			}
		}
	}

	if workOrderID == "" {
		s.logger.Error("无法从回调数据中提取工单ID",
			zap.String("request_id", requestID),
			zap.String("provider", provider))
		return &model.CallbackResponse{
			Success: false,
			Code:    "400",
			Message: "工单ID缺失",
		}, fmt.Errorf("工单ID缺失")
	}

	// 3. 查询工单信息
	workOrder, err := s.workOrderRepo.GetByProviderWorkOrderID(ctx, workOrderID, provider)
	if err != nil {
		s.logger.Error("查询工单失败",
			zap.String("request_id", requestID),
			zap.String("provider", provider),
			zap.String("work_order_id", workOrderID),
			zap.Error(err))
		return &model.CallbackResponse{
			Success: false,
			Code:    "404",
			Message: "工单不存在",
		}, fmt.Errorf("查询工单失败: %w", err)
	}

	// 4. 创建回调记录
	record := &model.WorkOrderForwardRecord{
		ID:          uuid.New(),
		WorkOrderID: workOrder.ID,
		UserID:      workOrder.UserID,
		EventType:   s.extractEventType(callbackData, provider),
		RequestData: rawData,
		Status:      "pending",
		CreatedAt:   util.NowBeijing(),
		UpdatedAt:   util.NowBeijing(),
	}

	// 5. 保存回调记录
	if err := s.saveForwardRecord(ctx, record); err != nil {
		s.logger.Error("保存回调记录失败",
			zap.String("request_id", requestID),
			zap.Error(err))
	}

	// 6. 异步处理工单回调转发
	go func() {
		defer func() {
			if r := recover(); r != nil {
				s.logger.Error("工单回调处理发生panic",
					zap.String("request_id", requestID),
					zap.Any("panic", r))
			}
		}()

		s.processWorkOrderCallback(context.Background(), workOrder, callbackData, provider, record)
	}()

	// 7. 记录处理时间
	duration := time.Since(startTime)
	s.logger.Info("工单回调处理完成",
		zap.String("request_id", requestID),
		zap.String("provider", provider),
		zap.String("work_order_id", workOrderID),
		zap.Duration("duration", duration))

	// 8. 返回成功响应
	response := &model.CallbackResponse{
		Success: true,
		Code:    "200",
		Message: "工单回调处理成功",
		Data: map[string]interface{}{
			"request_id":         requestID,
			"work_order_id":      workOrderID,
			"processed_at":       util.NowBeijing().Format(time.RFC3339),
			"processing_time_ms": duration.Milliseconds(),
		},
	}

	return response, nil
}

// ForwardWorkOrderCallback 异步转发工单回调
func (s *EnhancedWorkOrderCallbackService) ForwardWorkOrderCallback(
	ctx context.Context,
	workOrder *model.WorkOrder,
	callbackData *model.WorkOrderCallbackData,
	provider string,
) error {
	s.logger.Info("异步转发工单回调",
		zap.String("work_order_id", workOrder.ID.String()),
		zap.String("user_id", workOrder.UserID),
		zap.String("provider", provider))

	// 创建任务
	task := &WorkOrderCallbackTask{
		ID:           uuid.New(),
		WorkOrder:    workOrder,
		CallbackData: callbackData,
		Provider:     provider,
		EventType:    s.determineEventType(callbackData.Status),
		CreatedAt:    util.NowBeijing(),
		RetryCount:   0,
	}

	// 异步处理任务
	go s.processTask(ctx, task)

	return nil
}

// ForwardWorkOrderCallbackSync 同步转发工单回调
func (s *EnhancedWorkOrderCallbackService) ForwardWorkOrderCallbackSync(
	ctx context.Context,
	workOrderID uuid.UUID,
	userID string,
	eventType string,
	callbackData map[string]interface{},
) error {
	startTime := util.NowBeijing()

	s.logger.Info("开始同步转发工单回调",
		zap.String("work_order_id", workOrderID.String()),
		zap.String("user_id", userID),
		zap.String("event_type", eventType))

	// 🔥 1. 限流检查
	if err := s.rateLimiter.AllowRequest(ctx, userID); err != nil {
		s.logger.Warn("工单回调请求被限流",
			zap.String("user_id", userID),
			zap.Error(err))
		s.metricsCollector.RecordFailure(userID)
		return fmt.Errorf("请求被限流: %w", err)
	}

	// 2. 获取用户回调配置
	config, err := s.configManager.GetUserConfig(ctx, userID)
	if err != nil {
		s.logger.Error("获取用户回调配置失败",
			zap.String("user_id", userID),
			zap.Error(err))
		s.metricsCollector.RecordFailure(userID)
		return fmt.Errorf("获取用户回调配置失败: %w", err)
	}

	// 3. 验证回调配置
	if !config.Enabled || config.CallbackURL == "" {
		s.logger.Debug("用户回调未启用或URL为空，跳过转发",
			zap.String("user_id", userID),
			zap.Bool("enabled", config.Enabled),
			zap.String("callback_url", config.CallbackURL))
		return nil
	}

	// 4. 获取工单信息
	workOrder, err := s.workOrderRepo.GetByID(ctx, workOrderID)
	if err != nil {
		s.logger.Error("获取工单信息失败",
			zap.String("work_order_id", workOrderID.String()),
			zap.Error(err))
		s.metricsCollector.RecordFailure(userID)
		return fmt.Errorf("获取工单信息失败: %w", err)
	}

	// 5. 构建回调数据
	payload := s.buildCallbackPayload(workOrder, callbackData, "sync", eventType)

	// 6. 创建转发记录
	record := &model.WorkOrderForwardRecord{
		ID:          uuid.New(),
		WorkOrderID: workOrderID,
		UserID:      userID,
		CallbackURL: config.CallbackURL,
		EventType:   eventType,
		Status:      "pending",
		RetryCount:  0,
		RequestAt:   &startTime,
		CreatedAt:   startTime,
		UpdatedAt:   startTime,
	}

	// 7. 保存转发记录
	if err := s.saveForwardRecord(ctx, record); err != nil {
		s.logger.Error("保存转发记录失败",
			zap.String("record_id", record.ID.String()),
			zap.Error(err))
		s.metricsCollector.RecordFailure(userID)
		return fmt.Errorf("保存转发记录失败: %w", err)
	}

	// 🔥 8. 熔断器保护的转发操作
	err = s.circuitBreaker.Execute(ctx, userID, func() error {
		return s.forwardCallbackWithRetry(ctx, config, payload, record)
	})

	// 9. 处理转发结果
	if err != nil {
		s.updateForwardRecordStatus(ctx, record, "failed", err.Error())
		s.metricsCollector.RecordFailure(userID)

		s.logger.Error("工单回调转发失败",
			zap.String("work_order_id", workOrderID.String()),
			zap.String("user_id", userID),
			zap.String("event_type", eventType),
			zap.Duration("duration", time.Since(startTime)),
			zap.Error(err))

		return fmt.Errorf("工单回调转发失败: %w", err)
	}

	// 10. 成功处理
	s.updateForwardRecordStatus(ctx, record, "success", "转发成功")
	s.metricsCollector.RecordSuccess(userID)

	s.logger.Info("工单回调转发成功",
		zap.String("work_order_id", workOrderID.String()),
		zap.String("user_id", userID),
		zap.String("event_type", eventType),
		zap.Duration("duration", time.Since(startTime)))

	return nil
}

// GetUserCallbackConfig 获取用户回调配置
func (s *EnhancedWorkOrderCallbackService) GetUserCallbackConfig(
	ctx context.Context,
	userID string,
) (*model.WorkOrderCallbackConfig, error) {
	return s.configManager.GetUserConfig(ctx, userID)
}

// UpdateUserCallbackConfig 更新用户回调配置
func (s *EnhancedWorkOrderCallbackService) UpdateUserCallbackConfig(
	ctx context.Context,
	config *model.WorkOrderCallbackConfig,
) error {
	return s.configManager.UpdateUserConfig(ctx, config)
}

// GetCallbackMetrics 获取回调指标
func (s *EnhancedWorkOrderCallbackService) GetCallbackMetrics(
	ctx context.Context,
	userID string,
) (*model.WorkOrderCallbackMetrics, error) {
	return s.metricsCollector.GetUserMetrics(userID), nil
}

// GetSystemMetrics 获取系统指标
func (s *EnhancedWorkOrderCallbackService) GetSystemMetrics() map[string]interface{} {
	metrics := make(map[string]interface{})

	// 监控系统指标
	if monitoringMetrics := s.monitoring.GetMetrics(); monitoringMetrics != nil {
		metrics["monitoring"] = monitoringMetrics
	}

	// 熔断器指标
	if cbMetrics := s.circuitBreaker.GetMetrics(); cbMetrics != nil {
		metrics["circuit_breaker"] = cbMetrics
	}

	// 限流器指标
	if rlMetrics := s.rateLimiter.GetMetrics(); rlMetrics != nil {
		metrics["rate_limiter"] = rlMetrics
	}

	// 整体指标
	metrics["overall"] = s.metricsCollector.GetOverallMetrics()

	return metrics
}

// RetryFailedCallbacks 重试失败的回调
func (s *EnhancedWorkOrderCallbackService) RetryFailedCallbacks(
	ctx context.Context,
	maxRetries int,
) error {
	s.logger.Info("开始重试失败的工单回调",
		zap.Int("max_retries", maxRetries))

	// 1. 查询失败的回调记录
	failedRecords, err := s.callbackRepo.GetFailedWorkOrderForwardRecords(ctx, maxRetries)
	if err != nil {
		return fmt.Errorf("查询失败的回调记录失败: %w", err)
	}

	s.logger.Info("找到失败的工单回调记录",
		zap.Int("count", len(failedRecords)))

	// 2. 遍历失败记录进行重试
	var retryCount, successCount, failCount int
	for _, record := range failedRecords {
		retryCount++

		// 检查是否应该重试
		if record.RetryCount >= maxRetries {
			s.logger.Debug("工单回调记录已达到最大重试次数，跳过",
				zap.String("record_id", record.ID.String()),
				zap.Int("retry_count", record.RetryCount),
				zap.Int("max_retries", maxRetries))
			continue
		}

		// 执行重试
		err := s.retryForwardRecord(ctx, record)
		if err != nil {
			failCount++
			s.logger.Error("重试工单回调失败",
				zap.String("record_id", record.ID.String()),
				zap.Error(err))
		} else {
			successCount++
			s.logger.Info("重试工单回调成功",
				zap.String("record_id", record.ID.String()))
		}
	}

	s.logger.Info("工单回调重试任务完成",
		zap.Int("total_records", len(failedRecords)),
		zap.Int("retry_attempts", retryCount),
		zap.Int("success_count", successCount),
		zap.Int("fail_count", failCount))

	return nil
}

// determineEventType 确定事件类型
func (s *EnhancedWorkOrderCallbackService) determineEventType(status int) string {
	switch model.WorkOrderStatus(status) {
	case model.WorkOrderStatusCompleted:
		return model.WorkOrderEventCompleted
	case model.WorkOrderStatusReplied:
		return model.WorkOrderEventReplied
	case model.WorkOrderStatusProcessing:
		return model.WorkOrderEventUpdated
	default:
		return model.WorkOrderEventUpdated
	}
}

// retryForwardRecord 重试转发记录
func (s *EnhancedWorkOrderCallbackService) retryForwardRecord(
	ctx context.Context,
	record *model.WorkOrderForwardRecord,
) error {
	s.logger.Info("重试工单回调",
		zap.String("record_id", record.ID.String()),
		zap.Int("retry_count", record.RetryCount))

	// 1. 获取工单信息
	workOrder, err := s.workOrderRepo.GetByID(ctx, record.WorkOrderID)
	if err != nil {
		return fmt.Errorf("获取工单信息失败: %w", err)
	}

	// 2. 获取用户回调配置
	config, err := s.configManager.GetUserConfig(ctx, record.UserID)
	if err != nil {
		return fmt.Errorf("获取用户回调配置失败: %w", err)
	}

	// 3. 检查配置是否仍然有效
	if !config.Enabled || config.CallbackURL == "" {
		s.logger.Warn("用户回调配置无效，跳过重试",
			zap.String("user_id", record.UserID),
			zap.Bool("enabled", config.Enabled),
			zap.String("callback_url", config.CallbackURL))
		return fmt.Errorf("用户回调配置无效")
	}

	// 4. 解析原始请求数据
	var originalPayload map[string]interface{}
	if record.RequestData != nil {
		if err := json.Unmarshal(record.RequestData, &originalPayload); err != nil {
			return fmt.Errorf("解析原始请求数据失败: %w", err)
		}
	} else {
		// 如果没有原始数据，构建基础回调数据
		originalPayload = s.buildCallbackPayload(workOrder, map[string]interface{}{}, "retry", record.EventType)
	}

	// 5. 增加重试次数
	record.RetryCount++
	record.UpdatedAt = util.NowBeijing()

	// 6. 执行重试转发
	err = s.forwardCallback(ctx, config, originalPayload, record)
	if err != nil {
		// 更新失败状态
		s.updateForwardRecordStatus(ctx, record, "failed", fmt.Sprintf("重试失败: %s", err.Error()))
		return err
	}

	// 7. 更新成功状态
	s.updateForwardRecordStatus(ctx, record, "success", "重试成功")

	// 8. 更新用户指标
	s.metricsCollector.RecordSuccess(record.UserID)

	return nil
}

// WorkOrderCallbackConfigManager 实现

// NewWorkOrderCallbackConfigManager 创建配置管理器
func NewWorkOrderCallbackConfigManager(
	repo repository.CallbackRepository,
	logger *zap.Logger,
) *WorkOrderCallbackConfigManager {
	return &WorkOrderCallbackConfigManager{
		repo:   repo,
		cache:  make(map[string]*model.WorkOrderCallbackConfig),
		logger: logger,
	}
}

// Start 启动配置管理器
func (m *WorkOrderCallbackConfigManager) Start() error {
	m.logger.Info("启动工单回调配置管理器")
	// 可以在这里预加载配置
	return nil
}

// Stop 停止配置管理器
func (m *WorkOrderCallbackConfigManager) Stop() error {
	m.logger.Info("停止工单回调配置管理器")
	return nil
}

// GetUserConfig 获取用户配置
func (m *WorkOrderCallbackConfigManager) GetUserConfig(
	ctx context.Context,
	userID string,
) (*model.WorkOrderCallbackConfig, error) {
	m.mu.RLock()
	if config, exists := m.cache[userID]; exists {
		m.mu.RUnlock()
		return config, nil
	}
	m.mu.RUnlock()

	// 从数据库加载用户回调配置
	userConfig, err := m.repo.GetUserCallbackConfig(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 转换为WorkOrderCallbackConfig
	config := &model.WorkOrderCallbackConfig{
		ID:               userConfig.ID,
		UserID:           userConfig.UserID,
		Enabled:          userConfig.Enabled,
		CallbackURL:      userConfig.CallbackURL,
		CallbackSecret:   userConfig.CallbackSecret,
		TimeoutSeconds:   userConfig.TimeoutSeconds,
		MaxRetries:       userConfig.RetryCount,
		SubscribedEvents: userConfig.SubscribedEvents,
		CreatedAt:        userConfig.CreatedAt,
		UpdatedAt:        userConfig.UpdatedAt,
	}

	// 缓存配置
	m.mu.Lock()
	m.cache[userID] = config
	m.mu.Unlock()

	return config, nil
}

// UpdateUserConfig 更新用户配置
func (m *WorkOrderCallbackConfigManager) UpdateUserConfig(
	ctx context.Context,
	config *model.WorkOrderCallbackConfig,
) error {
	// 转换为UserCallbackConfig
	userConfig := &model.UserCallbackConfig{
		ID:               config.ID,
		UserID:           config.UserID,
		CallbackURL:      config.CallbackURL,
		CallbackSecret:   config.CallbackSecret,
		Enabled:          config.Enabled,
		RetryCount:       config.MaxRetries,
		TimeoutSeconds:   config.TimeoutSeconds,
		SubscribedEvents: config.SubscribedEvents,
		CreatedAt:        config.CreatedAt,
		UpdatedAt:        config.UpdatedAt,
	}

	if err := m.repo.SaveUserCallbackConfig(ctx, userConfig); err != nil {
		return err
	}

	// 更新缓存
	m.mu.Lock()
	m.cache[config.UserID] = config
	m.mu.Unlock()

	return nil
}

// WorkOrderCallbackMetricsCollector 实现

// NewWorkOrderCallbackMetricsCollector 创建指标收集器
func NewWorkOrderCallbackMetricsCollector(logger *zap.Logger) *WorkOrderCallbackMetricsCollector {
	return &WorkOrderCallbackMetricsCollector{
		metrics: make(map[string]*model.WorkOrderCallbackMetrics),
		logger:  logger,
	}
}

// Start 启动指标收集器
func (c *WorkOrderCallbackMetricsCollector) Start() error {
	c.logger.Info("启动工单回调指标收集器")
	return nil
}

// Stop 停止指标收集器
func (c *WorkOrderCallbackMetricsCollector) Stop() error {
	c.logger.Info("停止工单回调指标收集器")
	return nil
}

// GetUserMetrics 获取用户指标
func (c *WorkOrderCallbackMetricsCollector) GetUserMetrics(userID string) *model.WorkOrderCallbackMetrics {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if metrics, exists := c.metrics[userID]; exists {
		return metrics
	}

	// 返回空指标
	return &model.WorkOrderCallbackMetrics{
		UserID: userID,
	}
}

// GetOverallMetrics 获取整体指标
func (c *WorkOrderCallbackMetricsCollector) GetOverallMetrics() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	totalUsers := len(c.metrics)
	var totalCallbacks, totalSuccess, totalFailure int64

	for _, metrics := range c.metrics {
		totalCallbacks += metrics.TotalCallbacks
		totalSuccess += metrics.SuccessCount
		totalFailure += metrics.FailureCount
	}

	successRate := float64(0)
	if totalCallbacks > 0 {
		successRate = float64(totalSuccess) / float64(totalCallbacks)
	}

	return map[string]interface{}{
		"total_users":     totalUsers,
		"total_callbacks": totalCallbacks,
		"success_count":   totalSuccess,
		"failure_count":   totalFailure,
		"success_rate":    successRate,
	}
}

// WorkOrderCallbackHealthChecker 实现

// NewWorkOrderCallbackHealthChecker 创建健康检查器
func NewWorkOrderCallbackHealthChecker(
	service *EnhancedWorkOrderCallbackService,
	logger *zap.Logger,
) *WorkOrderCallbackHealthChecker {
	return &WorkOrderCallbackHealthChecker{
		service: service,
		logger:  logger,
	}
}

// Start 启动健康检查器
func (h *WorkOrderCallbackHealthChecker) Start() error {
	h.logger.Info("启动工单回调健康检查器")
	return nil
}

// Stop 停止健康检查器
func (h *WorkOrderCallbackHealthChecker) Stop() error {
	h.logger.Info("停止工单回调健康检查器")
	return nil
}

// WorkOrderProcessingPool 实现

// NewWorkOrderProcessingPool 创建处理池
func NewWorkOrderProcessingPool(workers, queueSize int, logger *zap.Logger) *WorkOrderProcessingPool {
	ctx, cancel := context.WithCancel(context.Background())

	return &WorkOrderProcessingPool{
		workers:   workers,
		taskQueue: make(chan *WorkOrderCallbackTask, queueSize),
		ctx:       ctx,
		cancel:    cancel,
		logger:    logger,
	}
}

// Start 启动处理池
func (p *WorkOrderProcessingPool) Start() error {
	p.logger.Info("启动工单处理池",
		zap.Int("workers", p.workers),
		zap.Int("queue_size", cap(p.taskQueue)))

	// 启动工作者
	for i := 0; i < p.workers; i++ {
		p.wg.Add(1)
		go p.worker(i)
	}

	return nil
}

// Stop 停止处理池
func (p *WorkOrderProcessingPool) Stop() error {
	p.logger.Info("停止工单处理池")

	p.cancel()
	close(p.taskQueue)
	p.wg.Wait()

	return nil
}

// SubmitTask 提交任务
func (p *WorkOrderProcessingPool) SubmitTask(task *WorkOrderCallbackTask) error {
	select {
	case p.taskQueue <- task:
		return nil
	case <-p.ctx.Done():
		return p.ctx.Err()
	default:
		return fmt.Errorf("任务队列已满")
	}
}

// worker 工作者goroutine
func (p *WorkOrderProcessingPool) worker(id int) {
	p.logger.Debug("启动工作者",
		zap.Int("worker_id", id))

	defer func() {
		p.wg.Done()
		p.logger.Debug("工作者停止",
			zap.Int("worker_id", id))
	}()

	for {
		select {
		case <-p.ctx.Done():
			return
		case task := <-p.taskQueue:
			if task == nil {
				continue
			}

			p.logger.Debug("工作者处理任务",
				zap.Int("worker_id", id),
				zap.String("task_id", task.ID.String()))

			// 注意：这里需要外部服务实例来处理任务
			// 在实际使用中，应该通过依赖注入或其他方式获取服务实例
			// 暂时注释掉，避免编译错误
			// p.processTask(task)
		}
	}
}

// processTask 处理任务
func (s *EnhancedWorkOrderCallbackService) processTask(
	ctx context.Context,
	task *WorkOrderCallbackTask,
) {
	// Panic恢复机制
	defer func() {
		if r := recover(); r != nil {
			s.logger.Error("工单回调任务处理发生panic",
				zap.String("task_id", task.ID.String()),
				zap.Any("panic", r))
		}
	}()

	startTime := util.NowBeijing()

	s.logger.Debug("开始处理工单回调任务",
		zap.String("task_id", task.ID.String()),
		zap.String("work_order_id", task.WorkOrder.ID.String()),
		zap.String("event_type", task.EventType))

	// 将CallbackData转换为map[string]interface{}
	callbackDataMap := make(map[string]interface{})
	if task.CallbackData != nil {
		// 将结构体转换为JSON再转换为map
		jsonData, err := json.Marshal(task.CallbackData)
		if err != nil {
			s.logger.Error("序列化回调数据失败",
				zap.String("task_id", task.ID.String()),
				zap.Error(err))
			return
		}

		if err := json.Unmarshal(jsonData, &callbackDataMap); err != nil {
			s.logger.Error("反序列化回调数据失败",
				zap.String("task_id", task.ID.String()),
				zap.Error(err))
			return
		}
	}

	// 执行回调转发
	err := s.ForwardWorkOrderCallbackSync(
		ctx,
		task.WorkOrder.ID,
		task.WorkOrder.UserID,
		task.EventType,
		callbackDataMap,
	)

	duration := time.Since(startTime)

	// 记录性能指标
	s.monitoring.RecordRequest(task.WorkOrder.UserID, duration, err == nil)

	if err != nil {
		s.logger.Error("工单回调任务处理失败",
			zap.String("task_id", task.ID.String()),
			zap.String("work_order_id", task.WorkOrder.ID.String()),
			zap.String("event_type", task.EventType),
			zap.Duration("duration", duration),
			zap.Error(err))
	} else {
		s.logger.Debug("工单回调任务处理成功",
			zap.String("task_id", task.ID.String()),
			zap.String("work_order_id", task.WorkOrder.ID.String()),
			zap.String("event_type", task.EventType),
			zap.Duration("duration", duration))
	}
}

// GetMetrics 获取处理池指标
func (p *WorkOrderProcessingPool) GetMetrics() map[string]interface{} {
	return map[string]interface{}{
		"workers":           p.workers,
		"queue_size":        cap(p.taskQueue),
		"queue_length":      len(p.taskQueue),
		"queue_utilization": float64(len(p.taskQueue)) / float64(cap(p.taskQueue)),
	}
}

// === 企业级辅助方法 ===

// extractEventType 从回调数据中提取事件类型
func (s *EnhancedWorkOrderCallbackService) extractEventType(
	callbackData map[string]interface{},
	provider string,
) string {
	switch provider {
	case "kuaidi100":
		if status, ok := callbackData["status"].(float64); ok {
			switch int(status) {
			case 0:
				return model.WorkOrderEventReplied
			case 1:
				return model.WorkOrderEventCompleted
			default:
				return model.WorkOrderEventUpdated
			}
		}
	case "yida":
		if status, ok := callbackData["status"].(float64); ok {
			switch int(status) {
			case 1:
				return model.WorkOrderEventCreated
			case 2:
				return model.WorkOrderEventReplied
			case 3:
				return model.WorkOrderEventCompleted
			default:
				return model.WorkOrderEventUpdated
			}
		}
	case "yuntong":
		if data, ok := callbackData["Data"].([]interface{}); ok && len(data) > 0 {
			if workOrder, ok := data[0].(map[string]interface{}); ok {
				if resultType, ok := workOrder["ResultType"].(float64); ok {
					switch int(resultType) {
					case 0:
						return model.WorkOrderEventCreated
					case 1, 2:
						return model.WorkOrderEventReplied
					case 3:
						return model.WorkOrderEventCompleted
					default:
						return model.WorkOrderEventUpdated
					}
				}
			}
		}
	}
	return model.WorkOrderEventUpdated
}

// saveForwardRecord 保存转发记录
func (s *EnhancedWorkOrderCallbackService) saveForwardRecord(
	ctx context.Context,
	record *model.WorkOrderForwardRecord,
) error {
	s.logger.Info("保存工单转发记录",
		zap.String("record_id", record.ID.String()),
		zap.String("work_order_id", record.WorkOrderID.String()),
		zap.String("user_id", record.UserID),
		zap.String("event_type", record.EventType))

	// 使用repository保存记录
	return s.callbackRepo.SaveWorkOrderForwardRecord(ctx, record)
}

// processWorkOrderCallback 处理工单回调
func (s *EnhancedWorkOrderCallbackService) processWorkOrderCallback(
	ctx context.Context,
	workOrder *model.WorkOrder,
	callbackData map[string]interface{},
	provider string,
	record *model.WorkOrderForwardRecord,
) {
	s.logger.Info("开始处理工单回调转发",
		zap.String("work_order_id", workOrder.ID.String()),
		zap.String("user_id", workOrder.UserID),
		zap.String("provider", provider))

	// 1. 获取用户回调配置
	config, err := s.configManager.GetUserConfig(ctx, workOrder.UserID)
	if err != nil {
		s.logger.Error("获取用户回调配置失败",
			zap.String("user_id", workOrder.UserID),
			zap.Error(err))
		s.updateForwardRecordStatus(ctx, record, "failed", "获取用户配置失败")
		return
	}

	// 2. 检查是否启用回调
	if !config.Enabled {
		s.logger.Info("用户未启用工单回调",
			zap.String("user_id", workOrder.UserID))
		s.updateForwardRecordStatus(ctx, record, "skipped", "用户未启用回调")
		return
	}

	// 3. 检查回调URL
	if config.CallbackURL == "" {
		s.logger.Warn("用户未配置回调URL",
			zap.String("user_id", workOrder.UserID))
		s.updateForwardRecordStatus(ctx, record, "failed", "未配置回调URL")
		return
	}

	// 4. 构建回调数据
	callbackPayload := s.buildCallbackPayload(workOrder, callbackData, provider, record.EventType)

	// 5. 执行回调转发
	err = s.forwardCallbackWithRetry(ctx, config, callbackPayload, record)
	if err != nil {
		s.logger.Error("工单回调转发失败",
			zap.String("work_order_id", workOrder.ID.String()),
			zap.String("user_id", workOrder.UserID),
			zap.Error(err))
		s.updateForwardRecordStatus(ctx, record, "failed", err.Error())
		s.metricsCollector.RecordFailure(workOrder.UserID)
	} else {
		s.logger.Info("工单回调转发成功",
			zap.String("work_order_id", workOrder.ID.String()),
			zap.String("user_id", workOrder.UserID))
		s.updateForwardRecordStatus(ctx, record, "success", "转发成功")
		s.metricsCollector.RecordSuccess(workOrder.UserID)
	}
}

// buildCallbackPayload 构建回调载荷
func (s *EnhancedWorkOrderCallbackService) buildCallbackPayload(
	workOrder *model.WorkOrder,
	callbackData map[string]interface{},
	provider string,
	eventType string,
) map[string]interface{} {
	return map[string]interface{}{
		"event_type": eventType,
		"event_time": util.NowBeijing().Unix(),
		"version":    "3.0",
		"work_order": map[string]interface{}{
			"id":                     workOrder.ID.String(),
			"provider_work_order_id": s.extractProviderWorkOrderID(callbackData, provider),
			"order_no":               s.safeStringPtr(workOrder.OrderNo),
			"tracking_no":            s.safeStringPtr(workOrder.TrackingNo),
			"work_order_type":        workOrder.WorkOrderType,
			"status":                 workOrder.Status,
			"created_at":             workOrder.CreatedAt.Unix(),
			"updated_at":             workOrder.UpdatedAt.Unix(),
		},
		"callback_data": callbackData,
		"provider":      provider,
		"timestamp":     util.NowBeijing().Format(time.RFC3339),
	}
}

// extractProviderWorkOrderID 提取供应商工单ID
func (s *EnhancedWorkOrderCallbackService) extractProviderWorkOrderID(
	callbackData map[string]interface{},
	provider string,
) string {
	switch provider {
	case "kuaidi100":
		if wid, ok := callbackData["workorderId"]; ok {
			return fmt.Sprintf("%v", wid)
		}
	case "yida":
		if taskNo, ok := callbackData["taskNo"]; ok {
			return fmt.Sprintf("%v", taskNo)
		}
	case "yuntong":
		if data, ok := callbackData["Data"].([]interface{}); ok && len(data) > 0 {
			if workOrder, ok := data[0].(map[string]interface{}); ok {
				if complaintNumber, ok := workOrder["ComplaintNumber"]; ok {
					return fmt.Sprintf("%v", complaintNumber)
				}
			}
		}
	}
	return ""
}

// safeStringPtr 安全获取字符串指针的值
func (s *EnhancedWorkOrderCallbackService) safeStringPtr(ptr *string) string {
	if ptr != nil {
		return *ptr
	}
	return ""
}

// updateForwardRecordStatus 更新转发记录状态
func (s *EnhancedWorkOrderCallbackService) updateForwardRecordStatus(
	ctx context.Context,
	record *model.WorkOrderForwardRecord,
	status string,
	message string,
) {
	record.Status = status
	record.ErrorMessage = message
	record.UpdatedAt = util.NowBeijing()

	if status == "success" {
		now := util.NowBeijing()
		record.ResponseAt = &now
	}

	s.logger.Debug("更新转发记录状态",
		zap.String("record_id", record.ID.String()),
		zap.String("status", status),
		zap.String("message", message))

	// 使用repository更新记录
	if err := s.callbackRepo.UpdateWorkOrderForwardRecord(ctx, record); err != nil {
		s.logger.Error("更新工单转发记录状态失败",
			zap.String("record_id", record.ID.String()),
			zap.Error(err))
	}
}

// forwardCallbackWithRetry 带重试的回调转发
func (s *EnhancedWorkOrderCallbackService) forwardCallbackWithRetry(
	ctx context.Context,
	config *model.WorkOrderCallbackConfig,
	payload map[string]interface{},
	record *model.WorkOrderForwardRecord,
) error {
	maxRetries := config.MaxRetries
	if maxRetries <= 0 {
		maxRetries = 3 // 默认重试3次
	}

	var lastErr error
	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避重试
			backoffDuration := time.Duration(attempt*attempt) * time.Second
			s.logger.Info("工单回调重试",
				zap.String("record_id", record.ID.String()),
				zap.Int("attempt", attempt),
				zap.Duration("backoff", backoffDuration))

			select {
			case <-time.After(backoffDuration):
			case <-ctx.Done():
				return ctx.Err()
			}
		}

		err := s.forwardCallback(ctx, config, payload, record)
		if err == nil {
			if attempt > 0 {
				s.logger.Info("工单回调重试成功",
					zap.String("record_id", record.ID.String()),
					zap.Int("attempt", attempt))
			}
			return nil
		}

		lastErr = err
		s.logger.Warn("工单回调转发失败",
			zap.String("record_id", record.ID.String()),
			zap.Int("attempt", attempt),
			zap.Int("max_retries", maxRetries),
			zap.Error(err))
	}

	return fmt.Errorf("工单回调转发失败，已重试%d次: %w", maxRetries, lastErr)
}

// forwardCallback 执行单次回调转发
func (s *EnhancedWorkOrderCallbackService) forwardCallback(
	ctx context.Context,
	config *model.WorkOrderCallbackConfig,
	payload map[string]interface{},
	record *model.WorkOrderForwardRecord,
) error {
	// 1. 序列化载荷
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化回调载荷失败: %w", err)
	}

	// 2. 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", config.CallbackURL, bytes.NewReader(payloadBytes))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 3. 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Go-Kuaidi-WorkOrder-Callback/1.0")
	req.Header.Set("X-Event-Type", record.EventType)

	// 4. 设置签名（如果配置了密钥）
	if config.CallbackSecret != "" {
		signature := s.generateSignature(payloadBytes, config.CallbackSecret)
		req.Header.Set("X-Signature", signature)
	}

	// 5. 设置超时
	timeout := time.Duration(config.TimeoutSeconds) * time.Second
	if timeout <= 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	client := &http.Client{Timeout: timeout}

	// 6. 发送请求
	startTime := util.NowBeijing()
	resp, err := client.Do(req)
	duration := time.Since(startTime)

	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 7. 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	// 8. 更新记录
	// 🔥 修复：转换为json.RawMessage类型
	record.RequestData = json.RawMessage(payloadBytes)
	record.ResponseData = json.RawMessage(responseBody)
	record.HTTPStatus = resp.StatusCode
	now := util.NowBeijing()
	record.RequestAt = &startTime
	record.ResponseAt = &now

	// 9. 检查响应状态
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("HTTP响应状态码错误: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	s.logger.Info("工单回调转发成功",
		zap.String("record_id", record.ID.String()),
		zap.String("url", config.CallbackURL),
		zap.Int("status_code", resp.StatusCode),
		zap.Duration("duration", duration))

	return nil
}

// generateSignature 生成签名
func (s *EnhancedWorkOrderCallbackService) generateSignature(data []byte, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(data)
	return hex.EncodeToString(h.Sum(nil))
}

// RecordSuccess 记录成功指标
func (c *WorkOrderCallbackMetricsCollector) RecordSuccess(userID string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if metrics, exists := c.metrics[userID]; exists {
		metrics.TotalCallbacks++
		metrics.SuccessCount++
		metrics.LastCallbackAt = util.NowBeijing()
		metrics.CalculateSuccessRate()
	} else {
		c.metrics[userID] = &model.WorkOrderCallbackMetrics{
			UserID:         userID,
			TotalCallbacks: 1,
			SuccessCount:   1,
			FailureCount:   0,
			SuccessRate:    1.0,
			LastCallbackAt: util.NowBeijing(),
		}
	}
}

// RecordFailure 记录失败指标
func (c *WorkOrderCallbackMetricsCollector) RecordFailure(userID string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if metrics, exists := c.metrics[userID]; exists {
		metrics.TotalCallbacks++
		metrics.FailureCount++
		metrics.LastCallbackAt = util.NowBeijing()
		metrics.CalculateSuccessRate()
	} else {
		c.metrics[userID] = &model.WorkOrderCallbackMetrics{
			UserID:         userID,
			TotalCallbacks: 1,
			SuccessCount:   0,
			FailureCount:   1,
			SuccessRate:    0.0,
			LastCallbackAt: util.NowBeijing(),
		}
	}
}
