package service

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/infrastructure/database"
	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// TransactionManagerImpl 事务管理器实现
type TransactionManagerImpl struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewTransactionManager 创建事务管理器
func NewTransactionManager(db *sql.DB) TransactionManagerInterface {
	logger, _ := zap.NewProduction()
	return &TransactionManagerImpl{
		db:     db,
		logger: logger,
	}
}

// ExecuteInTransaction 在事务中执行函数
func (tm *TransactionManagerImpl) ExecuteInTransaction(ctx context.Context, fn func(ctx context.Context) error) error {
	return tm.ExecuteInTransactionWithOptions(ctx, &TransactionOptions{
		IsolationLevel: "READ_COMMITTED",
		ReadOnly:       false,
		Timeout:        30 * time.Second,
	}, fn)
}

// ExecuteInTransactionWithOptions 使用选项在事务中执行函数
func (tm *TransactionManagerImpl) ExecuteInTransactionWithOptions(ctx context.Context, opts *TransactionOptions, fn func(ctx context.Context) error) error {
	// 设置默认选项
	if opts == nil {
		opts = &TransactionOptions{
			IsolationLevel: "READ_COMMITTED",
			ReadOnly:       false,
			Timeout:        30 * time.Second,
		}
	}

	// 设置超时
	if opts.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, opts.Timeout)
		defer cancel()
	}

	// 开始事务
	txOpts := &sql.TxOptions{
		Isolation: tm.parseIsolationLevel(opts.IsolationLevel),
		ReadOnly:  opts.ReadOnly,
	}

	tx, err := tm.db.BeginTx(ctx, txOpts)
	if err != nil {
		tm.logger.Error("开始事务失败",
			zap.Error(err),
			zap.String("isolation_level", opts.IsolationLevel),
			zap.Bool("read_only", opts.ReadOnly))
		return fmt.Errorf("begin transaction failed: %w", err)
	}

	// 👉 将 *sql.Tx 注入到新的 ctx，供仓储层识别
	txCtx := database.NewTxContext(ctx, tx)

	// 记录事务开始
	start := util.NowBeijing()
	tm.logger.Debug("事务开始",
		zap.String("isolation_level", opts.IsolationLevel),
		zap.Bool("read_only", opts.ReadOnly))

	// 执行业务逻辑
	err = fn(txCtx)

	// 记录事务执行时间
	duration := time.Since(start)
	tm.logger.Debug("事务执行完成",
		zap.Duration("duration", duration),
		zap.Error(err))

	if err != nil {
		// 回滚事务
		if rollbackErr := tx.Rollback(); rollbackErr != nil {
			tm.logger.Error("回滚事务失败",
				zap.Error(rollbackErr),
				zap.Error(err))
			return fmt.Errorf("rollback failed: %w (original error: %v)", rollbackErr, err)
		}

		tm.logger.Info("事务回滚成功",
			zap.Duration("duration", duration),
			zap.Error(err))
		return err
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		tm.logger.Error("提交事务失败",
			zap.Error(err),
			zap.Duration("duration", duration))
		return fmt.Errorf("commit transaction failed: %w", err)
	}

	tm.logger.Info("事务提交成功",
		zap.Duration("duration", duration))
	return nil
}

// ExecuteWithRetry 执行带重试的事务
func (tm *TransactionManagerImpl) ExecuteWithRetry(ctx context.Context, maxRetries int, fn func(ctx context.Context) error) error {
	var lastErr error

	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避
			backoff := time.Duration(attempt*attempt) * 100 * time.Millisecond
			tm.logger.Info("重试事务",
				zap.Int("attempt", attempt),
				zap.Duration("backoff", backoff))

			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(backoff):
			}
		}

		err := tm.ExecuteInTransaction(ctx, fn)
		if err == nil {
			if attempt > 0 {
				tm.logger.Info("事务重试成功",
					zap.Int("attempts", attempt+1))
			}
			return nil
		}

		lastErr = err

		// 检查是否应该重试
		if !tm.isRetryableError(err) {
			tm.logger.Error("事务错误不可重试",
				zap.Error(err),
				zap.Int("attempt", attempt))
			break
		}

		tm.logger.Warn("事务执行失败，准备重试",
			zap.Error(err),
			zap.Int("attempt", attempt),
			zap.Int("max_retries", maxRetries))
	}

	tm.logger.Error("事务重试失败",
		zap.Error(lastErr),
		zap.Int("max_retries", maxRetries))
	return lastErr
}

// BeginDistributedTransaction 开始分布式事务
func (tm *TransactionManagerImpl) BeginDistributedTransaction(ctx context.Context, transactionID string) error {
	// 简单实现：记录分布式事务开始
	tm.logger.Info("开始分布式事务",
		zap.String("transaction_id", transactionID))
	return nil
}

// CommitDistributedTransaction 提交分布式事务
func (tm *TransactionManagerImpl) CommitDistributedTransaction(ctx context.Context, transactionID string) error {
	// 简单实现：记录分布式事务提交
	tm.logger.Info("提交分布式事务",
		zap.String("transaction_id", transactionID))
	return nil
}

// RollbackDistributedTransaction 回滚分布式事务
func (tm *TransactionManagerImpl) RollbackDistributedTransaction(ctx context.Context, transactionID string) error {
	// 简单实现：记录分布式事务回滚
	tm.logger.Info("回滚分布式事务",
		zap.String("transaction_id", transactionID))
	return nil
}

// parseIsolationLevel 解析隔离级别
func (tm *TransactionManagerImpl) parseIsolationLevel(level string) sql.IsolationLevel {
	switch strings.ToUpper(level) {
	case "READ_UNCOMMITTED":
		return sql.LevelReadUncommitted
	case "READ_COMMITTED":
		return sql.LevelReadCommitted
	case "REPEATABLE_READ":
		return sql.LevelRepeatableRead
	case "SERIALIZABLE":
		return sql.LevelSerializable
	default:
		return sql.LevelDefault
	}
}

// isRetryableError 检查错误是否可重试
func (tm *TransactionManagerImpl) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())

	// 可重试的错误类型
	retryableErrors := []string{
		"deadlock",
		"timeout",
		"connection",
		"temporary",
		"serialization failure",
		"could not serialize access",
	}

	for _, retryableErr := range retryableErrors {
		if strings.Contains(errStr, retryableErr) {
			return true
		}
	}

	return false
}
