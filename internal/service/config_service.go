package service

import (
	"context"
	"sync"
	"time"

	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/config"
)

// ConfigServiceImpl 配置服务实现
type ConfigServiceImpl struct {
	businessConfig *BusinessConfig
	mutex          sync.RWMutex
}

// NewConfigService 创建配置服务
func NewConfigService() ConfigServiceInterface {
	return &ConfigServiceImpl{
		businessConfig: DefaultBusinessConfig(),
	}
}

// GetBusinessConfig 获取业务配置
func (s *ConfigServiceImpl) GetBusinessConfig() *BusinessConfig {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.businessConfig
}

// GetOrderConfig 获取订单配置
func (s *ConfigServiceImpl) GetOrderConfig() *OrderConfig {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.businessConfig.Order
}

// GetBillingConfig 获取计费配置
func (s *ConfigServiceImpl) GetBillingConfig() *BillingConfig {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.businessConfig.Billing
}

// GetBalanceConfig 获取余额配置
func (s *ConfigServiceImpl) GetBalanceConfig() *BalanceConfig {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.businessConfig.Balance
}

// GetCallbackConfig 获取回调配置
func (s *ConfigServiceImpl) GetCallbackConfig() *CallbackConfig {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.businessConfig.Callback
}

// UpdateBusinessConfig 更新业务配置
func (s *ConfigServiceImpl) UpdateBusinessConfig(config *BusinessConfig) error {
	if err := s.ValidateConfig(config); err != nil {
		return err
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.businessConfig = config
	return nil
}

// ReloadConfig 重新加载配置
func (s *ConfigServiceImpl) ReloadConfig() error {
	// 从配置文件重新加载
	_, err := config.Load()
	if err != nil {
		return err
	}

	// 转换为业务配置
	businessConfig := &BusinessConfig{
		Order: &OrderConfig{
			PreChargeEnabled: true, // 强制启用预扣费
			PreChargeTimeout: 30 * time.Second,
			CreateTimeout:    60 * time.Second,
			MaxOrdersPerUser: 100,
			MaxOrdersPerDay:  1000,
			MaxWeightKg:      decimal.NewFromFloat(100.0),
			MaxVolumeCubicM:  decimal.NewFromFloat(1.0),
		},
		Billing: &BillingConfig{
			PrecisionThreshold:    decimal.NewFromFloat(0.01),
			RoundingMode:          "ROUND_HALF_UP",
			MaxFeeAmount:          decimal.NewFromFloat(999999.99),
			MinFeeAmount:          decimal.NewFromFloat(0.01),
			AutoProcessThreshold:  decimal.NewFromFloat(100.0),
			ManualReviewThreshold: decimal.NewFromFloat(1000.0),
			ProcessTimeout:        30 * time.Second,
		},
		Balance: &BalanceConfig{
			MaxBalance:            decimal.NewFromFloat(1000000.0),
			MinBalance:            decimal.NewFromFloat(0.0),
			FreezeTimeout:         24 * time.Hour,
			AutoUnfreezeEnabled:   true,
			MaxTransactionAmount:  decimal.NewFromFloat(10000.0),
			DailyTransactionLimit: decimal.NewFromFloat(100000.0),
			ConcurrencyControl:    true,
			LockTimeout:           5 * time.Second,
		},
		Callback: &CallbackConfig{
			MaxConcurrency:      50,
			ProcessTimeout:      30 * time.Second,
			MaxRetries:          3,
			RetryInterval:       1 * time.Second,
			ForwardEnabled:      true,
			ForwardTimeout:      10 * time.Second,
			SignatureValidation: true,
			TimestampValidation: true,
			TimestampTolerance:  5 * time.Minute,
		},
		Transaction: &TransactionConfig{
			DefaultTimeout:     30 * time.Second,
			LongRunningTimeout: 5 * time.Minute,
			IsolationLevel:     "READ_COMMITTED",
			MaxRetries:         3,
			RetryInterval:      1 * time.Second,
			DeadlockDetection:  true,
			DeadlockTimeout:    10 * time.Second,
		},
		Retry: &RetryConfig{
			MaxAttempts:       3,
			InitialInterval:   1 * time.Second,
			MaxInterval:       30 * time.Second,
			BackoffMultiplier: 2.0,
			JitterEnabled:     true,
			JitterFactor:      0.1,
			RetryableErrors:   []string{"timeout", "connection_error", "temporary_failure"},
		},
	}

	return s.UpdateBusinessConfig(businessConfig)
}

// ValidateConfig 验证配置
func (s *ConfigServiceImpl) ValidateConfig(config *BusinessConfig) error {
	if config == nil {
		return ErrInvalidConfig
	}

	if config.Order == nil {
		return ErrInvalidConfig
	}

	if config.Order.PreChargeTimeout <= 0 {
		return ErrInvalidConfig
	}

	if config.Order.CreateTimeout <= 0 {
		return ErrInvalidConfig
	}

	return nil
}

// WatchConfigChanges 监听配置变化
func (s *ConfigServiceImpl) WatchConfigChanges(ctx context.Context, callback func(*BusinessConfig)) error {
	// 简单实现：定期检查配置变化
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			// 检查配置是否有变化
			callback(s.GetBusinessConfig())
		}
	}
}

// DefaultBusinessConfig 默认业务配置
func DefaultBusinessConfig() *BusinessConfig {
	return &BusinessConfig{
		Order: &OrderConfig{
			PreChargeEnabled: true, // 强制启用预扣费
			PreChargeTimeout: 30 * time.Second,
			CreateTimeout:    60 * time.Second,
			MaxOrdersPerUser: 100,
			MaxOrdersPerDay:  1000,
			MaxWeightKg:      decimal.NewFromFloat(100.0),
			MaxVolumeCubicM:  decimal.NewFromFloat(1.0),
		},
		Billing: &BillingConfig{
			PrecisionThreshold:    decimal.NewFromFloat(0.01),
			RoundingMode:          "ROUND_HALF_UP",
			MaxFeeAmount:          decimal.NewFromFloat(999999.99),
			MinFeeAmount:          decimal.NewFromFloat(0.01),
			AutoProcessThreshold:  decimal.NewFromFloat(100.0),
			ManualReviewThreshold: decimal.NewFromFloat(1000.0),
			ProcessTimeout:        30 * time.Second,
		},
		Balance: &BalanceConfig{
			MaxBalance:            decimal.NewFromFloat(1000000.0),
			MinBalance:            decimal.NewFromFloat(0.0),
			FreezeTimeout:         24 * time.Hour,
			AutoUnfreezeEnabled:   true,
			MaxTransactionAmount:  decimal.NewFromFloat(10000.0),
			DailyTransactionLimit: decimal.NewFromFloat(100000.0),
			ConcurrencyControl:    true,
			LockTimeout:           5 * time.Second,
		},
		Callback: &CallbackConfig{
			MaxConcurrency:      50,
			ProcessTimeout:      30 * time.Second,
			MaxRetries:          3,
			RetryInterval:       1 * time.Second,
			ForwardEnabled:      true,
			ForwardTimeout:      10 * time.Second,
			SignatureValidation: true,
			TimestampValidation: true,
			TimestampTolerance:  5 * time.Minute,
		},
		Transaction: &TransactionConfig{
			DefaultTimeout:     30 * time.Second,
			LongRunningTimeout: 5 * time.Minute,
			IsolationLevel:     "READ_COMMITTED",
			MaxRetries:         3,
			RetryInterval:      1 * time.Second,
			DeadlockDetection:  true,
			DeadlockTimeout:    10 * time.Second,
		},
		Retry: &RetryConfig{
			MaxAttempts:       3,
			InitialInterval:   1 * time.Second,
			MaxInterval:       30 * time.Second,
			BackoffMultiplier: 2.0,
			JitterEnabled:     true,
			JitterFactor:      0.1,
			RetryableErrors:   []string{"timeout", "connection_error", "temporary_failure"},
		},
	}
}
