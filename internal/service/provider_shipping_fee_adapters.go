package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"

)

// ProviderShippingFeeAdapter 供应商运费查询适配器接口
type ProviderShippingFeeAdapter interface {
	// 查询运费详情
	QueryShippingFeeDetail(ctx context.Context, orderRecord *model.OrderRecord) (*model.DetailedShippingFeeInfo, error)
	
	// 获取供应商名称
	GetProviderName() string
	
	// 是否支持运费查询
	IsSupported() bool
}

// Kuaidi100ShippingFeeAdapter 快递100运费查询适配器
type Kuaidi100ShippingFeeAdapter struct {
	providerAdapter adapter.ProviderAdapter
	logger          *zap.Logger
}

// NewKuaidi100ShippingFeeAdapter 创建快递100运费查询适配器
func NewKuaidi100ShippingFeeAdapter(providerAdapter adapter.ProviderAdapter, logger *zap.Logger) *Kuaidi100ShippingFeeAdapter {
	return &Kuaidi100ShippingFeeAdapter{
		providerAdapter: providerAdapter,
		logger:          logger,
	}
}

// QueryShippingFeeDetail 查询快递100运费详情
func (a *Kuaidi100ShippingFeeAdapter) QueryShippingFeeDetail(ctx context.Context, orderRecord *model.OrderRecord) (*model.DetailedShippingFeeInfo, error) {
	a.logger.Info("查询快递100运费详情",
		zap.String("order_no", orderRecord.OrderNo),
		zap.String("task_id", orderRecord.TaskId))

	// 调用快递100查询订单API
	orderInfo, err := a.providerAdapter.QueryOrder(ctx, orderRecord.TaskId, orderRecord.TrackingNo)
	if err != nil {
		return nil, fmt.Errorf("查询快递100订单信息失败: %w", err)
	}

	// 构建详细运费信息
	detailedInfo := &model.DetailedShippingFeeInfo{
		OrderID:     orderRecord.ID,
		OrderNo:     orderRecord.OrderNo,
		TrackingNo:  orderRecord.TrackingNo,
		Provider:    "kuaidi100",
		ExpressType: orderRecord.ExpressType,
		TotalFee:    orderInfo.Price,
		Currency:    "CNY",
		QueryTime:   util.NowBeijing().Format(time.RFC3339),
		Supported:   true,
	}

	// 解析费用明细（快递100可能返回详细费用信息）
	detailedInfo.FeeDetails = a.parseKuaidi100FeeDetails(orderInfo)
	
	// 构建重量信息
	if orderInfo.Weight > 0 {
		detailedInfo.WeightInfo = &model.ShippingWeightInfo{
			OrderWeight:   orderRecord.Weight,
			ActualWeight:  orderInfo.Weight,
			ChargedWeight: orderInfo.Weight,
		}
	}

	// 保存原始数据
	if rawData, err := json.Marshal(orderInfo); err == nil {
		detailedInfo.RawData = string(rawData)
	}

	return detailedInfo, nil
}

// parseKuaidi100FeeDetails 解析快递100费用明细
func (a *Kuaidi100ShippingFeeAdapter) parseKuaidi100FeeDetails(orderInfo *model.OrderInfo) []model.ShippingFeeDetail {
	var feeDetails []model.ShippingFeeDetail

	// 基础运费
	if orderInfo.Price > 0 {
		feeDetails = append(feeDetails, model.ShippingFeeDetail{
			Type:        "freight",
			TypeName:    "基础运费",
			Amount:      orderInfo.Price,
			Description: "快递100基础运费",
			Unit:        "元",
		})
	}

	// 如果有其他费用信息，可以在这里解析
	// 快递100的订单查询API可能返回更详细的费用信息

	return feeDetails
}

// GetProviderName 获取供应商名称
func (a *Kuaidi100ShippingFeeAdapter) GetProviderName() string {
	return "kuaidi100"
}

// IsSupported 是否支持运费查询
func (a *Kuaidi100ShippingFeeAdapter) IsSupported() bool {
	return true
}

// YuntongShippingFeeAdapter 云通运费查询适配器
type YuntongShippingFeeAdapter struct {
	providerAdapter adapter.ProviderAdapter
	logger          *zap.Logger
}

// NewYuntongShippingFeeAdapter 创建云通运费查询适配器
func NewYuntongShippingFeeAdapter(providerAdapter adapter.ProviderAdapter, logger *zap.Logger) *YuntongShippingFeeAdapter {
	return &YuntongShippingFeeAdapter{
		providerAdapter: providerAdapter,
		logger:          logger,
	}
}

// QueryShippingFeeDetail 查询云通运费详情
func (a *YuntongShippingFeeAdapter) QueryShippingFeeDetail(ctx context.Context, orderRecord *model.OrderRecord) (*model.DetailedShippingFeeInfo, error) {
	a.logger.Info("查询云通运费详情",
		zap.String("order_no", orderRecord.OrderNo),
		zap.String("tracking_no", orderRecord.TrackingNo))

	// 调用云通查询订单API
	orderInfo, err := a.providerAdapter.QueryOrder(ctx, orderRecord.OrderNo, orderRecord.TrackingNo)
	if err != nil {
		return nil, fmt.Errorf("查询云通订单信息失败: %w", err)
	}

	// 构建详细运费信息
	detailedInfo := &model.DetailedShippingFeeInfo{
		OrderID:     orderRecord.ID,
		OrderNo:     orderRecord.OrderNo,
		TrackingNo:  orderRecord.TrackingNo,
		Provider:    "yuntong",
		ExpressType: orderRecord.ExpressType,
		TotalFee:    orderInfo.Price,
		Currency:    "CNY",
		QueryTime:   util.NowBeijing().Format(time.RFC3339),
		Supported:   true,
	}

	// 解析费用明细（云通可能返回详细费用信息）
	detailedInfo.FeeDetails = a.parseYuntongFeeDetails(orderInfo)
	
	// 构建重量信息
	if orderInfo.Weight > 0 {
		detailedInfo.WeightInfo = &model.ShippingWeightInfo{
			OrderWeight:   orderRecord.Weight,
			ActualWeight:  orderInfo.Weight,
			ChargedWeight: orderInfo.Weight,
		}
	}

	// 保存原始数据
	if rawData, err := json.Marshal(orderInfo); err == nil {
		detailedInfo.RawData = string(rawData)
	}

	return detailedInfo, nil
}

// parseYuntongFeeDetails 解析云通费用明细
func (a *YuntongShippingFeeAdapter) parseYuntongFeeDetails(orderInfo *model.OrderInfo) []model.ShippingFeeDetail {
	var feeDetails []model.ShippingFeeDetail

	// 基础运费
	if orderInfo.Price > 0 {
		feeDetails = append(feeDetails, model.ShippingFeeDetail{
			Type:        "freight",
			TypeName:    "基础运费",
			Amount:      orderInfo.Price,
			Description: "云通基础运费",
			Unit:        "元",
		})
	}

	// 云通可能返回更详细的费用信息，可以在这里解析
	// 例如：燃油费、保价费等

	return feeDetails
}

// GetProviderName 获取供应商名称
func (a *YuntongShippingFeeAdapter) GetProviderName() string {
	return "yuntong"
}

// IsSupported 是否支持运费查询
func (a *YuntongShippingFeeAdapter) IsSupported() bool {
	return true
}

// YidaShippingFeeAdapter 易达运费查询适配器
type YidaShippingFeeAdapter struct {
	logger *zap.Logger
}

// NewYidaShippingFeeAdapter 创建易达运费查询适配器
func NewYidaShippingFeeAdapter(logger *zap.Logger) *YidaShippingFeeAdapter {
	return &YidaShippingFeeAdapter{
		logger: logger,
	}
}

// QueryShippingFeeDetail 查询易达运费详情
func (a *YidaShippingFeeAdapter) QueryShippingFeeDetail(ctx context.Context, orderRecord *model.OrderRecord) (*model.DetailedShippingFeeInfo, error) {
	// 易达不支持查询订单详情
	return &model.DetailedShippingFeeInfo{
		OrderID:     orderRecord.ID,
		OrderNo:     orderRecord.OrderNo,
		TrackingNo:  orderRecord.TrackingNo,
		Provider:    "yida",
		ExpressType: orderRecord.ExpressType,
		TotalFee:    0,
		Currency:    "CNY",
		QueryTime:   util.NowBeijing().Format(time.RFC3339),
		Supported:   false,
		Error:       "易达API不支持查询订单详情功能",
	}, nil
}

// GetProviderName 获取供应商名称
func (a *YidaShippingFeeAdapter) GetProviderName() string {
	return "yida"
}

// IsSupported 是否支持运费查询
func (a *YidaShippingFeeAdapter) IsSupported() bool {
	return false
}

// ProviderShippingFeeAdapterFactory 供应商运费查询适配器工厂
type ProviderShippingFeeAdapterFactory struct {
	providerManager *adapter.ProviderManager
	logger          *zap.Logger
}

// NewProviderShippingFeeAdapterFactory 创建供应商运费查询适配器工厂
func NewProviderShippingFeeAdapterFactory(providerManager *adapter.ProviderManager, logger *zap.Logger) *ProviderShippingFeeAdapterFactory {
	return &ProviderShippingFeeAdapterFactory{
		providerManager: providerManager,
		logger:          logger,
	}
}

// CreateAdapter 创建供应商运费查询适配器
func (f *ProviderShippingFeeAdapterFactory) CreateAdapter(provider string) (ProviderShippingFeeAdapter, error) {
	switch provider {
	case "kuaidi100":
		if providerAdapter, exists := f.providerManager.Get("kuaidi100"); exists {
			return NewKuaidi100ShippingFeeAdapter(providerAdapter, f.logger), nil
		}
		return nil, fmt.Errorf("快递100供应商适配器未注册")
	
	case "yuntong":
		if providerAdapter, exists := f.providerManager.Get("yuntong"); exists {
			return NewYuntongShippingFeeAdapter(providerAdapter, f.logger), nil
		}
		return nil, fmt.Errorf("云通供应商适配器未注册")
	
	case "yida":
		return NewYidaShippingFeeAdapter(f.logger), nil
	
	default:
		return nil, fmt.Errorf("不支持的供应商: %s", provider)
	}
}
