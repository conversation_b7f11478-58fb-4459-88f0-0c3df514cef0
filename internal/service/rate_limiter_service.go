package service

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// RateLimiterService 操作频率限制服务接口
type RateLimiterService interface {
	// 检查管理员操作频率限制
	CheckAdminOperationLimit(ctx context.Context, adminID, operation string) error
	// 记录管理员操作
	RecordAdminOperation(ctx context.Context, adminID, operation string) error
	// 清理过期的限制记录
	CleanupExpiredLimits(ctx context.Context) (int, error)
	// 获取管理员操作统计
	GetAdminOperationStats(ctx context.Context, adminID string, duration time.Duration) (map[string]int, error)
}

// DefaultRateLimiterService 默认频率限制服务实现
type DefaultRateLimiterService struct {
	db            *sql.DB
	configService SystemConfigService
	logger        *zap.Logger
}

// NewRateLimiterService 创建频率限制服务
func NewRateLimiterService(
	db *sql.DB,
	configService SystemConfigService,
	logger *zap.Logger,
) RateLimiterService {
	return &DefaultRateLimiterService{
		db:            db,
		configService: configService,
		logger:        logger,
	}
}

// CheckAdminOperationLimit 检查管理员操作频率限制
func (s *DefaultRateLimiterService) CheckAdminOperationLimit(ctx context.Context, adminID, operation string) error {
	// 检查是否启用频率限制
	rateLimitEnabled := s.configService.GetConfigAsBoolWithDefault("admin_balance.rate_limit_enabled", true)
	if !rateLimitEnabled {
		return nil
	}

	// 获取配置的限制参数
	maxOperationsPerMin := s.configService.GetConfigAsIntWithDefault("admin_balance.max_operations_per_minute", 10)
	
	// 查询当前时间窗口内的操作次数
	query := `
		SELECT operation_count 
		FROM admin_operation_limits 
		WHERE admin_id = $1 AND operation_type = $2 AND expires_at > NOW()
	`
	
	var currentCount int
	err := s.db.QueryRowContext(ctx, query, adminID, operation).Scan(&currentCount)
	if err != nil && err != sql.ErrNoRows {
		s.logger.Error("查询操作频率限制失败", 
			zap.String("admin_id", adminID),
			zap.String("operation", operation),
			zap.Error(err))
		return fmt.Errorf("查询操作频率限制失败: %w", err)
	}

	// 检查是否超过限制
	if currentCount >= maxOperationsPerMin {
		s.logger.Warn("管理员操作频率超限",
			zap.String("admin_id", adminID),
			zap.String("operation", operation),
			zap.Int("current_count", currentCount),
			zap.Int("max_allowed", maxOperationsPerMin))
		return fmt.Errorf("操作过于频繁，每分钟最多允许%d次%s操作", maxOperationsPerMin, operation)
	}

	return nil
}

// RecordAdminOperation 记录管理员操作
func (s *DefaultRateLimiterService) RecordAdminOperation(ctx context.Context, adminID, operation string) error {
	// 检查是否启用频率限制
	rateLimitEnabled := s.configService.GetConfigAsBoolWithDefault("admin_balance.rate_limit_enabled", true)
	if !rateLimitEnabled {
		return nil
	}

	// 使用UPSERT操作更新或插入操作记录
	query := `
		INSERT INTO admin_operation_limits (admin_id, operation_type, operation_count, window_start, expires_at)
		VALUES ($1, $2, 1, NOW(), NOW() + INTERVAL '1 minute')
		ON CONFLICT (admin_id, operation_type) 
		DO UPDATE SET 
			operation_count = CASE 
				WHEN admin_operation_limits.expires_at > NOW() THEN admin_operation_limits.operation_count + 1
				ELSE 1
			END,
			window_start = CASE 
				WHEN admin_operation_limits.expires_at > NOW() THEN admin_operation_limits.window_start
				ELSE NOW()
			END,
			expires_at = CASE 
				WHEN admin_operation_limits.expires_at > NOW() THEN admin_operation_limits.expires_at
				ELSE NOW() + INTERVAL '1 minute'
			END,
			updated_at = NOW()
	`

	_, err := s.db.ExecContext(ctx, query, adminID, operation)
	if err != nil {
		s.logger.Error("记录管理员操作失败",
			zap.String("admin_id", adminID),
			zap.String("operation", operation),
			zap.Error(err))
		return fmt.Errorf("记录管理员操作失败: %w", err)
	}

	s.logger.Debug("记录管理员操作成功",
		zap.String("admin_id", adminID),
		zap.String("operation", operation))

	return nil
}

// CleanupExpiredLimits 清理过期的限制记录
func (s *DefaultRateLimiterService) CleanupExpiredLimits(ctx context.Context) (int, error) {
	query := `DELETE FROM admin_operation_limits WHERE expires_at < NOW()`
	
	result, err := s.db.ExecContext(ctx, query)
	if err != nil {
		s.logger.Error("清理过期限制记录失败", zap.Error(err))
		return 0, fmt.Errorf("清理过期限制记录失败: %w", err)
	}

	deletedCount, err := result.RowsAffected()
	if err != nil {
		s.logger.Error("获取删除记录数失败", zap.Error(err))
		return 0, fmt.Errorf("获取删除记录数失败: %w", err)
	}

	if deletedCount > 0 {
		s.logger.Info("清理过期限制记录成功", zap.Int64("deleted_count", deletedCount))
	}

	return int(deletedCount), nil
}

// GetAdminOperationStats 获取管理员操作统计
func (s *DefaultRateLimiterService) GetAdminOperationStats(ctx context.Context, adminID string, duration time.Duration) (map[string]int, error) {
	query := `
		SELECT operation_type, SUM(operation_count) as total_count
		FROM admin_operation_limits 
		WHERE admin_id = $1 AND window_start >= NOW() - INTERVAL '%d minutes'
		GROUP BY operation_type
	`

	minutes := int(duration.Minutes())
	rows, err := s.db.QueryContext(ctx, fmt.Sprintf(query, minutes), adminID)
	if err != nil {
		s.logger.Error("查询管理员操作统计失败",
			zap.String("admin_id", adminID),
			zap.Duration("duration", duration),
			zap.Error(err))
		return nil, fmt.Errorf("查询管理员操作统计失败: %w", err)
	}
	defer rows.Close()

	stats := make(map[string]int)
	for rows.Next() {
		var operationType string
		var totalCount int
		
		if err := rows.Scan(&operationType, &totalCount); err != nil {
			s.logger.Error("扫描操作统计数据失败", zap.Error(err))
			continue
		}
		
		stats[operationType] = totalCount
	}

	if err := rows.Err(); err != nil {
		s.logger.Error("遍历操作统计数据失败", zap.Error(err))
		return nil, fmt.Errorf("遍历操作统计数据失败: %w", err)
	}

	return stats, nil
}

// AdminOperationLimitInfo 管理员操作限制信息
type AdminOperationLimitInfo struct {
	AdminID       string    `json:"admin_id"`
	OperationType string    `json:"operation_type"`
	CurrentCount  int       `json:"current_count"`
	MaxAllowed    int       `json:"max_allowed"`
	WindowStart   time.Time `json:"window_start"`
	ExpiresAt     time.Time `json:"expires_at"`
	IsLimited     bool      `json:"is_limited"`
}

// GetAdminOperationLimitInfo 获取管理员操作限制详细信息
func (s *DefaultRateLimiterService) GetAdminOperationLimitInfo(ctx context.Context, adminID, operation string) (*AdminOperationLimitInfo, error) {
	maxOperationsPerMin := s.configService.GetConfigAsIntWithDefault("admin_balance.max_operations_per_minute", 10)
	
	query := `
		SELECT operation_count, window_start, expires_at
		FROM admin_operation_limits 
		WHERE admin_id = $1 AND operation_type = $2 AND expires_at > NOW()
	`
	
	info := &AdminOperationLimitInfo{
		AdminID:       adminID,
		OperationType: operation,
		MaxAllowed:    maxOperationsPerMin,
		CurrentCount:  0,
		IsLimited:     false,
	}

	var windowStart, expiresAt time.Time
	err := s.db.QueryRowContext(ctx, query, adminID, operation).Scan(&info.CurrentCount, &windowStart, &expiresAt)
	if err != nil && err != sql.ErrNoRows {
		s.logger.Error("查询操作限制信息失败",
			zap.String("admin_id", adminID),
			zap.String("operation", operation),
			zap.Error(err))
		return nil, fmt.Errorf("查询操作限制信息失败: %w", err)
	}

	if err != sql.ErrNoRows {
		info.WindowStart = windowStart
		info.ExpiresAt = expiresAt
		info.IsLimited = info.CurrentCount >= info.MaxAllowed
	}

	return info, nil
}

// ResetAdminOperationLimit 重置管理员操作限制（管理员功能）
func (s *DefaultRateLimiterService) ResetAdminOperationLimit(ctx context.Context, adminID, operation string) error {
	query := `DELETE FROM admin_operation_limits WHERE admin_id = $1 AND operation_type = $2`
	
	_, err := s.db.ExecContext(ctx, query, adminID, operation)
	if err != nil {
		s.logger.Error("重置管理员操作限制失败",
			zap.String("admin_id", adminID),
			zap.String("operation", operation),
			zap.Error(err))
		return fmt.Errorf("重置管理员操作限制失败: %w", err)
	}

	s.logger.Info("重置管理员操作限制成功",
		zap.String("admin_id", adminID),
		zap.String("operation", operation))

	return nil
}

// GetAllAdminOperationLimits 获取所有管理员操作限制（监控功能）
func (s *DefaultRateLimiterService) GetAllAdminOperationLimits(ctx context.Context) ([]*AdminOperationLimitInfo, error) {
	query := `
		SELECT admin_id, operation_type, operation_count, window_start, expires_at
		FROM admin_operation_limits 
		WHERE expires_at > NOW()
		ORDER BY admin_id, operation_type
	`
	
	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		s.logger.Error("查询所有管理员操作限制失败", zap.Error(err))
		return nil, fmt.Errorf("查询所有管理员操作限制失败: %w", err)
	}
	defer rows.Close()

	maxOperationsPerMin := s.configService.GetConfigAsIntWithDefault("admin_balance.max_operations_per_minute", 10)
	var limits []*AdminOperationLimitInfo

	for rows.Next() {
		info := &AdminOperationLimitInfo{
			MaxAllowed: maxOperationsPerMin,
		}
		
		if err := rows.Scan(&info.AdminID, &info.OperationType, &info.CurrentCount, &info.WindowStart, &info.ExpiresAt); err != nil {
			s.logger.Error("扫描操作限制数据失败", zap.Error(err))
			continue
		}
		
		info.IsLimited = info.CurrentCount >= info.MaxAllowed
		limits = append(limits, info)
	}

	if err := rows.Err(); err != nil {
		s.logger.Error("遍历操作限制数据失败", zap.Error(err))
		return nil, fmt.Errorf("遍历操作限制数据失败: %w", err)
	}

	return limits, nil
}
