package service

import (
	"encoding/json"
	"strconv"
	"sync"

	"go.uber.org/zap"
)

// SimpleSystemConfigService 简单的系统配置服务实现
// 用于在没有完整数据库配置表的情况下提供基本的配置功能
type SimpleSystemConfigService struct {
	logger      *zap.Logger
	configCache map[string]string
	mutex       sync.RWMutex
}

// NewSimpleSystemConfigService 创建简单的系统配置服务
func NewSimpleSystemConfigService(logger *zap.Logger) SystemConfigServiceInterface {
	service := &SimpleSystemConfigService{
		logger:      logger,
		configCache: make(map[string]string),
	}

	// 初始化默认配置
	service.initDefaultConfig()

	return service
}

// initDefaultConfig 初始化默认配置
func (s *SimpleSystemConfigService) initDefaultConfig() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 基础配置
	s.configCache["max_page_size"] = "100"
	s.configCache["default_page_size"] = "20"
	s.configCache["default_sort_field"] = "created_at"
	s.configCache["default_sort_order"] = "desc" // 使用小写，符合验证规则
	s.configCache["batch_operation_max_items"] = "100"
	s.configCache["batch_export_max_items"] = "10000"

	// 搜索字段配置
	defaultSearchFields := []string{"customer_order_no", "order_no", "tracking_no"}
	searchFieldsJSON, _ := json.Marshal(defaultSearchFields)
	s.configCache["default_search_fields"] = string(searchFieldsJSON)

	s.logger.Info("Simple system config service initialized with default values")
}

// GetConfig 获取配置值
func (s *SimpleSystemConfigService) GetConfig(key string) (string, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	value, exists := s.configCache[key]
	if !exists {
		return "", ErrConfigNotFound
	}

	return value, nil
}

// GetConfigWithDefault 获取配置值，如果不存在则返回默认值
func (s *SimpleSystemConfigService) GetConfigWithDefault(key, defaultValue string) string {
	value, err := s.GetConfig(key)
	if err != nil {
		return defaultValue
	}
	return value
}

// GetConfigAsInt 获取整数配置值
func (s *SimpleSystemConfigService) GetConfigAsInt(key string) (int, error) {
	value, err := s.GetConfig(key)
	if err != nil {
		return 0, err
	}

	intValue, err := strconv.Atoi(value)
	if err != nil {
		return 0, ErrInvalidConfigValue
	}

	return intValue, nil
}

// GetConfigAsIntWithDefault 获取整数配置值，如果不存在或无效则返回默认值
func (s *SimpleSystemConfigService) GetConfigAsIntWithDefault(key string, defaultValue int) int {
	value, err := s.GetConfigAsInt(key)
	if err != nil {
		return defaultValue
	}
	return value
}

// GetConfigAsBool 获取布尔配置值
func (s *SimpleSystemConfigService) GetConfigAsBool(key string) (bool, error) {
	value, err := s.GetConfig(key)
	if err != nil {
		return false, err
	}

	boolValue, err := strconv.ParseBool(value)
	if err != nil {
		return false, ErrInvalidConfigValue
	}

	return boolValue, nil
}

// GetConfigAsBoolWithDefault 获取布尔配置值，如果不存在或无效则返回默认值
func (s *SimpleSystemConfigService) GetConfigAsBoolWithDefault(key string, defaultValue bool) bool {
	value, err := s.GetConfigAsBool(key)
	if err != nil {
		return defaultValue
	}
	return value
}

// GetConfigAsJSON 获取JSON配置值并解析到指定结构
func (s *SimpleSystemConfigService) GetConfigAsJSON(key string, result interface{}) error {
	value, err := s.GetConfig(key)
	if err != nil {
		return err
	}

	if err := json.Unmarshal([]byte(value), result); err != nil {
		return ErrInvalidConfigValue
	}

	return nil
}

// GetValidBillingStatuses 获取有效的计费状态列表
func (s *SimpleSystemConfigService) GetValidBillingStatuses() ([]string, error) {
	// 返回默认的计费状态
	return []string{"pending", "confirmed"}, nil // 已删除settled
}

// IsBillingStatusValid 检查计费状态是否有效
func (s *SimpleSystemConfigService) IsBillingStatusValid(status string) bool {
	validStatuses, _ := s.GetValidBillingStatuses()
	for _, validStatus := range validStatuses {
		if validStatus == status {
			return true
		}
	}
	return false
}

// GetValidSearchFields 获取有效的搜索字段列表
func (s *SimpleSystemConfigService) GetValidSearchFields(userType string) ([]string, error) {
	// 根据用户类型返回不同的搜索字段
	switch userType {
	case "admin":
		return []string{
			"customer_order_no", "order_no", "tracking_no", "user_id",
			"username", "user_email", "express_type", "provider",
		}, nil
	default:
		return []string{"customer_order_no", "order_no", "tracking_no"}, nil
	}
}

// IsSearchFieldValid 检查搜索字段是否有效
func (s *SimpleSystemConfigService) IsSearchFieldValid(field, userType string) bool {
	fields, err := s.GetValidSearchFields(userType)
	if err != nil {
		return false
	}

	for _, validField := range fields {
		if validField == field {
			return true
		}
	}
	return false
}

// GetDefaultSearchFields 获取默认搜索字段
func (s *SimpleSystemConfigService) GetDefaultSearchFields() ([]string, error) {
	var fields []string
	if err := s.GetConfigAsJSON("default_search_fields", &fields); err != nil {
		return []string{"customer_order_no", "order_no", "tracking_no"}, nil
	}
	return fields, nil
}

// GetValidSortFields 获取有效的排序字段映射
func (s *SimpleSystemConfigService) GetValidSortFields(userType string) (map[string]string, error) {
	// 根据用户类型返回不同的排序字段
	switch userType {
	case "admin":
		return map[string]string{
			"created_at":   "order_records.created_at",
			"updated_at":   "order_records.updated_at",
			"order_no":     "order_records.order_no",
			"tracking_no":  "order_records.tracking_no",
			"status":       "order_records.status",
			"express_type": "order_records.express_type",
			"provider":     "order_records.provider",
			"price":        "order_records.price",
			"weight":       "order_records.weight",
			"user_id":      "order_records.user_id",
		}, nil
	default:
		return map[string]string{
			"created_at":   "order_records.created_at",
			"updated_at":   "order_records.updated_at",
			"order_no":     "order_records.order_no",
			"tracking_no":  "order_records.tracking_no",
			"status":       "order_records.status",
			"express_type": "order_records.express_type",
			"price":        "order_records.price",
		}, nil
	}
}

// IsSortFieldValid 检查排序字段是否有效
func (s *SimpleSystemConfigService) IsSortFieldValid(field, userType string) bool {
	fields, err := s.GetValidSortFields(userType)
	if err != nil {
		return false
	}

	_, exists := fields[field]
	return exists
}

// GetDefaultSortField 获取默认排序字段
func (s *SimpleSystemConfigService) GetDefaultSortField() string {
	return s.GetConfigWithDefault("default_sort_field", "created_at")
}

// GetDefaultSortOrder 获取默认排序方向
func (s *SimpleSystemConfigService) GetDefaultSortOrder() string {
	return s.GetConfigWithDefault("default_sort_order", "desc")
}

// GetBatchOperationMaxItems 获取批量操作最大项目数
func (s *SimpleSystemConfigService) GetBatchOperationMaxItems() int {
	return s.GetConfigAsIntWithDefault("batch_operation_max_items", 100)
}

// GetExportMaxItems 获取导出最大项目数
func (s *SimpleSystemConfigService) GetExportMaxItems() int {
	return s.GetConfigAsIntWithDefault("batch_export_max_items", 10000)
}

// GetValidStatusTransitions 获取有效的状态转换列表
func (s *SimpleSystemConfigService) GetValidStatusTransitions(fromStatus, userType string) ([]string, error) {
	// 使用硬编码的状态转换规则
	transitions := map[string][]string{
		"submitted":          {"assigned", "submit_failed", "print_failed", "cancelled"},
		"assigned":           {"awaiting_pickup", "cancelled"},
		"awaiting_pickup":    {"picked_up", "pickup_failed", "cancelled"},
		"picked_up":          {"in_transit", "billed", "exception", "cancelled"},
		"in_transit":         {"out_for_delivery", "delivered", "exception", "returned", "forwarded"},
		"out_for_delivery":   {"delivered", "delivered_abnormal", "exception", "returned"},
		"delivered":          {"billed"},
		"delivered_abnormal": {"billed"},
		"exception":          {"in_transit", "returned", "cancelled"},
		"pickup_failed":      {"awaiting_pickup", "cancelled"},
		"returned":           {"cancelled"},
		"cancelled":          {"revived"},
	}

	if validTransitions, exists := transitions[fromStatus]; exists {
		return validTransitions, nil
	}

	return []string{}, nil
}

// IsStatusTransitionValid 检查状态转换是否有效
func (s *SimpleSystemConfigService) IsStatusTransitionValid(fromStatus, toStatus, userType string) bool {
	transitions, err := s.GetValidStatusTransitions(fromStatus, userType)
	if err != nil {
		return false
	}

	for _, validTransition := range transitions {
		if validTransition == toStatus {
			return true
		}
	}
	return false
}

// GetOperationPermissions 获取操作权限
func (s *SimpleSystemConfigService) GetOperationPermissions(status, userType string) (map[string]bool, error) {
	// 根据状态和用户类型返回操作权限
	permissions := map[string]bool{
		"can_cancel":        false,
		"can_update_status": false,
		"can_refund":        false,
		"can_export":        false,
		"can_view_detail":   true,
	}

	// 管理员有更多权限
	if userType == "admin" {
		permissions["can_export"] = true
		permissions["can_update_status"] = true

		switch status {
		case "submitted", "assigned", "awaiting_pickup":
			permissions["can_cancel"] = true
		case "picked_up", "in_transit":
			permissions["can_update_status"] = true
		case "delivered", "delivered_abnormal":
			permissions["can_refund"] = true
		case "exception":
			permissions["can_cancel"] = true
			permissions["can_refund"] = true
		}
	}

	return permissions, nil
}

// IsOperationAllowed 检查操作是否被允许
func (s *SimpleSystemConfigService) IsOperationAllowed(status, operation, userType string) bool {
	permissions, err := s.GetOperationPermissions(status, userType)
	if err != nil {
		return false
	}

	allowed, exists := permissions[operation]
	return exists && allowed
}

// RefreshCache 刷新缓存
func (s *SimpleSystemConfigService) RefreshCache() error {
	// 简单实现：重新初始化默认配置
	s.initDefaultConfig()
	s.logger.Info("Simple system config cache refreshed")
	return nil
}

// ClearCache 清空缓存
func (s *SimpleSystemConfigService) ClearCache() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.configCache = make(map[string]string)
	s.logger.Info("Simple system config cache cleared")
}
