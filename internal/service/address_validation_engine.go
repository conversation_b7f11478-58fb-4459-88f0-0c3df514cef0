package service

import (
	"context"
	"fmt"
	"strings"

	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// AddressValidationEngine 地址验证引擎
type AddressValidationEngine struct {
	logger          *zap.Logger
	addressLibrary  AddressLibraryService
	regionManager   RegionManagerInterface
	validationRules []ValidationRule
}

// ValidationRule 验证规则接口
type ValidationRule interface {
	Validate(ctx context.Context, addressInfo *model.AddressInfo, originalText string) (*ValidationResult, error)
	Priority() int
	Name() string
}

// ValidationResult 验证结果
type ValidationResult struct {
	IsValid     bool                   `json:"is_valid"`
	Confidence  float64                `json:"confidence"`  // 置信度 0-1
	Issues      []ValidationIssue      `json:"issues"`      // 发现的问题
	Corrections *model.AddressInfo     `json:"corrections"` // 修正建议
	Metadata    map[string]interface{} `json:"metadata"`    // 额外信息
}

// ValidationIssue 验证问题
type ValidationIssue struct {
	Type        string `json:"type"`        // 问题类型
	Field       string `json:"field"`       // 问题字段
	Description string `json:"description"` // 问题描述
	Severity    string `json:"severity"`    // 严重程度: low, medium, high
}

// RegionManagerInterface 地区管理器接口
type RegionManagerInterface interface {
	FindProvinceByName(name string) (*Province, error)
	FindCityByName(provinceName, cityName string) (*City, error)
	FindDistrictByName(provinceName, cityName, districtName string) (*District, error)
	ValidateAddressHierarchy(provinceName, cityName, districtName string) bool
}

// NewAddressValidationEngine 创建地址验证引擎
func NewAddressValidationEngine(
	logger *zap.Logger,
	addressLibrary AddressLibraryService,
	regionManager RegionManagerInterface,
) *AddressValidationEngine {
	engine := &AddressValidationEngine{
		logger:         logger,
		addressLibrary: addressLibrary,
		regionManager:  regionManager,
	}

	// 注册验证规则（按优先级排序）
	engine.validationRules = []ValidationRule{
		NewHierarchyValidationRule(logger, regionManager),
		NewMunicipalityValidationRule(logger, regionManager),
		NewNameConsistencyValidationRule(logger),
		NewCompletenessValidationRule(logger),
	}

	return engine
}

// ValidateAndCorrect 验证并修正地址信息
func (e *AddressValidationEngine) ValidateAndCorrect(ctx context.Context, addressInfo *model.AddressInfo, originalText string) (*model.AddressInfo, error) {
	e.logger.Info("🔍 开始地址验证和修正",
		zap.String("original_text", originalText),
		zap.String("province", addressInfo.ProvinceName),
		zap.String("city", addressInfo.CityName),
		zap.String("district", addressInfo.DistrictName))

	// 执行所有验证规则
	var allIssues []ValidationIssue
	var bestCorrection *model.AddressInfo
	var highestConfidence float64

	for _, rule := range e.validationRules {
		result, err := rule.Validate(ctx, addressInfo, originalText)
		if err != nil {
			e.logger.Warn("验证规则执行失败",
				zap.String("rule", rule.Name()),
				zap.Error(err))
			continue
		}

		// 收集问题
		allIssues = append(allIssues, result.Issues...)

		// 选择最佳修正建议
		if result.Corrections != nil && result.Confidence > highestConfidence {
			bestCorrection = result.Corrections
			highestConfidence = result.Confidence
		}

		e.logger.Debug("验证规则执行完成",
			zap.String("rule", rule.Name()),
			zap.Bool("valid", result.IsValid),
			zap.Float64("confidence", result.Confidence),
			zap.Int("issues", len(result.Issues)))
	}

	// 决定是否应用修正
	if bestCorrection != nil && highestConfidence > 0.8 {
		e.logger.Info("✅ 应用地址修正",
			zap.Float64("confidence", highestConfidence),
			zap.String("original_city", addressInfo.CityName),
			zap.String("corrected_city", bestCorrection.CityName),
			zap.String("original_district", addressInfo.DistrictName),
			zap.String("corrected_district", bestCorrection.DistrictName))

		return bestCorrection, nil
	}

	// 记录发现的问题
	if len(allIssues) > 0 {
		e.logger.Warn("⚠️ 地址验证发现问题",
			zap.Int("issue_count", len(allIssues)),
			zap.Any("issues", allIssues))
	}

	return addressInfo, nil
}

// HierarchyValidationRule 地址层次验证规则
type HierarchyValidationRule struct {
	logger        *zap.Logger
	regionManager RegionManagerInterface
}

func NewHierarchyValidationRule(logger *zap.Logger, regionManager RegionManagerInterface) *HierarchyValidationRule {
	return &HierarchyValidationRule{
		logger:        logger,
		regionManager: regionManager,
	}
}

func (r *HierarchyValidationRule) Name() string {
	return "HierarchyValidation"
}

func (r *HierarchyValidationRule) Priority() int {
	return 100
}

func (r *HierarchyValidationRule) Validate(ctx context.Context, addressInfo *model.AddressInfo, originalText string) (*ValidationResult, error) {
	result := &ValidationResult{
		IsValid:    true,
		Confidence: 1.0,
		Issues:     []ValidationIssue{},
		Metadata:   make(map[string]interface{}),
	}

	// 验证地址层次结构
	isValid := r.regionManager.ValidateAddressHierarchy(
		addressInfo.ProvinceName,
		addressInfo.CityName,
		addressInfo.DistrictName,
	)

	if !isValid {
		result.IsValid = false
		result.Issues = append(result.Issues, ValidationIssue{
			Type:        "hierarchy_mismatch",
			Field:       "address_hierarchy",
			Description: "地址层次结构不匹配",
			Severity:    "high",
		})

		// 尝试修正
		correction := r.attemptCorrection(addressInfo, originalText)
		if correction != nil {
			result.Corrections = correction
			result.Confidence = 0.9
		}
	}

	return result, nil
}

func (r *HierarchyValidationRule) attemptCorrection(addressInfo *model.AddressInfo, originalText string) *model.AddressInfo {
	// 实现地址层次修正逻辑
	// 这里可以根据原始文本和标准数据库进行智能修正
	return nil
}

// MunicipalityValidationRule 直辖市验证规则
type MunicipalityValidationRule struct {
	logger        *zap.Logger
	regionManager RegionManagerInterface
}

func NewMunicipalityValidationRule(logger *zap.Logger, regionManager RegionManagerInterface) *MunicipalityValidationRule {
	return &MunicipalityValidationRule{
		logger:        logger,
		regionManager: regionManager,
	}
}

func (r *MunicipalityValidationRule) Name() string {
	return "MunicipalityValidation"
}

func (r *MunicipalityValidationRule) Priority() int {
	return 90
}

func (r *MunicipalityValidationRule) Validate(ctx context.Context, addressInfo *model.AddressInfo, originalText string) (*ValidationResult, error) {
	result := &ValidationResult{
		IsValid:    true,
		Confidence: 1.0,
		Issues:     []ValidationIssue{},
		Metadata:   make(map[string]interface{}),
	}

	// 检查直辖市的特殊情况
	municipalities := []string{"北京", "天津", "上海", "重庆"}

	for _, municipality := range municipalities {
		if strings.Contains(originalText, municipality) || addressInfo.ProvinceName == municipality || strings.Contains(addressInfo.ProvinceName, municipality) {
			// 🔥 增强验证：检查原始文本中的区县信息
			if r.hasDistrictMismatch(addressInfo, municipality, originalText) {
				result.IsValid = false
				result.Issues = append(result.Issues, ValidationIssue{
					Type:        "municipality_district_mismatch",
					Field:       "district_name",
					Description: fmt.Sprintf("%s的区县解析结果与原始文本不匹配", municipality),
					Severity:    "high",
				})

				// 生成修正建议
				correction := r.correctMunicipalityStructure(addressInfo, municipality, originalText)
				if correction != nil {
					result.Corrections = correction
					result.Confidence = 0.95
				}
			} else if r.isDistrictMisclassifiedAsCity(addressInfo, municipality) {
				result.IsValid = false
				result.Issues = append(result.Issues, ValidationIssue{
					Type:        "municipality_structure_error",
					Field:       "city_district",
					Description: fmt.Sprintf("%s的区县被错误识别为城市", municipality),
					Severity:    "high",
				})

				// 生成修正建议
				correction := r.correctMunicipalityStructure(addressInfo, municipality, originalText)
				if correction != nil {
					result.Corrections = correction
					result.Confidence = 0.95
				}
			}
			break
		}
	}

	return result, nil
}

func (r *MunicipalityValidationRule) isDistrictMisclassifiedAsCity(addressInfo *model.AddressInfo, municipality string) bool {
	// 检查是否是直辖市的区县被错误识别为城市
	municipalityDistricts := map[string][]string{
		"北京": {"东城区", "西城区", "朝阳区", "丰台区", "石景山区", "海淀区", "门头沟区", "房山区", "通州区", "顺义区", "昌平区", "大兴区", "怀柔区", "平谷区", "密云区", "延庆区"},
		"上海": {"黄浦区", "徐汇区", "长宁区", "静安区", "普陀区", "虹口区", "杨浦区", "闵行区", "宝山区", "嘉定区", "浦东新区", "金山区", "松江区", "青浦区", "奉贤区", "崇明区"},
		"天津": {"和平区", "河东区", "河西区", "南开区", "河北区", "红桥区", "东丽区", "西青区", "津南区", "北辰区", "武清区", "宝坻区", "滨海新区", "宁河区", "静海区", "蓟州区"},
		"重庆": {"万州区", "涪陵区", "渝中区", "大渡口区", "江北区", "沙坪坝区", "九龙坡区", "南岸区", "北碚区", "綦江区", "大足区", "渝北区", "巴南区", "黔江区", "长寿区", "江津区", "合川区", "永川区", "南川区", "璧山区", "铜梁区", "潼南区", "荣昌区", "开州区", "梁平区", "武隆区"},
	}

	districts, exists := municipalityDistricts[municipality]
	if !exists {
		return false
	}

	// 检查城市字段是否包含区县名称
	for _, district := range districts {
		if addressInfo.CityName == district {
			return true
		}
	}

	return false
}

func (r *MunicipalityValidationRule) correctMunicipalityStructure(addressInfo *model.AddressInfo, municipality, originalText string) *model.AddressInfo {
	// 创建修正后的地址信息
	corrected := &model.AddressInfo{
		Name:          addressInfo.Name,
		Mobile:        addressInfo.Mobile,
		Phone:         addressInfo.Phone,
		DetailAddress: addressInfo.DetailAddress,
		FullAddress:   addressInfo.FullAddress,
		Latitude:      addressInfo.Latitude,
		Longitude:     addressInfo.Longitude,
		Confidence:    addressInfo.Confidence,
	}

	// 修正直辖市结构
	switch municipality {
	case "北京":
		corrected.ProvinceCode = "110000"
		corrected.ProvinceName = "北京市"
		corrected.CityCode = "110100"
		corrected.CityName = "北京市"

		// 如果城市字段是区县，移动到区县字段
		if r.isBeijingDistrict(addressInfo.CityName) {
			corrected.DistrictName = addressInfo.CityName
			corrected.DistrictCode = r.getBeijingDistrictCode(addressInfo.CityName)

			// 如果原区县字段是街道，移动到街道字段
			if strings.Contains(addressInfo.DistrictName, "街道") {
				corrected.TownName = addressInfo.DistrictName
			}
		}

	case "天津":
		corrected.ProvinceCode = "120000"
		corrected.ProvinceName = "天津市"
		corrected.CityCode = "120100"
		corrected.CityName = "天津市"

		// 🔥 增强天津地址处理：从原始文本中提取正确的区县
		originalDistrict := r.extractDistrictFromOriginalText("天津", originalText)
		if originalDistrict != "" {
			corrected.DistrictName = originalDistrict
			corrected.DistrictCode = r.getTianjinDistrictCode(originalDistrict)

			r.logger.Info("🔧 天津地址修正成功",
				zap.String("original_text", originalText),
				zap.String("extracted_district", originalDistrict),
				zap.String("district_code", corrected.DistrictCode))
		} else if r.isTianjinDistrict(addressInfo.CityName) || r.isTianjinDistrict(addressInfo.DistrictName) {
			// 从城市或区县字段中提取正确的区县名称
			districtName := r.extractTianjinDistrict(addressInfo.CityName, addressInfo.DistrictName)
			if districtName != "" {
				corrected.DistrictName = districtName
				corrected.DistrictCode = r.getTianjinDistrictCode(districtName)
			}
		}

		// 如果原区县字段是街道，移动到街道字段
		if strings.Contains(addressInfo.DistrictName, "街道") {
			corrected.TownName = addressInfo.DistrictName
		}

	case "上海":
		corrected.ProvinceCode = "310000"
		corrected.ProvinceName = "上海市"
		corrected.CityCode = "310100"
		corrected.CityName = "上海市"

		if r.isShanghaiDistrict(addressInfo.CityName) {
			corrected.DistrictName = addressInfo.CityName
			corrected.DistrictCode = r.getShanghaiDistrictCode(addressInfo.CityName)

			if strings.Contains(addressInfo.DistrictName, "街道") {
				corrected.TownName = addressInfo.DistrictName
			}
		}

	case "重庆":
		corrected.ProvinceCode = "500000"
		corrected.ProvinceName = "重庆市"
		corrected.CityCode = "500100"
		corrected.CityName = "重庆市"

		if r.isChongqingDistrict(addressInfo.CityName) {
			corrected.DistrictName = addressInfo.CityName
			corrected.DistrictCode = r.getChongqingDistrictCode(addressInfo.CityName)

			if strings.Contains(addressInfo.DistrictName, "街道") {
				corrected.TownName = addressInfo.DistrictName
			}
		}
	}

	return corrected
}

func (r *MunicipalityValidationRule) isBeijingDistrict(name string) bool {
	beijingDistricts := []string{"东城区", "西城区", "朝阳区", "丰台区", "石景山区", "海淀区", "门头沟区", "房山区", "通州区", "顺义区", "昌平区", "大兴区", "怀柔区", "平谷区", "密云区", "延庆区"}
	for _, district := range beijingDistricts {
		if name == district {
			return true
		}
	}
	return false
}

func (r *MunicipalityValidationRule) getBeijingDistrictCode(name string) string {
	codes := map[string]string{
		"东城区": "110101", "西城区": "110102", "朝阳区": "110105", "丰台区": "110106",
		"石景山区": "110107", "海淀区": "110108", "门头沟区": "110109", "房山区": "110111",
		"通州区": "110112", "顺义区": "110113", "昌平区": "110114", "大兴区": "110115",
		"怀柔区": "110116", "平谷区": "110117", "密云区": "110118", "延庆区": "110119",
	}
	return codes[name]
}

func (r *MunicipalityValidationRule) isShanghaiDistrict(name string) bool {
	shanghaiDistricts := []string{"黄浦区", "徐汇区", "长宁区", "静安区", "普陀区", "虹口区", "杨浦区", "闵行区", "宝山区", "嘉定区", "浦东新区", "金山区", "松江区", "青浦区", "奉贤区", "崇明区"}
	for _, district := range shanghaiDistricts {
		if name == district {
			return true
		}
	}
	return false
}

func (r *MunicipalityValidationRule) getShanghaiDistrictCode(name string) string {
	codes := map[string]string{
		"黄浦区": "310101", "徐汇区": "310104", "长宁区": "310105", "静安区": "310106",
		"普陀区": "310107", "虹口区": "310109", "杨浦区": "310110", "闵行区": "310112",
		"宝山区": "310113", "嘉定区": "310114", "浦东新区": "310115", "金山区": "310116",
		"松江区": "310117", "青浦区": "310118", "奉贤区": "310120", "崇明区": "310151",
	}
	return codes[name]
}

// NameConsistencyValidationRule 名称一致性验证规则
type NameConsistencyValidationRule struct {
	logger *zap.Logger
}

func NewNameConsistencyValidationRule(logger *zap.Logger) *NameConsistencyValidationRule {
	return &NameConsistencyValidationRule{
		logger: logger,
	}
}

func (r *NameConsistencyValidationRule) Name() string {
	return "NameConsistencyValidation"
}

func (r *NameConsistencyValidationRule) Priority() int {
	return 80
}

func (r *NameConsistencyValidationRule) Validate(ctx context.Context, addressInfo *model.AddressInfo, originalText string) (*ValidationResult, error) {
	result := &ValidationResult{
		IsValid:    true,
		Confidence: 1.0,
		Issues:     []ValidationIssue{},
		Metadata:   make(map[string]interface{}),
	}

	// 检查原始文本与解析结果的一致性
	if !strings.Contains(originalText, addressInfo.ProvinceName) &&
		!strings.Contains(originalText, strings.TrimSuffix(addressInfo.ProvinceName, "市")) &&
		!strings.Contains(originalText, strings.TrimSuffix(addressInfo.ProvinceName, "省")) {
		result.Issues = append(result.Issues, ValidationIssue{
			Type:        "province_name_mismatch",
			Field:       "province_name",
			Description: "省份名称与原始文本不匹配",
			Severity:    "medium",
		})
	}

	if !strings.Contains(originalText, addressInfo.CityName) &&
		!strings.Contains(originalText, strings.TrimSuffix(addressInfo.CityName, "市")) {
		result.Issues = append(result.Issues, ValidationIssue{
			Type:        "city_name_mismatch",
			Field:       "city_name",
			Description: "城市名称与原始文本不匹配",
			Severity:    "medium",
		})
	}

	if !strings.Contains(originalText, addressInfo.DistrictName) &&
		!strings.Contains(originalText, strings.TrimSuffix(addressInfo.DistrictName, "区")) &&
		!strings.Contains(originalText, strings.TrimSuffix(addressInfo.DistrictName, "县")) {
		result.Issues = append(result.Issues, ValidationIssue{
			Type:        "district_name_mismatch",
			Field:       "district_name",
			Description: "区县名称与原始文本不匹配",
			Severity:    "medium",
		})
	}

	if len(result.Issues) > 0 {
		result.IsValid = false
		result.Confidence = 0.6
	}

	return result, nil
}

// CompletenessValidationRule 完整性验证规则
type CompletenessValidationRule struct {
	logger *zap.Logger
}

func NewCompletenessValidationRule(logger *zap.Logger) *CompletenessValidationRule {
	return &CompletenessValidationRule{
		logger: logger,
	}
}

func (r *CompletenessValidationRule) Name() string {
	return "CompletenessValidation"
}

func (r *CompletenessValidationRule) Priority() int {
	return 70
}

func (r *CompletenessValidationRule) Validate(ctx context.Context, addressInfo *model.AddressInfo, originalText string) (*ValidationResult, error) {
	result := &ValidationResult{
		IsValid:    true,
		Confidence: 1.0,
		Issues:     []ValidationIssue{},
		Metadata:   make(map[string]interface{}),
	}

	// 检查必要字段的完整性
	if addressInfo.Name == "" || addressInfo.Name == "未识别" {
		result.Issues = append(result.Issues, ValidationIssue{
			Type:        "missing_name",
			Field:       "name",
			Description: "缺少收件人姓名",
			Severity:    "high",
		})
	}

	if addressInfo.Mobile == "" && addressInfo.Phone == "" {
		result.Issues = append(result.Issues, ValidationIssue{
			Type:        "missing_contact",
			Field:       "mobile_phone",
			Description: "缺少联系电话",
			Severity:    "high",
		})
	}

	if addressInfo.ProvinceName == "" || addressInfo.ProvinceName == "未识别省份" {
		result.Issues = append(result.Issues, ValidationIssue{
			Type:        "missing_province",
			Field:       "province_name",
			Description: "缺少省份信息",
			Severity:    "high",
		})
	}

	if addressInfo.CityName == "" || addressInfo.CityName == "未识别城市" {
		result.Issues = append(result.Issues, ValidationIssue{
			Type:        "missing_city",
			Field:       "city_name",
			Description: "缺少城市信息",
			Severity:    "high",
		})
	}

	if addressInfo.DetailAddress == "" || addressInfo.DetailAddress == "未识别详细地址" {
		result.Issues = append(result.Issues, ValidationIssue{
			Type:        "missing_detail",
			Field:       "detail_address",
			Description: "缺少详细地址",
			Severity:    "high",
		})
	}

	// 计算完整性得分
	totalFields := 5
	missingFields := len(result.Issues)

	if missingFields > 0 {
		result.IsValid = false
		result.Confidence = float64(totalFields-missingFields) / float64(totalFields)
	}

	return result, nil
}

// 天津市相关方法
func (r *MunicipalityValidationRule) isTianjinDistrict(name string) bool {
	tianjinDistricts := []string{"和平区", "河东区", "河西区", "南开区", "河北区", "红桥区", "东丽区", "西青区", "津南区", "北辰区", "武清区", "宝坻区", "滨海新区", "宁河区", "静海区", "蓟州区"}
	for _, district := range tianjinDistricts {
		if strings.Contains(name, district) {
			return true
		}
	}
	return false
}

func (r *MunicipalityValidationRule) extractTianjinDistrict(cityName, districtName string) string {
	tianjinDistricts := []string{"和平区", "河东区", "河西区", "南开区", "河北区", "红桥区", "东丽区", "西青区", "津南区", "北辰区", "武清区", "宝坻区", "滨海新区", "宁河区", "静海区", "蓟州区"}

	// 从城市名称中提取
	for _, district := range tianjinDistricts {
		if strings.Contains(cityName, district) {
			return district
		}
	}

	// 从区县名称中提取
	for _, district := range tianjinDistricts {
		if strings.Contains(districtName, district) {
			return district
		}
	}

	return ""
}

func (r *MunicipalityValidationRule) getTianjinDistrictCode(name string) string {
	codes := map[string]string{
		"和平区": "120101", "河东区": "120102", "河西区": "120103", "南开区": "120104",
		"河北区": "120105", "红桥区": "120106", "东丽区": "120110", "西青区": "120111",
		"津南区": "120112", "北辰区": "120113", "武清区": "120114", "宝坻区": "120115",
		"滨海新区": "120116", "宁河区": "120117", "静海区": "120118", "蓟州区": "120119",
	}
	return codes[name]
}

// 重庆市相关方法
func (r *MunicipalityValidationRule) isChongqingDistrict(name string) bool {
	chongqingDistricts := []string{"万州区", "涪陵区", "渝中区", "大渡口区", "江北区", "沙坪坝区", "九龙坡区", "南岸区", "北碚区", "綦江区", "大足区", "渝北区", "巴南区", "黔江区", "长寿区", "江津区", "合川区", "永川区", "南川区", "璧山区", "铜梁区", "潼南区", "荣昌区", "开州区", "梁平区", "武隆区"}
	for _, district := range chongqingDistricts {
		if name == district {
			return true
		}
	}
	return false
}

func (r *MunicipalityValidationRule) getChongqingDistrictCode(name string) string {
	codes := map[string]string{
		"万州区": "500101", "涪陵区": "500102", "渝中区": "500103", "大渡口区": "500104",
		"江北区": "500105", "沙坪坝区": "500106", "九龙坡区": "500107", "南岸区": "500108",
		"北碚区": "500109", "綦江区": "500110", "大足区": "500111", "渝北区": "500112",
		"巴南区": "500113", "黔江区": "500114", "长寿区": "500115", "江津区": "500116",
		"合川区": "500117", "永川区": "500118", "南川区": "500119", "璧山区": "500120",
		"铜梁区": "500121", "潼南区": "500122", "荣昌区": "500123", "开州区": "500124",
		"梁平区": "500125", "武隆区": "500126",
	}
	return codes[name]
}

// hasDistrictMismatch 检查区县信息是否与原始文本不匹配
func (r *MunicipalityValidationRule) hasDistrictMismatch(addressInfo *model.AddressInfo, municipality, originalText string) bool {
	switch municipality {
	case "天津":
		// 检查原始文本中是否包含天津的区县
		tianjinDistricts := []string{"和平区", "河东区", "河西区", "南开区", "河北区", "红桥区", "东丽区", "西青区", "津南区", "北辰区", "武清区", "宝坻区", "滨海新区", "宁河区", "静海区", "蓟州区"}

		// 查找原始文本中的区县
		var originalDistrict string
		for _, district := range tianjinDistricts {
			if strings.Contains(originalText, district) {
				originalDistrict = district
				break
			}
		}

		// 如果找到了原始区县，但解析结果不匹配
		if originalDistrict != "" && addressInfo.DistrictName != originalDistrict {
			r.logger.Warn("🔧 检测到天津区县不匹配",
				zap.String("original_text", originalText),
				zap.String("original_district", originalDistrict),
				zap.String("parsed_district", addressInfo.DistrictName))
			return true
		}

	case "北京":
		beijingDistricts := []string{"东城区", "西城区", "朝阳区", "丰台区", "石景山区", "海淀区", "门头沟区", "房山区", "通州区", "顺义区", "昌平区", "大兴区", "怀柔区", "平谷区", "密云区", "延庆区"}

		var originalDistrict string
		for _, district := range beijingDistricts {
			if strings.Contains(originalText, district) {
				originalDistrict = district
				break
			}
		}

		if originalDistrict != "" && addressInfo.DistrictName != originalDistrict {
			return true
		}

	case "上海":
		shanghaiDistricts := []string{"黄浦区", "徐汇区", "长宁区", "静安区", "普陀区", "虹口区", "杨浦区", "闵行区", "宝山区", "嘉定区", "浦东新区", "金山区", "松江区", "青浦区", "奉贤区", "崇明区"}

		var originalDistrict string
		for _, district := range shanghaiDistricts {
			if strings.Contains(originalText, district) {
				originalDistrict = district
				break
			}
		}

		if originalDistrict != "" && addressInfo.DistrictName != originalDistrict {
			return true
		}

	case "重庆":
		chongqingDistricts := []string{"万州区", "涪陵区", "渝中区", "大渡口区", "江北区", "沙坪坝区", "九龙坡区", "南岸区", "北碚区", "綦江区", "大足区", "渝北区", "巴南区", "黔江区", "长寿区", "江津区", "合川区", "永川区", "南川区", "璧山区", "铜梁区", "潼南区", "荣昌区", "开州区", "梁平区", "武隆区"}

		var originalDistrict string
		for _, district := range chongqingDistricts {
			if strings.Contains(originalText, district) {
				originalDistrict = district
				break
			}
		}

		if originalDistrict != "" && addressInfo.DistrictName != originalDistrict {
			return true
		}
	}

	return false
}

// extractDistrictFromOriginalText 从原始文本中提取区县信息
func (r *MunicipalityValidationRule) extractDistrictFromOriginalText(municipality, originalText string) string {
	switch municipality {
	case "天津":
		tianjinDistricts := []string{"和平区", "河东区", "河西区", "南开区", "河北区", "红桥区", "东丽区", "西青区", "津南区", "北辰区", "武清区", "宝坻区", "滨海新区", "宁河区", "静海区", "蓟州区"}

		for _, district := range tianjinDistricts {
			if strings.Contains(originalText, district) {
				return district
			}
		}

	case "北京":
		beijingDistricts := []string{"东城区", "西城区", "朝阳区", "丰台区", "石景山区", "海淀区", "门头沟区", "房山区", "通州区", "顺义区", "昌平区", "大兴区", "怀柔区", "平谷区", "密云区", "延庆区"}

		for _, district := range beijingDistricts {
			if strings.Contains(originalText, district) {
				return district
			}
		}

	case "上海":
		shanghaiDistricts := []string{"黄浦区", "徐汇区", "长宁区", "静安区", "普陀区", "虹口区", "杨浦区", "闵行区", "宝山区", "嘉定区", "浦东新区", "金山区", "松江区", "青浦区", "奉贤区", "崇明区"}

		for _, district := range shanghaiDistricts {
			if strings.Contains(originalText, district) {
				return district
			}
		}

	case "重庆":
		chongqingDistricts := []string{"万州区", "涪陵区", "渝中区", "大渡口区", "江北区", "沙坪坝区", "九龙坡区", "南岸区", "北碚区", "綦江区", "大足区", "渝北区", "巴南区", "黔江区", "长寿区", "江津区", "合川区", "永川区", "南川区", "璧山区", "铜梁区", "潼南区", "荣昌区", "开州区", "梁平区", "武隆区"}

		for _, district := range chongqingDistricts {
			if strings.Contains(originalText, district) {
				return district
			}
		}
	}

	return ""
}
