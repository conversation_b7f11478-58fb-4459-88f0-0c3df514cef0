package service

import (
	"fmt"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/model"
)

// 🔥 企业级交易描述生成器
// 提供智能、多层次的交易描述生成功能

// TransactionDescriptionGenerator 交易描述生成器
type TransactionDescriptionGenerator struct {
	templates map[model.TransactionType]DescriptionTemplate
}

// DescriptionTemplate 描述模板
type DescriptionTemplate struct {
	ShortTemplate    string // 简短描述模板
	DetailTemplate   string // 详细描述模板
	FriendlyTemplate string // 用户友好描述模板
}

// TransactionContext 交易上下文
type TransactionContext struct {
	// 基础信息
	Amount   decimal.Decimal `json:"amount"`
	Currency string          `json:"currency"`

	// 订单信息
	OrderNo         string `json:"order_no"`
	PlatformOrderNo string `json:"platform_order_no"` // 🔥 新增：平台订单号
	CustomerOrderNo string `json:"customer_order_no"`
	TrackingNo      string `json:"tracking_no"`

	// 业务上下文
	OriginalWeight float64 `json:"original_weight,omitempty"`
	ActualWeight   float64 `json:"actual_weight,omitempty"`
	WeightDiff     float64 `json:"weight_diff,omitempty"`
	Reason         string  `json:"reason,omitempty"`
	OperatorName   string  `json:"operator_name,omitempty"`

	// 费用明细
	OriginalFee    decimal.Decimal `json:"original_fee,omitempty"`
	AdjustmentType string          `json:"adjustment_type,omitempty"`

	// 时间信息
	Timestamp time.Time `json:"timestamp"`
}

// NewTransactionDescriptionGenerator 创建交易描述生成器
func NewTransactionDescriptionGenerator() *TransactionDescriptionGenerator {
	generator := &TransactionDescriptionGenerator{
		templates: make(map[model.TransactionType]DescriptionTemplate),
	}
	generator.initTemplates()
	return generator
}

// initTemplates 初始化描述模板
func (g *TransactionDescriptionGenerator) initTemplates() {
	// 充值类模板
	g.templates[model.TransactionTypeUserDeposit] = DescriptionTemplate{
		ShortTemplate:    "用户充值 {{.Amount}}{{.Currency}}",
		DetailTemplate:   "用户通过{{.PaymentMethod}}充值{{.Amount}}{{.Currency}}",
		FriendlyTemplate: "您成功充值了{{.Amount}}元，余额已到账",
	}

	g.templates[model.TransactionTypeAdminDeposit] = DescriptionTemplate{
		ShortTemplate:    "管理员充值 {{.Amount}}{{.Currency}}",
		DetailTemplate:   "管理员{{.OperatorName}}为用户充值{{.Amount}}{{.Currency}}",
		FriendlyTemplate: "管理员为您充值了{{.Amount}}元",
	}

	// 支付类模板
	g.templates[model.TransactionTypeOrderPreCharge] = DescriptionTemplate{
		ShortTemplate:    "订单{{.CustomerOrderNo}}下单预收 {{.Amount}}{{.Currency}}",
		DetailTemplate:   "订单{{.CustomerOrderNo}}下单时预收运费{{.Amount}}{{.Currency}}",
		FriendlyTemplate: "订单{{.CustomerOrderNo}}预收费用{{.Amount}}元",
	}

	// 🚨 已删除：TransactionTypePickupActualCharge - 揽件实收_多退少补（与超重/超轻逻辑冲突）
	// 现在使用 TransactionTypeOverweightCharge 和 TransactionTypeUnderweightRefund 替代

	g.templates[model.TransactionTypeOrderInterceptCharge] = DescriptionTemplate{
		ShortTemplate:    "订单{{.CustomerOrderNo}}拦截补收 {{.Amount}}{{.Currency}}",
		DetailTemplate:   "订单{{.CustomerOrderNo}}在运输过程中被拦截，产生额外费用{{.Amount}}{{.Currency}}",
		FriendlyTemplate: "订单{{.CustomerOrderNo}}拦截费用{{.Amount}}元",
	}

	g.templates[model.TransactionTypeReturnCharge] = DescriptionTemplate{
		ShortTemplate:    "订单{{.CustomerOrderNo}}退回收费 {{.Amount}}{{.Currency}}",
		DetailTemplate:   "订单{{.CustomerOrderNo}}(运单号:{{.TrackingNo}})包裹退回产生费用{{.Amount}}{{.Currency}}，原因：{{.Reason}}",
		FriendlyTemplate: "订单{{.CustomerOrderNo}}包裹退回，产生费用{{.Amount}}元",
	}

	g.templates[model.TransactionTypeOrderReviveRecharge] = DescriptionTemplate{
		ShortTemplate:    "订单{{.CustomerOrderNo}}复活重计费 {{.Amount}}{{.Currency}}",
		DetailTemplate:   "已取消订单{{.CustomerOrderNo}}重新激活，重新计费{{.Amount}}{{.Currency}}",
		FriendlyTemplate: "订单{{.CustomerOrderNo}}重新激活，计费{{.Amount}}元",
	}

	// 退款类模板
	g.templates[model.TransactionTypeOrderCancelRefund] = DescriptionTemplate{
		ShortTemplate:    "订单{{.CustomerOrderNo}}取消退款 {{.Amount}}{{.Currency}}",
		DetailTemplate:   "订单{{.CustomerOrderNo}}已取消，退还运费{{.Amount}}{{.Currency}}",
		FriendlyTemplate: "订单{{.CustomerOrderNo}}已取消，退款{{.Amount}}元已到账",
	}

	// 调整类模板
	g.templates[model.TransactionTypeBalanceAdjustment] = DescriptionTemplate{
		ShortTemplate:    "调账 {{.Amount}}{{.Currency}}",
		DetailTemplate:   "管理员{{.OperatorName}}进行余额调账{{.Amount}}{{.Currency}}，原因：{{.Reason}}",
		FriendlyTemplate: "管理员为您调账{{.Amount}}元",
	}
}

// GenerateDescriptions 生成交易描述
func (g *TransactionDescriptionGenerator) GenerateDescriptions(
	transactionType model.TransactionType,
	context TransactionContext,
) (short, detail, friendly string) {
	template, exists := g.templates[transactionType]
	if !exists {
		// 使用默认模板
		return g.generateDefaultDescriptions(transactionType, context)
	}

	// 准备模板变量
	vars := g.prepareTemplateVars(context)

	// 生成各种描述
	short = g.renderTemplate(template.ShortTemplate, vars)
	detail = g.renderTemplate(template.DetailTemplate, vars)
	friendly = g.renderTemplate(template.FriendlyTemplate, vars)

	return short, detail, friendly
}

// prepareTemplateVars 准备模板变量
func (g *TransactionDescriptionGenerator) prepareTemplateVars(context TransactionContext) map[string]interface{} {
	vars := map[string]interface{}{
		"Amount":          context.Amount.String(),
		"Currency":        context.Currency,
		"OrderNo":         context.OrderNo,
		"CustomerOrderNo": context.CustomerOrderNo,
		"TrackingNo":      context.TrackingNo,
		"OriginalWeight":  fmt.Sprintf("%.2f", context.OriginalWeight),
		"ActualWeight":    fmt.Sprintf("%.2f", context.ActualWeight),
		"WeightDiff":      fmt.Sprintf("%.2f", context.WeightDiff),
		"Reason":          context.Reason,
		"OperatorName":    context.OperatorName,
		"OriginalFee":     context.OriginalFee.String(),
		"AdjustmentType":  context.AdjustmentType,
		"Timestamp":       context.Timestamp.Format("2006-01-02 15:04:05"),
	}

	// 处理调整类型的显示
	if context.AdjustmentType == "increase" {
		vars["AdjustmentType"] = "补收"
	} else if context.AdjustmentType == "decrease" {
		vars["AdjustmentType"] = "退还"
	}

	return vars
}

// renderTemplate 渲染模板
func (g *TransactionDescriptionGenerator) renderTemplate(template string, vars map[string]interface{}) string {
	result := template
	for key, value := range vars {
		placeholder := fmt.Sprintf("{{.%s}}", key)
		if valueStr, ok := value.(string); ok && valueStr != "" {
			result = strings.ReplaceAll(result, placeholder, valueStr)
		} else {
			// 移除空值的占位符
			result = strings.ReplaceAll(result, placeholder, "")
		}
	}

	// 清理多余的空格和标点
	result = strings.TrimSpace(result)
	result = strings.ReplaceAll(result, "  ", " ")
	result = strings.ReplaceAll(result, "( )", "")
	result = strings.ReplaceAll(result, "()", "")

	return result
}

// generateDefaultDescriptions 生成默认描述
func (g *TransactionDescriptionGenerator) generateDefaultDescriptions(
	transactionType model.TransactionType,
	context TransactionContext,
) (short, detail, friendly string) {
	typeName := transactionType.GetDisplayName()
	amount := context.Amount.String() + context.Currency

	short = fmt.Sprintf("%s %s", typeName, amount)
	detail = short
	friendly = short

	if context.CustomerOrderNo != "" {
		detail = fmt.Sprintf("订单%s %s %s", context.CustomerOrderNo, typeName, amount)
		friendly = detail
	}

	return short, detail, friendly
}
