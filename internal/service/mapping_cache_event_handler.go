package service

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/repository"
)

// MappingCacheEventHandler 映射缓存事件处理器
type MappingCacheEventHandler struct {
	mappingCacheService *express.ExpressMappingCacheService
	weightCacheRepo     repository.WeightTierCacheRepository
	logger              *zap.Logger
}

// NewMappingCacheEventHandler 创建映射缓存事件处理器
func NewMappingCacheEventHandler(
	mappingCacheService *express.ExpressMappingCacheService,
	weightCacheRepo repository.WeightTierCacheRepository,
	logger *zap.Logger,
) *MappingCacheEventHandler {
	return &MappingCacheEventHandler{
		mappingCacheService: mappingCacheService,
		weightCacheRepo:     weightCacheRepo,
		logger:              logger,
	}
}

// HandleCacheEvent 处理缓存事件
func (h *MappingCacheEventHandler) HandleCacheEvent(ctx context.Context, event CacheEvent) error {
	switch event.Type {
	case EventCompanyStatusChanged:
		return h.handleCompanyStatusChanged(ctx, event)
	case EventMappingChanged:
		return h.handleMappingChanged(ctx, event)
	case EventProviderChanged:
		return h.handleProviderChanged(ctx, event)
	default:
		h.logger.Debug("忽略未知事件类型", zap.String("event_type", string(event.Type)))
		return nil
	}
}

// GetHandlerName 获取处理器名称
func (h *MappingCacheEventHandler) GetHandlerName() string {
	return "MappingCacheEventHandler"
}

// handleCompanyStatusChanged 处理快递公司状态变更事件
func (h *MappingCacheEventHandler) handleCompanyStatusChanged(ctx context.Context, event CacheEvent) error {
	companyCode := event.CompanyCode
	if companyCode == "" {
		return fmt.Errorf("快递公司代码为空")
	}

	h.logger.Info("处理快递公司状态变更事件",
		zap.String("company_code", companyCode))

	var errors []error

	// 1. 清除映射缓存中该快递公司的所有缓存条目
	if h.mappingCacheService != nil {
		if err := h.clearCompanyMappingCache(ctx, companyCode); err != nil {
			h.logger.Error("清除快递公司映射缓存失败",
				zap.Error(err),
				zap.String("company_code", companyCode))
			errors = append(errors, fmt.Errorf("映射缓存清理失败: %w", err))
		}
	}

	// 2. 清除重量档位缓存中该快递公司的所有条目
	if h.weightCacheRepo != nil {
		if err := h.clearCompanyWeightCache(ctx, companyCode); err != nil {
			h.logger.Error("清除快递公司重量档位缓存失败",
				zap.Error(err),
				zap.String("company_code", companyCode))
			errors = append(errors, fmt.Errorf("重量档位缓存清理失败: %w", err))
		}
	}

	if len(errors) > 0 {
		return errors[0]
	}

	h.logger.Info("快递公司状态变更事件处理完成",
		zap.String("company_code", companyCode))

	return nil
}

// handleMappingChanged 处理映射关系变更事件
func (h *MappingCacheEventHandler) handleMappingChanged(ctx context.Context, event CacheEvent) error {
	companyCode := event.CompanyCode
	providerCode := event.ProviderCode

	h.logger.Info("处理映射关系变更事件",
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode))

	// 清除特定的映射缓存条目
	if h.mappingCacheService != nil {
		h.mappingCacheService.ClearSpecificCache(companyCode, providerCode)
	}

	// 清除特定的重量档位缓存
	if h.weightCacheRepo != nil && companyCode != "" && providerCode != "" {
		if err := h.weightCacheRepo.InvalidateCacheByPattern(ctx, providerCode, companyCode); err != nil {
			h.logger.Error("清除特定重量档位缓存失败",
				zap.Error(err),
				zap.String("company_code", companyCode),
				zap.String("provider_code", providerCode))
			return fmt.Errorf("清除重量档位缓存失败: %w", err)
		}
	}

	h.logger.Info("映射关系变更事件处理完成",
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode))

	return nil
}

// handleProviderChanged 处理供应商变更事件
func (h *MappingCacheEventHandler) handleProviderChanged(ctx context.Context, event CacheEvent) error {
	providerCode := event.ProviderCode
	if providerCode == "" {
		return fmt.Errorf("供应商代码为空")
	}

	h.logger.Info("处理供应商变更事件",
		zap.String("provider_code", providerCode))

	// 清除该供应商的所有映射缓存
	if h.mappingCacheService != nil {
		h.mappingCacheService.ClearProviderCache(providerCode)
	}

	// 清除该供应商的所有重量档位缓存
	if h.weightCacheRepo != nil {
		// 这里可以实现更精确的清理，暂时清理所有缓存
		if err := h.weightCacheRepo.ClearAllCache(ctx); err != nil {
			h.logger.Error("清除供应商重量档位缓存失败",
				zap.Error(err),
				zap.String("provider_code", providerCode))
			return fmt.Errorf("清除重量档位缓存失败: %w", err)
		}
	}

	h.logger.Info("供应商变更事件处理完成",
		zap.String("provider_code", providerCode))

	return nil
}

// clearCompanyMappingCache 清除快递公司的映射缓存
func (h *MappingCacheEventHandler) clearCompanyMappingCache(ctx context.Context, companyCode string) error {
	// 通过调用映射缓存服务的方法清除特定快递公司的缓存
	h.mappingCacheService.ClearCompanyCache(companyCode)
	return nil
}

// clearCompanyWeightCache 清除快递公司的重量档位缓存
func (h *MappingCacheEventHandler) clearCompanyWeightCache(ctx context.Context, companyCode string) error {
	// 清除该快递公司在所有供应商下的重量档位缓存
	providers := []string{"kuaidi100", "yida", "yuntong", "cainiao", "kuaidiniao"}

	for _, providerCode := range providers {
		if err := h.weightCacheRepo.InvalidateCacheByPattern(ctx, providerCode, companyCode); err != nil {
			h.logger.Warn("清除特定供应商的重量档位缓存失败",
				zap.Error(err),
				zap.String("provider_code", providerCode),
				zap.String("company_code", companyCode))
			// 继续处理其他供应商，不中断流程
		}
	}

	return nil
}