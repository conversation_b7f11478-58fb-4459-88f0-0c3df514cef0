package service

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/user"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// AdminBalanceService 管理员余额服务接口
type AdminBalanceService interface {
	// 余额查询功能
	GetBalanceOverview(ctx context.Context) (*model.AdminBalanceOverview, error)
	GetUserBalanceList(ctx context.Context, req *model.UserBalanceListRequest) (*model.UserBalanceListResponse, error)
	GetUserBalanceDetail(ctx context.Context, userID string) (*model.UserBalanceDetail, error)
	GetBalanceAnomalies(ctx context.Context, req *model.AnomalyRequest) (*model.AnomalyResponse, error)

	// 余额操作功能
	ManualDeposit(ctx context.Context, req *model.AdminDepositRequest) (*model.AdminOperationResponse, error)
	AdjustBalance(ctx context.Context, req *model.BalanceAdjustmentRequest) (*model.AdminOperationResponse, error)
	ForceRefund(ctx context.Context, req *model.ForceRefundRequest) (*model.AdminOperationResponse, error)
	BatchOperation(ctx context.Context, req *model.BatchOperationRequest) (*model.BatchOperationResponse, error)

	// 交易管理功能
	GetAdminTransactionList(ctx context.Context, req *model.AdminTransactionListRequest) (*model.AdminTransactionListResponse, error)
	UpdateTransactionStatus(ctx context.Context, req *model.UpdateTransactionStatusRequest) (*model.AdminOperationResponse, error)

	// 统计分析功能
	GetBalanceStatistics(ctx context.Context) (*model.BalanceStatistics, error)
	GetTransactionStatistics(ctx context.Context, req *model.TransactionStatisticsRequest) (*model.TransactionStatistics, error)

	// 审计日志功能
	GetAuditLogs(ctx context.Context, req *model.AuditLogRequest) (*model.AuditLogResponse, error)

	// 导出功能
	ExportAuditLogs(ctx context.Context, req *model.ExportAuditLogRequest) ([]byte, string, error)
	ExportUserBalances(ctx context.Context, req *model.ExportUserBalanceRequest) ([]byte, string, error)
	ExportTransactions(ctx context.Context, req *model.ExportTransactionRequest) ([]byte, string, error)
}

// DefaultAdminBalanceService 默认管理员余额服务实现
type DefaultAdminBalanceService struct {
	adminBalanceRepo repository.AdminBalanceRepository
	balanceRepo      repository.BalanceRepository
	userRepo         user.UserRepository
	db               *sql.DB
	logger           *zap.Logger
	config           *config.Config
	configService    SystemConfigService
	rateLimiter      RateLimiterService
}

// NewDefaultAdminBalanceService 创建默认管理员余额服务
func NewDefaultAdminBalanceService(
	adminBalanceRepo repository.AdminBalanceRepository,
	balanceRepo repository.BalanceRepository,
	userRepo user.UserRepository,
	db *sql.DB,
	logger *zap.Logger,
	config *config.Config,
	configService SystemConfigService,
	rateLimiter RateLimiterService,
) AdminBalanceService {
	return &DefaultAdminBalanceService{
		adminBalanceRepo: adminBalanceRepo,
		balanceRepo:      balanceRepo,
		userRepo:         userRepo,
		db:               db,
		logger:           logger,
		config:           config,
		configService:    configService,
		rateLimiter:      rateLimiter,
	}
}

// GetBalanceOverview 获取余额总览
func (s *DefaultAdminBalanceService) GetBalanceOverview(ctx context.Context) (*model.AdminBalanceOverview, error) {
	overview, err := s.adminBalanceRepo.GetBalanceOverview(ctx)
	if err != nil {
		s.logger.Error("获取余额总览失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取余额总览失败")
	}

	return overview, nil
}

// GetUserBalanceList 获取用户余额列表
func (s *DefaultAdminBalanceService) GetUserBalanceList(ctx context.Context, req *model.UserBalanceListRequest) (*model.UserBalanceListResponse, error) {
	// 从配置获取分页参数
	defaultPageSize := s.configService.GetConfigAsIntWithDefault("admin_balance.default_page_size", 20)
	maxPageSize := s.configService.GetConfigAsIntWithDefault("admin_balance.max_page_size", 100)

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = defaultPageSize
	}
	if req.PageSize > maxPageSize {
		req.PageSize = maxPageSize
	}

	response, err := s.adminBalanceRepo.GetUserBalanceList(ctx, req)
	if err != nil {
		s.logger.Error("获取用户余额列表失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取用户余额列表失败")
	}

	return response, nil
}

// GetUserBalanceDetail 获取用户余额详情
func (s *DefaultAdminBalanceService) GetUserBalanceDetail(ctx context.Context, userID string) (*model.UserBalanceDetail, error) {
	if userID == "" {
		return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
	}

	detail, err := s.adminBalanceRepo.GetUserBalanceDetail(ctx, userID)
	if err != nil {
		s.logger.Error("获取用户余额详情失败", zap.String("user_id", userID), zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取用户余额详情失败")
	}

	return detail, nil
}

// GetBalanceAnomalies 获取余额异常
func (s *DefaultAdminBalanceService) GetBalanceAnomalies(ctx context.Context, req *model.AnomalyRequest) (*model.AnomalyResponse, error) {
	// 从配置获取分页参数
	defaultPageSize := s.configService.GetConfigAsIntWithDefault("admin_balance.default_page_size", 20)
	maxPageSize := s.configService.GetConfigAsIntWithDefault("admin_balance.max_page_size", 100)

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = defaultPageSize
	}
	if req.PageSize > maxPageSize {
		req.PageSize = maxPageSize
	}

	response, err := s.adminBalanceRepo.GetBalanceAnomalies(ctx, req)
	if err != nil {
		s.logger.Error("获取余额异常失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取余额异常失败")
	}

	return response, nil
}

// ManualDeposit 手动充值
func (s *DefaultAdminBalanceService) ManualDeposit(ctx context.Context, req *model.AdminDepositRequest) (*model.AdminOperationResponse, error) {
	// 检查操作频率限制
	if err := s.rateLimiter.CheckAdminOperationLimit(ctx, req.AdminID, "manual_deposit"); err != nil {
		s.logger.Warn("管理员充值操作被频率限制",
			zap.String("admin_id", req.AdminID),
			zap.String("user_id", req.UserID),
			zap.Error(err))
		return nil, fmt.Errorf("操作频率限制: %w", err)
	}

	// 验证参数
	if err := s.validateDepositRequest(req); err != nil {
		return nil, err
	}

	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		s.logger.Error("开始事务失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "开始事务失败")
	}
	defer tx.Rollback()

	// 验证用户存在
	_, err = s.userRepo.FindByID(req.UserID)
	if err != nil {
		s.logger.Error("用户不存在", zap.String("user_id", req.UserID), zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeNotFound, "用户不存在")
	}

	// 获取当前余额（使用乐观锁），如果不存在则自动创建
	balance, err := s.balanceRepo.GetBalance(ctx, req.UserID)
	if err != nil {
		// 🔥 企业级修复：如果余额账户不存在，自动创建
		if strings.Contains(err.Error(), "用户余额不存在") {
			s.logger.Info("用户余额账户不存在，自动创建", zap.String("user_id", req.UserID))

			// 创建余额账户
			newBalance := &model.UserBalance{
				UserID:   req.UserID,
				Balance:  decimal.Zero,
				Currency: "CNY",
				Status:   model.BalanceStatusActive,
				Version:  0,
			}

			if err := s.balanceRepo.CreateBalance(ctx, newBalance); err != nil {
				s.logger.Error("创建余额账户失败", zap.String("user_id", req.UserID), zap.Error(err))
				return nil, errors.NewBusinessError(errors.ErrCodeInternal, "创建余额账户失败")
			}

			// 重新获取余额
			balance, err = s.balanceRepo.GetBalance(ctx, req.UserID)
			if err != nil {
				s.logger.Error("重新获取余额失败", zap.String("user_id", req.UserID), zap.Error(err))
				return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取余额失败")
			}
		} else {
			s.logger.Error("获取余额失败", zap.String("user_id", req.UserID), zap.Error(err))
			return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取余额失败")
		}
	}

	// 计算新余额
	newBalance := balance.Balance.Add(req.Amount)

	// 🔥 企业级改造：创建增强的交易记录
	transaction := &model.BalanceTransaction{
		UserID:        req.UserID,
		Type:          model.TransactionTypeAdminDeposit,
		Amount:        req.Amount,
		Currency:      balance.Currency,
		BalanceBefore: balance.Balance,
		BalanceAfter:  newBalance,

		// 🔥 订单关联信息（充值类交易不关联订单，设置为空）
		OrderNo:         "",
		PlatformOrderNo: "",
		CustomerOrderNo: "",
		TrackingNo:      "",

		// 交易分类信息（新增）
		Category: string(model.TransactionTypeAdminDeposit.GetCategory()),
		SubType:  string(model.TransactionTypeAdminDeposit),

		// 描述信息（增强）
		Description:       req.Description,
		DetailDescription: fmt.Sprintf("管理员%s为用户充值%s%s，原因：%s", req.AdminID, req.Amount.String(), balance.Currency, req.Reason),
		UserFriendlyDesc:  fmt.Sprintf("管理员为您充值了%s元", req.Amount.String()),

		// 业务上下文（增强）
		Metadata: map[string]interface{}{
			"admin_operation": true,
			"reason":          req.Reason,
		},
		BusinessContext: map[string]interface{}{
			"transaction_time": util.NowBeijing(),
			"amount_info": map[string]interface{}{
				"original_amount": req.Amount.String(),
				"currency":        balance.Currency,
			},
			"operation_reason": req.Reason,
			"admin_info": map[string]interface{}{
				"admin_id": req.AdminID,
			},
		},

		OperatorID: req.AdminID,
		Status:     model.TransactionStatusCompleted,
	}

	if err := s.balanceRepo.CreateTransaction(ctx, transaction); err != nil {
		s.logger.Error("创建交易记录失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "创建交易记录失败")
	}

	// 更新余额
	if err := s.balanceRepo.UpdateBalanceWithVersion(ctx, req.UserID, newBalance, decimal.Zero, balance.Version); err != nil {
		s.logger.Error("更新余额失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "更新余额失败")
	}

	// 创建管理员操作审计记录
	auditLog := &model.AdminBalanceAuditLog{
		AdminID:       req.AdminID,
		TargetUserID:  req.UserID,
		OperationType: "manual_deposit",
		Amount:        req.Amount,
		Reason:        req.Reason,
		BeforeBalance: balance.Balance,
		AfterBalance:  newBalance,
		IPAddress:     getIPFromContext(ctx),
		UserAgent:     getUserAgentFromContext(ctx),
	}

	if err := s.adminBalanceRepo.CreateAuditLog(ctx, auditLog); err != nil {
		s.logger.Error("创建审计日志失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "创建审计日志失败")
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		s.logger.Error("提交事务失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "提交事务失败")
	}

	// 记录操作到频率限制器
	if err := s.rateLimiter.RecordAdminOperation(ctx, req.AdminID, "manual_deposit"); err != nil {
		s.logger.Error("记录管理员操作失败", zap.Error(err))
		// 不影响主要业务流程，只记录错误
	}

	s.logger.Info("管理员充值成功",
		zap.String("admin_id", req.AdminID),
		zap.String("user_id", req.UserID),
		zap.String("amount", req.Amount.String()),
		zap.String("transaction_id", transaction.ID))

	return &model.AdminOperationResponse{
		Success:       true,
		TransactionID: transaction.ID,
		NewBalance:    newBalance,
		Message:       "充值成功",
	}, nil
}

// validateDepositRequest 验证充值请求
func (s *DefaultAdminBalanceService) validateDepositRequest(req *model.AdminDepositRequest) error {
	if req.UserID == "" {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
	}

	// 从配置获取最小金额限制
	minAmount := s.configService.GetConfigWithDefault("admin_balance.min_balance_amount", "0.01")
	minAmountDecimal, _ := decimal.NewFromString(minAmount)

	// 🔥 修改：管理员手动充值不限制最大金额，只检查最小金额
	if req.Amount.LessThan(minAmountDecimal) {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, fmt.Sprintf("充值金额不能小于%s", minAmount))
	}

	// 🔥 移除最大金额限制检查，管理员充值不受限制
	// 只保留基本的业务逻辑验证：金额必须为正数
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "充值金额必须大于0")
	}

	if req.Reason == "" {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "充值原因不能为空")
	}
	if req.AdminID == "" {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "管理员ID不能为空")
	}

	s.logger.Info("管理员充值验证通过",
		zap.String("admin_id", req.AdminID),
		zap.String("user_id", req.UserID),
		zap.String("amount", req.Amount.String()),
		zap.String("reason", req.Reason))

	return nil
}

// AdjustBalance 调整余额
func (s *DefaultAdminBalanceService) AdjustBalance(ctx context.Context, req *model.BalanceAdjustmentRequest) (*model.AdminOperationResponse, error) {
	// 检查操作频率限制
	if err := s.rateLimiter.CheckAdminOperationLimit(ctx, req.AdminID, "balance_adjustment"); err != nil {
		s.logger.Warn("管理员余额调整操作被频率限制",
			zap.String("admin_id", req.AdminID),
			zap.String("user_id", req.UserID),
			zap.Error(err))
		return nil, fmt.Errorf("操作频率限制: %w", err)
	}

	// 验证参数
	if err := s.validateAdjustmentRequest(req); err != nil {
		return nil, err
	}

	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		s.logger.Error("开始事务失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "开始事务失败")
	}
	defer tx.Rollback()

	// 验证用户存在
	_, err = s.userRepo.FindByID(req.UserID)
	if err != nil {
		s.logger.Error("用户不存在", zap.String("user_id", req.UserID), zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeNotFound, "用户不存在")
	}

	// 获取当前余额
	balance, err := s.balanceRepo.GetBalance(ctx, req.UserID)
	if err != nil {
		s.logger.Error("获取余额失败", zap.String("user_id", req.UserID), zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取余额失败")
	}

	// 计算新余额
	var newBalance decimal.Decimal
	var transactionType model.TransactionType
	var adjustmentAmount decimal.Decimal

	if req.AdjustmentType == "increase" {
		newBalance = balance.Balance.Add(req.Amount)
		transactionType = model.TransactionTypeBalanceAdjustment
		adjustmentAmount = req.Amount
	} else if req.AdjustmentType == "decrease" {
		if balance.Balance.LessThan(req.Amount) {
			return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest, "余额不足，无法扣减")
		}
		newBalance = balance.Balance.Sub(req.Amount)
		transactionType = model.TransactionTypeBalanceAdjustment
		adjustmentAmount = req.Amount.Neg()
	} else {
		return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest, "无效的调整类型")
	}

	// 创建交易记录
	transaction := &model.BalanceTransaction{
		UserID:        req.UserID,
		Type:          transactionType,
		Amount:        adjustmentAmount,
		Currency:      balance.Currency,
		BalanceBefore: balance.Balance,
		BalanceAfter:  newBalance,
		Description:   req.Description,
		OperatorID:    req.AdminID,
		Status:        model.TransactionStatusCompleted,
		Metadata: map[string]interface{}{
			"admin_operation": true,
			"adjustment_type": req.AdjustmentType,
			"reason":          req.Reason,
		},
	}

	if err := s.balanceRepo.CreateTransaction(ctx, transaction); err != nil {
		s.logger.Error("创建交易记录失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "创建交易记录失败")
	}

	// 更新余额
	if err := s.balanceRepo.UpdateBalanceWithVersion(ctx, req.UserID, newBalance, decimal.Zero, balance.Version); err != nil {
		s.logger.Error("更新余额失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "更新余额失败")
	}

	// 创建审计日志
	auditLog := &model.AdminBalanceAuditLog{
		AdminID:       req.AdminID,
		TargetUserID:  req.UserID,
		OperationType: "balance_adjustment",
		Amount:        adjustmentAmount,
		Reason:        req.Reason,
		BeforeBalance: balance.Balance,
		AfterBalance:  newBalance,
		IPAddress:     getIPFromContext(ctx),
		UserAgent:     getUserAgentFromContext(ctx),
		Metadata: map[string]interface{}{
			"adjustment_type": req.AdjustmentType,
		},
	}

	if err := s.adminBalanceRepo.CreateAuditLog(ctx, auditLog); err != nil {
		s.logger.Error("创建审计日志失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "创建审计日志失败")
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		s.logger.Error("提交事务失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "提交事务失败")
	}

	s.logger.Info("管理员余额调整成功",
		zap.String("admin_id", req.AdminID),
		zap.String("user_id", req.UserID),
		zap.String("adjustment_type", req.AdjustmentType),
		zap.String("amount", req.Amount.String()),
		zap.String("transaction_id", transaction.ID))

	return &model.AdminOperationResponse{
		Success:       true,
		TransactionID: transaction.ID,
		NewBalance:    newBalance,
		Message:       "余额调整成功",
	}, nil
}

// validateAdjustmentRequest 验证调整请求
func (s *DefaultAdminBalanceService) validateAdjustmentRequest(req *model.BalanceAdjustmentRequest) error {
	if req.UserID == "" {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
	}

	// 从配置获取最小金额限制
	minAmount := s.configService.GetConfigWithDefault("admin_balance.min_balance_amount", "0.01")
	minAmountDecimal, _ := decimal.NewFromString(minAmount)

	// 🔥 修改：管理员余额调整不限制最大金额，只检查最小金额
	if req.Amount.LessThan(minAmountDecimal) {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, fmt.Sprintf("调整金额不能小于%s", minAmount))
	}

	// 🔥 移除最大金额限制检查，管理员调整不受限制
	// 只保留基本的业务逻辑验证：金额必须为正数
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "调整金额必须大于0")
	}

	if req.AdjustmentType != "increase" && req.AdjustmentType != "decrease" {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "调整类型必须是increase或decrease")
	}
	if req.Reason == "" {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "调整原因不能为空")
	}
	if req.AdminID == "" {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "管理员ID不能为空")
	}

	s.logger.Info("管理员余额调整验证通过",
		zap.String("admin_id", req.AdminID),
		zap.String("user_id", req.UserID),
		zap.String("amount", req.Amount.String()),
		zap.String("adjustment_type", req.AdjustmentType),
		zap.String("reason", req.Reason))

	return nil
}

// ForceRefund 强制退款
func (s *DefaultAdminBalanceService) ForceRefund(ctx context.Context, req *model.ForceRefundRequest) (*model.AdminOperationResponse, error) {
	// 验证参数
	if err := s.validateRefundRequest(req); err != nil {
		return nil, err
	}

	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		s.logger.Error("开始事务失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "开始事务失败")
	}
	defer tx.Rollback()

	// 验证用户存在
	_, err = s.userRepo.FindByID(req.UserID)
	if err != nil {
		s.logger.Error("用户不存在", zap.String("user_id", req.UserID), zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeNotFound, "用户不存在")
	}

	// 获取当前余额
	balance, err := s.balanceRepo.GetBalance(ctx, req.UserID)
	if err != nil {
		s.logger.Error("获取余额失败", zap.String("user_id", req.UserID), zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取余额失败")
	}

	// 计算新余额
	newBalance := balance.Balance.Add(req.Amount)

	// 创建交易记录
	transaction := &model.BalanceTransaction{
		UserID:        req.UserID,
		Type:          model.TransactionTypeOrderCancelRefund,
		Amount:        req.Amount,
		Currency:      balance.Currency,
		BalanceBefore: balance.Balance,
		BalanceAfter:  newBalance,
		OrderNo:       req.OrderNo,
		Description:   req.Description,
		OperatorID:    req.AdminID,
		Status:        model.TransactionStatusCompleted,
		Metadata: map[string]interface{}{
			"admin_operation":         true,
			"force_refund":            true,
			"reason":                  req.Reason,
			"original_transaction_id": req.TransactionID,
		},
	}

	if err := s.balanceRepo.CreateTransaction(ctx, transaction); err != nil {
		s.logger.Error("创建交易记录失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "创建交易记录失败")
	}

	// 更新余额
	if err := s.balanceRepo.UpdateBalanceWithVersion(ctx, req.UserID, newBalance, decimal.Zero, balance.Version); err != nil {
		s.logger.Error("更新余额失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "更新余额失败")
	}

	// 创建审计日志
	auditLog := &model.AdminBalanceAuditLog{
		AdminID:       req.AdminID,
		TargetUserID:  req.UserID,
		OperationType: "force_refund",
		Amount:        req.Amount,
		Reason:        req.Reason,
		BeforeBalance: balance.Balance,
		AfterBalance:  newBalance,
		IPAddress:     getIPFromContext(ctx),
		UserAgent:     getUserAgentFromContext(ctx),
		Metadata: map[string]interface{}{
			"order_no":                req.OrderNo,
			"original_transaction_id": req.TransactionID,
		},
	}

	if err := s.adminBalanceRepo.CreateAuditLog(ctx, auditLog); err != nil {
		s.logger.Error("创建审计日志失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "创建审计日志失败")
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		s.logger.Error("提交事务失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "提交事务失败")
	}

	s.logger.Info("管理员强制退款成功",
		zap.String("admin_id", req.AdminID),
		zap.String("user_id", req.UserID),
		zap.String("amount", req.Amount.String()),
		zap.String("order_no", req.OrderNo),
		zap.String("transaction_id", transaction.ID))

	return &model.AdminOperationResponse{
		Success:       true,
		TransactionID: transaction.ID,
		NewBalance:    newBalance,
		Message:       "强制退款成功",
	}, nil
}

// 验证函数

func (s *DefaultAdminBalanceService) validateRefundRequest(req *model.ForceRefundRequest) error {
	if req.UserID == "" {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "用户ID不能为空")
	}
	if req.Amount.LessThanOrEqual(decimal.Zero) {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "退款金额必须大于0")
	}
	if req.Reason == "" {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "退款原因不能为空")
	}
	if req.AdminID == "" {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "管理员ID不能为空")
	}
	return nil
}

// 辅助函数
func getIPFromContext(ctx context.Context) string {
	if ip, ok := ctx.Value("client_ip").(string); ok {
		return ip
	}
	return ""
}

func getUserAgentFromContext(ctx context.Context) string {
	if ua, ok := ctx.Value("user_agent").(string); ok {
		return ua
	}
	return ""
}

// BatchOperation 批量操作
func (s *DefaultAdminBalanceService) BatchOperation(ctx context.Context, req *model.BatchOperationRequest) (*model.BatchOperationResponse, error) {
	// 从配置获取批量操作限制
	maxBatchSize := s.configService.GetConfigAsIntWithDefault("admin_balance.max_batch_size", 100)
	maxConcurrency := s.configService.GetConfigAsIntWithDefault("admin_balance.max_concurrency", 10)

	if len(req.UserIDs) > maxBatchSize {
		return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest, fmt.Sprintf("批量操作数量超过限制: %d", maxBatchSize))
	}

	response := &model.BatchOperationResponse{
		TotalCount:   len(req.UserIDs),
		SuccessCount: 0,
		FailureCount: 0,
		Results:      make([]*model.BatchOperationResult, 0, len(req.UserIDs)),
	}

	// 使用工作池控制并发
	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup
	var mu sync.Mutex

	for _, userID := range req.UserIDs {
		wg.Add(1)
		go func(uid string) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			result := s.processSingleBatchOperation(ctx, uid, req)

			mu.Lock()
			response.Results = append(response.Results, result)
			if result.Success {
				response.SuccessCount++
			} else {
				response.FailureCount++
			}
			mu.Unlock()
		}(userID)
	}

	wg.Wait()

	return response, nil
}

// processSingleBatchOperation 处理单个批量操作
func (s *DefaultAdminBalanceService) processSingleBatchOperation(ctx context.Context, userID string, req *model.BatchOperationRequest) *model.BatchOperationResult {
	result := &model.BatchOperationResult{
		UserID:  userID,
		Success: false,
	}

	switch req.OperationType {
	case "deposit":
		depositReq := &model.AdminDepositRequest{
			UserID:      userID,
			Amount:      req.Amount,
			Reason:      req.Reason,
			Description: fmt.Sprintf("批量充值: %s", req.Reason),
			AdminID:     req.AdminID,
		}
		resp, err := s.ManualDeposit(ctx, depositReq)
		if err != nil {
			result.Error = err.Error()
		} else {
			result.Success = resp.Success
			result.TransactionID = resp.TransactionID
			result.NewBalance = resp.NewBalance
		}

	case "adjustment":
		adjustReq := &model.BalanceAdjustmentRequest{
			UserID:         userID,
			Amount:         req.Amount,
			AdjustmentType: "increase", // 默认增加
			Reason:         req.Reason,
			Description:    fmt.Sprintf("批量调整: %s", req.Reason),
			AdminID:        req.AdminID,
		}
		resp, err := s.AdjustBalance(ctx, adjustReq)
		if err != nil {
			result.Error = err.Error()
		} else {
			result.Success = resp.Success
			result.TransactionID = resp.TransactionID
			result.NewBalance = resp.NewBalance
		}

	default:
		result.Error = "不支持的批量操作类型"
	}

	return result
}

// GetAdminTransactionList 获取管理员交易列表
func (s *DefaultAdminBalanceService) GetAdminTransactionList(ctx context.Context, req *model.AdminTransactionListRequest) (*model.AdminTransactionListResponse, error) {
	// 从配置获取分页参数
	defaultPageSize := s.configService.GetConfigAsIntWithDefault("admin_balance.default_page_size", 20)
	maxPageSize := s.configService.GetConfigAsIntWithDefault("admin_balance.max_page_size", 100)

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = defaultPageSize
	}
	if req.PageSize > maxPageSize {
		req.PageSize = maxPageSize
	}

	response, err := s.adminBalanceRepo.GetAdminTransactionList(ctx, req)
	if err != nil {
		s.logger.Error("获取管理员交易列表失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取交易列表失败")
	}

	return response, nil
}

// UpdateTransactionStatus 更新交易状态
func (s *DefaultAdminBalanceService) UpdateTransactionStatus(ctx context.Context, req *model.UpdateTransactionStatusRequest) (*model.AdminOperationResponse, error) {
	// 验证参数
	if req.TransactionID == "" {
		return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest, "交易ID不能为空")
	}
	if req.Status == "" {
		return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest, "状态不能为空")
	}
	if req.Reason == "" {
		return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest, "原因不能为空")
	}

	// 开始事务
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		s.logger.Error("开始事务失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "开始事务失败")
	}
	defer tx.Rollback()

	// 获取原交易记录
	transaction, err := s.balanceRepo.GetTransactionByID(ctx, req.TransactionID)
	if err != nil {
		s.logger.Error("获取交易记录失败", zap.String("transaction_id", req.TransactionID), zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeNotFound, "交易记录不存在")
	}

	// 更新交易状态
	if err := s.balanceRepo.UpdateTransactionStatus(ctx, req.TransactionID, model.TransactionStatus(req.Status)); err != nil {
		s.logger.Error("更新交易状态失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "更新交易状态失败")
	}

	// 创建审计日志
	auditLog := &model.AdminBalanceAuditLog{
		AdminID:       req.AdminID,
		TargetUserID:  transaction.UserID,
		OperationType: "update_transaction_status",
		Amount:        transaction.Amount,
		Reason:        req.Reason,
		BeforeBalance: transaction.BalanceBefore,
		AfterBalance:  transaction.BalanceAfter,
		IPAddress:     getIPFromContext(ctx),
		UserAgent:     getUserAgentFromContext(ctx),
		Metadata: map[string]interface{}{
			"transaction_id": req.TransactionID,
			"old_status":     string(transaction.Status),
			"new_status":     req.Status,
		},
	}

	if err := s.adminBalanceRepo.CreateAuditLog(ctx, auditLog); err != nil {
		s.logger.Error("创建审计日志失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "创建审计日志失败")
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		s.logger.Error("提交事务失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "提交事务失败")
	}

	s.logger.Info("管理员更新交易状态成功",
		zap.String("admin_id", req.AdminID),
		zap.String("transaction_id", req.TransactionID),
		zap.String("new_status", req.Status))

	return &model.AdminOperationResponse{
		Success: true,
		Message: "交易状态更新成功",
	}, nil
}

// GetBalanceStatistics 获取余额统计
func (s *DefaultAdminBalanceService) GetBalanceStatistics(ctx context.Context) (*model.BalanceStatistics, error) {
	stats, err := s.adminBalanceRepo.GetBalanceStatistics(ctx)
	if err != nil {
		s.logger.Error("获取余额统计失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取余额统计失败")
	}

	return stats, nil
}

// GetTransactionStatistics 获取交易统计
func (s *DefaultAdminBalanceService) GetTransactionStatistics(ctx context.Context, req *model.TransactionStatisticsRequest) (*model.TransactionStatistics, error) {
	// 验证参数
	if req.StartDate == "" || req.EndDate == "" {
		return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest, "开始日期和结束日期不能为空")
	}

	// 解析日期
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest, "开始日期格式错误")
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return nil, errors.NewBusinessError(errors.ErrCodeInvalidRequest, "结束日期格式错误")
	}

	// 设置时间范围（包含整天）
	startTime := startDate
	endTime := endDate.Add(24 * time.Hour).Add(-time.Nanosecond)

	// 查询交易统计数据
	query := `
		SELECT
			transaction_type,
			COUNT(*) as count,
			COALESCE(SUM(amount), 0) as total_amount
		FROM balance_transactions
		WHERE created_at >= $1 AND created_at <= $2
		GROUP BY transaction_type
	`

	rows, err := s.db.QueryContext(ctx, query, startTime, endTime)
	if err != nil {
		s.logger.Error("查询交易统计失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "查询交易统计失败")
	}
	defer rows.Close()

	stats := &model.TransactionStatistics{
		TotalTransactions:    0,
		TotalAmount:          decimal.Zero,
		DepositCount:         0,
		DepositAmount:        decimal.Zero,
		WithdrawCount:        0,
		WithdrawAmount:       decimal.Zero,
		RefundCount:          0,
		RefundAmount:         decimal.Zero,
		AdjustmentCount:      0,
		AdjustmentAmount:     decimal.Zero,
		AvgTransactionAmount: decimal.Zero,
		UpdatedAt:            util.NowBeijing(),
	}

	for rows.Next() {
		var transactionType string
		var count int64
		var amount decimal.Decimal

		err := rows.Scan(&transactionType, &count, &amount)
		if err != nil {
			s.logger.Error("扫描交易统计数据失败", zap.Error(err))
			continue
		}

		stats.TotalTransactions += count
		stats.TotalAmount = stats.TotalAmount.Add(amount)

		switch transactionType {
		case "user_deposit", "admin_deposit":
			stats.DepositCount += count
			stats.DepositAmount = stats.DepositAmount.Add(amount)
		case "order_pre_charge", "order_intercept_charge", "return_charge", "order_revive_recharge":
			stats.WithdrawCount += count
			stats.WithdrawAmount = stats.WithdrawAmount.Add(amount)
		case "order_cancel_refund":
			stats.RefundCount += count
			stats.RefundAmount = stats.RefundAmount.Add(amount)
		case "balance_adjustment":
			stats.AdjustmentCount += count
			stats.AdjustmentAmount = stats.AdjustmentAmount.Add(amount)
		// 🚨 已废弃的交易类型，记录但不统计
		case "pickup_actual_charge":
			// 记录发现废弃交易类型，但不计入统计
			s.logger.Warn("发现已废弃的pickup_actual_charge交易类型",
				zap.String("transaction_type", transactionType),
				zap.Int64("count", count),
				zap.String("amount", amount.String()))
			continue
		}
	}

	// 计算平均交易金额
	if stats.TotalTransactions > 0 {
		stats.AvgTransactionAmount = stats.TotalAmount.Div(decimal.NewFromInt(stats.TotalTransactions))
	}

	return stats, nil
}

// GetAuditLogs 获取审计日志
func (s *DefaultAdminBalanceService) GetAuditLogs(ctx context.Context, req *model.AuditLogRequest) (*model.AuditLogResponse, error) {
	// 从配置获取分页参数
	defaultPageSize := s.configService.GetConfigAsIntWithDefault("admin_balance.default_page_size", 20)
	maxPageSize := s.configService.GetConfigAsIntWithDefault("admin_balance.max_page_size", 100)

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = defaultPageSize
	}
	if req.PageSize > maxPageSize {
		req.PageSize = maxPageSize
	}

	// 计算offset
	offset := (req.Page - 1) * req.PageSize

	// 调用repository方法
	logs, err := s.adminBalanceRepo.GetAuditLogs(ctx, req.TargetUserID, req.PageSize, offset)
	if err != nil {
		s.logger.Error("获取审计日志失败", zap.Error(err))
		return nil, errors.NewBusinessError(errors.ErrCodeInternal, "获取审计日志失败")
	}

	// 构建响应
	response := &model.AuditLogResponse{
		Items:      logs,
		Total:      int64(len(logs)), // 简化处理，实际应该查询总数
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: int((int64(len(logs)) + int64(req.PageSize) - 1) / int64(req.PageSize)),
	}

	return response, nil
}

// ExportAuditLogs 导出审计日志
func (s *DefaultAdminBalanceService) ExportAuditLogs(ctx context.Context, req *model.ExportAuditLogRequest) ([]byte, string, error) {
	// 验证参数
	if req.StartDate == "" || req.EndDate == "" {
		return nil, "", errors.NewBusinessError(errors.ErrCodeInvalidRequest, "开始日期和结束日期不能为空")
	}

	// 从配置获取导出限制
	exportMaxRecords := s.configService.GetConfigAsIntWithDefault("admin_balance.export_max_records", 10000)

	// 获取审计日志数据
	logs, err := s.adminBalanceRepo.GetAuditLogs(ctx, req.TargetUserID, exportMaxRecords, 0)
	if err != nil {
		s.logger.Error("获取审计日志数据失败", zap.Error(err))
		return nil, "", errors.NewBusinessError(errors.ErrCodeInternal, "获取审计日志数据失败")
	}

	// 生成Excel文件
	fileData, err := s.generateAuditLogExcel(logs)
	if err != nil {
		s.logger.Error("生成Excel文件失败", zap.Error(err))
		return nil, "", errors.NewBusinessError(errors.ErrCodeInternal, "生成Excel文件失败")
	}

	filename := fmt.Sprintf("audit_logs_%s_%s.xlsx", req.StartDate, req.EndDate)

	// 记录导出操作
	s.logger.Info("管理员导出审计日志",
		zap.String("admin_id", req.AdminID),
		zap.String("start_date", req.StartDate),
		zap.String("end_date", req.EndDate),
		zap.Int("record_count", len(logs)))

	return fileData, filename, nil
}

// ExportUserBalances 导出用户余额
func (s *DefaultAdminBalanceService) ExportUserBalances(ctx context.Context, req *model.ExportUserBalanceRequest) ([]byte, string, error) {
	// 从配置获取导出限制
	exportMaxRecords := s.configService.GetConfigAsIntWithDefault("admin_balance.export_max_records", 10000)

	// 获取用户余额数据
	balanceReq := &model.UserBalanceListRequest{
		Page:     1,
		PageSize: exportMaxRecords,
		Status:   req.Status,
	}

	response, err := s.adminBalanceRepo.GetUserBalanceList(ctx, balanceReq)
	if err != nil {
		s.logger.Error("获取用户余额数据失败", zap.Error(err))
		return nil, "", errors.NewBusinessError(errors.ErrCodeInternal, "获取用户余额数据失败")
	}

	// 生成Excel文件
	fileData, err := s.generateUserBalanceExcel(response.Items)
	if err != nil {
		s.logger.Error("生成Excel文件失败", zap.Error(err))
		return nil, "", errors.NewBusinessError(errors.ErrCodeInternal, "生成Excel文件失败")
	}

	filename := fmt.Sprintf("user_balances_%d.xlsx", util.NowBeijing().Unix())

	// 记录导出操作
	s.logger.Info("管理员导出用户余额",
		zap.String("admin_id", req.AdminID),
		zap.Int("record_count", len(response.Items)))

	return fileData, filename, nil
}

// ExportTransactions 导出交易记录
func (s *DefaultAdminBalanceService) ExportTransactions(ctx context.Context, req *model.ExportTransactionRequest) ([]byte, string, error) {
	// 验证参数
	if req.StartDate == "" || req.EndDate == "" {
		return nil, "", errors.NewBusinessError(errors.ErrCodeInvalidRequest, "开始日期和结束日期不能为空")
	}

	// 从配置获取导出限制
	exportMaxRecords := s.configService.GetConfigAsIntWithDefault("admin_balance.export_max_records", 10000)

	// 获取交易记录数据
	transactionReq := &model.AdminTransactionListRequest{
		Page:      1,
		PageSize:  exportMaxRecords,
		UserID:    req.UserID,
		Type:      req.TransactionType,
		Status:    req.Status,
		StartTime: req.StartDate,
		EndTime:   req.EndDate,
	}

	response, err := s.adminBalanceRepo.GetAdminTransactionList(ctx, transactionReq)
	if err != nil {
		s.logger.Error("获取交易记录数据失败", zap.Error(err))
		return nil, "", errors.NewBusinessError(errors.ErrCodeInternal, "获取交易记录数据失败")
	}

	// 生成Excel文件
	fileData, err := s.generateTransactionExcel(response.Items)
	if err != nil {
		s.logger.Error("生成Excel文件失败", zap.Error(err))
		return nil, "", errors.NewBusinessError(errors.ErrCodeInternal, "生成Excel文件失败")
	}

	filename := fmt.Sprintf("transactions_%s_%s.xlsx", req.StartDate, req.EndDate)

	// 记录导出操作
	s.logger.Info("管理员导出交易记录",
		zap.String("admin_id", req.AdminID),
		zap.String("start_date", req.StartDate),
		zap.String("end_date", req.EndDate),
		zap.Int("record_count", len(response.Items)))

	return fileData, filename, nil
}

// generateAuditLogExcel 生成审计日志Excel文件
func (s *DefaultAdminBalanceService) generateAuditLogExcel(logs []*model.AdminBalanceAuditLog) ([]byte, error) {
	// 这里应该使用Excel库生成文件，为了简化，返回CSV格式的字节数据
	var buffer []byte
	header := "ID,管理员ID,目标用户ID,操作类型,金额,原因,操作前余额,操作后余额,IP地址,用户代理,创建时间\n"
	buffer = append(buffer, []byte(header)...)

	for _, log := range logs {
		line := fmt.Sprintf("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
			log.ID,
			log.AdminID,
			log.TargetUserID,
			log.OperationType,
			log.Amount.String(),
			log.Reason,
			log.BeforeBalance.String(),
			log.AfterBalance.String(),
			log.IPAddress,
			log.UserAgent,
			log.CreatedAt.Format("2006-01-02 15:04:05"))
		buffer = append(buffer, []byte(line)...)
	}

	return buffer, nil
}

// generateUserBalanceExcel 生成用户余额Excel文件
func (s *DefaultAdminBalanceService) generateUserBalanceExcel(balances []*model.UserBalanceListItem) ([]byte, error) {
	// 这里应该使用Excel库生成文件，为了简化，返回CSV格式的字节数据
	var buffer []byte
	header := "用户ID,用户名,邮箱,余额,冻结余额,可用余额,货币,状态,最后交易时间,交易次数,创建时间,更新时间\n"
	buffer = append(buffer, []byte(header)...)

	for _, balance := range balances {
		lastTransactionTime := ""
		if balance.LastTransactionAt != nil {
			lastTransactionTime = balance.LastTransactionAt.Format("2006-01-02 15:04:05")
		}

		line := fmt.Sprintf("%s,%s,%s,%s,%s,%s,%s,%s,%d,%s,%s\n",
			balance.UserID,
			balance.Username,
			balance.Email,
			balance.Balance.String(),
			balance.AvailableBalance.String(),
			balance.Currency,
			balance.Status,
			lastTransactionTime,
			balance.TransactionCount,
			balance.CreatedAt.Format("2006-01-02 15:04:05"),
			balance.UpdatedAt.Format("2006-01-02 15:04:05"))
		buffer = append(buffer, []byte(line)...)
	}

	return buffer, nil
}

// generateTransactionExcel 生成交易记录Excel文件
func (s *DefaultAdminBalanceService) generateTransactionExcel(transactions []*model.BalanceTransaction) ([]byte, error) {
	// 这里应该使用Excel库生成文件，为了简化，返回CSV格式的字节数据
	var buffer []byte
	header := "交易ID,用户ID,类型,金额,货币,操作前余额,操作后余额,订单号,描述,操作员ID,状态,创建时间\n"
	buffer = append(buffer, []byte(header)...)

	for _, tx := range transactions {
		line := fmt.Sprintf("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
			tx.ID,
			tx.UserID,
			string(tx.Type),
			tx.Amount.String(),
			tx.Currency,
			tx.BalanceBefore.String(),
			tx.BalanceAfter.String(),
			tx.OrderNo,
			tx.Description,
			tx.OperatorID,
			string(tx.Status),
			tx.CreatedAt.Format("2006-01-02 15:04:05"))
		buffer = append(buffer, []byte(line)...)
	}

	return buffer, nil
}
