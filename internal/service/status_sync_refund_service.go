package service

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"
)

// StatusSyncRefundService 状态同步退款服务
// 🔥 企业级核心服务：当订单状态同步为取消时自动触发退款
type StatusSyncRefundService struct {
	db              *sql.DB
	orderRepository repository.OrderRepository
	balanceService  BalanceServiceInterface
	auditService    AuditServiceInterface
	logger          *zap.Logger
}

// NewStatusSyncRefundService 创建状态同步退款服务
func NewStatusSyncRefundService(
	db *sql.DB,
	orderRepository repository.OrderRepository,
	balanceService BalanceServiceInterface,
	auditService AuditServiceInterface,
	logger *zap.Logger,
) *StatusSyncRefundService {
	return &StatusSyncRefundService{
		db:              db,
		orderRepository: orderRepository,
		balanceService:  balanceService,
		auditService:    auditService,
		logger:          logger,
	}
}

// RefundPolicy 退款策略
type RefundPolicy struct {
	// 需要退款的状态列表
	RefundableStatuses []model.SystemOrderStatus
	// 退款计算策略
	RefundCalculationStrategy string
	// 是否允许部分退款
	AllowPartialRefund bool
	// 最大退款金额
	MaxRefundAmount decimal.Decimal
}

// GetDefaultRefundPolicy 获取默认退款策略
func (s *StatusSyncRefundService) GetDefaultRefundPolicy() *RefundPolicy {
	return &RefundPolicy{
		RefundableStatuses: []model.SystemOrderStatus{
			model.StatusCancelled,    // 已取消
			model.StatusVoided,       // 已作废
			model.StatusSubmitFailed, // 提交失败
			model.StatusPrintFailed,  // 面单生成失败
		},
		RefundCalculationStrategy: "full_refund", // 全额退款
		AllowPartialRefund:        false,
		MaxRefundAmount:           decimal.NewFromFloat(10000), // 最大退款10000元
	}
}

// ProcessStatusSyncRefund 处理状态同步退款
// 🔥 企业级核心方法：事务性处理状态更新和退款
func (s *StatusSyncRefundService) ProcessStatusSyncRefund(
	ctx context.Context,
	orderID int64,
	oldStatus, newStatus model.SystemOrderStatus,
	operatorID string,
) (*StatusSyncRefundResult, error) {

	result := &StatusSyncRefundResult{
		OrderID:         orderID,
		OldStatus:       oldStatus,
		NewStatus:       newStatus,
		RefundTriggered: false,
		RefundAmount:    decimal.Zero,
		ProcessTime:     util.NowBeijing(),
		Success:         false,
	}

	s.logger.Info("🔍 开始状态同步退款处理",
		zap.Int64("order_id", orderID),
		zap.String("old_status", string(oldStatus)),
		zap.String("new_status", string(newStatus)),
		zap.String("operator_id", operatorID))

	// 🔥 修复：检查新状态是否为需要退款的状态（不管旧状态如何）
	policy := s.GetDefaultRefundPolicy()
	if !s.isRefundableStatus(newStatus, policy) {
		result.Success = true
		result.Message = "当前状态不需要退款"
		s.logger.Info("✅ 当前状态不需要退款",
			zap.Int64("order_id", orderID),
			zap.String("new_status", string(newStatus)))
		return result, nil
	}

	// 2. 获取订单信息
	orderRecord, err := s.orderRepository.FindByID(ctx, orderID)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("获取订单信息失败: %v", err)
		s.logger.Error("❌ 获取订单信息失败",
			zap.Int64("order_id", orderID),
			zap.Error(err))
		return result, fmt.Errorf("获取订单信息失败: %w", err)
	}

	// 3. 🔥 检查是否已经退过款
	hasRefunded, err := s.checkIfAlreadyRefunded(ctx, orderRecord)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("检查退款状态失败: %v", err)
		s.logger.Error("❌ 检查退款状态失败",
			zap.Int64("order_id", orderID),
			zap.String("order_no", orderRecord.OrderNo),
			zap.Error(err))
		return result, fmt.Errorf("检查退款状态失败: %w", err)
	}

	if hasRefunded {
		result.Success = true
		result.Message = "订单已退款，跳过重复退款"
		s.logger.Info("✅ 订单已退款，跳过重复退款",
			zap.Int64("order_id", orderID),
			zap.String("order_no", orderRecord.OrderNo))
		return result, nil
	}

	// 4. 计算退款金额
	refundAmount, err := s.calculateRefundAmount(ctx, orderRecord, policy)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("计算退款金额失败: %v", err)
		s.logger.Error("❌ 计算退款金额失败",
			zap.Int64("order_id", orderID),
			zap.String("order_no", orderRecord.OrderNo),
			zap.Error(err))
		return result, fmt.Errorf("计算退款金额失败: %w", err)
	}

	if refundAmount.LessThanOrEqual(decimal.Zero) {
		result.Success = true
		result.Message = "无需退款（金额为零）"
		s.logger.Info("✅ 无需退款，金额为零",
			zap.Int64("order_id", orderID),
			zap.String("order_no", orderRecord.OrderNo))
		return result, nil
	}

	// 4. 开始事务处理
	tx, err := s.db.BeginTx(ctx, nil)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("开始事务失败: %v", err)
		return result, fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 5. 更新订单状态
	if err := s.orderRepository.UpdateStatus(ctx, orderID, string(newStatus)); err != nil {
		result.ErrorMessage = fmt.Sprintf("更新订单状态失败: %v", err)
		s.logger.Error("❌ 更新订单状态失败",
			zap.Int64("order_id", orderID),
			zap.Error(err))
		return result, fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 6. 执行退款
	if err := s.executeRefund(ctx, orderRecord, refundAmount, operatorID); err != nil {
		result.ErrorMessage = fmt.Sprintf("执行退款失败: %v", err)
		s.logger.Error("❌ 执行退款失败",
			zap.Int64("order_id", orderID),
			zap.String("order_no", orderRecord.OrderNo),
			zap.String("refund_amount", refundAmount.String()),
			zap.Error(err))
		return result, fmt.Errorf("执行退款失败: %w", err)
	}

	// 7. 提交事务
	if err := tx.Commit(); err != nil {
		result.ErrorMessage = fmt.Sprintf("提交事务失败: %v", err)
		return result, fmt.Errorf("提交事务失败: %w", err)
	}

	// 8. 记录审计日志
	s.logRefundAudit(ctx, orderRecord, refundAmount, oldStatus, newStatus, operatorID)

	result.Success = true
	result.RefundTriggered = true
	result.RefundAmount = refundAmount
	result.Message = "状态同步退款成功"

	s.logger.Info("✅ 状态同步退款处理完成",
		zap.Int64("order_id", orderID),
		zap.String("order_no", orderRecord.OrderNo),
		zap.String("user_id", orderRecord.UserID),
		zap.String("refund_amount", refundAmount.String()),
		zap.String("old_status", string(oldStatus)),
		zap.String("new_status", string(newStatus)))

	return result, nil
}

// isRefundableStatus 检查状态是否需要退款（不考虑状态变化）
func (s *StatusSyncRefundService) isRefundableStatus(
	status model.SystemOrderStatus,
	policy *RefundPolicy,
) bool {
	// 检查状态是否在退款状态列表中
	for _, refundableStatus := range policy.RefundableStatuses {
		if status == refundableStatus {
			return true
		}
	}
	return false
}

// checkIfAlreadyRefunded 检查订单是否已经退过款
func (s *StatusSyncRefundService) checkIfAlreadyRefunded(
	ctx context.Context,
	orderRecord *model.OrderRecord,
) (bool, error) {
	// 🔥 这里需要调用余额服务检查是否有退款记录
	// 由于我们没有直接的查询方法，我们可以通过尝试退款来检查
	// 如果余额服务有防重复退款机制，这个方法会更安全

	// 暂时返回false，让余额服务处理重复退款检查
	return false, nil
}

// calculateRefundAmount 计算退款金额
func (s *StatusSyncRefundService) calculateRefundAmount(
	ctx context.Context,
	orderRecord *model.OrderRecord,
	policy *RefundPolicy,
) (decimal.Decimal, error) {

	switch policy.RefundCalculationStrategy {
	case "full_refund":
		// 全额退款：返回订单价格
		return decimal.NewFromFloat(orderRecord.Price), nil

	case "partial_refund":
		// 部分退款：根据订单状态计算
		return s.calculatePartialRefund(ctx, orderRecord)

	default:
		return decimal.NewFromFloat(orderRecord.Price), nil
	}
}

// calculatePartialRefund 计算部分退款金额
func (s *StatusSyncRefundService) calculatePartialRefund(
	ctx context.Context,
	orderRecord *model.OrderRecord,
) (decimal.Decimal, error) {
	// 这里可以根据业务规则计算部分退款
	// 例如：根据订单进度、时间等因素
	orderPrice := decimal.NewFromFloat(orderRecord.Price)

	// 示例：如果订单还未开始处理，全额退款
	if orderRecord.Status == "submitted" || orderRecord.Status == "assigned" {
		return orderPrice, nil
	}

	// 示例：如果订单已开始处理，退款80%
	return orderPrice.Mul(decimal.NewFromFloat(0.8)), nil
}

// executeRefund 执行退款
func (s *StatusSyncRefundService) executeRefund(
	ctx context.Context,
	orderRecord *model.OrderRecord,
	refundAmount decimal.Decimal,
	operatorID string,
) error {

	// 使用现有的余额服务执行退款
	return s.balanceService.RefundForOrder(
		ctx,
		orderRecord.UserID,
		orderRecord.OrderNo,
		refundAmount,
	)
}

// logRefundAudit 记录退款审计日志
func (s *StatusSyncRefundService) logRefundAudit(
	ctx context.Context,
	orderRecord *model.OrderRecord,
	refundAmount decimal.Decimal,
	oldStatus, newStatus model.SystemOrderStatus,
	operatorID string,
) {
	if s.auditService != nil {
		s.auditService.LogOrderOperation(ctx, fmt.Sprintf("%d", orderRecord.ID), "status_sync_refund", operatorID, map[string]interface{}{
			"order_no":       orderRecord.OrderNo,
			"user_id":        orderRecord.UserID,
			"old_status":     string(oldStatus),
			"new_status":     string(newStatus),
			"refund_amount":  refundAmount.String(),
			"refund_reason":  "状态同步自动退款",
			"trigger_source": "status_sync",
		})
	}
}

// StatusSyncRefundResult 状态同步退款结果
type StatusSyncRefundResult struct {
	OrderID         int64                   `json:"order_id"`
	OldStatus       model.SystemOrderStatus `json:"old_status"`
	NewStatus       model.SystemOrderStatus `json:"new_status"`
	RefundTriggered bool                    `json:"refund_triggered"`
	RefundAmount    decimal.Decimal         `json:"refund_amount"`
	Success         bool                    `json:"success"`
	Message         string                  `json:"message"`
	ErrorMessage    string                  `json:"error_message,omitempty"`
	ProcessTime     time.Time               `json:"process_time"`
}
