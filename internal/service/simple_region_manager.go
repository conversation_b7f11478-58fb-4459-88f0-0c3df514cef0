package service

import (
	"fmt"
	"strings"

	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// Province 省份信息
type Province struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

// City 城市信息
type City struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

// District 区县信息
type District struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

// SimpleRegionManager 简化的地区管理器实现
type SimpleRegionManager struct {
	logger         *zap.Logger
	addressLibrary AddressLibraryService
}

// NewSimpleRegionManager 创建简化的地区管理器
func NewSimpleRegionManager(logger *zap.Logger, addressLibrary AddressLibraryService) *SimpleRegionManager {
	return &SimpleRegionManager{
		logger:         logger,
		addressLibrary: addressLibrary,
	}
}

// FindProvinceByName 根据名称查找省份
func (rm *SimpleRegionManager) FindProvinceByName(name string) (*Province, error) {
	// 获取地区数据
	areaData, err := rm.addressLibrary.GetAreaCascader()
	if err != nil {
		return nil, fmt.Errorf("获取地区数据失败: %w", err)
	}

	// 标准化省份名称
	normalizedName := rm.normalizeProvinceName(name)

	for _, province := range areaData {
		if rm.matchProvinceName(province.Label, normalizedName) {
			return &Province{
				Code: province.Value,
				Name: province.Label,
			}, nil
		}
	}

	return nil, fmt.Errorf("未找到省份: %s", name)
}

// FindCityByName 根据名称查找城市
func (rm *SimpleRegionManager) FindCityByName(provinceName, cityName string) (*City, error) {
	// 先找到省份
	province, err := rm.FindProvinceByName(provinceName)
	if err != nil {
		return nil, err
	}

	// 获取地区数据
	areaData, err := rm.addressLibrary.GetAreaCascader()
	if err != nil {
		return nil, fmt.Errorf("获取地区数据失败: %w", err)
	}

	// 找到对应的省份数据
	var provinceData *model.AreaNode
	for _, p := range areaData {
		if p.Value == province.Code {
			provinceData = p
			break
		}
	}

	if provinceData == nil || provinceData.Children == nil {
		return nil, fmt.Errorf("省份 %s 下没有城市数据", provinceName)
	}

	// 标准化城市名称
	normalizedName := rm.normalizeCityName(cityName)

	for _, city := range provinceData.Children {
		if rm.matchCityName(city.Label, normalizedName) {
			return &City{
				Code: city.Value,
				Name: city.Label,
			}, nil
		}
	}

	return nil, fmt.Errorf("在省份 %s 中未找到城市: %s", provinceName, cityName)
}

// FindDistrictByName 根据名称查找区县
func (rm *SimpleRegionManager) FindDistrictByName(provinceName, cityName, districtName string) (*District, error) {
	// 先找到城市
	city, err := rm.FindCityByName(provinceName, cityName)
	if err != nil {
		return nil, err
	}

	// 获取地区数据
	areaData, err := rm.addressLibrary.GetAreaCascader()
	if err != nil {
		return nil, fmt.Errorf("获取地区数据失败: %w", err)
	}

	// 找到对应的城市数据
	var cityData *model.AreaNode
	for _, province := range areaData {
		if province.Children != nil {
			for _, c := range province.Children {
				if c.Value == city.Code {
					cityData = c
					break
				}
			}
		}
		if cityData != nil {
			break
		}
	}

	if cityData == nil || cityData.Children == nil {
		return nil, fmt.Errorf("城市 %s 下没有区县数据", cityName)
	}

	// 标准化区县名称
	normalizedName := rm.normalizeDistrictName(districtName)

	for _, district := range cityData.Children {
		if rm.matchDistrictName(district.Label, normalizedName) {
			return &District{
				Code: district.Value,
				Name: district.Label,
			}, nil
		}
	}

	return nil, fmt.Errorf("在城市 %s 中未找到区县: %s", cityName, districtName)
}

// ValidateAddressHierarchy 验证地址层次结构
func (rm *SimpleRegionManager) ValidateAddressHierarchy(provinceName, cityName, districtName string) bool {
	// 检查省份是否存在
	province, err := rm.FindProvinceByName(provinceName)
	if err != nil {
		rm.logger.Debug("省份验证失败", zap.String("province", provinceName), zap.Error(err))
		return false
	}

	// 检查城市是否在该省份下
	city, err := rm.FindCityByName(province.Name, cityName)
	if err != nil {
		rm.logger.Debug("城市验证失败",
			zap.String("province", province.Name),
			zap.String("city", cityName),
			zap.Error(err))
		return false
	}

	// 如果没有区县信息，只验证到城市级别
	if districtName == "" {
		return true
	}

	// 检查区县是否在该城市下
	_, err = rm.FindDistrictByName(province.Name, city.Name, districtName)
	if err != nil {
		rm.logger.Debug("区县验证失败",
			zap.String("province", province.Name),
			zap.String("city", city.Name),
			zap.String("district", districtName),
			zap.Error(err))
		return false
	}

	return true
}

// 名称标准化和匹配方法

func (rm *SimpleRegionManager) normalizeProvinceName(name string) string {
	name = strings.TrimSpace(name)
	// 移除常见后缀
	suffixes := []string{"省", "市", "自治区", "特别行政区"}
	for _, suffix := range suffixes {
		if strings.HasSuffix(name, suffix) {
			return strings.TrimSuffix(name, suffix)
		}
	}
	return name
}

func (rm *SimpleRegionManager) normalizeCityName(name string) string {
	name = strings.TrimSpace(name)
	// 移除常见后缀
	suffixes := []string{"市", "区", "县", "自治州", "地区"}
	for _, suffix := range suffixes {
		if strings.HasSuffix(name, suffix) {
			return strings.TrimSuffix(name, suffix)
		}
	}
	return name
}

func (rm *SimpleRegionManager) normalizeDistrictName(name string) string {
	name = strings.TrimSpace(name)
	// 移除常见后缀
	suffixes := []string{"区", "县", "市"}
	for _, suffix := range suffixes {
		if strings.HasSuffix(name, suffix) {
			return strings.TrimSuffix(name, suffix)
		}
	}
	return name
}

func (rm *SimpleRegionManager) matchProvinceName(label, normalized string) bool {
	// 精确匹配
	if label == normalized {
		return true
	}

	// 去除后缀匹配
	labelNormalized := rm.normalizeProvinceName(label)
	return labelNormalized == normalized
}

func (rm *SimpleRegionManager) matchCityName(label, normalized string) bool {
	// 精确匹配
	if label == normalized {
		return true
	}

	// 去除后缀匹配
	labelNormalized := rm.normalizeCityName(label)
	return labelNormalized == normalized
}

func (rm *SimpleRegionManager) matchDistrictName(label, normalized string) bool {
	// 精确匹配
	if label == normalized {
		return true
	}

	// 去除后缀匹配
	labelNormalized := rm.normalizeDistrictName(label)
	return labelNormalized == normalized
}
