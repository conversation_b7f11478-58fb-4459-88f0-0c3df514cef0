package service

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// OrderPriceValidationService 订单价格验证服务接口
type OrderPriceValidationService interface {
	ValidateOrderPrice(ctx context.Context, req *model.OrderRequest) (*model.OrderPriceValidationResult, error)
}

// DefaultOrderPriceValidationService 默认订单价格验证服务实现
type DefaultOrderPriceValidationService struct {
	configService      PriceValidationConfigService
	weightCacheService WeightTierCacheService
	priceService       *PriceService
	providerManager    interface {
		Get(name string) (interface{}, bool)
	} // 🔥 供应商管理器接口
	blacklistService   *RegionBlacklistService // 🎯 新增：地区黑名单服务
	expressCompanyRepo interface {
		GetCompanyByCode(code string) (*express.ExpressCompany, error)
		GetConfigsByCompany(companyID string) ([]*express.ExpressCompanyConfig, error)
	} // 🚀 新增：快递公司仓储
	logger *zap.Logger
}

// NewOrderPriceValidationService 创建订单价格验证服务
func NewOrderPriceValidationService(
	configService PriceValidationConfigService,
	weightCacheService WeightTierCacheService,
	priceService *PriceService,
	providerManager interface {
		Get(name string) (interface{}, bool)
	},
	blacklistService *RegionBlacklistService,
	expressCompanyRepo interface {
		GetCompanyByCode(code string) (*express.ExpressCompany, error)
		GetConfigsByCompany(companyID string) ([]*express.ExpressCompanyConfig, error)
	},
	logger *zap.Logger,
) OrderPriceValidationService {
	return &DefaultOrderPriceValidationService{
		configService:      configService,
		weightCacheService: weightCacheService,
		priceService:       priceService,
		providerManager:    providerManager,
		blacklistService:   blacklistService,
		expressCompanyRepo: expressCompanyRepo,
		logger:             logger,
	}
}

// ValidateOrderPrice 验证订单价格
func (s *DefaultOrderPriceValidationService) ValidateOrderPrice(ctx context.Context, req *model.OrderRequest) (*model.OrderPriceValidationResult, error) {
	result := &model.OrderPriceValidationResult{
		ValidationTime: util.NowBeijing().Format(time.RFC3339),
	}

	// 1. 获取价格验证配置
	config, err := s.configService.GetPriceValidationConfig(ctx)
	if err != nil {
		s.logger.Error("获取价格验证配置失败", zap.Error(err))
		result.IsValid = false
		result.ValidationResult = "error"
		result.ActionTaken = "order_reject"
		result.ErrorMessage = fmt.Sprintf("获取价格验证配置失败: %v", err)
		return result, err
	}

	// 2. 检查是否启用价格验证
	if !config.EnableValidation {
		s.logger.Debug("价格验证已禁用，跳过验证")
		result.IsValid = true
		result.ValidationResult = "skip"
		result.ActionTaken = "order_proceed"
		return result, nil
	}

	// 3. 检查价格来源
	if req.PriceSource != "cache" {
		s.logger.Debug("非缓存价格，跳过验证",
			zap.String("price_source", req.PriceSource))
		result.IsValid = true
		result.ValidationResult = "skip"
		result.ActionTaken = "order_proceed"
		return result, nil
	}

	// 🚀 新增：检查是否为dedicated接口快递公司
	if s.isDedicatedInterfaceExpress(req.ExpressType) {
		s.logger.Warn("🔥 德邦/京东等dedicated接口快递，跳过价格验证",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("express_type", req.ExpressType),
			zap.String("reason", "dedicated接口快递公司使用实时查价，无需验证"))
		result.IsValid = true
		result.ValidationResult = "skip_dedicated"
		result.ActionTaken = "order_proceed"
		return result, nil
	}

	// 4. 验证缓存价格参数
	if req.CachedPrice <= 0 {
		s.logger.Error("缓存价格无效",
			zap.Float64("cached_price", req.CachedPrice))
		result.IsValid = false
		result.ValidationResult = "error"
		result.ActionTaken = "order_reject"
		result.ErrorMessage = "缓存价格无效，请重新查价"
		return result, fmt.Errorf("缓存价格无效: %f", req.CachedPrice)
	}

	result.CachedPrice = req.CachedPrice

	// 5. 调用实时API获取当前价格
	realtimePrice, err := s.getRealtimePrice(ctx, req, config)
	if err != nil {
		s.logger.Error("获取实时价格失败", zap.Error(err))
		result.IsValid = false
		result.ValidationResult = "error"
		result.ActionTaken = "order_reject"
		result.ErrorMessage = fmt.Sprintf("获取实时价格失败: %v", err)

		// 🔥 修复：价格验证失败时清除缓存
		if config.CacheInvalidateOnFail {
			s.logger.Info("价格验证失败，开始清除缓存",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("provider", req.Provider),
				zap.String("express_type", req.ExpressType))
			go s.invalidateCache(context.Background(), req)
		}

		return result, err
	}

	result.RealtimePrice = realtimePrice

	// 6. 计算价格差异
	priceDifference := math.Abs(req.CachedPrice - realtimePrice)
	result.PriceDifference = priceDifference

	s.logger.Info("价格验证对比",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.Float64("cached_price", req.CachedPrice),
		zap.Float64("realtime_price", realtimePrice),
		zap.Float64("price_difference", priceDifference),
		zap.Float64("price_tolerance", config.PriceTolerance))

	// 7. 判断价格是否在容差范围内
	if priceDifference <= config.PriceTolerance {
		// 价格验证通过
		result.IsValid = true
		result.ValidationResult = "pass"
		result.ActionTaken = "order_proceed"

		s.logger.Info("价格验证通过",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Float64("price_difference", priceDifference))

		return result, nil
	} else {
		// 价格验证失败
		result.IsValid = false
		result.ValidationResult = "fail"
		result.ActionTaken = "order_reject"

		// 🔥 统一的价格变动提示
		result.ErrorMessage = "此线路价格已变动，请您重新查价重新下单"

		s.logger.Warn("价格验证失败，价格已变动",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Float64("cached_price", req.CachedPrice),
			zap.Float64("realtime_price", realtimePrice),
			zap.Float64("price_difference", priceDifference),
			zap.Float64("price_tolerance", config.PriceTolerance))

		// 8. 如果配置了失败时失效缓存，则异步失效缓存
		if config.CacheInvalidateOnFail {
			go s.invalidateCacheByWeight(context.Background(), req)
		}

		return result, nil
	}
}

// getRealtimePrice 获取实时价格
func (s *DefaultOrderPriceValidationService) getRealtimePrice(ctx context.Context, req *model.OrderRequest, config *model.PriceValidationConfig) (float64, error) {
	// 设置超时上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(config.ValidationTimeout)*time.Second)
	defer cancel()

	// 🔥 修复：价格验证时直接调用供应商API，使用实际下单地址
	// 不通过重量档位缓存服务，避免使用固定的测试地址

	// 🔥 关键修复：计算计费重量，确保与缓存查询使用相同的重量
	chargedWeight := req.Package.Weight
	if req.Package.Length > 0 && req.Package.Width > 0 && req.Package.Height > 0 {
		// 计算体积重量（cm³ / 8000）
		volumeWeight := float64(req.Package.Length*req.Package.Width*req.Package.Height) / 8000.0
		if volumeWeight > chargedWeight {
			chargedWeight = volumeWeight
		}
	}

	s.logger.Info("价格验证 - 重量计算",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.Float64("original_weight", req.Package.Weight),
		zap.Float64("charged_weight", chargedWeight),
		zap.String("express_type", req.ExpressType))

	// 🔥 关键修复：构建价格查询请求，与查价阶段保持参数一致
	// 不传递体积信息，避免云通供应商对带体积参数的请求有更严格的路线限制
	priceReq := &model.PriceRequest{
		CustomerOrderNo: req.CustomerOrderNo,
		ExpressType:     req.ExpressType,
		ProductType:     req.ProductType,
		Sender:          req.Sender,   // 🔥 使用实际寄件人地址
		Receiver:        req.Receiver, // 🔥 使用实际收件人地址
		Package: model.PackageInfo{
			Weight:    chargedWeight, // 🔥 关键修复：使用计费重量，与缓存查询保持一致
			Quantity:  req.Package.Quantity,
			GoodsName: req.Package.GoodsName,
			// 🔥 关键：不传递体积相关参数，与查价阶段保持一致
			// Length:    req.Package.Length,
			// Width:     req.Package.Width,
			// Height:    req.Package.Height,
			// Volume:    req.Package.Volume,
		},
		PayMethod: req.PayMethod,
		Provider:  req.Provider,
		IsCompare: false, // 只获取指定供应商的价格
	}

	// 🔥 修复：直接调用供应商API，绕过价格服务的缓存和固定地址
	// 获取供应商适配器
	if s.providerManager == nil {
		return 0, fmt.Errorf("供应商管理器未初始化")
	}

	adapterInterface, exists := s.providerManager.Get(req.Provider)
	if !exists {
		return 0, fmt.Errorf("不支持的供应商: %s", req.Provider)
	}

	// 类型断言为供应商适配器
	adapter, ok := adapterInterface.(interface {
		QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error)
	})
	if !ok {
		return 0, fmt.Errorf("供应商适配器类型错误: %s", req.Provider)
	}

	// 重试机制 - 直接调用供应商API
	var lastErr error
	for attempt := 1; attempt <= config.MaxRetryAttempts; attempt++ {
		s.logger.Info("价格验证 - 直接调用供应商API",
			zap.String("provider", req.Provider),
			zap.String("express_type", req.ExpressType),
			zap.Int("attempt", attempt),
			zap.String("sender_address", req.Sender.Address),
			zap.String("receiver_address", req.Receiver.Address))

		// 直接调用供应商API
		prices, err := adapter.QueryPrice(timeoutCtx, priceReq)
		if err != nil {
			lastErr = err

			// 🎯 记录查价失败错误到黑名单（所有供应商统一处理）
			if strings.Contains(err.Error(), "不支持") || strings.Contains(err.Error(), "暂未开放") {
				if s.blacklistService != nil {
					route := fmt.Sprintf("%s->%s", req.Sender.Province, req.Receiver.Province)
					s.blacklistService.RecordFailure(req.Provider, route, req.ExpressType, err.Error())

					s.logger.Warn("供应商地区路线不支持（已记录到黑名单）",
						zap.String("provider", req.Provider),
						zap.String("route", route),
						zap.String("express_code", req.ExpressType),
						zap.String("error_type", "REGION_NOT_SUPPORTED"),
						zap.String("reason", "该地区路线供应商暂不支持，已自动加入黑名单"),
						zap.String("error_message", err.Error()))
				}
			}

			s.logger.Warn("实时查价失败，准备重试",
				zap.Int("attempt", attempt),
				zap.Int("max_attempts", config.MaxRetryAttempts),
				zap.Error(err))

			if attempt < config.MaxRetryAttempts {
				time.Sleep(time.Duration(attempt) * time.Second) // 递增延迟
				continue
			}
			break
		}

		if len(prices) == 0 {
			lastErr = fmt.Errorf("供应商 %s 没有可用的价格信息", req.Provider)
			s.logger.Warn("实时查价无结果，准备重试",
				zap.Int("attempt", attempt),
				zap.Int("max_attempts", config.MaxRetryAttempts),
				zap.String("provider", req.Provider))

			if attempt < config.MaxRetryAttempts {
				time.Sleep(time.Duration(attempt) * time.Second)
				continue
			}
			break
		}

		// 🔥 调试：打印所有返回的价格数据
		s.logger.Info("价格验证 - 供应商API返回数据",
			zap.String("provider", req.Provider),
			zap.String("express_type", req.ExpressType),
			zap.Int("data_count", len(prices)))

		for i, priceData := range prices {
			s.logger.Info("价格验证 - 价格数据详情",
				zap.Int("index", i),
				zap.String("returned_provider", priceData.Provider),
				zap.String("returned_express_code", priceData.ExpressCode),
				zap.Float64("price", priceData.Price),
				zap.String("express_name", priceData.ExpressName))
		}

		// 🔥 关键修复：查找匹配的价格，支持快递代码别名映射
		for _, priceData := range prices {
			if priceData.Provider == req.Provider {
				// 🔥 使用智能代码匹配，支持YD<->YUNDA等别名映射
				isMatch := s.isExpressCodeMatch(priceData.ExpressCode, req.ExpressType)
				s.logger.Info("价格验证 - 快递代码匹配检查",
					zap.String("provider", req.Provider),
					zap.String("returned_express_code", priceData.ExpressCode),
					zap.String("request_express_type", req.ExpressType),
					zap.Bool("is_match", isMatch))

				if isMatch {
					s.logger.Debug("获取实时价格成功（代码匹配）",
						zap.String("provider", req.Provider),
						zap.String("request_express_type", req.ExpressType),
						zap.String("returned_express_code", priceData.ExpressCode),
						zap.Float64("price", priceData.Price),
						zap.Int("attempt", attempt))
					return priceData.Price, nil
				}
			}
		}

		// 🔥 增强错误信息，显示所有可用的快递代码
		var availableCodes []string
		for _, priceData := range prices {
			if priceData.Provider == req.Provider {
				availableCodes = append(availableCodes, priceData.ExpressCode)
			}
		}

		lastErr = fmt.Errorf("未找到匹配的价格信息：供应商=%s，请求快递类型=%s，可用快递代码=%v",
			req.Provider, req.ExpressType, availableCodes)
	}

	return 0, fmt.Errorf("获取实时价格失败，已重试%d次: %w", config.MaxRetryAttempts, lastErr)
}

// isDedicatedInterfaceExpress 检查快递公司是否配置为dedicated接口
func (s *DefaultOrderPriceValidationService) isDedicatedInterfaceExpress(expressCode string) bool {
	if s.expressCompanyRepo == nil {
		return false
	}

	// 根据快递代码获取快递公司信息
	company, err := s.expressCompanyRepo.GetCompanyByCode(expressCode)
	if err != nil {
		s.logger.Debug("获取快递公司信息失败",
			zap.String("express_code", expressCode),
			zap.Error(err))
		return false
	}

	// 获取快递公司配置
	configs, err := s.expressCompanyRepo.GetConfigsByCompany(company.ID)
	if err != nil {
		s.logger.Debug("获取快递公司配置失败",
			zap.String("express_code", expressCode),
			zap.String("company_id", company.ID),
			zap.Error(err))
		return false
	}

	// 检查是否配置为dedicated接口
	for _, config := range configs {
		if config.ConfigKey == "interface_type" && config.ConfigValue == "dedicated" {
			s.logger.Info("快递公司配置为dedicated接口",
				zap.String("express_code", expressCode),
				zap.String("company_id", company.ID))
			return true
		}
	}

	return false
}

// invalidateCache 异步失效缓存（单个线路）
func (s *DefaultOrderPriceValidationService) invalidateCache(ctx context.Context, req *model.OrderRequest) {
	s.logger.Info("开始异步失效缓存",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("provider", req.Provider),
		zap.String("express_type", req.ExpressType))

	// 🔥 修复：实际调用缓存服务清除缓存
	if s.weightCacheService != nil {
		err := s.weightCacheService.InvalidateCacheEntry(ctx,
			req.Sender.Province,
			req.Receiver.Province,
			req.Provider,
			req.ExpressType,
			req.Package.Weight)

		if err != nil {
			s.logger.Error("缓存失效失败",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("from_province", req.Sender.Province),
				zap.String("to_province", req.Receiver.Province),
				zap.String("provider", req.Provider),
				zap.String("express_type", req.ExpressType),
				zap.Float64("weight", req.Package.Weight),
				zap.Error(err))
		} else {
			s.logger.Info("缓存失效成功",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("from_province", req.Sender.Province),
				zap.String("to_province", req.Receiver.Province),
				zap.String("provider", req.Provider),
				zap.String("express_type", req.ExpressType),
				zap.Float64("weight", req.Package.Weight))
		}
	} else {
		s.logger.Error("weightCacheService 未初始化，无法清除缓存")
	}
}

// invalidateCacheByWeight 按重量失效所有地区的缓存（批量失效）
func (s *DefaultOrderPriceValidationService) invalidateCacheByWeight(ctx context.Context, req *model.OrderRequest) {
	s.logger.Info("开始按重量批量失效缓存",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("provider", req.Provider),
		zap.String("express_type", req.ExpressType),
		zap.Float64("weight", req.Package.Weight))

	if s.weightCacheService != nil {
		// 🔥 新增：调用按重量批量失效缓存的方法
		err := s.weightCacheService.InvalidateCacheByWeight(ctx,
			req.Provider,
			req.ExpressType,
			req.Package.Weight)

		if err != nil {
			s.logger.Error("按重量批量失效缓存失败",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("provider", req.Provider),
				zap.String("express_type", req.ExpressType),
				zap.Float64("weight", req.Package.Weight),
				zap.Error(err))
		} else {
			s.logger.Info("按重量批量失效缓存成功",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("provider", req.Provider),
				zap.String("express_type", req.ExpressType),
				zap.Float64("weight", req.Package.Weight))
		}
	} else {
		s.logger.Error("weightCacheService 未初始化，无法清除缓存")
	}
}

// isExpressCodeMatch 智能快递代码匹配，支持别名映射
// 🔥 企业级修复：解决不同供应商使用不同快递代码标准的问题
func (s *DefaultOrderPriceValidationService) isExpressCodeMatch(code1, code2 string) bool {
	// 直接匹配
	if code1 == code2 {
		return true
	}

	// 标准化处理（去除空格，转大写）
	normalize := func(code string) string {
		return strings.ToUpper(strings.TrimSpace(code))
	}

	n1, n2 := normalize(code1), normalize(code2)
	if n1 == n2 {
		return true
	}

	// 🔥 快递代码标准化映射（键与值均需提前 Normalize）
	expressCodeMap := map[string][]string{
		"STO":  {"STO", "SHENTONG", "申通"},
		"ZTO":  {"ZTO", "ZHONGTONG", "中通"},
		"YTO":  {"YTO", "YUANTONG", "圆通"},
		"YD":   {"YD", "YUND", "YUNDA", "韵达", "YUNDA_EXPRESS"},
		"SF":   {"SF", "SHUNFENG", "顺丰"},
		"JD":   {"JD", "JINGDONG", "京东"},
		"EMS":  {"EMS", "EMS_CN", "邮政"},
		"JT":   {"JT", "JTSD", "JITU", "极兔"}, // 🔥 修复：添加JTSD映射，解决快递鸟极兔代码匹配问题
		"DBL":  {"DBL", "DEBANG", "德邦"},
		"HTKY": {"HTKY", "BAISHI", "百世"},
	}

	// 再做别名比较（同属一个主代码即视为匹配）
	for _, codes := range expressCodeMap {
		found1, found2 := false, false
		for _, c := range codes {
			cNorm := normalize(c)
			if cNorm == n1 {
				found1 = true
			}
			if cNorm == n2 {
				found2 = true
			}
		}
		if found1 && found2 {
			s.logger.Debug("快递代码别名匹配成功",
				zap.String("code1", code1),
				zap.String("code2", code2),
				zap.String("normalized1", n1),
				zap.String("normalized2", n2))
			return true
		}
	}

	return false
}
