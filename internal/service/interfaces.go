package service

import (
	"context"
	"errors"
	"time"

	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/model"
)

// 错误定义
var (
	ErrConfigNotFound     = errors.New("config not found")
	ErrInvalidConfigValue = errors.New("invalid config value")
)

// OrderServiceInterface 订单服务接口
type OrderServiceInterface interface {
	// 订单创建
	CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResponse, error)
	CreateOrderWithPreCharge(ctx context.Context, req *model.OrderRequest) (*model.OrderResponse, error)

	// 订单查询
	QueryOrder(ctx context.Context, req *model.QueryOrderRequest) (*model.QueryOrderResponse, error)
	GetOrderByNo(ctx context.Context, orderNo string) (*model.OrderRecord, error)
	GetOrderByCustomerNo(ctx context.Context, customerOrderNo string) (*model.OrderRecord, error)

	// 订单操作
	CancelOrder(ctx context.Context, req *model.CancelOrderRequest) (*model.CancelOrderResponse, error)
	UpdateOrderStatus(ctx context.Context, orderNo string, status string) error

	// 订单列表
	ListOrders(ctx context.Context, req *model.OrderListRequest) (*model.OrderListResponse, error)
	GetOrderStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (*model.OrderStatistics, error)
}

// BalanceServiceInterface 余额服务接口
type BalanceServiceInterface interface {
	// 余额查询
	GetBalance(ctx context.Context, userID string) (*model.BalanceResponse, error)
	GetBalanceHistory(ctx context.Context, userID string, limit, offset int) ([]*model.TransactionResponse, error)

	// 交易记录查询
	GetTransactionByOrderNo(ctx context.Context, orderNo string) (*model.TransactionResponse, error)

	// 余额操作
	Deposit(ctx context.Context, req *model.DepositRequest) (*model.TransactionResponse, error)
	Payment(ctx context.Context, req *model.PaymentRequest) (*model.TransactionResponse, error)
	Refund(ctx context.Context, req *model.BalanceRequest) (*model.TransactionResponse, error)
	Transfer(ctx context.Context, req *model.TransferRequest) (*model.TransactionResponse, error)

	PreChargeForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error
	PreChargeForOrderWithDetails(ctx context.Context, userID, customerOrderNo, platformOrderNo string, amount decimal.Decimal) error
	RefundForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error
	GetOrderNetPayment(ctx context.Context, userID, orderNo, customerOrderNo string) (decimal.Decimal, error)

	// 余额预检查方法 - 🔥 新增：修复余额不足却创建订单成功的BUG
	CheckBalanceForOrder(ctx context.Context, userID string, amount decimal.Decimal) (*model.BalanceCheckResult, error)
	CheckAndReserveBalance(ctx context.Context, userID string, amount decimal.Decimal, reservationID string) error
	ReleaseReservedBalance(ctx context.Context, userID string, reservationID string) error

	// 内部方法
	CreateBalanceIfNotExists(ctx context.Context, userID string) error
	ProcessPaymentCallback(ctx context.Context, depositID string, success bool, transactionID string) error
}

// BillingServiceInterface 计费服务接口
type BillingServiceInterface interface {
	// 计费更新
	UpdateOrderBilling(ctx context.Context, req *UpdateBillingRequest) error
	UpdateBillingWithTransaction(ctx context.Context, req *UpdateBillingRequest) error

	// 计费查询
	GetOrderBillingDetails(ctx context.Context, orderNo string) (*OrderBillingInfo, error)
	GetBillingHistory(ctx context.Context, orderNo string) ([]*model.OrderBillingHistory, error)

	// 费用差额处理
	ProcessBillingDifference(ctx context.Context, orderNo string, actualFee float64, reason string) error
	ProcessBillingDifferenceWithBalance(ctx context.Context, orderNo string, actualFee float64, reason string) error

	// 统计查询
	GetBillingStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (*BillingStatistics, error)

	// 费用计算
	CalculateFeeDifference(estimatedFee, actualFee float64) float64
	ValidateFeeAmount(amount float64) error
}

// CallbackServiceInterface 回调服务接口
type CallbackServiceInterface interface {
	// 回调处理
	ProcessCallback(ctx context.Context, provider string, rawData []byte, headers map[string]string) (*model.CallbackResponse, error)
	ProcessCallbackAsync(ctx context.Context, provider string, rawData []byte, headers map[string]string) error

	// 回调查询
	GetCallbackRecord(ctx context.Context, recordID string) (*model.UnifiedCallbackRecord, error)
	ListCallbackRecords(ctx context.Context, req *model.CallbackListRequest) (*model.CallbackListResponse, error)

	// 回调重试
	RetryCallback(ctx context.Context, recordID string) error
	RetryFailedCallbacks(ctx context.Context, maxRetries int) error

	// 适配器管理
	RegisterProviderAdapter(provider string, adapter CallbackAdapterInterface) error
	GetProviderAdapter(provider string) (CallbackAdapterInterface, bool)
}

// CallbackAdapterInterface 回调适配器接口
type CallbackAdapterInterface interface {
	// 签名验证
	ValidateSignature(rawData []byte, headers map[string]string) error

	// 数据解析
	ParseCallbackData(rawData []byte) (*model.CallbackData, error)

	// 数据标准化
	StandardizeData(data *model.CallbackData) (*model.StandardizedCallbackData, error)

	// 获取供应商名称
	GetProviderName() string

	// 获取支持的事件类型
	GetSupportedEventTypes() []string
}

// PriceServiceInterface 价格服务接口
type PriceServiceInterface interface {
	// 价格查询
	QueryPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error)
	QueryPriceWithCache(ctx context.Context, req *model.PriceRequest) (*model.PriceResponse, error)

	// 价格比较
	ComparePrice(ctx context.Context, req *model.PriceRequest) (*model.PriceComparisonResponse, error)
	GetBestPrice(ctx context.Context, req *model.PriceRequest) (*model.PriceInfo, error)

	// 缓存管理（已废弃，保持接口兼容性）
	WarmupCache(ctx context.Context, routes []model.RouteInfo) error
	ClearCache(ctx context.Context, cacheKey string) error

	// 价格统计
	GetPriceStatistics(ctx context.Context, req *model.PriceStatisticsRequest) (*model.PriceStatisticsResponse, error)

	// 供应商管理器访问（用于灵活价格表服务直接调用供应商API）
	GetProviderManager() *adapter.ProviderManager
}

// 注意：混合价格服务已废弃，已直接使用实时价格服务

// TransactionManagerInterface 事务管理器接口
type TransactionManagerInterface interface {
	// 事务执行
	ExecuteInTransaction(ctx context.Context, fn func(ctx context.Context) error) error
	ExecuteInTransactionWithOptions(ctx context.Context, opts *TransactionOptions, fn func(ctx context.Context) error) error

	// 事务重试
	ExecuteWithRetry(ctx context.Context, maxRetries int, fn func(ctx context.Context) error) error

	// 分布式事务支持
	BeginDistributedTransaction(ctx context.Context, transactionID string) error
	CommitDistributedTransaction(ctx context.Context, transactionID string) error
	RollbackDistributedTransaction(ctx context.Context, transactionID string) error
}

// ConfigServiceInterface 配置服务接口
type ConfigServiceInterface interface {
	// 配置获取
	GetBusinessConfig() *BusinessConfig
	GetOrderConfig() *OrderConfig
	GetBillingConfig() *BillingConfig
	GetBalanceConfig() *BalanceConfig
	GetCallbackConfig() *CallbackConfig

	// 配置更新
	UpdateBusinessConfig(config *BusinessConfig) error
	ReloadConfig() error

	// 配置验证
	ValidateConfig(config *BusinessConfig) error

	// 配置监听
	WatchConfigChanges(ctx context.Context, callback func(*BusinessConfig)) error
}

// AuditServiceInterface 审计服务接口
type AuditServiceInterface interface {
	// 操作记录
	LogOperation(ctx context.Context, operation *AuditOperation) error
	LogOrderOperation(ctx context.Context, orderNo, operation, userID string, details map[string]interface{}) error
	LogBalanceOperation(ctx context.Context, userID, operation string, amount decimal.Decimal, details map[string]interface{}) error

	// 审计查询
	GetAuditLogs(ctx context.Context, req *AuditLogRequest) (*AuditLogResponse, error)
	GetUserAuditLogs(ctx context.Context, userID string, startTime, endTime time.Time) ([]*AuditLog, error)

	// 审计统计
	GetAuditStatistics(ctx context.Context, req *AuditStatisticsRequest) (*AuditStatisticsResponse, error)
}

// NotificationServiceInterface 通知服务接口
type NotificationServiceInterface interface {
	// 通知发送
	SendOrderNotification(ctx context.Context, orderNo string, event string, data map[string]interface{}) error
	SendBalanceNotification(ctx context.Context, userID string, event string, data map[string]interface{}) error
	SendSystemNotification(ctx context.Context, level string, message string, data map[string]interface{}) error

	// 通知模板
	RegisterTemplate(templateID string, template *NotificationTemplate) error
	SendTemplateNotification(ctx context.Context, templateID string, recipients []string, data map[string]interface{}) error

	// 通知历史
	GetNotificationHistory(ctx context.Context, req *NotificationHistoryRequest) (*NotificationHistoryResponse, error)
}

// CacheServiceInterface 缓存服务接口（已废弃）
// 注意：缓存功能已完全移除，此接口仅保留用于向后兼容
type CacheServiceInterface interface {
	// 基础缓存操作（已废弃）
	Get(ctx context.Context, key string) (interface{}, error)
	Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)

	// 批量操作（已废弃）
	GetMulti(ctx context.Context, keys []string) (map[string]interface{}, error)
	SetMulti(ctx context.Context, items map[string]interface{}, ttl time.Duration) error
	DeleteMulti(ctx context.Context, keys []string) error

	// 缓存管理（已废弃）
	Clear(ctx context.Context, pattern string) error
	GetStats(ctx context.Context) (*CacheStats, error)

	// 分布式锁（已废弃）
	AcquireLock(ctx context.Context, key string, ttl time.Duration) (bool, error)
	ReleaseLock(ctx context.Context, key string) error
}

// 辅助类型定义
type TransactionOptions struct {
	IsolationLevel string
	ReadOnly       bool
	Timeout        time.Duration
}

// BillingStatistics 计费统计
type BillingStatistics struct {
	TotalOrders     int64           `json:"total_orders"`
	TotalAmount     decimal.Decimal `json:"total_amount"`
	AverageAmount   decimal.Decimal `json:"average_amount"`
	PendingOrders   int64           `json:"pending_orders"`
	ConfirmedOrders int64           `json:"confirmed_orders"`
	// 🚨 已删除：SettledOrders - 结算功能已废弃
}

// AuditOperation 审计操作
type AuditOperation struct {
	ID         string                 `json:"id"`
	UserID     string                 `json:"user_id"`
	Operation  string                 `json:"operation"`
	Resource   string                 `json:"resource"`
	ResourceID string                 `json:"resource_id"`
	Details    map[string]interface{} `json:"details"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	Timestamp  time.Time              `json:"timestamp"`
}

// AuditLog 审计日志
type AuditLog struct {
	ID         string                 `json:"id"`
	UserID     string                 `json:"user_id"`
	Operation  string                 `json:"operation"`
	Resource   string                 `json:"resource"`
	ResourceID string                 `json:"resource_id"`
	Details    map[string]interface{} `json:"details"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	Timestamp  time.Time              `json:"timestamp"`
	Status     string                 `json:"status"`
}

// AuditLogRequest 审计日志请求
type AuditLogRequest struct {
	UserID    string    `json:"user_id"`
	Operation string    `json:"operation"`
	Resource  string    `json:"resource"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Limit     int       `json:"limit"`
	Offset    int       `json:"offset"`
}

// AuditLogResponse 审计日志响应
type AuditLogResponse struct {
	Logs  []*AuditLog `json:"logs"`
	Total int64       `json:"total"`
}

// AuditStatisticsRequest 审计统计请求
type AuditStatisticsRequest struct {
	UserID    string    `json:"user_id"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

// AuditStatisticsResponse 审计统计响应
type AuditStatisticsResponse struct {
	TotalOperations int64            `json:"total_operations"`
	OperationStats  map[string]int64 `json:"operation_stats"`
	ResourceStats   map[string]int64 `json:"resource_stats"`
	DailyStats      map[string]int64 `json:"daily_stats"`
}

// NotificationTemplate 通知模板
type NotificationTemplate struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Type      string                 `json:"type"`
	Subject   string                 `json:"subject"`
	Content   string                 `json:"content"`
	Variables map[string]interface{} `json:"variables"`
}

// NotificationHistoryRequest 通知历史请求
type NotificationHistoryRequest struct {
	UserID    string    `json:"user_id"`
	Type      string    `json:"type"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Limit     int       `json:"limit"`
	Offset    int       `json:"offset"`
}

// NotificationHistoryResponse 通知历史响应
type NotificationHistoryResponse struct {
	Notifications []*NotificationHistory `json:"notifications"`
	Total         int64                  `json:"total"`
}

// NotificationHistory 通知历史
type NotificationHistory struct {
	ID      string                 `json:"id"`
	UserID  string                 `json:"user_id"`
	Type    string                 `json:"type"`
	Subject string                 `json:"subject"`
	Content string                 `json:"content"`
	Status  string                 `json:"status"`
	SentAt  time.Time              `json:"sent_at"`
	Data    map[string]interface{} `json:"data"`
}

// CacheStats 缓存统计
type CacheStats struct {
	Hits        int64   `json:"hits"`
	Misses      int64   `json:"misses"`
	HitRate     float64 `json:"hit_rate"`
	KeyCount    int64   `json:"key_count"`
	MemoryUsage int64   `json:"memory_usage"`
}

type BusinessConfig struct {
	Order       *OrderConfig
	Billing     *BillingConfig
	Balance     *BalanceConfig
	Callback    *CallbackConfig
	Transaction *TransactionConfig
	Retry       *RetryConfig
}

type OrderConfig struct {
	PreChargeEnabled bool
	PreChargeTimeout time.Duration
	CreateTimeout    time.Duration
	MaxOrdersPerUser int
	MaxOrdersPerDay  int
	MaxWeightKg      decimal.Decimal
	MaxVolumeCubicM  decimal.Decimal
}

type BillingConfig struct {
	PrecisionThreshold    decimal.Decimal
	RoundingMode          string
	MaxFeeAmount          decimal.Decimal
	MinFeeAmount          decimal.Decimal
	AutoProcessThreshold  decimal.Decimal
	ManualReviewThreshold decimal.Decimal
	ProcessTimeout        time.Duration
}

type BalanceConfig struct {
	MaxBalance            decimal.Decimal
	MinBalance            decimal.Decimal
	FreezeTimeout         time.Duration
	AutoUnfreezeEnabled   bool
	MaxTransactionAmount  decimal.Decimal
	DailyTransactionLimit decimal.Decimal
	ConcurrencyControl    bool
	LockTimeout           time.Duration
}

type CallbackConfig struct {
	MaxConcurrency      int
	ProcessTimeout      time.Duration
	MaxRetries          int
	RetryInterval       time.Duration
	ForwardEnabled      bool
	ForwardTimeout      time.Duration
	SignatureValidation bool
	TimestampValidation bool
	TimestampTolerance  time.Duration
}

type TransactionConfig struct {
	DefaultTimeout     time.Duration
	LongRunningTimeout time.Duration
	IsolationLevel     string
	MaxRetries         int
	RetryInterval      time.Duration
	DeadlockDetection  bool
	DeadlockTimeout    time.Duration
}

type RetryConfig struct {
	MaxAttempts       int
	InitialInterval   time.Duration
	MaxInterval       time.Duration
	BackoffMultiplier float64
	JitterEnabled     bool
	JitterFactor      float64
	RetryableErrors   []string
}

// AdminOrderServiceInterface 管理员订单服务接口
type AdminOrderServiceInterface interface {
	// 管理员订单列表查询
	GetAdminOrderList(ctx context.Context, req *model.AdminOrderListRequest) (*model.AdminOrderListResponse, error)

	// 管理员订单详情查询
	GetAdminOrderDetail(ctx context.Context, orderID int64, operatorID string) (*model.AdminOrderDetail, error)

	// 管理员更新订单状态
	UpdateOrderStatus(ctx context.Context, orderID int64, newStatus, operatorID, reason string) error

	// 管理员批量更新订单状态
	BatchUpdateOrderStatus(ctx context.Context, orderIDs []int64, newStatus, operatorID, reason string) (*BatchOperationResult, error)

	// 管理员获取订单统计
	GetAdminOrderStatistics(ctx context.Context, filter *AdminStatisticsFilter) (*model.AdminOrderStatistics, error)

	// 管理员导出订单数据
	ExportOrders(ctx context.Context, req *model.AdminOrderListRequest, operatorID string) ([]byte, error)

	// 查询供应商运费信息
	QueryProviderShippingFee(ctx context.Context, orderID int64, operatorID string) (*model.ProviderShippingFeeResponse, error)

	// 批量查询供应商运费信息
	BatchQueryProviderShippingFee(ctx context.Context, orderIDs []int64, operatorID string) (*model.BatchProviderShippingFeeResponse, error)

	// 查询详细的供应商运费信息
	QueryDetailedProviderShippingFee(ctx context.Context, orderID int64, operatorID string) (*model.DetailedShippingFeeInfo, error)

	// 批量验证订单价格
	BatchValidatePrices(ctx context.Context, req *model.BatchPriceValidationRequest, operatorID string) (*model.BatchPriceValidationResponse, error)

	// 同步单个订单状态
	SyncOrderStatus(ctx context.Context, orderID int64) (*model.StatusSyncResult, error)

	// 批量同步订单状态
	BatchSyncOrderStatus(ctx context.Context, req *model.BatchStatusSyncRequest, operatorID string) (*model.BatchStatusSyncResponse, error)
}

// ProviderShippingFeeServiceInterface 供应商运费查询服务接口
type ProviderShippingFeeServiceInterface interface {
	// 查询单个订单的供应商运费
	QueryShippingFee(ctx context.Context, orderRecord *model.OrderRecord) (*model.DetailedShippingFeeInfo, error)

	// 批量查询供应商运费
	BatchQueryShippingFee(ctx context.Context, orderRecords []*model.OrderRecord) ([]*model.DetailedShippingFeeInfo, error)

	// 检查供应商是否支持运费查询
	IsProviderSupported(provider string) bool

	// 获取支持的供应商列表
	GetSupportedProviders() []string

	// 检查是否是订单状态异常错误
	IsProviderOrderStatusError(errorMsg string) bool
}

// PriceValidationServiceInterface 价格验证服务接口
type PriceValidationServiceInterface interface {
	// 批量验证订单价格
	ValidateOrderPrices(ctx context.Context, orders []*model.AdminOrderListItem) error

	// 清理过期缓存
	ClearExpiredCache()

	// 获取缓存统计信息
	GetCacheStats() map[string]interface{}
}

// SystemConfigServiceInterface 系统配置服务接口
type SystemConfigServiceInterface interface {
	// 基础配置获取
	GetConfig(key string) (string, error)
	GetConfigWithDefault(key, defaultValue string) string
	GetConfigAsInt(key string) (int, error)
	GetConfigAsIntWithDefault(key string, defaultValue int) int
	GetConfigAsBool(key string) (bool, error)
	GetConfigAsBoolWithDefault(key string, defaultValue bool) bool
	GetConfigAsJSON(key string, result interface{}) error

	// 计费状态相关
	GetValidBillingStatuses() ([]string, error)
	IsBillingStatusValid(status string) bool

	// 搜索字段相关
	GetValidSearchFields(userType string) ([]string, error)
	IsSearchFieldValid(field, userType string) bool
	GetDefaultSearchFields() ([]string, error)

	// 排序字段相关
	GetValidSortFields(userType string) (map[string]string, error)
	IsSortFieldValid(field, userType string) bool
	GetDefaultSortField() string
	GetDefaultSortOrder() string

	// 状态转换相关
	GetValidStatusTransitions(fromStatus, userType string) ([]string, error)
	IsStatusTransitionValid(fromStatus, toStatus, userType string) bool

	// 操作权限相关
	GetOperationPermissions(status, userType string) (map[string]bool, error)
	IsOperationAllowed(status, operation, userType string) bool

	// 批量操作限制
	GetBatchOperationMaxItems() int
	GetExportMaxItems() int

	// 缓存管理
	RefreshCache() error
	ClearCache()
}

// BatchOperationResult 批量操作结果
type BatchOperationResult struct {
	TotalItems   int           `json:"total_items"`
	SuccessItems int           `json:"success_items"`
	FailedItems  int           `json:"failed_items"`
	Errors       []string      `json:"errors,omitempty"`
	Duration     time.Duration `json:"duration"`
}

// AdminStatisticsFilter 管理员统计过滤器
type AdminStatisticsFilter struct {
	UserID        *string    `json:"user_id,omitempty"`
	Status        *string    `json:"status,omitempty"`
	Provider      *string    `json:"provider,omitempty"`
	BillingStatus *string    `json:"billing_status,omitempty"`
	StartTime     *time.Time `json:"start_time,omitempty"`
	EndTime       *time.Time `json:"end_time,omitempty"`
}
