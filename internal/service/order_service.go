package service

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"regexp"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/shopspring/decimal"
	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"
)

// OrderService 订单服务
type OrderService struct {
	providerManager        *adapter.ProviderManager
	priceService           PriceServiceInterface
	orderRepository        repository.OrderRepository
	balanceService         BalanceServiceInterface
	configService          ConfigServiceInterface
	transactionManager     TransactionManagerInterface
	auditService           AuditServiceInterface
	priceValidationService OrderPriceValidationService       // 价格验证服务
	orderAttemptRepo       repository.OrderAttemptRepository // 🚀 新增：下单尝试记录仓库
	statusHistoryService   OrderStatusHistoryService         // 🔥 新增：状态历史服务
	failedOrderService     *FailedOrderService               // 🔥 新增：失败订单服务
	platformOrderGenerator *PlatformOrderGenerator           // 🔥 新增：平台订单号生成器
	logger                 *zap.Logger
}

// NewOrderService 创建订单服务
func NewOrderService(
	providerManager *adapter.ProviderManager,
	priceService *PriceService,
	orderRepository repository.OrderRepository,
) *OrderService {
	return &OrderService{
		providerManager: providerManager,
		priceService:    priceService,
		orderRepository: orderRepository,
	}
}

// NewEnhancedOrderService 创建增强订单服务
func NewEnhancedOrderService(
	providerManager *adapter.ProviderManager,
	priceService PriceServiceInterface,
	orderRepository repository.OrderRepository,
	balanceService BalanceServiceInterface,
	configService ConfigServiceInterface,
	transactionManager TransactionManagerInterface,
	auditService AuditServiceInterface,
	priceValidationService OrderPriceValidationService,
	orderAttemptRepo repository.OrderAttemptRepository, // 🚀 新增：下单尝试记录仓库
	statusHistoryService OrderStatusHistoryService, // 🔥 新增：状态历史服务
	failedOrderService *FailedOrderService, // 🔥 新增：失败订单服务
	platformOrderGenerator *PlatformOrderGenerator, // 🔥 新增：平台订单号生成器
	logger *zap.Logger,
) *OrderService {
	return &OrderService{
		providerManager:        providerManager,
		priceService:           priceService,
		orderRepository:        orderRepository,
		balanceService:         balanceService,
		configService:          configService,
		transactionManager:     transactionManager,
		auditService:           auditService,
		priceValidationService: priceValidationService,
		orderAttemptRepo:       orderAttemptRepo,       // 🚀 新增：注入下单尝试记录仓库
		statusHistoryService:   statusHistoryService,   // 🔥 新增：注入状态历史服务
		failedOrderService:     failedOrderService,     // 🔥 新增：注入失败订单服务
		platformOrderGenerator: platformOrderGenerator, // 🔥 新增：注入平台订单号生成器
		logger:                 logger,
	}
}

// SetPriceUpdateTriggerService 设置价格更新触发服务（已废弃）
func (s *OrderService) SetPriceUpdateTriggerService(priceUpdateTriggerService interface{}) {
	// 已废弃，保持兼容性
}

// CreateOrderWithPreCharge 创建订单并强制预扣费
func (s *OrderService) CreateOrderWithPreCharge(ctx context.Context, req *model.OrderRequest) (*model.OrderResponse, error) {
	// 强制检查必要服务
	if s.configService == nil || s.balanceService == nil || s.transactionManager == nil {
		// 🔥 调试：记录哪些服务为nil
		s.logger.Error("🚨 CreateOrderWithPreCharge: 必要服务未配置",
			zap.Bool("configService_nil", s.configService == nil),
			zap.Bool("balanceService_nil", s.balanceService == nil),
			zap.Bool("transactionManager_nil", s.transactionManager == nil))

		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "系统配置错误：扣费服务未配置，无法处理订单",
		}, nil
	}

	// 🔥 调试：记录进入CreateOrderWithPreCharge方法
	s.logger.Info("🔥 CreateOrderWithPreCharge: 开始执行",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("user_id", req.UserID))

	// 强制要求用户ID
	if req.UserID == "" {
		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "用户ID不能为空，所有订单必须关联用户账户",
		}, nil
	}

	// 获取配置（强制启用预扣费）
	orderConfig := s.configService.GetOrderConfig()
	if orderConfig == nil {
		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "系统配置错误：订单配置未找到",
		}, nil
	}

	// 🔥 修复：改为先创建订单，成功后再扣费的正确流程
	var orderResponse *model.OrderResponse
	err := s.transactionManager.ExecuteInTransactionWithOptions(ctx, &TransactionOptions{
		Timeout: orderConfig.CreateTimeout,
	}, func(txCtx context.Context) error {
		// 1. 验证请求参数
		if err := s.validateOrderRequest(req); err != nil {
			return errors.WrapError(errors.ErrCodeInvalidRequest, "订单参数验证失败", err)
		}

		// 2. 实时价格验证（如果启用且使用缓存价格）
		var validationResult *model.OrderPriceValidationResult
		if s.priceValidationService != nil {
			result, err := s.priceValidationService.ValidateOrderPrice(txCtx, req)
			if err != nil {
				s.logger.Error("价格验证失败",
					zap.String("customer_order_no", req.CustomerOrderNo),
					zap.Error(err))
				return errors.WrapError(errors.ErrCodeInternal, "价格验证失败", err)
			}
			validationResult = result

			// 如果价格验证不通过，拒绝订单
			if !validationResult.IsValid {
				s.logger.Warn("价格验证不通过，拒绝订单",
					zap.String("customer_order_no", req.CustomerOrderNo),
					zap.String("validation_result", validationResult.ValidationResult),
					zap.String("error_message", validationResult.ErrorMessage),
					zap.Float64("cached_price", validationResult.CachedPrice),
					zap.Float64("realtime_price", validationResult.RealtimePrice),
					zap.Float64("price_difference", validationResult.PriceDifference))

				return errors.WrapError(errors.ErrCodePriceChanged, validationResult.ErrorMessage,
					fmt.Errorf("价格验证失败: %s", validationResult.ValidationResult))
			}

			s.logger.Info("价格验证通过",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("validation_result", validationResult.ValidationResult),
				zap.Float64("cached_price", validationResult.CachedPrice),
				zap.Float64("realtime_price", validationResult.RealtimePrice))
		}

		// 3. 计算价格并确定供应商
		estimatedFee, selectedProvider, err := s.calculatePrice(txCtx, req)
		if err != nil {
			return errors.WrapError(errors.ErrCodeInternal, "计算价格失败", err)
		}

		// 4. 将选定的供应商设置到请求中，确保下单使用相同供应商
		req.Provider = selectedProvider

		// 🔥 关键修复：在订单创建前进行余额预检查，避免余额不足却创建订单成功的BUG
		s.logger.Info("🔥 开始余额预检查",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.String("estimated_fee", estimatedFee.String()))

		s.recordOrderAttempt(txCtx, req, "BALANCE_PRE_CHECK", "开始余额预检查", nil)

		// 🔥 调试：检查余额服务是否可用
		if s.balanceService == nil {
			s.logger.Error("🚨 余额服务为nil，无法执行余额检查",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("user_id", req.UserID))
			return errors.WrapError(errors.ErrCodeInternal, "余额服务未配置", nil)
		}

		s.logger.Info("🔥 调用余额服务CheckBalanceForOrder方法",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.String("estimated_fee", estimatedFee.String()),
			zap.String("balance_service_type", fmt.Sprintf("%T", s.balanceService)))

		balanceCheckResult, err := s.balanceService.CheckBalanceForOrder(txCtx, req.UserID, estimatedFee)

		s.logger.Info("🔥 余额服务CheckBalanceForOrder方法调用完成",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.Bool("has_error", err != nil),
			zap.Bool("has_result", balanceCheckResult != nil))

		if err != nil {
			s.logger.Error("🚨 余额预检查失败",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("user_id", req.UserID),
				zap.Error(err))
			s.recordOrderAttempt(txCtx, req, "BALANCE_PRE_CHECK_ERROR", "余额预检查失败", err)
			return errors.WrapError(errors.ErrCodeInternal, "余额检查失败", err)
		}

		if !balanceCheckResult.IsSufficient {
			s.recordOrderAttempt(txCtx, req, "BALANCE_PRE_CHECK_INSUFFICIENT", "余额不足", nil)
			s.logger.Warn("余额不足，拒绝创建订单",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("user_id", req.UserID),
				zap.String("requested_amount", estimatedFee.String()),
				zap.String("current_balance", balanceCheckResult.CurrentBalance.String()),
				zap.String("shortage", balanceCheckResult.Shortage.String()))

			// 🔥 优化：创建友好的余额不足错误信息
			balanceError := &model.BalanceInsufficientError{
				CurrentBalance:  balanceCheckResult.CurrentBalance.InexactFloat64(),
				RequiredAmount:  estimatedFee.InexactFloat64(),
				ShortageAmount:  balanceCheckResult.Shortage.InexactFloat64(),
				RechargeURL:     "/api/v1/balance/deposit", // 充值接口
				RechargeGuide:   "请联系客服充值",
				SuggestedAmount: balanceCheckResult.Shortage.Add(decimal.NewFromFloat(50)).InexactFloat64(), // 建议多充值50元
				UserFriendlyMsg: "账户余额不足，请联系客服",
			}

			// 🔥 使用现有的错误包装方法，将详细信息放在错误消息中
			return errors.WrapError(errors.ErrCodeInsufficientBalance,
				balanceError.UserFriendlyMsg, nil)
		}
		s.recordOrderAttempt(txCtx, req, "BALANCE_PRE_CHECK_SUCCESS", "余额预检查通过", nil)

		// 5. 🔥 关键修复：余额检查通过后才创建订单
		s.recordOrderAttempt(txCtx, req, "ORDER_CREATE_START", "开始创建订单（修复后流程）", nil)
		response, err := s.createOrderInternalWithValidation(txCtx, req, estimatedFee, validationResult)
		if err != nil {
			s.recordOrderAttempt(txCtx, req, "ORDER_CREATE_FAILED", "订单创建失败", err)
			return err
		}
		s.recordOrderAttempt(txCtx, req, "ORDER_CREATE_SUCCESS", "订单创建成功", nil)

		// 6. 🔥 关键修复：只有订单创建成功后才执行扣费
		s.recordOrderAttempt(txCtx, req, "BALANCE_POST_CHARGE", "订单创建成功，开始扣费", nil)

		// 🚀 性能优化：使用新的PreChargeForOrderWithDetails方法，传入完整订单信息
		// 从response中获取平台订单号
		platformOrderNo := ""
		if response.Data != nil {
			platformOrderNo = response.Data.PlatformOrderNo
		}

		if err := s.balanceService.PreChargeForOrderWithDetails(txCtx, req.UserID, req.CustomerOrderNo, platformOrderNo, estimatedFee); err != nil {
			s.recordOrderAttempt(txCtx, req, "BALANCE_POST_CHARGE_FAILED", "订单创建成功后扣费失败", err)
			return errors.WrapError(errors.ErrCodeInsufficientBalance, "订单创建成功但扣费失败", err)
		}
		s.recordOrderAttempt(txCtx, req, "BALANCE_POST_CHARGE_SUCCESS", "订单创建成功后扣费完成", nil)

		orderResponse = response

		// 7. 记录审计日志
		if s.auditService != nil {
			s.auditService.LogOrderOperation(txCtx, response.Data.OrderNo, "create_with_post_charge", req.UserID, map[string]any{
				"estimated_fee":     estimatedFee,
				"customer_order_no": req.CustomerOrderNo,
				"provider":          selectedProvider,
				"express_type":      req.ExpressType,
			})
		}

		return nil
	})

	if err != nil {
		s.logger.Error("订单创建流程失败（修复后流程）",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.Error(err))

		// 🔥 修复后：由于改为先创建订单再扣费，失败时不会有预扣费问题
		// 如果是订单创建失败，则没有扣费；如果是扣费失败，事务会回滚订单创建
		s.logger.Info("订单创建流程失败，修复后流程确保无资金风险",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.Error(err),
			zap.String("fix_status", "no_manual_intervention_needed"))

		// 🔥 关键修复：在事务外保存失败订单记录，确保用户能在订单列表中看到失败订单
		if s.failedOrderService != nil {
			go func() {
				// 使用新的context避免取消影响失败订单保存
				newCtx := context.Background()

				// 计算预估价格用于失败订单记录
				estimatedFee := decimal.Zero
				if s.priceService != nil {
					priceReq := &model.PriceRequest{
						CustomerOrderNo: req.CustomerOrderNo,
						ExpressType:     req.ExpressType,
						Sender: model.SenderInfo{
							Province: req.Sender.Province,
							City:     req.Sender.City,
							District: req.Sender.District,
						},
						Receiver: model.ReceiverInfo{
							Province: req.Receiver.Province,
							City:     req.Receiver.City,
							District: req.Receiver.District,
						},
						Package: model.PackageInfo{
							Weight:   req.Package.Weight,
							Volume:   req.Package.Volume,
							Quantity: req.Package.Quantity,
						},
						PayMethod: req.PayMethod,
					}
					if priceResp, priceErr := s.priceService.QueryPrice(newCtx, priceReq); priceErr == nil && priceResp.Success && len(priceResp.Data) > 0 {
						estimatedFee = decimal.NewFromFloat(priceResp.Data[0].Price)
					}
				}

				failedOrderReq := &model.FailedOrderCreateRequest{
					OrderRequest:   req,
					EstimatedPrice: estimatedFee.InexactFloat64(),
					FailureInfo: &model.OrderFailureInfo{
						FailureReason:  s.extractFailureReason(err),
						FailureMessage: err.Error(),
						FailureStage:   s.extractFailureStage(err),
						FailureSource:  s.extractFailureSource(err),
						FailureTime:    time.Now(),
						CanRetry:       s.canRetryError(err),
					},
				}

				if saveErr := s.failedOrderService.CreateFailedOrderRecord(newCtx, failedOrderReq); saveErr != nil {
					s.logger.Error("保存失败订单记录失败",
						zap.String("customer_order_no", req.CustomerOrderNo),
						zap.String("user_id", req.UserID),
						zap.Error(saveErr))
				} else {
					s.logger.Info("成功保存失败订单记录，用户可在订单列表中查看",
						zap.String("customer_order_no", req.CustomerOrderNo),
						zap.String("user_id", req.UserID),
						zap.String("failure_reason", s.extractFailureReason(err)))
				}
			}()
		}

		// 返回业务错误
		if businessErr, ok := err.(errors.BusinessError); ok {
			return &model.OrderResponse{
				Success: false,
				Code:    businessErr.HTTPStatus(),
				Message: businessErr.Message(),
			}, nil
		}

		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: err.Error(),
		}, nil
	}

	return orderResponse, nil
}

// calculatePrice 计算价格（确保供应商一致性）
func (s *OrderService) calculatePrice(ctx context.Context, req *model.OrderRequest) (decimal.Decimal, string, error) {
	// 1. 先确定要使用的供应商
	provider, err := s.selectProvider(ctx, req)
	if err != nil {
		return decimal.Zero, "", fmt.Errorf("选择供应商失败: %w", err)
	}

	// 2. 构建价格查询请求，指定供应商确保一致性
	priceReq := &model.PriceRequest{
		CustomerOrderNo: req.CustomerOrderNo,
		ExpressType:     req.ExpressType,
		ProductType:     req.ProductType,
		Sender:          req.Sender,
		Receiver:        req.Receiver,
		Package:         req.Package,
		PayMethod:       req.PayMethod,
		Provider:        provider, // 强制指定供应商
		IsCompare:       false,    // 只获取指定供应商的价格
	}

	// 3. 查询指定供应商的价格
	resp, err := s.priceService.QueryPrice(ctx, priceReq)
	if err != nil {
		return decimal.Zero, "", fmt.Errorf("查询供应商 %s 价格失败: %w", provider, err)
	}

	if !resp.Success || len(resp.Data) == 0 {
		return decimal.Zero, "", fmt.Errorf("供应商 %s 没有可用的价格信息", provider)
	}

	// 4. 🔥 修复：选择最优价格而不是第一个价格
	var bestPriceData *model.StandardizedPrice
	var bestPrice float64 = math.MaxFloat64

	// 遍历所有价格选项，选择最低价格
	for i, priceData := range resp.Data {
		// 验证供应商一致性
		if priceData.Provider != provider {
			s.logger.Warn("跳过供应商不一致的价格",
				zap.Int("index", i),
				zap.String("expected_provider", provider),
				zap.String("actual_provider", priceData.Provider))
			continue
		}

		// 验证快递公司一致性
		if priceData.ExpressCode != req.ExpressType && !s.isExpressCodeMatch(priceData.ExpressCode, req.ExpressType) {
			s.logger.Debug("跳过快递公司不匹配的价格",
				zap.Int("index", i),
				zap.String("expected_express", req.ExpressType),
				zap.String("actual_express", priceData.ExpressCode))
			continue
		}

		// 选择最低价格
		if priceData.Price < bestPrice {
			bestPrice = priceData.Price
			bestPriceData = &priceData
			s.logger.Info("发现更优价格",
				zap.Int("index", i),
				zap.String("channel_id", priceData.ChannelID),
				zap.String("product_code", priceData.ProductCode),
				zap.Float64("price", priceData.Price))
		}
	}

	// 检查是否找到有效价格
	if bestPriceData == nil {
		return decimal.Zero, "", fmt.Errorf("供应商 %s 没有匹配的价格信息，快递公司: %s", provider, req.ExpressType)
	}

	s.logger.Info("🎯 选择最优价格成功",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("provider", provider),
		zap.String("express_type", req.ExpressType),
		zap.String("selected_channel", bestPriceData.ChannelID),
		zap.String("selected_product", bestPriceData.ProductCode),
		zap.Float64("selected_price", bestPriceData.Price),
		zap.Int("total_options", len(resp.Data)))

	// 5. 🔥 关键修复：将选择的渠道信息保存到订单请求中，确保下单使用相同渠道
	req.ChannelID = bestPriceData.ChannelID
	// 使用ProductType字段保存产品代码（如果原来没有设置的话）
	if req.ProductType == "" {
		req.ProductType = bestPriceData.ProductCode
	}

	s.logger.Info("🔗 渠道信息已传递到订单请求",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("channel_id", req.ChannelID),
		zap.String("product_type", req.ProductType),
		zap.String("selected_product_code", bestPriceData.ProductCode),
		zap.String("provider", provider))

	// 6. 返回最优价格和确定的供应商
	return decimal.NewFromFloat(bestPriceData.Price), provider, nil
}

// createOrderInternalWithValidation 内部创建订单方法（带价格验证结果）
func (s *OrderService) createOrderInternalWithValidation(ctx context.Context, req *model.OrderRequest, estimatedFee decimal.Decimal, validationResult *model.OrderPriceValidationResult) (*model.OrderResponse, error) {
	// 🚀 新增：记录下单开始
	s.recordOrderAttempt(ctx, req, "ORDER_CREATE_START", "开始创建订单", nil)

	// 使用已确定的供应商（在calculateEstimatedFee中已设置）
	provider := req.Provider
	if provider == "" {
		// 如果没有预设供应商，则选择供应商（兼容性处理）
		var err error
		provider, err = s.selectProvider(ctx, req)
		if err != nil {
			s.recordOrderAttempt(ctx, req, "PROVIDER_SELECT_FAILED", "选择供应商失败", err)
			return nil, errors.WrapError(errors.ErrCodeProviderNotFound, "选择供应商失败", err)
		}
	}

	s.logger.Info("使用供应商创建订单",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("provider", provider),
		zap.String("express_type", req.ExpressType))

	// 🚀 新增：记录供应商选择
	s.recordOrderAttempt(ctx, req, "PROVIDER_SELECTED", fmt.Sprintf("选择供应商: %s", provider), nil)

	// 获取供应商适配器
	providerAdapter, ok := s.providerManager.Get(provider)
	if !ok {
		s.recordOrderAttempt(ctx, req, "PROVIDER_ADAPTER_NOT_FOUND", fmt.Sprintf("供应商适配器不存在: %s", provider), nil)
		return nil, errors.NewBusinessError(errors.ErrCodeProviderNotFound, fmt.Sprintf("供应商 %s 不存在", provider))
	}

	// 🚀 新增：记录供应商API调用开始
	s.recordOrderAttempt(ctx, req, "PROVIDER_API_CALL_START", fmt.Sprintf("开始调用%s供应商API", provider), nil)

	// 🚀 详细记录供应商API调用
	// 🔥 关键修复：在调用供应商API之前生成平台订单号
	var platformOrderNo string
	if s.platformOrderGenerator != nil {
		generatedOrderNo, err := s.platformOrderGenerator.GeneratePlatformOrderNo(ctx)
		if err != nil {
			s.logger.Error("生成平台订单号失败", zap.Error(err))
			// 不阻断订单创建流程，继续使用空的平台订单号
		} else {
			platformOrderNo = generatedOrderNo
			s.logger.Info("平台订单号生成成功", zap.String("platform_order_no", platformOrderNo))
		}
	} else {
		s.logger.Warn("平台订单号生成器未初始化，跳过平台订单号生成")
	}

	// 🔥 关键修复：将平台订单号设置到请求中，供应商适配器将使用此订单号
	req.PlatformOrderNo = platformOrderNo

	// 🔥 关键修复：在调用供应商API前计算体积重量，确保与查价阶段一致
	originalWeight := req.Package.Weight
	chargedWeight := req.Package.CalculateChargedWeight()

	// 🔥 关键修复：使用计费重量替换原始重量
	req.Package.Weight = chargedWeight

	s.logger.Info("重量计算完成",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.Float64("original_weight", originalWeight),
		zap.Float64("volume_weight", float64(req.Package.Length*req.Package.Width*req.Package.Height)/8000.0),
		zap.Float64("charged_weight", chargedWeight),
		zap.String("express_type", req.ExpressType))

	s.logger.Info("开始调用供应商API创建订单",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("platform_order_no", req.PlatformOrderNo), // 🔥 新增：记录平台订单号
		zap.String("user_id", req.UserID),
		zap.String("provider", provider),
		zap.String("express_type", req.ExpressType),
		zap.String("sender_province", req.Sender.Province),
		zap.String("sender_city", req.Sender.City),
		zap.String("receiver_province", req.Receiver.Province),
		zap.String("receiver_city", req.Receiver.City),
		zap.Float64("weight", req.Package.Weight),
		zap.Float64("volume", req.Package.Volume))

	startTime := util.NowBeijing()
	orderResult, err := providerAdapter.CreateOrder(ctx, req)
	processingTime := time.Since(startTime)

	if err != nil {
		// 🚀 详细记录失败信息，便于问题排查
		s.logger.Error("供应商API调用失败 - 完整错误信息",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.String("provider", provider),
			zap.String("express_type", req.ExpressType),
			zap.String("channel_id", req.ChannelID),
			zap.String("product_type", req.ProductType),
			zap.Duration("processing_time", processingTime),
			zap.String("error_message", err.Error()),
			zap.String("sender_info", fmt.Sprintf("%s-%s-%s", req.Sender.Province, req.Sender.City, req.Sender.District)),
			zap.String("receiver_info", fmt.Sprintf("%s-%s-%s", req.Receiver.Province, req.Receiver.City, req.Receiver.District)),
			zap.Float64("package_weight", req.Package.Weight),
			zap.Float64("package_volume", req.Package.Volume),
			zap.Error(err))

		s.recordOrderAttemptWithResponse(ctx, req, "PROVIDER_API_CALL_FAILED", fmt.Sprintf("%s供应商API调用失败", provider), nil, err)
		// 🔥 修复：直接返回原始错误，避免包装
		return nil, err
	}

	// 🚀 详细记录成功信息
	s.logger.Info("供应商API调用成功 - 完整响应信息",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("user_id", req.UserID),
		zap.String("provider", provider),
		zap.String("express_type", req.ExpressType),
		zap.Duration("processing_time", processingTime),
		zap.String("tracking_no", orderResult.TrackingNo),
		zap.String("task_id", orderResult.TaskId),
		zap.String("order_result", fmt.Sprintf("%+v", orderResult)))

	s.recordOrderAttemptWithResponse(ctx, req, "PROVIDER_API_CALL_SUCCESS", fmt.Sprintf("%s供应商API调用成功", provider), orderResult, nil)

	// 🔥 新增：确保OrderResult包含CustomerOrderNo
	orderResult.CustomerOrderNo = req.CustomerOrderNo

	// 🚀 新增：记录数据库保存开始
	s.recordOrderAttempt(ctx, req, "DB_SAVE_START", "开始保存订单到数据库", nil)

	// 保存订单记录
	estimatedFeeFloat, _ := estimatedFee.Float64()

	// 添加调试日志
	s.logger.Info("准备保存订单记录",
		zap.String("order_no", orderResult.OrderNo),
		zap.Float64("price_decimal", estimatedFeeFloat),
		zap.String("price_string", estimatedFee.String()))

	orderRecord := &model.OrderRecord{
		PlatformOrderNo: platformOrderNo, // 🔥 设置平台订单号（已在前面生成）
		CustomerOrderNo: req.CustomerOrderNo,
		OrderNo:         orderResult.OrderNo,
		TrackingNo:      orderResult.TrackingNo,
		TaskId:          orderResult.TaskId,
		PollToken:       orderResult.PollToken,
		ExpressType:     req.ExpressType,
		ProductType:     req.ProductType,
		Provider:        provider,
		Status:          "submitted", // 🔥 修复：使用标准状态名称
		UserID:          req.UserID,
		Weight:          req.Package.Weight,
		Price:           estimatedFeeFloat, // 统一使用Price作为主要价格字段
		ActualFee:       0,                 // 初始时实际费用为0，等待回调更新
		OrderVolume:     req.Package.Volume,
		BillingStatus:   model.BillingStatusPending,
		CreatedAt:       util.NowBeijing(),
		UpdatedAt:       util.NowBeijing(),
	}

	// 设置价格验证结果
	if validationResult != nil {
		now := util.NowBeijing()
		orderRecord.PriceValidationSystemPrice = &estimatedFeeFloat
		orderRecord.PriceValidationProviderPrice = &validationResult.RealtimePrice
		orderRecord.PriceValidationQueryTime = &now

		// 设置查询状态
		queryStatus := "success"
		if validationResult.ValidationResult == "error" {
			queryStatus = "failed"
		} else if validationResult.ValidationResult == "skip" {
			queryStatus = "skipped"
		}
		orderRecord.PriceValidationQueryStatus = &queryStatus

		// 设置盈亏状态
		profitStatus := "unknown"
		if validationResult.RealtimePrice > 0 && estimatedFeeFloat > 0 {
			if estimatedFeeFloat > validationResult.RealtimePrice {
				profitStatus = "profit"
			} else if estimatedFeeFloat < validationResult.RealtimePrice {
				profitStatus = "loss"
			} else {
				profitStatus = "break_even"
			}
		}
		orderRecord.PriceValidationProfitStatus = &profitStatus

		// 设置错误信息（如果有）
		if validationResult.ErrorMessage != "" {
			orderRecord.PriceValidationErrorMessage = &validationResult.ErrorMessage
		}

		// 设置是否支持验证
		supported := true
		orderRecord.PriceValidationSupported = &supported

		s.logger.Info("价格验证结果已设置到订单记录",
			zap.String("order_no", orderResult.OrderNo),
			zap.Float64("system_price", estimatedFeeFloat),
			zap.Float64("provider_price", validationResult.RealtimePrice),
			zap.String("profit_status", profitStatus),
			zap.String("query_status", queryStatus))
	}

	// 验证订单记录中的值
	s.logger.Info("订单记录创建完成",
		zap.String("order_no", orderRecord.OrderNo),
		zap.Float64("price", orderRecord.Price),
		zap.Float64("actual_fee", orderRecord.ActualFee))

	// 🔍 调试：检查平台订单号
	if orderRecord.PlatformOrderNo != "" {
		s.logger.Info("平台订单号已设置",
			zap.String("platform_order_no", orderRecord.PlatformOrderNo),
			zap.Int("length", len(orderRecord.PlatformOrderNo)))
	} else {
		s.logger.Info("平台订单号为空")
	}

	// 序列化相关信息
	if senderInfo, err := json.Marshal(req.Sender); err == nil {
		orderRecord.SenderInfo = string(senderInfo)
	}
	if receiverInfo, err := json.Marshal(req.Receiver); err == nil {
		orderRecord.ReceiverInfo = string(receiverInfo)
	}
	if packageInfo, err := json.Marshal(req.Package); err == nil {
		orderRecord.PackageInfo = string(packageInfo)
	}
	if requestData, err := json.Marshal(req); err == nil {
		orderRecord.RequestData = string(requestData)
	}
	if responseData, err := json.Marshal(orderResult); err == nil {
		orderRecord.ResponseData = string(responseData)
	}

	// 保存订单记录
	if err := s.orderRepository.Save(ctx, orderRecord); err != nil {
		// 🚀 新增：记录数据库保存失败
		s.recordOrderAttempt(ctx, req, "DB_SAVE_FAILED", "保存订单到数据库失败", err)
		return nil, errors.WrapError(errors.ErrCodeInternal, "保存订单记录失败", err)
	}

	// 🚀 新增：记录数据库保存成功
	s.recordOrderAttempt(ctx, req, "DB_SAVE_SUCCESS", "订单保存到数据库成功", nil)

	// 🔥 新增：记录初始状态历史
	if s.statusHistoryService != nil {
		historyReq := &model.RecordStatusChangeRequest{
			OrderNo:         orderRecord.OrderNo,
			FromStatus:      "", // 初始状态，无前置状态
			ToStatus:        orderRecord.Status,
			Provider:        provider,
			RawStatus:       "order_created",
			ChangeSource:    "system",
			OperatorID:      "",
			OperatorName:    "",
			ChangeReason:    "订单创建",
			UserID:          req.UserID,
			CustomerOrderNo: req.CustomerOrderNo,
			Extra: map[string]interface{}{
				"order_creation": true,
				"provider":       provider,
				"express_type":   req.ExpressType,
				"estimated_fee":  estimatedFeeFloat,
			},
		}

		if err := s.statusHistoryService.RecordStatusChange(ctx, historyReq); err != nil {
			s.logger.Error("记录初始状态历史失败",
				zap.String("order_no", orderRecord.OrderNo),
				zap.Error(err))
			// 不返回错误，避免影响主要业务流程
		}
	}

	// 保存后验证数据
	s.logger.Info("订单记录保存成功",
		zap.String("order_no", orderRecord.OrderNo),
		zap.Int64("record_id", orderRecord.ID),
		zap.Float64("saved_price", orderRecord.Price))

	// 🔥 修复：将生成的平台订单号设置回orderResult，确保API响应包含平台订单号
	orderResult.PlatformOrderNo = platformOrderNo

	// 🚀 新增：记录订单创建最终成功
	s.recordOrderAttempt(ctx, req, "ORDER_CREATE_SUCCESS", "订单创建完全成功", nil)

	return &model.OrderResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    orderResult,
	}, nil
}

// createOrderInternal 内部创建订单方法（兼容性方法）
func (s *OrderService) createOrderInternal(ctx context.Context, req *model.OrderRequest, estimatedFee decimal.Decimal) (*model.OrderResponse, error) {
	return s.createOrderInternalWithValidation(ctx, req, estimatedFee, nil)
}

// validateOrderRequest 验证订单请求
func (s *OrderService) validateOrderRequest(req *model.OrderRequest) error {
	if req.CustomerOrderNo == "" {
		return fmt.Errorf("客户订单号不能为空")
	}
	if req.UserID == "" {
		return fmt.Errorf("用户ID不能为空")
	}
	if req.ExpressType == "" {
		return fmt.Errorf("快递类型不能为空")
	}
	if req.Sender.Name == "" || req.Sender.Mobile == "" {
		return fmt.Errorf("发件人信息不完整")
	}
	if req.Receiver.Name == "" || req.Receiver.Mobile == "" {
		return fmt.Errorf("收件人信息不完整")
	}

	// 🔥 修复：使用放宽的电话号码验证
	if err := validatePhoneNumber(req.Sender.Mobile, "寄件人电话"); err != nil {
		return err
	}
	if err := validatePhoneNumber(req.Receiver.Mobile, "收件人电话"); err != nil {
		return err
	}
	if req.Package.Weight <= 0 {
		return fmt.Errorf("包裹重量必须大于0")
	}

	// 检查配置限制
	if s.configService != nil {
		orderConfig := s.configService.GetOrderConfig()
		if decimal.NewFromFloat(req.Package.Weight).GreaterThan(orderConfig.MaxWeightKg) {
			maxWeight, _ := orderConfig.MaxWeightKg.Float64()
			return fmt.Errorf("包裹重量超过限制: %.2f kg", maxWeight)
		}
		if req.Package.Volume > 0 && decimal.NewFromFloat(req.Package.Volume).GreaterThan(orderConfig.MaxVolumeCubicM) {
			maxVolume, _ := orderConfig.MaxVolumeCubicM.Float64()
			return fmt.Errorf("包裹体积超过限制: %.3f m³", maxVolume)
		}
	}

	return nil
}

// selectProvider 选择供应商
func (s *OrderService) selectProvider(_ context.Context, req *model.OrderRequest) (string, error) {
	// 如果请求中指定了供应商，直接使用
	if req.Provider != "" {
		return req.Provider, nil
	}

	// 根据快递类型选择默认供应商
	switch req.ExpressType {
	case "SF":
		return "yida", nil
	case "YTO", "STO", "ZTO", "YUNDA", "JD":
		return "kuaidi100", nil
	default:
		return "yuntong", nil
	}
}

// CreateOrder 创建订单（强制扣费版本）
func (s *OrderService) CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResponse, error) {
	// 检查是否配置了余额服务
	if s.balanceService == nil {
		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "系统配置错误：余额服务未配置，无法处理订单",
		}, nil
	}

	// 强制要求用户ID
	if req.UserID == "" {
		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "用户ID不能为空，所有订单必须关联用户账户",
		}, nil
	}

	// 验证请求参数
	if err := s.validateOrderRequest(req); err != nil {
		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
		}, nil
	}

	// 强制使用预扣费流程
	return s.CreateOrderWithPreCharge(ctx, req)
}

// CreateOrderWithLockedPrice 使用锁定价格创建订单
func (s *OrderService) CreateOrderWithLockedPrice(ctx context.Context, req *model.OrderRequest, lockedPrice float64) (*model.OrderResponse, error) {
	// 强制检查必要服务
	if s.configService == nil || s.balanceService == nil || s.transactionManager == nil {
		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "系统配置错误：扣费服务未配置，无法处理订单",
		}, nil
	}

	// 强制要求用户ID
	if req.UserID == "" {
		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "用户ID不能为空，所有订单必须关联用户账户",
		}, nil
	}

	// 获取配置（强制启用预扣费）
	orderConfig := s.configService.GetOrderConfig()
	if orderConfig == nil {
		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: "系统配置错误：订单配置未找到",
		}, nil
	}

	// 使用锁定价格作为预估费用
	estimatedFee := decimal.NewFromFloat(lockedPrice)

	// 在事务中执行预扣费和订单创建
	var orderResponse *model.OrderResponse
	err := s.transactionManager.ExecuteInTransactionWithOptions(ctx, &TransactionOptions{
		Timeout: orderConfig.CreateTimeout,
	}, func(txCtx context.Context) error {
		// 1. 验证请求参数
		if err := s.validateOrderRequest(req); err != nil {
			return errors.WrapError(errors.ErrCodeInvalidRequest, "订单参数验证失败", err)
		}

		// 2. 🔥 修复：先创建订单（使用确定的供应商）
		response, err := s.createOrderInternal(txCtx, req, estimatedFee)
		if err != nil {
			return err
		}

		// 3. 🔥 修复：订单创建成功后再扣费（使用锁定价格）
		// 🚀 性能优化：使用新的PreChargeForOrderWithDetails方法
		platformOrderNo := ""
		if response.Data != nil {
			platformOrderNo = response.Data.PlatformOrderNo
		}

		if err := s.balanceService.PreChargeForOrderWithDetails(txCtx, req.UserID, req.CustomerOrderNo, platformOrderNo, estimatedFee); err != nil {
			return errors.WrapError(errors.ErrCodeInsufficientBalance, "订单创建成功但扣费失败", err)
		}

		orderResponse = response

		// 4. 记录审计日志
		if s.auditService != nil {
			s.auditService.LogOrderOperation(txCtx, response.Data.OrderNo, "create_with_locked_price", req.UserID, map[string]any{
				"locked_price":      lockedPrice,
				"estimated_fee":     estimatedFee,
				"customer_order_no": req.CustomerOrderNo,
				"provider":          req.Provider,
				"express_type":      req.ExpressType,
			})
		}

		return nil
	})

	if err != nil {
		s.logger.Error("使用锁定价格创建订单失败",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.Float64("locked_price", lockedPrice),
			zap.Error(err))

		// 返回业务错误
		if businessErr, ok := err.(errors.BusinessError); ok {
			return &model.OrderResponse{
				Success: false,
				Code:    businessErr.HTTPStatus(),
				Message: businessErr.Message(),
			}, nil
		}

		return &model.OrderResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: err.Error(),
		}, nil
	}

	s.logger.Info("使用锁定价格创建订单成功",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("order_no", orderResponse.Data.OrderNo),
		zap.Float64("locked_price", lockedPrice))

	return orderResponse, nil
}

// CancelOrder 取消订单 - 重构为两阶段流程
func (s *OrderService) CancelOrder(ctx context.Context, req *model.CancelOrderRequest) (*model.CancelOrderResponse, error) {
	// 创建生产级身份解析服务
	identityResolver := NewUserIdentityResolver(nil, s.logger) // 暂时传nil，后续可以注入用户仓库

	// 使用新的取消服务处理取消逻辑
	cancellationService := NewOrderCancellationService(
		s.orderRepository,
		s.balanceService,
		s.transactionManager,
		s.auditService,
		s.providerManager,
		identityResolver,
		s.logger,
	)

	// 转换请求格式
	cancellationReq := &CancellationRequest{
		OrderNo:         req.OrderNo,
		TrackingNo:      req.TrackingNo,
		PlatformOrderNo: req.PlatformOrderNo, // 🔥 新增：支持明确指定平台订单号
		UserID:          req.UserID,
		Reason:          req.Reason,
	}

	// 发起取消请求
	resp, err := cancellationService.InitiateCancellation(ctx, cancellationReq)
	if err != nil {
		return &model.CancelOrderResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("发起取消失败: %s", err.Error()),
		}, nil
	}

	// 转换响应格式
	return &model.CancelOrderResponse{
		Success: resp.Success,
		Code:    resp.Code,
		Message: resp.Message,
	}, nil
}

// DeleteFailedOrder 删除失败订单
func (s *OrderService) DeleteFailedOrder(ctx context.Context, orderNo, userID string) error {
	// 参数验证
	if orderNo == "" {
		return fmt.Errorf("订单号不能为空")
	}
	if userID == "" {
		return fmt.Errorf("用户ID不能为空")
	}

	// 查询订单详情验证权限和状态
	order, err := s.orderRepository.FindByOrderNo(ctx, orderNo)
	if err != nil {
		s.logger.Error("查询订单失败",
			zap.String("order_no", orderNo),
			zap.String("user_id", userID),
			zap.Error(err))
		return fmt.Errorf("查询订单失败: %w", err)
	}

	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	// 验证用户权限
	if order.UserID != userID {
		return fmt.Errorf("无权限访问该订单")
	}

	// 验证只能删除失败状态的订单
	if order.Status != model.OrderStatusFailed {
		return fmt.Errorf("只能删除失败状态的订单，当前状态: %s", order.Status)
	}

	// 删除失败订单记录
	err = s.orderRepository.DeleteFailedOrder(ctx, orderNo, userID)
	if err != nil {
		s.logger.Error("删除失败订单记录失败",
			zap.String("order_no", orderNo),
			zap.String("user_id", userID),
			zap.Error(err))
		return fmt.Errorf("删除失败订单记录失败: %w", err)
	}

	s.logger.Info("成功删除失败订单",
		zap.String("order_no", orderNo),
		zap.String("user_id", userID),
		zap.String("customer_order_no", order.CustomerOrderNo))

	return nil
}

// QueryOrder 查询订单
func (s *OrderService) QueryOrder(ctx context.Context, req *model.QueryOrderRequest) (*model.QueryOrderResponse, error) {
	// 验证请求参数
	if req.OrderNo == "" && req.TrackingNo == "" {
		return &model.QueryOrderResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "订单号和运单号不能同时为空",
		}, nil
	}

	// 🔥 使用高并发安全的智能订单查找服务
	smartFinder := NewSmartOrderFinder(s.orderRepository, s.logger)
	orderRecord, err := smartFinder.FindOrderByOrderNoOrTrackingNo(ctx, req.OrderNo, req.TrackingNo, req.UserID)
	if err != nil {
		return &model.QueryOrderResponse{
			Success: false,
			Code:    model.StatusNotFound,
			Message: fmt.Sprintf("订单不存在: %s", err.Error()),
		}, nil
	}

	// 验证用户权限：确保用户只能查看自己的订单
	if req.UserID != "" && orderRecord.UserID != req.UserID {
		return &model.QueryOrderResponse{
			Success: false,
			Code:    model.StatusForbidden,
			Message: "无权限查看此订单",
		}, nil
	}

	// 构建用户订单详情（系统内部信息）
	// 获取状态描述
	statusDesc := orderRecord.Status
	if orderRecord.Status == "pending" {
		statusDesc = "待处理"
	} else if orderRecord.Status == "processing" {
		statusDesc = "处理中"
	} else if orderRecord.Status == "in_transit" {
		statusDesc = "运输中"
	} else if orderRecord.Status == "delivered" {
		statusDesc = "已送达"
	} else if orderRecord.Status == "cancelled" {
		statusDesc = "已取消"
	}

	// 获取快递公司名称
	expressName := orderRecord.ExpressType
	expressNameMap := map[string]string{
		"SF":   "顺丰速运",
		"ZTO":  "中通快递",
		"YTO":  "圆通速递",
		"STO":  "申通快递",
		"YD":   "韵达速递",
		"JD":   "京东快递",
		"EMS":  "中国邮政",
		"HTKY": "百世快递",
	}
	if name, exists := expressNameMap[orderRecord.ExpressType]; exists {
		expressName = name
	}

	// 获取供应商名称
	providerName := orderRecord.Provider
	providerNameMap := map[string]string{
		"kuaidi100": "快递100",
		"yida":      "易达",
		"yuntong":   "云通",
	}
	if name, exists := providerNameMap[orderRecord.Provider]; exists {
		providerName = name
	}

	userOrderDetail := &model.UserOrderDetail{
		// 基本信息
		OrderNo:         orderRecord.OrderNo,
		CustomerOrderNo: orderRecord.CustomerOrderNo,
		TrackingNo:      orderRecord.TrackingNo,
		ExpressType:     orderRecord.ExpressType,
		ExpressName:     expressName,
		ProductType:     orderRecord.ProductType,
		Provider:        orderRecord.Provider,
		ProviderName:    providerName,

		// 状态信息
		Status:     orderRecord.Status,
		StatusDesc: statusDesc,
		// 🔥 修复：处理 timestamp without time zone 的时区问题
		CreatedAt: util.EnsureBeijingTimezone(orderRecord.CreatedAt),
		UpdatedAt: util.EnsureBeijingTimezone(orderRecord.UpdatedAt),

		// 费用信息
		Price:         orderRecord.Price,
		ActualFee:     orderRecord.ActualFee,
		InsuranceFee:  orderRecord.InsuranceFee,
		BillingStatus: orderRecord.BillingStatus,

		// 重量体积信息
		Weight:        orderRecord.Weight,        // 下单重量
		OrderVolume:   orderRecord.OrderVolume,   // 下单体积
		ActualWeight:  orderRecord.ActualWeight,  // 实际重量
		ActualVolume:  orderRecord.ActualVolume,  // 实际体积
		ChargedWeight: orderRecord.ChargedWeight, // 计费重量

		// 地址信息
		SenderInfo:   orderRecord.SenderInfo,
		ReceiverInfo: orderRecord.ReceiverInfo,
		PackageInfo:  orderRecord.PackageInfo,

		// 系统信息
		TaskId:    orderRecord.TaskId,
		PollToken: orderRecord.PollToken,
		UserID:    orderRecord.UserID,

		// 🔥 修复：添加失败订单相关字段
		FailureReason:  orderRecord.FailureReason,
		FailureMessage: orderRecord.FailureMessage,
		FailureStage:   orderRecord.FailureStage,
		FailureTime:    orderRecord.FailureTime,
		CanRetry:       orderRecord.CanRetry,
	}

	s.logger.Info("查询系统订单详情",
		zap.String("order_no", orderRecord.OrderNo),
		zap.String("user_id", orderRecord.UserID),
		zap.String("status", orderRecord.Status))

	return &model.QueryOrderResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "查询成功",
		Data:    userOrderDetail,
	}, nil
}

// GetOrderList 获取订单列表
func (s *OrderService) GetOrderList(ctx context.Context, req *model.OrderListRequest) (*model.OrderListResponse, error) {
	// 验证请求参数
	if err := model.ValidateOrderListRequest(req); err != nil {
		return &model.OrderListResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: err.Error(),
		}, nil
	}

	// 🔥 修复：确保失败订单也能显示在列表中
	// 如果没有指定状态过滤，则包含所有状态（包括失败订单）
	s.logger.Debug("订单列表查询参数",
		zap.String("user_id", req.UserID),
		zap.String("status", req.Status),
		zap.Int("page", req.Page),
		zap.Int("page_size", req.PageSize),
		zap.Bool("batch_mode", req.BatchMode),
		zap.Int("tracking_nos_count", len(req.TrackingNos)))

	// 🔥 新增：批量查询性能监控
	startTime := time.Now()

	// 查询订单列表
	items, total, err := s.orderRepository.ListWithFilter(ctx, req)
	if err != nil {
		s.logger.Error("查询订单列表失败",
			zap.String("user_id", req.UserID),
			zap.Bool("batch_mode", req.BatchMode),
			zap.Error(err))
		return &model.OrderListResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("查询订单列表失败: %s", err.Error()),
		}, nil
	}

	// 🔥 新增：计算查询耗时
	queryDuration := time.Since(startTime)

	// 🔥 修复：记录查询结果，便于调试失败订单显示问题
	s.logger.Debug("订单列表查询结果",
		zap.String("user_id", req.UserID),
		zap.Int64("total", total),
		zap.Int("items_count", len(items)),
		zap.Duration("query_duration", queryDuration))

	// 计算分页信息
	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	hasNext := req.Page < totalPages
	hasPrev := req.Page > 1

	// 🔥 新增：构建响应数据
	responseData := &model.OrderListData{
		Items:      items,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	}

	// 🔥 新增：批量查询统计信息
	if req.BatchMode && len(req.TrackingNos) > 0 {
		batchStats := s.calculateBatchQueryStats(req.TrackingNos, items, queryDuration)
		responseData.BatchStats = batchStats

		// 记录批量查询统计日志
		s.logger.Info("批量查询统计",
			zap.String("user_id", req.UserID),
			zap.Int("total_queried", batchStats.TotalQueried),
			zap.Int("found_count", batchStats.FoundCount),
			zap.Int("not_found_count", len(batchStats.NotFound)),
			zap.String("query_time", batchStats.QueryTime))
	}

	return &model.OrderListResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    responseData,
	}, nil
}

// GetOrderStatistics 获取订单统计信息
func (s *OrderService) GetOrderStatistics(ctx context.Context, userID string) (*model.OrderStatisticsResponse, error) {
	stats, err := s.orderRepository.GetOrderStatistics(ctx, userID)
	if err != nil {
		return &model.OrderStatisticsResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("获取订单统计失败: %s", err.Error()),
		}, nil
	}

	return &model.OrderStatisticsResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    stats,
	}, nil
}

// QueryTrack 查询物流轨迹
func (s *OrderService) QueryTrack(ctx context.Context, req *model.TrackQueryRequest) (*model.TrackQueryResponse, error) {
	// 验证请求参数
	if req.TrackingNo == "" {
		return &model.TrackQueryResponse{
			Success: false,
			Code:    model.StatusBadRequest,
			Message: "运单号不能为空",
		}, nil
	}

	// 🔥 使用高并发安全的智能订单查找服务
	smartFinder := NewSmartOrderFinder(s.orderRepository, s.logger)
	orderRecord, err := smartFinder.FindOrderByOrderNoOrTrackingNo(ctx, req.OrderNo, req.TrackingNo, "")
	if err != nil {
		return &model.TrackQueryResponse{
			Success: false,
			Code:    model.StatusNotFound,
			Message: fmt.Sprintf("订单不存在: %s", err.Error()),
		}, nil
	}

	// 🚀 获取供应商适配器用于物流跟踪（不检查启用状态）
	// 物流跟踪功能不应受供应商启用/禁用开关影响，用户有权查询已下订单的物流状态
	providerAdapter, ok := s.providerManager.GetForTracking(orderRecord.Provider)
	if !ok {
		return &model.TrackQueryResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("供应商 %s 适配器不存在", orderRecord.Provider),
		}, nil
	}

	// 🔥 菜鸟供应商特殊处理：需要使用菜鸟订单ID查询物流轨迹
	var trackInfo *model.TrackInfo

	if orderRecord.Provider == "cainiao" {
		// 菜鸟供应商：使用菜鸟订单ID查询物流轨迹
		if cainiaoAdapter, ok := providerAdapter.(*adapter.CainiaoAdapter); ok {
			// 从订单记录中获取菜鸟订单ID（存储在TaskId字段中）
			cainiaoOrderId := orderRecord.TaskId
			if cainiaoOrderId == "" {
				return &model.TrackQueryResponse{
					Success: false,
					Code:    model.StatusInternalServerError,
					Message: "菜鸟订单ID不存在，无法查询物流轨迹",
				}, nil
			}

			s.logger.Info("菜鸟物流轨迹查询",
				zap.String("tracking_no", orderRecord.TrackingNo),
				zap.String("cainiao_order_id", cainiaoOrderId),
				zap.String("order_no", orderRecord.OrderNo))

			trackInfo, err = cainiaoAdapter.QueryTrackByOrderId(ctx, cainiaoOrderId, orderRecord.TrackingNo)
		} else {
			return &model.TrackQueryResponse{
				Success: false,
				Code:    model.StatusInternalServerError,
				Message: "菜鸟适配器类型转换失败",
			}, nil
		}
	} else {
		// 其他供应商：使用标准接口
		// 使用数据库中保存的pollToken
		pollToken := orderRecord.PollToken
		// 如果请求中提供了pollToken，则使用请求中的pollToken
		if req.PollToken != "" {
			pollToken = req.PollToken
		}
		trackInfo, err = providerAdapter.QueryTrack(ctx, orderRecord.TrackingNo, orderRecord.ExpressType, req.Phone, pollToken, req.From, req.To)
	}

	if err != nil {
		return &model.TrackQueryResponse{
			Success: false,
			Code:    model.StatusInternalServerError,
			Message: fmt.Sprintf("查询物流轨迹失败: %s", err.Error()),
		}, nil
	}

	// 设置订单号
	trackInfo.OrderNo = orderRecord.OrderNo

	return &model.TrackQueryResponse{
		Success: true,
		Code:    model.StatusSuccess,
		Message: "成功",
		Data:    trackInfo,
	}, nil
}

// GenerateCustomerOrderNo 生成客户订单号
func (s *OrderService) GenerateCustomerOrderNo(ctx context.Context, provider string) (string, error) {
	return s.orderRepository.GenerateCustomerOrderNo(ctx, provider)
}

// 🔥 修复：FindByCustomerOrderNoAndUser 根据客户订单号和用户ID查询订单 - 集成智能查询服务
func (s *OrderService) FindByCustomerOrderNoAndUser(ctx context.Context, customerOrderNo, userID string) (*model.OrderRecord, error) {
	// 🚀 使用智能查询服务进行高效查询
	smartFinder := NewSmartOrderFinder(s.orderRepository, s.logger)
	order, err := smartFinder.FindOrderByAnyIdentifier(ctx, customerOrderNo, userID)
	if err != nil {
		s.logger.Debug("智能查询服务查找订单失败",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("user_id", userID),
			zap.Error(err))
		return nil, fmt.Errorf("订单不存在或无权访问: %w", err)
	}

	// 🔒 双重验证：确保用户ID匹配（智能查询服务已做过验证，这里是额外保护）
	if order.UserID != userID {
		s.logger.Warn("订单用户ID不匹配",
			zap.String("customer_order_no", customerOrderNo),
			zap.String("expected_user_id", userID),
			zap.String("actual_user_id", order.UserID))
		return nil, fmt.Errorf("订单不属于当前用户")
	}

	s.logger.Debug("✅ 智能查询服务成功找到用户订单",
		zap.String("customer_order_no", customerOrderNo),
		zap.String("user_id", userID),
		zap.String("order_no", order.OrderNo))

	return order, nil
}

// isExpressCodeMatch 检查快递代码是否匹配（支持多种格式）
func (s *OrderService) isExpressCodeMatch(code1, code2 string) bool {
	// 统一格式化函数，支持去除渠道后缀（如 "INT" 等）并转换为大写
	normalize := func(code string) string {
		code = strings.ToUpper(strings.TrimSpace(code))
		// 去掉减号或下划线后面的渠道标识，保留主代码
		if idx := strings.IndexAny(code, "-_"); idx != -1 {
			code = code[:idx]
		}
		return code
	}

	// 先做主代码比较，快速路径
	n1, n2 := normalize(code1), normalize(code2)
	if n1 == n2 {
		return true
	}

	// 快递代码标准化映射（键与值均需提前 Normalize）
	expressCodeMap := map[string][]string{
		"STO":  {"STO", "SHENTONG", "申通"},
		"ZTO":  {"ZTO", "ZHONGTONG", "中通"},
		"YTO":  {"YTO", "YUANTONG", "圆通"},
		"YD":   {"YD", "YUND", "YUNDA", "韵达", "YUNDA_EXPRESS"},
		"SF":   {"SF", "SHUNFENG", "顺丰"},
		"JD":   {"JD", "JINGDONG", "京东"},
		"EMS":  {"EMS", "EMS_CN", "邮政"},
		"JT":   {"JT", "JTSD", "JITU", "极兔"}, // 🔥 修复：添加JTSD映射，解决快递鸟极兔代码匹配问题
		"DBL":  {"DBL", "DEBANG", "德邦"},
		"HTKY": {"HTKY", "BAISHI", "百世"},
	}

	// 再做别名比较（同属一个主代码即视为匹配）
	for _, codes := range expressCodeMap {
		found1, found2 := false, false
		for _, c := range codes {
			cNorm := normalize(c)
			if cNorm == n1 {
				found1 = true
			}
			if cNorm == n2 {
				found2 = true
			}
		}
		if found1 && found2 {
			return true
		}
	}

	return false
}

// 🚀 新增：recordOrderAttempt 记录下单尝试
func (s *OrderService) recordOrderAttempt(ctx context.Context, req *model.OrderRequest, stage string, description string, err error) {
	s.recordOrderAttemptWithResponse(ctx, req, stage, description, nil, err)
}

// 🚀 新增：recordOrderAttemptWithResponse 记录带响应数据的下单尝试
func (s *OrderService) recordOrderAttemptWithResponse(ctx context.Context, req *model.OrderRequest, stage string, description string, responseData interface{}, err error) {
	if s.orderAttemptRepo == nil {
		// 如果没有配置下单尝试记录仓库，只记录日志
		if err != nil {
			s.logger.Error("下单尝试失败",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("stage", stage),
				zap.String("description", description),
				zap.Any("response_data", responseData),
				zap.Error(err))
		} else {
			s.logger.Info("下单尝试",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("stage", stage),
				zap.String("description", description),
				zap.Any("response_data", responseData))
		}
		return
	}

	// 创建下单尝试记录
	attemptReq := &model.OrderAttemptRequest{
		CustomerOrderNo:  req.CustomerOrderNo,
		UserID:           req.UserID,
		Provider:         req.Provider,
		ExpressType:      req.ExpressType,
		AttemptStage:     stage,
		StageDescription: description,
		Success:          err == nil,
		RequestData:      req,
		ResponseData:     responseData, // 🚀 新增：记录响应数据
	}

	if err != nil {
		attemptReq.ErrorMessage = err.Error()
		if businessErr, ok := err.(errors.BusinessError); ok {
			attemptReq.ErrorCode = string(businessErr.Code())
		}
	}

	// 创建下单尝试记录
	attempt, createErr := repository.CreateOrderAttemptFromRequest(attemptReq)
	if createErr != nil {
		s.logger.Error("创建下单尝试记录对象失败",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Error(createErr))
		return
	}

	// 保存到数据库（异步，不影响主流程）
	go func() {
		if saveErr := s.orderAttemptRepo.Create(context.Background(), attempt); saveErr != nil {
			s.logger.Error("保存下单尝试记录失败",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("stage", stage),
				zap.Error(saveErr))
		}
	}()
}

// RecordOrderAttempt 公开的记录下单尝试方法（供外部调用）
func (s *OrderService) RecordOrderAttempt(ctx context.Context, attemptReq *model.OrderAttemptRequest) error {
	if s.orderAttemptRepo == nil {
		// 如果没有配置下单尝试记录仓库，只记录日志
		if !attemptReq.Success {
			s.logger.Error("下单尝试失败",
				zap.String("customer_order_no", attemptReq.CustomerOrderNo),
				zap.String("stage", attemptReq.AttemptStage),
				zap.String("description", attemptReq.StageDescription),
				zap.String("error_message", attemptReq.ErrorMessage))
		} else {
			s.logger.Info("下单尝试",
				zap.String("customer_order_no", attemptReq.CustomerOrderNo),
				zap.String("stage", attemptReq.AttemptStage),
				zap.String("description", attemptReq.StageDescription))
		}
		return nil
	}

	// 创建下单尝试记录
	attempt, createErr := repository.CreateOrderAttemptFromRequest(attemptReq)
	if createErr != nil {
		s.logger.Error("创建下单尝试记录对象失败",
			zap.String("customer_order_no", attemptReq.CustomerOrderNo),
			zap.Error(createErr))
		return createErr
	}

	// 保存到数据库
	if saveErr := s.orderAttemptRepo.Create(ctx, attempt); saveErr != nil {
		s.logger.Error("保存下单尝试记录失败",
			zap.String("customer_order_no", attemptReq.CustomerOrderNo),
			zap.String("stage", attemptReq.AttemptStage),
			zap.Error(saveErr))
		return saveErr
	}

	return nil
}

// validatePhoneNumber 验证电话号码格式（支持手机号和固定电话）
func validatePhoneNumber(phone, fieldName string) error {
	if phone == "" {
		return fmt.Errorf("%s不能为空", fieldName)
	}

	// 🔥 修复：使用与input_validator一致的宽松验证规则
	// 支持手机号、固定电话、400电话等多种格式
	matched, _ := regexp.MatchString(`^[\d\s\-\+\(\)]{7,20}$`, phone)
	if !matched {
		return fmt.Errorf("%s格式不正确（支持手机号和固定电话）: %s", fieldName, phone)
	}
	return nil
}

// extractFailureReason 从错误中提取失败原因（返回标准常量）
func (s *OrderService) extractFailureReason(err error) string {
	if err == nil {
		return model.FailureReasonUnknown
	}

	errStr := err.Error()

	// 🔥 修复：根据常见错误模式提取失败原因，返回标准常量
	if strings.Contains(errStr, "地址超区") || strings.Contains(errStr, "暂不支持寄件") {
		return model.FailureReasonProvider // 供应商错误
	}
	if strings.Contains(errStr, "余额不足") || strings.Contains(errStr, "insufficient balance") {
		return model.FailureReasonBalance // 余额不足
	}
	if strings.Contains(errStr, "价格变化") || strings.Contains(errStr, "price changed") || strings.Contains(errStr, "价格验证") {
		return model.FailureReasonPrice // 价格验证失败
	}
	if strings.Contains(errStr, "网络") || strings.Contains(errStr, "timeout") || strings.Contains(errStr, "connection") {
		return model.FailureReasonNetwork // 网络错误
	}
	if strings.Contains(errStr, "供应商") || strings.Contains(errStr, "provider") {
		return model.FailureReasonProvider // 供应商错误
	}
	if strings.Contains(errStr, "参数") || strings.Contains(errStr, "invalid") || strings.Contains(errStr, "验证") {
		return model.FailureReasonValidation // 参数验证失败
	}
	if strings.Contains(errStr, "超时") || strings.Contains(errStr, "timeout") {
		return model.FailureReasonTimeout // 超时错误
	}

	return model.FailureReasonSystem // 系统错误
}

// extractFailureStage 从错误中提取失败阶段
func (s *OrderService) extractFailureStage(err error) string {
	if err == nil {
		return "未知阶段"
	}

	errStr := err.Error()

	// 根据错误内容判断失败阶段
	if strings.Contains(errStr, "价格") || strings.Contains(errStr, "price") {
		return "价格计算"
	}
	if strings.Contains(errStr, "余额") || strings.Contains(errStr, "balance") {
		return "余额扣费"
	}
	if strings.Contains(errStr, "供应商") || strings.Contains(errStr, "provider") || strings.Contains(errStr, "API") {
		return "供应商下单"
	}
	if strings.Contains(errStr, "参数") || strings.Contains(errStr, "验证") || strings.Contains(errStr, "validate") {
		return "参数验证"
	}
	if strings.Contains(errStr, "数据库") || strings.Contains(errStr, "database") {
		return "数据保存"
	}

	return "订单创建"
}

// extractFailureSource 从错误中提取失败来源（provider-供应商, system-系统）
func (s *OrderService) extractFailureSource(err error) string {
	if err == nil {
		return "system"
	}

	errStr := err.Error()

	// 供应商相关错误
	providerErrors := []string{
		"地址超区",
		"暂不支持寄件",
		"供应商",
		"provider",
		"网络错误",
		"timeout",
		"connection",
		"超时",
		"服务暂不可用",
		"接口异常",
		"第三方",
		"API调用失败",
	}

	for _, pattern := range providerErrors {
		if strings.Contains(errStr, pattern) {
			return "provider"
		}
	}

	// 系统相关错误
	systemErrors := []string{
		"余额不足",
		"参数验证",
		"价格验证",
		"系统错误",
		"数据库",
		"配置错误",
		"internal",
		"system",
		"验证失败",
		"invalid",
	}

	for _, pattern := range systemErrors {
		if strings.Contains(errStr, pattern) {
			return "system"
		}
	}

	// 默认认为是系统错误
	return "system"
}

// canRetryError 判断错误是否可重试
func (s *OrderService) canRetryError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// 不可重试的错误类型
	nonRetryableErrors := []string{
		"地址超区",
		"暂不支持寄件",
		"余额不足",
		"参数错误",
		"invalid",
		"验证失败",
	}

	for _, nonRetryable := range nonRetryableErrors {
		if strings.Contains(errStr, nonRetryable) {
			return false
		}
	}

	// 可重试的错误类型（网络错误、临时系统错误等）
	retryableErrors := []string{
		"网络",
		"timeout",
		"connection",
		"系统繁忙",
		"服务器错误",
		"internal server error",
	}

	for _, retryable := range retryableErrors {
		if strings.Contains(errStr, retryable) {
			return true
		}
	}

	// 默认不可重试
	return false
}

// 🔥 新增：计算批量查询统计信息
func (s *OrderService) calculateBatchQueryStats(trackingNos []string, items []*model.OrderListItem, queryDuration time.Duration) *model.BatchQueryStats {
	// 创建已找到运单号的映射
	foundTrackingNos := make(map[string]bool)
	for _, item := range items {
		if item.TrackingNo != "" {
			foundTrackingNos[item.TrackingNo] = true
		}
	}

	// 计算未找到的运单号
	var notFound []string
	for _, trackingNo := range trackingNos {
		if !foundTrackingNos[trackingNo] {
			notFound = append(notFound, trackingNo)
		}
	}

	// 格式化查询耗时（毫秒）
	queryTimeMs := fmt.Sprintf("%.2fms", float64(queryDuration.Nanoseconds())/1000000)

	return &model.BatchQueryStats{
		TotalQueried: len(trackingNos),
		FoundCount:   len(items),
		NotFound:     notFound,
		QueryTime:    queryTimeMs,
	}
}
