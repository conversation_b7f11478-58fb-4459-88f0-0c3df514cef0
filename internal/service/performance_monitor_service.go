package service

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// PerformanceMonitorService 性能监控服务接口
type PerformanceMonitorService interface {
	// 记录性能指标
	RecordMetric(ctx context.Context, metric *PerformanceMetric) error
	// 获取性能统计
	GetPerformanceStats(ctx context.Context, req *PerformanceStatsRequest) (*PerformanceStatsResponse, error)
	// 获取慢查询统计
	GetSlowQueryStats(ctx context.Context, duration time.Duration) ([]*SlowQueryStat, error)
	// 检测性能异常
	DetectPerformanceAnomalies(ctx context.Context, threshold time.Duration) ([]*PerformanceAnomaly, error)
}

// PerformanceMetric 性能指标
type PerformanceMetric struct {
	ID             string                 `json:"id"`
	MetricName     string                 `json:"metric_name"`
	MetricValue    decimal.Decimal        `json:"metric_value"`
	MetricUnit     string                 `json:"metric_unit"` // ms, count, bytes, etc.
	OperationType  string                 `json:"operation_type,omitempty"`
	AdminID        string                 `json:"admin_id,omitempty"`
	AdditionalData map[string]interface{} `json:"additional_data,omitempty"`
	CreatedAt      time.Time              `json:"created_at"`
}

// PerformanceStatsRequest 性能统计请求
type PerformanceStatsRequest struct {
	MetricName    string    `json:"metric_name,omitempty"`
	OperationType string    `json:"operation_type,omitempty"`
	AdminID       string    `json:"admin_id,omitempty"`
	StartTime     time.Time `json:"start_time"`
	EndTime       time.Time `json:"end_time"`
}

// PerformanceStatsResponse 性能统计响应
type PerformanceStatsResponse struct {
	MetricName    string                     `json:"metric_name"`
	TotalCount    int64                      `json:"total_count"`
	AverageValue  decimal.Decimal            `json:"average_value"`
	MinValue      decimal.Decimal            `json:"min_value"`
	MaxValue      decimal.Decimal            `json:"max_value"`
	MedianValue   decimal.Decimal            `json:"median_value"`
	P95Value      decimal.Decimal            `json:"p95_value"`
	P99Value      decimal.Decimal            `json:"p99_value"`
	ValuesByType  map[string]decimal.Decimal `json:"values_by_type"`
	ValuesByAdmin map[string]decimal.Decimal `json:"values_by_admin"`
	TrendData     []*TrendPoint              `json:"trend_data"`
}

// TrendPoint 趋势数据点
type TrendPoint struct {
	Timestamp time.Time       `json:"timestamp"`
	Value     decimal.Decimal `json:"value"`
	Count     int64           `json:"count"`
}

// SlowQueryStat 慢查询统计
type SlowQueryStat struct {
	OperationType  string          `json:"operation_type"`
	Count          int64           `json:"count"`
	AverageTime    decimal.Decimal `json:"average_time"`
	MaxTime        decimal.Decimal `json:"max_time"`
	TotalTime      decimal.Decimal `json:"total_time"`
	LastOccurrence time.Time       `json:"last_occurrence"`
}

// PerformanceAnomaly 性能异常
type PerformanceAnomaly struct {
	OperationType  string          `json:"operation_type"`
	AdminID        string          `json:"admin_id"`
	MetricName     string          `json:"metric_name"`
	CurrentValue   decimal.Decimal `json:"current_value"`
	ThresholdValue decimal.Decimal `json:"threshold_value"`
	Severity       string          `json:"severity"` // low, medium, high, critical
	Description    string          `json:"description"`
	DetectedAt     time.Time       `json:"detected_at"`
}

// DefaultPerformanceMonitorService 默认性能监控服务实现
type DefaultPerformanceMonitorService struct {
	db            *sql.DB
	configService SystemConfigService
	logger        *zap.Logger
}

// NewPerformanceMonitorService 创建性能监控服务
func NewPerformanceMonitorService(
	db *sql.DB,
	configService SystemConfigService,
	logger *zap.Logger,
) PerformanceMonitorService {
	return &DefaultPerformanceMonitorService{
		db:            db,
		configService: configService,
		logger:        logger,
	}
}

// RecordMetric 记录性能指标
func (s *DefaultPerformanceMonitorService) RecordMetric(ctx context.Context, metric *PerformanceMetric) error {
	// 检查是否启用性能监控
	metricsEnabled := s.configService.GetConfigAsBoolWithDefault("admin_balance.metrics_enabled", true)
	if !metricsEnabled {
		return nil
	}

	// 序列化附加数据
	additionalDataJSON, err := json.Marshal(metric.AdditionalData)
	if err != nil {
		s.logger.Error("序列化附加数据失败", zap.Error(err))
		additionalDataJSON = []byte("{}")
	}

	query := `
		INSERT INTO admin_balance_performance_metrics (
			id, metric_name, metric_value, metric_unit, operation_type,
			admin_id, additional_data, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	_, err = s.db.ExecContext(ctx, query,
		metric.ID, metric.MetricName, metric.MetricValue, metric.MetricUnit,
		metric.OperationType, metric.AdminID, additionalDataJSON, metric.CreatedAt,
	)

	if err != nil {
		s.logger.Error("记录性能指标失败",
			zap.String("metric_name", metric.MetricName),
			zap.String("operation_type", metric.OperationType),
			zap.Error(err))
		return fmt.Errorf("记录性能指标失败: %w", err)
	}

	s.logger.Debug("记录性能指标成功",
		zap.String("metric_name", metric.MetricName),
		zap.String("metric_value", metric.MetricValue.String()),
		zap.String("operation_type", metric.OperationType))

	return nil
}

// GetPerformanceStats 获取性能统计
func (s *DefaultPerformanceMonitorService) GetPerformanceStats(ctx context.Context, req *PerformanceStatsRequest) (*PerformanceStatsResponse, error) {
	// 构建查询条件
	whereClause := "WHERE created_at >= $1 AND created_at <= $2"
	args := []interface{}{req.StartTime, req.EndTime}
	argIndex := 3

	if req.MetricName != "" {
		whereClause += fmt.Sprintf(" AND metric_name = $%d", argIndex)
		args = append(args, req.MetricName)
		argIndex++
	}

	if req.OperationType != "" {
		whereClause += fmt.Sprintf(" AND operation_type = $%d", argIndex)
		args = append(args, req.OperationType)
		argIndex++
	}

	if req.AdminID != "" {
		whereClause += fmt.Sprintf(" AND admin_id = $%d", argIndex)
		args = append(args, req.AdminID)
		argIndex++
	}

	// 查询基本统计信息
	statsQuery := fmt.Sprintf(`
		SELECT 
			COUNT(*) as total_count,
			AVG(metric_value) as avg_value,
			MIN(metric_value) as min_value,
			MAX(metric_value) as max_value,
			PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY metric_value) as median_value,
			PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY metric_value) as p95_value,
			PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY metric_value) as p99_value
		FROM admin_balance_performance_metrics %s
	`, whereClause)

	var stats PerformanceStatsResponse
	stats.MetricName = req.MetricName
	stats.ValuesByType = make(map[string]decimal.Decimal)
	stats.ValuesByAdmin = make(map[string]decimal.Decimal)

	err := s.db.QueryRowContext(ctx, statsQuery, args...).Scan(
		&stats.TotalCount, &stats.AverageValue, &stats.MinValue, &stats.MaxValue,
		&stats.MedianValue, &stats.P95Value, &stats.P99Value,
	)
	if err != nil {
		s.logger.Error("查询性能统计失败", zap.Error(err))
		return nil, fmt.Errorf("查询性能统计失败: %w", err)
	}

	// 查询按操作类型分组的统计
	if req.OperationType == "" {
		typeStatsQuery := fmt.Sprintf(`
			SELECT operation_type, AVG(metric_value) as avg_value
			FROM admin_balance_performance_metrics %s AND operation_type IS NOT NULL
			GROUP BY operation_type
		`, whereClause)

		rows, err := s.db.QueryContext(ctx, typeStatsQuery, args...)
		if err != nil {
			s.logger.Error("查询按类型分组的性能统计失败", zap.Error(err))
		} else {
			defer rows.Close()
			for rows.Next() {
				var operationType string
				var avgValue decimal.Decimal
				if err := rows.Scan(&operationType, &avgValue); err != nil {
					s.logger.Error("扫描类型统计数据失败", zap.Error(err))
					continue
				}
				stats.ValuesByType[operationType] = avgValue
			}
		}
	}

	// 查询按管理员分组的统计
	if req.AdminID == "" {
		adminStatsQuery := fmt.Sprintf(`
			SELECT admin_id, AVG(metric_value) as avg_value
			FROM admin_balance_performance_metrics %s AND admin_id IS NOT NULL
			GROUP BY admin_id
		`, whereClause)

		rows, err := s.db.QueryContext(ctx, adminStatsQuery, args...)
		if err != nil {
			s.logger.Error("查询按管理员分组的性能统计失败", zap.Error(err))
		} else {
			defer rows.Close()
			for rows.Next() {
				var adminID string
				var avgValue decimal.Decimal
				if err := rows.Scan(&adminID, &avgValue); err != nil {
					s.logger.Error("扫描管理员统计数据失败", zap.Error(err))
					continue
				}
				stats.ValuesByAdmin[adminID] = avgValue
			}
		}
	}

	// 查询趋势数据（按小时分组）
	trendQuery := fmt.Sprintf(`
		SELECT 
			DATE_TRUNC('hour', created_at) as hour_bucket,
			AVG(metric_value) as avg_value,
			COUNT(*) as count
		FROM admin_balance_performance_metrics %s
		GROUP BY hour_bucket
		ORDER BY hour_bucket
	`, whereClause)

	rows, err := s.db.QueryContext(ctx, trendQuery, args...)
	if err != nil {
		s.logger.Error("查询趋势数据失败", zap.Error(err))
	} else {
		defer rows.Close()
		for rows.Next() {
			var point TrendPoint
			if err := rows.Scan(&point.Timestamp, &point.Value, &point.Count); err != nil {
				s.logger.Error("扫描趋势数据失败", zap.Error(err))
				continue
			}
			stats.TrendData = append(stats.TrendData, &point)
		}
	}

	return &stats, nil
}

// GetSlowQueryStats 获取慢查询统计
func (s *DefaultPerformanceMonitorService) GetSlowQueryStats(ctx context.Context, duration time.Duration) ([]*SlowQueryStat, error) {
	slowQueryThreshold := s.configService.GetConfigAsIntWithDefault("admin_balance.slow_query_threshold_ms", 1000)

	query := `
		SELECT 
			operation_type,
			COUNT(*) as count,
			AVG(metric_value) as avg_time,
			MAX(metric_value) as max_time,
			SUM(metric_value) as total_time,
			MAX(created_at) as last_occurrence
		FROM admin_balance_performance_metrics
		WHERE metric_name = 'execution_time' 
			AND metric_unit = 'ms'
			AND metric_value >= $1
			AND created_at >= NOW() - INTERVAL '%d minutes'
			AND operation_type IS NOT NULL
		GROUP BY operation_type
		ORDER BY avg_time DESC
	`

	minutes := int(duration.Minutes())
	rows, err := s.db.QueryContext(ctx, fmt.Sprintf(query, minutes), slowQueryThreshold)
	if err != nil {
		s.logger.Error("查询慢查询统计失败", zap.Error(err))
		return nil, fmt.Errorf("查询慢查询统计失败: %w", err)
	}
	defer rows.Close()

	var stats []*SlowQueryStat
	for rows.Next() {
		stat := &SlowQueryStat{}
		if err := rows.Scan(&stat.OperationType, &stat.Count, &stat.AverageTime, &stat.MaxTime, &stat.TotalTime, &stat.LastOccurrence); err != nil {
			s.logger.Error("扫描慢查询统计数据失败", zap.Error(err))
			continue
		}
		stats = append(stats, stat)
	}

	if err := rows.Err(); err != nil {
		s.logger.Error("遍历慢查询统计数据失败", zap.Error(err))
		return nil, fmt.Errorf("遍历慢查询统计数据失败: %w", err)
	}

	return stats, nil
}

// DetectPerformanceAnomalies 检测性能异常
func (s *DefaultPerformanceMonitorService) DetectPerformanceAnomalies(ctx context.Context, threshold time.Duration) ([]*PerformanceAnomaly, error) {
	slowQueryThreshold := s.configService.GetConfigAsIntWithDefault("admin_balance.slow_query_threshold_ms", 1000)
	thresholdMs := decimal.NewFromFloat(threshold.Seconds() * 1000)

	var anomalies []*PerformanceAnomaly

	// 检测慢查询异常
	slowQueryQuery := `
		SELECT
			operation_type, admin_id, metric_value, created_at
		FROM admin_balance_performance_metrics
		WHERE metric_name = 'execution_time'
			AND metric_unit = 'ms'
			AND metric_value >= $1
			AND created_at >= NOW() - INTERVAL '1 hour'
		ORDER BY metric_value DESC
		LIMIT 50
	`

	rows, err := s.db.QueryContext(ctx, slowQueryQuery, slowQueryThreshold)
	if err != nil {
		s.logger.Error("查询慢查询异常失败", zap.Error(err))
		return nil, fmt.Errorf("查询慢查询异常失败: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var operationType, adminID string
		var metricValue decimal.Decimal
		var detectedAt time.Time

		if err := rows.Scan(&operationType, &adminID, &metricValue, &detectedAt); err != nil {
			s.logger.Error("扫描慢查询数据失败", zap.Error(err))
			continue
		}

		severity := "medium"
		if metricValue.GreaterThan(thresholdMs.Mul(decimal.NewFromInt(2))) {
			severity = "high"
		}
		if metricValue.GreaterThan(thresholdMs.Mul(decimal.NewFromInt(5))) {
			severity = "critical"
		}

		anomalies = append(anomalies, &PerformanceAnomaly{
			OperationType:  operationType,
			AdminID:        adminID,
			MetricName:     "execution_time",
			CurrentValue:   metricValue,
			ThresholdValue: thresholdMs,
			Severity:       severity,
			Description:    fmt.Sprintf("操作执行时间过长: %sms", metricValue.String()),
			DetectedAt:     detectedAt,
		})
	}

	// 检测高频错误异常
	errorRateQuery := `
		SELECT
			operation_type, admin_id, COUNT(*) as error_count
		FROM admin_balance_audit_logs_enhanced
		WHERE result = 'failed'
			AND created_at >= NOW() - INTERVAL '1 hour'
		GROUP BY operation_type, admin_id
		HAVING COUNT(*) > 10
		ORDER BY error_count DESC
	`

	rows, err = s.db.QueryContext(ctx, errorRateQuery)
	if err != nil {
		s.logger.Error("查询错误率异常失败", zap.Error(err))
	} else {
		defer rows.Close()
		for rows.Next() {
			var operationType, adminID string
			var errorCount int64

			if err := rows.Scan(&operationType, &adminID, &errorCount); err != nil {
				s.logger.Error("扫描错误率数据失败", zap.Error(err))
				continue
			}

			severity := "medium"
			if errorCount > 50 {
				severity = "high"
			}
			if errorCount > 100 {
				severity = "critical"
			}

			anomalies = append(anomalies, &PerformanceAnomaly{
				OperationType:  operationType,
				AdminID:        adminID,
				MetricName:     "error_rate",
				CurrentValue:   decimal.NewFromInt(errorCount),
				ThresholdValue: decimal.NewFromInt(10),
				Severity:       severity,
				Description:    fmt.Sprintf("1小时内错误次数过多: %d次", errorCount),
				DetectedAt:     util.NowBeijing(),
			})
		}
	}

	return anomalies, nil
}
