package service

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// AddressParseService 地址解析服务接口
type AddressParseService interface {
	// ParseAddress 解析单个地址
	ParseAddress(ctx context.Context, req *model.AddressParseRequest) (*model.AddressInfo, error)

	// BatchParseAddress 批量解析地址
	BatchParseAddress(ctx context.Context, req *model.BatchAddressParseRequest) (*model.BatchAddressParseData, error)

	// ValidateAddress 验证地址
	ValidateAddress(ctx context.Context, req *model.AddressValidationRequest) (*model.AddressValidationData, error)

	// SaveParseHistory 保存解析历史
	SaveParseHistory(ctx context.Context, userID string, originalText string, result *model.AddressInfo, success bool, errorMsg string, processTime float64) error

	// GetParseHistory 获取解析历史
	GetParseHistory(ctx context.Context, userID string, limit, offset int) ([]*model.AddressParseHistory, int64, error)
}

// DefaultAddressParseService 默认地址解析服务实现
type DefaultAddressParseService struct {
	logger           *zap.Logger
	httpClient       *http.Client
	addressLibrary   AddressLibraryService
	apiURL           string
	headers          map[string]string
	validationEngine *AddressValidationEngine
}

// NewAddressParseService 创建地址解析服务
func NewAddressParseService(logger *zap.Logger, addressLibrary AddressLibraryService) AddressParseService {
	// 创建HTTP客户端，跳过SSL验证
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	client := &http.Client{
		Transport: tr,
		Timeout:   30 * time.Second,
	}

	// 设置API配置
	headers := map[string]string{
		"Content-Type": "application/json",
		"lop-dn":       "logistics-mrd.jd.com", // 必须的参数
	}

	// 创建地区管理器
	regionManager := NewSimpleRegionManager(logger, addressLibrary)

	// 创建验证引擎
	validationEngine := NewAddressValidationEngine(logger, addressLibrary, regionManager)

	return &DefaultAddressParseService{
		logger:           logger,
		httpClient:       client,
		addressLibrary:   addressLibrary,
		validationEngine: validationEngine,
		apiURL:           "https://lop-proxy.jd.com/address/queryAddressRecognition",
		headers:          headers,
	}
}

// ParseAddress 解析单个地址 - 多重保障机制
func (s *DefaultAddressParseService) ParseAddress(ctx context.Context, req *model.AddressParseRequest) (*model.AddressInfo, error) {
	startTime := util.NowBeijing()

	// 🔥 治本方案：预处理输入文本
	originalText := req.Text
	preprocessedText := s.preprocessAddressText(req.Text)
	req.Text = preprocessedText

	s.logger.Info("🚀 开始多重保障地址解析",
		zap.String("original_text", originalText),
		zap.String("preprocessed_text", preprocessedText),
		zap.Float64("lat", req.Lat),
		zap.Float64("lng", req.Lng))

	// 🔥 第一重保障：京东API解析
	addressInfo, err := s.parseWithJDAPI(ctx, req)
	if err == nil && s.validateAddressInfo(addressInfo) {
		s.logger.Info("✅ 京东API解析成功",
			zap.String("name", addressInfo.Name),
			zap.String("mobile", addressInfo.Mobile),
			zap.String("province", addressInfo.ProvinceName),
			zap.String("city", addressInfo.CityName),
			zap.String("district", addressInfo.DistrictName))
		return addressInfo, nil
	}

	s.logger.Warn("⚠️ 京东API解析失败或结果不准确，启用备用解析", zap.Error(err))

	// 🔥 第二重保障：正则表达式解析
	addressInfo, err = s.parseWithRegex(req.Text)
	if err == nil && s.validateAddressInfo(addressInfo) {
		s.logger.Info("✅ 正则表达式解析成功",
			zap.String("name", addressInfo.Name),
			zap.String("mobile", addressInfo.Mobile),
			zap.String("province", addressInfo.ProvinceName),
			zap.String("city", addressInfo.CityName),
			zap.String("district", addressInfo.DistrictName))
		return addressInfo, nil
	}

	s.logger.Warn("⚠️ 正则表达式解析失败，启用兜底解析", zap.Error(err))

	// 🔥 第三重保障：兜底解析（智能分割）
	addressInfo = s.parseWithFallback(req.Text)

	elapsed := time.Since(startTime)
	s.logger.Info("🎯 兜底解析完成",
		zap.Duration("elapsed", elapsed),
		zap.String("name", addressInfo.Name),
		zap.String("mobile", addressInfo.Mobile),
		zap.String("province", addressInfo.ProvinceName),
		zap.String("city", addressInfo.CityName),
		zap.String("district", addressInfo.DistrictName))

	return addressInfo, nil
}

// parseWithJDAPI 使用京东API解析地址
func (s *DefaultAddressParseService) parseWithJDAPI(ctx context.Context, req *model.AddressParseRequest) (*model.AddressInfo, error) {
	// 构建请求payload
	payload := []model.AddressParseRequest{*req}

	// 序列化请求
	jsonData, err := json.Marshal(payload)
	if err != nil {
		s.logger.Error("序列化请求失败", zap.Error(err))
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	s.logger.Info("🔍 发送请求到京东API",
		zap.String("url", s.apiURL),
		zap.String("payload", string(jsonData)))

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", s.apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		s.logger.Error("创建HTTP请求失败", zap.Error(err))
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	for key, value := range s.headers {
		httpReq.Header.Set(key, value)
	}

	// 发送请求
	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		s.logger.Error("发送HTTP请求失败", zap.Error(err))
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	s.logger.Info("API请求完成", zap.Int("statusCode", resp.StatusCode))

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		s.logger.Error("API请求失败", zap.Int("statusCode", resp.StatusCode))
		return nil, fmt.Errorf("API请求失败，状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Error("读取响应体失败", zap.Error(err))
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	s.logger.Info("🔍 收到京东API响应",
		zap.String("response", string(responseBody)))

	// 解析响应
	var apiResponse model.AddressParseResponse
	if err := json.Unmarshal(responseBody, &apiResponse); err != nil {
		s.logger.Error("解析API响应失败", zap.Error(err))
		return nil, fmt.Errorf("解析API响应失败: %w", err)
	}

	// 检查API响应
	if apiResponse.Code != 0 || !apiResponse.Success {
		s.logger.Error("API返回错误",
			zap.Int("code", apiResponse.Code),
			zap.String("message", apiResponse.Message))
		return nil, fmt.Errorf("API返回错误: %s", apiResponse.Message)
	}

	// 检查数据
	if apiResponse.Data == nil || apiResponse.Data.AddressInfo == nil {
		s.logger.Error("API返回数据为空")
		return nil, fmt.Errorf("API返回数据为空")
	}

	addressInfo := apiResponse.Data.AddressInfo

	// 🔥 关键修复：将根级别的姓名和电话信息合并到AddressInfo中
	if addressInfo.Name == "" && apiResponse.Data.Name != "" {
		addressInfo.Name = apiResponse.Data.Name
	}
	if addressInfo.Mobile == "" && apiResponse.Data.MobilePhone != "" {
		addressInfo.Mobile = apiResponse.Data.MobilePhone
	}
	if addressInfo.Phone == "" && apiResponse.Data.Phone != "" {
		addressInfo.Phone = apiResponse.Data.Phone
	}

	// 🔥 新增修复：智能合并街道信息到详细地址
	addressInfo = s.enhanceDetailAddress(addressInfo)

	s.logger.Info("🔧 地址信息增强完成",
		zap.String("townName", addressInfo.TownName),
		zap.String("detailAddress", addressInfo.DetailAddress),
		zap.String("fullAddress", addressInfo.FullAddress))

	// 🔥 治本方案：使用验证引擎进行智能验证和修正
	if s.validationEngine != nil {
		validatedInfo, err := s.validationEngine.ValidateAndCorrect(ctx, addressInfo, req.Text)
		if err != nil {
			s.logger.Warn("地址验证引擎执行失败", zap.Error(err))
		} else {
			s.logger.Info("✅ 地址验证引擎处理完成",
				zap.String("original_city", addressInfo.CityName),
				zap.String("validated_city", validatedInfo.CityName),
				zap.String("original_district", addressInfo.DistrictName),
				zap.String("validated_district", validatedInfo.DistrictName))
			return validatedInfo, nil
		}
	}

	// 🔥 兜底方案：使用原有的修正逻辑
	if s.needsCorrection(req.Text, addressInfo) {
		s.logger.Warn("🔧 检测到地址解析结果需要修正",
			zap.String("original", req.Text),
			zap.String("parsed_city", addressInfo.CityName),
			zap.String("parsed_district", addressInfo.DistrictName))

		// 尝试修正地址信息
		correctedInfo := s.correctAddressInfo(req.Text, addressInfo)
		if correctedInfo != nil {
			s.logger.Info("✅ 地址信息修正成功",
				zap.String("corrected_city", correctedInfo.CityName),
				zap.String("corrected_district", correctedInfo.DistrictName))
			return correctedInfo, nil
		}
	}

	return addressInfo, nil
}

// validateAddressInfo 验证地址信息的完整性和准确性
func (s *DefaultAddressParseService) validateAddressInfo(addressInfo *model.AddressInfo) bool {
	if addressInfo == nil {
		return false
	}

	// 检查基本信息完整性
	hasBasicInfo := addressInfo.Name != "" || addressInfo.Mobile != "" || addressInfo.Phone != ""
	hasAddressInfo := addressInfo.ProvinceName != "" && addressInfo.CityName != "" && addressInfo.DetailAddress != ""

	s.logger.Debug("🔍 验证地址信息",
		zap.Bool("hasBasicInfo", hasBasicInfo),
		zap.Bool("hasAddressInfo", hasAddressInfo),
		zap.String("province", addressInfo.ProvinceName),
		zap.String("city", addressInfo.CityName),
		zap.String("district", addressInfo.DistrictName))

	return hasBasicInfo && hasAddressInfo
}

// parseWithRegex 使用正则表达式解析地址
func (s *DefaultAddressParseService) parseWithRegex(text string) (*model.AddressInfo, error) {
	s.logger.Info("🔧 启用正则表达式解析", zap.String("text", text))

	addressInfo := &model.AddressInfo{}

	// 正则表达式模式
	patterns := map[string]string{
		"name":     `([^\n\r\d]{2,4})\s*[\n\r]`,                                // 姓名
		"mobile":   `1[3-9]\d{9}`,                                              // 手机号
		"phone":    `\d{3,4}-?\d{7,8}`,                                         // 固定电话
		"province": `(北京|天津|上海|重庆|[^省市区县]{2,3}省)`,                              // 省份
		"city":     `([^市区县]{2,4}市)`,                                           // 城市
		"district": `([^区县]{2,4}[区县])`,                                         // 区县
		"street":   `([^街道]{2,6}[街道])`,                                         // 街道
		"detail":   `([^省市区县\n\r]+(?:路|街|巷|号|室|层|楼|栋|单元|小区|大厦|广场|中心)[^\n\r]*)`, // 详细地址
	}

	// 提取姓名
	if nameRegex := regexp.MustCompile(patterns["name"]); nameRegex != nil {
		if matches := nameRegex.FindStringSubmatch(text); len(matches) > 1 {
			addressInfo.Name = strings.TrimSpace(matches[1])
		}
	}

	// 提取手机号
	if mobileRegex := regexp.MustCompile(patterns["mobile"]); mobileRegex != nil {
		if match := mobileRegex.FindString(text); match != "" {
			addressInfo.Mobile = match
			addressInfo.Phone = match
		}
	}

	// 提取固定电话（如果没有手机号）
	if addressInfo.Phone == "" {
		if phoneRegex := regexp.MustCompile(patterns["phone"]); phoneRegex != nil {
			if match := phoneRegex.FindString(text); match != "" {
				addressInfo.Phone = match
			}
		}
	}

	// 提取省份
	if provinceRegex := regexp.MustCompile(patterns["province"]); provinceRegex != nil {
		if matches := provinceRegex.FindStringSubmatch(text); len(matches) > 1 {
			province := matches[1]
			if !strings.HasSuffix(province, "省") && !strings.Contains("北京天津上海重庆", province) {
				province += "省"
			}
			addressInfo.ProvinceName = province
		}
	}

	// 提取城市
	if cityRegex := regexp.MustCompile(patterns["city"]); cityRegex != nil {
		if matches := cityRegex.FindStringSubmatch(text); len(matches) > 1 {
			addressInfo.CityName = matches[1]
		}
	}

	// 提取区县
	if districtRegex := regexp.MustCompile(patterns["district"]); districtRegex != nil {
		if matches := districtRegex.FindStringSubmatch(text); len(matches) > 1 {
			addressInfo.DistrictName = matches[1]
		}
	}

	// 提取街道
	if streetRegex := regexp.MustCompile(patterns["street"]); streetRegex != nil {
		if matches := streetRegex.FindStringSubmatch(text); len(matches) > 1 {
			addressInfo.TownName = matches[1]
		}
	}

	// 提取详细地址
	if detailRegex := regexp.MustCompile(patterns["detail"]); detailRegex != nil {
		if match := detailRegex.FindString(text); match != "" {
			addressInfo.DetailAddress = strings.TrimSpace(match)
		}
	}

	s.logger.Info("🔧 正则表达式解析结果",
		zap.String("name", addressInfo.Name),
		zap.String("mobile", addressInfo.Mobile),
		zap.String("province", addressInfo.ProvinceName),
		zap.String("city", addressInfo.CityName),
		zap.String("district", addressInfo.DistrictName),
		zap.String("street", addressInfo.TownName),
		zap.String("detail", addressInfo.DetailAddress))

	// 🔥 应用地址增强逻辑
	addressInfo = s.enhanceDetailAddress(addressInfo)

	// 检查是否解析出足够的信息
	if addressInfo.Name == "" && addressInfo.Mobile == "" && addressInfo.Phone == "" {
		return nil, fmt.Errorf("正则表达式未能提取到基本信息")
	}

	if addressInfo.ProvinceName == "" || addressInfo.DetailAddress == "" {
		return nil, fmt.Errorf("正则表达式未能提取到完整地址信息")
	}

	return addressInfo, nil
}

// parseWithFallback 兜底解析方法
func (s *DefaultAddressParseService) parseWithFallback(text string) *model.AddressInfo {
	s.logger.Info("🛡️ 启用兜底解析", zap.String("text", text))

	addressInfo := &model.AddressInfo{}

	// 按行分割文本
	lines := strings.Split(text, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		s.logger.Debug("🔍 分析行内容", zap.Int("line", i), zap.String("content", line))

		// 第一行通常是姓名
		if i == 0 && addressInfo.Name == "" {
			// 移除可能的前缀
			line = strings.TrimPrefix(line, "姓名：")
			line = strings.TrimPrefix(line, "- 姓名：")
			line = strings.TrimSpace(line)
			if len(line) >= 2 && len(line) <= 4 && !regexp.MustCompile(`\d`).MatchString(line) {
				addressInfo.Name = line
				continue
			}
		}

		// 检查是否是电话号码
		if regexp.MustCompile(`1[3-9]\d{9}`).MatchString(line) {
			if addressInfo.Mobile == "" {
				addressInfo.Mobile = regexp.MustCompile(`1[3-9]\d{9}`).FindString(line)
				addressInfo.Phone = addressInfo.Mobile
			}
			continue
		}

		// 检查是否是地址信息
		if strings.Contains(line, "省") || strings.Contains(line, "市") || strings.Contains(line, "区") || strings.Contains(line, "县") {
			// 移除可能的前缀
			line = strings.TrimPrefix(line, "地址：")
			line = strings.TrimPrefix(line, "- 地址：")
			line = strings.TrimSpace(line)

			// 简单的地址分割
			if addressInfo.ProvinceName == "" {
				if strings.Contains(line, "天津") {
					addressInfo.ProvinceName = "天津市"
					addressInfo.CityName = "天津市"
				} else if strings.Contains(line, "北京") {
					addressInfo.ProvinceName = "北京市"
					addressInfo.CityName = "北京市"
				} else if strings.Contains(line, "上海") {
					addressInfo.ProvinceName = "上海市"
					addressInfo.CityName = "上海市"
				} else if strings.Contains(line, "重庆") {
					addressInfo.ProvinceName = "重庆市"
					addressInfo.CityName = "重庆市"
				}
			}

			// 提取区县信息
			if addressInfo.DistrictName == "" {
				if strings.Contains(line, "和平区") {
					addressInfo.DistrictName = "和平区"
				} else if strings.Contains(line, "河西区") {
					addressInfo.DistrictName = "河西区"
				} else if strings.Contains(line, "南开区") {
					addressInfo.DistrictName = "南开区"
				} else if strings.Contains(line, "河东区") {
					addressInfo.DistrictName = "河东区"
				} else if strings.Contains(line, "河北区") {
					addressInfo.DistrictName = "河北区"
				} else if strings.Contains(line, "红桥区") {
					addressInfo.DistrictName = "红桥区"
				}
			}

			// 提取详细地址
			if addressInfo.DetailAddress == "" {
				// 查找详细地址部分
				detailStart := -1
				for _, keyword := range []string{"路", "街", "巷", "号", "室", "层", "楼", "栋", "单元", "小区", "大厦", "广场", "中心"} {
					if idx := strings.Index(line, keyword); idx != -1 {
						// 向前查找地址开始位置
						start := 0
						for j := idx; j >= 0; j-- {
							if strings.ContainsAny(string(line[j]), "省市区县") {
								start = j + 1
								break
							}
						}
						detailStart = start
						break
					}
				}

				if detailStart >= 0 {
					addressInfo.DetailAddress = strings.TrimSpace(line[detailStart:])
				}
			}
		}
	}

	// 设置默认值
	if addressInfo.Name == "" {
		addressInfo.Name = "未识别"
	}
	if addressInfo.ProvinceName == "" {
		addressInfo.ProvinceName = "未识别省份"
	}
	if addressInfo.CityName == "" {
		addressInfo.CityName = "未识别城市"
	}
	if addressInfo.DistrictName == "" {
		addressInfo.DistrictName = "未识别区县"
	}
	if addressInfo.DetailAddress == "" {
		addressInfo.DetailAddress = "未识别详细地址"
	}

	s.logger.Info("🛡️ 兜底解析完成",
		zap.String("name", addressInfo.Name),
		zap.String("mobile", addressInfo.Mobile),
		zap.String("province", addressInfo.ProvinceName),
		zap.String("city", addressInfo.CityName),
		zap.String("district", addressInfo.DistrictName),
		zap.String("detail", addressInfo.DetailAddress))

	return addressInfo
}

// needsCorrection 检查地址解析结果是否需要修正
func (s *DefaultAddressParseService) needsCorrection(originalText string, addressInfo *model.AddressInfo) bool {
	if addressInfo == nil {
		return true
	}

	// 检查常见的错误解析情况

	// 1. 天津市地址被错误解析为滨海新区
	if strings.Contains(originalText, "天津市") && strings.Contains(originalText, "和平区") {
		if addressInfo.CityName == "滨海新区" || addressInfo.DistrictName == "天津港保税区" {
			s.logger.Warn("🔧 检测到天津市地址解析错误",
				zap.String("original", originalText),
				zap.String("parsed_city", addressInfo.CityName),
				zap.String("parsed_district", addressInfo.DistrictName))
			return true
		}
	}

	// 2. 北京市地址层次错误修正
	if strings.Contains(originalText, "北京市") || (addressInfo.ProvinceName == "北京" && strings.Contains(originalText, "海淀区")) {
		// 检查海淀区是否被错误识别为城市
		if addressInfo.CityName == "海淀区" || addressInfo.CityName == "朝阳区" ||
			addressInfo.CityName == "西城区" || addressInfo.CityName == "东城区" ||
			addressInfo.CityName == "丰台区" || addressInfo.CityName == "石景山区" ||
			addressInfo.CityName == "门头沟区" || addressInfo.CityName == "房山区" ||
			addressInfo.CityName == "通州区" || addressInfo.CityName == "顺义区" ||
			addressInfo.CityName == "昌平区" || addressInfo.CityName == "大兴区" ||
			addressInfo.CityName == "怀柔区" || addressInfo.CityName == "平谷区" ||
			addressInfo.CityName == "密云区" || addressInfo.CityName == "延庆区" {
			s.logger.Warn("🔧 检测到北京市地址层次错误",
				zap.String("original", originalText),
				zap.String("parsed_province", addressInfo.ProvinceName),
				zap.String("parsed_city", addressInfo.CityName),
				zap.String("parsed_district", addressInfo.DistrictName))
			return true
		}
	}

	// 3. 其他常见错误模式可以在这里添加

	return false
}

// correctAddressInfo 修正地址信息
func (s *DefaultAddressParseService) correctAddressInfo(originalText string, addressInfo *model.AddressInfo) *model.AddressInfo {
	if addressInfo == nil {
		return nil
	}

	correctedInfo := &model.AddressInfo{
		Name:          addressInfo.Name,
		Mobile:        addressInfo.Mobile,
		Phone:         addressInfo.Phone,
		ProvinceCode:  addressInfo.ProvinceCode,
		ProvinceName:  addressInfo.ProvinceName,
		CityCode:      addressInfo.CityCode,
		CityName:      addressInfo.CityName,
		DistrictCode:  addressInfo.DistrictCode,
		DistrictName:  addressInfo.DistrictName,
		TownCode:      addressInfo.TownCode,
		TownName:      addressInfo.TownName,
		DetailAddress: addressInfo.DetailAddress,
		FullAddress:   addressInfo.FullAddress,
		Latitude:      addressInfo.Latitude,
		Longitude:     addressInfo.Longitude,
		Confidence:    addressInfo.Confidence,
	}

	// 修正天津市地址
	if strings.Contains(originalText, "天津市") && strings.Contains(originalText, "和平区") {
		if addressInfo.CityName == "滨海新区" || addressInfo.DistrictName == "天津港保税区" {
			correctedInfo.ProvinceCode = "3"
			correctedInfo.ProvinceName = "天津市"
			correctedInfo.CityCode = "3"
			correctedInfo.CityName = "天津市"
			correctedInfo.DistrictCode = "120101"
			correctedInfo.DistrictName = "和平区"

			s.logger.Info("🔧 修正天津市地址信息",
				zap.String("original_city", addressInfo.CityName),
				zap.String("corrected_city", correctedInfo.CityName),
				zap.String("original_district", addressInfo.DistrictName),
				zap.String("corrected_district", correctedInfo.DistrictName))

			return correctedInfo
		}
	}

	// 修正北京市地址层次错误
	if strings.Contains(originalText, "北京市") || (addressInfo.ProvinceName == "北京" && strings.Contains(originalText, "海淀区")) {
		// 检查海淀区是否被错误识别为城市
		if addressInfo.CityName == "海淀区" {
			correctedInfo.ProvinceCode = "1"
			correctedInfo.ProvinceName = "北京市"
			correctedInfo.CityCode = "1"
			correctedInfo.CityName = "北京市"
			correctedInfo.DistrictCode = "110108"
			correctedInfo.DistrictName = "海淀区"

			// 保持原有的街道信息，但移到TownName字段
			if addressInfo.DistrictName != "" && strings.Contains(addressInfo.DistrictName, "街道") {
				correctedInfo.TownName = addressInfo.DistrictName
			}

			s.logger.Info("🔧 修正北京市海淀区地址信息",
				zap.String("original_city", addressInfo.CityName),
				zap.String("corrected_city", correctedInfo.CityName),
				zap.String("original_district", addressInfo.DistrictName),
				zap.String("corrected_district", correctedInfo.DistrictName),
				zap.String("corrected_town", correctedInfo.TownName))

			return correctedInfo
		}

		// 处理其他北京区县
		beijingDistricts := map[string]string{
			"朝阳区": "110105", "西城区": "110102", "东城区": "110101",
			"丰台区": "110106", "石景山区": "110107", "门头沟区": "110109",
			"房山区": "110111", "通州区": "110112", "顺义区": "110113",
			"昌平区": "110114", "大兴区": "110115", "怀柔区": "110116",
			"平谷区": "110117", "密云区": "110118", "延庆区": "110119",
		}

		if districtCode, exists := beijingDistricts[addressInfo.CityName]; exists {
			correctedInfo.ProvinceCode = "1"
			correctedInfo.ProvinceName = "北京市"
			correctedInfo.CityCode = "1"
			correctedInfo.CityName = "北京市"
			correctedInfo.DistrictCode = districtCode
			correctedInfo.DistrictName = addressInfo.CityName

			// 保持原有的街道信息
			if addressInfo.DistrictName != "" && strings.Contains(addressInfo.DistrictName, "街道") {
				correctedInfo.TownName = addressInfo.DistrictName
			}

			s.logger.Info("🔧 修正北京市地址信息",
				zap.String("original_city", addressInfo.CityName),
				zap.String("corrected_city", correctedInfo.CityName),
				zap.String("original_district", addressInfo.DistrictName),
				zap.String("corrected_district", correctedInfo.DistrictName),
				zap.String("corrected_town", correctedInfo.TownName))

			return correctedInfo
		}
	}

	// 其他修正逻辑可以在这里添加

	return nil
}

// enhanceDetailAddress 智能增强详细地址信息
func (s *DefaultAddressParseService) enhanceDetailAddress(addressInfo *model.AddressInfo) *model.AddressInfo {
	if addressInfo == nil {
		return addressInfo
	}

	s.logger.Debug("🔧 开始增强详细地址",
		zap.String("original_detail", addressInfo.DetailAddress),
		zap.String("townName", addressInfo.TownName))

	// 1. 构建完整地址
	s.buildFullAddress(addressInfo)

	// 2. 智能合并街道信息到详细地址
	s.mergeStreetInfoToDetail(addressInfo)

	s.logger.Debug("🔧 详细地址增强完成",
		zap.String("enhanced_detail", addressInfo.DetailAddress),
		zap.String("fullAddress", addressInfo.FullAddress))

	return addressInfo
}

// buildFullAddress 构建完整地址
func (s *DefaultAddressParseService) buildFullAddress(addressInfo *model.AddressInfo) {
	if addressInfo == nil {
		return
	}

	var fullAddressParts []string

	// 添加省份
	if addressInfo.ProvinceName != "" && addressInfo.ProvinceName != "未识别省份" {
		fullAddressParts = append(fullAddressParts, addressInfo.ProvinceName)
	}

	// 添加城市（避免重复）
	if addressInfo.CityName != "" && addressInfo.CityName != "未识别城市" {
		if addressInfo.ProvinceName != addressInfo.CityName {
			fullAddressParts = append(fullAddressParts, addressInfo.CityName)
		}
	}

	// 添加区县
	if addressInfo.DistrictName != "" && addressInfo.DistrictName != "未识别区县" {
		fullAddressParts = append(fullAddressParts, addressInfo.DistrictName)
	}

	// 添加街道
	if addressInfo.TownName != "" && addressInfo.TownName != "未识别街道" {
		fullAddressParts = append(fullAddressParts, addressInfo.TownName)
	}

	// 添加详细地址
	if addressInfo.DetailAddress != "" && addressInfo.DetailAddress != "未识别详细地址" {
		fullAddressParts = append(fullAddressParts, addressInfo.DetailAddress)
	}

	addressInfo.FullAddress = strings.Join(fullAddressParts, "")

	s.logger.Debug("🔧 构建完整地址",
		zap.Strings("parts", fullAddressParts),
		zap.String("fullAddress", addressInfo.FullAddress))
}

// mergeStreetInfoToDetail 智能合并街道信息到详细地址
func (s *DefaultAddressParseService) mergeStreetInfoToDetail(addressInfo *model.AddressInfo) {
	if addressInfo == nil || addressInfo.TownName == "" || addressInfo.TownName == "未识别街道" {
		return
	}

	// 检查详细地址是否已经包含街道信息
	if strings.Contains(addressInfo.DetailAddress, addressInfo.TownName) {
		s.logger.Debug("🔧 详细地址已包含街道信息，无需合并",
			zap.String("detailAddress", addressInfo.DetailAddress),
			zap.String("townName", addressInfo.TownName))
		return
	}

	// 智能合并策略
	originalDetail := addressInfo.DetailAddress

	// 策略1：如果详细地址为空或只是默认值，直接使用街道名称
	if originalDetail == "" || originalDetail == "未识别详细地址" {
		addressInfo.DetailAddress = addressInfo.TownName
		s.logger.Info("🔧 详细地址为空，使用街道名称",
			zap.String("townName", addressInfo.TownName),
			zap.String("new_detail", addressInfo.DetailAddress))
		return
	}

	// 策略2：在详细地址前添加街道信息
	addressInfo.DetailAddress = addressInfo.TownName + " " + originalDetail

	s.logger.Info("🔧 街道信息已合并到详细地址",
		zap.String("townName", addressInfo.TownName),
		zap.String("original_detail", originalDetail),
		zap.String("enhanced_detail", addressInfo.DetailAddress))
}

// preprocessAddressText 预处理地址文本
func (s *DefaultAddressParseService) preprocessAddressText(text string) string {
	s.logger.Debug("🔧 开始预处理地址文本", zap.String("original", text))

	// 1. 基础清理
	processed := strings.TrimSpace(text)

	// 2. 标准化换行符
	processed = strings.ReplaceAll(processed, "\r\n", "\n")
	processed = strings.ReplaceAll(processed, "\r", "\n")

	// 3. 清理多余空格
	processed = regexp.MustCompile(`\s+`).ReplaceAllString(processed, " ")

	// 4. 标准化地址格式
	processed = s.standardizeAddressFormat(processed)

	s.logger.Debug("🔧 地址文本预处理完成",
		zap.String("original", text),
		zap.String("processed", processed))

	return processed
}

// standardizeAddressFormat 标准化地址格式
func (s *DefaultAddressParseService) standardizeAddressFormat(text string) string {
	// 处理重复的省市信息
	// 例如："天津市 天津市 和平区" -> "天津市和平区"

	// 1. 处理直辖市重复
	municipalities := []string{"北京市", "天津市", "上海市", "重庆市"}
	for _, municipality := range municipalities {
		// 匹配模式：天津市 天津市 -> 天津市
		pattern := regexp.MustCompile(fmt.Sprintf(`%s\s+%s`, regexp.QuoteMeta(municipality), regexp.QuoteMeta(municipality)))
		text = pattern.ReplaceAllString(text, municipality)

		// 匹配模式：天津 天津市 -> 天津市
		baseName := strings.TrimSuffix(municipality, "市")
		pattern2 := regexp.MustCompile(fmt.Sprintf(`%s\s+%s`, regexp.QuoteMeta(baseName), regexp.QuoteMeta(municipality)))
		text = pattern2.ReplaceAllString(text, municipality)
	}

	// 2. 清理异常字符
	text = regexp.MustCompile(`[^\p{Han}\p{L}\p{N}\s\-()（）]`).ReplaceAllString(text, "")

	// 3. 标准化空格
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	text = strings.TrimSpace(text)

	return text
}

// BatchParseAddress 批量解析地址
func (s *DefaultAddressParseService) BatchParseAddress(ctx context.Context, req *model.BatchAddressParseRequest) (*model.BatchAddressParseData, error) {
	startTime := util.NowBeijing()

	s.logger.Info("开始批量解析地址",
		zap.Int("count", len(req.Addresses)),
		zap.Int("concurrency", req.Concurrency))

	// 设置默认并发数
	concurrency := req.Concurrency
	if concurrency <= 0 {
		concurrency = 5
	}
	if concurrency > 20 {
		concurrency = 20 // 限制最大并发数
	}

	// 创建结果切片
	results := make([]model.AddressParseResult, len(req.Addresses))

	// 使用goroutine池进行并发处理
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, concurrency)

	for i, addr := range req.Addresses {
		wg.Add(1)
		go func(index int, address model.AddressParseRequest) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 解析单个地址
			addrStartTime := util.NowBeijing()
			addressInfo, err := s.ParseAddress(ctx, &address)
			processTime := time.Since(addrStartTime).Seconds()

			// 构建结果
			result := model.AddressParseResult{
				Index:       index,
				ProcessTime: processTime,
			}

			if err != nil {
				result.Success = false
				result.Error = err.Error()
				s.logger.Error("地址解析失败",
					zap.Int("index", index),
					zap.String("text", address.Text),
					zap.Error(err))
			} else {
				result.Success = true
				result.AddressInfo = addressInfo
			}

			results[index] = result
		}(i, addr)
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 计算统计信息
	totalTime := time.Since(startTime).Seconds()
	statistics := s.calculateStatistics(results)

	s.logger.Info("批量地址解析完成",
		zap.Int("total", statistics.Total),
		zap.Int("success", statistics.Success),
		zap.Int("failed", statistics.Failed),
		zap.Float64("successRate", statistics.SuccessRate),
		zap.Float64("totalTime", totalTime))

	return &model.BatchAddressParseData{
		Results:     results,
		Statistics:  statistics,
		ProcessTime: totalTime,
	}, nil
}

// calculateStatistics 计算统计信息
func (s *DefaultAddressParseService) calculateStatistics(results []model.AddressParseResult) *model.ParseStatistics {
	total := len(results)
	success := 0

	for _, result := range results {
		if result.Success {
			success++
		}
	}

	failed := total - success
	successRate := 0.0
	if total > 0 {
		successRate = float64(success) / float64(total) * 100
	}

	return &model.ParseStatistics{
		Total:       total,
		Success:     success,
		Failed:      failed,
		SuccessRate: successRate,
	}
}

// ValidateAddress 验证地址
func (s *DefaultAddressParseService) ValidateAddress(ctx context.Context, req *model.AddressValidationRequest) (*model.AddressValidationData, error) {
	s.logger.Info("开始验证地址",
		zap.String("provinceCode", req.ProvinceCode),
		zap.String("cityCode", req.CityCode),
		zap.String("districtCode", req.DistrictCode))

	// 使用地址库验证
	isValid, err := s.addressLibrary.ValidateAddress(req.ProvinceCode, req.CityCode, req.DistrictCode)
	if err != nil {
		s.logger.Error("地址验证失败", zap.Error(err))
		return nil, fmt.Errorf("地址验证失败: %w", err)
	}

	// 获取标准化地址
	standardizedAddress := ""
	if isValid {
		fullPath, err := s.addressLibrary.GetFullAddressPath(req.DistrictCode)
		if err == nil {
			standardizedAddress = fullPath + req.DetailAddress
		}
	}

	// 构建响应
	confidence := 0.0
	if isValid {
		confidence = 1.0
	}

	data := &model.AddressValidationData{
		IsValid:             isValid,
		Confidence:          confidence,
		StandardizedAddress: standardizedAddress,
		Suggestions:         []string{}, // 可以后续扩展建议功能
	}

	s.logger.Info("地址验证完成",
		zap.Bool("isValid", isValid),
		zap.String("standardizedAddress", standardizedAddress))

	return data, nil
}

// SaveParseHistory 保存解析历史
func (s *DefaultAddressParseService) SaveParseHistory(ctx context.Context, userID string, originalText string, result *model.AddressInfo, success bool, errorMsg string, processTime float64) error {
	// 这里可以实现数据库保存逻辑
	// 由于当前没有数据库连接，先记录日志
	s.logger.Info("保存解析历史",
		zap.String("userID", userID),
		zap.String("originalText", originalText),
		zap.Bool("success", success),
		zap.Float64("processTime", processTime))

	return nil
}

// GetParseHistory 获取解析历史
func (s *DefaultAddressParseService) GetParseHistory(ctx context.Context, userID string, limit, offset int) ([]*model.AddressParseHistory, int64, error) {
	// 这里可以实现数据库查询逻辑
	// 由于当前没有数据库连接，返回空结果
	s.logger.Info("获取解析历史",
		zap.String("userID", userID),
		zap.Int("limit", limit),
		zap.Int("offset", offset))

	return []*model.AddressParseHistory{}, 0, nil
}
