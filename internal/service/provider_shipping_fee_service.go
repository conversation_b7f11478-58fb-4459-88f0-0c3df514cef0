package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"

)

// ProviderShippingFeeService 供应商运费查询服务
type ProviderShippingFeeService struct {
	providerManager *adapter.ProviderManager
	logger          *zap.Logger
	
	// 支持的供应商配置
	supportedProviders map[string]bool
	
	// 并发控制
	maxConcurrency int
}

// NewProviderShippingFeeService 创建供应商运费查询服务
func NewProviderShippingFeeService(
	providerManager *adapter.ProviderManager,
	logger *zap.Logger,
) *ProviderShippingFeeService {
	return &ProviderShippingFeeService{
		providerManager: providerManager,
		logger:          logger,
		supportedProviders: map[string]bool{
			"kuaidi100": true,
			"yuntong":   true,
			"yida":      false, // 易达不支持查询订单详情
		},
		maxConcurrency: 10, // 最大并发数
	}
}

// QueryShippingFee 查询单个订单的供应商运费
func (s *ProviderShippingFeeService) QueryShippingFee(ctx context.Context, orderRecord *model.OrderRecord) (*model.DetailedShippingFeeInfo, error) {
	s.logger.Info("开始查询供应商运费",
		zap.Int64("order_id", orderRecord.ID),
		zap.String("order_no", orderRecord.OrderNo),
		zap.String("provider", orderRecord.Provider))

	// 检查供应商是否支持
	if !s.IsProviderSupported(orderRecord.Provider) {
		return &model.DetailedShippingFeeInfo{
			OrderID:     orderRecord.ID,
			OrderNo:     orderRecord.OrderNo,
			TrackingNo:  orderRecord.TrackingNo,
			Provider:    orderRecord.Provider,
			ExpressType: orderRecord.ExpressType,
			TotalFee:    0,
			Currency:    "CNY",
			QueryTime:   util.NowBeijing().Format(time.RFC3339),
			Supported:   false,
			Error:       fmt.Sprintf("供应商 %s 不支持运费查询", orderRecord.Provider),
		}, nil
	}

	// 获取供应商适配器
	providerAdapter, exists := s.providerManager.Get(orderRecord.Provider)
	if !exists {
		return nil, fmt.Errorf("未找到供应商适配器: %s", orderRecord.Provider)
	}

	// 🔥 企业级修复：检查task_id是否为空，特别是快递100订单
	if orderRecord.TaskId == "" && orderRecord.Provider == "kuaidi100" {
		s.logger.Warn("快递100订单task_id为空，无法查询价格",
			zap.Int64("order_id", orderRecord.ID),
			zap.String("order_no", orderRecord.OrderNo),
			zap.String("tracking_no", orderRecord.TrackingNo))

		return &model.DetailedShippingFeeInfo{
			OrderID:     orderRecord.ID,
			OrderNo:     orderRecord.OrderNo,
			TrackingNo:  orderRecord.TrackingNo,
			Provider:    orderRecord.Provider,
			ExpressType: orderRecord.ExpressType,
			TotalFee:    0,
			Currency:    "CNY",
			QueryTime:   util.NowBeijing().Format(time.RFC3339),
			Supported:   true,
			Error:       "订单task_id为空，无法查询快递100价格信息。这可能是历史订单数据问题，请联系技术支持。",
		}, nil
	}

	// 调用供应商API查询订单信息
	orderInfo, err := providerAdapter.QueryOrder(ctx, orderRecord.TaskId, orderRecord.TrackingNo)
	if err != nil {
		s.logger.Error("查询供应商订单信息失败",
			zap.Int64("order_id", orderRecord.ID),
			zap.String("provider", orderRecord.Provider),
			zap.String("task_id", orderRecord.TaskId),
			zap.Error(err))

		// 🔥 企业级修复：检查是否是订单状态异常（已取消等）
		errorMsg := err.Error()
		if s.isProviderOrderStatusError(errorMsg) {
			s.logger.Warn("检测到订单状态异常，需要同步状态",
				zap.Int64("order_id", orderRecord.ID),
				zap.String("order_no", orderRecord.OrderNo),
				zap.String("error", errorMsg))

			// 返回特殊的错误信息，标识需要状态同步
			return &model.DetailedShippingFeeInfo{
				OrderID:     orderRecord.ID,
				OrderNo:     orderRecord.OrderNo,
				TrackingNo:  orderRecord.TrackingNo,
				Provider:    orderRecord.Provider,
				ExpressType: orderRecord.ExpressType,
				TotalFee:    0,
				Currency:    "CNY",
				QueryTime:   util.NowBeijing().Format(time.RFC3339),
				Supported:   true,
				Error:       fmt.Sprintf("订单状态异常: %s", errorMsg),
			}, nil
		}

		return &model.DetailedShippingFeeInfo{
			OrderID:     orderRecord.ID,
			OrderNo:     orderRecord.OrderNo,
			TrackingNo:  orderRecord.TrackingNo,
			Provider:    orderRecord.Provider,
			ExpressType: orderRecord.ExpressType,
			TotalFee:    0,
			Currency:    "CNY",
			QueryTime:   util.NowBeijing().Format(time.RFC3339),
			Supported:   true,
			Error:       fmt.Sprintf("查询失败: %s", err.Error()),
		}, nil
	}

	// 转换为详细运费信息
	detailedInfo := s.convertToDetailedShippingFeeInfo(orderRecord, orderInfo)
	
	s.logger.Info("供应商运费查询成功",
		zap.Int64("order_id", orderRecord.ID),
		zap.String("provider", orderRecord.Provider),
		zap.Float64("total_fee", detailedInfo.TotalFee))

	return detailedInfo, nil
}

// BatchQueryShippingFee 批量查询供应商运费
func (s *ProviderShippingFeeService) BatchQueryShippingFee(ctx context.Context, orderRecords []*model.OrderRecord) ([]*model.DetailedShippingFeeInfo, error) {
	if len(orderRecords) == 0 {
		return []*model.DetailedShippingFeeInfo{}, nil
	}

	s.logger.Info("开始批量查询供应商运费",
		zap.Int("order_count", len(orderRecords)))

	// 使用并发控制
	semaphore := make(chan struct{}, s.maxConcurrency)
	results := make([]*model.DetailedShippingFeeInfo, len(orderRecords))
	var wg sync.WaitGroup
	var mu sync.Mutex

	for i, orderRecord := range orderRecords {
		wg.Add(1)
		go func(index int, record *model.OrderRecord) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 查询单个订单运费
			result, err := s.QueryShippingFee(ctx, record)
			if err != nil {
				s.logger.Error("批量查询中单个订单失败",
					zap.Int64("order_id", record.ID),
					zap.Error(err))
				
				// 创建错误结果
				result = &model.DetailedShippingFeeInfo{
					OrderID:     record.ID,
					OrderNo:     record.OrderNo,
					TrackingNo:  record.TrackingNo,
					Provider:    record.Provider,
					ExpressType: record.ExpressType,
					TotalFee:    0,
					Currency:    "CNY",
					QueryTime:   util.NowBeijing().Format(time.RFC3339),
					Supported:   false,
					Error:       fmt.Sprintf("查询失败: %s", err.Error()),
				}
			}

			// 线程安全地设置结果
			mu.Lock()
			results[index] = result
			mu.Unlock()
		}(i, orderRecord)
	}

	// 等待所有查询完成
	wg.Wait()

	s.logger.Info("批量查询供应商运费完成",
		zap.Int("total_count", len(orderRecords)),
		zap.Int("success_count", s.countSuccessResults(results)))

	return results, nil
}

// IsProviderSupported 检查供应商是否支持运费查询
func (s *ProviderShippingFeeService) IsProviderSupported(provider string) bool {
	supported, exists := s.supportedProviders[provider]
	return exists && supported
}

// GetSupportedProviders 获取支持的供应商列表
func (s *ProviderShippingFeeService) GetSupportedProviders() []string {
	var providers []string
	for provider, supported := range s.supportedProviders {
		if supported {
			providers = append(providers, provider)
		}
	}
	return providers
}

// IsProviderOrderStatusError 检查是否是订单状态异常错误（公开方法）
func (s *ProviderShippingFeeService) IsProviderOrderStatusError(errorMsg string) bool {
	return s.isProviderOrderStatusError(errorMsg)
}

// convertToDetailedShippingFeeInfo 转换为详细运费信息
func (s *ProviderShippingFeeService) convertToDetailedShippingFeeInfo(orderRecord *model.OrderRecord, orderInfo *model.OrderInfo) *model.DetailedShippingFeeInfo {
	detailedInfo := &model.DetailedShippingFeeInfo{
		OrderID:     orderRecord.ID,
		OrderNo:     orderRecord.OrderNo,
		TrackingNo:  orderRecord.TrackingNo,
		Provider:    orderRecord.Provider,
		ExpressType: orderRecord.ExpressType,
		TotalFee:    orderInfo.Price,
		Currency:    "CNY",
		QueryTime:   util.NowBeijing().Format(time.RFC3339),
		Supported:   true,
	}

	// 构建费用明细（基于供应商返回的信息）
	if orderInfo.Price > 0 {
		detailedInfo.FeeDetails = []model.ShippingFeeDetail{
			{
				Type:        "freight",
				TypeName:    "运费",
				Amount:      orderInfo.Price,
				Description: "基础运费",
				Unit:        "元",
			},
		}
	}

	// 构建重量信息
	if orderInfo.Weight > 0 {
		detailedInfo.WeightInfo = &model.ShippingWeightInfo{
			OrderWeight:   orderRecord.Weight,
			ActualWeight:  orderInfo.Weight,
			ChargedWeight: orderInfo.Weight,
		}
	}

	return detailedInfo
}

// countSuccessResults 统计成功结果数量
func (s *ProviderShippingFeeService) countSuccessResults(results []*model.DetailedShippingFeeInfo) int {
	count := 0
	for _, result := range results {
		if result != nil && result.Error == "" {
			count++
		}
	}
	return count
}

// isProviderOrderStatusError 检查是否是订单状态异常错误
func (s *ProviderShippingFeeService) isProviderOrderStatusError(errorMsg string) bool {
	// 检查常见的订单状态异常关键词
	statusErrorKeywords := []string{
		"订单状态异常",
		"用户主动取消",
		"订单已取消",
		"出单失败",
		"下单失败",
		"已退回",
	}

	for _, keyword := range statusErrorKeywords {
		if providerContains(errorMsg, keyword) {
			return true
		}
	}
	return false
}

// providerContains 检查字符串是否包含子字符串（简单实现）
func providerContains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(s) > len(substr) &&
			(s[:len(substr)] == substr ||
			 s[len(s)-len(substr):] == substr ||
			 containsInMiddle(s, substr))))
}

// containsInMiddle 检查字符串中间是否包含子字符串
func containsInMiddle(s, substr string) bool {
	for i := 1; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
