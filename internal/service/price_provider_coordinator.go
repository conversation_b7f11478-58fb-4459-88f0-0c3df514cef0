package service

import (
	"context"
	"fmt"
	"log"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// PriceProviderCoordinator 供应商协调器
// 职责: 协调多个供应商的价格查询，处理并发查询和结果合并
// 遵循单一职责原则 (SRP)
type PriceProviderCoordinator struct {
	providerManager  *adapter.ProviderManager
	mappingService   express.ExpressMappingService
	blacklistService *RegionBlacklistService // 🎯 新增：地区黑名单服务
}

// NewPriceProviderCoordinator 创建供应商协调器
func NewPriceProviderCoordinator(providerManager *adapter.ProviderManager, mappingService express.ExpressMappingService, blacklistService *RegionBlacklistService) *PriceProviderCoordinator {
	return &PriceProviderCoordinator{
		providerManager:  providerManager,
		mappingService:   mappingService,
		blacklistService: blacklistService,
	}
}

// QueryAllCompanies 查询所有快递公司价格 - 双阶段策略确保完整性
func (c *PriceProviderCoordinator) QueryAllCompanies(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
	// 🔍 详细调试：记录查询所有公司的开始
	log.Printf("🚀 [DEBUG] 开始查询所有快递公司价格")
	log.Printf("🔍 [DEBUG] 请求参数: 指定供应商=%s, 快递类型=%s, 重量=%.2fkg",
		req.Provider, req.ExpressType, req.Package.Weight)

	// 获取供应商
	var providers []adapter.ProviderAdapter
	if req.Provider != "" {
		// 如果指定了供应商，则只查询该供应商
		log.Printf("🎯 [DEBUG] 指定供应商查询: %s", req.Provider)
		provider, ok := c.providerManager.Get(req.Provider)
		if !ok {
			log.Printf("❌ [ERROR] 指定的供应商不存在: %s", req.Provider)
			return nil, fmt.Errorf("供应商 %s 不存在", req.Provider)
		}
		providers = []adapter.ProviderAdapter{provider}
	} else {
		// 🎯 修复：标准查价接口包含所有供应商，通过快递公司的interface_type配置进行过滤
		// 🔥 修复：排除菜鸟和快递鸟供应商，它们只在实时查价接口中使用
		log.Printf("🌐 [DEBUG] 获取所有启用的供应商...")
		allProviders := c.providerManager.GetAll()

		// 过滤掉菜鸟和快递鸟供应商
		for _, provider := range allProviders {
			if provider.Name() != "cainiao" && provider.Name() != "kuaidiniao" {
				providers = append(providers, provider)
			}
		}

		// 🔍 详细调试：显示获取到的所有供应商
		log.Printf("📋 [DEBUG] 获取到的供应商列表 (总数: %d, 排除菜鸟后: %d):", len(allProviders), len(providers))
		for i, provider := range providers {
			log.Printf("   %d. 供应商: %s", i+1, provider.Name())
		}

		// 🔍 特别检查快递鸟是否在列表中
		kuaidiniaoFound := false
		for _, provider := range providers {
			if provider.Name() == "kuaidiniao" {
				kuaidiniaoFound = true
				break
			}
		}
		if kuaidiniaoFound {
			log.Printf("✅ [DEBUG] 快递鸟供应商已包含在列表中")
		} else {
			log.Printf("❌ [WARNING] 快递鸟供应商未包含在列表中！")
		}
	}

	if len(providers) == 0 {
		log.Printf("❌ [ERROR] 没有可用的供应商")
		return nil, fmt.Errorf("没有可用的供应商")
	}

	// 从数据库获取所有支持的快递公司映射关系
	log.Printf("🗺️ [DEBUG] 开始构建供应商映射关系...")
	expressCodesMap := make(map[string]map[string]string)

	// 初始化每个供应商的映射 - 🔍 添加快递鸟映射初始化
	expressCodesMap[model.ExpressYida] = make(map[string]string)
	expressCodesMap[model.ExpressYuntong] = make(map[string]string)
	expressCodesMap[model.ExpressKuaidi100] = make(map[string]string)
	expressCodesMap["kuaidiniao"] = make(map[string]string) // 🔍 确保快递鸟映射被初始化

	// 🎯 获取每个供应商支持的快递公司列表
	totalExpectedQueries := 0
	queryTasks := make([]QueryTask, 0)

	for _, provider := range providers {
		providerCode := provider.Name()
		log.Printf("🔍 [DEBUG] 正在为供应商 %s 查询映射关系...", providerCode)

		// 获取该供应商支持的快递公司列表
		supportedCompanies, err := c.mappingService.GetSupportedCompanies(ctx, providerCode)
		if err != nil {
			log.Printf("❌ [ERROR] 获取供应商 %s 支持的快递公司失败: %v", providerCode, err)
			// 🔍 特别关注快递鸟的错误
			if providerCode == "kuaidiniao" {
				log.Printf("🚨 [CRITICAL] 快递鸟映射关系获取失败！这可能导致快递鸟无法被调用")
			}
			continue
		}

		// 🔍 详细调试：记录每个供应商支持的快递公司
		log.Printf("📊 [DEBUG] 供应商 %s 支持的快递公司 (总数: %d):", providerCode, len(supportedCompanies))
		for i, mapping := range supportedCompanies {
			log.Printf("   %d. %s -> %s", i+1, mapping.CompanyCode, mapping.ProviderCompanyCode)
		}

		// 构建映射表和查询任务
		for _, mapping := range supportedCompanies {
			expressCodesMap[providerCode][mapping.CompanyCode] = mapping.ProviderCompanyCode
			queryTasks = append(queryTasks, QueryTask{
				Provider:     provider,
				StandardCode: mapping.CompanyCode,
				ProviderCode: mapping.ProviderCompanyCode,
			})
			totalExpectedQueries++
		}

		log.Printf("✅ [DEBUG] 供应商 %s 总共支持 %d 家快递公司，生成 %d 个查询任务",
			providerCode, len(expressCodesMap[providerCode]), len(supportedCompanies))
	}

	log.Printf("📈 [DEBUG] 预期总查询数量: %d", totalExpectedQueries)

	// 🔍 统计快递鸟相关的查询任务
	kuaidiniaoTasks := 0
	for _, task := range queryTasks {
		if task.Provider.Name() == "kuaidiniao" {
			kuaidiniaoTasks++
		}
	}
	log.Printf("🎯 [DEBUG] 快递鸟相关查询任务数量: %d", kuaidiniaoTasks)

	// 🚀 双阶段查询策略
	return c.executeTwoPhaseQuery(ctx, req, queryTasks, totalExpectedQueries)
}

// QueryTask 查询任务
type QueryTask struct {
	Provider     adapter.ProviderAdapter
	StandardCode string
	ProviderCode string
}

// executeTwoPhaseQuery 执行双阶段查询
func (c *PriceProviderCoordinator) executeTwoPhaseQuery(ctx context.Context, req *model.PriceRequest, queryTasks []QueryTask, expectedTotal int) ([]model.StandardizedPrice, error) {
	var mu sync.Mutex
	var allPrices []model.StandardizedPrice
	var failedTasks []QueryTask

	// 第一阶段：快速查询（800ms超时）
	log.Printf("🚀 第一阶段：快速查询开始，总任务数: %d", len(queryTasks))

	var wg1 sync.WaitGroup
	for _, task := range queryTasks {
		wg1.Add(1)
		go func(t QueryTask) {
			defer wg1.Done()

			// 第一阶段：快速超时
			ctx1, cancel1 := context.WithTimeout(ctx, 800*time.Millisecond)
			defer cancel1()

			prices, err := c.executeQueryTask(ctx1, req, t)

			mu.Lock()
			if err != nil {
				failedTasks = append(failedTasks, t)
				log.Printf("第一阶段失败: %s-%s, 将在第二阶段重试", t.Provider.Name(), t.StandardCode)
			} else {
				allPrices = append(allPrices, prices...)
				log.Printf("第一阶段成功: %s-%s, 获得%d个价格", t.Provider.Name(), t.StandardCode, len(prices))
			}
			mu.Unlock()
		}(task)
	}

	// 等待第一阶段完成（最多1秒）
	done1 := make(chan struct{})
	go func() {
		wg1.Wait()
		close(done1)
	}()

	select {
	case <-done1:
		log.Printf("✅ 第一阶段完成")
	case <-time.After(1 * time.Second):
		log.Printf("⏰ 第一阶段超时，但继续第二阶段")
	}

	// 检查第一阶段结果
	mu.Lock()
	phase1Results := len(allPrices)
	phase1Failed := len(failedTasks)
	mu.Unlock()

	log.Printf("📊 第一阶段统计: 成功%d个价格, 失败%d个任务", phase1Results, phase1Failed)

	// 第二阶段：补全查询（仅对失败的任务重试，更长超时）
	if len(failedTasks) > 0 {
		log.Printf("🔄 第二阶段：补全查询开始，重试任务数: %d", len(failedTasks))

		var wg2 sync.WaitGroup
		for _, task := range failedTasks {
			wg2.Add(1)
			go func(t QueryTask) {
				defer wg2.Done()

				// 第二阶段：较长超时确保成功
				ctx2, cancel2 := context.WithTimeout(ctx, 2*time.Second)
				defer cancel2()

				prices, err := c.executeQueryTask(ctx2, req, t)

				mu.Lock()
				if err != nil {
					log.Printf("第二阶段仍然失败: %s-%s, 错误: %v", t.Provider.Name(), t.StandardCode, err)
				} else {
					allPrices = append(allPrices, prices...)
					log.Printf("第二阶段成功: %s-%s, 获得%d个价格", t.Provider.Name(), t.StandardCode, len(prices))
				}
				mu.Unlock()
			}(task)
		}

		// 等待第二阶段完成（最多2.5秒）
		done2 := make(chan struct{})
		go func() {
			wg2.Wait()
			close(done2)
		}()

		select {
		case <-done2:
			log.Printf("✅ 第二阶段完成")
		case <-time.After(2500 * time.Millisecond):
			log.Printf("⏰ 第二阶段超时")
		}
	}

	// 最终统计
	mu.Lock()
	finalResults := len(allPrices)
	mu.Unlock()

	log.Printf("📈 最终统计: 总共获得%d个价格, 预期%d个, 完成率%.1f%%",
		finalResults, expectedTotal, float64(finalResults)/float64(expectedTotal)*100)

	// 如果结果太少，返回错误
	if len(allPrices) == 0 {
		return nil, fmt.Errorf("所有查询都失败")
	}

	// 按价格从低到高排序
	sort.Slice(allPrices, func(i, j int) bool {
		return allPrices[i].Price < allPrices[j].Price
	})

	return allPrices, nil
}

// executeQueryTask 执行单个查询任务
func (c *PriceProviderCoordinator) executeQueryTask(ctx context.Context, req *model.PriceRequest, task QueryTask) ([]model.StandardizedPrice, error) {
	// 创建供应商特定的请求
	singleReq := *req
	singleReq.Provider = task.Provider.Name()
	singleReq.ExpressType = task.StandardCode
	singleReq.QueryAllCompanies = false

	// --- 性能日志：开始计时 ---
	startTime := util.NowBeijing()

	// 查询价格
	prices, err := task.Provider.QueryPrice(ctx, &singleReq)

	// --- 性能日志：结束计时并记录 ---
	duration := time.Since(startTime)
	log.Printf(`{"msg":"Provider performance measurement", "provider":"%s", "company_code":"%s", "duration_ms":%d, "error": %v}`,
		task.Provider.Name(), task.StandardCode, duration.Milliseconds(), err != nil)

	return prices, err
}

// QueryAllProviders 查询所有供应商价格 - 确保完整性策略
func (c *PriceProviderCoordinator) QueryAllProviders(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
	// 获取所有供应商
	// 🔥 修复：排除菜鸟供应商，菜鸟供应商只在JD专用接口中使用
	allProviders := c.providerManager.GetAll()
	var providers []adapter.ProviderAdapter
	for _, provider := range allProviders {
		if provider.Name() != "cainiao" {
			providers = append(providers, provider)
		}
	}

	if len(providers) == 0 {
		return nil, fmt.Errorf("没有可用的供应商")
	}

	// 并行查询所有供应商价格
	var wg sync.WaitGroup
	var mu sync.Mutex
	var allPrices []model.StandardizedPrice
	var errs []error

	for _, provider := range providers {
		wg.Add(1)
		go func(p adapter.ProviderAdapter) {
			defer wg.Done()

			// 合理超时时间确保完整性
			ctx, cancel := context.WithTimeout(ctx, 1500*time.Millisecond)
			defer cancel()

			// --- 性能日志：开始计时 ---
			startTime := util.NowBeijing()

			// 查询价格
			prices, err := p.QueryPrice(ctx, req)

			// --- 性能日志：结束计时并记录 ---
			duration := time.Since(startTime)
			log.Printf(`{"msg":"Provider performance measurement", "provider":"%s", "duration_ms":%d, "price_count":%d, "error":%v}`,
				p.Name(), duration.Milliseconds(), len(prices), err != nil)

			if err != nil {
				// 🎯 记录查价失败错误到黑名单（所有供应商统一处理）
				if c.blacklistService != nil && (strings.Contains(err.Error(), "不支持") || strings.Contains(err.Error(), "暂未开放")) {
					route := fmt.Sprintf("%s->%s", req.Sender.Province, req.Receiver.Province)
					c.blacklistService.RecordFailure(p.Name(), route, req.ExpressType, err.Error())

					log.Printf("供应商地区路线不支持（已记录到黑名单）: provider=%s, route=%s, express_code=%s, error=%s",
						p.Name(), route, req.ExpressType, err.Error())
				}

				mu.Lock()
				errs = append(errs, fmt.Errorf("供应商 %s 查询失败: %w", p.Name(), err))
				mu.Unlock()
				return
			}

			// 合并结果
			mu.Lock()
			allPrices = append(allPrices, prices...)
			mu.Unlock()
		}(provider)
	}

	// 等待所有供应商完成
	wg.Wait()

	// 如果所有供应商都失败，返回错误
	if len(allPrices) == 0 && len(errs) > 0 {
		return nil, fmt.Errorf("所有供应商查询失败: %v", errs)
	}

	// 按价格从低到高排序
	sort.Slice(allPrices, func(i, j int) bool {
		return allPrices[i].Price < allPrices[j].Price
	})

	return allPrices, nil
}

// QuerySingleProvider 查询单个供应商价格
func (c *PriceProviderCoordinator) QuerySingleProvider(ctx context.Context, req *model.PriceRequest, providerName string) ([]model.StandardizedPrice, error) {
	provider, ok := c.providerManager.Get(providerName)
	if !ok {
		return nil, fmt.Errorf("供应商 %s 不存在", providerName)
	}

	// 🔥 修复：增加超时时间，确保价格验证时有足够时间完成API调用
	// 从800ms增加到3秒，特别是对于下单时的价格验证
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	// 查询价格
	prices, err := provider.QueryPrice(ctx, req)
	if err != nil {
		// 🎯 记录查价失败错误到黑名单（所有供应商统一处理）
		if c.blacklistService != nil && (strings.Contains(err.Error(), "不支持") || strings.Contains(err.Error(), "暂未开放")) {
			route := fmt.Sprintf("%s->%s", req.Sender.Province, req.Receiver.Province)
			c.blacklistService.RecordFailure(providerName, route, req.ExpressType, err.Error())

			log.Printf("供应商地区路线不支持（已记录到黑名单）: provider=%s, route=%s, express_code=%s, error=%s",
				providerName, route, req.ExpressType, err.Error())
		}

		return nil, fmt.Errorf("供应商 %s 查询失败: %s", providerName, err.Error())
	}

	return prices, nil
}

// GetProviderManager 获取供应商管理器
func (c *PriceProviderCoordinator) GetProviderManager() *adapter.ProviderManager {
	return c.providerManager
}

// GetAllProviders 获取所有供应商
func (c *PriceProviderCoordinator) GetAllProviders() []adapter.ProviderAdapter {
	return c.providerManager.GetAll()
}

// GetSupportedCompanies 获取供应商支持的快递公司列表
func (c *PriceProviderCoordinator) GetSupportedCompanies(ctx context.Context, providerCode string) ([]*express.ExpressMappingCache, error) {
	return c.mappingService.GetSupportedCompanies(ctx, providerCode)
}
