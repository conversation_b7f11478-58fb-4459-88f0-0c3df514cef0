package callback

import (
	"encoding/json"
	"fmt"

	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"

)

// YidaCallbackAdapter 易达回调适配器
type YidaCallbackAdapter struct {
	// 易达回调不需要公钥验证，根据官方文档，回调验证通过IP白名单等方式
}

// NewYidaCallbackAdapter 创建易达回调适配器
func NewYidaCallbackAdapter() ProviderCallbackAdapter {
	return &YidaCallbackAdapter{}
}

// ValidateSignature 验证易达签名
func (a *YidaCallbackAdapter) ValidateSignature(rawData []byte, headers map[string]string) error {
	// 根据易达官方文档，回调接口没有提到签名验证机制
	// 易达建议通过IP白名单等方式验证回调来源的安全性
	// 这里暂时不进行签名验证，可以根据实际需求添加IP白名单验证
	return nil
}

// ParseCallback 解析易达回调数据
func (a *YidaCallbackAdapter) ParseCallback(rawData []byte) (*model.ParsedCallbackData, error) {
	// 首先尝试解析普通回调数据结构
	var callbackData struct {
		OrderNo      string      `json:"orderNo"`      // 必填：易达订单号
		DeliveryId   string      `json:"deliveryId"`   // 运单号（可选）
		ThirdNo      string      `json:"thirdNo"`      // 商家订单号（可选）
		DeliveryType string      `json:"deliveryType"` // 必填：快递类型
		PushType     int         `json:"pushType"`     // 必填：推送类型
		ContextObj   interface{} `json:"contextObj"`   // 推送内容
	}

	// 尝试解析工单回调数据结构
	var ticketCallbackData struct {
		TaskNo  string      `json:"taskNo"`  // 工单编号
		Status  int         `json:"status"`  // 工单状态
		Content string      `json:"content"` // 回复内容
		URLs    string      `json:"urls"`    // 图片地址
		Type    int         `json:"type"`    // 工单类型
		Data    interface{} `json:"data"`    // 详情附加信息
	}

	// 先尝试解析为普通回调
	if err := json.Unmarshal(rawData, &callbackData); err == nil && callbackData.OrderNo != "" {
		return a.parseNormalCallback(&callbackData)
	}

	// 再尝试解析为工单回调
	if err := json.Unmarshal(rawData, &ticketCallbackData); err == nil && ticketCallbackData.TaskNo != "" {
		return a.parseTicketCallback(&ticketCallbackData)
	}

	return nil, fmt.Errorf("无法解析易达回调数据，数据格式不符合预期")
}

// parseNormalCallback 解析普通回调数据
func (a *YidaCallbackAdapter) parseNormalCallback(callbackData *struct {
	OrderNo      string      `json:"orderNo"`
	DeliveryId   string      `json:"deliveryId"`
	ThirdNo      string      `json:"thirdNo"`
	DeliveryType string      `json:"deliveryType"`
	PushType     int         `json:"pushType"`
	ContextObj   interface{} `json:"contextObj"`
}) (*model.ParsedCallbackData, error) {
	// 验证必填字段
	if callbackData.OrderNo == "" {
		return nil, fmt.Errorf("易达订单号(orderNo)不能为空")
	}
	if callbackData.DeliveryType == "" {
		return nil, fmt.Errorf("快递类型(deliveryType)不能为空")
	}

	// 确定回调类型
	var callbackType string
	switch callbackData.PushType {
	case constants.YidaPushTypeStatusPush: // 状态推送
		callbackType = constants.EventTypeOrderStatusChanged
	case constants.YidaPushTypeBillingPush: // 账单推送
		callbackType = constants.EventTypeBillingUpdated
	case constants.YidaPushTypePickupPush: // 揽收推送
		callbackType = constants.EventTypePickupInfoUpdated
	case constants.YidaPushTypeOrderChange: // 订单变更
		callbackType = constants.EventTypeOrderChanged
	default:
		return nil, fmt.Errorf("未知的易达回调类型: %d", callbackData.PushType)
	}

	return &model.ParsedCallbackData{
		Type:            callbackType,
		OrderNo:         "",                   // 🔥 修复：OrderNo留空，由统一回调服务查找，确保使用平台订单号
		CustomerOrderNo: callbackData.ThirdNo, // 🔥 修复：ThirdNo是客户订单号
		TrackingNo:      callbackData.DeliveryId,
		Data:            callbackData.ContextObj,
		Timestamp:       util.NowBeijing(),
	}, nil
}

// parseTicketCallback 解析工单回调数据
func (a *YidaCallbackAdapter) parseTicketCallback(ticketData *struct {
	TaskNo  string      `json:"taskNo"`
	Status  int         `json:"status"`
	Content string      `json:"content"`
	URLs    string      `json:"urls"`
	Type    int         `json:"type"`
	Data    interface{} `json:"data"`
}) (*model.ParsedCallbackData, error) {
	// 验证必填字段
	if ticketData.TaskNo == "" {
		return nil, fmt.Errorf("工单编号(taskNo)不能为空")
	}

	return &model.ParsedCallbackData{
		Type:            constants.EventTypeTicketReplied,
		OrderNo:         "",
		CustomerOrderNo: "",
		TrackingNo:      "",
		Data: map[string]interface{}{
			"taskNo":  ticketData.TaskNo,
			"status":  ticketData.Status,
			"content": ticketData.Content,
			"urls":    ticketData.URLs,
			"type":    ticketData.Type,
			"data":    ticketData.Data,
		},
		Timestamp: util.NowBeijing(),
	}, nil
}

// BuildResponse 构建易达响应
func (a *YidaCallbackAdapter) BuildResponse(data *model.StandardizedCallbackData) *model.CallbackResponse {
	// 根据易达官方文档，回调响应格式必须为：
	// {
	//     "msg":"接收成功",
	//     "code":"200",
	//     "success":true
	// }
	return &model.CallbackResponse{
		Success: constants.ResponseValueTrue,
		Code:    constants.HTTPStatusOK,
		Message: constants.MessageReceived,
		Data: map[string]interface{}{
			"msg":                              constants.MessageReceived,
			constants.YidaResponseFieldCode:    constants.HTTPStatusOK,
			constants.YidaResponseFieldSuccess: constants.ResponseValueTrue,
		},
	}
}
