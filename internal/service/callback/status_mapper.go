package callback

import (
	"fmt"
	"strconv"
	"time"

	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"

)

// StatusResult 状态映射结果
type StatusResult struct {
	Status      string                 `json:"status"`      // 统一状态
	Provider    string                 `json:"provider"`    // 供应商
	RawStatus   string                 `json:"raw_status"`  // 原始状态
	Timestamp   time.Time              `json:"timestamp"`   // 时间戳
	Extra       map[string]interface{} `json:"extra"`       // 额外信息
	Description string                 `json:"description"` // 状态描述
}

// GetDescription 获取状态描述
func (sr *StatusResult) GetDescription() string {
	if sr.Description != "" {
		return sr.Description
	}
	return model.GetOrderStatusDesc(sr.Status)
}

// UnifiedStatusMapper 统一状态映射器
type UnifiedStatusMapper struct {
	kuaidi100Mapping map[int]string
	yidaMapping      map[string]string
	yuntongMapping   map[int]string
}

// NewUnifiedStatusMapper 创建统一状态映射器
func NewUnifiedStatusMapper() *UnifiedStatusMapper {
	return &UnifiedStatusMapper{
		kuaidi100Mapping: initKuaidi100StatusMapping(),
		yidaMapping:      initYidaStatusMapping(),
		yuntongMapping:   initYuntongStatusMapping(),
	}
}

// MapKuaidi100Status 映射快递100状态
func (m *UnifiedStatusMapper) MapKuaidi100Status(status int, data map[string]interface{}) StatusResult {
	mappedStatus := m.kuaidi100Mapping[status]
	if mappedStatus == "" {
		mappedStatus = model.OrderStatusException
	}

	result := StatusResult{
		Status:    mappedStatus,
		Provider:  constants.ProviderKuaidi100,
		RawStatus: strconv.Itoa(status),
		Timestamp: util.NowBeijing(),
		Extra:     make(map[string]interface{}),
	}

	// 处理特殊状态
	switch status {
	case 14: // 异常签收
		result.Extra["delivery_type"] = "abnormal"
	case 155: // 重量调整
		result.Extra["weight_change"] = true
		if weight, ok := data["weight"]; ok {
			result.Extra["new_weight"] = weight
		}
		if freight, ok := data["freight"]; ok {
			result.Extra["new_freight"] = freight
		}
	case 166: // 订单复活
		result.Extra["revival_reason"] = "package_already_shipped"
	}

	return result
}

// MapYidaStatus 映射易达状态
func (m *UnifiedStatusMapper) MapYidaStatus(pushType int, status string, data map[string]interface{}) StatusResult {
	var mappedStatus string
	result := StatusResult{
		Provider:  constants.ProviderYida,
		RawStatus: fmt.Sprintf("pushType:%d,status:%s", pushType, status),
		Timestamp: util.NowBeijing(),
		Extra:     make(map[string]interface{}),
	}

	switch pushType {
	case 1: // 状态推送
		mappedStatus = m.yidaMapping[status]
		if mappedStatus == "" {
			mappedStatus = model.OrderStatusException
		}

	case 2: // 计费推送
		mappedStatus = model.OrderStatusBilled
		result.Extra["billing_update"] = true
		if realWeight, ok := data["realWeight"]; ok {
			result.Extra["real_weight"] = realWeight
		}
		if calcFeeWeight, ok := data["calcFeeWeight"]; ok {
			result.Extra["calc_fee_weight"] = calcFeeWeight
		}
		if feeBlocks, ok := data["feeBlockList"]; ok {
			result.Extra["fee_blocks"] = feeBlocks
		}

	case 3: // 揽收推送
		mappedStatus = model.OrderStatusPickedUp
		result.Extra["pickup_info"] = true
		if courierName, ok := data["courierName"]; ok {
			result.Extra["courier_name"] = courierName
		}
		if courierPhone, ok := data["courierPhone"]; ok {
			result.Extra["courier_phone"] = courierPhone
		}
		if pickupCode, ok := data["pickUpCode"]; ok {
			result.Extra["pickup_code"] = pickupCode
		}
		if siteName, ok := data["siteName"]; ok {
			result.Extra["site_name"] = siteName
		}

	case 5: // 订单变更
		mappedStatus = model.OrderStatusWeightUpdated
		result.Extra["order_change"] = true
		if newBusinessType, ok := data["businessTypeNew"]; ok {
			result.Extra["new_business_type"] = newBusinessType
		}
		if newDeliveryID, ok := data["deliveryIdNew"]; ok {
			result.Extra["new_delivery_id"] = newDeliveryID
		}

	default:
		mappedStatus = model.OrderStatusException
	}

	result.Status = mappedStatus
	return result
}

// MapYuntongStatus 映射云通状态
func (m *UnifiedStatusMapper) MapYuntongStatus(state int, data map[string]interface{}) StatusResult {
	mappedStatus := m.yuntongMapping[state]
	if mappedStatus == "" {
		mappedStatus = model.OrderStatusException
	}

	result := StatusResult{
		Status:    mappedStatus,
		Provider:  constants.ProviderYuntong,
		RawStatus: strconv.Itoa(state),
		Timestamp: util.NowBeijing(),
		Extra:     make(map[string]interface{}),
	}

	// 处理特殊状态
	switch state {
	case 301: // 计费
		result.Extra["billing_info"] = true
		if weight, ok := data["Weight"]; ok {
			result.Extra["weight"] = weight
		}
		if cost, ok := data["Cost"]; ok {
			result.Extra["cost"] = cost
		}
		if insureAmount, ok := data["InsureAmount"]; ok {
			result.Extra["insure_amount"] = insureAmount
		}
		if packageFee, ok := data["PackageFee"]; ok {
			result.Extra["package_fee"] = packageFee
		}
		if otherFee, ok := data["OtherFee"]; ok {
			result.Extra["other_fee"] = otherFee
		}
		if chargedWeight, ok := data["chargedWeight"]; ok {
			result.Extra["charged_weight"] = chargedWeight
		}

	case 501: // 转寄
		result.Extra["forward_info"] = true
		if backLogisticCode, ok := data["BackLogisticCode"]; ok {
			result.Extra["back_logistic_code"] = backLogisticCode
		}
		if backFee, ok := data["BackFee"]; ok {
			result.Extra["back_fee"] = backFee
		}

	case 208: // 重量更新
		result.Extra["weight_update"] = true
		if weight, ok := data["Weight"]; ok {
			result.Extra["new_weight"] = weight
		}
	}

	return result
}

// ValidateStatusTransition 验证状态转换是否合法
func (m *UnifiedStatusMapper) ValidateStatusTransition(fromStatus, toStatus string) bool {
	return model.IsValidTransition(fromStatus, toStatus)
}

// GetStatusGroup 获取状态分组
func (m *UnifiedStatusMapper) GetStatusGroup(status string) string {
	return model.GetStatusGroup(status)
}

// IsTerminalStatus 检查是否为终态
func (m *UnifiedStatusMapper) IsTerminalStatus(status string) bool {
	return model.IsTerminalStatus(status)
}

// ConvertProviderStatusToStandard 将供应商状态转换为标准状态
func (m *UnifiedStatusMapper) ConvertProviderStatusToStandard(provider string, providerStatus interface{}, data map[string]interface{}) StatusResult {
	switch provider {
	case constants.ProviderYuntong:
		if status, ok := providerStatus.(int); ok {
			return m.MapYuntongStatus(status, data)
		}
		if statusStr, ok := providerStatus.(string); ok {
			if status, err := strconv.Atoi(statusStr); err == nil {
				return m.MapYuntongStatus(status, data)
			}
		}
	case constants.ProviderYida:
		if pushType, ok := data["pushType"].(int); ok {
			if status, ok := providerStatus.(string); ok {
				return m.MapYidaStatus(pushType, status, data)
			}
		}
	case constants.ProviderKuaidi100:
		if status, ok := providerStatus.(int); ok {
			return m.MapKuaidi100Status(status, data)
		}
		if statusStr, ok := providerStatus.(string); ok {
			if status, err := strconv.Atoi(statusStr); err == nil {
				return m.MapKuaidi100Status(status, data)
			}
		}
	}

	// 返回异常状态
	return StatusResult{
		Status:    model.OrderStatusException,
		Provider:  provider,
		RawStatus: fmt.Sprintf("%v", providerStatus),
		Timestamp: util.NowBeijing(),
		Extra:     make(map[string]interface{}),
	}
}

// initYuntongStatusMapping 初始化云通状态映射
func initYuntongStatusMapping() map[int]string {
	return map[int]string{
		100: model.OrderStatusSubmitted,     // 下单成功
		400: model.OrderStatusSubmitFailed,  // 下单失败
		102: model.OrderStatusAssigned,      // 分配网点
		103: model.OrderStatusAssigned,      // 分配快递员
		104: model.OrderStatusPickedUp,      // 已取件
		301: model.OrderStatusBilled,        // 计费/已揽件
		208: model.OrderStatusWeightUpdated, // 更新重量
		203: model.OrderStatusCancelled,     // 取消订单
		204: model.OrderStatusPickupFailed,  // 揽收失败
		205: model.OrderStatusVoided,        // 作废
		2:   model.OrderStatusInTransit,     // 在途中
		3:   model.OrderStatusDelivered,     // 签收
		500: model.OrderStatusException,     // 异常
		501: model.OrderStatusForwarded,     // 已转寄
	}
}

// initYidaStatusMapping 初始化易达状态映射
func initYidaStatusMapping() map[string]string {
	return map[string]string{
		"1":  model.OrderStatusAwaitingPickup, // 待取件
		"11": model.OrderStatusPickedUp,       // 已取件
		"2":  model.OrderStatusInTransit,      // 运输中
		"3":  model.OrderStatusDelivered,      // 已签收
		"6":  model.OrderStatusException,      // 异常
		"10": model.OrderStatusCancelled,      // 已取消
	}
}

// initKuaidi100StatusMapping 初始化快递100状态映射
func initKuaidi100StatusMapping() map[int]string {
	return map[int]string{
		0:   model.OrderStatusSubmitted,         // 下单成功
		1:   model.OrderStatusAssigned,          // 已接单
		2:   model.OrderStatusAwaitingPickup,    // 收件中
		9:   model.OrderStatusCancelled,         // 用户主动取消
		10:  model.OrderStatusPickedUp,          // 已取件
		11:  model.OrderStatusPickupFailed,      // 揽货失败
		12:  model.OrderStatusReturned,          // 已退回
		13:  model.OrderStatusDelivered,         // 已签收
		14:  model.OrderStatusDeliveredAbnormal, // 异常签收
		15:  model.OrderStatusBilled,            // 已结算
		99:  model.OrderStatusCancelled,         // 订单已取消
		101: model.OrderStatusInTransit,         // 运输中
		155: model.OrderStatusWeightUpdated,     // 修改重量
		166: model.OrderStatusRevived,           // 订单复活
		200: model.OrderStatusSubmitted,         // 已出单
		201: model.OrderStatusPrintFailed,       // 出单失败
		400: model.OrderStatusOutForDelivery,    // 派送中
		610: model.OrderStatusSubmitFailed,      // 下单失败
	}
}

// GetAllStatusMappings 获取所有状态映射（用于调试和文档）
func (m *UnifiedStatusMapper) GetAllStatusMappings() map[string]interface{} {
	return map[string]interface{}{
		"yuntong_mappings": map[string]interface{}{
			"100": model.OrderStatusSubmitted + " (下单成功)",
			"400": model.OrderStatusSubmitFailed + " (下单失败)",
			"102": model.OrderStatusAssigned + " (分配网点)",
			"103": model.OrderStatusAssigned + " (分配快递员)",
			"104": model.OrderStatusPickedUp + " (已取件)",
			"301": model.OrderStatusBilled + " (计费/已揽件)",
			"208": model.OrderStatusWeightUpdated + " (更新重量)",
			"203": model.OrderStatusCancelled + " (取消订单)",
			"204": model.OrderStatusPickupFailed + " (揽收失败)",
			"205": model.OrderStatusVoided + " (作废)",
			"2":   model.OrderStatusInTransit + " (在途中)",
			"3":   model.OrderStatusDelivered + " (签收)",
			"500": model.OrderStatusException + " (异常)",
			"501": model.OrderStatusForwarded + " (已转寄)",
		},
		"yida_mappings": map[string]interface{}{
			"1":  model.OrderStatusAwaitingPickup + " (待取件)",
			"11": model.OrderStatusPickedUp + " (已取件)",
			"2":  model.OrderStatusInTransit + " (运输中)",
			"3":  model.OrderStatusDelivered + " (已签收)",
			"6":  model.OrderStatusException + " (异常)",
			"10": model.OrderStatusCancelled + " (已取消)",
		},
		"kuaidi100_mappings": map[string]interface{}{
			"0":   model.OrderStatusSubmitted + " (下单成功)",
			"1":   model.OrderStatusAssigned + " (已接单)",
			"2":   model.OrderStatusAwaitingPickup + " (收件中)",
			"9":   model.OrderStatusCancelled + " (用户主动取消)",
			"10":  model.OrderStatusPickedUp + " (已取件)",
			"11":  model.OrderStatusPickupFailed + " (揽货失败)",
			"12":  model.OrderStatusReturned + " (已退回)",
			"13":  model.OrderStatusDelivered + " (已签收)",
			"14":  model.OrderStatusDeliveredAbnormal + " (异常签收)",
			"15":  model.OrderStatusBilled + " (已结算)",
			"99":  model.OrderStatusCancelled + " (订单已取消)",
			"101": model.OrderStatusInTransit + " (运输中)",
			"155": model.OrderStatusWeightUpdated + " (修改重量)",
			"166": model.OrderStatusRevived + " (订单复活)",
			"200": model.OrderStatusSubmitted + " (已出单)",
			"201": model.OrderStatusPrintFailed + " (出单失败)",
			"400": model.OrderStatusOutForDelivery + " (派送中)",
			"610": model.OrderStatusSubmitFailed + " (下单失败)",
		},
	}
}
