package callback

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
)

// UnifiedCallbackBuilder 统一回调构建器
// 🔥 重新设计：完全统一的回调格式，供应商无关
// 遵循第一性原理：所有供应商的回调都转换为统一格式
type UnifiedCallbackBuilder struct {
	systemConfigService service.SystemConfigService
	logger              *zap.Logger
}

// NewUnifiedCallbackBuilder 创建统一回调构建器
func NewUnifiedCallbackBuilder(systemConfigService service.SystemConfigService, logger *zap.Logger) *UnifiedCallbackBuilder {
	return &UnifiedCallbackBuilder{
		systemConfigService: systemConfigService,
		logger:              logger,
	}
}

// BuildCallbackData 构建统一的回调数据
// 遵循SOLID原则：单一职责，只负责数据转换
func (b *UnifiedCallbackBuilder) BuildCallbackData(ctx context.Context, data *model.StandardizedCallbackData) (*model.UnifiedCallbackData, error) {
	if data == nil {
		return nil, fmt.Errorf("输入数据不能为空")
	}

	// 构建基础回调数据
	callbackData := &model.UnifiedCallbackData{
		CustomerOrderNo: data.CustomerOrderNo, // 🔥 新增：客户订单号
		OrderNo:         data.OrderNo,         // 🔥 保持：供应商订单号（向后兼容）
		PlatformOrderNo: data.PlatformOrderNo, // 🔥 新增：平台订单号（明确标识）
		TrackingNo:      data.TrackingNo,
		Timestamp:       data.Timestamp.Format(time.RFC3339),
	}

	// 🔥 新增：根据配置决定是否包含供应商字段
	includeSupplier, err := b.systemConfigService.GetBoolConfig(ctx, "callback", "include_supplier_code", false)
	if err != nil {
		b.logger.Warn("获取供应商字段配置失败，使用默认值false",
			zap.Error(err))
		includeSupplier = false
	}

	if includeSupplier && data.Provider != "" {
		callbackData.SupplierCode = data.Provider
		b.logger.Debug("回调数据中包含供应商字段",
			zap.String("supplier_code", data.Provider),
			zap.String("order_no", data.OrderNo))
	}

	// 🔥 调试日志：记录构建的基础回调数据
	b.logger.Debug("构建基础回调数据",
		zap.String("order_no", callbackData.OrderNo),
		zap.String("platform_order_no", callbackData.PlatformOrderNo),
		zap.String("customer_order_no", callbackData.CustomerOrderNo),
		zap.String("tracking_no", callbackData.TrackingNo),
		zap.String("supplier_code", callbackData.SupplierCode),
		zap.Bool("include_supplier", includeSupplier))

	// 🔥 修复：根据字符串事件类型构建不同的数据
	switch data.EventType {
	case model.EventTypeOrderStatusChanged:
		return b.buildStatusUpdateCallback(callbackData, data)
	case model.EventTypeBillingUpdated:
		return b.buildBillingCallback(callbackData, data)
	case model.EventTypeTicketReplied:
		return b.buildExceptionCallback(callbackData, data)
	default:
		return b.buildStatusUpdateCallback(callbackData, data) // 默认为状态更新
	}
}

// buildStatusUpdateCallback 构建状态更新回调
// 🔥 核心改进：智能判断是否为揽收事件
func (b *UnifiedCallbackBuilder) buildStatusUpdateCallback(callbackData *model.UnifiedCallbackData, data *model.StandardizedCallbackData) (*model.UnifiedCallbackData, error) {
	statusData, ok := data.Data.(*model.OrderStatusChangedData)
	if !ok {
		return nil, fmt.Errorf("无效的状态变更数据类型")
	}

	// 智能判断事件类型
	if b.isPickupEvent(statusData) {
		return b.buildPickupCallback(callbackData, statusData)
	}

	// 构建状态更新事件
	callbackData.EventType = model.UnifiedEventTypeStatusUpdate
	callbackData.Data = &model.UnifiedStatusData{
		Status: model.UnifiedStatus{
			Code: statusData.NewStatus,
			Name: statusData.StatusDesc,
		},
		UpdateTime:  statusData.UpdateTime.Format(time.RFC3339),
		Description: statusData.StatusDesc,
	}

	return callbackData, nil
}

// buildPickupCallback 构建揽收回调
func (b *UnifiedCallbackBuilder) buildPickupCallback(callbackData *model.UnifiedCallbackData, statusData *model.OrderStatusChangedData) (*model.UnifiedCallbackData, error) {
	// 🔥 修复：增强快递员信息提取
	var courier model.UnifiedCourier
	var pickupCode string

	if statusData.CourierInfo != nil {
		courier = model.UnifiedCourier{
			Name:  statusData.CourierInfo.Name,
			Phone: statusData.CourierInfo.Phone,
		}

		// 添加站点信息
		if statusData.CourierInfo.Station != "" {
			courier.Station = &model.UnifiedStation{
				Name: statusData.CourierInfo.Station,
			}
		}

		// 从CourierInfo中提取取件码
		if statusData.CourierInfo.PickupCode != "" {
			pickupCode = statusData.CourierInfo.PickupCode
		}
	}

	// 🔥 修复：从Extra字段中提取额外的快递员信息
	if statusData.Extra != nil {
		// 如果CourierInfo中没有快递员信息，尝试从Extra中提取
		if courier.Name == "" {
			if name, ok := statusData.Extra["courier_name"].(string); ok {
				courier.Name = name
			}
		}
		if courier.Phone == "" {
			if phone, ok := statusData.Extra["courier_mobile"].(string); ok {
				courier.Phone = phone
			}
		}
		if courier.Station == nil {
			if station, ok := statusData.Extra["courier_company"].(string); ok {
				courier.Station = &model.UnifiedStation{
					Name: station,
				}
			}
		}
		if pickupCode == "" {
			if code, ok := statusData.Extra["got_code"].(string); ok {
				pickupCode = code
			}
		}
	}

	// 🔥 修复：如果没有快递员信息，则不构建揽收回调
	if courier.Name == "" && courier.Phone == "" {
		return nil, fmt.Errorf("揽收事件缺少快递员信息")
	}

	callbackData.EventType = model.UnifiedEventTypePickup
	callbackData.Data = &model.UnifiedPickupData{
		Courier:    courier,
		PickupCode: pickupCode,
		PickupTime: statusData.UpdateTime.Format(time.RFC3339),
	}

	return callbackData, nil
}

// buildBillingCallback 构建计费回调
// 🔥 新设计：统一的计费事件格式
func (b *UnifiedCallbackBuilder) buildBillingCallback(callbackData *model.UnifiedCallbackData, data *model.StandardizedCallbackData) (*model.UnifiedCallbackData, error) {
	billingData, ok := data.Data.(*model.BillingUpdatedData)
	if !ok {
		return nil, fmt.Errorf("无效的计费数据类型")
	}

	callbackData.EventType = model.UnifiedEventTypeBilling

	// 构建统一的费用列表
	fees := make([]model.UnifiedFee, 0, len(billingData.FeeDetails))
	for _, fee := range billingData.FeeDetails {
		unifiedFee := model.UnifiedFee{
			Type:   b.mapFeeType(fee.FeeType),
			Name:   fee.FeeDesc,
			Amount: fee.Amount,
		}
		fees = append(fees, unifiedFee)
	}

	callbackData.Data = &model.UnifiedBillingData{
		Weight: model.UnifiedWeight{
			Actual:  billingData.Weight,
			Charged: billingData.ChargedWeight,
			Volume:  billingData.Volume,
		},
		PackageCount: billingData.PackageCount,
		Fees:         fees,
		TotalAmount:  billingData.TotalFee,
		BillingTime:  billingData.UpdateTime.Format(time.RFC3339),
	}

	return callbackData, nil
}

// buildExceptionCallback 构建异常回调
// 🔥 新设计：统一的异常事件格式
func (b *UnifiedCallbackBuilder) buildExceptionCallback(callbackData *model.UnifiedCallbackData, data *model.StandardizedCallbackData) (*model.UnifiedCallbackData, error) {
	ticketData, ok := data.Data.(*model.TicketRepliedData)
	if !ok {
		return nil, fmt.Errorf("无效的工单数据类型")
	}

	callbackData.EventType = model.UnifiedEventTypeException
	callbackData.Data = &model.UnifiedExceptionData{
		Type:        ticketData.TicketType,
		Description: ticketData.Reply,
		OccurTime:   ticketData.ReplyTime.Format(time.RFC3339),
		Solution:    ticketData.Status,
	}

	return callbackData, nil
}

// isPickupEvent 判断是否为揽收事件
// 🔥 修复：支持从Extra字段中提取快递员信息
func (b *UnifiedCallbackBuilder) isPickupEvent(statusData *model.OrderStatusChangedData) bool {
	hasPickupStatus := false
	hasCourierInfo := false

	// 检查状态是否是揽收相关
	pickupStatuses := []string{
		"assigned",        // 已分配快递员
		"awaiting_pickup", // 等待揽收
		"picked_up",       // 已取件
		"collected",       // 已揽收
	}

	for _, status := range pickupStatuses {
		if statusData.NewStatus == status {
			hasPickupStatus = true
			break
		}
	}

	// 🔥 修复：优先从CourierInfo中检查快递员信息
	if statusData.CourierInfo != nil {
		if statusData.CourierInfo.Name != "" || statusData.CourierInfo.Phone != "" {
			hasCourierInfo = true
		}
	}

	// 🔥 修复：如果CourierInfo中没有快递员信息，从Extra字段中检查
	if !hasCourierInfo && statusData.Extra != nil {
		courierName, hasName := statusData.Extra["courier_name"].(string)
		courierMobile, hasMobile := statusData.Extra["courier_mobile"].(string)
		courierPhone, hasPhone := statusData.Extra["courier_phone"].(string)

		if (hasName && courierName != "") ||
			(hasMobile && courierMobile != "") ||
			(hasPhone && courierPhone != "") {
			hasCourierInfo = true
		}
	}

	// 🔥 修复：对于菜鸟的特殊事件类型直接判断为揽收事件
	if statusData.Extra != nil {
		if eventType, ok := statusData.Extra["event_type"].(string); ok {
			if eventType == "SEEK_DELIVERY_SUCCESS" || eventType == "OUT_ORDER_COURIER_UPDATE" {
				return hasCourierInfo // 有快递员信息就是揽收事件
			}
		}
	}

	// 必须同时满足：有快递员信息且状态为揽收相关
	return hasPickupStatus && hasCourierInfo
}

// mapFeeType 映射费用类型到统一标准
// 🔥 统一标准：使用语义化的费用类型
func (b *UnifiedCallbackBuilder) mapFeeType(feeType string) string {
	// 统一转换为小写，去除空格
	feeType = strings.ToLower(strings.TrimSpace(feeType))

	feeTypeMap := map[string]string{
		// 运费 & 基本费用
		"freight": "freight",

		// 保价相关
		"insurance":        "insurance",
		"insurancefee":     "insurance",
		"fullinsurancefee": "insurance",

		// 耗材 / 包装
		"package":      "packaging",
		"packaging":    "packaging",
		"packagingfee": "packaging",

		// 燃油 / 附加费
		"fuel":                        "fuel",
		"festival":                    "fuel", // 春节加派费视作燃油附加费
		"surcharge":                   "fuel",
		"resourceconditioningcharges": "fuel",

		// 上门取件 & 送货上门
		"pickup":             "pickup",
		"pickupfee":          "pickup",
		"pickupsservicefee":  "pickup",
		"pickupservicefee":   "pickup",
		"delivery":           "delivery",
		"deliveryfee":        "delivery",
		"deliveryservicefee": "delivery",

		// 代收货款
		"cod":           "cod",
		"collectionfee": "cod",

		// 逆向 / 退回
		"reverse":    "return",
		"return":     "return",
		"return_fee": "return",
		"returnfee":  "return",

		// 超区 / 超长超重
		"overweight":              "other", // 仍归类为other，前端可自定义展示
		"underweight":             "other",
		"overlengthoverweightfee": "other",
		"exceededareafee":         "other",

		// 其他直接归类
		"handlingcharges":             "other",
		"signandreturn":               "other",
		"specialwarehousingfee":       "other",
		"animalquarantinecertificate": "other",
		"expresscompensation":         "other",
		"receivedeliveryfeeclaims":    "other",
		"expressaddrchangefee":        "other",
		"freshfee":                    "other",
		"other":                       "other",
	}

	if mappedType, ok := feeTypeMap[feeType]; ok {
		return mappedType
	}

	return "other" // 未识别的费用统一归类为其他
}
