package callback

import (
	"fmt"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
)

// YidaStyleCallbackBuilder 易达风格回调数据构建器
// 遵循单一职责原则：专门负责构建易达风格的回调数据
type YidaStyleCallbackBuilder struct{}

// NewYidaStyleCallbackBuilder 创建易达风格回调数据构建器
func NewYidaStyleCallbackBuilder() *YidaStyleCallbackBuilder {
	return &YidaStyleCallbackBuilder{}
}

// BuildCallbackData 构建易达风格的回调数据
// 遵循开闭原则：通过接口扩展，不修改现有代码
func (b *YidaStyleCallbackBuilder) BuildCallbackData(data *model.StandardizedCallbackData) (*model.YidaStyleCallbackData, error) {
	// 基础数据
	callbackData := &model.YidaStyleCallbackData{
		OrderNo:         data.OrderNo,
		CustomerOrderNo: data.CustomerOrderNo,
		TrackingNo:      data.TrackingNo,
		Provider:        data.Provider,
		Timestamp:       data.Timestamp.Format(time.RFC3339),
	}

	// 根据事件类型构建不同的推送数据
	switch data.EventType {
	case model.EventTypeOrderStatusChanged:
		return b.buildStatusUpdateCallback(callbackData, data)
	case model.EventTypeBillingUpdated:
		return b.buildBillingCallback(callbackData, data)
	case model.EventTypeTicketReplied:
		return b.buildExceptionCallback(callbackData, data)
	// 🔥 新增：支持更多事件类型
	case "order_changed":
		return b.buildOrderChangeCallback(callbackData, data)
	default:
		return nil, fmt.Errorf("不支持的事件类型: %s", data.EventType)
	}
}

// buildStatusUpdateCallback 构建状态更新回调
// 遵循DRY原则：提取公共的状态处理逻辑
func (b *YidaStyleCallbackBuilder) buildStatusUpdateCallback(callbackData *model.YidaStyleCallbackData, data *model.StandardizedCallbackData) (*model.YidaStyleCallbackData, error) {
	statusData, ok := data.Data.(*model.OrderStatusChangedData)
	if !ok {
		return nil, fmt.Errorf("无效的状态变更数据类型")
	}

	// 🔥 修复：智能判断推送类型
	pushType := b.determinePushType(statusData)

	switch pushType {
	case model.PushTypePickup:
		return b.buildPickupCallback(callbackData, statusData)
	case model.PushTypeStatusUpdate:
		// 构建状态更新推送
		callbackData.PushType = model.PushTypeStatusUpdate
	default:
		// 默认为状态更新推送
		callbackData.PushType = model.PushTypeStatusUpdate
	}
	callbackData.Context = &model.StatusUpdateContext{
		Status: model.StatusInfo{
			Code:        statusData.NewStatus,
			Name:        statusData.StatusDesc,
			Description: statusData.StatusDesc,
		},
		UpdateTime: statusData.UpdateTime.Format(time.RFC3339),
		ProviderStatus: b.buildProviderStatus(statusData.Extra),
		Extra:          b.cleanExtra(statusData.Extra),
	}

	return callbackData, nil
}

// buildPickupCallback 构建揽收推送
// 遵循KISS原则：保持简单直接的逻辑
// 🔥 修复：添加完善的空值检查
func (b *YidaStyleCallbackBuilder) buildPickupCallback(callbackData *model.YidaStyleCallbackData, statusData *model.OrderStatusChangedData) (*model.YidaStyleCallbackData, error) {
	// 安全检查
	if statusData.CourierInfo == nil {
		return nil, fmt.Errorf("揽收推送缺少快递员信息")
	}

	callbackData.PushType = model.PushTypePickup

	// 构建快递员信息
	courier := model.CourierDetail{
		Name:  statusData.CourierInfo.Name,
		Phone: statusData.CourierInfo.Phone,
	}

	// 添加站点信息（如果有）
	if statusData.CourierInfo.Station != "" {
		courier.Station = &model.StationInfo{
			Name: statusData.CourierInfo.Station,
		}
	}

	// 提取取件码
	pickupCode := ""
	if statusData.Extra != nil {
		if code, ok := statusData.Extra["pickup_code"].(string); ok {
			pickupCode = code
		}
	}

	callbackData.Context = &model.PickupContext{
		Courier:    courier,
		PickupCode: pickupCode,
		PickupTime: statusData.UpdateTime.Format(time.RFC3339),
	}

	return callbackData, nil
}

// buildBillingCallback 构建计费推送
// 遵循单一职责原则：专门处理计费数据
func (b *YidaStyleCallbackBuilder) buildBillingCallback(callbackData *model.YidaStyleCallbackData, data *model.StandardizedCallbackData) (*model.YidaStyleCallbackData, error) {
	billingData, ok := data.Data.(*model.BillingUpdatedData)
	if !ok {
		return nil, fmt.Errorf("无效的计费数据类型")
	}

	callbackData.PushType = model.PushTypeBilling

	// 构建费用列表
	fees := make([]model.FeeInfo, 0, len(billingData.FeeDetails))
	for _, fee := range billingData.FeeDetails {
		feeInfo := model.FeeInfo{
			Name:   fee.FeeDesc,
			Amount: fee.Amount,
		}

		// 映射费用类型
		feeInfo.Type = b.mapFeeType(fee.FeeType)
		fees = append(fees, feeInfo)
	}

	callbackData.Context = &model.BillingContext{
		Weight: model.WeightInfo{
			Actual:  billingData.Weight,
			Charged: billingData.ChargedWeight,
			Volume:  billingData.Volume,
		},
		PackageCount: billingData.PackageCount,
		Fees:         fees,
		TotalFee:     billingData.TotalFee,
		UpdateTime:   billingData.UpdateTime.Format(time.RFC3339),
	}

	return callbackData, nil
}

// buildExceptionCallback 构建异常推送
// 遵循YAGNI原则：只实现当前需要的功能
func (b *YidaStyleCallbackBuilder) buildExceptionCallback(callbackData *model.YidaStyleCallbackData, data *model.StandardizedCallbackData) (*model.YidaStyleCallbackData, error) {
	ticketData, ok := data.Data.(*model.TicketRepliedData)
	if !ok {
		return nil, fmt.Errorf("无效的工单数据类型")
	}

	callbackData.PushType = model.PushTypeException
	callbackData.Context = &model.ExceptionContext{
		ExceptionType: ticketData.TicketType,
		Description:   ticketData.Reply,
		OccurredTime:  ticketData.ReplyTime.Format(time.RFC3339),
		Resolution:    ticketData.Status,
	}

	return callbackData, nil
}

// buildOrderChangeCallback 构建订单变更推送
// 🔥 新增：支持订单变更推送 (push_type: 5)
func (b *YidaStyleCallbackBuilder) buildOrderChangeCallback(callbackData *model.YidaStyleCallbackData, data *model.StandardizedCallbackData) (*model.YidaStyleCallbackData, error) {
	// 这里可以根据实际的订单变更数据结构来实现
	// 目前先提供一个基础实现
	callbackData.PushType = model.PushTypeOrderChange

	// 从Extra字段中提取变更信息
	changeType := "status_change" // 默认为状态变更
	changeReason := ""

	if statusData, ok := data.Data.(*model.OrderStatusChangedData); ok {
		if statusData.Extra != nil {
			if reason, ok := statusData.Extra["change_reason"].(string); ok {
				changeReason = reason
			}
			if cType, ok := statusData.Extra["change_type"].(string); ok {
				changeType = cType
			}
		}
	}

	callbackData.Context = &model.OrderChangeContext{
		ChangeType:   changeType,
		ChangeTime:   data.Timestamp.Format(time.RFC3339),
		ChangeReason: changeReason,
	}

	return callbackData, nil
}

// isPickupStatus 判断是否为揽收状态
// 遵循DRY原则：避免重复的状态判断逻辑
// 🔥 修复：完整覆盖所有需要生成揽收推送的状态
func (b *YidaStyleCallbackBuilder) isPickupStatus(status string) bool {
	pickupStatuses := []string{
		"assigned",        // 已分配快递员
		"awaiting_pickup", // 等待揽收
		"picked_up",       // 已取件
		"collected",       // 已揽收（别名）
	}

	for _, pickupStatus := range pickupStatuses {
		if status == pickupStatus {
			return true
		}
	}
	return false
}

// determinePushType 智能判断推送类型
// 🔥 新增：根据状态和数据内容智能判断推送类型
func (b *YidaStyleCallbackBuilder) determinePushType(statusData *model.OrderStatusChangedData) int {
	// 1. 如果有快递员信息且是揽收相关状态，生成揽收推送
	if statusData.CourierInfo != nil &&
	   (statusData.CourierInfo.Name != "" || statusData.CourierInfo.Phone != "") &&
	   b.isPickupStatus(statusData.NewStatus) {
		return model.PushTypePickup
	}

	// 2. 如果状态是计费相关，但这里应该由计费事件处理，所以返回状态更新
	// 计费推送由 EventTypeBillingUpdated 事件触发

	// 3. 如果状态是异常相关，但这里应该由工单事件处理，所以返回状态更新
	// 异常推送由 EventTypeTicketReplied 事件触发

	// 4. 默认返回状态更新推送
	return model.PushTypeStatusUpdate
}

// buildProviderStatus 构建供应商状态信息
func (b *YidaStyleCallbackBuilder) buildProviderStatus(extra map[string]interface{}) *model.ProviderStatusInfo {
	if extra == nil {
		return nil
	}

	providerStatus := &model.ProviderStatusInfo{}
	
	if code, ok := extra["provider_status"]; ok {
		providerStatus.Code = code
	}
	
	if desc, ok := extra["provider_desc"].(string); ok {
		providerStatus.Description = desc
	}

	// 如果没有有效信息，返回nil
	if providerStatus.Code == nil && providerStatus.Description == "" {
		return nil
	}

	return providerStatus
}

// cleanExtra 清理Extra字段，移除无用信息
// 遵循KISS原则：只保留有用的信息
func (b *YidaStyleCallbackBuilder) cleanExtra(extra map[string]interface{}) map[string]interface{} {
	if extra == nil {
		return nil
	}

	cleaned := make(map[string]interface{})
	
	// 只保留有价值的字段
	valuableFields := []string{
		"comments",
		"operate_type", 
		"raw_status",
		"pickup_code",
		"site_name",
	}

	for _, field := range valuableFields {
		if value, ok := extra[field]; ok && value != "" && value != nil {
			cleaned[field] = value
		}
	}

	if len(cleaned) == 0 {
		return nil
	}

	return cleaned
}

// mapFeeType 映射费用类型到易达标准
// 遵循开闭原则：通过映射表扩展，不修改核心逻辑
func (b *YidaStyleCallbackBuilder) mapFeeType(feeType string) int {
	feeTypeMap := map[string]int{
		"freight":   0,   // 运费
		"insurance": 1,   // 保价费
		"festival":  2,   // 春节费
		"package":   3,   // 耗材费
		"reverse":   10,  // 逆向费
		"other":     100, // 其他费
	}

	if mappedType, ok := feeTypeMap[feeType]; ok {
		return mappedType
	}

	return 100 // 默认为其他费用
}
