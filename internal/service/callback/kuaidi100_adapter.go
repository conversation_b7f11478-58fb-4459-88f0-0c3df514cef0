package callback

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"

	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// Kuaidi100CallbackAdapter 快递100回调适配器 - 符合官方规范
type Kuaidi100CallbackAdapter struct {
	salt   string
	logger *zap.Logger
}

// NewKuaidi100CallbackAdapter 创建快递100回调适配器
func NewKuaidi100CallbackAdapter(salt string) ProviderCallbackAdapter {
	logger, _ := zap.NewProduction()
	return &Kuaidi100CallbackAdapter{
		salt:   salt,
		logger: logger,
	}
}

// ValidateSignature 验证快递100签名 - 符合接口规范
func (a *Kuaidi100CallbackAdapter) ValidateSignature(rawData []byte, headers map[string]string) error {
	// 解析HTTP表单数据
	formData, err := url.ParseQuery(string(rawData))
	if err != nil {
		return fmt.Errorf("解析HTTP表单数据失败: %w", err)
	}

	sign := formData.Get("sign")
	param := formData.Get("param")

	if sign == "" || param == "" {
		return fmt.Errorf("缺少必要的签名参数 sign 或 param")
	}

	if a.salt == "" {
		// 如果没有配置salt，跳过签名验证
		return nil
	}

	// 计算签名：MD5(param + salt)
	expectedSign := fmt.Sprintf("%x", md5.Sum([]byte(param+a.salt)))
	expectedSign = strings.ToUpper(expectedSign)

	if sign != expectedSign {
		return fmt.Errorf("签名验证失败: 期望 %s, 实际 %s", expectedSign, sign)
	}

	return nil
}

// ParseCallback 解析快递100回调数据 - 符合接口规范
func (a *Kuaidi100CallbackAdapter) ParseCallback(rawData []byte) (*model.ParsedCallbackData, error) {
	a.logger.Info("开始解析快递100回调数据", zap.Int("data_size", len(rawData)))

	// 解析HTTP表单数据
	formData, err := url.ParseQuery(string(rawData))
	if err != nil {
		a.logger.Error("解析HTTP表单数据失败", zap.Error(err), zap.String("raw_data", string(rawData)))
		return nil, fmt.Errorf("解析HTTP表单数据失败: %w", err)
	}

	// 提取顶级字段
	taskId := formData.Get("taskId") // 订单回调有taskId，轨迹推送回调没有
	sign := formData.Get("sign")
	param := formData.Get("param")

	// sign和param是必填的
	if sign == "" {
		return nil, fmt.Errorf("签名(sign)不能为空")
	}
	if param == "" {
		return nil, fmt.Errorf("参数(param)不能为空")
	}

	// 解码param参数
	paramData, err := url.QueryUnescape(param)
	if err != nil {
		return nil, fmt.Errorf("解码快递100 param参数失败: %w", err)
	}

	// 首先尝试解析为轨迹推送格式
	var trackingStruct struct {
		Status     string `json:"status"`     // polling, shutdown, abort
		Billstatus string `json:"billstatus"` // got, check
		Message    string `json:"message"`    // 消息
		LastResult struct {
			Message string `json:"message"`
			Nu      string `json:"nu"`      // 运单号
			IsCheck string `json:"ischeck"` // 是否签收
			Com     string `json:"com"`     // 快递公司
			State   string `json:"state"`   // 状态
			Data    []struct {
				Context    string `json:"context"`    // 轨迹描述
				Time       string `json:"time"`       // 时间
				Ftime      string `json:"ftime"`      // 格式化时间
				Status     string `json:"status"`     // 状态描述
				AreaCode   string `json:"areaCode"`   // 区域代码
				AreaName   string `json:"areaName"`   // 区域名称
				StatusCode string `json:"statusCode"` // 状态代码
			} `json:"data"`
		} `json:"lastResult"`
	}

	// 尝试解析为轨迹推送
	if err := json.Unmarshal([]byte(paramData), &trackingStruct); err == nil && trackingStruct.Status != "" && trackingStruct.LastResult.Nu != "" {
		// 这是轨迹推送回调
		a.logger.Info("识别为快递100轨迹推送回调",
			zap.String("status", trackingStruct.Status),
			zap.String("tracking_no", trackingStruct.LastResult.Nu))
		return a.parseTrackingCallback(taskId, trackingStruct)
	}

	// 解析为订单回调格式
	var paramStruct struct {
		Kuaidicom string `json:"kuaidicom"` // 必填：快递公司编码
		Kuaidinum string `json:"kuaidinum"` // 必填：快递单号
		Status    string `json:"status"`    // 必填：状态 (string type for "polling", "abort", etc.)
		Message   string `json:"message"`   // 消息
		Data      struct {
			OrderId       string `json:"orderId"`       // 必填：平台订单ID
			Status        int    `json:"status"`        // 必填：订单状态 (int type for 0, 1, 10 etc.)
			SentStatus    int    `json:"sentStatus"`    // 🔥 新增：发送状态
			CancelMsg9    string `json:"cancelMsg9"`    // 取消原因
			CancelMsg99   string `json:"cancelMsg99"`   // 取消原因
			CourierName   string `json:"courierName"`   // 快递员姓名
			CourierMobile string `json:"courierMobile"` // 快递员电话
			NetTel        string `json:"netTel"`        // 网点电话
			NetCode       string `json:"netCode"`       // 网点编码
			Weight        string `json:"weight"`        // 重量
			DefPrice      string `json:"defPrice"`      // 预估价格
			Freight       string `json:"freight"`       // 运费
			Volume        string `json:"volume"`        // 体积
			ActualWeight  string `json:"actualWeight"`  // 实际重量
			VolumeWeight  string `json:"volumeWeight"`  // 🔥 新增：体积重量
			ServiceType   string `json:"serviceType"`   // 🔥 新增：服务类型
			FirstWeight   string `json:"firstWeight"`   // 🔥 新增：首重
			FirstPrice    string `json:"firstPrice"`    // 🔥 新增：首重价格
			OverWeight    string `json:"overWeight"`    // 🔥 新增：超重
			OfficalPrice  string `json:"officalPrice"`  // 🔥 新增：官方价格
			OverPriceUnit string `json:"overPriceUnit"` // 🔥 新增：超重价格单位
			ThirdOrderId  string `json:"thirdOrderId"`  // 🔥 新增：第三方订单ID
			PayType       string `json:"payType"`       // 🔥 新增：支付类型
			PayStatus     int    `json:"payStatus"`     // 🔥 新增：支付状态
			UserCancel    bool   `json:"userCancel"`    // 🔥 新增：用户取消
			Comment       string `json:"comment"`       // 🔥 新增：备注
			UpdateTime    int64  `json:"updateTime"`    // 🔥 新增：更新时间
			ExtraInfo     string `json:"extraInfo"`     // 🔥 新增：额外信息
			FeeDetails    []struct {
				FeeType     string      `json:"feeType"`     // 费用类型
				FeeDesc     string      `json:"feeDesc"`     // 费用描述
				Amount      string      `json:"amount"`      // 金额
				PayStatus   interface{} `json:"payStatus"`   // 支付状态（可能是string或int）
				PayTaskId   string      `json:"payTaskId"`   // 支付任务ID
				OrderBaseId interface{} `json:"orderBaseId"` // 订单基础ID（可能是string或int）
			} `json:"feeDetails"` // 费用明细
			PrintTaskId string `json:"printTaskId"` // 打印任务ID
			Label       string `json:"label"`       // 电子面单
			PickupCode  string `json:"pickupCode"`  // 取件码
			PollToken   string `json:"pollToken"`   // 必填：轮询令牌
		} `json:"data"` // 必填: data object itself
	}

	if err := json.Unmarshal([]byte(paramData), &paramStruct); err != nil {
		a.logger.Error("解析快递100 param JSON数据失败", zap.Error(err), zap.String("param_data", paramData))
		return nil, fmt.Errorf("解析快递100 param JSON数据失败: %w", err)
	}

	// 验证param中的必填字段
	if paramStruct.Kuaidicom == "" {
		return nil, fmt.Errorf("快递公司编码(kuaidicom)不能为空")
	}
	if paramStruct.Kuaidinum == "" {
		return nil, fmt.Errorf("快递单号(kuaidinum)不能为空")
	}
	if paramStruct.Status == "" {
		return nil, fmt.Errorf("状态(param.status)不能为空")
	}

	// Validate param.data and its required fields
	if paramStruct.Data.OrderId == "" {
		return nil, fmt.Errorf("平台订单ID(param.data.orderId)不能为空")
	}
	if paramStruct.Data.PollToken == "" {
		return nil, fmt.Errorf("查询密钥(param.data.pollToken)不能为空")
	}

	// 快递100的订单回调统一处理为订单状态变更
	// 具体的事件类型在标准化阶段根据状态码和数据内容确定
	callbackType := constants.CallbackTypeOrderCallback

	a.logger.Info("成功解析快递100订单回调",
		zap.String("callback_type", callbackType),
		zap.String("order_id", paramStruct.Data.OrderId),
		zap.String("tracking_no", paramStruct.Kuaidinum),
		zap.Int("status", paramStruct.Data.Status),
		zap.String("express_company", paramStruct.Kuaidicom),
		zap.String("poll_token", paramStruct.Data.PollToken))

	// 🔥 新增：详细的调试日志，记录关键映射信息
	a.logger.Debug("快递100回调数据映射详情",
		zap.String("原始_order_id", paramStruct.Data.OrderId),
		zap.String("映射到_order_no", paramStruct.Data.OrderId),
		zap.String("运单号", paramStruct.Kuaidinum),
		zap.String("快递公司", paramStruct.Kuaidicom),
		zap.String("状态", paramStruct.Status),
		zap.Int("数据状态", paramStruct.Data.Status))

	return &model.ParsedCallbackData{
		Type:            callbackType,
		OrderNo:         paramStruct.Data.OrderId, // 🔥 修复：快递100的orderId实际是平台订单号(order_no)
		CustomerOrderNo: "",                       // CustomerOrderNo留空，由统一回调服务查找
		TrackingNo:      paramStruct.Kuaidinum,
		Data:            paramStruct.Data,
		Timestamp:       util.NowBeijing(),
	}, nil
}

// parseTrackingCallback 解析轨迹推送回调
func (a *Kuaidi100CallbackAdapter) parseTrackingCallback(_ string, trackingData interface{}) (*model.ParsedCallbackData, error) {
	// 将interface{}转换为具体的轨迹数据结构
	trackingBytes, err := json.Marshal(trackingData)
	if err != nil {
		return nil, fmt.Errorf("序列化轨迹数据失败: %w", err)
	}

	var trackingStruct struct {
		Status     string `json:"status"`     // polling, shutdown, abort
		Billstatus string `json:"billstatus"` // got, check
		Message    string `json:"message"`    // 消息
		LastResult struct {
			Message string `json:"message"`
			Nu      string `json:"nu"`      // 运单号
			IsCheck string `json:"ischeck"` // 是否签收
			Com     string `json:"com"`     // 快递公司
			State   string `json:"state"`   // 状态
			Data    []struct {
				Context    string `json:"context"`    // 轨迹描述
				Time       string `json:"time"`       // 时间
				Ftime      string `json:"ftime"`      // 格式化时间
				Status     string `json:"status"`     // 状态描述
				AreaCode   string `json:"areaCode"`   // 区域代码
				AreaName   string `json:"areaName"`   // 区域名称
				StatusCode string `json:"statusCode"` // 状态代码
			} `json:"data"`
		} `json:"lastResult"`
	}

	if err := json.Unmarshal(trackingBytes, &trackingStruct); err != nil {
		return nil, fmt.Errorf("解析轨迹推送数据失败: %w", err)
	}

	// 验证必填字段
	if trackingStruct.Status == "" {
		return nil, fmt.Errorf("轨迹推送状态(status)不能为空")
	}
	if trackingStruct.LastResult.Nu == "" {
		return nil, fmt.Errorf("运单号(lastResult.nu)不能为空")
	}

	// 根据状态判断回调类型
	var callbackType string
	switch trackingStruct.Status {
	case constants.Kuaidi100StatusPolling:
		callbackType = constants.CallbackTypeStatusUpdate
	case constants.Kuaidi100StatusShutdown:
		callbackType = constants.CallbackTypeTrackingComplete
	case constants.Kuaidi100StatusAbort:
		callbackType = constants.CallbackTypeTrackingAbort
	default:
		callbackType = constants.CallbackTypeStatusUpdate
	}

	return &model.ParsedCallbackData{
		Type:            callbackType,
		OrderNo:         "",
		CustomerOrderNo: "", // 轨迹推送没有订单号，需要通过运单号查找
		TrackingNo:      trackingStruct.LastResult.Nu,
		Data:            trackingStruct,
		Timestamp:       util.NowBeijing(),
	}, nil
}

// BuildResponse 构建快递100响应 - 符合官方规范
func (a *Kuaidi100CallbackAdapter) BuildResponse(data *model.StandardizedCallbackData) *model.CallbackResponse {
	return &model.CallbackResponse{
		Success: constants.ResponseValueTrue,
		Code:    constants.HTTPStatusOK,
		Message: constants.MessageSuccess,
		Data: map[string]interface{}{
			constants.Kuaidi100ResponseFieldResult:     constants.ResponseValueTrue,
			constants.Kuaidi100ResponseFieldReturnCode: constants.HTTPStatusOK,
			constants.Kuaidi100ResponseFieldMessage:    constants.MessageSuccess,
		},
	}
}
