package callback

import (
	"context"

	"github.com/shopspring/decimal"

	"github.com/your-org/go-kuaidi/internal/interfaces"
	"github.com/your-org/go-kuaidi/internal/service"
)

// BillingServiceAdapter 计费服务适配器
type BillingServiceAdapter struct {
	billingService service.BillingService
}

func NewBillingServiceAdapter(billingService service.BillingService) interfaces.BillingService {
	return &BillingServiceAdapter{
		billingService: billingService,
	}
}

func (a *BillingServiceAdapter) UpdateOrderBilling(ctx context.Context, req *interfaces.UpdateBillingRequest) error {
	// 转换请求类型
	serviceReq := &service.UpdateBillingRequest{
		OrderNo:       req.OrderNo,
		BillingType:   req.BillingType,
		FreightFee:    req.FreightFee,
		InsuranceFee:  req.InsuranceFee,
		PackageFee:    req.PackageFee,
		PickupFee:     req.Pickup<PERSON>ee,
		DeliveryFee:   req.DeliveryFee,
		CodFee:        req.CodFee,
		OtherFee:      req.OtherFee,
		TotalFee:      req.TotalFee,
		Weight:        req.Weight,
		Volume:        req.Volume,
		ChargedWeight: req.ChargedWeight,
		Provider:      req.Provider,
		Source:        req.Source,
		Reason:        req.Reason,
		RawData:       req.RawData,
	}
	return a.billingService.UpdateOrderBilling(ctx, serviceReq)
}

func (a *BillingServiceAdapter) ProcessBillingDifference(ctx context.Context, orderNo string, actualFee float64, reason string) error {
	return a.billingService.ProcessBillingDifference(ctx, orderNo, actualFee, reason)
}

// BalanceServiceAdapter 余额服务适配器
type BalanceServiceAdapter struct {
	balanceService service.BalanceService
}

func NewBalanceServiceAdapter(balanceService service.BalanceService) interfaces.BalanceService {
	return &BalanceServiceAdapter{
		balanceService: balanceService,
	}
}

func (a *BalanceServiceAdapter) RefundForOrder(ctx context.Context, userID, orderNo string, amount decimal.Decimal) error {
	return a.balanceService.RefundForOrder(ctx, userID, orderNo, amount)
}

func (a *BalanceServiceAdapter) GetOrderNetPayment(ctx context.Context, userID, orderNo, customerOrderNo string) (decimal.Decimal, error) {
	return a.balanceService.GetOrderNetPayment(ctx, userID, orderNo, customerOrderNo)
}
