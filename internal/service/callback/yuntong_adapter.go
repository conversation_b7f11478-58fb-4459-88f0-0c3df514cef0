package callback

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// YuntongCallbackAdapter 云通回调适配器
type YuntongCallbackAdapter struct {
	eBusinessID string
	secretKey   string
}

// NewYuntongCallbackAdapter 创建云通回调适配器
func NewYuntongCallbackAdapter(eBusinessID, secretKey string) ProviderCallbackAdapter {
	return &YuntongCallbackAdapter{
		eBusinessID: eBusinessID,
		secretKey:   secretKey,
	}
}

// ValidateSignature 验证云通签名
func (a *YuntongCallbackAdapter) ValidateSignature(rawData []byte, headers map[string]string) error {
	// 云通回调暂时不验证签名，根据实际需求可以添加
	// 可以通过IP白名单等方式验证
	return nil
}

// ParseCallback 解析云通回调数据 - 强制使用嵌套逻辑
func (a *YuntongCallbackAdapter) ParseCallback(rawData []byte) (*model.ParsedCallbackData, error) {
	// 🔥 企业级修复：强制只使用嵌套格式处理云通回调
	// 根据云通官方文档，所有回调都应该是嵌套格式（包含RequestData字段）
	var nestedData struct {
		RequestData string      `json:"RequestData"`
		DataSign    string      `json:"DataSign"`
		RequestType interface{} `json:"RequestType,omitempty"`
	}

	// 解析嵌套格式
	if err := json.Unmarshal(rawData, &nestedData); err != nil {
		return nil, fmt.Errorf("云通回调数据格式错误，必须包含RequestData字段: %w", err)
	}

	// 验证必须的嵌套字段
	if nestedData.RequestData == "" {
		return nil, fmt.Errorf("云通回调RequestData字段不能为空")
	}

	// 只使用嵌套格式解析
	return a.parseNestedCallback([]byte(nestedData.RequestData), nestedData.RequestType)
}

// parseNestedCallback 解析嵌套的云通回调数据
func (a *YuntongCallbackAdapter) parseNestedCallback(requestData []byte, requestType interface{}) (*model.ParsedCallbackData, error) {
	// 解析时间戳
	timestamp := util.NowBeijing()

	// 确定请求类型
	var reqType string
	if requestType != nil {
		switch v := requestType.(type) {
		case string:
			reqType = v
		case float64:
			reqType = fmt.Sprintf("%.0f", v)
		case int:
			reqType = fmt.Sprintf("%d", v)
		default:
			reqType = constants.YuntongRequestTypeOrderPush // 默认为订单推送
		}
	} else {
		reqType = constants.YuntongRequestTypeOrderPush // 默认为订单推送
	}

	// 根据请求类型解析数据
	switch reqType {
	case constants.YuntongRequestTypeOrderPush: // 订单推送
		return a.parseOrderPush(requestData, timestamp)
	case constants.YuntongRequestTypeBillingPush: // 到付月结计费推送
		return a.parseBillingPush(requestData, timestamp)
	case constants.YuntongRequestTypeTicketReply: // 工单回复推送
		return a.parseTicketReply(requestData, timestamp)
	default:
		return a.parseOrderPush(requestData, timestamp) // 默认按订单推送处理
	}
}

// parseOrderPush 解析订单推送（RequestType=103）
func (a *YuntongCallbackAdapter) parseOrderPush(rawData []byte, timestamp time.Time) (*model.ParsedCallbackData, error) {
	var callbackData struct {
		RequestType interface{} `json:"RequestType,omitempty"` // 🔥 修复：兼容字符串和数字
		PushTime    string      `json:"PushTime"`
		EBusinessID interface{} `json:"EBusinessID"` // 🔥 修复：兼容字符串和数字
		Data        []struct {
			ShipperCode      string  `json:"ShipperCode"`
			OrderCode        string  `json:"OrderCode"`
			LogisticCode     string  `json:"LogisticCode"`
			Reason           string  `json:"Reason"`
			State            int     `json:"State"`
			Comments         string  `json:"comments"`
			CreateTime       string  `json:"CreateTime"`
			OperateType      int     `json:"OperateType"`
			Weight           float64 `json:"Weight"`
			Cost             float64 `json:"Cost"`
			InsureAmount     float64 `json:"InsureAmount"`
			PackageFee       float64 `json:"PackageFee"`
			OtherFee         float64 `json:"OtherFee"`
			TotalFee         float64 `json:"TotalFee"`
			Volume           float64 `json:"Volume"`
			OfficialFee      float64 `json:"OfficialFee"`
			ChargedWeight    float64 `json:"chargedWeight"`
			BackLogisticCode string  `json:"BackLogisticCode"`
			BackFee          float64 `json:"BackFee"`
			Quantity         int     `json:"Quantity"`
			ExpType          string  `json:"ExpType"`
			PayType          int     `json:"PayType"`
			PickerInfo       []struct {
				PersonName     string `json:"PersonName"`
				PersonTel      string `json:"PersonTel"`
				PersonCode     string `json:"PersonCode"`
				StationName    string `json:"StationName"`
				StationCode    string `json:"StationCode"`
				StationAddress string `json:"StationAddress"`
				StationTel     string `json:"StationTel"`
				PickupCode     string `json:"PickupCode"`
			} `json:"PickerInfo"`
			Costing struct {
				Discount    int     `json:"discount"`
				UpperGround int     `json:"upperGround"`
				GroundPrice float64 `json:"groundPrice"`
				RateOfStage float64 `json:"rateOfStage"`
			} `json:"Costing"`
		} `json:"Data"`
		Count int `json:"Count"`
	}

	if err := json.Unmarshal(rawData, &callbackData); err != nil {
		return nil, fmt.Errorf("解析云通订单推送数据失败: %w", err)
	}

	// 🔥 企业级调试：记录实际回调数据结构
	fmt.Printf("🔍 云通回调数据调试 - Count: %d, Data长度: %d\n", callbackData.Count, len(callbackData.Data))
	fmt.Printf("🔍 原始回调数据: %s\n", string(rawData))
	if len(callbackData.Data) > 0 {
		fmt.Printf("🔍 第一个Data元素 - OrderCode: %s, State: %d, PickerInfo长度: %d\n",
			callbackData.Data[0].OrderCode, callbackData.Data[0].State, len(callbackData.Data[0].PickerInfo))
		if len(callbackData.Data[0].PickerInfo) > 0 {
			fmt.Printf("🔍 揽件员信息 - PersonName: %s, PersonTel: %s\n",
				callbackData.Data[0].PickerInfo[0].PersonName, callbackData.Data[0].PickerInfo[0].PersonTel)
		}
	}

	// 🔥 企业级修复：根据官方文档，Count为0的情况可能是特殊推送类型
	// 不应该直接拒绝，而是尝试解析其他可能的数据结构
	if callbackData.Count == 0 && len(callbackData.Data) == 0 {
		// 尝试解析为其他格式的回调数据
		return a.parseSpecialYuntongCallback(rawData, timestamp)
	}
	if len(callbackData.Data) == 0 {
		return nil, fmt.Errorf("云通订单推送数据为空")
	}
	if len(callbackData.Data) != callbackData.Count {
		return nil, fmt.Errorf("云通订单推送Count(%d)与Data数组长度(%d)不匹配", callbackData.Count, len(callbackData.Data))
	}

	orderData := callbackData.Data[0]

	// 验证必填字段
	if orderData.OrderCode == "" {
		return nil, fmt.Errorf("客户订单编号(OrderCode)不能为空")
	}
	if orderData.CreateTime == "" {
		return nil, fmt.Errorf("推送时间(CreateTime)不能为空")
	}
	// State和OperateType是必填字段，但0值是有效的，所以不需要验证非空

	// 根据State确定具体的事件类型
	var eventType string
	switch orderData.State {
	case constants.YuntongStateOrderSuccess: // 下单成功
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStateOrderFailed: // 下单失败
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStateAssignNetwork: // 分配网点
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStateAssignCourier: // 分配快递员
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStatePickedUp: // 已取件
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStateBilling: // 计费
		eventType = model.EventTypeBillingUpdated
	case constants.YuntongStateUpdateWeight: // 更新重量
		eventType = model.EventTypeBillingUpdated
	case constants.YuntongStateCancelOrder: // 取消订单
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStatePickupFailed: // 揽收失败
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStateVoid: // 作废
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStateInTransit: // 在途中
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStateDelivered: // 签收
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStateException: // 异常
		eventType = model.EventTypeOrderStatusChanged
	case constants.YuntongStateForwarded: // 已转寄
		eventType = model.EventTypeOrderStatusChanged
	default:
		eventType = model.EventTypeOrderStatusChanged
	}

	return &model.ParsedCallbackData{
		Type:            eventType,
		OrderNo:         "",                  // 🔥 修复：OrderNo留空，由统一回调服务查找
		CustomerOrderNo: orderData.OrderCode, // 🔥 修复：OrderCode是客户订单号
		TrackingNo:      orderData.LogisticCode,
		Data:            orderData,
		Timestamp:       timestamp,
	}, nil
}

// parseBillingPush 解析到付月结计费推送（RequestType=104）
func (a *YuntongCallbackAdapter) parseBillingPush(rawData []byte, timestamp time.Time) (*model.ParsedCallbackData, error) {
	// 根据官方文档第899-923行，RequestType=104使用Data数组结构
	var callbackData struct {
		RequestType interface{} `json:"RequestType,omitempty"` // 🔥 修复：兼容字符串和数字
		PushTime    string      `json:"PushTime"`
		EBusinessID interface{} `json:"EBusinessID"` // 🔥 修复：兼容字符串和数字
		Data        []struct {
			ShipperCode      string  `json:"ShipperCode"`
			MonthCode        string  `json:"MonthCode"` // 必填
			OrderCode        string  `json:"OrderCode"` // 必填
			LogisticCode     string  `json:"LogisticCode"`
			Reason           string  `json:"Reason"`
			State            int     `json:"State"` // 必填：301=计费，203=取消订单
			Comments         string  `json:"comments"`
			CreateTime       string  `json:"CreateTime"`  // 必填
			OperateType      int     `json:"OperateType"` // 必填
			Weight           float64 `json:"Weight"`
			Cost             float64 `json:"Cost"`
			InsureAmount     float64 `json:"InsureAmount"`
			PackageFee       float64 `json:"PackageFee"`
			OtherFee         float64 `json:"OtherFee"`
			Volume           float64 `json:"Volume"`
			OfficialFee      float64 `json:"OfficialFee"`
			ChargedWeight    float64 `json:"chargedWeight"`
			BackLogisticCode string  `json:"BackLogisticCode"`
			BackFee          float64 `json:"BackFee"`
			Quantity         int     `json:"Quantity"` // 可选
			ExpType          string  `json:"ExpType"`  // 可选
			PayType          int     `json:"PayType"`  // 可选
			// 注意：根据官方示例（第899-923行），Sender和Receiver字段在回调中不存在
		} `json:"Data"`
		Count int `json:"Count"`
	}

	if err := json.Unmarshal(rawData, &callbackData); err != nil {
		return nil, fmt.Errorf("解析云通计费推送数据失败: %w", err)
	}

	// 验证Count字段和Data数组
	if callbackData.Count == 0 {
		return nil, fmt.Errorf("云通计费推送Count不能为0")
	}
	if len(callbackData.Data) == 0 {
		return nil, fmt.Errorf("云通计费推送数据为空")
	}
	if len(callbackData.Data) != callbackData.Count {
		return nil, fmt.Errorf("云通计费推送Count(%d)与Data数组长度(%d)不匹配", callbackData.Count, len(callbackData.Data))
	}

	billingData := callbackData.Data[0]

	// 验证必填字段
	// 注意：根据官方文档示例（第899-923行），MonthCode和ExpType在回调中不是必填的
	if billingData.OrderCode == "" {
		return nil, fmt.Errorf("客户订单编号(OrderCode)不能为空")
	}
	if billingData.CreateTime == "" {
		return nil, fmt.Errorf("推送时间(CreateTime)不能为空")
	}
	// ExpType在官方示例中没有出现，不作为必填字段验证

	return &model.ParsedCallbackData{
		Type:            model.EventTypeBillingUpdated,
		OrderNo:         "",                    // 🔥 修复：OrderNo留空，由统一回调服务查找
		CustomerOrderNo: billingData.OrderCode, // 🔥 修复：OrderCode是客户订单号
		TrackingNo:      billingData.LogisticCode,
		Data:            billingData,
		Timestamp:       timestamp,
	}, nil
}

// parseTicketReply 解析工单回复推送（RequestType=105）
func (a *YuntongCallbackAdapter) parseTicketReply(rawData []byte, timestamp time.Time) (*model.ParsedCallbackData, error) {
	var callbackData struct {
		RequestType interface{} `json:"RequestType,omitempty"` // 🔥 修复：兼容字符串和数字
		PushTime    string      `json:"PushTime"`
		EBusinessID interface{} `json:"EBusinessID"` // 🔥 修复：兼容字符串和数字
		Data        []struct {
			ShipperCode     string   `json:"ShipperCode"`
			OrderCode       string   `json:"OrderCode"`
			LogisticCode    string   `json:"LogisticCode"`
			Reason          string   `json:"Reason"`
			State           int      `json:"State"` // 固定值401
			CreateTime      string   `json:"CreateTime"`
			OperateType     int      `json:"OperateType"`     // 固定值1
			ComplaintNumber int      `json:"ComplaintNumber"` // 修复：改为int类型
			ComplaintType   int      `json:"ComplaintType"`
			ResultType      int      `json:"ResultType"`
			DealResult      string   `json:"dealResult"`
			PicList         []string `json:"PicList"`
		} `json:"Data"`
		Count int `json:"Count"`
	}

	if err := json.Unmarshal(rawData, &callbackData); err != nil {
		return nil, fmt.Errorf("解析云通工单回复数据失败: %w", err)
	}

	// 验证Count字段和Data数组
	if callbackData.Count == 0 {
		return nil, fmt.Errorf("云通工单回复Count不能为0")
	}
	if len(callbackData.Data) == 0 {
		return nil, fmt.Errorf("云通工单回复数据为空")
	}
	if len(callbackData.Data) != callbackData.Count {
		return nil, fmt.Errorf("云通工单回复Count(%d)与Data数组长度(%d)不匹配", callbackData.Count, len(callbackData.Data))
	}

	ticketData := callbackData.Data[0]

	// 验证必填字段和固定值
	if ticketData.ShipperCode == "" {
		return nil, fmt.Errorf("快递公司编码(ShipperCode)不能为空")
	}
	if ticketData.OrderCode == "" {
		return nil, fmt.Errorf("客户订单编号(OrderCode)不能为空")
	}
	if ticketData.State != constants.YuntongStateTicketReply {
		return nil, fmt.Errorf("工单回复State必须为%d，实际为%d", constants.YuntongStateTicketReply, ticketData.State)
	}
	if ticketData.OperateType != constants.YuntongOperateTypeTicketReply {
		return nil, fmt.Errorf("工单回复OperateType必须为%d，实际为%d", constants.YuntongOperateTypeTicketReply, ticketData.OperateType)
	}
	if ticketData.ComplaintNumber <= 0 {
		return nil, fmt.Errorf("工单号(ComplaintNumber)不能为空或无效")
	}

	return &model.ParsedCallbackData{
		Type:            model.EventTypeTicketReplied,
		OrderNo:         "",                   // 🔥 修复：OrderNo留空，由统一回调服务查找，与其他回调保持一致
		CustomerOrderNo: ticketData.OrderCode, // 🔥 修复：OrderCode是客户订单号
		TrackingNo:      ticketData.LogisticCode,
		Data:            ticketData,
		Timestamp:       timestamp,
	}, nil
}

// BuildResponse 构建云通响应 - 严格按照官方文档格式
func (a *YuntongCallbackAdapter) BuildResponse(data *model.StandardizedCallbackData) *model.CallbackResponse {
	// 🔥 企业级修复：严格按照云通官方文档返回格式
	// 官方要求的响应格式（直接返回，不包装）：
	// {
	//     "UpdateTime": "2021-04-23 14:30:07",
	//     "EBusinessID": "1662067",
	//     "Reason": "成功",
	//     "Success": true
	// }

	// 🔥 企业级监控：记录响应构建时间，用于分析重复推送
	responseTime := util.NowBeijing()

	// 构建云通期望的直接响应格式
	yuntongResponse := map[string]interface{}{
		"UpdateTime":  responseTime.Format(constants.TimeFormatYuntong),
		"EBusinessID": a.eBusinessID,
		"Reason":      "成功",
		"Success":     true,
	}

	// 记录响应构建日志，便于分析重复推送问题
	// 注意：这里使用fmt.Printf是为了确保日志能够被监控脚本捕获
	// 因为这个适配器没有logger实例，所以使用标准输出
	fmt.Printf("🔧 云通响应构建 - 订单: %s, 响应时间: %s, EBusinessID: %s\n",
		data.OrderNo, responseTime.Format(constants.TimeFormatYuntong), a.eBusinessID)

	// 返回特殊的响应格式，标记为直接返回
	return &model.CallbackResponse{
		Success:      true,
		Code:         constants.HTTPStatusOK,
		Message:      "成功",
		Data:         yuntongResponse,
		DirectReturn: true, // 标记为直接返回，不包装
	}
}

// parseSpecialYuntongCallback 解析特殊的云通回调数据（Count=0的情况）
func (a *YuntongCallbackAdapter) parseSpecialYuntongCallback(rawData []byte, timestamp time.Time) (*model.ParsedCallbackData, error) {
	// 🔥 企业级修复：处理云通可能的特殊回调格式

	// 1. 尝试解析为直接的订单数据（不在Data数组中）
	var directOrderData struct {
		PushTime     string      `json:"PushTime"`
		EBusinessID  interface{} `json:"EBusinessID"`
		ShipperCode  string      `json:"ShipperCode"`
		OrderCode    string      `json:"OrderCode"`
		LogisticCode string      `json:"LogisticCode"`
		Reason       string      `json:"Reason"`
		State        int         `json:"State"`
		Comments     string      `json:"comments"`
		CreateTime   string      `json:"CreateTime"`
		OperateType  int         `json:"OperateType"`
		RequestType  interface{} `json:"RequestType,omitempty"`
	}

	if err := json.Unmarshal(rawData, &directOrderData); err == nil {
		if directOrderData.OrderCode != "" && directOrderData.State > 0 {
			// 构造标准的ParsedCallbackData
			return &model.ParsedCallbackData{
				Type:            model.EventTypeOrderStatusChanged,
				OrderNo:         "",                        // 🔥 修复：OrderNo留空，由统一回调服务查找
				CustomerOrderNo: directOrderData.OrderCode, // 🔥 修复：OrderCode是客户订单号
				TrackingNo:      directOrderData.LogisticCode,
				Data:            directOrderData,
				Timestamp:       timestamp,
			}, nil
		}
	}

	// 2. 如果都解析不了，返回错误
	return nil, fmt.Errorf("无法解析云通特殊回调数据，原始数据: %s", string(rawData))
}
