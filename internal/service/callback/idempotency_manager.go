package callback

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// IdempotencyManager 精准幂等性管理器 - 仅处理明确的重复场景
// 🚨 重要：不阻止正常的业务流程，只识别技术层面的重复请求
type IdempotencyManager struct {
	logger    *zap.Logger
	cache     map[string]*IdempotencyEntry // 存储请求指纹和处理信息
	mutex     sync.RWMutex                 // 读写锁保证线程安全
	maxSize   int                          // 最大缓存大小
	ttl       time.Duration                // 短期TTL，避免阻止正常业务
	cleanupCh chan struct{}                // 清理通道
}

// IdempotencyEntry 幂等性条目
type IdempotencyEntry struct {
	ProcessedAt time.Time `json:"processed_at"`
	ExpiresAt   time.Time `json:"expires_at"`
	Count       int       `json:"count"`       // 重复次数统计
	LastAccess  time.Time `json:"last_access"` // 最后访问时间
}

// NewIdempotencyManager 创建幂等性管理器 - 企业级配置
func NewIdempotencyManager(logger *zap.Logger) *IdempotencyManager {
	im := &IdempotencyManager{
		logger:    logger,
		cache:     make(map[string]*IdempotencyEntry),
		maxSize:   10000,            // 最大缓存10000条记录
		ttl:       10 * time.Minute, // 10分钟TTL，只阻止极短时间内的重复
		cleanupCh: make(chan struct{}),
	}

	// 启动后台清理协程
	go im.startCleanupRoutine()

	return im
}

// startCleanupRoutine 启动清理协程
func (im *IdempotencyManager) startCleanupRoutine() {
	ticker := time.NewTicker(time.Hour) // 每小时清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			im.CleanupExpiredEntries()
		case <-im.cleanupCh:
			return
		}
	}
}

// Stop 停止幂等性管理器
func (im *IdempotencyManager) Stop() {
	close(im.cleanupCh)
}

// CallbackIdempotencyKey 回调幂等性键
type CallbackIdempotencyKey struct {
	OrderNo   string  `json:"order_no"`
	Provider  string  `json:"provider"`
	EventType string  `json:"event_type"`
	TotalFee  float64 `json:"total_fee"`
	Weight    float64 `json:"weight"`
	Volume    float64 `json:"volume"`
	Timestamp string  `json:"timestamp"` // 精确到分钟，允许同一分钟内的合理更新
}

// GenerateCallbackHash 生成回调哈希值
func (im *IdempotencyManager) GenerateCallbackHash(orderNo, provider, eventType string, totalFee, weight, volume float64) (string, error) {
	// 构建幂等性键
	key := CallbackIdempotencyKey{
		OrderNo:   orderNo,
		Provider:  provider,
		EventType: eventType,
		TotalFee:  totalFee,
		Weight:    weight,
		Volume:    volume,
		Timestamp: util.NowBeijing().Format("2006-01-02T15:04:05"), // 精确到秒，只阻止极短时间内的重复
	}

	// 序列化为JSON
	keyBytes, err := json.Marshal(key)
	if err != nil {
		return "", fmt.Errorf("序列化幂等性键失败: %w", err)
	}

	// 生成SHA256哈希
	hash := sha256.Sum256(keyBytes)
	return hex.EncodeToString(hash[:]), nil
}

// IsCallbackProcessed 检查回调是否已处理 - 线程安全实现
func (im *IdempotencyManager) IsCallbackProcessed(ctx context.Context, callbackHash string) bool {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	// 检查缓存中是否存在
	if entry, exists := im.cache[callbackHash]; exists {
		// 检查是否在有效期内
		if util.NowBeijing().Before(entry.ExpiresAt) {
			// 更新访问时间和计数
			entry.LastAccess = util.NowBeijing()
			entry.Count++

			im.logger.Info("发现重复回调，跳过处理",
				zap.String("callback_hash", callbackHash),
				zap.Time("processed_at", entry.ProcessedAt),
				zap.Int("repeat_count", entry.Count))
			return true
		}
	}

	return false
}

// MarkCallbackProcessed 标记回调已处理 - 线程安全实现
func (im *IdempotencyManager) MarkCallbackProcessed(ctx context.Context, callbackHash string) {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	now := util.NowBeijing()
	entry := &IdempotencyEntry{
		ProcessedAt: now,
		ExpiresAt:   now.Add(im.ttl),
		Count:       1,
		LastAccess:  now,
	}

	// 检查缓存大小限制
	if len(im.cache) >= im.maxSize {
		im.evictOldestEntries()
	}

	im.cache[callbackHash] = entry
	im.logger.Info("标记回调已处理",
		zap.String("callback_hash", callbackHash),
		zap.Time("processed_at", now))
}

// WeightFeeIdempotencyKey 重量费用幂等性键
type WeightFeeIdempotencyKey struct {
	OrderNo         string  `json:"order_no"`
	UserID          string  `json:"user_id"`
	TransactionType string  `json:"transaction_type"`
	Amount          float64 `json:"amount"`
	OriginalWeight  float64 `json:"original_weight"`
	ActualWeight    float64 `json:"actual_weight"`
	Date            string  `json:"date"` // 精确到日期
}

// GenerateWeightFeeHash 生成重量费用哈希值
func (im *IdempotencyManager) GenerateWeightFeeHash(orderNo, userID, transactionType string, amount, originalWeight, actualWeight float64) (string, error) {
	key := WeightFeeIdempotencyKey{
		OrderNo:         orderNo,
		UserID:          userID,
		TransactionType: transactionType,
		Amount:          amount,
		OriginalWeight:  originalWeight,
		ActualWeight:    actualWeight,
		Date:            util.NowBeijing().Format("2006-01-02"),
	}

	keyBytes, err := json.Marshal(key)
	if err != nil {
		return "", fmt.Errorf("序列化重量费用幂等性键失败: %w", err)
	}

	hash := sha256.Sum256(keyBytes)
	return hex.EncodeToString(hash[:]), nil
}

// IsWeightFeeProcessed 检查重量费用是否已处理 - 线程安全实现
func (im *IdempotencyManager) IsWeightFeeProcessed(ctx context.Context, weightFeeHash string) bool {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	if entry, exists := im.cache[weightFeeHash]; exists {
		if util.NowBeijing().Before(entry.ExpiresAt) {
			// 更新访问时间和计数
			entry.LastAccess = util.NowBeijing()
			entry.Count++

			im.logger.Info("发现重复重量费用处理，跳过",
				zap.String("weight_fee_hash", weightFeeHash),
				zap.Time("processed_at", entry.ProcessedAt),
				zap.Int("repeat_count", entry.Count))
			return true
		}
	}
	return false
}

// MarkWeightFeeProcessed 标记重量费用已处理 - 线程安全实现
func (im *IdempotencyManager) MarkWeightFeeProcessed(ctx context.Context, weightFeeHash string) {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	now := util.NowBeijing()
	entry := &IdempotencyEntry{
		ProcessedAt: now,
		ExpiresAt:   now.Add(im.ttl),
		Count:       1,
		LastAccess:  now,
	}

	// 检查缓存大小限制
	if len(im.cache) >= im.maxSize {
		im.evictOldestEntries()
	}

	im.cache[weightFeeHash] = entry
	im.logger.Info("标记重量费用已处理",
		zap.String("weight_fee_hash", weightFeeHash),
		zap.Time("processed_at", now))
}

// CleanupExpiredEntries 清理过期条目 - 线程安全实现
func (im *IdempotencyManager) CleanupExpiredEntries() {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	now := util.NowBeijing()
	deletedCount := 0

	for hash, entry := range im.cache {
		if now.After(entry.ExpiresAt) {
			delete(im.cache, hash)
			deletedCount++
		}
	}

	im.logger.Info("清理过期幂等性条目完成",
		zap.Int("deleted_entries", deletedCount),
		zap.Int("remaining_entries", len(im.cache)))
}

// evictOldestEntries 淘汰最旧的条目
func (im *IdempotencyManager) evictOldestEntries() {
	// 找到最旧的条目并删除（简单LRU实现）
	var oldestHash string
	var oldestTime time.Time = util.NowBeijing()

	for hash, entry := range im.cache {
		if entry.LastAccess.Before(oldestTime) {
			oldestTime = entry.LastAccess
			oldestHash = hash
		}
	}

	if oldestHash != "" {
		delete(im.cache, oldestHash)
		im.logger.Info("淘汰最旧的幂等性条目",
			zap.String("evicted_hash", oldestHash),
			zap.Time("last_access", oldestTime))
	}
}

// CacheStats 缓存统计信息结构
type CacheStats struct {
	TotalEntries    int           `json:"total_entries"`
	MaxSize         int           `json:"max_size"`
	TTL             time.Duration `json:"ttl"`
	CacheType       string        `json:"cache_type"`
	CleanupInterval string        `json:"cleanup_interval"`
	HitRate         float64       `json:"hit_rate"`
}

// GetCacheStats 获取缓存统计信息 - 线程安全实现
func (im *IdempotencyManager) GetCacheStats() CacheStats {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	// 计算命中率（基于重复次数）
	totalAccess := 0
	hitCount := 0
	for _, entry := range im.cache {
		totalAccess += entry.Count
		if entry.Count > 1 {
			hitCount += entry.Count - 1 // 第一次不算命中
		}
	}

	hitRate := 0.0
	if totalAccess > 0 {
		hitRate = float64(hitCount) / float64(totalAccess)
	}

	return CacheStats{
		TotalEntries:    len(im.cache),
		MaxSize:         im.maxSize,
		TTL:             im.ttl,
		CacheType:       "memory",
		CleanupInterval: "1h",
		HitRate:         hitRate,
	}
}
