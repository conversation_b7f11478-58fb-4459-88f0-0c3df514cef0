package callback

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"
)

// UnifiedCallbackForwarder 统一回调转发器
// 🔥 重新设计：完全统一的回调格式，供应商无关
// 遵循企业级标准：格式固定，不因供应商而变化
type UnifiedCallbackForwarder struct {
	callbackRepository repository.CallbackRepository
	httpClient         *http.Client
	builder            *UnifiedCallbackBuilder
	logger             *zap.Logger
	retryScheduler     *CallbackRetryScheduler // 🔥 新增：重试调度器
}

// NewUnifiedCallbackForwarder 创建统一回调转发器
func NewUnifiedCallbackForwarder(
	callbackRepository repository.CallbackRepository,
	httpClient *http.Client,
	systemConfigService service.SystemConfigService,
	logger *zap.Logger,
) *UnifiedCallbackForwarder {
	return &UnifiedCallbackForwarder{
		callbackRepository: callbackRepository,
		httpClient:         httpClient,
		builder:            NewUnifiedCallbackBuilder(systemConfigService, logger),
		logger:             logger,
	}
}

// SetRetryScheduler 设置重试调度器（避免循环依赖）
func (f *UnifiedCallbackForwarder) SetRetryScheduler(scheduler *CallbackRetryScheduler) {
	f.retryScheduler = scheduler
}

// Forward 转发回调到用户（统一格式）
// 🔥 核心改进：完全统一的格式，供应商信息完全隐藏
func (f *UnifiedCallbackForwarder) Forward(ctx context.Context, record *model.UnifiedCallbackRecord, data *model.StandardizedCallbackData) error {
	// 🔥 修复：输入验证
	if record == nil {
		return fmt.Errorf("回调记录不能为空")
	}
	if data == nil {
		return fmt.Errorf("标准化数据不能为空")
	}
	if record.UserID == "" {
		return fmt.Errorf("用户ID不能为空")
	}
	// 1. 查询用户回调配置
	config, err := f.callbackRepository.GetUserCallbackConfig(ctx, record.UserID)
	if err != nil || config == nil {
		f.logger.Info("用户未配置回调或配置不存在",
			zap.String("user_id", record.UserID))
		return nil
	}

	if !config.Enabled {
		f.logger.Info("用户回调已禁用",
			zap.String("user_id", record.UserID))
		return nil
	}

	// 2. 检查事件订阅
	if !f.isEventSubscribed(config.SubscribedEvents, data.EventType) {
		f.logger.Info("用户未订阅此事件类型",
			zap.String("user_id", record.UserID),
			zap.String("event_type", data.EventType))
		return nil
	}

	// 3. 构建统一格式的回调数据
	callbackData, err := f.builder.BuildCallbackData(ctx, data)
	if err != nil {
		return fmt.Errorf("构建统一回调数据失败: %w", err)
	}

	// 4. 生成签名
	signature, err := f.generateSignature(callbackData, config.CallbackSecret)
	if err != nil {
		return fmt.Errorf("生成签名失败: %w", err)
	}

	// 5. 创建转发记录
	forwardRecord := &model.CallbackForwardRecord{
		ID:               uuid.New(),
		CallbackRecordID: record.ID,
		UserID:           record.UserID,
		CallbackURL:      config.CallbackURL,
		Status:           model.CallbackStatusPending,
		CreatedAt:        util.NowBeijing(),
	}

	// 序列化请求数据
	requestDataBytes, err := json.Marshal(callbackData)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}
	forwardRecord.RequestData = requestDataBytes

	// 6. 保存转发记录
	if err := f.callbackRepository.SaveForwardRecord(ctx, forwardRecord); err != nil {
		return fmt.Errorf("保存转发记录失败: %w", err)
	}

	// 7. 发送回调请求
	return f.sendCallbackRequest(ctx, config, callbackData, signature, forwardRecord, record)
}

// sendCallbackRequest 发送统一格式的回调请求
// 🔥 企业级标准：统一的请求格式和错误处理
func (f *UnifiedCallbackForwarder) sendCallbackRequest(ctx context.Context, config *model.UserCallbackConfig, data *model.UnifiedCallbackData, signature string, record *model.CallbackForwardRecord, callbackRecord *model.UnifiedCallbackRecord) error {
	// 构建HTTP请求
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", config.CallbackURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Go-Kuaidi-Unified-Callback/3.0")
	req.Header.Set("X-Timestamp", strconv.FormatInt(util.NowBeijing().Unix(), 10))
	req.Header.Set("X-Event-Type", strconv.Itoa(data.EventType))

	if signature != "" {
		req.Header.Set("X-Signature", signature)
	}

	// 🔥 修复：设置合理的超时时间和重试机制
	timeout := 45 * time.Second // 增加默认超时时间到45秒
	if config.TimeoutSeconds > 0 {
		configTimeout := time.Duration(config.TimeoutSeconds) * time.Second
		// 限制最大超时时间为5分钟，防止长时间阻塞
		if configTimeout <= 5*time.Minute {
			timeout = configTimeout
		}
	}

	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	req = req.WithContext(ctx)

	// 记录请求时间
	requestTime := util.NowBeijing()
	record.RequestAt = &requestTime

	f.logger.Info("发送统一格式用户回调请求",
		zap.String("user_id", record.UserID),
		zap.String("callback_url", config.CallbackURL),
		zap.String("order_no", data.OrderNo),
		zap.Int("event_type", data.EventType))

	// 🔥 修复：增加重试机制处理超时问题
	var resp *http.Response
	maxRetries := 3
	retryDelay := 2 * time.Second

	for attempt := 1; attempt <= maxRetries; attempt++ {
		// 为每次重试创建新的请求上下文，设置超时
		reqCtx, cancel := context.WithTimeout(ctx, timeout)
		reqWithCtx := req.WithContext(reqCtx)

		resp, err = f.httpClient.Do(reqWithCtx)
		cancel() // 立即释放上下文资源

		if err == nil {
			break // 成功，跳出重试循环
		}

		// 记录重试日志
		f.logger.Warn("回调请求失败，准备重试",
			zap.String("user_id", record.UserID),
			zap.String("callback_url", config.CallbackURL),
			zap.Int("attempt", attempt),
			zap.Int("max_retries", maxRetries),
			zap.Error(err))

		// 如果不是最后一次尝试，等待后重试
		if attempt < maxRetries {
			time.Sleep(retryDelay)
			retryDelay *= 2 // 指数退避
		}
	}

	// 所有重试都失败
	if err != nil {
		record.Status = model.CallbackStatusFailed
		record.ErrorMessage = fmt.Sprintf("重试%d次后仍然失败: %v", maxRetries, err)
		f.callbackRepository.UpdateForwardRecord(ctx, record)
		return fmt.Errorf("发送HTTP请求失败(重试%d次): %w", maxRetries, err)
	}
	defer resp.Body.Close()

	// 记录响应时间
	responseTime := util.NowBeijing()
	record.ResponseAt = &responseTime
	record.HTTPStatus = resp.StatusCode

	// 🔥 修复：安全读取响应体，防止内存泄漏
	var responseBody []byte
	if resp.Body != nil {
		// 限制响应体大小，防止恶意大响应
		const maxResponseSize = 1024 * 1024 // 1MB
		limitedReader := &io.LimitedReader{R: resp.Body, N: maxResponseSize}

		var err error
		responseBody, err = io.ReadAll(limitedReader)
		if err != nil {
			f.logger.Warn("读取响应体失败",
				zap.String("user_id", record.UserID),
				zap.Error(err))
			responseBody = []byte{}
		}
	}

	// 保存响应数据
	if len(responseBody) > 0 {
		responseData := json.RawMessage(responseBody)
		record.ResponseData = &responseData
	}

	// 判断是否成功
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		// 验证响应格式
		if f.isValidResponse(responseBody) {
			record.Status = model.CallbackStatusSuccess
			f.logger.Info("统一格式用户回调请求成功",
				zap.String("user_id", record.UserID),
				zap.String("callback_url", config.CallbackURL),
				zap.Int("status_code", resp.StatusCode),
				zap.Int("event_type", data.EventType),
				zap.String("order_no", data.OrderNo))
		} else {
			record.Status = model.CallbackStatusFailed
			record.ErrorMessage = f.buildResponseFormatError(responseBody)
			f.logger.Error("用户回调响应格式不符合标准",
				zap.String("user_id", record.UserID),
				zap.String("callback_url", config.CallbackURL),
				zap.String("order_no", data.OrderNo),
				zap.Int("event_type", data.EventType),
				zap.String("response", string(responseBody)),
				zap.String("expected_format", `{"code": 200, "success": true, "msg": "接收成功"}`))
		}
	} else {
		record.Status = model.CallbackStatusFailed
		record.ErrorMessage = fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(responseBody))
		f.logger.Error("统一格式用户回调请求失败",
			zap.String("user_id", record.UserID),
			zap.String("callback_url", config.CallbackURL),
			zap.String("order_no", data.OrderNo),
			zap.Int("status_code", resp.StatusCode),
			zap.String("response", string(responseBody)))
	}

	// 更新转发记录
	f.callbackRepository.UpdateForwardRecord(ctx, record)

	if record.Status == model.CallbackStatusFailed {
		// 🔥 新增：调度重试任务
		if f.retryScheduler != nil {
			if err := f.retryScheduler.ScheduleRetry(ctx, record, callbackRecord); err != nil {
				f.logger.Error("调度重试任务失败",
					zap.String("forward_record_id", record.ID.String()),
					zap.String("user_id", record.UserID),
					zap.Error(err))
			}
		}
		return fmt.Errorf("用户回调请求失败: HTTP %d", resp.StatusCode)
	}

	return nil
}

// isEventSubscribed 检查事件是否被订阅
func (f *UnifiedCallbackForwarder) isEventSubscribed(subscribedEvents []string, eventType string) bool {
	for _, event := range subscribedEvents {
		if event == eventType {
			return true
		}
	}
	return false
}

// generateSignature 生成签名
func (f *UnifiedCallbackForwarder) generateSignature(data *model.UnifiedCallbackData, secret string) (string, error) {
	if secret == "" {
		return "", nil
	}

	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	// 使用HMAC-SHA256生成签名
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(jsonData)
	return hex.EncodeToString(h.Sum(nil)), nil
}

// isValidResponse 验证响应格式
// 🔥 企业级标准：严格的响应格式验证
func (f *UnifiedCallbackForwarder) isValidResponse(responseBody []byte) bool {
	if len(responseBody) == 0 {
		return false
	}

	var response map[string]interface{}
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return false
	}

	// 检查必要字段
	code, hasCode := response["code"]
	success, hasSuccess := response["success"]

	if !hasCode || !hasSuccess {
		return false
	}

	// 验证success字段
	successBool, ok := success.(bool)
	if !ok || !successBool {
		return false
	}

	// 验证code字段
	switch v := code.(type) {
	case float64:
		return int(v) == 200
	case int:
		return v == 200
	case string:
		return v == "200"
	default:
		return false
	}
}

// buildResponseFormatError 构建响应格式错误信息
func (f *UnifiedCallbackForwarder) buildResponseFormatError(responseBody []byte) string {
	if len(responseBody) == 0 {
		return "响应为空。标准格式: {\"code\": 200, \"success\": true, \"msg\": \"接收成功\"}"
	}

	var response map[string]interface{}
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return fmt.Sprintf("响应不是有效的JSON格式: %v。标准格式: {\"code\": 200, \"success\": true, \"msg\": \"接收成功\"}", err)
	}

	var issues []string

	// 检查code字段
	if code, hasCode := response["code"]; !hasCode {
		issues = append(issues, "缺少必需的'code'字段")
	} else {
		switch v := code.(type) {
		case float64:
			if int(v) != 200 {
				issues = append(issues, fmt.Sprintf("code字段值为%d，应为200", int(v)))
			}
		case string:
			if v != "200" {
				issues = append(issues, fmt.Sprintf("code字段值为'%s'，应为200或'200'", v))
			}
		default:
			issues = append(issues, fmt.Sprintf("code字段类型错误，当前为%T，应为number或string", code))
		}
	}

	// 检查success字段
	if success, hasSuccess := response["success"]; !hasSuccess {
		issues = append(issues, "缺少必需的'success'字段")
	} else {
		if successBool, ok := success.(bool); !ok {
			issues = append(issues, fmt.Sprintf("success字段类型错误，当前为%T，应为boolean", success))
		} else if !successBool {
			issues = append(issues, "success字段值为false，应为true")
		}
	}

	if len(issues) == 0 {
		return "响应格式验证失败，原因未知"
	}

	return fmt.Sprintf("响应格式不符合标准: %s。正确格式: {\"code\": 200, \"success\": true, \"msg\": \"接收成功\"}",
		strings.Join(issues, "; "))
}
