package callback

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"
)

// UnifiedCallbackProcessor 统一回调处理器
type UnifiedCallbackProcessor struct {
	statusMapper       *UnifiedStatusMapper
	orderRepository    repository.OrderRepository
	smartOrderFinder   *service.SmartOrderFinder // 🔥 新增：智能订单查找服务
	statusRepository   repository.OrderStatusRepository
	callbackRepository repository.CallbackRepository
	balanceService     service.BalanceServiceInterface // 🔥 新增：余额服务，用于计算实际支付金额
	validationMode     string                          // 验证模式：strict/warn/skip
	logger             *zap.Logger
}

// NewUnifiedCallbackProcessor 创建统一回调处理器
func NewUnifiedCallbackProcessor(
	orderRepo repository.OrderRepository,
	smartOrderFinder *service.SmartOrderFinder, // 🔥 新增：智能订单查找服务参数
	statusRepo repository.OrderStatusRepository,
	callbackRepo repository.CallbackRepository,
	balanceService service.BalanceServiceInterface, // 🔥 新增：余额服务参数
	logger *zap.Logger,
) *UnifiedCallbackProcessor {
	return &UnifiedCallbackProcessor{
		statusMapper:       NewUnifiedStatusMapper(),
		orderRepository:    orderRepo,
		smartOrderFinder:   smartOrderFinder, // 🔥 新增：设置智能订单查找服务
		statusRepository:   statusRepo,
		callbackRepository: callbackRepo,
		balanceService:     balanceService, // 🔥 新增：设置余额服务
		validationMode:     constants.DefaultValidationMode,
		logger:             logger,
	}
}

// SetValidationMode 设置验证模式
func (p *UnifiedCallbackProcessor) SetValidationMode(mode string) {
	p.validationMode = mode
}

// ProcessStatusChange 处理状态变更
func (p *UnifiedCallbackProcessor) ProcessStatusChange(ctx context.Context, orderNo string, result StatusResult) error {
	// 1. 验证状态转换合法性
	currentStatus, err := p.statusRepository.GetOrderCurrentStatus(ctx, orderNo)
	if err != nil {
		p.logger.Error("获取订单状态失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return fmt.Errorf("获取订单状态失败: %w", err)
	}

	// 2. 🔥 修复：检查订单取消相关状态的处理逻辑
	if currentStatus == model.OrderStatusCancelled {
		// 如果新状态也是取消相关，允许处理以执行退款和清理逻辑
		if result.Status == model.OrderStatusCancelled || result.Status == model.OrderStatusVoided {
			p.logger.Info("订单已取消，但允许处理取消逻辑以执行退款和清理",
				zap.String("order_no", orderNo),
				zap.String("current_status", currentStatus),
				zap.String("attempted_status", result.Status),
				zap.String("provider", result.Provider))
			// 继续处理
		} else {
			p.logger.Warn("订单已被取消，拒绝非取消状态更新",
				zap.String("order_no", orderNo),
				zap.String("current_status", currentStatus),
				zap.String("attempted_status", result.Status),
				zap.String("provider", result.Provider))
			// 记录回调但不更新状态
			return nil
		}
	} else if currentStatus == model.OrderStatusCancelling {
		// 🔥 新增：处理取消中状态，允许转换为已取消或已作废
		if result.Status == model.OrderStatusCancelled || result.Status == model.OrderStatusVoided {
			p.logger.Info("订单取消中，收到供应商取消确认，允许状态转换",
				zap.String("order_no", orderNo),
				zap.String("current_status", currentStatus),
				zap.String("attempted_status", result.Status),
				zap.String("provider", result.Provider))
			// 继续处理，完成取消流程
		} else {
			p.logger.Warn("订单取消中，拒绝非取消状态更新",
				zap.String("order_no", orderNo),
				zap.String("current_status", currentStatus),
				zap.String("attempted_status", result.Status),
				zap.String("provider", result.Provider))
			// 记录回调但不更新状态
			return nil
		}
	}

	if currentStatus != "" && p.validationMode != constants.ValidationModeSkip {
		if !p.statusMapper.ValidateStatusTransition(currentStatus, result.Status) {
			errorMsg := fmt.Sprintf("非法状态转换: %s -> %s", currentStatus, result.Status)

			switch p.validationMode {
			case constants.ValidationModeStrict:
				p.logger.Error("状态转换不合法，拒绝处理",
					zap.String("order_no", orderNo),
					zap.String("from_status", currentStatus),
					zap.String("to_status", result.Status),
					zap.String("provider", result.Provider))
				return errors.New(errorMsg)

			case constants.ValidationModeWarn:
				p.logger.Warn("状态转换不合法，记录警告但继续处理",
					zap.String("order_no", orderNo),
					zap.String("from_status", currentStatus),
					zap.String("to_status", result.Status),
					zap.String("provider", result.Provider))
				// 记录但不阻断流程
			}
		}
	}

	// 2. 更新订单状态（确保包含变更来源信息）

	// 3. 更新订单状态（确保包含变更来源信息）
	if result.Extra == nil {
		result.Extra = make(map[string]interface{})
	}
	result.Extra["change_source"] = "callback"
	result.Extra["raw_status"] = result.RawStatus

	updateReq := &model.OrderStatusUpdateRequest{
		OrderNo:    orderNo,
		NewStatus:  result.Status,
		UpdateTime: result.Timestamp,
		Provider:   result.Provider,
		Extra:      result.Extra,
	}

	if err := p.statusRepository.UpdateOrderStatus(ctx, updateReq); err != nil {
		p.logger.Error("更新订单状态失败",
			zap.String("order_no", orderNo),
			zap.String("status", result.Status),
			zap.Error(err))
		return fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 3. 执行状态相关的业务逻辑
	if err := p.handleStatusBusinessLogic(ctx, orderNo, result); err != nil {
		p.logger.Error("处理状态业务逻辑失败",
			zap.String("order_no", orderNo),
			zap.String("status", result.Status),
			zap.Error(err))
		// 不返回错误，避免阻断主流程
	}

	// 4. 记录状态变更历史
	p.recordStatusHistory(ctx, orderNo, result)

	p.logger.Info("状态变更处理完成",
		zap.String("order_no", orderNo),
		zap.String("status", result.Status),
		zap.String("provider", result.Provider))

	return nil
}

// handleStatusBusinessLogic 处理状态相关的业务逻辑
func (p *UnifiedCallbackProcessor) handleStatusBusinessLogic(ctx context.Context, orderNo string, result StatusResult) error {
	switch result.Status {
	case model.OrderStatusSubmitted:
		p.logger.Info("订单已提交", zap.String("order_no", orderNo))

	case model.OrderStatusAssigned:
		p.logger.Info("订单已分配", zap.String("order_no", orderNo))

	case model.OrderStatusAwaitingPickup:
		p.logger.Info("等待揽收", zap.String("order_no", orderNo))

	case model.OrderStatusPickedUp:
		p.logger.Info("订单已揽收", zap.String("order_no", orderNo))
		// 处理揽收信息更新
		if result.Extra["pickup_info"] != nil {
			p.updatePickupInfo(ctx, orderNo, result.Extra)
		}

	case model.OrderStatusInTransit:
		p.logger.Info("订单运输中", zap.String("order_no", orderNo))

	case model.OrderStatusOutForDelivery:
		p.logger.Info("订单派送中", zap.String("order_no", orderNo))

	case model.OrderStatusDelivered:
		p.logger.Info("订单已签收", zap.String("order_no", orderNo))
		// 🚨 已删除：订单结算功能（多余的业务逻辑）
		// 所有费用调整都是实时处理的：预收、超重、超轻

	case model.OrderStatusDeliveredAbnormal:
		p.logger.Info("订单异常签收", zap.String("order_no", orderNo))
		// 记录异常签收信息
		if result.Extra["delivery_type"] != nil {
			p.logger.Info("异常签收类型",
				zap.String("order_no", orderNo),
				zap.Any("delivery_type", result.Extra["delivery_type"]))
		}
		// 🚨 已删除：订单结算功能（多余的业务逻辑）
		// 所有费用调整都是实时处理的：预收、超重、超轻

	case model.OrderStatusBilled:
		p.logger.Info("订单已计费", zap.String("order_no", orderNo))
		// 处理计费信息更新
		if result.Extra["billing_info"] != nil || result.Extra["billing_update"] != nil {
			p.updateBillingInfo(ctx, orderNo, result.Extra)
		}

	case model.OrderStatusCancelled:
		p.logger.Info("订单已取消", zap.String("order_no", orderNo))
		p.triggerRefund(ctx, orderNo)

	case model.OrderStatusVoided:
		p.logger.Info("订单已作废", zap.String("order_no", orderNo))

	case model.OrderStatusException:
		p.logger.Warn("订单异常", zap.String("order_no", orderNo))

	case model.OrderStatusWeightUpdated:
		p.logger.Info("订单重量更新", zap.String("order_no", orderNo))
		if result.Extra["weight_change"] != nil || result.Extra["weight_update"] != nil {
			p.handleWeightAdjustment(ctx, orderNo, result.Extra)
		}

	case model.OrderStatusRevived:
		p.logger.Info("订单复活", zap.String("order_no", orderNo))
		if result.Extra["revival_reason"] != nil {
			p.logger.Info("订单复活原因",
				zap.String("order_no", orderNo),
				zap.Any("reason", result.Extra["revival_reason"]))
		}

	case model.OrderStatusForwarded:
		p.logger.Info("订单已转寄", zap.String("order_no", orderNo))
		if result.Extra["forward_info"] != nil {
			p.handleForwardInfo(ctx, orderNo, result.Extra)
		}

	case model.OrderStatusReturned:
		p.logger.Info("订单已退回", zap.String("order_no", orderNo))

	case model.OrderStatusPickupFailed:
		p.logger.Warn("揽收失败", zap.String("order_no", orderNo))

	case model.OrderStatusSubmitFailed:
		p.logger.Error("订单提交失败", zap.String("order_no", orderNo))

	case model.OrderStatusPrintFailed:
		p.logger.Error("面单生成失败", zap.String("order_no", orderNo))
	}

	return nil
}

// updatePickupInfo 更新揽收信息
func (p *UnifiedCallbackProcessor) updatePickupInfo(ctx context.Context, orderNo string, extra map[string]interface{}) {
	p.logger.Info("更新揽收信息",
		zap.String("order_no", orderNo),
		zap.Any("pickup_info", extra))

	// 1. 获取订单信息
	order, err := p.orderRepository.FindByOrderNo(ctx, orderNo)
	if err != nil {
		p.logger.Error("获取订单信息失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return
	}

	// 2. 更新揽收相关信息到订单记录
	needUpdate := false

	// 提取重量信息并更新
	if weight, ok := extra["weight"]; ok {
		if weightFloat, ok := weight.(float64); ok && weightFloat > 0 {
			order.ActualWeight = weightFloat
			needUpdate = true
		}
	}

	// 提取体积信息并更新
	if volume, ok := extra["volume"]; ok {
		if volumeFloat, ok := volume.(float64); ok && volumeFloat > 0 {
			order.ActualVolume = volumeFloat
			needUpdate = true
		}
	}

	// 提取揽件员信息并更新
	if courierName, ok := extra["courier_name"]; ok {
		if nameStr, ok := courierName.(string); ok && nameStr != "" {
			order.CourierName = nameStr
			needUpdate = true
		}
	}

	if courierPhone, ok := extra["courier_phone"]; ok {
		if phoneStr, ok := courierPhone.(string); ok && phoneStr != "" {
			order.CourierPhone = phoneStr
			needUpdate = true
		}
	}

	if courierCode, ok := extra["courier_code"]; ok {
		if codeStr, ok := courierCode.(string); ok && codeStr != "" {
			order.CourierCode = codeStr
			needUpdate = true
		}
	}

	if stationName, ok := extra["station_name"]; ok {
		if stationStr, ok := stationName.(string); ok && stationStr != "" {
			order.StationName = stationStr
			needUpdate = true
		}
	}

	if pickupCode, ok := extra["pickup_code"]; ok {
		if pickupStr, ok := pickupCode.(string); ok && pickupStr != "" {
			order.PickupCode = pickupStr
			needUpdate = true
		}
	}

	// 3. 🔥 企业级修复：使用专门的更新方法，避免覆盖其他字段
	if needUpdate {
		// 分别更新揽收信息和重量体积信息

		// 更新揽收员信息
		pickupReq := &repository.OrderPickupUpdateRequest{
			OrderNo:      orderNo,
			CourierName:  order.CourierName,
			CourierPhone: order.CourierPhone,
			CourierCode:  order.CourierCode,
			StationName:  order.StationName,
			PickupCode:   order.PickupCode,
		}

		if err := p.orderRepository.UpdateOrderPickupInfo(ctx, pickupReq); err != nil {
			p.logger.Error("更新订单揽收员信息失败",
				zap.String("order_no", orderNo),
				zap.Error(err))
		}

		// 更新重量体积信息（如果有的话）
		if order.ActualWeight > 0 || order.ActualVolume > 0 {
			weightReq := &repository.OrderWeightUpdateRequest{
				OrderNo:      orderNo,
				ActualWeight: order.ActualWeight,
				ActualVolume: order.ActualVolume,
			}

			if err := p.orderRepository.UpdateOrderWeightInfo(ctx, weightReq); err != nil {
				p.logger.Error("更新订单重量体积信息失败",
					zap.String("order_no", orderNo),
					zap.Error(err))
			}
		}

		p.logger.Info("订单揽收信息更新成功",
			zap.String("order_no", orderNo),
			zap.Float64("actual_weight", order.ActualWeight),
			zap.Float64("actual_volume", order.ActualVolume),
			zap.String("courier_name", order.CourierName),
			zap.String("courier_phone", order.CourierPhone),
			zap.String("station_name", order.StationName))
	}
}

// updateBillingInfo 更新计费信息
func (p *UnifiedCallbackProcessor) updateBillingInfo(ctx context.Context, orderNo string, extra map[string]interface{}) {
	p.logger.Info("更新计费信息",
		zap.String("order_no", orderNo),
		zap.Any("billing_info", extra))

	// 1. 获取订单信息
	order, err := p.orderRepository.FindByOrderNo(ctx, orderNo)
	if err != nil {
		p.logger.Error("获取订单信息失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return
	}

	// 2. 提取计费信息
	var weight float64
	var totalFee float64
	var feeDetails []model.FeeDetail
	var hasWeightUpdate bool
	var hasFeeUpdate bool

	if w, ok := extra["weight"]; ok {
		if weightFloat, ok := w.(float64); ok && weightFloat > 0 {
			weight = weightFloat
			hasWeightUpdate = true
		}
	}

	if fee, ok := extra["total_fee"]; ok {
		if feeFloat, ok := fee.(float64); ok && feeFloat > 0 {
			totalFee = feeFloat
			hasFeeUpdate = true
		}
	}

	// 提取费用明细
	if details, ok := extra["fee_details"]; ok {
		if detailsSlice, ok := details.([]interface{}); ok {
			for _, detail := range detailsSlice {
				if detailMap, ok := detail.(map[string]interface{}); ok {
					feeDetail := model.FeeDetail{}
					if name, ok := detailMap["name"].(string); ok {
						feeDetail.Name = name
					}
					if amount, ok := detailMap["amount"].(float64); ok {
						feeDetail.Amount = amount
					}
					feeDetails = append(feeDetails, feeDetail)
				}
			}
		}
	}

	// 3. 调用订单仓库更新计费信息
	// 只有当确实有有效的计费信息更新时才执行更新操作
	if hasWeightUpdate || hasFeeUpdate {
		// 如果没有重量更新，使用原有重量
		if !hasWeightUpdate {
			weight = order.Weight
		}
		// 如果没有费用更新，保持原有价格不变
		if !hasFeeUpdate {
			totalFee = order.Price // 保护原有价格不被重置
		}

		billingReq := &repository.OrderBillingUpdateRequest{
			OrderNo:    orderNo,
			Weight:     weight,
			TotalFee:   totalFee,
			FeeDetails: feeDetails,
			Provider:   order.Provider,
			UpdateTime: util.NowBeijing(),
		}

		if err := p.orderRepository.UpdateOrderBilling(ctx, billingReq); err != nil {
			p.logger.Error("更新订单计费信息失败",
				zap.String("order_no", orderNo),
				zap.Error(err))
		} else {
			p.logger.Info("订单计费信息更新成功",
				zap.String("order_no", orderNo),
				zap.Float64("weight", weight),
				zap.Float64("total_fee", totalFee),
				zap.Bool("weight_updated", hasWeightUpdate),
				zap.Bool("fee_updated", hasFeeUpdate))
		}
	} else {
		p.logger.Info("回调中没有有效的计费信息更新，跳过计费更新",
			zap.String("order_no", orderNo))
	}
}

// 🚨 已删除：triggerSettlement 方法（订单结算功能已废弃）
// 原因：所有费用调整都是实时处理的，不需要额外的"结算"概念

// triggerRefund 触发退款
func (p *UnifiedCallbackProcessor) triggerRefund(ctx context.Context, orderNo string) {
	p.logger.Info("触发退款逻辑", zap.String("order_no", orderNo))

	// 1. 获取订单信息
	order, err := p.orderRepository.FindByOrderNo(ctx, orderNo)
	if err != nil {
		p.logger.Error("获取订单信息失败",
			zap.String("order_no", orderNo),
			zap.Error(err))
		return
	}

	// 2. 检查是否需要退款
	if !p.shouldRefundOrder(order) {
		p.logger.Warn("订单状态不需要退款",
			zap.String("order_no", orderNo),
			zap.String("status", order.Status))
		return
	}

	// 3. 计算退款金额
	refundAmount := p.calculateRefundAmount(order)

	// 4. 触发退款通知或处理
	p.logger.Info("订单退款处理完成",
		zap.String("order_no", orderNo),
		zap.String("user_id", order.UserID),
		zap.Float64("refund_amount", refundAmount))
}

// handleWeightAdjustment 处理重量调整
func (p *UnifiedCallbackProcessor) handleWeightAdjustment(ctx context.Context, orderNo string, extra map[string]interface{}) {
	_ = ctx // 保留用于未来扩展
	p.logger.Info("处理重量调整",
		zap.String("order_no", orderNo),
		zap.Any("weight_info", extra))
	// 这里可以实现具体的重量调整逻辑
}

// handleForwardInfo 处理转寄信息
func (p *UnifiedCallbackProcessor) handleForwardInfo(ctx context.Context, orderNo string, extra map[string]interface{}) {
	_ = ctx // 保留用于未来扩展
	p.logger.Info("处理转寄信息",
		zap.String("order_no", orderNo),
		zap.Any("forward_info", extra))
	// 这里可以实现具体的转寄信息处理逻辑
}

// recordStatusHistory 记录状态变更历史
func (p *UnifiedCallbackProcessor) recordStatusHistory(ctx context.Context, orderNo string, result StatusResult) {
	// 这里可以实现状态变更历史记录逻辑
	p.logger.Debug("记录状态变更历史",
		zap.String("order_no", orderNo),
		zap.String("status", result.Status),
		zap.String("provider", result.Provider),
		zap.Time("timestamp", result.Timestamp))

	// 可以在这里调用状态历史仓库记录状态变更
	// 例如：p.statusHistoryRepository.CreateStatusHistory(ctx, &model.OrderStatusHistory{...})
}

// 🚨 已删除：isOrderReadyForSettlement 方法（订单结算功能已废弃）
// 原因：所有费用调整都是实时处理的，不需要额外的"结算"概念

// shouldRefundOrder 检查订单是否应该退款
func (p *UnifiedCallbackProcessor) shouldRefundOrder(order *model.OrderRecord) bool {
	// 检查订单状态是否允许退款
	refundableStatuses := []string{
		model.OrderStatusCancelled,
		model.OrderStatusVoided,
		model.OrderStatusPickupFailed,
	}

	for _, status := range refundableStatuses {
		if order.Status == status {
			return true
		}
	}
	return false
}

// calculateRefundAmount 计算退款金额 - 🔥 企业级修复：使用GetOrderNetPayment
func (p *UnifiedCallbackProcessor) calculateRefundAmount(order *model.OrderRecord) float64 {
	// 🔥 企业级修复：统一使用余额服务的GetOrderNetPayment方法
	// 这是唯一正确的计算订单实际支付金额的方法
	if p.balanceService != nil {
		// 调用余额服务获取订单的实际净支付金额
		// 这个方法会查询所有相关的交易记录并计算净支付金额（总支付-总退款）
		actualPaidAmount, err := p.balanceService.GetOrderNetPayment(context.Background(), order.UserID, order.OrderNo, order.CustomerOrderNo)
		if err != nil {
			p.logger.Error("❌ 查询订单实际支付金额失败，使用降级方案",
				zap.String("order_no", order.OrderNo),
				zap.String("customer_order_no", order.CustomerOrderNo),
				zap.String("user_id", order.UserID),
				zap.Error(err))
			// 降级方案：使用订单价格
			return order.Price
		}

		actualPaidFloat, _ := actualPaidAmount.Float64()

		p.logger.Info("✅ 使用企业级标准方法计算退款金额",
			zap.String("order_no", order.OrderNo),
			zap.String("customer_order_no", order.CustomerOrderNo),
			zap.String("user_id", order.UserID),
			zap.Float64("actual_net_payment", actualPaidFloat),
			zap.Float64("order_price", order.Price),
			zap.String("method", "GetOrderNetPayment"))

		return actualPaidFloat
	}

	// 降级方案：根据订单状态计算（仅在余额服务不可用时使用）
	p.logger.Warn("⚠️ 余额服务不可用，使用降级方案计算退款金额",
		zap.String("order_no", order.OrderNo))

	switch order.Status {
	case model.OrderStatusCancelled:
		// 取消订单，全额退款
		p.logger.Info("取消订单，全额退款（降级方案）",
			zap.String("order_no", order.OrderNo),
			zap.Float64("price", order.Price))
		return order.Price
	case model.OrderStatusVoided:
		// 作废订单，全额退款
		p.logger.Info("作废订单，全额退款（降级方案）",
			zap.String("order_no", order.OrderNo),
			zap.Float64("price", order.Price))
		return order.Price
	case model.OrderStatusPickupFailed:
		// 取件失败，扣除部分手续费后退款
		handlingFee := order.Price * 0.1 // 10%手续费
		if handlingFee < 1.0 {
			handlingFee = 1.0 // 最低1元手续费
		}
		refundAmount := order.Price - handlingFee
		p.logger.Info("取件失败，扣除手续费后退款（降级方案）",
			zap.String("order_no", order.OrderNo),
			zap.Float64("price", order.Price),
			zap.Float64("handling_fee", handlingFee),
			zap.Float64("refund_amount", refundAmount))
		return refundAmount
	default:
		return 0
	}
}
