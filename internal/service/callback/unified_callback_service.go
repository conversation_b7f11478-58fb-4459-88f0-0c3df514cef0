package callback

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"
)

// 🔥 数据一致性：错误定义
var (
	ErrDuplicateCallback = errors.New("重复的回调请求")
	ErrInvalidCallback   = errors.New("无效的回调数据")
)

// UnifiedCallbackService 统一回调服务
// 🔥 重构：移除冗余转发器，仅保留统一转发器
type UnifiedCallbackService struct {
	providerAdapters   map[string]ProviderCallbackAdapter
	standardizer       *CallbackStandardizer
	internalProcessor  *InternalCallbackProcessor
	unifiedForwarder   *UnifiedCallbackForwarder // 🔥 唯一的转发器
	callbackRepository repository.CallbackRepository
	orderRepository    repository.OrderRepository
	smartOrderFinder   *service.SmartOrderFinder // 🔥 新增：智能订单查找服务
	logger             *zap.Logger
}

// NewUnifiedCallbackService 创建统一回调服务
func NewUnifiedCallbackService(
	callbackRepo repository.CallbackRepository,
	orderRepo repository.OrderRepository,
	billingService service.BillingService,
	balanceService service.BalanceService,
	mappingService express.ExpressMappingService,
	expressCompanyRepo express.ExpressCompanyRepository,
	smartOrderFinder *service.SmartOrderFinder, // 🔥 新增：智能订单查找服务
	systemConfigService service.SystemConfigService, // 🔥 新增：系统配置服务
	statusUpdater *service.OrderStatusUpdater, // 🔥 新增：订单状态更新器
	logger *zap.Logger,
) *UnifiedCallbackService {
	service := &UnifiedCallbackService{
		providerAdapters:   make(map[string]ProviderCallbackAdapter),
		standardizer:       NewCallbackStandardizer(mappingService, logger),
		callbackRepository: callbackRepo,
		orderRepository:    orderRepo,
		smartOrderFinder:   smartOrderFinder, // 🔥 新增：设置智能订单查找服务
		logger:             logger,
	}

	// 🔥 重构：初始化内部处理器和统一转发器
	billingAdapter := NewBillingServiceAdapter(billingService)
	balanceAdapter := NewBalanceServiceAdapter(balanceService)

	// 🔥 修复：正确注入 OrderStatusUpdater，解决订单取消不退款的问题
	service.internalProcessor = NewInternalCallbackProcessor(orderRepo, smartOrderFinder, billingAdapter, balanceAdapter, expressCompanyRepo, statusUpdater, logger)

	// 🔥 初始化唯一的统一转发器 - 优化HTTP客户端配置
	transport := &http.Transport{
		MaxIdleConns:          100,
		MaxIdleConnsPerHost:   30,
		MaxConnsPerHost:       50,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ResponseHeaderTimeout: 30 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		DialContext: (&net.Dialer{
			Timeout:   10 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		DisableCompression: false,
		ForceAttemptHTTP2:  true,
	}

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second, // 增加总体超时时间到60秒
	}
	service.unifiedForwarder = NewUnifiedCallbackForwarder(callbackRepo, httpClient, systemConfigService, logger)

	return service
}

// RegisterProviderAdapter 注册供应商适配器
func (s *UnifiedCallbackService) RegisterProviderAdapter(provider string, adapter ProviderCallbackAdapter) {
	s.providerAdapters[provider] = adapter
}

// RegisterProviderAdapterInterface 注册供应商适配器（接口版本）
func (s *UnifiedCallbackService) RegisterProviderAdapterInterface(provider string, adapter service.CallbackAdapterInterface) error {
	// 这里需要适配器类型转换
	// 暂时返回错误，表示需要类型适配
	return fmt.Errorf("适配器类型不匹配，需要实现类型转换")
}

// ProcessCallback 处理回调 - 🔥 数据一致性修复
func (s *UnifiedCallbackService) ProcessCallback(ctx context.Context, provider string, rawData []byte, headers map[string]string) (*model.CallbackResponse, error) {
	requestID := uuid.New().String()

	s.logger.Info("接收到回调",
		zap.String("request_id", requestID),
		zap.String("provider", provider),
		zap.Int("data_size", len(rawData)),
		zap.Any("headers", headers))

	// 🔥 数据一致性：在事务中处理整个回调流程
	return s.processCallbackWithTransaction(ctx, requestID, provider, rawData, headers)
}

// processCallbackWithTransaction 在事务中处理回调，确保数据一致性
func (s *UnifiedCallbackService) processCallbackWithTransaction(ctx context.Context, requestID, provider string, rawData []byte, headers map[string]string) (*model.CallbackResponse, error) {
	// 1. 获取供应商适配器
	adapter, exists := s.providerAdapters[provider]
	if !exists {
		return nil, fmt.Errorf("不支持的供应商: %s", provider)
	}

	// 2. 验证签名
	if err := adapter.ValidateSignature(rawData, headers); err != nil {
		s.logger.Error("签名验证失败",
			zap.String("request_id", requestID),
			zap.String("provider", provider),
			zap.Error(err))
		return nil, fmt.Errorf("签名验证失败: %w", err)
	}

	// 3. 解析回调数据
	parsedData, err := adapter.ParseCallback(rawData)
	if err != nil {
		s.logger.Error("解析回调数据失败",
			zap.String("request_id", requestID),
			zap.String("provider", provider),
			zap.Error(err))
		return nil, fmt.Errorf("解析回调数据失败: %w", err)
	}

	// 4. 🔥 新增：智能查找用户和订单信息
	userID, orderNo, customerOrderNo, err := s.findUserAndOrderInfo(ctx, parsedData, provider)
	if err != nil {
		s.logger.Warn("查找用户和订单信息失败",
			zap.String("request_id", requestID),
			zap.String("provider", provider),
			zap.String("parsed_order_no", parsedData.OrderNo),
			zap.String("parsed_customer_order_no", parsedData.CustomerOrderNo),
			zap.String("parsed_tracking_no", parsedData.TrackingNo),
			zap.String("callback_type", parsedData.Type),
			zap.Int("raw_data_size", len(rawData)),
			zap.Error(err))

		// 🔥 新增：记录详细的诊断信息
		s.logger.Debug("回调数据诊断信息",
			zap.String("request_id", requestID),
			zap.String("provider", provider),
			zap.String("raw_data_preview", s.truncateString(string(rawData), 500)), // 只记录前500字符
			zap.Any("parsed_data", parsedData))
		// 不阻断流程，继续处理内部逻辑
	}

	// 🔥 调试日志：记录查找结果
	s.logger.Info("订单信息查找结果",
		zap.String("request_id", requestID),
		zap.String("provider", provider),
		zap.String("found_user_id", userID),
		zap.String("found_order_no", orderNo),
		zap.String("found_customer_order_no", customerOrderNo),
		zap.String("original_parsed_order_no", parsedData.OrderNo),
		zap.String("original_parsed_customer_order_no", parsedData.CustomerOrderNo))

	// 更新解析数据
	parsedData.UserID = userID
	if orderNo != "" {
		parsedData.OrderNo = orderNo
	}
	if customerOrderNo != "" {
		parsedData.CustomerOrderNo = customerOrderNo
	}

	// 5. 数据标准化
	standardizedData, err := s.standardizer.Standardize(provider, parsedData)
	if err != nil {
		s.logger.Error("数据标准化失败",
			zap.String("request_id", requestID),
			zap.String("provider", provider),
			zap.Error(err))
		return nil, fmt.Errorf("数据标准化失败: %w", err)
	}

	// 🔥 修复：使用智能订单查找服务获取正确的平台订单号和TrackingNo
	if standardizedData.CustomerOrderNo != "" && s.smartOrderFinder != nil {
		// 使用智能订单查找服务，它会正确查找并返回完整的订单信息
		if order, err := s.smartOrderFinder.FindOrderByAnyIdentifier(ctx, standardizedData.CustomerOrderNo, userID); err == nil {
			// 智能订单查找服务会返回完整的订单信息，包括正确的平台订单号和TrackingNo
			standardizedData.PlatformOrderNo = order.PlatformOrderNo

			// 🔥 修复：补充TrackingNo（特别是菜鸟的GOT_SUCCESS等事件）
			if standardizedData.TrackingNo == "" && order.TrackingNo != "" {
				standardizedData.TrackingNo = order.TrackingNo
				s.logger.Info("补充TrackingNo成功",
					zap.String("customer_order_no", standardizedData.CustomerOrderNo),
					zap.String("tracking_no", order.TrackingNo),
					zap.String("provider", provider))
			}

			s.logger.Info("智能订单查找成功",
				zap.String("customer_order_no", standardizedData.CustomerOrderNo),
				zap.String("platform_order_no", order.PlatformOrderNo),
				zap.String("order_no", order.OrderNo),
				zap.String("tracking_no", standardizedData.TrackingNo))
		} else {
			s.logger.Warn("智能订单查找失败，回退到普通查询",
				zap.String("customer_order_no", standardizedData.CustomerOrderNo),
				zap.Error(err))
			// 🔥 修复：使用智能查询服务作为降级逻辑
			smartFinder := service.NewSmartOrderFinder(s.orderRepository, s.logger)
			if order, err := smartFinder.FindOrderByAnyIdentifier(ctx, standardizedData.CustomerOrderNo, ""); err == nil && order.Provider == provider {
				standardizedData.PlatformOrderNo = order.PlatformOrderNo

				// 🔥 修复：在降级逻辑中也补充TrackingNo
				if standardizedData.TrackingNo == "" && order.TrackingNo != "" {
					standardizedData.TrackingNo = order.TrackingNo
					s.logger.Info("智能查询服务降级逻辑补充TrackingNo成功",
						zap.String("customer_order_no", standardizedData.CustomerOrderNo),
						zap.String("tracking_no", order.TrackingNo),
						zap.String("provider", provider))
				}
			}
		}
	}

	// 🔥 数据一致性：幂等性检查
	if err := s.checkCallbackIdempotency(ctx, requestID, standardizedData, rawData); err != nil {
		if err == ErrDuplicateCallback {
			// 重复回调，返回成功响应但不处理
			s.logger.Info("检测到重复回调，跳过处理",
				zap.String("request_id", requestID),
				zap.String("provider", provider),
				zap.String("customer_order_no", standardizedData.CustomerOrderNo))
			return adapter.BuildResponse(standardizedData), nil
		}
		return nil, fmt.Errorf("幂等性检查失败: %w", err)
	}

	// 🔥 调试日志：记录标准化结果
	s.logger.Info("数据标准化结果",
		zap.String("request_id", requestID),
		zap.String("provider", provider),
		zap.String("standardized_order_no", standardizedData.OrderNo),
		zap.String("standardized_platform_order_no", standardizedData.PlatformOrderNo),
		zap.String("standardized_customer_order_no", standardizedData.CustomerOrderNo),
		zap.String("standardized_tracking_no", standardizedData.TrackingNo))

	// 6. 保存回调记录
	record := &model.UnifiedCallbackRecord{
		ID:              uuid.New(),
		Provider:        provider,
		CallbackType:    parsedData.Type,
		OrderNo:         standardizedData.OrderNo,         // 🔥 修复：使用标准化后的OrderNo
		CustomerOrderNo: standardizedData.CustomerOrderNo, // 🔥 修复：使用标准化后的CustomerOrderNo
		TrackingNo:      standardizedData.TrackingNo,      // 🔥 修复：使用标准化后的TrackingNo
		UserID:          userID,
		RawSignature:    headers["signature"],
		EventType:       standardizedData.EventType,
		InternalStatus:  model.CallbackStatusPending,
		ExternalStatus:  model.CallbackStatusPending,
		ReceivedAt:      util.NowBeijing(),
		CreatedAt:       util.NowBeijing(),
		UpdatedAt:       util.NowBeijing(),
	}

	// 处理原始数据，确保是有效的JSON
	if json.Valid(rawData) {
		// 如果是有效的JSON，直接使用
		record.RawData = rawData
	} else {
		// 如果不是有效的JSON（如表单数据），包装成JSON对象
		rawDataWrapper := map[string]interface{}{
			"content_type": headers["content-type"],
			"raw_content":  string(rawData),
		}
		if wrappedData, err := json.Marshal(rawDataWrapper); err == nil {
			record.RawData = wrappedData
		} else {
			// 如果包装失败，使用简单的字符串JSON
			record.RawData = json.RawMessage(`{"raw_content":"` + string(rawData) + `"}`)
		}
	}

	// 序列化标准化数据
	standardizedDataBytes, err := json.Marshal(standardizedData)
	if err != nil {
		s.logger.Error("序列化标准化数据失败", zap.Error(err))
	} else {
		record.StandardizedData = standardizedDataBytes
	}

	if err := s.callbackRepository.SaveCallbackRecord(ctx, record); err != nil {
		s.logger.Error("保存回调记录失败",
			zap.String("request_id", requestID),
			zap.Error(err))
		// 不阻断流程，继续处理
	}

	// 7. 🔥 修复：同步处理内部回调，确保数据一致性
	internalErr := s.processInternalCallbackSync(ctx, record, standardizedData)
	if internalErr != nil {
		s.logger.Error("内部回调处理失败",
			zap.String("request_id", requestID),
			zap.String("order_no", standardizedData.OrderNo),
			zap.Error(internalErr))
		// 🔥 关键修复：内部处理失败时不标记为成功
		return nil, fmt.Errorf("内部回调处理失败: %w", internalErr)
	}

	// 8. 异步处理外部回调转发（不影响主流程）
	if userID != "" {
		// 🔥 统一化：仅使用统一格式转发器
		go s.processUnifiedCallback(context.Background(), record, standardizedData)

		// 🔥 云通301计费状态兼容性处理：同时生成运输中状态回调
		if provider == constants.ProviderYuntong && standardizedData.EventType == model.EventTypeBillingUpdated {
			go s.processYuntongBillingCompatibility(context.Background(), record, standardizedData)
		}
	}

	// 8. 返回供应商期望的响应格式
	response := adapter.BuildResponse(standardizedData)

	s.logger.Info("回调处理完成",
		zap.String("request_id", requestID),
		zap.String("provider", provider),
		zap.String("event_type", standardizedData.EventType),
		zap.String("order_no", standardizedData.OrderNo)) // 🔥 修复：使用标准化后的OrderNo

	return response, nil
}

// 🔥 修复：findUserAndOrderInfo 使用智能查询服务查找用户和订单信息
func (s *UnifiedCallbackService) findUserAndOrderInfo(ctx context.Context, parsedData *model.ParsedCallbackData, provider string) (string, string, string, error) {
	// 🔥 创建智能订单查找服务
	smartFinder := service.NewSmartOrderFinder(s.orderRepository, s.logger)

	// 策略1：如果有客户订单号，优先通过客户订单号查找
	if parsedData.CustomerOrderNo != "" {
		s.logger.Debug("🔍 使用智能查询服务查找客户订单号",
			zap.String("customer_order_no", parsedData.CustomerOrderNo),
			zap.String("provider", provider))

		order, err := smartFinder.FindOrderByAnyIdentifier(ctx, parsedData.CustomerOrderNo, "")
		if err == nil && order.Provider == provider {
			s.logger.Info("✅ 智能查询服务通过客户订单号找到订单",
				zap.String("customer_order_no", parsedData.CustomerOrderNo),
				zap.String("user_id", order.UserID),
				zap.String("order_no", order.OrderNo))
			return order.UserID, order.OrderNo, order.CustomerOrderNo, nil
		}
		s.logger.Debug("客户订单号查找失败", zap.Error(err))
	}

	// 策略2：如果有平台订单号，通过平台订单号查找
	if parsedData.OrderNo != "" {
		s.logger.Debug("🔍 使用智能查询服务查找订单号",
			zap.String("order_no", parsedData.OrderNo),
			zap.String("provider", provider))

		order, err := smartFinder.FindOrderByAnyIdentifier(ctx, parsedData.OrderNo, "")
		if err == nil && order.Provider == provider {
			s.logger.Info("✅ 智能查询服务通过订单号找到订单",
				zap.String("order_no", parsedData.OrderNo),
				zap.String("user_id", order.UserID),
				zap.String("customer_order_no", order.CustomerOrderNo))
			return order.UserID, order.OrderNo, order.CustomerOrderNo, nil
		}
		s.logger.Debug("订单号查找失败", zap.Error(err))
	}

	// 策略3：通过运单号查找
	if parsedData.TrackingNo != "" {
		s.logger.Debug("🔍 使用智能查询服务查找运单号",
			zap.String("tracking_no", parsedData.TrackingNo),
			zap.String("provider", provider))

		order, err := smartFinder.FindOrderByAnyIdentifier(ctx, parsedData.TrackingNo, "")
		if err == nil && order.Provider == provider {
			s.logger.Info("✅ 智能查询服务通过运单号找到订单",
				zap.String("tracking_no", parsedData.TrackingNo),
				zap.String("user_id", order.UserID),
				zap.String("order_no", order.OrderNo),
				zap.String("customer_order_no", order.CustomerOrderNo))
			return order.UserID, order.OrderNo, order.CustomerOrderNo, nil
		}
		s.logger.Debug("运单号查找失败", zap.Error(err))
	}

	s.logger.Error("❌ 智能查询服务未找到匹配的订单记录",
		zap.String("provider", provider),
		zap.String("parsed_order_no", parsedData.OrderNo),
		zap.String("parsed_customer_order_no", parsedData.CustomerOrderNo),
		zap.String("parsed_tracking_no", parsedData.TrackingNo))

	return "", "", "", fmt.Errorf("未找到匹配的订单记录")
}

// truncateString 截断字符串到指定长度
func (s *UnifiedCallbackService) truncateString(str string, maxLen int) string {
	if len(str) <= maxLen {
		return str
	}
	return str[:maxLen] + "..."
}

// processInternalCallback 处理内部回调
func (s *UnifiedCallbackService) processInternalCallback(ctx context.Context, record *model.UnifiedCallbackRecord, data *model.StandardizedCallbackData) {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Error("内部回调处理发生panic",
				zap.String("record_id", record.ID.String()),
				zap.Any("panic", r))

			// 更新记录状态
			record.InternalStatus = model.CallbackStatusFailed
			record.InternalError = fmt.Sprintf("panic: %v", r)
			now := util.NowBeijing()
			record.InternalProcessedAt = &now
			record.UpdatedAt = util.NowBeijing()
			s.callbackRepository.UpdateCallbackRecord(ctx, record)
		}
	}()

	s.logger.Info("开始内部回调处理",
		zap.String("record_id", record.ID.String()),
		zap.String("event_type", data.EventType))

	// 更新处理状态
	record.InternalStatus = model.CallbackStatusProcessing
	record.UpdatedAt = util.NowBeijing()
	s.callbackRepository.UpdateCallbackRecord(ctx, record)

	// 执行内部处理
	err := s.internalProcessor.Process(ctx, record, data)

	// 更新处理结果
	now := util.NowBeijing()
	record.InternalProcessedAt = &now
	record.UpdatedAt = util.NowBeijing()

	if err != nil {
		record.InternalStatus = model.CallbackStatusFailed
		record.InternalError = err.Error()
		s.logger.Error("内部回调处理失败",
			zap.String("record_id", record.ID.String()),
			zap.Error(err))
	} else {
		record.InternalStatus = model.CallbackStatusSuccess
		s.logger.Info("内部回调处理成功",
			zap.String("record_id", record.ID.String()))
	}

	s.callbackRepository.UpdateCallbackRecord(ctx, record)
}

// processInternalCallbackSync 同步处理内部回调，确保数据一致性
func (s *UnifiedCallbackService) processInternalCallbackSync(ctx context.Context, record *model.UnifiedCallbackRecord, data *model.StandardizedCallbackData) error {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Error("同步内部回调处理发生panic",
				zap.String("record_id", record.ID.String()),
				zap.Any("panic", r))

			// 更新记录状态
			record.InternalStatus = model.CallbackStatusFailed
			record.InternalError = fmt.Sprintf("panic: %v", r)
			now := util.NowBeijing()
			record.InternalProcessedAt = &now
			record.UpdatedAt = util.NowBeijing()
			s.callbackRepository.UpdateCallbackRecord(ctx, record)
		}
	}()

	s.logger.Info("开始同步内部回调处理",
		zap.String("record_id", record.ID.String()),
		zap.String("event_type", data.EventType),
		zap.String("order_no", data.OrderNo))

	// 更新处理状态
	record.InternalStatus = model.CallbackStatusProcessing
	record.UpdatedAt = util.NowBeijing()
	s.callbackRepository.UpdateCallbackRecord(ctx, record)

	// 执行内部处理
	err := s.internalProcessor.Process(ctx, record, data)

	// 更新处理结果
	now := util.NowBeijing()
	record.InternalProcessedAt = &now
	record.UpdatedAt = util.NowBeijing()

	if err != nil {
		record.InternalStatus = model.CallbackStatusFailed
		record.InternalError = err.Error()
		s.logger.Error("同步内部回调处理失败",
			zap.String("record_id", record.ID.String()),
			zap.String("order_no", data.OrderNo),
			zap.Error(err))
	} else {
		record.InternalStatus = model.CallbackStatusSuccess
		s.logger.Info("同步内部回调处理成功",
			zap.String("record_id", record.ID.String()),
			zap.String("order_no", data.OrderNo))
	}

	s.callbackRepository.UpdateCallbackRecord(ctx, record)
	return err
}

// 🔥 移除：processExternalCallback 和 processYidaStyleCallback 已删除
// 统一使用 processUnifiedCallback 处理所有外部回调转发

// processUnifiedCallback 处理统一格式外部回调转发
// 🔥 新增：使用完全统一的格式，供应商无关
func (s *UnifiedCallbackService) processUnifiedCallback(ctx context.Context, record *model.UnifiedCallbackRecord, data *model.StandardizedCallbackData) {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Error("统一格式回调转发发生panic",
				zap.String("record_id", record.ID.String()),
				zap.Any("panic", r))

			// 更新记录状态
			record.ExternalStatus = model.CallbackStatusFailed
			record.ExternalError = fmt.Sprintf("panic: %v", r)
			now := util.NowBeijing()
			record.ExternalProcessedAt = &now
			record.UpdatedAt = util.NowBeijing()
			s.callbackRepository.UpdateCallbackRecord(ctx, record)
		}
	}()

	// 🔥 修复：转发层面的幂等性检查，防止重复转发
	forwardIdempotencyKey := s.generateForwardIdempotencyKey(data, record.ID.String())
	if s.isForwardAlreadyProcessed(ctx, forwardIdempotencyKey) {
		s.logger.Info("🚨 检测到重复转发请求，跳过转发",
			zap.String("record_id", record.ID.String()),
			zap.String("user_id", record.UserID),
			zap.String("event_type", data.EventType),
			zap.String("order_no", data.OrderNo),
			zap.String("forward_idempotency_key", forwardIdempotencyKey))

		// 标记为已处理，但不执行转发
		record.ExternalStatus = model.CallbackStatusSuccess
		record.UpdatedAt = util.NowBeijing()
		s.callbackRepository.UpdateCallbackRecord(ctx, record)
		return
	}

	s.logger.Info("开始统一格式回调转发",
		zap.String("record_id", record.ID.String()),
		zap.String("user_id", record.UserID),
		zap.String("event_type", data.EventType),
		zap.String("order_no", data.OrderNo),
		zap.String("platform_order_no", data.PlatformOrderNo),
		zap.String("forward_idempotency_key", forwardIdempotencyKey))

	// 更新处理状态
	record.ExternalStatus = model.CallbackStatusProcessing
	record.UpdatedAt = util.NowBeijing()
	s.callbackRepository.UpdateCallbackRecord(ctx, record)

	// 执行统一格式转发 - 直接传递原始数据，不需要转换
	err := s.unifiedForwarder.Forward(ctx, record, data)

	// 🔥 修复：标记转发已处理
	if err == nil {
		s.markForwardAsProcessed(ctx, forwardIdempotencyKey, record.ID.String())
	}

	// 更新处理结果
	now := util.NowBeijing()
	record.ExternalProcessedAt = &now
	record.UpdatedAt = util.NowBeijing()

	if err != nil {
		record.ExternalStatus = model.CallbackStatusFailed
		record.ExternalError = err.Error()
		s.logger.Error("统一格式回调转发失败",
			zap.String("record_id", record.ID.String()),
			zap.String("order_no", data.OrderNo),
			zap.String("platform_order_no", data.PlatformOrderNo),
			zap.Error(err))
	} else {
		record.ExternalStatus = model.CallbackStatusSuccess
		s.logger.Info("统一格式回调转发成功",
			zap.String("record_id", record.ID.String()),
			zap.String("order_no", data.OrderNo),
			zap.String("platform_order_no", data.PlatformOrderNo))
	}

	s.callbackRepository.UpdateCallbackRecord(ctx, record)
}

// GetCallbackRecords 获取回调记录（管理员用）
func (s *UnifiedCallbackService) GetCallbackRecords(ctx context.Context, userID string, limit, offset int) ([]*model.UnifiedCallbackRecord, error) {
	return s.callbackRepository.GetCallbackRecordsByUserID(ctx, userID, limit, offset)
}

// GetCallbackRecordsCount 获取回调记录总数（管理员用）
func (s *UnifiedCallbackService) GetCallbackRecordsCount(ctx context.Context, userID string) (int64, error) {
	return s.callbackRepository.GetCallbackRecordsCount(ctx, userID)
}

// GetForwardRecords 获取转发记录（用户用）
func (s *UnifiedCallbackService) GetForwardRecords(ctx context.Context, userID string, limit, offset int) ([]*model.CallbackForwardRecord, error) {
	return s.callbackRepository.GetForwardRecordsByUserID(ctx, userID, limit, offset)
}

// GetForwardRecordsCount 获取转发记录总数（用户用）
func (s *UnifiedCallbackService) GetForwardRecordsCount(ctx context.Context, userID string) (int64, error) {
	return s.callbackRepository.GetForwardRecordsCountByUserID(ctx, userID)
}

// GetUserCallbackConfig 获取用户回调配置
func (s *UnifiedCallbackService) GetUserCallbackConfig(ctx context.Context, userID string) (*model.UserCallbackConfig, error) {
	return s.callbackRepository.GetUserCallbackConfig(ctx, userID)
}

// SaveUserCallbackConfig 保存用户回调配置
func (s *UnifiedCallbackService) SaveUserCallbackConfig(ctx context.Context, config *model.UserCallbackConfig) error {
	return s.callbackRepository.SaveUserCallbackConfig(ctx, config)
}

// UpdateUserCallbackConfig 更新用户回调配置
func (s *UnifiedCallbackService) UpdateUserCallbackConfig(ctx context.Context, config *model.UserCallbackConfig) error {
	return s.callbackRepository.UpdateUserCallbackConfig(ctx, config)
}

// GetEnhancedForwardRecords 获取增强的转发记录（包含更多信息）
func (s *UnifiedCallbackService) GetEnhancedForwardRecords(ctx context.Context, userID string, limit, offset int) ([]*model.EnhancedForwardRecord, error) {
	// 获取转发记录
	forwardRecords, err := s.callbackRepository.GetForwardRecordsByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, err
	}

	var enhancedRecords []*model.EnhancedForwardRecord
	for _, record := range forwardRecords {
		// 获取对应的统一回调记录以获取更多信息
		callbackRecord, err := s.callbackRepository.GetCallbackRecord(ctx, record.CallbackRecordID)
		if err != nil {
			s.logger.Warn("获取回调记录失败", zap.String("callback_record_id", record.CallbackRecordID.String()), zap.Error(err))
			// 即使获取失败，也创建基础的增强记录
			enhancedRecord := &model.EnhancedForwardRecord{
				ID:              record.ID,
				EventType:       "unknown",
				OrderNo:         "",
				CustomerOrderNo: "",
				TrackingNo:      "",
				CallbackURL:     record.CallbackURL,
				RequestData:     record.RequestData,
				ResponseData:    record.ResponseData,
				HTTPStatus:      record.HTTPStatus,
				Status:          record.Status,
				RetryCount:      record.RetryCount,
				ErrorMessage:    record.ErrorMessage,
				RequestAt:       record.RequestAt,
				ResponseAt:      record.ResponseAt,
				CreatedAt:       record.CreatedAt,
			}
			enhancedRecords = append(enhancedRecords, enhancedRecord)
			continue
		}

		// 创建增强记录
		enhancedRecord := &model.EnhancedForwardRecord{
			ID:              record.ID,
			EventType:       callbackRecord.EventType,
			OrderNo:         callbackRecord.OrderNo,
			CustomerOrderNo: callbackRecord.CustomerOrderNo,
			TrackingNo:      callbackRecord.TrackingNo,
			CallbackURL:     record.CallbackURL,
			RequestData:     record.RequestData,
			ResponseData:    record.ResponseData,
			HTTPStatus:      record.HTTPStatus,
			Status:          record.Status,
			RetryCount:      record.RetryCount,
			ErrorMessage:    record.ErrorMessage,
			RequestAt:       record.RequestAt,
			ResponseAt:      record.ResponseAt,
			CreatedAt:       record.CreatedAt,
		}
		enhancedRecords = append(enhancedRecords, enhancedRecord)
	}

	return enhancedRecords, nil
}

// GetCallbackStatistics 获取回调统计
func (s *UnifiedCallbackService) GetCallbackStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (map[string]interface{}, error) {
	return s.callbackRepository.GetCallbackStatistics(ctx, userID, startTime, endTime)
}

// GetForwardRecordStatistics 获取转发记录统计（用户用）
func (s *UnifiedCallbackService) GetForwardRecordStatistics(ctx context.Context, userID string, startTime, endTime time.Time) (map[string]interface{}, error) {
	return s.callbackRepository.GetForwardRecordStatistics(ctx, userID, startTime, endTime)
}

// GetAdminCallbackStatistics 获取管理员回调统计（全局统计）
func (s *UnifiedCallbackService) GetAdminCallbackStatistics(ctx context.Context, userID string, startTime, endTime time.Time, provider, eventType string) (map[string]interface{}, error) {
	return s.callbackRepository.GetAdminCallbackStatistics(ctx, userID, startTime, endTime, provider, eventType)
}

// RetryCallback 重试回调处理
func (s *UnifiedCallbackService) RetryCallback(ctx context.Context, recordID uuid.UUID) error {
	record, err := s.callbackRepository.GetCallbackRecord(ctx, recordID)
	if err != nil {
		return fmt.Errorf("获取回调记录失败: %w", err)
	}

	// 解析标准化数据
	var standardizedData model.StandardizedCallbackData
	if err := json.Unmarshal(record.StandardizedData, &standardizedData); err != nil {
		return fmt.Errorf("解析标准化数据失败: %w", err)
	}

	// 🔥 修复：根据事件类型正确构造Data字段的具体类型
	if err := s.reconstructDataType(&standardizedData); err != nil {
		return fmt.Errorf("重构数据类型失败: %w", err)
	}

	// 重置状态
	record.InternalStatus = model.CallbackStatusPending
	record.ExternalStatus = model.CallbackStatusPending
	record.InternalError = ""
	record.ExternalError = ""
	record.InternalProcessedAt = nil
	record.ExternalProcessedAt = nil
	record.RetryCount++
	record.UpdatedAt = util.NowBeijing()

	if err := s.callbackRepository.UpdateCallbackRecord(ctx, record); err != nil {
		return fmt.Errorf("更新回调记录失败: %w", err)
	}

	// 重新处理
	go s.processInternalCallback(context.Background(), record, &standardizedData)
	if record.UserID != "" {
		// 🔥 统一化：使用统一格式转发器
		go s.processUnifiedCallback(context.Background(), record, &standardizedData)
	}

	return nil
}

// 🔥 新增：实现CallbackServiceInterface接口的缺失方法

// ProcessCallbackAsync 异步处理回调
func (s *UnifiedCallbackService) ProcessCallbackAsync(ctx context.Context, provider string, rawData []byte, headers map[string]string) error {
	go func() {
		_, err := s.ProcessCallback(context.Background(), provider, rawData, headers)
		if err != nil {
			s.logger.Error("异步回调处理失败",
				zap.String("provider", provider),
				zap.Error(err))
		}
	}()
	return nil
}

// GetCallbackRecord 获取回调记录
func (s *UnifiedCallbackService) GetCallbackRecord(ctx context.Context, recordID string) (*model.UnifiedCallbackRecord, error) {
	id, err := uuid.Parse(recordID)
	if err != nil {
		return nil, fmt.Errorf("无效的记录ID: %w", err)
	}
	return s.callbackRepository.GetCallbackRecord(ctx, id)
}

// GetForwardRecord 获取转发记录
func (s *UnifiedCallbackService) GetForwardRecord(ctx context.Context, recordID uuid.UUID) (*model.CallbackForwardRecord, error) {
	return s.callbackRepository.GetForwardRecord(ctx, recordID)
}

// ListCallbackRecords 列出回调记录
func (s *UnifiedCallbackService) ListCallbackRecords(ctx context.Context, req *model.CallbackListRequest) (*model.CallbackListResponse, error) {
	// 注意：CallbackListRequest没有UserID字段，这里需要从上下文或其他方式获取
	// 暂时使用空字符串，实际使用时需要从认证上下文获取用户ID
	userID := "" // TODO: 从认证上下文获取用户ID

	// 获取记录
	records, err := s.callbackRepository.GetCallbackRecordsByUserID(ctx, userID, req.Limit, req.Offset)
	if err != nil {
		return nil, err
	}

	// 获取总数
	total, err := s.callbackRepository.GetCallbackRecordsCount(ctx, userID)
	if err != nil {
		return nil, err
	}

	return &model.CallbackListResponse{
		Success: true,
		Code:    200,
		Message: "获取成功",
		Data:    records,
		Total:   total,
	}, nil
}

// RetryFailedCallbacks 重试失败的回调
func (s *UnifiedCallbackService) RetryFailedCallbacks(ctx context.Context, maxRetries int) error {
	// 这里可以实现批量重试逻辑
	// 暂时返回nil，表示功能待实现
	s.logger.Info("批量重试失败回调功能待实现", zap.Int("max_retries", maxRetries))
	return nil
}

// GetProviderAdapter 获取供应商适配器
func (s *UnifiedCallbackService) GetProviderAdapter(provider string) (service.CallbackAdapterInterface, bool) {
	// 这里需要适配器类型转换
	// 暂时返回nil，表示需要类型适配
	return nil, false
}

// 🔥 重构：获取增强的转发记录（高性能，支持筛选）
func (s *UnifiedCallbackService) GetEnhancedForwardRecordsWithFilters(ctx context.Context, userID string, limit, offset int, filters map[string]string, forceRefresh bool) ([]*model.EnhancedForwardRecord, error) {
	// 🔥 性能优化：使用JOIN查询，一次性获取所有数据
	enhancedRecords, err := s.callbackRepository.GetEnhancedForwardRecordsWithJoin(ctx, userID, limit, offset, filters)
	if err != nil {
		s.logger.Error("获取增强转发记录失败", zap.Error(err))
		return nil, err
	}

	s.logger.Info("获取增强转发记录成功",
		zap.String("user_id", userID),
		zap.Int("total_records", len(enhancedRecords)))

	return enhancedRecords, nil
}

// 🔥 新增：获取转发记录总数（支持筛选）
func (s *UnifiedCallbackService) GetForwardRecordsCountWithFilters(ctx context.Context, userID string, filters map[string]string) (int64, error) {
	// 🔥 性能优化：直接使用数据库统计
	return s.callbackRepository.GetEnhancedForwardRecordsCountWithFilters(ctx, userID, filters)
}

// reconstructDataType 重构标准化数据中的Data字段类型
// 解决重推时JSON反序列化导致的类型丢失问题
func (s *UnifiedCallbackService) reconstructDataType(data *model.StandardizedCallbackData) error {
	// 如果Data已经是正确的类型，直接返回
	switch data.EventType {
	case model.EventTypeBillingUpdated:
		if _, ok := data.Data.(*model.BillingUpdatedData); ok {
			return nil // 已经是正确类型
		}

		// 将map[string]interface{}转换为BillingUpdatedData
		dataMap, ok := data.Data.(map[string]interface{})
		if !ok {
			return fmt.Errorf("计费数据格式错误: 期望map[string]interface{}, 实际类型: %T", data.Data)
		}

		// 重新序列化再反序列化到正确的类型
		dataBytes, err := json.Marshal(dataMap)
		if err != nil {
			return fmt.Errorf("序列化计费数据失败: %w", err)
		}

		var billingData model.BillingUpdatedData
		if err := json.Unmarshal(dataBytes, &billingData); err != nil {
			return fmt.Errorf("反序列化计费数据失败: %w", err)
		}

		data.Data = &billingData

	case model.EventTypeOrderStatusChanged:
		if _, ok := data.Data.(*model.OrderStatusChangedData); ok {
			return nil // 已经是正确类型
		}

		dataMap, ok := data.Data.(map[string]interface{})
		if !ok {
			return fmt.Errorf("状态变更数据格式错误: 期望map[string]interface{}, 实际类型: %T", data.Data)
		}

		dataBytes, err := json.Marshal(dataMap)
		if err != nil {
			return fmt.Errorf("序列化状态变更数据失败: %w", err)
		}

		var statusData model.OrderStatusChangedData
		if err := json.Unmarshal(dataBytes, &statusData); err != nil {
			return fmt.Errorf("反序列化状态变更数据失败: %w", err)
		}

		data.Data = &statusData

	case model.EventTypeTicketReplied:
		if _, ok := data.Data.(*model.WorkOrderCallbackData); ok {
			return nil // 已经是正确类型
		}

		dataMap, ok := data.Data.(map[string]interface{})
		if !ok {
			return fmt.Errorf("工单回复数据格式错误: 期望map[string]interface{}, 实际类型: %T", data.Data)
		}

		dataBytes, err := json.Marshal(dataMap)
		if err != nil {
			return fmt.Errorf("序列化工单回复数据失败: %w", err)
		}

		var ticketData model.WorkOrderCallbackData
		if err := json.Unmarshal(dataBytes, &ticketData); err != nil {
			return fmt.Errorf("反序列化工单回复数据失败: %w", err)
		}

		data.Data = &ticketData

	default:
		return fmt.Errorf("未知的事件类型: %s", data.EventType)
	}

	return nil
}

// processYuntongBillingCompatibility 处理云通计费状态兼容性
// 🔥 企业级修复：云通301计费状态同时生成运输中状态回调转发给用户
func (s *UnifiedCallbackService) processYuntongBillingCompatibility(ctx context.Context, originalRecord *model.UnifiedCallbackRecord, billingData *model.StandardizedCallbackData) {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Error("云通计费兼容性处理发生panic",
				zap.String("order_no", originalRecord.OrderNo),
				zap.Any("panic", r))
		}
	}()

	s.logger.Info("开始云通计费兼容性处理 - 生成运输中状态回调",
		zap.String("order_no", originalRecord.OrderNo),
		zap.String("provider", originalRecord.Provider))

	// 1. 构建运输中状态的标准化数据
	inTransitData := &model.OrderStatusChangedData{
		NewStatus:  model.OrderStatusInTransit,
		StatusDesc: "运输中",
		UpdateTime: util.NowBeijing(),
		Extra: map[string]interface{}{
			"compatibility_source": "yuntong_billing_301",
			"generated_reason":     "云通301计费状态兼容性处理",
			"original_event_type":  string(billingData.EventType), // 🔥 修复：确保类型为string
		},
	}

	// 2. 创建运输中状态的标准化回调数据
	inTransitStandardizedData := &model.StandardizedCallbackData{
		EventType:       model.EventTypeOrderStatusChanged,
		OrderNo:         originalRecord.OrderNo,
		CustomerOrderNo: originalRecord.CustomerOrderNo,
		TrackingNo:      originalRecord.TrackingNo,
		Provider:        originalRecord.Provider,
		Timestamp:       util.NowBeijing(),
		Data:            inTransitData,
	}

	// 3. 创建新的回调记录用于转发
	// 🔥 修复：正确序列化StandardizedData为JSON
	standardizedDataBytes, err := json.Marshal(inTransitStandardizedData)
	if err != nil {
		s.logger.Error("序列化运输中状态数据失败",
			zap.String("order_no", originalRecord.OrderNo),
			zap.Error(err))
		return
	}

	inTransitRecord := &model.UnifiedCallbackRecord{
		ID:               uuid.New(),
		Provider:         originalRecord.Provider,
		CallbackType:     "order_status", // 设置回调类型
		OrderNo:          originalRecord.OrderNo,
		CustomerOrderNo:  originalRecord.CustomerOrderNo,
		TrackingNo:       originalRecord.TrackingNo,
		UserID:           originalRecord.UserID,
		RawData:          []byte(`{"generated_from":"yuntong_billing_301","reason":"compatibility"}`),
		StandardizedData: standardizedDataBytes, // 🔥 修复：使用正确的字段名
		EventType:        model.EventTypeOrderStatusChanged,
		InternalStatus:   model.CallbackStatusSuccess,
		ExternalStatus:   model.CallbackStatusPending,
		ReceivedAt:       util.NowBeijing(),
		CreatedAt:        util.NowBeijing(),
		UpdatedAt:        util.NowBeijing(),
	}

	// 4. 保存回调记录
	if err := s.callbackRepository.SaveCallbackRecord(ctx, inTransitRecord); err != nil {
		s.logger.Error("保存云通运输中状态回调记录失败",
			zap.String("order_no", originalRecord.OrderNo),
			zap.Error(err))
		return
	}

	// 5. 转发运输中状态回调给用户
	if err := s.unifiedForwarder.Forward(ctx, inTransitRecord, inTransitStandardizedData); err != nil {
		s.logger.Error("转发云通运输中状态回调失败",
			zap.String("order_no", originalRecord.OrderNo),
			zap.String("record_id", inTransitRecord.ID.String()),
			zap.Error(err))

		// 更新转发状态为失败
		inTransitRecord.ExternalStatus = model.CallbackStatusFailed
		inTransitRecord.ExternalError = err.Error()
		inTransitRecord.UpdatedAt = util.NowBeijing()
		s.callbackRepository.UpdateCallbackRecord(ctx, inTransitRecord)
	} else {
		s.logger.Info("云通运输中状态回调转发成功",
			zap.String("order_no", originalRecord.OrderNo),
			zap.String("record_id", inTransitRecord.ID.String()))

		// 更新转发状态为成功
		inTransitRecord.ExternalStatus = model.CallbackStatusSuccess
		inTransitRecord.UpdatedAt = util.NowBeijing()
		now := util.NowBeijing()
		inTransitRecord.ExternalProcessedAt = &now
		s.callbackRepository.UpdateCallbackRecord(ctx, inTransitRecord)
	}
}

// 🔥 数据一致性：幂等性检查方法（简化版本）
func (s *UnifiedCallbackService) checkCallbackIdempotency(ctx context.Context, requestID string, data *model.StandardizedCallbackData, rawData []byte) error {
	// 1. 生成幂等性键
	idempotencyKey := s.generateIdempotencyKey(data, rawData)

	// 2. 简化版本：暂时只记录幂等性键，用于调试和监控
	// TODO: 后续可以添加专门的幂等性表或Redis缓存来实现真正的重复检查
	s.logger.Debug("执行幂等性检查",
		zap.String("request_id", requestID),
		zap.String("idempotency_key", idempotencyKey),
		zap.String("customer_order_no", data.CustomerOrderNo),
		zap.String("provider", data.Provider),
		zap.String("event_type", data.EventType))

	// 暂时总是返回成功，不阻断处理流程
	// 真正的幂等性检查将在后续版本中实现
	return nil
}

// generateIdempotencyKey 生成幂等性键
func (s *UnifiedCallbackService) generateIdempotencyKey(data *model.StandardizedCallbackData, rawData []byte) string {
	// 使用多个字段组合生成唯一键
	content := fmt.Sprintf("%s_%s_%s_%s_%d",
		data.Provider,
		data.CustomerOrderNo,
		data.TrackingNo,
		data.EventType,
		data.Timestamp.Unix())

	// 添加原始数据哈希，确保完全相同的数据才被认为是重复
	hash := sha256.Sum256(rawData)
	content += "_" + hex.EncodeToString(hash[:8]) // 只取前8字节，减少键长度

	// 生成最终哈希
	finalHash := sha256.Sum256([]byte(content))
	return hex.EncodeToString(finalHash[:])
}

// 🔥 优化：转发层面的幂等性检查方法 - 企业级实现
type ForwardIdempotencyEntry struct {
	ProcessedAt time.Time `json:"processed_at"`
	RecordID    string    `json:"record_id"` // 回调记录ID，确保绝对唯一性
	ExpiresAt   time.Time `json:"expires_at"`
}

var forwardCache = make(map[string]*ForwardIdempotencyEntry) // 增强的内存缓存
var forwardCacheMutex sync.RWMutex

// generateForwardIdempotencyKey 生成转发幂等性键 - 🔥 优化版本
func (s *UnifiedCallbackService) generateForwardIdempotencyKey(data *model.StandardizedCallbackData, recordID string) string {
	// 🔥 核心优化：使用纳秒级时间戳 + 回调记录ID，确保绝对唯一性
	content := fmt.Sprintf("forward_%s_%s_%s_%d_%s",
		data.OrderNo,
		data.CustomerOrderNo,
		data.EventType,
		data.Timestamp.UnixNano(), // 🔥 关键修复：使用纳秒级时间戳
		recordID)                  // 🔥 关键修复：增加回调记录ID确保唯一性

	hash := sha256.Sum256([]byte(content))
	return hex.EncodeToString(hash[:16]) // 取前16字节，减少键长度
}

// isForwardAlreadyProcessed 检查转发是否已处理 - 🔥 优化版本
func (s *UnifiedCallbackService) isForwardAlreadyProcessed(ctx context.Context, key string) bool {
	forwardCacheMutex.RLock()
	defer forwardCacheMutex.RUnlock()

	entry, exists := forwardCache[key]
	if !exists {
		return false
	}

	// 检查是否过期
	if util.NowBeijing().After(entry.ExpiresAt) {
		// 过期条目，异步清理
		go func() {
			forwardCacheMutex.Lock()
			defer forwardCacheMutex.Unlock()
			delete(forwardCache, key)
		}()
		return false
	}

	s.logger.Info("🚨 检测到重复转发请求（优化版）",
		zap.String("forward_key", key),
		zap.String("original_record_id", entry.RecordID),
		zap.Time("processed_at", entry.ProcessedAt))

	return true
}

// markForwardAsProcessed 标记转发已处理 - 🔥 优化版本
func (s *UnifiedCallbackService) markForwardAsProcessed(ctx context.Context, key string, recordID string) {
	forwardCacheMutex.Lock()
	defer forwardCacheMutex.Unlock()

	now := util.NowBeijing()
	entry := &ForwardIdempotencyEntry{
		ProcessedAt: now,
		RecordID:    recordID,
		ExpiresAt:   now.Add(10 * time.Minute), // 10分钟过期，防止长期误判
	}

	forwardCache[key] = entry

	s.logger.Info("✅ 标记转发已处理（优化版）",
		zap.String("forward_key", key),
		zap.String("record_id", recordID),
		zap.Time("processed_at", now),
		zap.Time("expires_at", entry.ExpiresAt))

	// 🔥 优化：智能缓存清理，防止内存泄漏
	if len(forwardCache) > 10000 {
		s.cleanupExpiredForwardCache()
	}
}

// cleanupExpiredForwardCache 清理过期的转发缓存 - 🔥 新增方法
func (s *UnifiedCallbackService) cleanupExpiredForwardCache() {
	now := util.NowBeijing()
	expiredKeys := make([]string, 0)

	// 收集过期的键
	for key, entry := range forwardCache {
		if now.After(entry.ExpiresAt) {
			expiredKeys = append(expiredKeys, key)
		}
	}

	// 删除过期条目
	for _, key := range expiredKeys {
		delete(forwardCache, key)
	}

	s.logger.Info("🧹 清理过期转发缓存",
		zap.Int("expired_count", len(expiredKeys)),
		zap.Int("remaining_count", len(forwardCache)))

	// 如果清理后仍然过多，清理最老的一半
	if len(forwardCache) > 8000 {
		count := 0
		for key := range forwardCache {
			delete(forwardCache, key)
			count++
			if count > 4000 {
				break
			}
		}
		s.logger.Info("🧹 强制清理转发缓存",
			zap.Int("force_cleaned_count", count),
			zap.Int("final_count", len(forwardCache)))
	}
}

// ClearForwardIdempotencyCache 清理特定的转发幂等性缓存 - 🔥 管理员工具方法
func (s *UnifiedCallbackService) ClearForwardIdempotencyCache(data *model.StandardizedCallbackData, recordID string) {
	forwardCacheMutex.Lock()
	defer forwardCacheMutex.Unlock()

	// 生成要清理的幂等性键
	key := s.generateForwardIdempotencyKey(data, recordID)

	// 从缓存中删除
	if entry, exists := forwardCache[key]; exists {
		delete(forwardCache, key)
		s.logger.Info("🧹 清理特定转发幂等性缓存",
			zap.String("forward_key", key),
			zap.String("record_id", recordID),
			zap.String("order_no", data.OrderNo),
			zap.String("event_type", data.EventType),
			zap.Time("original_processed_at", entry.ProcessedAt))
	} else {
		s.logger.Info("🧹 转发幂等性缓存不存在，无需清理",
			zap.String("forward_key", key),
			zap.String("record_id", recordID),
			zap.String("order_no", data.OrderNo))
	}
}
