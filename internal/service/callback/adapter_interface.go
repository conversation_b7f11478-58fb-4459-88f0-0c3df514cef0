package callback

import (
	"github.com/your-org/go-kuaidi/internal/model"
)

// ProviderCallbackAdapter 供应商回调适配器接口
type ProviderCallbackAdapter interface {
	// ValidateSignature 验证签名
	ValidateSignature(rawData []byte, headers map[string]string) error
	// ParseCallback 解析回调数据
	ParseCallback(rawData []byte) (*model.ParsedCallbackData, error)
	// BuildResponse 构建响应
	BuildResponse(data *model.StandardizedCallbackData) *model.CallbackResponse
}
