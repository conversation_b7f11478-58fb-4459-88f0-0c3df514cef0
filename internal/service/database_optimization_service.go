package service

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/your-org/go-kuaidi/internal/cache"
	"github.com/your-org/go-kuaidi/internal/database"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// DatabaseOptimizationService 数据库优化服务
// 基于第一性原理设计：统一管理数据库性能优化组件
type DatabaseOptimizationService struct {
	db          *sql.DB
	redisClient *redis.Client
	logger      *zap.Logger

	// 优化组件
	cacheService        *cache.QueryCacheService
	performanceMonitor  *database.QueryPerformanceMonitor
	paginationOptimizer *database.PaginationOptimizer
	weightCacheService  WeightTierCacheService // 重量档位缓存服务

	// 配置
	config *OptimizationConfig
}

// OptimizationConfig 优化配置
type OptimizationConfig struct {
	// 缓存配置
	CacheEnabled    bool
	CacheDefaultTTL time.Duration

	// 监控配置
	MonitorEnabled            bool
	SlowQueryThreshold        time.Duration
	PerformanceReportInterval time.Duration

	// 分页配置
	DefaultPageSize         int
	MaxPageSize             int
	DeepPaginationThreshold int

	// 查询超时
	QueryTimeout time.Duration
}

// NewDatabaseOptimizationService 创建数据库优化服务
func NewDatabaseOptimizationService(
	db *sql.DB,
	redisClient *redis.Client,
	logger *zap.Logger,
) *DatabaseOptimizationService {
	config := &OptimizationConfig{
		CacheEnabled:              true,
		CacheDefaultTTL:           5 * time.Minute,
		MonitorEnabled:            true,
		SlowQueryThreshold:        1 * time.Second,
		PerformanceReportInterval: 5 * time.Minute,
		DefaultPageSize:           20,
		MaxPageSize:               100,
		DeepPaginationThreshold:   1000,
		QueryTimeout:              30 * time.Second,
	}

	// 创建缓存服务
	cacheService := cache.NewQueryCacheService(redisClient, logger)

	// 创建性能监控器
	performanceMonitor := database.NewQueryPerformanceMonitor(db, logger)

	// 创建分页优化器
	paginationOptimizer := database.NewPaginationOptimizer(db, logger)

	return &DatabaseOptimizationService{
		db:                  db,
		redisClient:         redisClient,
		logger:              logger,
		cacheService:        cacheService,
		performanceMonitor:  performanceMonitor,
		paginationOptimizer: paginationOptimizer,
		config:              config,
	}
}

// Start 启动优化服务
func (s *DatabaseOptimizationService) Start(ctx context.Context) error {
	s.logger.Info("启动数据库优化服务")

	// 启动性能监控
	if s.config.MonitorEnabled {
		s.performanceMonitor.Start(ctx)
		s.logger.Info("性能监控器已启动")
	}

	// 预热缓存
	if s.config.CacheEnabled {
		go s.warmupCache(ctx)
		s.logger.Info("缓存预热已启动")
	}

	return nil
}

// SetWeightCacheService 设置重量档位缓存服务
func (s *DatabaseOptimizationService) SetWeightCacheService(service WeightTierCacheService) {
	s.weightCacheService = service
	s.logger.Info("重量档位缓存服务已设置")
}

// GetCacheService 获取缓存服务
func (s *DatabaseOptimizationService) GetCacheService() *cache.QueryCacheService {
	return s.cacheService
}

// GetPerformanceMonitor 获取性能监控器
func (s *DatabaseOptimizationService) GetPerformanceMonitor() *database.QueryPerformanceMonitor {
	return s.performanceMonitor
}

// RecordQueryMetrics 记录查询指标
func (s *DatabaseOptimizationService) RecordQueryMetrics(
	operation string,
	duration time.Duration,
	rowsAffected, rowsReturned int64,
	cacheHit bool,
) {
	if !s.config.MonitorEnabled {
		return
	}

	metrics := &database.QueryMetrics{
		Operation:    operation,
		Duration:     duration,
		RowsAffected: rowsAffected,
		RowsReturned: rowsReturned,
		CacheHit:     cacheHit,
		Timestamp:    util.NowBeijing(),
	}

	s.performanceMonitor.RecordQuery(metrics)
}

// InvalidateCache 使缓存失效
func (s *DatabaseOptimizationService) InvalidateCache(ctx context.Context, cacheType string, identifier string) error {
	if !s.config.CacheEnabled {
		return nil
	}

	switch cacheType {
	case "user":
		return s.cacheService.InvalidateUserCache(ctx, identifier)
	case "order":
		return s.cacheService.InvalidateOrderCache(ctx, identifier)
	default:
		return s.cacheService.InvalidatePattern(ctx, fmt.Sprintf("*%s*", identifier))
	}
}

// GetPerformanceReport 获取性能报告
func (s *DatabaseOptimizationService) GetPerformanceReport() map[string]interface{} {
	if !s.config.MonitorEnabled {
		return map[string]interface{}{
			"message": "性能监控未启用",
		}
	}

	report := s.performanceMonitor.GetPerformanceReport()

	// 添加缓存统计
	if s.config.CacheEnabled {
		if cacheStats, err := s.cacheService.GetCacheStats(context.Background()); err == nil {
			report["cache_stats"] = cacheStats
		}
	}

	return report
}

// OptimizeQuery 优化查询执行
func (s *DatabaseOptimizationService) OptimizeQuery(
	ctx context.Context,
	queryFunc func(context.Context) (interface{}, error),
	cacheKey string,
	cacheType cache.QueryType,
) (interface{}, error) {
	startTime := util.NowBeijing()
	var result interface{}
	var err error
	cacheHit := false

	// 尝试从缓存获取
	if s.config.CacheEnabled && cacheKey != "" {
		if hit, cacheErr := s.cacheService.Get(ctx, cacheType, cacheKey, &result); cacheErr == nil && hit {
			cacheHit = true
			s.logger.Debug("查询缓存命中",
				zap.String("cache_key", cacheKey),
				zap.String("cache_type", string(cacheType)))
		}
	}

	// 如果缓存未命中，执行查询
	if !cacheHit {
		result, err = queryFunc(ctx)
		if err != nil {
			return nil, err
		}

		// 缓存结果
		if s.config.CacheEnabled && cacheKey != "" {
			if cacheErr := s.cacheService.Set(ctx, cacheType, cacheKey, result); cacheErr != nil {
				s.logger.Warn("缓存设置失败",
					zap.String("cache_key", cacheKey),
					zap.Error(cacheErr))
			}
		}
	}

	// 记录性能指标
	duration := time.Since(startTime)
	s.RecordQueryMetrics(
		fmt.Sprintf("OptimizeQuery_%s", cacheType),
		duration,
		0, // rowsAffected
		1, // rowsReturned (假设返回一个结果)
		cacheHit,
	)

	return result, nil
}

// warmupCache 预热缓存
func (s *DatabaseOptimizationService) warmupCache(ctx context.Context) {
	s.logger.Info("开始系统缓存预热")

	// 创建带超时的上下文，避免预热阻塞服务启动
	warmupCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// 并发预热各种缓存
	var wg sync.WaitGroup

	// 预热快递公司数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.warmupExpressCompanies(warmupCtx)
	}()

	// 预热订单统计数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.warmupRecentOrderStats(warmupCtx)
	}()

	// 预热重量档位缓存（热门路线）
	wg.Add(1)
	go func() {
		defer wg.Done()
		s.warmupPopularRoutes(warmupCtx)
	}()

	// 等待所有预热任务完成
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		s.logger.Info("系统缓存预热完成")
	case <-warmupCtx.Done():
		s.logger.Warn("系统缓存预热超时，部分预热任务可能未完成")
	}
}

// warmupExpressCompanies 预热快递公司数据
func (s *DatabaseOptimizationService) warmupExpressCompanies(ctx context.Context) {
	query := `
		SELECT code, name, english_name, is_active
		FROM express_companies 
		WHERE is_active = true
		ORDER BY sort_order DESC
	`

	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		s.logger.Error("预热快递公司数据失败", zap.Error(err))
		return
	}
	defer rows.Close()

	var companies []map[string]interface{}
	for rows.Next() {
		var code, name, englishName string
		var isActive bool

		if err := rows.Scan(&code, &name, &englishName, &isActive); err != nil {
			continue
		}

		companies = append(companies, map[string]interface{}{
			"code":         code,
			"name":         name,
			"english_name": englishName,
			"is_active":    isActive,
		})
	}

	// 缓存快递公司数据
	cacheKey := "express_companies_active"
	if err := s.cacheService.Set(ctx, cache.QueryTypeExpressCompany, cacheKey, companies); err != nil {
		s.logger.Error("缓存快递公司数据失败", zap.Error(err))
	} else {
		s.logger.Debug("快递公司数据预热完成", zap.Int("count", len(companies)))
	}
}

// warmupRecentOrderStats 预热最近订单统计
func (s *DatabaseOptimizationService) warmupRecentOrderStats(ctx context.Context) {
	query := `
		SELECT 
			status,
			provider,
			COUNT(*) as count,
			SUM(price) as total_amount
		FROM order_records 
		WHERE created_at >= NOW() - INTERVAL '24 hours'
		GROUP BY status, provider
	`

	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		s.logger.Error("预热订单统计数据失败", zap.Error(err))
		return
	}
	defer rows.Close()

	var stats []map[string]interface{}
	for rows.Next() {
		var status, provider string
		var count int64
		var totalAmount float64

		if err := rows.Scan(&status, &provider, &count, &totalAmount); err != nil {
			continue
		}

		stats = append(stats, map[string]interface{}{
			"status":       status,
			"provider":     provider,
			"count":        count,
			"total_amount": totalAmount,
		})
	}

	// 缓存统计数据
	cacheKey := "order_stats_24h"
	if err := s.cacheService.Set(ctx, cache.QueryTypeOrderStats, cacheKey, stats); err != nil {
		s.logger.Error("缓存订单统计数据失败", zap.Error(err))
	} else {
		s.logger.Debug("订单统计数据预热完成", zap.Int("count", len(stats)))
	}
}

// warmupPopularRoutes 预热热门路线的重量档位缓存
func (s *DatabaseOptimizationService) warmupPopularRoutes(ctx context.Context) {
	s.logger.Info("开始预热热门路线缓存")

	// 定义热门路线
	popularRoutes := []*model.RouteDefinition{
		{RouteKey: "北京市->上海市"},
		{RouteKey: "上海市->北京市"},
		{RouteKey: "北京市->广东省"},
		{RouteKey: "广东省->北京市"},
		{RouteKey: "上海市->广东省"},
		{RouteKey: "广东省->上海市"},
		{RouteKey: "北京市->浙江省"},
		{RouteKey: "浙江省->北京市"},
	}

	// 常用供应商
	providers := []string{"kuaidi100", "cainiao", "yida", "yuntong"}

	// 常用重量
	weights := []int{1, 2, 3, 5, 10}

	// 如果有重量档位缓存服务，调用其预热方法
	if s.weightCacheService != nil {
		if err := s.weightCacheService.WarmupCache(ctx, popularRoutes, providers, weights, []string{}); err != nil {
			s.logger.Error("预热热门路线缓存失败", zap.Error(err))
		} else {
			s.logger.Info("热门路线缓存预热完成",
				zap.Int("routes", len(popularRoutes)),
				zap.Int("providers", len(providers)),
				zap.Int("weights", len(weights)))
		}
	} else {
		s.logger.Warn("重量档位缓存服务未初始化，跳过热门路线预热")
	}
}

// GetOptimizationMetrics 获取优化指标
func (s *DatabaseOptimizationService) GetOptimizationMetrics() map[string]interface{} {
	metrics := map[string]interface{}{
		"config": map[string]interface{}{
			"cache_enabled":             s.config.CacheEnabled,
			"monitor_enabled":           s.config.MonitorEnabled,
			"slow_query_threshold_ms":   s.config.SlowQueryThreshold.Milliseconds(),
			"default_page_size":         s.config.DefaultPageSize,
			"max_page_size":             s.config.MaxPageSize,
			"deep_pagination_threshold": s.config.DeepPaginationThreshold,
		},
		"timestamp": util.NowBeijing(),
	}

	// 添加性能统计
	if s.config.MonitorEnabled {
		if stats := s.performanceMonitor.GetStats(); stats != nil {
			metrics["performance"] = stats
		}
	}

	// 添加缓存统计
	if s.config.CacheEnabled {
		if cacheStats, err := s.cacheService.GetCacheStats(context.Background()); err == nil {
			metrics["cache"] = cacheStats
		}
	}

	return metrics
}
