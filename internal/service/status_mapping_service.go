package service

import (
	"context"
	"fmt"
	"sync"

	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/model"
)

// StatusMappingService 状态映射服务
type StatusMappingService struct {
	logger   *zap.Logger
	mappings map[string]*model.ProviderStatusMapping
	mutex    sync.RWMutex
}

// NewStatusMappingService 创建状态映射服务
func NewStatusMappingService(logger *zap.Logger) *StatusMappingService {
	service := &StatusMappingService{
		logger:   logger,
		mappings: make(map[string]*model.ProviderStatusMapping),
	}

	// 初始化默认映射
	service.initializeDefaultMappings()

	return service
}

// initializeDefaultMappings 初始化默认状态映射
func (s *StatusMappingService) initializeDefaultMappings() {
	// 快递100状态映射
	s.mappings["kuaidi100"] = &model.ProviderStatusMapping{
		ProviderCode:  "kuaidi100",
		ProviderName:  "快递100",
		DefaultStatus: model.StatusUnknown,
		SupportedOps:  []string{"query_order", "sync_status"},
		StatusMappings: map[string]model.SystemOrderStatus{
			"0":   model.StatusSubmitted,         // 下单成功
			"1":   model.StatusAssigned,          // 已接单
			"2":   model.StatusAwaitingPickup,    // 收件中
			"9":   model.StatusCancelled,         // 用户主动取消
			"10":  model.StatusPickedUp,          // 已取件
			"11":  model.StatusPickupFailed,      // 揽货失败
			"12":  model.StatusReturned,          // 已退回
			"13":  model.StatusDelivered,         // 已签收
			"14":  model.StatusDeliveredAbnormal, // 异常签收
			"15":  model.StatusBilled,            // 已结算
			"99":  model.StatusCancelled,         // 订单已取消
			"101": model.StatusInTransit,         // 运输中
			"155": model.StatusWeightUpdated,     // 修改重量
			"166": model.StatusRevived,           // 订单复活
			"200": model.StatusSubmitted,         // 已出单
			"201": model.StatusSubmitFailed,      // 出单失败
			"400": model.StatusOutForDelivery,    // 派送中
			"610": model.StatusSubmitFailed,      // 下单失败
		},
	}

	// 云通状态映射
	s.mappings["yuntong"] = &model.ProviderStatusMapping{
		ProviderCode:  "yuntong",
		ProviderName:  "云通",
		DefaultStatus: model.StatusUnknown,
		SupportedOps:  []string{"query_order", "sync_status"},
		StatusMappings: map[string]model.SystemOrderStatus{
			"0": model.StatusSubmitted, // 已下单
			"1": model.StatusAssigned,  // 已接单
			"2": model.StatusInTransit, // 运输中
			"3": model.StatusDelivered, // 已签收
			"4": model.StatusException, // 异常
			"5": model.StatusReturned,  // 已退回
			"9": model.StatusCancelled, // 已取消
		},
	}

	// 易达状态映射
	s.mappings["yida"] = &model.ProviderStatusMapping{
		ProviderCode:  "yida",
		ProviderName:  "易达",
		DefaultStatus: model.StatusUnknown,
		SupportedOps:  []string{"query_order", "sync_status"}, // 支持查询和状态同步
		StatusMappings: map[string]model.SystemOrderStatus{
			"0":  model.StatusSubmitted,      // 已下单
			"1":  model.StatusAwaitingPickup, // 等待揽收
			"2":  model.StatusInTransit,      // 运输中
			"3":  model.StatusDelivered,      // 已签收
			"6":  model.StatusException,      // 异常
			"10": model.StatusCancelled,      // 已取消
			"11": model.StatusPickedUp,       // 已揽收
		},
	}

	// 菜鸟裹裹状态映射
	s.mappings["cainiao"] = &model.ProviderStatusMapping{
		ProviderCode:  "cainiao",
		ProviderName:  "菜鸟裹裹",
		DefaultStatus: model.StatusUnknown,
		SupportedOps:  []string{"query_order", "sync_status", "callback"},
		StatusMappings: map[string]model.SystemOrderStatus{
			// === 订单状态映射（官方数字编码）===
			"-1": model.StatusCancelled, // 订单已取消
			"0":  model.StatusSubmitted, // 订单已创建
			"20": model.StatusAssigned,  // 已分配运力
			"30": model.StatusPickedUp,  // 已取件
			"40": model.StatusBilled,    // 已完结

			// === 物流状态映射（官方事件编码）===
			"ACCEPT":         model.StatusPickedUp,          // 已揽件 → 已揽收
			"TRANSPORT":      model.StatusInTransit,         // 运输中 → 运输中
			"DELIVERING":     model.StatusOutForDelivery,    // 派送中 → 派送中
			"SIGN":           model.StatusDelivered,         // 已签收 → 已签收
			"FAILED":         model.StatusException,         // 异常提醒 → 异常
			"REJECT":         model.StatusDeliveredAbnormal, // 拒签 → 异常签收
			"AGENT_SIGN":     model.StatusAwaitingPickup,    // 待取件 → 等待揽收
			"STA_DELIVERING": model.StatusOutForDelivery,    // 驿站派送中 → 派送中
			"ORDER_TRANSER":  model.StatusForwarded,         // 已转单 → 已转寄
			"REVERSE_RETURN": model.StatusReturned,          // 退货返回 → 已退回

			// === 兼容字符串状态（向后兼容）===
			"CREATED":   model.StatusSubmitted, // 兼容 → 已提交
			"CANCELLED": model.StatusCancelled, // 兼容 → 已取消
			"COMPLETED": model.StatusBilled,    // 兼容 → 已计费

			// === 其他兼容状态 ===
			"TRANSITING": model.StatusInTransit, // 在途 → 运输中
			"DELIVERED":  model.StatusDelivered, // 已签收 → 已签收
			"EXCEPTION":  model.StatusException, // 异常 → 异常
			"RETURNED":   model.StatusReturned,  // 已退回 → 已退回
			"BACK":       model.StatusReturned,  // 退回 → 已退回
		},
	}

	s.logger.Info("状态映射初始化完成",
		zap.Int("provider_count", len(s.mappings)))
}

// MapProviderStatusToSystem 将供应商状态映射为系统状态
func (s *StatusMappingService) MapProviderStatusToSystem(ctx context.Context, provider, providerStatus string) (model.SystemOrderStatus, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	mapping, exists := s.mappings[provider]
	if !exists {
		s.logger.Warn("未找到供应商状态映射",
			zap.String("provider", provider))
		return model.StatusUnknown, fmt.Errorf("未支持的供应商: %s", provider)
	}

	if systemStatus, exists := mapping.StatusMappings[providerStatus]; exists {
		s.logger.Debug("状态映射成功",
			zap.String("provider", provider),
			zap.String("provider_status", providerStatus),
			zap.String("system_status", string(systemStatus)))
		return systemStatus, nil
	}

	s.logger.Warn("未找到状态映射",
		zap.String("provider", provider),
		zap.String("provider_status", providerStatus),
		zap.String("default_status", string(mapping.DefaultStatus)))

	return mapping.DefaultStatus, nil
}

// ValidateStatusTransition 验证状态转换是否有效
func (s *StatusMappingService) ValidateStatusTransition(ctx context.Context, provider string, fromStatus, toStatus model.SystemOrderStatus) (bool, []string) {
	var errors []string

	// 检查基本的状态转换规则
	if !fromStatus.IsValidTransition(toStatus) {
		errors = append(errors, fmt.Sprintf("不允许从 %s 转换到 %s", fromStatus.GetLabel(), toStatus.GetLabel()))
	}

	// 检查状态优先级（防止状态倒退）
	if fromStatus.GetPriority() > toStatus.GetPriority() {
		// 允许某些特殊情况的状态倒退
		allowedDowngrades := map[model.SystemOrderStatus][]model.SystemOrderStatus{
			model.StatusException: {model.StatusInTransit, model.StatusRevived},
			model.StatusReturned:  {model.StatusRevived},
			model.StatusCancelled: {model.StatusRevived},
		}

		if allowed, exists := allowedDowngrades[fromStatus]; exists {
			found := false
			for _, allowedStatus := range allowed {
				if allowedStatus == toStatus {
					found = true
					break
				}
			}
			if !found {
				errors = append(errors, fmt.Sprintf("状态优先级冲突: %s (优先级 %d) -> %s (优先级 %d)",
					fromStatus.GetLabel(), fromStatus.GetPriority(),
					toStatus.GetLabel(), toStatus.GetPriority()))
			}
		} else {
			errors = append(errors, fmt.Sprintf("状态优先级冲突: %s (优先级 %d) -> %s (优先级 %d)",
				fromStatus.GetLabel(), fromStatus.GetPriority(),
				toStatus.GetLabel(), toStatus.GetPriority()))
		}
	}

	// 检查供应商特定规则
	s.mutex.RLock()
	mapping, exists := s.mappings[provider]
	s.mutex.RUnlock()

	if exists && mapping.StatusRules != nil {
		// 这里可以添加供应商特定的状态转换规则检查
		// 暂时保留扩展点
	}

	return len(errors) == 0, errors
}

// IsProviderSupported 检查供应商是否支持状态同步
func (s *StatusMappingService) IsProviderSupported(provider string) bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	mapping, exists := s.mappings[provider]
	if !exists {
		return false
	}

	// 检查是否支持状态同步操作
	for _, op := range mapping.SupportedOps {
		if op == "sync_status" || op == "query_order" {
			return true
		}
	}

	return false
}

// GetSupportedProviders 获取支持状态同步的供应商列表
func (s *StatusMappingService) GetSupportedProviders() []string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	var providers []string
	for providerCode, mapping := range s.mappings {
		for _, op := range mapping.SupportedOps {
			if op == "sync_status" || op == "query_order" {
				providers = append(providers, providerCode)
				break
			}
		}
	}

	return providers
}

// GetProviderMapping 获取供应商状态映射配置
func (s *StatusMappingService) GetProviderMapping(provider string) (*model.ProviderStatusMapping, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	mapping, exists := s.mappings[provider]
	if !exists {
		return nil, fmt.Errorf("未找到供应商 %s 的状态映射", provider)
	}

	// 返回副本以防止外部修改
	result := *mapping
	return &result, nil
}

// UpdateProviderMapping 更新供应商状态映射（用于动态配置）
func (s *StatusMappingService) UpdateProviderMapping(provider string, mapping *model.ProviderStatusMapping) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if mapping == nil {
		return fmt.Errorf("状态映射配置不能为空")
	}

	if mapping.ProviderCode != provider {
		return fmt.Errorf("供应商代码不匹配: %s != %s", mapping.ProviderCode, provider)
	}

	s.mappings[provider] = mapping
	s.logger.Info("供应商状态映射已更新",
		zap.String("provider", provider),
		zap.String("provider_name", mapping.ProviderName),
		zap.Int("status_count", len(mapping.StatusMappings)))

	return nil
}

// GetStatusStatistics 获取状态统计信息
func (s *StatusMappingService) GetStatusStatistics() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_providers":     len(s.mappings),
		"supported_providers": len(s.GetSupportedProviders()),
		"providers":           make(map[string]interface{}),
	}

	for provider, mapping := range s.mappings {
		stats["providers"].(map[string]interface{})[provider] = map[string]interface{}{
			"name":           mapping.ProviderName,
			"status_count":   len(mapping.StatusMappings),
			"supported_ops":  mapping.SupportedOps,
			"default_status": mapping.DefaultStatus,
		}
	}

	return stats
}

// ResolveStatusConflict 解决状态冲突
func (s *StatusMappingService) ResolveStatusConflict(ctx context.Context, currentStatus, newStatus model.SystemOrderStatus, provider string) model.SystemOrderStatus {
	// 如果状态相同，无需解决冲突
	if currentStatus == newStatus {
		return currentStatus
	}

	// 使用优先级解决冲突
	if newStatus.GetPriority() > currentStatus.GetPriority() {
		s.logger.Info("状态冲突解决：使用高优先级状态",
			zap.String("provider", provider),
			zap.String("current_status", string(currentStatus)),
			zap.String("new_status", string(newStatus)),
			zap.Int("current_priority", currentStatus.GetPriority()),
			zap.Int("new_priority", newStatus.GetPriority()))
		return newStatus
	}

	s.logger.Info("状态冲突解决：保持当前状态",
		zap.String("provider", provider),
		zap.String("current_status", string(currentStatus)),
		zap.String("new_status", string(newStatus)),
		zap.Int("current_priority", currentStatus.GetPriority()),
		zap.Int("new_priority", newStatus.GetPriority()))

	return currentStatus
}
