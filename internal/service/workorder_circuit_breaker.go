package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
	"github.com/your-org/go-kuaidi/internal/util"

)

// WorkOrderCircuitBreakerState 熔断器状态
type WorkOrderCircuitBreakerState int

const (
	// CircuitBreakerClosed 熔断器关闭状态（正常工作）
	CircuitBreakerClosed WorkOrderCircuitBreakerState = iota
	// CircuitBreakerOpen 熔断器开启状态（拒绝请求）
	CircuitBreakerOpen
	// CircuitBreakerHalfOpen 熔断器半开状态（试探性允许部分请求）
	CircuitBreakerHalfOpen
)

// WorkOrderCircuitBreakerConfig 熔断器配置
type WorkOrderCircuitBreakerConfig struct {
	// 失败阈值
	FailureThreshold int
	// 时间窗口（秒）
	TimeWindow time.Duration
	// 恢复时间（秒）
	RecoveryTimeout time.Duration
	// 半开状态最大请求数
	HalfOpenMaxRequests int
	// 半开状态成功阈值
	HalfOpenSuccessThreshold int
}

// WorkOrderCircuitBreaker 工单回调熔断器
type WorkOrderCircuitBreaker struct {
	config        WorkOrderCircuitBreakerConfig
	state         WorkOrderCircuitBreakerState
	failureCount  int
	successCount  int
	requestCount  int
	lastFailTime  time.Time
	lastStateTime time.Time
	mu            sync.RWMutex
	logger        *zap.Logger
}

// NewWorkOrderCircuitBreaker 创建工单回调熔断器
func NewWorkOrderCircuitBreaker(config WorkOrderCircuitBreakerConfig, logger *zap.Logger) *WorkOrderCircuitBreaker {
	if config.FailureThreshold <= 0 {
		config.FailureThreshold = 5
	}
	if config.TimeWindow <= 0 {
		config.TimeWindow = 60 * time.Second
	}
	if config.RecoveryTimeout <= 0 {
		config.RecoveryTimeout = 30 * time.Second
	}
	if config.HalfOpenMaxRequests <= 0 {
		config.HalfOpenMaxRequests = 3
	}
	if config.HalfOpenSuccessThreshold <= 0 {
		config.HalfOpenSuccessThreshold = 2
	}

	return &WorkOrderCircuitBreaker{
		config:        config,
		state:         CircuitBreakerClosed,
		lastStateTime: util.NowBeijing(),
		logger:        logger,
	}
}

// Execute 执行受熔断器保护的操作
func (cb *WorkOrderCircuitBreaker) Execute(ctx context.Context, userID string, operation func() error) error {
	// 检查是否允许执行
	if !cb.AllowRequest(userID) {
		return fmt.Errorf("熔断器开启，拒绝请求")
	}

	// 执行操作
	err := operation()
	
	// 记录结果
	if err != nil {
		cb.RecordFailure(userID)
		return err
	} else {
		cb.RecordSuccess(userID)
		return nil
	}
}

// AllowRequest 检查是否允许请求
func (cb *WorkOrderCircuitBreaker) AllowRequest(userID string) bool {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	now := util.NowBeijing()

	switch cb.state {
	case CircuitBreakerClosed:
		// 关闭状态：检查是否需要开启熔断器
		if cb.shouldTrip(now) {
			cb.setState(CircuitBreakerOpen, now)
			cb.logger.Warn("工单回调熔断器开启",
				zap.String("user_id", userID),
				zap.Int("failure_count", cb.failureCount),
				zap.Int("threshold", cb.config.FailureThreshold))
			return false
		}
		return true

	case CircuitBreakerOpen:
		// 开启状态：检查是否可以进入半开状态
		if now.Sub(cb.lastStateTime) >= cb.config.RecoveryTimeout {
			cb.setState(CircuitBreakerHalfOpen, now)
			cb.logger.Info("工单回调熔断器进入半开状态",
				zap.String("user_id", userID))
			return true
		}
		return false

	case CircuitBreakerHalfOpen:
		// 半开状态：限制请求数量
		if cb.requestCount >= cb.config.HalfOpenMaxRequests {
			return false
		}
		cb.requestCount++
		return true

	default:
		return false
	}
}

// RecordSuccess 记录成功
func (cb *WorkOrderCircuitBreaker) RecordSuccess(userID string) {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.successCount++

	switch cb.state {
	case CircuitBreakerHalfOpen:
		if cb.successCount >= cb.config.HalfOpenSuccessThreshold {
			cb.setState(CircuitBreakerClosed, util.NowBeijing())
			cb.logger.Info("工单回调熔断器恢复关闭状态",
				zap.String("user_id", userID),
				zap.Int("success_count", cb.successCount))
		}
	}
}

// RecordFailure 记录失败
func (cb *WorkOrderCircuitBreaker) RecordFailure(userID string) {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.failureCount++
	cb.lastFailTime = util.NowBeijing()

	switch cb.state {
	case CircuitBreakerHalfOpen:
		// 半开状态下的失败直接回到开启状态
		cb.setState(CircuitBreakerOpen, util.NowBeijing())
		cb.logger.Warn("工单回调熔断器从半开状态回到开启状态",
			zap.String("user_id", userID))
	}
}

// shouldTrip 检查是否应该触发熔断
func (cb *WorkOrderCircuitBreaker) shouldTrip(now time.Time) bool {
	// 检查时间窗口
	if now.Sub(cb.lastFailTime) > cb.config.TimeWindow {
		// 重置计数器
		cb.failureCount = 0
		return false
	}

	// 检查失败次数
	return cb.failureCount >= cb.config.FailureThreshold
}

// setState 设置状态
func (cb *WorkOrderCircuitBreaker) setState(state WorkOrderCircuitBreakerState, now time.Time) {
	cb.state = state
	cb.lastStateTime = now
	
	// 重置计数器
	if state == CircuitBreakerClosed || state == CircuitBreakerHalfOpen {
		cb.requestCount = 0
		cb.successCount = 0
		if state == CircuitBreakerClosed {
			cb.failureCount = 0
		}
	}
}

// GetState 获取当前状态
func (cb *WorkOrderCircuitBreaker) GetState() WorkOrderCircuitBreakerState {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.state
}

// GetStateName 获取状态名称
func (cb *WorkOrderCircuitBreaker) GetStateName() string {
	switch cb.GetState() {
	case CircuitBreakerClosed:
		return "closed"
	case CircuitBreakerOpen:
		return "open"
	case CircuitBreakerHalfOpen:
		return "half-open"
	default:
		return "unknown"
	}
}

// GetMetrics 获取熔断器指标
func (cb *WorkOrderCircuitBreaker) GetMetrics() map[string]interface{} {
	cb.mu.RLock()
	defer cb.mu.RUnlock()

	return map[string]interface{}{
		"state":           cb.GetStateName(),
		"failure_count":   cb.failureCount,
		"success_count":   cb.successCount,
		"request_count":   cb.requestCount,
		"last_fail_time":  cb.lastFailTime.Format(time.RFC3339),
		"last_state_time": cb.lastStateTime.Format(time.RFC3339),
		"config": map[string]interface{}{
			"failure_threshold":           cb.config.FailureThreshold,
			"time_window_seconds":         cb.config.TimeWindow.Seconds(),
			"recovery_timeout_seconds":    cb.config.RecoveryTimeout.Seconds(),
			"half_open_max_requests":      cb.config.HalfOpenMaxRequests,
			"half_open_success_threshold": cb.config.HalfOpenSuccessThreshold,
		},
	}
}

// Reset 重置熔断器
func (cb *WorkOrderCircuitBreaker) Reset() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.state = CircuitBreakerClosed
	cb.failureCount = 0
	cb.successCount = 0
	cb.requestCount = 0
	cb.lastStateTime = util.NowBeijing()
	
	cb.logger.Info("工单回调熔断器已重置")
} 