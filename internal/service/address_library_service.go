package service

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// AddressLibraryService 地址库服务接口
type AddressLibraryService interface {
	// LoadAddressLibrary 加载地址库
	LoadAddressLibrary() error

	// GetAreaCascader 获取地区级联数据
	GetAreaCascader() ([]*model.AreaNode, error)

	// FindAreaByCode 根据代码查找地区
	FindAreaByCode(code string) (*model.AreaNode, error)

	// FindAreaByName 根据名称查找地区
	FindAreaByName(name string) ([]*model.AreaNode, error)

	// ValidateAddress 验证地址
	ValidateAddress(provinceCode, cityCode, districtCode string) (bool, error)

	// GetFullAddressPath 获取完整地址路径
	GetFullAddressPath(districtCode string) (string, error)

	// SearchSimilarAreas 搜索相似地区
	SearchSimilarAreas(keyword string, limit int) ([]*model.AreaNode, error)
}

// DefaultAddressLibraryService 默认地址库服务实现
type DefaultAddressLibraryService struct {
	logger      *zap.Logger
	dataPath    string
	areaData    []*model.AreaNode
	areaCodeMap map[string]*model.AreaNode   // 代码到地区的映射
	areaNameMap map[string][]*model.AreaNode // 名称到地区的映射
	mutex       sync.RWMutex
	loaded      bool
}

// NewAddressLibraryService 创建地址库服务
func NewAddressLibraryService(logger *zap.Logger, dataPath string) AddressLibraryService {
	return &DefaultAddressLibraryService{
		logger:      logger,
		dataPath:    dataPath,
		areaCodeMap: make(map[string]*model.AreaNode),
		areaNameMap: make(map[string][]*model.AreaNode),
	}
}

// LoadAddressLibrary 加载地址库
func (s *DefaultAddressLibraryService) LoadAddressLibrary() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.loaded {
		s.logger.Info("地址库已加载，跳过重复加载")
		return nil
	}

	// 构建文件路径
	filePath := filepath.Join(s.dataPath, "getAreaCascaderVo.json")

	s.logger.Info("开始加载地址库", zap.String("filePath", filePath))

	// 读取文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		s.logger.Error("读取地址库文件失败", zap.Error(err))
		return fmt.Errorf("读取地址库文件失败: %w", err)
	}

	// 解析JSON
	var response model.AreaCascaderResponse
	if err := json.Unmarshal(data, &response); err != nil {
		s.logger.Error("解析地址库JSON失败", zap.Error(err))
		return fmt.Errorf("解析地址库JSON失败: %w", err)
	}

	if response.Code != 200 {
		return fmt.Errorf("地址库数据格式错误: code=%d, msg=%s", response.Code, response.Msg)
	}

	// 存储数据
	s.areaData = response.Data

	// 构建索引
	s.buildIndexes()

	s.loaded = true
	s.logger.Info("地址库加载完成",
		zap.Int("provinces", len(s.areaData)),
		zap.Int("totalAreas", len(s.areaCodeMap)))

	return nil
}

// buildIndexes 构建索引
func (s *DefaultAddressLibraryService) buildIndexes() {
	// 清空现有索引
	s.areaCodeMap = make(map[string]*model.AreaNode)
	s.areaNameMap = make(map[string][]*model.AreaNode)

	// 递归构建索引
	var buildIndex func(node *model.AreaNode)
	buildIndex = func(node *model.AreaNode) {
		if node == nil {
			return
		}

		// 代码索引
		s.areaCodeMap[node.Value] = node

		// 名称索引
		name := strings.TrimSpace(node.Label)
		if name != "" {
			s.areaNameMap[name] = append(s.areaNameMap[name], node)
		}

		// 递归处理子节点
		if node.Children != nil {
			for _, child := range node.Children {
				buildIndex(child)
			}
		}
	}

	// 构建索引
	for _, province := range s.areaData {
		buildIndex(province)
	}
}

// GetAreaCascader 获取地区级联数据
func (s *DefaultAddressLibraryService) GetAreaCascader() ([]*model.AreaNode, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.loaded {
		return nil, fmt.Errorf("地址库未加载")
	}

	return s.areaData, nil
}

// FindAreaByCode 根据代码查找地区
func (s *DefaultAddressLibraryService) FindAreaByCode(code string) (*model.AreaNode, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.loaded {
		return nil, fmt.Errorf("地址库未加载")
	}

	area, exists := s.areaCodeMap[code]
	if !exists {
		return nil, fmt.Errorf("未找到代码为 %s 的地区", code)
	}

	return area, nil
}

// FindAreaByName 根据名称查找地区
func (s *DefaultAddressLibraryService) FindAreaByName(name string) ([]*model.AreaNode, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.loaded {
		return nil, fmt.Errorf("地址库未加载")
	}

	areas, exists := s.areaNameMap[name]
	if !exists {
		return nil, fmt.Errorf("未找到名称为 %s 的地区", name)
	}

	return areas, nil
}

// ValidateAddress 验证地址
func (s *DefaultAddressLibraryService) ValidateAddress(provinceCode, cityCode, districtCode string) (bool, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.loaded {
		return false, fmt.Errorf("地址库未加载")
	}

	// 验证省份
	province, exists := s.areaCodeMap[provinceCode]
	if !exists {
		return false, nil
	}

	// 验证城市
	var cityFound bool
	if province.Children != nil {
		for _, city := range province.Children {
			if city.Value == cityCode {
				cityFound = true

				// 验证区县
				if city.Children != nil {
					for _, district := range city.Children {
						if district.Value == districtCode {
							return true, nil
						}
					}
				}
				break
			}
		}
	}

	if !cityFound {
		return false, nil
	}

	return false, nil
}

// GetFullAddressPath 获取完整地址路径
func (s *DefaultAddressLibraryService) GetFullAddressPath(districtCode string) (string, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.loaded {
		return "", fmt.Errorf("地址库未加载")
	}

	_, exists := s.areaCodeMap[districtCode]
	if !exists {
		return "", fmt.Errorf("未找到区县代码: %s", districtCode)
	}

	// 查找完整路径
	var path []string

	// 查找省份和城市
	for _, province := range s.areaData {
		if province.Children != nil {
			for _, city := range province.Children {
				if city.Children != nil {
					for _, dist := range city.Children {
						if dist.Value == districtCode {
							path = append(path, province.Label, city.Label, dist.Label)
							return strings.Join(path, ""), nil
						}
					}
				}
			}
		}
	}

	return "", fmt.Errorf("未找到区县的完整路径: %s", districtCode)
}

// SearchSimilarAreas 搜索相似地区
func (s *DefaultAddressLibraryService) SearchSimilarAreas(keyword string, limit int) ([]*model.AreaNode, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if !s.loaded {
		return nil, fmt.Errorf("地址库未加载")
	}

	if limit <= 0 {
		limit = 10
	}

	var results []*model.AreaNode
	keyword = strings.TrimSpace(strings.ToLower(keyword))

	// 搜索匹配的地区
	for name, areas := range s.areaNameMap {
		if strings.Contains(strings.ToLower(name), keyword) {
			results = append(results, areas...)
			if len(results) >= limit {
				break
			}
		}
	}

	// 限制结果数量
	if len(results) > limit {
		results = results[:limit]
	}

	return results, nil
}
