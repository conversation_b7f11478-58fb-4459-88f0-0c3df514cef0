package service

import (
	"fmt"
	"log"

	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/utils"
	"go.uber.org/zap"
)

// PriceValidator 价格验证器
// 职责: 验证价格查询请求参数和预处理数据
// 遵循单一职责原则 (SRP)
type PriceValidator struct {
	volumeCalc *utils.VolumeWeightCalculator
	logger     *zap.Logger
}

// NewPriceValidator 创建价格验证器
func NewPriceValidator(logger *zap.Logger, expressCompanyRepo express.ExpressCompanyRepository) *PriceValidator {
	return &PriceValidator{
		volumeCalc: utils.NewVolumeWeightCalculator(logger, expressCompanyRepo),
		logger:     logger,
	}
}

// ValidateRequest 验证价格查询请求
func (v *PriceValidator) ValidateRequest(req *model.PriceRequest) error {
	// 验证寄件人信息
	if req.Sender.Province == "" || req.Sender.City == "" {
		return fmt.Errorf("寄件人省市不能为空")
	}

	// 验证收件人信息
	if req.Receiver.Province == "" || req.Receiver.City == "" {
		return fmt.Errorf("收件人省市不能为空")
	}

	// 验证包裹信息
	if req.Package.Weight <= 0 {
		return fmt.Errorf("包裹重量必须大于0")
	}

	return nil
}

// PreprocessVolumeWeight 预处理体积重量
// 为单个快递公司预处理体积重量，在查价前计算体积重量，确保供应商获得正确的计费重量
func (v *PriceValidator) PreprocessVolumeWeight(req *model.PriceRequest) {
	// 如果没有体积信息，跳过处理
	if req.Package.Volume <= 0 {
		return
	}

	// 如果指定了快递公司，使用该公司的抛比计算
	if req.ExpressType != "" {
		originalWeight := req.Package.Weight
		chargedWeight, err := v.volumeCalc.CalculateChargedWeight(req.ExpressType, req.Package.Weight, req.Package.Volume)
		if err == nil {
			// 更新请求中的重量为计费重量
			req.Package.Weight = chargedWeight
			log.Printf("单快递公司体积重量预处理完成: %s, 原重量=%.3fkg, 计费重量=%.3fkg",
				req.ExpressType, originalWeight, chargedWeight)
			return
		} else {
			// 如果计算失败，报错而不是跳过
			v.logger.Error("体积重量预处理失败",
				zap.String("express_type", req.ExpressType),
				zap.Error(err))
			panic(fmt.Sprintf("体积重量预处理失败: %v。请检查快递公司 %s 的配置", err, req.ExpressType))
		}
	}

	// 如果没有指定快递公司，报错
	v.logger.Error("体积重量预处理时缺少快递公司代码")
	panic("体积重量预处理时缺少快递公司代码。请指定具体的快递公司")
}

// ShouldPreprocessVolumeWeight 判断是否需要预处理体积重量
// 只有在查询单个快递公司时才预处理体积重量
// 查询多个快递公司时，让每个快递公司使用自己的抛比计算
func (v *PriceValidator) ShouldPreprocessVolumeWeight(req *model.PriceRequest) bool {
	return req.ExpressType != ""
}
