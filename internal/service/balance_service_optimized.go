package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// 🚀 性能优化版余额服务
type OptimizedBalanceService struct {
	*DefaultBalanceService
	cache          *TransactionCache
	queryOptimizer *QueryOptimizer
	batchProcessor *BatchProcessor
}

// TransactionCache 交易记录缓存（简化版，仅使用内存缓存）
type TransactionCache struct {
	cacheTimeout time.Duration
	mu           sync.RWMutex
	localCache   map[string]*CacheEntry
}

type CacheEntry struct {
	Data      interface{}
	ExpiresAt time.Time
}

// QueryOptimizer 查询优化器
type QueryOptimizer struct {
	logger         *zap.Logger
	slowQueryLimit time.Duration
}

// BatchProcessor 批处理器
type BatchProcessor struct {
	orderInfoCache map[string]*OrderInfo
	mu             sync.RWMutex
	logger         *zap.Logger
	balanceRepo    repository.BalanceRepository // 余额仓储
	orderRepo      repository.OrderRepository   // 订单仓储，用于查询运单号
}

type OrderInfo struct {
	OrderNo         string // 供应商订单号
	PlatformOrderNo string // 平台订单号
	CustomerOrderNo string // 客户订单号
	TrackingNo      string // 运单号
	CachedAt        time.Time
}

// NewOptimizedBalanceService 创建性能优化版余额服务
func NewOptimizedBalanceService(
	baseService *DefaultBalanceService,
	logger *zap.Logger,
) *OptimizedBalanceService {

	cache := &TransactionCache{
		cacheTimeout: 30 * time.Second, // 🔥 修复：缩短缓存时间到30秒，确保退款记录及时显示
		localCache:   make(map[string]*CacheEntry),
	}

	optimizer := &QueryOptimizer{
		logger:         logger,
		slowQueryLimit: 100 * time.Millisecond,
	}

	batchProcessor := &BatchProcessor{
		orderInfoCache: make(map[string]*OrderInfo),
		logger:         logger,
		balanceRepo:    baseService.repository,
		orderRepo:      baseService.orderRepository, // 添加订单仓储引用
	}

	return &OptimizedBalanceService{
		DefaultBalanceService: baseService,
		cache:                 cache,
		queryOptimizer:        optimizer,
		batchProcessor:        batchProcessor,
	}
}

// 🚀 优化版交易历史查询 - 核心优化方法
func (s *OptimizedBalanceService) GetTransactionHistoryWithFiltersOptimized(
	ctx context.Context,
	userID string,
	limit, offset int,
	typeFilter, statusFilter, startTimeStr, endTimeStr, customerOrderNo, orderNo, trackingNo string,
) ([]*model.TransactionResponse, int64, error) {

	queryStartTime := util.NowBeijing()
	defer func() {
		duration := time.Since(queryStartTime)
		if duration > s.queryOptimizer.slowQueryLimit {
			s.queryOptimizer.logger.Warn("慢查询检测",
				zap.String("user_id", userID),
				zap.Duration("duration", duration),
				zap.Int("limit", limit),
				zap.Int("offset", offset),
			)
		}
	}()

	// 1. 检查是否强制刷新（通过参数检测）
	forceRefresh := strings.Contains(startTimeStr, "_t=") || strings.Contains(endTimeStr, "_t=") ||
		strings.Contains(customerOrderNo, "_t=") || strings.Contains(orderNo, "_t=") ||
		strings.Contains(trackingNo, "_t=")

	// 2. 尝试从缓存获取（除非强制刷新）
	cacheKey := s.buildCacheKey(userID, limit, offset, typeFilter, statusFilter, startTimeStr, endTimeStr, customerOrderNo, orderNo, trackingNo)
	if !forceRefresh {
		if cachedResult := s.cache.Get(cacheKey); cachedResult != nil {
			s.queryOptimizer.logger.Debug("缓存命中", zap.String("cache_key", cacheKey))
			result := cachedResult.(*CachedTransactionResult)
			return result.Transactions, result.Total, nil
		}
	} else {
		s.queryOptimizer.logger.Debug("强制刷新，跳过缓存", zap.String("cache_key", cacheKey))
	}

	// 2. 优化版数据库查询
	transactions, total, err := s.queryTransactionsOptimized(ctx, userID, limit, offset, typeFilter, statusFilter, startTimeStr, endTimeStr, customerOrderNo, orderNo, trackingNo)
	if err != nil {
		return nil, 0, err
	}

	// 3. 批量预加载订单信息（减少N+1查询问题）
	if err := s.batchProcessor.preloadOrderInfo(ctx, transactions); err != nil {
		s.queryOptimizer.logger.Warn("批量预加载订单信息失败", zap.Error(err))
	}

	// 4. 转换为响应格式（优化版）
	responses := s.convertToResponsesOptimized(transactions)

	// 5. 缓存结果
	result := &CachedTransactionResult{
		Transactions: responses,
		Total:        total,
	}
	s.cache.Set(cacheKey, result)

	s.queryOptimizer.logger.Debug("查询完成",
		zap.String("user_id", userID),
		zap.Int("result_count", len(responses)),
		zap.Int64("total", total),
		zap.Duration("duration", time.Since(queryStartTime)),
	)

	return responses, total, nil
}

type CachedTransactionResult struct {
	Transactions []*model.TransactionResponse
	Total        int64
}

// buildCacheKey 构建缓存键
func (s *OptimizedBalanceService) buildCacheKey(userID string, limit, offset int, filters ...string) string {
	var keyParts []string
	keyParts = append(keyParts, "txn_history", userID, fmt.Sprintf("%d_%d", limit, offset))

	for _, filter := range filters {
		if filter != "" {
			keyParts = append(keyParts, filter)
		}
	}

	return strings.Join(keyParts, ":")
}

// queryTransactionsOptimized 优化版数据库查询
func (s *OptimizedBalanceService) queryTransactionsOptimized(
	ctx context.Context,
	userID string,
	limit, offset int,
	typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo string,
) ([]*model.BalanceTransaction, int64, error) {

	// 使用优化后的SQL查询
	// 1. 减少不必要的字段查询
	// 2. 添加查询提示
	// 3. 避免复杂的JOIN操作

	// 对于运单号查询，使用更高效的方式
	if trackingNo != "" {
		return s.queryByTrackingNoOptimized(ctx, userID, limit, offset, trackingNo)
	}

	// 普通查询优化
	transactions, err := s.repository.GetTransactionsByUserIDWithFilters(ctx, userID, limit, offset, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo)
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	total, err := s.repository.GetTransactionCountByUserIDWithFilters(ctx, userID, typeFilter, statusFilter, startTime, endTime, customerOrderNo, orderNo, trackingNo)
	if err != nil {
		return nil, 0, err
	}

	return transactions, total, nil
}

// queryByTrackingNoOptimized 优化版运单号查询
func (s *OptimizedBalanceService) queryByTrackingNoOptimized(
	ctx context.Context,
	userID string,
	limit, offset int,
	trackingNo string,
) ([]*model.BalanceTransaction, int64, error) {

	// 1. 先从缓存查找运单号对应的订单号
	orderNos := s.batchProcessor.getOrderNosByTrackingNo(trackingNo)

	if len(orderNos) == 0 {
		// 2. 从数据库查询运单号对应的订单
		s.queryOptimizer.logger.Debug("从数据库查询运单号对应的订单",
			zap.String("tracking_no", trackingNo))

		if s.batchProcessor.orderRepo != nil {
			// 🔥 使用高并发安全的智能订单查找服务
			smartFinder := NewSmartOrderFinder(s.batchProcessor.orderRepo, s.queryOptimizer.logger)
			orderRecord, err := smartFinder.FindOrderByAnyIdentifier(ctx, trackingNo, "")
			if err == nil && orderRecord != nil {
				// 找到订单，添加到结果中
				orderNos = append(orderNos, orderRecord.OrderNo, orderRecord.CustomerOrderNo)

				// 缓存订单信息
				orderInfo := &OrderInfo{
					OrderNo:         orderRecord.OrderNo,
					CustomerOrderNo: orderRecord.CustomerOrderNo,
					TrackingNo:      orderRecord.TrackingNo,
					CachedAt:        util.NowBeijing(),
				}
				s.batchProcessor.cacheOrderInfo(orderRecord.OrderNo, orderInfo)
				s.batchProcessor.cacheOrderInfo(orderRecord.CustomerOrderNo, orderInfo)

				s.queryOptimizer.logger.Debug("成功通过运单号查询到订单",
					zap.String("tracking_no", trackingNo),
					zap.String("order_no", orderRecord.OrderNo),
					zap.String("customer_order_no", orderRecord.CustomerOrderNo))
			} else {
				s.queryOptimizer.logger.Debug("运单号查询未找到匹配订单",
					zap.String("tracking_no", trackingNo),
					zap.Error(err))
			}
		}
	}

	if len(orderNos) == 0 {
		// 没有找到对应的订单，返回空结果
		return []*model.BalanceTransaction{}, 0, nil
	}

	// 3. 使用订单号查询交易记录（避免复杂JOIN）
	var allTransactions []*model.BalanceTransaction
	var totalCount int64

	for _, orderNo := range orderNos {
		transactions, err := s.repository.GetTransactionsByUserIDWithFilters(ctx, userID, limit, offset, "", "", "", "", "", orderNo, "")
		if err != nil {
			continue
		}

		allTransactions = append(allTransactions, transactions...)

		count, err := s.repository.GetTransactionCountByUserIDWithFilters(ctx, userID, "", "", "", "", "", orderNo, "")
		if err == nil {
			totalCount += count
		}
	}

	// 4. 去重和排序
	allTransactions = s.deduplicateAndSort(allTransactions)

	// 5. 应用分页
	start := offset
	end := offset + limit
	if start > len(allTransactions) {
		return []*model.BalanceTransaction{}, totalCount, nil
	}
	if end > len(allTransactions) {
		end = len(allTransactions)
	}

	return allTransactions[start:end], totalCount, nil
}

// deduplicateAndSort 去重和排序
func (s *OptimizedBalanceService) deduplicateAndSort(transactions []*model.BalanceTransaction) []*model.BalanceTransaction {
	seen := make(map[string]bool)
	var result []*model.BalanceTransaction

	for _, tx := range transactions {
		if !seen[tx.ID] {
			seen[tx.ID] = true
			result = append(result, tx)
		}
	}

	// 按创建时间倒序排序
	for i := 0; i < len(result)-1; i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i].CreatedAt.Before(result[j].CreatedAt) {
				result[i], result[j] = result[j], result[i]
			}
		}
	}

	return result
}

// convertToResponsesOptimized 优化版转换响应格式
func (s *OptimizedBalanceService) convertToResponsesOptimized(transactions []*model.BalanceTransaction) []*model.TransactionResponse {
	responses := make([]*model.TransactionResponse, len(transactions))

	for i, tx := range transactions {
		// 优先从缓存或metadata获取订单信息
		trackingNo, customerOrderNo, platformOrderNo := s.getOrderInfoOptimized(tx)

		responses[i] = &model.TransactionResponse{
			ID:            tx.ID,
			UserID:        tx.UserID,
			Type:          tx.Type,
			Amount:        tx.Amount,
			Currency:      tx.Currency,
			BalanceBefore: tx.BalanceBefore,
			BalanceAfter:  tx.BalanceAfter,

			// 订单关联信息（优化版）
			OrderNo:         tx.OrderNo,      // 供应商订单号（保持向后兼容）
			PlatformOrderNo: platformOrderNo, // 平台订单号
			CustomerOrderNo: customerOrderNo, // 客户订单号
			TrackingNo:      trackingNo,      // 运单号

			// 其他字段
			TransactionCategory: tx.Category,
			TransactionSubType:  tx.SubType,
			ReferenceID:         tx.ReferenceID,
			Description:         tx.Description,
			DetailDescription:   tx.DetailDescription,
			UserFriendlyDesc:    tx.UserFriendlyDesc,
			Metadata:            tx.Metadata,
			BusinessContext:     tx.BusinessContext,

			// 操作信息 - 🔥 修复：添加缺失的字段映射
			OperatorID: tx.OperatorID,
			Status:     tx.Status,
			CreatedAt:  tx.CreatedAt,
		}
	}

	return responses
}

// getOrderInfoOptimized 优化版获取订单信息
func (s *OptimizedBalanceService) getOrderInfoOptimized(tx *model.BalanceTransaction) (trackingNo, customerOrderNo, platformOrderNo string) {
	// 1. 优先从metadata获取
	if tx.Metadata != nil {
		if tNo, ok := tx.Metadata["tracking_no"].(string); ok {
			trackingNo = tNo
		}
		if custOrderNo, ok := tx.Metadata["customer_order_no"].(string); ok {
			customerOrderNo = custOrderNo
		}
		if platOrderNo, ok := tx.Metadata["original_order_no"].(string); ok {
			platformOrderNo = platOrderNo
		}
	}

	// 2. 从交易记录字段获取
	if customerOrderNo == "" {
		customerOrderNo = tx.CustomerOrderNo
	}
	if trackingNo == "" {
		trackingNo = tx.TrackingNo
	}

	// 3. 从缓存获取
	if orderInfo := s.batchProcessor.getOrderInfo(tx.OrderNo); orderInfo != nil {
		if trackingNo == "" {
			trackingNo = orderInfo.TrackingNo
		}
		if customerOrderNo == "" {
			customerOrderNo = orderInfo.CustomerOrderNo
		}
		if platformOrderNo == "" {
			platformOrderNo = orderInfo.PlatformOrderNo // 使用正确的平台订单号字段
		}
	}

	// 4. 如果运单号仍然为空，从订单表查询
	if (trackingNo == "" || platformOrderNo == "") && tx.OrderNo != "" {
		if orderInfo := s.batchProcessor.fetchOrderInfoFromDB(context.Background(), tx.OrderNo); orderInfo != nil {
			if trackingNo == "" {
				trackingNo = orderInfo.TrackingNo
			}
			if customerOrderNo == "" {
				customerOrderNo = orderInfo.CustomerOrderNo
			}
			// 🔥 修复：正确设置平台订单号
			if platformOrderNo == "" {
				platformOrderNo = orderInfo.PlatformOrderNo // 使用订单记录中的真实平台订单号
			}
		}
	}

	// 5. 如果还是没有平台订单号，使用交易记录中的平台订单号
	if platformOrderNo == "" {
		platformOrderNo = tx.PlatformOrderNo // 优先使用交易记录中的平台订单号
	}

	// 6. 最后的备选：如果还是没有，使用供应商订单号
	if platformOrderNo == "" {
		platformOrderNo = tx.OrderNo
	}

	return trackingNo, customerOrderNo, platformOrderNo
}

// BatchProcessor 方法实现
func (bp *BatchProcessor) preloadOrderInfo(ctx context.Context, transactions []*model.BalanceTransaction) error {
	// 批量预加载订单信息，减少数据库查询次数
	orderNos := make([]string, 0, len(transactions))
	for _, tx := range transactions {
		if tx.OrderNo != "" && !bp.hasOrderInfo(tx.OrderNo) {
			orderNos = append(orderNos, tx.OrderNo)
		}
	}

	if len(orderNos) == 0 {
		return nil
	}

	// 这里可以实现批量查询订单信息的逻辑
	// 由于当前架构限制，暂时保持现有逻辑
	bp.logger.Debug("批量预加载订单信息", zap.Int("order_count", len(orderNos)))

	return nil
}

func (bp *BatchProcessor) hasOrderInfo(orderNo string) bool {
	bp.mu.RLock()
	defer bp.mu.RUnlock()

	info, exists := bp.orderInfoCache[orderNo]
	if !exists {
		return false
	}

	// 检查缓存是否过期（5分钟）
	return time.Since(info.CachedAt) < 5*time.Minute
}

func (bp *BatchProcessor) getOrderInfo(orderNo string) *OrderInfo {
	bp.mu.RLock()
	defer bp.mu.RUnlock()

	if info, exists := bp.orderInfoCache[orderNo]; exists {
		if time.Since(info.CachedAt) < 5*time.Minute {
			return info
		}
	}
	return nil
}

func (bp *BatchProcessor) cacheOrderInfo(orderNo string, info *OrderInfo) {
	bp.mu.Lock()
	defer bp.mu.Unlock()

	bp.orderInfoCache[orderNo] = info
}

func (bp *BatchProcessor) getOrderNosByTrackingNo(trackingNo string) []string {
	bp.mu.RLock()
	defer bp.mu.RUnlock()

	var orderNos []string
	for _, info := range bp.orderInfoCache {
		if info.TrackingNo == trackingNo {
			orderNos = append(orderNos, info.OrderNo, info.CustomerOrderNo)
		}
	}
	return orderNos
}

// fetchOrderInfoFromDB 从数据库获取订单信息
func (bp *BatchProcessor) fetchOrderInfoFromDB(ctx context.Context, orderNo string) *OrderInfo {
	// 检查缓存中是否已存在
	if info := bp.getOrderInfo(orderNo); info != nil {
		return info
	}

	// 🔥 使用高并发安全的智能订单查找服务
	if bp.orderRepo != nil {
		smartFinder := NewSmartOrderFinder(bp.orderRepo, bp.logger)
		orderRecord, err := smartFinder.FindOrderByAnyIdentifier(ctx, orderNo, "")
		if err != nil {
			bp.logger.Debug("智能订单查找失败",
				zap.String("order_no", orderNo),
				zap.Error(err))
			return nil
		}

		if orderRecord != nil {
			orderInfo := &OrderInfo{
				OrderNo:         orderRecord.OrderNo,         // 供应商订单号
				PlatformOrderNo: orderRecord.PlatformOrderNo, // 平台订单号
				CustomerOrderNo: orderRecord.CustomerOrderNo, // 客户订单号
				TrackingNo:      orderRecord.TrackingNo,      // 运单号
				CachedAt:        util.NowBeijing(),
			}

			// 缓存订单信息
			bp.cacheOrderInfo(orderNo, orderInfo)

			bp.logger.Debug("成功获取订单信息",
				zap.String("order_no", orderNo),
				zap.String("customer_order_no", orderRecord.CustomerOrderNo),
				zap.String("tracking_no", orderRecord.TrackingNo))

			return orderInfo
		}
	}

	return nil
}

// TransactionCache 方法实现
func (tc *TransactionCache) Get(key string) interface{} {
	tc.mu.RLock()
	defer tc.mu.RUnlock()

	if entry, exists := tc.localCache[key]; exists && util.NowBeijing().Before(entry.ExpiresAt) {
		return entry.Data
	}

	return nil
}

func (tc *TransactionCache) Set(key string, value interface{}) {
	expiresAt := util.NowBeijing().Add(tc.cacheTimeout)

	tc.mu.Lock()
	defer tc.mu.Unlock()

	tc.localCache[key] = &CacheEntry{
		Data:      value,
		ExpiresAt: expiresAt,
	}
}

// CleanupExpired 定期清理过期的本地缓存
func (tc *TransactionCache) CleanupExpired() {
	tc.mu.Lock()
	defer tc.mu.Unlock()

	now := util.NowBeijing()
	for key, entry := range tc.localCache {
		if now.After(entry.ExpiresAt) {
			delete(tc.localCache, key)
		}
	}
}
