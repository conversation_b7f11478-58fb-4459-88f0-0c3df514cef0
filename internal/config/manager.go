package config

import (
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// UserManagementConfig 用户管理配置
type UserManagementConfig struct {
	// 分页配置
	DefaultPageSize int `json:"default_page_size"`
	MaxPageSize     int `json:"max_page_size"`

	// 批量操作配置
	MaxBatchSize int `json:"max_batch_size"`

	// 密码策略配置
	PasswordMinLength int  `json:"password_min_length"`
	PasswordMaxLength int  `json:"password_max_length"`
	RequireUppercase  bool `json:"require_uppercase"`
	RequireLowercase  bool `json:"require_lowercase"`
	RequireNumbers    bool `json:"require_numbers"`
	RequireSpecial    bool `json:"require_special"`

	// 用户名策略配置
	UsernameMinLength int `json:"username_min_length"`
	UsernameMaxLength int `json:"username_max_length"`

	// 会话配置
	OnlineThresholdMinutes int `json:"online_threshold_minutes"`

	// 审计配置
	EnableAuditLog bool `json:"enable_audit_log"`

	// 缓存配置
	CacheEnabled bool          `json:"cache_enabled"`
	CacheTTL     time.Duration `json:"cache_ttl"`
}

// RoleConfig 角色配置
type RoleConfig struct {
	ID          string `json:"id" db:"id"`
	Name        string `json:"name" db:"name"`
	Description string `json:"description" db:"description"`
	IsSystem    bool   `json:"is_system" db:"is_system"`
	IsActive    bool   `json:"is_active" db:"is_active"`
}

// PermissionConfig 权限配置
type PermissionConfig struct {
	ID          string `json:"id" db:"id"`
	Name        string `json:"name" db:"name"`
	Resource    string `json:"resource" db:"resource"`
	Action      string `json:"action" db:"action"`
	Description string `json:"description" db:"description"`
	IsSystem    bool   `json:"is_system" db:"is_system"`
}

// ConfigManager 统一配置管理器
type ConfigManager struct {
	viper  *viper.Viper
	logger *zap.Logger
	mu     sync.RWMutex
}

// EnhancedPerformanceConfig 增强性能配置
type EnhancedPerformanceConfig struct {
	// 查价接口配置
	PriceQuery PriceQueryConfig `mapstructure:"price_query"`

	// 缓存配置
	Cache CacheConfig `mapstructure:"cache"`

	// 超时配置
	Timeout TimeoutConfig `mapstructure:"timeout"`

	// 重试配置
	Retry RetryConfig `mapstructure:"retry"`

	// 并发配置
	Concurrency ConcurrencyConfig `mapstructure:"concurrency"`
}

// PriceQueryConfig 查价配置
type PriceQueryConfig struct {
	// 快速响应策略配置
	FastResponse FastResponseConfig `mapstructure:"fast_response"`

	// 降级策略配置
	Fallback FallbackConfig `mapstructure:"fallback"`

	// 默认值配置
	Defaults DefaultsConfig `mapstructure:"defaults"`
}

// FastResponseConfig 快速响应配置
type FastResponseConfig struct {
	// 是否启用快速响应
	Enabled bool `mapstructure:"enabled"`

	// 最大等待时间（毫秒）
	MaxWaitMs int `mapstructure:"max_wait_ms"`

	// 第一阶段超时（毫秒）
	FirstStageTimeoutMs int `mapstructure:"first_stage_timeout_ms"`

	// 供应商超时（毫秒）
	ProviderTimeoutMs int `mapstructure:"provider_timeout_ms"`

	// 最小结果数量
	MinResults int `mapstructure:"min_results"`
}

// FallbackConfig 降级配置
type FallbackConfig struct {
	// 是否启用降级
	Enabled bool `mapstructure:"enabled"`

	// 降级超时（毫秒）
	TimeoutMs int `mapstructure:"timeout_ms"`

	// 优先供应商列表
	PreferredProviders []string `mapstructure:"preferred_providers"`
}

// DefaultsConfig 默认值配置
type DefaultsConfig struct {
	// 默认区县
	District string `mapstructure:"district"`

	// 默认寄件人信息
	SenderName   string `mapstructure:"sender_name"`
	SenderMobile string `mapstructure:"sender_mobile"`

	// 默认收件人信息
	ReceiverName   string `mapstructure:"receiver_name"`
	ReceiverMobile string `mapstructure:"receiver_mobile"`

	// 默认货物名称
	GoodsName string `mapstructure:"goods_name"`

	// 默认数量
	Quantity int `mapstructure:"quantity"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	// 价格缓存时间（分钟）
	PriceTTLMinutes int `mapstructure:"price_ttl_minutes"`

	// 快速查询缓存时间（分钟）
	FastQueryTTLMinutes int `mapstructure:"fast_query_ttl_minutes"`

	// 降级查询缓存时间（分钟）
	FallbackTTLMinutes int `mapstructure:"fallback_ttl_minutes"`

	// 预热缓存时间（分钟）
	WarmupTTLMinutes int `mapstructure:"warmup_ttl_minutes"`
}

// TimeoutConfig 超时配置
type TimeoutConfig struct {
	// 供应商查询超时（毫秒）
	ProviderQueryMs int `mapstructure:"provider_query_ms"`

	// 总体查询超时（毫秒）
	TotalQueryMs int `mapstructure:"total_query_ms"`

	// HTTP客户端超时（秒）
	HTTPClientSeconds int `mapstructure:"http_client_seconds"`

	// 数据库查询超时（秒）
	DatabaseSeconds int `mapstructure:"database_seconds"`

	// Redis操作超时（秒）
	RedisSeconds int `mapstructure:"redis_seconds"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	// 最大重试次数
	MaxAttempts int `mapstructure:"max_attempts"`

	// 重试间隔（毫秒）
	IntervalMs int `mapstructure:"interval_ms"`

	// 退避倍数
	BackoffMultiplier float64 `mapstructure:"backoff_multiplier"`

	// 最大重试间隔（毫秒）
	MaxIntervalMs int `mapstructure:"max_interval_ms"`
}

// ConcurrencyConfig 并发配置
type ConcurrencyConfig struct {
	// 工作池大小
	WorkerPoolSize int `mapstructure:"worker_pool_size"`

	// 任务队列大小
	TaskQueueSize int `mapstructure:"task_queue_size"`

	// 最大并发查询数
	MaxConcurrentQueries int `mapstructure:"max_concurrent_queries"`
}

var (
	globalConfigManager *ConfigManager
	configOnce          sync.Once
)

// GetConfigManager 获取全局配置管理器
func GetConfigManager() *ConfigManager {
	configOnce.Do(func() {
		logger, _ := zap.NewProduction()
		globalConfigManager = NewConfigManager(logger)
		if err := globalConfigManager.Load(); err != nil {
			logger.Fatal("Failed to load configuration", zap.Error(err))
		}
	})
	return globalConfigManager
}

// NewConfigManager 创建配置管理器
func NewConfigManager(logger *zap.Logger) *ConfigManager {
	v := viper.New()

	// 设置配置文件名和类型 - 优先使用YAML格式
	v.SetConfigName("config")
	v.SetConfigType("yaml")
	v.AddConfigPath(".")
	v.AddConfigPath("./config")
	v.AddConfigPath("/etc/go-kuaidi")

	// 设置环境变量前缀
	v.SetEnvPrefix("GOKUAIDI")
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	return &ConfigManager{
		viper:  v,
		logger: logger,
	}
}

// Load 加载配置
func (cm *ConfigManager) Load() error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// 设置默认值
	cm.setDefaults()

	// 读取配置文件
	if err := cm.viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			cm.logger.Warn("Configuration file not found, using defaults and environment variables")
		} else {
			return fmt.Errorf("failed to read config file: %w", err)
		}
	}

	// 验证配置
	if err := cm.validate(); err != nil {
		return fmt.Errorf("configuration validation failed: %w", err)
	}

	cm.logger.Info("Configuration loaded successfully",
		zap.String("config_file", cm.viper.ConfigFileUsed()))

	return nil
}

// setDefaults 设置默认值
func (cm *ConfigManager) setDefaults() {
	// 用户管理配置默认值
	cm.viper.SetDefault("user_management.default_page_size", 20)
	cm.viper.SetDefault("user_management.max_page_size", 100)
	cm.viper.SetDefault("user_management.max_batch_size", 100)
	cm.viper.SetDefault("user_management.password_min_length", 8)
	cm.viper.SetDefault("user_management.password_max_length", 72)
	cm.viper.SetDefault("user_management.require_uppercase", true)
	cm.viper.SetDefault("user_management.require_lowercase", true)
	cm.viper.SetDefault("user_management.require_numbers", true)
	cm.viper.SetDefault("user_management.require_special", true)
	cm.viper.SetDefault("user_management.username_min_length", 3)
	cm.viper.SetDefault("user_management.username_max_length", 50)
	cm.viper.SetDefault("user_management.online_threshold_minutes", 15)
	cm.viper.SetDefault("user_management.enable_audit_log", true)
	cm.viper.SetDefault("user_management.cache_enabled", true)
	cm.viper.SetDefault("user_management.cache_ttl", "10m")

	// 性能配置默认值
	cm.viper.SetDefault("performance.price_query.fast_response.enabled", true)
	cm.viper.SetDefault("performance.price_query.fast_response.max_wait_ms", 1500)
	cm.viper.SetDefault("performance.price_query.fast_response.first_stage_timeout_ms", 800)
	cm.viper.SetDefault("performance.price_query.fast_response.provider_timeout_ms", 600)
	cm.viper.SetDefault("performance.price_query.fast_response.min_results", 1)

	cm.viper.SetDefault("performance.price_query.fallback.enabled", true)
	cm.viper.SetDefault("performance.price_query.fallback.timeout_ms", 2000)
	cm.viper.SetDefault("performance.price_query.fallback.preferred_providers", []string{"yida", "kuaidi100"})

	cm.viper.SetDefault("performance.price_query.defaults.district", "市辖区")
	cm.viper.SetDefault("performance.price_query.defaults.sender_name", "寄件人")
	cm.viper.SetDefault("performance.price_query.defaults.sender_mobile", "13800000000")
	cm.viper.SetDefault("performance.price_query.defaults.receiver_name", "收件人")
	cm.viper.SetDefault("performance.price_query.defaults.receiver_mobile", "13900000000")
	cm.viper.SetDefault("performance.price_query.defaults.goods_name", "普通货物")
	cm.viper.SetDefault("performance.price_query.defaults.quantity", 1)

	cm.viper.SetDefault("performance.cache.price_ttl_minutes", 15)
	cm.viper.SetDefault("performance.cache.fast_query_ttl_minutes", 10)
	cm.viper.SetDefault("performance.cache.fallback_ttl_minutes", 5)
	cm.viper.SetDefault("performance.cache.warmup_ttl_minutes", 20)

	cm.viper.SetDefault("performance.timeout.provider_query_ms", 1000)
	cm.viper.SetDefault("performance.timeout.total_query_ms", 3000)
	cm.viper.SetDefault("performance.timeout.http_client_seconds", 10)
	cm.viper.SetDefault("performance.timeout.database_seconds", 5)
	cm.viper.SetDefault("performance.timeout.redis_seconds", 3)

	cm.viper.SetDefault("performance.retry.max_attempts", 3)
	cm.viper.SetDefault("performance.retry.interval_ms", 100)
	cm.viper.SetDefault("performance.retry.backoff_multiplier", 2.0)
	cm.viper.SetDefault("performance.retry.max_interval_ms", 1000)

	cm.viper.SetDefault("performance.concurrency.worker_pool_size", 20)
	cm.viper.SetDefault("performance.concurrency.task_queue_size", 100)
	cm.viper.SetDefault("performance.concurrency.max_concurrent_queries", 50)
}

// validate 验证配置
func (cm *ConfigManager) validate() error {
	// 验证超时配置
	if cm.viper.GetInt("performance.timeout.provider_query_ms") <= 0 {
		return fmt.Errorf("provider query timeout must be positive")
	}

	if cm.viper.GetInt("performance.timeout.total_query_ms") <= 0 {
		return fmt.Errorf("total query timeout must be positive")
	}

	// 验证缓存配置
	if cm.viper.GetInt("performance.cache.price_ttl_minutes") <= 0 {
		return fmt.Errorf("price cache TTL must be positive")
	}

	// 验证并发配置
	if cm.viper.GetInt("performance.concurrency.worker_pool_size") <= 0 {
		return fmt.Errorf("worker pool size must be positive")
	}

	return nil
}

// GetEnhancedPerformanceConfig 获取增强性能配置
func (cm *ConfigManager) GetEnhancedPerformanceConfig() *EnhancedPerformanceConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	var config EnhancedPerformanceConfig
	if err := cm.viper.UnmarshalKey("performance", &config); err != nil {
		cm.logger.Error("Failed to unmarshal performance config", zap.Error(err))
		return cm.getDefaultEnhancedPerformanceConfig()
	}

	return &config
}

// getDefaultEnhancedPerformanceConfig 获取默认增强性能配置
func (cm *ConfigManager) getDefaultEnhancedPerformanceConfig() *EnhancedPerformanceConfig {
	return &EnhancedPerformanceConfig{
		PriceQuery: PriceQueryConfig{
			FastResponse: FastResponseConfig{
				Enabled:             true,
				MaxWaitMs:           1500,
				FirstStageTimeoutMs: 800,
				ProviderTimeoutMs:   600,
				MinResults:          1,
			},
			Fallback: FallbackConfig{
				Enabled:            true,
				TimeoutMs:          2000,
				PreferredProviders: []string{"yida", "kuaidi100"},
			},
			Defaults: DefaultsConfig{
				District:       "市辖区",
				SenderName:     "寄件人",
				SenderMobile:   "13800000000",
				ReceiverName:   "收件人",
				ReceiverMobile: "13900000000",
				GoodsName:      "普通货物",
				Quantity:       1,
			},
		},
		Cache: CacheConfig{
			PriceTTLMinutes:     15,
			FastQueryTTLMinutes: 10,
			FallbackTTLMinutes:  5,
			WarmupTTLMinutes:    20,
		},
		Timeout: TimeoutConfig{
			ProviderQueryMs:   1000,
			TotalQueryMs:      3000,
			HTTPClientSeconds: 10,
			DatabaseSeconds:   5,
			RedisSeconds:      3,
		},
		Retry: RetryConfig{
			MaxAttempts:       3,
			IntervalMs:        100,
			BackoffMultiplier: 2.0,
			MaxIntervalMs:     1000,
		},
		Concurrency: ConcurrencyConfig{
			WorkerPoolSize:       20,
			TaskQueueSize:        100,
			MaxConcurrentQueries: 50,
		},
	}
}

// GetString 获取字符串配置，支持环境变量替换
func (cm *ConfigManager) GetString(key string) string {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	value := cm.viper.GetString(key)
	return cm.expandEnvVars(value)
}

// GetInt 获取整数配置
func (cm *ConfigManager) GetInt(key string) int {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.viper.GetInt(key)
}

// GetBool 获取布尔配置
func (cm *ConfigManager) GetBool(key string) bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.viper.GetBool(key)
}

// GetDuration 获取时间间隔配置
func (cm *ConfigManager) GetDuration(key string) time.Duration {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.viper.GetDuration(key)
}

// GetStringSlice 获取字符串切片配置
func (cm *ConfigManager) GetStringSlice(key string) []string {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.viper.GetStringSlice(key)
}

// GetEnvString 获取环境变量字符串
func GetEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// GetEnvInt 获取环境变量整数
func GetEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// GetEnvBool 获取环境变量布尔值
func GetEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// expandEnvVars 展开环境变量，支持 ${VAR:-default} 语法
func (cm *ConfigManager) expandEnvVars(value string) string {
	// 匹配 ${VAR:-default} 或 ${VAR} 格式
	re := regexp.MustCompile(`\$\{([^}]+)\}`)

	return re.ReplaceAllStringFunc(value, func(match string) string {
		// 移除 ${ 和 }
		envExpr := match[2 : len(match)-1]

		// 检查是否有默认值 (VAR:-default)
		if strings.Contains(envExpr, ":-") {
			parts := strings.SplitN(envExpr, ":-", 2)
			envVar := parts[0]
			defaultVal := parts[1]

			// 移除默认值周围的引号
			if len(defaultVal) >= 2 &&
				((defaultVal[0] == '"' && defaultVal[len(defaultVal)-1] == '"') ||
					(defaultVal[0] == '\'' && defaultVal[len(defaultVal)-1] == '\'')) {
				defaultVal = defaultVal[1 : len(defaultVal)-1]
			}

			if envValue := os.Getenv(envVar); envValue != "" {
				return envValue
			}
			return defaultVal
		}

		// 没有默认值，直接获取环境变量
		return os.Getenv(envExpr)
	})
}

// GetUserManagementConfig 获取用户管理配置
func (cm *ConfigManager) GetUserManagementConfig() *UserManagementConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	return &UserManagementConfig{
		DefaultPageSize:        cm.viper.GetInt("user_management.default_page_size"),
		MaxPageSize:            cm.viper.GetInt("user_management.max_page_size"),
		MaxBatchSize:           cm.viper.GetInt("user_management.max_batch_size"),
		PasswordMinLength:      cm.viper.GetInt("user_management.password_min_length"),
		PasswordMaxLength:      cm.viper.GetInt("user_management.password_max_length"),
		RequireUppercase:       cm.viper.GetBool("user_management.require_uppercase"),
		RequireLowercase:       cm.viper.GetBool("user_management.require_lowercase"),
		RequireNumbers:         cm.viper.GetBool("user_management.require_numbers"),
		RequireSpecial:         cm.viper.GetBool("user_management.require_special"),
		UsernameMinLength:      cm.viper.GetInt("user_management.username_min_length"),
		UsernameMaxLength:      cm.viper.GetInt("user_management.username_max_length"),
		OnlineThresholdMinutes: cm.viper.GetInt("user_management.online_threshold_minutes"),
		EnableAuditLog:         cm.viper.GetBool("user_management.enable_audit_log"),
		CacheEnabled:           cm.viper.GetBool("user_management.cache_enabled"),
		CacheTTL:               cm.viper.GetDuration("user_management.cache_ttl"),
	}
}

// GetRole 获取角色配置（模拟实现，实际应从数据库获取）
func (cm *ConfigManager) GetRole(roleID string) (*RoleConfig, bool) {
	// 这里应该从数据库获取，暂时使用硬编码的默认值
	defaultRoles := map[string]*RoleConfig{
		"admin": {
			ID:          "admin",
			Name:        "管理员",
			Description: "系统管理员",
			IsSystem:    true,
			IsActive:    true,
		},
		"user": {
			ID:          "user",
			Name:        "普通用户",
			Description: "普通用户",
			IsSystem:    true,
			IsActive:    true,
		},
		"system_admin": {
			ID:          "system_admin",
			Name:        "系统管理员",
			Description: "超级管理员",
			IsSystem:    true,
			IsActive:    true,
		},
	}

	role, exists := defaultRoles[roleID]
	if !exists {
		return nil, false
	}

	// 返回副本
	roleCopy := *role
	return &roleCopy, true
}

// GetAllRoles 获取所有角色
func (cm *ConfigManager) GetAllRoles() []*RoleConfig {
	// 这里应该从数据库获取，暂时使用硬编码的默认值
	defaultRoles := []*RoleConfig{
		{
			ID:          "admin",
			Name:        "管理员",
			Description: "系统管理员",
			IsSystem:    true,
			IsActive:    true,
		},
		{
			ID:          "user",
			Name:        "普通用户",
			Description: "普通用户",
			IsSystem:    true,
			IsActive:    true,
		},
		{
			ID:          "system_admin",
			Name:        "系统管理员",
			Description: "超级管理员",
			IsSystem:    true,
			IsActive:    true,
		},
	}

	// 返回副本
	roles := make([]*RoleConfig, len(defaultRoles))
	for i, role := range defaultRoles {
		roleCopy := *role
		roles[i] = &roleCopy
	}

	return roles
}

// GetPermission 获取权限配置（模拟实现）
func (cm *ConfigManager) GetPermission(permissionID string) (*PermissionConfig, bool) {
	// 这里应该从数据库获取，暂时返回false
	return nil, false
}

// GetPermissionByResourceAction 根据资源和操作获取权限（模拟实现）
func (cm *ConfigManager) GetPermissionByResourceAction(resource, action string) (*PermissionConfig, bool) {
	// 这里应该从数据库获取，暂时返回false
	return nil, false
}

// GetErrorMessage 获取错误消息
func (cm *ConfigManager) GetErrorMessage(code, locale string) string {
	// 默认错误消息映射
	defaultMessages := map[string]map[string]string{
		"zh-CN": {
			"user_not_found":             "用户不存在",
			"user_already_exists":        "用户已存在",
			"invalid_credentials":        "用户名或密码错误",
			"permission_denied":          "权限不足",
			"unauthorized":               "用户未认证",
			"forbidden":                  "禁止访问",
			"invalid_request":            "请求参数错误",
			"internal_error":             "服务器内部错误",
			"conflict":                   "资源冲突",
			"not_found":                  "资源不存在",
			"username_required":          "用户名不能为空",
			"email_required":             "邮箱不能为空",
			"password_required":          "密码不能为空",
			"invalid_email":              "邮箱格式错误",
			"password_too_short":         "密码长度不足",
			"password_too_long":          "密码长度过长",
			"username_too_short":         "用户名长度不足",
			"username_too_long":          "用户名长度过长",
			"invalid_username":           "用户名格式错误",
			"cannot_modify_self":         "不能修改自己的账号",
			"cannot_delete_admin":        "不能删除管理员账号",
			"batch_size_exceeded":        "批量操作数量超出限制",
			"user_ids_empty":             "用户ID列表不能为空",
			"invalid_role_id":            "无效的角色ID",
			"password_require_uppercase": "密码必须包含大写字母",
			"password_require_lowercase": "密码必须包含小写字母",
			"password_require_numbers":   "密码必须包含数字",
			"password_require_special":   "密码必须包含特殊字符",
			"invalid_page":               "页码无效",
			"invalid_page_size":          "分页大小无效",
			"keyword_too_long":           "关键词过长",
			"invalid_time_range":         "时间范围无效",
		},
		"en-US": {
			"user_not_found":      "User not found",
			"user_already_exists": "User already exists",
			"invalid_credentials": "Invalid username or password",
			"permission_denied":   "Permission denied",
			"unauthorized":        "Unauthorized",
			"forbidden":           "Forbidden",
			"invalid_request":     "Invalid request parameters",
			"internal_error":      "Internal server error",
			"conflict":            "Resource conflict",
			"not_found":           "Resource not found",
		},
	}

	if messages, exists := defaultMessages[locale]; exists {
		if message, exists := messages[code]; exists {
			return message
		}
	}

	// 回退到默认语言
	if locale != "zh-CN" {
		if messages, exists := defaultMessages["zh-CN"]; exists {
			if message, exists := messages[code]; exists {
				return message
			}
		}
	}

	// 返回错误代码作为默认消息
	return code
}
