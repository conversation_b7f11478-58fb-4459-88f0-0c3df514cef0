package config

import (
	"database/sql"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	_ "github.com/lib/pq"
)

// 这里使用 config.go 中定义的 DatabaseConfig 和 RedisConfig 结构

// ConnectDatabase 连接到PostgreSQL数据库
func ConnectDatabase(cfg DatabaseConfig) (*sql.DB, error) {
	// 解析连接字符串
	u, err := url.Parse(cfg.ConnectionString)
	if err != nil {
		return nil, fmt.Errorf("解析数据库连接字符串失败: %w", err)
	}

	// 确保是PostgreSQL连接
	if u.Scheme != "postgresql" {
		return nil, fmt.Errorf("不支持的数据库类型: %s", u.Scheme)
	}

	// 连接数据库
	db, err := sql.Open("postgres", cfg.ConnectionString)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)
	if cfg.ConnMaxIdleTime > 0 {
		db.SetConnMaxIdleTime(time.Duration(cfg.ConnMaxIdleTime) * time.Second)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("测试数据库连接失败: %w", err)
	}

	return db, nil
}

// ConnectRedis 连接到Redis
func ConnectRedis(cfg RedisConfig) (*redis.Client, error) {
	// 解析连接字符串
	u, err := url.Parse(cfg.ConnectionString)
	if err != nil {
		return nil, fmt.Errorf("解析Redis连接字符串失败: %w", err)
	}

	// 确保是Redis连接
	if u.Scheme != "redis" {
		return nil, fmt.Errorf("不支持的Redis连接类型: %s", u.Scheme)
	}

	// 解析密码
	password, _ := u.User.Password()

	// 解析端口
	port := u.Port()
	if port == "" {
		port = "6379" // 默认Redis端口
	}

	// 解析数据库索引
	db := cfg.DB
	if dbParam := u.Query().Get("db"); dbParam != "" {
		if dbIndex, err := strconv.Atoi(dbParam); err == nil {
			db = dbIndex
		}
	}

	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%s", u.Hostname(), port),
		Password: password,
		DB:       db,
	})

	return rdb, nil
}
