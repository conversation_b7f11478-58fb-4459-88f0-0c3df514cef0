package config

import (
	"fmt"
	"time"

	"github.com/shopspring/decimal"
)

// BusinessConfig 业务配置
type BusinessConfig struct {
	// 订单配置
	Order OrderConfig `mapstructure:"order" yaml:"order" json:"order"`

	// 计费配置
	Billing BillingConfig `mapstructure:"billing" yaml:"billing" json:"billing"`

	// 余额配置
	Balance BalanceConfig `mapstructure:"balance" yaml:"balance" json:"balance"`

	// 回调配置
	Callback BusinessCallbackConfig `mapstructure:"callback" yaml:"callback" json:"callback"`

	// 事务配置
	Transaction TransactionConfig `mapstructure:"transaction" yaml:"transaction" json:"transaction"`

	// 重试配置
	Retry BusinessRetryConfig `mapstructure:"retry" yaml:"retry" json:"retry"`
}

// OrderConfig 订单配置
type OrderConfig struct {
	// 预扣费配置
	PreChargeEnabled bool          `mapstructure:"pre_charge_enabled" yaml:"pre_charge_enabled" json:"pre_charge_enabled"`
	PreChargeTimeout time.Duration `mapstructure:"pre_charge_timeout" yaml:"pre_charge_timeout" json:"pre_charge_timeout"`

	// 订单超时配置
	CreateTimeout time.Duration `mapstructure:"create_timeout" yaml:"create_timeout" json:"create_timeout"`
	CancelTimeout time.Duration `mapstructure:"cancel_timeout" yaml:"cancel_timeout" json:"cancel_timeout"`

	// 订单限制
	MaxOrdersPerUser int `mapstructure:"max_orders_per_user" yaml:"max_orders_per_user" json:"max_orders_per_user"`
	MaxOrdersPerDay  int `mapstructure:"max_orders_per_day" yaml:"max_orders_per_day" json:"max_orders_per_day"`

	// 重量和体积限制
	MaxWeightKg     decimal.Decimal `mapstructure:"max_weight_kg" yaml:"max_weight_kg" json:"max_weight_kg"`
	MaxVolumeCubicM decimal.Decimal `mapstructure:"max_volume_cubic_m" yaml:"max_volume_cubic_m" json:"max_volume_cubic_m"`
}

// BillingConfig 计费配置
type BillingConfig struct {
	// 费用精度配置
	PrecisionThreshold decimal.Decimal `mapstructure:"precision_threshold" yaml:"precision_threshold" json:"precision_threshold"`
	RoundingMode       string          `mapstructure:"rounding_mode" yaml:"rounding_mode" json:"rounding_mode"`

	// 费用限制
	MaxFeeAmount decimal.Decimal `mapstructure:"max_fee_amount" yaml:"max_fee_amount" json:"max_fee_amount"`
	MinFeeAmount decimal.Decimal `mapstructure:"min_fee_amount" yaml:"min_fee_amount" json:"min_fee_amount"`

	// 差额处理配置
	AutoProcessThreshold  decimal.Decimal `mapstructure:"auto_process_threshold" yaml:"auto_process_threshold" json:"auto_process_threshold"`
	ManualReviewThreshold decimal.Decimal `mapstructure:"manual_review_threshold" yaml:"manual_review_threshold" json:"manual_review_threshold"`

	// 计费超时配置
	ProcessTimeout time.Duration `mapstructure:"process_timeout" yaml:"process_timeout" json:"process_timeout"`

	// 异步处理配置
	AsyncHistoryEnabled bool `mapstructure:"async_history_enabled" yaml:"async_history_enabled" json:"async_history_enabled"`
	HistoryBatchSize    int  `mapstructure:"history_batch_size" yaml:"history_batch_size" json:"history_batch_size"`
}

// BalanceConfig 余额配置
type BalanceConfig struct {
	// 余额限制
	MaxBalance decimal.Decimal `mapstructure:"max_balance" yaml:"max_balance" json:"max_balance"`
	MinBalance decimal.Decimal `mapstructure:"min_balance" yaml:"min_balance" json:"min_balance"`

	// 冻结配置
	FreezeTimeout       time.Duration `mapstructure:"freeze_timeout" yaml:"freeze_timeout" json:"freeze_timeout"`
	AutoUnfreezeEnabled bool          `mapstructure:"auto_unfreeze_enabled" yaml:"auto_unfreeze_enabled" json:"auto_unfreeze_enabled"`

	// 交易限制
	MaxTransactionAmount  decimal.Decimal `mapstructure:"max_transaction_amount" yaml:"max_transaction_amount" json:"max_transaction_amount"`
	DailyTransactionLimit decimal.Decimal `mapstructure:"daily_transaction_limit" yaml:"daily_transaction_limit" json:"daily_transaction_limit"`

	// 并发控制
	ConcurrencyControl bool          `mapstructure:"concurrency_control" yaml:"concurrency_control" json:"concurrency_control"`
	LockTimeout        time.Duration `mapstructure:"lock_timeout" yaml:"lock_timeout" json:"lock_timeout"`
}

// BusinessCallbackConfig 业务回调配置
type BusinessCallbackConfig struct {
	// 处理配置
	MaxConcurrency int           `mapstructure:"max_concurrency" yaml:"max_concurrency" json:"max_concurrency"`
	ProcessTimeout time.Duration `mapstructure:"process_timeout" yaml:"process_timeout" json:"process_timeout"`

	// 重试配置
	MaxRetries         int           `mapstructure:"max_retries" yaml:"max_retries" json:"max_retries"`
	RetryInterval      time.Duration `mapstructure:"retry_interval" yaml:"retry_interval" json:"retry_interval"`
	RetryBackoffFactor float64       `mapstructure:"retry_backoff_factor" yaml:"retry_backoff_factor" json:"retry_backoff_factor"`

	// 转发配置
	ForwardEnabled bool          `mapstructure:"forward_enabled" yaml:"forward_enabled" json:"forward_enabled"`
	ForwardTimeout time.Duration `mapstructure:"forward_timeout" yaml:"forward_timeout" json:"forward_timeout"`

	// 验证配置
	SignatureValidation bool          `mapstructure:"signature_validation" yaml:"signature_validation" json:"signature_validation"`
	TimestampValidation bool          `mapstructure:"timestamp_validation" yaml:"timestamp_validation" json:"timestamp_validation"`
	TimestampTolerance  time.Duration `mapstructure:"timestamp_tolerance" yaml:"timestamp_tolerance" json:"timestamp_tolerance"`
}

// TransactionConfig 事务配置
type TransactionConfig struct {
	// 超时配置
	DefaultTimeout     time.Duration `mapstructure:"default_timeout" yaml:"default_timeout" json:"default_timeout"`
	LongRunningTimeout time.Duration `mapstructure:"long_running_timeout" yaml:"long_running_timeout" json:"long_running_timeout"`

	// 隔离级别
	IsolationLevel string `mapstructure:"isolation_level" yaml:"isolation_level" json:"isolation_level"`

	// 重试配置
	MaxRetries    int           `mapstructure:"max_retries" yaml:"max_retries" json:"max_retries"`
	RetryInterval time.Duration `mapstructure:"retry_interval" yaml:"retry_interval" json:"retry_interval"`

	// 死锁检测
	DeadlockDetection bool          `mapstructure:"deadlock_detection" yaml:"deadlock_detection" json:"deadlock_detection"`
	DeadlockTimeout   time.Duration `mapstructure:"deadlock_timeout" yaml:"deadlock_timeout" json:"deadlock_timeout"`
}

// BusinessRetryConfig 业务重试配置
type BusinessRetryConfig struct {
	// 基础重试配置
	MaxAttempts       int           `mapstructure:"max_attempts" yaml:"max_attempts" json:"max_attempts"`
	InitialInterval   time.Duration `mapstructure:"initial_interval" yaml:"initial_interval" json:"initial_interval"`
	MaxInterval       time.Duration `mapstructure:"max_interval" yaml:"max_interval" json:"max_interval"`
	BackoffMultiplier float64       `mapstructure:"backoff_multiplier" yaml:"backoff_multiplier" json:"backoff_multiplier"`

	// 抖动配置
	JitterEnabled bool    `mapstructure:"jitter_enabled" yaml:"jitter_enabled" json:"jitter_enabled"`
	JitterFactor  float64 `mapstructure:"jitter_factor" yaml:"jitter_factor" json:"jitter_factor"`

	// 可重试错误类型
	RetryableErrors []string `mapstructure:"retryable_errors" yaml:"retryable_errors" json:"retryable_errors"`
}

// DefaultBusinessConfig 默认业务配置
func DefaultBusinessConfig() *BusinessConfig {
	return &BusinessConfig{
		Order: OrderConfig{
			PreChargeEnabled: true,
			PreChargeTimeout: 30 * time.Second,
			CreateTimeout:    60 * time.Second,
			CancelTimeout:    30 * time.Second,
			MaxOrdersPerUser: 100,
			MaxOrdersPerDay:  1000,
			MaxWeightKg:      decimal.NewFromFloat(100.0),
			MaxVolumeCubicM:  decimal.NewFromFloat(1.0),
		},
		Billing: BillingConfig{
			PrecisionThreshold:    decimal.NewFromFloat(0.01),
			RoundingMode:          "ROUND_HALF_UP",
			MaxFeeAmount:          decimal.NewFromFloat(999999.99),
			MinFeeAmount:          decimal.NewFromFloat(0.01),
			AutoProcessThreshold:  decimal.NewFromFloat(100.0),
			ManualReviewThreshold: decimal.NewFromFloat(1000.0),
			ProcessTimeout:        30 * time.Second,
			AsyncHistoryEnabled:   true,
			HistoryBatchSize:      100,
		},
		Balance: BalanceConfig{
			MaxBalance:            decimal.NewFromFloat(1000000.0),
			MinBalance:            decimal.NewFromFloat(0.0),
			FreezeTimeout:         24 * time.Hour,
			AutoUnfreezeEnabled:   true,
			MaxTransactionAmount:  decimal.NewFromFloat(10000.0),
			DailyTransactionLimit: decimal.NewFromFloat(100000.0),
			ConcurrencyControl:    true,
			LockTimeout:           5 * time.Second,
		},
		Callback: BusinessCallbackConfig{
			MaxConcurrency:      50,
			ProcessTimeout:      30 * time.Second,
			MaxRetries:          3,
			RetryInterval:       1 * time.Second,
			RetryBackoffFactor:  2.0,
			ForwardEnabled:      true,
			ForwardTimeout:      10 * time.Second,
			SignatureValidation: true,
			TimestampValidation: true,
			TimestampTolerance:  5 * time.Minute,
		},
		Transaction: TransactionConfig{
			DefaultTimeout:     30 * time.Second,
			LongRunningTimeout: 5 * time.Minute,
			IsolationLevel:     "READ_COMMITTED",
			MaxRetries:         3,
			RetryInterval:      100 * time.Millisecond,
			DeadlockDetection:  true,
			DeadlockTimeout:    10 * time.Second,
		},
		Retry: BusinessRetryConfig{
			MaxAttempts:       3,
			InitialInterval:   100 * time.Millisecond,
			MaxInterval:       30 * time.Second,
			BackoffMultiplier: 2.0,
			JitterEnabled:     true,
			JitterFactor:      0.1,
			RetryableErrors: []string{
				"connection refused",
				"connection reset",
				"timeout",
				"temporary failure",
				"too many connections",
				"service unavailable",
				"internal server error",
				"bad gateway",
			},
		},
	}
}

// Validate 验证业务配置
func (bc *BusinessConfig) Validate() error {
	// 验证订单配置
	if bc.Order.MaxWeightKg.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("order.max_weight_kg must be greater than 0")
	}

	// 验证计费配置
	if bc.Billing.PrecisionThreshold.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("billing.precision_threshold must be greater than 0")
	}

	// 验证余额配置
	if bc.Balance.MaxBalance.LessThanOrEqual(bc.Balance.MinBalance) {
		return fmt.Errorf("balance.max_balance must be greater than min_balance")
	}

	// 验证回调配置
	if bc.Callback.MaxConcurrency <= 0 {
		return fmt.Errorf("callback.max_concurrency must be greater than 0")
	}

	// 验证事务配置
	if bc.Transaction.DefaultTimeout <= 0 {
		return fmt.Errorf("transaction.default_timeout must be greater than 0")
	}

	// 验证重试配置
	if bc.Retry.MaxAttempts <= 0 {
		return fmt.Errorf("retry.max_attempts must be greater than 0")
	}

	return nil
}

// GetOrderConfig 获取订单配置
func (bc *BusinessConfig) GetOrderConfig() *OrderConfig {
	return &bc.Order
}

// GetBillingConfig 获取计费配置
func (bc *BusinessConfig) GetBillingConfig() *BillingConfig {
	return &bc.Billing
}

// GetBalanceConfig 获取余额配置
func (bc *BusinessConfig) GetBalanceConfig() *BalanceConfig {
	return &bc.Balance
}

// GetCallbackConfig 获取回调配置
func (bc *BusinessConfig) GetCallbackConfig() *BusinessCallbackConfig {
	return &bc.Callback
}

// GetTransactionConfig 获取事务配置
func (bc *BusinessConfig) GetTransactionConfig() *TransactionConfig {
	return &bc.Transaction
}

// GetRetryConfig 获取重试配置
func (bc *BusinessConfig) GetRetryConfig() *BusinessRetryConfig {
	return &bc.Retry
}
