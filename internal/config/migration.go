package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"go.uber.org/zap"
	"gopkg.in/yaml.v3"
)

// ConfigMigrator 配置迁移器
// 负责将分散的JSON配置文件迁移到统一的YAML配置
type ConfigMigrator struct {
	logger *zap.Logger
}

// NewConfigMigrator 创建配置迁移器
func NewConfigMigrator(logger *zap.Logger) *ConfigMigrator {
	return &ConfigMigrator{
		logger: logger,
	}
}

// MigrateToUnifiedConfig 迁移到统一配置
func (cm *ConfigMigrator) MigrateToUnifiedConfig() error {
	cm.logger.Info("开始配置迁移...")

	// 1. 检查是否已存在统一配置文件
	if _, err := os.Stat("config/config.yaml"); err == nil {
		cm.logger.Info("统一配置文件已存在，跳过迁移")
		return nil
	}

	// 2. 创建统一配置结构
	unifiedConfig := make(map[string]interface{})

	// 3. 迁移主配置文件
	if err := cm.migrateMainConfig(unifiedConfig); err != nil {
		return fmt.Errorf("迁移主配置失败: %w", err)
	}

	// 4. 迁移供应商配置
	if err := cm.migrateProviderConfigs(unifiedConfig); err != nil {
		return fmt.Errorf("迁移供应商配置失败: %w", err)
	}

	// 5. 迁移快递公司配置
	if err := cm.migrateExpressConfig(unifiedConfig); err != nil {
		return fmt.Errorf("迁移快递公司配置失败: %w", err)
	}

	// 6. 写入统一配置文件
	if err := cm.writeUnifiedConfig(unifiedConfig); err != nil {
		return fmt.Errorf("写入统一配置失败: %w", err)
	}

	cm.logger.Info("配置迁移完成")
	return nil
}

// migrateMainConfig 迁移主配置文件
func (cm *ConfigMigrator) migrateMainConfig(unifiedConfig map[string]interface{}) error {
	// 尝试读取现有的JSON配置文件
	configPaths := []string{"config.json", "config/config.json"}

	for _, path := range configPaths {
		if _, err := os.Stat(path); err == nil {
			cm.logger.Info("发现主配置文件", zap.String("path", path))

			data, err := os.ReadFile(path)
			if err != nil {
				return fmt.Errorf("读取配置文件失败: %w", err)
			}

			var config map[string]interface{}
			if err := json.Unmarshal(data, &config); err != nil {
				return fmt.Errorf("解析配置文件失败: %w", err)
			}

			// 合并到统一配置
			for key, value := range config {
				unifiedConfig[key] = value
			}

			cm.logger.Info("主配置文件迁移完成")
			return nil
		}
	}

	cm.logger.Info("未发现主配置文件，使用默认配置")
	return nil
}

// migrateProviderConfigs 迁移供应商配置
func (cm *ConfigMigrator) migrateProviderConfigs(unifiedConfig map[string]interface{}) error {
	providersDir := "config/providers"

	if _, err := os.Stat(providersDir); os.IsNotExist(err) {
		cm.logger.Info("供应商配置目录不存在，跳过迁移")
		return nil
	}

	providers := make(map[string]interface{})
	providerFiles := []string{"kuaidi100.json", "yida.json", "yuntong.json"}

	for _, filename := range providerFiles {
		filePath := filepath.Join(providersDir, filename)
		if _, err := os.Stat(filePath); err == nil {
			cm.logger.Info("发现供应商配置文件", zap.String("path", filePath))

			data, err := os.ReadFile(filePath)
			if err != nil {
				cm.logger.Warn("读取供应商配置文件失败", zap.String("path", filePath), zap.Error(err))
				continue
			}

			var providerConfig map[string]interface{}
			if err := json.Unmarshal(data, &providerConfig); err != nil {
				cm.logger.Warn("解析供应商配置文件失败", zap.String("path", filePath), zap.Error(err))
				continue
			}

			// 提取供应商名称
			providerName := filename[:len(filename)-5] // 去掉.json后缀
			providers[providerName] = providerConfig

			cm.logger.Info("供应商配置迁移完成", zap.String("provider", providerName))
		}
	}

	if len(providers) > 0 {
		unifiedConfig["providers"] = providers
	}

	return nil
}

// migrateExpressConfig 迁移快递公司配置
func (cm *ConfigMigrator) migrateExpressConfig(unifiedConfig map[string]interface{}) error {
	expressConfigPath := "config/express_companies.json"

	if _, err := os.Stat(expressConfigPath); os.IsNotExist(err) {
		cm.logger.Info("快递公司配置文件不存在，跳过迁移")
		return nil
	}

	cm.logger.Info("发现快递公司配置文件", zap.String("path", expressConfigPath))

	data, err := os.ReadFile(expressConfigPath)
	if err != nil {
		return fmt.Errorf("读取快递公司配置文件失败: %w", err)
	}

	var expressConfig map[string]interface{}
	if err := json.Unmarshal(data, &expressConfig); err != nil {
		return fmt.Errorf("解析快递公司配置文件失败: %w", err)
	}

	unifiedConfig["express_companies"] = expressConfig
	cm.logger.Info("快递公司配置迁移完成")

	return nil
}

// writeUnifiedConfig 写入统一配置文件
func (cm *ConfigMigrator) writeUnifiedConfig(unifiedConfig map[string]interface{}) error {
	// 确保配置目录存在
	if err := os.MkdirAll("config", 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 转换为YAML格式
	yamlData, err := yaml.Marshal(unifiedConfig)
	if err != nil {
		return fmt.Errorf("转换为YAML格式失败: %w", err)
	}

	// 写入文件
	outputPath := "config/config.yaml"
	if err := os.WriteFile(outputPath, yamlData, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	cm.logger.Info("统一配置文件已生成", zap.String("path", outputPath))
	return nil
}

// BackupOldConfigs 备份旧配置文件
func (cm *ConfigMigrator) BackupOldConfigs() error {
	backupDir := "config/backups"
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	// 备份文件列表
	backupFiles := []string{
		"config.json",
		"config/config.json",
		"config/providers/kuaidi100.json",
		"config/providers/yida.json",
		"config/providers/yuntong.json",
		"config/express_companies.json",
	}

	for _, filePath := range backupFiles {
		if _, err := os.Stat(filePath); err == nil {
			// 计算备份文件名
			backupPath := filepath.Join(backupDir, filepath.Base(filePath))

			// 复制文件
			if err := cm.copyFile(filePath, backupPath); err != nil {
				cm.logger.Warn("备份文件失败", zap.String("source", filePath), zap.Error(err))
			} else {
				cm.logger.Info("文件已备份", zap.String("source", filePath), zap.String("backup", backupPath))
			}
		}
	}

	return nil
}

// copyFile 复制文件
func (cm *ConfigMigrator) copyFile(src, dst string) error {
	data, err := os.ReadFile(src)
	if err != nil {
		return err
	}
	return os.WriteFile(dst, data, 0644)
}

// ValidateUnifiedConfig 验证统一配置
func (cm *ConfigMigrator) ValidateUnifiedConfig() error {
	configPath := "config/config.yaml"

	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("统一配置文件不存在: %s", configPath)
	}

	// 尝试加载配置
	manager := NewConfigManager(cm.logger)
	if err := manager.Load(); err != nil {
		return fmt.Errorf("加载统一配置失败: %w", err)
	}

	cm.logger.Info("统一配置验证通过")
	return nil
}

// GetMigrationStatus 获取迁移状态
func (cm *ConfigMigrator) GetMigrationStatus() map[string]bool {
	status := make(map[string]bool)

	// 检查统一配置文件
	status["unified_config"] = cm.fileExists("config/config.yaml")

	// 检查旧配置文件
	status["old_main_config"] = cm.fileExists("config.json") || cm.fileExists("config/config.json")
	status["old_provider_configs"] = cm.fileExists("config/providers/kuaidi100.json") ||
		cm.fileExists("config/providers/yida.json") ||
		cm.fileExists("config/providers/yuntong.json")
	status["old_express_config"] = cm.fileExists("config/express_companies.json")

	// 检查备份文件
	status["backup_exists"] = cm.fileExists("config/backups")

	return status
}

// fileExists 检查文件是否存在
func (cm *ConfigMigrator) fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}
