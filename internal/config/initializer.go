package config

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"go.uber.org/zap"
)

// ConfigInitializer 配置初始化器
type ConfigInitializer struct {
	logger    *zap.Logger
	manager   *ConfigManager
	validator *ConfigValidator
	migrator  *ConfigMigrator
	adapter   *ConfigAdapter
	mu        sync.RWMutex
}

// InitializationResult 初始化结果
type InitializationResult struct {
	Success          bool              `json:"success"`
	ConfigPath       string            `json:"config_path"`
	MigrationDone    bool              `json:"migration_done"`
	ValidationResult *ValidationResult `json:"validation_result"`
	Errors           []string          `json:"errors"`
	Warnings         []string          `json:"warnings"`
}

// NewConfigInitializer 创建配置初始化器
func NewConfigInitializer(logger *zap.Logger) *ConfigInitializer {
	manager := NewConfigManager(logger)
	validator := NewConfigValidator(logger)
	migrator := NewConfigMigrator(logger)
	adapter := NewConfigAdapter(manager, logger)

	return &ConfigInitializer{
		logger:    logger,
		manager:   manager,
		validator: validator,
		migrator:  migrator,
		adapter:   adapter,
	}
}

// Initialize 初始化配置系统
func (ci *ConfigInitializer) Initialize() (*InitializationResult, error) {
	ci.mu.Lock()
	defer ci.mu.Unlock()

	result := &InitializationResult{
		Success:       false,
		Errors:        []string{},
		Warnings:      []string{},
		MigrationDone: false,
	}

	ci.logger.Info("开始初始化配置系统...")

	// 1. 检查配置文件状态
	configStatus := ci.checkConfigStatus()
	result.ConfigPath = configStatus.UnifiedConfigPath

	// 2. 如果需要，执行配置迁移
	if configStatus.NeedsMigration {
		ci.logger.Info("检测到需要配置迁移，开始执行...")
		if err := ci.performMigration(); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("配置迁移失败: %v", err))
			return result, err
		}
		result.MigrationDone = true
		ci.logger.Info("配置迁移完成")
	}

	// 3. 加载配置
	if err := ci.manager.Load(); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("加载配置失败: %v", err))
		return result, err
	}

	// 4. 验证配置
	validationResult := ci.validator.ValidateConfig(ci.manager)
	result.ValidationResult = validationResult

	if !validationResult.Valid {
		result.Errors = append(result.Errors, "配置验证失败")
		result.Errors = append(result.Errors, validationResult.Errors...)
		return result, fmt.Errorf("配置验证失败")
	}

	// 5. 设置全局配置管理器
	if err := ci.setGlobalManager(); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("设置全局配置管理器失败: %v", err))
		return result, err
	}

	// 6. 验证环境变量
	envValidation := ci.validator.ValidateEnvironmentVariables()
	result.Warnings = append(result.Warnings, envValidation.Warnings...)

	result.Success = true
	ci.logger.Info("配置系统初始化完成",
		zap.String("config_path", result.ConfigPath),
		zap.Bool("migration_done", result.MigrationDone),
		zap.Bool("validation_passed", validationResult.Valid))

	return result, nil
}

// ConfigStatus 配置状态
type ConfigStatus struct {
	UnifiedConfigExists bool
	UnifiedConfigPath   string
	OldConfigsExist     bool
	NeedsMigration      bool
}

// checkConfigStatus 检查配置状态
func (ci *ConfigInitializer) checkConfigStatus() *ConfigStatus {
	status := &ConfigStatus{}

	// 检查统一配置文件
	unifiedPaths := []string{
		"config/config.yaml",
		"config/config.yml",
		"./config.yaml",
		"./config.yml",
	}

	for _, path := range unifiedPaths {
		if _, err := os.Stat(path); err == nil {
			status.UnifiedConfigExists = true
			status.UnifiedConfigPath = path
			break
		}
	}

	// 检查旧配置文件
	oldConfigPaths := []string{
		"config.json",
		"config/config.json",
		"config/providers/kuaidi100.json",
		"config/providers/yida.json",
		"config/providers/yuntong.json",
		"config/express_companies.json",
	}

	for _, path := range oldConfigPaths {
		if _, err := os.Stat(path); err == nil {
			status.OldConfigsExist = true
			break
		}
	}

	// 判断是否需要迁移
	status.NeedsMigration = !status.UnifiedConfigExists && status.OldConfigsExist

	// 如果没有统一配置文件，设置默认路径
	if !status.UnifiedConfigExists {
		status.UnifiedConfigPath = "config/config.yaml"
	}

	return status
}

// performMigration 执行配置迁移
func (ci *ConfigInitializer) performMigration() error {
	// 备份旧配置
	if err := ci.migrator.BackupOldConfigs(); err != nil {
		ci.logger.Warn("备份旧配置失败", zap.Error(err))
	}

	// 执行迁移
	if err := ci.migrator.MigrateToUnifiedConfig(); err != nil {
		return fmt.Errorf("迁移配置失败: %w", err)
	}

	// 验证迁移结果
	if err := ci.migrator.ValidateUnifiedConfig(); err != nil {
		return fmt.Errorf("验证迁移结果失败: %w", err)
	}

	return nil
}

// setGlobalManager 设置全局配置管理器
func (ci *ConfigInitializer) setGlobalManager() error {
	// 这里可以设置全局单例
	// 注意：这个函数主要用于确保配置管理器正确初始化
	// 实际的全局管理器设置在manager.go中处理
	return nil
}

// CreateDefaultConfig 创建默认配置文件
func (ci *ConfigInitializer) CreateDefaultConfig() error {
	configPath := "config/config.yaml"

	// 确保配置目录存在
	if err := os.MkdirAll(filepath.Dir(configPath), 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 检查文件是否已存在
	if _, err := os.Stat(configPath); err == nil {
		return fmt.Errorf("配置文件已存在: %s", configPath)
	}

	// 创建默认配置内容
	defaultConfig := ci.getDefaultConfigContent()

	// 写入文件
	if err := os.WriteFile(configPath, []byte(defaultConfig), 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	ci.logger.Info("默认配置文件已创建", zap.String("path", configPath))
	return nil
}

// getDefaultConfigContent 获取默认配置内容
func (ci *ConfigInitializer) getDefaultConfigContent() string {
	return `# Go-Kuaidi 统一配置文件
# 支持环境变量覆盖，格式：GOKUAIDI_SECTION_KEY=value

# 环境配置
environment: development

# 服务器配置
server:
  port: 8081
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120
  max_header_bytes: 1048576

# 数据库配置
database:
  connection_string: "postgresql://my@localhost:5432/go_kuaidi?sslmode=disable"
  max_open_conns: 50
  max_idle_conns: 25
  conn_max_lifetime: 600
  conn_max_idle_time: 300

# Redis配置
redis:
  connection_string: "redis://localhost:6379"
  db: 0
  pool_size: 20
  min_idle_conns: 5
  max_conn_age: 300
  pool_timeout: 30
  idle_timeout: 300

# 认证配置
auth:
  token_expiry_seconds: 3600
  issuer: "go-kuaidi"
  audience: "go-kuaidi-api"
  jwt_secret: "your-jwt-secret-here"
  expiration_hours: 24

# 安全配置
security:
  signature:
    enabled: false
    skip_paths:
      - "/health"
      - "/metrics"

# 性能配置
performance:
  cache:
    price_ttl_minutes: 30
    region_ttl_minutes: 1440
    express_company_ttl_minutes: 1440
  timeout:
    provider_query_ms: 5000
    total_query_ms: 10000
    database_query_ms: 3000
  retry:
    max_attempts: 3
    initial_delay_ms: 100
    max_delay_ms: 1000
    backoff_multiplier: 2.0
  concurrency:
    worker_pool_size: 10
    max_concurrent_requests: 100

# 供应商配置
providers:
  # 快递100配置
  kuaidi100:
    api_key: ""
    enabled: false
    base_url: "https://poll.kuaidi100.com/order/borderapi.do"
    timeout: 10s
    salt: ""
    allowed_ips:
      - "127.0.0.1"
      - "***********/24"
  
  # 易达配置
  yida:
    username: ""
    private_key: ""
    enabled: false
    allowed_ips:
      - "127.0.0.1"
      - "***********/24"
  
  # 云通配置
  yuntong:
    business_id: ""
    api_key: ""
    e_business_id: ""
    secret_key: ""
    enabled: false
    allowed_ips:
      - "127.0.0.1"
      - "***********/24"

  # 快递鸟配置
  kuaidiniao:
    e_business_id: ""
    api_key: ""
    base_url: "https://api.kdniao.com/api/dist"
    environment: "production"
    enabled: false
    timeout: 10s
    allowed_ips:
      - "127.0.0.1"
      - "***********/24"

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"
  file_path: "logs/go-kuaidi.log"
  max_size_mb: 100
  max_backups: 3
  max_age_days: 30
  compress: true

# 监控配置
monitoring:
  enabled: true
  metrics_path: "/metrics"
  health_path: "/health"
  pprof_enabled: false

# 地区配置
region:
  data_path: "config/getAreaCascaderVo.json"
  auto_load: true

# 功能开关
features:
  enable_signature_validation: false
  enable_rate_limiting: true
  enable_audit_logging: true
  enable_performance_monitoring: true
  enable_cache_warmup: true
`
}

// GetManager 获取配置管理器
func (ci *ConfigInitializer) GetManager() *ConfigManager {
	ci.mu.RLock()
	defer ci.mu.RUnlock()
	return ci.manager
}

// GetAdapter 获取配置适配器
func (ci *ConfigInitializer) GetAdapter() *ConfigAdapter {
	ci.mu.RLock()
	defer ci.mu.RUnlock()
	return ci.adapter
}

// GetValidator 获取配置验证器
func (ci *ConfigInitializer) GetValidator() *ConfigValidator {
	ci.mu.RLock()
	defer ci.mu.RUnlock()
	return ci.validator
}

// Reload 重新加载配置
func (ci *ConfigInitializer) Reload() error {
	ci.mu.Lock()
	defer ci.mu.Unlock()

	// 重新加载配置
	if err := ci.manager.Load(); err != nil {
		return fmt.Errorf("重新加载配置失败: %w", err)
	}

	// 验证配置
	validationResult := ci.validator.ValidateConfig(ci.manager)
	if !validationResult.Valid {
		return fmt.Errorf("配置验证失败: %v", validationResult.Errors)
	}

	ci.logger.Info("配置重新加载完成")
	return nil
}
