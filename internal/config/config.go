package config

import (
	"encoding/json"
	"os"
	"path/filepath"
)

// Config 配置
type Config struct {
	Server           ServerConfig           `json:"server"`
	Database         DatabaseConfig         `json:"database"`
	CallbackDatabase DatabaseConfig         `json:"callback_database"` // 🔄 新增：回调数据库配置
	Redis            RedisConfig            `json:"redis"`
	Auth             AuthConfig             `json:"auth"`
	Providers        map[string]interface{} `json:"providers"`
	Security         SecurityConfig         `json:"security"`
	Performance      PerformanceConfig      `json:"performance"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	// 签名配置
	Signature SignatureConfig `json:"signature"`
}

// SignatureConfig 签名配置
type SignatureConfig struct {
	// 是否启用签名验证
	Enabled bool `json:"enabled"`

	// 跳过签名验证的路径
	SkipPaths []string `json:"skip_paths"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port           int `json:"port"`
	ReadTimeout    int `json:"read_timeout"`
	WriteTimeout   int `json:"write_timeout"`
	IdleTimeout    int `json:"idle_timeout"`
	MaxHeaderBytes int `json:"max_header_bytes"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	ConnectionString string `json:"connection_string"`
	MaxOpenConns     int    `json:"max_open_conns"`
	MaxIdleConns     int    `json:"max_idle_conns"`
	ConnMaxLifetime  int    `json:"conn_max_lifetime"`
	ConnMaxIdleTime  int    `json:"conn_max_idle_time"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	ConnectionString string `json:"connection_string"`
	DB               int    `json:"db"`
	PoolSize         int    `json:"pool_size"`
	MinIdleConns     int    `json:"min_idle_conns"`
	MaxConnAge       int    `json:"max_conn_age"`
	PoolTimeout      int    `json:"pool_timeout"`
	IdleTimeout      int    `json:"idle_timeout"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	TokenExpirySeconds int    `json:"token_expiry_seconds"`
	Issuer             string `json:"issuer"`
	Audience           string `json:"audience"`
	PrivateKeyPath     string `json:"private_key_path"`
	PublicKeyPath      string `json:"public_key_path"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	WorkerPoolSize              int  `json:"worker_pool_size"`
	TaskQueueSize               int  `json:"task_queue_size"`
	CacheTTLSeconds             int  `json:"cache_ttl_seconds"`
	SlowRequestThresholdMs      int  `json:"slow_request_threshold_ms"`
	EnableCompression           bool `json:"enable_compression"`
	EnablePerformanceMonitoring bool `json:"enable_performance_monitoring"`
}

// Load 加载配置
func Load() (*Config, error) {
	// 获取配置文件路径
	configPath := os.Getenv("CONFIG_PATH")
	if configPath == "" {
		configPath = "config.json"
	}

	// 读取配置文件
	data, err := os.ReadFile(filepath.Clean(configPath))
	if err != nil {
		return nil, err
	}

	// 解析配置文件
	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, err
	}

	return &config, nil
}
