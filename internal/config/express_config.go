package config

import (
	"fmt"
	"sync"
)

// ExpressCompany 快递公司配置 - 已废弃，请使用数据库
// 保留此结构体仅为向后兼容，新代码请使用 express.ExpressCompany
type ExpressCompany struct {
	Code                string            `json:"code"`                  // 标准代码
	Name                string            `json:"name"`                  // 名称
	Providers           []string          `json:"providers"`             // 支持的供应商
	ProviderCodes       map[string]string `json:"provider_codes"`        // 供应商特定代码
	VolumeWeightRatio   int               `json:"volume_weight_ratio"`   // 体积重量系数(抛比) cm³/kg
	MaxWeightKg         float64           `json:"max_weight_kg"`         // 最大重量限制
	MaxVolumeCm3        float64           `json:"max_volume_cm3"`        // 最大体积限制(立方厘米)
	SupportVolumeWeight bool              `json:"support_volume_weight"` // 是否支持体积重量计算
}

// ExpressCompaniesConfig 快递公司配置 - 已废弃
// 所有快递公司配置已迁移到数据库，请使用 ExpressCompanyRepository
type ExpressCompaniesConfig struct {
	EnabledCompanies []ExpressCompany `json:"enabled_companies"` // 启用的快递公司
}

var (
	expressConfig     *ExpressCompaniesConfig
	expressConfigOnce sync.Once
)

// LoadExpressConfig 加载快递公司配置 - 已废弃
// 快递公司配置已迁移到数据库，请使用 ExpressCompanyRepository.GetCompanies
func LoadExpressConfig() (*ExpressCompaniesConfig, error) {
	var err error
	expressConfigOnce.Do(func() {
		// 从统一配置管理器加载配置
		configManager := GetConfigManager()
		if configManager == nil {
			err = fmt.Errorf("配置管理器未初始化")
			return
		}

		// 返回空配置，所有配置从数据库获取
		config := &ExpressCompaniesConfig{
			EnabledCompanies: []ExpressCompany{},
		}

		expressConfig = config
	})

	return expressConfig, err
}

// GetExpressConfig 获取快递公司配置 - 已废弃
// 快递公司配置已迁移到数据库，请使用 ExpressCompanyRepository
func GetExpressConfig() *ExpressCompaniesConfig {
	if expressConfig == nil {
		config, err := LoadExpressConfig()
		if err != nil {
			// 如果加载失败，返回空配置
			return &ExpressCompaniesConfig{
				EnabledCompanies: []ExpressCompany{},
			}
		}
		return config
	}
	return expressConfig
}

// IsEnabledExpressCompany 检查快递公司是否启用 - 已废弃
// 请使用 ExpressCompanyRepository.GetCompanyByCode
func IsEnabledExpressCompany(code string) bool {
	// 已废弃：配置已迁移到数据库
	return false
}

// GetEnabledExpressCompanies 获取启用的快递公司列表 - 已废弃
// 请使用 ExpressCompanyRepository.GetCompanies
func GetEnabledExpressCompanies() []ExpressCompany {
	// 已废弃：配置已迁移到数据库，返回空切片
	return []ExpressCompany{}
}

// 以下函数已废弃，快递公司映射已迁移到数据库
// 请使用 ExpressMappingService 进行快递公司映射查询

// GetProviderCode 已废弃：请使用 ExpressMappingService.GetProviderCompanyCode
func GetProviderCode(standardCode, provider string) (string, bool) {
	// 已废弃：配置已迁移到数据库，请使用 ExpressMappingService
	return "", false
}

// GetStandardCode 已废弃：请使用 ExpressMappingService.GetStandardCompanyCode
func GetStandardCode(providerCode, provider string) (string, bool) {
	// 已废弃：配置已迁移到数据库，请使用 ExpressMappingService
	return "", false
}

// GetExpressCompanyByCode 已废弃：请使用数据库查询
func GetExpressCompanyByCode(code string) (ExpressCompany, bool) {
	// 已废弃：配置已迁移到数据库，请使用 ExpressCompanyRepository
	return ExpressCompany{}, false
}

// GetSupportedProviders 已废弃：请使用 ExpressMappingService.GetSupportedCompanies
func GetSupportedProviders(code string) []string {
	// 已废弃：配置已迁移到数据库，请使用 ExpressMappingService
	return []string{}
}

// IsProviderSupportExpressCompany 已废弃：请使用 ExpressMappingService.GetCompanyMapping
func IsProviderSupportExpressCompany(provider, code string) bool {
	// 已废弃：配置已迁移到数据库，请使用 ExpressMappingService
	return false
}
