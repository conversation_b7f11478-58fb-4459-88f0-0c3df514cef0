package config

import (
	"fmt"
	"sync"
)

// ProviderConfig 供应商配置
type ProviderConfig struct {
	Name         string                 `json:"name"`
	BaseURL      string                 `json:"base_url"`
	Timeout      int                    `json:"timeout"`
	APIEndpoints map[string]string      `json:"api_endpoints"`
	APIMethods   map[string]string      `json:"api_methods"`
	Parameters   map[string]interface{} `json:"parameters"`
	StatusCodes  map[string]string      `json:"status_codes"`
	// ExpressCodes 字段已删除，映射关系现在完全从数据库获取
	ProductTypes map[string]string `json:"product_types"`
	PayMethods   map[string]string `json:"pay_methods"`
}

// ProviderConfigManager 供应商配置管理器
type ProviderConfigManager struct {
	providers map[string]*ProviderConfig
	mutex     sync.RWMutex
}

var (
	providerConfigManager     *ProviderConfigManager
	providerConfigManagerOnce sync.Once
)

// GetProviderConfigManager 获取供应商配置管理器
func GetProviderConfigManager() *ProviderConfigManager {
	providerConfigManagerOnce.Do(func() {
		providerConfigManager = &ProviderConfigManager{
			providers: make(map[string]*ProviderConfig),
		}
		// 加载配置
		providerConfigManager.loadConfigs()
	})
	return providerConfigManager
}

// loadConfigs 加载所有供应商配置
func (m *ProviderConfigManager) loadConfigs() {
	// 从统一配置管理器获取配置
	configManager := GetConfigManager()

	// 加载各个供应商配置
	providers := []string{"kuaidi100", "yida", "yuntong"}
	for _, provider := range providers {
		if config := m.loadProviderConfigFromYAML(configManager, provider); config != nil {
			m.providers[provider] = config
		}
	}
}

// loadProviderConfigFromYAML 从YAML配置加载供应商配置
func (m *ProviderConfigManager) loadProviderConfigFromYAML(configManager *ConfigManager, provider string) *ProviderConfig {
	// 检查供应商是否启用
	if !configManager.GetBool(fmt.Sprintf("providers.%s.enabled", provider)) {
		return nil
	}

	config := &ProviderConfig{
		Name:    provider,
		BaseURL: configManager.GetString(fmt.Sprintf("providers.%s.base_url", provider)),
		Timeout: configManager.GetInt(fmt.Sprintf("providers.%s.timeout", provider)),
	}

	// 获取API端点配置
	if endpoints := configManager.viper.GetStringMapString(fmt.Sprintf("providers.%s.api_endpoints", provider)); endpoints != nil {
		config.APIEndpoints = endpoints
	}

	// 获取API方法配置
	if methods := configManager.viper.GetStringMapString(fmt.Sprintf("providers.%s.api_methods", provider)); methods != nil {
		config.APIMethods = methods
	}

	// 获取参数配置
	if params := configManager.viper.GetStringMap(fmt.Sprintf("providers.%s.parameters", provider)); params != nil {
		config.Parameters = params
	}

	// 获取状态码配置
	if statusCodes := configManager.viper.GetStringMapString(fmt.Sprintf("providers.%s.status_codes", provider)); statusCodes != nil {
		config.StatusCodes = statusCodes
	}

	// 获取产品类型配置
	if productTypes := configManager.viper.GetStringMapString(fmt.Sprintf("providers.%s.product_types", provider)); productTypes != nil {
		config.ProductTypes = productTypes
	}

	// 获取支付方式配置
	if payMethods := configManager.viper.GetStringMapString(fmt.Sprintf("providers.%s.pay_methods", provider)); payMethods != nil {
		config.PayMethods = payMethods
	}

	return config
}

// GetProviderConfig 获取供应商配置
func (m *ProviderConfigManager) GetProviderConfig(provider string) (*ProviderConfig, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	config, ok := m.providers[provider]
	return config, ok
}

// GetAPIEndpoint 获取API端点
func (m *ProviderConfigManager) GetAPIEndpoint(provider, endpoint string) string {
	if config, ok := m.GetProviderConfig(provider); ok {
		if url, exists := config.APIEndpoints[endpoint]; exists {
			return url
		}
	}
	return ""
}

// GetAPIMethod 获取API方法名
func (m *ProviderConfigManager) GetAPIMethod(provider, method string) string {
	if config, ok := m.GetProviderConfig(provider); ok {
		if methodName, exists := config.APIMethods[method]; exists {
			return methodName
		}
	}
	return ""
}

// GetExpressCode 已废弃 - 快递公司代码映射已迁移到数据库
// 请使用 ExpressMappingService.GetProviderCompanyCode 替代
// func (m *ProviderConfigManager) GetExpressCode(provider, standardCode string) string

// GetProductType 获取产品类型
func (m *ProviderConfigManager) GetProductType(provider, expressCode string) string {
	if config, ok := m.GetProviderConfig(provider); ok {
		if productType, exists := config.ProductTypes[expressCode]; exists {
			return productType
		}
	}
	return ""
}

// GetPayMethod 获取支付方式
func (m *ProviderConfigManager) GetPayMethod(provider, payMethod string) string {
	if config, ok := m.GetProviderConfig(provider); ok {
		if method, exists := config.PayMethods[payMethod]; exists {
			return method
		}
	}
	return payMethod
}

// GetParameter 获取参数值
func (m *ProviderConfigManager) GetParameter(provider, param string) interface{} {
	if config, ok := m.GetProviderConfig(provider); ok {
		if value, exists := config.Parameters[param]; exists {
			return value
		}
	}
	return nil
}

// GetStatusCode 获取状态码描述
func (m *ProviderConfigManager) GetStatusCode(provider, code string) string {
	if config, ok := m.GetProviderConfig(provider); ok {
		if desc, exists := config.StatusCodes[code]; exists {
			return desc
		}
	}
	return code
}
