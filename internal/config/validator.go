package config

import (
	"fmt"
	"net/url"
	"strings"

	"go.uber.org/zap"
)

// ConfigValidator 配置验证器
type ConfigValidator struct {
	logger *zap.Logger
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator(logger *zap.Logger) *ConfigValidator {
	return &ConfigValidator{
		logger: logger,
	}
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid    bool     `json:"valid"`
	Errors   []string `json:"errors"`
	Warnings []string `json:"warnings"`
}

// ValidateConfig 验证配置
func (cv *ConfigValidator) ValidateConfig(manager *ConfigManager) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// 验证服务器配置
	cv.validateServerConfig(manager, result)

	// 验证数据库配置
	cv.validateDatabaseConfig(manager, result)

	// 验证Redis配置
	cv.validateRedisConfig(manager, result)

	// 验证认证配置
	cv.validateAuthConfig(manager, result)

	// 验证供应商配置
	cv.validateProvidersConfig(manager, result)

	// 验证性能配置
	cv.validatePerformanceConfig(manager, result)

	// 设置最终验证状态
	result.Valid = len(result.Errors) == 0

	return result
}

// validateServerConfig 验证服务器配置
func (cv *ConfigValidator) validateServerConfig(manager *ConfigManager, result *ValidationResult) {
	// 验证端口
	port := manager.GetInt("server.port")
	if port <= 0 || port > 65535 {
		result.Errors = append(result.Errors, fmt.Sprintf("无效的服务器端口: %d", port))
	}

	// 验证超时配置
	readTimeout := manager.GetInt("server.read_timeout")
	if readTimeout <= 0 {
		result.Errors = append(result.Errors, "服务器读取超时必须大于0")
	}

	writeTimeout := manager.GetInt("server.write_timeout")
	if writeTimeout <= 0 {
		result.Errors = append(result.Errors, "服务器写入超时必须大于0")
	}

	idleTimeout := manager.GetInt("server.idle_timeout")
	if idleTimeout <= 0 {
		result.Errors = append(result.Errors, "服务器空闲超时必须大于0")
	}

	// 验证最大头部字节数
	maxHeaderBytes := manager.GetInt("server.max_header_bytes")
	if maxHeaderBytes <= 0 {
		result.Errors = append(result.Errors, "最大头部字节数必须大于0")
	}
}

// validateDatabaseConfig 验证数据库配置
func (cv *ConfigValidator) validateDatabaseConfig(manager *ConfigManager, result *ValidationResult) {
	// 验证连接字符串
	connStr := manager.GetString("database.connection_string")
	if connStr == "" {
		result.Errors = append(result.Errors, "数据库连接字符串不能为空")
		return
	}

	// 解析连接字符串
	if _, err := url.Parse(connStr); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("无效的数据库连接字符串: %v", err))
	}

	// 验证连接池配置
	maxOpenConns := manager.GetInt("database.max_open_conns")
	if maxOpenConns <= 0 {
		result.Errors = append(result.Errors, "数据库最大打开连接数必须大于0")
	}

	maxIdleConns := manager.GetInt("database.max_idle_conns")
	if maxIdleConns <= 0 {
		result.Errors = append(result.Errors, "数据库最大空闲连接数必须大于0")
	}

	if maxIdleConns > maxOpenConns {
		result.Warnings = append(result.Warnings, "数据库最大空闲连接数不应大于最大打开连接数")
	}

	// 验证连接生存时间
	connMaxLifetime := manager.GetInt("database.conn_max_lifetime")
	if connMaxLifetime <= 0 {
		result.Errors = append(result.Errors, "数据库连接最大生存时间必须大于0")
	}
}

// validateRedisConfig 验证Redis配置
func (cv *ConfigValidator) validateRedisConfig(manager *ConfigManager, result *ValidationResult) {
	// 验证连接字符串
	connStr := manager.GetString("redis.connection_string")
	if connStr == "" {
		result.Errors = append(result.Errors, "Redis连接字符串不能为空")
		return
	}

	// 解析连接字符串
	if _, err := url.Parse(connStr); err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("无效的Redis连接字符串: %v", err))
	}

	// 验证数据库索引
	db := manager.GetInt("redis.db")
	if db < 0 || db > 15 {
		result.Errors = append(result.Errors, "Redis数据库索引必须在0-15之间")
	}

	// 验证连接池配置
	poolSize := manager.GetInt("redis.pool_size")
	if poolSize <= 0 {
		result.Errors = append(result.Errors, "Redis连接池大小必须大于0")
	}

	minIdleConns := manager.GetInt("redis.min_idle_conns")
	if minIdleConns < 0 {
		result.Errors = append(result.Errors, "Redis最小空闲连接数不能为负数")
	}

	if minIdleConns > poolSize {
		result.Warnings = append(result.Warnings, "Redis最小空闲连接数不应大于连接池大小")
	}
}

// validateAuthConfig 验证认证配置
func (cv *ConfigValidator) validateAuthConfig(manager *ConfigManager, result *ValidationResult) {
	// 验证令牌过期时间
	tokenExpiry := manager.GetInt("auth.token_expiry_seconds")
	if tokenExpiry <= 0 {
		result.Errors = append(result.Errors, "认证令牌过期时间必须大于0")
	}

	// 验证发行者
	issuer := manager.GetString("auth.issuer")
	if issuer == "" {
		result.Warnings = append(result.Warnings, "建议设置认证令牌发行者")
	}

	// 验证受众
	audience := manager.GetString("auth.audience")
	if audience == "" {
		result.Warnings = append(result.Warnings, "建议设置认证令牌受众")
	}
}

// validateProvidersConfig 验证供应商配置
func (cv *ConfigValidator) validateProvidersConfig(manager *ConfigManager, result *ValidationResult) {
	providers := []string{"kuaidi100", "yida", "yuntong"}
	enabledCount := 0

	for _, provider := range providers {
		enabled := manager.GetBool(fmt.Sprintf("providers.%s.enabled", provider))
		if enabled {
			enabledCount++
			cv.validateProviderConfig(manager, provider, result)
		}
	}

	if enabledCount == 0 {
		result.Warnings = append(result.Warnings, "没有启用任何供应商，系统可能无法正常工作")
	}
}

// validateProviderConfig 验证单个供应商配置
func (cv *ConfigValidator) validateProviderConfig(manager *ConfigManager, provider string, result *ValidationResult) {
	switch provider {
	case "kuaidi100":
		apiKey := manager.GetString("providers.kuaidi100.api_key")
		if apiKey == "" {
			result.Errors = append(result.Errors, "快递100 API密钥不能为空")
		}

		baseURL := manager.GetString("providers.kuaidi100.base_url")
		if baseURL == "" {
			result.Errors = append(result.Errors, "快递100基础URL不能为空")
		} else if _, err := url.Parse(baseURL); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("快递100基础URL格式无效: %v", err))
		}

	case "yida":
		username := manager.GetString("providers.yida.username")
		if username == "" {
			result.Errors = append(result.Errors, "易达用户名不能为空")
		}

		privateKey := manager.GetString("providers.yida.private_key")
		if privateKey == "" {
			result.Errors = append(result.Errors, "易达私钥不能为空")
		}

	case "yuntong":
		businessID := manager.GetString("providers.yuntong.business_id")
		if businessID == "" {
			result.Errors = append(result.Errors, "云通商户ID不能为空")
		}

		apiKey := manager.GetString("providers.yuntong.api_key")
		if apiKey == "" {
			result.Errors = append(result.Errors, "云通API密钥不能为空")
		}
	}

	// 验证超时配置
	timeout := manager.GetDuration(fmt.Sprintf("providers.%s.timeout", provider))
	if timeout <= 0 {
		result.Errors = append(result.Errors, fmt.Sprintf("%s供应商超时时间必须大于0", provider))
	}
}

// validatePerformanceConfig 验证性能配置
func (cv *ConfigValidator) validatePerformanceConfig(manager *ConfigManager, result *ValidationResult) {
	// 验证缓存TTL
	priceTTL := manager.GetInt("performance.cache.price_ttl_minutes")
	if priceTTL <= 0 {
		result.Errors = append(result.Errors, "价格缓存TTL必须大于0")
	}

	// 验证超时配置
	providerTimeout := manager.GetInt("performance.timeout.provider_query_ms")
	if providerTimeout <= 0 {
		result.Errors = append(result.Errors, "供应商查询超时必须大于0")
	}

	totalTimeout := manager.GetInt("performance.timeout.total_query_ms")
	if totalTimeout <= 0 {
		result.Errors = append(result.Errors, "总查询超时必须大于0")
	}

	if providerTimeout >= totalTimeout {
		result.Warnings = append(result.Warnings, "供应商查询超时不应大于等于总查询超时")
	}

	// 验证重试配置
	maxAttempts := manager.GetInt("performance.retry.max_attempts")
	if maxAttempts <= 0 {
		result.Errors = append(result.Errors, "最大重试次数必须大于0")
	}

	// 验证并发配置
	workerPoolSize := manager.GetInt("performance.concurrency.worker_pool_size")
	if workerPoolSize <= 0 {
		result.Errors = append(result.Errors, "工作池大小必须大于0")
	}
}

// ValidateEnvironmentVariables 验证环境变量
func (cv *ConfigValidator) ValidateEnvironmentVariables() *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// 检查关键环境变量
	criticalEnvVars := []string{
		"GOKUAIDI_DATABASE_CONNECTION_STRING",
		"GOKUAIDI_REDIS_CONNECTION_STRING",
	}

	for _, envVar := range criticalEnvVars {
		if value := GetEnvString(envVar, ""); value == "" {
			result.Warnings = append(result.Warnings, fmt.Sprintf("环境变量 %s 未设置", envVar))
		}
	}

	return result
}

// ValidateConfigFile 验证配置文件格式
func (cv *ConfigValidator) ValidateConfigFile(filePath string) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// 检查文件扩展名
	if !strings.HasSuffix(filePath, ".yaml") && !strings.HasSuffix(filePath, ".yml") {
		result.Warnings = append(result.Warnings, "建议使用YAML格式的配置文件")
	}

	// 这里可以添加更多的文件格式验证逻辑

	return result
}

// GetValidationSummary 获取验证摘要
func (cv *ConfigValidator) GetValidationSummary(result *ValidationResult) string {
	summary := fmt.Sprintf("配置验证结果: %s\n", map[bool]string{true: "✅ 通过", false: "❌ 失败"}[result.Valid])

	if len(result.Errors) > 0 {
		summary += fmt.Sprintf("错误 (%d):\n", len(result.Errors))
		for i, err := range result.Errors {
			summary += fmt.Sprintf("  %d. %s\n", i+1, err)
		}
	}

	if len(result.Warnings) > 0 {
		summary += fmt.Sprintf("警告 (%d):\n", len(result.Warnings))
		for i, warning := range result.Warnings {
			summary += fmt.Sprintf("  %d. %s\n", i+1, warning)
		}
	}

	return summary
}
