package config

import "time"

// CallbackConfig 回调系统配置
type CallbackConfig struct {
	// 网关配置
	Gateway GatewayConfig `yaml:"gateway" mapstructure:"gateway"`

	// 供应商配置
	Providers ProvidersConfig `yaml:"providers" mapstructure:"providers"`

	// 队列配置
	Queue QueueConfig `yaml:"queue" mapstructure:"queue"`

	// 转发配置
	Forward ForwardConfig `yaml:"forward" mapstructure:"forward"`
}

// GatewayConfig 网关配置
type GatewayConfig struct {
	Timeout        time.Duration `yaml:"timeout" mapstructure:"timeout"`                   // 请求超时时间
	MaxRetries     int           `yaml:"max_retries" mapstructure:"max_retries"`           // 最大重试次数
	RetryInterval  time.Duration `yaml:"retry_interval" mapstructure:"retry_interval"`     // 重试间隔
	RateLimit      int           `yaml:"rate_limit" mapstructure:"rate_limit"`             // 限流配置
	EnableSecurity bool          `yaml:"enable_security" mapstructure:"enable_security"`   // 是否启用安全验证
	EnableAuditLog bool          `yaml:"enable_audit_log" mapstructure:"enable_audit_log"` // 是否启用审计日志
}

// ProvidersConfig 供应商配置
type ProvidersConfig struct {
	Yuntong   YuntongConfig   `yaml:"yuntong" mapstructure:"yuntong"`
	Yida      YidaConfig      `yaml:"yida" mapstructure:"yida"`
	Kuaidi100 Kuaidi100Config `yaml:"kuaidi100" mapstructure:"kuaidi100"`
	Cainiao   CainiaoConfig   `yaml:"cainiao" mapstructure:"cainiao"`
}

// YuntongConfig 云通配置
type YuntongConfig struct {
	EBusinessID string   `yaml:"e_business_id" mapstructure:"e_business_id"` // 商户ID
	SecretKey   string   `yaml:"secret_key" mapstructure:"secret_key"`       // 密钥
	AllowedIPs  []string `yaml:"allowed_ips" mapstructure:"allowed_ips"`     // 允许的IP列表
	Enabled     bool     `yaml:"enabled" mapstructure:"enabled"`             // 是否启用
}

// YidaConfig 易达配置
type YidaConfig struct {
	PrivateKey string   `yaml:"private_key" mapstructure:"private_key"` // RSA私钥
	AllowedIPs []string `yaml:"allowed_ips" mapstructure:"allowed_ips"` // 允许的IP列表
	Enabled    bool     `yaml:"enabled" mapstructure:"enabled"`         // 是否启用
}

// Kuaidi100Config 快递100配置
type Kuaidi100Config struct {
	Salt       string   `yaml:"salt" mapstructure:"salt"`               // 签名盐值
	AllowedIPs []string `yaml:"allowed_ips" mapstructure:"allowed_ips"` // 允许的IP列表
	Enabled    bool     `yaml:"enabled" mapstructure:"enabled"`         // 是否启用
}

// CainiaoConfig 菜鸟裹裹配置
type CainiaoConfig struct {
	AccessCode string   `yaml:"access_code" mapstructure:"access_code"` // 接入编码，用于签名验证
	AllowedIPs []string `yaml:"allowed_ips" mapstructure:"allowed_ips"` // 允许的IP列表
	Enabled    bool     `yaml:"enabled" mapstructure:"enabled"`         // 是否启用
}

// QueueConfig 队列配置
type QueueConfig struct {
	Type       string        `yaml:"type" mapstructure:"type"`               // 队列类型: redis/rabbitmq/kafka
	URL        string        `yaml:"url" mapstructure:"url"`                 // 连接URL
	MaxWorkers int           `yaml:"max_workers" mapstructure:"max_workers"` // 最大工作协程数
	BufferSize int           `yaml:"buffer_size" mapstructure:"buffer_size"` // 缓冲区大小
	Timeout    time.Duration `yaml:"timeout" mapstructure:"timeout"`         // 处理超时时间
	RetryDelay time.Duration `yaml:"retry_delay" mapstructure:"retry_delay"` // 重试延迟
	MaxRetries int           `yaml:"max_retries" mapstructure:"max_retries"` // 最大重试次数
}

// ForwardConfig 转发配置
type ForwardConfig struct {
	DefaultTimeout     time.Duration `yaml:"default_timeout" mapstructure:"default_timeout"`           // 默认超时时间
	DefaultRetryCount  int           `yaml:"default_retry_count" mapstructure:"default_retry_count"`   // 默认重试次数
	MaxRetryCount      int           `yaml:"max_retry_count" mapstructure:"max_retry_count"`           // 最大重试次数
	RetryInterval      time.Duration `yaml:"retry_interval" mapstructure:"retry_interval"`             // 重试间隔
	MaxRetryInterval   time.Duration `yaml:"max_retry_interval" mapstructure:"max_retry_interval"`     // 最大重试间隔
	EnableRetryBackoff bool          `yaml:"enable_retry_backoff" mapstructure:"enable_retry_backoff"` // 是否启用指数退避
	MaxConcurrency     int           `yaml:"max_concurrency" mapstructure:"max_concurrency"`           // 最大并发数
}

// DefaultCallbackConfig 默认回调配置
func DefaultCallbackConfig() *CallbackConfig {
	return &CallbackConfig{
		Gateway: GatewayConfig{
			Timeout:        30 * time.Second,
			MaxRetries:     3,
			RetryInterval:  5 * time.Second,
			RateLimit:      1000, // 每秒1000个请求
			EnableSecurity: true,
			EnableAuditLog: true,
		},
		Providers: ProvidersConfig{
			Yuntong: YuntongConfig{
				EBusinessID: "",
				SecretKey:   "",
				AllowedIPs:  []string{},
				Enabled:     true,
			},
			Yida: YidaConfig{
				PrivateKey: "",
				AllowedIPs: []string{},
				Enabled:    true,
			},
			Kuaidi100: Kuaidi100Config{
				Salt:       "",
				AllowedIPs: []string{},
				Enabled:    true,
			},
			Cainiao: CainiaoConfig{
				AccessCode: "",
				AllowedIPs: []string{},
				Enabled:    true,
			},
		},
		Queue: QueueConfig{
			Type:       "redis",
			URL:        "redis://localhost:6379/0",
			MaxWorkers: 10,
			BufferSize: 1000,
			Timeout:    30 * time.Second,
			RetryDelay: 5 * time.Second,
			MaxRetries: 3,
		},
		Forward: ForwardConfig{
			DefaultTimeout:     30 * time.Second,
			DefaultRetryCount:  3,
			MaxRetryCount:      5,
			RetryInterval:      5 * time.Second,
			MaxRetryInterval:   5 * time.Minute,
			EnableRetryBackoff: true,
			MaxConcurrency:     50,
		},
	}
}

// Validate 验证配置
func (c *CallbackConfig) Validate() error {
	// 验证网关配置
	if c.Gateway.Timeout <= 0 {
		c.Gateway.Timeout = 30 * time.Second
	}
	if c.Gateway.MaxRetries < 0 {
		c.Gateway.MaxRetries = 3
	}
	if c.Gateway.RetryInterval <= 0 {
		c.Gateway.RetryInterval = 5 * time.Second
	}
	if c.Gateway.RateLimit <= 0 {
		c.Gateway.RateLimit = 1000
	}

	// 验证队列配置
	if c.Queue.Type == "" {
		c.Queue.Type = "redis"
	}
	if c.Queue.MaxWorkers <= 0 {
		c.Queue.MaxWorkers = 10
	}
	if c.Queue.BufferSize <= 0 {
		c.Queue.BufferSize = 1000
	}
	if c.Queue.Timeout <= 0 {
		c.Queue.Timeout = 30 * time.Second
	}

	// 验证转发配置
	if c.Forward.DefaultTimeout <= 0 {
		c.Forward.DefaultTimeout = 30 * time.Second
	}
	if c.Forward.DefaultRetryCount < 0 {
		c.Forward.DefaultRetryCount = 3
	}
	if c.Forward.MaxRetryCount < c.Forward.DefaultRetryCount {
		c.Forward.MaxRetryCount = c.Forward.DefaultRetryCount
	}
	if c.Forward.RetryInterval <= 0 {
		c.Forward.RetryInterval = 5 * time.Second
	}
	if c.Forward.MaxRetryInterval <= c.Forward.RetryInterval {
		c.Forward.MaxRetryInterval = 5 * time.Minute
	}
	if c.Forward.MaxConcurrency <= 0 {
		c.Forward.MaxConcurrency = 50
	}

	return nil
}

// IsProviderEnabled 检查供应商是否启用
func (c *CallbackConfig) IsProviderEnabled(provider string) bool {
	switch provider {
	case "yuntong":
		return c.Providers.Yuntong.Enabled
	case "yida":
		return c.Providers.Yida.Enabled
	case "kuaidi100":
		return c.Providers.Kuaidi100.Enabled
	case "cainiao":
		return c.Providers.Cainiao.Enabled
	default:
		return false
	}
}

// GetProviderAllowedIPs 获取供应商允许的IP列表
func (c *CallbackConfig) GetProviderAllowedIPs(provider string) []string {
	switch provider {
	case "yuntong":
		return c.Providers.Yuntong.AllowedIPs
	case "yida":
		return c.Providers.Yida.AllowedIPs
	case "kuaidi100":
		return c.Providers.Kuaidi100.AllowedIPs
	case "cainiao":
		return c.Providers.Cainiao.AllowedIPs
	default:
		return []string{}
	}
}
