package util

import (
	"net/http"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// APIErrorResponse 错误响应结构
type APIErrorResponse struct {
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// APISuccessResponse 成功响应结构
type APISuccessResponse struct {
	Success bool        `json:"success"`
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ErrorResponse 返回错误响应
func ErrorResponse(c *gin.Context, code int, message, details string) {
	c.JSON(code, APIErrorResponse{
		Success: false,
		Code:    code,
		Message: message,
		Details: details,
	})
}

// SuccessResponse 返回成功响应
func SuccessResponse(c *gin.Context, message string, data interface{}) {
	c.<PERSON>(http.StatusOK, APISuccessResponse{
		Success: true,
		Code:    http.StatusOK,
		Message: message,
		Data:    data,
	})
}

// ValidateStruct 验证结构体
func ValidateStruct(s interface{}) error {
	validate := validator.New()
	
	// 注册自定义标签名函数
	validate.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})
	
	return validate.Struct(s)
}
