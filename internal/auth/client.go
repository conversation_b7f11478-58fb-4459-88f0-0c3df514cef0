package auth

import (
	"crypto/rand"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/security"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

var (
	ErrInvalidCredentials = errors.New("invalid client credentials")
	ErrClientDisabled     = errors.New("client is disabled")
)

// Client 表示API客户端
type Client struct {
	ID            string    `json:"id"`
	Secret        string    `json:"secret"`
	Name          string    `json:"name"`
	AllowedScopes []string  `json:"allowed_scopes"`
	IsActive      bool      `json:"is_active"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// ClientRepository 定义客户端存储接口
type ClientRepository interface {
	// FindByID 根据ID查找客户端
	FindByID(id string) (*Client, error)

	// Create 创建新客户端
	Create(client *Client) error

	// Update 更新客户端
	Update(client *Client) error

	// Delete 删除客户端
	Delete(id string) error
}

// ClientService 定义客户端服务接口
type ClientService interface {
	// Authenticate 验证客户端凭证
	Authenticate(clientID, clientSecret string) (*Client, error)

	// ValidateScopes 验证客户端是否有权访问指定范围
	ValidateScopes(client *Client, requestedScopes []string) ([]string, error)

	// GenerateClientCredentials 生成新的客户端凭证
	GenerateClientCredentials(name string, allowedScopes []string) (*Client, string, error)

	// ResetClientSecret 重置客户端密钥
	ResetClientSecret(clientID string) (string, error)

	// FindByID 根据ID查找客户端
	FindByID(clientID string) (*Client, error)
}

// DefaultClientService 实现ClientService接口
type DefaultClientService struct {
	repository ClientRepository
	logger     *zap.Logger
}

// NewClientService 创建新的客户端服务
func NewClientService(repository ClientRepository) ClientService {
	logger, _ := zap.NewProduction()
	return &DefaultClientService{
		repository: repository,
		logger:     logger,
	}
}

// Authenticate 验证客户端凭证
func (s *DefaultClientService) Authenticate(clientID, clientSecret string) (*Client, error) {
	// 查找客户端
	client, err := s.repository.FindByID(clientID)
	if err != nil {
		// 使用常量时间比较防止时序攻击
		_ = bcrypt.CompareHashAndPassword([]byte("dummy"), []byte(clientSecret))
		return nil, ErrInvalidCredentials
	}

	// 检查客户端状态
	if !client.IsActive {
		return nil, ErrClientDisabled
	}

	// 支持新AES加密方式
	if !strings.HasPrefix(client.Secret, "$2a$") {
		// 尝试AES解密
		decrypted, decErr := security.DecryptSecret(client.Secret)
		if decErr == nil {
			if decrypted != clientSecret {
				s.logger.Warn("Client authentication failed", zap.String("client_id", clientID), zap.String("error", "invalid_credentials"))
				return nil, ErrInvalidCredentials
			}
		} else {
			// 如果解密失败，回退到旧逻辑
		}
	}

	if strings.HasPrefix(client.Secret, "$2a$") {
		// 以bcrypt哈希开头，使用bcrypt验证
		if err := bcrypt.CompareHashAndPassword([]byte(client.Secret), []byte(clientSecret)); err != nil {
			s.logger.Warn("Client authentication failed",
				zap.String("client_id", clientID),
				zap.String("error", "invalid_credentials"))
			return nil, ErrInvalidCredentials
		}
	}

	// 记录成功的认证事件
	s.logger.Info("Client authenticated successfully",
		zap.String("client_id", clientID))

	return client, nil
}

// ValidateScopes 验证客户端是否有权访问指定范围
func (s *DefaultClientService) ValidateScopes(client *Client, requestedScopes []string) ([]string, error) {
	if len(requestedScopes) == 0 {
		return client.AllowedScopes, nil
	}

	// 验证请求的范围是否在允许的范围内
	var validScopes []string
	for _, scope := range requestedScopes {
		for _, allowedScope := range client.AllowedScopes {
			if scope == allowedScope {
				validScopes = append(validScopes, scope)
				break
			}
		}
	}

	return validScopes, nil
}

// GenerateClientCredentials 生成新的客户端凭证
func (s *DefaultClientService) GenerateClientCredentials(name string, allowedScopes []string) (*Client, string, error) {
	// 生成客户端ID
	clientID := GenerateSecureRandomString(24)

	// 生成客户端密钥
	clientSecret := GenerateSecureRandomString(32)

	// 生产环境：使用AES-GCM加密存储
	encrypted, err := security.EncryptSecret(clientSecret)
	if err != nil {
		return nil, "", fmt.Errorf("failed to encrypt secret: %w", err)
	}

	// 直接存储明文 Secret（⚠️ 注意：生产环境应加密存储或使用KMS）
	now := time.Now()
	client := &Client{
		ID:            clientID,
		Secret:        encrypted,
		Name:          name,
		AllowedScopes: allowedScopes,
		IsActive:      true,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	// 保存客户端
	if err := s.repository.Create(client); err != nil {
		return nil, "", fmt.Errorf("failed to create client: %w", err)
	}

	return client, clientSecret, nil
}

// ResetClientSecret 重置客户端密钥
func (s *DefaultClientService) ResetClientSecret(clientID string) (string, error) {
	// 查找客户端
	client, err := s.repository.FindByID(clientID)
	if err != nil {
		return "", fmt.Errorf("failed to find client: %w", err)
	}

	// 检查客户端状态
	if !client.IsActive {
		return "", ErrClientDisabled
	}

	// 生成新的客户端密钥
	clientSecret := GenerateSecureRandomString(32)

	encrypted, err := security.EncryptSecret(clientSecret)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt secret: %w", err)
	}

	// 更新客户端
	client.Secret = encrypted
	client.UpdatedAt = time.Now()

	// 保存客户端
	if err := s.repository.Update(client); err != nil {
		return "", fmt.Errorf("failed to update client: %w", err)
	}

	return clientSecret, nil
}

// FindByID 根据ID查找客户端
func (s *DefaultClientService) FindByID(clientID string) (*Client, error) {
	// 直接调用存储库的FindByID方法
	client, err := s.repository.FindByID(clientID)
	if err != nil {
		return nil, fmt.Errorf("failed to find client: %w", err)
	}

	return client, nil
}

// GenerateSecureRandomString 生成安全的随机字符串
func GenerateSecureRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)

	// 使用crypto/rand生成随机字节
	if _, err := rand.Read(b); err != nil {
		panic(fmt.Errorf("failed to generate random bytes: %w", err))
	}

	// 将随机字节映射到字符集
	for i := range b {
		b[i] = charset[int(b[i])%len(charset)]
	}

	return string(b)
}
