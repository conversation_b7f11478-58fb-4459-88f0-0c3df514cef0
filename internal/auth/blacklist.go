package auth

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// TokenBlacklist 定义令牌黑名单接口
type TokenBlacklist interface {
	// Add 将令牌添加到黑名单
	Add(ctx context.Context, tokenID string, expiration time.Duration) error

	// IsBlacklisted 检查令牌是否在黑名单中
	IsBlacklisted(ctx context.Context, tokenID string) (bool, error)
}

// RedisTokenBlacklist 使用Redis实现令牌黑名单
type RedisTokenBlacklist struct {
	client *redis.Client
	prefix string
}

// NewRedisTokenBlacklist 创建新的Redis令牌黑名单
func NewRedisTokenBlacklist(client *redis.Client, prefix string) TokenBlacklist {
	return &RedisTokenBlacklist{
		client: client,
		prefix: prefix,
	}
}

// Add 将令牌添加到黑名单
func (b *RedisTokenBlacklist) Add(ctx context.Context, tokenID string, expiration time.Duration) error {
	key := fmt.Sprintf("%s:%s", b.prefix, tokenID)
	if err := b.client.Set(ctx, key, "1", expiration).Err(); err != nil {
		return fmt.Errorf("failed to add token to blacklist: %w", err)
	}
	return nil
}

// IsBlacklisted 检查令牌是否在黑名单中
func (b *RedisTokenBlacklist) IsBlacklisted(ctx context.Context, tokenID string) (bool, error) {
	key := fmt.Sprintf("%s:%s", b.prefix, tokenID)
	result, err := b.client.Exists(ctx, key).Result()
	if err != nil {
		return false, fmt.Errorf("failed to check token in blacklist: %w", err)
	}
	return result > 0, nil
}
