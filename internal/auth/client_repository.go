package auth

import (
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/lib/pq"
)

// PostgresClientRepository 实现ClientRepository接口
type PostgresClientRepository struct {
	db *sql.DB
}

// NewPostgresClientRepository 创建新的PostgreSQL客户端存储
func NewPostgresClientRepository(db *sql.DB) ClientRepository {
	return &PostgresClientRepository{
		db: db,
	}
}

// FindByID 根据ID查找客户端
func (r *PostgresClientRepository) FindByID(id string) (*Client, error) {
	query := `
		SELECT id, secret, name, allowed_scopes, is_active, created_at, updated_at
		FROM oauth_clients
		WHERE id = $1
	`

	var client Client
	var allowedScopes pq.StringArray

	err := r.db.QueryRow(query, id).Scan(
		&client.ID,
		&client.Secret,
		&client.Name,
		&allowedScopes,
		&client.IsActive,
		&client.CreatedAt,
		&client.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("client not found: %w", err)
		}
		return nil, fmt.Errorf("failed to query client: %w", err)
	}

	client.AllowedScopes = []string(allowedScopes)

	return &client, nil
}

// Create 创建新客户端
func (r *PostgresClientRepository) Create(client *Client) error {
	query := `
		INSERT INTO oauth_clients (id, secret, name, allowed_scopes, is_active, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
	`

	_, err := r.db.Exec(
		query,
		client.ID,
		client.Secret,
		client.Name,
		pq.Array(client.AllowedScopes),
		client.IsActive,
		client.CreatedAt,
		client.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to create client: %w", err)
	}

	return nil
}

// Update 更新客户端
func (r *PostgresClientRepository) Update(client *Client) error {
	query := `
		UPDATE oauth_clients
		SET secret = $2, name = $3, allowed_scopes = $4, is_active = $5, updated_at = $6
		WHERE id = $1
	`

	client.UpdatedAt = time.Now()

	result, err := r.db.Exec(
		query,
		client.ID,
		client.Secret,
		client.Name,
		pq.Array(client.AllowedScopes),
		client.IsActive,
		client.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update client: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("client not found")
	}

	return nil
}

// Delete 删除客户端
func (r *PostgresClientRepository) Delete(id string) error {
	query := `
		DELETE FROM oauth_clients
		WHERE id = $1
	`

	result, err := r.db.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete client: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("client not found")
	}

	return nil
}
