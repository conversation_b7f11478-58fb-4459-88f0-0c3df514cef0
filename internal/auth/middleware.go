package auth

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// JWTAuthMiddleware 创建JWT认证中间件
func JWTAuthMiddleware(tokenService TokenService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Authorization头获取令牌
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    401,
				"message": "Authorization header is required",
			})
			return
		}

		// 解析Bearer令牌
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    401,
				"message": "Authorization header format must be Bearer {token}",
			})
			return
		}

		tokenString := parts[1]

		// 验证令牌
		claims, err := tokenService.ValidateToken(tokenString)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    401,
				"message": "Invalid or expired token",
			})
			return
		}

		// 将令牌声明存储到上下文中
		c.Set("claims", claims)
		c.Set("client_id", claims.ClientID)
		c.Set("scopes", claims.Scopes)

		// 🔥 修复：设置用户ID到上下文中
		// 对于用户令牌，Subject字段包含用户ID
		// 对于客户端令牌，ClientID字段包含客户端ID
		if claims.Subject != "" {
			// 用户令牌：Subject是用户ID
			c.Set("user_id", claims.Subject)
			c.Set("userID", claims.Subject) // 兼容其他中间件
		} else if claims.ClientID != "" {
			// 客户端令牌：ClientID作为用户标识
			c.Set("user_id", claims.ClientID)
			c.Set("userID", claims.ClientID) // 兼容其他中间件
		}

		c.Next()
	}
}

// ScopeRequiredMiddleware 创建范围验证中间件
func ScopeRequiredMiddleware(requiredScope string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文中获取范围
		scopes, exists := c.Get("scopes")
		if !exists {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    401,
				"message": "Authentication required",
			})
			return
		}

		// 验证范围
		scopeList, ok := scopes.([]string)
		if !ok {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"code":    500,
				"message": "Internal server error",
			})
			return
		}

		// 检查是否有所需范围
		hasScope := false
		for _, scope := range scopeList {
			if scope == requiredScope {
				hasScope = true
				break
			}
		}

		if !hasScope {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"success": false,
				"code":    403,
				"message": "Insufficient scope",
			})
			return
		}

		c.Next()
	}
}
