package auth

import (
	"context"
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// TokenRequest 表示令牌请求
type TokenRequest struct {
	GrantType    string `json:"grant_type" form:"grant_type" binding:"required"`
	ClientID     string `json:"client_id" form:"client_id" binding:"required"`
	ClientSecret string `json:"client_secret" form:"client_secret" binding:"required"`
	Scope        string `json:"scope" form:"scope"`
}

// TokenResponse 表示令牌响应
type TokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
	Scope       string `json:"scope"`
	TokenID     string `json:"token_id"`
}

// AuthController 处理认证相关请求
type AuthController struct {
	clientService ClientService
	tokenService  TokenService
	tokenExpiry   time.Duration
}

// NewAuthController 创建新的认证控制器
func NewAuthController(clientService ClientService, tokenService TokenService, tokenExpiry time.Duration) *AuthController {
	return &AuthController{
		clientService: clientService,
		tokenService:  tokenService,
		tokenExpiry:   tokenExpiry,
	}
}

// HandleTokenRequest 处理令牌请求
func (c *AuthController) HandleTokenRequest(ctx *gin.Context) {
	var req TokenRequest

	// 绑定请求参数
	if err := ctx.ShouldBind(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":             "invalid_request",
			"error_description": "Invalid request parameters",
		})
		return
	}

	// 验证授权类型
	if req.GrantType != "client_credentials" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":             "unsupported_grant_type",
			"error_description": "Only client_credentials grant type is supported",
		})
		return
	}

	// 验证客户端凭证
	client, err := c.clientService.Authenticate(req.ClientID, req.ClientSecret)
	if err != nil {
		var statusCode int
		var errorType, errorDesc string

		switch {
		case errors.Is(err, ErrInvalidCredentials):
			statusCode = http.StatusUnauthorized
			errorType = "invalid_client"
			errorDesc = "Invalid client credentials"
		case errors.Is(err, ErrClientDisabled):
			statusCode = http.StatusForbidden
			errorType = "access_denied"
			errorDesc = "Client is disabled"
		default:
			statusCode = http.StatusInternalServerError
			errorType = "server_error"
			errorDesc = "An internal server error occurred"
		}

		ctx.JSON(statusCode, gin.H{
			"error":             errorType,
			"error_description": errorDesc,
		})
		return
	}

	// 解析请求的范围
	var requestedScopes []string
	if req.Scope != "" {
		requestedScopes = strings.Split(req.Scope, " ")
	}

	// 验证范围
	validScopes, err := c.clientService.ValidateScopes(client, requestedScopes)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"error":             "invalid_scope",
			"error_description": "The requested scope is invalid",
		})
		return
	}

	// 生成访问令牌
	accessToken, err := c.tokenService.GenerateAccessToken(client.ID, validScopes)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":             "server_error",
			"error_description": "Failed to generate access token",
		})
		return
	}

	// 解析令牌以获取令牌ID
	claims, err := c.tokenService.ValidateToken(accessToken)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"error":             "server_error",
			"error_description": "Failed to validate access token",
		})
		return
	}

	// 返回令牌响应
	ctx.JSON(http.StatusOK, TokenResponse{
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   int(c.tokenExpiry.Seconds()),
		Scope:       strings.Join(validScopes, " "),
		TokenID:     claims.ID,
	})
}

// RevokeToken 撤销令牌
func (c *AuthController) RevokeToken(ctx context.Context, tokenID string) error {
	return c.tokenService.RevokeToken(ctx, tokenID)
}
