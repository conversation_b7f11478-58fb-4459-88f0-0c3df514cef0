package auth

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
	"path/filepath"
)

// GenerateRSAKeyPair 生成RSA密钥对
func GenerateRSAKeyPair(bits int) (*rsa.PrivateKey, *rsa.PublicKey, error) {
	// 生成私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, bits)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to generate RSA key pair: %w", err)
	}

	// 提取公钥
	publicKey := &privateKey.PublicKey

	return privateKey, publicKey, nil
}

// SaveRSAKeyPair 将RSA密钥对保存到文件
func SaveRSAKeyPair(privateKey *rsa.PrivateKey, privateKeyPath, publicKeyPath string) error {
	// 创建目录
	if err := os.MkdirAll(filepath.Dir(privateKeyPath), 0700); err != nil {
		return fmt.Errorf("failed to create directory for private key: %w", err)
	}

	if err := os.MkdirAll(filepath.Dir(publicKeyPath), 0700); err != nil {
		return fmt.Errorf("failed to create directory for public key: %w", err)
	}

	// 编码私钥
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	})

	// 保存私钥
	if err := os.WriteFile(privateKeyPath, privateKeyPEM, 0600); err != nil {
		return fmt.Errorf("failed to save private key: %w", err)
	}

	// 编码公钥
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		return fmt.Errorf("failed to marshal public key: %w", err)
	}

	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	// 保存公钥
	if err := os.WriteFile(publicKeyPath, publicKeyPEM, 0644); err != nil {
		return fmt.Errorf("failed to save public key: %w", err)
	}

	return nil
}

// LoadRSAPrivateKey 从文件加载RSA私钥
func LoadRSAPrivateKey(privateKeyPath string) (*rsa.PrivateKey, error) {
	// 读取私钥文件
	privateKeyPEM, err := os.ReadFile(privateKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read private key file: %w", err)
	}

	// 解码PEM
	block, _ := pem.Decode(privateKeyPEM)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block containing private key")
	}

	// 解析私钥
	var privateKey *rsa.PrivateKey

	// 尝试解析 PKCS1 格式
	privateKeyPKCS1, errPKCS1 := x509.ParsePKCS1PrivateKey(block.Bytes)
	if errPKCS1 == nil {
		privateKey = privateKeyPKCS1
	} else {
		// 如果失败，尝试解析 PKCS8 格式
		pkcs8Key, errPKCS8 := x509.ParsePKCS8PrivateKey(block.Bytes)
		if errPKCS8 != nil {
			return nil, fmt.Errorf("failed to parse private key: PKCS1: %v, PKCS8: %v", errPKCS1, errPKCS8)
		}

		var ok bool
		privateKey, ok = pkcs8Key.(*rsa.PrivateKey)
		if !ok {
			return nil, fmt.Errorf("not an RSA private key")
		}
	}

	return privateKey, nil
}

// LoadRSAPublicKey 从文件加载RSA公钥
func LoadRSAPublicKey(publicKeyPath string) (*rsa.PublicKey, error) {
	// 读取公钥文件
	publicKeyPEM, err := os.ReadFile(publicKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read public key file: %w", err)
	}

	// 解码PEM
	block, _ := pem.Decode(publicKeyPEM)
	if block == nil || block.Type != "PUBLIC KEY" {
		return nil, fmt.Errorf("failed to decode PEM block containing public key")
	}

	// 解析公钥
	publicKeyInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	// 转换为RSA公钥
	publicKey, ok := publicKeyInterface.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("not an RSA public key")
	}

	return publicKey, nil
}

// keysMatch 判断公私钥是否匹配（通过比较模数和指数）
func keysMatch(priv *rsa.PrivateKey, pub *rsa.PublicKey) bool {
	return priv.PublicKey.N.Cmp(pub.N) == 0 && priv.PublicKey.E == pub.E
}

// EnsureRSAKeyPair 尝试加载密钥对，若不存在或不匹配则自动重新生成并持久化
func EnsureRSAKeyPair(privateKeyPath, publicKeyPath string) (*rsa.PrivateKey, *rsa.PublicKey, error) {
	priv, pub, err := func() (*rsa.PrivateKey, *rsa.PublicKey, error) {
		priv, perr := LoadRSAPrivateKey(privateKeyPath)
		if perr != nil {
			return nil, nil, perr
		}

		pub, uerr := LoadRSAPublicKey(publicKeyPath)
		if uerr != nil {
			return nil, nil, uerr
		}

		return priv, pub, nil
	}()

	// 如果加载成功且匹配直接返回
	if err == nil && keysMatch(priv, pub) {
		return priv, pub, nil
	}

	// 加载失败或不匹配 -> 重新生成
	newPriv, newPub, genErr := GenerateRSAKeyPair(2048)
	if genErr != nil {
		return nil, nil, fmt.Errorf("failed to generate rsa key pair: %w", genErr)
	}

	// 持久化新的密钥对
	if saveErr := SaveRSAKeyPair(newPriv, privateKeyPath, publicKeyPath); saveErr != nil {
		return nil, nil, fmt.Errorf("failed to save generated rsa key pair: %w", saveErr)
	}

	return newPriv, newPub, nil
}
