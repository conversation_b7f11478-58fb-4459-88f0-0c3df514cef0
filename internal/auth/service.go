package auth

import (
	"context"
	"crypto/rsa"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
)

// TokenService 定义令牌相关操作的接口
type TokenService interface {
	// GenerateAccessToken 生成访问令牌
	GenerateAccessToken(clientID string, scopes []string) (string, error)

	// GenerateUserToken 生成用户令牌（用于管理员登录）
	GenerateUserToken(userID string, scopes []string) (string, error)

	// ValidateToken 验证令牌并返回声明
	ValidateToken(tokenString string) (*TokenClaims, error)

	// RevokeToken 撤销令牌
	RevokeToken(ctx context.Context, tokenID string) error
}

// TokenClaims 定义JWT令牌的声明
type TokenClaims struct {
	jwt.RegisteredClaims
	ClientID string   `json:"client_id"`
	Scopes   []string `json:"scopes"`
}

// JWTService 实现TokenService接口
type JWTService struct {
	privateKey    *rsa.PrivateKey
	publicKey     *rsa.PublicKey
	issuer        string
	audience      string
	tokenExpiry   time.Duration
	signingMethod jwt.SigningMethod
	blacklist     TokenBlacklist
}

// NewJWTService 创建新的JWT服务
func NewJWTService(
	privateKey *rsa.PrivateKey,
	publicKey *rsa.PublicKey,
	issuer string,
	audience string,
	tokenExpiry time.Duration,
	blacklist TokenBlacklist,
) TokenService {
	return &JWTService{
		privateKey:    privateKey,
		publicKey:     publicKey,
		issuer:        issuer,
		audience:      audience,
		tokenExpiry:   tokenExpiry,
		signingMethod: jwt.SigningMethodRS256,
		blacklist:     blacklist,
	}
}

// GenerateAccessToken 生成JWT访问令牌
func (s *JWTService) GenerateAccessToken(clientID string, scopes []string) (string, error) {
	now := time.Now()

	// 无条件记录原始配置
	fmt.Printf("GenerateAccessToken: original tokenExpiry=%v (%d seconds)\n", s.tokenExpiry, int(s.tokenExpiry.Seconds()))

	// 保险逻辑：确保tokenExpiry不小于10分钟
	actualExpiry := s.tokenExpiry
	if actualExpiry < 10*time.Minute {
		actualExpiry = time.Hour // 强制1小时
		fmt.Printf("WARNING: tokenExpiry %v < 10min, forced to 1 hour\n", s.tokenExpiry)
	}

	// 增加5分钟缓冲时间解决时钟偏差问题
	expiresAt := now.Add(actualExpiry).Add(5 * time.Minute)
	fmt.Printf("JWT Generation: now=%d, expiry=%v, expiresAt=%d, diff=%d seconds (with 5min buffer)\n",
		now.Unix(), actualExpiry, expiresAt.Unix(), expiresAt.Unix()-now.Unix())

	// 创建令牌声明
	claims := TokenClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    s.issuer,
			Subject:   clientID,
			Audience:  jwt.ClaimStrings{s.audience},
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        uuid.New().String(),
		},
		ClientID: clientID,
		Scopes:   scopes,
	}

	// 创建令牌
	token := jwt.NewWithClaims(s.signingMethod, claims)

	// 签名令牌
	return token.SignedString(s.privateKey)
}

// GenerateUserToken 生成用户JWT令牌（用于管理员登录）
func (s *JWTService) GenerateUserToken(userID string, scopes []string) (string, error) {
	now := time.Now()

	// 无条件记录原始配置
	fmt.Printf("GenerateUserToken: original tokenExpiry=%v (%d seconds)\n", s.tokenExpiry, int(s.tokenExpiry.Seconds()))

	// 保险逻辑：确保tokenExpiry不小于10分钟
	actualExpiry := s.tokenExpiry
	if actualExpiry < 10*time.Minute {
		actualExpiry = time.Hour // 强制1小时
		fmt.Printf("WARNING: tokenExpiry %v < 10min in GenerateUserToken, forced to 1 hour\n", s.tokenExpiry)
	}

	// 增加5分钟缓冲时间解决时钟偏差问题
	expiresAt := now.Add(actualExpiry).Add(5 * time.Minute)
	fmt.Printf("JWT User Token Generation: now=%d, expiry=%v, expiresAt=%d, diff=%d seconds (with 5min buffer)\n",
		now.Unix(), actualExpiry, expiresAt.Unix(), expiresAt.Unix()-now.Unix())

	// 创建令牌声明，使用用户ID作为subject
	claims := TokenClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    s.issuer,
			Subject:   userID, // 使用用户ID而不是客户端ID
			Audience:  jwt.ClaimStrings{s.audience},
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        uuid.New().String(),
		},
		ClientID: userID, // 为了兼容性，也设置ClientID为用户ID
		Scopes:   scopes,
	}

	// 创建令牌
	token := jwt.NewWithClaims(s.signingMethod, claims)

	// 签名令牌
	return token.SignedString(s.privateKey)
}

// ValidateToken 验证JWT令牌并返回声明
func (s *JWTService) ValidateToken(tokenString string) (*TokenClaims, error) {
	// 首先进行基本的令牌格式验证，但不验证签名
	// 这样可以提取令牌ID用于黑名单检查，避免时序攻击
	parser := jwt.NewParser(jwt.WithoutClaimsValidation())
	unverifiedToken, _, err := parser.ParseUnverified(tokenString, &TokenClaims{})
	if err != nil {
		return nil, fmt.Errorf("invalid token format: %w", err)
	}

	// 提取未验证的声明用于黑名单检查
	unverifiedClaims, ok := unverifiedToken.Claims.(*TokenClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims format")
	}

	// 优先检查令牌是否在黑名单中（在验证签名之前）
	if s.blacklist != nil && unverifiedClaims.ID != "" {
		isBlacklisted, err := s.blacklist.IsBlacklisted(context.Background(), unverifiedClaims.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to check token blacklist: %w", err)
		}
		if isBlacklisted {
			return nil, fmt.Errorf("token has been revoked")
		}
	}

	// 现在进行完整的令牌验证（包括签名验证）
	token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.publicKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	// 验证令牌有效性
	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	// 提取验证后的声明
	claims, ok := token.Claims.(*TokenClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	return claims, nil
}

// RevokeToken 撤销令牌
func (s *JWTService) RevokeToken(ctx context.Context, tokenID string) error {
	if s.blacklist == nil {
		return fmt.Errorf("token blacklist not configured")
	}

	return s.blacklist.Add(ctx, tokenID, s.tokenExpiry)
}
