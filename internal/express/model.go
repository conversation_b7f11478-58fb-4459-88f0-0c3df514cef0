package express

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// ExpressCompany 快递公司模型
type ExpressCompany struct {
	ID                  string     `json:"id" db:"id"`
	Code                string     `json:"code" db:"code"`
	Name                string     `json:"name" db:"name"`
	EnglishName         *string    `json:"english_name,omitempty" db:"english_name"`
	OfficialWebsite     *string    `json:"official_website,omitempty" db:"official_website"`
	Description         *string    `json:"description,omitempty" db:"description"`
	IsActive            bool       `json:"is_active" db:"is_active"`
	SortOrder           int        `json:"sort_order" db:"sort_order"`
	VolumeWeightRatio   int        `json:"volume_weight_ratio" db:"volume_weight_ratio"`
	MaxWeightKg         *float64   `json:"max_weight_kg,omitempty" db:"max_weight_kg"`
	MaxVolumeCm3        *float64   `json:"max_volume_cm3,omitempty" db:"max_volume_cm3"`
	SupportVolumeWeight bool       `json:"support_volume_weight" db:"support_volume_weight"`
	CreatedAt           time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt           *time.Time `json:"deleted_at,omitempty" db:"deleted_at"`
	IsDeleted           bool       `json:"is_deleted" db:"is_deleted"`
	CreatedBy           *string    `json:"created_by,omitempty" db:"created_by"`
	UpdatedBy           *string    `json:"updated_by,omitempty" db:"updated_by"`
	DeletedBy           *string    `json:"deleted_by,omitempty" db:"deleted_by"`

	// 关联数据（不存储在数据库中）
	Providers []ExpressProvider               `json:"providers,omitempty"`
	Services  []ExpressCompanyServiceModel    `json:"services,omitempty"`
	Configs   map[string]ExpressCompanyConfig `json:"configs,omitempty"`
	Mappings  []ExpressCompanyProviderMapping `json:"mappings,omitempty"`
}

// ExpressProvider 快递供应商模型
type ExpressProvider struct {
	ID             string `json:"id" db:"id"`
	Code           string `json:"code" db:"code"`
	Name           string `json:"name" db:"name"`
	APIBaseURL     string `json:"api_base_url" db:"api_base_url"`
	APIVersion     string `json:"api_version" db:"api_version"`
	TimeoutSeconds int    `json:"timeout_seconds" db:"timeout_seconds"`
	MaxRetries     int    `json:"max_retries" db:"max_retries"`
	// IsActive字段已移除 - 供应商启用状态现在通过system_configs.provider_{code}.enabled管理
	Priority           int        `json:"priority" db:"priority"`
	RateLimitPerSecond int        `json:"rate_limit_per_second" db:"rate_limit_per_second"`
	Description        *string    `json:"description,omitempty" db:"description"`
	CreatedAt          time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt          time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt          *time.Time `json:"deleted_at,omitempty" db:"deleted_at"`
	IsDeleted          bool       `json:"is_deleted" db:"is_deleted"`
	CreatedBy          *string    `json:"created_by,omitempty" db:"created_by"`
	UpdatedBy          *string    `json:"updated_by,omitempty" db:"updated_by"`
	DeletedBy          *string    `json:"deleted_by,omitempty" db:"deleted_by"`
}

// ExpressCompanyProviderMapping 快递公司与供应商映射模型
type ExpressCompanyProviderMapping struct {
	ID                  string                 `json:"id" db:"id"`
	CompanyID           string                 `json:"company_id" db:"company_id"`
	ProviderID          string                 `json:"provider_id" db:"provider_id"`
	ProviderCompanyCode string                 `json:"provider_company_code" db:"provider_company_code"`
	IsSupported         bool                   `json:"is_supported" db:"is_supported"`
	IsPreferred         bool                   `json:"is_preferred" db:"is_preferred"`
	WeightLimitKg       *float64               `json:"weight_limit_kg,omitempty" db:"weight_limit_kg"`
	SizeLimitCmLength   *float64               `json:"size_limit_cm_length,omitempty" db:"size_limit_cm_length"`
	SizeLimitCmWidth    *float64               `json:"size_limit_cm_width,omitempty" db:"size_limit_cm_width"`
	SizeLimitCmHeight   *float64               `json:"size_limit_cm_height,omitempty" db:"size_limit_cm_height"`
	SupportedServices   []string               `json:"supported_services,omitempty"`
	PricingConfig       map[string]interface{} `json:"pricing_config,omitempty"`
	ProductTypeMappings []byte                 `json:"product_type_mappings,omitempty" db:"product_type_mappings"`
	PayMethodMappings   []byte                 `json:"pay_method_mappings,omitempty" db:"pay_method_mappings"`
	StatusCodeMappings  []byte                 `json:"status_code_mappings,omitempty" db:"status_code_mappings"`
	CreatedAt           time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time              `json:"updated_at" db:"updated_at"`
	DeletedAt           *time.Time             `json:"deleted_at,omitempty" db:"deleted_at"`
	IsDeleted           bool                   `json:"is_deleted" db:"is_deleted"`
	CreatedBy           *string                `json:"created_by,omitempty" db:"created_by"`
	UpdatedBy           *string                `json:"updated_by,omitempty" db:"updated_by"`
	DeletedBy           *string                `json:"deleted_by,omitempty" db:"deleted_by"`

	// 关联数据
	Company  *ExpressCompany  `json:"company,omitempty"`
	Provider *ExpressProvider `json:"provider,omitempty"`
}

// ExpressCompanyServiceModel 快递公司服务类型模型
type ExpressCompanyServiceModel struct {
	ID            string     `json:"id" db:"id"`
	CompanyID     string     `json:"company_id" db:"company_id"`
	ServiceCode   string     `json:"service_code" db:"service_code"`
	ServiceName   string     `json:"service_name" db:"service_name"`
	Description   *string    `json:"description,omitempty" db:"description"`
	EstimatedDays *int       `json:"estimated_days,omitempty" db:"estimated_days"`
	IsActive      bool       `json:"is_active" db:"is_active"`
	SortOrder     int        `json:"sort_order" db:"sort_order"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt     *time.Time `json:"deleted_at,omitempty" db:"deleted_at"`
	IsDeleted     bool       `json:"is_deleted" db:"is_deleted"`
	CreatedBy     *string    `json:"created_by,omitempty" db:"created_by"`
	UpdatedBy     *string    `json:"updated_by,omitempty" db:"updated_by"`
	DeletedBy     *string    `json:"deleted_by,omitempty" db:"deleted_by"`
}

// ExpressCompanyConfig 快递公司配置模型
type ExpressCompanyConfig struct {
	ID          string     `json:"id" db:"id"`
	CompanyID   string     `json:"company_id" db:"company_id"`
	ConfigKey   string     `json:"config_key" db:"config_key"`
	ConfigValue string     `json:"config_value" db:"config_value"`
	ConfigType  string     `json:"config_type" db:"config_type"`
	Description *string    `json:"description,omitempty" db:"description"`
	IsActive    bool       `json:"is_active" db:"is_active"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" db:"deleted_at"`
	IsDeleted   bool       `json:"is_deleted" db:"is_deleted"`
	CreatedBy   *string    `json:"created_by,omitempty" db:"created_by"`
	UpdatedBy   *string    `json:"updated_by,omitempty" db:"updated_by"`
	DeletedBy   *string    `json:"deleted_by,omitempty" db:"deleted_by"`
}

// ExpressCompanyAuditLog 快递公司管理审计日志模型
type ExpressCompanyAuditLog struct {
	ID               string                 `json:"id" db:"id"`
	OperatorID       string                 `json:"operator_id" db:"operator_id"`
	TargetCompanyID  *string                `json:"target_company_id,omitempty" db:"target_company_id"`
	Action           string                 `json:"action" db:"action"`
	Operation        string                 `json:"operation" db:"operation"`
	OperationDetails map[string]interface{} `json:"operation_details,omitempty"`
	Result           string                 `json:"result" db:"result"`
	ErrorMessage     *string                `json:"error_message,omitempty" db:"error_message"`
	IPAddress        *string                `json:"ip_address,omitempty" db:"ip_address"`
	UserAgent        *string                `json:"user_agent,omitempty" db:"user_agent"`
	CreatedAt        time.Time              `json:"created_at" db:"created_at"`
}

// NewExpressCompany 创建新的快递公司
func NewExpressCompany(code, name string, createdBy *string) *ExpressCompany {
	return &ExpressCompany{
		ID:        uuid.New().String(),
		Code:      code,
		Name:      name,
		IsActive:  true,
		SortOrder: 0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		CreatedBy: createdBy,
		UpdatedBy: createdBy,
	}
}

// NewExpressProvider 创建新的快递供应商
func NewExpressProvider(code, name, apiBaseURL string, createdBy *string) *ExpressProvider {
	return &ExpressProvider{
		ID:             uuid.New().String(),
		Code:           code,
		Name:           name,
		APIBaseURL:     apiBaseURL,
		APIVersion:     "v1",
		TimeoutSeconds: 30,
		MaxRetries:     3,
		// IsActive字段已移除 - 供应商启用状态现在通过system_configs.provider_{code}.enabled管理
		Priority:           0,
		RateLimitPerSecond: 10,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
		CreatedBy:          createdBy,
		UpdatedBy:          createdBy,
	}
}

// NewExpressCompanyProviderMapping 创建新的快递公司供应商映射
func NewExpressCompanyProviderMapping(companyID, providerID, providerCompanyCode string, createdBy *string) *ExpressCompanyProviderMapping {
	return &ExpressCompanyProviderMapping{
		ID:                  uuid.New().String(),
		CompanyID:           companyID,
		ProviderID:          providerID,
		ProviderCompanyCode: providerCompanyCode,
		IsSupported:         true,
		IsPreferred:         false,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
		CreatedBy:           createdBy,
		UpdatedBy:           createdBy,
	}
}

// NewExpressCompanyService 创建新的快递公司服务
func NewExpressCompanyService(companyID, serviceCode, serviceName string, createdBy *string) *ExpressCompanyServiceModel {
	return &ExpressCompanyServiceModel{
		ID:          uuid.New().String(),
		CompanyID:   companyID,
		ServiceCode: serviceCode,
		ServiceName: serviceName,
		IsActive:    true,
		SortOrder:   0,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		CreatedBy:   createdBy,
		UpdatedBy:   createdBy,
	}
}

// NewExpressCompanyConfig 创建新的快递公司配置
func NewExpressCompanyConfig(companyID, configKey, configValue, configType string, createdBy *string) *ExpressCompanyConfig {
	return &ExpressCompanyConfig{
		ID:          uuid.New().String(),
		CompanyID:   companyID,
		ConfigKey:   configKey,
		ConfigValue: configValue,
		ConfigType:  configType,
		IsActive:    true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		CreatedBy:   createdBy,
		UpdatedBy:   createdBy,
	}
}

// NewExpressCompanyAuditLog 创建新的审计日志
func NewExpressCompanyAuditLog(operatorID string, targetCompanyID *string, action, operation string, details map[string]interface{}, result string, ipAddress, userAgent *string) *ExpressCompanyAuditLog {
	return &ExpressCompanyAuditLog{
		ID:               uuid.New().String(),
		OperatorID:       operatorID,
		TargetCompanyID:  targetCompanyID,
		Action:           action,
		Operation:        operation,
		OperationDetails: details,
		Result:           result,
		IPAddress:        ipAddress,
		UserAgent:        userAgent,
		CreatedAt:        time.Now(),
	}
}

// GetParsedSupportedServices 解析支持的服务列表
func (m *ExpressCompanyProviderMapping) GetParsedSupportedServices() []string {
	if len(m.SupportedServices) > 0 {
		return m.SupportedServices
	}
	return []string{}
}

// GetParsedPricingConfig 解析价格配置
func (m *ExpressCompanyProviderMapping) GetParsedPricingConfig() map[string]interface{} {
	if m.PricingConfig != nil {
		return m.PricingConfig
	}
	return make(map[string]interface{})
}

// GetParsedOperationDetails 解析操作详情
func (log *ExpressCompanyAuditLog) GetParsedOperationDetails() map[string]interface{} {
	if log.OperationDetails != nil {
		return log.OperationDetails
	}
	return make(map[string]interface{})
}

// GetConfigValueAsString 获取配置值作为字符串
func (config *ExpressCompanyConfig) GetConfigValueAsString() string {
	return config.ConfigValue
}

// GetConfigValueAsInt 获取配置值作为整数
func (config *ExpressCompanyConfig) GetConfigValueAsInt() (int, error) {
	var value int
	err := json.Unmarshal([]byte(config.ConfigValue), &value)
	return value, err
}

// GetConfigValueAsFloat 获取配置值作为浮点数
func (config *ExpressCompanyConfig) GetConfigValueAsFloat() (float64, error) {
	var value float64
	err := json.Unmarshal([]byte(config.ConfigValue), &value)
	return value, err
}

// GetConfigValueAsBool 获取配置值作为布尔值
func (config *ExpressCompanyConfig) GetConfigValueAsBool() (bool, error) {
	var value bool
	err := json.Unmarshal([]byte(config.ConfigValue), &value)
	return value, err
}

// GetConfigValueAsJSON 获取配置值作为JSON对象
func (config *ExpressCompanyConfig) GetConfigValueAsJSON() (map[string]interface{}, error) {
	var value map[string]interface{}
	err := json.Unmarshal([]byte(config.ConfigValue), &value)
	return value, err
}
