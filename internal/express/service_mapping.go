package express

import (
	"context"
	"fmt"

	"go.uber.org/zap"
)

// CreateMapping 创建映射关系
func (s *DefaultExpressCompanyService) CreateMapping(ctx context.Context, req CreateMappingRequest, operatorID string) (*ExpressCompanyProviderMapping, error) {
	// 验证请求参数
	if err := s.validateCreateMappingRequest(req); err != nil {
		return nil, err
	}

	// 检查快递公司是否存在
	_, err := s.repository.GetCompanyByID(req.CompanyID)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return nil, fmt.Errorf("快递公司不存在")
		}
		return nil, fmt.Errorf("查询快递公司失败: %w", err)
	}

	// 检查供应商是否存在
	_, err = s.repository.GetProviderByID(req.ProviderID)
	if err != nil {
		if err == ErrExpressProviderNotFound {
			return nil, fmt.Errorf("供应商不存在")
		}
		return nil, fmt.Errorf("查询供应商失败: %w", err)
	}

	// 检查映射关系是否已存在
	existingMapping, err := s.repository.GetMapping(req.CompanyID, req.ProviderID)
	if err != nil && err != ErrExpressMappingNotFound {
		return nil, fmt.Errorf("检查映射关系失败: %w", err)
	}
	if existingMapping != nil {
		return nil, fmt.Errorf("映射关系已存在")
	}

	// 创建映射关系
	mapping := NewExpressCompanyProviderMapping(req.CompanyID, req.ProviderID, req.ProviderCompanyCode, &operatorID)

	// 设置可选字段
	if req.IsSupported != nil {
		mapping.IsSupported = *req.IsSupported
	}
	if req.IsPreferred != nil {
		mapping.IsPreferred = *req.IsPreferred
	}
	if req.WeightLimitKg != nil {
		mapping.WeightLimitKg = req.WeightLimitKg
	}
	if req.SizeLimitCmLength != nil {
		mapping.SizeLimitCmLength = req.SizeLimitCmLength
	}
	if req.SizeLimitCmWidth != nil {
		mapping.SizeLimitCmWidth = req.SizeLimitCmWidth
	}
	if req.SizeLimitCmHeight != nil {
		mapping.SizeLimitCmHeight = req.SizeLimitCmHeight
	}
	if req.SupportedServices != nil {
		mapping.SupportedServices = req.SupportedServices
	}
	if req.PricingConfig != nil {
		mapping.PricingConfig = req.PricingConfig
	}

	// 保存到数据库
	if err := s.repository.CreateMapping(mapping); err != nil {
		s.logger.Error("Failed to create express company provider mapping",
			zap.Error(err),
			zap.String("company_id", req.CompanyID),
			zap.String("provider_id", req.ProviderID))
		return nil, fmt.Errorf("创建映射关系失败: %w", err)
	}

	// 🔥 修复：映射创建后立即刷新缓存，确保新映射立即生效
	if s.cacheService != nil {
		if err := s.cacheService.RefreshCache(ctx); err != nil {
			s.logger.Warn("映射创建后刷新缓存失败",
				zap.Error(err),
				zap.String("mapping_id", mapping.ID),
				zap.String("company_id", mapping.CompanyID),
				zap.String("provider_id", mapping.ProviderID))
			// 不返回错误，因为创建已经成功，缓存刷新失败不应该影响主要操作
		} else {
			s.logger.Info("映射创建后缓存刷新成功",
				zap.String("mapping_id", mapping.ID),
				zap.String("company_id", mapping.CompanyID),
				zap.String("provider_id", mapping.ProviderID),
				zap.Bool("is_supported", mapping.IsSupported))
		}
	}

	// 🔥 新增：映射创建后，通知所有相关缓存系统进行同步
	if err := s.notifyMappingStateChange(ctx, mapping); err != nil {
		s.logger.Warn("映射创建状态变更通知失败",
			zap.Error(err),
			zap.String("mapping_id", mapping.ID),
			zap.String("company_id", mapping.CompanyID),
			zap.String("provider_id", mapping.ProviderID))
		// 不返回错误，因为主要操作已经成功
	}

	s.logger.Info("Express company provider mapping created successfully",
		zap.String("mapping_id", mapping.ID),
		zap.String("company_id", mapping.CompanyID),
		zap.String("provider_id", mapping.ProviderID),
		zap.String("operator_id", operatorID))

	return mapping, nil
}

// GetMappingByID 根据ID获取映射关系
func (s *DefaultExpressCompanyService) GetMappingByID(ctx context.Context, id string) (*ExpressCompanyProviderMapping, error) {
	if id == "" {
		return nil, fmt.Errorf("映射关系ID不能为空")
	}

	mapping, err := s.repository.GetMappingByID(id)
	if err != nil {
		if err == ErrExpressMappingNotFound {
			return nil, fmt.Errorf("映射关系不存在")
		}
		s.logger.Error("Failed to get express company provider mapping by ID",
			zap.Error(err),
			zap.String("mapping_id", id))
		return nil, fmt.Errorf("获取映射关系失败: %w", err)
	}

	return mapping, nil
}

// GetMapping 获取特定的映射关系
func (s *DefaultExpressCompanyService) GetMapping(ctx context.Context, companyID, providerID string) (*ExpressCompanyProviderMapping, error) {
	if companyID == "" || providerID == "" {
		return nil, fmt.Errorf("快递公司ID和供应商ID不能为空")
	}

	mapping, err := s.repository.GetMapping(companyID, providerID)
	if err != nil {
		if err == ErrExpressMappingNotFound {
			return nil, fmt.Errorf("映射关系不存在")
		}
		s.logger.Error("Failed to get express company provider mapping",
			zap.Error(err),
			zap.String("company_id", companyID),
			zap.String("provider_id", providerID))
		return nil, fmt.Errorf("获取映射关系失败: %w", err)
	}

	return mapping, nil
}

// GetMappingsByCompany 根据快递公司ID获取映射关系
func (s *DefaultExpressCompanyService) GetMappingsByCompany(ctx context.Context, companyID string) ([]*ExpressCompanyProviderMapping, error) {
	if companyID == "" {
		return nil, fmt.Errorf("快递公司ID不能为空")
	}

	mappings, err := s.repository.GetMappingsByCompany(companyID)
	if err != nil {
		s.logger.Error("Failed to get mappings by company",
			zap.Error(err),
			zap.String("company_id", companyID))
		return nil, fmt.Errorf("获取快递公司映射关系失败: %w", err)
	}

	return mappings, nil
}

// GetMappingsByProvider 根据供应商ID获取映射关系
func (s *DefaultExpressCompanyService) GetMappingsByProvider(ctx context.Context, providerID string) ([]*ExpressCompanyProviderMapping, error) {
	if providerID == "" {
		return nil, fmt.Errorf("供应商ID不能为空")
	}

	mappings, err := s.repository.GetMappingsByProvider(providerID)
	if err != nil {
		s.logger.Error("Failed to get mappings by provider",
			zap.Error(err),
			zap.String("provider_id", providerID))
		return nil, fmt.Errorf("获取供应商映射关系失败: %w", err)
	}

	return mappings, nil
}

// UpdateMapping 更新映射关系
func (s *DefaultExpressCompanyService) UpdateMapping(ctx context.Context, id string, req UpdateMappingRequest, operatorID string) (*ExpressCompanyProviderMapping, error) {
	if id == "" {
		return nil, fmt.Errorf("映射关系ID不能为空")
	}

	// 获取现有映射关系
	mapping, err := s.repository.GetMappingByID(id)
	if err != nil {
		if err == ErrExpressMappingNotFound {
			return nil, fmt.Errorf("映射关系不存在")
		}
		return nil, fmt.Errorf("获取映射关系失败: %w", err)
	}

	// 更新字段
	if req.ProviderCompanyCode != nil {
		if err := s.validateProviderCompanyCode(*req.ProviderCompanyCode); err != nil {
			return nil, err
		}
		mapping.ProviderCompanyCode = *req.ProviderCompanyCode
	}
	if req.IsSupported != nil {
		mapping.IsSupported = *req.IsSupported
	}
	if req.IsPreferred != nil {
		mapping.IsPreferred = *req.IsPreferred
	}
	if req.WeightLimitKg != nil {
		mapping.WeightLimitKg = req.WeightLimitKg
	}
	if req.SizeLimitCmLength != nil {
		mapping.SizeLimitCmLength = req.SizeLimitCmLength
	}
	if req.SizeLimitCmWidth != nil {
		mapping.SizeLimitCmWidth = req.SizeLimitCmWidth
	}
	if req.SizeLimitCmHeight != nil {
		mapping.SizeLimitCmHeight = req.SizeLimitCmHeight
	}
	if req.SupportedServices != nil {
		mapping.SupportedServices = req.SupportedServices
	}
	if req.PricingConfig != nil {
		mapping.PricingConfig = req.PricingConfig
	}

	mapping.UpdatedBy = &operatorID

	// 🚀 修复：先提交数据库，再刷新缓存，保证缓存用到最新数据
	if err := s.repository.UpdateMapping(mapping); err != nil {
		s.logger.Error("Failed to update express company provider mapping",
			zap.Error(err),
			zap.String("mapping_id", id))
		return nil, fmt.Errorf("更新映射关系失败: %w", err)
	}

	// 🚀 新增：数据库提交后，立即查询最新is_supported值并打印
	latestMapping, err := s.repository.GetMappingByID(id)
	if err == nil {
		s.logger.Info("[DEBUG] 数据库最新is_supported值", zap.String("mapping_id", id), zap.String("company_id", latestMapping.CompanyID), zap.String("provider_id", latestMapping.ProviderID), zap.Bool("is_supported", latestMapping.IsSupported))
	}

	// 🚀 修复：映射更新后立即刷新缓存，确保禁用状态立即生效
	if s.cacheService != nil {
		// 获取供应商信息，用于精确刷新
		provider, err := s.repository.GetProviderByID(mapping.ProviderID)
		if err != nil {
			s.logger.Error("获取供应商信息失败", zap.Error(err))
			if err := s.cacheService.RefreshCache(ctx); err != nil {
				s.logger.Warn("映射更新后全局缓存刷新失败", zap.Error(err))
			} else {
				s.logger.Info("映射更新后全局缓存刷新成功",
					zap.String("mapping_id", mapping.ID),
					zap.Bool("is_supported", mapping.IsSupported))
			}
		} else {
			s.logger.Info("开始强制刷新供应商缓存",
				zap.String("mapping_id", mapping.ID),
				zap.String("provider_code", provider.Code),
				zap.Bool("is_supported", mapping.IsSupported))

			if err := s.cacheService.RefreshProviderCache(ctx, provider.Code); err != nil {
				s.logger.Error("刷新供应商缓存失败",
					zap.String("provider_code", provider.Code),
					zap.Error(err))
				if err := s.cacheService.RefreshCache(ctx); err != nil {
					s.logger.Error("备选全局缓存刷新也失败", zap.Error(err))
				}
			} else {
				s.logger.Info("映射更新后缓存刷新成功",
					zap.String("mapping_id", mapping.ID),
					zap.String("provider_code", provider.Code),
					zap.Bool("is_supported", mapping.IsSupported))
			}
			// 🚀 新增：无论单供应商缓存是否刷新成功，都强制刷新全局映射缓存，确保查价全局缓存热生效
			if err := s.cacheService.RefreshCache(ctx); err != nil {
				s.logger.Warn("全局映射缓存刷新失败", zap.Error(err))
			} else {
				s.logger.Info("全局映射缓存刷新成功",
					zap.String("mapping_id", mapping.ID),
					zap.String("provider_code", provider.Code),
					zap.Bool("is_supported", mapping.IsSupported))
			}
			// 🚀 新增：刷新后打印缓存内容，便于调试
			if s.cacheService != nil {
				companies, _ := s.cacheService.GetSupportedCompanies(ctx, provider.Code)
				for i, c := range companies {
					s.logger.Info("[DEBUG] 刷新后供应商缓存内容", zap.String("provider_code", provider.Code), zap.Int("index", i), zap.String("company_code", c.CompanyCode), zap.Bool("is_supported", c.IsSupported))
				}
			}
		}
	}

	// 🔥 新增：映射状态变更后，通知所有相关缓存系统进行同步
	if err := s.notifyMappingStateChange(ctx, mapping); err != nil {
		s.logger.Warn("映射状态变更通知失败",
			zap.Error(err),
			zap.String("mapping_id", mapping.ID),
			zap.String("company_id", mapping.CompanyID),
			zap.String("provider_id", mapping.ProviderID))
		// 不返回错误，因为主要操作已经成功
	}

	s.logger.Info("Express company provider mapping updated successfully",
		zap.String("mapping_id", mapping.ID),
		zap.String("company_id", mapping.CompanyID),
		zap.String("provider_id", mapping.ProviderID),
		zap.String("operator_id", operatorID))

	return mapping, nil
}

// notifyMappingStateChange 通知映射状态变更，触发相关缓存同步
func (s *DefaultExpressCompanyService) notifyMappingStateChange(ctx context.Context, mapping *ExpressCompanyProviderMapping) error {
	// 获取快递公司和供应商信息
	company, err := s.repository.GetCompanyByID(mapping.CompanyID)
	if err != nil {
		return fmt.Errorf("获取快递公司信息失败: %w", err)
	}

	provider, err := s.repository.GetProviderByID(mapping.ProviderID)
	if err != nil {
		return fmt.Errorf("获取供应商信息失败: %w", err)
	}

	s.logger.Info("开始通知映射状态变更",
		zap.String("company_code", company.Code),
		zap.String("company_name", company.Name),
		zap.String("provider_code", provider.Code),
		zap.String("provider_name", provider.Name),
		zap.Bool("is_supported", mapping.IsSupported),
		zap.String("mapping_id", mapping.ID))

	// 🚀 通知缓存事件系统处理相关缓存
	if s.eventPublisher != nil {
		s.eventPublisher.PublishMappingChangedEvent(company.Code, provider.Code)
		s.logger.Info("映射状态变更事件已发布",
			zap.String("company_code", company.Code),
			zap.String("provider_code", provider.Code),
			zap.Bool("is_supported", mapping.IsSupported))
	} else {
		s.logger.Debug("事件发布器未设置，跳过缓存失效事件发布")
	}

	return nil
}

// DeleteMapping 删除映射关系
func (s *DefaultExpressCompanyService) DeleteMapping(ctx context.Context, id string, operatorID string) error {
	if id == "" {
		return fmt.Errorf("映射关系ID不能为空")
	}

	// 检查映射关系是否存在
	mapping, err := s.repository.GetMappingByID(id)
	if err != nil {
		if err == ErrExpressMappingNotFound {
			return fmt.Errorf("映射关系不存在")
		}
		return fmt.Errorf("获取映射关系失败: %w", err)
	}

	// 删除映射关系
	if err := s.repository.DeleteMapping(id); err != nil {
		s.logger.Error("Failed to delete express company provider mapping",
			zap.Error(err),
			zap.String("mapping_id", id))
		return fmt.Errorf("删除映射关系失败: %w", err)
	}

	// 🔥 修复：映射删除后立即刷新缓存，确保删除状态立即生效
	if s.cacheService != nil {
		if err := s.cacheService.RefreshCache(ctx); err != nil {
			s.logger.Warn("映射删除后刷新缓存失败",
				zap.Error(err),
				zap.String("mapping_id", id),
				zap.String("company_id", mapping.CompanyID),
				zap.String("provider_id", mapping.ProviderID))
			// 不返回错误，因为删除已经成功，缓存刷新失败不应该影响主要操作
		} else {
			s.logger.Info("映射删除后缓存刷新成功",
				zap.String("mapping_id", id),
				zap.String("company_id", mapping.CompanyID),
				zap.String("provider_id", mapping.ProviderID))
		}
	}

	// 🔥 新增：映射删除后，通知所有相关缓存系统进行同步
	if err := s.notifyMappingStateChange(ctx, mapping); err != nil {
		s.logger.Warn("映射删除状态变更通知失败",
			zap.Error(err),
			zap.String("mapping_id", id),
			zap.String("company_id", mapping.CompanyID),
			zap.String("provider_id", mapping.ProviderID))
		// 不返回错误，因为主要操作已经成功
	}

	s.logger.Info("Express company provider mapping deleted successfully",
		zap.String("mapping_id", id),
		zap.String("company_id", mapping.CompanyID),
		zap.String("provider_id", mapping.ProviderID),
		zap.String("operator_id", operatorID))

	return nil
}

// 验证创建映射关系请求
func (s *DefaultExpressCompanyService) validateCreateMappingRequest(req CreateMappingRequest) error {
	if req.CompanyID == "" {
		return fmt.Errorf("快递公司ID不能为空")
	}

	if req.ProviderID == "" {
		return fmt.Errorf("供应商ID不能为空")
	}

	if err := s.validateProviderCompanyCode(req.ProviderCompanyCode); err != nil {
		return err
	}

	return nil
}

// 验证供应商快递公司代码
func (s *DefaultExpressCompanyService) validateProviderCompanyCode(code string) error {
	if code == "" {
		return fmt.Errorf("供应商快递公司代码不能为空")
	}

	if len(code) < 1 || len(code) > 50 {
		return fmt.Errorf("供应商快递公司代码长度必须在1-50个字符之间")
	}

	return nil
}

// GetMappings 获取映射关系列表
func (s *DefaultExpressCompanyService) GetMappings(ctx context.Context, filter MappingFilter, pagination Pagination) (*MappingListResult, error) {
	// 验证分页参数
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.PageSize <= 0 || pagination.PageSize > 100 {
		pagination.PageSize = 20
	}

	// 验证排序参数
	if filter.SortBy != "" {
		allowedSortFields := []string{"created_at", "updated_at", "company_id", "provider_id", "is_supported", "is_preferred"}
		if !s.isValidSortField(filter.SortBy, allowedSortFields) {
			return nil, fmt.Errorf("无效的排序字段: %s", filter.SortBy)
		}
	}

	if filter.SortOrder != "" && filter.SortOrder != "ASC" && filter.SortOrder != "DESC" {
		filter.SortOrder = "DESC"
	}

	// 调用Repository层获取数据
	result, err := s.repository.GetMappings(filter, pagination)
	if err != nil {
		s.logger.Error("Failed to get express company provider mappings",
			zap.Error(err),
			zap.Any("filter", filter),
			zap.Any("pagination", pagination))
		return nil, fmt.Errorf("获取映射关系列表失败: %w", err)
	}

	return result, nil
}
