package express

import "errors"

// 快递公司管理相关错误定义
var (
	// 快递公司错误
	ErrExpressCompanyNotFound = errors.New("快递公司不存在")
	ErrExpressCompanyExists   = errors.New("快递公司已存在")
	
	// 供应商错误
	ErrExpressProviderNotFound = errors.New("供应商不存在")
	ErrExpressProviderExists   = errors.New("供应商已存在")
	
	// 映射关系错误
	ErrExpressMappingNotFound = errors.New("映射关系不存在")
	ErrExpressMappingExists   = errors.New("映射关系已存在")
	
	// 服务错误
	ErrExpressServiceNotFound = errors.New("服务不存在")
	ErrExpressServiceExists   = errors.New("服务已存在")
	
	// 配置错误
	ErrExpressConfigNotFound = errors.New("配置不存在")
	ErrExpressConfigExists   = errors.New("配置已存在")
	
	// 审计日志错误
	ErrExpressAuditLogNotFound = errors.New("审计日志不存在")
	
	// 业务逻辑错误
	ErrInvalidCompanyCode    = errors.New("无效的快递公司代码")
	ErrInvalidProviderCode   = errors.New("无效的供应商代码")
	ErrInvalidServiceCode    = errors.New("无效的服务代码")
	ErrInvalidConfigKey      = errors.New("无效的配置键")
	ErrInvalidConfigValue    = errors.New("无效的配置值")
	ErrInvalidConfigType     = errors.New("无效的配置类型")
	ErrCompanyNotActive      = errors.New("快递公司未启用")
	ErrProviderNotActive     = errors.New("供应商未启用")
	ErrServiceNotActive      = errors.New("服务未启用")
	ErrMappingNotSupported   = errors.New("映射关系不支持")
)
