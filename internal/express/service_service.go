package express

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
)

// CreateService 创建服务
func (s *DefaultExpressCompanyService) CreateService(ctx context.Context, req CreateServiceRequest, operatorID string) (*ExpressCompanyServiceModel, error) {
	// 验证请求参数
	if err := s.validateCreateServiceRequest(req); err != nil {
		return nil, err
	}
	
	// 检查快递公司是否存在
	_, err := s.repository.GetCompanyByID(req.CompanyID)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return nil, fmt.Errorf("快递公司不存在")
		}
		return nil, fmt.Errorf("查询快递公司失败: %w", err)
	}
	
	// 检查服务代码是否已存在（同一快递公司内）
	existingServices, err := s.repository.GetServicesByCompany(req.CompanyID)
	if err != nil {
		return nil, fmt.Errorf("查询现有服务失败: %w", err)
	}
	
	for _, existingService := range existingServices {
		if existingService.ServiceCode == req.ServiceCode {
			return nil, fmt.Errorf("服务代码 %s 在该快递公司中已存在", req.ServiceCode)
		}
	}
	
	// 创建服务
	service := NewExpressCompanyService(req.CompanyID, req.ServiceCode, req.ServiceName, &operatorID)
	
	// 设置可选字段
	if req.Description != nil {
		service.Description = req.Description
	}
	if req.EstimatedDays != nil {
		service.EstimatedDays = req.EstimatedDays
	}
	if req.IsActive != nil {
		service.IsActive = *req.IsActive
	}
	if req.SortOrder != nil {
		service.SortOrder = *req.SortOrder
	}
	
	// 保存到数据库
	if err := s.repository.CreateService(service); err != nil {
		s.logger.Error("Failed to create express company service",
			zap.Error(err),
			zap.String("company_id", req.CompanyID),
			zap.String("service_code", req.ServiceCode))
		return nil, fmt.Errorf("创建快递公司服务失败: %w", err)
	}
	
	s.logger.Info("Express company service created successfully",
		zap.String("service_id", service.ID),
		zap.String("company_id", service.CompanyID),
		zap.String("service_code", service.ServiceCode),
		zap.String("operator_id", operatorID))
	
	return service, nil
}

// GetServicesByCompany 根据快递公司ID获取服务列表
func (s *DefaultExpressCompanyService) GetServicesByCompany(ctx context.Context, companyID string) ([]*ExpressCompanyServiceModel, error) {
	if companyID == "" {
		return nil, fmt.Errorf("快递公司ID不能为空")
	}
	
	// 检查快递公司是否存在
	_, err := s.repository.GetCompanyByID(companyID)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return nil, fmt.Errorf("快递公司不存在")
		}
		return nil, fmt.Errorf("查询快递公司失败: %w", err)
	}
	
	services, err := s.repository.GetServicesByCompany(companyID)
	if err != nil {
		s.logger.Error("Failed to get services by company",
			zap.Error(err),
			zap.String("company_id", companyID))
		return nil, fmt.Errorf("获取快递公司服务失败: %w", err)
	}
	
	return services, nil
}

// UpdateService 更新服务
func (s *DefaultExpressCompanyService) UpdateService(ctx context.Context, id string, req UpdateServiceRequest, operatorID string) (*ExpressCompanyServiceModel, error) {
	if id == "" {
		return nil, fmt.Errorf("服务ID不能为空")
	}
	
	// 获取现有服务
	services, err := s.repository.GetServicesByCompany("")
	if err != nil {
		return nil, fmt.Errorf("查询服务失败: %w", err)
	}
	
	var service *ExpressCompanyServiceModel
	for _, svc := range services {
		if svc.ID == id {
			service = svc
			break
		}
	}
	
	if service == nil {
		return nil, fmt.Errorf("服务不存在")
	}
	
	// 更新字段
	if req.ServiceName != nil {
		if err := s.validateServiceName(*req.ServiceName); err != nil {
			return nil, err
		}
		service.ServiceName = *req.ServiceName
	}
	if req.Description != nil {
		service.Description = req.Description
	}
	if req.EstimatedDays != nil {
		if err := s.validateEstimatedDays(*req.EstimatedDays); err != nil {
			return nil, err
		}
		service.EstimatedDays = req.EstimatedDays
	}
	if req.IsActive != nil {
		service.IsActive = *req.IsActive
	}
	if req.SortOrder != nil {
		service.SortOrder = *req.SortOrder
	}
	
	service.UpdatedBy = &operatorID
	
	// 保存更新
	if err := s.repository.UpdateService(service); err != nil {
		s.logger.Error("Failed to update express company service",
			zap.Error(err),
			zap.String("service_id", id))
		return nil, fmt.Errorf("更新快递公司服务失败: %w", err)
	}
	
	s.logger.Info("Express company service updated successfully",
		zap.String("service_id", service.ID),
		zap.String("company_id", service.CompanyID),
		zap.String("service_code", service.ServiceCode),
		zap.String("operator_id", operatorID))
	
	return service, nil
}

// DeleteService 删除服务
func (s *DefaultExpressCompanyService) DeleteService(ctx context.Context, id string, operatorID string) error {
	if id == "" {
		return fmt.Errorf("服务ID不能为空")
	}
	
	// 检查服务是否存在
	services, err := s.repository.GetServicesByCompany("")
	if err != nil {
		return fmt.Errorf("查询服务失败: %w", err)
	}
	
	var service *ExpressCompanyServiceModel
	for _, svc := range services {
		if svc.ID == id {
			service = svc
			break
		}
	}
	
	if service == nil {
		return fmt.Errorf("服务不存在")
	}
	
	// 删除服务
	if err := s.repository.DeleteService(id); err != nil {
		s.logger.Error("Failed to delete express company service",
			zap.Error(err),
			zap.String("service_id", id))
		return fmt.Errorf("删除快递公司服务失败: %w", err)
	}
	
	s.logger.Info("Express company service deleted successfully",
		zap.String("service_id", id),
		zap.String("company_id", service.CompanyID),
		zap.String("service_code", service.ServiceCode),
		zap.String("operator_id", operatorID))
	
	return nil
}

// 验证创建服务请求
func (s *DefaultExpressCompanyService) validateCreateServiceRequest(req CreateServiceRequest) error {
	if req.CompanyID == "" {
		return fmt.Errorf("快递公司ID不能为空")
	}
	
	if err := s.validateServiceCode(req.ServiceCode); err != nil {
		return err
	}
	
	if err := s.validateServiceName(req.ServiceName); err != nil {
		return err
	}
	
	if req.EstimatedDays != nil {
		if err := s.validateEstimatedDays(*req.EstimatedDays); err != nil {
			return err
		}
	}
	
	return nil
}

// 验证服务代码
func (s *DefaultExpressCompanyService) validateServiceCode(code string) error {
	if code == "" {
		return fmt.Errorf("服务代码不能为空")
	}
	
	if len(code) < 1 || len(code) > 50 {
		return fmt.Errorf("服务代码长度必须在1-50个字符之间")
	}
	
	// 只允许字母、数字和下划线
	for _, char := range code {
		if !((char >= 'A' && char <= 'Z') || (char >= 'a' && char <= 'z') || 
			 (char >= '0' && char <= '9') || char == '_') {
			return fmt.Errorf("服务代码只能包含字母、数字和下划线")
		}
	}
	
	return nil
}

// 验证服务名称
func (s *DefaultExpressCompanyService) validateServiceName(name string) error {
	if name == "" {
		return fmt.Errorf("服务名称不能为空")
	}
	
	if len(strings.TrimSpace(name)) < 1 {
		return fmt.Errorf("服务名称不能为空")
	}
	
	if len(name) > 100 {
		return fmt.Errorf("服务名称长度不能超过100个字符")
	}
	
	return nil
}

// 验证预计送达天数
func (s *DefaultExpressCompanyService) validateEstimatedDays(days int) error {
	if days < 0 {
		return fmt.Errorf("预计送达天数不能小于0")
	}
	
	if days > 30 {
		return fmt.Errorf("预计送达天数不能超过30天")
	}
	
	return nil
}
