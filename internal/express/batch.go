package express

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// BatchOperation 批量操作接口
type BatchOperation interface {
	// Execute 执行批量操作
	Execute(ctx context.Context) (*BatchResult, error)
	// Validate 验证批量操作
	Validate() error
	// GetOperationType 获取操作类型
	GetOperationType() string
	// GetItemCount 获取操作项数量
	GetItemCount() int
}

// BatchResult 批量操作结果
type BatchResult struct {
	TotalItems    int                    `json:"total_items"`
	SuccessItems  int                    `json:"success_items"`
	FailedItems   int                    `json:"failed_items"`
	Results       []BatchItemResult      `json:"results"`
	Errors        []BatchError           `json:"errors,omitempty"`
	Duration      time.Duration          `json:"duration"`
	OperationType string                 `json:"operation_type"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// BatchItemResult 批量操作单项结果
type BatchItemResult struct {
	Index   int         `json:"index"`
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// BatchError 批量操作错误
type BatchError struct {
	Index   int    `json:"index"`
	Message string `json:"message"`
	Code    string `json:"code,omitempty"`
}

// BatchCreateMappingsRequest 批量创建映射关系请求
type BatchCreateMappingsRequest struct {
	Mappings   []CreateMappingRequest `json:"mappings" binding:"required,min=1,max=100"`
	OperatorID string                 `json:"-"` // 由中间件设置
}

// Validate 验证批量创建映射关系请求
func (r *BatchCreateMappingsRequest) Validate() error {
	if len(r.Mappings) == 0 {
		return fmt.Errorf("映射关系列表不能为空")
	}
	if len(r.Mappings) > 100 {
		return fmt.Errorf("单次批量操作不能超过100个映射关系")
	}
	
	// 验证每个映射关系
	for i, mapping := range r.Mappings {
		if err := mapping.Validate(); err != nil {
			return fmt.Errorf("第%d个映射关系验证失败: %w", i+1, err)
		}
	}
	
	// 检查重复的映射关系
	seen := make(map[string]bool)
	for i, mapping := range r.Mappings {
		key := fmt.Sprintf("%s:%s", mapping.CompanyID, mapping.ProviderID)
		if seen[key] {
			return fmt.Errorf("第%d个映射关系重复: 公司ID %s 和供应商ID %s", i+1, mapping.CompanyID, mapping.ProviderID)
		}
		seen[key] = true
	}
	
	return nil
}

// Execute 执行批量创建映射关系
func (r *BatchCreateMappingsRequest) Execute(ctx context.Context) (*BatchResult, error) {
	// 这个方法将在服务层实现
	return nil, fmt.Errorf("not implemented")
}

// GetOperationType 获取操作类型
func (r *BatchCreateMappingsRequest) GetOperationType() string {
	return "batch_create_mappings"
}

// GetItemCount 获取操作项数量
func (r *BatchCreateMappingsRequest) GetItemCount() int {
	return len(r.Mappings)
}

// BatchUpdateMappingsRequest 批量更新映射关系请求
type BatchUpdateMappingsRequest struct {
	Updates    []BatchUpdateMappingItem `json:"updates" binding:"required,min=1,max=100"`
	OperatorID string                   `json:"-"` // 由中间件设置
}

// BatchUpdateMappingItem 批量更新映射关系项
type BatchUpdateMappingItem struct {
	ID      string                `json:"id" binding:"required,uuid"`
	Updates UpdateMappingRequest  `json:"updates" binding:"required"`
}

// Validate 验证批量更新映射关系请求
func (r *BatchUpdateMappingsRequest) Validate() error {
	if len(r.Updates) == 0 {
		return fmt.Errorf("更新列表不能为空")
	}
	if len(r.Updates) > 100 {
		return fmt.Errorf("单次批量操作不能超过100个更新")
	}
	
	// 验证每个更新项
	for i, update := range r.Updates {
		if err := ValidateUUID(update.ID); err != nil {
			return fmt.Errorf("第%d个更新项ID验证失败: %w", i+1, err)
		}
		if err := update.Updates.Validate(); err != nil {
			return fmt.Errorf("第%d个更新项验证失败: %w", i+1, err)
		}
	}
	
	// 检查重复的ID
	seen := make(map[string]bool)
	for i, update := range r.Updates {
		if seen[update.ID] {
			return fmt.Errorf("第%d个更新项ID重复: %s", i+1, update.ID)
		}
		seen[update.ID] = true
	}
	
	return nil
}

// GetOperationType 获取操作类型
func (r *BatchUpdateMappingsRequest) GetOperationType() string {
	return "batch_update_mappings"
}

// GetItemCount 获取操作项数量
func (r *BatchUpdateMappingsRequest) GetItemCount() int {
	return len(r.Updates)
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	IDs        []string `json:"ids" binding:"required,min=1,max=100"`
	OperatorID string   `json:"-"` // 由中间件设置
}

// Validate 验证批量删除请求
func (r *BatchDeleteRequest) Validate() error {
	if len(r.IDs) == 0 {
		return fmt.Errorf("ID列表不能为空")
	}
	if len(r.IDs) > 100 {
		return fmt.Errorf("单次批量操作不能超过100个ID")
	}
	
	// 验证每个ID
	for i, id := range r.IDs {
		if err := ValidateUUID(id); err != nil {
			return fmt.Errorf("第%d个ID验证失败: %w", i+1, err)
		}
	}
	
	// 检查重复的ID
	seen := make(map[string]bool)
	for i, id := range r.IDs {
		if seen[id] {
			return fmt.Errorf("第%d个ID重复: %s", i+1, id)
		}
		seen[id] = true
	}
	
	return nil
}

// GetOperationType 获取操作类型
func (r *BatchDeleteRequest) GetOperationType() string {
	return "batch_delete"
}

// GetItemCount 获取操作项数量
func (r *BatchDeleteRequest) GetItemCount() int {
	return len(r.IDs)
}

// BatchProcessor 批量处理器
type BatchProcessor struct {
	maxConcurrency int
	logger         *zap.Logger
	metrics        MetricsCollector
}

// NewBatchProcessor 创建批量处理器
func NewBatchProcessor(maxConcurrency int, logger *zap.Logger, metrics MetricsCollector) *BatchProcessor {
	if maxConcurrency <= 0 {
		maxConcurrency = 10 // 默认并发数
	}
	
	return &BatchProcessor{
		maxConcurrency: maxConcurrency,
		logger:         logger,
		metrics:        metrics,
	}
}

// ProcessBatch 处理批量操作
func (p *BatchProcessor) ProcessBatch(ctx context.Context, items []interface{}, processor func(ctx context.Context, item interface{}) (interface{}, error)) *BatchResult {
	start := time.Now()
	
	result := &BatchResult{
		TotalItems: len(items),
		Results:    make([]BatchItemResult, len(items)),
		Errors:     make([]BatchError, 0),
		Duration:   0,
	}
	
	// 创建工作池
	semaphore := make(chan struct{}, p.maxConcurrency)
	var wg sync.WaitGroup
	var mu sync.Mutex
	
	// 处理每个项目
	for i, item := range items {
		wg.Add(1)
		go func(index int, data interface{}) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			
			// 处理项目
			processedData, err := processor(ctx, data)
			
			// 更新结果
			mu.Lock()
			if err != nil {
				result.Results[index] = BatchItemResult{
					Index:   index,
					Success: false,
					Error:   err.Error(),
				}
				result.Errors = append(result.Errors, BatchError{
					Index:   index,
					Message: err.Error(),
				})
				result.FailedItems++
			} else {
				result.Results[index] = BatchItemResult{
					Index:   index,
					Success: true,
					Data:    processedData,
				}
				result.SuccessItems++
			}
			mu.Unlock()
		}(i, item)
	}
	
	// 等待所有任务完成
	wg.Wait()
	
	result.Duration = time.Since(start)
	
	// 记录指标
	if p.metrics != nil {
		p.metrics.RecordDuration("batch_operation_duration", result.Duration)
		p.metrics.IncrementCounter("batch_operations_total")
		p.metrics.SetGauge("batch_operation_success_rate", float64(result.SuccessItems)/float64(result.TotalItems))
	}
	
	p.logger.Info("Batch operation completed",
		zap.Int("total_items", result.TotalItems),
		zap.Int("success_items", result.SuccessItems),
		zap.Int("failed_items", result.FailedItems),
		zap.Duration("duration", result.Duration))
	
	return result
}

// ValidateBatchSize 验证批量操作大小
func ValidateBatchSize(size int, maxSize int) error {
	if size <= 0 {
		return fmt.Errorf("批量操作大小必须大于0")
	}
	if size > maxSize {
		return fmt.Errorf("批量操作大小不能超过%d", maxSize)
	}
	return nil
}

// SplitBatch 分割批量操作
func SplitBatch(items []interface{}, batchSize int) [][]interface{} {
	if batchSize <= 0 {
		batchSize = 50 // 默认批量大小
	}
	
	var batches [][]interface{}
	for i := 0; i < len(items); i += batchSize {
		end := i + batchSize
		if end > len(items) {
			end = len(items)
		}
		batches = append(batches, items[i:end])
	}
	
	return batches
}

// BatchImportRequest 批量导入请求
type BatchImportRequest struct {
	Data       []byte `json:"data" binding:"required"`
	Format     string `json:"format" binding:"required,oneof=csv json excel"`
	OperatorID string `json:"-"` // 由中间件设置
}

// Validate 验证批量导入请求
func (r *BatchImportRequest) Validate() error {
	if len(r.Data) == 0 {
		return fmt.Errorf("导入数据不能为空")
	}
	if len(r.Data) > 10*1024*1024 { // 10MB限制
		return fmt.Errorf("导入数据大小不能超过10MB")
	}
	
	validFormats := []string{"csv", "json", "excel"}
	for _, format := range validFormats {
		if r.Format == format {
			return nil
		}
	}
	
	return fmt.Errorf("不支持的导入格式: %s，支持的格式: csv, json, excel", r.Format)
}

// BatchExportRequest 批量导出请求
type BatchExportRequest struct {
	Format    string                 `json:"format" binding:"required,oneof=csv json excel"`
	Filter    map[string]interface{} `json:"filter,omitempty"`
	Fields    []string               `json:"fields,omitempty"`
	SortBy    string                 `json:"sort_by,omitempty"`
	SortOrder string                 `json:"sort_order,omitempty"`
}

// Validate 验证批量导出请求
func (r *BatchExportRequest) Validate() error {
	validFormats := []string{"csv", "json", "excel"}
	for _, format := range validFormats {
		if r.Format == format {
			return nil
		}
	}
	
	return fmt.Errorf("不支持的导出格式: %s，支持的格式: csv, json, excel", r.Format)
}
