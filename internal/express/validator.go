package express

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/google/uuid"
)

// Validator 验证器接口
type Validator interface {
	Validate() error
}

// ValidateUUID 验证UUID格式
func ValidateUUID(id string) error {
	if id == "" {
		return fmt.Errorf("ID不能为空")
	}
	if _, err := uuid.Parse(id); err != nil {
		return fmt.Errorf("无效的UUID格式: %s", id)
	}
	return nil
}

// ValidateCode 验证代码格式（字母、数字、下划线、连字符）
func ValidateCode(code string) error {
	if code == "" {
		return fmt.Errorf("代码不能为空")
	}
	if len(code) < 2 || len(code) > 50 {
		return fmt.Errorf("代码长度必须在2-50个字符之间")
	}

	// 只允许字母、数字、下划线、连字符
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, code)
	if !matched {
		return fmt.E<PERSON>rf("代码只能包含字母、数字、下划线和连字符")
	}
	return nil
}

// ValidateName 验证名称格式
func ValidateName(name string) error {
	if name == "" {
		return fmt.Errorf("名称不能为空")
	}
	if len(name) < 1 || len(name) > 100 {
		return fmt.Errorf("名称长度必须在1-100个字符之间")
	}

	// 去除首尾空格
	name = strings.TrimSpace(name)
	if name == "" {
		return fmt.Errorf("名称不能为空白字符")
	}
	return nil
}

// ValidateURL 验证URL格式
func ValidateURL(url string) error {
	if url == "" {
		return nil // URL可以为空
	}

	// 简单的URL格式验证
	matched, _ := regexp.MatchString(`^https?://[^\s/$.?#].[^\s]*$`, url)
	if !matched {
		return fmt.Errorf("无效的URL格式: %s", url)
	}
	return nil
}

// ValidatePhone 验证电话号码格式
func ValidatePhone(phone string) error {
	if phone == "" {
		return nil // 电话号码可以为空
	}

	// 支持国内外电话号码格式
	matched, _ := regexp.MatchString(`^[\d\s\-\+\(\)]{7,20}$`, phone)
	if !matched {
		return fmt.Errorf("无效的电话号码格式: %s", phone)
	}
	return nil
}

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) error {
	if email == "" {
		return nil // 邮箱可以为空
	}

	matched, _ := regexp.MatchString(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`, email)
	if !matched {
		return fmt.Errorf("无效的邮箱格式: %s", email)
	}
	return nil
}

// ValidatePositiveNumber 验证正数
func ValidatePositiveNumber(value *float64, fieldName string) error {
	if value == nil {
		return nil // 可选字段
	}
	if *value <= 0 {
		return fmt.Errorf("%s必须大于0", fieldName)
	}
	return nil
}

// ValidatePositiveInt 验证正整数
func ValidatePositiveInt(value *int, fieldName string) error {
	if value == nil {
		return nil // 可选字段
	}
	if *value <= 0 {
		return fmt.Errorf("%s必须大于0", fieldName)
	}
	return nil
}

// ValidateConfigType 验证配置类型
func ValidateConfigType(configType string) error {
	validTypes := []string{"string", "number", "boolean", "json", "array"}
	for _, validType := range validTypes {
		if configType == validType {
			return nil
		}
	}
	return fmt.Errorf("无效的配置类型: %s，支持的类型: %s", configType, strings.Join(validTypes, ", "))
}

// ValidateConfigValue 验证配置值
func ValidateConfigValue(configType, configValue string) error {
	if configValue == "" {
		return fmt.Errorf("配置值不能为空")
	}

	switch configType {
	case "string":
		// 字符串类型无需特殊验证
		return nil
	case "number":
		if _, err := strconv.ParseFloat(configValue, 64); err != nil {
			return fmt.Errorf("配置值必须是有效的数字: %s", configValue)
		}
		return nil
	case "boolean":
		if _, err := strconv.ParseBool(configValue); err != nil {
			return fmt.Errorf("配置值必须是有效的布尔值(true/false): %s", configValue)
		}
		return nil
	case "json":
		var temp interface{}
		if err := json.Unmarshal([]byte(configValue), &temp); err != nil {
			return fmt.Errorf("配置值必须是有效的JSON格式: %v", err)
		}
		return nil
	case "array":
		var temp []interface{}
		if err := json.Unmarshal([]byte(configValue), &temp); err != nil {
			return fmt.Errorf("配置值必须是有效的JSON数组格式: %v", err)
		}
		return nil
	default:
		return fmt.Errorf("不支持的配置类型: %s", configType)
	}
}

// ValidateServices 验证服务列表
func ValidateServices(services []string) error {
	if len(services) == 0 {
		return fmt.Errorf("支持的服务列表不能为空")
	}

	validServices := map[string]bool{
		"standard":  true, // 标准快递
		"express":   true, // 特快专递
		"economy":   true, // 经济快递
		"overnight": true, // 隔夜达
		"same_day":  true, // 当日达
		"next_day":  true, // 次日达
		"two_day":   true, // 两日达
		"ground":    true, // 陆运
		"air":       true, // 空运
		"sea":       true, // 海运
	}

	for _, service := range services {
		if !validServices[service] {
			return fmt.Errorf("不支持的服务类型: %s", service)
		}
	}
	return nil
}

// ValidatePricingConfig 验证价格配置
func ValidatePricingConfig(config map[string]interface{}) error {
	if config == nil {
		return nil // 价格配置可以为空
	}

	// 验证基础价格
	if basePrice, exists := config["base_price"]; exists {
		if price, ok := basePrice.(float64); !ok || price < 0 {
			return fmt.Errorf("基础价格必须是非负数")
		}
	}

	// 验证重量价格
	if weightPrice, exists := config["weight_price_per_kg"]; exists {
		if price, ok := weightPrice.(float64); !ok || price < 0 {
			return fmt.Errorf("重量价格必须是非负数")
		}
	}

	// 验证距离价格
	if distancePrice, exists := config["distance_price_per_km"]; exists {
		if price, ok := distancePrice.(float64); !ok || price < 0 {
			return fmt.Errorf("距离价格必须是非负数")
		}
	}

	return nil
}

// ValidatePagination 验证分页参数
func ValidatePagination(pagination *Pagination) error {
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.PageSize <= 0 {
		pagination.PageSize = 20
	}
	if pagination.PageSize > 100 {
		pagination.PageSize = 100
	}
	return nil
}

// ValidateSortOrder 验证排序参数
func ValidateSortOrder(sortOrder string) string {
	if sortOrder != "ASC" && sortOrder != "DESC" {
		return "DESC" // 默认降序
	}
	return sortOrder
}

// ValidateIPAddress 验证IP地址格式
func ValidateIPAddress(ip string) error {
	if ip == "" {
		return nil // IP地址可以为空
	}

	// 简单的IP地址格式验证
	matched, _ := regexp.MatchString(`^(\d{1,3}\.){3}\d{1,3}$`, ip)
	if !matched {
		return fmt.Errorf("无效的IP地址格式: %s", ip)
	}
	return nil
}

// ValidateUserAgent 验证User-Agent
func ValidateUserAgent(userAgent string) error {
	if userAgent == "" {
		return nil // User-Agent可以为空
	}

	if len(userAgent) > 500 {
		return fmt.Errorf("User-Agent长度不能超过500个字符")
	}
	return nil
}

// ValidateDescription 验证描述字段
func ValidateDescription(description *string) error {
	if description == nil {
		return nil // 描述可以为空
	}

	if len(*description) > 1000 {
		return fmt.Errorf("描述长度不能超过1000个字符")
	}
	return nil
}

// ValidateAPIVersion 验证API版本
func ValidateAPIVersion(version string) error {
	if version == "" {
		return fmt.Errorf("API版本不能为空")
	}

	// 验证版本格式 (如: v1, v2.0, v1.2.3)
	matched, _ := regexp.MatchString(`^v\d+(\.\d+)*$`, version)
	if !matched {
		return fmt.Errorf("无效的API版本格式: %s，应该类似于 v1, v2.0, v1.2.3", version)
	}
	return nil
}

// ValidateTimeout 验证超时时间
func ValidateTimeout(timeout int) error {
	if timeout <= 0 {
		return fmt.Errorf("超时时间必须大于0秒")
	}
	if timeout > 300 {
		return fmt.Errorf("超时时间不能超过300秒")
	}
	return nil
}

// ValidateRetries 验证重试次数
func ValidateRetries(retries int) error {
	if retries < 0 {
		return fmt.Errorf("重试次数不能为负数")
	}
	if retries > 10 {
		return fmt.Errorf("重试次数不能超过10次")
	}
	return nil
}

// ValidateRateLimit 验证限流配置
func ValidateRateLimit(rateLimit int) error {
	if rateLimit <= 0 {
		return fmt.Errorf("限流配置必须大于0")
	}
	if rateLimit > 1000 {
		return fmt.Errorf("限流配置不能超过1000次/秒")
	}
	return nil
}
