package express

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
)

// ExpressCompanyService 快递公司服务接口
type ExpressCompanyService interface {
	// 快递公司管理
	CreateCompany(ctx context.Context, req CreateCompanyRequest, operatorID string) (*ExpressCompany, error)
	GetCompanyByID(ctx context.Context, id string) (*ExpressCompany, error)
	GetCompanyByCode(ctx context.Context, code string) (*ExpressCompany, error)
	GetCompanies(ctx context.Context, filter CompanyFilter, pagination Pagination) (*CompanyListResult, error)
	UpdateCompany(ctx context.Context, id string, req UpdateCompanyRequest, operatorID string) (*ExpressCompany, error)
	DeleteCompany(ctx context.Context, id string, operatorID string) error

	// 供应商管理
	CreateProvider(ctx context.Context, req CreateProviderRequest, operatorID string) (*ExpressProvider, error)
	GetProviderByID(ctx context.Context, id string) (*ExpressProvider, error)
	GetProviderByCode(ctx context.Context, code string) (*ExpressProvider, error)
	GetProviders(ctx context.Context, filter ProviderFilter, pagination Pagination) (*ProviderListResult, error)
	UpdateProvider(ctx context.Context, id string, req UpdateProviderRequest, operatorID string) (*ExpressProvider, error)
	DeleteProvider(ctx context.Context, id string, operatorID string) error

	// 映射关系管理
	CreateMapping(ctx context.Context, req CreateMappingRequest, operatorID string) (*ExpressCompanyProviderMapping, error)
	GetMappingByID(ctx context.Context, id string) (*ExpressCompanyProviderMapping, error)
	GetMappings(ctx context.Context, filter MappingFilter, pagination Pagination) (*MappingListResult, error)
	GetMapping(ctx context.Context, companyID, providerID string) (*ExpressCompanyProviderMapping, error)
	GetMappingsByCompany(ctx context.Context, companyID string) ([]*ExpressCompanyProviderMapping, error)
	GetMappingsByProvider(ctx context.Context, providerID string) ([]*ExpressCompanyProviderMapping, error)
	UpdateMapping(ctx context.Context, id string, req UpdateMappingRequest, operatorID string) (*ExpressCompanyProviderMapping, error)
	DeleteMapping(ctx context.Context, id string, operatorID string) error

	// 服务管理
	CreateService(ctx context.Context, req CreateServiceRequest, operatorID string) (*ExpressCompanyServiceModel, error)
	GetServicesByCompany(ctx context.Context, companyID string) ([]*ExpressCompanyServiceModel, error)
	UpdateService(ctx context.Context, id string, req UpdateServiceRequest, operatorID string) (*ExpressCompanyServiceModel, error)
	DeleteService(ctx context.Context, id string, operatorID string) error

	// 配置管理
	CreateConfig(ctx context.Context, req CreateConfigRequest, operatorID string) (*ExpressCompanyConfig, error)
	GetConfigsByCompany(ctx context.Context, companyID string) ([]*ExpressCompanyConfig, error)
	GetConfig(ctx context.Context, companyID, configKey string) (*ExpressCompanyConfig, error)
	UpdateConfig(ctx context.Context, id string, req UpdateConfigRequest, operatorID string) (*ExpressCompanyConfig, error)
	DeleteConfig(ctx context.Context, id string, operatorID string) error

	// 审计日志
	GetAuditLogs(ctx context.Context, filter AuditLogFilter, pagination Pagination) (*AuditLogListResult, error)

	// 业务方法
	GetCompanyWithDetails(ctx context.Context, id string) (*ExpressCompany, error)
	GetActiveCompanies(ctx context.Context) ([]*ExpressCompany, error)
	GetActiveProviders(ctx context.Context) ([]*ExpressProvider, error)
	GetProviderCompanyCode(ctx context.Context, companyCode, providerCode string) (string, error)
	ValidateCompanyProviderMapping(ctx context.Context, companyID, providerID string) error
}

// CacheEventPublisher 缓存事件发布器接口
type CacheEventPublisher interface {
	PublishCompanyStatusChangedEvent(companyCode string, isActive bool)
	PublishMappingChangedEvent(companyCode, providerCode string)
	PublishProviderChangedEvent(providerCode string)
}

// DefaultExpressCompanyService 默认快递公司服务实现
type DefaultExpressCompanyService struct {
	repository     ExpressCompanyRepository
	cacheService   *ExpressMappingCacheService
	eventPublisher CacheEventPublisher // 🔥 事件发布器，用于通知缓存变更
	logger         *zap.Logger
}

// NewDefaultExpressCompanyService 创建默认快递公司服务
func NewDefaultExpressCompanyService(repository ExpressCompanyRepository, cacheService *ExpressMappingCacheService, logger *zap.Logger) ExpressCompanyService {
	return &DefaultExpressCompanyService{
		repository:     repository,
		cacheService:   cacheService,
		eventPublisher: nil, // 🔥 初始为nil，通过SetEventPublisher设置
		logger:         logger,
	}
}

// SetEventPublisher 设置事件发布器（避免循环依赖）
func (s *DefaultExpressCompanyService) SetEventPublisher(publisher CacheEventPublisher) {
	s.eventPublisher = publisher
}

// CreateCompany 创建快递公司
func (s *DefaultExpressCompanyService) CreateCompany(ctx context.Context, req CreateCompanyRequest, operatorID string) (*ExpressCompany, error) {
	// 验证请求参数
	if err := s.validateCreateCompanyRequest(req); err != nil {
		return nil, err
	}

	// 检查代码是否已存在
	existingCompany, err := s.repository.GetCompanyByCode(req.Code)
	if err != nil && err != ErrExpressCompanyNotFound {
		s.logger.Error("Failed to check company code existence",
			zap.Error(err),
			zap.String("code", req.Code))
		return nil, fmt.Errorf("检查快递公司代码失败: %w", err)
	}
	if existingCompany != nil {
		return nil, fmt.Errorf("快递公司代码 %s 已存在", req.Code)
	}

	// 创建快递公司
	company := NewExpressCompany(req.Code, req.Name, &operatorID)

	// 设置可选字段
	if req.EnglishName != nil {
		company.EnglishName = req.EnglishName
	}
	if req.OfficialWebsite != nil {
		company.OfficialWebsite = req.OfficialWebsite
	}
	if req.Description != nil {
		company.Description = req.Description
	}
	if req.IsActive != nil {
		company.IsActive = *req.IsActive
	}
	if req.SortOrder != nil {
		company.SortOrder = *req.SortOrder
	}
	if req.VolumeWeightRatio != nil {
		company.VolumeWeightRatio = *req.VolumeWeightRatio
	}
	if req.MaxWeightKg != nil {
		company.MaxWeightKg = req.MaxWeightKg
	}

	// 保存到数据库
	if err := s.repository.CreateCompany(company); err != nil {
		s.logger.Error("Failed to create express company",
			zap.Error(err),
			zap.String("code", req.Code),
			zap.String("name", req.Name))
		return nil, fmt.Errorf("创建快递公司失败: %w", err)
	}

	s.logger.Info("Express company created successfully",
		zap.String("company_id", company.ID),
		zap.String("code", company.Code),
		zap.String("name", company.Name),
		zap.String("operator_id", operatorID))

	return company, nil
}

// GetCompanyByID 根据ID获取快递公司
func (s *DefaultExpressCompanyService) GetCompanyByID(ctx context.Context, id string) (*ExpressCompany, error) {
	if id == "" {
		return nil, fmt.Errorf("快递公司ID不能为空")
	}

	company, err := s.repository.GetCompanyByID(id)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return nil, fmt.Errorf("快递公司不存在")
		}
		s.logger.Error("Failed to get express company by ID",
			zap.Error(err),
			zap.String("company_id", id))
		return nil, fmt.Errorf("获取快递公司失败: %w", err)
	}

	return company, nil
}

// GetCompanyByCode 根据代码获取快递公司
func (s *DefaultExpressCompanyService) GetCompanyByCode(ctx context.Context, code string) (*ExpressCompany, error) {
	if code == "" {
		return nil, fmt.Errorf("快递公司代码不能为空")
	}

	company, err := s.repository.GetCompanyByCode(code)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return nil, fmt.Errorf("快递公司不存在")
		}
		s.logger.Error("Failed to get express company by code",
			zap.Error(err),
			zap.String("company_code", code))
		return nil, fmt.Errorf("获取快递公司失败: %w", err)
	}

	return company, nil
}

// GetCompanies 获取快递公司列表
func (s *DefaultExpressCompanyService) GetCompanies(ctx context.Context, filter CompanyFilter, pagination Pagination) (*CompanyListResult, error) {
	// 验证分页参数
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.PageSize <= 0 || pagination.PageSize > 100 {
		pagination.PageSize = 20
	}

	// 验证排序参数
	if filter.SortBy != "" {
		allowedSortFields := []string{"code", "name", "sort_order", "created_at", "updated_at"}
		if !s.isValidSortField(filter.SortBy, allowedSortFields) {
			return nil, fmt.Errorf("无效的排序字段: %s", filter.SortBy)
		}
	}

	if filter.SortOrder != "" && filter.SortOrder != "ASC" && filter.SortOrder != "DESC" {
		filter.SortOrder = "DESC"
	}

	result, err := s.repository.GetCompanies(filter, pagination)
	if err != nil {
		s.logger.Error("Failed to get express companies",
			zap.Error(err),
			zap.Any("filter", filter),
			zap.Any("pagination", pagination))
		return nil, fmt.Errorf("获取快递公司列表失败: %w", err)
	}

	return result, nil
}

// UpdateCompany 更新快递公司
func (s *DefaultExpressCompanyService) UpdateCompany(ctx context.Context, id string, req UpdateCompanyRequest, operatorID string) (*ExpressCompany, error) {
	if id == "" {
		return nil, fmt.Errorf("快递公司ID不能为空")
	}

	// 获取现有快递公司
	company, err := s.repository.GetCompanyByID(id)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return nil, fmt.Errorf("快递公司不存在")
		}
		return nil, fmt.Errorf("获取快递公司失败: %w", err)
	}

	// 更新字段
	if req.Name != nil {
		if err := s.validateCompanyName(*req.Name); err != nil {
			return nil, err
		}
		company.Name = *req.Name
	}
	if req.EnglishName != nil {
		company.EnglishName = req.EnglishName
	}
	if req.OfficialWebsite != nil {
		company.OfficialWebsite = req.OfficialWebsite
	}
	if req.Description != nil {
		company.Description = req.Description
	}
	if req.IsActive != nil {
		company.IsActive = *req.IsActive
	}
	if req.SortOrder != nil {
		company.SortOrder = *req.SortOrder
	}
	if req.VolumeWeightRatio != nil {
		company.VolumeWeightRatio = *req.VolumeWeightRatio
	}
	if req.MaxWeightKg != nil {
		company.MaxWeightKg = req.MaxWeightKg
	}

	company.UpdatedBy = &operatorID

	// 保存更新
	if err := s.repository.UpdateCompany(company); err != nil {
		s.logger.Error("Failed to update express company",
			zap.Error(err),
			zap.String("company_id", id))
		return nil, fmt.Errorf("更新快递公司失败: %w", err)
	}

	// 🔥 事件驱动：如果更新了快递公司的启用状态，发布缓存失效事件
	if req.IsActive != nil && s.eventPublisher != nil {
		s.eventPublisher.PublishCompanyStatusChangedEvent(company.Code, company.IsActive)
		s.logger.Info("快递公司启用状态已更新，缓存失效事件已发布",
			zap.String("company_id", company.ID),
			zap.String("company_code", company.Code),
			zap.Bool("is_active", company.IsActive))
	}

	s.logger.Info("Express company updated successfully",
		zap.String("company_id", company.ID),
		zap.String("code", company.Code),
		zap.String("operator_id", operatorID))

	return company, nil
}

// DeleteCompany 删除快递公司
func (s *DefaultExpressCompanyService) DeleteCompany(ctx context.Context, id string, operatorID string) error {
	if id == "" {
		return fmt.Errorf("快递公司ID不能为空")
	}

	// 检查快递公司是否存在
	company, err := s.repository.GetCompanyByID(id)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return fmt.Errorf("快递公司不存在")
		}
		return fmt.Errorf("获取快递公司失败: %w", err)
	}

	// 检查是否有关联的映射关系
	mappings, err := s.repository.GetMappingsByCompany(id)
	if err != nil {
		s.logger.Error("Failed to check company mappings",
			zap.Error(err),
			zap.String("company_id", id))
		return fmt.Errorf("检查快递公司映射关系失败: %w", err)
	}

	if len(mappings) > 0 {
		return fmt.Errorf("无法删除快递公司，存在 %d 个关联的供应商映射关系", len(mappings))
	}

	// 删除快递公司
	if err := s.repository.DeleteCompany(id); err != nil {
		s.logger.Error("Failed to delete express company",
			zap.Error(err),
			zap.String("company_id", id))
		return fmt.Errorf("删除快递公司失败: %w", err)
	}

	s.logger.Info("Express company deleted successfully",
		zap.String("company_id", id),
		zap.String("code", company.Code),
		zap.String("operator_id", operatorID))

	return nil
}

// 验证创建快递公司请求
func (s *DefaultExpressCompanyService) validateCreateCompanyRequest(req CreateCompanyRequest) error {
	if err := s.validateCompanyCode(req.Code); err != nil {
		return err
	}

	if err := s.validateCompanyName(req.Name); err != nil {
		return err
	}

	return nil
}

// 验证快递公司代码
func (s *DefaultExpressCompanyService) validateCompanyCode(code string) error {
	if code == "" {
		return fmt.Errorf("快递公司代码不能为空")
	}

	if len(code) < 2 || len(code) > 20 {
		return fmt.Errorf("快递公司代码长度必须在2-20个字符之间")
	}

	// 只允许字母、数字和下划线
	for _, char := range code {
		if !((char >= 'A' && char <= 'Z') || (char >= 'a' && char <= 'z') ||
			(char >= '0' && char <= '9') || char == '_') {
			return fmt.Errorf("快递公司代码只能包含字母、数字和下划线")
		}
	}

	return nil
}

// 验证快递公司名称
func (s *DefaultExpressCompanyService) validateCompanyName(name string) error {
	if name == "" {
		return fmt.Errorf("快递公司名称不能为空")
	}

	if len(strings.TrimSpace(name)) < 2 {
		return fmt.Errorf("快递公司名称长度不能少于2个字符")
	}

	if len(name) > 100 {
		return fmt.Errorf("快递公司名称长度不能超过100个字符")
	}

	return nil
}

// 验证排序字段
func (s *DefaultExpressCompanyService) isValidSortField(field string, allowedFields []string) bool {
	for _, allowed := range allowedFields {
		if field == allowed {
			return true
		}
	}
	return false
}

// GetActiveCompanies 获取启用的快递公司列表
func (s *DefaultExpressCompanyService) GetActiveCompanies(ctx context.Context) ([]*ExpressCompany, error) {
	filter := CompanyFilter{
		IsActive:  &[]bool{true}[0],
		SortBy:    "sort_order",
		SortOrder: "DESC",
	}

	pagination := Pagination{
		Page:     1,
		PageSize: 100,
	}

	result, err := s.repository.GetCompanies(filter, pagination)
	if err != nil {
		return nil, err
	}

	return result.Companies, nil
}

// GetActiveProviders 获取启用的供应商列表
func (s *DefaultExpressCompanyService) GetActiveProviders(ctx context.Context) ([]*ExpressProvider, error) {
	filter := ProviderFilter{
		IsActive:  &[]bool{true}[0],
		SortBy:    "priority",
		SortOrder: "DESC",
	}

	pagination := Pagination{
		Page:     1,
		PageSize: 100,
	}

	result, err := s.repository.GetProviders(filter, pagination)
	if err != nil {
		return nil, err
	}

	return result.Providers, nil
}

// GetProviderCompanyCode 获取供应商特定的快递公司代码
func (s *DefaultExpressCompanyService) GetProviderCompanyCode(ctx context.Context, companyCode, providerCode string) (string, error) {
	// 根据快递公司代码获取快递公司
	company, err := s.repository.GetCompanyByCode(companyCode)
	if err != nil {
		return "", fmt.Errorf("快递公司不存在: %w", err)
	}

	// 根据供应商代码获取供应商
	provider, err := s.repository.GetProviderByCode(providerCode)
	if err != nil {
		return "", fmt.Errorf("供应商不存在: %w", err)
	}

	// 获取映射关系
	mapping, err := s.repository.GetMapping(company.ID, provider.ID)
	if err != nil {
		return "", fmt.Errorf("映射关系不存在: %w", err)
	}

	if !mapping.IsSupported {
		return "", fmt.Errorf("该供应商不支持此快递公司")
	}

	return mapping.ProviderCompanyCode, nil
}

// ValidateCompanyProviderMapping 验证快递公司与供应商映射关系
func (s *DefaultExpressCompanyService) ValidateCompanyProviderMapping(ctx context.Context, companyID, providerID string) error {
	mapping, err := s.repository.GetMapping(companyID, providerID)
	if err != nil {
		return fmt.Errorf("映射关系不存在: %w", err)
	}

	if !mapping.IsSupported {
		return fmt.Errorf("该供应商不支持此快递公司")
	}

	return nil
}

// GetCompanyWithDetails 获取快递公司详细信息（包含关联数据）
func (s *DefaultExpressCompanyService) GetCompanyWithDetails(ctx context.Context, id string) (*ExpressCompany, error) {
	// 获取基础信息
	company, err := s.repository.GetCompanyByID(id)
	if err != nil {
		return nil, err
	}

	// 获取映射关系
	mappings, err := s.repository.GetMappingsByCompany(id)
	if err != nil {
		s.logger.Warn("Failed to get company mappings",
			zap.String("company_id", id),
			zap.Error(err))
	} else {
		// 转换指针切片为值切片
		mappingValues := make([]ExpressCompanyProviderMapping, len(mappings))
		for i, mapping := range mappings {
			mappingValues[i] = *mapping
		}
		company.Mappings = mappingValues
	}

	// 获取服务列表
	services, err := s.repository.GetServicesByCompany(id)
	if err != nil {
		s.logger.Warn("Failed to get company services",
			zap.String("company_id", id),
			zap.Error(err))
	} else {
		// 转换指针切片为值切片
		serviceValues := make([]ExpressCompanyServiceModel, len(services))
		for i, service := range services {
			serviceValues[i] = *service
		}
		company.Services = serviceValues
	}

	// 获取配置列表
	configs, err := s.repository.GetConfigsByCompany(id)
	if err != nil {
		s.logger.Warn("Failed to get company configs",
			zap.String("company_id", id),
			zap.Error(err))
	} else {
		configMap := make(map[string]ExpressCompanyConfig)
		for _, config := range configs {
			configMap[config.ConfigKey] = *config
		}
		company.Configs = configMap
	}

	return company, nil
}
