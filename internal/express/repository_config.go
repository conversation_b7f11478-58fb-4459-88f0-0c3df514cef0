package express

import (
	"database/sql"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// CreateConfig 创建配置
func (r *PostgresExpressCompanyRepository) CreateConfig(config *ExpressCompanyConfig) error {
	query := `
		INSERT INTO express_company_configs (
			id, company_id, config_key, config_value, config_type,
			description, is_active,
			created_at, updated_at, created_by, updated_by
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
	`

	_, err := r.db.Exec(query,
		config.ID, config.CompanyID, config.ConfigKey, config.ConfigValue,
		config.ConfigType, config.Description, config.IsActive,
		config.CreatedAt, config.UpdatedAt, config.CreatedBy, config.UpdatedBy,
	)

	if err != nil {
		r.logger.Error("Failed to create express company config",
			zap.Error(err),
			zap.String("company_id", config.CompanyID),
			zap.String("config_key", config.ConfigKey))
		return fmt.Errorf("创建快递公司配置失败: %w", err)
	}

	return nil
}

// GetConfigsByCompany 根据快递公司ID获取配置列表
func (r *PostgresExpressCompanyRepository) GetConfigsByCompany(companyID string) ([]*ExpressCompanyConfig, error) {
	query := `
		SELECT id, company_id, config_key, config_value, config_type,
			   description, is_active,
			   created_at, updated_at, created_by, updated_by
		FROM express_company_configs
		WHERE company_id = $1
		ORDER BY config_key ASC
	`

	rows, err := r.db.Query(query, companyID)
	if err != nil {
		r.logger.Error("Failed to query express company configs",
			zap.Error(err),
			zap.String("company_id", companyID))
		return nil, fmt.Errorf("查询快递公司配置失败: %w", err)
	}
	defer rows.Close()

	var configs []*ExpressCompanyConfig
	for rows.Next() {
		var config ExpressCompanyConfig
		err := rows.Scan(
			&config.ID, &config.CompanyID, &config.ConfigKey, &config.ConfigValue,
			&config.ConfigType, &config.Description, &config.IsActive,
			&config.CreatedAt, &config.UpdatedAt, &config.CreatedBy, &config.UpdatedBy,
		)
		if err != nil {
			r.logger.Error("Failed to scan express company config row", zap.Error(err))
			return nil, fmt.Errorf("扫描快递公司配置数据失败: %w", err)
		}
		configs = append(configs, &config)
	}

	return configs, nil
}

// GetConfig 获取特定配置
func (r *PostgresExpressCompanyRepository) GetConfig(companyID, configKey string) (*ExpressCompanyConfig, error) {
	query := `
		SELECT id, company_id, config_key, config_value, config_type,
			   description, is_active,
			   created_at, updated_at, created_by, updated_by
		FROM express_company_configs
		WHERE company_id = $1 AND config_key = $2
	`

	var config ExpressCompanyConfig
	err := r.db.QueryRow(query, companyID, configKey).Scan(
		&config.ID, &config.CompanyID, &config.ConfigKey, &config.ConfigValue,
		&config.ConfigType, &config.Description, &config.IsActive,
		&config.CreatedAt, &config.UpdatedAt, &config.CreatedBy, &config.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrExpressConfigNotFound
		}
		r.logger.Error("Failed to get express company config",
			zap.Error(err),
			zap.String("company_id", companyID),
			zap.String("config_key", configKey))
		return nil, fmt.Errorf("查询快递公司配置失败: %w", err)
	}

	return &config, nil
}

// UpdateConfig 更新配置
func (r *PostgresExpressCompanyRepository) UpdateConfig(config *ExpressCompanyConfig) error {
	query := `
		UPDATE express_company_configs SET
			config_value = $2, config_type = $3, description = $4,
			is_active = $5, updated_at = $6, updated_by = $7
		WHERE id = $1
	`

	config.UpdatedAt = time.Now()

	result, err := r.db.Exec(query,
		config.ID, config.ConfigValue, config.ConfigType, config.Description,
		config.IsActive, config.UpdatedAt, config.UpdatedBy,
	)

	if err != nil {
		r.logger.Error("Failed to update express company config",
			zap.Error(err),
			zap.String("config_id", config.ID))
		return fmt.Errorf("更新快递公司配置失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrExpressConfigNotFound
	}

	return nil
}

// DeleteConfig 删除配置
func (r *PostgresExpressCompanyRepository) DeleteConfig(id string) error {
	query := "DELETE FROM express_company_configs WHERE id = $1"

	result, err := r.db.Exec(query, id)
	if err != nil {
		r.logger.Error("Failed to delete express company config",
			zap.Error(err),
			zap.String("config_id", id))
		return fmt.Errorf("删除快递公司配置失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrExpressConfigNotFound
	}

	return nil
}

// GetConfigByID 根据ID获取配置（内部使用）
func (r *PostgresExpressCompanyRepository) GetConfigByID(id string) (*ExpressCompanyConfig, error) {
	query := `
		SELECT id, company_id, config_key, config_value, config_type,
			   description, is_active,
			   created_at, updated_at, created_by, updated_by
		FROM express_company_configs
		WHERE id = $1
	`

	var config ExpressCompanyConfig
	err := r.db.QueryRow(query, id).Scan(
		&config.ID, &config.CompanyID, &config.ConfigKey, &config.ConfigValue,
		&config.ConfigType, &config.Description, &config.IsActive,
		&config.CreatedAt, &config.UpdatedAt, &config.CreatedBy, &config.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrExpressConfigNotFound
		}
		r.logger.Error("Failed to get express company config by ID",
			zap.Error(err),
			zap.String("config_id", id))
		return nil, fmt.Errorf("查询快递公司配置失败: %w", err)
	}

	return &config, nil
}
