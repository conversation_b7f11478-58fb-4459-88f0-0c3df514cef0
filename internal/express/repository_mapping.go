package express

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
)

// parseSupportedServices 解析支持的服务列表，兼容对象和数组格式
func (r *PostgresExpressCompanyRepository) parseSupportedServices(supportedServicesJSON []byte) []string {
	if len(supportedServicesJSON) == 0 {
		return []string{}
	}

	// 尝试解析为字符串数组
	var services []string
	if err := json.Unmarshal(supportedServicesJSON, &services); err == nil {
		return services
	}

	// 如果解析失败，尝试解析为对象格式并转换
	var servicesObj map[string]interface{}
	if err := json.Unmarshal(supportedServicesJSON, &servicesObj); err == nil {
		// 从对象中提取服务列表
		var extractedServices []string
		for key := range servicesObj {
			extractedServices = append(extractedServices, key)
		}
		r.logger.Info("成功从对象格式转换支持的服务列表",
			zap.Strings("services", extractedServices),
			zap.String("original_json", string(supportedServicesJSON)))
		return extractedServices
	}

	// 都失败了，记录警告并返回空数组
	r.logger.Warn("Failed to parse supported services",
		zap.String("json_data", string(supportedServicesJSON)))
	return []string{}
}

// CreateMapping 创建映射关系
func (r *PostgresExpressCompanyRepository) CreateMapping(mapping *ExpressCompanyProviderMapping) error {
	// 序列化JSON字段
	supportedServicesJSON, err := json.Marshal(mapping.SupportedServices)
	if err != nil {
		return fmt.Errorf("序列化支持的服务失败: %w", err)
	}

	pricingConfigJSON, err := json.Marshal(mapping.PricingConfig)
	if err != nil {
		return fmt.Errorf("序列化价格配置失败: %w", err)
	}

	query := `
		INSERT INTO express_company_provider_mappings (
			id, company_id, provider_id, provider_company_code, is_supported,
			is_preferred, weight_limit_kg, size_limit_cm_length, size_limit_cm_width,
			size_limit_cm_height, supported_services, pricing_config,
			created_at, updated_at, created_by, updated_by
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
	`

	_, err = r.db.Exec(query,
		mapping.ID, mapping.CompanyID, mapping.ProviderID, mapping.ProviderCompanyCode,
		mapping.IsSupported, mapping.IsPreferred, mapping.WeightLimitKg,
		mapping.SizeLimitCmLength, mapping.SizeLimitCmWidth, mapping.SizeLimitCmHeight,
		supportedServicesJSON, pricingConfigJSON,
		mapping.CreatedAt, mapping.UpdatedAt, mapping.CreatedBy, mapping.UpdatedBy,
	)

	if err != nil {
		r.logger.Error("Failed to create express company provider mapping",
			zap.Error(err),
			zap.String("company_id", mapping.CompanyID),
			zap.String("provider_id", mapping.ProviderID))
		return fmt.Errorf("创建映射关系失败: %w", err)
	}

	return nil
}

// GetMappingByID 根据ID获取映射关系
func (r *PostgresExpressCompanyRepository) GetMappingByID(id string) (*ExpressCompanyProviderMapping, error) {
	query := `
		SELECT id, company_id, provider_id, provider_company_code, is_supported,
			   is_preferred, weight_limit_kg, size_limit_cm_length, size_limit_cm_width,
			   size_limit_cm_height, supported_services, pricing_config,
			   created_at, updated_at, created_by, updated_by
		FROM express_company_provider_mappings 
		WHERE id = $1
	`

	var mapping ExpressCompanyProviderMapping
	var supportedServicesJSON, pricingConfigJSON []byte

	err := r.db.QueryRow(query, id).Scan(
		&mapping.ID, &mapping.CompanyID, &mapping.ProviderID, &mapping.ProviderCompanyCode,
		&mapping.IsSupported, &mapping.IsPreferred, &mapping.WeightLimitKg,
		&mapping.SizeLimitCmLength, &mapping.SizeLimitCmWidth, &mapping.SizeLimitCmHeight,
		&supportedServicesJSON, &pricingConfigJSON,
		&mapping.CreatedAt, &mapping.UpdatedAt, &mapping.CreatedBy, &mapping.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrExpressMappingNotFound
		}
		r.logger.Error("Failed to get express company provider mapping by ID",
			zap.Error(err),
			zap.String("mapping_id", id))
		return nil, fmt.Errorf("查询映射关系失败: %w", err)
	}

	// 反序列化JSON字段
	mapping.SupportedServices = r.parseSupportedServices(supportedServicesJSON)

	if pricingConfigJSON != nil {
		err = json.Unmarshal(pricingConfigJSON, &mapping.PricingConfig)
		if err != nil {
			r.logger.Warn("Failed to unmarshal pricing config", zap.Error(err))
		}
	}

	return &mapping, nil
}

// GetMapping 获取特定的映射关系
func (r *PostgresExpressCompanyRepository) GetMapping(companyID, providerID string) (*ExpressCompanyProviderMapping, error) {
	query := `
		SELECT id, company_id, provider_id, provider_company_code, is_supported,
			   is_preferred, weight_limit_kg, size_limit_cm_length, size_limit_cm_width,
			   size_limit_cm_height, supported_services, pricing_config,
			   COALESCE(product_type_mappings, '{}') as product_type_mappings,
			   COALESCE(pay_method_mappings, '{}') as pay_method_mappings,
			   COALESCE(status_code_mappings, '{}') as status_code_mappings,
			   created_at, updated_at, created_by, updated_by
		FROM express_company_provider_mappings
		WHERE company_id = $1 AND provider_id = $2
	`

	var mapping ExpressCompanyProviderMapping
	var supportedServicesJSON, pricingConfigJSON []byte

	err := r.db.QueryRow(query, companyID, providerID).Scan(
		&mapping.ID, &mapping.CompanyID, &mapping.ProviderID, &mapping.ProviderCompanyCode,
		&mapping.IsSupported, &mapping.IsPreferred, &mapping.WeightLimitKg,
		&mapping.SizeLimitCmLength, &mapping.SizeLimitCmWidth, &mapping.SizeLimitCmHeight,
		&supportedServicesJSON, &pricingConfigJSON,
		&mapping.ProductTypeMappings, &mapping.PayMethodMappings, &mapping.StatusCodeMappings,
		&mapping.CreatedAt, &mapping.UpdatedAt, &mapping.CreatedBy, &mapping.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrExpressMappingNotFound
		}
		r.logger.Error("Failed to get express company provider mapping",
			zap.Error(err),
			zap.String("company_id", companyID),
			zap.String("provider_id", providerID))
		return nil, fmt.Errorf("查询映射关系失败: %w", err)
	}

	// 反序列化JSON字段
	mapping.SupportedServices = r.parseSupportedServices(supportedServicesJSON)

	if pricingConfigJSON != nil {
		err = json.Unmarshal(pricingConfigJSON, &mapping.PricingConfig)
		if err != nil {
			r.logger.Warn("Failed to unmarshal pricing config", zap.Error(err))
		}
	}

	return &mapping, nil
}

// GetMappingsByCompany 根据快递公司ID获取映射关系
func (r *PostgresExpressCompanyRepository) GetMappingsByCompany(companyID string) ([]*ExpressCompanyProviderMapping, error) {
	query := `
		SELECT m.id, m.company_id, m.provider_id, m.provider_company_code, m.is_supported,
			   m.is_preferred, m.weight_limit_kg, m.size_limit_cm_length, m.size_limit_cm_width,
			   m.size_limit_cm_height, m.supported_services, m.pricing_config,
			   m.created_at, m.updated_at, m.created_by, m.updated_by,
			   p.code as provider_code, p.name as provider_name
		FROM express_company_provider_mappings m
		LEFT JOIN express_providers p ON m.provider_id = p.id
		WHERE m.company_id = $1
		ORDER BY p.priority DESC, m.created_at DESC
	`

	rows, err := r.db.Query(query, companyID)
	if err != nil {
		r.logger.Error("Failed to query mappings by company",
			zap.Error(err),
			zap.String("company_id", companyID))
		return nil, fmt.Errorf("查询快递公司映射关系失败: %w", err)
	}
	defer rows.Close()

	var mappings []*ExpressCompanyProviderMapping
	for rows.Next() {
		var mapping ExpressCompanyProviderMapping
		var supportedServicesJSON, pricingConfigJSON []byte
		var providerCode, providerName string

		err := rows.Scan(
			&mapping.ID, &mapping.CompanyID, &mapping.ProviderID, &mapping.ProviderCompanyCode,
			&mapping.IsSupported, &mapping.IsPreferred, &mapping.WeightLimitKg,
			&mapping.SizeLimitCmLength, &mapping.SizeLimitCmWidth, &mapping.SizeLimitCmHeight,
			&supportedServicesJSON, &pricingConfigJSON,
			&mapping.CreatedAt, &mapping.UpdatedAt, &mapping.CreatedBy, &mapping.UpdatedBy,
			&providerCode, &providerName,
		)
		if err != nil {
			r.logger.Error("Failed to scan mapping row", zap.Error(err))
			return nil, fmt.Errorf("扫描映射关系数据失败: %w", err)
		}

		// 反序列化JSON字段
		mapping.SupportedServices = r.parseSupportedServices(supportedServicesJSON)

		if pricingConfigJSON != nil {
			err = json.Unmarshal(pricingConfigJSON, &mapping.PricingConfig)
			if err != nil {
				r.logger.Warn("Failed to unmarshal pricing config", zap.Error(err))
			}
		}

		// 设置关联的供应商信息
		mapping.Provider = &ExpressProvider{
			ID:   mapping.ProviderID,
			Code: providerCode,
			Name: providerName,
		}

		mappings = append(mappings, &mapping)
	}

	return mappings, nil
}

// GetMappingsByProvider 根据供应商ID获取映射关系
func (r *PostgresExpressCompanyRepository) GetMappingsByProvider(providerID string) ([]*ExpressCompanyProviderMapping, error) {
	query := `
		SELECT m.id, m.company_id, m.provider_id, m.provider_company_code, m.is_supported,
			   m.is_preferred, m.weight_limit_kg, m.size_limit_cm_length, m.size_limit_cm_width,
			   m.size_limit_cm_height, m.supported_services, m.pricing_config,
			   m.created_at, m.updated_at, m.created_by, m.updated_by,
			   c.code as company_code, c.name as company_name, c.is_active as company_is_active
		FROM express_company_provider_mappings m
		LEFT JOIN express_companies c ON m.company_id = c.id
		WHERE m.provider_id = $1
		ORDER BY c.sort_order DESC, m.created_at DESC
	`

	rows, err := r.db.Query(query, providerID)
	if err != nil {
		r.logger.Error("Failed to query mappings by provider",
			zap.Error(err),
			zap.String("provider_id", providerID))
		return nil, fmt.Errorf("查询供应商映射关系失败: %w", err)
	}
	defer rows.Close()

	var mappings []*ExpressCompanyProviderMapping
	for rows.Next() {
		var mapping ExpressCompanyProviderMapping
		var supportedServicesJSON, pricingConfigJSON []byte
		var companyCode, companyName string
		var companyIsActive bool

		err := rows.Scan(
			&mapping.ID, &mapping.CompanyID, &mapping.ProviderID, &mapping.ProviderCompanyCode,
			&mapping.IsSupported, &mapping.IsPreferred, &mapping.WeightLimitKg,
			&mapping.SizeLimitCmLength, &mapping.SizeLimitCmWidth, &mapping.SizeLimitCmHeight,
			&supportedServicesJSON, &pricingConfigJSON,
			&mapping.CreatedAt, &mapping.UpdatedAt, &mapping.CreatedBy, &mapping.UpdatedBy,
			&companyCode, &companyName, &companyIsActive,
		)
		if err != nil {
			r.logger.Error("Failed to scan mapping row", zap.Error(err))
			return nil, fmt.Errorf("扫描映射关系数据失败: %w", err)
		}

		// 反序列化JSON字段
		mapping.SupportedServices = r.parseSupportedServices(supportedServicesJSON)

		if pricingConfigJSON != nil {
			err = json.Unmarshal(pricingConfigJSON, &mapping.PricingConfig)
			if err != nil {
				r.logger.Warn("Failed to unmarshal pricing config", zap.Error(err))
			}
		}

		// 设置关联的快递公司信息
		mapping.Company = &ExpressCompany{
			ID:       mapping.CompanyID,
			Code:     companyCode,
			Name:     companyName,
			IsActive: companyIsActive,
		}

		mappings = append(mappings, &mapping)
	}

	return mappings, nil
}

// UpdateMapping 更新映射关系
func (r *PostgresExpressCompanyRepository) UpdateMapping(mapping *ExpressCompanyProviderMapping) error {
	// 序列化JSON字段
	supportedServicesJSON, err := json.Marshal(mapping.SupportedServices)
	if err != nil {
		return fmt.Errorf("序列化支持的服务失败: %w", err)
	}

	pricingConfigJSON, err := json.Marshal(mapping.PricingConfig)
	if err != nil {
		return fmt.Errorf("序列化价格配置失败: %w", err)
	}

	query := `
		UPDATE express_company_provider_mappings SET
			provider_company_code = $2, is_supported = $3, is_preferred = $4,
			weight_limit_kg = $5, size_limit_cm_length = $6, size_limit_cm_width = $7,
			size_limit_cm_height = $8, supported_services = $9, pricing_config = $10,
			updated_at = $11, updated_by = $12
		WHERE id = $1
	`

	mapping.UpdatedAt = time.Now()

	result, err := r.db.Exec(query,
		mapping.ID, mapping.ProviderCompanyCode, mapping.IsSupported, mapping.IsPreferred,
		mapping.WeightLimitKg, mapping.SizeLimitCmLength, mapping.SizeLimitCmWidth,
		mapping.SizeLimitCmHeight, supportedServicesJSON, pricingConfigJSON,
		mapping.UpdatedAt, mapping.UpdatedBy,
	)

	if err != nil {
		r.logger.Error("Failed to update express company provider mapping",
			zap.Error(err),
			zap.String("mapping_id", mapping.ID))
		return fmt.Errorf("更新映射关系失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrExpressMappingNotFound
	}

	return nil
}

// DeleteMapping 删除映射关系
func (r *PostgresExpressCompanyRepository) DeleteMapping(id string) error {
	query := "DELETE FROM express_company_provider_mappings WHERE id = $1"

	result, err := r.db.Exec(query, id)
	if err != nil {
		r.logger.Error("Failed to delete express company provider mapping",
			zap.Error(err),
			zap.String("mapping_id", id))
		return fmt.Errorf("删除映射关系失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrExpressMappingNotFound
	}

	return nil
}

// GetMappings 获取映射关系列表
func (r *PostgresExpressCompanyRepository) GetMappings(filter MappingFilter, pagination Pagination) (*MappingListResult, error) {
	// 构建查询条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	if filter.CompanyID != "" {
		conditions = append(conditions, fmt.Sprintf("m.company_id = $%d", argIndex))
		args = append(args, filter.CompanyID)
		argIndex++
	}

	if filter.ProviderID != "" {
		conditions = append(conditions, fmt.Sprintf("m.provider_id = $%d", argIndex))
		args = append(args, filter.ProviderID)
		argIndex++
	}

	if filter.IsSupported != nil {
		conditions = append(conditions, fmt.Sprintf("m.is_supported = $%d", argIndex))
		args = append(args, *filter.IsSupported)
		argIndex++
	}

	if filter.IsPreferred != nil {
		conditions = append(conditions, fmt.Sprintf("m.is_preferred = $%d", argIndex))
		args = append(args, *filter.IsPreferred)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 查询总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM express_company_provider_mappings m
		LEFT JOIN express_companies c ON m.company_id = c.id
		LEFT JOIN express_providers p ON m.provider_id = p.id
		%s
	`, whereClause)

	var total int64
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count express company provider mappings", zap.Error(err))
		return nil, fmt.Errorf("查询映射关系总数失败: %w", err)
	}

	// 查询数据
	orderBy := "ORDER BY p.priority DESC, m.created_at DESC"
	if filter.SortBy != "" {
		orderBy = fmt.Sprintf("ORDER BY m.%s %s", filter.SortBy, filter.SortOrder)
	}

	dataQuery := fmt.Sprintf(`
		SELECT m.id, m.company_id, m.provider_id, m.provider_company_code, m.is_supported,
			   m.is_preferred, m.weight_limit_kg, m.size_limit_cm_length, m.size_limit_cm_width,
			   m.size_limit_cm_height, m.supported_services, m.pricing_config,
			   m.created_at, m.updated_at, m.created_by, m.updated_by,
			   c.code as company_code, c.name as company_name, c.is_active as company_is_active,
			   p.code as provider_code, p.name as provider_name
		FROM express_company_provider_mappings m
		LEFT JOIN express_companies c ON m.company_id = c.id
		LEFT JOIN express_providers p ON m.provider_id = p.id
		%s %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, pagination.PageSize, pagination.Offset())
	rows, err := r.db.Query(dataQuery, args...)
	if err != nil {
		r.logger.Error("Failed to query express company provider mappings", zap.Error(err))
		return nil, fmt.Errorf("查询映射关系失败: %w", err)
	}
	defer rows.Close()

	var mappings []*ExpressCompanyProviderMapping
	for rows.Next() {
		var mapping ExpressCompanyProviderMapping
		var supportedServicesJSON, pricingConfigJSON []byte
		var companyCode, companyName, providerCode, providerName sql.NullString
		var companyIsActive sql.NullBool

		err := rows.Scan(
			&mapping.ID, &mapping.CompanyID, &mapping.ProviderID, &mapping.ProviderCompanyCode,
			&mapping.IsSupported, &mapping.IsPreferred, &mapping.WeightLimitKg,
			&mapping.SizeLimitCmLength, &mapping.SizeLimitCmWidth, &mapping.SizeLimitCmHeight,
			&supportedServicesJSON, &pricingConfigJSON,
			&mapping.CreatedAt, &mapping.UpdatedAt, &mapping.CreatedBy, &mapping.UpdatedBy,
			&companyCode, &companyName, &companyIsActive, &providerCode, &providerName,
		)
		if err != nil {
			r.logger.Error("Failed to scan express company provider mapping row", zap.Error(err))
			return nil, fmt.Errorf("扫描映射关系数据失败: %w", err)
		}

		// 反序列化JSON字段
		mapping.SupportedServices = r.parseSupportedServices(supportedServicesJSON)

		if len(pricingConfigJSON) > 0 {
			if err := json.Unmarshal(pricingConfigJSON, &mapping.PricingConfig); err != nil {
				r.logger.Warn("Failed to unmarshal pricing config", zap.Error(err))
			}
		}

		// 设置关联信息
		if companyCode.Valid && companyName.Valid {
			mapping.Company = &ExpressCompany{
				ID:       mapping.CompanyID,
				Code:     companyCode.String,
				Name:     companyName.String,
				IsActive: companyIsActive.Bool, // 使用从数据库获取的is_active值
			}
		}
		if providerCode.Valid && providerName.Valid {
			mapping.Provider = &ExpressProvider{
				ID:   mapping.ProviderID,
				Code: providerCode.String,
				Name: providerName.String,
			}
		}

		mappings = append(mappings, &mapping)
	}

	return &MappingListResult{
		Mappings: mappings,
		Total:    total,
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
	}, nil
}
