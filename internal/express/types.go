package express

import (
	"fmt"
)

// Pagination 分页参数
type Pagination struct {
	Page     int `json:"page" form:"page" binding:"min=1"`
	PageSize int `json:"page_size" form:"page_size" binding:"min=1,max=100"`
}

// Offset 计算偏移量
func (p Pagination) Offset() int {
	return (p.Page - 1) * p.PageSize
}

// CompanyFilter 快递公司查询过滤器
type CompanyFilter struct {
	Keyword   string `json:"keyword" form:"keyword"`
	IsActive  *bool  `json:"is_active" form:"is_active"`
	SortBy    string `json:"sort_by" form:"sort_by"`
	SortOrder string `json:"sort_order" form:"sort_order"`
}

// ProviderFilter 供应商查询过滤器
type ProviderFilter struct {
	Keyword   string `json:"keyword" form:"keyword"`
	IsActive  *bool  `json:"is_active" form:"is_active"`
	SortBy    string `json:"sort_by" form:"sort_by"`
	SortOrder string `json:"sort_order" form:"sort_order"`
}

// MappingFilter 映射关系查询过滤器
type MappingFilter struct {
	CompanyID   string `json:"company_id" form:"company_id"`
	ProviderID  string `json:"provider_id" form:"provider_id"`
	IsSupported *bool  `json:"is_supported" form:"is_supported"`
	IsPreferred *bool  `json:"is_preferred" form:"is_preferred"`
	SortBy      string `json:"sort_by" form:"sort_by"`
	SortOrder   string `json:"sort_order" form:"sort_order"`
}

// ServiceFilter 服务查询过滤器
type ServiceFilter struct {
	CompanyID string `json:"company_id" form:"company_id"`
	IsActive  *bool  `json:"is_active" form:"is_active"`
	SortBy    string `json:"sort_by" form:"sort_by"`
	SortOrder string `json:"sort_order" form:"sort_order"`
}

// ConfigFilter 配置查询过滤器
type ConfigFilter struct {
	CompanyID  string `json:"company_id" form:"company_id"`
	ConfigKey  string `json:"config_key" form:"config_key"`
	ConfigType string `json:"config_type" form:"config_type"`
	IsActive   *bool  `json:"is_active" form:"is_active"`
	SortBy     string `json:"sort_by" form:"sort_by"`
	SortOrder  string `json:"sort_order" form:"sort_order"`
}

// AuditLogFilter 审计日志查询过滤器
type AuditLogFilter struct {
	OperatorID      string `json:"operator_id" form:"operator_id"`
	TargetCompanyID string `json:"target_company_id" form:"target_company_id"`
	Action          string `json:"action" form:"action"`
	Operation       string `json:"operation" form:"operation"`
	Result          string `json:"result" form:"result"`
	StartTime       string `json:"start_time" form:"start_time"`
	EndTime         string `json:"end_time" form:"end_time"`
	SortBy          string `json:"sort_by" form:"sort_by"`
	SortOrder       string `json:"sort_order" form:"sort_order"`
}

// CompanyListResult 快递公司列表结果
type CompanyListResult struct {
	Companies []*ExpressCompany `json:"companies"`
	Total     int64             `json:"total"`
	Page      int               `json:"page"`
	PageSize  int               `json:"page_size"`
}

// ProviderListResult 供应商列表结果
type ProviderListResult struct {
	Providers []*ExpressProvider `json:"providers"`
	Total     int64              `json:"total"`
	Page      int                `json:"page"`
	PageSize  int                `json:"page_size"`
}

// MappingListResult 映射关系列表结果
type MappingListResult struct {
	Mappings []*ExpressCompanyProviderMapping `json:"mappings"`
	Total    int64                            `json:"total"`
	Page     int                              `json:"page"`
	PageSize int                              `json:"page_size"`
}

// ServiceListResult 服务列表结果
type ServiceListResult struct {
	Services []*ExpressCompanyServiceModel `json:"services"`
	Total    int64                         `json:"total"`
	Page     int                           `json:"page"`
	PageSize int                           `json:"page_size"`
}

// ConfigListResult 配置列表结果
type ConfigListResult struct {
	Configs  []*ExpressCompanyConfig `json:"configs"`
	Total    int64                   `json:"total"`
	Page     int                     `json:"page"`
	PageSize int                     `json:"page_size"`
}

// AuditLogListResult 审计日志列表结果
type AuditLogListResult struct {
	Logs     []*ExpressCompanyAuditLog `json:"logs"`
	Total    int64                     `json:"total"`
	Page     int                       `json:"page"`
	PageSize int                       `json:"page_size"`
}

// CreateCompanyRequest 创建快递公司请求
type CreateCompanyRequest struct {
	Code              string   `json:"code" binding:"required,min=2,max=20"`
	Name              string   `json:"name" binding:"required,min=2,max=100"`
	EnglishName       *string  `json:"english_name,omitempty"`
	OfficialWebsite   *string  `json:"official_website,omitempty"`
	Description       *string  `json:"description,omitempty"`
	IsActive          *bool    `json:"is_active,omitempty"`
	SortOrder         *int     `json:"sort_order,omitempty"`
	VolumeWeightRatio *int     `json:"volume_weight_ratio,omitempty"`
	MaxWeightKg       *float64 `json:"max_weight_kg,omitempty"`
}

// UpdateCompanyRequest 更新快递公司请求
type UpdateCompanyRequest struct {
	Name              *string  `json:"name,omitempty"`
	EnglishName       *string  `json:"english_name,omitempty"`
	OfficialWebsite   *string  `json:"official_website,omitempty"`
	Description       *string  `json:"description,omitempty"`
	IsActive          *bool    `json:"is_active,omitempty"`
	SortOrder         *int     `json:"sort_order,omitempty"`
	VolumeWeightRatio *int     `json:"volume_weight_ratio,omitempty"`
	MaxWeightKg       *float64 `json:"max_weight_kg,omitempty"`
}

// CreateProviderRequest 创建供应商请求
type CreateProviderRequest struct {
	Code               string  `json:"code" binding:"required,min=2,max=50"`
	Name               string  `json:"name" binding:"required,min=2,max=100"`
	APIBaseURL         string  `json:"api_base_url" binding:"required,url"`
	APIVersion         *string `json:"api_version,omitempty"`
	TimeoutSeconds     *int    `json:"timeout_seconds,omitempty"`
	MaxRetries         *int    `json:"max_retries,omitempty"`
	IsActive           *bool   `json:"is_active,omitempty"`
	Priority           *int    `json:"priority,omitempty"`
	RateLimitPerSecond *int    `json:"rate_limit_per_second,omitempty"`
	Description        *string `json:"description,omitempty"`
}

// UpdateProviderRequest 更新供应商请求
type UpdateProviderRequest struct {
	Name               *string `json:"name,omitempty"`
	APIBaseURL         *string `json:"api_base_url,omitempty"`
	APIVersion         *string `json:"api_version,omitempty"`
	TimeoutSeconds     *int    `json:"timeout_seconds,omitempty"`
	MaxRetries         *int    `json:"max_retries,omitempty"`
	IsActive           *bool   `json:"is_active,omitempty"`
	Priority           *int    `json:"priority,omitempty"`
	RateLimitPerSecond *int    `json:"rate_limit_per_second,omitempty"`
	Description        *string `json:"description,omitempty"`
}

// CreateMappingRequest 创建映射关系请求
type CreateMappingRequest struct {
	CompanyID           string                 `json:"company_id" binding:"required,uuid"`
	ProviderID          string                 `json:"provider_id" binding:"required,uuid"`
	ProviderCompanyCode string                 `json:"provider_company_code" binding:"required,min=1,max=50"`
	IsSupported         *bool                  `json:"is_supported,omitempty"`
	IsPreferred         *bool                  `json:"is_preferred,omitempty"`
	WeightLimitKg       *float64               `json:"weight_limit_kg,omitempty"`
	SizeLimitCmLength   *float64               `json:"size_limit_cm_length,omitempty"`
	SizeLimitCmWidth    *float64               `json:"size_limit_cm_width,omitempty"`
	SizeLimitCmHeight   *float64               `json:"size_limit_cm_height,omitempty"`
	SupportedServices   []string               `json:"supported_services,omitempty"`
	PricingConfig       map[string]interface{} `json:"pricing_config,omitempty"`
}

// UpdateMappingRequest 更新映射关系请求
type UpdateMappingRequest struct {
	ProviderCompanyCode *string                `json:"provider_company_code,omitempty"`
	IsSupported         *bool                  `json:"is_supported,omitempty"`
	IsPreferred         *bool                  `json:"is_preferred,omitempty"`
	WeightLimitKg       *float64               `json:"weight_limit_kg,omitempty"`
	SizeLimitCmLength   *float64               `json:"size_limit_cm_length,omitempty"`
	SizeLimitCmWidth    *float64               `json:"size_limit_cm_width,omitempty"`
	SizeLimitCmHeight   *float64               `json:"size_limit_cm_height,omitempty"`
	SupportedServices   []string               `json:"supported_services,omitempty"`
	PricingConfig       map[string]interface{} `json:"pricing_config,omitempty"`
}

// CreateServiceRequest 创建服务请求
type CreateServiceRequest struct {
	CompanyID     string  `json:"company_id" binding:"required,uuid"`
	ServiceCode   string  `json:"service_code" binding:"required,min=1,max=50"`
	ServiceName   string  `json:"service_name" binding:"required,min=1,max=100"`
	Description   *string `json:"description,omitempty"`
	EstimatedDays *int    `json:"estimated_days,omitempty"`
	IsActive      *bool   `json:"is_active,omitempty"`
	SortOrder     *int    `json:"sort_order,omitempty"`
}

// UpdateServiceRequest 更新服务请求
type UpdateServiceRequest struct {
	ServiceName   *string `json:"service_name,omitempty"`
	Description   *string `json:"description,omitempty"`
	EstimatedDays *int    `json:"estimated_days,omitempty"`
	IsActive      *bool   `json:"is_active,omitempty"`
	SortOrder     *int    `json:"sort_order,omitempty"`
}

// CreateConfigRequest 创建配置请求
type CreateConfigRequest struct {
	CompanyID   string  `json:"company_id" binding:"required,uuid"`
	ConfigKey   string  `json:"config_key" binding:"required,min=1,max=100"`
	ConfigValue string  `json:"config_value" binding:"required"`
	ConfigType  string  `json:"config_type" binding:"required,oneof=string number boolean json"`
	Description *string `json:"description,omitempty"`
	IsActive    *bool   `json:"is_active,omitempty"`
}

// UpdateConfigRequest 更新配置请求
type UpdateConfigRequest struct {
	ConfigValue *string `json:"config_value,omitempty"`
	ConfigType  *string `json:"config_type,omitempty"`
	Description *string `json:"description,omitempty"`
	IsActive    *bool   `json:"is_active,omitempty"`
}

// ===== 验证方法 =====

// Validate 验证创建快递公司请求
func (r *CreateCompanyRequest) Validate() error {
	if err := ValidateCode(r.Code); err != nil {
		return fmt.Errorf("快递公司代码验证失败: %w", err)
	}
	if err := ValidateName(r.Name); err != nil {
		return fmt.Errorf("快递公司名称验证失败: %w", err)
	}
	if r.EnglishName != nil {
		if err := ValidateName(*r.EnglishName); err != nil {
			return fmt.Errorf("英文名称验证失败: %w", err)
		}
	}
	if r.OfficialWebsite != nil {
		if err := ValidateURL(*r.OfficialWebsite); err != nil {
			return fmt.Errorf("官方网站验证失败: %w", err)
		}
	}
	if r.MaxWeightKg != nil {
		if err := ValidatePositiveNumber(r.MaxWeightKg, "最大承重"); err != nil {
			return fmt.Errorf("最大承重验证失败: %w", err)
		}
	}
	if err := ValidateDescription(r.Description); err != nil {
		return fmt.Errorf("描述验证失败: %w", err)
	}
	return nil
}

// Validate 验证更新快递公司请求
func (r *UpdateCompanyRequest) Validate() error {
	if r.Name != nil {
		if err := ValidateName(*r.Name); err != nil {
			return fmt.Errorf("快递公司名称验证失败: %w", err)
		}
	}
	if r.EnglishName != nil {
		if err := ValidateName(*r.EnglishName); err != nil {
			return fmt.Errorf("英文名称验证失败: %w", err)
		}
	}
	if r.OfficialWebsite != nil {
		if err := ValidateURL(*r.OfficialWebsite); err != nil {
			return fmt.Errorf("官方网站验证失败: %w", err)
		}
	}
	if r.MaxWeightKg != nil {
		if err := ValidatePositiveNumber(r.MaxWeightKg, "最大承重"); err != nil {
			return fmt.Errorf("最大承重验证失败: %w", err)
		}
	}
	if err := ValidateDescription(r.Description); err != nil {
		return fmt.Errorf("描述验证失败: %w", err)
	}
	return nil
}

// Validate 验证创建供应商请求
func (r *CreateProviderRequest) Validate() error {
	if err := ValidateCode(r.Code); err != nil {
		return fmt.Errorf("供应商代码验证失败: %w", err)
	}
	if err := ValidateName(r.Name); err != nil {
		return fmt.Errorf("供应商名称验证失败: %w", err)
	}
	if err := ValidateURL(r.APIBaseURL); err != nil {
		return fmt.Errorf("API基础URL验证失败: %w", err)
	}
	if r.APIVersion != nil {
		if err := ValidateAPIVersion(*r.APIVersion); err != nil {
			return fmt.Errorf("API版本验证失败: %w", err)
		}
	}
	if r.TimeoutSeconds != nil {
		if err := ValidateTimeout(*r.TimeoutSeconds); err != nil {
			return fmt.Errorf("超时时间验证失败: %w", err)
		}
	}
	if r.MaxRetries != nil {
		if err := ValidateRetries(*r.MaxRetries); err != nil {
			return fmt.Errorf("重试次数验证失败: %w", err)
		}
	}
	if r.RateLimitPerSecond != nil {
		if err := ValidateRateLimit(*r.RateLimitPerSecond); err != nil {
			return fmt.Errorf("限流配置验证失败: %w", err)
		}
	}
	if err := ValidateDescription(r.Description); err != nil {
		return fmt.Errorf("描述验证失败: %w", err)
	}
	return nil
}

// Validate 验证更新供应商请求
func (r *UpdateProviderRequest) Validate() error {
	if r.Name != nil {
		if err := ValidateName(*r.Name); err != nil {
			return fmt.Errorf("供应商名称验证失败: %w", err)
		}
	}
	if r.APIBaseURL != nil {
		if err := ValidateURL(*r.APIBaseURL); err != nil {
			return fmt.Errorf("API基础URL验证失败: %w", err)
		}
	}
	if r.APIVersion != nil {
		if err := ValidateAPIVersion(*r.APIVersion); err != nil {
			return fmt.Errorf("API版本验证失败: %w", err)
		}
	}
	if r.TimeoutSeconds != nil {
		if err := ValidateTimeout(*r.TimeoutSeconds); err != nil {
			return fmt.Errorf("超时时间验证失败: %w", err)
		}
	}
	if r.MaxRetries != nil {
		if err := ValidateRetries(*r.MaxRetries); err != nil {
			return fmt.Errorf("重试次数验证失败: %w", err)
		}
	}
	if r.RateLimitPerSecond != nil {
		if err := ValidateRateLimit(*r.RateLimitPerSecond); err != nil {
			return fmt.Errorf("限流配置验证失败: %w", err)
		}
	}
	if err := ValidateDescription(r.Description); err != nil {
		return fmt.Errorf("描述验证失败: %w", err)
	}
	return nil
}

// Validate 验证创建映射关系请求
func (r *CreateMappingRequest) Validate() error {
	if err := ValidateUUID(r.CompanyID); err != nil {
		return fmt.Errorf("快递公司ID验证失败: %w", err)
	}
	if err := ValidateUUID(r.ProviderID); err != nil {
		return fmt.Errorf("供应商ID验证失败: %w", err)
	}
	if err := ValidateCode(r.ProviderCompanyCode); err != nil {
		return fmt.Errorf("供应商快递公司代码验证失败: %w", err)
	}
	if err := ValidatePositiveNumber(r.WeightLimitKg, "重量限制"); err != nil {
		return err
	}
	if err := ValidatePositiveNumber(r.SizeLimitCmLength, "长度限制"); err != nil {
		return err
	}
	if err := ValidatePositiveNumber(r.SizeLimitCmWidth, "宽度限制"); err != nil {
		return err
	}
	if err := ValidatePositiveNumber(r.SizeLimitCmHeight, "高度限制"); err != nil {
		return err
	}
	if len(r.SupportedServices) > 0 {
		if err := ValidateServices(r.SupportedServices); err != nil {
			return err
		}
	}
	if err := ValidatePricingConfig(r.PricingConfig); err != nil {
		return err
	}
	return nil
}

// Validate 验证更新映射关系请求
func (r *UpdateMappingRequest) Validate() error {
	if r.ProviderCompanyCode != nil {
		if err := ValidateCode(*r.ProviderCompanyCode); err != nil {
			return fmt.Errorf("供应商快递公司代码验证失败: %w", err)
		}
	}
	if err := ValidatePositiveNumber(r.WeightLimitKg, "重量限制"); err != nil {
		return err
	}
	if err := ValidatePositiveNumber(r.SizeLimitCmLength, "长度限制"); err != nil {
		return err
	}
	if err := ValidatePositiveNumber(r.SizeLimitCmWidth, "宽度限制"); err != nil {
		return err
	}
	if err := ValidatePositiveNumber(r.SizeLimitCmHeight, "高度限制"); err != nil {
		return err
	}
	if len(r.SupportedServices) > 0 {
		if err := ValidateServices(r.SupportedServices); err != nil {
			return err
		}
	}
	if err := ValidatePricingConfig(r.PricingConfig); err != nil {
		return err
	}
	return nil
}

// Validate 验证创建服务请求
func (r *CreateServiceRequest) Validate() error {
	if err := ValidateUUID(r.CompanyID); err != nil {
		return fmt.Errorf("快递公司ID验证失败: %w", err)
	}
	if err := ValidateCode(r.ServiceCode); err != nil {
		return fmt.Errorf("服务代码验证失败: %w", err)
	}
	if err := ValidateName(r.ServiceName); err != nil {
		return fmt.Errorf("服务名称验证失败: %w", err)
	}
	if err := ValidateDescription(r.Description); err != nil {
		return fmt.Errorf("描述验证失败: %w", err)
	}
	if err := ValidatePositiveInt(r.EstimatedDays, "预计天数"); err != nil {
		return err
	}
	return nil
}

// Validate 验证更新服务请求
func (r *UpdateServiceRequest) Validate() error {
	if r.ServiceName != nil {
		if err := ValidateName(*r.ServiceName); err != nil {
			return fmt.Errorf("服务名称验证失败: %w", err)
		}
	}
	if err := ValidateDescription(r.Description); err != nil {
		return fmt.Errorf("描述验证失败: %w", err)
	}
	if err := ValidatePositiveInt(r.EstimatedDays, "预计天数"); err != nil {
		return err
	}
	return nil
}

// Validate 验证创建配置请求
func (r *CreateConfigRequest) Validate() error {
	if err := ValidateUUID(r.CompanyID); err != nil {
		return fmt.Errorf("快递公司ID验证失败: %w", err)
	}
	if r.ConfigKey == "" {
		return fmt.Errorf("配置键不能为空")
	}
	if len(r.ConfigKey) > 100 {
		return fmt.Errorf("配置键长度不能超过100个字符")
	}
	if err := ValidateConfigType(r.ConfigType); err != nil {
		return err
	}
	if err := ValidateConfigValue(r.ConfigType, r.ConfigValue); err != nil {
		return err
	}
	if err := ValidateDescription(r.Description); err != nil {
		return fmt.Errorf("描述验证失败: %w", err)
	}
	return nil
}

// Validate 验证更新配置请求
func (r *UpdateConfigRequest) Validate() error {
	if r.ConfigType != nil && r.ConfigValue != nil {
		if err := ValidateConfigType(*r.ConfigType); err != nil {
			return err
		}
		if err := ValidateConfigValue(*r.ConfigType, *r.ConfigValue); err != nil {
			return err
		}
	}
	if err := ValidateDescription(r.Description); err != nil {
		return fmt.Errorf("描述验证失败: %w", err)
	}
	return nil
}
