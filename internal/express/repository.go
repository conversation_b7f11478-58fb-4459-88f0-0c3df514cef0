package express

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
)

// ExpressCompanyRepository 快递公司数据访问接口
type ExpressCompanyRepository interface {
	// 快递公司管理
	CreateCompany(company *ExpressCompany) error
	GetCompanyByID(id string) (*ExpressCompany, error)
	GetCompanyByCode(code string) (*ExpressCompany, error)
	GetCompanies(filter CompanyFilter, pagination Pagination) (*CompanyListResult, error)
	UpdateCompany(company *ExpressCompany) error
	DeleteCompany(id string) error

	// 供应商管理
	CreateProvider(provider *ExpressProvider) error
	GetProviderByID(id string) (*ExpressProvider, error)
	GetProviderByCode(code string) (*ExpressProvider, error)
	GetProviders(filter ProviderFilter, pagination Pagination) (*ProviderListResult, error)
	UpdateProvider(provider *ExpressProvider) error
	DeleteProvider(id string) error

	// 映射关系管理
	CreateMapping(mapping *ExpressCompanyProviderMapping) error
	GetMappingByID(id string) (*ExpressCompanyProviderMapping, error)
	GetMappings(filter MappingFilter, pagination Pagination) (*MappingListResult, error)
	GetMappingsByCompany(companyID string) ([]*ExpressCompanyProviderMapping, error)
	GetMappingsByProvider(providerID string) ([]*ExpressCompanyProviderMapping, error)
	GetMapping(companyID, providerID string) (*ExpressCompanyProviderMapping, error)
	UpdateMapping(mapping *ExpressCompanyProviderMapping) error
	DeleteMapping(id string) error

	// 服务管理
	CreateService(service *ExpressCompanyServiceModel) error
	GetServicesByCompany(companyID string) ([]*ExpressCompanyServiceModel, error)
	UpdateService(service *ExpressCompanyServiceModel) error
	DeleteService(id string) error

	// 配置管理
	CreateConfig(config *ExpressCompanyConfig) error
	GetConfigsByCompany(companyID string) ([]*ExpressCompanyConfig, error)
	GetConfig(companyID, configKey string) (*ExpressCompanyConfig, error)
	UpdateConfig(config *ExpressCompanyConfig) error
	DeleteConfig(id string) error

	// 审计日志
	CreateAuditLog(log *ExpressCompanyAuditLog) error
	GetAuditLogs(filter AuditLogFilter, pagination Pagination) (*AuditLogListResult, error)
}

// PostgresExpressCompanyRepository PostgreSQL实现
type PostgresExpressCompanyRepository struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewPostgresExpressCompanyRepository 创建PostgreSQL快递公司仓库
func NewPostgresExpressCompanyRepository(db *sql.DB, logger *zap.Logger) ExpressCompanyRepository {
	return &PostgresExpressCompanyRepository{
		db:     db,
		logger: logger,
	}
}

// CreateCompany 创建快递公司
func (r *PostgresExpressCompanyRepository) CreateCompany(company *ExpressCompany) error {
	query := `
		INSERT INTO express_companies (
			id, code, name, english_name, official_website, 
			description, is_active, sort_order, 
			volume_weight_ratio, max_weight_kg, max_volume_cm3, support_volume_weight,
			created_at, updated_at, created_by, updated_by
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
	`

	_, err := r.db.Exec(query,
		company.ID, company.Code, company.Name, company.EnglishName,
		company.OfficialWebsite, company.Description, company.IsActive, company.SortOrder,
		company.VolumeWeightRatio, company.MaxWeightKg, company.MaxVolumeCm3, company.SupportVolumeWeight,
		company.CreatedAt, company.UpdatedAt, company.CreatedBy, company.UpdatedBy,
	)

	if err != nil {
		r.logger.Error("Failed to create express company",
			zap.Error(err),
			zap.String("company_code", company.Code),
			zap.String("company_name", company.Name))
		return fmt.Errorf("创建快递公司失败: %w", err)
	}

	return nil
}

// GetCompanyByID 根据ID获取快递公司
func (r *PostgresExpressCompanyRepository) GetCompanyByID(id string) (*ExpressCompany, error) {
	query := `
		SELECT id, code, name, english_name, official_website,
			   description, is_active, sort_order,
			   volume_weight_ratio, max_weight_kg, max_volume_cm3, support_volume_weight,
			   created_at, updated_at, created_by, updated_by
		FROM express_companies 
		WHERE id = $1
	`

	var company ExpressCompany
	err := r.db.QueryRow(query, id).Scan(
		&company.ID, &company.Code, &company.Name, &company.EnglishName,
		&company.OfficialWebsite, &company.Description, &company.IsActive, &company.SortOrder,
		&company.VolumeWeightRatio, &company.MaxWeightKg, &company.MaxVolumeCm3, &company.SupportVolumeWeight,
		&company.CreatedAt, &company.UpdatedAt, &company.CreatedBy, &company.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrExpressCompanyNotFound
		}
		r.logger.Error("Failed to get express company by ID",
			zap.Error(err),
			zap.String("company_id", id))
		return nil, fmt.Errorf("查询快递公司失败: %w", err)
	}

	return &company, nil
}

// GetCompanyByCode 根据代码获取快递公司
func (r *PostgresExpressCompanyRepository) GetCompanyByCode(code string) (*ExpressCompany, error) {
	query := `
		SELECT id, code, name, english_name, official_website,
			   description, is_active, sort_order,
			   volume_weight_ratio, max_weight_kg,
			   max_volume_cm3, support_volume_weight,
			   created_at, updated_at, created_by, updated_by
		FROM express_companies
		WHERE code = $1
	`

	var company ExpressCompany
	err := r.db.QueryRow(query, code).Scan(
		&company.ID, &company.Code, &company.Name, &company.EnglishName,
		&company.OfficialWebsite, &company.Description, &company.IsActive, &company.SortOrder,
		&company.VolumeWeightRatio, &company.MaxWeightKg, &company.MaxVolumeCm3, &company.SupportVolumeWeight,
		&company.CreatedAt, &company.UpdatedAt, &company.CreatedBy, &company.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrExpressCompanyNotFound
		}
		r.logger.Error("Failed to get express company by code",
			zap.Error(err),
			zap.String("company_code", code))
		return nil, fmt.Errorf("查询快递公司失败: %w", err)
	}

	return &company, nil
}

// GetCompanies 获取快递公司列表
func (r *PostgresExpressCompanyRepository) GetCompanies(filter CompanyFilter, pagination Pagination) (*CompanyListResult, error) {
	// 构建查询条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	if filter.IsActive != nil {
		conditions = append(conditions, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *filter.IsActive)
		argIndex++
	}

	if filter.Keyword != "" {
		conditions = append(conditions, fmt.Sprintf("(code ILIKE $%d OR name ILIKE $%d)", argIndex, argIndex))
		args = append(args, "%"+filter.Keyword+"%")
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM express_companies %s", whereClause)
	var total int64
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count express companies", zap.Error(err))
		return nil, fmt.Errorf("查询快递公司总数失败: %w", err)
	}

	// 查询数据
	orderBy := "ORDER BY sort_order DESC, created_at DESC"
	if filter.SortBy != "" {
		orderBy = fmt.Sprintf("ORDER BY %s %s", filter.SortBy, filter.SortOrder)
	}

	dataQuery := fmt.Sprintf(`
		SELECT id, code, name, english_name, official_website,
			   description, is_active, sort_order,
			   volume_weight_ratio, max_weight_kg, max_volume_cm3, support_volume_weight,
			   created_at, updated_at, created_by, updated_by
		FROM express_companies %s %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, pagination.PageSize, pagination.Offset())

	rows, err := r.db.Query(dataQuery, args...)
	if err != nil {
		r.logger.Error("Failed to query express companies", zap.Error(err))
		return nil, fmt.Errorf("查询快递公司列表失败: %w", err)
	}
	defer rows.Close()

	var companies []*ExpressCompany
	for rows.Next() {
		var company ExpressCompany
		err := rows.Scan(
			&company.ID, &company.Code, &company.Name, &company.EnglishName,
			&company.OfficialWebsite, &company.Description, &company.IsActive, &company.SortOrder,
			&company.VolumeWeightRatio, &company.MaxWeightKg, &company.MaxVolumeCm3, &company.SupportVolumeWeight,
			&company.CreatedAt, &company.UpdatedAt, &company.CreatedBy, &company.UpdatedBy,
		)
		if err != nil {
			r.logger.Error("Failed to scan express company row", zap.Error(err))
			return nil, fmt.Errorf("扫描快递公司数据失败: %w", err)
		}
		companies = append(companies, &company)
	}

	return &CompanyListResult{
		Companies: companies,
		Total:     total,
		Page:      pagination.Page,
		PageSize:  pagination.PageSize,
	}, nil
}

// UpdateCompany 更新快递公司
func (r *PostgresExpressCompanyRepository) UpdateCompany(company *ExpressCompany) error {
	query := `
		UPDATE express_companies SET
			name = $2, english_name = $3, official_website = $4,
			description = $5, is_active = $6, sort_order = $7, 
			volume_weight_ratio = $8, max_weight_kg = $9, max_volume_cm3 = $10, 
			support_volume_weight = $11, updated_at = $12, updated_by = $13
		WHERE id = $1
	`

	company.UpdatedAt = time.Now()

	result, err := r.db.Exec(query,
		company.ID, company.Name, company.EnglishName, company.OfficialWebsite,
		company.Description, company.IsActive, company.SortOrder,
		company.VolumeWeightRatio, company.MaxWeightKg, company.MaxVolumeCm3,
		company.SupportVolumeWeight, company.UpdatedAt, company.UpdatedBy,
	)

	if err != nil {
		r.logger.Error("Failed to update express company",
			zap.Error(err),
			zap.String("company_id", company.ID))
		return fmt.Errorf("更新快递公司失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrExpressCompanyNotFound
	}

	return nil
}

// DeleteCompany 删除快递公司
func (r *PostgresExpressCompanyRepository) DeleteCompany(id string) error {
	query := "DELETE FROM express_companies WHERE id = $1"

	result, err := r.db.Exec(query, id)
	if err != nil {
		r.logger.Error("Failed to delete express company",
			zap.Error(err),
			zap.String("company_id", id))
		return fmt.Errorf("删除快递公司失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrExpressCompanyNotFound
	}

	return nil
}

// CreateProvider 创建供应商
func (r *PostgresExpressCompanyRepository) CreateProvider(provider *ExpressProvider) error {
	query := `
		INSERT INTO express_providers (
			id, code, name, api_base_url, api_version, timeout_seconds,
			max_retries, priority, rate_limit_per_second,
			description, created_at, updated_at, created_by, updated_by
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
	`

	_, err := r.db.Exec(query,
		provider.ID, provider.Code, provider.Name, provider.APIBaseURL,
		provider.APIVersion, provider.TimeoutSeconds, provider.MaxRetries,
		provider.Priority, provider.RateLimitPerSecond,
		provider.Description, provider.CreatedAt, provider.UpdatedAt,
		provider.CreatedBy, provider.UpdatedBy,
	)

	if err != nil {
		r.logger.Error("Failed to create express provider",
			zap.Error(err),
			zap.String("provider_code", provider.Code))
		return fmt.Errorf("创建供应商失败: %w", err)
	}

	return nil
}

// GetProviderByID 根据ID获取供应商
func (r *PostgresExpressCompanyRepository) GetProviderByID(id string) (*ExpressProvider, error) {
	query := `
		SELECT id, code, name, api_base_url, api_version, timeout_seconds,
			   max_retries, priority, rate_limit_per_second,
			   description, created_at, updated_at, created_by, updated_by
		FROM express_providers
		WHERE id = $1
	`

	var provider ExpressProvider
	err := r.db.QueryRow(query, id).Scan(
		&provider.ID, &provider.Code, &provider.Name, &provider.APIBaseURL,
		&provider.APIVersion, &provider.TimeoutSeconds, &provider.MaxRetries,
		&provider.Priority, &provider.RateLimitPerSecond,
		&provider.Description, &provider.CreatedAt, &provider.UpdatedAt,
		&provider.CreatedBy, &provider.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrExpressProviderNotFound
		}
		r.logger.Error("Failed to get express provider by ID",
			zap.Error(err),
			zap.String("provider_id", id))
		return nil, fmt.Errorf("查询供应商失败: %w", err)
	}

	return &provider, nil
}

// GetProviderByCode 根据代码获取供应商
func (r *PostgresExpressCompanyRepository) GetProviderByCode(code string) (*ExpressProvider, error) {
	query := `
		SELECT id, code, name, api_base_url, api_version, timeout_seconds,
			   max_retries, priority, rate_limit_per_second,
			   description, created_at, updated_at, created_by, updated_by
		FROM express_providers
		WHERE code = $1
	`

	var provider ExpressProvider
	err := r.db.QueryRow(query, code).Scan(
		&provider.ID, &provider.Code, &provider.Name, &provider.APIBaseURL,
		&provider.APIVersion, &provider.TimeoutSeconds, &provider.MaxRetries,
		&provider.Priority, &provider.RateLimitPerSecond,
		&provider.Description, &provider.CreatedAt, &provider.UpdatedAt,
		&provider.CreatedBy, &provider.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrExpressProviderNotFound
		}
		r.logger.Error("Failed to get express provider by code",
			zap.Error(err),
			zap.String("provider_code", code))
		return nil, fmt.Errorf("查询供应商失败: %w", err)
	}

	return &provider, nil
}

// GetProviders 获取供应商列表
func (r *PostgresExpressCompanyRepository) GetProviders(filter ProviderFilter, pagination Pagination) (*ProviderListResult, error) {
	// 构建查询条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	// 注意：IsActive过滤器已移除，供应商状态现在通过system_configs管理
	// if filter.IsActive != nil {
	//     // 供应商启用状态现在通过system_configs.provider_{code}.enabled管理
	// }

	if filter.Keyword != "" {
		conditions = append(conditions, fmt.Sprintf("(code ILIKE $%d OR name ILIKE $%d)", argIndex, argIndex))
		args = append(args, "%"+filter.Keyword+"%")
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM express_providers %s", whereClause)
	var total int64
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count express providers", zap.Error(err))
		return nil, fmt.Errorf("查询供应商总数失败: %w", err)
	}

	// 查询数据
	orderBy := "ORDER BY priority DESC, created_at DESC"
	if filter.SortBy != "" {
		orderBy = fmt.Sprintf("ORDER BY %s %s", filter.SortBy, filter.SortOrder)
	}

	dataQuery := fmt.Sprintf(`
		SELECT id, code, name, api_base_url, api_version, timeout_seconds,
			   max_retries, priority, rate_limit_per_second,
			   description, created_at, updated_at, created_by, updated_by
		FROM express_providers %s %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, pagination.PageSize, pagination.Offset())

	rows, err := r.db.Query(dataQuery, args...)
	if err != nil {
		r.logger.Error("Failed to query express providers", zap.Error(err))
		return nil, fmt.Errorf("查询供应商列表失败: %w", err)
	}
	defer rows.Close()

	var providers []*ExpressProvider
	for rows.Next() {
		var provider ExpressProvider
		err := rows.Scan(
			&provider.ID, &provider.Code, &provider.Name, &provider.APIBaseURL,
			&provider.APIVersion, &provider.TimeoutSeconds, &provider.MaxRetries,
			&provider.Priority, &provider.RateLimitPerSecond,
			&provider.Description, &provider.CreatedAt, &provider.UpdatedAt,
			&provider.CreatedBy, &provider.UpdatedBy,
		)
		if err != nil {
			r.logger.Error("Failed to scan express provider row", zap.Error(err))
			return nil, fmt.Errorf("扫描供应商数据失败: %w", err)
		}
		providers = append(providers, &provider)
	}

	return &ProviderListResult{
		Providers: providers,
		Total:     total,
		Page:      pagination.Page,
		PageSize:  pagination.PageSize,
	}, nil
}

// UpdateProvider 更新供应商
func (r *PostgresExpressCompanyRepository) UpdateProvider(provider *ExpressProvider) error {
	query := `
		UPDATE express_providers SET
			name = $2, api_base_url = $3, api_version = $4, timeout_seconds = $5,
			max_retries = $6, priority = $7, rate_limit_per_second = $8,
			description = $9, updated_at = $10, updated_by = $11
		WHERE id = $1
	`

	provider.UpdatedAt = time.Now()

	result, err := r.db.Exec(query,
		provider.ID, provider.Name, provider.APIBaseURL, provider.APIVersion,
		provider.TimeoutSeconds, provider.MaxRetries,
		provider.Priority, provider.RateLimitPerSecond, provider.Description,
		provider.UpdatedAt, provider.UpdatedBy,
	)

	if err != nil {
		r.logger.Error("Failed to update express provider",
			zap.Error(err),
			zap.String("provider_id", provider.ID))
		return fmt.Errorf("更新供应商失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrExpressProviderNotFound
	}

	return nil
}

// DeleteProvider 删除供应商
func (r *PostgresExpressCompanyRepository) DeleteProvider(id string) error {
	query := "DELETE FROM express_providers WHERE id = $1"

	result, err := r.db.Exec(query, id)
	if err != nil {
		r.logger.Error("Failed to delete express provider",
			zap.Error(err),
			zap.String("provider_id", id))
		return fmt.Errorf("删除供应商失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrExpressProviderNotFound
	}

	return nil
}
