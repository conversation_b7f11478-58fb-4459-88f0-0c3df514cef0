package express

import (
	"encoding/json"
	"fmt"
	"strings"

	"go.uber.org/zap"
)

// CreateAuditLog 创建审计日志
func (r *PostgresExpressCompanyRepository) CreateAuditLog(log *ExpressCompanyAuditLog) error {
	// 序列化JSON字段
	var operationDetailsJSON []byte
	var err error

	if log.OperationDetails != nil {
		operationDetailsJSON, err = json.Marshal(log.OperationDetails)
		if err != nil {
			r.logger.Error("Failed to marshal operation details", zap.Error(err))
			return fmt.Errorf("序列化操作详情失败: %w", err)
		}
	}

	query := `
		INSERT INTO express_company_audit_logs (
			id, operator_id, target_company_id, action, operation,
			operation_details, result, error_message, ip_address, user_agent,
			created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
	`

	_, err = r.db.Exec(query,
		log.ID, log.OperatorID, log.TargetCompanyID, log.Action, log.Operation,
		operationDetailsJSON, log.Result, log.ErrorMessage, log.IPAddress, log.UserAgent,
		log.CreatedAt,
	)

	if err != nil {
		r.logger.Error("Failed to create express company audit log",
			zap.Error(err),
			zap.String("operator_id", log.OperatorID),
			zap.String("action", log.Action))
		return fmt.Errorf("创建快递公司审计日志失败: %w", err)
	}

	return nil
}

// GetAuditLogs 获取审计日志列表
func (r *PostgresExpressCompanyRepository) GetAuditLogs(filter AuditLogFilter, pagination Pagination) (*AuditLogListResult, error) {
	// 构建查询条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	if filter.OperatorID != "" {
		conditions = append(conditions, fmt.Sprintf("l.operator_id = $%d", argIndex))
		args = append(args, filter.OperatorID)
		argIndex++
	}

	if filter.TargetCompanyID != "" {
		conditions = append(conditions, fmt.Sprintf("l.target_company_id = $%d", argIndex))
		args = append(args, filter.TargetCompanyID)
		argIndex++
	}

	if filter.Action != "" {
		conditions = append(conditions, fmt.Sprintf("l.action = $%d", argIndex))
		args = append(args, filter.Action)
		argIndex++
	}

	if filter.Operation != "" {
		conditions = append(conditions, fmt.Sprintf("l.operation = $%d", argIndex))
		args = append(args, filter.Operation)
		argIndex++
	}

	if filter.Result != "" {
		conditions = append(conditions, fmt.Sprintf("l.result = $%d", argIndex))
		args = append(args, filter.Result)
		argIndex++
	}

	if filter.StartTime != "" {
		conditions = append(conditions, fmt.Sprintf("l.created_at >= $%d", argIndex))
		args = append(args, filter.StartTime)
		argIndex++
	}

	if filter.EndTime != "" {
		conditions = append(conditions, fmt.Sprintf("l.created_at <= $%d", argIndex))
		args = append(args, filter.EndTime)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 查询总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM express_company_audit_logs l
		LEFT JOIN users u ON l.operator_id = u.id
		LEFT JOIN express_companies c ON l.target_company_id = c.id
		%s
	`, whereClause)

	var total int64
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count express company audit logs", zap.Error(err))
		return nil, fmt.Errorf("查询审计日志总数失败: %w", err)
	}

	// 查询数据
	orderBy := "ORDER BY l.created_at DESC"
	if filter.SortBy != "" {
		orderBy = fmt.Sprintf("ORDER BY l.%s %s", filter.SortBy, filter.SortOrder)
	}

	dataQuery := fmt.Sprintf(`
		SELECT l.id, l.operator_id, l.target_company_id, l.action, l.operation,
			   l.operation_details, l.result, l.error_message, l.ip_address, l.user_agent,
			   l.created_at
		FROM express_company_audit_logs l
		%s %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, pagination.PageSize, pagination.Offset())
	rows, err := r.db.Query(dataQuery, args...)
	if err != nil {
		r.logger.Error("Failed to query express company audit logs", zap.Error(err))
		return nil, fmt.Errorf("查询审计日志失败: %w", err)
	}
	defer rows.Close()

	var logs []*ExpressCompanyAuditLog
	for rows.Next() {
		var log ExpressCompanyAuditLog
		var operationDetailsJSON []byte

		err := rows.Scan(
			&log.ID, &log.OperatorID, &log.TargetCompanyID, &log.Action, &log.Operation,
			&operationDetailsJSON, &log.Result, &log.ErrorMessage, &log.IPAddress, &log.UserAgent,
			&log.CreatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan express company audit log row", zap.Error(err))
			return nil, fmt.Errorf("扫描审计日志数据失败: %w", err)
		}

		// 反序列化JSON字段
		if len(operationDetailsJSON) > 0 {
			if err := json.Unmarshal(operationDetailsJSON, &log.OperationDetails); err != nil {
				r.logger.Warn("Failed to unmarshal operation details", zap.Error(err))
			}
		}

		logs = append(logs, &log)
	}

	return &AuditLogListResult{
		Logs:     logs,
		Total:    total,
		Page:     pagination.Page,
		PageSize: pagination.PageSize,
	}, nil
}
