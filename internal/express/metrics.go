package express

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// MetricsCollector 指标收集器接口
type MetricsCollector interface {
	// RecordDuration 记录操作耗时
	RecordDuration(operation string, duration time.Duration, labels ...string)
	// IncrementCounter 增加计数器
	IncrementCounter(metric string, labels ...string)
	// SetGauge 设置仪表盘值
	SetGauge(metric string, value float64, labels ...string)
	// RecordHistogram 记录直方图
	RecordHistogram(metric string, value float64, labels ...string)
}

// SimpleMetricsCollector 简单指标收集器（用于开发和测试）
type SimpleMetricsCollector struct {
	counters   map[string]int64
	gauges     map[string]float64
	histograms map[string][]float64
	durations  map[string][]time.Duration
	mutex      sync.RWMutex
	logger     *zap.Logger
}

// NewSimpleMetricsCollector 创建简单指标收集器
func NewSimpleMetricsCollector(logger *zap.Logger) *SimpleMetricsCollector {
	return &SimpleMetricsCollector{
		counters:   make(map[string]int64),
		gauges:     make(map[string]float64),
		histograms: make(map[string][]float64),
		durations:  make(map[string][]time.Duration),
		logger:     logger,
	}
}

// RecordDuration 记录操作耗时
func (m *SimpleMetricsCollector) RecordDuration(operation string, duration time.Duration, labels ...string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	key := m.buildKey(operation, labels...)
	if m.durations[key] == nil {
		m.durations[key] = make([]time.Duration, 0)
	}
	m.durations[key] = append(m.durations[key], duration)

	// 记录到日志
	m.logger.Debug("Operation duration recorded",
		zap.String("operation", operation),
		zap.Duration("duration", duration),
		zap.Strings("labels", labels))
}

// IncrementCounter 增加计数器
func (m *SimpleMetricsCollector) IncrementCounter(metric string, labels ...string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	key := m.buildKey(metric, labels...)
	m.counters[key]++

	m.logger.Debug("Counter incremented",
		zap.String("metric", metric),
		zap.Int64("value", m.counters[key]),
		zap.Strings("labels", labels))
}

// SetGauge 设置仪表盘值
func (m *SimpleMetricsCollector) SetGauge(metric string, value float64, labels ...string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	key := m.buildKey(metric, labels...)
	m.gauges[key] = value

	m.logger.Debug("Gauge set",
		zap.String("metric", metric),
		zap.Float64("value", value),
		zap.Strings("labels", labels))
}

// RecordHistogram 记录直方图
func (m *SimpleMetricsCollector) RecordHistogram(metric string, value float64, labels ...string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	key := m.buildKey(metric, labels...)
	if m.histograms[key] == nil {
		m.histograms[key] = make([]float64, 0)
	}
	m.histograms[key] = append(m.histograms[key], value)

	m.logger.Debug("Histogram value recorded",
		zap.String("metric", metric),
		zap.Float64("value", value),
		zap.Strings("labels", labels))
}

// GetCounters 获取所有计数器
func (m *SimpleMetricsCollector) GetCounters() map[string]int64 {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result := make(map[string]int64)
	for k, v := range m.counters {
		result[k] = v
	}
	return result
}

// GetGauges 获取所有仪表盘
func (m *SimpleMetricsCollector) GetGauges() map[string]float64 {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result := make(map[string]float64)
	for k, v := range m.gauges {
		result[k] = v
	}
	return result
}

// GetDurations 获取所有耗时记录
func (m *SimpleMetricsCollector) GetDurations() map[string][]time.Duration {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result := make(map[string][]time.Duration)
	for k, v := range m.durations {
		result[k] = make([]time.Duration, len(v))
		copy(result[k], v)
	}
	return result
}

// buildKey 构建指标键
func (m *SimpleMetricsCollector) buildKey(metric string, labels ...string) string {
	key := metric
	for i := 0; i < len(labels); i += 2 {
		if i+1 < len(labels) {
			key += ":" + labels[i] + "=" + labels[i+1]
		}
	}
	return key
}

// MetricsMiddleware 指标中间件
type MetricsMiddleware struct {
	collector MetricsCollector
	logger    *zap.Logger
}

// NewMetricsMiddleware 创建指标中间件
func NewMetricsMiddleware(collector MetricsCollector, logger *zap.Logger) *MetricsMiddleware {
	return &MetricsMiddleware{
		collector: collector,
		logger:    logger,
	}
}

// RecordOperation 记录操作指标
func (m *MetricsMiddleware) RecordOperation(ctx context.Context, operation string, fn func() error) error {
	start := time.Now()
	
	// 增加操作计数
	m.collector.IncrementCounter("express_operations_total", "operation", operation)
	
	// 执行操作
	err := fn()
	
	// 记录耗时
	duration := time.Since(start)
	m.collector.RecordDuration("express_operation_duration", duration, "operation", operation)
	
	// 记录结果
	if err != nil {
		m.collector.IncrementCounter("express_operations_errors_total", "operation", operation)
		m.logger.Error("Operation failed",
			zap.String("operation", operation),
			zap.Duration("duration", duration),
			zap.Error(err))
	} else {
		m.collector.IncrementCounter("express_operations_success_total", "operation", operation)
		m.logger.Debug("Operation completed",
			zap.String("operation", operation),
			zap.Duration("duration", duration))
	}
	
	return err
}

// RecordCacheOperation 记录缓存操作指标
func (m *MetricsMiddleware) RecordCacheOperation(operation string, hit bool) {
	m.collector.IncrementCounter("express_cache_operations_total", "operation", operation)
	
	if hit {
		m.collector.IncrementCounter("express_cache_hits_total", "operation", operation)
	} else {
		m.collector.IncrementCounter("express_cache_misses_total", "operation", operation)
	}
}

// RecordDatabaseOperation 记录数据库操作指标
func (m *MetricsMiddleware) RecordDatabaseOperation(operation string, duration time.Duration, err error) {
	m.collector.IncrementCounter("express_database_operations_total", "operation", operation)
	m.collector.RecordDuration("express_database_operation_duration", duration, "operation", operation)
	
	if err != nil {
		m.collector.IncrementCounter("express_database_errors_total", "operation", operation)
	}
}

// RecordValidationResult 记录验证结果指标
func (m *MetricsMiddleware) RecordValidationResult(requestType string, valid bool) {
	m.collector.IncrementCounter("express_validations_total", "request_type", requestType)
	
	if valid {
		m.collector.IncrementCounter("express_validations_success_total", "request_type", requestType)
	} else {
		m.collector.IncrementCounter("express_validations_failed_total", "request_type", requestType)
	}
}

// 指标名称常量
const (
	// 操作指标
	MetricOperationsTotal        = "express_operations_total"
	MetricOperationDuration      = "express_operation_duration"
	MetricOperationsSuccessTotal = "express_operations_success_total"
	MetricOperationsErrorsTotal  = "express_operations_errors_total"

	// 缓存指标
	MetricCacheOperationsTotal = "express_cache_operations_total"
	MetricCacheHitsTotal       = "express_cache_hits_total"
	MetricCacheMissesTotal     = "express_cache_misses_total"

	// 数据库指标
	MetricDatabaseOperationsTotal = "express_database_operations_total"
	MetricDatabaseOperationDuration = "express_database_operation_duration"
	MetricDatabaseErrorsTotal     = "express_database_errors_total"

	// 验证指标
	MetricValidationsTotal        = "express_validations_total"
	MetricValidationsSuccessTotal = "express_validations_success_total"
	MetricValidationsFailedTotal  = "express_validations_failed_total"

	// 业务指标
	MetricCompaniesTotal   = "express_companies_total"
	MetricProvidersTotal   = "express_providers_total"
	MetricMappingsTotal    = "express_mappings_total"
	MetricServicesTotal    = "express_services_total"
	MetricConfigsTotal     = "express_configs_total"
	MetricAuditLogsTotal   = "express_audit_logs_total"
)

// 操作名称常量
const (
	// 快递公司操作
	OperationCreateCompany = "create_company"
	OperationUpdateCompany = "update_company"
	OperationDeleteCompany = "delete_company"
	OperationGetCompany    = "get_company"
	OperationListCompanies = "list_companies"

	// 供应商操作
	OperationCreateProvider = "create_provider"
	OperationUpdateProvider = "update_provider"
	OperationDeleteProvider = "delete_provider"
	OperationGetProvider    = "get_provider"
	OperationListProviders  = "list_providers"

	// 映射关系操作
	OperationCreateMapping = "create_mapping"
	OperationUpdateMapping = "update_mapping"
	OperationDeleteMapping = "delete_mapping"
	OperationGetMapping    = "get_mapping"
	OperationListMappings  = "list_mappings"

	// 服务操作
	OperationCreateService = "create_service"
	OperationUpdateService = "update_service"
	OperationDeleteService = "delete_service"
	OperationGetService    = "get_service"
	OperationListServices  = "list_services"

	// 配置操作
	OperationCreateConfig = "create_config"
	OperationUpdateConfig = "update_config"
	OperationDeleteConfig = "delete_config"
	OperationGetConfig    = "get_config"
	OperationListConfigs  = "list_configs"

	// 审计日志操作
	OperationCreateAuditLog = "create_audit_log"
	OperationListAuditLogs  = "list_audit_logs"

	// 缓存操作
	OperationCacheGet    = "cache_get"
	OperationCacheSet    = "cache_set"
	OperationCacheDelete = "cache_delete"

	// 数据库操作
	OperationDatabaseQuery  = "database_query"
	OperationDatabaseInsert = "database_insert"
	OperationDatabaseUpdate = "database_update"
	OperationDatabaseDelete = "database_delete"
)
