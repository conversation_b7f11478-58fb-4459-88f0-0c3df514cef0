package express

import (
	"database/sql"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// CreateService 创建服务
func (r *PostgresExpressCompanyRepository) CreateService(service *ExpressCompanyServiceModel) error {
	query := `
		INSERT INTO express_company_services (
			id, company_id, service_code, service_name, description,
			estimated_days, is_active, sort_order,
			created_at, updated_at, created_by, updated_by
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
	`

	_, err := r.db.Exec(query,
		service.ID, service.CompanyID, service.ServiceCode, service.ServiceName,
		service.Description, service.EstimatedDays, service.IsActive, service.SortOrder,
		service.CreatedAt, service.UpdatedAt, service.CreatedBy, service.UpdatedBy,
	)

	if err != nil {
		r.logger.Error("Failed to create express company service",
			zap.Error(err),
			zap.String("company_id", service.CompanyID),
			zap.String("service_code", service.ServiceCode))
		return fmt.Errorf("创建快递公司服务失败: %w", err)
	}

	return nil
}

// GetServicesByCompany 根据快递公司ID获取服务列表
func (r *PostgresExpressCompanyRepository) GetServicesByCompany(companyID string) ([]*ExpressCompanyServiceModel, error) {
	query := `
		SELECT id, company_id, service_code, service_name, description,
			   estimated_days, is_active, sort_order,
			   created_at, updated_at, created_by, updated_by
		FROM express_company_services
		WHERE company_id = $1
		ORDER BY sort_order ASC, created_at DESC
	`

	rows, err := r.db.Query(query, companyID)
	if err != nil {
		r.logger.Error("Failed to query express company services",
			zap.Error(err),
			zap.String("company_id", companyID))
		return nil, fmt.Errorf("查询快递公司服务失败: %w", err)
	}
	defer rows.Close()

	var services []*ExpressCompanyServiceModel
	for rows.Next() {
		var service ExpressCompanyServiceModel
		err := rows.Scan(
			&service.ID, &service.CompanyID, &service.ServiceCode, &service.ServiceName,
			&service.Description, &service.EstimatedDays, &service.IsActive, &service.SortOrder,
			&service.CreatedAt, &service.UpdatedAt, &service.CreatedBy, &service.UpdatedBy,
		)
		if err != nil {
			r.logger.Error("Failed to scan express company service row", zap.Error(err))
			return nil, fmt.Errorf("扫描快递公司服务数据失败: %w", err)
		}
		services = append(services, &service)
	}

	return services, nil
}

// UpdateService 更新服务
func (r *PostgresExpressCompanyRepository) UpdateService(service *ExpressCompanyServiceModel) error {
	query := `
		UPDATE express_company_services SET
			service_name = $2, description = $3, estimated_days = $4,
			is_active = $5, sort_order = $6, updated_at = $7, updated_by = $8
		WHERE id = $1
	`

	service.UpdatedAt = time.Now()

	result, err := r.db.Exec(query,
		service.ID, service.ServiceName, service.Description, service.EstimatedDays,
		service.IsActive, service.SortOrder, service.UpdatedAt, service.UpdatedBy,
	)

	if err != nil {
		r.logger.Error("Failed to update express company service",
			zap.Error(err),
			zap.String("service_id", service.ID))
		return fmt.Errorf("更新快递公司服务失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取更新行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrExpressServiceNotFound
	}

	return nil
}

// DeleteService 删除服务
func (r *PostgresExpressCompanyRepository) DeleteService(id string) error {
	query := "DELETE FROM express_company_services WHERE id = $1"

	result, err := r.db.Exec(query, id)
	if err != nil {
		r.logger.Error("Failed to delete express company service",
			zap.Error(err),
			zap.String("service_id", id))
		return fmt.Errorf("删除快递公司服务失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return ErrExpressServiceNotFound
	}

	return nil
}

// GetServiceByID 根据ID获取服务（内部使用）
func (r *PostgresExpressCompanyRepository) GetServiceByID(id string) (*ExpressCompanyServiceModel, error) {
	query := `
		SELECT id, company_id, service_code, service_name, description,
			   estimated_days, is_active, sort_order,
			   created_at, updated_at, created_by, updated_by
		FROM express_company_services
		WHERE id = $1
	`

	var service ExpressCompanyServiceModel
	err := r.db.QueryRow(query, id).Scan(
		&service.ID, &service.CompanyID, &service.ServiceCode, &service.ServiceName,
		&service.Description, &service.EstimatedDays, &service.IsActive, &service.SortOrder,
		&service.CreatedAt, &service.UpdatedAt, &service.CreatedBy, &service.UpdatedBy,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrExpressServiceNotFound
		}
		r.logger.Error("Failed to get express company service by ID",
			zap.Error(err),
			zap.String("service_id", id))
		return nil, fmt.Errorf("查询快递公司服务失败: %w", err)
	}

	return &service, nil
}
