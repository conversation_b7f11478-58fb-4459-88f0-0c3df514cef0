package express

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"go.uber.org/zap"
)

// CreateProvider 创建供应商
func (s *DefaultExpressCompanyService) CreateProvider(ctx context.Context, req CreateProviderRequest, operatorID string) (*ExpressProvider, error) {
	// 验证请求参数
	if err := s.validateCreateProviderRequest(req); err != nil {
		return nil, err
	}

	// 检查代码是否已存在
	existingProvider, err := s.repository.GetProviderByCode(req.Code)
	if err != nil && err != ErrExpressProviderNotFound {
		s.logger.Error("Failed to check provider code existence",
			zap.Error(err),
			zap.String("code", req.Code))
		return nil, fmt.Errorf("检查供应商代码失败: %w", err)
	}
	if existingProvider != nil {
		return nil, fmt.Errorf("供应商代码 %s 已存在", req.Code)
	}

	// 创建供应商
	provider := NewExpressProvider(req.Code, req.Name, req.APIBaseURL, &operatorID)

	// 设置可选字段
	if req.APIVersion != nil {
		provider.APIVersion = *req.APIVersion
	}
	if req.TimeoutSeconds != nil {
		provider.TimeoutSeconds = *req.TimeoutSeconds
	}
	if req.MaxRetries != nil {
		provider.MaxRetries = *req.MaxRetries
	}
	// IsActive字段已移除 - 供应商启用状态现在通过system_configs.provider_{code}.enabled管理
	// if req.IsActive != nil {
	//     provider.IsActive = *req.IsActive
	// }
	if req.Priority != nil {
		provider.Priority = *req.Priority
	}
	if req.RateLimitPerSecond != nil {
		provider.RateLimitPerSecond = *req.RateLimitPerSecond
	}
	if req.Description != nil {
		provider.Description = req.Description
	}

	// 保存到数据库
	if err := s.repository.CreateProvider(provider); err != nil {
		s.logger.Error("Failed to create express provider",
			zap.Error(err),
			zap.String("code", req.Code),
			zap.String("name", req.Name))
		return nil, fmt.Errorf("创建供应商失败: %w", err)
	}

	s.logger.Info("Express provider created successfully",
		zap.String("provider_id", provider.ID),
		zap.String("code", provider.Code),
		zap.String("name", provider.Name),
		zap.String("operator_id", operatorID))

	return provider, nil
}

// GetProviderByID 根据ID获取供应商
func (s *DefaultExpressCompanyService) GetProviderByID(ctx context.Context, id string) (*ExpressProvider, error) {
	if id == "" {
		return nil, fmt.Errorf("供应商ID不能为空")
	}

	provider, err := s.repository.GetProviderByID(id)
	if err != nil {
		if err == ErrExpressProviderNotFound {
			return nil, fmt.Errorf("供应商不存在")
		}
		s.logger.Error("Failed to get express provider by ID",
			zap.Error(err),
			zap.String("provider_id", id))
		return nil, fmt.Errorf("获取供应商失败: %w", err)
	}

	return provider, nil
}

// GetProviderByCode 根据代码获取供应商
func (s *DefaultExpressCompanyService) GetProviderByCode(ctx context.Context, code string) (*ExpressProvider, error) {
	if code == "" {
		return nil, fmt.Errorf("供应商代码不能为空")
	}

	provider, err := s.repository.GetProviderByCode(code)
	if err != nil {
		if err == ErrExpressProviderNotFound {
			return nil, fmt.Errorf("供应商不存在")
		}
		s.logger.Error("Failed to get express provider by code",
			zap.Error(err),
			zap.String("provider_code", code))
		return nil, fmt.Errorf("获取供应商失败: %w", err)
	}

	return provider, nil
}

// GetProviders 获取供应商列表
func (s *DefaultExpressCompanyService) GetProviders(ctx context.Context, filter ProviderFilter, pagination Pagination) (*ProviderListResult, error) {
	// 验证分页参数
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.PageSize <= 0 || pagination.PageSize > 100 {
		pagination.PageSize = 20
	}

	// 验证排序参数
	if filter.SortBy != "" {
		allowedSortFields := []string{"code", "name", "priority", "created_at", "updated_at"}
		if !s.isValidSortField(filter.SortBy, allowedSortFields) {
			return nil, fmt.Errorf("无效的排序字段: %s", filter.SortBy)
		}
	}

	if filter.SortOrder != "" && filter.SortOrder != "ASC" && filter.SortOrder != "DESC" {
		filter.SortOrder = "DESC"
	}

	result, err := s.repository.GetProviders(filter, pagination)
	if err != nil {
		s.logger.Error("Failed to get express providers",
			zap.Error(err),
			zap.Any("filter", filter),
			zap.Any("pagination", pagination))
		return nil, fmt.Errorf("获取供应商列表失败: %w", err)
	}

	return result, nil
}

// UpdateProvider 更新供应商
func (s *DefaultExpressCompanyService) UpdateProvider(ctx context.Context, id string, req UpdateProviderRequest, operatorID string) (*ExpressProvider, error) {
	if id == "" {
		return nil, fmt.Errorf("供应商ID不能为空")
	}

	// 获取现有供应商
	provider, err := s.repository.GetProviderByID(id)
	if err != nil {
		if err == ErrExpressProviderNotFound {
			return nil, fmt.Errorf("供应商不存在")
		}
		return nil, fmt.Errorf("获取供应商失败: %w", err)
	}

	// 更新字段
	if req.Name != nil {
		if err := s.validateProviderName(*req.Name); err != nil {
			return nil, err
		}
		provider.Name = *req.Name
	}
	if req.APIBaseURL != nil {
		if err := s.validateAPIBaseURL(*req.APIBaseURL); err != nil {
			return nil, err
		}
		provider.APIBaseURL = *req.APIBaseURL
	}
	if req.APIVersion != nil {
		provider.APIVersion = *req.APIVersion
	}
	if req.TimeoutSeconds != nil {
		if err := s.validateTimeoutSeconds(*req.TimeoutSeconds); err != nil {
			return nil, err
		}
		provider.TimeoutSeconds = *req.TimeoutSeconds
	}
	if req.MaxRetries != nil {
		if err := s.validateMaxRetries(*req.MaxRetries); err != nil {
			return nil, err
		}
		provider.MaxRetries = *req.MaxRetries
	}
	// IsActive字段已移除 - 供应商启用状态现在通过system_configs.provider_{code}.enabled管理
	// if req.IsActive != nil {
	//     provider.IsActive = *req.IsActive
	// }
	if req.Priority != nil {
		provider.Priority = *req.Priority
	}
	if req.RateLimitPerSecond != nil {
		if err := s.validateRateLimitPerSecond(*req.RateLimitPerSecond); err != nil {
			return nil, err
		}
		provider.RateLimitPerSecond = *req.RateLimitPerSecond
	}
	if req.Description != nil {
		provider.Description = req.Description
	}

	provider.UpdatedBy = &operatorID

	// 保存更新
	if err := s.repository.UpdateProvider(provider); err != nil {
		s.logger.Error("Failed to update express provider",
			zap.Error(err),
			zap.String("provider_id", id))
		return nil, fmt.Errorf("更新供应商失败: %w", err)
	}

	s.logger.Info("Express provider updated successfully",
		zap.String("provider_id", provider.ID),
		zap.String("code", provider.Code),
		zap.String("operator_id", operatorID))

	return provider, nil
}

// DeleteProvider 删除供应商
func (s *DefaultExpressCompanyService) DeleteProvider(ctx context.Context, id string, operatorID string) error {
	if id == "" {
		return fmt.Errorf("供应商ID不能为空")
	}

	// 检查供应商是否存在
	provider, err := s.repository.GetProviderByID(id)
	if err != nil {
		if err == ErrExpressProviderNotFound {
			return fmt.Errorf("供应商不存在")
		}
		return fmt.Errorf("获取供应商失败: %w", err)
	}

	// 检查是否有关联的映射关系
	mappings, err := s.repository.GetMappingsByProvider(id)
	if err != nil {
		s.logger.Error("Failed to check provider mappings",
			zap.Error(err),
			zap.String("provider_id", id))
		return fmt.Errorf("检查供应商映射关系失败: %w", err)
	}

	if len(mappings) > 0 {
		return fmt.Errorf("无法删除供应商，存在 %d 个关联的快递公司映射关系", len(mappings))
	}

	// 删除供应商
	if err := s.repository.DeleteProvider(id); err != nil {
		s.logger.Error("Failed to delete express provider",
			zap.Error(err),
			zap.String("provider_id", id))
		return fmt.Errorf("删除供应商失败: %w", err)
	}

	s.logger.Info("Express provider deleted successfully",
		zap.String("provider_id", id),
		zap.String("code", provider.Code),
		zap.String("operator_id", operatorID))

	return nil
}

// 验证创建供应商请求
func (s *DefaultExpressCompanyService) validateCreateProviderRequest(req CreateProviderRequest) error {
	if err := s.validateProviderCode(req.Code); err != nil {
		return err
	}

	if err := s.validateProviderName(req.Name); err != nil {
		return err
	}

	if err := s.validateAPIBaseURL(req.APIBaseURL); err != nil {
		return err
	}

	if req.TimeoutSeconds != nil {
		if err := s.validateTimeoutSeconds(*req.TimeoutSeconds); err != nil {
			return err
		}
	}

	if req.MaxRetries != nil {
		if err := s.validateMaxRetries(*req.MaxRetries); err != nil {
			return err
		}
	}

	if req.RateLimitPerSecond != nil {
		if err := s.validateRateLimitPerSecond(*req.RateLimitPerSecond); err != nil {
			return err
		}
	}

	return nil
}

// 验证供应商代码
func (s *DefaultExpressCompanyService) validateProviderCode(code string) error {
	if code == "" {
		return fmt.Errorf("供应商代码不能为空")
	}

	if len(code) < 2 || len(code) > 50 {
		return fmt.Errorf("供应商代码长度必须在2-50个字符之间")
	}

	// 只允许字母、数字和下划线
	for _, char := range code {
		if !((char >= 'A' && char <= 'Z') || (char >= 'a' && char <= 'z') ||
			(char >= '0' && char <= '9') || char == '_') {
			return fmt.Errorf("供应商代码只能包含字母、数字和下划线")
		}
	}

	return nil
}

// 验证供应商名称
func (s *DefaultExpressCompanyService) validateProviderName(name string) error {
	if name == "" {
		return fmt.Errorf("供应商名称不能为空")
	}

	if len(strings.TrimSpace(name)) < 2 {
		return fmt.Errorf("供应商名称长度不能少于2个字符")
	}

	if len(name) > 100 {
		return fmt.Errorf("供应商名称长度不能超过100个字符")
	}

	return nil
}

// 验证API基础URL
func (s *DefaultExpressCompanyService) validateAPIBaseURL(apiURL string) error {
	if apiURL == "" {
		return fmt.Errorf("API基础URL不能为空")
	}

	parsedURL, err := url.Parse(apiURL)
	if err != nil {
		return fmt.Errorf("API基础URL格式无效: %w", err)
	}

	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return fmt.Errorf("API基础URL必须使用http或https协议")
	}

	if parsedURL.Host == "" {
		return fmt.Errorf("API基础URL必须包含有效的主机名")
	}

	return nil
}

// 验证超时时间
func (s *DefaultExpressCompanyService) validateTimeoutSeconds(timeout int) error {
	if timeout <= 0 {
		return fmt.Errorf("超时时间必须大于0")
	}

	if timeout > 300 {
		return fmt.Errorf("超时时间不能超过300秒")
	}

	return nil
}

// 验证最大重试次数
func (s *DefaultExpressCompanyService) validateMaxRetries(retries int) error {
	if retries < 0 {
		return fmt.Errorf("最大重试次数不能小于0")
	}

	if retries > 10 {
		return fmt.Errorf("最大重试次数不能超过10")
	}

	return nil
}

// 验证每秒请求限制
func (s *DefaultExpressCompanyService) validateRateLimitPerSecond(rateLimit int) error {
	if rateLimit <= 0 {
		return fmt.Errorf("每秒请求限制必须大于0")
	}

	if rateLimit > 1000 {
		return fmt.Errorf("每秒请求限制不能超过1000")
	}

	return nil
}
