package express

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"
)

// CreateConfig 创建配置
func (s *DefaultExpressCompanyService) CreateConfig(ctx context.Context, req CreateConfigRequest, operatorID string) (*ExpressCompanyConfig, error) {
	// 验证请求参数
	if err := s.validateCreateConfigRequest(req); err != nil {
		return nil, err
	}
	
	// 检查快递公司是否存在
	_, err := s.repository.GetCompanyByID(req.CompanyID)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return nil, fmt.Errorf("快递公司不存在")
		}
		return nil, fmt.Errorf("查询快递公司失败: %w", err)
	}
	
	// 检查配置键是否已存在（同一快递公司内）
	existingConfig, err := s.repository.GetConfig(req.CompanyID, req.ConfigKey)
	if err != nil && err != ErrExpressConfigNotFound {
		return nil, fmt.Errorf("查询现有配置失败: %w", err)
	}
	if existingConfig != nil {
		return nil, fmt.Errorf("配置键 %s 在该快递公司中已存在", req.ConfigKey)
	}
	
	// 验证配置值格式
	if err := s.validateConfigValue(req.ConfigValue, req.ConfigType); err != nil {
		return nil, err
	}
	
	// 创建配置
	config := NewExpressCompanyConfig(req.CompanyID, req.ConfigKey, req.ConfigValue, req.ConfigType, &operatorID)
	
	// 设置可选字段
	if req.Description != nil {
		config.Description = req.Description
	}
	if req.IsActive != nil {
		config.IsActive = *req.IsActive
	}
	
	// 保存到数据库
	if err := s.repository.CreateConfig(config); err != nil {
		s.logger.Error("Failed to create express company config",
			zap.Error(err),
			zap.String("company_id", req.CompanyID),
			zap.String("config_key", req.ConfigKey))
		return nil, fmt.Errorf("创建快递公司配置失败: %w", err)
	}
	
	s.logger.Info("Express company config created successfully",
		zap.String("config_id", config.ID),
		zap.String("company_id", config.CompanyID),
		zap.String("config_key", config.ConfigKey),
		zap.String("operator_id", operatorID))
	
	return config, nil
}

// GetConfigsByCompany 根据快递公司ID获取配置列表
func (s *DefaultExpressCompanyService) GetConfigsByCompany(ctx context.Context, companyID string) ([]*ExpressCompanyConfig, error) {
	if companyID == "" {
		return nil, fmt.Errorf("快递公司ID不能为空")
	}
	
	// 检查快递公司是否存在
	_, err := s.repository.GetCompanyByID(companyID)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return nil, fmt.Errorf("快递公司不存在")
		}
		return nil, fmt.Errorf("查询快递公司失败: %w", err)
	}
	
	configs, err := s.repository.GetConfigsByCompany(companyID)
	if err != nil {
		s.logger.Error("Failed to get configs by company",
			zap.Error(err),
			zap.String("company_id", companyID))
		return nil, fmt.Errorf("获取快递公司配置失败: %w", err)
	}
	
	return configs, nil
}

// GetConfig 获取特定配置
func (s *DefaultExpressCompanyService) GetConfig(ctx context.Context, companyID, configKey string) (*ExpressCompanyConfig, error) {
	if companyID == "" || configKey == "" {
		return nil, fmt.Errorf("快递公司ID和配置键不能为空")
	}
	
	config, err := s.repository.GetConfig(companyID, configKey)
	if err != nil {
		if err == ErrExpressConfigNotFound {
			return nil, fmt.Errorf("配置不存在")
		}
		s.logger.Error("Failed to get express company config",
			zap.Error(err),
			zap.String("company_id", companyID),
			zap.String("config_key", configKey))
		return nil, fmt.Errorf("获取快递公司配置失败: %w", err)
	}
	
	return config, nil
}

// UpdateConfig 更新配置
func (s *DefaultExpressCompanyService) UpdateConfig(ctx context.Context, id string, req UpdateConfigRequest, operatorID string) (*ExpressCompanyConfig, error) {
	if id == "" {
		return nil, fmt.Errorf("配置ID不能为空")
	}
	
	// 获取现有配置
	configs, err := s.repository.GetConfigsByCompany("")
	if err != nil {
		return nil, fmt.Errorf("查询配置失败: %w", err)
	}
	
	var config *ExpressCompanyConfig
	for _, cfg := range configs {
		if cfg.ID == id {
			config = cfg
			break
		}
	}
	
	if config == nil {
		return nil, fmt.Errorf("配置不存在")
	}
	
	// 更新字段
	if req.ConfigValue != nil {
		configType := config.ConfigType
		if req.ConfigType != nil {
			configType = *req.ConfigType
		}
		
		if err := s.validateConfigValue(*req.ConfigValue, configType); err != nil {
			return nil, err
		}
		
		config.ConfigValue = *req.ConfigValue
	}
	if req.ConfigType != nil {
		if err := s.validateConfigType(*req.ConfigType); err != nil {
			return nil, err
		}
		config.ConfigType = *req.ConfigType
	}
	if req.Description != nil {
		config.Description = req.Description
	}
	if req.IsActive != nil {
		config.IsActive = *req.IsActive
	}
	
	config.UpdatedBy = &operatorID
	
	// 保存更新
	if err := s.repository.UpdateConfig(config); err != nil {
		s.logger.Error("Failed to update express company config",
			zap.Error(err),
			zap.String("config_id", id))
		return nil, fmt.Errorf("更新快递公司配置失败: %w", err)
	}
	
	s.logger.Info("Express company config updated successfully",
		zap.String("config_id", config.ID),
		zap.String("company_id", config.CompanyID),
		zap.String("config_key", config.ConfigKey),
		zap.String("operator_id", operatorID))
	
	return config, nil
}

// DeleteConfig 删除配置
func (s *DefaultExpressCompanyService) DeleteConfig(ctx context.Context, id string, operatorID string) error {
	if id == "" {
		return fmt.Errorf("配置ID不能为空")
	}
	
	// 检查配置是否存在
	configs, err := s.repository.GetConfigsByCompany("")
	if err != nil {
		return fmt.Errorf("查询配置失败: %w", err)
	}
	
	var config *ExpressCompanyConfig
	for _, cfg := range configs {
		if cfg.ID == id {
			config = cfg
			break
		}
	}
	
	if config == nil {
		return fmt.Errorf("配置不存在")
	}
	
	// 删除配置
	if err := s.repository.DeleteConfig(id); err != nil {
		s.logger.Error("Failed to delete express company config",
			zap.Error(err),
			zap.String("config_id", id))
		return fmt.Errorf("删除快递公司配置失败: %w", err)
	}
	
	s.logger.Info("Express company config deleted successfully",
		zap.String("config_id", id),
		zap.String("company_id", config.CompanyID),
		zap.String("config_key", config.ConfigKey),
		zap.String("operator_id", operatorID))
	
	return nil
}

// GetAuditLogs 获取审计日志列表
func (s *DefaultExpressCompanyService) GetAuditLogs(ctx context.Context, filter AuditLogFilter, pagination Pagination) (*AuditLogListResult, error) {
	// 验证分页参数
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.PageSize <= 0 || pagination.PageSize > 100 {
		pagination.PageSize = 20
	}
	
	// 验证排序参数
	if filter.SortBy != "" {
		allowedSortFields := []string{"created_at", "action", "operation", "result"}
		if !s.isValidSortField(filter.SortBy, allowedSortFields) {
			return nil, fmt.Errorf("无效的排序字段: %s", filter.SortBy)
		}
	}
	
	if filter.SortOrder != "" && filter.SortOrder != "ASC" && filter.SortOrder != "DESC" {
		filter.SortOrder = "DESC"
	}
	
	result, err := s.repository.GetAuditLogs(filter, pagination)
	if err != nil {
		s.logger.Error("Failed to get express company audit logs",
			zap.Error(err),
			zap.Any("filter", filter),
			zap.Any("pagination", pagination))
		return nil, fmt.Errorf("获取快递公司审计日志失败: %w", err)
	}
	
	return result, nil
}

// 验证创建配置请求
func (s *DefaultExpressCompanyService) validateCreateConfigRequest(req CreateConfigRequest) error {
	if req.CompanyID == "" {
		return fmt.Errorf("快递公司ID不能为空")
	}
	
	if err := s.validateConfigKey(req.ConfigKey); err != nil {
		return err
	}
	
	if err := s.validateConfigType(req.ConfigType); err != nil {
		return err
	}
	
	if err := s.validateConfigValue(req.ConfigValue, req.ConfigType); err != nil {
		return err
	}
	
	return nil
}

// 验证配置键
func (s *DefaultExpressCompanyService) validateConfigKey(key string) error {
	if key == "" {
		return fmt.Errorf("配置键不能为空")
	}
	
	if len(key) < 1 || len(key) > 100 {
		return fmt.Errorf("配置键长度必须在1-100个字符之间")
	}
	
	// 只允许字母、数字和下划线
	for _, char := range key {
		if !((char >= 'A' && char <= 'Z') || (char >= 'a' && char <= 'z') || 
			 (char >= '0' && char <= '9') || char == '_') {
			return fmt.Errorf("配置键只能包含字母、数字和下划线")
		}
	}
	
	return nil
}

// 验证配置类型
func (s *DefaultExpressCompanyService) validateConfigType(configType string) error {
	allowedTypes := []string{"string", "number", "boolean", "json"}
	for _, allowedType := range allowedTypes {
		if configType == allowedType {
			return nil
		}
	}
	
	return fmt.Errorf("配置类型必须是以下之一: %s", strings.Join(allowedTypes, ", "))
}

// 验证配置值
func (s *DefaultExpressCompanyService) validateConfigValue(value, configType string) error {
	if value == "" {
		return fmt.Errorf("配置值不能为空")
	}
	
	switch configType {
	case "string":
		// 字符串类型无需特殊验证
		return nil
		
	case "number":
		// 验证是否为有效数字
		if _, err := strconv.ParseFloat(value, 64); err != nil {
			return fmt.Errorf("配置值必须是有效的数字")
		}
		return nil
		
	case "boolean":
		// 验证是否为有效布尔值
		if _, err := strconv.ParseBool(value); err != nil {
			return fmt.Errorf("配置值必须是有效的布尔值 (true/false)")
		}
		return nil
		
	case "json":
		// 验证是否为有效JSON
		var jsonData interface{}
		if err := json.Unmarshal([]byte(value), &jsonData); err != nil {
			return fmt.Errorf("配置值必须是有效的JSON格式")
		}
		return nil
		
	default:
		return fmt.Errorf("不支持的配置类型: %s", configType)
	}
}
