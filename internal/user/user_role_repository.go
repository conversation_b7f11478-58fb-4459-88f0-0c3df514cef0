package user

import (
	"database/sql"
	"fmt"
	"time"
)

// AddRole 为用户添加角色
func (r *PostgresUserRepository) AddRole(userID, roleID string) error {
	// 检查用户是否存在
	_, err := r.FindByID(userID)
	if err != nil {
		return err
	}

	// 插入用户角色关联记录
	_, err = r.db.Exec(
		`INSERT INTO user_roles (user_id, role_id, created_at)
		VALUES ($1, $2, $3)
		ON CONFLICT (user_id, role_id) DO NOTHING`,
		userID, roleID, time.Now(),
	)
	if err != nil {
		return fmt.Errorf("添加用户角色失败: %w", err)
	}

	return nil
}

// RemoveRole 从用户中移除角色
func (r *PostgresUserRepository) RemoveRole(userID, roleID string) error {
	// 检查用户是否存在
	_, err := r.FindByID(userID)
	if err != nil {
		return err
	}

	// 检查是否是用户的默认角色
	var defaultRoleID string
	err = r.db.QueryRow("SELECT default_role_id FROM users WHERE id = $1", userID).Scan(&defaultRoleID)
	if err != nil {
		return fmt.Errorf("查询用户默认角色失败: %w", err)
	}

	// 如果是默认角色，不允许移除
	if defaultRoleID == roleID {
		return fmt.Errorf("不能移除用户的默认角色，请先更改默认角色")
	}

	// 删除用户角色关联记录
	result, err := r.db.Exec(
		"DELETE FROM user_roles WHERE user_id = $1 AND role_id = $2",
		userID, roleID,
	)
	if err != nil {
		return fmt.Errorf("移除用户角色失败: %w", err)
	}

	// 检查是否有记录被删除
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}
	if rowsAffected == 0 {
		return fmt.Errorf("用户角色关联不存在")
	}

	return nil
}

// SetDefaultRole 设置用户的默认角色
func (r *PostgresUserRepository) SetDefaultRole(userID, roleID string) error {
	// 检查用户是否存在
	_, err := r.FindByID(userID)
	if err != nil {
		return err
	}

	// 检查角色是否存在
	// 注意：这里假设有一个RoleRepository可用，实际实现中需要注入或创建
	// roleRepo := NewPostgresRoleRepository(r.db)
	// _, err = roleRepo.FindByID(roleID)
	// if err != nil {
	// 	return err
	// }

	// 更新用户的默认角色
	result, err := r.db.Exec(
		"UPDATE users SET default_role_id = $1, updated_at = $2 WHERE id = $3",
		roleID, time.Now(), userID,
	)
	if err != nil {
		return fmt.Errorf("设置用户默认角色失败: %w", err)
	}

	// 检查是否有记录被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}
	if rowsAffected == 0 {
		return ErrUserNotFound
	}

	// 确保用户角色关联存在
	_, err = r.db.Exec(
		`INSERT INTO user_roles (user_id, role_id, created_at)
		VALUES ($1, $2, $3)
		ON CONFLICT (user_id, role_id) DO NOTHING`,
		userID, roleID, time.Now(),
	)
	if err != nil {
		return fmt.Errorf("添加用户角色关联失败: %w", err)
	}

	return nil
}

// FindRoles 查找用户的所有角色
func (r *PostgresUserRepository) FindRoles(userID string) ([]string, error) {
	// 检查用户是否存在
	_, err := r.FindByID(userID)
	if err != nil {
		return nil, err
	}

	// 查询用户的默认角色
	var defaultRoleID string
	err = r.db.QueryRow("SELECT default_role_id FROM users WHERE id = $1", userID).Scan(&defaultRoleID)
	if err != nil {
		return nil, fmt.Errorf("查询用户默认角色失败: %w", err)
	}

	// 查询用户的所有角色
	rows, err := r.db.Query(
		"SELECT role_id FROM user_roles WHERE user_id = $1",
		userID,
	)
	if err != nil {
		return nil, fmt.Errorf("查询用户角色失败: %w", err)
	}
	defer rows.Close()

	var roles []string
	for rows.Next() {
		var roleID string
		err := rows.Scan(&roleID)
		if err != nil {
			return nil, fmt.Errorf("扫描角色ID失败: %w", err)
		}
		roles = append(roles, roleID)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代角色行失败: %w", err)
	}

	// 如果默认角色不为空且不在角色列表中，添加到列表中
	if defaultRoleID != "" {
		found := false
		for _, roleID := range roles {
			if roleID == defaultRoleID {
				found = true
				break
			}
		}
		if !found {
			roles = append(roles, defaultRoleID)
		}
	}

	return roles, nil
}

// FindByRole 查找拥有指定角色的所有用户
func (r *PostgresUserRepository) FindByRole(roleID string) ([]*User, error) {
	// 查询拥有指定角色的所有用户
	rows, err := r.db.Query(
		`SELECT u.id, u.username, u.email, u.password_hash, u.callback_url, u.client_id, u.is_active, u.default_role_id, u.created_at, u.updated_at
		FROM users u
		LEFT JOIN user_roles ur ON u.id = ur.user_id
		WHERE ur.role_id = $1 OR u.default_role_id = $1
		ORDER BY u.username`,
		roleID,
	)
	if err != nil {
		return nil, fmt.Errorf("查询拥有角色的用户失败: %w", err)
	}
	defer rows.Close()

	var users []*User
	for rows.Next() {
		var user User
		var defaultRoleID sql.NullString
		err := rows.Scan(
			&user.ID, &user.Username, &user.Email, &user.PasswordHash, &user.CallbackURL, &user.ClientID, &user.IsActive, &defaultRoleID, &user.CreatedAt, &user.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描用户行失败: %w", err)
		}
		if defaultRoleID.Valid {
			user.DefaultRoleID = defaultRoleID.String
		}
		users = append(users, &user)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代用户行失败: %w", err)
	}

	return users, nil
}

// HasPermission 检查用户是否拥有指定权限
func (r *PostgresUserRepository) HasPermission(userID, permissionID string) (bool, error) {
	// 检查用户是否存在
	_, err := r.FindByID(userID)
	if err != nil {
		return false, err
	}

	// 查询用户是否通过角色拥有指定权限
	var count int
	err = r.db.QueryRow(
		`SELECT COUNT(*) FROM role_permissions rp
		WHERE rp.permission_id = $1 AND (
			rp.role_id IN (
				SELECT role_id FROM user_roles WHERE user_id = $2
			) OR rp.role_id = (
				SELECT default_role_id FROM users WHERE id = $2
			)
		)`,
		permissionID, userID,
	).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("查询用户权限失败: %w", err)
	}

	return count > 0, nil
}
