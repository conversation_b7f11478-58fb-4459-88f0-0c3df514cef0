package user

import (
	"database/sql"
	"errors"
	"fmt"
	"time"
)

// PostgresPermissionRepository PostgreSQL权限存储库实现
type PostgresPermissionRepository struct {
	db *sql.DB
}

// NewPostgresPermissionRepository 创建新的PostgreSQL权限存储库
func NewPostgresPermissionRepository(db *sql.DB) PermissionRepository {
	return &PostgresPermissionRepository{
		db: db,
	}
}

// Create 创建新权限
func (r *PostgresPermissionRepository) Create(permission *Permission) error {
	// 检查权限名是否已存在
	existingPermission, err := r.FindByName(permission.Name)
	if err != nil && !errors.Is(err, ErrPermissionNotFound) {
		return fmt.Errorf("检查权限名是否存在失败: %w", err)
	}
	if existingPermission != nil {
		return ErrPermissionAlreadyExists
	}

	// 检查资源和操作组合是否已存在
	existingPermission, err = r.FindByResourceAction(permission.Resource, permission.Action)
	if err != nil && !errors.Is(err, ErrPermissionNotFound) {
		return fmt.Errorf("检查资源和操作组合是否存在失败: %w", err)
	}
	if existingPermission != nil {
		return fmt.Errorf("资源 %s 和操作 %s 的权限已存在", permission.Resource, permission.Action)
	}

	// 设置创建和更新时间
	now := time.Now()
	permission.CreatedAt = now
	permission.UpdatedAt = now

	// 插入权限记录
	_, err = r.db.Exec(
		`INSERT INTO permissions (id, name, description, resource, action, is_system, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
		permission.ID, permission.Name, permission.Description, permission.Resource, permission.Action,
		permission.IsSystem, permission.CreatedAt, permission.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("创建权限失败: %w", err)
	}

	return nil
}

// FindByID 根据ID查找权限
func (r *PostgresPermissionRepository) FindByID(id string) (*Permission, error) {
	var permission Permission
	err := r.db.QueryRow(
		`SELECT id, name, description, resource, action, is_system, created_at, updated_at
		FROM permissions WHERE id = $1`,
		id,
	).Scan(
		&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action,
		&permission.IsSystem, &permission.CreatedAt, &permission.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrPermissionNotFound
		}
		return nil, fmt.Errorf("查询权限失败: %w", err)
	}

	return &permission, nil
}

// FindByName 根据名称查找权限
func (r *PostgresPermissionRepository) FindByName(name string) (*Permission, error) {
	var permission Permission
	err := r.db.QueryRow(
		`SELECT id, name, description, resource, action, is_system, created_at, updated_at
		FROM permissions WHERE name = $1`,
		name,
	).Scan(
		&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action,
		&permission.IsSystem, &permission.CreatedAt, &permission.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrPermissionNotFound
		}
		return nil, fmt.Errorf("查询权限失败: %w", err)
	}

	return &permission, nil
}

// FindByResourceAction 根据资源和操作查找权限
func (r *PostgresPermissionRepository) FindByResourceAction(resource, action string) (*Permission, error) {
	var permission Permission
	err := r.db.QueryRow(
		`SELECT id, name, description, resource, action, is_system, created_at, updated_at
		FROM permissions WHERE resource = $1 AND action = $2`,
		resource, action,
	).Scan(
		&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action,
		&permission.IsSystem, &permission.CreatedAt, &permission.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrPermissionNotFound
		}
		return nil, fmt.Errorf("查询权限失败: %w", err)
	}

	return &permission, nil
}

// FindAll 查找所有权限
func (r *PostgresPermissionRepository) FindAll() ([]*Permission, error) {
	rows, err := r.db.Query(
		`SELECT id, name, description, resource, action, is_system, created_at, updated_at
		FROM permissions ORDER BY resource, action`,
	)
	if err != nil {
		return nil, fmt.Errorf("查询所有权限失败: %w", err)
	}
	defer rows.Close()

	var permissions []*Permission
	for rows.Next() {
		var permission Permission
		err := rows.Scan(
			&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action,
			&permission.IsSystem, &permission.CreatedAt, &permission.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描权限行失败: %w", err)
		}
		permissions = append(permissions, &permission)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代权限行失败: %w", err)
	}

	return permissions, nil
}

// Update 更新权限信息
func (r *PostgresPermissionRepository) Update(permission *Permission) error {
	// 检查是否是系统权限
	existingPermission, err := r.FindByID(permission.ID)
	if err != nil {
		return err
	}
	if existingPermission.IsSystem && (existingPermission.Name != permission.Name ||
		existingPermission.Resource != permission.Resource ||
		existingPermission.Action != permission.Action ||
		!permission.IsSystem) {
		return ErrSystemPermissionCannotBeModified
	}

	// 更新更新时间
	permission.UpdatedAt = time.Now()

	// 更新权限记录
	result, err := r.db.Exec(
		`UPDATE permissions SET name = $1, description = $2, resource = $3, action = $4, is_system = $5, updated_at = $6
		WHERE id = $7`,
		permission.Name, permission.Description, permission.Resource, permission.Action,
		permission.IsSystem, permission.UpdatedAt, permission.ID,
	)
	if err != nil {
		return fmt.Errorf("更新权限失败: %w", err)
	}

	// 检查是否有记录被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}
	if rowsAffected == 0 {
		return ErrPermissionNotFound
	}

	return nil
}

// Delete 删除权限
func (r *PostgresPermissionRepository) Delete(id string) error {
	// 检查是否是系统权限
	permission, err := r.FindByID(id)
	if err != nil {
		return err
	}
	if permission.IsSystem {
		return ErrSystemPermissionCannotBeDeleted
	}

	// 删除权限记录
	result, err := r.db.Exec("DELETE FROM permissions WHERE id = $1", id)
	if err != nil {
		return fmt.Errorf("删除权限失败: %w", err)
	}

	// 检查是否有记录被删除
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}
	if rowsAffected == 0 {
		return ErrPermissionNotFound
	}

	return nil
}

// FindByRole 查找角色的所有权限
func (r *PostgresPermissionRepository) FindByRole(roleID string) ([]*Permission, error) {
	rows, err := r.db.Query(
		`SELECT p.id, p.name, p.description, p.resource, p.action, p.is_system, p.created_at, p.updated_at
		FROM permissions p
		JOIN role_permissions rp ON p.id = rp.permission_id
		WHERE rp.role_id = $1
		ORDER BY p.resource, p.action`,
		roleID,
	)
	if err != nil {
		return nil, fmt.Errorf("查询角色权限失败: %w", err)
	}
	defer rows.Close()

	var permissions []*Permission
	for rows.Next() {
		var permission Permission
		err := rows.Scan(
			&permission.ID, &permission.Name, &permission.Description, &permission.Resource, &permission.Action,
			&permission.IsSystem, &permission.CreatedAt, &permission.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描权限行失败: %w", err)
		}
		permissions = append(permissions, &permission)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代权限行失败: %w", err)
	}

	return permissions, nil
}
