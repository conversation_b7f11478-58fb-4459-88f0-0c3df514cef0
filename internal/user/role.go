package user

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

var (
	// ErrRoleNotFound 角色未找到错误
	ErrRoleNotFound = errors.New("role not found")

	// ErrRoleAlreadyExists 角色已存在错误
	ErrRoleAlreadyExists = errors.New("role already exists")

	// ErrSystemRoleCannotBeModified 系统角色不能被修改错误
	ErrSystemRoleCannotBeModified = errors.New("system role cannot be modified")

	// ErrSystemRoleCannotBeDeleted 系统角色不能被删除错误
	ErrSystemRoleCannotBeDeleted = errors.New("system role cannot be deleted")
)

// 预定义角色ID
const (
	RoleAdmin       = "admin"
	RoleUser        = "user"
	RoleGuest       = "guest"
	RoleSystemAdmin = "system_admin"
)

// Role 表示系统角色
type Role struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	IsSystem    bool      `json:"is_system"`
	Permissions []string  `json:"permissions,omitempty"` // 权限ID列表
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// NewRole 创建新角色
func NewRole(name, description string, isSystem bool) *Role {
	return &Role{
		ID:          uuid.New().String(),
		Name:        name,
		Description: description,
		IsSystem:    isSystem,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

// RoleRepository 角色存储接口
type RoleRepository interface {
	// Create 创建新角色
	Create(role *Role) error

	// FindByID 根据ID查找角色
	FindByID(id string) (*Role, error)

	// FindByName 根据名称查找角色
	FindByName(name string) (*Role, error)

	// FindAll 查找所有角色
	FindAll() ([]*Role, error)

	// Update 更新角色信息
	Update(role *Role) error

	// Delete 删除角色
	Delete(id string) error

	// AddPermission 为角色添加权限
	AddPermission(roleID, permissionID string) error

	// RemovePermission 从角色中移除权限
	RemovePermission(roleID, permissionID string) error

	// FindPermissions 查找角色的所有权限
	FindPermissions(roleID string) ([]string, error)

	// FindByPermission 查找拥有指定权限的所有角色
	FindByPermission(permissionID string) ([]*Role, error)
}
