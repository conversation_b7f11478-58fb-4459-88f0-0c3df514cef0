package user

import (
	"errors"
)

// RoleService 角色服务接口
type RoleService interface {
	// CreateRole 创建新角色
	CreateRole(name, description string, isSystem bool) (*Role, error)

	// GetRoleByID 根据ID获取角色
	GetRoleByID(id string) (*Role, error)

	// GetRoleByName 根据名称获取角色
	GetRoleByName(name string) (*Role, error)

	// GetAllRoles 获取所有角色
	GetAllRoles() ([]*Role, error)

	// UpdateRole 更新角色信息
	UpdateRole(role *Role) error

	// DeleteRole 删除角色
	DeleteRole(id string) error

	// AddPermissionToRole 为角色添加权限
	AddPermissionToRole(roleID, permissionID string) error

	// RemovePermissionFromRole 从角色中移除权限
	RemovePermissionFromRole(roleID, permissionID string) error

	// GetRolePermissions 获取角色的所有权限
	GetRolePermissions(roleID string) ([]*Permission, error)

	// GetRolesByPermission 获取拥有指定权限的所有角色
	GetRolesByPermission(permissionID string) ([]*Role, error)
}

// DefaultRoleService 默认角色服务实现
type DefaultRoleService struct {
	roleRepository       RoleRepository
	permissionRepository PermissionRepository
}

// NewRoleService 创建新的角色服务
func NewRoleService(roleRepository RoleRepository, permissionRepository PermissionRepository) RoleService {
	return &DefaultRoleService{
		roleRepository:       roleRepository,
		permissionRepository: permissionRepository,
	}
}

// CreateRole 创建新角色
func (s *DefaultRoleService) CreateRole(name, description string, isSystem bool) (*Role, error) {
	// 验证角色名
	if err := validateRoleName(name); err != nil {
		return nil, err
	}

	// 创建角色
	role := NewRole(name, description, isSystem)

	// 保存角色
	if err := s.roleRepository.Create(role); err != nil {
		return nil, err
	}

	return role, nil
}

// GetRoleByID 根据ID获取角色
func (s *DefaultRoleService) GetRoleByID(id string) (*Role, error) {
	return s.roleRepository.FindByID(id)
}

// GetRoleByName 根据名称获取角色
func (s *DefaultRoleService) GetRoleByName(name string) (*Role, error) {
	return s.roleRepository.FindByName(name)
}

// GetAllRoles 获取所有角色
func (s *DefaultRoleService) GetAllRoles() ([]*Role, error) {
	return s.roleRepository.FindAll()
}

// UpdateRole 更新角色信息
func (s *DefaultRoleService) UpdateRole(role *Role) error {
	// 验证角色名
	if err := validateRoleName(role.Name); err != nil {
		return err
	}

	// 检查是否是系统角色
	existingRole, err := s.roleRepository.FindByID(role.ID)
	if err != nil {
		return err
	}
	if existingRole.IsSystem && (existingRole.Name != role.Name || !role.IsSystem) {
		return ErrSystemRoleCannotBeModified
	}

	return s.roleRepository.Update(role)
}

// DeleteRole 删除角色
func (s *DefaultRoleService) DeleteRole(id string) error {
	// 检查是否是系统角色
	role, err := s.roleRepository.FindByID(id)
	if err != nil {
		return err
	}
	if role.IsSystem {
		return ErrSystemRoleCannotBeDeleted
	}

	return s.roleRepository.Delete(id)
}

// AddPermissionToRole 为角色添加权限
func (s *DefaultRoleService) AddPermissionToRole(roleID, permissionID string) error {
	// 检查角色是否存在
	_, err := s.roleRepository.FindByID(roleID)
	if err != nil {
		return err
	}

	// 检查权限是否存在
	_, err = s.permissionRepository.FindByID(permissionID)
	if err != nil {
		return err
	}

	return s.roleRepository.AddPermission(roleID, permissionID)
}

// RemovePermissionFromRole 从角色中移除权限
func (s *DefaultRoleService) RemovePermissionFromRole(roleID, permissionID string) error {
	// 检查角色是否存在
	_, err := s.roleRepository.FindByID(roleID)
	if err != nil {
		return err
	}

	// 检查权限是否存在
	_, err = s.permissionRepository.FindByID(permissionID)
	if err != nil {
		return err
	}

	return s.roleRepository.RemovePermission(roleID, permissionID)
}

// GetRolePermissions 获取角色的所有权限
func (s *DefaultRoleService) GetRolePermissions(roleID string) ([]*Permission, error) {
	// 检查角色是否存在
	_, err := s.roleRepository.FindByID(roleID)
	if err != nil {
		return nil, err
	}

	return s.permissionRepository.FindByRole(roleID)
}

// GetRolesByPermission 获取拥有指定权限的所有角色
func (s *DefaultRoleService) GetRolesByPermission(permissionID string) ([]*Role, error) {
	// 检查权限是否存在
	_, err := s.permissionRepository.FindByID(permissionID)
	if err != nil {
		return nil, err
	}

	return s.roleRepository.FindByPermission(permissionID)
}

// 验证角色名
func validateRoleName(name string) error {
	if name == "" {
		return errors.New("角色名不能为空")
	}
	if len(name) < 2 {
		return errors.New("角色名长度不能小于2个字符")
	}
	if len(name) > 50 {
		return errors.New("角色名长度不能超过50个字符")
	}
	return nil
}
