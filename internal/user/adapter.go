package user

import (
	"github.com/your-org/go-kuaidi/internal/auth"
)

// ClientServiceAdapter 客户端服务适配器
type ClientServiceAdapter struct {
	authClientService auth.ClientService
}

// NewClientServiceAdapter 创建新的客户端服务适配器
func NewClientServiceAdapter(authClientService auth.ClientService) ClientService {
	return &ClientServiceAdapter{
		authClientService: authClientService,
	}
}

// GenerateClientCredentials 生成客户端凭证
func (a *ClientServiceAdapter) GenerateClientCredentials(name string, allowedScopes []string) (interface{}, string, error) {
	client, secret, err := a.authClientService.GenerateClientCredentials(name, allowedScopes)
	if err != nil {
		return nil, "", err
	}
	return client, secret, nil
}

// Authenticate 认证客户端
func (a *ClientServiceAdapter) Authenticate(clientID, clientSecret string) (interface{}, error) {
	client, err := a.authClientService.Authenticate(clientID, clientSecret)
	if err != nil {
		return nil, err
	}
	return client, nil
}

// ValidateScopes 验证作用域
func (a *ClientServiceAdapter) ValidateScopes(client interface{}, requestedScopes []string) ([]string, error) {
	authClient, ok := client.(*auth.Client)
	if !ok {
		return nil, nil
	}
	return a.authClientService.ValidateScopes(authClient, requestedScopes)
}

// FindByID 根据ID查找客户端
func (a *ClientServiceAdapter) FindByID(clientID string) (interface{}, error) {
	return a.authClientService.FindByID(clientID)
}

// ResetClientSecret 重置客户端密钥（保持id不变）
func (a *ClientServiceAdapter) ResetClientSecret(clientID string) (string, error) {
	return a.authClientService.ResetClientSecret(clientID)
}
