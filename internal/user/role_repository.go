package user

import (
	"database/sql"
	"errors"
	"fmt"
	"time"
)

// PostgresRoleRepository PostgreSQL角色存储库实现
type PostgresRoleRepository struct {
	db *sql.DB
}

// NewPostgresRoleRepository 创建新的PostgreSQL角色存储库
func NewPostgresRoleRepository(db *sql.DB) RoleRepository {
	return &PostgresRoleRepository{
		db: db,
	}
}

// Create 创建新角色
func (r *PostgresRoleRepository) Create(role *Role) error {
	// 检查角色名是否已存在
	existingRole, err := r.FindByName(role.Name)
	if err != nil && !errors.Is(err, ErrRoleNotFound) {
		return fmt.Errorf("检查角色名是否存在失败: %w", err)
	}
	if existingRole != nil {
		return ErrRoleAlreadyExists
	}

	// 设置创建和更新时间
	now := time.Now()
	role.CreatedAt = now
	role.UpdatedAt = now

	// 插入角色记录
	_, err = r.db.Exec(
		`INSERT INTO roles (id, name, description, is_system, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)`,
		role.ID, role.Name, role.Description, role.IsSystem, role.CreatedAt, role.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("创建角色失败: %w", err)
	}

	return nil
}

// FindByID 根据ID查找角色
func (r *PostgresRoleRepository) FindByID(id string) (*Role, error) {
	var role Role
	err := r.db.QueryRow(
		`SELECT id, name, description, is_system, created_at, updated_at
		FROM roles WHERE id = $1`,
		id,
	).Scan(
		&role.ID, &role.Name, &role.Description, &role.IsSystem, &role.CreatedAt, &role.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrRoleNotFound
		}
		return nil, fmt.Errorf("查询角色失败: %w", err)
	}

	// 查询角色的权限
	permissions, err := r.FindPermissions(role.ID)
	if err != nil {
		return nil, fmt.Errorf("查询角色权限失败: %w", err)
	}
	role.Permissions = permissions

	return &role, nil
}

// FindByName 根据名称查找角色
func (r *PostgresRoleRepository) FindByName(name string) (*Role, error) {
	var role Role
	err := r.db.QueryRow(
		`SELECT id, name, description, is_system, created_at, updated_at
		FROM roles WHERE name = $1`,
		name,
	).Scan(
		&role.ID, &role.Name, &role.Description, &role.IsSystem, &role.CreatedAt, &role.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrRoleNotFound
		}
		return nil, fmt.Errorf("查询角色失败: %w", err)
	}

	// 查询角色的权限
	permissions, err := r.FindPermissions(role.ID)
	if err != nil {
		return nil, fmt.Errorf("查询角色权限失败: %w", err)
	}
	role.Permissions = permissions

	return &role, nil
}

// FindAll 查找所有角色
func (r *PostgresRoleRepository) FindAll() ([]*Role, error) {
	rows, err := r.db.Query(
		`SELECT id, name, description, is_system, created_at, updated_at
		FROM roles ORDER BY name`,
	)
	if err != nil {
		return nil, fmt.Errorf("查询所有角色失败: %w", err)
	}
	defer rows.Close()

	var roles []*Role
	for rows.Next() {
		var role Role
		err := rows.Scan(
			&role.ID, &role.Name, &role.Description, &role.IsSystem, &role.CreatedAt, &role.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描角色行失败: %w", err)
		}

		// 查询角色的权限
		permissions, err := r.FindPermissions(role.ID)
		if err != nil {
			return nil, fmt.Errorf("查询角色权限失败: %w", err)
		}
		role.Permissions = permissions

		roles = append(roles, &role)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代角色行失败: %w", err)
	}

	return roles, nil
}

// Update 更新角色信息
func (r *PostgresRoleRepository) Update(role *Role) error {
	// 检查是否是系统角色
	existingRole, err := r.FindByID(role.ID)
	if err != nil {
		return err
	}
	if existingRole.IsSystem && (existingRole.Name != role.Name || !role.IsSystem) {
		return ErrSystemRoleCannotBeModified
	}

	// 更新更新时间
	role.UpdatedAt = time.Now()

	// 更新角色记录
	result, err := r.db.Exec(
		`UPDATE roles SET name = $1, description = $2, is_system = $3, updated_at = $4
		WHERE id = $5`,
		role.Name, role.Description, role.IsSystem, role.UpdatedAt, role.ID,
	)
	if err != nil {
		return fmt.Errorf("更新角色失败: %w", err)
	}

	// 检查是否有记录被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}
	if rowsAffected == 0 {
		return ErrRoleNotFound
	}

	return nil
}

// Delete 删除角色
func (r *PostgresRoleRepository) Delete(id string) error {
	// 检查是否是系统角色
	role, err := r.FindByID(id)
	if err != nil {
		return err
	}
	if role.IsSystem {
		return ErrSystemRoleCannotBeDeleted
	}

	// 删除角色记录
	result, err := r.db.Exec("DELETE FROM roles WHERE id = $1", id)
	if err != nil {
		return fmt.Errorf("删除角色失败: %w", err)
	}

	// 检查是否有记录被删除
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}
	if rowsAffected == 0 {
		return ErrRoleNotFound
	}

	return nil
}

// AddPermission 为角色添加权限
func (r *PostgresRoleRepository) AddPermission(roleID, permissionID string) error {
	// 检查角色是否存在
	_, err := r.FindByID(roleID)
	if err != nil {
		return err
	}

	// 插入角色权限关联记录
	_, err = r.db.Exec(
		`INSERT INTO role_permissions (role_id, permission_id, created_at)
		VALUES ($1, $2, $3)
		ON CONFLICT (role_id, permission_id) DO NOTHING`,
		roleID, permissionID, time.Now(),
	)
	if err != nil {
		return fmt.Errorf("添加角色权限失败: %w", err)
	}

	return nil
}

// RemovePermission 从角色中移除权限
func (r *PostgresRoleRepository) RemovePermission(roleID, permissionID string) error {
	// 检查角色是否存在
	_, err := r.FindByID(roleID)
	if err != nil {
		return err
	}

	// 删除角色权限关联记录
	result, err := r.db.Exec(
		"DELETE FROM role_permissions WHERE role_id = $1 AND permission_id = $2",
		roleID, permissionID,
	)
	if err != nil {
		return fmt.Errorf("移除角色权限失败: %w", err)
	}

	// 检查是否有记录被删除
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}
	if rowsAffected == 0 {
		return fmt.Errorf("角色权限关联不存在")
	}

	return nil
}

// FindPermissions 查找角色的所有权限
func (r *PostgresRoleRepository) FindPermissions(roleID string) ([]string, error) {
	rows, err := r.db.Query(
		`SELECT permission_id FROM role_permissions WHERE role_id = $1`,
		roleID,
	)
	if err != nil {
		return nil, fmt.Errorf("查询角色权限失败: %w", err)
	}
	defer rows.Close()

	var permissions []string
	for rows.Next() {
		var permissionID string
		err := rows.Scan(&permissionID)
		if err != nil {
			return nil, fmt.Errorf("扫描权限ID失败: %w", err)
		}
		permissions = append(permissions, permissionID)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代权限行失败: %w", err)
	}

	return permissions, nil
}

// FindByPermission 查找拥有指定权限的所有角色
func (r *PostgresRoleRepository) FindByPermission(permissionID string) ([]*Role, error) {
	rows, err := r.db.Query(
		`SELECT r.id, r.name, r.description, r.is_system, r.created_at, r.updated_at
		FROM roles r
		JOIN role_permissions rp ON r.id = rp.role_id
		WHERE rp.permission_id = $1
		ORDER BY r.name`,
		permissionID,
	)
	if err != nil {
		return nil, fmt.Errorf("查询拥有权限的角色失败: %w", err)
	}
	defer rows.Close()

	var roles []*Role
	for rows.Next() {
		var role Role
		err := rows.Scan(
			&role.ID, &role.Name, &role.Description, &role.IsSystem, &role.CreatedAt, &role.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描角色行失败: %w", err)
		}
		roles = append(roles, &role)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代角色行失败: %w", err)
	}

	return roles, nil
}
