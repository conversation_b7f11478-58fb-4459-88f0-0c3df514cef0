package user

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"github.com/your-org/go-kuaidi/internal/errors"
	"go.uber.org/zap"
)

// UserAuditAction 用户审计操作类型
type UserAuditAction string

const (
	AuditActionUserCreate        UserAuditAction = "user_create"
	AuditActionUserUpdate        UserAuditAction = "user_update"
	AuditActionUserDelete        UserAuditAction = "user_delete"
	AuditActionUserStatusUpdate  UserAuditAction = "user_status_update"
	AuditActionPasswordReset     UserAuditAction = "password_reset"
	AuditActionBatchStatusUpdate UserAuditAction = "batch_status_update"
	AuditActionUserRestore       UserAuditAction = "user_restore"
	AuditActionUserForceDelete   UserAuditAction = "user_force_delete"
)

// UserAuditLog 用户操作审计日志
type UserAuditLog struct {
	ID           string                 `json:"id" db:"id"`
	OperatorID   string                 `json:"operator_id" db:"operator_id"`
	TargetUserID *string                `json:"target_user_id" db:"target_user_id"`
	Action       UserAuditAction        `json:"action" db:"action"`
	Details      map[string]interface{} `json:"details" db:"details"`
	Result       string                 `json:"result" db:"result"`
	IPAddress    string                 `json:"ip_address" db:"ip_address"`
	UserAgent    string                 `json:"user_agent" db:"user_agent"`
	CreatedAt    time.Time              `json:"created_at" db:"created_at"`
}

// UserAuditRepository 用户审计日志存储库接口
type UserAuditRepository interface {
	// Create 创建审计日志
	Create(log *UserAuditLog) error

	// FindByOperator 根据操作者查询审计日志
	FindByOperator(operatorID string, limit, offset int) ([]*UserAuditLog, int64, error)

	// FindByTargetUser 根据目标用户查询审计日志
	FindByTargetUser(targetUserID string, limit, offset int) ([]*UserAuditLog, int64, error)

	// FindByAction 根据操作类型查询审计日志
	FindByAction(action UserAuditAction, limit, offset int) ([]*UserAuditLog, int64, error)

	// FindByTimeRange 根据时间范围查询审计日志
	FindByTimeRange(startTime, endTime time.Time, limit, offset int) ([]*UserAuditLog, int64, error)
}

// PostgresUserAuditRepository PostgreSQL用户审计日志存储库实现
type PostgresUserAuditRepository struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewPostgresUserAuditRepository 创建PostgreSQL用户审计日志存储库
func NewPostgresUserAuditRepository(db *sql.DB, logger *zap.Logger) UserAuditRepository {
	return &PostgresUserAuditRepository{
		db:     db,
		logger: logger,
	}
}

// Create 创建审计日志
func (r *PostgresUserAuditRepository) Create(log *UserAuditLog) error {
	if log.ID == "" {
		log.ID = uuid.New().String()
	}

	if log.CreatedAt.IsZero() {
		log.CreatedAt = time.Now()
	}

	// 序列化详情
	detailsJSON, err := json.Marshal(log.Details)
	if err != nil {
		r.logger.Error("Failed to marshal audit log details", zap.Error(err))
		return err
	}

	// 设置默认结果为success
	if log.Result == "" {
		log.Result = "success"
	}

	query := `
		INSERT INTO user_audit_logs (id, operator_id, target_user_id, action, details, operation, operation_details, result, ip_address, user_agent, created_at)
		VALUES ($1, $2, $3, $4, $5, $4, $5, $6, $7, $8, $9)
	`

	_, err = r.db.Exec(query,
		log.ID,
		log.OperatorID,
		log.TargetUserID,
		string(log.Action),
		string(detailsJSON),
		log.Result,
		log.IPAddress,
		log.UserAgent,
		log.CreatedAt,
	)

	if err != nil {
		r.logger.Error("Failed to create audit log",
			zap.Error(err),
			zap.String("operator_id", log.OperatorID),
			zap.String("action", string(log.Action)))
		return err
	}

	r.logger.Info("Successfully created audit log",
		zap.String("id", log.ID),
		zap.String("operator_id", log.OperatorID),
		zap.String("action", string(log.Action)))

	return nil
}

// FindByOperator 根据操作者查询审计日志
func (r *PostgresUserAuditRepository) FindByOperator(operatorID string, limit, offset int) ([]*UserAuditLog, int64, error) {
	// 查询总数
	countQuery := `SELECT COUNT(*) FROM user_audit_logs WHERE operator_id = $1`
	var total int64
	err := r.db.QueryRow(countQuery, operatorID).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count audit logs by operator", zap.Error(err))
		return nil, 0, err
	}

	// 查询数据
	query := `
		SELECT id, operator_id, target_user_id,
		       COALESCE(action, operation) as action,
		       COALESCE(details, operation_details, '{}') as details,
		       ip_address, user_agent, created_at
		FROM user_audit_logs
		WHERE operator_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.Query(query, operatorID, limit, offset)
	if err != nil {
		r.logger.Error("Failed to query audit logs by operator", zap.Error(err))
		return nil, 0, err
	}
	defer rows.Close()

	var logs []*UserAuditLog
	for rows.Next() {
		log, err := r.scanAuditLog(rows)
		if err != nil {
			r.logger.Error("Failed to scan audit log", zap.Error(err))
			continue
		}
		logs = append(logs, log)
	}

	return logs, total, rows.Err()
}

// FindByTargetUser 根据目标用户查询审计日志
func (r *PostgresUserAuditRepository) FindByTargetUser(targetUserID string, limit, offset int) ([]*UserAuditLog, int64, error) {
	// 查询总数
	countQuery := `SELECT COUNT(*) FROM user_audit_logs WHERE target_user_id = $1`
	var total int64
	err := r.db.QueryRow(countQuery, targetUserID).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count audit logs by target user", zap.Error(err))
		return nil, 0, err
	}

	// 查询数据
	query := `
		SELECT id, operator_id, target_user_id,
		       COALESCE(action, operation) as action,
		       COALESCE(details, operation_details, '{}') as details,
		       ip_address, user_agent, created_at
		FROM user_audit_logs
		WHERE target_user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.Query(query, targetUserID, limit, offset)
	if err != nil {
		r.logger.Error("Failed to query audit logs by target user", zap.Error(err))
		return nil, 0, err
	}
	defer rows.Close()

	var logs []*UserAuditLog
	for rows.Next() {
		log, err := r.scanAuditLog(rows)
		if err != nil {
			r.logger.Error("Failed to scan audit log", zap.Error(err))
			continue
		}
		logs = append(logs, log)
	}

	return logs, total, rows.Err()
}

// FindByAction 根据操作类型查询审计日志
func (r *PostgresUserAuditRepository) FindByAction(action UserAuditAction, limit, offset int) ([]*UserAuditLog, int64, error) {
	// 查询总数
	countQuery := `SELECT COUNT(*) FROM user_audit_logs WHERE action = $1`
	var total int64
	err := r.db.QueryRow(countQuery, string(action)).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count audit logs by action", zap.Error(err))
		return nil, 0, err
	}

	// 查询数据
	query := `
		SELECT id, operator_id, target_user_id,
		       COALESCE(action, operation) as action,
		       COALESCE(details, operation_details, '{}') as details,
		       ip_address, user_agent, created_at
		FROM user_audit_logs
		WHERE COALESCE(action, operation) = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.Query(query, string(action), limit, offset)
	if err != nil {
		r.logger.Error("Failed to query audit logs by action", zap.Error(err))
		return nil, 0, err
	}
	defer rows.Close()

	var logs []*UserAuditLog
	for rows.Next() {
		log, err := r.scanAuditLog(rows)
		if err != nil {
			r.logger.Error("Failed to scan audit log", zap.Error(err))
			continue
		}
		logs = append(logs, log)
	}

	return logs, total, rows.Err()
}

// FindByTimeRange 根据时间范围查询审计日志
func (r *PostgresUserAuditRepository) FindByTimeRange(startTime, endTime time.Time, limit, offset int) ([]*UserAuditLog, int64, error) {
	// 查询总数
	countQuery := `SELECT COUNT(*) FROM user_audit_logs WHERE created_at BETWEEN $1 AND $2`
	var total int64
	err := r.db.QueryRow(countQuery, startTime, endTime).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count audit logs by time range", zap.Error(err))
		return nil, 0, err
	}

	// 查询数据
	query := `
		SELECT id, operator_id, target_user_id,
		       COALESCE(action, operation) as action,
		       COALESCE(details, operation_details, '{}') as details,
		       ip_address, user_agent, created_at
		FROM user_audit_logs
		WHERE created_at BETWEEN $1 AND $2
		ORDER BY created_at DESC
		LIMIT $3 OFFSET $4
	`

	rows, err := r.db.Query(query, startTime, endTime, limit, offset)
	if err != nil {
		r.logger.Error("Failed to query audit logs by time range", zap.Error(err))
		return nil, 0, err
	}
	defer rows.Close()

	var logs []*UserAuditLog
	for rows.Next() {
		log, err := r.scanAuditLog(rows)
		if err != nil {
			r.logger.Error("Failed to scan audit log", zap.Error(err))
			continue
		}
		logs = append(logs, log)
	}

	return logs, total, rows.Err()
}

// scanAuditLog 扫描审计日志行
func (r *PostgresUserAuditRepository) scanAuditLog(rows *sql.Rows) (*UserAuditLog, error) {
	var log UserAuditLog
	var targetUserID sql.NullString
	var detailsJSON string

	err := rows.Scan(
		&log.ID,
		&log.OperatorID,
		&targetUserID,
		&log.Action,
		&detailsJSON,
		&log.IPAddress,
		&log.UserAgent,
		&log.CreatedAt,
	)
	if err != nil {
		return nil, err
	}

	if targetUserID.Valid {
		log.TargetUserID = &targetUserID.String
	}

	// 反序列化详情
	if detailsJSON != "" {
		err = json.Unmarshal([]byte(detailsJSON), &log.Details)
		if err != nil {
			r.logger.Warn("Failed to unmarshal audit log details",
				zap.Error(err),
				zap.String("log_id", log.ID))
			log.Details = make(map[string]interface{})
		}
	} else {
		log.Details = make(map[string]interface{})
	}

	return &log, nil
}

// UserAuditService 用户审计服务接口
type UserAuditService interface {
	// LogUserOperation 记录用户操作
	LogUserOperation(ctx context.Context, operatorID string, targetUserID *string, action UserAuditAction, details map[string]interface{}, ipAddress, userAgent string) error

	// LogUserOperationWithResult 记录用户操作（带结果）
	LogUserOperationWithResult(ctx context.Context, operatorID string, targetUserID *string, action UserAuditAction, details map[string]interface{}, result, ipAddress, userAgent string) error

	// GetAuditLogs 获取审计日志
	GetAuditLogs(filter AuditLogFilter, pagination Pagination) (*AuditLogListResult, error)
}

// AuditLogFilter 审计日志过滤条件
type AuditLogFilter struct {
	OperatorID   string          `json:"operator_id,omitempty"`
	TargetUserID string          `json:"target_user_id,omitempty"`
	Action       UserAuditAction `json:"action,omitempty"`
	StartTime    *time.Time      `json:"start_time,omitempty"`
	EndTime      *time.Time      `json:"end_time,omitempty"`
}

// AuditLogListResult 审计日志列表结果
type AuditLogListResult struct {
	Items      []*UserAuditLog `json:"items"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	PageSize   int             `json:"page_size"`
	TotalPages int             `json:"total_pages"`
}

// DefaultUserAuditService 默认用户审计服务实现
type DefaultUserAuditService struct {
	repository UserAuditRepository
	logger     *zap.Logger
}

// NewDefaultUserAuditService 创建默认用户审计服务
func NewDefaultUserAuditService(repository UserAuditRepository, logger *zap.Logger) UserAuditService {
	return &DefaultUserAuditService{
		repository: repository,
		logger:     logger,
	}
}

// LogUserOperation 记录用户操作
func (s *DefaultUserAuditService) LogUserOperation(ctx context.Context, operatorID string, targetUserID *string, action UserAuditAction, details map[string]interface{}, ipAddress, userAgent string) error {
	return s.LogUserOperationWithResult(ctx, operatorID, targetUserID, action, details, "success", ipAddress, userAgent)
}

// LogUserOperationWithResult 记录用户操作（带结果）
func (s *DefaultUserAuditService) LogUserOperationWithResult(ctx context.Context, operatorID string, targetUserID *string, action UserAuditAction, details map[string]interface{}, result, ipAddress, userAgent string) error {
	if details == nil {
		details = make(map[string]interface{})
	}

	log := &UserAuditLog{
		OperatorID:   operatorID,
		TargetUserID: targetUserID,
		Action:       action,
		Details:      details,
		Result:       result,
		IPAddress:    ipAddress,
		UserAgent:    userAgent,
	}

	return s.repository.Create(log)
}

// GetAuditLogs 获取审计日志
func (s *DefaultUserAuditService) GetAuditLogs(filter AuditLogFilter, pagination Pagination) (*AuditLogListResult, error) {
	// 设置默认分页参数
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.PageSize <= 0 {
		pagination.PageSize = 20
	}

	offset := (pagination.Page - 1) * pagination.PageSize

	var logs []*UserAuditLog
	var total int64
	var err error

	// 根据过滤条件查询
	if filter.OperatorID != "" {
		logs, total, err = s.repository.FindByOperator(filter.OperatorID, pagination.PageSize, offset)
	} else if filter.TargetUserID != "" {
		logs, total, err = s.repository.FindByTargetUser(filter.TargetUserID, pagination.PageSize, offset)
	} else if filter.Action != "" {
		logs, total, err = s.repository.FindByAction(filter.Action, pagination.PageSize, offset)
	} else if filter.StartTime != nil && filter.EndTime != nil {
		logs, total, err = s.repository.FindByTimeRange(*filter.StartTime, *filter.EndTime, pagination.PageSize, offset)
	} else {
		// 默认查询最近的日志
		endTime := time.Now()
		startTime := endTime.AddDate(0, 0, -30) // 最近30天
		logs, total, err = s.repository.FindByTimeRange(startTime, endTime, pagination.PageSize, offset)
	}

	if err != nil {
		s.logger.Error("Failed to get audit logs", zap.Error(err))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取审计日志失败",
			err,
		)
	}

	// 计算总页数
	totalPages := int((total + int64(pagination.PageSize) - 1) / int64(pagination.PageSize))

	result := &AuditLogListResult{
		Items:      logs,
		Total:      total,
		Page:       pagination.Page,
		PageSize:   pagination.PageSize,
		TotalPages: totalPages,
	}

	return result, nil
}
