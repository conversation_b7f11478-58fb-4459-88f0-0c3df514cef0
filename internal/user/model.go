package user

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

var (
	// ErrUserNotFound 用户未找到错误
	ErrUserNotFound = errors.New("user not found")

	// ErrUserAlreadyExists 用户已存在错误
	ErrUserAlreadyExists = errors.New("user already exists")

	// ErrInvalidCredentials 无效的凭证错误
	ErrInvalidCredentials = errors.New("invalid credentials")

	// ErrRoleAssignmentFailed 角色分配失败错误
	ErrRoleAssignmentFailed = errors.New("role assignment failed")

	// ErrPermissionDenied 权限被拒绝错误
	ErrPermissionDenied = errors.New("permission denied")
)

// User 表示系统用户
type User struct {
	ID            string     `json:"id"`
	Username      string     `json:"username"`
	Email         string     `json:"email"`
	PasswordHash  string     `json:"-"`
	CallbackURL   string     `json:"callback_url"`
	ClientID      string     `json:"client_id"`
	IsActive      bool       `json:"is_active"`
	DefaultRoleID string     `json:"default_role_id"`
	Roles         []string   `json:"roles,omitempty"` // 角色ID列表
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	DeletedAt     *time.Time `json:"deleted_at,omitempty"` // 软删除时间
}

// NewUser 创建新用户
func NewUser(username, email, passwordHash string) *User {
	return &User{
		ID:           uuid.New().String(),
		Username:     username,
		Email:        email,
		PasswordHash: passwordHash,
		IsActive:     true,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
}

// HasRole 检查用户是否拥有指定角色
func (u *User) HasRole(roleID string) bool {
	if u.DefaultRoleID == roleID {
		return true
	}
	for _, r := range u.Roles {
		if r == roleID {
			return true
		}
	}
	return false
}

// UserRepository 用户存储接口
type UserRepository interface {
	// Create 创建新用户
	Create(user *User) error

	// FindByID 根据ID查找用户
	FindByID(id string) (*User, error)

	// FindByUsername 根据用户名查找用户
	FindByUsername(username string) (*User, error)

	// FindByEmail 根据邮箱查找用户
	FindByEmail(email string) (*User, error)

	// FindByClientID 根据客户端ID查找用户
	FindByClientID(clientID string) (*User, error)

	// Update 更新用户信息
	Update(user *User) error

	// Delete 删除用户
	Delete(id string) error

	// AddRole 为用户添加角色
	AddRole(userID, roleID string) error

	// RemoveRole 从用户中移除角色
	RemoveRole(userID, roleID string) error

	// SetDefaultRole 设置用户的默认角色
	SetDefaultRole(userID, roleID string) error

	// FindRoles 查找用户的所有角色
	FindRoles(userID string) ([]string, error)

	// FindByRole 查找拥有指定角色的所有用户
	FindByRole(roleID string) ([]*User, error)

	// HasPermission 检查用户是否拥有指定权限
	HasPermission(userID, permissionID string) (bool, error)
}

// AdminUserRepository 管理员用户存储库接口
type AdminUserRepository interface {
	UserRepository

	// FindWithFilter 根据过滤条件查找用户（分页）
	FindWithFilter(filter UserFilter, pagination Pagination) ([]*UserWithRoles, int64, error)

	// GetUserStatistics 获取用户统计信息
	GetUserStatistics() (*UserStatistics, error)

	// SoftDelete 软删除用户
	SoftDelete(userID string) error

	// UpdatePassword 更新用户密码
	UpdatePassword(userID, passwordHash string) error

	// GetUserWithRoles 获取包含角色信息的用户
	GetUserWithRoles(userID string) (*UserWithRoles, error)

	// BatchUpdateStatus 批量更新用户状态
	BatchUpdateStatus(userIDs []string, isActive bool) error

	// ForceDelete 永久删除用户（硬删除）
	ForceDelete(userID string) error
}
