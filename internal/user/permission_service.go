package user

import (
	"errors"
)

// PermissionService 权限服务接口
type PermissionService interface {
	// CreatePermission 创建新权限
	CreatePermission(name, description, resource, action string, isSystem bool) (*Permission, error)

	// GetPermissionByID 根据ID获取权限
	GetPermissionByID(id string) (*Permission, error)

	// GetPermissionByName 根据名称获取权限
	GetPermissionByName(name string) (*Permission, error)

	// GetPermissionByResourceAction 根据资源和操作获取权限
	GetPermissionByResourceAction(resource, action string) (*Permission, error)

	// GetAllPermissions 获取所有权限
	GetAllPermissions() ([]*Permission, error)

	// UpdatePermission 更新权限信息
	UpdatePermission(permission *Permission) error

	// DeletePermission 删除权限
	DeletePermission(id string) error

	// GetPermissionsByRole 获取角色的所有权限
	GetPermissionsByRole(roleID string) ([]*Permission, error)
}

// DefaultPermissionService 默认权限服务实现
type DefaultPermissionService struct {
	permissionRepository PermissionRepository
}

// NewPermissionService 创建新的权限服务
func NewPermissionService(permissionRepository PermissionRepository) PermissionService {
	return &DefaultPermissionService{
		permissionRepository: permissionRepository,
	}
}

// CreatePermission 创建新权限
func (s *DefaultPermissionService) CreatePermission(name, description, resource, action string, isSystem bool) (*Permission, error) {
	// 验证权限名
	if err := validatePermissionName(name); err != nil {
		return nil, err
	}

	// 验证资源和操作
	if err := validateResourceAction(resource, action); err != nil {
		return nil, err
	}

	// 创建权限
	permission := NewPermission(name, description, resource, action, isSystem)

	// 保存权限
	if err := s.permissionRepository.Create(permission); err != nil {
		return nil, err
	}

	return permission, nil
}

// GetPermissionByID 根据ID获取权限
func (s *DefaultPermissionService) GetPermissionByID(id string) (*Permission, error) {
	return s.permissionRepository.FindByID(id)
}

// GetPermissionByName 根据名称获取权限
func (s *DefaultPermissionService) GetPermissionByName(name string) (*Permission, error) {
	return s.permissionRepository.FindByName(name)
}

// GetPermissionByResourceAction 根据资源和操作获取权限
func (s *DefaultPermissionService) GetPermissionByResourceAction(resource, action string) (*Permission, error) {
	return s.permissionRepository.FindByResourceAction(resource, action)
}

// GetAllPermissions 获取所有权限
func (s *DefaultPermissionService) GetAllPermissions() ([]*Permission, error) {
	return s.permissionRepository.FindAll()
}

// UpdatePermission 更新权限信息
func (s *DefaultPermissionService) UpdatePermission(permission *Permission) error {
	// 验证权限名
	if err := validatePermissionName(permission.Name); err != nil {
		return err
	}

	// 验证资源和操作
	if err := validateResourceAction(permission.Resource, permission.Action); err != nil {
		return err
	}

	// 检查是否是系统权限
	existingPermission, err := s.permissionRepository.FindByID(permission.ID)
	if err != nil {
		return err
	}
	if existingPermission.IsSystem && (existingPermission.Name != permission.Name ||
		existingPermission.Resource != permission.Resource ||
		existingPermission.Action != permission.Action ||
		!permission.IsSystem) {
		return ErrSystemPermissionCannotBeModified
	}

	return s.permissionRepository.Update(permission)
}

// DeletePermission 删除权限
func (s *DefaultPermissionService) DeletePermission(id string) error {
	// 检查是否是系统权限
	permission, err := s.permissionRepository.FindByID(id)
	if err != nil {
		return err
	}
	if permission.IsSystem {
		return ErrSystemPermissionCannotBeDeleted
	}

	return s.permissionRepository.Delete(id)
}

// GetPermissionsByRole 获取角色的所有权限
func (s *DefaultPermissionService) GetPermissionsByRole(roleID string) ([]*Permission, error) {
	return s.permissionRepository.FindByRole(roleID)
}

// 验证权限名
func validatePermissionName(name string) error {
	if name == "" {
		return errors.New("权限名不能为空")
	}
	if len(name) < 2 {
		return errors.New("权限名长度不能小于2个字符")
	}
	if len(name) > 100 {
		return errors.New("权限名长度不能超过100个字符")
	}
	return nil
}

// 验证资源和操作
func validateResourceAction(resource, action string) error {
	if resource == "" {
		return errors.New("资源不能为空")
	}
	if len(resource) > 50 {
		return errors.New("资源长度不能超过50个字符")
	}
	if action == "" {
		return errors.New("操作不能为空")
	}
	if len(action) > 50 {
		return errors.New("操作长度不能超过50个字符")
	}
	return nil
}
