package user

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

var (
	// ErrPermissionNotFound 权限未找到错误
	ErrPermissionNotFound = errors.New("permission not found")

	// ErrPermissionAlreadyExists 权限已存在错误
	ErrPermissionAlreadyExists = errors.New("permission already exists")

	// ErrSystemPermissionCannotBeModified 系统权限不能被修改错误
	ErrSystemPermissionCannotBeModified = errors.New("system permission cannot be modified")

	// ErrSystemPermissionCannotBeDeleted 系统权限不能被删除错误
	ErrSystemPermissionCannotBeDeleted = errors.New("system permission cannot be deleted")
)

// Permission 表示系统权限
type Permission struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Resource    string    `json:"resource"` // 资源，如user, role, express_price等
	Action      string    `json:"action"`   // 操作，如create, read, update, delete等
	IsSystem    bool      `json:"is_system"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// NewPermission 创建新权限
func NewPermission(name, description, resource, action string, isSystem bool) *Permission {
	return &Permission{
		ID:          uuid.New().String(),
		Name:        name,
		Description: description,
		Resource:    resource,
		Action:      action,
		IsSystem:    isSystem,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

// PermissionRepository 权限存储接口
type PermissionRepository interface {
	// Create 创建新权限
	Create(permission *Permission) error

	// FindByID 根据ID查找权限
	FindByID(id string) (*Permission, error)

	// FindByName 根据名称查找权限
	FindByName(name string) (*Permission, error)

	// FindByResourceAction 根据资源和操作查找权限
	FindByResourceAction(resource, action string) (*Permission, error)

	// FindAll 查找所有权限
	FindAll() ([]*Permission, error)

	// Update 更新权限信息
	Update(permission *Permission) error

	// Delete 删除权限
	Delete(id string) error

	// FindByRole 查找角色的所有权限
	FindByRole(roleID string) ([]*Permission, error)
}
