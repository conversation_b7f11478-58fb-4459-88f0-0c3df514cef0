package user

import (
	"context"
	"slices"

	"github.com/your-org/go-kuaidi/internal/auth"
	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/errors"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

// DefaultAdminUserService 默认管理员用户服务实现
type DefaultAdminUserService struct {
	*DefaultUserService
	adminRepository AdminUserRepository
	auditService    UserAuditService
	configManager   *config.ConfigManager
	logger          *zap.Logger
}

// NewDefaultAdminUserService 创建默认管理员用户服务
func NewDefaultAdminUserService(
	userService *DefaultUserService,
	adminRepository AdminUserRepository,
	auditService UserAuditService,
	configManager *config.ConfigManager,
	logger *zap.Logger,
) AdminUserService {
	return &DefaultAdminUserService{
		DefaultUserService: userService,
		adminRepository:    adminRepository,
		auditService:       auditService,
		configManager:      configManager,
		logger:             logger,
	}
}

// GetUsers 获取用户列表（分页）
func (s *DefaultAdminUserService) GetUsers(filter UserFilter, pagination Pagination) (*UserListResult, error) {
	// 获取配置
	config := s.configManager.GetUserManagementConfig()

	// 先设置默认值
	s.setPaginationDefaults(&pagination, config)

	// 然后验证分页参数
	if err := s.validatePaginationWithConfig(pagination, config); err != nil {
		s.logger.Warn("Invalid pagination parameters", zap.Error(err))
		return nil, err
	}

	// 验证过滤条件
	if err := s.validateUserFilterWithConfig(filter, config); err != nil {
		s.logger.Warn("Invalid filter parameters", zap.Error(err))
		return nil, err
	}

	// 查询用户列表
	users, total, err := s.adminRepository.FindWithFilter(filter, pagination)
	if err != nil {
		s.logger.Error("Failed to get users with filter",
			zap.Error(err),
			zap.Any("filter", filter),
			zap.Any("pagination", pagination))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取用户列表失败",
			err,
		)
	}

	// 计算总页数
	totalPages := pagination.CalculateTotalPages(total)

	result := &UserListResult{
		Items:      users,
		Total:      total,
		Page:       pagination.Page,
		PageSize:   pagination.PageSize,
		TotalPages: totalPages,
	}

	s.logger.Info("Successfully retrieved users",
		zap.Int64("total", total),
		zap.Int("page", pagination.Page),
		zap.Int("page_size", pagination.PageSize))

	return result, nil
}

// CreateUser 管理员创建用户
func (s *DefaultAdminUserService) CreateUser(req CreateUserRequest) (*User, error) {
	// 获取配置并验证请求参数
	config := s.configManager.GetUserManagementConfig()
	if err := s.validateCreateUserRequestWithConfig(req, config); err != nil {
		s.logger.Warn("Invalid create user request", zap.Error(err))
		return nil, err
	}

	// 检查用户名是否已存在
	if existingUser, err := s.repository.FindByUsername(req.Username); err == nil && existingUser != nil {
		return nil, errors.NewBusinessError(
			errors.ErrCodeConflict,
			"用户名已存在",
		)
	} else if err != nil && err != ErrUserNotFound {
		s.logger.Error("Failed to check username existence", zap.Error(err))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"检查用户名失败",
			err,
		)
	}

	// 检查邮箱是否已存在
	if existingUser, err := s.repository.FindByEmail(req.Email); err == nil && existingUser != nil {
		return nil, errors.NewBusinessError(
			errors.ErrCodeConflict,
			"邮箱已存在",
		)
	} else if err != nil && err != ErrUserNotFound {
		s.logger.Error("Failed to check email existence", zap.Error(err))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"检查邮箱失败",
			err,
		)
	}

	// 生成密码哈希
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		s.logger.Error("Failed to hash password", zap.Error(err))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"密码加密失败",
			err,
		)
	}

	// 为用户创建客户端凭证
	clientName := req.Username + "'s API Client"
	allowedScopes := []string{"express:read", "express:write"}

	clientInterface, _, err := s.clientService.GenerateClientCredentials(clientName, allowedScopes)
	if err != nil {
		s.logger.Error("Failed to generate client credentials", zap.Error(err))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"生成客户端凭证失败",
			err,
		)
	}

	// 类型断言
	client, ok := clientInterface.(*auth.Client)
	if !ok {
		s.logger.Error("Invalid client type returned from client service")
		return nil, errors.NewBusinessError(
			errors.ErrCodeInternal,
			"无效的客户端类型",
		)
	}

	// 创建用户
	user := &User{
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: string(passwordHash),
		ClientID:     client.ID,
		IsActive:     true,
	}

	// 如果请求中指定了激活状态，使用指定的状态
	if req.IsActive != nil {
		user.IsActive = *req.IsActive
	}

	// 保存用户
	if err := s.repository.Create(user); err != nil {
		s.logger.Error("Failed to create user",
			zap.Error(err),
			zap.String("username", req.Username),
			zap.String("email", req.Email))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"创建用户失败",
			err,
		)
	}

	// 设置用户角色
	roleID := req.RoleID
	if roleID == "" {
		// 从配置中获取默认角色
		if defaultRole, exists := s.configManager.GetRole("user"); exists {
			roleID = defaultRole.ID
		} else {
			roleID = "user" // 回退到硬编码值
		}
	}

	if err := s.repository.SetDefaultRole(user.ID, roleID); err != nil {
		s.logger.Warn("Failed to set default role for user",
			zap.Error(err),
			zap.String("user_id", user.ID),
			zap.String("role_id", roleID))
		// 不中断流程，只记录警告
	}

	s.logger.Info("Successfully created user",
		zap.String("user_id", user.ID),
		zap.String("username", user.Username),
		zap.String("email", user.Email),
		zap.String("role_id", roleID))

	return user, nil
}

// UpdateUserByAdmin 管理员更新用户
func (s *DefaultAdminUserService) UpdateUserByAdmin(userID string, req UpdateUserRequest) (*User, error) {
	// 验证请求参数
	if err := ValidateUpdateUserRequest(req); err != nil {
		s.logger.Warn("Invalid update user request", zap.Error(err))
		return nil, err
	}

	// 获取现有用户
	existingUser, err := s.repository.FindByID(userID)
	if err != nil {
		if err == ErrUserNotFound {
			return nil, errors.NewBusinessError(
				errors.ErrCodeNotFound,
				"用户不存在",
			)
		}
		s.logger.Error("Failed to get user", zap.Error(err), zap.String("user_id", userID))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取用户信息失败",
			err,
		)
	}

	// 检查用户名冲突（如果要更新用户名）
	if req.Username != "" && req.Username != existingUser.Username {
		if conflictUser, err := s.repository.FindByUsername(req.Username); err == nil && conflictUser != nil {
			return nil, errors.NewBusinessError(
				errors.ErrCodeConflict,
				"用户名已存在",
			)
		} else if err != nil && err != ErrUserNotFound {
			s.logger.Error("Failed to check username conflict", zap.Error(err))
			return nil, errors.NewBusinessErrorWithCause(
				errors.ErrCodeInternal,
				"检查用户名冲突失败",
				err,
			)
		}
		existingUser.Username = req.Username
	}

	// 检查邮箱冲突（如果要更新邮箱）
	if req.Email != "" && req.Email != existingUser.Email {
		if conflictUser, err := s.repository.FindByEmail(req.Email); err == nil && conflictUser != nil {
			return nil, errors.NewBusinessError(
				errors.ErrCodeConflict,
				"邮箱已存在",
			)
		} else if err != nil && err != ErrUserNotFound {
			s.logger.Error("Failed to check email conflict", zap.Error(err))
			return nil, errors.NewBusinessErrorWithCause(
				errors.ErrCodeInternal,
				"检查邮箱冲突失败",
				err,
			)
		}
		existingUser.Email = req.Email
	}

	// 更新激活状态
	if req.IsActive != nil {
		existingUser.IsActive = *req.IsActive
	}

	// 保存更新
	if err := s.repository.Update(existingUser); err != nil {
		s.logger.Error("Failed to update user",
			zap.Error(err),
			zap.String("user_id", userID))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"更新用户失败",
			err,
		)
	}

	s.logger.Info("Successfully updated user",
		zap.String("user_id", userID),
		zap.String("username", existingUser.Username))

	return existingUser, nil
}

// UpdateUserStatus 更新用户状态
func (s *DefaultAdminUserService) UpdateUserStatus(userID string, isActive bool) error {
	// 获取现有用户
	existingUser, err := s.repository.FindByID(userID)
	if err != nil {
		if err == ErrUserNotFound {
			return errors.NewBusinessError(
				errors.ErrCodeNotFound,
				"用户不存在",
			)
		}
		s.logger.Error("Failed to get user", zap.Error(err), zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取用户信息失败",
			err,
		)
	}

	// 更新状态
	existingUser.IsActive = isActive

	// 保存更新
	if err := s.repository.Update(existingUser); err != nil {
		s.logger.Error("Failed to update user status",
			zap.Error(err),
			zap.String("user_id", userID),
			zap.Bool("is_active", isActive))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"更新用户状态失败",
			err,
		)
	}

	s.logger.Info("Successfully updated user status",
		zap.String("user_id", userID),
		zap.Bool("is_active", isActive))

	return nil
}

// ResetUserPassword 管理员重置用户密码
func (s *DefaultAdminUserService) ResetUserPassword(userID, newPassword string) error {
	// 获取配置并验证密码
	config := s.configManager.GetUserManagementConfig()
	if err := s.validatePasswordWithConfig(newPassword, config); err != nil {
		s.logger.Warn("Invalid password", zap.Error(err))
		return err
	}

	// 检查用户是否存在
	_, err := s.repository.FindByID(userID)
	if err != nil {
		if err == ErrUserNotFound {
			return errors.NewBusinessError(
				errors.ErrCodeNotFound,
				"用户不存在",
			)
		}
		s.logger.Error("Failed to get user", zap.Error(err), zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取用户信息失败",
			err,
		)
	}

	// 生成密码哈希
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		s.logger.Error("Failed to hash password", zap.Error(err))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"密码加密失败",
			err,
		)
	}

	// 更新密码
	if err := s.adminRepository.UpdatePassword(userID, string(passwordHash)); err != nil {
		s.logger.Error("Failed to update password",
			zap.Error(err),
			zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"更新密码失败",
			err,
		)
	}

	s.logger.Info("Successfully reset user password",
		zap.String("user_id", userID))

	return nil
}

// DeleteUser 删除用户（软删除）
func (s *DefaultAdminUserService) DeleteUser(userID string) error {
	// 检查用户是否存在
	existingUser, err := s.repository.FindByID(userID)
	if err != nil {
		if err == ErrUserNotFound {
			return errors.NewBusinessError(
				errors.ErrCodeNotFound,
				"用户不存在",
			)
		}
		s.logger.Error("Failed to get user", zap.Error(err), zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取用户信息失败",
			err,
		)
	}

	// 检查是否为系统管理员
	roleIDs, err := s.repository.FindRoles(userID)
	if err != nil {
		s.logger.Error("Failed to get user roles", zap.Error(err), zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取用户角色失败",
			err,
		)
	}

	// 防止删除管理员用户
	adminRoleID := "admin"
	if adminRole, exists := s.configManager.GetRole("admin"); exists {
		adminRoleID = adminRole.ID
	}

	if slices.Contains(roleIDs, adminRoleID) {
		return errors.NewBusinessError(
			errors.ErrCodeForbidden,
			s.configManager.GetErrorMessage("cannot_delete_admin", "zh-CN"),
		)
	}

	// 执行软删除
	if err := s.adminRepository.SoftDelete(userID); err != nil {
		s.logger.Error("Failed to soft delete user",
			zap.Error(err),
			zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"删除用户失败",
			err,
		)
	}

	s.logger.Info("Successfully deleted user",
		zap.String("user_id", userID),
		zap.String("username", existingUser.Username))

	return nil
}

// ForceDeleteUser 永久删除用户（硬删除）
func (s *DefaultAdminUserService) ForceDeleteUser(userID string) error {
	// 检查用户是否存在
	existingUser, err := s.repository.FindByID(userID)
	if err != nil {
		if err == ErrUserNotFound {
			return errors.NewBusinessError(
				errors.ErrCodeNotFound,
				"用户不存在",
			)
		}
		s.logger.Error("Failed to get user", zap.Error(err), zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取用户信息失败",
			err,
		)
	}

	// 检查是否为系统管理员
	roleIDs, err := s.repository.FindRoles(userID)
	if err != nil {
		s.logger.Error("Failed to get user roles", zap.Error(err), zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取用户角色失败",
			err,
		)
	}

	// 防止删除管理员用户
	adminRoleID := "admin"
	if adminRole, exists := s.configManager.GetRole("admin"); exists {
		adminRoleID = adminRole.ID
	}

	if slices.Contains(roleIDs, adminRoleID) {
		return errors.NewBusinessError(
			errors.ErrCodeForbidden,
			s.configManager.GetErrorMessage("cannot_delete_admin", "zh-CN"),
		)
	}

	// 执行硬删除
	if err := s.adminRepository.ForceDelete(userID); err != nil {
		s.logger.Error("Failed to force delete user",
			zap.Error(err),
			zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"永久删除用户失败",
			err,
		)
	}

	s.logger.Info("Successfully force deleted user",
		zap.String("user_id", userID),
		zap.String("username", existingUser.Username))

	return nil
}

// GetUserStatistics 获取用户统计信息
func (s *DefaultAdminUserService) GetUserStatistics() (*UserStatistics, error) {
	stats, err := s.adminRepository.GetUserStatistics()
	if err != nil {
		s.logger.Error("Failed to get user statistics", zap.Error(err))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取用户统计信息失败",
			err,
		)
	}

	s.logger.Info("Successfully retrieved user statistics",
		zap.Int64("total_users", stats.TotalUsers),
		zap.Int64("active_users", stats.ActiveUsers))

	return stats, nil
}

// BatchUpdateStatus 批量更新用户状态
func (s *DefaultAdminUserService) BatchUpdateStatus(userIDs []string, isActive bool) error {
	// 获取配置
	config := s.configManager.GetUserManagementConfig()

	// 验证用户ID列表
	if len(userIDs) == 0 {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("user_ids_empty", "zh-CN"),
		)
	}

	if len(userIDs) > config.MaxBatchSize {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("batch_size_exceeded", "zh-CN"),
		)
	}

	// 执行批量更新
	err := s.adminRepository.BatchUpdateStatus(userIDs, isActive)
	if err != nil {
		s.logger.Error("Failed to batch update user status",
			zap.Error(err),
			zap.Strings("user_ids", userIDs),
			zap.Bool("is_active", isActive))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"批量更新用户状态失败",
			err,
		)
	}

	s.logger.Info("Successfully batch updated user status",
		zap.Strings("user_ids", userIDs),
		zap.Bool("is_active", isActive),
		zap.Int("count", len(userIDs)))

	return nil
}

// validatePaginationWithConfig 使用配置验证分页参数
func (s *DefaultAdminUserService) validatePaginationWithConfig(pagination Pagination, config *config.UserManagementConfig) error {
	if pagination.Page < 1 {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("invalid_page", "zh-CN"),
		)
	}

	if pagination.PageSize < 1 {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("invalid_page_size", "zh-CN"),
		)
	}

	return nil
}

// validateUserFilterWithConfig 使用配置验证用户过滤条件
func (s *DefaultAdminUserService) validateUserFilterWithConfig(filter UserFilter, _ *config.UserManagementConfig) error {
	// 验证关键词长度
	if len(filter.Keyword) > 100 {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("keyword_too_long", "zh-CN"),
		)
	}

	// 验证时间范围
	if filter.CreatedAfter != nil && filter.CreatedBefore != nil {
		if filter.CreatedAfter.After(*filter.CreatedBefore) {
			return errors.NewBusinessError(
				errors.ErrCodeInvalidRequest,
				s.configManager.GetErrorMessage("invalid_time_range", "zh-CN"),
			)
		}
	}

	if filter.LastLoginAfter != nil && filter.LastLoginBefore != nil {
		if filter.LastLoginAfter.After(*filter.LastLoginBefore) {
			return errors.NewBusinessError(
				errors.ErrCodeInvalidRequest,
				s.configManager.GetErrorMessage("invalid_time_range", "zh-CN"),
			)
		}
	}

	return nil
}

// setPaginationDefaults 使用配置设置分页默认值
func (s *DefaultAdminUserService) setPaginationDefaults(pagination *Pagination, config *config.UserManagementConfig) {
	if pagination.Page == 0 {
		pagination.Page = 1
	}

	if pagination.PageSize == 0 {
		pagination.PageSize = config.DefaultPageSize
	}

	if pagination.OrderBy == "" {
		pagination.OrderBy = "created_at"
	}

	if pagination.Order == "" {
		pagination.Order = "desc"
	}

	// 仅当 MaxPageSize > 0 时启用裁剪逻辑；若配置缺失或被错误覆盖为 0，则视为"不限制"
	if config.MaxPageSize > 0 && pagination.PageSize > config.MaxPageSize {
		s.logger.Warn("page_size exceeds MaxPageSize, auto-trim",
			zap.Int("requested", pagination.PageSize),
			zap.Int("max", config.MaxPageSize))
		pagination.PageSize = config.MaxPageSize
	}
}

// validatePasswordWithConfig 使用配置验证密码
func (s *DefaultAdminUserService) validatePasswordWithConfig(password string, config *config.UserManagementConfig) error {
	if len(password) < config.PasswordMinLength {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("password_too_short", "zh-CN"),
		)
	}

	if len(password) > config.PasswordMaxLength {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("password_too_long", "zh-CN"),
		)
	}

	// 检查密码复杂度要求
	if config.RequireUppercase && !containsUppercase(password) {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("password_require_uppercase", "zh-CN"),
		)
	}

	if config.RequireLowercase && !containsLowercase(password) {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("password_require_lowercase", "zh-CN"),
		)
	}

	if config.RequireNumbers && !containsNumbers(password) {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("password_require_numbers", "zh-CN"),
		)
	}

	if config.RequireSpecial && !containsSpecialChars(password) {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			s.configManager.GetErrorMessage("password_require_special", "zh-CN"),
		)
	}

	return nil
}

// validateCreateUserRequestWithConfig 使用配置验证创建用户请求
func (s *DefaultAdminUserService) validateCreateUserRequestWithConfig(req CreateUserRequest, config *config.UserManagementConfig) error {
	// 验证用户名
	if err := validateNonEmptyString(req.Username, "用户名"); err != nil {
		return err
	}

	minLen := config.UsernameMinLength
	if minLen <= 0 {
		minLen = 3
	}
	maxLen := config.UsernameMaxLength
	if maxLen <= 0 {
		maxLen = 50
	}

	if err := validateStringLength(req.Username, "用户名", minLen, maxLen); err != nil {
		return err
	}

	// 使用验证服务验证用户名
	if s.validationService != nil {
		if err := s.validationService.ValidateUsername(req.Username); err != nil {
			return err
		}
	} else {
		// 回退到硬编码验证
		if err := validateUsername(req.Username); err != nil {
			return err
		}
	}

	// 验证邮箱
	if err := validateNonEmptyString(req.Email, "邮箱"); err != nil {
		return err
	}

	// 使用验证服务验证邮箱
	if s.validationService != nil {
		if err := s.validationService.ValidateEmail(req.Email); err != nil {
			return err
		}
	} else {
		// 回退到硬编码验证
		if err := validateEmail(req.Email); err != nil {
			return err
		}
	}

	// 验证密码
	if err := validateNonEmptyString(req.Password, "密码"); err != nil {
		return err
	}

	// 使用验证服务验证密码
	if s.validationService != nil {
		if err := s.validationService.ValidatePassword(req.Password); err != nil {
			return err
		}
	} else {
		// 回退到配置验证
		if err := s.validatePasswordWithConfig(req.Password, config); err != nil {
			return err
		}
	}

	// 验证角色ID（如果提供）
	if req.RoleID != "" {
		if _, exists := s.configManager.GetRole(req.RoleID); !exists {
			return errors.NewBusinessError(
				errors.ErrCodeInvalidRequest,
				s.configManager.GetErrorMessage("invalid_role_id", "zh-CN"),
			)
		}
	}

	return nil
}

// CreateUserWithAudit 管理员创建用户（带审计）
func (s *DefaultAdminUserService) CreateUserWithAudit(ctx context.Context, operatorID string, req CreateUserRequest, ipAddress, userAgent string) (*User, error) {
	// 调用原有的创建用户方法
	user, err := s.CreateUser(req)
	if err != nil {
		// 记录失败的审计日志
		details := map[string]interface{}{
			"username": req.Username,
			"email":    req.Email,
			"error":    err.Error(),
		}
		if auditErr := s.auditService.LogUserOperationWithResult(ctx, operatorID, nil, AuditActionUserCreate, details, "failure", ipAddress, userAgent); auditErr != nil {
			s.logger.Warn("Failed to log audit for failed user creation", zap.Error(auditErr))
		}
		return nil, err
	}

	// 记录成功的审计日志
	details := map[string]interface{}{
		"username":  user.Username,
		"email":     user.Email,
		"is_active": user.IsActive,
		"role_id":   req.RoleID,
	}
	if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &user.ID, AuditActionUserCreate, details, ipAddress, userAgent); auditErr != nil {
		s.logger.Warn("Failed to log audit for user creation", zap.Error(auditErr))
	}

	return user, nil
}

// UpdateUserByAdminWithAudit 管理员更新用户（带审计）
func (s *DefaultAdminUserService) UpdateUserByAdminWithAudit(ctx context.Context, operatorID, userID string, req UpdateUserRequest, ipAddress, userAgent string) (*User, error) {
	// 获取更新前的用户信息
	oldUser, err := s.repository.FindByID(userID)
	if err != nil {
		return nil, err
	}

	// 调用原有的更新用户方法
	user, err := s.UpdateUserByAdmin(userID, req)
	if err != nil {
		// 记录失败的审计日志
		details := map[string]interface{}{
			"target_user_id": userID,
			"changes":        req,
			"error":          err.Error(),
		}
		if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionUserUpdate, details, ipAddress, userAgent); auditErr != nil {
			s.logger.Warn("Failed to log audit for failed user update", zap.Error(auditErr))
		}
		return nil, err
	}

	// 记录成功的审计日志
	details := map[string]interface{}{
		"target_user_id": userID,
		"old_values": map[string]interface{}{
			"username":  oldUser.Username,
			"email":     oldUser.Email,
			"is_active": oldUser.IsActive,
		},
		"new_values": map[string]interface{}{
			"username":  user.Username,
			"email":     user.Email,
			"is_active": user.IsActive,
		},
		"changes": req,
	}
	if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionUserUpdate, details, ipAddress, userAgent); auditErr != nil {
		s.logger.Warn("Failed to log audit for user update", zap.Error(auditErr))
	}

	return user, nil
}

// UpdateUserStatusWithAudit 更新用户状态（带审计）
func (s *DefaultAdminUserService) UpdateUserStatusWithAudit(ctx context.Context, operatorID, userID string, isActive bool, ipAddress, userAgent string) error {
	// 获取更新前的用户信息
	oldUser, err := s.repository.FindByID(userID)
	if err != nil {
		return err
	}

	// 调用原有的更新状态方法
	err = s.UpdateUserStatus(userID, isActive)
	if err != nil {
		// 记录失败的审计日志
		details := map[string]interface{}{
			"target_user_id": userID,
			"old_status":     oldUser.IsActive,
			"new_status":     isActive,
			"error":          err.Error(),
		}
		if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionUserStatusUpdate, details, ipAddress, userAgent); auditErr != nil {
			s.logger.Warn("Failed to log audit for failed status update", zap.Error(auditErr))
		}
		return err
	}

	// 记录成功的审计日志
	details := map[string]interface{}{
		"target_user_id": userID,
		"old_status":     oldUser.IsActive,
		"new_status":     isActive,
	}
	if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionUserStatusUpdate, details, ipAddress, userAgent); auditErr != nil {
		s.logger.Warn("Failed to log audit for status update", zap.Error(auditErr))
	}

	return nil
}

// ResetUserPasswordWithAudit 管理员重置用户密码（带审计）
func (s *DefaultAdminUserService) ResetUserPasswordWithAudit(ctx context.Context, operatorID, userID, newPassword string, ipAddress, userAgent string) error {
	// 调用原有的重置密码方法
	err := s.ResetUserPassword(userID, newPassword)
	if err != nil {
		// 记录失败的审计日志
		details := map[string]interface{}{
			"target_user_id": userID,
			"error":          err.Error(),
		}
		if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionPasswordReset, details, ipAddress, userAgent); auditErr != nil {
			s.logger.Warn("Failed to log audit for failed password reset", zap.Error(auditErr))
		}
		return err
	}

	// 记录成功的审计日志
	details := map[string]interface{}{
		"target_user_id": userID,
	}
	if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionPasswordReset, details, ipAddress, userAgent); auditErr != nil {
		s.logger.Warn("Failed to log audit for password reset", zap.Error(auditErr))
	}

	return nil
}

// DeleteUserWithAudit 删除用户（带审计）
func (s *DefaultAdminUserService) DeleteUserWithAudit(ctx context.Context, operatorID, userID string, ipAddress, userAgent string) error {
	// 获取删除前的用户信息
	user, err := s.repository.FindByID(userID)
	if err != nil {
		return err
	}

	// 调用原有的删除用户方法
	err = s.DeleteUser(userID)
	if err != nil {
		// 记录失败的审计日志
		details := map[string]interface{}{
			"target_user_id": userID,
			"username":       user.Username,
			"email":          user.Email,
			"error":          err.Error(),
		}
		if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionUserDelete, details, ipAddress, userAgent); auditErr != nil {
			s.logger.Warn("Failed to log audit for failed user deletion", zap.Error(auditErr))
		}
		return err
	}

	// 记录成功的审计日志
	details := map[string]interface{}{
		"target_user_id": userID,
		"username":       user.Username,
		"email":          user.Email,
	}
	if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionUserDelete, details, ipAddress, userAgent); auditErr != nil {
		s.logger.Warn("Failed to log audit for user deletion", zap.Error(auditErr))
	}

	return nil
}

// BatchUpdateStatusWithAudit 批量更新用户状态（带审计）
func (s *DefaultAdminUserService) BatchUpdateStatusWithAudit(ctx context.Context, operatorID string, userIDs []string, isActive bool, ipAddress, userAgent string) error {
	// 调用原有的批量更新方法
	err := s.BatchUpdateStatus(userIDs, isActive)
	if err != nil {
		// 记录失败的审计日志
		details := map[string]interface{}{
			"user_ids":   userIDs,
			"new_status": isActive,
			"count":      len(userIDs),
			"error":      err.Error(),
		}
		if auditErr := s.auditService.LogUserOperation(ctx, operatorID, nil, AuditActionBatchStatusUpdate, details, ipAddress, userAgent); auditErr != nil {
			s.logger.Warn("Failed to log audit for failed batch status update", zap.Error(auditErr))
		}
		return err
	}

	// 记录成功的审计日志
	details := map[string]interface{}{
		"user_ids":   userIDs,
		"new_status": isActive,
		"count":      len(userIDs),
	}
	if auditErr := s.auditService.LogUserOperation(ctx, operatorID, nil, AuditActionBatchStatusUpdate, details, ipAddress, userAgent); auditErr != nil {
		s.logger.Warn("Failed to log audit for batch status update", zap.Error(auditErr))
	}

	return nil
}

// GetAuditLogs 获取审计日志
func (s *DefaultAdminUserService) GetAuditLogs(filter AuditLogFilter, pagination Pagination) (*AuditLogListResult, error) {
	return s.auditService.GetAuditLogs(filter, pagination)
}
