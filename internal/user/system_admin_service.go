package user

import (
	"context"
	"database/sql"

	"github.com/your-org/go-kuaidi/internal/errors"
	"go.uber.org/zap"
)

// SystemAdminService 系统管理员服务接口
type SystemAdminService interface {
	AdminUserService

	// ForceDeleteUser 强制删除用户（硬删除）
	ForceDeleteUser(userID string) error

	// ForceDeleteUserWithAudit 强制删除用户（带审计）
	ForceDeleteUserWithAudit(ctx context.Context, operatorID, userID string, ipAddress, userAgent string) error

	// RestoreUser 恢复已删除用户
	RestoreUser(userID string) error

	// RestoreUserWithAudit 恢复已删除用户（带审计）
	RestoreUserWithAudit(ctx context.Context, operatorID, userID string, ipAddress, userAgent string) error

	// GetDeletedUsers 获取已删除用户列表
	GetDeletedUsers(pagination Pagination) (*UserListResult, error)

	// PurgeOldAuditLogs 清理旧的审计日志
	PurgeOldAuditLogs(olderThanDays int) (int64, error)

	// GetSystemStatistics 获取系统统计信息
	GetSystemStatistics() (*SystemStatistics, error)
}

// SystemStatistics 系统统计信息
type SystemStatistics struct {
	TotalUsers       int64 `json:"total_users"`
	ActiveUsers      int64 `json:"active_users"`
	InactiveUsers    int64 `json:"inactive_users"`
	DeletedUsers     int64 `json:"deleted_users"`
	TotalAuditLogs   int64 `json:"total_audit_logs"`
	RecentOperations int64 `json:"recent_operations"` // 最近24小时的操作数
}

// SystemAdminRepository 系统管理员存储库接口
type SystemAdminRepository interface {
	AdminUserRepository

	// ForceDelete 强制删除用户（硬删除）
	ForceDelete(userID string) error

	// Restore 恢复已删除用户
	Restore(userID string) error

	// FindDeleted 查找已删除用户
	FindDeleted(pagination Pagination) ([]*User, int64, error)

	// GetSystemStatistics 获取系统统计信息
	GetSystemStatistics() (*SystemStatistics, error)

	// PurgeOldAuditLogs 清理旧的审计日志
	PurgeOldAuditLogs(olderThanDays int) (int64, error)
}

// PostgresSystemAdminRepository PostgreSQL系统管理员存储库实现
type PostgresSystemAdminRepository struct {
	*PostgresAdminUserRepository
}

// NewPostgresSystemAdminRepository 创建PostgreSQL系统管理员存储库
func NewPostgresSystemAdminRepository(db *sql.DB, logger *zap.Logger) SystemAdminRepository {
	adminRepo := NewPostgresAdminUserRepository(db, logger).(*PostgresAdminUserRepository)
	return &PostgresSystemAdminRepository{
		PostgresAdminUserRepository: adminRepo,
	}
}

// ForceDelete 强制删除用户（硬删除）
func (r *PostgresSystemAdminRepository) ForceDelete(userID string) error {
	tx, err := r.db.Begin()
	if err != nil {
		r.logger.Error("Failed to begin transaction", zap.Error(err))
		return err
	}
	defer tx.Rollback()

	// 删除用户角色关联
	_, err = tx.Exec("DELETE FROM user_roles WHERE user_id = $1", userID)
	if err != nil {
		r.logger.Error("Failed to delete user roles", zap.Error(err), zap.String("user_id", userID))
		return err
	}

	// 删除用户权限关联
	_, err = tx.Exec("DELETE FROM user_permissions WHERE user_id = $1", userID)
	if err != nil {
		r.logger.Error("Failed to delete user permissions", zap.Error(err), zap.String("user_id", userID))
		return err
	}

	// 删除用户
	result, err := tx.Exec("DELETE FROM users WHERE id = $1", userID)
	if err != nil {
		r.logger.Error("Failed to force delete user", zap.Error(err), zap.String("user_id", userID))
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Failed to get rows affected", zap.Error(err))
		return err
	}

	if rowsAffected == 0 {
		return ErrUserNotFound
	}

	if err := tx.Commit(); err != nil {
		r.logger.Error("Failed to commit transaction", zap.Error(err))
		return err
	}

	r.logger.Info("Successfully force deleted user", zap.String("user_id", userID))
	return nil
}

// Restore 恢复已删除用户
func (r *PostgresSystemAdminRepository) Restore(userID string) error {
	query := `
		UPDATE users 
		SET deleted_at = NULL, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1 AND deleted_at IS NOT NULL
	`

	result, err := r.db.Exec(query, userID)
	if err != nil {
		r.logger.Error("Failed to restore user", zap.Error(err), zap.String("user_id", userID))
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Failed to get rows affected", zap.Error(err))
		return err
	}

	if rowsAffected == 0 {
		return ErrUserNotFound
	}

	r.logger.Info("Successfully restored user", zap.String("user_id", userID))
	return nil
}

// FindDeleted 查找已删除用户
func (r *PostgresSystemAdminRepository) FindDeleted(pagination Pagination) ([]*User, int64, error) {
	// 查询总数
	countQuery := `SELECT COUNT(*) FROM users WHERE deleted_at IS NOT NULL`
	var total int64
	err := r.db.QueryRow(countQuery).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count deleted users", zap.Error(err))
		return nil, 0, err
	}

	// 查询数据
	offset := (pagination.Page - 1) * pagination.PageSize
	query := `
		SELECT id, username, email, password_hash, client_id, callback_url, is_active, created_at, updated_at
		FROM users
		WHERE deleted_at IS NOT NULL
		ORDER BY deleted_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := r.db.Query(query, pagination.PageSize, offset)
	if err != nil {
		r.logger.Error("Failed to query deleted users", zap.Error(err))
		return nil, 0, err
	}
	defer rows.Close()

	var users []*User
	for rows.Next() {
		var user User

		err := rows.Scan(
			&user.ID,
			&user.Username,
			&user.Email,
			&user.PasswordHash,
			&user.ClientID,
			&user.CallbackURL,
			&user.IsActive,
			&user.CreatedAt,
			&user.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan deleted user", zap.Error(err))
			continue
		}

		users = append(users, &user)
	}

	return users, total, rows.Err()
}

// GetSystemStatistics 获取系统统计信息
func (r *PostgresSystemAdminRepository) GetSystemStatistics() (*SystemStatistics, error) {
	stats := &SystemStatistics{}

	// 查询用户统计
	userStatsQuery := `
		SELECT 
			COUNT(*) as total_users,
			COUNT(CASE WHEN is_active = true AND deleted_at IS NULL THEN 1 END) as active_users,
			COUNT(CASE WHEN is_active = false AND deleted_at IS NULL THEN 1 END) as inactive_users,
			COUNT(CASE WHEN deleted_at IS NOT NULL THEN 1 END) as deleted_users
		FROM users
	`

	err := r.db.QueryRow(userStatsQuery).Scan(
		&stats.TotalUsers,
		&stats.ActiveUsers,
		&stats.InactiveUsers,
		&stats.DeletedUsers,
	)
	if err != nil {
		r.logger.Error("Failed to get user statistics", zap.Error(err))
		return nil, err
	}

	// 查询审计日志统计
	auditStatsQuery := `
		SELECT 
			COUNT(*) as total_audit_logs,
			COUNT(CASE WHEN created_at >= NOW() - INTERVAL '24 hours' THEN 1 END) as recent_operations
		FROM user_audit_logs
	`

	err = r.db.QueryRow(auditStatsQuery).Scan(
		&stats.TotalAuditLogs,
		&stats.RecentOperations,
	)
	if err != nil {
		r.logger.Error("Failed to get audit statistics", zap.Error(err))
		return nil, err
	}

	return stats, nil
}

// PurgeOldAuditLogs 清理旧的审计日志
func (r *PostgresSystemAdminRepository) PurgeOldAuditLogs(olderThanDays int) (int64, error) {
	query := `DELETE FROM user_audit_logs WHERE created_at < NOW() - INTERVAL '%d days'`

	result, err := r.db.Exec(query, olderThanDays)
	if err != nil {
		r.logger.Error("Failed to purge old audit logs", zap.Error(err), zap.Int("older_than_days", olderThanDays))
		return 0, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Failed to get rows affected", zap.Error(err))
		return 0, err
	}

	r.logger.Info("Successfully purged old audit logs",
		zap.Int64("rows_affected", rowsAffected),
		zap.Int("older_than_days", olderThanDays))

	return rowsAffected, nil
}

// DefaultSystemAdminService 默认系统管理员服务实现
type DefaultSystemAdminService struct {
	*DefaultAdminUserService
	systemRepository SystemAdminRepository
}

// NewDefaultSystemAdminService 创建默认系统管理员服务
func NewDefaultSystemAdminService(
	adminUserService *DefaultAdminUserService,
	systemRepository SystemAdminRepository,
) SystemAdminService {
	return &DefaultSystemAdminService{
		DefaultAdminUserService: adminUserService,
		systemRepository:        systemRepository,
	}
}

// ForceDeleteUser 强制删除用户（硬删除）
func (s *DefaultSystemAdminService) ForceDeleteUser(userID string) error {
	// 检查用户是否存在
	_, err := s.repository.FindByID(userID)
	if err != nil {
		if err == ErrUserNotFound {
			return errors.NewBusinessError(
				errors.ErrCodeNotFound,
				"用户不存在",
			)
		}
		s.logger.Error("Failed to get user", zap.Error(err), zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取用户信息失败",
			err,
		)
	}

	// 执行强制删除
	if err := s.systemRepository.ForceDelete(userID); err != nil {
		s.logger.Error("Failed to force delete user",
			zap.Error(err),
			zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"强制删除用户失败",
			err,
		)
	}

	s.logger.Info("Successfully force deleted user", zap.String("user_id", userID))
	return nil
}

// ForceDeleteUserWithAudit 强制删除用户（带审计）
func (s *DefaultSystemAdminService) ForceDeleteUserWithAudit(ctx context.Context, operatorID, userID string, ipAddress, userAgent string) error {
	// 获取删除前的用户信息
	user, err := s.repository.FindByID(userID)
	if err != nil {
		return err
	}

	// 调用强制删除
	err = s.ForceDeleteUser(userID)
	if err != nil {
		// 记录失败的审计日志
		details := map[string]interface{}{
			"target_user_id": userID,
			"username":       user.Username,
			"email":          user.Email,
			"error":          err.Error(),
		}
		if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionUserForceDelete, details, ipAddress, userAgent); auditErr != nil {
			s.logger.Warn("Failed to log audit for failed force deletion", zap.Error(auditErr))
		}
		return err
	}

	// 记录成功的审计日志
	details := map[string]interface{}{
		"target_user_id": userID,
		"username":       user.Username,
		"email":          user.Email,
	}
	if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionUserForceDelete, details, ipAddress, userAgent); auditErr != nil {
		s.logger.Warn("Failed to log audit for force deletion", zap.Error(auditErr))
	}

	return nil
}

// RestoreUser 恢复已删除用户
func (s *DefaultSystemAdminService) RestoreUser(userID string) error {
	// 执行恢复
	if err := s.systemRepository.Restore(userID); err != nil {
		if err == ErrUserNotFound {
			return errors.NewBusinessError(
				errors.ErrCodeNotFound,
				"用户不存在或未被删除",
			)
		}
		s.logger.Error("Failed to restore user",
			zap.Error(err),
			zap.String("user_id", userID))
		return errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"恢复用户失败",
			err,
		)
	}

	s.logger.Info("Successfully restored user", zap.String("user_id", userID))
	return nil
}

// RestoreUserWithAudit 恢复已删除用户（带审计）
func (s *DefaultSystemAdminService) RestoreUserWithAudit(ctx context.Context, operatorID, userID string, ipAddress, userAgent string) error {
	// 调用恢复用户
	err := s.RestoreUser(userID)
	if err != nil {
		// 记录失败的审计日志
		details := map[string]interface{}{
			"target_user_id": userID,
			"error":          err.Error(),
		}
		if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionUserRestore, details, ipAddress, userAgent); auditErr != nil {
			s.logger.Warn("Failed to log audit for failed restoration", zap.Error(auditErr))
		}
		return err
	}

	// 记录成功的审计日志
	details := map[string]interface{}{
		"target_user_id": userID,
	}
	if auditErr := s.auditService.LogUserOperation(ctx, operatorID, &userID, AuditActionUserRestore, details, ipAddress, userAgent); auditErr != nil {
		s.logger.Warn("Failed to log audit for restoration", zap.Error(auditErr))
	}

	return nil
}

// GetDeletedUsers 获取已删除用户列表
func (s *DefaultSystemAdminService) GetDeletedUsers(pagination Pagination) (*UserListResult, error) {
	// 设置默认分页参数
	if pagination.Page <= 0 {
		pagination.Page = 1
	}
	if pagination.PageSize <= 0 {
		pagination.PageSize = 20
	}

	// 查询已删除用户
	users, total, err := s.systemRepository.FindDeleted(pagination)
	if err != nil {
		s.logger.Error("Failed to get deleted users", zap.Error(err))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取已删除用户列表失败",
			err,
		)
	}

	// 转换为UserWithRoles类型
	userWithRoles := make([]*UserWithRoles, len(users))
	for i, user := range users {
		userWithRoles[i] = &UserWithRoles{
			User:  user,
			Roles: []*Role{}, // 已删除用户不需要角色信息
		}
	}

	// 计算总页数
	totalPages := pagination.CalculateTotalPages(total)

	result := &UserListResult{
		Items:      userWithRoles,
		Total:      total,
		Page:       pagination.Page,
		PageSize:   pagination.PageSize,
		TotalPages: totalPages,
	}

	s.logger.Info("Successfully retrieved deleted users",
		zap.Int64("total", total),
		zap.Int("page", pagination.Page))

	return result, nil
}

// PurgeOldAuditLogs 清理旧的审计日志
func (s *DefaultSystemAdminService) PurgeOldAuditLogs(olderThanDays int) (int64, error) {
	if olderThanDays <= 0 {
		return 0, errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			"清理天数必须大于0",
		)
	}

	count, err := s.systemRepository.PurgeOldAuditLogs(olderThanDays)
	if err != nil {
		s.logger.Error("Failed to purge old audit logs", zap.Error(err))
		return 0, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"清理旧审计日志失败",
			err,
		)
	}

	s.logger.Info("Successfully purged old audit logs", zap.Int64("count", count))
	return count, nil
}

// GetSystemStatistics 获取系统统计信息
func (s *DefaultSystemAdminService) GetSystemStatistics() (*SystemStatistics, error) {
	stats, err := s.systemRepository.GetSystemStatistics()
	if err != nil {
		s.logger.Error("Failed to get system statistics", zap.Error(err))
		return nil, errors.NewBusinessErrorWithCause(
			errors.ErrCodeInternal,
			"获取系统统计信息失败",
			err,
		)
	}

	s.logger.Info("Successfully retrieved system statistics",
		zap.Int64("total_users", stats.TotalUsers),
		zap.Int64("active_users", stats.ActiveUsers))

	return stats, nil
}
