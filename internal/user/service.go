package user

import (
	"context"
	"errors"
	"fmt"
	"log"
	"regexp"
	"strings"

	"github.com/your-org/go-kuaidi/internal/auth"
	"golang.org/x/crypto/bcrypt"
)

// UserService 用户服务接口
type UserService interface {
	// Register 注册新用户
	Register(username, email, password string) (*User, error)

	// Authenticate 认证用户
	Authenticate(usernameOrEmail, password string) (*User, error)

	// GetUserByID 根据ID获取用户
	GetUserByID(id string) (*User, error)

	// GetUserByUsername 根据用户名获取用户
	GetUserByUsername(username string) (*User, error)

	// FindUserByClientID 根据客户端ID查找用户
	FindUserByClientID(clientID string) (*User, error)

	// UpdateUser 更新用户信息
	UpdateUser(user *User) error

	// ChangePassword 更改用户密码
	ChangePassword(userID, currentPassword, newPassword string) error

	// UpdateCallbackURL 更新回调URL
	UpdateCallbackURL(userID, callbackURL string) error

	// ResetClientSecret 重置客户端密钥
	ResetClientSecret(userID string) (string, error)
}

// AdminUserService 管理员用户服务接口
type AdminUserService interface {
	UserService

	// GetUsers 获取用户列表（分页）
	GetUsers(filter UserFilter, pagination Pagination) (*UserListResult, error)

	// CreateUser 管理员创建用户
	CreateUser(req CreateUserRequest) (*User, error)

	// UpdateUserByAdmin 管理员更新用户
	UpdateUserByAdmin(userID string, req UpdateUserRequest) (*User, error)

	// UpdateUserStatus 更新用户状态
	UpdateUserStatus(userID string, isActive bool) error

	// ResetUserPassword 管理员重置用户密码
	ResetUserPassword(userID, newPassword string) error

	// DeleteUser 删除用户（软删除）
	DeleteUser(userID string) error

	// GetUserStatistics 获取用户统计信息
	GetUserStatistics() (*UserStatistics, error)

	// BatchUpdateStatus 批量更新用户状态
	BatchUpdateStatus(userIDs []string, isActive bool) error

	// CreateUserWithAudit 管理员创建用户（带审计）
	CreateUserWithAudit(ctx context.Context, operatorID string, req CreateUserRequest, ipAddress, userAgent string) (*User, error)

	// UpdateUserByAdminWithAudit 管理员更新用户（带审计）
	UpdateUserByAdminWithAudit(ctx context.Context, operatorID, userID string, req UpdateUserRequest, ipAddress, userAgent string) (*User, error)

	// UpdateUserStatusWithAudit 更新用户状态（带审计）
	UpdateUserStatusWithAudit(ctx context.Context, operatorID, userID string, isActive bool, ipAddress, userAgent string) error

	// ResetUserPasswordWithAudit 管理员重置用户密码（带审计）
	ResetUserPasswordWithAudit(ctx context.Context, operatorID, userID, newPassword string, ipAddress, userAgent string) error

	// DeleteUserWithAudit 删除用户（带审计）
	DeleteUserWithAudit(ctx context.Context, operatorID, userID string, ipAddress, userAgent string) error

	// BatchUpdateStatusWithAudit 批量更新用户状态（带审计）
	BatchUpdateStatusWithAudit(ctx context.Context, operatorID string, userIDs []string, isActive bool, ipAddress, userAgent string) error

	// GetAuditLogs 获取审计日志
	GetAuditLogs(filter AuditLogFilter, pagination Pagination) (*AuditLogListResult, error)

	// ForceDeleteUser 永久删除用户（硬删除）
	ForceDeleteUser(userID string) error
}

// DefaultUserService 默认用户服务实现
type DefaultUserService struct {
	repository           UserRepository
	clientService        ClientService
	roleRepository       RoleRepository
	permissionRepository PermissionRepository
	validationService    ValidationService
}

// ClientService 客户端服务接口
type ClientService interface {
	// GenerateClientCredentials 生成客户端凭证
	GenerateClientCredentials(name string, allowedScopes []string) (interface{}, string, error)

	// Authenticate 认证客户端
	Authenticate(clientID, clientSecret string) (interface{}, error)

	// ValidateScopes 验证作用域
	ValidateScopes(client interface{}, requestedScopes []string) ([]string, error)

	// FindByID 根据ID查找客户端
	FindByID(clientID string) (interface{}, error)

	// ResetClientSecret 重置客户端密钥
	ResetClientSecret(clientID string) (string, error)
}

// NewUserService 创建新的用户服务
func NewUserService(repository UserRepository, clientService ClientService, roleRepository RoleRepository, permissionRepository PermissionRepository, validationService ValidationService) UserService {
	return &DefaultUserService{
		repository:           repository,
		clientService:        clientService,
		roleRepository:       roleRepository,
		permissionRepository: permissionRepository,
		validationService:    validationService,
	}
}

// Register 注册新用户
func (s *DefaultUserService) Register(username, email, password string) (*User, error) {
	// 使用验证服务验证用户名
	if s.validationService != nil {
		if err := s.validationService.ValidateUsername(username); err != nil {
			return nil, err
		}
	} else {
		// 回退到硬编码验证
		if err := validateUsername(username); err != nil {
			return nil, err
		}
	}

	// 使用验证服务验证邮箱
	if s.validationService != nil {
		if err := s.validationService.ValidateEmail(email); err != nil {
			return nil, err
		}
	} else {
		// 回退到硬编码验证
		if err := validateEmail(email); err != nil {
			return nil, err
		}
	}

	// 使用验证服务验证密码
	if s.validationService != nil {
		if err := s.validationService.ValidatePassword(password); err != nil {
			return nil, err
		}
	} else {
		// 回退到硬编码验证
		if err := validatePassword(password); err != nil {
			return nil, err
		}
	}

	// 生成密码哈希
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("生成密码哈希失败: %w", err)
	}

	// 为用户创建客户端凭证
	clientName := username + "'s API Client"
	allowedScopes := []string{"express:read", "express:write", "workorder:read", "workorder:write"}

	clientInterface, _, err := s.clientService.GenerateClientCredentials(clientName, allowedScopes)
	if err != nil {
		return nil, fmt.Errorf("生成客户端凭证失败: %w", err)
	}

	// 类型断言
	client, ok := clientInterface.(*auth.Client)
	if !ok {
		return nil, fmt.Errorf("无效的客户端类型")
	}

	// 创建用户
	user := &User{
		Username:     username,
		Email:        email,
		PasswordHash: string(passwordHash),
		ClientID:     client.ID,
		IsActive:     true,
	}

	// 保存用户
	if err := s.repository.Create(user); err != nil {
		return nil, err
	}

	// 为用户分配默认角色（普通用户角色）
	if err := s.repository.SetDefaultRole(user.ID, RoleUser); err != nil {
		// 记录错误但不中断流程
		log.Printf("为用户 %s 设置默认角色失败: %v", user.ID, err)
	}

	return user, nil
}

// Authenticate 认证用户
func (s *DefaultUserService) Authenticate(usernameOrEmail, password string) (*User, error) {
	var user *User
	var err error

	// 判断输入是邮箱还是用户名
	if isEmail(usernameOrEmail) {
		user, err = s.repository.FindByEmail(usernameOrEmail)
	} else {
		user, err = s.repository.FindByUsername(usernameOrEmail)
	}

	if err != nil {
		if errors.Is(err, ErrUserNotFound) {
			return nil, ErrInvalidCredentials
		}
		return nil, err
	}

	// 检查用户是否激活
	if !user.IsActive {
		return nil, errors.New("user is not active")
	}

	// 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password))
	if err != nil {
		return nil, ErrInvalidCredentials
	}

	return user, nil
}

// GetUserByID 根据ID获取用户
func (s *DefaultUserService) GetUserByID(id string) (*User, error) {
	return s.repository.FindByID(id)
}

// GetUserByUsername 根据用户名获取用户
func (s *DefaultUserService) GetUserByUsername(username string) (*User, error) {
	return s.repository.FindByUsername(username)
}

// FindUserByClientID 根据客户端ID查找用户
func (s *DefaultUserService) FindUserByClientID(clientID string) (*User, error) {
	return s.repository.FindByClientID(clientID)
}

// UpdateUser 更新用户信息
func (s *DefaultUserService) UpdateUser(user *User) error {
	// 使用验证服务验证邮箱
	if s.validationService != nil {
		if err := s.validationService.ValidateEmail(user.Email); err != nil {
			return err
		}
	} else {
		// 回退到硬编码验证
		if err := validateEmail(user.Email); err != nil {
			return err
		}
	}

	return s.repository.Update(user)
}

// ChangePassword 更改用户密码
func (s *DefaultUserService) ChangePassword(userID, currentPassword, newPassword string) error {
	// 获取用户
	user, err := s.repository.FindByID(userID)
	if err != nil {
		return err
	}

	// 验证当前密码
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(currentPassword))
	if err != nil {
		return ErrInvalidCredentials
	}

	// 使用验证服务验证新密码
	if s.validationService != nil {
		if err := s.validationService.ValidatePassword(newPassword); err != nil {
			return err
		}
	} else {
		// 回退到硬编码验证
		if err := validatePassword(newPassword); err != nil {
			return err
		}
	}

	// 生成新密码哈希
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("生成密码哈希失败: %w", err)
	}

	// 更新密码
	user.PasswordHash = string(passwordHash)
	return s.repository.Update(user)
}

// UpdateCallbackURL 更新回调URL
func (s *DefaultUserService) UpdateCallbackURL(userID, callbackURL string) error {
	// 获取用户
	user, err := s.repository.FindByID(userID)
	if err != nil {
		return err
	}

	// 使用验证服务验证回调URL
	if s.validationService != nil {
		if err := s.validationService.ValidateField("callback_url", callbackURL); err != nil {
			return err
		}
	} else {
		// 回退到硬编码验证
		if err := validateCallbackURL(callbackURL); err != nil {
			return err
		}
	}

	// 更新回调URL
	user.CallbackURL = callbackURL
	return s.repository.Update(user)
}

// ResetClientSecret 重置客户端密钥
func (s *DefaultUserService) ResetClientSecret(userID string) (string, error) {
	// 获取用户
	user, err := s.repository.FindByID(userID)
	if err != nil {
		return "", err
	}

	// 检查现有客户端是否存在
	if client, _ := s.clientService.FindByID(user.ClientID); client != nil {
		// 仅重置密钥，保持client_id不变
		return s.clientService.ResetClientSecret(user.ClientID)
	}

	// 若客户端记录缺失，才创建新客户端并绑定
	clientName := user.Username + "'s API Client"
	allowedScopes := []string{"express:read", "express:write", "workorder:read", "workorder:write"}

	clientInterface, clientSecret, err := s.clientService.GenerateClientCredentials(clientName, allowedScopes)
	if err != nil {
		return "", fmt.Errorf("生成客户端凭证失败: %w", err)
	}

	newClient, ok := clientInterface.(*auth.Client)
	if !ok {
		return "", fmt.Errorf("无效的客户端类型")
	}

	user.ClientID = newClient.ID
	if err := s.repository.Update(user); err != nil {
		return "", err
	}
	return clientSecret, nil
}

// 判断是否为邮箱
func isEmail(s string) bool {
	return strings.Contains(s, "@")
}

// 验证回调URL
func validateCallbackURL(url string) error {
	// 如果URL为空，则视为有效（表示删除回调URL）
	if url == "" {
		return nil
	}

	// URL长度检查
	if len(url) > 500 {
		return errors.New("callback URL must be less than 500 characters")
	}

	// URL格式检查
	urlRegex := regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
	if !urlRegex.MatchString(url) {
		return errors.New("invalid callback URL format")
	}

	return nil
}
