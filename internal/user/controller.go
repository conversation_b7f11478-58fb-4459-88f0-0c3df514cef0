package user

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/auth"
)

// RegisterRequest 表示注册请求
type RegisterRequest struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
}

// RegisterResponse 表示注册响应
type RegisterResponse struct {
	UserID       string `json:"user_id"`
	Username     string `json:"username"`
	Email        string `json:"email"`
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
}

// UserController 用户控制器
type UserController struct {
	userService   UserService
	clientService auth.ClientService
	repository    UserRepository
}

// NewUserController 创建新的用户控制器
func NewUserController(userService UserService, clientService auth.ClientService) *UserController {
	// 获取用户存储库
	repository, ok := userService.(*DefaultUserService)
	var userRepo UserRepository
	if ok {
		userRepo = repository.repository
	}

	return &UserController{
		userService:   userService,
		clientService: clientService,
		repository:    userRepo,
	}
}

// GetUserRepository 获取用户存储库
func (c *UserController) GetUserRepository() UserRepository {
	return c.repository
}

// HandleRegister 处理用户注册
func (c *UserController) HandleRegister(ctx *gin.Context) {
	var req RegisterRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    http.StatusBadRequest,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 注册用户
	user, err := c.userService.Register(req.Username, req.Email, req.Password)
	if err != nil {
		statusCode := http.StatusInternalServerError
		errorMessage := "Failed to register user"

		if errors.Is(err, ErrUserAlreadyExists) {
			statusCode = http.StatusConflict
			errorMessage = "User already exists"
		}

		ctx.JSON(statusCode, gin.H{
			"success": false,
			"code":    statusCode,
			"message": errorMessage,
			"error":   err.Error(),
		})
		return
	}

	// 为用户创建客户端凭证
	// 使用用户名作为客户端ID的一部分，确保唯一性
	clientName := user.Username + "'s API Client"
	// 默认授权范围：快递读写 + 工单读写
	allowedScopes := []string{"express:read", "express:write", "workorder:read", "workorder:write"}

	client, clientSecret, err := c.clientService.GenerateClientCredentials(clientName, allowedScopes)
	if err != nil {
		// 注册成功但创建客户端失败，返回部分成功响应
		ctx.JSON(http.StatusOK, gin.H{
			"success": true,
			"code":    http.StatusOK,
			"message": "User registered successfully, but failed to generate client credentials",
			"data": gin.H{
				"user_id":  user.ID,
				"username": user.Username,
				"email":    user.Email,
			},
			"error": err.Error(),
		})
		return
	}

	// 直接使用客户端ID
	user.ClientID = client.ID
	if err := c.userService.UpdateUser(user); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    http.StatusInternalServerError,
			"message": "Failed to update user with client ID",
			"error":   err.Error(),
		})
		return
	}

	// 触发注册成功回调函数
	triggerRegisterSuccessCallbacks(user, client.ID, clientSecret)

	// 返回用户信息和客户端凭证
	ctx.JSON(http.StatusCreated, gin.H{
		"success": true,
		"code":    http.StatusCreated,
		"message": "User registered successfully",
		"data": RegisterResponse{
			UserID:       user.ID,
			Username:     user.Username,
			Email:        user.Email,
			ClientID:     client.ID,
			ClientSecret: clientSecret,
		},
	})
}

// HandleGetProfile 处理获取用户个人资料
func (c *UserController) HandleGetProfile(ctx *gin.Context) {
	// 🔥 修复：优先从上下文中获取用户ID，其次获取客户端ID
	userID := ctx.GetString("user_id")
	if userID == "" {
		userID = ctx.GetString("userID") // 兼容其他中间件
	}

	var user *User
	var err error

	if userID != "" {
		// 优先通过用户ID查找
		user, err = c.userService.GetUserByID(userID)
		if err != nil && !errors.Is(err, ErrUserNotFound) {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"code":    http.StatusInternalServerError,
				"message": "Failed to get user profile",
				"error":   err.Error(),
			})
			return
		}
	}

	// 如果通过用户ID找不到，尝试通过客户端ID查找（向后兼容）
	if user == nil {
		clientID := ctx.GetString("client_id")
		if clientID == "" {
			ctx.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"code":    http.StatusUnauthorized,
				"message": "Unauthorized: no user identification found",
			})
			return
		}

		user, err = c.userService.FindUserByClientID(clientID)
		if err != nil {
			statusCode := http.StatusInternalServerError
			errorMessage := "Failed to get user profile"

			if errors.Is(err, ErrUserNotFound) {
				statusCode = http.StatusNotFound
				errorMessage = "User not found"
			}

			ctx.JSON(statusCode, gin.H{
				"success": false,
				"code":    statusCode,
				"message": errorMessage,
				"error":   err.Error(),
			})
			return
		}
	}

	if user == nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"code":    http.StatusNotFound,
			"message": "User not found",
		})
		return
	}

	// 返回用户信息
	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "User profile retrieved successfully",
		"data": gin.H{
			"user_id":      user.ID,
			"username":     user.Username,
			"email":        user.Email,
			"callback_url": user.CallbackURL,
			"client_id":    user.ClientID,
			"is_active":    user.IsActive,
			"created_at":   user.CreatedAt,
			"updated_at":   user.UpdatedAt,
		},
	})
}

// UpdateCallbackURLRequest 表示更新回调URL请求
type UpdateCallbackURLRequest struct {
	CallbackURL string `json:"callback_url"`
}

// HandleUpdateCallbackURL 处理更新回调URL
func (c *UserController) HandleUpdateCallbackURL(ctx *gin.Context) {
	// 从上下文中获取客户端ID
	clientID := ctx.GetString("client_id")
	if clientID == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"code":    http.StatusUnauthorized,
			"message": "Unauthorized",
		})
		return
	}

	// 查找具有此客户端ID的用户
	user, err := c.userService.FindUserByClientID(clientID)
	if err != nil {
		statusCode := http.StatusInternalServerError
		errorMessage := "Failed to get user profile"

		if errors.Is(err, ErrUserNotFound) {
			statusCode = http.StatusNotFound
			errorMessage = "User not found"
		}

		ctx.JSON(statusCode, gin.H{
			"success": false,
			"code":    statusCode,
			"message": errorMessage,
			"error":   err.Error(),
		})
		return
	}

	// 解析请求
	var req UpdateCallbackURLRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"code":    http.StatusBadRequest,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 更新回调URL
	err = c.userService.UpdateCallbackURL(user.ID, req.CallbackURL)
	if err != nil {
		statusCode := http.StatusInternalServerError
		errorMessage := "Failed to update callback URL"

		if errors.Is(err, ErrUserNotFound) {
			statusCode = http.StatusNotFound
			errorMessage = "User not found"
		}

		ctx.JSON(statusCode, gin.H{
			"success": false,
			"code":    statusCode,
			"message": errorMessage,
			"error":   err.Error(),
		})
		return
	}

	// 返回成功响应
	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "Callback URL updated successfully",
	})
}

// HandleResetClientSecret 处理重置客户端密钥
func (c *UserController) HandleResetClientSecret(ctx *gin.Context) {
	// 从上下文中获取客户端ID
	clientID := ctx.GetString("client_id")
	if clientID == "" {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"code":    http.StatusUnauthorized,
			"message": "Unauthorized",
		})
		return
	}

	// 查找具有此客户端ID的用户
	user, err := c.userService.FindUserByClientID(clientID)
	if err != nil {
		statusCode := http.StatusInternalServerError
		errorMessage := "Failed to get user profile"

		if errors.Is(err, ErrUserNotFound) {
			statusCode = http.StatusNotFound
			errorMessage = "User not found"
		}

		ctx.JSON(statusCode, gin.H{
			"success": false,
			"code":    statusCode,
			"message": errorMessage,
			"error":   err.Error(),
		})
		return
	}

	// 重置客户端密钥
	clientSecret, err := c.userService.ResetClientSecret(user.ID)
	if err != nil {
		statusCode := http.StatusInternalServerError
		errorMessage := "Failed to reset client secret"

		if errors.Is(err, ErrUserNotFound) {
			statusCode = http.StatusNotFound
			errorMessage = "User not found"
		}

		ctx.JSON(statusCode, gin.H{
			"success": false,
			"code":    statusCode,
			"message": errorMessage,
			"error":   err.Error(),
		})
		return
	}

	// 获取更新后的用户信息
	updatedUser, err := c.userService.GetUserByID(user.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"code":    http.StatusInternalServerError,
			"message": "Failed to get user information",
			"error":   err.Error(),
		})
		return
	}

	// 返回成功响应
	ctx.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    http.StatusOK,
		"message": "Client secret reset successfully",
		"data": gin.H{
			"client_id":     updatedUser.ClientID,
			"client_secret": clientSecret,
		},
	})
}
