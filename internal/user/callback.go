package user

// RegisterSuccessCallback 定义注册成功回调函数类型
type RegisterSuccessCallback func(user *User, clientID, clientSecret string)

// 存储注册成功回调函数
var registerSuccessCallbacks []RegisterSuccessCallback

// RegisterOnRegisterSuccess 注册用户注册成功的回调函数
func (c *UserController) RegisterOnRegisterSuccess(callback RegisterSuccessCallback) {
	registerSuccessCallbacks = append(registerSuccessCallbacks, callback)
}

// triggerRegisterSuccessCallbacks 触发所有注册成功回调函数
func triggerRegisterSuccessCallbacks(user *User, clientID, clientSecret string) {
	for _, callback := range registerSuccessCallbacks {
		callback(user, clientID, clientSecret)
	}
}
