package user

import (
	"regexp"
	"strconv"
	"strings"
	"unicode"

	"github.com/your-org/go-kuaidi/internal/errors"
)

// 密码验证相关的正则表达式
var (
	emailRegex    = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	usernameRegex = regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
)

// validateUsername 验证用户名
func validateUsername(username string) error {
	if len(username) < 3 {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			"用户名长度不能少于3个字符",
		)
	}

	if len(username) > 50 {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			"用户名长度不能超过50个字符",
		)
	}

	if !usernameRegex.MatchString(username) {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			"用户名只能包含字母、数字、下划线和连字符",
		)
	}

	return nil
}

// validateEmail 验证邮箱
func validateEmail(email string) error {
	if !emailRegex.MatchString(email) {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			"邮箱格式不正确",
		)
	}

	return nil
}

// validatePassword 验证密码（基础版本，不使用配置）
func validatePassword(password string) error {
	if len(password) < 8 {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			"密码长度不能少于8个字符",
		)
	}

	if len(password) > 72 {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			"密码长度不能超过72个字符",
		)
	}

	// 基本的密码复杂度检查
	hasUpper := containsUppercase(password)
	hasLower := containsLowercase(password)
	hasNumber := containsNumbers(password)
	hasSpecial := containsSpecialChars(password)

	if !hasUpper || !hasLower || !hasNumber || !hasSpecial {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			"密码必须包含大写字母、小写字母、数字和特殊字符",
		)
	}

	return nil
}

// containsUppercase 检查是否包含大写字母
func containsUppercase(s string) bool {
	for _, r := range s {
		if unicode.IsUpper(r) {
			return true
		}
	}
	return false
}

// containsLowercase 检查是否包含小写字母
func containsLowercase(s string) bool {
	for _, r := range s {
		if unicode.IsLower(r) {
			return true
		}
	}
	return false
}

// containsNumbers 检查是否包含数字
func containsNumbers(s string) bool {
	for _, r := range s {
		if unicode.IsDigit(r) {
			return true
		}
	}
	return false
}

// containsSpecialChars 检查是否包含特殊字符
func containsSpecialChars(s string) bool {
	specialChars := "!@#$%^&*()_+-=[]{}|;:,.<>?"
	return strings.ContainsAny(s, specialChars)
}

// containsOnlyWhitespace 检查字符串是否只包含空白字符
func containsOnlyWhitespace(s string) bool {
	return strings.TrimSpace(s) == ""
}

// validateNonEmptyString 验证非空字符串
func validateNonEmptyString(value, fieldName string) error {
	if value == "" || containsOnlyWhitespace(value) {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			fieldName+"不能为空",
		)
	}

	return nil
}

// validateStringLength 验证字符串长度
func validateStringLength(value, fieldName string, minLength, maxLength int) error {
	length := len(value)

	if minLength > 0 && length < minLength {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			fieldName+"长度不能少于"+strconv.Itoa(minLength)+"个字符",
		)
	}

	// 当 maxLength <= 0 视为"不限制"
	if maxLength > 0 && length > maxLength {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			fieldName+"长度不能超过"+strconv.Itoa(maxLength)+"个字符",
		)
	}

	return nil
}
