package user

import (
	"time"

	"github.com/your-org/go-kuaidi/internal/errors"
)

// UserFilter 用户过滤条件
type UserFilter struct {
	// 关键词搜索（用户名或邮箱）
	Keyword string `json:"keyword,omitempty"`

	// 状态过滤
	Status *bool `json:"status,omitempty"` // nil=全部, true=激活, false=禁用

	// 角色过滤
	RoleID string `json:"role_id,omitempty"`

	// 是否包含软删除用户
	IncludeDeleted bool `json:"include_deleted,omitempty"`

	// 创建时间范围
	CreatedAfter  *time.Time `json:"created_after,omitempty"`
	CreatedBefore *time.Time `json:"created_before,omitempty"`

	// 最后登录时间范围
	LastLoginAfter  *time.Time `json:"last_login_after,omitempty"`
	LastLoginBefore *time.Time `json:"last_login_before,omitempty"`
}

// Pagination 分页参数
type Pagination struct {
	Page     int    `json:"page" binding:"min=1"`
	PageSize int    `json:"page_size" binding:"min=1,max=100"`
	OrderBy  string `json:"order_by,omitempty"` // 排序字段
	Order    string `json:"order,omitempty"`    // asc/desc
}

// UserListResult 用户列表结果
type UserListResult struct {
	Items      []*UserWithRoles `json:"items"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	TotalPages int              `json:"total_pages"`
}

// UserWithRoles 包含角色信息的用户
type UserWithRoles struct {
	*User
	Roles      []*Role    `json:"roles"`
	LastLogin  *time.Time `json:"last_login,omitempty"`
	LoginCount int64      `json:"login_count"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50" validate:"username"`
	Email    string `json:"email" binding:"required,email" validate:"email"`
	Password string `json:"password" binding:"required,min=8,max=72" validate:"password"`
	RoleID   string `json:"role_id,omitempty" validate:"role_exists"`
	IsActive *bool  `json:"is_active,omitempty"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Username string `json:"username,omitempty" binding:"omitempty,min=3,max=50" validate:"username"`
	Email    string `json:"email,omitempty" binding:"omitempty,email" validate:"email"`
	IsActive *bool  `json:"is_active,omitempty"`
}

// UserStatistics 用户统计信息
type UserStatistics struct {
	TotalUsers        int64 `json:"total_users"`
	ActiveUsers       int64 `json:"active_users"`
	InactiveUsers     int64 `json:"inactive_users"`
	NewUsersToday     int64 `json:"new_users_today"`
	NewUsersThisWeek  int64 `json:"new_users_this_week"`
	NewUsersThisMonth int64 `json:"new_users_this_month"`
	OnlineUsers       int64 `json:"online_users"`
}

// 预定义的用户管理错误
var (
	ErrUserManagementUnauthorized = errors.NewBusinessError(
		errors.ErrCodeUnauthorized,
		"无权限执行用户管理操作",
	)

	ErrUserManagementForbidden = errors.NewBusinessError(
		errors.ErrCodeForbidden,
		"禁止执行此用户管理操作",
	)

	ErrCannotModifySelf = errors.NewBusinessError(
		errors.ErrCodeForbidden,
		"不能修改自己的账号状态",
	)

	ErrCannotDeleteSelf = errors.NewBusinessError(
		errors.ErrCodeForbidden,
		"不能删除自己的账号",
	)

	ErrCannotDeleteAdmin = errors.NewBusinessError(
		errors.ErrCodeForbidden,
		"不能删除管理员账号",
	)

	ErrInvalidUserFilter = errors.NewBusinessError(
		errors.ErrCodeInvalidRequest,
		"无效的用户过滤条件",
	)

	ErrInvalidPagination = errors.NewBusinessError(
		errors.ErrCodeInvalidRequest,
		"无效的分页参数",
	)
)

// ValidateUserFilter 验证用户过滤条件
func ValidateUserFilter(filter UserFilter) error {
	// 验证时间范围
	if filter.CreatedAfter != nil && filter.CreatedBefore != nil {
		if filter.CreatedAfter.After(*filter.CreatedBefore) {
			return WithDetails(ErrInvalidUserFilter, map[string]interface{}{
				"field": "created_time_range",
				"error": "开始时间不能晚于结束时间",
			})
		}
	}

	if filter.LastLoginAfter != nil && filter.LastLoginBefore != nil {
		if filter.LastLoginAfter.After(*filter.LastLoginBefore) {
			return WithDetails(ErrInvalidUserFilter, map[string]interface{}{
				"field": "last_login_time_range",
				"error": "开始时间不能晚于结束时间",
			})
		}
	}

	return nil
}

// ValidatePagination 验证分页参数（基础版本，使用硬编码限制）
func ValidatePagination(pagination Pagination) error {
	if pagination.Page < 1 {
		return WithDetails(ErrInvalidPagination, map[string]interface{}{
			"field": "page",
			"error": "页码必须大于0",
		})
	}

	// 使用默认的最大页面大小
	maxPageSize := 100
	if pagination.PageSize < 1 || pagination.PageSize > maxPageSize {
		return WithDetails(ErrInvalidPagination, map[string]interface{}{
			"field": "page_size",
			"error": "每页大小必须在1-100之间",
		})
	}

	// 验证排序字段
	validOrderFields := map[string]bool{
		"username":   true,
		"email":      true,
		"created_at": true,
		"updated_at": true,
		"last_login": true,
	}

	if pagination.OrderBy != "" && !validOrderFields[pagination.OrderBy] {
		return WithDetails(ErrInvalidPagination, map[string]interface{}{
			"field":        "order_by",
			"error":        "无效的排序字段",
			"valid_fields": []string{"username", "email", "created_at", "updated_at", "last_login"},
		})
	}

	if pagination.Order != "" && pagination.Order != "asc" && pagination.Order != "desc" {
		return WithDetails(ErrInvalidPagination, map[string]interface{}{
			"field": "order",
			"error": "排序方向必须是asc或desc",
		})
	}

	return nil
}

// SetDefaults 设置分页默认值
func (p *Pagination) SetDefaults() {
	if p.Page < 1 {
		p.Page = 1
	}
	if p.PageSize < 1 {
		p.PageSize = 20
	}
	if p.OrderBy == "" {
		p.OrderBy = "created_at"
	}
	if p.Order == "" {
		p.Order = "desc"
	}
}

// GetOffset 计算偏移量
func (p *Pagination) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}

// CalculateTotalPages 计算总页数
func (p *Pagination) CalculateTotalPages(total int64) int {
	if total == 0 {
		return 0
	}
	return int((total + int64(p.PageSize) - 1) / int64(p.PageSize))
}

// ValidateCreateUserRequest 验证创建用户请求
func ValidateCreateUserRequest(req CreateUserRequest) error {
	if err := validateUsername(req.Username); err != nil {
		return err
	}

	if err := validateEmail(req.Email); err != nil {
		return err
	}

	if err := validatePassword(req.Password); err != nil {
		return err
	}

	return nil
}

// ValidateUpdateUserRequest 验证更新用户请求
func ValidateUpdateUserRequest(req UpdateUserRequest) error {
	if req.Username != "" {
		if err := validateUsername(req.Username); err != nil {
			return err
		}
	}

	if req.Email != "" {
		if err := validateEmail(req.Email); err != nil {
			return err
		}
	}

	return nil
}

// WithDetails 为错误添加详情（扩展方法）
func WithDetails(err errors.BusinessError, details map[string]interface{}) errors.BusinessError {
	return errors.NewBusinessErrorWithDetails(err.Code(), err.Message(), details)
}
