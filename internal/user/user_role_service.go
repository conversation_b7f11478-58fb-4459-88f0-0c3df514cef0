package user

import (
	"errors"
	"fmt"
)

// 扩展UserService接口，添加角色相关方法
type UserRoleService interface {
	// AddRoleToUser 为用户添加角色
	AddRoleToUser(userID, roleID string) error

	// RemoveRoleFromUser 从用户中移除角色
	RemoveRoleFromUser(userID, roleID string) error

	// SetDefaultRoleForUser 设置用户的默认角色
	SetDefaultRoleForUser(userID, roleID string) error

	// GetUserRoles 获取用户的所有角色
	GetUserRoles(userID string) ([]*Role, error)

	// GetUsersByRole 获取拥有指定角色的所有用户
	GetUsersByRole(roleID string) ([]*User, error)

	// HasPermission 检查用户是否拥有指定权限
	HasPermission(userID, permissionID string) (bool, error)

	// HasResourcePermission 检查用户是否拥有指定资源的指定操作权限
	HasResourcePermission(userID, resource, action string) (bool, error)

	// HasRole 检查用户是否拥有指定角色
	HasRole(userID, roleID string) (bool, error)

	// RegisterWithRole 注册新用户并分配角色
	RegisterWithRole(username, email, password, roleID string) (*User, error)
}

// 扩展DefaultUserService，实现UserRoleService接口
func (s *DefaultUserService) AddRoleToUser(userID, roleID string) error {
	// 检查用户是否存在
	_, err := s.repository.FindByID(userID)
	if err != nil {
		return err
	}

	// 添加角色
	return s.repository.AddRole(userID, roleID)
}

func (s *DefaultUserService) RemoveRoleFromUser(userID, roleID string) error {
	// 检查用户是否存在
	_, err := s.repository.FindByID(userID)
	if err != nil {
		return err
	}

	// 移除角色
	return s.repository.RemoveRole(userID, roleID)
}

func (s *DefaultUserService) SetDefaultRoleForUser(userID, roleID string) error {
	// 检查用户是否存在
	_, err := s.repository.FindByID(userID)
	if err != nil {
		return err
	}

	// 设置默认角色
	return s.repository.SetDefaultRole(userID, roleID)
}

func (s *DefaultUserService) GetUserRoles(userID string) ([]*Role, error) {
	// 检查用户是否存在
	_, err := s.repository.FindByID(userID)
	if err != nil {
		return nil, err
	}

	// 获取用户角色ID列表
	roleIDs, err := s.repository.FindRoles(userID)
	if err != nil {
		return nil, err
	}

	// 获取角色详情
	var roles []*Role
	for _, roleID := range roleIDs {
		role, err := s.roleRepository.FindByID(roleID)
		if err != nil {
			return nil, err
		}
		roles = append(roles, role)
	}
	return roles, nil
}

func (s *DefaultUserService) GetUsersByRole(roleID string) ([]*User, error) {
	// 获取拥有指定角色的所有用户
	return s.repository.FindByRole(roleID)
}

func (s *DefaultUserService) HasPermission(userID, permissionID string) (bool, error) {
	// 检查用户是否拥有指定权限
	return s.repository.HasPermission(userID, permissionID)
}

func (s *DefaultUserService) HasResourcePermission(userID, resource, action string) (bool, error) {
	// 根据资源和操作获取权限
	permission, err := s.permissionRepository.FindByResourceAction(resource, action)
	if err != nil {
		if errors.Is(err, ErrPermissionNotFound) {
			return false, nil
		}
		return false, err
	}

	// 检查用户是否拥有该权限
	return s.HasPermission(userID, permission.ID)
}

func (s *DefaultUserService) HasRole(userID, roleID string) (bool, error) {
	// 检查用户是否存在
	user, err := s.repository.FindByID(userID)
	if err != nil {
		return false, err
	}

	// 检查默认角色
	if user.DefaultRoleID == roleID {
		return true, nil
	}

	// 获取用户角色ID列表
	roleIDs, err := s.repository.FindRoles(userID)
	if err != nil {
		return false, err
	}

	// 检查是否包含指定角色
	for _, id := range roleIDs {
		if id == roleID {
			return true, nil
		}
	}

	return false, nil
}

func (s *DefaultUserService) RegisterWithRole(username, email, password, roleID string) (*User, error) {
	// 注册用户
	user, err := s.Register(username, email, password)
	if err != nil {
		return nil, err
	}

	// 设置默认角色
	if roleID != "" {
		err = s.repository.SetDefaultRole(user.ID, roleID)
		if err != nil {
			return nil, fmt.Errorf("设置默认角色失败: %w", err)
		}
	}

	return user, nil
}
