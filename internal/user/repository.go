package user

import (
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// PostgresUserRepository PostgreSQL用户存储库实现
type PostgresUserRepository struct {
	db *sql.DB
}

// NewPostgresUserRepository 创建新的PostgreSQL用户存储库
func NewPostgresUserRepository(db *sql.DB) *PostgresUserRepository {
	return &PostgresUserRepository{
		db: db,
	}
}

// Create 创建新用户
func (r *PostgresUserRepository) Create(user *User) error {
	// 检查用户名是否已存在
	existingUser, err := r.FindByUsername(user.Username)
	if err != nil && !errors.Is(err, ErrUserNotFound) {
		return fmt.Errorf("检查用户名是否存在失败: %w", err)
	}
	if existingUser != nil {
		return ErrUserAlreadyExists
	}

	// 检查邮箱是否已存在
	existingUser, err = r.FindByEmail(user.Email)
	if err != nil && !errors.Is(err, ErrUserNotFound) {
		return fmt.Errorf("检查邮箱是否存在失败: %w", err)
	}
	if existingUser != nil {
		return ErrUserAlreadyExists
	}

	// 如果没有ID，生成一个新的UUID
	if user.ID == "" {
		user.ID = uuid.New().String()
	}

	// 设置创建和更新时间
	now := time.Now()
	user.CreatedAt = now
	user.UpdatedAt = now

	// 插入用户记录
	_, err = r.db.Exec(
		`INSERT INTO users (id, username, email, password_hash, callback_url, client_id, is_active, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
		user.ID, user.Username, user.Email, user.PasswordHash, user.CallbackURL, user.ClientID, user.IsActive, user.CreatedAt, user.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("创建用户失败: %w", err)
	}

	return nil
}

// FindByID 根据ID查找用户
func (r *PostgresUserRepository) FindByID(id string) (*User, error) {
	var user User
	var callbackURL sql.NullString

	err := r.db.QueryRow(
		`SELECT id, username, email, password_hash, callback_url, client_id, is_active, created_at, updated_at
		FROM users WHERE id = $1`,
		id,
	).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash, &callbackURL, &user.ClientID, &user.IsActive, &user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 处理callback_url的NULL值
	if callbackURL.Valid {
		user.CallbackURL = callbackURL.String
	}

	return &user, nil
}

// FindByUsername 根据用户名查找用户
func (r *PostgresUserRepository) FindByUsername(username string) (*User, error) {
	var user User
	var callbackURL sql.NullString

	err := r.db.QueryRow(
		`SELECT id, username, email, password_hash, callback_url, client_id, is_active, created_at, updated_at
		FROM users WHERE username = $1`,
		username,
	).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash, &callbackURL, &user.ClientID, &user.IsActive, &user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 处理callback_url的NULL值
	if callbackURL.Valid {
		user.CallbackURL = callbackURL.String
	}

	return &user, nil
}

// FindByEmail 根据邮箱查找用户
func (r *PostgresUserRepository) FindByEmail(email string) (*User, error) {
	var user User
	var callbackURL sql.NullString

	err := r.db.QueryRow(
		`SELECT id, username, email, password_hash, callback_url, client_id, is_active, created_at, updated_at
		FROM users WHERE email = $1`,
		email,
	).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash, &callbackURL, &user.ClientID, &user.IsActive, &user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 处理callback_url的NULL值
	if callbackURL.Valid {
		user.CallbackURL = callbackURL.String
	}

	return &user, nil
}

// Update 更新用户信息
func (r *PostgresUserRepository) Update(user *User) error {
	// 更新更新时间
	user.UpdatedAt = time.Now()

	// 更新用户记录
	result, err := r.db.Exec(
		`UPDATE users SET username = $1, email = $2, password_hash = $3, callback_url = $4, client_id = $5, is_active = $6, updated_at = $7
		WHERE id = $8`,
		user.Username, user.Email, user.PasswordHash, user.CallbackURL, user.ClientID, user.IsActive, user.UpdatedAt, user.ID,
	)
	if err != nil {
		return fmt.Errorf("更新用户失败: %w", err)
	}

	// 检查是否有记录被更新
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}
	if rowsAffected == 0 {
		return ErrUserNotFound
	}

	return nil
}

// FindByClientID 根据客户端ID查找用户
func (r *PostgresUserRepository) FindByClientID(clientID string) (*User, error) {
	var user User
	var callbackURL sql.NullString

	err := r.db.QueryRow(
		`SELECT id, username, email, password_hash, callback_url, client_id, is_active, created_at, updated_at
		FROM users WHERE client_id = $1`,
		clientID,
	).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash, &callbackURL, &user.ClientID, &user.IsActive, &user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	// 处理callback_url的NULL值
	if callbackURL.Valid {
		user.CallbackURL = callbackURL.String
	}

	return &user, nil
}

// Delete 删除用户
func (r *PostgresUserRepository) Delete(id string) error {
	// 删除用户记录
	result, err := r.db.Exec("DELETE FROM users WHERE id = $1", id)
	if err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}

	// 检查是否有记录被删除
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}
	if rowsAffected == 0 {
		return ErrUserNotFound
	}

	return nil
}
