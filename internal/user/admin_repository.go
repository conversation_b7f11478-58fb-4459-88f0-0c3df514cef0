package user

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/errors"
	"go.uber.org/zap"
)

// PostgresAdminUserRepository PostgreSQL管理员用户存储库实现
type PostgresAdminUserRepository struct {
	*PostgresUserRepository
	logger *zap.Logger
}

// NewPostgresAdminUserRepository 创建新的PostgreSQL管理员用户存储库
func NewPostgresAdminUserRepository(db *sql.DB, logger *zap.Logger) AdminUserRepository {
	baseRepo := NewPostgresUserRepository(db)
	return &PostgresAdminUserRepository{
		PostgresUserRepository: baseRepo,
		logger:                 logger,
	}
}

// FindWithFilter 根据过滤条件查找用户（分页）
func (r *PostgresAdminUserRepository) FindWithFilter(filter UserFilter, pagination Pagination) ([]*UserWithRoles, int64, error) {
	// 构建查询条件
	conditions, args, argIndex := r.buildFilterConditions(filter)

	// 构建WHERE子句
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 构建ORDER BY子句
	orderClause := r.buildOrderClause(pagination)

	// 计算偏移量
	offset := pagination.GetOffset()

	// 查询总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(DISTINCT u.id)
		FROM users u
		LEFT JOIN user_roles ur ON u.id = ur.user_id
		LEFT JOIN roles r ON ur.role_id = r.id OR u.default_role_id = r.id
		%s
	`, whereClause)

	var total int64
	err := r.db.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		r.logger.Error("Failed to count users", zap.Error(err))
		return nil, 0, fmt.Errorf("查询用户总数失败: %w", err)
	}

	// 查询用户数据
	dataQuery := fmt.Sprintf(`
		SELECT DISTINCT
			u.id, u.username, u.email, u.password_hash, u.callback_url,
			u.client_id, u.is_active, u.created_at, u.updated_at, u.deleted_at,
			COALESCE(u.default_role_id, '') as default_role_id,
			COALESCE(ul.last_login, '1970-01-01 00:00:00'::timestamp) as last_login,
			COALESCE(ul.login_count, 0) as login_count
		FROM users u
		LEFT JOIN user_roles ur ON u.id = ur.user_id
		LEFT JOIN roles r ON ur.role_id = r.id OR u.default_role_id = r.id
		LEFT JOIN user_login_stats ul ON u.id = ul.user_id
		%s
		%s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderClause, argIndex, argIndex+1)

	args = append(args, pagination.PageSize, offset)

	rows, err := r.db.Query(dataQuery, args...)
	if err != nil {
		r.logger.Error("Failed to query users", zap.Error(err))
		return nil, 0, fmt.Errorf("查询用户列表失败: %w", err)
	}
	defer rows.Close()

	var users []*UserWithRoles
	for rows.Next() {
		var user UserWithRoles
		user.User = &User{} // 初始化User指针
		var defaultRoleID sql.NullString
		var callbackURL sql.NullString
		var lastLogin time.Time

		err := rows.Scan(
			&user.User.ID, &user.User.Username, &user.User.Email, &user.User.PasswordHash,
			&callbackURL, &user.User.ClientID, &user.User.IsActive,
			&user.User.CreatedAt, &user.User.UpdatedAt, &user.User.DeletedAt,
			&defaultRoleID, &lastLogin, &user.LoginCount,
		)
		if err != nil {
			r.logger.Error("Failed to scan user row", zap.Error(err))
			return nil, 0, fmt.Errorf("扫描用户行失败: %w", err)
		}

		// 设置回调URL
		if callbackURL.Valid {
			user.User.CallbackURL = callbackURL.String
		}

		// 设置默认角色ID
		if defaultRoleID.Valid {
			user.User.DefaultRoleID = defaultRoleID.String
		}

		// 设置最后登录时间
		if !lastLogin.IsZero() && lastLogin.Year() > 1970 {
			user.LastLogin = &lastLogin
		}

		// 查询用户角色
		roles, err := r.getUserRoles(user.ID)
		if err != nil {
			r.logger.Warn("Failed to get user roles",
				zap.Error(err),
				zap.String("user_id", user.ID))
			// 不中断流程，设置空角色列表
			roles = []*Role{}
		}
		user.Roles = roles

		users = append(users, &user)
	}

	if err := rows.Err(); err != nil {
		r.logger.Error("Error iterating user rows", zap.Error(err))
		return nil, 0, fmt.Errorf("迭代用户行失败: %w", err)
	}

	r.logger.Info("Successfully queried users with filter",
		zap.Int64("total", total),
		zap.Int("returned", len(users)),
		zap.Any("filter", filter))

	return users, total, nil
}

// buildFilterConditions 构建过滤条件
func (r *PostgresAdminUserRepository) buildFilterConditions(filter UserFilter) ([]string, []any, int) {
	var conditions []string
	var args []any
	argIndex := 1

	// 基础条件：根据IncludeDeleted参数决定是否排除软删除的用户
	if !filter.IncludeDeleted {
		conditions = append(conditions, "u.deleted_at IS NULL")
	}

	// 关键词搜索（用户名或邮箱）
	if filter.Keyword != "" {
		conditions = append(conditions, fmt.Sprintf("(u.username ILIKE $%d OR u.email ILIKE $%d)", argIndex, argIndex))
		keyword := "%" + filter.Keyword + "%"
		args = append(args, keyword)
		argIndex++
	}

	// 状态过滤
	if filter.Status != nil {
		conditions = append(conditions, fmt.Sprintf("u.is_active = $%d", argIndex))
		args = append(args, *filter.Status)
		argIndex++
	}

	// 角色过滤
	if filter.RoleID != "" {
		conditions = append(conditions, fmt.Sprintf("(ur.role_id = $%d OR u.default_role_id = $%d)", argIndex, argIndex))
		args = append(args, filter.RoleID)
		argIndex++
	}

	// 创建时间范围过滤
	if filter.CreatedAfter != nil {
		conditions = append(conditions, fmt.Sprintf("u.created_at >= $%d", argIndex))
		args = append(args, *filter.CreatedAfter)
		argIndex++
	}

	if filter.CreatedBefore != nil {
		conditions = append(conditions, fmt.Sprintf("u.created_at <= $%d", argIndex))
		args = append(args, *filter.CreatedBefore)
		argIndex++
	}

	// 最后登录时间范围过滤
	if filter.LastLoginAfter != nil {
		conditions = append(conditions, fmt.Sprintf("ul.last_login >= $%d", argIndex))
		args = append(args, *filter.LastLoginAfter)
		argIndex++
	}

	if filter.LastLoginBefore != nil {
		conditions = append(conditions, fmt.Sprintf("ul.last_login <= $%d", argIndex))
		args = append(args, *filter.LastLoginBefore)
		argIndex++
	}

	return conditions, args, argIndex
}

// buildOrderClause 构建排序子句
func (r *PostgresAdminUserRepository) buildOrderClause(pagination Pagination) string {
	// 映射前端字段到数据库字段
	fieldMap := map[string]string{
		"username":   "u.username",
		"email":      "u.email",
		"created_at": "u.created_at",
		"updated_at": "u.updated_at",
		"last_login": "ul.last_login",
	}

	dbField, exists := fieldMap[pagination.OrderBy]
	if !exists {
		dbField = "u.created_at" // 默认排序字段
	}

	order := strings.ToUpper(pagination.Order)
	if order != "ASC" && order != "DESC" {
		order = "DESC" // 默认排序方向
	}

	return fmt.Sprintf("ORDER BY %s %s", dbField, order)
}

// getUserRoles 获取用户角色
func (r *PostgresAdminUserRepository) getUserRoles(userID string) ([]*Role, error) {
	query := `
		SELECT DISTINCT r.id, r.name, r.description, r.is_system, r.created_at, r.updated_at
		FROM roles r
		WHERE r.id IN (
			SELECT role_id FROM user_roles WHERE user_id = $1
			UNION
			SELECT default_role_id FROM users WHERE id = $1 AND default_role_id IS NOT NULL
		)
		ORDER BY r.name
	`

	rows, err := r.db.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户角色失败: %w", err)
	}
	defer rows.Close()

	var roles []*Role
	for rows.Next() {
		var role Role
		err := rows.Scan(
			&role.ID, &role.Name, &role.Description,
			&role.IsSystem, &role.CreatedAt, &role.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描角色行失败: %w", err)
		}
		roles = append(roles, &role)
	}

	return roles, rows.Err()
}

// GetUserStatistics 获取用户统计信息
func (r *PostgresAdminUserRepository) GetUserStatistics() (*UserStatistics, error) {
	query := `
		SELECT
			COUNT(*) as total_users,
			COUNT(CASE WHEN is_active = true THEN 1 END) as active_users,
			COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_users,
			COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as new_users_today,
			COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as new_users_this_week,
			COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as new_users_this_month
		FROM users
		WHERE deleted_at IS NULL
	`

	var stats UserStatistics
	err := r.db.QueryRow(query).Scan(
		&stats.TotalUsers,
		&stats.ActiveUsers,
		&stats.InactiveUsers,
		&stats.NewUsersToday,
		&stats.NewUsersThisWeek,
		&stats.NewUsersThisMonth,
	)
	if err != nil {
		r.logger.Error("Failed to get user statistics", zap.Error(err))
		return nil, fmt.Errorf("获取用户统计信息失败: %w", err)
	}

	// 获取在线用户数（最近15分钟有活动的用户）
	onlineQuery := `
		SELECT COUNT(DISTINCT user_id)
		FROM user_login_stats
		WHERE last_activity >= NOW() - INTERVAL '15 minutes'
	`
	err = r.db.QueryRow(onlineQuery).Scan(&stats.OnlineUsers)
	if err != nil {
		r.logger.Warn("Failed to get online users count", zap.Error(err))
		// 不中断流程，设置为0
		stats.OnlineUsers = 0
	}

	r.logger.Info("Successfully retrieved user statistics",
		zap.Int64("total_users", stats.TotalUsers),
		zap.Int64("active_users", stats.ActiveUsers),
		zap.Int64("online_users", stats.OnlineUsers))

	return &stats, nil
}

// SoftDelete 软删除用户
func (r *PostgresAdminUserRepository) SoftDelete(userID string) error {
	query := `
		UPDATE users
		SET deleted_at = NOW(), is_active = false, updated_at = NOW()
		WHERE id = $1 AND deleted_at IS NULL
	`

	result, err := r.db.Exec(query, userID)
	if err != nil {
		r.logger.Error("Failed to soft delete user",
			zap.Error(err),
			zap.String("user_id", userID))
		return fmt.Errorf("软删除用户失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Failed to get rows affected", zap.Error(err))
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return errors.NewBusinessError(
			errors.ErrCodeNotFound,
			"用户不存在或已被删除",
		)
	}

	r.logger.Info("Successfully soft deleted user", zap.String("user_id", userID))
	return nil
}

// UpdatePassword 更新用户密码
func (r *PostgresAdminUserRepository) UpdatePassword(userID, passwordHash string) error {
	query := `
		UPDATE users
		SET password_hash = $1, updated_at = NOW()
		WHERE id = $2 AND deleted_at IS NULL
	`

	result, err := r.db.Exec(query, passwordHash, userID)
	if err != nil {
		r.logger.Error("Failed to update user password",
			zap.Error(err),
			zap.String("user_id", userID))
		return fmt.Errorf("更新用户密码失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Failed to get rows affected", zap.Error(err))
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return errors.NewBusinessError(
			errors.ErrCodeNotFound,
			"用户不存在",
		)
	}

	r.logger.Info("Successfully updated user password", zap.String("user_id", userID))
	return nil
}

// ForceDelete 永久删除用户（硬删除）
func (r *PostgresAdminUserRepository) ForceDelete(userID string) error {
	// 开始事务
	tx, err := r.db.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 删除用户角色关联
	_, err = tx.Exec("DELETE FROM user_roles WHERE user_id = $1", userID)
	if err != nil {
		return fmt.Errorf("删除用户角色关联失败: %w", err)
	}

	// 删除用户余额记录
	_, err = tx.Exec("DELETE FROM user_balances WHERE user_id = $1", userID)
	if err != nil {
		return fmt.Errorf("删除用户余额记录失败: %w", err)
	}

	// 删除用户相关的订单记录（如果有的话）
	_, err = tx.Exec("DELETE FROM order_records WHERE user_id = $1", userID)
	if err != nil {
		return fmt.Errorf("删除用户订单记录失败: %w", err)
	}

	// 删除用户相关的回调记录
	_, err = tx.Exec("DELETE FROM unified_callback_records WHERE user_id = $1", userID)
	if err != nil {
		return fmt.Errorf("删除用户回调记录失败: %w", err)
	}

	// 最后删除用户本身
	result, err := tx.Exec("DELETE FROM users WHERE id = $1", userID)
	if err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("用户不存在或已被删除")
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	r.logger.Info("Successfully force deleted user", zap.String("user_id", userID))
	return nil
}

// GetUserWithRoles 获取包含角色信息的用户
func (r *PostgresAdminUserRepository) GetUserWithRoles(userID string) (*UserWithRoles, error) {
	// 查询用户基本信息
	user, err := r.FindByID(userID)
	if err != nil {
		return nil, err
	}

	// 查询用户角色
	roles, err := r.getUserRoles(userID)
	if err != nil {
		r.logger.Warn("Failed to get user roles",
			zap.Error(err),
			zap.String("user_id", userID))
		roles = []*Role{} // 设置空角色列表
	}

	// 查询登录统计
	var lastLogin sql.NullTime
	var loginCount int64
	loginQuery := `
		SELECT last_login, login_count
		FROM user_login_stats
		WHERE user_id = $1
	`
	err = r.db.QueryRow(loginQuery, userID).Scan(&lastLogin, &loginCount)
	if err != nil && err != sql.ErrNoRows {
		r.logger.Warn("Failed to get user login stats",
			zap.Error(err),
			zap.String("user_id", userID))
	}

	userWithRoles := &UserWithRoles{
		User:       user,
		Roles:      roles,
		LoginCount: loginCount,
	}

	if lastLogin.Valid {
		userWithRoles.LastLogin = &lastLogin.Time
	}

	return userWithRoles, nil
}

// BatchUpdateStatus 批量更新用户状态
func (r *PostgresAdminUserRepository) BatchUpdateStatus(userIDs []string, isActive bool) error {
	if len(userIDs) == 0 {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			"用户ID列表不能为空",
		)
	}

	// 构建占位符
	placeholders := make([]string, len(userIDs))
	args := make([]any, len(userIDs)+1)
	args[0] = isActive

	for i, userID := range userIDs {
		placeholders[i] = fmt.Sprintf("$%d", i+2)
		args[i+1] = userID
	}

	query := fmt.Sprintf(`
		UPDATE users
		SET is_active = $1, updated_at = NOW()
		WHERE id IN (%s) AND deleted_at IS NULL
	`, strings.Join(placeholders, ","))

	result, err := r.db.Exec(query, args...)
	if err != nil {
		r.logger.Error("Failed to batch update user status",
			zap.Error(err),
			zap.Strings("user_ids", userIDs),
			zap.Bool("is_active", isActive))
		return fmt.Errorf("批量更新用户状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Failed to get rows affected", zap.Error(err))
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	r.logger.Info("Successfully batch updated user status",
		zap.Strings("user_ids", userIDs),
		zap.Bool("is_active", isActive),
		zap.Int64("rows_affected", rowsAffected))

	return nil
}
