package user

import (
	"database/sql"
	"encoding/json"
	"regexp"
	"time"

	"github.com/your-org/go-kuaidi/internal/errors"
	"go.uber.org/zap"
)

// ValidationRule 验证规则
type ValidationRule struct {
	ID          string                 `json:"id" db:"id"`
	Name        string                 `json:"name" db:"name"`
	Type        string                 `json:"type" db:"type"`
	Config      map[string]interface{} `json:"config" db:"config"`
	IsActive    bool                   `json:"is_active" db:"is_active"`
	Description string                 `json:"description" db:"description"`
	CreatedAt   time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at" db:"updated_at"`
}

// ValidationRuleRepository 验证规则存储库接口
type ValidationRuleRepository interface {
	// FindByName 根据名称查找验证规则
	FindByName(name string) (*ValidationRule, error)

	// FindActiveRules 获取所有激活的验证规则
	FindActiveRules() ([]*ValidationRule, error)

	// Create 创建验证规则
	Create(rule *ValidationRule) error

	// Update 更新验证规则
	Update(rule *ValidationRule) error

	// Delete 删除验证规则
	Delete(id string) error
}

// PostgresValidationRuleRepository PostgreSQL验证规则存储库实现
type PostgresValidationRuleRepository struct {
	db     *sql.DB
	logger *zap.Logger
}

// NewPostgresValidationRuleRepository 创建PostgreSQL验证规则存储库
func NewPostgresValidationRuleRepository(db *sql.DB, logger *zap.Logger) ValidationRuleRepository {
	return &PostgresValidationRuleRepository{
		db:     db,
		logger: logger,
	}
}

// FindByName 根据名称查找验证规则
func (r *PostgresValidationRuleRepository) FindByName(name string) (*ValidationRule, error) {
	query := `
		SELECT id, name, type, config, is_active, description, created_at, updated_at
		FROM validation_rules
		WHERE name = $1 AND is_active = true
	`

	var rule ValidationRule
	var configJSON string

	err := r.db.QueryRow(query, name).Scan(
		&rule.ID,
		&rule.Name,
		&rule.Type,
		&configJSON,
		&rule.IsActive,
		&rule.Description,
		&rule.CreatedAt,
		&rule.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrValidationRuleNotFound
		}
		r.logger.Error("Failed to find validation rule by name", zap.Error(err), zap.String("name", name))
		return nil, err
	}

	// 解析配置JSON
	if err := json.Unmarshal([]byte(configJSON), &rule.Config); err != nil {
		r.logger.Error("Failed to unmarshal validation rule config", zap.Error(err), zap.String("rule_id", rule.ID))
		return nil, err
	}

	return &rule, nil
}

// FindActiveRules 获取所有激活的验证规则
func (r *PostgresValidationRuleRepository) FindActiveRules() ([]*ValidationRule, error) {
	query := `
		SELECT id, name, type, config, is_active, description, created_at, updated_at
		FROM validation_rules
		WHERE is_active = true
		ORDER BY name
	`

	rows, err := r.db.Query(query)
	if err != nil {
		r.logger.Error("Failed to query active validation rules", zap.Error(err))
		return nil, err
	}
	defer rows.Close()

	var rules []*ValidationRule
	for rows.Next() {
		var rule ValidationRule
		var configJSON string

		err := rows.Scan(
			&rule.ID,
			&rule.Name,
			&rule.Type,
			&configJSON,
			&rule.IsActive,
			&rule.Description,
			&rule.CreatedAt,
			&rule.UpdatedAt,
		)
		if err != nil {
			r.logger.Error("Failed to scan validation rule", zap.Error(err))
			continue
		}

		// 解析配置JSON
		if err := json.Unmarshal([]byte(configJSON), &rule.Config); err != nil {
			r.logger.Error("Failed to unmarshal validation rule config", zap.Error(err), zap.String("rule_id", rule.ID))
			continue
		}

		rules = append(rules, &rule)
	}

	return rules, rows.Err()
}

// Create 创建验证规则
func (r *PostgresValidationRuleRepository) Create(rule *ValidationRule) error {
	// 序列化配置
	configJSON, err := json.Marshal(rule.Config)
	if err != nil {
		r.logger.Error("Failed to marshal validation rule config", zap.Error(err))
		return err
	}

	query := `
		INSERT INTO validation_rules (id, name, type, config, is_active, description, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	now := time.Now()
	rule.CreatedAt = now
	rule.UpdatedAt = now

	_, err = r.db.Exec(query,
		rule.ID,
		rule.Name,
		rule.Type,
		string(configJSON),
		rule.IsActive,
		rule.Description,
		rule.CreatedAt,
		rule.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("Failed to create validation rule", zap.Error(err), zap.String("name", rule.Name))
		return err
	}

	r.logger.Info("Successfully created validation rule", zap.String("id", rule.ID), zap.String("name", rule.Name))
	return nil
}

// Update 更新验证规则
func (r *PostgresValidationRuleRepository) Update(rule *ValidationRule) error {
	// 序列化配置
	configJSON, err := json.Marshal(rule.Config)
	if err != nil {
		r.logger.Error("Failed to marshal validation rule config", zap.Error(err))
		return err
	}

	query := `
		UPDATE validation_rules
		SET name = $2, type = $3, config = $4, is_active = $5, description = $6, updated_at = $7
		WHERE id = $1
	`

	rule.UpdatedAt = time.Now()

	result, err := r.db.Exec(query,
		rule.ID,
		rule.Name,
		rule.Type,
		string(configJSON),
		rule.IsActive,
		rule.Description,
		rule.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("Failed to update validation rule", zap.Error(err), zap.String("id", rule.ID))
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Failed to get rows affected", zap.Error(err))
		return err
	}

	if rowsAffected == 0 {
		return ErrValidationRuleNotFound
	}

	r.logger.Info("Successfully updated validation rule", zap.String("id", rule.ID), zap.String("name", rule.Name))
	return nil
}

// Delete 删除验证规则
func (r *PostgresValidationRuleRepository) Delete(id string) error {
	query := `DELETE FROM validation_rules WHERE id = $1`

	result, err := r.db.Exec(query, id)
	if err != nil {
		r.logger.Error("Failed to delete validation rule", zap.Error(err), zap.String("id", id))
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Failed to get rows affected", zap.Error(err))
		return err
	}

	if rowsAffected == 0 {
		return ErrValidationRuleNotFound
	}

	r.logger.Info("Successfully deleted validation rule", zap.String("id", id))
	return nil
}

// ValidationService 验证服务接口
type ValidationService interface {
	// ValidateUsername 验证用户名
	ValidateUsername(username string) error

	// ValidateEmail 验证邮箱
	ValidateEmail(email string) error

	// ValidatePassword 验证密码
	ValidatePassword(password string) error

	// ValidateField 验证字段
	ValidateField(fieldName, value string) error

	// LoadRules 加载验证规则
	LoadRules() error
}

// DefaultValidationService 默认验证服务实现
type DefaultValidationService struct {
	repository ValidationRuleRepository
	rules      map[string]*ValidationRule
	logger     *zap.Logger
}

// NewDefaultValidationService 创建默认验证服务
func NewDefaultValidationService(repository ValidationRuleRepository, logger *zap.Logger) ValidationService {
	return &DefaultValidationService{
		repository: repository,
		rules:      make(map[string]*ValidationRule),
		logger:     logger,
	}
}

// LoadRules 加载验证规则
func (s *DefaultValidationService) LoadRules() error {
	rules, err := s.repository.FindActiveRules()
	if err != nil {
		s.logger.Error("Failed to load validation rules", zap.Error(err))
		return err
	}

	// 清空现有规则
	s.rules = make(map[string]*ValidationRule)

	// 加载新规则
	for _, rule := range rules {
		s.rules[rule.Name] = rule
	}

	s.logger.Info("Successfully loaded validation rules", zap.Int("count", len(rules)))
	return nil
}

// ValidateUsername 验证用户名
func (s *DefaultValidationService) ValidateUsername(username string) error {
	return s.ValidateField("username", username)
}

// ValidateEmail 验证邮箱
func (s *DefaultValidationService) ValidateEmail(email string) error {
	return s.ValidateField("email", email)
}

// ValidatePassword 验证密码
func (s *DefaultValidationService) ValidatePassword(password string) error {
	// 先验证密码长度
	if err := s.ValidateField("password", password); err != nil {
		return err
	}

	// 再验证密码强度
	if err := s.ValidateField("password_strength", password); err != nil {
		return err
	}

	return nil
}

// ValidateField 验证字段
func (s *DefaultValidationService) ValidateField(fieldName, value string) error {
	rule, exists := s.rules[fieldName]
	if !exists {
		// 如果没有找到规则，使用默认验证
		return s.validateWithDefaults(fieldName, value)
	}

	return s.validateWithRule(rule, value)
}

// validateWithRule 使用规则验证
func (s *DefaultValidationService) validateWithRule(rule *ValidationRule, value string) error {
	switch rule.Type {
	case "length":
		return s.validateLength(rule, value)
	case "regex":
		return s.validateRegex(rule, value)
	case "required":
		return s.validateRequired(rule, value)
	default:
		s.logger.Warn("Unknown validation rule type", zap.String("type", rule.Type))
		return nil
	}
}

// validateLength 验证长度
func (s *DefaultValidationService) validateLength(rule *ValidationRule, value string) error {
	minLength, hasMin := rule.Config["min_length"].(float64)
	maxLength, hasMax := rule.Config["max_length"].(float64)

	if hasMin && len(value) < int(minLength) {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			rule.Config["min_error"].(string),
		)
	}

	if hasMax && len(value) > int(maxLength) {
		return errors.NewBusinessError(
			errors.ErrCodeInvalidRequest,
			rule.Config["max_error"].(string),
		)
	}

	return nil
}

// validateRegex 验证正则表达式
func (s *DefaultValidationService) validateRegex(rule *ValidationRule, value string) error {
	pattern, ok := rule.Config["pattern"].(string)
	if !ok {
		s.logger.Error("Invalid regex pattern in validation rule", zap.String("rule_id", rule.ID))
		return nil
	}

	matched, err := regexp.MatchString(pattern, value)
	if err != nil {
		s.logger.Error("Failed to match regex pattern", zap.Error(err), zap.String("pattern", pattern))
		return nil
	}

	if !matched {
		errorMsg, ok := rule.Config["error"].(string)
		if !ok {
			errorMsg = "格式不正确"
		}
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, errorMsg)
	}

	return nil
}

// validateRequired 验证必填
func (s *DefaultValidationService) validateRequired(rule *ValidationRule, value string) error {
	if value == "" {
		errorMsg, ok := rule.Config["error"].(string)
		if !ok {
			errorMsg = "不能为空"
		}
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, errorMsg)
	}
	return nil
}

// validateWithDefaults 使用默认规则验证
func (s *DefaultValidationService) validateWithDefaults(fieldName, value string) error {
	switch fieldName {
	case "username":
		return validateUsername(value)
	case "email":
		return validateEmail(value)
	case "password":
		return validatePassword(value)
	case "password_strength":
		return validatePasswordStrength(value)
	default:
		return nil
	}
}

// validatePasswordStrength 验证密码强度（回退函数）
func validatePasswordStrength(password string) error {
	// 检查密码是否包含至少一个小写字母、一个大写字母、一个数字和一个特殊字符
	hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)
	hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
	hasDigit := regexp.MustCompile(`\d`).MatchString(password)
	hasSpecial := regexp.MustCompile(`[@$!%*?&]`).MatchString(password)

	if !hasLower {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "密码必须包含至少一个小写字母")
	}
	if !hasUpper {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "密码必须包含至少一个大写字母")
	}
	if !hasDigit {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "密码必须包含至少一个数字")
	}
	if !hasSpecial {
		return errors.NewBusinessError(errors.ErrCodeInvalidRequest, "密码必须包含特殊字符")
	}

	return nil
}

// 错误定义
var (
	ErrValidationRuleNotFound = errors.NewBusinessError(errors.ErrCodeNotFound, "validation rule not found")
)
