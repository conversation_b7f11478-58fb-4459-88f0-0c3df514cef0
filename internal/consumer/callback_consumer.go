package consumer

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/your-org/go-kuaidi/internal/interfaces"
	"go.uber.org/zap"
)

// CallbackNotification 回调通知消息
type CallbackNotification struct {
	ID       string `json:"id"`
	Provider string `json:"provider"`
}

// CallbackRawData 原始回调数据
type CallbackRawData struct {
	ID         string            `json:"id"`
	Provider   string            `json:"provider"`
	RawBody    string            `json:"raw_body"`
	Headers    map[string]string `json:"headers"`
	ClientIP   string            `json:"client_ip"`
	ReceivedAt time.Time         `json:"received_at"`
	Processed  bool              `json:"processed"`
	CreatedAt  time.Time         `json:"created_at"`
}

// CallbackConsumer 回调消费者
type CallbackConsumer struct {
	receiverDB      *sql.DB // 回调接收服务的数据库
	mainSystemDB    *sql.DB // 主系统数据库
	redis           *redis.Client
	queueName       string
	callbackService interfaces.CallbackServiceInterface // 主系统原有的回调服务
	logger          *zap.Logger
}

func NewCallbackConsumer(
	receiverDB *sql.DB,
	mainSystemDB *sql.DB,
	redis *redis.Client,
	queueName string,
	callbackService interfaces.CallbackServiceInterface,
	logger *zap.Logger,
) *CallbackConsumer {
	return &CallbackConsumer{
		receiverDB:      receiverDB,
		mainSystemDB:    mainSystemDB,
		redis:           redis,
		queueName:       queueName,
		callbackService: callbackService,
		logger:          logger,
	}
}

// Start 启动消费者
func (c *CallbackConsumer) Start(ctx context.Context) error {
	c.logger.Info("启动回调消费者", zap.String("queue", c.queueName))

	for {
		select {
		case <-ctx.Done():
			c.logger.Info("停止回调消费者")
			return ctx.Err()
		default:
			// 从Redis队列获取通知
			result, err := c.redis.BRPop(ctx, 5*time.Second, c.queueName).Result()
			if err != nil {
				if err == redis.Nil {
					// 队列为空，继续等待
					continue
				}
				c.logger.Error("从Redis获取消息失败", zap.Error(err))
				time.Sleep(1 * time.Second)
				continue
			}

			if len(result) < 2 {
				continue
			}

			// 解析通知消息
			var notification CallbackNotification
			if err := json.Unmarshal([]byte(result[1]), &notification); err != nil {
				c.logger.Error("解析通知消息失败", zap.Error(err))
				continue
			}

			// 处理回调
			if err := c.processCallback(ctx, &notification); err != nil {
				c.logger.Error("处理回调失败",
					zap.String("id", notification.ID),
					zap.String("provider", notification.Provider),
					zap.Error(err))
			}
		}
	}
}

// processCallback 处理回调
func (c *CallbackConsumer) processCallback(ctx context.Context, notification *CallbackNotification) error {
	c.logger.Info("处理回调",
		zap.String("id", notification.ID),
		zap.String("provider", notification.Provider))

	// 1. 从回调接收服务数据库获取原始数据
	rawData, err := c.getRawCallbackData(ctx, notification.ID)
	if err != nil {
		return fmt.Errorf("获取原始回调数据失败: %w", err)
	}

	// 2. 调用主系统原有的回调处理逻辑
	response, err := c.callbackService.ProcessCallback(
		ctx,
		rawData.Provider,
		[]byte(rawData.RawBody),
		rawData.Headers,
	)

	if err != nil {
		c.logger.Error("主系统处理回调失败", zap.Error(err))
		return err
	}

	c.logger.Info("主系统处理回调成功",
		zap.String("id", notification.ID),
		zap.Any("response", response))

	// 3. 标记为已处理
	err = c.markAsProcessed(ctx, notification.ID)
	if err != nil {
		c.logger.Error("标记已处理失败", zap.Error(err))
		// 不返回错误，因为业务逻辑已经处理成功
	}

	c.logger.Info("回调处理完成", zap.String("id", notification.ID))
	return nil
}

// getRawCallbackData 从回调接收服务数据库获取原始数据
func (c *CallbackConsumer) getRawCallbackData(ctx context.Context, id string) (*CallbackRawData, error) {
	query := `
		SELECT id, provider, raw_body, headers, client_ip, 
		       received_at, processed, created_at
		FROM callback_raw_data 
		WHERE id = $1
	`

	row := c.receiverDB.QueryRowContext(ctx, query, id)

	var data CallbackRawData
	var headersJSON []byte

	err := row.Scan(
		&data.ID,
		&data.Provider,
		&data.RawBody,
		&headersJSON,
		&data.ClientIP,
		&data.ReceivedAt,
		&data.Processed,
		&data.CreatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("查询回调数据失败: %w", err)
	}

	// 反序列化headers
	err = json.Unmarshal(headersJSON, &data.Headers)
	if err != nil {
		return nil, fmt.Errorf("解析headers失败: %w", err)
	}

	return &data, nil
}

// markAsProcessed 标记为已处理
func (c *CallbackConsumer) markAsProcessed(ctx context.Context, id string) error {
	query := `UPDATE callback_raw_data SET processed = true WHERE id = $1`
	_, err := c.receiverDB.ExecContext(ctx, query, id)
	return err
}
