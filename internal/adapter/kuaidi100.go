package adapter

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// Kuaidi100Adapter 快递100适配器
type Kuaidi100Adapter struct {
	config             Kuaidi100Config
	client             *http.Client
	logger             *zap.Logger
	mappingService     express.ExpressMappingService
	expressCompanyRepo express.ExpressCompanyRepository
}

// Kuaidi100Config 快递100配置
type Kuaidi100Config struct {
	Key      string `json:"key"`      // 授权key
	Secret   string `json:"secret"`   // 授权secret
	Customer string `json:"customer"` // 授权ID
	BaseURL  string `json:"base_url"` // 接口地址
	Timeout  int    `json:"timeout"`  // 超时时间(秒)
}

// NewKuaidi100Adapter 创建快递100适配器
func NewKuaidi100Adapter(config Kuaidi100Config, expressCompanyRepo express.ExpressCompanyRepository) *Kuaidi100Adapter {
	// 创建日志记录器
	logger, _ := zap.NewProduction()

	// 优化HTTP客户端配置
	transport := &http.Transport{
		MaxIdleConns:          200,              // 增加最大空闲连接数
		MaxIdleConnsPerHost:   50,               // 增加每个主机最大空闲连接数
		IdleConnTimeout:       60 * time.Second, // 减少空闲连接超时
		DisableCompression:    false,            // 启用压缩
		ForceAttemptHTTP2:     true,             // 强制尝试HTTP/2
		ResponseHeaderTimeout: 8 * time.Second,  // 响应头超时
		ExpectContinueTimeout: 1 * time.Second,  // Expect: 100-continue超时
	}

	return &Kuaidi100Adapter{
		config:             config,
		expressCompanyRepo: expressCompanyRepo,
		client: &http.Client{
			Timeout:   time.Duration(config.Timeout) * time.Second,
			Transport: transport,
		},
		logger: logger,
		// mappingService 将在初始化后通过 SetMappingService 设置
	}
}

// SetMappingService 设置映射服务
func (a *Kuaidi100Adapter) SetMappingService(mappingService express.ExpressMappingService) {
	a.mappingService = mappingService
}

// Name 获取供应商名称
func (a *Kuaidi100Adapter) Name() string {
	return model.ExpressKuaidi100
}

// formatPhoneForKuaidi100Comprehensive 综合处理Mobile和Tel字段，符合快递100API要求
func (a *Kuaidi100Adapter) formatPhoneForKuaidi100Comprehensive(mobile string, tel string) (resultTel string, resultMobile string) {
	// 优先使用明确指定的tel字段（固定电话）
	if tel != "" {
		a.logger.Info("🔥 快递100电话格式化：使用专用Tel字段",
			zap.String("tel", tel))
		return tel, ""
	}

	// 优先使用明确指定的mobile字段（手机号）
	if mobile != "" {
		// 对mobile字段进行智能识别
		telResult, mobileResult := a.formatPhoneForKuaidi100(mobile)
		if mobileResult != "" {
			a.logger.Info("🔥 快递100电话格式化：Mobile字段识别为手机号",
				zap.String("mobile", mobile),
				zap.String("result", mobileResult))
			return "", mobileResult
		} else {
			a.logger.Info("🔥 快递100电话格式化：Mobile字段识别为固定电话",
				zap.String("mobile", mobile),
				zap.String("result", telResult))
			return telResult, ""
		}
	}

	// 都为空的情况
	return "", ""
}

// formatPhoneForKuaidi100 智能格式化电话号码，符合快递100API要求（使用生产级验证器）
// 快递100 API要求：手机号和电话号二者其一必填
func (a *Kuaidi100Adapter) formatPhoneForKuaidi100(phone string) (tel string, mobile string) {
	if phone == "" {
		return "", ""
	}

	// 使用生产级电话验证器
	validator := util.GetPhoneValidator()
	result := validator.ValidatePhone(phone)

	if !result.IsValid {
		a.logger.Warn("快递100电话格式化：电话号码格式无效",
			zap.String("original", phone),
			zap.String("error_code", result.ErrorCode))
		return phone, "" // 格式无效时作为固定电话处理
	}

	// 根据验证结果进行格式化
	switch result.Type {
	case util.PhoneTypeMobile:
		a.logger.Debug("快递100电话格式化：识别为手机号",
			zap.String("original", phone),
			zap.String("cleaned", result.Cleaned))
		return "", result.Cleaned
	case util.PhoneTypeLandline:
		a.logger.Debug("快递100电话格式化：识别为固定电话",
			zap.String("original", phone),
			zap.String("cleaned", result.Cleaned))
		return result.Cleaned, ""
	default:
		a.logger.Warn("快递100电话格式化：未知电话类型",
			zap.String("original", phone))
		return phone, ""
	}
}

// QueryPrice 查询价格
func (a *Kuaidi100Adapter) QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
	// 如果指定了供应商但不是kuaidi100，则返回空结果
	if req.Provider != "" && req.Provider != model.ExpressKuaidi100 {
		return []model.StandardizedPrice{}, nil
	}

	// 🔥 修复：先计算计费重量，再选择服务类型
	calcWeight := a.calculateChargedWeightFromPackage(req.ExpressType, req.Package)

	// 构建请求参数
	params := map[string]interface{}{
		"sender": map[string]interface{}{
			"name":     req.Sender.Name,
			"mobile":   req.Sender.Mobile,
			"province": req.Sender.Province,
			"city":     req.Sender.City,
			"district": req.Sender.District,
			"address":  req.Sender.Address,
		},
		"receiver": map[string]interface{}{
			"name":     req.Receiver.Name,
			"mobile":   req.Receiver.Mobile,
			"province": req.Receiver.Province,
			"city":     req.Receiver.City,
			"district": req.Receiver.District,
			"address":  req.Receiver.Address,
		},
		"package": map[string]interface{}{
			"weight":      calcWeight, // 🔥 修复：使用计费重量
			"volume":      req.Package.Volume,
			"quantity":    req.Package.Quantity,
			"insureValue": req.Package.InsureValue,
		},
		"payMethod":   req.PayMethod,
		"expressType": req.ExpressType,
		"productType": req.ProductType,
		"serviceType": a.getServiceTypeForExpress(req.ExpressType, calcWeight), // 🔥 修复：使用计费重量选择服务类型
		"weight":      calcWeight,                                              // 🔥 修复：使用计费重量
	}

	// 如果指定了快递公司，则只查询该快递公司
	if req.ExpressType != "" {
		fmt.Printf("[快递100] 查询单个快递公司 %s 价格\n", req.ExpressType)
		return a.querySingleExpressPrice(ctx, params, req.ExpressType)
	} else if req.QueryAllCompanies {
		// 如果是查询所有快递公司，但没有指定快递公司代码，则返回空结果
		// 因为我们现在使用单个查询的方式来实现查询所有快递公司
		fmt.Printf("[快递100] 必须指定快递公司代码，不支持批量查询所有快递公司价格\n")
		return []model.StandardizedPrice{}, nil
	} else {
		// 如果既没有指定快递公司，也没有设置查询所有快递公司，则返回错误
		return nil, fmt.Errorf("必须指定快递公司代码或设置查询所有快递公司")
	}
}

// 查询单个快递公司价格
func (a *Kuaidi100Adapter) querySingleExpressPrice(ctx context.Context, params map[string]interface{}, expressType string) ([]model.StandardizedPrice, error) {
	// 获取包裹重量
	pkg := params["package"].(map[string]interface{})
	weight := pkg["weight"].(float64)

	// 构建快递100价格查询参数
	kuaidiCom := a.mapExpressCode(expressType, weight)
	if kuaidiCom == "" {
		// 🔧 修复：映射失败时返回空结果而不是错误，避免阻塞其他供应商查询
		fmt.Printf("[快递100] 快递公司 %s 映射失败，跳过查询\n", expressType)
		return []model.StandardizedPrice{}, nil
	}

	// 构建发件人和收件人地址
	sender := params["sender"].(map[string]interface{})
	receiver := params["receiver"].(map[string]interface{})

	sendManPrintAddr := fmt.Sprintf("%s%s%s",
		sender["province"],
		sender["city"],
		sender["district"])

	recManPrintAddr := fmt.Sprintf("%s%s%s",
		receiver["province"],
		receiver["city"],
		receiver["district"])

	// 🔥 修复：智能处理电话号码格式，符合快递100API要求
	senderTel, senderMobile := a.formatPhoneForKuaidi100(sender["mobile"].(string))
	receiverTel, receiverMobile := a.formatPhoneForKuaidi100(receiver["mobile"].(string))

	// 🔥 企业级修复：快递100不支持体积参数，需要在本地计算体积重量
	// 计算正确的计费重量
	packageInfo := model.PackageInfo{
		Weight: weight,
		Volume: pkg["volume"].(float64),
	}
	// 尝试获取长宽高信息
	if length, ok := pkg["length"].(float64); ok {
		packageInfo.Length = length
	}
	if width, ok := pkg["width"].(float64); ok {
		packageInfo.Width = width
	}
	if height, ok := pkg["height"].(float64); ok {
		packageInfo.Height = height
	}
	calcWeight := a.calculateChargedWeightFromPackage(expressType, packageInfo)

	// 构建价格查询参数
	priceParams := map[string]interface{}{
		"kuaidicom":        kuaidiCom,
		"sendManPrintAddr": sendManPrintAddr,
		"recManPrintAddr":  recManPrintAddr,
		"weight":           fmt.Sprintf("%.1f", calcWeight),                     // 使用计费重量而不是实际重量
		"serviceType":      a.getServiceTypeForExpress(expressType, calcWeight), // 根据快递公司和重量选择服务类型
	}

	// 🔥 修复：根据电话类型只传递对应字段
	// 🚨 重要修复：快递100 API只支持 Mobile 字段，不支持 Tel 字段
	// 需要将固定电话也放入 Mobile 字段中
	if senderMobile != "" {
		params["sendManMobile"] = senderMobile
	} else if senderTel != "" {
		// 🔥 修复：快递100不支持sendManTel字段，固定电话也要放入sendManMobile
		params["sendManMobile"] = senderTel
	}
	if receiverMobile != "" {
		params["recManMobile"] = receiverMobile
	} else if receiverTel != "" {
		// 🔥 修复：快递100不支持recManTel字段，固定电话也要放入recManMobile
		params["recManMobile"] = receiverTel
	}

	// 调用快递100 API
	configManager := config.GetProviderConfigManager()
	priceMethod := configManager.GetAPIMethod("kuaidi100", "price_query")
	if priceMethod == "" {
		priceMethod = "price" // 默认值
	}
	result, err := a.callKuaidi100API(ctx, priceMethod, priceParams)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var response struct {
		Result     bool   `json:"result"`
		ReturnCode string `json:"returnCode"`
		Message    string `json:"message"`
		Data       struct {
			Price          string `json:"price"`          // 总价
			DefPrice       string `json:"defPrice"`       // 标准价
			FirstPrice     string `json:"firstPrice"`     // 首重价格
			OverPrice      string `json:"overPrice"`      // 续重总价
			OverPricePerKg string `json:"overPricePerKg"` // 每公斤续重价格
			ServiceType    string `json:"serviceType"`    // 服务类型
			KuaidiCom      string `json:"kuaidiCom"`      // 快递公司代码
		} `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if !response.Result || response.ReturnCode != "200" {
		// 特殊处理德邦物流的错误
		if expressType == "DBL" && strings.Contains(response.Message, "大于2.49公斤") {
			a.logger.Warn("德邦物流标准快递不支持超过2.49公斤的包裹",
				zap.String("express_code", expressType),
				zap.Float64("weight", weight),
				zap.String("error", response.Message))
			return nil, fmt.Errorf("德邦物流标准快递不支持超过2.49公斤的包裹，请使用德邦大件360或其他快递公司")
		} else if expressType == "DBL360" && strings.Contains(response.Message, "该区域暂时不开放") {
			a.logger.Warn("德邦大件360在当前区域不提供服务",
				zap.String("express_code", expressType),
				zap.Float64("weight", weight),
				zap.String("error", response.Message))
			return nil, fmt.Errorf("德邦大件360在当前区域不提供服务，请使用其他快递公司")
		}
		return nil, fmt.Errorf("查询价格失败: %s", response.Message)
	}

	// 转换价格字符串为浮点数
	price, _ := strconv.ParseFloat(response.Data.Price, 64)
	overPricePerKg, _ := strconv.ParseFloat(response.Data.OverPricePerKg, 64)

	// 🔥 企业级修复：使用之前计算的计费重量

	// 转换为标准价格结构
	standardPrice := model.StandardizedPrice{
		ExpressCode:          expressType, // 使用原始的快递公司代码，而不是转换后的
		ExpressName:          a.getExpressName(response.Data.KuaidiCom),
		ProductCode:          response.Data.ServiceType,
		ProductName:          response.Data.ServiceType,
		Price:                price,
		ContinuedWeightPerKg: overPricePerKg, // 每公斤续重价格
		CalcWeight:           calcWeight,
		Provider:             a.Name(),
		ChannelID:            fmt.Sprintf("%s_%s", a.Name(), response.Data.KuaidiCom),
	}

	return []model.StandardizedPrice{standardPrice}, nil
}

// CreateOrder 创建订单
func (a *Kuaidi100Adapter) CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResult, error) {
	// 🚀 新增：记录详细的请求信息
	a.logger.Info("快递100创建订单开始",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("express_type", req.ExpressType),
		zap.String("channel_id", req.ChannelID),
		zap.String("provider", "kuaidi100"))

	// 🔥 修复：强制使用查价时选择的渠道信息，确保价格一致性
	if req.ChannelID == "" {
		err := fmt.Errorf("快递100供应商必须提供渠道ID，请先查价获取正确的渠道信息")
		a.logger.Error("快递100创建订单失败：缺少渠道ID",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Error(err))
		return nil, err
	}

	// 从渠道ID中提取快递公司代码
	kuaidicom := strings.TrimPrefix(req.ChannelID, a.Name()+"_")
	if kuaidicom == "" || kuaidicom == req.ChannelID {
		err := fmt.Errorf("无效的渠道ID格式: %s，期望格式: kuaidi100_xxx", req.ChannelID)
		a.logger.Error("快递100创建订单失败：无效渠道ID格式",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("channel_id", req.ChannelID),
			zap.Error(err))
		return nil, err
	}

	fmt.Printf("[快递100API] 🔗 使用查价时选择的渠道: %s -> %s\n", req.ChannelID, kuaidicom)

	// 🔥 修复：计算正确的计费重量（考虑体积重量）
	calcWeight := a.calculateChargedWeightFromPackage(req.ExpressType, req.Package)

	// 🔥 修复：综合处理Mobile和Tel字段，符合快递100API要求
	senderTel, senderMobile := a.formatPhoneForKuaidi100Comprehensive(req.Sender.Mobile, req.Sender.Tel)
	receiverTel, receiverMobile := a.formatPhoneForKuaidi100Comprehensive(req.Receiver.Mobile, req.Receiver.Tel)

	// 构建请求参数
	params := map[string]interface{}{
		"kuaidicom":  kuaidicom,
		"recManName": req.Receiver.Name,
		"recManPrintAddr": fmt.Sprintf("%s%s%s%s",
			req.Receiver.Province,
			req.Receiver.City,
			req.Receiver.District,
			req.Receiver.Address),
		"sendManName": req.Sender.Name,
		"sendManPrintAddr": fmt.Sprintf("%s%s%s%s",
			req.Sender.Province,
			req.Sender.City,
			req.Sender.District,
			req.Sender.Address),
		"cargo":        req.Package.GoodsName,
		"weight":       fmt.Sprintf("%.1f", calcWeight),                         // 🔥 修复：使用计费重量而不是原始重量
		"payment":      "SHIPPER",                                               // 寄付
		"serviceType":  a.getServiceTypeForExpress(req.ExpressType, calcWeight), // 根据快递公司和重量选择服务类型
		"callBackUrl":  a.getCallbackURL(),                                      // 从配置获取回调地址
		"thirdOrderId": req.PlatformOrderNo,                                     // 🔥 新增：使用平台订单号作为第三方订单号
	}

	// 🔥 修复：根据电话类型只传递对应字段
	// 🚨 重要修复：快递100 API只支持 Mobile 字段，不支持 Tel 字段
	// 需要将固定电话也放入 Mobile 字段中
	if senderMobile != "" {
		params["sendManMobile"] = senderMobile
	} else if senderTel != "" {
		// 🔥 修复：快递100不支持sendManTel字段，固定电话也要放入sendManMobile
		params["sendManMobile"] = senderTel
	}
	if receiverMobile != "" {
		params["recManMobile"] = receiverMobile
	} else if receiverTel != "" {
		// 🔥 修复：快递100不支持recManTel字段，固定电话也要放入recManMobile
		params["recManMobile"] = receiverTel
	}

	// 添加备注
	if req.Package.Remark != "" {
		params["remark"] = req.Package.Remark
	}

	// 添加预约取件时间
	if req.Pickup.StartTime != "" && req.Pickup.EndTime != "" {
		// 🚀 修复：动态计算dayType，根据预约日期设置
		dayType := a.calculateDayType(req.Pickup.StartTime)
		params["dayType"] = dayType
		params["pickupStartTime"] = a.formatPickupTime(req.Pickup.StartTime)
		params["pickupEndTime"] = a.formatPickupTime(req.Pickup.EndTime)

		fmt.Printf("[快递100API] 添加预约取件时间 - dayType: %s, 开始: %s, 结束: %s\n",
			dayType, params["pickupStartTime"], params["pickupEndTime"])
	}

	// 🚀 新增：记录API调用开始
	a.logger.Info("快递100API调用开始",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("method", "order_create"),
		zap.String("kuaidicom", kuaidicom),
		zap.Any("request_data", params))

	// 调用快递100 API
	configManager := config.GetProviderConfigManager()
	createMethod := configManager.GetAPIMethod("kuaidi100", "order_create")
	if createMethod == "" {
		createMethod = "bOrder" // 默认值
	}
	result, err := a.callKuaidi100API(ctx, createMethod, params)
	if err != nil {
		// 🚀 新增：记录API调用失败
		a.logger.Error("快递100API调用失败",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("method", createMethod),
			zap.Error(err),
			zap.Any("request_data", params))
		return nil, err
	}

	// 🚀 新增：记录API调用成功
	a.logger.Info("快递100API调用成功",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("method", createMethod),
		zap.String("response", result))

	// 解析响应
	var response struct {
		Result     bool   `json:"result"`
		ReturnCode string `json:"returnCode"`
		Message    string `json:"message"`
		Data       struct {
			TaskId    string `json:"taskId"`
			OrderId   string `json:"orderId"`
			Kuaidinum string `json:"kuaidinum"`
			PollToken string `json:"pollToken"`
			EOrder    string `json:"eOrder"`
		} `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		// 🚀 新增：记录响应解析失败
		a.logger.Error("快递100响应解析失败",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("response", result),
			zap.Error(err))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 🚀 新增：记录响应解析成功
	a.logger.Info("快递100响应解析成功",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.Bool("result", response.Result),
		zap.String("return_code", response.ReturnCode),
		zap.String("message", response.Message))

	if !response.Result || response.ReturnCode != "200" {
		// 🔧 特殊处理：识别"供应商不支持"的情况
		if strings.Contains(response.Message, "大于2.49公斤") ||
			strings.Contains(response.Message, "该区域暂时不开放") ||
			strings.Contains(response.Message, "不支持") ||
			strings.Contains(response.Message, "暂未开放") {
			a.logger.Debug("快递100供应商不支持该快递公司或线路",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("express_type", req.ExpressType),
				zap.String("return_code", response.ReturnCode),
				zap.String("message", response.Message))
			return nil, fmt.Errorf("供应商不支持该快递公司: %s", response.Message)
		}

		// 🚀 详细记录业务失败详情（便于问题排查）
		a.logger.Error("快递100业务失败 - 完整失败信息",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.String("express_type", req.ExpressType),
			zap.String("error_type", "BUSINESS_FAILED"),
			zap.String("message", response.Message),
			zap.String("return_code", response.ReturnCode),
			zap.String("sender_info", fmt.Sprintf("%s-%s-%s", req.Sender.Province, req.Sender.City, req.Sender.District)),
			zap.String("receiver_info", fmt.Sprintf("%s-%s-%s", req.Receiver.Province, req.Receiver.City, req.Receiver.District)),
			zap.Float64("package_weight", req.Package.Weight),
			zap.Float64("package_volume", req.Package.Volume),
			zap.String("full_response", result))
		return nil, fmt.Errorf("%s", response.Message)
	}

	// 转换为标准订单结果
	orderResult := &model.OrderResult{
		OrderNo:    response.Data.OrderId,
		TrackingNo: response.Data.Kuaidinum,
		TaskId:     response.Data.TaskId,    // 保存taskId
		PollToken:  response.Data.PollToken, // 保存pollToken，用于查询物流轨迹
		PickupCode: "",                      // 快递100下单API不返回取件码
		Price:      0,                       // 快递100下单API不返回价格
		PrintData:  response.Data.EOrder,
	}

	return orderResult, nil
}

// CancelOrder 取消订单
func (a *Kuaidi100Adapter) CancelOrder(ctx context.Context, taskId string, orderNo string, reason string) error {
	// 构建请求参数
	params := map[string]interface{}{
		"taskId":    taskId,  // 任务ID
		"orderId":   orderNo, // 订单ID
		"cancelMsg": reason,  // 取消原因
	}

	// 调用快递100 API
	configManager := config.GetProviderConfigManager()
	cancelMethod := configManager.GetAPIMethod("kuaidi100", "order_cancel")
	if cancelMethod == "" {
		cancelMethod = "cancel" // 默认值
	}
	result, err := a.callKuaidi100OrderAPI(ctx, cancelMethod, params)
	if err != nil {
		return err
	}

	// 解析响应
	var response struct {
		Result     bool        `json:"result"`
		ReturnCode string      `json:"returnCode"`
		Message    string      `json:"message"`
		Data       interface{} `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if !response.Result || response.ReturnCode != "200" {
		return fmt.Errorf("取消订单失败: %s", response.Message)
	}

	return nil
}

// 🚀 calculateDayType 根据预约时间动态计算dayType
func (a *Kuaidi100Adapter) calculateDayType(pickupTime string) string {
	// 解析预约时间
	t, err := time.Parse(time.RFC3339, pickupTime)
	if err != nil {
		fmt.Printf("[快递100API] 解析预约时间失败，默认使用'今天': %s, 错误: %v\n",
			pickupTime, err)
		return "今天"
	}

	// 🔥 修复：转换为北京时间进行日期比较
	beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
	pickupBeijingTime := t.In(beijingLocation)

	// 获取当前时间（北京时间）
	now := util.NowBeijing()

	// 计算日期差
	nowDate := now.Format("2006-01-02")
	pickupDateStr := pickupBeijingTime.Format("2006-01-02")

	fmt.Printf("[快递100API] 日期计算详情 - 原始: %s, 北京时间: %s, 当前日期: %s, 预约日期: %s\n",
		pickupTime, pickupBeijingTime.Format("2006-01-02 15:04:05"), nowDate, pickupDateStr)

	if pickupDateStr == nowDate {
		return "今天"
	} else if pickupDateStr == now.Add(24*time.Hour).Format("2006-01-02") {
		return "明天"
	} else if pickupDateStr == now.Add(48*time.Hour).Format("2006-01-02") {
		return "后天"
	} else {
		// 超过后天的情况，默认使用"后天"
		return "后天"
	}
}

// 🚀 formatPickupTime 将ISO 8601时间格式转换为快递100API要求的格式
func (a *Kuaidi100Adapter) formatPickupTime(isoTime string) string {
	// 解析ISO 8601格式时间
	t, err := time.Parse(time.RFC3339, isoTime)
	if err != nil {
		fmt.Printf("[快递100API] 解析预约时间失败，使用原始格式: %s, 错误: %v\n",
			isoTime, err)
		return isoTime
	}

	// 🔥 修复：转换为北京时间，避免直接使用UTC时间
	beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
	beijingTime := t.In(beijingLocation)

	// 🚀 转换为快递100API要求的格式：HH:mm（仅时分）
	formatted := beijingTime.Format("15:04")

	fmt.Printf("[快递100API] 时间转换详情 - 原始: %s, UTC: %s, 北京时间: %s, 格式化: %s\n",
		isoTime, t.Format("15:04"), beijingTime.Format("15:04"), formatted)

	return formatted
}

// QueryOrder 查询订单
func (a *Kuaidi100Adapter) QueryOrder(ctx context.Context, taskId string, trackingNo string) (*model.OrderInfo, error) {
	// 🔥 企业级修复：兼容处理taskId为空的情况
	// 当taskId为空时，尝试使用orderId（即trackingNo对应的订单号）
	var queryParam string
	if taskId != "" {
		queryParam = taskId
	} else if trackingNo != "" {
		// 如果taskId为空但有trackingNo，尝试通过其他方式查询
		// 注意：这里需要根据实际业务逻辑调整
		a.logger.Warn("taskId为空，尝试使用trackingNo查询",
			zap.String("tracking_no", trackingNo))
		return nil, fmt.Errorf("taskId为空且trackingNo查询暂不支持，请联系技术支持补充订单taskId")
	} else {
		return nil, fmt.Errorf("taskId和trackingNo都为空，无法查询订单")
	}

	// 构建请求参数
	params := map[string]interface{}{
		"taskId": queryParam, // 使用taskId查询订单
	}

	// 调用快递100 API
	configManager := config.GetProviderConfigManager()
	queryMethod := configManager.GetAPIMethod("kuaidi100", "order_query")
	if queryMethod == "" {
		queryMethod = "detail" // 默认值
	}
	result, err := a.callKuaidi100OrderAPI(ctx, queryMethod, params)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var response struct {
		Result     bool   `json:"result"`
		ReturnCode string `json:"returnCode"`
		Message    string `json:"message"`
		Data       struct {
			TaskId          string `json:"taskId"`
			CreateTime      string `json:"createTime"`
			OrderId         string `json:"orderId"`
			KuaidiCom       string `json:"kuaidiCom"`
			KuaidiNum       string `json:"kuaidiNum"`
			SendName        string `json:"sendName"`
			SendMobile      string `json:"sendMobile"`
			SendProvince    string `json:"sendProvince"`
			SendCity        string `json:"sendCity"`
			SendDistrict    string `json:"sendDistrict"`
			SendAddr        string `json:"sendAddr"`
			RecName         string `json:"recName"`
			RecMobile       string `json:"recMobile"`
			RecProvince     string `json:"recProvince"`
			RecCity         string `json:"recCity"`
			RecDistrict     string `json:"recDistrict"`
			RecAddr         string `json:"recAddr"`
			Valins          string `json:"valins"`
			Cargo           string `json:"cargo"`
			ServiceType     string `json:"serviceType"`
			PreWeight       string `json:"preWeight"`
			LastWeight      string `json:"lastWeight"`
			Comment         string `json:"comment"`
			DayType         string `json:"dayType"`
			PickupStartTime string `json:"pickupStartTime"`
			PickupEndTime   string `json:"pickupEndTime"`
			Payment         string `json:"payment"`
			Freight         string `json:"freight"`
			CourierName     string `json:"courierName"`
			CourierMobile   string `json:"courierMobile"`
			Status          int    `json:"status"`
			PayStatus       int    `json:"payStatus"`
			DefPrice        string `json:"defPrice"`
		} `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		// 如果响应为空，返回一个默认的订单信息
		if result == "{}" {
			return &model.OrderInfo{
				OrderNo:    params["taskId"].(string),
				TrackingNo: trackingNo,
				Status:     "unknown",
				StatusDesc: "订单信息暂未获取",
				CreatedAt:  util.NowBeijing(),
			}, nil
		}
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if !response.Result || response.ReturnCode != "200" {
		return nil, fmt.Errorf("查询订单失败: %s", response.Message)
	}

	// 获取订单状态描述
	statusDesc := getOrderStatusDesc(response.Data.Status)

	// 🔥 修复：使用北京时区解析创建时间
	var createTime time.Time
	if response.Data.CreateTime != "" {
		beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
		parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", response.Data.CreateTime, beijingLocation)
		if err != nil {
			createTime = util.NowBeijing()
		} else {
			createTime = parsedTime
		}
	} else {
		createTime = util.NowBeijing()
	}

	// 检查订单状态
	var statusMsg string
	switch response.Data.Status {
	case 0:
		statusMsg = "下单成功"
	case 1:
		statusMsg = "已接单"
	case 2:
		statusMsg = "收件中"
	case 9:
		statusMsg = "用户主动取消"
	case 10:
		statusMsg = "已取件"
	case 11:
		statusMsg = "揽货失败"
	case 12:
		statusMsg = "已退回"
	case 13:
		statusMsg = "已签收"
	case 14:
		statusMsg = "异常签收"
	case 15:
		statusMsg = "已结算"
	case 99:
		statusMsg = "订单已取消"
	case 101:
		statusMsg = "运输中"
	case 200:
		statusMsg = "已出单"
	case 201:
		statusMsg = "出单失败"
	case 610:
		statusMsg = "下单失败"
	case 155:
		statusMsg = "修改重量"
	case 166:
		statusMsg = "订单复活"
	case 400:
		statusMsg = "派送中"
	default:
		statusMsg = fmt.Sprintf("未知状态(%d)", response.Data.Status)
	}

	// 如果订单已取消或失败，返回错误信息
	if response.Data.Status == 9 || response.Data.Status == 99 || response.Data.Status == 201 || response.Data.Status == 610 {
		return nil, fmt.Errorf("订单状态异常：%s，无法获取价格信息", statusMsg)
	}

	// 解析重量和价格
	weight, _ := strconv.ParseFloat(response.Data.LastWeight, 64)
	if weight == 0 {
		weight, _ = strconv.ParseFloat(response.Data.PreWeight, 64)
	}
	price, _ := strconv.ParseFloat(response.Data.Freight, 64)
	if price == 0 {
		price, _ = strconv.ParseFloat(response.Data.DefPrice, 64)
	}

	// 如果价格仍为0，记录详细信息用于调试
	if price == 0 {
		a.logger.Warn("快递100订单价格为0",
			zap.String("order_id", response.Data.OrderId),
			zap.Int("status", response.Data.Status),
			zap.String("status_msg", statusMsg),
			zap.String("freight", response.Data.Freight),
			zap.String("def_price", response.Data.DefPrice),
			zap.Int("pay_status", response.Data.PayStatus))
	}

	// 转换为标准订单信息
	orderInfo := &model.OrderInfo{
		OrderNo:        response.Data.OrderId,
		TrackingNo:     response.Data.KuaidiNum,
		ExpressType:    response.Data.KuaidiCom,
		Status:         fmt.Sprintf("%d", response.Data.Status),
		StatusDesc:     statusDesc,
		Weight:         weight,
		Price:          price,
		CreatedAt:      createTime,
		CourierName:    response.Data.CourierName,
		CourierPhone:   response.Data.CourierMobile,
		CourierCode:    "",
		StationName:    "",
		StationCode:    "",
		StationAddress: "",
		StationPhone:   "",
		PickupCode:     "",
	}

	return orderInfo, nil
}

// 调用快递100 API
func (a *Kuaidi100Adapter) callKuaidi100API(ctx context.Context, method string, params map[string]interface{}) (string, error) {
	// 将参数转换为JSON字符串
	paramsJSON, err := json.Marshal(params)
	if err != nil {
		return "", fmt.Errorf("参数序列化失败: %w", err)
	}

	// 构建请求参数
	t := strconv.FormatInt(util.NowBeijing().Unix()*1000, 10) // 毫秒级时间戳
	sign := a.generateSign(string(paramsJSON), t, a.config.Key, a.config.Secret)

	// 构建请求数据
	data := url.Values{}
	data.Set("method", method) // 使用传入的method参数
	data.Set("key", a.config.Key)
	data.Set("t", t)
	data.Set("sign", sign)
	data.Set("param", string(paramsJSON))

	// 打印请求信息
	fmt.Printf("快递100 API 请求URL: %s\n", a.config.BaseURL)
	fmt.Printf("快递100 API 请求方法: %s\n", method)
	fmt.Printf("快递100 API 请求参数: %+v\n", data)
	fmt.Printf("快递100 API 请求参数JSON: %s\n", string(paramsJSON))

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, a.config.BaseURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	resp, err := a.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应体失败: %w", err)
	}

	// 打印响应信息
	fmt.Printf("快递100 API 响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("快递100 API 响应内容: %s\n", string(respBody))

	// 检查响应是否为空
	if len(respBody) == 0 {
		return "{}", nil
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return "", fmt.Errorf("解析响应失败: %w", err)
	}

	// 将响应转换为JSON字符串
	resultJSON, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("响应序列化失败: %w", err)
	}

	return string(resultJSON), nil
}

// 调用快递100订单查询API
func (a *Kuaidi100Adapter) callKuaidi100OrderAPI(ctx context.Context, method string, params map[string]interface{}) (string, error) {
	// 将参数转换为JSON字符串
	paramsJSON, err := json.Marshal(params)
	if err != nil {
		return "", fmt.Errorf("参数序列化失败: %w", err)
	}

	// 构建请求参数
	t := strconv.FormatInt(util.NowBeijing().Unix()*1000, 10) // 毫秒级时间戳
	sign := a.generateSign(string(paramsJSON), t, a.config.Key, a.config.Secret)

	// 构建请求数据
	data := url.Values{}
	data.Set("method", method) // 使用传入的method参数
	data.Set("key", a.config.Key)
	data.Set("t", t)
	data.Set("sign", sign)
	data.Set("param", string(paramsJSON))

	// 获取API端点 - 查询订单详情使用order_detail端点
	configManager := config.GetProviderConfigManager()
	orderURL := configManager.GetAPIEndpoint("kuaidi100", "order_detail")
	if orderURL == "" {
		orderURL = "https://order.kuaidi100.com/order/borderapi.do" // 默认值
	}

	// 打印请求信息
	fmt.Printf("快递100 订单API 请求URL: %s\n", orderURL)
	fmt.Printf("快递100 订单API 请求方法: %s\n", method)
	fmt.Printf("快递100 订单API 请求参数: %+v\n", data)
	fmt.Printf("快递100 订单API 请求参数JSON: %s\n", string(paramsJSON))

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, orderURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	resp, err := a.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应体失败: %w", err)
	}

	// 打印响应信息
	fmt.Printf("快递100 订单API 响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("快递100 订单API 响应内容: %s\n", string(respBody))

	// 检查响应是否为空
	if len(respBody) == 0 {
		return "{}", nil
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return "", fmt.Errorf("解析响应失败: %w", err)
	}

	// 将响应转换为JSON字符串
	resultJSON, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("响应序列化失败: %w", err)
	}

	return string(resultJSON), nil
}

// 生成签名
func (a *Kuaidi100Adapter) generateSign(param, t, key, secret string) string {
	// 按照快递100的签名规则生成签名
	// 签名规则：MD5(param + t + key + secret)
	signStr := param + t + key + secret
	hash := md5.Sum([]byte(signStr))
	return strings.ToUpper(hex.EncodeToString(hash[:]))
}

// 生成快递100实时查询API签名
func (a *Kuaidi100Adapter) generatePollSign(param, key, customer string) string {
	// 按照快递100的签名规则生成签名
	// 签名规则：MD5(param + key + customer)
	signStr := param + key + customer
	hash := md5.Sum([]byte(signStr))
	return strings.ToUpper(hex.EncodeToString(hash[:]))
}

// QueryTrack 查询物流轨迹
func (a *Kuaidi100Adapter) QueryTrack(ctx context.Context, trackingNo, expressType, phone, pollToken string, from, to string) (*model.TrackInfo, error) {
	// 将统一的快递公司代码转换为快递100的代码
	// 物流查询不需要考虑重量，所以传入0
	kuaidicom := a.mapExpressCode(expressType, 0)
	if kuaidicom == "" {
		return nil, fmt.Errorf("不支持的快递公司: %s", expressType)
	}

	// 构建请求参数
	params := map[string]interface{}{
		"com":      kuaidicom,
		"num":      trackingNo,
		"resultv2": "4",    // 开通行政解析功能以及物流轨迹增加物流高级状态名称、状态值并且返回出发、目的及当前城市信息
		"show":     "0",    // 返回json格式
		"order":    "desc", // 降序排列
	}

	// 添加可选参数
	if phone != "" {
		params["phone"] = phone
	}
	if from != "" {
		params["from"] = from
	}
	if to != "" {
		params["to"] = to
	}

	// 将参数转换为JSON字符串
	paramsJSON, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("参数序列化失败: %w", err)
	}

	// 构建请求参数
	data := url.Values{}
	data.Set("customer", a.config.Customer)
	data.Set("param", string(paramsJSON))

	// 如果有pollToken，则使用pollToken
	if pollToken != "" {
		data.Set("pollToken", pollToken)
	}

	// 生成签名
	sign := a.generatePollSign(string(paramsJSON), a.config.Key, a.config.Customer)
	data.Set("sign", sign)

	// 获取API端点
	configManager := config.GetProviderConfigManager()
	pollURL := configManager.GetAPIEndpoint("kuaidi100", "poll")
	if pollURL == "" {
		pollURL = "https://poll.kuaidi100.com/poll/query.do" // 默认值
	}

	// 打印请求信息
	fmt.Printf("快递100 Track API 请求URL: %s\n", pollURL)
	fmt.Printf("快递100 Track API 请求参数: %+v\n", data)

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, pollURL, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 发送请求
	resp, err := a.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}

	// 打印响应信息
	fmt.Printf("快递100 Track API 响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("快递100 Track API 响应内容: %s\n", string(respBody))

	// 解析响应
	var response struct {
		Message   string `json:"message"`
		State     string `json:"state"`
		Status    string `json:"status"`
		Condition string `json:"condition"`
		IsCheck   string `json:"ischeck"`
		Com       string `json:"com"`
		Nu        string `json:"nu"`
		Data      []struct {
			Context    string `json:"context"`
			Time       string `json:"time"`
			Ftime      string `json:"ftime"`
			Status     string `json:"status"`
			StatusCode string `json:"statusCode"`
			AreaCode   string `json:"areaCode"`
			AreaName   string `json:"areaName"`
			Location   string `json:"location"`
		} `json:"data"`
		RouteInfo struct {
			From struct {
				Number string `json:"number"`
				Name   string `json:"name"`
			} `json:"from"`
			Cur struct {
				Number string `json:"number"`
				Name   string `json:"name"`
			} `json:"cur"`
			To struct {
				Number string `json:"number"`
				Name   string `json:"name"`
			} `json:"to"`
		} `json:"routeInfo"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查响应状态
	if response.Status != "200" && response.Message != "ok" {
		return nil, fmt.Errorf("查询物流轨迹失败: %s", response.Message)
	}

	// 转换为标准物流轨迹信息
	trackInfo := &model.TrackInfo{
		OrderNo:     "",
		TrackingNo:  response.Nu,
		ExpressType: response.Com,
		State:       response.State,
		StateDesc:   model.StateMap[response.State],
		IsCheck:     response.IsCheck,
		Tracks:      make([]*model.TrackItem, 0, len(response.Data)),
	}

	// 转换物流轨迹项
	for _, item := range response.Data {
		// 🔥 修复：正确解析快递100 API返回的时间
		// 快递100返回的时间字符串是北京时间格式，需要明确指定为北京时区
		var t time.Time
		if item.Time != "" {
			// 使用北京时区解析时间
			beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
			parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", item.Time, beijingLocation)
			if err != nil {
				// 如果解析失败，记录错误并使用当前北京时间
				fmt.Printf("快递100轨迹时间解析失败: %s, 错误: %v, 使用当前时间\n", item.Time, err)
				t = util.NowBeijing()
			} else {
				t = parsedTime
			}
		} else {
			t = util.NowBeijing()
		}

		trackItem := &model.TrackItem{
			Context:    item.Context,
			Time:       t,
			Status:     item.Status,
			StatusCode: item.StatusCode,
			AreaCode:   item.AreaCode,
			AreaName:   item.AreaName,
			Location:   item.Location,
		}

		trackInfo.Tracks = append(trackInfo.Tracks, trackItem)
	}

	// 转换路由信息
	if response.RouteInfo.From.Number != "" || response.RouteInfo.Cur.Number != "" || response.RouteInfo.To.Number != "" {
		trackInfo.RouteInfo = &model.RouteInfo{}

		if response.RouteInfo.From.Number != "" {
			trackInfo.RouteInfo.From = &model.RouteCity{
				Number: response.RouteInfo.From.Number,
				Name:   response.RouteInfo.From.Name,
			}
		}

		if response.RouteInfo.Cur.Number != "" {
			trackInfo.RouteInfo.Cur = &model.RouteCity{
				Number: response.RouteInfo.Cur.Number,
				Name:   response.RouteInfo.Cur.Name,
			}
		}

		if response.RouteInfo.To.Number != "" {
			trackInfo.RouteInfo.To = &model.RouteCity{
				Number: response.RouteInfo.To.Number,
				Name:   response.RouteInfo.To.Name,
			}
		}
	}

	return trackInfo, nil
}

// 获取订单状态描述
func getOrderStatusDesc(status int) string {
	// 从配置获取状态码描述
	configManager := config.GetProviderConfigManager()
	statusStr := fmt.Sprintf("%d", status)
	desc := configManager.GetStatusCode("kuaidi100", statusStr)

	// 如果找到状态描述，返回它，否则返回未知状态
	if desc != statusStr {
		return desc
	}
	return fmt.Sprintf("未知状态(%d)", status)
}

// 将统一的快递公司代码转换为快递100的代码
// weight参数用于自动处理德邦物流的重量限制
func (a *Kuaidi100Adapter) mapExpressCode(expressCode string, weight float64) string {
	ctx := context.Background()

	// 🚀 德邦物流特殊处理：统一使用debangkuaidi代码，通过serviceType区分产品
	if expressCode == "DBL" {
		// 使用数据库映射服务获取德邦快递的快递100代码
		if a.mappingService != nil {
			if mappedCode, err := a.mappingService.GetProviderCompanyCode(ctx, expressCode, "kuaidi100"); err == nil {
				a.logger.Debug("德邦快递代码映射成功",
					zap.String("provider", "kuaidi100"),
					zap.String("standard_code", expressCode),
					zap.String("provider_code", mappedCode),
					zap.Float64("weight", weight))
				return mappedCode
			} else {
				a.logger.Error("德邦快递代码映射失败",
					zap.String("standard_code", expressCode),
					zap.String("provider", "kuaidi100"),
					zap.Error(err))
				return ""
			}
		}
	}

	// 使用数据库映射服务获取快递公司代码映射
	if a.mappingService != nil {
		if mappedCode, err := a.mappingService.GetProviderCompanyCode(ctx, expressCode, "kuaidi100"); err == nil {
			a.logger.Debug("快递代码映射成功",
				zap.String("provider", "kuaidi100"),
				zap.String("standard_code", expressCode),
				zap.String("provider_code", mappedCode))
			return mappedCode
		} else {
			// 🔧 修复：映射失败时抛出错误，不使用原代码
			a.logger.Error("快递100供应商快递代码映射失败",
				zap.String("standard_code", expressCode),
				zap.String("provider", "kuaidi100"),
				zap.Error(err))
			// 返回错误而不是继续使用无效代码
			return ""
		}
	}

	// 如果映射服务不可用，返回错误
	a.logger.Error("映射服务不可用，无法转换快递代码",
		zap.String("express_code", expressCode))
	return ""
}

// 获取快递公司名称
func (a *Kuaidi100Adapter) getExpressName(expressCode string) string {
	// 从配置获取产品类型名称
	configManager := config.GetProviderConfigManager()
	productName := configManager.GetProductType("kuaidi100", expressCode)

	// 如果找到产品类型名称，返回它，否则返回原代码
	if productName != "" {
		return productName
	}
	return expressCode
}

// 获取服务类型
func (a *Kuaidi100Adapter) getServiceType() string {
	// 从配置获取服务类型
	configManager := config.GetProviderConfigManager()
	serviceType := configManager.GetParameter("kuaidi100", "service_type")

	if serviceType != nil {
		if str, ok := serviceType.(string); ok {
			return str
		}
	}
	return "标准快递" // 默认值
}

// 🚀 根据快递公司和重量选择合适的服务类型
func (a *Kuaidi100Adapter) getServiceTypeForExpress(expressCode string, weight float64) string {
	// 德邦快递特殊处理：根据重量选择产品类型
	if expressCode == "DBL" {
		if weight > 3.0 {
			// 重量超过3KG，使用德邦大件360
			a.logger.Info("德邦快递重量超过3KG，选择德邦大件360",
				zap.String("express_code", expressCode),
				zap.Float64("weight", weight))
			return "德邦大件360"
		} else {
			// 重量3KG以下，使用标准快递
			a.logger.Info("德邦快递重量3KG以下，选择标准快递",
				zap.String("express_code", expressCode),
				zap.Float64("weight", weight))
			return "标准快递"
		}
	}

	// 其他快递公司使用默认服务类型
	return a.getServiceType()
}

// calculateChargedWeightFromPackage 从包裹信息计算计费重量（优先使用长宽高）
func (a *Kuaidi100Adapter) calculateChargedWeightFromPackage(expressCode string, pkg model.PackageInfo) float64 {
	// 🔥 企业级修复：使用统一的体积重量计算器
	return a.calculateChargedWeightEnterprise(expressCode, pkg)
}

// calculateChargedWeightEnterprise 企业级体积重量计算（从数据库获取配置）
func (a *Kuaidi100Adapter) calculateChargedWeightEnterprise(expressCode string, pkg model.PackageInfo) float64 {
	// 先计算体积
	var volumeCm3 float64
	if pkg.Length > 0 && pkg.Width > 0 && pkg.Height > 0 {
		// 长宽高单位是cm，直接计算体积（cm³）
		volumeCm3 = pkg.Length * pkg.Width * pkg.Height
	} else if pkg.Volume > 0 {
		// 如果没有长宽高，从m³转换为cm³
		volumeCm3 = pkg.Volume * 1000000
	}

	a.logger.Debug("快递100企业级计算器输入参数",
		zap.String("express_code", expressCode),
		zap.Float64("actual_weight", pkg.Weight),
		zap.Float64("length", pkg.Length),
		zap.Float64("width", pkg.Width),
		zap.Float64("height", pkg.Height),
		zap.Float64("volume_cm3", volumeCm3))

	// 如果没有体积信息，直接返回实际重量（向上取整）
	if volumeCm3 <= 0 {
		result := math.Ceil(pkg.Weight)
		a.logger.Debug("快递100企业级计算器：无体积信息，使用实际重量",
			zap.Float64("result", result))
		return result
	}

	// 从数据库获取快递公司配置
	if a.expressCompanyRepo == nil {
		a.logger.Error("ExpressCompanyRepository 未初始化",
			zap.String("express_code", expressCode))
		return math.Ceil(pkg.Weight)
	}

	company, err := a.expressCompanyRepo.GetCompanyByCode(expressCode)
	if err != nil {
		a.logger.Error("快递100企业级计算器：获取快递公司配置失败",
			zap.String("express_code", expressCode),
			zap.Error(err))
		panic(fmt.Sprintf("快递公司 %s 配置获取失败: %v。请在数据库 express_companies 表中配置正确的快递公司信息", expressCode, err))
	}

	if !company.IsActive {
		a.logger.Error("快递100企业级计算器：快递公司已禁用",
			zap.String("express_code", expressCode))
		panic(fmt.Sprintf("快递公司 %s 已禁用。请在数据库中启用该快递公司", expressCode))
	}

	if company.VolumeWeightRatio <= 0 {
		a.logger.Error("快递100企业级计算器：快递公司抛比配置无效",
			zap.String("express_code", expressCode),
			zap.Int("volume_ratio", company.VolumeWeightRatio))
		panic(fmt.Sprintf("快递公司 %s 抛比配置无效: %d。请在数据库 express_companies 表中配置正确的 volume_weight_ratio 值", expressCode, company.VolumeWeightRatio))
	}

	volumeRatio := company.VolumeWeightRatio
	companyName := company.Name
	a.logger.Debug("快递100企业级计算器：从数据库获取抛比配置",
		zap.String("express_code", expressCode),
		zap.String("company_name", companyName),
		zap.Int("volume_ratio", volumeRatio))

	// 计算体积重量
	volumeWeight := volumeCm3 / float64(volumeRatio)

	// 取实际重量和体积重量的较大值
	chargedWeight := math.Max(pkg.Weight, volumeWeight)

	// 向上取整到最近的整数（快递行业标准）
	result := math.Ceil(chargedWeight)

	a.logger.Debug("快递100企业级计算器计算结果",
		zap.String("company_name", companyName),
		zap.Int("volume_ratio", volumeRatio),
		zap.Float64("volume_weight", volumeWeight),
		zap.Float64("actual_weight", pkg.Weight),
		zap.Float64("charged_weight", result))

	return result
}

// getCallbackURL 获取回调URL
// 遵循依赖倒置原则：依赖回调URL管理器抽象
func (a *Kuaidi100Adapter) getCallbackURL() string {
	callbackManager := config.GetCallbackURLManager()
	return callbackManager.GetCallbackURL("kuaidi100")
}
