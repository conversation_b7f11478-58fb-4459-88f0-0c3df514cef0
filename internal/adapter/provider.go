package adapter

import (
	"context"
	"fmt"

	"github.com/your-org/go-kuaidi/internal/model"
)

// ProviderAdapter 供应商适配器接口
type ProviderAdapter interface {
	// 基本信息
	Name() string

	// 价格查询
	QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error)

	// 下单
	CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResult, error)

	// 取消订单
	CancelOrder(ctx context.Context, taskId string, orderNo string, reason string) error

	// 查询订单
	QueryOrder(ctx context.Context, orderNo string, trackingNo string) (*model.OrderInfo, error)

	// 查询物流轨迹
	QueryTrack(ctx context.Context, trackingNo, expressType, phone, pollToken string, from, to string) (*model.TrackInfo, error)
}

// ProviderFactory 供应商工厂
type ProviderFactory interface {
	// 创建供应商适配器
	Create() ProviderAdapter
}

// ProviderStatusChecker 供应商状态检查器接口
type ProviderStatusChecker interface {
	// IsProviderActive 检查供应商是否启用
	IsProviderActive(ctx context.Context, providerCode string) (bool, error)
	// GetActiveProviders 获取所有启用的供应商代码列表
	GetActiveProviders(ctx context.Context) ([]string, error)
}

// ProviderManager 供应商管理器
type ProviderManager struct {
	providers     map[string]ProviderAdapter
	statusChecker ProviderStatusChecker

	// 动态管理支持
	dynamicManager *DynamicProviderManager
	isDynamic      bool
}

// NewProviderManager 创建供应商管理器
func NewProviderManager() *ProviderManager {
	return &ProviderManager{
		providers: make(map[string]ProviderAdapter),
		isDynamic: false,
	}
}

// NewDynamicProviderManagerWrapper 创建动态供应商管理器包装器
func NewDynamicProviderManagerWrapper(dynamicManager *DynamicProviderManager) *ProviderManager {
	return &ProviderManager{
		providers:      make(map[string]ProviderAdapter),
		dynamicManager: dynamicManager,
		isDynamic:      true,
	}
}

// SetStatusChecker 设置状态检查器
func (m *ProviderManager) SetStatusChecker(checker ProviderStatusChecker) {
	m.statusChecker = checker
}

// Register 注册供应商适配器
func (m *ProviderManager) Register(provider ProviderAdapter) {
	m.providers[provider.Name()] = provider
}

// Get 获取供应商适配器（检查启用状态）
func (m *ProviderManager) Get(name string) (ProviderAdapter, bool) {
	// 如果是动态管理器，委托给动态管理器
	if m.isDynamic && m.dynamicManager != nil {
		return m.dynamicManager.Get(name)
	}

	// 传统静态管理逻辑
	provider, ok := m.providers[name]
	if !ok {
		return nil, false
	}

	// 如果有状态检查器，验证供应商是否启用
	if m.statusChecker != nil {
		ctx := context.Background()
		isActive, err := m.statusChecker.IsProviderActive(ctx, name)
		if err != nil || !isActive {
			return nil, false
		}
	}

	return provider, true
}

// GetForTracking 获取供应商适配器用于物流跟踪（不检查启用状态）
// 物流跟踪功能不应受供应商启用/禁用开关影响，因为用户有权查询已下订单的物流状态
func (m *ProviderManager) GetForTracking(name string) (ProviderAdapter, bool) {
	// 如果是动态管理器，委托给动态管理器的跟踪专用方法
	if m.isDynamic && m.dynamicManager != nil {
		return m.dynamicManager.GetForTracking(name)
	}

	// 传统静态管理逻辑（不检查启用状态）
	provider, ok := m.providers[name]
	if !ok {
		return nil, false
	}

	return provider, true
}

// GetAll 获取所有启用的供应商适配器
func (m *ProviderManager) GetAll() []ProviderAdapter {
	// 如果是动态管理器，委托给动态管理器
	if m.isDynamic && m.dynamicManager != nil {
		return m.dynamicManager.GetAll()
	}

	// 传统静态管理逻辑
	var providers []ProviderAdapter

	// 如果没有状态检查器，返回所有注册的适配器
	if m.statusChecker == nil {
		for _, provider := range m.providers {
			providers = append(providers, provider)
		}
		return providers
	}

	// 使用状态检查器获取启用的供应商
	ctx := context.Background()
	activeProviderCodes, err := m.statusChecker.GetActiveProviders(ctx)
	if err != nil {
		// 如果获取失败，记录错误但返回空列表以确保安全
		return providers
	}

	// 只返回启用的供应商适配器
	for _, code := range activeProviderCodes {
		if provider, exists := m.providers[code]; exists {
			providers = append(providers, provider)
		}
	}

	return providers
}

// GetAllExcluding 获取所有启用的供应商适配器，排除指定的供应商
func (m *ProviderManager) GetAllExcluding(excludeProviders []string) []ProviderAdapter {
	// 如果是动态管理器，委托给动态管理器
	if m.isDynamic && m.dynamicManager != nil {
		return m.dynamicManager.GetAllExcluding(excludeProviders)
	}

	// 传统静态管理逻辑
	var providers []ProviderAdapter
	excludeSet := make(map[string]bool)
	for _, exclude := range excludeProviders {
		excludeSet[exclude] = true
	}

	// 如果没有状态检查器，返回所有注册的适配器（排除指定的）
	if m.statusChecker == nil {
		for name, provider := range m.providers {
			if !excludeSet[name] {
				providers = append(providers, provider)
			}
		}
		return providers
	}

	// 使用状态检查器获取启用的供应商
	ctx := context.Background()
	activeProviderCodes, err := m.statusChecker.GetActiveProviders(ctx)
	if err != nil {
		// 如果获取失败，记录错误但返回空列表以确保安全
		return providers
	}

	// 只返回启用且未被排除的供应商适配器
	for _, code := range activeProviderCodes {
		if !excludeSet[code] {
			if provider, exists := m.providers[code]; exists {
				providers = append(providers, provider)
			}
		}
	}

	return providers
}

// GetAllRegistered 获取所有注册的供应商适配器（不检查状态）
func (m *ProviderManager) GetAllRegistered() []ProviderAdapter {
	var providers []ProviderAdapter
	for _, provider := range m.providers {
		providers = append(providers, provider)
	}
	return providers
}

// ReloadProvider 重新加载指定供应商（仅动态管理器支持）
func (m *ProviderManager) ReloadProvider(ctx context.Context, providerCode string) error {
	if !m.isDynamic || m.dynamicManager == nil {
		return fmt.Errorf("当前管理器不支持动态重载，请使用动态供应商管理器")
	}
	return m.dynamicManager.ReloadProvider(ctx, providerCode)
}

// ReloadAllProviders 重新加载所有供应商（仅动态管理器支持）
func (m *ProviderManager) ReloadAllProviders(ctx context.Context) error {
	if !m.isDynamic || m.dynamicManager == nil {
		return fmt.Errorf("当前管理器不支持动态重载，请使用动态供应商管理器")
	}
	return m.dynamicManager.ReloadAllProviders(ctx)
}

// GetProviderStatus 获取供应商状态（仅动态管理器支持）
func (m *ProviderManager) GetProviderStatus(ctx context.Context) (map[string]ProviderStatus, error) {
	if !m.isDynamic || m.dynamicManager == nil {
		return nil, fmt.Errorf("当前管理器不支持状态查询，请使用动态供应商管理器")
	}
	return m.dynamicManager.GetProviderStatus(ctx), nil
}

// IsDynamic 检查是否为动态管理器
func (m *ProviderManager) IsDynamic() bool {
	return m.isDynamic
}

// StartDynamicManager 启动动态管理器（仅动态管理器支持）
func (m *ProviderManager) StartDynamicManager(ctx context.Context) error {
	if !m.isDynamic || m.dynamicManager == nil {
		return fmt.Errorf("当前管理器不支持动态管理")
	}
	return m.dynamicManager.Start(ctx)
}

// StopDynamicManager 停止动态管理器（仅动态管理器支持）
func (m *ProviderManager) StopDynamicManager(ctx context.Context) error {
	if !m.isDynamic || m.dynamicManager == nil {
		return fmt.Errorf("当前管理器不支持动态管理")
	}
	return m.dynamicManager.Stop(ctx)
}

// GetDynamicManager 获取内部动态管理器（仅动态管理器支持）
func (m *ProviderManager) GetDynamicManager() *DynamicProviderManager {
	if !m.isDynamic {
		return nil
	}
	return m.dynamicManager
}
