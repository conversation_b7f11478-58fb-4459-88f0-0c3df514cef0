package workorder

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
	"go.uber.org/zap"
)

// YuntongWorkOrderAdapter 云通工单适配器
type YuntongWorkOrderAdapter struct {
	businessID string
	apiKey     string
	baseURL    string
	httpClient *http.Client
	repo       repository.WorkOrderRepository
	logger     *zap.Logger
}

// NewYuntongWorkOrderAdapter 创建云通工单适配器
func NewYuntongWorkOrderAdapter(businessID, apiKey string, repo repository.WorkOrderRepository, logger *zap.Logger) service.WorkOrderProviderAdapter {
	return &YuntongWorkOrderAdapter{
		businessID: businessID,
		apiKey:     apiKey,
		baseURL:    "https://open.yuntongzy.com/express/api/OrderService",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		repo:   repo,
		logger: logger,
	}
}

// generateSign 生成云通API签名
// 根据云通官方文档：MD5(RequestData + ApiKey) → Base64 → URL编码
func (a *YuntongWorkOrderAdapter) generateSign(requestData string) string {
	// 1. 拼接RequestData和ApiKey
	signString := requestData + a.apiKey
	a.logger.Debug("云通签名原始字符串", zap.String("sign_string", signString))

	// 2. MD5加密
	hash := md5.Sum([]byte(signString))
	md5Str := fmt.Sprintf("%x", hash)
	a.logger.Debug("云通签名MD5结果", zap.String("md5", md5Str))

	// 3. Base64编码
	base64Str := base64.StdEncoding.EncodeToString([]byte(md5Str))
	a.logger.Debug("云通签名Base64结果", zap.String("base64", base64Str))

	// 4. URL编码
	urlEncoded := url.QueryEscape(base64Str)
	a.logger.Debug("云通签名最终结果", zap.String("final_sign", urlEncoded))

	return urlEncoded
}

// CreateWorkOrder 创建工单
func (a *YuntongWorkOrderAdapter) CreateWorkOrder(ctx context.Context, req *model.CreateWorkOrderRequest) (*service.ProviderWorkOrderResponse, error) {
	// 1. 直接使用服务层传递的云通工单类型
	// 服务层已经完成了统一类型到云通类型的转换
	yuntongWorkOrderType := req.WorkOrderType

	a.logger.Info("云通工单创建",
		zap.String("provider", "yuntong"),
		zap.Int("yuntong_work_order_type", yuntongWorkOrderType))

	// 2. 验证云通特定的参数要求（不验证类型转换）
	if err := a.validateYuntongBasicRequest(req); err != nil {
		return nil, fmt.Errorf("云通工单参数验证失败: %w", err)
	}

	// 3. 验证云通工单类型和参数
	if err := a.validateYuntongWorkOrderType(req, yuntongWorkOrderType); err != nil {
		return nil, fmt.Errorf("云通工单类型验证失败: %w", err)
	}

	// 4. 构建请求数据
	requestData := map[string]interface{}{
		"ComplaintType":    yuntongWorkOrderType,
		"ComplaintContent": req.Content,
	}

	// 添加订单号或运单号（根据云通官方文档，两者必须填一个，不能同时填写）
	// 优先使用订单号，如果没有订单号再使用运单号（因为订单号通常权限更可靠）
	if req.OrderNo != nil && *req.OrderNo != "" {
		requestData["OrderCode"] = *req.OrderNo
		a.logger.Info("云通工单使用订单号", zap.String("order_code", *req.OrderNo))
	} else if req.TrackingNo != nil && *req.TrackingNo != "" {
		requestData["LogisticCode"] = *req.TrackingNo
		a.logger.Info("云通工单使用运单号", zap.String("logistic_code", *req.TrackingNo))
	} else {
		return nil, fmt.Errorf("运单号和订单号必须提供其中一个")
	}

	// 添加可选参数
	if req.FeedbackWeight != nil {
		requestData["ActualWeight"] = *req.FeedbackWeight
	}

	// 处理体积参数
	if providerData, ok := req.ProviderSpecificData["yuntong"].(map[string]interface{}); ok {
		if actualVolume, exists := providerData["actualVolume"]; exists {
			requestData["ActualVolume"] = actualVolume
		}
	}

	// 处理商品名称（计费异常时必传）
	if yuntongWorkOrderType == 2 { // 统一类型2(重量异常) → 云通计费异常(类型2)
		goodsName := "商品" // 默认值
		if providerData, ok := req.ProviderSpecificData["yuntong"].(map[string]interface{}); ok {
			if name, exists := providerData["goodsName"].(string); exists && name != "" {
				goodsName = name
			}
		}
		requestData["GoodsName"] = goodsName
	}

	// 处理附件
	if len(req.AttachmentURLs) > 0 {
		requestData["PicList"] = req.AttachmentURLs
	}

	// 5. 序列化RequestData
	requestDataJSON, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化RequestData失败: %w", err)
	}
	requestDataStr := string(requestDataJSON)

	// 6. 生成签名
	dataSign := a.generateSign(requestDataStr)

	// 7. 构建完整请求体（按照云通官方文档格式）
	fullRequest := map[string]interface{}{
		"RequestType": "1807",
		"EBusinessID": a.businessID,
		"RequestData": requestDataStr,
		"DataSign":    dataSign,
	}

	// 8. 序列化完整请求
	requestJSON, err := json.Marshal(fullRequest)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	a.logger.Info("云通创建工单请求",
		zap.String("url", a.baseURL),
		zap.String("request_type", "1807"),
		zap.String("business_id", a.businessID),
		zap.Int("unified_type", req.WorkOrderType),
		zap.Int("yuntong_type", yuntongWorkOrderType), // 🔥 修复：现在是int类型
		zap.String("request_data", requestDataStr),
		zap.String("data_sign", dataSign),
		zap.String("full_request_body", string(requestJSON)))

	// 9. 发送请求
	resp, err := a.httpClient.Post(a.baseURL, "application/json", bytes.NewBuffer(requestJSON))
	if err != nil {
		return nil, fmt.Errorf("发送创建工单请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 10. 解析响应
	var response YuntongWorkOrderResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析创建工单响应失败: %w", err)
	}

	a.logger.Info("云通创建工单响应",
		zap.Bool("success", response.Success),
		zap.Int("result_code", response.ResultCode),
		zap.String("complaint_number", response.ComplaintNumber), // 修复：改为String
		zap.String("unique_request_number", response.UniquerRequestNumber))

	if !response.Success {
		return nil, fmt.Errorf("创建工单失败: Success=false, ResultCode=%d", response.ResultCode)
	}

	if response.ResultCode != 100 {
		// 根据云通API文档，不同的ResultCode有不同含义
		errorMsg := a.getErrorMessage(response.ResultCode)
		return nil, fmt.Errorf("创建工单失败: %s (ResultCode=%d)", errorMsg, response.ResultCode)
	}

	// 11. 转换为统一响应格式
	return &service.ProviderWorkOrderResponse{
		ProviderWorkOrderID: response.ComplaintNumber, // 修复：直接使用字符串
		Status:              0,                        // 云通默认状态为未处理
		Message:             "创建成功",
		Data: map[string]interface{}{
			"complaintNumber":     response.ComplaintNumber,
			"resultCode":          response.ResultCode,
			"uniqueRequestNumber": response.UniquerRequestNumber,
			"eBusinessID":         response.EBusinessID,
		},
	}, nil
}

// QueryWorkOrder 查询工单
func (a *YuntongWorkOrderAdapter) QueryWorkOrder(ctx context.Context, providerWorkOrderID string) (*service.ProviderWorkOrderResponse, error) {
	// 注意：云通查询工单接口需要使用LogisticCode或OrderCode，而不是ComplaintNumber
	// 这里我们假设providerWorkOrderID是运单号，实际使用时可能需要调整

	// 1. 构建请求数据（使用LogisticCode查询）
	requestData := map[string]interface{}{
		"LogisticCode": providerWorkOrderID,
	}

	// 2. 序列化RequestData
	requestDataJSON, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化RequestData失败: %w", err)
	}
	requestDataStr := string(requestDataJSON)

	// 3. 生成签名
	dataSign := a.generateSign(requestDataStr)

	// 4. 构建完整请求体
	fullRequest := map[string]interface{}{
		"RequestType": "1818",
		"EBusinessID": a.businessID,
		"RequestData": requestDataStr,
		"DataSign":    dataSign,
	}

	// 5. 序列化完整请求
	requestJSON, err := json.Marshal(fullRequest)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	a.logger.Info("云通查询工单请求",
		zap.String("url", a.baseURL),
		zap.String("request_type", "1818"),
		zap.String("business_id", a.businessID),
		zap.String("logistic_code", providerWorkOrderID))

	// 6. 发送请求
	resp, err := a.httpClient.Post(a.baseURL, "application/json", bytes.NewBuffer(requestJSON))
	if err != nil {
		return nil, fmt.Errorf("发送查询工单请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 7. 解析响应
	var response YuntongQueryWorkOrderResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析查询工单响应失败: %w", err)
	}

	a.logger.Info("云通查询工单响应",
		zap.Bool("success", response.Success),
		zap.Int("result_code", response.ResultCode),
		zap.Int("data_count", len(response.Data)))

	if !response.Success {
		return nil, fmt.Errorf("查询工单失败: Success=false, ResultCode=%d", response.ResultCode)
	}

	if response.ResultCode != 100 {
		errorMsg := a.getErrorMessage(response.ResultCode)
		return nil, fmt.Errorf("查询工单失败: %s (ResultCode=%d)", errorMsg, response.ResultCode)
	}

	// 8. 转换为统一响应格式
	var status int
	var complaintNumber string
	if len(response.Data) > 0 {
		status = a.mapStatusFromString(response.Data[0].Status)
		complaintNumber = strconv.Itoa(response.Data[0].ComplaintNumber)
	}

	return &service.ProviderWorkOrderResponse{
		ProviderWorkOrderID: complaintNumber, // 使用实际的工单号
		Status:              status,
		Message:             "查询成功",
		Data: map[string]interface{}{
			"detail":              response.Data,
			"uniqueRequestNumber": response.UniquerRequestNumber,
			"eBusinessID":         response.EBusinessID,
			"totalCount":          len(response.Data),
		},
	}, nil
}

// ReplyWorkOrder 回复工单
func (a *YuntongWorkOrderAdapter) ReplyWorkOrder(ctx context.Context, providerWorkOrderID string, content string, attachmentURLs []string) error {
	// 1. 构建请求数据
	requestData := map[string]interface{}{
		"ComplaintNumber": providerWorkOrderID,
		"Reason":          content,
	}

	// 处理附件
	if len(attachmentURLs) > 0 {
		requestData["PicList"] = attachmentURLs
	}

	// 2. 序列化RequestData
	requestDataJSON, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("序列化RequestData失败: %w", err)
	}
	requestDataStr := string(requestDataJSON)

	// 3. 生成签名
	dataSign := a.generateSign(requestDataStr)

	// 4. 构建完整请求体
	fullRequest := map[string]interface{}{
		"RequestType": "1819",
		"EBusinessID": a.businessID,
		"RequestData": requestDataStr,
		"DataSign":    dataSign,
	}

	// 3. 序列化请求
	requestJSON, err := json.Marshal(fullRequest)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 4. 发送请求
	resp, err := a.httpClient.Post(a.baseURL, "application/json", bytes.NewBuffer(requestJSON))
	if err != nil {
		return fmt.Errorf("发送回复工单请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 5. 解析响应
	var response YuntongWorkOrderResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("解析回复工单响应失败: %w", err)
	}

	if !response.Success || response.ResultCode != 100 {
		return fmt.Errorf("回复工单失败: ResultCode=%d", response.ResultCode)
	}

	return nil
}

// DeleteWorkOrder 删除/取消工单
func (a *YuntongWorkOrderAdapter) DeleteWorkOrder(ctx context.Context, providerWorkOrderID string, reason string) error {
	// 云通API不支持直接删除工单，但可以尝试取消相关订单
	// 这里我们记录删除请求，但不执行实际的API调用
	a.logger.Info("云通不支持删除工单，仅记录删除请求",
		zap.String("provider_work_order_id", providerWorkOrderID),
		zap.String("reason", reason))

	// 返回成功，表示本地删除可以继续
	return nil
}

// UploadAttachment 上传附件
func (a *YuntongWorkOrderAdapter) UploadAttachment(ctx context.Context, fileName string, fileContent []byte) (string, error) {
	// 云通没有单独的上传接口，需要使用第三方存储服务
	// 这里返回一个模拟的URL，实际应该集成OSS等存储服务
	return fmt.Sprintf("https://example.com/uploads/%s", fileName), nil
}

// ParseCallback 解析回调数据
func (a *YuntongWorkOrderAdapter) ParseCallback(ctx context.Context, callbackData interface{}) (*model.WorkOrderCallbackData, error) {
	// 1. 验证回调数据格式
	data, ok := callbackData.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("回调数据格式错误")
	}

	// 2. 验证必填字段（根据官方文档要求）
	if err := a.validateWorkOrderCallbackData(data); err != nil {
		return nil, fmt.Errorf("回调数据验证失败: %w", err)
	}

	// 3. 检查是否是工单回复推送
	requestType, _ := data["RequestType"].(string)
	if requestType != "105" {
		return nil, fmt.Errorf("不是工单回复推送，RequestType=%s", requestType)
	}

	// 4. 解析Data数组
	dataArray, ok := data["Data"].([]interface{})
	if !ok || len(dataArray) == 0 {
		return nil, fmt.Errorf("回调数据Data数组格式错误或为空")
	}

	// 5. 提取工单数据
	workOrderData, ok := dataArray[0].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("工单数据格式错误")
	}

	// 6. 提取所有必要字段
	complaintNumber, _ := workOrderData["ComplaintNumber"].(string)
	reason, _ := workOrderData["Reason"].(string)
	resultType, _ := workOrderData["ResultType"].(float64)
	dealResult, _ := workOrderData["dealResult"].(string)

	// 提取额外字段用于完整性记录
	shipperCode, _ := workOrderData["ShipperCode"].(string)
	orderCode, _ := workOrderData["OrderCode"].(string)
	logisticCode, _ := workOrderData["LogisticCode"].(string)
	createTime, _ := workOrderData["CreateTime"].(string)
	operateType, _ := workOrderData["OperateType"].(float64)
	complaintType, _ := workOrderData["ComplaintType"].(float64)

	// 7. 解析附件
	var attachmentURLs []string
	if picList, exists := workOrderData["PicList"].([]interface{}); exists {
		for _, pic := range picList {
			if picStr, ok := pic.(string); ok && picStr != "" {
				attachmentURLs = append(attachmentURLs, picStr)
			}
		}
	}

	// 8. 构建回复内容（包含处理结果）
	content := reason
	if dealResult != "" {
		content = reason + " " + dealResult
	}

	// 9. 生成回复人信息
	committer := "云通客服"
	if operateType == 1 {
		committer = "云通平台客服"
	}

	// 10. 记录详细的回调信息用于调试和监控
	a.logger.Info("云通工单回调解析完成",
		zap.String("complaint_number", complaintNumber),
		zap.String("order_code", orderCode),
		zap.String("logistic_code", logisticCode),
		zap.String("shipper_code", shipperCode),
		zap.Int("result_type", int(resultType)),
		zap.Int("complaint_type", int(complaintType)),
		zap.String("reason", reason),
		zap.String("deal_result", dealResult),
		zap.String("create_time", createTime),
		zap.Int("operate_type", int(operateType)),
		zap.Int("attachment_count", len(attachmentURLs)))

	return &model.WorkOrderCallbackData{
		ProviderWorkOrderID: complaintNumber,
		Status:              int(resultType),
		Content:             content,
		Committer:           committer,
		AttachmentURLs:      attachmentURLs,
	}, nil
}

// getErrorMessage 获取错误消息
func (a *YuntongWorkOrderAdapter) getErrorMessage(resultCode int) string {
	switch resultCode {
	case 100:
		return "成功"
	case 101:
		return "参数错误"
	case 102:
		return "签名验证失败"
	case 103:
		return "商户不存在"
	case 104:
		return "权限不足"
	case 105:
		return "运单号不存在"
	case 106:
		return "工单类型不支持"
	case 107:
		return "工单已存在"
	case 108:
		return "工单不存在"
	case 109:
		return "系统异常"
	case 110:
		return "服务超时"
	case 300:
		return "无效请求或参数格式错误"
	case 301:
		return "重复提交"
	case 302:
		return "业务处理失败"
	default:
		return "未知错误"
	}
}

// mapStatusFromString 将字符串状态映射为数字
func (a *YuntongWorkOrderAdapter) mapStatusFromString(status string) int {
	switch status {
	case "未处理":
		return 0
	case "无需处理":
		return 1
	case "驳回":
		return 2
	case "通过":
		return 3
	default:
		return 0
	}
}

// 云通响应结构体
type YuntongWorkOrderResponse struct {
	EBusinessID          string `json:"EBusinessID"`
	ComplaintNumber      string `json:"ComplaintNumber"` // 修复：改为string类型
	Success              bool   `json:"Success"`
	ResultCode           int    `json:"ResultCode"`
	UniquerRequestNumber string `json:"UniquerRequestNumber"`
}

type YuntongQueryWorkOrderResponse struct {
	EBusinessID          string `json:"EBusinessID"`
	Success              bool   `json:"Success"`
	ResultCode           int    `json:"ResultCode"`
	UniquerRequestNumber string `json:"UniquerRequestNumber"`
	Data                 []struct {
		OrderCode       string   `json:"OrderCode"`
		LogisticCode    string   `json:"LogisticCode"`
		ComplaintNumber int      `json:"ComplaintNumber"`
		ComplaintType   string   `json:"ComplaintType"`
		CreateTime      string   `json:"createTime"`
		Status          string   `json:"status"`
		DealResult      string   `json:"dealResult"`
		PicList         []string `json:"PicList"`
		DetailTracks    []struct {
			Time   string `json:"Time"`
			Name   string `json:"Name"`
			Result string `json:"Result"`
		} `json:"DetailTracks"`
	} `json:"data"`
}

// validateYuntongBasicRequest 验证云通工单基础请求参数（不包括类型验证）
func (a *YuntongWorkOrderAdapter) validateYuntongBasicRequest(req *model.CreateWorkOrderRequest) error {
	// 1. 验证订单号或运单号（根据云通官方文档，两者必须填一个）
	if (req.OrderNo == nil || *req.OrderNo == "") && (req.TrackingNo == nil || *req.TrackingNo == "") {
		return fmt.Errorf("订单号和运单号至少需要提供一个")
	}

	// 2. 验证工单内容不能为空
	if req.Content == "" {
		return fmt.Errorf("工单内容不能为空")
	}

	return nil
}

// validateWorkOrderCallbackData 验证云通工单回调数据
func (a *YuntongWorkOrderAdapter) validateWorkOrderCallbackData(data map[string]interface{}) error {
	// 1. 验证顶级必填字段
	requiredFields := []string{"RequestType", "EBusinessID", "Data", "Count"}
	for _, field := range requiredFields {
		if _, exists := data[field]; !exists {
			return fmt.Errorf("缺少必填字段: %s", field)
		}
	}

	// 2. 验证RequestType
	requestType, ok := data["RequestType"].(string)
	if !ok || requestType != "105" {
		return fmt.Errorf("RequestType必须为字符串'105'")
	}

	// 3. 验证EBusinessID
	eBusinessID, ok := data["EBusinessID"].(string)
	if !ok || eBusinessID == "" {
		return fmt.Errorf("EBusinessID不能为空")
	}

	// 4. 验证Data数组
	dataArray, ok := data["Data"].([]interface{})
	if !ok {
		return fmt.Errorf("Data必须为数组")
	}

	// 5. 验证Count字段
	count, ok := data["Count"].(float64)
	if !ok || count < 0 {
		return fmt.Errorf("Count必须为非负数")
	}

	// 6. 验证Data数组长度与Count一致
	if int(count) != len(dataArray) {
		return fmt.Errorf("Count值(%d)与Data数组长度(%d)不一致", int(count), len(dataArray))
	}

	// 7. 验证Data数组中的工单数据
	if len(dataArray) > 0 {
		workOrderData, ok := dataArray[0].(map[string]interface{})
		if !ok {
			return fmt.Errorf("Data数组中的工单数据格式错误")
		}

		// 验证工单数据的必填字段
		workOrderRequiredFields := []string{"ComplaintNumber", "ResultType", "ShipperCode", "OrderCode", "LogisticCode"}
		for _, field := range workOrderRequiredFields {
			if _, exists := workOrderData[field]; !exists {
				return fmt.Errorf("工单数据缺少必填字段: %s", field)
			}
		}

		// 验证ComplaintNumber不为空
		if complaintNumber, ok := workOrderData["ComplaintNumber"].(string); !ok || complaintNumber == "" {
			return fmt.Errorf("ComplaintNumber不能为空")
		}

		// 验证ResultType为有效值
		if resultType, ok := workOrderData["ResultType"].(float64); !ok || (resultType < 0 || resultType > 3) {
			return fmt.Errorf("ResultType必须为0-3之间的数字")
		}
	}

	return nil
}

// validateYuntongWorkOrderType 验证云通工单类型和参数
func (a *YuntongWorkOrderAdapter) validateYuntongWorkOrderType(req *model.CreateWorkOrderRequest, yuntongType int) error {
	// 1. 验证云通支持的工单类型（使用数字类型映射）
	supportedTypes := map[int]string{
		2: "重量或计费异常",   // 计费异常
		3: "取消快递订单",    // 取消订单
		4: "催促快递员上门取件", // 催促取件
		5: "催促快递员派送",   // 催促派送
		6: "拦截转寄",      // 拦截转寄
		7: "线下收费",      // 线下收费
		9: "其他物流问题",    // 其他问题
	}

	typeName, isSupported := supportedTypes[yuntongType]
	if !isSupported {
		return fmt.Errorf("云通不支持工单类型: %d", yuntongType)
	}

	// 2. 验证工单类型特定的必填参数
	switch yuntongType {
	case 2: // 计费异常工单
		if req.FeedbackWeight == nil {
			return fmt.Errorf("计费异常工单必须填写实际重量(ActualWeight)")
		}
		// 验证商品名称（通过ProviderSpecificData传递）
		if providerData, ok := req.ProviderSpecificData["yuntong"].(map[string]interface{}); ok {
			if goodsName, exists := providerData["goodsName"].(string); !exists || goodsName == "" {
				a.logger.Warn("计费异常工单建议提供商品名称", zap.Int("type", yuntongType))
			}
		}
	}

	a.logger.Info("云通工单参数验证通过",
		zap.Int("yuntong_type", yuntongType),
		zap.String("type_name", typeName),
		zap.String("content", req.Content))

	return nil
}
