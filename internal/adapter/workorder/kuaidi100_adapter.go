package workorder

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// Kuaidi100WorkOrderAdapter 快递100工单适配器
type Kuaidi100WorkOrderAdapter struct {
	apiKey     string
	secret     string
	customer   string
	baseURL    string
	httpClient *http.Client
	logger     *zap.Logger
	repo       repository.WorkOrderRepository
}

// NewKuaidi100WorkOrderAdapter 创建快递100工单适配器
func NewKuaidi100WorkOrderAdapter(apiKey, secret, customer string, repo repository.WorkOrderRepository, logger *zap.Logger) service.WorkOrderProviderAdapter {
	return &Kuaidi100WorkOrderAdapter{
		apiKey:   apiKey,
		secret:   secret,
		customer: customer,
		baseURL:  "https://api.kuaidi100.com",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
		repo:   repo,
	}
}

// CreateWorkOrder 创建工单
func (a *Kuaidi100WorkOrderAdapter) CreateWorkOrder(ctx context.Context, req *model.CreateWorkOrderRequest) (*service.ProviderWorkOrderResponse, error) {
	// 1. 直接使用服务层传递的快递100工单类型
	// 服务层已经完成了统一类型到快递100类型的转换
	kuaidi100WorkOrderType := req.WorkOrderType

	a.logger.Info("快递100工单创建",
		zap.String("provider", "kuaidi100"),
		zap.Int("kuaidi100_work_order_type", kuaidi100WorkOrderType),
		zap.String("tracking_no", *req.TrackingNo))

	// 2. 验证快递100特定的参数要求
	if err := a.validateKuaidi100WorkOrderRequest(req, kuaidi100WorkOrderType); err != nil {
		a.logger.Error("快递100工单参数验证失败",
			zap.Int("kuaidi100_type", kuaidi100WorkOrderType),
			zap.Error(err))
		return nil, fmt.Errorf("快递100工单参数验证失败: %w", err)
	}

	// 3. 构建请求参数
	param := map[string]interface{}{
		"kuaidinum":  *req.TrackingNo,        // 快递100必须有快递单号
		"secondType": kuaidi100WorkOrderType, // 使用转换后的快递100工单类型
		"desc": map[string]interface{}{
			"content": req.Content,
		},
	}

	// 添加可选参数
	if req.FeedbackWeight != nil {
		// 快递100要求反馈重量不能超过五个字
		weightStr := fmt.Sprintf("%.1f", *req.FeedbackWeight)
		if len(weightStr) > 5 {
			return nil, fmt.Errorf("反馈重量不能超过五个字，当前: %s", weightStr)
		}
		param["telWeight"] = weightStr

		a.logger.Info("快递100重量异常工单验证通过",
			zap.Float64("feedback_weight", *req.FeedbackWeight),
			zap.String("weight_str", weightStr))
	}

	if req.GoodsValue != nil {
		param["modityValue"] = fmt.Sprintf("%.2f", *req.GoodsValue)

		a.logger.Info("快递100商品价值工单验证通过",
			zap.Float64("goods_value", *req.GoodsValue))
	}

	// 设置回调地址，统一由系统决定，不依赖外部传入
	callbackManager := config.GetCallbackURLManager()
	callbackURL := callbackManager.GetCallbackURL("kuaidi100")
	param["callBackUrl"] = callbackURL
	param["msgCallBackUrl"] = callbackURL

	// 处理附件
	if len(req.AttachmentURLs) > 0 {
		attachments := make([]map[string]interface{}, 0, len(req.AttachmentURLs))
		for _, url := range req.AttachmentURLs {
			attachments = append(attachments, map[string]interface{}{
				"type": 0, // 图片类型
				"uri":  url,
			})
		}
		if desc, ok := param["desc"].(map[string]interface{}); ok {
			desc["attach"] = attachments
		}

		a.logger.Info("快递100工单附件验证通过",
			zap.Int("attachment_count", len(req.AttachmentURLs)))
	}

	// 4. 序列化参数
	paramJSON, err := json.Marshal(param)
	if err != nil {
		return nil, fmt.Errorf("序列化请求参数失败: %w", err)
	}

	// 5. 生成签名
	timestamp := strconv.FormatInt(util.NowBeijing().UnixMilli(), 10)
	sign := a.generateSignature(string(paramJSON), timestamp)

	// 6. 构建请求数据 - 使用与下单接口相同的参数顺序
	data := url.Values{}
	data.Set("method", "workorder") // 添加method参数
	data.Set("key", a.apiKey)
	data.Set("t", timestamp)
	data.Set("sign", sign)
	data.Set("param", string(paramJSON))

	// 记录API请求详情
	a.logger.Info("快递100工单API请求",
		zap.String("url", a.baseURL+"/workorder/api/create"),
		zap.String("method", "POST"),
		zap.String("param", string(paramJSON)),
		zap.String("timestamp", timestamp))

	// 7. 发送请求
	resp, err := a.httpClient.PostForm(a.baseURL+"/workorder/api/create", data)
	if err != nil {
		a.logger.Error("发送快递100创建工单请求失败", zap.Error(err))
		return nil, fmt.Errorf("发送创建工单请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 8. 解析响应
	var response Kuaidi100WorkOrderResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		a.logger.Error("解析快递100创建工单响应失败", zap.Error(err))
		return nil, fmt.Errorf("解析创建工单响应失败: %w", err)
	}

	// 记录API响应详情
	a.logger.Info("快递100工单API响应",
		zap.Int("code", response.Code),
		zap.String("message", response.Message),
		zap.Bool("success", response.Success),
		zap.Any("data", response.Data))

	if !response.Success || response.Code != 200 {
		a.logger.Error("快递100创建工单失败",
			zap.Int("code", response.Code),
			zap.String("message", response.Message),
			zap.Bool("success", response.Success))
		return nil, fmt.Errorf("创建工单失败: [%d] %s", response.Code, response.Message)
	}

	// 9. 转换为统一响应格式
	result := &service.ProviderWorkOrderResponse{
		ProviderWorkOrderID: strconv.Itoa(response.Data.ID),
		Status:              response.Data.Status,
		Message:             response.Message,
		Data: map[string]interface{}{
			"id":     response.Data.ID,
			"status": response.Data.Status,
		},
	}

	a.logger.Info("快递100工单创建成功",
		zap.String("provider_work_order_id", result.ProviderWorkOrderID),
		zap.Int("status", result.Status),
		zap.String("message", result.Message))

	return result, nil
}

// QueryWorkOrder 查询工单
func (a *Kuaidi100WorkOrderAdapter) QueryWorkOrder(ctx context.Context, providerWorkOrderID string) (*service.ProviderWorkOrderResponse, error) {
	// 1. 构建请求参数
	consultID, err := strconv.Atoi(providerWorkOrderID)
	if err != nil {
		return nil, fmt.Errorf("工单ID格式错误: %w", err)
	}

	param := map[string]interface{}{
		"consultId": consultID,
	}

	// 2. 序列化参数
	paramJSON, err := json.Marshal(param)
	if err != nil {
		return nil, fmt.Errorf("序列化请求参数失败: %w", err)
	}

	// 3. 生成签名
	timestamp := strconv.FormatInt(util.NowBeijing().UnixMilli(), 10)
	sign := a.generateSignature(string(paramJSON), timestamp)

	// 4. 构建请求数据 - 使用与下单接口相同的参数顺序
	data := url.Values{}
	data.Set("method", "status") // 查询工单状态的method参数
	data.Set("key", a.apiKey)
	data.Set("t", timestamp)
	data.Set("sign", sign)
	data.Set("param", string(paramJSON))

	// 5. 发送请求
	resp, err := a.httpClient.PostForm(a.baseURL+"/workorder/api/status", data)
	if err != nil {
		return nil, fmt.Errorf("发送查询工单请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 6. 解析响应
	var response Kuaidi100WorkOrderDetailResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析查询工单响应失败: %w", err)
	}

	if !response.Success || response.Code != 200 {
		return nil, fmt.Errorf("查询工单失败: %s", response.Message)
	}

	// 7. 转换为统一响应格式
	return &service.ProviderWorkOrderResponse{
		ProviderWorkOrderID: providerWorkOrderID,
		Status:              response.Data.Status,
		Message:             response.Message,
		Data: map[string]interface{}{
			"detail": response.Data,
		},
	}, nil
}

// ReplyWorkOrder 回复工单
func (a *Kuaidi100WorkOrderAdapter) ReplyWorkOrder(ctx context.Context, providerWorkOrderID string, content string, attachmentURLs []string) error {
	// 1. 构建请求参数
	consultID, err := strconv.Atoi(providerWorkOrderID)
	if err != nil {
		return fmt.Errorf("工单ID格式错误: %w", err)
	}

	param := map[string]interface{}{
		"consultId": consultID,
		"content":   content,
	}

	// 处理附件
	if len(attachmentURLs) > 0 {
		attachments := make([]map[string]interface{}, 0, len(attachmentURLs))
		for _, url := range attachmentURLs {
			attachments = append(attachments, map[string]interface{}{
				"type": 0, // 图片类型
				"uri":  url,
			})
		}
		param["attach"] = attachments
	}

	// 2. 序列化参数
	paramJSON, err := json.Marshal(param)
	if err != nil {
		return fmt.Errorf("序列化请求参数失败: %w", err)
	}

	// 3. 生成签名
	timestamp := strconv.FormatInt(util.NowBeijing().UnixMilli(), 10)
	sign := a.generateSignature(string(paramJSON), timestamp)

	// 4. 构建请求数据
	data := url.Values{}
	data.Set("key", a.apiKey)
	data.Set("param", string(paramJSON))
	data.Set("t", timestamp)
	data.Set("sign", sign)
	data.Set("method", "addReply")

	// 5. 发送请求
	resp, err := a.httpClient.PostForm(a.baseURL+"/workorder/api/reply", data)
	if err != nil {
		return fmt.Errorf("发送回复工单请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 6. 解析响应
	var response Kuaidi100WorkOrderResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("解析回复工单响应失败: %w", err)
	}

	if !response.Success || response.Code != 200 {
		return fmt.Errorf("回复工单失败: %s", response.Message)
	}

	return nil
}

// DeleteWorkOrder 删除/取消工单
func (a *Kuaidi100WorkOrderAdapter) DeleteWorkOrder(ctx context.Context, providerWorkOrderID string, reason string) error {
	// 快递100 API不支持直接删除工单，但可以尝试取消相关订单
	// 这里我们记录删除请求，但不执行实际的API调用
	a.logger.Info("快递100不支持删除工单，仅记录删除请求",
		zap.String("provider_work_order_id", providerWorkOrderID),
		zap.String("reason", reason))

	// 返回成功，表示本地删除可以继续
	return nil
}

// UploadAttachment 上传附件
func (a *Kuaidi100WorkOrderAdapter) UploadAttachment(ctx context.Context, fileName string, fileContent []byte) (string, error) {
	// 1. 构建multipart请求
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件
	part, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return "", fmt.Errorf("创建文件表单失败: %w", err)
	}

	if _, err := part.Write(fileContent); err != nil {
		return "", fmt.Errorf("写入文件内容失败: %w", err)
	}

	// 添加其他参数
	timestamp := strconv.FormatInt(util.NowBeijing().UnixMilli(), 10)
	param := map[string]interface{}{
		"fileName": strings.TrimSuffix(fileName, "."+getFileExtension(fileName)),
	}

	paramJSON, _ := json.Marshal(param)
	sign := a.generateSignature(string(paramJSON), timestamp)

	writer.WriteField("key", a.apiKey)
	writer.WriteField("param", string(paramJSON))
	writer.WriteField("t", timestamp)
	writer.WriteField("sign", sign)

	writer.Close()

	// 2. 发送请求
	req, err := http.NewRequestWithContext(ctx, "POST", a.baseURL+"/workorder/api/upload", &buf)
	if err != nil {
		return "", fmt.Errorf("创建上传请求失败: %w", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	resp, err := a.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送上传请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 3. 解析响应
	var response Kuaidi100UploadResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("解析上传响应失败: %w", err)
	}

	if !response.Success || response.Code != 200 {
		return "", fmt.Errorf("上传附件失败: %s", response.Message)
	}

	return response.Data, nil
}

// ParseCallback 解析回调数据
func (a *Kuaidi100WorkOrderAdapter) ParseCallback(ctx context.Context, callbackData interface{}) (*model.WorkOrderCallbackData, error) {
	// 1. 验证回调数据格式
	data, ok := callbackData.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("回调数据格式错误")
	}

	// 2. 验证必填字段
	if err := a.validateKuaidi100CallbackData(data); err != nil {
		return nil, fmt.Errorf("快递100工单回调数据验证失败: %w", err)
	}

	// 3. 提取工单ID
	workOrderID, ok := data["workorderId"]
	if !ok {
		return nil, fmt.Errorf("回调数据缺少工单ID")
	}

	// 4. 判断是工单结果回调还是留言回调
	if committer, exists := data["committer"]; exists {
		// 留言回调格式
		content, _ := data["content"].(string)
		lastModified, _ := data["lastModified"].(string)

		// 处理附件
		var attachmentURLs []string
		if attachList, exists := data["attach"].([]interface{}); exists {
			for _, attachItem := range attachList {
				if attach, ok := attachItem.(map[string]interface{}); ok {
					if uri, exists := attach["uri"].(string); exists && uri != "" {
						attachmentURLs = append(attachmentURLs, uri)
					}
				}
			}
		}

		// 记录留言回调信息
		a.logger.Info("快递100工单留言回调解析完成",
			zap.String("work_order_id", fmt.Sprintf("%v", workOrderID)),
			zap.String("committer", fmt.Sprintf("%v", committer)),
			zap.String("content", content),
			zap.String("last_modified", lastModified),
			zap.Int("attachment_count", len(attachmentURLs)))

		return &model.WorkOrderCallbackData{
			ProviderWorkOrderID: fmt.Sprintf("%v", workOrderID),
			Status:              0, // 留言回调不改变状态
			Content:             content,
			Committer:           fmt.Sprintf("%v", committer),
			AttachmentURLs:      attachmentURLs,
		}, nil
	} else {
		// 工单结果回调格式
		status, _ := data["status"].(float64)
		result, _ := data["result"].(string)

		// 记录工单结果回调信息
		a.logger.Info("快递100工单结果回调解析完成",
			zap.String("work_order_id", fmt.Sprintf("%v", workOrderID)),
			zap.Int("status", int(status)),
			zap.String("result", result))

		return &model.WorkOrderCallbackData{
			ProviderWorkOrderID: fmt.Sprintf("%v", workOrderID),
			Status:              int(status),
			Content:             result,
			Committer:           "快递100客服",
			AttachmentURLs:      []string{},
		}, nil
	}
}

// generateSignature 生成签名
func (a *Kuaidi100WorkOrderAdapter) generateSignature(param, timestamp string) string {
	// 快递100工单API签名算法: MD5(param + timestamp + key + secret)
	// 与下单接口保持一致
	signStr := param + timestamp + a.apiKey + a.secret
	hash := md5.Sum([]byte(signStr))
	return fmt.Sprintf("%X", hash)
}

// getFileExtension 获取文件扩展名
func getFileExtension(fileName string) string {
	parts := strings.Split(fileName, ".")
	if len(parts) > 1 {
		return parts[len(parts)-1]
	}
	return ""
}

// 快递100响应结构体
type Kuaidi100WorkOrderResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Success bool   `json:"success"`
	Time    int    `json:"time"`
	Data    struct {
		ID     int `json:"id"`
		Status int `json:"status"`
	} `json:"data"`
}

type Kuaidi100WorkOrderDetailResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Success bool   `json:"success"`
	Time    int    `json:"time"`
	Data    struct {
		ID           int    `json:"id"`
		Status       int    `json:"status"`
		StatusDesc   string `json:"statusDesc"`
		SecondType   int    `json:"secondType"`
		SecondDesc   string `json:"secondDesc"`
		KuaidiNum    string `json:"kuaidiNum"`
		KuaidiCom    string `json:"kuaidiCom"`
		CreateTime   string `json:"createTime"`
		LastModified string `json:"lastModified"`
		Desc         string `json:"desc"`
		Promise      string `json:"promise"`
		TelWeight    string `json:"telWeight"`
		Weight       string `json:"weight"`
		ReplyList    []struct {
			Committer    string `json:"committer"`
			Content      string `json:"content"`
			LastModified string `json:"lastModified"`
			Attach       []struct {
				URI  string `json:"uri"`
				Type int    `json:"type"`
			} `json:"attach"`
		} `json:"replyList"`
		OpRecords []struct {
			ID      int    `json:"id"`
			Content string `json:"content"`
			Result  string `json:"result"`
		} `json:"opRecords"`
	} `json:"data"`
}

type Kuaidi100UploadResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Success bool   `json:"success"`
	Time    int    `json:"time"`
	Data    string `json:"data"` // 文件URL
}

// validateKuaidi100WorkOrderRequest 验证快递100工单请求参数
func (a *Kuaidi100WorkOrderAdapter) validateKuaidi100WorkOrderRequest(req *model.CreateWorkOrderRequest, kuaidi100Type int) error {
	// 1. 验证快递单号（快递100必须有快递单号）
	if req.TrackingNo == nil || *req.TrackingNo == "" {
		return fmt.Errorf("快递100工单必须提供快递单号")
	}

	// 2. 验证工单内容不能为空
	if req.Content == "" {
		return fmt.Errorf("快递100工单内容不能为空")
	}

	// 3. 验证快递100支持的工单类型（根据用户指定的6种类型映射）
	supportedTypes := map[int]string{
		1:  "催取件",  // 统一类型1(催取件) 和 统一类型17(重新分配快递员) → 快递100类型1(催取件)
		4:  "重量异常", // 统一类型2(重量异常) → 快递100类型4(重量异常)
		17: "催派送",  // 统一类型12(催派送) → 快递100类型17(催派送)
		16: "催物流",  // 统一类型16(物流停滞) → 快递100类型16(催物流)
		5:  "虚假揽收", // 统一类型19(取消订单) → 快递100类型5(虚假揽收)
	}

	typeName, isSupported := supportedTypes[kuaidi100Type]
	if !isSupported {
		return fmt.Errorf("快递100不支持工单类型: %d", kuaidi100Type)
	}

	// 4. 验证工单类型特定的必填参数
	switch kuaidi100Type {
	case 4: // 重量异常
		if req.FeedbackWeight == nil {
			return fmt.Errorf("重量异常工单必须填写反馈重量(telWeight)")
		}
		// 验证重量字符长度（不能超过五个字）
		weightStr := fmt.Sprintf("%.1f", *req.FeedbackWeight)
		if len(weightStr) > 5 {
			return fmt.Errorf("反馈重量不能超过五个字，当前: %s", weightStr)
		}
	}

	a.logger.Info("快递100工单参数验证通过",
		zap.String("tracking_no", *req.TrackingNo),
		zap.Int("kuaidi100_type", kuaidi100Type),
		zap.String("type_name", typeName),
		zap.String("content", req.Content))

	return nil
}

// validateKuaidi100CallbackData 验证快递100工单回调数据
func (a *Kuaidi100WorkOrderAdapter) validateKuaidi100CallbackData(data map[string]interface{}) error {
	// 1. 验证workorderId（必填，number类型）
	workOrderID, exists := data["workorderId"]
	if !exists {
		return fmt.Errorf("缺少必填字段: workorderId")
	}

	// 验证workorderId是数字类型
	switch v := workOrderID.(type) {
	case int, int64, float64:
		// 数字类型，符合要求
	case string:
		// 字符串类型，尝试转换为数字
		if _, err := strconv.Atoi(v); err != nil {
			return fmt.Errorf("workorderId必须是数字类型，当前值: %v", v)
		}
	default:
		return fmt.Errorf("workorderId必须是数字类型，当前类型: %T", v)
	}

	// 2. 判断回调类型并验证相应字段
	if committer, exists := data["committer"]; exists {
		// 留言回调验证
		if committer == "" {
			return fmt.Errorf("留言回调的committer不能为空")
		}

		if content, exists := data["content"]; !exists || content == "" {
			return fmt.Errorf("留言回调的content不能为空")
		}

		if lastModified, exists := data["lastModified"]; !exists || lastModified == "" {
			return fmt.Errorf("留言回调的lastModified不能为空")
		}

		// 验证附件格式（可选）
		if attach, exists := data["attach"]; exists {
			if attachArray, ok := attach.([]interface{}); ok {
				for i, item := range attachArray {
					if attachItem, ok := item.(map[string]interface{}); ok {
						// 验证附件类型
						if attachType, exists := attachItem["type"]; exists {
							if typeInt, ok := attachType.(int); ok && typeInt != 0 {
								return fmt.Errorf("附件[%d]类型必须为0（图片），当前: %d", i, typeInt)
							}
						}
						// URI是可选的，但如果存在不能为空
						if uri, exists := attachItem["uri"]; exists {
							if uriStr, ok := uri.(string); !ok || uriStr == "" {
								return fmt.Errorf("附件[%d]的uri不能为空", i)
							}
						}
					}
				}
			}
		}
	} else {
		// 工单结果回调验证
		if status, exists := data["status"]; !exists {
			return fmt.Errorf("工单结果回调缺少必填字段: status")
		} else {
			// 验证status是int类型
			switch v := status.(type) {
			case int, int64, float64:
				// 数字类型，符合要求
			case string:
				// 字符串类型，尝试转换为数字
				if _, err := strconv.Atoi(v); err != nil {
					return fmt.Errorf("status必须是数字类型，当前值: %v", v)
				}
			default:
				return fmt.Errorf("status必须是数字类型，当前类型: %T", v)
			}
		}

		// result是可选的，但如果存在应该是字符串
		if result, exists := data["result"]; exists {
			if _, ok := result.(string); !ok {
				return fmt.Errorf("result必须是字符串类型，当前类型: %T", result)
			}
		}
	}

	return nil
}
