package workorder

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// YidaWorkOrderAdapter 易达工单适配器
type YidaWorkOrderAdapter struct {
	username   string
	privateKey string
	baseURL    string
	httpClient *http.Client
	repo       repository.WorkOrderRepository
	logger     *zap.Logger
}

// NewYidaWorkOrderAdapter 创建易达工单适配器
func NewYidaWorkOrderAdapter(username, privateKey string, repo repository.WorkOrderRepository, logger *zap.Logger) service.WorkOrderProviderAdapter {
	return &YidaWorkOrderAdapter{
		username:   username,
		privateKey: privateKey,
		baseURL:    "https://www.yida178.cn/prod-api/thirdApi/execute",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		repo:   repo,
		logger: logger,
	}
}

// CreateWorkOrder 创建工单
func (a *YidaWorkOrderAdapter) CreateWorkOrder(ctx context.Context, req *model.CreateWorkOrderRequest) (*service.ProviderWorkOrderResponse, error) {
	// 1. 直接使用服务层传递的易达工单类型
	// 服务层已经完成了统一类型到易达类型的转换
	yidaWorkOrderType := req.WorkOrderType

	a.logger.Info("易达工单创建",
		zap.String("provider", "yida"),
		zap.Int("yida_work_order_type", yidaWorkOrderType))

	// 2. 验证易达特定的参数要求（不验证类型转换）
	if err := a.validateYidaBasicRequest(req); err != nil {
		return nil, fmt.Errorf("易达工单参数验证失败: %w", err)
	}

	// 3. 验证易达工单类型和参数
	if err := a.validateYidaWorkOrderType(req, yidaWorkOrderType); err != nil {
		return nil, fmt.Errorf("易达工单类型验证失败: %w", err)
	}

	// 4. 构建业务参数
	businessParams := map[string]interface{}{
		"type":    yidaWorkOrderType, // 🔥 修复：现在是int类型，符合易达API要求
		"content": req.Content,
	}

	// 添加订单号或运单号（两者必传一）
	if req.OrderNo != nil && *req.OrderNo != "" {
		businessParams["orderNo"] = *req.OrderNo
	}
	if req.TrackingNo != nil && *req.TrackingNo != "" {
		businessParams["deliveryId"] = *req.TrackingNo
	}

	// 添加可选参数
	if req.FeedbackWeight != nil {
		businessParams["weight"] = a.formatWeightForYida(*req.FeedbackWeight)
	}

	if req.GoodsValue != nil {
		businessParams["modity"] = fmt.Sprintf("%.2f", *req.GoodsValue)
	}

	if req.OverweightAmount != nil {
		businessParams["overweight"] = *req.OverweightAmount
	}

	// 设置回调地址，忽略外部传入的 CallbackURL，统一由系统配置决定
	callbackManager := config.GetCallbackURLManager()
	callbackURL := callbackManager.GetCallbackURL("yida")
	businessParams["notifyUrl"] = callbackURL

	// 处理附件（最多5张图片）
	if len(req.AttachmentURLs) > 0 {
		if len(req.AttachmentURLs) > 5 {
			return nil, fmt.Errorf("易达工单最多支持5张图片，当前提供了%d张", len(req.AttachmentURLs))
		}
		businessParams["urls"] = strings.Join(req.AttachmentURLs, ",")
	}

	// 处理重量异常特殊参数
	if yidaWorkOrderType == 2 { // 统一类型2(重量异常) → 易达重量异常(类型2)
		if providerData, ok := req.ProviderSpecificData["yida"].(map[string]interface{}); ok {
			if secondType, exists := providerData["secondType"]; exists {
				businessParams["secondType"] = secondType
			} else {
				// 默认为重量核实
				businessParams["secondType"] = 1
			}
		} else {
			businessParams["secondType"] = 1
		}
	}

	// 5. 构建完整请求
	timestamp := strconv.FormatInt(util.NowBeijing().UnixMilli(), 10)
	sign := a.generateSignature(timestamp)

	requestData := map[string]interface{}{
		"username":       a.username,
		"timestamp":      timestamp,
		"sign":           sign,
		"apiMethod":      "CREATE_WORK_ORDER",
		"businessParams": businessParams,
	}

	// 6. 序列化请求
	requestJSON, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	a.logger.Info("易达创建工单请求",
		zap.String("url", a.baseURL),
		zap.String("username", a.username),
		zap.Int("unified_type", req.WorkOrderType),
		zap.Int("yida_type", yidaWorkOrderType), // 🔥 修复：现在是int类型
		zap.String("api_method", "CREATE_WORK_ORDER"))

	// 7. 发送请求
	resp, err := a.httpClient.Post(a.baseURL, "application/json", bytes.NewBuffer(requestJSON))
	if err != nil {
		return nil, fmt.Errorf("发送创建工单请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 8. 解析响应
	var response YidaWorkOrderResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析创建工单响应失败: %w", err)
	}

	a.logger.Info("易达创建工单响应",
		zap.Int("code", response.Code),
		zap.String("msg", response.Msg),
		zap.String("task_no", response.Data.TaskNo),
		zap.Int("status", response.Data.Status))

	if response.Code != 200 {
		return nil, fmt.Errorf("创建工单失败: %s", response.Msg)
	}

	// 9. 转换为统一响应格式
	return &service.ProviderWorkOrderResponse{
		ProviderWorkOrderID: response.Data.TaskNo,
		Status:              response.Data.Status,
		Message:             response.Msg,
		Data: map[string]interface{}{
			"taskNo": response.Data.TaskNo,
			"status": response.Data.Status,
		},
	}, nil
}

// QueryWorkOrder 查询工单
func (a *YidaWorkOrderAdapter) QueryWorkOrder(ctx context.Context, providerWorkOrderID string) (*service.ProviderWorkOrderResponse, error) {
	// 易达没有单独的查询工单接口，这里返回基础信息
	return &service.ProviderWorkOrderResponse{
		ProviderWorkOrderID: providerWorkOrderID,
		Status:              1, // 默认状态
		Message:             "查询成功",
		Data: map[string]interface{}{
			"taskNo": providerWorkOrderID,
		},
	}, nil
}

// ReplyWorkOrder 回复工单
func (a *YidaWorkOrderAdapter) ReplyWorkOrder(ctx context.Context, providerWorkOrderID string, content string, attachmentURLs []string) error {
	// 1. 构建业务参数
	businessParams := map[string]interface{}{
		"taskNo":  providerWorkOrderID,
		"content": content,
	}

	// 处理附件
	if len(attachmentURLs) > 0 {
		businessParams["urls"] = strings.Join(attachmentURLs, ",")
	}

	// 2. 构建完整请求
	timestamp := strconv.FormatInt(util.NowBeijing().UnixMilli(), 10)
	sign := a.generateSignature(timestamp)

	requestData := map[string]interface{}{
		"username":       a.username,
		"timestamp":      timestamp,
		"sign":           sign,
		"apiMethod":      "REPLY_WORK_ORDER",
		"businessParams": businessParams,
	}

	// 3. 序列化请求
	requestJSON, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 4. 发送请求
	resp, err := a.httpClient.Post(a.baseURL, "application/json", bytes.NewBuffer(requestJSON))
	if err != nil {
		return fmt.Errorf("发送回复工单请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 5. 解析响应
	var response YidaWorkOrderResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("解析回复工单响应失败: %w", err)
	}

	if response.Code != 200 {
		return fmt.Errorf("回复工单失败: %s", response.Msg)
	}

	return nil
}

// DeleteWorkOrder 删除/取消工单
func (a *YidaWorkOrderAdapter) DeleteWorkOrder(ctx context.Context, providerWorkOrderID string, reason string) error {
	// 易达API不支持直接删除工单，但可以尝试取消相关订单
	// 这里我们记录删除请求，但不执行实际的API调用
	a.logger.Info("易达不支持删除工单，仅记录删除请求",
		zap.String("provider_work_order_id", providerWorkOrderID),
		zap.String("reason", reason))

	// 返回成功，表示本地删除可以继续
	return nil
}

// UploadAttachment 上传附件
func (a *YidaWorkOrderAdapter) UploadAttachment(ctx context.Context, fileName string, fileContent []byte) (string, error) {
	// 易达没有单独的上传接口，需要使用第三方存储服务
	// 这里返回一个模拟的URL，实际应该集成OSS等存储服务
	return fmt.Sprintf("https://example.com/uploads/%s", fileName), nil
}

// ParseCallback 解析回调数据
func (a *YidaWorkOrderAdapter) ParseCallback(ctx context.Context, callbackData interface{}) (*model.WorkOrderCallbackData, error) {
	// 1. 验证回调数据格式
	data, ok := callbackData.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("回调数据格式错误")
	}

	// 2. 验证必填字段（根据官方文档要求）
	if err := a.validateYidaCallbackData(data); err != nil {
		return nil, fmt.Errorf("易达工单回调数据验证失败: %w", err)
	}

	// 3. 提取必要字段
	taskNo, ok := data["taskNo"].(string)
	if !ok || taskNo == "" {
		return nil, fmt.Errorf("回调数据缺少工单编号")
	}

	status, ok := data["status"].(float64)
	if !ok {
		return nil, fmt.Errorf("回调数据缺少状态信息")
	}

	content, _ := data["content"].(string)
	urls, _ := data["urls"].(string)

	// 提取工单类型（用于验证和日志记录）
	workOrderType, _ := data["type"].(float64)

	// 解析附件URL
	var attachmentURLs []string
	if urls != "" {
		attachmentURLs = strings.Split(urls, ",")
		// 清理空字符串
		var cleanURLs []string
		for _, url := range attachmentURLs {
			if trimmed := strings.TrimSpace(url); trimmed != "" {
				cleanURLs = append(cleanURLs, trimmed)
			}
		}
		attachmentURLs = cleanURLs
	}

	// 处理特殊数据和生成回复人信息
	var committer string
	if dataObj, exists := data["data"].(map[string]interface{}); exists {
		if weight, exists := dataObj["weight"]; exists {
			committer = fmt.Sprintf("易达客服 (核实重量: %v kg)", weight)
		}
	}

	if committer == "" {
		// 根据工单类型生成更具体的回复人信息（统一化改造：调整为6种核心类型）
		switch int(workOrderType) {
		case model.WorkOrderTypeWeightException:
			committer = "易达客服 (重量核实)"
		case model.WorkOrderTypePickupReminder:
			committer = "易达客服 (揽收处理)"
		case model.WorkOrderTypeDeliveryReminder:
			committer = "易达客服 (派送处理)"
		case model.WorkOrderTypeLogisticsStall:
			committer = "易达客服 (物流处理)"
		case model.WorkOrderTypeReassignCourier:
			committer = "易达客服 (快递员调配)"
		case model.WorkOrderTypeCancelOrder:
			committer = "易达客服 (订单处理)"
		default:
			committer = "易达客服"
		}
	}

	// 记录回调信息用于调试
	a.logger.Info("易达工单回调解析完成",
		zap.String("task_no", taskNo),
		zap.Int("status", int(status)),
		zap.Int("work_order_type", int(workOrderType)),
		zap.String("committer", committer),
		zap.Int("attachment_count", len(attachmentURLs)))

	return &model.WorkOrderCallbackData{
		ProviderWorkOrderID: taskNo,
		Status:              int(status),
		Content:             content,
		Committer:           committer,
		AttachmentURLs:      attachmentURLs,
	}, nil
}

// validateYidaCallbackData 验证易达工单回调数据
func (a *YidaWorkOrderAdapter) validateYidaCallbackData(data map[string]interface{}) error {
	// 1. 验证taskNo（必填，String类型）
	taskNo, exists := data["taskNo"]
	if !exists {
		return fmt.Errorf("缺少必填字段: taskNo")
	}
	if taskNoStr, ok := taskNo.(string); !ok || taskNoStr == "" {
		return fmt.Errorf("taskNo必须是非空字符串，当前值: %v", taskNo)
	}

	// 2. 验证status（必填，Integer类型）
	status, exists := data["status"]
	if !exists {
		return fmt.Errorf("缺少必填字段: status")
	}

	// 验证status是数字类型并且是有效的状态码
	var statusInt int
	switch v := status.(type) {
	case int:
		statusInt = v
	case float64:
		statusInt = int(v)
	case string:
		if intVal, err := strconv.Atoi(v); err == nil {
			statusInt = intVal
		} else {
			return fmt.Errorf("status必须是数字类型，当前值: %v", v)
		}
	default:
		return fmt.Errorf("status必须是数字类型，当前类型: %T", v)
	}

	// 验证status是有效的易达状态码（1-待回复，2-已回复，3-已完结）
	if statusInt < 1 || statusInt > 3 {
		return fmt.Errorf("status必须是有效的易达状态码(1-3)，当前值: %d", statusInt)
	}

	// 3. 验证type（必填，Integer类型）
	workOrderType, exists := data["type"]
	if !exists {
		return fmt.Errorf("缺少必填字段: type")
	}

	// 验证type是数字类型
	switch v := workOrderType.(type) {
	case int, float64:
		// 数字类型，符合要求
	case string:
		if _, err := strconv.Atoi(v); err != nil {
			return fmt.Errorf("type必须是数字类型，当前值: %v", v)
		}
	default:
		return fmt.Errorf("type必须是数字类型，当前类型: %T", v)
	}

	// 4. 验证data（必填，Object类型）
	dataObj, exists := data["data"]
	if !exists {
		return fmt.Errorf("缺少必填字段: data")
	}

	// data必须是对象类型
	if _, ok := dataObj.(map[string]interface{}); !ok {
		return fmt.Errorf("data必须是对象类型，当前类型: %T", dataObj)
	}

	// 5. 验证可选字段的格式
	// content是可选的，但如果存在应该是字符串
	if content, exists := data["content"]; exists {
		if _, ok := content.(string); !ok {
			return fmt.Errorf("content必须是字符串类型，当前类型: %T", content)
		}
	}

	// urls是可选的，但如果存在应该是字符串
	if urls, exists := data["urls"]; exists {
		if _, ok := urls.(string); !ok {
			return fmt.Errorf("urls必须是字符串类型，当前类型: %T", urls)
		}
	}

	return nil
}

// generateSignature 生成签名
func (a *YidaWorkOrderAdapter) generateSignature(timestamp string) string {
	// 易达签名算法: MD5(JSON.stringify({privateKey, timestamp, username})).toUpperCase()
	signData := map[string]string{
		"privateKey": a.privateKey,
		"timestamp":  timestamp,
		"username":   a.username,
	}

	// 按字段名排序生成JSON
	signStr := fmt.Sprintf(`{"privateKey":"%s","timestamp":"%s","username":"%s"}`,
		signData["privateKey"], signData["timestamp"], signData["username"])

	hash := md5.Sum([]byte(signStr))
	return fmt.Sprintf("%X", hash)
}

// validateYidaWorkOrderType 验证易达工单类型和参数
func (a *YidaWorkOrderAdapter) validateYidaWorkOrderType(req *model.CreateWorkOrderRequest, yidaType int) error {
	// 1. 验证易达支持的工单类型（使用数字类型映射）
	supportedTypes := map[int]string{
		1:  "催促快递员上门揽收",  // 催揽收
		2:  "重量与实际不符",    // 重量异常
		10: "催促快递员派送",    // 催派送
		14: "物流信息长时间未更新", // 物流停滞
		15: "更换负责的快递员",   // 重新分配快递员
		16: "取消快递运单",     // 取消运单
	}

	typeName, isSupported := supportedTypes[yidaType]
	if !isSupported {
		return fmt.Errorf("易达不支持工单类型: %d", yidaType)
	}

	// 2. 验证工单类型特定的必填参数
	switch yidaType {
	case 2: // 重量异常工单
		if req.FeedbackWeight == nil {
			return fmt.Errorf("重量异常工单必须填写反馈重量(weight)")
		}
		// 验证secondType参数（通过ProviderSpecificData传递）
		if providerData, ok := req.ProviderSpecificData["yida"].(map[string]interface{}); ok {
			if secondType, exists := providerData["secondType"]; !exists {
				a.logger.Warn("重量异常工单建议提供secondType参数", zap.Int("type", yidaType))
			} else {
				a.logger.Info("重量异常工单secondType参数", zap.Any("second_type", secondType))
			}
		}
	}

	a.logger.Info("易达工单参数验证通过",
		zap.Int("yida_type", yidaType),
		zap.String("type_name", typeName),
		zap.String("content", req.Content))

	return nil
}

// validateYidaBasicRequest 验证易达工单基础请求参数（不包括类型验证）
func (a *YidaWorkOrderAdapter) validateYidaBasicRequest(req *model.CreateWorkOrderRequest) error {
	// 1. 验证订单号或运单号（两者必传一）
	if (req.OrderNo == nil || *req.OrderNo == "") && (req.TrackingNo == nil || *req.TrackingNo == "") {
		return fmt.Errorf("订单号和运单号至少需要提供一个")
	}

	// 2. 验证工单内容不能为空
	if req.Content == "" {
		return fmt.Errorf("工单内容不能为空")
	}

	return nil
}

// 易达响应结构体
type YidaWorkOrderResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		TaskNo string `json:"taskNo"`
		Status int    `json:"status"`
	} `json:"data"`
}

// 易达回调数据结构体
type YidaCallbackData struct {
	TaskNo  string `json:"taskNo"`
	Status  int    `json:"status"`
	Content string `json:"content"`
	URLs    string `json:"urls"`
	Type    int    `json:"type"`
	Data    struct {
		Weight float64 `json:"weight"`
	} `json:"data"`
}

// formatWeightForYida 格式化数值参数以符合易达API要求
// 易达API要求数值格式为最多2位小数，避免精度过高导致"重量格式不正确"等错误
func (a *YidaWorkOrderAdapter) formatWeightForYida(value float64) float64 {
	// 保留2位小数，避免精度过高导致API拒绝
	return math.Round(value*100) / 100
}
