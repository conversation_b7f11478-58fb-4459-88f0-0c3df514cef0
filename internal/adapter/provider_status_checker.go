package adapter

import (
	"context"
	"fmt"

	"go.uber.org/zap"
)

// DatabaseProviderStatusChecker 基于数据库的供应商状态检查器
// 现在使用统一的system_configs配置管理，不再依赖express_providers表
type DatabaseProviderStatusChecker struct {
	systemConfigService interface {
		GetConfigAsBoolWithDefault(key string, defaultValue bool) bool
	}
	logger *zap.Logger
}

// NewDatabaseProviderStatusChecker 创建数据库供应商状态检查器
func NewDatabaseProviderStatusChecker(systemConfigService interface {
	GetConfigAsBoolWithDefault(key string, defaultValue bool) bool
}, logger *zap.Logger) *DatabaseProviderStatusChecker {
	return &DatabaseProviderStatusChecker{
		systemConfigService: systemConfigService,
		logger:              logger,
	}
}

// IsProviderActive 检查供应商是否启用
// 现在从system_configs表中检查provider.{code}_enabled配置
func (c *DatabaseProviderStatusChecker) IsProviderActive(ctx context.Context, providerCode string) (bool, error) {
	// 构建配置键（格式：group.key，其中key使用下划线格式以符合数据库约束）
	configKey := fmt.Sprintf("provider.%s_enabled", providerCode)

	// 从system_configs获取启用状态
	enabled := c.systemConfigService.GetConfigAsBoolWithDefault(configKey, false)

	c.logger.Debug("供应商状态检查结果",
		zap.String("provider_code", providerCode),
		zap.String("config_key", configKey),
		zap.Bool("enabled", enabled))

	return enabled, nil
}

// GetActiveProviders 获取所有启用的供应商代码列表
// 现在从system_configs表中检查所有provider.{code}_enabled配置
func (c *DatabaseProviderStatusChecker) GetActiveProviders(ctx context.Context) ([]string, error) {
	// 定义已知的供应商代码
	knownProviders := []string{"kuaidi100", "yida", "yuntong", "cainiao", "kuaidiniao"}

	var activeCodes []string
	for _, providerCode := range knownProviders {
		configKey := fmt.Sprintf("provider.%s_enabled", providerCode)
		enabled := c.systemConfigService.GetConfigAsBoolWithDefault(configKey, false)

		// 添加详细的调试日志
		c.logger.Info("检查供应商启用状态",
			zap.String("provider_code", providerCode),
			zap.String("config_key", configKey),
			zap.Bool("enabled", enabled))

		if enabled {
			activeCodes = append(activeCodes, providerCode)
		}
	}

	c.logger.Info("获取启用供应商列表成功",
		zap.Int("count", len(activeCodes)),
		zap.Strings("provider_codes", activeCodes))

	return activeCodes, nil
}
