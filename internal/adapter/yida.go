package adapter

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// YidaAdapter 易达适配器
type YidaAdapter struct {
	config             YidaConfig
	client             *http.Client
	logger             *zap.Logger
	mappingService     express.ExpressMappingService
	expressCompanyRepo express.ExpressCompanyRepository
}

// YidaConfig 易达配置
type YidaConfig struct {
	AppKey    string `json:"app_key"`    // 用户名
	AppSecret string `json:"app_secret"` // 私钥
	BaseURL   string `json:"base_url"`   // 接口地址
	Timeout   int    `json:"timeout"`    // 超时时间(秒)
}

// NewYidaAdapter 创建易达适配器
func NewYidaAdapter(config YidaConfig, expressCompanyRepo express.ExpressCompanyRepository) *YidaAdapter {
	// 创建日志记录器
	logger, _ := zap.NewProduction()

	// 优化HTTP客户端配置 - 提升性能
	transport := &http.Transport{
		MaxIdleConns:          200,              // 增加最大空闲连接数
		MaxIdleConnsPerHost:   50,               // 增加每个主机最大空闲连接数
		IdleConnTimeout:       60 * time.Second, // 减少空闲连接超时
		DisableCompression:    false,            // 启用压缩
		ForceAttemptHTTP2:     true,             // 强制尝试HTTP/2
		ResponseHeaderTimeout: 8 * time.Second,  // 响应头超时
		ExpectContinueTimeout: 1 * time.Second,  // Expect: 100-continue超时
	}

	return &YidaAdapter{
		config:             config,
		expressCompanyRepo: expressCompanyRepo,
		client: &http.Client{
			Timeout:   time.Duration(config.Timeout) * time.Second,
			Transport: transport,
		},
		logger: logger,
		// mappingService 将在初始化后通过 SetMappingService 设置
	}
}

// SetMappingService 设置映射服务
func (a *YidaAdapter) SetMappingService(mappingService express.ExpressMappingService) {
	a.mappingService = mappingService
}

// Name 获取供应商名称
func (a *YidaAdapter) Name() string {
	return model.ExpressYida
}

// formatPhoneForYidaComprehensive 综合处理Mobile和Tel字段，符合易达API要求
// 易达API要求：senderTel/senderMobile和receiveTel/receiveMobile中2选1必填
func (a *YidaAdapter) formatPhoneForYidaComprehensive(mobile string, tel string) (resultTel string, resultMobile string) {
	// 优先使用明确指定的tel字段（固定电话）
	if tel != "" {
		a.logger.Info("🔥 易达电话格式化：使用专用Tel字段",
			zap.String("tel", tel))
		return tel, ""
	}

	// 优先使用明确指定的mobile字段（手机号）
	if mobile != "" {
		// 对mobile字段进行智能识别
		telResult, mobileResult := a.formatPhoneForYida(mobile)
		if mobileResult != "" {
			a.logger.Info("🔥 易达电话格式化：Mobile字段识别为手机号",
				zap.String("mobile", mobile),
				zap.String("result", mobileResult))
			return "", mobileResult
		} else {
			a.logger.Info("🔥 易达电话格式化：Mobile字段识别为固定电话",
				zap.String("mobile", mobile),
				zap.String("result", telResult))
			return telResult, ""
		}
	}

	// 都为空的情况
	return "", ""
}

// formatPhoneForYida 智能格式化电话号码，符合易达API要求（使用生产级验证器）
// 易达API要求：senderTel/senderMobile和receiveTel/receiveMobile中2选1必填
func (a *YidaAdapter) formatPhoneForYida(phone string) (tel string, mobile string) {
	if phone == "" {
		return "", ""
	}

	// 使用生产级电话验证器
	validator := util.GetPhoneValidator()
	result := validator.ValidatePhone(phone)

	if !result.IsValid {
		a.logger.Warn("易达电话格式化：电话号码格式无效",
			zap.String("original", phone),
			zap.String("error_code", result.ErrorCode))
		return phone, "" // 格式无效时作为固定电话处理
	}

	// 根据验证结果进行格式化
	switch result.Type {
	case util.PhoneTypeMobile:
		a.logger.Debug("易达电话格式化：识别为手机号",
			zap.String("original", phone),
			zap.String("cleaned", result.Cleaned))
		return "", result.Cleaned
	case util.PhoneTypeLandline:
		a.logger.Debug("易达电话格式化：识别为固定电话",
			zap.String("original", phone),
			zap.String("cleaned", result.Cleaned))
		return result.Cleaned, ""
	default:
		a.logger.Warn("易达电话格式化：未知电话类型",
			zap.String("original", phone))
		return phone, ""
	}
}

// QueryPrice 查询价格
func (a *YidaAdapter) QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
	// 如果指定了供应商但不是yida，则返回空结果
	if req.Provider != "" && req.Provider != model.ExpressYida {
		return []model.StandardizedPrice{}, nil
	}

	// 🚀 修复：易达API查价要求必须提供姓名信息，当前端未提供时使用默认值
	// 不再跳过易达查价，而是使用默认姓名信息确保API调用成功
	if req.Sender.Name == "" || req.Receiver.Name == "" {
		a.logger.Info("易达API查价缺少姓名信息，使用默认值",
			zap.String("sender_name", req.Sender.Name),
			zap.String("receiver_name", req.Receiver.Name))
		// 使用默认姓名，不跳过查询
		if req.Sender.Name == "" {
			req.Sender.Name = "寄件人"
		}
		if req.Receiver.Name == "" {
			req.Receiver.Name = "收件人"
		}
	}

	// 🔥 修复：综合处理Mobile和Tel字段，符合易达API要求
	senderTel, senderMobile := a.formatPhoneForYidaComprehensive(req.Sender.Mobile, req.Sender.Tel)
	receiverTel, receiverMobile := a.formatPhoneForYidaComprehensive(req.Receiver.Mobile, req.Receiver.Tel)

	// 构建业务参数 - 严格按照易达API文档要求
	businessParams := map[string]interface{}{
		// 发件人信息 - 必传字段（查价时使用默认值）
		"senderName":     a.getNameForPriceQuery(req.Sender.Name, "发件人"),
		"senderProvince": req.Sender.Province,
		"senderCity":     req.Sender.City,
		"senderDistrict": a.getValidDistrict(req.Sender.District),
		"senderAddress":  a.getValidAddress(req.Sender.Address, "发件地址"),

		// 收件人信息 - 易达API查价也要求收件人信息完整
		"receiveName":     a.getNameForPriceQuery(req.Receiver.Name, "收件人"),
		"receiveProvince": req.Receiver.Province,
		"receiveCity":     req.Receiver.City,
		"receiveDistrict": a.getValidDistrict(req.Receiver.District),
		"receiveAddress":  a.getValidAddress(req.Receiver.Address, "收件地址"),

		// 包裹信息 - 必传字段
		"weight":       a.formatWeightForYida(req.Package.Weight), // 格式化重量
		"packageCount": req.Package.Quantity,
		"goods":        a.getValidGoodsName(req.Package.GoodsName),

		// 支付方式 - 必传字段
		"onlinePay": "ALL", // 支持线上和线下支付

		// 生成唯一的订单号 - 必传字段
		"thirdNo": fmt.Sprintf("PRICE_QUERY_%d", util.NowBeijing().UnixNano()),

		// 默认寄件通道类型
		"customerType": "kd",
	}

	// 🔥 修复：根据电话类型只传递对应字段
	if senderMobile != "" {
		businessParams["senderMobile"] = senderMobile
	} else if senderTel != "" {
		businessParams["senderTel"] = senderTel
	} else {
		// 如果都为空，使用默认占位手机号
		businessParams["senderMobile"] = a.getMobileForPriceQuery("")
	}
	if receiverMobile != "" {
		businessParams["receiveMobile"] = receiverMobile
	} else if receiverTel != "" {
		businessParams["receiveTel"] = receiverTel
	} else {
		// 如果都为空，使用默认占位手机号
		businessParams["receiveMobile"] = a.getMobileForPriceQuery("")
	}

	// 可选字段
	if req.PayMethod > 0 {
		businessParams["payMethod"] = req.PayMethod
	}

	if req.Package.InsureValue > 0 {
		businessParams["guaranteeValueAmount"] = req.Package.InsureValue
	}

	// 🔥 企业级修复：优先使用长宽高，如果没有则从体积计算
	if req.Package.Length > 0 && req.Package.Width > 0 && req.Package.Height > 0 {
		// 直接使用长宽高（单位：cm）- 格式化为2位小数
		businessParams["vloumLong"] = a.formatWeightForYida(req.Package.Length)
		businessParams["vloumWidth"] = a.formatWeightForYida(req.Package.Width)
		businessParams["vloumHeight"] = a.formatWeightForYida(req.Package.Height)
	} else if req.Package.Volume > 0 {
		// 如果没有长宽高，从体积计算（假设是正方体）
		// 体积单位转换：m³ → cm³
		volumeCm3 := req.Package.Volume * 1000000
		dimension := math.Pow(volumeCm3, 1.0/3.0)
		businessParams["vloumLong"] = a.formatWeightForYida(dimension)
		businessParams["vloumWidth"] = a.formatWeightForYida(dimension)
		businessParams["vloumHeight"] = a.formatWeightForYida(dimension)
	}

	// 如果指定了快递公司，则只查询该快递公司
	if req.ExpressType != "" {
		// 使用转换函数获取正确的快递公司代码
		convertedCode := a.convertExpressType(req.ExpressType)
		// 🔧 修复：如果转换失败（返回空字符串），跳过查询
		if convertedCode == "" {
			fmt.Printf("[易达API] 快递公司 %s 映射失败，跳过查询\n", req.ExpressType)
			return []model.StandardizedPrice{}, nil
		}
		businessParams["deliveryType"] = convertedCode
		fmt.Printf("[易达API] 查询单个快递公司 %s 价格\n", convertedCode)
	} else if req.QueryAllCompanies {
		// 如果是查询所有快递公司，但没有指定快递公司代码，则使用易达的批量查询功能
		fmt.Printf("[易达API] 查询所有快递公司价格\n")
	} else {
		// 如果既没有指定快递公司，也没有设置查询所有快递公司，则返回错误
		return nil, fmt.Errorf("必须指定快递公司代码或设置查询所有快递公司")
	}

	// 调用易达 API 查询价格
	fmt.Printf("[易达API] 查询价格，参数: %+v\n", businessParams)
	return a.queryPriceWithSmartPreOrder(ctx, businessParams)
}

// 使用SMART_PRE_ORDER查询价格
func (a *YidaAdapter) queryPriceWithSmartPreOrder(ctx context.Context, businessParams map[string]interface{}) ([]model.StandardizedPrice, error) {
	// 调用易达 API
	configManager := config.GetProviderConfigManager()
	priceMethod := configManager.GetAPIMethod("yida", "price_query")
	if priceMethod == "" {
		priceMethod = "SMART_PRE_ORDER" // 默认值
	}
	result, err := a.callYidaAPI(ctx, priceMethod, businessParams)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var response struct {
		Code    int                      `json:"code"`
		Message string                   `json:"msg"`
		Data    map[string][]interface{} `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 200 {
		// 🔧 特殊处理：识别"供应商不支持"的情况
		if strings.Contains(response.Message, "不支持") ||
			strings.Contains(response.Message, "暂未开放") ||
			strings.Contains(response.Message, "该区域") ||
			strings.Contains(response.Message, "线路") {
			a.logger.Debug("易达供应商不支持该快递公司或线路",
				zap.Int("code", response.Code),
				zap.String("message", response.Message))
			return nil, fmt.Errorf("供应商不支持该快递公司: %s", response.Message)
		}
		return nil, fmt.Errorf("查询价格失败: %s", response.Message)
	}

	// 转换为标准价格结构
	var prices []model.StandardizedPrice

	// 遍历所有快递公司
	for expressType, channels := range response.Data {
		// 遍历该快递公司的所有渠道
		for _, channelData := range channels {
			// 将channelData转换为map
			channelMap, ok := channelData.(map[string]interface{})
			if !ok {
				continue
			}

			// 打印渠道数据（调试用）
			channelJSON, _ := json.Marshal(channelMap)
			fmt.Printf("渠道数据: %s\n", string(channelJSON))

			// 提取渠道信息
			var channelID string
			switch v := channelMap["channelId"].(type) {
			case string:
				channelID = v
			case float64:
				channelID = fmt.Sprintf("%.0f", v)
			case json.Number:
				channelID = string(v)
			default:
				// 如果无法获取channelId，使用时间戳作为替代
				channelID = fmt.Sprintf("%d", util.NowBeijing().UnixNano())
			}

			// 提取其他字段
			var channelName, deliveryType, deliveryBusiness string
			var preOrderFee, calcFeeWeight, continuedWeightPerKg float64

			// 渠道名称
			if v, ok := channelMap["channelName"].(string); ok {
				channelName = v
			}

			// 快递类型
			if v, ok := channelMap["deliveryType"].(string); ok {
				deliveryType = v
			} else {
				deliveryType = expressType
			}

			// 产品类型
			if v, ok := channelMap["deliveryBusiness"].(string); ok {
				deliveryBusiness = v
			}

			// 预估订单总费
			switch v := channelMap["preOrderFee"].(type) {
			case string:
				preOrderFee, _ = strconv.ParseFloat(v, 64)
			case float64:
				preOrderFee = v
			case json.Number:
				preOrderFee, _ = v.Float64()
			}

			// 计费重量
			switch v := channelMap["calcFeeWeight"].(type) {
			case string:
				calcFeeWeight, _ = strconv.ParseFloat(v, 64)
			case float64:
				calcFeeWeight = v
			case json.Number:
				calcFeeWeight, _ = v.Float64()
			}

			// 提取续重价格
			// 根据calcFeeType和price字段解析续重价格
			calcFeeType, _ := channelMap["calcFeeType"].(string)

			if calcFeeType == "profit" {
				// 通票价格块
				if priceStr, ok := channelMap["price"].(string); ok {
					var priceRules []struct {
						Start float64 `json:"start"`
						End   float64 `json:"end"`
						First float64 `json:"first"`
						Add   float64 `json:"add"`
					}

					if err := json.Unmarshal([]byte(priceStr), &priceRules); err == nil && len(priceRules) > 0 {
						// 使用第一个规则的续重价格
						continuedWeightPerKg = priceRules[0].Add
					}
				}
			} else if calcFeeType == "discount" {
				// 折扣价格块
				if priceStr, ok := channelMap["price"].(string); ok {
					var discountRule struct {
						Discount float64 `json:"discount"`
						PerAdd   float64 `json:"perAdd"`
					}

					if err := json.Unmarshal([]byte(priceStr), &discountRule); err == nil {
						// 获取原价规则
						if originalPriceStr, ok := channelMap["originalPrice"].(string); ok {
							var originalPriceRules []struct {
								Start float64 `json:"start"`
								End   float64 `json:"end"`
								First float64 `json:"first"`
								Add   float64 `json:"add"`
							}

							if err := json.Unmarshal([]byte(originalPriceStr), &originalPriceRules); err == nil && len(originalPriceRules) > 0 {
								// 计算折扣后的续重价格
								continuedWeightPerKg = originalPriceRules[0].Add * discountRule.Discount
							}
						}
					}
				}
			}

			// 🔥 企业级修复：优先使用易达API返回的计费重量
			// 易达API已经根据长宽高和抛比计算了正确的计费重量
			calcWeight := calcFeeWeight
			if calcWeight <= 0 {
				// 如果API没有返回计费重量，使用我们自己的计算
				// 从businessParams中获取原始重量和体积信息进行计算
				weight, _ := businessParams["weight"].(float64)
				calcWeight = a.calculateChargedWeightFromPackage("", model.PackageInfo{
					Weight: weight,
					Length: businessParams["vloumLong"].(float64),
					Width:  businessParams["vloumWidth"].(float64),
					Height: businessParams["vloumHeight"].(float64),
				})
			}

			// 🔧 修复：将易达供应商代码转换为标准代码
			standardCode := a.convertProviderCodeToStandard(deliveryType)

			// 创建标准价格结构
			price := model.StandardizedPrice{
				ExpressCode:          standardCode,
				ExpressName:          channelName,
				ProductCode:          deliveryBusiness,
				ProductName:          channelName,
				Price:                preOrderFee,
				ContinuedWeightPerKg: continuedWeightPerKg,
				CalcWeight:           calcWeight,
				Provider:             a.Name(),
				ChannelID:            fmt.Sprintf("%s_%s", a.Name(), channelID),
			}

			// 打印价格信息（调试用）
			fmt.Printf("价格信息: 快递=%s, 渠道=%s, 总价=%.2f, 续重=%.2f/kg\n",
				price.ExpressCode, price.ExpressName, price.Price, price.ContinuedWeightPerKg)

			prices = append(prices, price)
		}
	}

	return prices, nil
}

// 转换快递公司代码为易达格式
func (a *YidaAdapter) convertExpressType(expressType string) string {
	ctx := context.Background()

	// 使用数据库映射服务获取快递公司代码映射
	if a.mappingService != nil {
		if mappedCode, err := a.mappingService.GetProviderCompanyCode(ctx, expressType, "yida"); err == nil {
			a.logger.Debug("快递代码映射成功",
				zap.String("provider", "yida"),
				zap.String("standard_code", expressType),
				zap.String("provider_code", mappedCode))
			return mappedCode
		} else {
			// 🔧 修复：映射失败时使用静态回退映射，而不是返回空字符串
			a.logger.Debug("易达供应商快递代码映射失败，使用静态回退映射",
				zap.String("standard_code", expressType),
				zap.String("provider", "yida"),
				zap.Error(err))

			// 静态回退映射表（来自配置文件备份）
			fallbackMapping := map[string]string{
				"ZTO": "ZTO",     // 中通
				"STO": "STO-INT", // 申通
				"YTO": "YTO",     // 圆通
				"YD":  "YUND",    // 韵达
				"JD":  "JD",      // 京东
				"JT":  "JT",      // 极兔
				"DBL": "DOP",     // 德邦快递
			}

			if mappedCode, exists := fallbackMapping[expressType]; exists {
				a.logger.Debug("使用静态回退映射",
					zap.String("standard_code", expressType),
					zap.String("mapped_code", mappedCode),
					zap.String("provider", "yida"))
				return mappedCode
			}

			// 如果静态映射也没有，返回原始代码
			a.logger.Warn("静态回退映射也未找到，使用原始代码",
				zap.String("express_code", expressType))
			return expressType
		}
	}

	// 如果映射服务不可用，使用静态映射
	a.logger.Warn("映射服务不可用，使用静态快递代码映射",
		zap.String("express_code", expressType))

	// 静态回退映射表
	fallbackMapping := map[string]string{
		"ZTO": "ZTO",     // 中通
		"STO": "STO-INT", // 申通
		"YTO": "YTO",     // 圆通
		"YD":  "YUND",    // 韵达
		"JD":  "JD",      // 京东
		"JT":  "JT",      // 极兔
		"DBL": "DOP",     // 德邦快递
	}

	if mappedCode, exists := fallbackMapping[expressType]; exists {
		return mappedCode
	}

	// 最后的回退：返回原始代码
	return expressType
}

// convertProviderCodeToStandard 将易达供应商代码转换为标准代码
func (a *YidaAdapter) convertProviderCodeToStandard(providerCode string) string {
	ctx := context.Background()

	// 使用数据库映射服务进行反向查询
	if a.mappingService != nil {
		if standardCode, err := a.mappingService.GetStandardCompanyCode(ctx, providerCode, "yida"); err == nil {
			a.logger.Debug("易达供应商代码反向映射成功",
				zap.String("provider", "yida"),
				zap.String("provider_code", providerCode),
				zap.String("standard_code", standardCode))
			return standardCode
		} else {
			a.logger.Debug("易达供应商代码反向映射失败，使用静态回退映射",
				zap.String("provider_code", providerCode),
				zap.String("provider", "yida"),
				zap.Error(err))
		}
	}

	// 静态回退映射表（供应商代码 → 标准代码）
	fallbackMapping := map[string]string{
		"ZTO":     "ZTO", // 中通
		"STO-INT": "STO", // 申通
		"YTO":     "YTO", // 圆通
		"YUND":    "YD",  // 韵达
		"JD":      "JD",  // 京东
		"JT":      "JT",  // 极兔
		"DOP":     "DBL", // 德邦快递
	}

	if standardCode, exists := fallbackMapping[providerCode]; exists {
		a.logger.Debug("使用静态回退映射进行反向转换",
			zap.String("provider_code", providerCode),
			zap.String("standard_code", standardCode),
			zap.String("provider", "yida"))
		return standardCode
	}

	// 如果静态映射也没有，返回原始代码
	a.logger.Warn("静态回退映射也未找到反向映射，使用原始代码",
		zap.String("provider_code", providerCode))
	return providerCode
}

// CreateOrder 创建订单
func (a *YidaAdapter) CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResult, error) {
	// 🚀 新增：记录详细的请求信息
	a.logger.Info("易达创建订单开始",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("express_type", req.ExpressType),
		zap.String("product_type", req.ProductType),
		zap.String("channel_id", req.ChannelID),
		zap.String("provider", "yida"))

	// 🔥 修复：强制使用查价时选择的渠道信息，确保价格一致性
	if req.ChannelID == "" {
		err := fmt.Errorf("易达供应商必须提供渠道ID，请先查价获取正确的渠道信息")
		a.logger.Error("易达创建订单失败：缺少渠道ID",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Error(err))
		return nil, err
	}

	if req.ProductType == "" {
		err := fmt.Errorf("易达供应商必须提供产品类型，请先查价获取正确的产品信息")
		a.logger.Error("易达创建订单失败：缺少产品类型",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Error(err))
		return nil, err
	}

	// 从渠道ID中提取渠道代码
	channelCode := strings.TrimPrefix(req.ChannelID, a.Name()+"_")
	if channelCode == "" || channelCode == req.ChannelID {
		err := fmt.Errorf("无效的渠道ID格式: %s，期望格式: yida_xxx", req.ChannelID)
		a.logger.Error("易达创建订单失败：无效渠道ID格式",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("channel_id", req.ChannelID),
			zap.Error(err))
		return nil, err
	}

	fmt.Printf("[易达API] 🔗 使用查价时选择的渠道: %s -> %s, 产品类型: %s\n",
		req.ChannelID, channelCode, req.ProductType)

	// 🔥 修复：综合处理Mobile和Tel字段，符合易达API要求
	senderTel, senderMobile := a.formatPhoneForYidaComprehensive(req.Sender.Mobile, req.Sender.Tel)
	receiverTel, receiverMobile := a.formatPhoneForYidaComprehensive(req.Receiver.Mobile, req.Receiver.Tel)

	// 构建业务参数 - 严格按照易达API文档要求
	businessParams := map[string]interface{}{
		// 基本信息 - 必传字段
		"thirdNo":          req.PlatformOrderNo,                   // 🔥 修改：使用平台订单号而不是客户订单号
		"deliveryType":     a.convertExpressType(req.ExpressType), // 快递公司
		"customerType":     a.getCustomerType(),                   // 寄件通道类型
		"deliveryBusiness": req.ProductType,                       // 产品类型

		// 🔥 强制使用查价时选择的渠道ID
		"channelId": channelCode,

		// 发件人信息 - 必传字段
		"senderProvince": req.Sender.Province,
		"senderCity":     req.Sender.City,
		"senderDistrict": req.Sender.District,
		"senderName":     req.Sender.Name,
		"senderAddress":  req.Sender.Address,

		// 收件人信息 - 必传字段
		"receiveProvince": req.Receiver.Province,
		"receiveCity":     req.Receiver.City,
		"receiveDistrict": req.Receiver.District,
		"receiveName":     req.Receiver.Name,
		"receiveAddress":  req.Receiver.Address,

		// 包裹信息 - 必传字段
		"goods":        a.getValidGoodsName(req.Package.GoodsName), // 托寄物名称
		"packageCount": req.Package.Quantity,                       // 包裹数
		"weight":       a.formatWeightForYida(req.Package.Weight),  // 重量(kg) - 格式化处理

		// 支付方式 - 根据易达API文档，创建订单时不能使用ALL
		"onlinePay": a.getOnlinePay(), // 线上支付
	}

	// 🔥 修复：根据电话类型只传递对应字段
	if senderMobile != "" {
		businessParams["senderMobile"] = senderMobile
	} else if senderTel != "" {
		businessParams["senderTel"] = senderTel
	}
	if receiverMobile != "" {
		businessParams["receiveMobile"] = receiverMobile
	} else if receiverTel != "" {
		businessParams["receiveTel"] = receiverTel
	}

	// 添加支付方式
	if req.PayMethod > 0 {
		businessParams["payMethod"] = req.PayMethod
	}

	// 添加保价金额
	if req.Package.InsureValue > 0 {
		businessParams["guaranteeValueAmount"] = req.Package.InsureValue
	}

	// 添加备注
	if req.Package.Remark != "" {
		businessParams["remark"] = req.Package.Remark
	}

	// 🔥 企业级修复：优先使用长宽高，如果没有则从体积计算
	if req.Package.Length > 0 && req.Package.Width > 0 && req.Package.Height > 0 {
		// 直接使用长宽高（单位：cm）- 格式化为2位小数
		businessParams["vloumLong"] = a.formatWeightForYida(req.Package.Length)
		businessParams["vloumWidth"] = a.formatWeightForYida(req.Package.Width)
		businessParams["vloumHeight"] = a.formatWeightForYida(req.Package.Height)
	} else if req.Package.Volume > 0 {
		// 如果没有长宽高，从体积计算（假设是正方体）
		// 体积单位转换：m³ → cm³
		volumeCm3 := req.Package.Volume * 1000000
		dimension := math.Pow(volumeCm3, 1.0/3.0)
		businessParams["vloumLong"] = a.formatWeightForYida(dimension)
		businessParams["vloumWidth"] = a.formatWeightForYida(dimension)
		businessParams["vloumHeight"] = a.formatWeightForYida(dimension)
	}

	// 添加预约取件时间
	if req.Pickup.StartTime != "" && req.Pickup.EndTime != "" {
		// 🚀 修复：转换为易达API要求的时间格式 yyyy-MM-dd HH:mm:ss
		businessParams["pickUpStartTime"] = a.formatPickupTime(req.Pickup.StartTime)
		businessParams["pickUpEndTime"] = a.formatPickupTime(req.Pickup.EndTime)

		a.logger.Info("添加预约取件时间",
			zap.String("原始开始时间", req.Pickup.StartTime),
			zap.String("转换后开始时间", businessParams["pickUpStartTime"].(string)),
			zap.String("原始结束时间", req.Pickup.EndTime),
			zap.String("转换后结束时间", businessParams["pickUpEndTime"].(string)))
	}

	// 添加增值服务
	if len(req.AddServices) > 0 {
		var orderIncrements []map[string]string
		for _, service := range req.AddServices {
			orderIncrements = append(orderIncrements, map[string]string{
				"type":  service.Type,
				"value": service.Value,
			})
		}
		businessParams["orderIncrements"] = orderIncrements
	}

	// 🔥 删除：易达供应商不支持回调URL参数，包含回调URL可能导致API失败

	// 🚀 新增：记录API调用开始
	a.logger.Info("易达API调用开始",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("method", "order_create"),
		zap.String("channel_code", channelCode),
		zap.Any("request_data", businessParams))

	// 调用易达 API
	configManager := config.GetProviderConfigManager()
	createMethod := configManager.GetAPIMethod("yida", "order_create")
	if createMethod == "" {
		createMethod = "SUBMIT_ORDER_V2" // 默认值
	}
	result, err := a.callYidaAPI(ctx, createMethod, businessParams)
	if err != nil {
		// 🚀 新增：记录API调用失败
		a.logger.Error("易达API调用失败",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("method", createMethod),
			zap.Error(err),
			zap.Any("request_data", businessParams))
		return nil, err
	}

	// 🚀 新增：记录API调用成功
	a.logger.Info("易达API调用成功",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("method", createMethod),
		zap.String("response", result))

	// 解析响应
	var response struct {
		Code    int    `json:"code"`
		Message string `json:"msg"`
		Data    struct {
			OrderNo          string      `json:"orderNo"`
			TrackingNo       string      `json:"deliveryId"`
			PickupCode       string      `json:"pickupCode"`
			ChildTrackingNos []string    `json:"childDeliveryIds"`
			PreOrderFee      interface{} `json:"preOrderFee"` // 使用interface{}类型，因为可能是字符串或浮点数
			PrintInfo        interface{} `json:"printInfo"`
			PollToken        string      `json:"pollToken"`
		} `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		// 🚀 新增：记录响应解析失败
		a.logger.Error("易达响应解析失败",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("response", result),
			zap.Error(err))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 🚀 新增：记录响应解析成功和详细数据
	a.logger.Info("易达响应解析成功",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.Int("code", response.Code),
		zap.String("message", response.Message),
		zap.String("returned_order_no", response.Data.OrderNo),
		zap.String("tracking_no", response.Data.TrackingNo),
		zap.String("pickup_code", response.Data.PickupCode))

	if response.Code != 200 {
		// 🔧 特殊处理：识别"供应商不支持"的情况
		if strings.Contains(response.Message, "不支持") ||
			strings.Contains(response.Message, "暂未开放") ||
			strings.Contains(response.Message, "该区域") ||
			strings.Contains(response.Message, "线路") {
			a.logger.Debug("易达供应商不支持该快递公司或线路",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("express_type", req.ExpressType),
				zap.Int("code", response.Code),
				zap.String("message", response.Message))
			return nil, fmt.Errorf("供应商不支持该快递公司: %s", response.Message)
		}

		// 🚀 详细记录业务失败详情（便于问题排查）
		a.logger.Error("易达业务失败 - 完整失败信息",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.String("express_type", req.ExpressType),
			zap.String("channel_id", req.ChannelID),
			zap.String("product_type", req.ProductType),
			zap.String("error_type", "BUSINESS_FAILED"),
			zap.String("message", response.Message),
			zap.Int("code", response.Code),
			zap.String("sender_info", fmt.Sprintf("%s-%s-%s", req.Sender.Province, req.Sender.City, req.Sender.District)),
			zap.String("receiver_info", fmt.Sprintf("%s-%s-%s", req.Receiver.Province, req.Receiver.City, req.Receiver.District)),
			zap.Float64("package_weight", req.Package.Weight),
			zap.Float64("package_volume", req.Package.Volume),
			zap.String("full_response", result))
		return nil, fmt.Errorf("%s", response.Message)
	}

	// 处理价格字段，可能是字符串或浮点数
	var price float64
	switch v := response.Data.PreOrderFee.(type) {
	case string:
		price, _ = strconv.ParseFloat(v, 64)
	case float64:
		price = v
	case json.Number:
		price, _ = v.Float64()
	}

	// 转换为标准订单结果
	orderResult := &model.OrderResult{
		OrderNo:          response.Data.OrderNo,
		TrackingNo:       response.Data.TrackingNo,
		PickupCode:       response.Data.PickupCode,
		ChildTrackingNos: response.Data.ChildTrackingNos,
		Price:            price,
		PrintData:        response.Data.PrintInfo,
		PollToken:        response.Data.PollToken,
	}

	return orderResult, nil
}

// CancelOrder 取消订单
func (a *YidaAdapter) CancelOrder(ctx context.Context, taskId string, orderNo string, reason string) error {
	// 构建业务参数
	businessParams := map[string]interface{}{
		"orderNo": orderNo,
		"reason":  reason,
	}

	// 调用易达 API
	configManager := config.GetProviderConfigManager()
	cancelMethod := configManager.GetAPIMethod("yida", "order_cancel")
	if cancelMethod == "" {
		cancelMethod = "CANCEL_ORDER" // 默认值
	}
	result, err := a.callYidaAPI(ctx, cancelMethod, businessParams)
	if err != nil {
		return err
	}

	// 解析响应
	var response struct {
		Code    int    `json:"code"`
		Message string `json:"msg"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 200 {
		return fmt.Errorf("取消订单失败: %s", response.Message)
	}

	return nil
}

// QueryOrder 查询订单
// 注意：根据易达API文档和测试结果，易达不支持直接查询订单详情功能
// 此方法通过查询物流轨迹来获取部分订单信息，但无法获取完整的订单详情
// 建议用户保存下单时返回的订单信息，或使用物流轨迹查询功能获取物流状态
func (a *YidaAdapter) QueryOrder(ctx context.Context, orderNo string, trackingNo string) (*model.OrderInfo, error) {
	// 返回易达不支持查询订单的说明
	return nil, fmt.Errorf("易达API不支持直接查询订单详情功能，请使用物流轨迹查询功能获取物流状态，或保存下单时返回的订单信息")

	/* 以下代码保留但不执行，仅作为参考
	// 构建业务参数
	businessParams := map[string]interface{}{}

	// 优先使用订单号
	if orderNo != "" {
		businessParams["orderNo"] = orderNo
	} else if trackingNo != "" {
		businessParams["deliveryId"] = trackingNo
	} else {
		return nil, fmt.Errorf("订单号和运单号不能同时为空")
	}

	// 调用易达 API
	result, err := a.callYidaAPI(ctx, "QUERY_ORDER_INFO_V2", businessParams)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var response struct {
		Code    int    `json:"code"`
		Message string `json:"msg"`
		Data    struct {
			OrderNo         string    `json:"order_no"`
			TrackingNo      string    `json:"tracking_no"`
			ExpressType     string    `json:"express_type"`
			Status          string    `json:"status"`
			StatusDesc      string    `json:"status_desc"`
			Weight          float64   `json:"weight"`
			Price           float64   `json:"price"`
			CreatedAt       string    `json:"created_at"`
			CourierName     string    `json:"courier_name"`
			CourierPhone    string    `json:"courier_phone"`
			CourierCode     string    `json:"courier_code"`
			StationName     string    `json:"station_name"`
			StationCode     string    `json:"station_code"`
			StationAddress  string    `json:"station_address"`
			StationPhone    string    `json:"station_phone"`
			PickupCode      string    `json:"pickup_code"`
		} `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if response.Code != 200 {
		return nil, fmt.Errorf("查询订单失败: %s", response.Message)
	}

	// 解析创建时间
	createTime, err := time.Parse("2006-01-02T15:04:05Z", response.Data.CreatedAt)
	if err != nil {
		// 尝试其他时间格式
		createTime, err = time.Parse("2006-01-02 15:04:05", response.Data.CreatedAt)
		if err != nil {
			createTime = util.NowBeijing()
		}
	}

	// 转换为标准订单信息
	orderInfo := &model.OrderInfo{
		OrderNo:        response.Data.OrderNo,
		TrackingNo:     response.Data.TrackingNo,
		ExpressType:    response.Data.ExpressType,
		Status:         response.Data.Status,
		StatusDesc:     response.Data.StatusDesc,
		Weight:         response.Data.Weight,
		Price:          response.Data.Price,
		CreatedAt:      createTime,
		CourierName:    response.Data.CourierName,
		CourierPhone:   response.Data.CourierPhone,
		CourierCode:    response.Data.CourierCode,
		StationName:    response.Data.StationName,
		StationCode:    response.Data.StationCode,
		StationAddress: response.Data.StationAddress,
		StationPhone:   response.Data.StationPhone,
		PickupCode:     response.Data.PickupCode,
	}

	return orderInfo, nil
	*/
}

// QueryTrack 查询物流轨迹
func (a *YidaAdapter) QueryTrack(ctx context.Context, trackingNo, expressType, phone, pollToken string, from, to string) (*model.TrackInfo, error) {
	// 构建业务参数
	businessParams := map[string]interface{}{}

	// 必须提供运单号
	if trackingNo == "" {
		return nil, fmt.Errorf("运单号不能为空")
	}

	businessParams["deliveryId"] = trackingNo

	// 如果提供了快递公司代码，也加入参数
	if expressType != "" {
		businessParams["deliveryType"] = a.convertExpressType(expressType)
	}

	// 调用易达 API
	configManager := config.GetProviderConfigManager()
	trackMethod := configManager.GetAPIMethod("yida", "track_query")
	if trackMethod == "" {
		trackMethod = "DELIVERY_TRACE" // 默认值
	}
	result, err := a.callYidaAPI(ctx, trackMethod, businessParams)
	if err != nil {
		return nil, err
	}

	// 解析基本响应
	var baseResponse struct {
		Code    int             `json:"code"`
		Message string          `json:"msg"`
		Data    json.RawMessage `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &baseResponse); err != nil {
		return nil, fmt.Errorf("解析基本响应失败: %w", err)
	}

	if baseResponse.Code != 200 {
		return nil, fmt.Errorf("查询物流轨迹失败: %s", baseResponse.Message)
	}

	// 创建标准物流轨迹信息
	trackInfo := &model.TrackInfo{
		OrderNo:     "",
		TrackingNo:  trackingNo,
		ExpressType: expressType,
		State:       "",
		StateDesc:   "",
		IsCheck:     "",
		Tracks:      make([]*model.TrackItem, 0),
	}

	// 检查data是否为null
	if string(baseResponse.Data) == "null" {
		// 返回空的轨迹信息
		return trackInfo, nil
	}

	// 尝试解析data为数组
	var trackArray []struct {
		Time string `json:"time"`
		Desc string `json:"desc"`
	}

	if err := json.Unmarshal(baseResponse.Data, &trackArray); err == nil && len(trackArray) > 0 {
		// 成功解析为数组，处理轨迹信息
		for _, item := range trackArray {
			// 🔥 修复：使用北京时区解析时间
			var t time.Time
			if item.Time != "" {
				beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
				// 优先尝试北京时区解析
				parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", item.Time, beijingLocation)
				if err != nil {
					// 尝试其他时间格式
					t, err = time.Parse("2006-01-02T15:04:05Z", item.Time)
					if err != nil {
						t = util.NowBeijing()
					} else {
						// 转换为北京时间
						t = util.ToBeijing(t)
					}
				} else {
					t = parsedTime
				}
			} else {
				t = util.NowBeijing()
			}

			trackItem := &model.TrackItem{
				Context:    item.Desc,
				Time:       t,
				Status:     "",
				StatusCode: "",
				AreaCode:   "",
				AreaName:   "",
				Location:   "",
			}

			trackInfo.Tracks = append(trackInfo.Tracks, trackItem)
		}

		// 根据轨迹信息设置状态
		if len(trackInfo.Tracks) > 0 {
			// 使用最新的轨迹项来确定状态
			latestTrack := trackInfo.Tracks[0]

			// 简单的状态判断逻辑
			if strings.Contains(latestTrack.Context, "签收") {
				trackInfo.State = "3" // 已签收
				trackInfo.StateDesc = "已签收"
			} else if strings.Contains(latestTrack.Context, "派送") {
				trackInfo.State = "2" // 派送中
				trackInfo.StateDesc = "派送中"
			} else if strings.Contains(latestTrack.Context, "运输") {
				trackInfo.State = "1" // 运输中
				trackInfo.StateDesc = "运输中"
			} else if strings.Contains(latestTrack.Context, "揽收") {
				trackInfo.State = "0" // 已揽收
				trackInfo.StateDesc = "已揽收"
			} else {
				trackInfo.State = "0" // 默认为已揽收
				trackInfo.StateDesc = "已揽收"
			}
		}

		return trackInfo, nil
	}

	// 如果不是数组，尝试解析为对象
	var trackObject struct {
		TrackingNo  string `json:"tracking_no"`
		ExpressType string `json:"express_type"`
		State       string `json:"state"`
		IsCheck     string `json:"is_check"`
		Tracks      []struct {
			Context    string `json:"context"`
			Time       string `json:"time"`
			Status     string `json:"status"`
			StatusCode string `json:"status_code"`
			AreaCode   string `json:"area_code"`
			AreaName   string `json:"area_name"`
			Location   string `json:"location"`
		} `json:"tracks"`
	}

	if err := json.Unmarshal(baseResponse.Data, &trackObject); err == nil {
		// 成功解析为对象，更新轨迹信息
		trackInfo.TrackingNo = trackObject.TrackingNo
		trackInfo.ExpressType = trackObject.ExpressType
		trackInfo.State = trackObject.State
		trackInfo.StateDesc = model.StateMap[trackObject.State]
		trackInfo.IsCheck = trackObject.IsCheck

		// 转换物流轨迹项
		for _, item := range trackObject.Tracks {
			// 🔥 修复：使用北京时区解析时间
			var t time.Time
			if item.Time != "" {
				beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
				// 优先尝试北京时区解析
				parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", item.Time, beijingLocation)
				if err != nil {
					// 尝试其他时间格式
					t, err = time.Parse("2006-01-02T15:04:05Z", item.Time)
					if err != nil {
						t = util.NowBeijing()
					} else {
						// 转换为北京时间
						t = util.ToBeijing(t)
					}
				} else {
					t = parsedTime
				}
			} else {
				t = util.NowBeijing()
			}

			trackItem := &model.TrackItem{
				Context:    item.Context,
				Time:       t,
				Status:     item.Status,
				StatusCode: item.StatusCode,
				AreaCode:   item.AreaCode,
				AreaName:   item.AreaName,
				Location:   item.Location,
			}

			trackInfo.Tracks = append(trackInfo.Tracks, trackItem)
		}

		return trackInfo, nil
	}

	// 如果都解析失败，返回空的轨迹信息
	fmt.Printf("无法解析物流轨迹数据: %s\n", string(baseResponse.Data))

	return trackInfo, nil
}

// 调用易达 API
func (a *YidaAdapter) callYidaAPI(ctx context.Context, apiMethod string, businessParams map[string]interface{}) (string, error) {
	// 获取当前时间戳（毫秒）
	timestamp := fmt.Sprintf("%d", util.NowBeijing().UnixNano()/1e6)

	// 构建请求参数 - 严格按照易达API文档要求
	requestParams := map[string]interface{}{
		"username":       a.config.AppKey,
		"timestamp":      timestamp,
		"apiMethod":      apiMethod,
		"businessParams": businessParams,
	}

	// 生成签名
	sign := a.generateSign(a.config.AppKey, timestamp, a.config.AppSecret)
	requestParams["sign"] = sign

	// 将参数转换为JSON字符串
	paramsJSON, err := json.Marshal(requestParams)
	if err != nil {
		return "", fmt.Errorf("参数序列化失败: %w", err)
	}

	// 打印请求参数（调试用）
	fmt.Printf("易达API请求参数: %s\n", string(paramsJSON))

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, a.config.BaseURL, strings.NewReader(string(paramsJSON)))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := a.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应体失败: %w", err)
	}

	// 打印响应体（调试用）
	fmt.Printf("易达API响应: %s\n", string(respBody))

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return "", fmt.Errorf("解析响应失败: %w", err)
	}

	// 将响应转换为JSON字符串
	resultJSON, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("响应序列化失败: %w", err)
	}

	return string(resultJSON), nil
}

// 获取客户类型
func (a *YidaAdapter) getCustomerType() string {
	// 从配置获取客户类型
	configManager := config.GetProviderConfigManager()
	customerType := configManager.GetParameter("yida", "customer_type")

	if customerType != nil {
		if str, ok := customerType.(string); ok {
			return str
		}
	}
	return "kd" // 默认值
}

// 获取在线支付设置
func (a *YidaAdapter) getOnlinePay() string {
	// 从配置获取在线支付设置
	configManager := config.GetProviderConfigManager()
	onlinePay := configManager.GetParameter("yida", "online_pay")

	if onlinePay != nil {
		if str, ok := onlinePay.(string); ok {
			return str
		}
	}
	return "Y" // 默认值
}

// 生成签名
func (a *YidaAdapter) generateSign(username, timestamp, privateKey string) string {
	// 按照易达的签名规则生成签名
	// 1. 用户名username, 时间戳（毫秒字符串）timestamp，私钥privateKey字段根据key值排序生成JSON字符串
	signMap := map[string]string{
		"username":   username,
		"timestamp":  timestamp,
		"privateKey": privateKey,
	}

	// 按照key排序
	var keys []string
	for k := range signMap {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 构建JSON对象
	jsonObj := make(map[string]string)
	for _, k := range keys {
		jsonObj[k] = signMap[k]
	}

	// 转换为JSON字符串
	jsonBytes, _ := json.Marshal(jsonObj)
	jsonStr := string(jsonBytes)

	// 打印签名字符串（调试用）
	fmt.Printf("易达API签名字符串: %s\n", jsonStr)

	// 2. 将生成的字符串按照MD5加密之后，转换成大写
	hash := md5.Sum([]byte(jsonStr))
	sign := strings.ToUpper(hex.EncodeToString(hash[:]))

	// 打印签名结果（调试用）
	fmt.Printf("易达API签名结果: %s\n", sign)

	return sign
}

// getValidAddress 获取有效的地址信息
// 易达API要求地址信息不能为空或只是占位符
func (a *YidaAdapter) getValidAddress(address, defaultDesc string) string {
	// 如果地址为空或是常见的占位符，使用更真实的默认地址
	if address == "" || address == "某地" || address == "详细地址" || len(strings.TrimSpace(address)) < 3 {
		// 从配置获取默认地址
		configManager := config.GetProviderConfigManager()
		if defaultAddressParam := configManager.GetParameter("yida", "default_address"); defaultAddressParam != nil {
			if defaultAddress, ok := defaultAddressParam.(string); ok && defaultAddress != "" {
				return defaultAddress
			}
		}
		// 使用更真实的默认地址
		return a.getRealisticAddress(defaultDesc)
	}

	// 检查是否包含占位符文本
	if strings.Contains(address, "某某") {
		// 从配置获取默认地址
		configManager := config.GetProviderConfigManager()
		if defaultAddressParam := configManager.GetParameter("yida", "default_address"); defaultAddressParam != nil {
			if defaultAddress, ok := defaultAddressParam.(string); ok && defaultAddress != "" {
				return defaultAddress
			}
		}
		// 使用更真实的默认地址
		return a.getRealisticAddress(defaultDesc)
	}

	// 确保地址足够详细（易达API要求）
	if len(strings.TrimSpace(address)) < 10 {
		// 从配置获取默认地址
		configManager := config.GetProviderConfigManager()
		if defaultAddressParam := configManager.GetParameter("yida", "default_address"); defaultAddressParam != nil {
			if defaultAddress, ok := defaultAddressParam.(string); ok && defaultAddress != "" {
				return defaultAddress
			}
		}
		// 补充地址信息使其更详细
		return a.getRealisticAddress(address + "街道")
	}

	return address
}

// getRealisticAddress 获取更真实的地址信息
func (a *YidaAdapter) getRealisticAddress(prefix string) string {
	// 根据前缀生成更真实的地址
	if strings.Contains(prefix, "发件") {
		return "西湖区文三路508号天苑大厦10楼1001室"
	} else if strings.Contains(prefix, "收件") {
		return "锦江区春熙路99号银石广场15楼1502室"
	}

	// 默认地址
	return "市中心商业区中央大街188号国际大厦8楼808室"
}

// getValidDistrict 获取有效的区县信息
// 易达API要求区县信息不能为空
func (a *YidaAdapter) getValidDistrict(district string) string {
	// 如果区县为空或包含占位符，使用更真实的默认区县
	if district == "" || len(strings.TrimSpace(district)) == 0 || strings.Contains(district, "某某") {
		return "市辖区"
	}
	return district
}

// calculateChargedWeightFromPackage 从包裹信息计算计费重量（优先使用长宽高）
func (a *YidaAdapter) calculateChargedWeightFromPackage(expressCode string, pkg model.PackageInfo) float64 {
	// 🔥 企业级修复：使用统一的体积重量计算器
	return a.calculateChargedWeightEnterprise(expressCode, pkg)
}

// calculateChargedWeightEnterprise 企业级体积重量计算（从数据库获取配置）
func (a *YidaAdapter) calculateChargedWeightEnterprise(expressCode string, pkg model.PackageInfo) float64 {
	// 先计算体积
	var volumeCm3 float64
	if pkg.Length > 0 && pkg.Width > 0 && pkg.Height > 0 {
		// 长宽高单位是cm，直接计算体积（cm³）
		volumeCm3 = pkg.Length * pkg.Width * pkg.Height
	} else if pkg.Volume > 0 {
		// 如果没有长宽高，从m³转换为cm³
		volumeCm3 = pkg.Volume * 1000000
	}

	a.logger.Debug("易达企业级计算器输入参数",
		zap.String("express_code", expressCode),
		zap.Float64("actual_weight", pkg.Weight),
		zap.Float64("length", pkg.Length),
		zap.Float64("width", pkg.Width),
		zap.Float64("height", pkg.Height),
		zap.Float64("volume_cm3", volumeCm3))

	// 如果没有体积信息，直接返回实际重量（向上取整）
	if volumeCm3 <= 0 {
		result := math.Ceil(pkg.Weight)
		a.logger.Debug("易达企业级计算器：无体积信息，使用实际重量",
			zap.Float64("result", result))
		return result
	}

	// 从数据库获取快递公司配置
	if a.expressCompanyRepo == nil {
		a.logger.Error("ExpressCompanyRepository 未初始化",
			zap.String("express_code", expressCode))
		return math.Ceil(pkg.Weight)
	}

	company, err := a.expressCompanyRepo.GetCompanyByCode(expressCode)
	if err != nil {
		a.logger.Error("易达企业级计算器：获取快递公司配置失败",
			zap.String("express_code", expressCode),
			zap.Error(err))
		panic(fmt.Sprintf("快递公司 %s 配置获取失败: %v。请在数据库 express_companies 表中配置正确的快递公司信息", expressCode, err))
	}

	if !company.IsActive {
		a.logger.Error("易达企业级计算器：快递公司已禁用",
			zap.String("express_code", expressCode))
		panic(fmt.Sprintf("快递公司 %s 已禁用。请在数据库中启用该快递公司", expressCode))
	}

	if company.VolumeWeightRatio <= 0 {
		a.logger.Error("易达企业级计算器：快递公司抛比配置无效",
			zap.String("express_code", expressCode),
			zap.Int("volume_ratio", company.VolumeWeightRatio))
		panic(fmt.Sprintf("快递公司 %s 抛比配置无效: %d。请在数据库 express_companies 表中配置正确的 volume_weight_ratio 值", expressCode, company.VolumeWeightRatio))
	}

	volumeRatio := company.VolumeWeightRatio
	companyName := company.Name
	a.logger.Debug("易达企业级计算器：从数据库获取抛比配置",
		zap.String("express_code", expressCode),
		zap.String("company_name", companyName),
		zap.Int("volume_ratio", volumeRatio))

	// 计算体积重量
	volumeWeight := volumeCm3 / float64(volumeRatio)

	// 取实际重量和体积重量的较大值
	chargedWeight := math.Max(pkg.Weight, volumeWeight)

	// 向上取整到最近的整数（快递行业标准）
	result := math.Ceil(chargedWeight)

	a.logger.Debug("易达企业级计算器计算结果",
		zap.String("company_name", companyName),
		zap.Int("volume_ratio", volumeRatio),
		zap.Float64("volume_weight", volumeWeight),
		zap.Float64("actual_weight", pkg.Weight),
		zap.Float64("charged_weight", result))

	return result
}

// formatWeightForYida 格式化数值参数以符合易达API要求
// 易达API要求数值格式为最多2位小数，避免精度过高导致"重量格式不正确"等错误
func (a *YidaAdapter) formatWeightForYida(value float64) float64 {
	// 保留2位小数，避免精度过高导致API拒绝
	return math.Round(value*100) / 100
}

// getNameForPriceQuery 确保查价请求中的姓名字段有效，不为空且长度<=30
func (a *YidaAdapter) getNameForPriceQuery(name, defaultDesc string) string {
	trimmed := strings.TrimSpace(name)
	if trimmed == "" {
		return defaultDesc
	}
	if len([]rune(trimmed)) > 30 {
		return string([]rune(trimmed)[:30])
	}
	return trimmed
}

// getMobileForPriceQuery 确保手机号字段有效；若缺失返回占位符以避免易达校验失败
func (a *YidaAdapter) getMobileForPriceQuery(mobile string) string {
	trimmed := strings.TrimSpace(mobile)
	if trimmed == "" {
		return "13800000000" // 默认占位手机号，可在配置里替换
	}
	return trimmed
}

// 🚀 formatPickupTime 将ISO 8601时间格式转换为易达API要求的格式
func (a *YidaAdapter) formatPickupTime(isoTime string) string {
	// 解析ISO 8601格式时间
	t, err := time.Parse(time.RFC3339, isoTime)
	if err != nil {
		a.logger.Warn("解析预约时间失败，使用原始格式",
			zap.String("iso_time", isoTime),
			zap.Error(err))
		return isoTime
	}

	// 🔥 修复：转换为北京时间，避免直接使用UTC时间
	beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
	beijingTime := t.In(beijingLocation)

	// 转换为易达API要求的格式：yyyy-MM-dd HH:mm:ss
	formatted := beijingTime.Format("2006-01-02 15:04:05")

	a.logger.Info("易达时间转换详情",
		zap.String("原始时间", isoTime),
		zap.String("UTC时间", t.Format("2006-01-02 15:04:05")),
		zap.String("北京时间", beijingTime.Format("2006-01-02 15:04:05")),
		zap.String("格式化结果", formatted))

	return formatted
}

// getValidGoodsName 获取有效的托寄物名称
// 易达API要求托寄物名称不能为空且不能超过100个字符
func (a *YidaAdapter) getValidGoodsName(goodsName string) string {
	trimmed := strings.TrimSpace(goodsName)

	// 如果为空，使用默认值
	if trimmed == "" {
		return "物品"
	}

	// 如果超过100个字符，截取前100个字符
	if len([]rune(trimmed)) > 100 {
		return string([]rune(trimmed)[:100])
	}

	return trimmed
}
