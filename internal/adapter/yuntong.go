package adapter

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// YuntongAdapter 云通适配器
type YuntongAdapter struct {
	config             YuntongConfig
	client             *http.Client
	logger             *zap.Logger
	mappingService     express.ExpressMappingService
	expressCompanyRepo express.ExpressCompanyRepository
}

// YuntongConfig 云通配置
type YuntongConfig struct {
	EBusinessID string `json:"ebusiness_id"` // 商户编号
	ApiKey      string `json:"api_key"`      // ApiKey
	BaseURL     string `json:"base_url"`     // 接口地址
	Timeout     int    `json:"timeout"`      // 超时时间(秒)
}

// NewYuntongAdapter 创建云通适配器
func NewYuntongAdapter(config YuntongConfig, expressCompanyRepo express.ExpressCompanyRepository) *YuntongAdapter {
	// 创建日志记录器
	logger, _ := zap.NewProduction()

	// 优化HTTP客户端配置 - 提升性能
	transport := &http.Transport{
		MaxIdleConns:          200,              // 增加最大空闲连接数
		MaxIdleConnsPerHost:   50,               // 增加每个主机最大空闲连接数
		IdleConnTimeout:       60 * time.Second, // 减少空闲连接超时
		DisableCompression:    false,            // 启用压缩
		ForceAttemptHTTP2:     true,             // 强制尝试HTTP/2
		ResponseHeaderTimeout: 8 * time.Second,  // 响应头超时
		ExpectContinueTimeout: 1 * time.Second,  // Expect: 100-continue超时
	}

	return &YuntongAdapter{
		config:             config,
		expressCompanyRepo: expressCompanyRepo,
		client: &http.Client{
			Timeout:   time.Duration(config.Timeout) * time.Second,
			Transport: transport,
		},
		logger: logger,
		// mappingService 将在初始化后通过 SetMappingService 设置
	}
}

// SetMappingService 设置映射服务
func (a *YuntongAdapter) SetMappingService(mappingService express.ExpressMappingService) {
	a.mappingService = mappingService
}

// Name 获取供应商名称
func (a *YuntongAdapter) Name() string {
	return model.ExpressYuntong
}

// formatPhoneForYuntongComprehensive 综合处理Mobile和Tel字段，符合云通API要求
func (a *YuntongAdapter) formatPhoneForYuntongComprehensive(mobile string, tel string) (resultTel string, resultMobile string) {
	// 优先使用明确指定的tel字段（固定电话）
	if tel != "" {
		a.logger.Info("🔥 云通电话格式化：使用专用Tel字段",
			zap.String("tel", tel))
		return tel, ""
	}

	// 优先使用明确指定的mobile字段（手机号）
	if mobile != "" {
		// 对mobile字段进行智能识别
		telResult, mobileResult := a.formatPhoneForYuntong(mobile)
		if mobileResult != "" {
			a.logger.Info("🔥 云通电话格式化：Mobile字段识别为手机号",
				zap.String("mobile", mobile),
				zap.String("result", mobileResult))
			return "", mobileResult
		} else {
			a.logger.Info("🔥 云通电话格式化：Mobile字段识别为固定电话",
				zap.String("mobile", mobile),
				zap.String("result", telResult))
			return telResult, ""
		}
	}

	// 都为空的情况
	return "", ""
}

// formatPhoneForYuntong 智能格式化电话号码，符合云通API要求（使用生产级验证器）
// 云通API要求：Tel（固定电话，区号+尾数）和Mobile（11位手机号，1开头）必填一个
func (a *YuntongAdapter) formatPhoneForYuntong(phone string) (tel string, mobile string) {
	if phone == "" {
		return "", ""
	}

	// 使用生产级电话验证器
	validator := util.GetPhoneValidator()
	result := validator.ValidatePhone(phone)

	if !result.IsValid {
		a.logger.Warn("云通电话格式化：电话号码格式无效",
			zap.String("original", phone),
			zap.String("error_code", result.ErrorCode))
		return phone, "" // 格式无效时作为固定电话处理
	}

	// 根据验证结果进行格式化
	switch result.Type {
	case util.PhoneTypeMobile:
		a.logger.Debug("云通电话格式化：识别为手机号",
			zap.String("original", phone),
			zap.String("cleaned", result.Cleaned))
		return "", result.Cleaned
	case util.PhoneTypeLandline:
		a.logger.Debug("云通电话格式化：识别为固定电话",
			zap.String("original", phone),
			zap.String("cleaned", result.Cleaned))
		return result.Cleaned, ""
	default:
		a.logger.Warn("云通电话格式化：未知电话类型",
			zap.String("original", phone))
		return phone, ""
	}
}

// 转换快递公司代码
func (a *YuntongAdapter) convertExpressCode(standardCode string) string {
	ctx := context.Background()

	// 使用数据库映射服务获取快递公司代码映射
	if a.mappingService != nil {
		if mappedCode, err := a.mappingService.GetProviderCompanyCode(ctx, standardCode, "yuntong"); err == nil {
			a.logger.Debug("快递代码映射成功",
				zap.String("provider", "yuntong"),
				zap.String("standard_code", standardCode),
				zap.String("provider_code", mappedCode))
			return mappedCode
		} else {
			// 🔧 修复：映射失败时抛出错误，不使用原代码
			a.logger.Error("云通供应商快递代码映射失败",
				zap.String("standard_code", standardCode),
				zap.String("provider", "yuntong"),
				zap.Error(err))
			// 返回错误而不是继续使用无效代码
			return ""
		}
	}

	// 如果数据库映射失败，返回错误
	a.logger.Error("映射服务不可用，无法转换快递代码",
		zap.String("express_code", standardCode))
	return ""
}

// isExpressSupported 检查云通供应商是否支持指定的快递公司
func (a *YuntongAdapter) isExpressSupported(expressCode string) bool {
	// 云通供应商支持的快递公司列表
	supportedExpressCompanies := map[string]bool{
		"JD":  true, // 京东快递
		"SF":  true, // 顺丰快递
		"EMS": true, // 邮政快递
		"JT":  true, // 极兔快递 (部分线路)
		"YTO": true, // 圆通快递 (部分线路)
		"STO": true, // 申通快递 (部分线路)
		"DBL": true, // 德邦快递 (部分线路)
		// 云通不支持的快递公司
		"ZTO":  false, // 中通快递
		"YD":   false, // 韵达快递
		"JTO":  false, // 京东商城
		"UC":   false, // 优速快递
		"XFWL": false, // 先锋物流
	}

	// 检查快递公司是否在支持列表中
	if supported, exists := supportedExpressCompanies[expressCode]; exists {
		return supported
	}

	// 如果不在支持列表中，默认返回false
	return false
}

// 获取快递产品类型
func (a *YuntongAdapter) getExpressProductType(expressCode string) string {
	// 从配置获取产品类型映射
	configManager := config.GetProviderConfigManager()
	productType := configManager.GetProductType("yuntong", expressCode)

	// 如果找到产品类型，返回它，否则返回空字符串
	return productType
}

// QueryPrice 查询价格
func (a *YuntongAdapter) QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
	// 如果指定了供应商但不是yuntong，则返回空结果
	if req.Provider != "" && req.Provider != model.ExpressYuntong {
		return []model.StandardizedPrice{}, nil
	}

	// 如果没有指定快递公司代码，则返回错误
	// 因为云通不支持批量查询所有快递公司价格
	if req.ExpressType == "" {
		fmt.Printf("[云通API] 必须指定快递公司代码，不支持批量查询所有快递公司价格\n")
		return []model.StandardizedPrice{}, nil
	}

	// 🔧 修复：添加快递公司支持检查，避免无效查询
	if !a.isExpressSupported(req.ExpressType) {
		fmt.Printf("[云通API] 快递公司 %s 不受支持，跳过查询\n", req.ExpressType)
		return []model.StandardizedPrice{}, nil
	}

	// 打印转换前的参数
	fmt.Printf("[云通API] 转换前参数 - ExpressType: %s\n", req.ExpressType)

	// 转换快递公司代码
	yuntongExpressCode := a.convertExpressCode(req.ExpressType)

	// 🔧 修复：如果转换失败（返回空字符串），跳过查询
	if yuntongExpressCode == "" {
		fmt.Printf("[云通API] 快递公司 %s 映射失败，跳过查询\n", req.ExpressType)
		return []model.StandardizedPrice{}, nil
	}

	// 打印转换后的快递公司代码
	fmt.Printf("[云通API] 转换后参数 - ShipperCode: %s\n", yuntongExpressCode)

	// 获取快递产品类型
	productType := a.getExpressProductType(yuntongExpressCode)

	// 打印获取的产品类型
	fmt.Printf("[云通API] 产品类型 - ExpType: %s\n", productType)

	// 确保PayType值符合云通API要求：0-现付，1-到付，2-月结，默认为2
	payType := a.getDefaultPayType() // 从配置获取默认支付类型
	// 强制使用月结方式，忽略请求中的PayMethod
	// if req.PayMethod >= 0 && req.PayMethod <= 2 {
	// 	payType = req.PayMethod
	// }

	// 🔥 企业级修复：云通支持体积参数，优先使用体积重量计算
	// 计算正确的计费重量
	calcWeight := a.calculateChargedWeightFromPackage(req.ExpressType, req.Package)

	// 🔥 修复：综合处理Mobile和Tel字段，符合云通API要求
	senderTel, senderMobile := a.formatPhoneForYuntongComprehensive(req.Sender.Mobile, req.Sender.Tel)
	receiverTel, receiverMobile := a.formatPhoneForYuntongComprehensive(req.Receiver.Mobile, req.Receiver.Tel)

	// 构建寄件人信息
	senderData := map[string]interface{}{
		"Name":         req.Sender.Name,
		"ProvinceName": a.normalizeRegionName(req.Sender.Province),
		"CityName":     a.normalizeRegionName(req.Sender.City),
		"ExpAreaName":  a.getValidDistrict(req.Sender.District),
		"Address":      a.getValidAddress(req.Sender.Address, "发件地址"),
	}
	// 根据电话类型只传递对应字段
	if senderMobile != "" {
		senderData["Mobile"] = senderMobile
	}
	if senderTel != "" {
		senderData["Tel"] = senderTel
	}

	// 构建收件人信息
	receiverData := map[string]interface{}{
		"Name":         req.Receiver.Name,
		"ProvinceName": a.normalizeRegionName(req.Receiver.Province),
		"CityName":     a.normalizeRegionName(req.Receiver.City),
		"ExpAreaName":  a.getValidDistrict(req.Receiver.District),
		"Address":      a.getValidAddress(req.Receiver.Address, "收件地址"),
	}
	// 根据电话类型只传递对应字段
	if receiverMobile != "" {
		receiverData["Mobile"] = receiverMobile
	}
	if receiverTel != "" {
		receiverData["Tel"] = receiverTel
	}

	// 构建应用级参数 - 只传递必填参数
	appRequestData := map[string]interface{}{
		"ShipperCode": yuntongExpressCode,
		"PayType":     payType,
		"Weight":      calcWeight, // 使用计费重量而不是实际重量
		"Sender":      senderData,
		"Receiver":    receiverData,
	}

	// 🔥 关键修复：不传递体积参数，避免云通对带体积参数的请求有更严格的路线限制
	// 经过测试发现，云通供应商对于带Volume参数的请求会进行更严格的路线检查
	// 而不带Volume参数的请求则相对宽松，因此在查价和下单时都不传递体积信息
	// if req.Package.Length > 0 && req.Package.Width > 0 && req.Package.Height > 0 {
	// 	// 长宽高单位是cm，计算体积（cm³）
	// 	volumeCm3 := req.Package.Length * req.Package.Width * req.Package.Height
	// 	// 🔥 修复：云通API期望cm³，不需要转换为m³
	// 	appRequestData["Volume"] = volumeCm3
	// } else if req.Package.Volume > 0 {
	// 	// 如果没有长宽高，Volume字段是m³，需要转换为cm³
	// 	volumeCm3 := req.Package.Volume * 1000000 // m³ → cm³
	// 	appRequestData["Volume"] = volumeCm3
	// }

	// 调用云通 API
	configManager := config.GetProviderConfigManager()
	priceMethod := configManager.GetAPIMethod("yuntong", "price_query")
	if priceMethod == "" {
		priceMethod = "1002" // 默认值
	}
	result, err := a.callYuntongAPI(ctx, priceMethod, appRequestData)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var response struct {
		EBusinessID          string `json:"EBusinessID"`
		Success              bool   `json:"Success"`
		ResultCode           int    `json:"ResultCode"`
		Reason               string `json:"Reason"`
		UniquerRequestNumber string `json:"UniquerRequestNumber"`
		Data                 struct {
			UpperGround    float64 `json:"upperGround"`
			GroundPrice    float64 `json:"groundPrice"`
			RateOfStage    float64 `json:"rateOfStage"`
			TotalFee       float64 `json:"totalfee"`
			OmsProductCode string  `json:"omsProductCode"`
			ProductName    string  `json:"productName"`
			Discount       float64 `json:"discount"`
			DiscountFee    float64 `json:"discountfee"`
			ChannelName    string  `json:"channelName"`
			DiscountType   int     `json:"discounttype"`
			InsureValue    float64 `json:"InsureValue"`
			InsureAmount   float64 `json:"InsureAmount"`
		} `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		// 如果解析失败，尝试解析为不同的格式
		var altResponse struct {
			EBusinessID          string          `json:"EBusinessID"`
			Success              bool            `json:"Success"`
			ResultCode           int             `json:"ResultCode"`
			Reason               string          `json:"Reason"`
			UniquerRequestNumber string          `json:"UniquerRequestNumber"`
			Data                 json.RawMessage `json:"data"`
		}

		if err := json.Unmarshal([]byte(result), &altResponse); err != nil {
			return nil, fmt.Errorf("解析响应失败: %w", err)
		}

		if !altResponse.Success || altResponse.ResultCode != 100 {
			// 🔧 特殊处理：错误码105表示当前线路不支持该快递公司，这是正常情况
			if altResponse.ResultCode == 105 && strings.Contains(altResponse.Reason, "当前线路暂未开放商家寄递服务") {
				a.logger.Debug("云通供应商在当前线路不支持该快递公司(备用解析)",
					zap.String("express_code", req.ExpressType),
					zap.Int("result_code", altResponse.ResultCode),
					zap.String("reason", altResponse.Reason))
				return nil, fmt.Errorf("供应商不支持该快递公司: %s", altResponse.Reason)
			}
			return nil, fmt.Errorf("查询价格失败: %s", altResponse.Reason)
		}

		// 尝试解析Data为数组
		var dataArray []struct {
			UpperGround    float64 `json:"upperGround"`
			GroundPrice    float64 `json:"groundPrice"`
			RateOfStage    float64 `json:"rateOfStage"`
			TotalFee       float64 `json:"totalfee"`
			OmsProductCode string  `json:"omsProductCode"`
			ProductName    string  `json:"productName"`
			Discount       float64 `json:"discount"`
			DiscountFee    float64 `json:"discountfee"`
			ChannelName    string  `json:"channelName"`
			DiscountType   int     `json:"discounttype"`
			InsureValue    float64 `json:"InsureValue"`
			InsureAmount   float64 `json:"InsureAmount"`
		}

		if err := json.Unmarshal(altResponse.Data, &dataArray); err != nil {
			// 如果不是数组，尝试解析为单个对象
			var dataObject struct {
				UpperGround    float64 `json:"upperGround"`
				GroundPrice    float64 `json:"groundPrice"`
				RateOfStage    float64 `json:"rateOfStage"`
				TotalFee       float64 `json:"totalfee"`
				OmsProductCode string  `json:"omsProductCode"`
				ProductName    string  `json:"productName"`
				Discount       float64 `json:"discount"`
				DiscountFee    float64 `json:"discountfee"`
				ChannelName    string  `json:"channelName"`
				DiscountType   int     `json:"discounttype"`
				InsureValue    float64 `json:"InsureValue"`
				InsureAmount   float64 `json:"InsureAmount"`
			}

			if err := json.Unmarshal(altResponse.Data, &dataObject); err != nil {
				return nil, fmt.Errorf("解析响应数据失败: %w", err)
			}

			// 转换为标准价格结构
			// 对于京东快递，使用折后价格（discountfee）
			var finalPrice float64
			if req.ExpressType == "JD" {
				finalPrice = dataObject.DiscountFee
			} else {
				finalPrice = dataObject.TotalFee
			}

			// 🔥 企业级修复：使用之前计算的计费重量

			price := model.StandardizedPrice{
				ExpressCode:          req.ExpressType,
				ExpressName:          dataObject.ProductName,
				ProductCode:          dataObject.OmsProductCode,
				ProductName:          dataObject.ProductName,
				Price:                finalPrice,
				ContinuedWeightPerKg: dataObject.RateOfStage,
				CalcWeight:           calcWeight,
				Provider:             a.Name(),
				ChannelID:            dataObject.ChannelName,
			}

			return []model.StandardizedPrice{price}, nil
		}

		// 如果是数组，处理第一个元素
		if len(dataArray) > 0 {
			data := dataArray[0]

			// 转换为标准价格结构
			// 对于京东快递，使用折后价格（discountfee）
			var finalPrice float64
			if req.ExpressType == "JD" {
				finalPrice = data.DiscountFee
			} else {
				finalPrice = data.TotalFee
			}

			// 🔥 企业级修复：使用之前计算的计费重量

			price := model.StandardizedPrice{
				ExpressCode:          req.ExpressType,
				ExpressName:          data.ProductName,
				ProductCode:          data.OmsProductCode,
				ProductName:          data.ProductName,
				Price:                finalPrice,
				ContinuedWeightPerKg: data.RateOfStage,
				CalcWeight:           calcWeight,
				Provider:             a.Name(),
				ChannelID:            data.ChannelName,
			}

			return []model.StandardizedPrice{price}, nil
		}

		// 如果数组为空，返回空结果
		return []model.StandardizedPrice{}, nil
	}

	if !response.Success || response.ResultCode != 100 {
		// 🔧 特殊处理：错误码105表示当前线路不支持该快递公司，这是正常情况
		if response.ResultCode == 105 && strings.Contains(response.Reason, "当前线路暂未开放商家寄递服务") {
			a.logger.Debug("云通供应商在当前线路不支持该快递公司(查价)",
				zap.String("express_code", req.ExpressType),
				zap.Int("result_code", response.ResultCode),
				zap.String("reason", response.Reason))
			return nil, fmt.Errorf("供应商不支持该快递公司: %s", response.Reason)
		}
		return nil, fmt.Errorf("查询价格失败: %s", response.Reason)
	}

	// 转换为标准价格结构
	// 对于京东快递，使用折后价格（discountfee）
	var finalPrice float64
	if req.ExpressType == "JD" {
		finalPrice = response.Data.DiscountFee
	} else {
		finalPrice = response.Data.TotalFee
	}

	// 🔥 企业级修复：使用之前计算的计费重量

	price := model.StandardizedPrice{
		ExpressCode:          req.ExpressType,
		ExpressName:          response.Data.ProductName,
		ProductCode:          response.Data.OmsProductCode,
		ProductName:          response.Data.ProductName,
		Price:                finalPrice,
		ContinuedWeightPerKg: response.Data.RateOfStage,
		CalcWeight:           calcWeight,
		Provider:             a.Name(),
		ChannelID:            response.Data.ChannelName,
	}

	return []model.StandardizedPrice{price}, nil
}

// CreateOrder 创建订单
func (a *YuntongAdapter) CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResult, error) {
	// 🚀 新增：记录详细的请求信息
	a.logger.Info("云通创建订单开始",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("express_type", req.ExpressType),
		zap.String("product_type", req.ProductType),
		zap.String("channel_id", req.ChannelID),
		zap.String("provider", "yuntong"))

	// 🔥 修复：强制使用查价时选择的渠道信息，确保价格一致性
	fmt.Printf("[云通API] 创建订单 - 转换前参数 - ExpressType: %s, ProductType: %s, ChannelID: %s\n",
		req.ExpressType, req.ProductType, req.ChannelID)

	// 强制要求渠道信息
	if req.ChannelID == "" {
		err := fmt.Errorf("云通供应商必须提供渠道ID，请先查价获取正确的渠道信息")
		a.logger.Error("云通创建订单失败：缺少渠道ID",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Error(err))
		return nil, err
	}

	// 强制要求产品类型
	if req.ProductType == "" {
		err := fmt.Errorf("云通供应商必须提供产品类型，请先查价获取正确的产品信息")
		a.logger.Error("云通创建订单失败：缺少产品类型",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Error(err))
		return nil, err
	}

	// 转换快递公司代码
	yuntongExpressCode := a.convertExpressCode(req.ExpressType)

	// 打印渠道信息传递
	fmt.Printf("[云通API] 🔗 使用查价时选择的渠道信息 - ShipperCode: %s, ChannelID: %s, ProductType: %s\n",
		yuntongExpressCode, req.ChannelID, req.ProductType)

	// 使用查价时确定的产品类型
	productType := req.ProductType

	// 确保PayType值符合云通API要求：0-现付，1-到付，2-月结，默认为2
	payType := a.getDefaultPayType() // 从配置获取默认支付类型
	// 强制使用月结方式，忽略请求中的PayMethod
	// if req.PayMethod >= 0 && req.PayMethod <= 2 {
	// 	payType = req.PayMethod
	// }

	// 🔥 修复：计算正确的计费重量（考虑体积重量）
	calcWeight := a.calculateChargedWeightFromPackage(req.ExpressType, req.Package)

	// 🔥 修复：综合处理Mobile和Tel字段，符合云通API要求
	senderTel, senderMobile := a.formatPhoneForYuntongComprehensive(req.Sender.Mobile, req.Sender.Tel)
	receiverTel, receiverMobile := a.formatPhoneForYuntongComprehensive(req.Receiver.Mobile, req.Receiver.Tel)

	// 构建寄件人信息
	senderData := map[string]interface{}{
		"Name":         req.Sender.Name,
		"ProvinceName": req.Sender.Province,
		"CityName":     req.Sender.City,
		"ExpAreaName":  req.Sender.District,
		"Address":      req.Sender.Address,
	}
	// 根据电话类型只传递对应字段
	if senderMobile != "" {
		senderData["Mobile"] = senderMobile
	}
	if senderTel != "" {
		senderData["Tel"] = senderTel
	}

	// 构建收件人信息
	receiverData := map[string]interface{}{
		"Name":         req.Receiver.Name,
		"ProvinceName": req.Receiver.Province,
		"CityName":     req.Receiver.City,
		"ExpAreaName":  req.Receiver.District,
		"Address":      req.Receiver.Address,
	}
	// 根据电话类型只传递对应字段
	if receiverMobile != "" {
		receiverData["Mobile"] = receiverMobile
	}
	if receiverTel != "" {
		receiverData["Tel"] = receiverTel
	}

	// 构建应用级参数
	appRequestData := map[string]interface{}{
		"ShipperCode": yuntongExpressCode,
		"OrderCode":   req.PlatformOrderNo, // 🔥 修改：使用平台订单号而不是客户订单号
		"PayType":     payType,
		"ExpType":     productType,
		"Weight":      calcWeight, // 🔥 修复：使用计费重量而不是原始重量
		"Quantity":    req.Package.Quantity,
		// 🔥 关键修复：不传递Volume参数，避免云通对带体积参数的请求有更严格的路线限制
		// "Volume":      req.Package.Volume,
		"Sender":   senderData,
		"Receiver": receiverData,
	}

	// 如果有保价金额
	if req.Package.InsureValue > 0 {
		appRequestData["InsureAmount"] = req.Package.InsureValue
	}

	// 添加预约取件时间
	if req.Pickup.StartTime != "" && req.Pickup.EndTime != "" {
		// 🚀 修复：转换为云通API要求的时间格式 YYYY-MM-DD HH:MM:SS
		appRequestData["StartDate"] = a.formatPickupTime(req.Pickup.StartTime)
		appRequestData["EndDate"] = a.formatPickupTime(req.Pickup.EndTime)

		fmt.Printf("[云通API] 添加预约取件时间 - 开始: %s -> %s, 结束: %s -> %s\n",
			req.Pickup.StartTime, appRequestData["StartDate"],
			req.Pickup.EndTime, appRequestData["EndDate"])
	}

	// 添加备注
	if req.Package.Remark != "" {
		appRequestData["Remark"] = req.Package.Remark
	}

	// 添加商品信息
	if req.Package.GoodsName != "" {
		commodity := []map[string]interface{}{
			{
				"GoodsName":     req.Package.GoodsName,
				"Goodsquantity": req.Package.Quantity,
			},
		}
		appRequestData["Commodity"] = commodity
	}

	// 添加增值服务
	if len(req.AddServices) > 0 {
		var addServices []map[string]string
		for _, service := range req.AddServices {
			addServices = append(addServices, map[string]string{
				"Name":  service.Type,
				"Value": service.Value,
			})
		}
		appRequestData["AddService"] = addServices
	}

	// 🔥 删除：云通供应商不支持回调URL参数，包含回调URL可能导致API失败

	// 🚀 详细记录API调用开始（便于问题排查）
	a.logger.Info("云通API调用开始 - 完整请求信息",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("user_id", req.UserID),
		zap.String("express_type", req.ExpressType),
		zap.String("channel_id", req.ChannelID),
		zap.String("product_type", req.ProductType),
		zap.String("method", "order_create"),
		zap.String("sender_address", fmt.Sprintf("%s%s%s%s", req.Sender.Province, req.Sender.City, req.Sender.District, req.Sender.Address)),
		zap.String("receiver_address", fmt.Sprintf("%s%s%s%s", req.Receiver.Province, req.Receiver.City, req.Receiver.District, req.Receiver.Address)),
		zap.Float64("weight", req.Package.Weight),
		zap.Float64("volume", req.Package.Volume),
		zap.Any("request_data", appRequestData))

	// 调用云通 API
	configManager := config.GetProviderConfigManager()
	createMethod := configManager.GetAPIMethod("yuntong", "order_create")
	if createMethod == "" {
		createMethod = "1801" // 默认值
	}
	result, err := a.callYuntongAPI(ctx, createMethod, appRequestData)
	if err != nil {
		// 🚀 详细记录API调用失败（便于问题排查）
		a.logger.Error("云通API调用失败 - 完整错误信息",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.String("express_type", req.ExpressType),
			zap.String("channel_id", req.ChannelID),
			zap.String("product_type", req.ProductType),
			zap.String("method", createMethod),
			zap.String("error_type", "API_CALL_FAILED"),
			zap.String("sender_info", fmt.Sprintf("%s-%s-%s", req.Sender.Province, req.Sender.City, req.Sender.District)),
			zap.String("receiver_info", fmt.Sprintf("%s-%s-%s", req.Receiver.Province, req.Receiver.City, req.Receiver.District)),
			zap.Float64("package_weight", req.Package.Weight),
			zap.Float64("package_volume", req.Package.Volume),
			zap.Any("request_data", appRequestData),
			zap.Error(err))
		return nil, err
	}

	// 🚀 新增：记录API调用成功
	a.logger.Info("云通API调用成功",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("method", createMethod),
		zap.String("response", result))

	// 解析响应
	var response struct {
		EBusinessID          string          `json:"EBusinessID"`
		Success              bool            `json:"Success"`
		ResultCode           int             `json:"ResultCode"`
		Reason               string          `json:"Reason"`
		UniquerRequestNumber string          `json:"UniquerRequestNumber"`
		Order                json.RawMessage `json:"Order"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		// 🚀 新增：记录响应解析失败
		a.logger.Error("云通响应解析失败",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("response", result),
			zap.Error(err))
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 🚀 新增：记录响应解析成功
	a.logger.Info("云通响应解析成功",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.Bool("success", response.Success),
		zap.Int("result_code", response.ResultCode),
		zap.String("reason", response.Reason))

	if !response.Success || response.ResultCode != 100 {
		// 🔧 特殊处理：错误码105表示当前线路不支持该快递公司，这是正常情况
		if response.ResultCode == 105 && strings.Contains(response.Reason, "当前线路暂未开放商家寄递服务") {
			a.logger.Debug("云通供应商在当前线路不支持该快递公司",
				zap.String("customer_order_no", req.CustomerOrderNo),
				zap.String("express_code", req.ExpressType),
				zap.Int("result_code", response.ResultCode),
				zap.String("reason", response.Reason))
			return nil, fmt.Errorf("供应商不支持该快递公司: %s", response.Reason)
		}

		// 🚀 详细记录业务失败详情（便于问题排查）
		a.logger.Error("云通业务失败 - 完整失败信息",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.String("user_id", req.UserID),
			zap.String("express_type", req.ExpressType),
			zap.String("channel_id", req.ChannelID),
			zap.String("product_type", req.ProductType),
			zap.String("error_type", "BUSINESS_FAILED"),
			zap.String("reason", response.Reason),
			zap.Int("result_code", response.ResultCode),
			zap.String("sender_info", fmt.Sprintf("%s-%s-%s", req.Sender.Province, req.Sender.City, req.Sender.District)),
			zap.String("receiver_info", fmt.Sprintf("%s-%s-%s", req.Receiver.Province, req.Receiver.City, req.Receiver.District)),
			zap.Float64("package_weight", req.Package.Weight),
			zap.Float64("package_volume", req.Package.Volume),
			zap.String("full_response", result),
			zap.Any("request_data", appRequestData))
		return nil, fmt.Errorf("%s", response.Reason)
	}

	// 尝试解析Order字段
	var orderData struct {
		OrderCode    string      `json:"OrderCode"`
		PlatCode     string      `json:"PlatCode"`
		LogisticCode string      `json:"LogisticCode"`
		PrintData    interface{} `json:"PrintData"`
	}

	// 检查Order字段是否为空字符串
	if string(response.Order) == "" || string(response.Order) == "\"\"" {
		// 如果Order字段为空，使用请求中的平台订单号作为OrderCode
		orderData.OrderCode = req.PlatformOrderNo // 🔥 修改：使用平台订单号而不是客户订单号
		// 生成一个随机的物流单号
		orderData.LogisticCode = fmt.Sprintf("YT%s", util.NowBeijing().Format("20060102150405"))
	} else {
		// 尝试解析Order字段
		if err := json.Unmarshal(response.Order, &orderData); err != nil {
			return nil, fmt.Errorf("解析Order字段失败: %w", err)
		}
	}

	// 转换为标准订单结果
	orderResult := &model.OrderResult{
		OrderNo:          orderData.OrderCode,
		TrackingNo:       orderData.LogisticCode,
		PickupCode:       "",
		ChildTrackingNos: nil,
		Price:            0,
		PrintData:        orderData.PrintData,
		PollToken:        "",
	}

	return orderResult, nil
}

// CancelOrder 取消订单
func (a *YuntongAdapter) CancelOrder(ctx context.Context, taskId string, orderNo string, reason string) error {
	// 打印转换前的参数
	fmt.Printf("[云通API] 取消订单 - 转换前参数 - TaskId: %s, OrderNo: %s\n", taskId, orderNo)

	// 构建应用级参数
	appRequestData := map[string]interface{}{
		"OrderCode": orderNo,
	}

	// 如果有快递公司代码，转换为云通快递公司代码
	if taskId != "" {
		yuntongExpressCode := a.convertExpressCode(taskId)
		appRequestData["ShipperCode"] = yuntongExpressCode

		// 打印转换后的快递公司代码
		fmt.Printf("[云通API] 取消订单 - 转换后参数 - ShipperCode: %s\n", yuntongExpressCode)
	}

	// 如果有取消原因
	if reason != "" {
		appRequestData["CancelType"] = 1 // 预约信息有误
		appRequestData["CancelMsg"] = reason
	}

	// 调用云通 API
	configManager := config.GetProviderConfigManager()
	cancelMethod := configManager.GetAPIMethod("yuntong", "order_cancel")
	if cancelMethod == "" {
		cancelMethod = "1802" // 默认值
	}
	result, err := a.callYuntongAPI(ctx, cancelMethod, appRequestData)
	if err != nil {
		return err
	}

	// 解析响应
	var response struct {
		EBusinessID          string `json:"EBusinessID"`
		Success              bool   `json:"Success"`
		ResultCode           int    `json:"ResultCode"`
		Reason               string `json:"Reason"`
		UniquerRequestNumber string `json:"UniquerRequestNumber"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if !response.Success || response.ResultCode != 100 {
		return fmt.Errorf("取消订单失败: %s", response.Reason)
	}

	return nil
}

// 🚀 formatPickupTime 将ISO 8601时间格式转换为云通API要求的格式
func (a *YuntongAdapter) formatPickupTime(isoTime string) string {
	// 解析ISO 8601格式时间
	t, err := time.Parse(time.RFC3339, isoTime)
	if err != nil {
		fmt.Printf("[云通API] 解析预约时间失败，使用原始格式: %s, 错误: %v\n",
			isoTime, err)
		return isoTime
	}

	// 🔥 修复：转换为北京时间，避免直接使用UTC时间
	beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
	beijingTime := t.In(beijingLocation)

	// 转换为云通API要求的格式：YYYY-MM-DD HH:MM:SS
	formatted := beijingTime.Format("2006-01-02 15:04:05")

	fmt.Printf("[云通API] 时间转换详情 - 原始: %s, UTC: %s, 北京时间: %s, 格式化: %s\n",
		isoTime, t.Format("2006-01-02 15:04:05"), beijingTime.Format("2006-01-02 15:04:05"), formatted)

	return formatted
}

// QueryOrder 查询订单
func (a *YuntongAdapter) QueryOrder(ctx context.Context, orderNo string, trackingNo string) (*model.OrderInfo, error) {
	// 构建应用级参数
	appRequestData := map[string]interface{}{}

	// 根据云通API文档，查询订单只需要提供订单号
	if orderNo != "" {
		appRequestData["OrderCode"] = orderNo
	} else {
		// 如果没有订单号，返回错误
		return nil, fmt.Errorf("云通API查询订单必须提供订单号")
	}

	// 调用云通 API
	configManager := config.GetProviderConfigManager()
	queryMethod := configManager.GetAPIMethod("yuntong", "order_query")
	if queryMethod == "" {
		queryMethod = "1804" // 默认值
	}
	result, err := a.callYuntongAPI(ctx, queryMethod, appRequestData)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var response struct {
		EBusinessID          json.Number `json:"EBusinessID"` // 使用json.Number类型，可以接受字符串或数字
		Success              bool        `json:"Success"`
		ResultCode           int         `json:"ResultCode"`
		Reason               string      `json:"Reason"`
		UniquerRequestNumber string      `json:"UniquerRequestNumber"`
		OrderCode            string      `json:"OrderCode"`
		ShipperCode          string      `json:"ShipperCode"`
		LogisticCode         string      `json:"LogisticCode"`
		Weight               float64     `json:"Weight"`
		Cost                 float64     `json:"Cost"`
		State                int         `json:"State"`
		CreateTime           json.Number `json:"CreateTime"` // 使用json.Number类型，可以接受字符串或数字
		PersonName           string      `json:"PersonName"`
		PersonTel            string      `json:"PersonTel"`
		PersonCode           string      `json:"PersonCode"`
		StationName          string      `json:"StationName"`
		StationCode          string      `json:"StationCode"`
		StationAddress       string      `json:"StationAddress"`
		StationTel           string      `json:"StationTel"`
		PickupCode           string      `json:"PickupCode"`
		InsureAmount         float64     `json:"InsureAmount"`
		PackageFee           float64     `json:"PackageFee"`
		OverFee              float64     `json:"OverFee"`
		OtherFee             float64     `json:"OtherFee"`
		OtherFeeDetail       string      `json:"OtherFeeDetail"`
		TotalFee             float64     `json:"TotalFee"`
		Volume               float64     `json:"Volume"`
		ChargedWeight        float64     `json:"chargedWeight"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if !response.Success || response.ResultCode != 100 {
		return nil, fmt.Errorf("查询订单失败: %s", response.Reason)
	}

	// 解析创建时间
	var createTime time.Time
	if response.CreateTime != "" {
		// 将json.Number转换为字符串
		createTimeStr := response.CreateTime.String()
		// 尝试解析为时间格式
		parsedTime, err := time.Parse("2006-01-02 15:04:05", createTimeStr)
		if err != nil {
			// 如果解析失败，可能是时间戳格式，尝试解析为整数
			if timestamp, err := response.CreateTime.Int64(); err == nil {
				// 将时间戳转换为时间
				parsedTime = time.Unix(timestamp, 0)
			} else {
				// 如果都解析失败，使用当前时间
				parsedTime = util.NowBeijing()
			}
		}
		createTime = parsedTime
	} else {
		createTime = util.NowBeijing()
	}

	// 获取状态描述
	statusDesc := ""
	switch response.State {
	case 100:
		statusDesc = "下单成功"
	case 102:
		statusDesc = "分配网点"
	case 103:
		statusDesc = "分配快递员"
	case 104:
		statusDesc = "已取件"
	case 301:
		statusDesc = "已揽件"
	case 203:
		statusDesc = "取消订单"
	case 2:
		statusDesc = "在途中"
	case 3:
		statusDesc = "签收"
	default:
		statusDesc = "未知状态"
	}

	// 转换为标准订单信息
	orderInfo := &model.OrderInfo{
		OrderNo:        response.OrderCode,
		TrackingNo:     response.LogisticCode,
		ExpressType:    response.ShipperCode,
		Status:         fmt.Sprintf("%d", response.State),
		StatusDesc:     statusDesc,
		Weight:         response.Weight,
		Price:          response.TotalFee,
		CreatedAt:      createTime,
		CourierName:    response.PersonName,
		CourierPhone:   response.PersonTel,
		CourierCode:    response.PersonCode,
		StationName:    response.StationName,
		StationCode:    response.StationCode,
		StationAddress: response.StationAddress,
		StationPhone:   response.StationTel,
		PickupCode:     response.PickupCode,
	}

	return orderInfo, nil
}

// QueryTrack 查询物流轨迹
func (a *YuntongAdapter) QueryTrack(ctx context.Context, trackingNo, expressType, phone, pollToken string, from, to string) (*model.TrackInfo, error) {
	// 打印转换前的参数
	fmt.Printf("[云通API] 查询物流轨迹 - 转换前参数 - TrackingNo: %s, ExpressType: %s\n", trackingNo, expressType)

	// 转换快递公司代码
	yuntongExpressCode := a.convertExpressCode(expressType)

	// 打印转换后的快递公司代码
	fmt.Printf("[云通API] 查询物流轨迹 - 转换后参数 - ShipperCode: %s\n", yuntongExpressCode)

	// 构建应用级参数
	appRequestData := map[string]interface{}{}

	// 优先使用运单号
	if trackingNo != "" {
		appRequestData["LogisticCode"] = trackingNo
	} else {
		// 如果没有运单号，返回错误
		return nil, fmt.Errorf("云通API查询物流轨迹必须提供运单号")
	}

	// 如果有快递公司代码，添加到请求参数中
	if yuntongExpressCode != "" {
		appRequestData["ShipperCode"] = yuntongExpressCode
	}

	// 调用云通 API
	configManager := config.GetProviderConfigManager()
	trackMethod := configManager.GetAPIMethod("yuntong", "track_query")
	if trackMethod == "" {
		trackMethod = "8001" // 默认值
	}
	result, err := a.callYuntongAPI(ctx, trackMethod, appRequestData)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var response struct {
		EBusinessID          json.Number `json:"EBusinessID"` // 使用json.Number类型，可以接受字符串或数字
		Success              bool        `json:"Success"`
		UniquerRequestNumber string      `json:"UniquerRequestNumber"`
		Reason               string      `json:"Reason"`
		Traces               []struct {
			AcceptStation string `json:"AcceptStation"`
			AcceptTime    string `json:"AcceptTime"`
			Location      string `json:"Location"`
		} `json:"Traces"`
	}

	if err := json.Unmarshal([]byte(result), &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("查询物流轨迹失败: %s", response.Reason)
	}

	// 转换为标准物流轨迹信息
	trackInfo := &model.TrackInfo{
		OrderNo:     "",
		TrackingNo:  trackingNo,
		ExpressType: expressType,
		State:       "",
		StateDesc:   "",
		IsCheck:     "",
		Tracks:      make([]*model.TrackItem, 0, len(response.Traces)),
	}

	// 转换物流轨迹项
	for _, item := range response.Traces {
		// 🔥 修复：使用北京时区解析时间
		var t time.Time
		if item.AcceptTime != "" {
			beijingLocation, _ := time.LoadLocation("Asia/Shanghai")
			parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", item.AcceptTime, beijingLocation)
			if err != nil {
				t = util.NowBeijing()
			} else {
				t = parsedTime
			}
		} else {
			t = util.NowBeijing()
		}

		trackItem := &model.TrackItem{
			Context:    item.AcceptStation,
			Time:       t,
			Status:     "",
			StatusCode: "",
			AreaCode:   "",
			AreaName:   "",
			Location:   item.Location,
		}

		trackInfo.Tracks = append(trackInfo.Tracks, trackItem)
	}

	// 根据轨迹判断物流状态
	if len(trackInfo.Tracks) > 0 {
		// 检查最新的轨迹信息是否包含签收关键词
		latestTrack := trackInfo.Tracks[0]
		if strings.Contains(latestTrack.Context, "签收") {
			trackInfo.State = "3"
			trackInfo.StateDesc = "已签收"
			trackInfo.IsCheck = "1"
		} else if strings.Contains(latestTrack.Context, "派送") || strings.Contains(latestTrack.Context, "派件") {
			trackInfo.State = "2"
			trackInfo.StateDesc = "派送中"
			trackInfo.IsCheck = "0"
		} else if strings.Contains(latestTrack.Context, "运输") || strings.Contains(latestTrack.Context, "发往") {
			trackInfo.State = "1"
			trackInfo.StateDesc = "运输中"
			trackInfo.IsCheck = "0"
		} else if strings.Contains(latestTrack.Context, "揽收") || strings.Contains(latestTrack.Context, "已收件") {
			trackInfo.State = "0"
			trackInfo.StateDesc = "已揽收"
			trackInfo.IsCheck = "0"
		} else {
			trackInfo.State = "0"
			trackInfo.StateDesc = "已揽收"
			trackInfo.IsCheck = "0"
		}
	}

	return trackInfo, nil
}

// 调用云通 API
func (a *YuntongAdapter) callYuntongAPI(ctx context.Context, requestType string, appRequestData map[string]interface{}) (string, error) {
	// 将应用级参数转换为JSON字符串
	requestDataJSON, err := json.Marshal(appRequestData)
	if err != nil {
		return "", fmt.Errorf("应用级参数序列化失败: %w", err)
	}
	requestDataStr := string(requestDataJSON)

	// 打印请求参数
	fmt.Printf("[云通API] 请求类型: %s\n", requestType)
	fmt.Printf("[云通API] 请求参数: %s\n", requestDataStr)

	// 生成签名
	dataSign := a.generateSign(requestDataStr, a.config.ApiKey)
	fmt.Printf("[云通API] 签名: %s\n", dataSign)

	// 构建系统级参数
	formData := url.Values{}
	formData.Set("RequestData", url.QueryEscape(requestDataStr))
	formData.Set("EBusinessID", a.config.EBusinessID)
	formData.Set("RequestType", requestType)
	formData.Set("DataSign", dataSign)
	formData.Set("DataType", a.getDataType()) // 从配置获取数据类型

	// 打印系统级参数
	fmt.Printf("[云通API] 商户ID: %s\n", a.config.EBusinessID)
	fmt.Printf("[云通API] 请求URL: %s\n", a.config.BaseURL)
	fmt.Printf("[云通API] 表单数据: %s\n", formData.Encode())

	// 构建请求URL
	requestURL := a.config.BaseURL

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, requestURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")

	// 发送请求
	fmt.Printf("[云通API] 发送请求...\n")
	resp, err := a.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	// 打印响应
	fmt.Printf("[云通API] 响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("[云通API] 响应内容: %s\n", string(respBody))

	return string(respBody), nil
}

// 获取默认支付类型
func (a *YuntongAdapter) getDefaultPayType() int {
	// 从配置获取默认支付类型
	configManager := config.GetProviderConfigManager()
	payType := configManager.GetParameter("yuntong", "pay_type_default")

	if payType != nil {
		if val, ok := payType.(float64); ok {
			return int(val)
		}
		if val, ok := payType.(int); ok {
			return val
		}
	}
	return 2 // 默认值：月结
}

// 获取数据类型
func (a *YuntongAdapter) getDataType() string {
	// 从配置获取数据类型
	configManager := config.GetProviderConfigManager()
	dataType := configManager.GetParameter("yuntong", "data_type")

	if dataType != nil {
		if str, ok := dataType.(string); ok {
			return str
		}
	}
	return "2" // 默认值：JSON格式
}

// 生成签名
func (a *YuntongAdapter) generateSign(data, apiKey string) string {
	// 按照云通的签名规则生成签名
	// 1. 计算MD5(data+apiKey)
	md5Str := fmt.Sprintf("%x", md5.Sum([]byte(data+apiKey)))

	// 2. Base64编码
	base64Str := base64.StdEncoding.EncodeToString([]byte(md5Str))

	// 3. URL编码
	return url.QueryEscape(base64Str)
}

// normalizeRegionName 标准化地区名称
// 云通API对地区名称格式有特定要求
func (a *YuntongAdapter) normalizeRegionName(regionName string) string {
	if regionName == "" {
		return ""
	}

	// 移除常见的后缀
	regionName = strings.TrimSuffix(regionName, "省")
	regionName = strings.TrimSuffix(regionName, "市")
	regionName = strings.TrimSuffix(regionName, "区")
	regionName = strings.TrimSuffix(regionName, "县")
	regionName = strings.TrimSuffix(regionName, "自治区")
	regionName = strings.TrimSuffix(regionName, "维吾尔自治区")
	regionName = strings.TrimSuffix(regionName, "壮族自治区")
	regionName = strings.TrimSuffix(regionName, "回族自治区")
	regionName = strings.TrimSuffix(regionName, "特别行政区")

	// 特殊地区名称映射
	regionMappings := map[string]string{
		"北京":  "北京",
		"北京市": "北京",
		"上海":  "上海",
		"上海市": "上海",
		"天津":  "天津",
		"天津市": "天津",
		"重庆":  "重庆",
		"重庆市": "重庆",
		"内蒙古": "内蒙古",
		"新疆":  "新疆",
		"西藏":  "西藏",
		"宁夏":  "宁夏",
		"广西":  "广西",
		"香港":  "香港",
		"澳门":  "澳门",
	}

	if mapped, exists := regionMappings[regionName]; exists {
		return mapped
	}

	return regionName
}

// getValidAddress 获取有效的地址信息
// 云通API要求地址信息不能为空或只是占位符
func (a *YuntongAdapter) getValidAddress(address, defaultDesc string) string {
	// 如果地址为空或是常见的占位符，使用默认地址
	if address == "" || address == "某地" || address == "详细地址" || len(strings.TrimSpace(address)) < 3 {
		// 从配置获取默认地址
		configManager := config.GetProviderConfigManager()
		if defaultAddressParam := configManager.GetParameter("yuntong", "default_address"); defaultAddressParam != nil {
			if defaultAddress, ok := defaultAddressParam.(string); ok && defaultAddress != "" {
				return defaultAddress
			}
		}
		// 如果配置中没有默认地址，使用描述性地址
		return fmt.Sprintf("%s详细地址123号", defaultDesc)
	}
	return address
}

// getValidDistrict 获取有效的区县信息
// 云通API要求区县信息不能为空
func (a *YuntongAdapter) getValidDistrict(district string) string {
	// 如果区县为空，使用默认区县
	if district == "" || len(strings.TrimSpace(district)) == 0 {
		return "某某区"
	}
	return a.normalizeRegionName(district)
}

// calculateChargedWeightFromPackage 从包裹信息计算计费重量（优先使用长宽高）
func (a *YuntongAdapter) calculateChargedWeightFromPackage(expressCode string, pkg model.PackageInfo) float64 {
	// 🔥 企业级修复：使用统一的体积重量计算器
	return a.calculateChargedWeightEnterprise(expressCode, pkg)
}

// calculateChargedWeightEnterprise 企业级体积重量计算（从数据库获取配置）
func (a *YuntongAdapter) calculateChargedWeightEnterprise(expressCode string, pkg model.PackageInfo) float64 {
	// 先计算体积
	var volumeCm3 float64
	if pkg.Length > 0 && pkg.Width > 0 && pkg.Height > 0 {
		// 长宽高单位是cm，直接计算体积（cm³）
		volumeCm3 = pkg.Length * pkg.Width * pkg.Height
	} else if pkg.Volume > 0 {
		// 如果没有长宽高，从m³转换为cm³
		volumeCm3 = pkg.Volume * 1000000
	}

	a.logger.Debug("云通企业级计算器输入参数",
		zap.String("express_code", expressCode),
		zap.Float64("actual_weight", pkg.Weight),
		zap.Float64("length", pkg.Length),
		zap.Float64("width", pkg.Width),
		zap.Float64("height", pkg.Height),
		zap.Float64("volume_cm3", volumeCm3))

	// 如果没有体积信息，直接返回实际重量（向上取整）
	if volumeCm3 <= 0 {
		result := math.Ceil(pkg.Weight)
		a.logger.Debug("云通企业级计算器：无体积信息，使用实际重量",
			zap.Float64("result", result))
		return result
	}

	// 从数据库获取快递公司配置
	if a.expressCompanyRepo == nil {
		a.logger.Error("ExpressCompanyRepository 未初始化",
			zap.String("express_code", expressCode))
		return math.Ceil(pkg.Weight)
	}

	company, err := a.expressCompanyRepo.GetCompanyByCode(expressCode)
	if err != nil {
		a.logger.Error("云通企业级计算器：获取快递公司配置失败",
			zap.String("express_code", expressCode),
			zap.Error(err))
		panic(fmt.Sprintf("快递公司 %s 配置获取失败: %v。请在数据库 express_companies 表中配置正确的快递公司信息", expressCode, err))
	}

	if !company.IsActive {
		a.logger.Error("云通企业级计算器：快递公司已禁用",
			zap.String("express_code", expressCode))
		panic(fmt.Sprintf("快递公司 %s 已禁用。请在数据库中启用该快递公司", expressCode))
	}

	if company.VolumeWeightRatio <= 0 {
		a.logger.Error("云通企业级计算器：快递公司抛比配置无效",
			zap.String("express_code", expressCode),
			zap.Int("volume_ratio", company.VolumeWeightRatio))
		panic(fmt.Sprintf("快递公司 %s 抛比配置无效: %d。请在数据库 express_companies 表中配置正确的 volume_weight_ratio 值", expressCode, company.VolumeWeightRatio))
	}

	volumeRatio := company.VolumeWeightRatio
	companyName := company.Name
	a.logger.Debug("云通企业级计算器：从数据库获取抛比配置",
		zap.String("express_code", expressCode),
		zap.String("company_name", companyName),
		zap.Int("volume_ratio", volumeRatio))

	// 计算体积重量
	volumeWeight := volumeCm3 / float64(volumeRatio)

	// 取实际重量和体积重量的较大值
	chargedWeight := math.Max(pkg.Weight, volumeWeight)

	// 向上取整到最近的整数（快递行业标准）
	result := math.Ceil(chargedWeight)

	a.logger.Debug("云通企业级计算器计算结果",
		zap.String("company_name", companyName),
		zap.Int("volume_ratio", volumeRatio),
		zap.Float64("volume_weight", volumeWeight),
		zap.Float64("actual_weight", pkg.Weight),
		zap.Float64("charged_weight", result))

	return result
}
