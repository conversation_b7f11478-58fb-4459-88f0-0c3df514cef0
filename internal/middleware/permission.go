package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/user"
)

// PermissionMiddleware 权限验证中间件
type PermissionMiddleware struct {
	userService user.UserRoleService
}

// NewPermissionMiddleware 创建新的权限验证中间件
func NewPermissionMiddleware(userService user.UserRoleService) *PermissionMiddleware {
	return &PermissionMiddleware{
		userService: userService,
	}
}

// RequirePermission 要求用户拥有指定权限
func (m *PermissionMiddleware) RequirePermission(permissionID string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文中获取用户ID并进行类型验证
		userIDRaw, exists := c.Get("userID")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "未授权，请先登录",
			})
			c.Abort()
			return
		}

		// 安全的类型断言，防止权限绕过
		userID, ok := userIDRaw.(string)
		if !ok || userID == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的用户身份信息",
			})
			c.Abort()
			return
		}

		// 验证权限ID参数
		if permissionID == "" {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "权限配置错误",
			})
			c.Abort()
			return
		}

		// 检查用户是否拥有指定权限
		hasPermission, err := m.userService.HasPermission(userID, permissionID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "检查权限失败: " + err.Error(),
			})
			c.Abort()
			return
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "权限不足，无法访问该资源",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireResourcePermission 要求用户拥有指定资源的指定操作权限
func (m *PermissionMiddleware) RequireResourcePermission(resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文中获取用户ID并进行类型验证
		userIDRaw, exists := c.Get("userID")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "未授权，请先登录",
			})
			c.Abort()
			return
		}

		// 安全的类型断言，防止权限绕过
		userID, ok := userIDRaw.(string)
		if !ok || userID == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的用户身份信息",
			})
			c.Abort()
			return
		}

		// 验证资源和操作参数
		if resource == "" || action == "" {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "权限配置错误",
			})
			c.Abort()
			return
		}

		// 检查用户是否拥有指定资源的指定操作权限
		hasPermission, err := m.userService.HasResourcePermission(userID, resource, action)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "检查权限失败: " + err.Error(),
			})
			c.Abort()
			return
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "权限不足，无法访问该资源",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRole 要求用户拥有指定角色
func (m *PermissionMiddleware) RequireRole(roleID string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文中获取用户ID并进行类型验证
		userIDRaw, exists := c.Get("userID")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "未授权，请先登录",
			})
			c.Abort()
			return
		}

		// 安全的类型断言，防止权限绕过
		userID, ok := userIDRaw.(string)
		if !ok || userID == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的用户身份信息",
			})
			c.Abort()
			return
		}

		// 验证角色ID参数
		if roleID == "" {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "角色配置错误",
			})
			c.Abort()
			return
		}

		// 获取用户的所有角色
		roles, err := m.userService.GetUserRoles(userID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "获取用户角色失败: " + err.Error(),
			})
			c.Abort()
			return
		}

		// 检查用户是否拥有指定角色
		hasRole := false
		for _, role := range roles {
			if role.ID == roleID {
				hasRole = true
				break
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "权限不足，无法访问该资源",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetUserID 从上下文中获取用户ID
func GetUserID(c *gin.Context) string {
	userID, exists := c.Get("userID")
	if !exists {
		return ""
	}
	return userID.(string)
}

// RequireAdminAuth 要求管理员权限
func RequireAdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文中获取用户ID
		userID, exists := c.Get("userID")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "未授权，请先登录",
			})
			c.Abort()
			return
		}

		// 检查是否为管理员（这里简化处理，实际应该检查角色）
		if userID == "" {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "权限不足，需要管理员权限",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
