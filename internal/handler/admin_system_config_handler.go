package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/middleware"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/service"
)

// AdminSystemConfigHandler 管理员系统配置处理器
type AdminSystemConfigHandler struct {
	systemConfigService service.SystemConfigService
	logger              *zap.Logger
}

// NewAdminSystemConfigHandler 创建管理员系统配置处理器
func NewAdminSystemConfigHandler(
	systemConfigService service.SystemConfigService,
	logger *zap.Logger,
) *AdminSystemConfigHandler {
	return &AdminSystemConfigHandler{
		systemConfigService: systemConfigService,
		logger:              logger,
	}
}

// RegisterRoutes 注册路由
func (h *AdminSystemConfigHandler) RegisterRoutes(router *gin.RouterGroup) {
	configGroup := router.Group("/system-configs")
	{
		// 配置管理
		configGroup.GET("", h.ListConfigs)
		configGroup.POST("", h.CreateConfig)
		configGroup.GET("/:id", h.GetConfig)
		configGroup.PUT("/:id", h.UpdateConfig)
		configGroup.DELETE("/:id", h.DeleteConfig)
		configGroup.POST("/batch", h.BatchUpdateConfigs)

		// 配置组管理
		configGroup.GET("/groups", h.GetConfigGroups)
		configGroup.GET("/groups/:group", h.GetConfigsByGroup)

		// 配置变更日志
		configGroup.GET("/change-logs", h.ListChangeLogs)

		// 配置模板
		configGroup.GET("/templates", h.ListTemplates)
		configGroup.POST("/templates", h.CreateTemplate)
		configGroup.GET("/templates/:name", h.GetTemplate)
		configGroup.PUT("/templates/:id", h.UpdateTemplate)
		configGroup.DELETE("/templates/:id", h.DeleteTemplate)
		configGroup.POST("/templates/:name/apply", h.ApplyTemplate)

		// 配置备份
		configGroup.GET("/backups", h.ListBackups)
		configGroup.POST("/backups", h.CreateBackup)
		configGroup.GET("/backups/:id", h.GetBackup)
		configGroup.POST("/backups/:id/restore", h.RestoreBackup)
		configGroup.DELETE("/backups/:id", h.DeleteBackup)

		// 工具方法
		configGroup.POST("/cache/refresh", h.RefreshCache)
		configGroup.POST("/validate", h.ValidateConfig)
	}
}

// ListConfigs 获取配置列表
func (h *AdminSystemConfigHandler) ListConfigs(c *gin.Context) {
	var req model.SystemConfigListRequest

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取配置列表
	result, err := h.systemConfigService.ListConfigs(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("获取配置列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取配置列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取配置列表成功",
		"data":    result,
	})
}

// CreateConfig 创建配置
func (h *AdminSystemConfigHandler) CreateConfig(c *gin.Context) {
	var req model.SystemConfigRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误", "error": err.Error()})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 创建配置
	config, err := h.systemConfigService.CreateConfig(c.Request.Context(), &req, operatorID)
	if err != nil {
		h.logger.Error("创建配置失败", zap.Error(err), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "创建配置失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": config})
}

// GetConfig 获取配置详情
func (h *AdminSystemConfigHandler) GetConfig(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "参数错误",
			"error":   "配置ID不能为空",
		})
		return
	}

	config, err := h.systemConfigService.GetConfigByID(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("获取配置详情失败", zap.Error(err), zap.String("id", id))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "获取配置详情失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": config})
}

// UpdateConfig 更新配置
func (h *AdminSystemConfigHandler) UpdateConfig(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误"})
		return
	}

	var req model.SystemConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误", "error": err.Error()})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 更新配置
	config, err := h.systemConfigService.UpdateConfig(c.Request.Context(), id, &req, operatorID)
	if err != nil {
		h.logger.Error("更新配置失败", zap.Error(err), zap.String("id", id), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "更新配置失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": config})
}

// DeleteConfig 删除配置
func (h *AdminSystemConfigHandler) DeleteConfig(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误"})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 删除配置
	if err := h.systemConfigService.DeleteConfig(c.Request.Context(), id, operatorID); err != nil {
		h.logger.Error("删除配置失败", zap.Error(err), zap.String("id", id), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "删除配置失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"message": "配置删除成功"}})
}

// BatchUpdateConfigs 批量更新配置
func (h *AdminSystemConfigHandler) BatchUpdateConfigs(c *gin.Context) {
	var req struct {
		Configs []*model.SystemConfig `json:"configs" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误", "error": err.Error()})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 批量更新配置
	if err := h.systemConfigService.BatchUpdateConfigs(c.Request.Context(), req.Configs, operatorID); err != nil {
		h.logger.Error("批量更新配置失败", zap.Error(err), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "批量更新配置失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"message": "批量更新配置成功"}})
}

// GetConfigGroups 获取配置组列表
func (h *AdminSystemConfigHandler) GetConfigGroups(c *gin.Context) {
	groups, err := h.systemConfigService.GetConfigGroups(c.Request.Context())
	if err != nil {
		h.logger.Error("获取配置组列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "获取配置组列表失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"groups": groups}})
}

// GetConfigsByGroup 根据组获取配置列表
func (h *AdminSystemConfigHandler) GetConfigsByGroup(c *gin.Context) {
	group := c.Param("group")
	if group == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误"})
		return
	}

	configs, err := h.systemConfigService.GetConfigsByGroup(c.Request.Context(), group)
	if err != nil {
		h.logger.Error("获取配置组配置失败", zap.Error(err), zap.String("group", group))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "获取配置组配置失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"configs": configs}})
}

// ListChangeLogs 获取配置变更日志列表
func (h *AdminSystemConfigHandler) ListChangeLogs(c *gin.Context) {
	var req model.ConfigChangeLogListRequest

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误", "error": err.Error()})
		return
	}

	// 获取变更日志列表
	result, err := h.systemConfigService.ListChangeLogs(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("获取配置变更日志失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "获取配置变更日志失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": result})
}

// ListTemplates 获取配置模板列表
func (h *AdminSystemConfigHandler) ListTemplates(c *gin.Context) {
	templates, err := h.systemConfigService.ListTemplates(c.Request.Context())
	if err != nil {
		h.logger.Error("获取配置模板列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "获取配置模板列表失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"templates": templates}})
}

// CreateTemplate 创建配置模板
func (h *AdminSystemConfigHandler) CreateTemplate(c *gin.Context) {
	var req model.ConfigTemplate

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误", "error": err.Error()})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 创建模板
	template, err := h.systemConfigService.CreateTemplate(c.Request.Context(), &req, operatorID)
	if err != nil {
		h.logger.Error("创建配置模板失败", zap.Error(err), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "创建配置模板失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": template})
}

// GetTemplate 获取配置模板详情
func (h *AdminSystemConfigHandler) GetTemplate(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误"})
		return
	}

	template, err := h.systemConfigService.GetTemplateByName(c.Request.Context(), name)
	if err != nil {
		h.logger.Error("获取配置模板详情失败", zap.Error(err), zap.String("name", name))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "获取配置模板详情失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": template})
}

// UpdateTemplate 更新配置模板
func (h *AdminSystemConfigHandler) UpdateTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误"})
		return
	}

	var req model.ConfigTemplate
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误", "error": err.Error()})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 更新模板
	template, err := h.systemConfigService.UpdateTemplate(c.Request.Context(), id, &req, operatorID)
	if err != nil {
		h.logger.Error("更新配置模板失败", zap.Error(err), zap.String("id", id), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "更新配置模板失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": template})
}

// DeleteTemplate 删除配置模板
func (h *AdminSystemConfigHandler) DeleteTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误"})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 删除模板
	if err := h.systemConfigService.DeleteTemplate(c.Request.Context(), id, operatorID); err != nil {
		h.logger.Error("删除配置模板失败", zap.Error(err), zap.String("id", id), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "删除配置模板失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"message": "配置模板删除成功"}})
}

// ApplyTemplate 应用配置模板
func (h *AdminSystemConfigHandler) ApplyTemplate(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误"})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 应用模板
	if err := h.systemConfigService.ApplyTemplate(c.Request.Context(), name, operatorID); err != nil {
		h.logger.Error("应用配置模板失败", zap.Error(err), zap.String("name", name), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "应用配置模板失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"message": "配置模板应用成功"}})
}

// ListBackups 获取配置备份列表
func (h *AdminSystemConfigHandler) ListBackups(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	backups, total, err := h.systemConfigService.ListBackups(c.Request.Context(), page, pageSize)
	if err != nil {
		h.logger.Error("获取配置备份列表失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "获取配置备份列表失败", "error": err.Error()})
		return
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取配置备份列表成功",
		"data": gin.H{
			"backups":     backups,
			"total":       total,
			"page":        page,
			"page_size":   pageSize,
			"total_pages": totalPages,
		},
	})
}

// CreateBackup 创建配置备份
func (h *AdminSystemConfigHandler) CreateBackup(c *gin.Context) {
	var req struct {
		BackupName        string `json:"backup_name" binding:"required"`
		BackupDescription string `json:"backup_description"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误", "error": err.Error()})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 创建备份
	backup, err := h.systemConfigService.CreateBackup(c.Request.Context(), req.BackupName, req.BackupDescription, operatorID)
	if err != nil {
		h.logger.Error("创建配置备份失败", zap.Error(err), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "创建配置备份失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": backup})
}

// GetBackup 获取配置备份详情
func (h *AdminSystemConfigHandler) GetBackup(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误"})
		return
	}

	backup, err := h.systemConfigService.GetBackupByID(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("获取配置备份详情失败", zap.Error(err), zap.String("id", id))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "获取配置备份详情失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": backup})
}

// RestoreBackup 从备份恢复配置
func (h *AdminSystemConfigHandler) RestoreBackup(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误"})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 恢复备份
	if err := h.systemConfigService.RestoreFromBackup(c.Request.Context(), id, operatorID); err != nil {
		h.logger.Error("恢复配置备份失败", zap.Error(err), zap.String("id", id), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "恢复配置备份失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"message": "配置备份恢复成功"}})
}

// DeleteBackup 删除配置备份
func (h *AdminSystemConfigHandler) DeleteBackup(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误"})
		return
	}

	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 删除备份
	if err := h.systemConfigService.DeleteBackup(c.Request.Context(), id, operatorID); err != nil {
		h.logger.Error("删除配置备份失败", zap.Error(err), zap.String("id", id), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "删除配置备份失败", "error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"message": "配置备份删除成功"}})
}

// RefreshCache 刷新配置缓存
func (h *AdminSystemConfigHandler) RefreshCache(c *gin.Context) {
	// 获取操作员ID
	operatorID := middleware.GetUserID(c)

	// 刷新缓存
	if err := h.systemConfigService.RefreshCache(); err != nil {
		h.logger.Error("刷新配置缓存失败", zap.Error(err), zap.String("operator", operatorID))
		c.JSON(http.StatusInternalServerError, gin.H{"success": false, "message": "刷新配置缓存失败", "error": err.Error()})
		return
	}

	h.logger.Info("配置缓存刷新成功", zap.String("operator", operatorID))
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"message": "配置缓存刷新成功"}})
}

// ValidateConfig 验证配置
func (h *AdminSystemConfigHandler) ValidateConfig(c *gin.Context) {
	var req model.SystemConfig

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "参数错误", "error": err.Error()})
		return
	}

	// 验证配置
	if err := h.systemConfigService.ValidateConfig(c.Request.Context(), &req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "配置验证失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "操作成功", "data": gin.H{"message": "配置验证通过"}})
}
