package pool

import (
	"context"
	"runtime"
	"sync"
	"time"
)

// Task 任务接口
type Task interface {
	Execute(ctx context.Context) error
}

// TaskFunc 任务函数类型
type TaskFunc func(ctx context.Context) error

// Execute 实现Task接口
func (f TaskFunc) Execute(ctx context.Context) error {
	return f(ctx)
}

// WorkerPool 工作池
type WorkerPool struct {
	workerCount int
	taskQueue   chan Task
	wg          sync.WaitGroup
	ctx         context.Context
	cancel      context.CancelFunc
	started     bool
	mu          sync.RWMutex
}

// NewWorkerPool 创建工作池
func NewWorkerPool(workerCount int, queueSize int) *WorkerPool {
	if workerCount <= 0 {
		workerCount = runtime.NumCPU() * 2 // 默认为CPU核心数的2倍
	}
	if queueSize <= 0 {
		queueSize = workerCount * 10 // 默认为工作者数量的10倍
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &WorkerPool{
		workerCount: workerCount,
		taskQueue:   make(chan Task, queueSize),
		ctx:         ctx,
		cancel:      cancel,
	}
}

// Start 启动工作池
func (p *WorkerPool) Start() {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.started {
		return
	}

	p.started = true

	// 启动工作者
	for i := 0; i < p.workerCount; i++ {
		p.wg.Add(1)
		go p.worker(i)
	}
}

// Stop 停止工作池
func (p *WorkerPool) Stop() {
	p.mu.Lock()
	defer p.mu.Unlock()

	if !p.started {
		return
	}

	// 取消上下文
	p.cancel()

	// 关闭任务队列
	close(p.taskQueue)

	// 等待所有工作者完成
	p.wg.Wait()

	p.started = false
}

// Submit 提交任务
func (p *WorkerPool) Submit(task Task) error {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if !p.started {
		return ErrPoolNotStarted
	}

	select {
	case p.taskQueue <- task:
		return nil
	case <-p.ctx.Done():
		return ErrPoolStopped
	default:
		return ErrQueueFull
	}
}

// SubmitWithTimeout 带超时的任务提交
func (p *WorkerPool) SubmitWithTimeout(task Task, timeout time.Duration) error {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if !p.started {
		return ErrPoolNotStarted
	}

	ctx, cancel := context.WithTimeout(p.ctx, timeout)
	defer cancel()

	select {
	case p.taskQueue <- task:
		return nil
	case <-ctx.Done():
		if ctx.Err() == context.DeadlineExceeded {
			return ErrSubmitTimeout
		}
		return ErrPoolStopped
	}
}

// worker 工作者
func (p *WorkerPool) worker(id int) {
	defer p.wg.Done()

	for {
		select {
		case task, ok := <-p.taskQueue:
			if !ok {
				// 任务队列已关闭
				return
			}

			// 执行任务
			if err := task.Execute(p.ctx); err != nil {
				// 可以在这里添加错误处理逻辑
				// 例如记录日志、发送监控指标等
			}

		case <-p.ctx.Done():
			// 上下文已取消
			return
		}
	}
}

// GetStats 获取工作池统计信息
func (p *WorkerPool) GetStats() PoolStats {
	p.mu.RLock()
	defer p.mu.RUnlock()

	return PoolStats{
		WorkerCount: p.workerCount,
		QueueSize:   cap(p.taskQueue),
		QueueLength: len(p.taskQueue),
		IsStarted:   p.started,
	}
}

// PoolStats 工作池统计信息
type PoolStats struct {
	WorkerCount int  `json:"worker_count"`
	QueueSize   int  `json:"queue_size"`
	QueueLength int  `json:"queue_length"`
	IsStarted   bool `json:"is_started"`
}

// 错误定义
var (
	ErrPoolNotStarted = &PoolError{Code: "POOL_NOT_STARTED", Message: "工作池未启动"}
	ErrPoolStopped    = &PoolError{Code: "POOL_STOPPED", Message: "工作池已停止"}
	ErrQueueFull      = &PoolError{Code: "QUEUE_FULL", Message: "任务队列已满"}
	ErrSubmitTimeout  = &PoolError{Code: "SUBMIT_TIMEOUT", Message: "任务提交超时"}
)

// PoolError 工作池错误
type PoolError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (e *PoolError) Error() string {
	return e.Message
}

// PriceQueryTask 价格查询任务
type PriceQueryTask struct {
	Provider   string
	Request    interface{}
	ResultChan chan<- TaskResult
}

// TaskResult 任务结果
type TaskResult struct {
	Provider string
	Data     interface{}
	Error    error
}

// Execute 执行价格查询任务
func (t *PriceQueryTask) Execute(ctx context.Context) error {
	// 这里会在PriceService中实现具体的查询逻辑
	// 现在只是一个占位符
	result := TaskResult{
		Provider: t.Provider,
		Data:     nil,
		Error:    nil,
	}

	select {
	case t.ResultChan <- result:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// 🚀 新增：高级Goroutine池管理器

// AdvancedWorkerPool 高级工作池
type AdvancedWorkerPool struct {
	*WorkerPool

	// 动态调整
	minWorkers     int
	maxWorkers     int
	currentWorkers int

	// 性能监控
	stats *AdvancedPoolStats

	// 自动调优
	autoScaling   bool
	scaleInterval time.Duration

	// 控制通道
	scaleChan chan int

	mutex sync.RWMutex
}

// AdvancedPoolStats 高级池统计信息
type AdvancedPoolStats struct {
	TasksSubmitted  int64
	TasksCompleted  int64
	TasksFailed     int64
	AverageWaitTime time.Duration
	AverageExecTime time.Duration
	QueueLength     int
	ActiveWorkers   int
	IdleWorkers     int

	mutex sync.RWMutex
}

// NewAdvancedWorkerPool 创建高级工作池
func NewAdvancedWorkerPool(minWorkers, maxWorkers, queueSize int) *AdvancedWorkerPool {
	if minWorkers <= 0 {
		minWorkers = runtime.NumCPU()
	}
	if maxWorkers <= 0 {
		maxWorkers = runtime.NumCPU() * 4
	}
	if maxWorkers < minWorkers {
		maxWorkers = minWorkers
	}

	basePool := NewWorkerPool(minWorkers, queueSize)

	return &AdvancedWorkerPool{
		WorkerPool:     basePool,
		minWorkers:     minWorkers,
		maxWorkers:     maxWorkers,
		currentWorkers: minWorkers,
		stats:          &AdvancedPoolStats{},
		autoScaling:    true,
		scaleInterval:  30 * time.Second,
		scaleChan:      make(chan int, 10),
	}
}

// StartWithAutoScaling 启动带自动扩缩容的工作池
func (p *AdvancedWorkerPool) StartWithAutoScaling() {
	p.WorkerPool.Start()

	if p.autoScaling {
		go p.autoScaleWorkers()
	}
}

// autoScaleWorkers 自动调整工作者数量
func (p *AdvancedWorkerPool) autoScaleWorkers() {
	ticker := time.NewTicker(p.scaleInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.evaluateAndScale()

		case newSize := <-p.scaleChan:
			p.scaleToSize(newSize)

		case <-p.ctx.Done():
			return
		}
	}
}

// evaluateAndScale 评估并调整规模
func (p *AdvancedWorkerPool) evaluateAndScale() {
	p.stats.mutex.RLock()
	queueLength := p.stats.QueueLength
	avgWaitTime := p.stats.AverageWaitTime
	p.stats.mutex.RUnlock()

	p.mutex.RLock()
	currentWorkers := p.currentWorkers
	p.mutex.RUnlock()

	// 扩容条件：队列长度过长或等待时间过长
	if (queueLength > currentWorkers*2 || avgWaitTime > 100*time.Millisecond) &&
		currentWorkers < p.maxWorkers {
		newSize := min(currentWorkers+2, p.maxWorkers)
		p.scaleToSize(newSize)
		return
	}

	// 缩容条件：队列为空且等待时间很短
	if queueLength == 0 && avgWaitTime < 10*time.Millisecond &&
		currentWorkers > p.minWorkers {
		newSize := max(currentWorkers-1, p.minWorkers)
		p.scaleToSize(newSize)
	}
}

// scaleToSize 调整到指定大小
func (p *AdvancedWorkerPool) scaleToSize(newSize int) {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if newSize == p.currentWorkers {
		return
	}

	if newSize > p.currentWorkers {
		// 扩容：启动新的工作者
		for i := p.currentWorkers; i < newSize; i++ {
			p.wg.Add(1)
			go p.worker(i)
		}
	}
	// 缩容通过context取消实现，工作者会自然退出

	p.currentWorkers = newSize
}

// GetStats 获取统计信息
func (p *AdvancedWorkerPool) GetStats() *AdvancedPoolStats {
	p.stats.mutex.RLock()
	defer p.stats.mutex.RUnlock()

	return &AdvancedPoolStats{
		TasksSubmitted:  p.stats.TasksSubmitted,
		TasksCompleted:  p.stats.TasksCompleted,
		TasksFailed:     p.stats.TasksFailed,
		AverageWaitTime: p.stats.AverageWaitTime,
		AverageExecTime: p.stats.AverageExecTime,
		QueueLength:     len(p.taskQueue),
		ActiveWorkers:   p.currentWorkers,
		IdleWorkers:     p.currentWorkers - p.stats.QueueLength,
	}
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// DefaultWorkerPool 默认工作池
var DefaultWorkerPool *AdvancedWorkerPool

// InitDefaultWorkerPool 初始化默认工作池
func InitDefaultWorkerPool() {
	if DefaultWorkerPool == nil {
		DefaultWorkerPool = NewAdvancedWorkerPool(runtime.NumCPU(), runtime.NumCPU()*4, 1000)
		DefaultWorkerPool.StartWithAutoScaling()
	}
}

// StopDefaultWorkerPool 停止默认工作池
func StopDefaultWorkerPool() {
	if DefaultWorkerPool != nil {
		DefaultWorkerPool.Stop()
		DefaultWorkerPool = nil
	}
}
