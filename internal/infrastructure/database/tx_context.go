package database

import (
	"context"
	"database/sql"
)

// txKeyType 是 context 中存储事务的私有 key 类型，避免冲突。
// 不导出确保只能通过本包提供的函数访问。
// 任何需要在同一个事务内执行 SQL 的代码，
// 都可以通过 NewTxContext 注入，再通过 TxFromContext 获取。
//
//	ctx := database.NewTxContext(ctx, tx)
//	if tx := database.TxFromContext(ctx); tx != nil { ... }
type txKeyType struct{}

var txKey = txKeyType{}

// NewTxContext 返回带有 *sql.Tx 的新 context。
func NewTxContext(ctx context.Context, tx *sql.Tx) context.Context {
	if tx == nil {
		return ctx
	}
	return context.WithValue(ctx, txKey, tx)
}

// TxFromContext 尝试从 context 中提取 *sql.Tx；若不存在则返回 nil。
func TxFromContext(ctx context.Context) *sql.Tx {
	if ctx == nil {
		return nil
	}
	if v := ctx.Value(txKey); v != nil {
		if tx, ok := v.(*sql.Tx); ok {
			return tx
		}
	}
	return nil
}
