import api from '@/utils/http'
import { UnifiedGatewayService, type RealtimePriceRequest, type RealtimePriceResponse } from './unifiedGatewayApi'
import type {
  BaseResponse,
  SimpleOrderRequest,
  SimpleOrderResponse,
  CancelOrderRequest,
  CancelOrderResponse,
  QueryOrderRequest,
  QueryOrderResponse,
  QueryTrackRequest,
  QueryTrackResponse,
  OrderListRequest,
  OrderListResponse,
  OrderStatisticsResponse,
  SimplePriceRequest,
  SimplePriceResponse,
  GetStatusHistoryRequest,
  GetStatusHistoryResponse
} from './model/kuaidiModel'

/**
 * 快递订单相关API服务
 */
export class ExpressService {
  /**
   * 统一下单（使用统一网关）
   * @param params 下单参数
   * @returns 下单响应
   */
  static async createOrder(params: SimpleOrderRequest): Promise<BaseResponse<SimpleOrderResponse>> {
    // 🚀 使用统一网关接口
    return UnifiedGatewayService.createOrder(params)
  }

  /**
   * 取消订单（使用统一网关）
   * @param params 取消订单参数
   * @returns 取消结果
   */
  static async cancelOrder(params: CancelOrderRequest): Promise<BaseResponse<CancelOrderResponse>> {
    // 🚀 使用统一网关接口
    return UnifiedGatewayService.cancelOrder(params)
  }

  /**
   * 🔥 新增：删除失败订单
   * @param params 删除参数
   * @returns 删除结果
   */
  static async deleteFailedOrder(params: { order_no: string }): Promise<BaseResponse<any>> {
    return api.del<BaseResponse<any>>({
      url: '/api/v1/express/order/failed',
      params
    })
  }

  /**
   * 查询订单
   * @param params 查询参数
   * @returns 订单详情
   */
  static async queryOrder(params: QueryOrderRequest): Promise<BaseResponse<QueryOrderResponse>> {
    return api.post<BaseResponse<QueryOrderResponse>>({
      url: '/api/v1/express/order/query',
      params
    })
  }

  /**
   * 查询物流轨迹
   * @param params 查询参数
   * @returns 物流轨迹
   */
  static async queryTrack(params: QueryTrackRequest): Promise<BaseResponse<QueryTrackResponse>> {
    return api.post<BaseResponse<QueryTrackResponse>>({
      url: '/api/v1/express/track',
      params
    })
  }

  /**
   * 获取订单列表
   * @param params 查询参数
   * @returns 订单列表
   */
  static async getOrderList(params?: OrderListRequest): Promise<BaseResponse<OrderListResponse>> {
    // 构建查询参数，过滤空值，但保留数组类型
    const filteredParams = Object.fromEntries(
      Object.entries(params || {}).filter(
        ([_, value]) => {
          // 保留数组类型（即使是空数组）
          if (Array.isArray(value)) {
            return true
          }
          // 过滤其他空值
          return value !== undefined && value !== null && value !== ''
        }
      )
    )

    return api.get<BaseResponse<OrderListResponse>>({
      url: '/api/v1/express/orders',
      params: filteredParams
    })
  }

  /**
   * 获取订单统计
   * @param params 查询参数
   * @returns 订单统计
   */
  static async getOrderStatistics(params?: {
    start_time?: string
    end_time?: string
  }): Promise<BaseResponse<OrderStatisticsResponse>> {
    return api.get<BaseResponse<OrderStatisticsResponse>>({
      url: '/api/v1/express/orders/statistics',
      params
    })
  }

  /**
   * 统一价格查询接口（使用统一网关）
   * @param params 查价参数
   * @returns 价格列表
   */
  static async queryPrice(params: SimplePriceRequest): Promise<BaseResponse<SimplePriceResponse>> {
    // 🚀 使用统一网关接口
    return UnifiedGatewayService.queryPrice(params)
  }

  /**
   * 🚀 实时查价接口
   * @param params 实时查价参数
   * @returns 实时价格列表
   */
  static async queryRealtimePrice(params: RealtimePriceRequest): Promise<BaseResponse<RealtimePriceResponse>> {
    // 🚀 使用统一网关实时查价接口
    return UnifiedGatewayService.queryRealtimePrice(params)
  }

  /**
   * 获取订单状态历史
   * @param params 查询参数
   * @returns 状态历史列表
   */
  static async getOrderStatusHistory(params: GetStatusHistoryRequest): Promise<BaseResponse<GetStatusHistoryResponse>> {
    const { order_no, customer_order_no, ...otherParams } = params
    let url = '/api/v1/express/orders/status-history'

    if (order_no) {
      url = `/api/v1/express/orders/${order_no}/status-history`
    } else if (customer_order_no) {
      url = `/api/v1/express/orders/status-history/${customer_order_no}`
    }

    return api.get<BaseResponse<GetStatusHistoryResponse>>({
      url,
      params: otherParams
    })
  }

  /**
   * 获取用户所有订单的状态历史
   * @param params 查询参数
   * @returns 状态历史列表
   */
  static async getUserOrdersStatusHistory(params?: { page?: number; limit?: number }): Promise<BaseResponse<GetStatusHistoryResponse>> {
    return api.get<BaseResponse<GetStatusHistoryResponse>>({
      url: '/api/v1/express/orders/status-history',
      params
    })
  }
}
