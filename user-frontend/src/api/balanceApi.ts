import api from '@/utils/http'
import type {
  BaseResponse,
  BalanceResponse,
  DepositRequest,
  PaymentRequest,
  RefundRequest,
  TransactionResponse,
  TransactionHistoryRequest,
  TransactionHistoryResponse
} from './model/kuaidiModel'

/**
 * 余额管理相关API服务
 */
export class BalanceService {
  /**
   * 获取用户余额
   * @returns 余额信息
   */
  static async getBalance(): Promise<BalanceResponse> {
    return api.get<BalanceResponse>({
      url: '/api/v1/balance'
    })
  }

  /**
   * 用户充值
   * @param params 充值参数
   * @returns 交易响应
   */
  static async deposit(params: DepositRequest): Promise<TransactionResponse> {
    return api.post<TransactionResponse>({
      url: '/api/v1/balance/deposit',
      params
    })
  }

  /**
   * 余额支付
   * @param params 支付参数
   * @returns 交易响应
   */
  static async payment(params: PaymentRequest): Promise<TransactionResponse> {
    return api.post<TransactionResponse>({
      url: '/api/v1/balance/payment',
      params
    })
  }

  /**
   * 用户退款
   * @param params 退款参数
   * @returns 交易响应
   */
  static async refund(params: RefundRequest): Promise<TransactionResponse> {
    return api.post<TransactionResponse>({
      url: '/api/v1/balance/refund',
      params
    })
  }

  /**
   * 获取交易历史
   * @param params 查询参数
   * @returns 交易历史
   */
  static async getTransactionHistory(
    params?: TransactionHistoryRequest
  ): Promise<TransactionHistoryResponse> {
    return api.get<TransactionHistoryResponse>({
      url: '/api/v1/balance/transactions',
      params
    })
  }

  /**
   * 获取交易历史（优化版）- 高性能接口
   * @param params 查询参数
   * @returns 交易历史
   */
  static async getTransactionHistoryOptimized(
    params?: TransactionHistoryRequest
  ): Promise<TransactionHistoryResponse> {
    return api.get<TransactionHistoryResponse>({
      url: '/api/v1/balance/transactions/optimized',
      params
    })
  }

  /**
   * 清除缓存（优化版）
   * @returns 操作结果
   */
  static async clearCache(): Promise<BaseResponse> {
    return api.post<BaseResponse>({
      url: '/api/v1/balance/cache/clear'
    })
  }

  /**
   * 健康检查（优化版）
   * @returns 健康状态
   */
  static async healthCheck(): Promise<BaseResponse> {
    return api.get<BaseResponse>({
      url: '/api/v1/balance/health'
    })
  }
}
