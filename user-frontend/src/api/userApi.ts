import api from '@/utils/http'
import type {
  BaseResponse,
  UserRegisterRequest,
  UserRegisterResponse,
  UserProfile,
  UpdateCallbackUrlRequest,
  ResetClientSecretResponse
} from './model/kuaidiModel'

/**
 * 用户管理相关API服务
 */
export class UserApiService {
  /**
   * 用户注册
   * @param params 注册参数
   * @returns 注册响应
   */
  static async register(params: UserRegisterRequest): Promise<BaseResponse<UserRegisterResponse>> {
    return api.post<BaseResponse<UserRegisterResponse>>({
      url: '/api/v1/users/register',
      params
    })
  }

  /**
   * 获取用户资料
   * @returns 用户资料
   */
  static async getProfile(): Promise<BaseResponse<UserProfile>> {
    return api.get<BaseResponse<UserProfile>>({
      url: '/api/v1/users/profile'
    })
  }

  /**
   * 更新回调URL
   * @param params 回调URL参数
   * @returns 更新结果
   */
  static async updateCallbackUrl(params: UpdateCallbackUrlRequest): Promise<BaseResponse> {
    return api.put<BaseResponse>({
      url: '/api/v1/users/callback-url',
      params
    })
  }

  /**
   * 重置客户端密钥
   * @returns 新的客户端密钥
   */
  static async resetClientSecret(): Promise<BaseResponse<ResetClientSecretResponse>> {
    return api.post<BaseResponse<ResetClientSecretResponse>>({
      url: '/api/v1/users/reset-client-secret'
    })
  }
}
