/**
 * 工单管理API服务
 * 基于后端 go-kuaidi/api/handler/workorder_handler.go 接口定义
 */

import api from '@/utils/http'
import type {
  WorkOrder,
  WorkOrderListRequest,
  WorkOrderListResponse,
  CreateWorkOrderRequest,
  SmartCreateWorkOrderRequest,
  ReplyWorkOrderRequest,
  UploadAttachmentResponse,
  WorkOrderTypeMapping,
  WorkOrderResponse,
  WorkOrderStatistics,
  WorkOrderTrend,
  WorkOrderTypeStatistics
} from './model/workOrderModel'

/**
 * 工单管理API服务类
 */
export class WorkOrderService {
  /**
   * 创建工单（统一使用智能创建方式）
   * @param params 智能创建工单请求参数
   * @returns 创建的工单信息
   */
  static async createWorkOrder(
    params: SmartCreateWorkOrderRequest
  ): Promise<WorkOrderResponse<WorkOrder>> {
    return api.post<WorkOrderResponse<WorkOrder>>({
      url: '/api/v1/workorders',
      params
    })
  }

  /**
   * 获取工单详情
   * @param id 工单ID
   * @returns 工单详情
   */
  static async getWorkOrder(id: string): Promise<WorkOrderResponse<WorkOrder>> {
    return api.get<WorkOrderResponse<WorkOrder>>({
      url: `/api/v1/workorders/${id}`
    })
  }

  /**
   * 获取工单列表
   * @param params 查询参数
   * @returns 工单列表
   */
  static async getWorkOrderList(
    params?: WorkOrderListRequest
  ): Promise<WorkOrderResponse<WorkOrderListResponse>> {
    // 构建查询参数，过滤空值
    const filteredParams = Object.fromEntries(
      Object.entries(params || {}).filter(
        ([_, value]) => value !== undefined && value !== null && value !== ''
      )
    )

    return api.get<WorkOrderResponse<WorkOrderListResponse>>({
      url: '/api/v1/workorders',
      params: filteredParams
    })
  }

  /**
   * 回复工单
   * @param id 工单ID
   * @param params 回复内容
   * @returns 回复结果
   */
  static async replyWorkOrder(
    id: string,
    params: ReplyWorkOrderRequest
  ): Promise<WorkOrderResponse> {
    return api.post<WorkOrderResponse>({
      url: `/api/v1/workorders/${id}/replies`,
      params
    })
  }

  /**
   * 删除工单
   * @param id 工单ID
   * @returns 删除结果
   */
  static async deleteWorkOrder(id: string): Promise<WorkOrderResponse> {
    return api.del<WorkOrderResponse>({
      url: `/api/v1/workorders/${id}`
    })
  }

  /**
   * 上传附件
   * @param file 文件对象
   * @returns 上传结果
   */
  static async uploadAttachment(file: File): Promise<WorkOrderResponse<UploadAttachmentResponse>> {
    const formData = new FormData()
    formData.append('file', file)

    return api.post<WorkOrderResponse<UploadAttachmentResponse>>({
      url: '/api/v1/workorders/attachments',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取支持的工单类型
   * @param provider 供应商名称（可选）
   * @returns 支持的工单类型列表
   */
  static async getSupportedTypes(
    provider?: string
  ): Promise<WorkOrderResponse<WorkOrderTypeMapping[]>> {
    const params = provider ? { provider } : {}

    return api.get<WorkOrderResponse<WorkOrderTypeMapping[]>>({
      url: '/api/v1/workorders/types',
      params
    })
  }

  /**
   * 获取工单统计数据
   * @param params 查询参数
   * @returns 统计数据
   */
  static async getWorkOrderStatistics(params?: {
    start_date?: string
    end_date?: string
    provider?: string
  }): Promise<WorkOrderResponse<WorkOrderStatistics>> {
    return api.get<WorkOrderResponse<WorkOrderStatistics>>({
      url: '/api/v1/workorders/statistics',
      params
    })
  }

  /**
   * 获取工单趋势数据
   * @param params 查询参数
   * @returns 趋势数据
   */
  static async getWorkOrderTrend(params?: {
    start_date?: string
    end_date?: string
    provider?: string
  }): Promise<WorkOrderResponse<WorkOrderTrend[]>> {
    return api.get<WorkOrderResponse<WorkOrderTrend[]>>({
      url: '/api/v1/workorders/trend',
      params
    })
  }

  /**
   * 获取工单类型统计
   * @param params 查询参数
   * @returns 类型统计数据
   */
  static async getWorkOrderTypeStatistics(params?: {
    start_date?: string
    end_date?: string
    provider?: string
  }): Promise<WorkOrderResponse<WorkOrderTypeStatistics[]>> {
    return api.get<WorkOrderResponse<WorkOrderTypeStatistics[]>>({
      url: '/api/v1/workorders/type-statistics',
      params
    })
  }

  /**
   * 批量上传附件
   * @param files 文件列表
   * @returns 上传结果列表
   */
  static async uploadMultipleAttachments(files: File[]): Promise<UploadAttachmentResponse[]> {
    const uploadPromises = files.map((file) => this.uploadAttachment(file))
    const results = await Promise.allSettled(uploadPromises)

    return results
      .filter(
        (result): result is PromiseFulfilledResult<WorkOrderResponse<UploadAttachmentResponse>> =>
          result.status === 'fulfilled' && result.value.success
      )
      .map((result) => result.value.data!)
  }

  /**
   * 获取工单详情（包含完整的回复和附件信息）
   * @param id 工单ID
   * @returns 完整的工单详情
   */
  static async getWorkOrderDetail(id: string): Promise<WorkOrder> {
    const response = await this.getWorkOrder(id)
    if (!response.success || !response.data) {
      throw new Error(response.message || '获取工单详情失败')
    }
    return response.data
  }

  /**
   * 检查工单是否可以回复
   * @param workOrder 工单对象
   * @returns 是否可以回复
   */
  static canReplyWorkOrder(workOrder: WorkOrder): boolean {
    // 只有待处理、处理中、已回复状态的工单可以回复
    return [1, 2, 3].includes(workOrder.status) // PENDING, PROCESSING, REPLIED
  }

  /**
   * 检查工单是否可以编辑
   * @param workOrder 工单对象
   * @returns 是否可以编辑
   */
  static canEditWorkOrder(workOrder: WorkOrder): boolean {
    // 只有待处理状态的工单可以编辑
    return workOrder.status === 1 // PENDING
  }

  /**
   * 检查工单是否可以删除
   * @param workOrder 工单对象
   * @returns 是否可以删除及原因
   */
  static canDeleteWorkOrder(workOrder: WorkOrder): { canDelete: boolean; reason?: string } {
    // 1. 状态检查：只能删除待处理、处理中状态的工单
    if (![1, 2].includes(workOrder.status)) {
      // PENDING, PROCESSING
      const statusName = this.formatWorkOrderStatus(workOrder.status).text
      return { canDelete: false, reason: `工单状态不允许删除，当前状态: ${statusName}` }
    }

    // 2. 时间限制检查：创建后24小时内才能删除
    const createdTime = new Date(workOrder.created_at).getTime()
    const currentTime = new Date().getTime()
    const hoursDiff = (currentTime - createdTime) / (1000 * 60 * 60)

    if (hoursDiff > 24) {
      return { canDelete: false, reason: '工单创建超过24小时，无法删除' }
    }

    return { canDelete: true }
  }

  /**
   * 格式化工单状态显示
   * @param status 状态值
   * @returns 状态显示文本和样式类
   */
  static formatWorkOrderStatus(status: number): { text: string; type: string } {
    const statusMap: Record<number, { text: string; type: string }> = {
      1: { text: '待处理', type: 'warning' },
      2: { text: '处理中', type: 'primary' },
      3: { text: '已回复', type: 'success' },
      4: { text: '已完结', type: 'info' }
    }
    return statusMap[status] || { text: '未知状态', type: 'danger' }
  }

  /**
   * 格式化工单优先级显示
   * @param priority 优先级值
   * @returns 优先级显示文本和样式类
   */
  static formatWorkOrderPriority(priority: number): { text: string; type: string } {
    const priorityMap: Record<number, { text: string; type: string }> = {
      1: { text: '高', type: 'danger' },
      2: { text: '中', type: 'warning' },
      3: { text: '低', type: 'success' }
    }
    return priorityMap[priority] || { text: '未知', type: 'info' }
  }

  /**
   * 获取工单类型显示名称（统一化改造：调整为6种核心类型）
   * @deprecated 请使用 useWorkOrderTypes composable 中的 getTypeName 方法
   * @param type 工单类型值
   * @returns 类型显示名称
   */
  static getWorkOrderTypeName(type: number): string {
    // 🔥 保留此方法用于向后兼容，但建议使用 useWorkOrderTypes
    console.warn(
      'getWorkOrderTypeName is deprecated. Please use useWorkOrderTypes composable instead.'
    )

    // 使用统一化改造后的6种核心类型作为备选
    const typeMap: Record<number, string> = {
      1: '催取件',
      2: '重量异常',
      12: '催派送',
      16: '物流停滞',
      17: '重新分配快递员',
      19: '取消订单'
    }
    return typeMap[type] || '未知类型'
  }
}
