import api from '@/utils/http'
import type {
  AddressParseParams,
  BatchParseAddressParams,
  ValidateAddressParams,
  SearchAreaParams,
  ParseHistoryParams,
  AddressInfo,
  ContactInfo
} from './model/addressModel'

/**
 * 地址解析相关API
 */
export const addressAPI = {
  /**
   * 解析单个地址
   * @param {Object} data - 地址解析请求数据
   * @param {string} data.text - 地址文本
   * @param {number} data.lat - 纬度
   * @param {number} data.lng - 经度
   * @param {number} data.addressType - 地址类型
   * @param {number} data.queryType - 查询类型
   * @returns {Promise} 解析结果
   */
  parseAddress(data: AddressParseParams) {
    return api.post({
      url: '/api/v1/address/parse',
      params: {
        text: data.text,
        addressType: data.addressType || 0,
        queryType: data.queryType || 0,
        lat: data.lat || 30.0,
        lng: data.lng || 110.0
      }
    })
  },

  /**
   * 批量解析地址
   * @param {Object} data - 批量解析请求数据
   * @param {Array} data.addresses - 地址列表
   * @param {number} data.concurrency - 并发数
   * @returns {Promise} 批量解析结果
   */
  batchParseAddress(data: BatchParseAddressParams) {
    return api.post({
      url: '/api/v1/address/batch-parse',
      params: {
        addresses: data.addresses.map((addr) => ({
          text: addr.text,
          addressType: addr.addressType || 0,
          queryType: addr.queryType || 0,
          lat: addr.lat || 30.0,
          lng: addr.lng || 110.0
        })),
        concurrency: data.concurrency || 5
      }
    })
  },

  /**
   * 验证地址
   * @param {Object} data - 地址验证请求数据
   * @param {string} data.provinceCode - 省份代码
   * @param {string} data.cityCode - 城市代码
   * @param {string} data.districtCode - 区县代码
   * @param {string} data.detailAddress - 详细地址
   * @returns {Promise} 验证结果
   */
  validateAddress(data: ValidateAddressParams) {
    return api.post({
      url: '/api/v1/address/validate',
      params: {
        provinceCode: data.provinceCode,
        cityCode: data.cityCode,
        districtCode: data.districtCode,
        detailAddress: data.detailAddress
      }
    })
  },

  /**
   * 获取地区级联数据
   * @returns {Promise} 地区数据
   */
  getAreaCascader() {
    return api.get({
      url: '/api/v1/address/area-cascader'
    })
  },

  /**
   * 搜索地区
   * @param {Object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.limit - 限制数量
   * @returns {Promise} 搜索结果
   */
  searchAreas(params: SearchAreaParams) {
    return api.get({
      url: '/api/v1/address/search-areas',
      params: {
        keyword: params.keyword,
        limit: params.limit || 10
      }
    })
  },

  /**
   * 获取解析历史
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @returns {Promise} 历史记录
   */
  getParseHistory(params: ParseHistoryParams) {
    return api.get({
      url: '/api/v1/address/history',
      params: {
        page: params.page || 1,
        size: params.size || 20
      }
    })
  }
}

/**
 * 地址工具函数
 */
export const addressUtils = {
  /**
   * 格式化地址文本
   * @param {Object} addressInfo - 地址信息
   * @returns {string} 格式化后的地址
   */
  formatAddress(addressInfo?: Partial<AddressInfo>): string {
    if (!addressInfo) return ''

    const parts = []
    if (addressInfo.provinceName) parts.push(addressInfo.provinceName)
    if (addressInfo.cityName) parts.push(addressInfo.cityName)
    if (addressInfo.districtName) parts.push(addressInfo.districtName)
    if (addressInfo.townName) parts.push(addressInfo.townName)
    if (addressInfo.detailAddress) parts.push(addressInfo.detailAddress)

    return parts.join('')
  },

  /**
   * 提取联系信息
   * @param {Object} addressInfo - 地址信息
   * @returns {Object} 联系信息
   */
  extractContactInfo(addressInfo?: Partial<AddressInfo>): ContactInfo {
    if (!addressInfo) return { name: '', mobile: '', phone: '' }

    return {
      name: addressInfo.name || '',
      mobile: addressInfo.mobile || '',
      phone: addressInfo.phone || ''
    }
  },

  /**
   * 提取地址信息
   * @param {Object} addressInfo - 地址信息
   * @returns {Object} 地址信息
   */
  extractAddressInfo(addressInfo?: Partial<AddressInfo>): Partial<AddressInfo> {
    if (!addressInfo) return {}

    return {
      provinceCode: addressInfo.provinceCode || '',
      provinceName: addressInfo.provinceName || '',
      cityCode: addressInfo.cityCode || '',
      cityName: addressInfo.cityName || '',
      districtCode: addressInfo.districtCode || '',
      districtName: addressInfo.districtName || '',
      townCode: addressInfo.townCode || '',
      townName: addressInfo.townName || '',
      detailAddress: addressInfo.detailAddress || ''
    }
  },

  /**
   * 验证手机号格式
   * @param {string} mobile - 手机号
   * @returns {boolean} 是否有效
   */
  validateMobile(mobile: string): boolean {
    if (!mobile) return false
    const mobileRegex = /^1[3-9]\d{9}$/
    return mobileRegex.test(mobile)
  },

  /**
   * 验证电话号码格式
   * @param {string} phone - 电话号码
   * @returns {boolean} 是否有效
   */
  validatePhone(phone: string): boolean {
    if (!phone) return false
    const phoneRegex = /^(\d{3,4}-?)?\d{7,8}$/
    return phoneRegex.test(phone)
  },

  /**
   * 清理地址文本
   * @param {string} text - 原始文本
   * @returns {string} 清理后的文本
   */
  cleanAddressText(text: string): string {
    if (!text) return ''

    return text
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/[\r\n]+/g, '\n') // 合并多个换行
      .trim()
  },

  /**
   * 解析地址文本中的联系信息
   * @param {string} text - 地址文本
   * @returns {Object} 解析结果
   */
  parseContactFromText(text: string): ContactInfo {
    if (!text) return {}

    const result: ContactInfo = {}

    // 提取手机号
    const mobileMatch = text.match(/1[3-9]\d{9}/)
    if (mobileMatch) {
      result.mobile = mobileMatch[0]
    }

    // 提取固定电话
    const phoneMatch = text.match(/(\d{3,4}-?)?\d{7,8}/)
    if (phoneMatch && !result.mobile) {
      result.phone = phoneMatch[0]
    }

    // 提取姓名（简单规则：中文字符，2-4个字）
    const nameMatch = text.match(/[\u4e00-\u9fa5]{2,4}/)
    if (nameMatch) {
      result.name = nameMatch[0]
    }

    return result
  },

  /**
   * 计算地址相似度
   * @param {string} addr1 - 地址1
   * @param {string} addr2 - 地址2
   * @returns {number} 相似度 (0-1)
   */
  calculateAddressSimilarity(addr1: string, addr2: string): number {
    if (!addr1 || !addr2) return 0

    const str1 = addr1.toLowerCase()
    const str2 = addr2.toLowerCase()

    if (str1 === str2) return 1

    // 简单的编辑距离算法
    const matrix = []
    const len1 = str1.length
    const len2 = str2.length

    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        if (str1.charAt(i - 1) === str2.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }

    const maxLen = Math.max(len1, len2)
    return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen
  },

  /**
   * 生成地址摘要
   * @param {Object} addressInfo - 地址信息
   * @returns {string} 地址摘要
   */
  generateAddressSummary(addressInfo?: Partial<AddressInfo>): string {
    if (!addressInfo) return ''

    const parts = []

    // 添加联系信息
    if (addressInfo.name) parts.push(`👤 ${addressInfo.name}`)
    if (addressInfo.mobile) parts.push(`📱 ${addressInfo.mobile}`)

    // 添加地址信息
    const address = this.formatAddress(addressInfo)
    if (address) parts.push(`📍 ${address}`)

    return parts.join(' | ')
  }
}

export default addressAPI
