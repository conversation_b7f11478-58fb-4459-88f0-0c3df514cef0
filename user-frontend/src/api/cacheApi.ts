import api from '@/utils/http'
import type {
  BaseResponse,
  WarmupRequest,
  WarmupStatusResponse,
  WarmupHistoryResponse,
  WarmupConfigResponse,
  QuickWarmupRequest,
  HealthCheckResponse
} from './model/kuaidiModel'

/**
 * 缓存管理相关API服务
 */
export class CacheService {
  /**
   * 启动缓存预热
   * @param params 预热参数
   * @returns 启动结果
   */
  static async startWarmup(params: WarmupRequest): Promise<
    BaseResponse<{
      strategy: string
      status: string
    }>
  > {
    return api.post<
      BaseResponse<{
        strategy: string
        status: string
      }>
    >({
      url: '/api/v1/cache/warmup/start',
      params
    })
  }

  /**
   * 停止缓存预热
   * @returns 停止结果
   */
  static async stopWarmup(): Promise<
    BaseResponse<{
      status: string
    }>
  > {
    return api.post<
      BaseResponse<{
        status: string
      }>
    >({
      url: '/api/v1/cache/warmup/stop'
    })
  }

  /**
   * 获取缓存预热状态
   * @returns 预热状态
   */
  static async getWarmupStatus(): Promise<BaseResponse<WarmupStatusResponse>> {
    return api.get<BaseResponse<WarmupStatusResponse>>({
      url: '/api/v1/cache/warmup/status'
    })
  }

  /**
   * 获取缓存预热历史
   * @param limit 返回记录数量
   * @returns 预热历史
   */
  static async getWarmupHistory(limit?: number): Promise<BaseResponse<WarmupHistoryResponse>> {
    return api.get<BaseResponse<WarmupHistoryResponse>>({
      url: '/api/v1/cache/warmup/history',
      params: limit ? { limit } : undefined
    })
  }

  /**
   * 获取缓存预热配置
   * @returns 预热配置
   */
  static async getWarmupConfig(): Promise<BaseResponse<WarmupConfigResponse>> {
    return api.get<BaseResponse<WarmupConfigResponse>>({
      url: '/api/v1/cache/warmup/config'
    })
  }

  /**
   * 快速启动缓存预热
   * @param params 快速预热参数
   * @returns 启动结果
   */
  static async quickWarmup(params: QuickWarmupRequest): Promise<
    BaseResponse<{
      strategy: string
      status: string
    }>
  > {
    return api.post<
      BaseResponse<{
        strategy: string
        status: string
      }>
    >({
      url: '/api/v1/cache/warmup/quick',
      params
    })
  }
}

/**
 * 健康检查相关API服务
 */
export class HealthService {
  /**
   * 健康检查
   * @returns 健康状态
   */
  static async checkHealth(): Promise<HealthCheckResponse> {
    return api.get<HealthCheckResponse>({
      url: '/health'
    })
  }
}
