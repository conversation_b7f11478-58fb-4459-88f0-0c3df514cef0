import api from '@/utils/http'
import type {
  BaseResponse,
  CallbackRecord,
  CallbackConfig,
  CallbackStatistics,
  CallbackListRequest,
  CallbackListResponse,
  UpdateCallbackConfigRequest,
  ApiKeyInfo,
  ResetClientSecretResponse
} from './model/kuaidiModel'

/**
 * 回调管理相关API服务
 */
export class CallbackService {
  /**
   * 获取用户回调记录列表
   * @param params 查询参数
   * @returns 回调记录列表
   */
  static async getCallbackRecords(
    params: CallbackListRequest
  ): Promise<BaseResponse<CallbackListResponse>> {
    return api.get<BaseResponse<CallbackListResponse>>({
      url: '/api/v1/user/callback/records',
      params
    })
  }

  /**
   * 获取回调记录详情
   * @param recordId 记录ID
   * @returns 回调记录详情
   */
  static async getCallbackRecord(recordId: string): Promise<BaseResponse<CallbackRecord>> {
    return api.get<BaseResponse<CallbackRecord>>({
      url: `/api/v1/user/callback/records/${recordId}`
    })
  }

  /**
   * 获取回调配置
   * @returns 回调配置
   */
  static async getCallbackConfig(): Promise<BaseResponse<CallbackConfig>> {
    return api.get<BaseResponse<CallbackConfig>>({
      url: '/api/v1/user/callback/config'
    })
  }

  /**
   * 更新回调配置
   * @param params 配置参数
   * @returns 更新结果
   */
  static async updateCallbackConfig(params: UpdateCallbackConfigRequest): Promise<BaseResponse> {
    return api.post<BaseResponse>({
      url: '/api/v1/user/callback/config',
      params
    })
  }

  /**
   * 获取回调统计
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 回调统计
   */
  static async getCallbackStatistics(
    startTime?: string,
    endTime?: string
  ): Promise<BaseResponse<CallbackStatistics>> {
    return api.get<BaseResponse<CallbackStatistics>>({
      url: '/api/v1/user/callback/statistics',
      params: {
        start_time: startTime,
        end_time: endTime
      }
    })
  }

  /**
   * 测试回调
   * @returns 测试结果
   */
  static async testCallback(): Promise<BaseResponse> {
    return api.post<BaseResponse>({
      url: '/api/v1/user/callback/test'
    })
  }

  /**
   * 重推单个回调记录
   * @param recordId 回调记录ID
   * @returns 重推结果
   */
  static async retryCallback(recordId: string): Promise<BaseResponse> {
    return api.post<BaseResponse>({
      url: `/api/v1/user/callback/retry/${recordId}`
    })
  }

  /**
   * 批量重推回调记录
   * @param recordIds 回调记录ID数组
   * @returns 批量重推结果
   */
  static async batchRetryCallbacks(recordIds: string[]): Promise<BaseResponse> {
    return api.post<BaseResponse>({
      url: '/api/v1/user/callback/batch-retry',
      params: {
        record_ids: recordIds
      }
    })
  }

  // ============ API密钥管理 ============

  /**
   * 获取API密钥信息
   * @returns API密钥信息
   */
  static async getApiKeyInfo(): Promise<BaseResponse<ApiKeyInfo>> {
    return api.get<BaseResponse<ApiKeyInfo>>({
      url: '/api/v1/users/profile'
    })
  }

  /**
   * 重置API密钥
   * @returns 新的密钥信息
   */
  static async resetApiKey(): Promise<BaseResponse<ResetClientSecretResponse>> {
    return api.post<BaseResponse<ResetClientSecretResponse>>({
      url: '/api/v1/users/reset-client-secret'
    })
  }
}
