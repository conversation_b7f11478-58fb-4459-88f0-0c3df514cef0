import api from '@/utils/http'
import type { BaseResponse } from './model/kuaidiModel'

/**
 * 系统配置项
 */
export interface SystemConfig {
  express_companies: ExpressCompany[]
  order_statuses: OrderStatus[]
  providers: Provider[]
  payment_methods: PaymentMethod[]
  regions: Region[]
}

/**
 * 快递公司配置
 */
export interface ExpressCompany {
  code: string
  name: string
  logo?: string
  is_active: boolean
  supported_services: string[]
  weight_limit: number
  size_limit?: {
    length: number
    width: number
    height: number
  }
}

/**
 * 订单状态配置
 */
export interface OrderStatus {
  code: string
  name: string
  description: string
  color: string
  icon?: string
  is_final: boolean
}

/**
 * 供应商配置
 */
export interface Provider {
  code: string
  name: string
  is_active: boolean
  supported_companies: string[]
  api_version: string
}

/**
 * 支付方式配置
 */
export interface PaymentMethod {
  code: number
  name: string
  description: string
  is_active: boolean
}

/**
 * 地区配置
 */
export interface Region {
  code: string
  name: string
  level: number
  parent_code?: string
  children?: Region[]
}

/**
 * 配置服务类
 */
export class ConfigService {
  private static configCache: SystemConfig | null = null
  private static cacheExpiry: number = 0
  private static readonly CACHE_DURATION = 30 * 60 * 1000 // 30分钟缓存

  /**
   * 获取系统配置
   * @param forceRefresh 是否强制刷新缓存
   * @returns 系统配置
   */
  static async getSystemConfig(forceRefresh = false): Promise<SystemConfig> {
    const now = Date.now()

    // 检查缓存是否有效
    if (!forceRefresh && this.configCache && now < this.cacheExpiry) {
      return this.configCache
    }

    try {
      const response = await api.get<BaseResponse<SystemConfig>>({
        url: '/api/v1/system/config'
      })

      if (response.success && response.data) {
        this.configCache = response.data
        this.cacheExpiry = now + this.CACHE_DURATION
        return response.data
      }

      // 如果API失败，返回默认配置
      return this.getDefaultConfig()
    } catch (error) {
      console.error('获取系统配置失败:', error)
      // 返回默认配置作为降级方案
      return this.getDefaultConfig()
    }
  }

  /**
   * 获取快递公司列表
   * @returns 快递公司列表
   */
  static async getExpressCompanies(): Promise<ExpressCompany[]> {
    const config = await this.getSystemConfig()
    return config.express_companies.filter((company) => company.is_active)
  }

  /**
   * 获取订单状态列表
   * @returns 订单状态列表
   */
  static async getOrderStatuses(): Promise<OrderStatus[]> {
    const config = await this.getSystemConfig()
    return config.order_statuses
  }

  /**
   * 获取供应商列表
   * @returns 供应商列表
   */
  static async getProviders(): Promise<Provider[]> {
    const config = await this.getSystemConfig()
    return config.providers.filter((provider) => provider.is_active)
  }

  /**
   * 获取支付方式列表
   * @returns 支付方式列表
   */
  static async getPaymentMethods(): Promise<PaymentMethod[]> {
    const config = await this.getSystemConfig()
    return config.payment_methods.filter((method) => method.is_active)
  }

  /**
   * 获取地区列表
   * @param parentCode 父级地区代码
   * @returns 地区列表
   */
  static async getRegions(parentCode?: string): Promise<Region[]> {
    const config = await this.getSystemConfig()

    if (!parentCode) {
      // 返回省级地区
      return config.regions.filter((region) => region.level === 1)
    }

    // 返回指定父级下的子地区
    const parent = this.findRegionByCode(config.regions, parentCode)
    return parent?.children || []
  }

  /**
   * 根据代码查找地区
   * @param regions 地区列表
   * @param code 地区代码
   * @returns 地区信息
   */
  private static findRegionByCode(regions: Region[], code: string): Region | undefined {
    for (const region of regions) {
      if (region.code === code) {
        return region
      }
      if (region.children) {
        const found = this.findRegionByCode(region.children, code)
        if (found) return found
      }
    }
    return undefined
  }

  /**
   * 清除配置缓存
   */
  static clearCache(): void {
    this.configCache = null
    this.cacheExpiry = 0
  }

  /**
   * 获取默认配置（降级方案）
   * @returns 默认系统配置
   */
  private static getDefaultConfig(): SystemConfig {
    return {
      express_companies: [
        {
          code: 'SF',
          name: '顺丰速运',
          is_active: true,
          supported_services: ['标准快递', '特快专递'],
          weight_limit: 50
        },
        {
          code: 'ZTO',
          name: '中通快递',
          is_active: true,
          supported_services: ['标准快递'],
          weight_limit: 50
        },
        {
          code: 'YTO',
          name: '圆通速递',
          is_active: true,
          supported_services: ['标准快递'],
          weight_limit: 50
        },
        {
          code: 'STO',
          name: '申通快递',
          is_active: true,
          supported_services: ['标准快递'],
          weight_limit: 50
        },
        {
          code: 'YD',
          name: '韵达速递',
          is_active: true,
          supported_services: ['标准快递'],
          weight_limit: 50
        },
        {
          code: 'JT',
          name: '极兔快递',
          is_active: true,
          supported_services: ['标准快递'],
          weight_limit: 50
        },
        {
          code: 'JD',
          name: '京东物流',
          is_active: true,
          supported_services: ['标准快递', '次日达'],
          weight_limit: 50
        },
        // 德邦物流已移除支持
        // {
        //   code: 'DBL',
        //   name: '德邦物流',
        //   is_active: false,
        //   supported_services: ['标准快递', '大件快递'],
        //   weight_limit: 100
        // },
        {
          code: 'DBL360',
          name: '德邦大件360',
          is_active: true,
          supported_services: ['大件快递'],
          weight_limit: 500
        },
        {
          code: 'EMS',
          name: '中国邮政',
          is_active: true,
          supported_services: ['标准快递', '特快专递'],
          weight_limit: 50
        },
        {
          code: 'HTKY',
          name: '百世快递',
          is_active: true,
          supported_services: ['标准快递'],
          weight_limit: 50
        }
      ],
      order_statuses: [
        // === 创建阶段 ===
        {
          code: 'created',
          name: '已创建',
          description: '订单已创建',
          color: 'info',
          is_final: false
        },

        // === 下单阶段 ===
        {
          code: 'submitted',
          name: '已提交',
          description: '订单已提交',
          color: 'primary',
          is_final: false
        },
        {
          code: 'submit_failed',
          name: '提交失败',
          description: '订单提交失败',
          color: 'danger',
          is_final: true
        },
        {
          code: 'print_failed',
          name: '面单生成失败',
          description: '面单生成失败',
          color: 'danger',
          is_final: true
        },

        // === 分配阶段 ===
        {
          code: 'assigned',
          name: '已分配',
          description: '已分配网点或快递员',
          color: 'info',
          is_final: false
        },
        {
          code: 'awaiting_pickup',
          name: '等待揽收',
          description: '等待快递员揽收',
          color: 'warning',
          is_final: false
        },

        // === 揽收阶段 ===
        {
          code: 'picked_up',
          name: '已揽收',
          description: '快递员已揽收',
          color: 'primary',
          is_final: false
        },
        {
          code: 'pickup_failed',
          name: '揽收失败',
          description: '快递员揽收失败',
          color: 'danger',
          is_final: false
        },

        // === 运输阶段 ===
        {
          code: 'in_transit',
          name: '运输中',
          description: '包裹正在运输',
          color: 'info',
          is_final: false
        },
        {
          code: 'out_for_delivery',
          name: '派送中',
          description: '包裹正在派送',
          color: 'info',
          is_final: false
        },

        // === 签收阶段 ===
        {
          code: 'delivered',
          name: '已签收',
          description: '包裹已签收',
          color: 'success',
          is_final: false
        },
        {
          code: 'delivered_abnormal',
          name: '异常签收',
          description: '异常签收',
          color: 'warning',
          is_final: false
        },

        // === 计费阶段 ===
        {
          code: 'billed',
          name: '已计费',
          description: '订单已计费完成',
          color: 'success',
          is_final: true
        },

        // === 异常状态 ===
        {
          code: 'exception',
          name: '异常',
          description: '订单异常',
          color: 'danger',
          is_final: false
        },
        {
          code: 'returned',
          name: '已退回',
          description: '包裹已退回',
          color: 'warning',
          is_final: false
        },
        {
          code: 'forwarded',
          name: '已转寄',
          description: '包裹已转寄',
          color: 'info',
          is_final: false
        },

        // === 取消状态 ===
        {
          code: 'cancelled',
          name: '已取消',
          description: '订单已取消',
          color: 'danger',
          is_final: true
        },
        {
          code: 'canceled',
          name: '已取消',
          description: '订单已取消',
          color: 'danger',
          is_final: true
        },
        {
          code: 'voided',
          name: '已作废',
          description: '订单已作废',
          color: 'danger',
          is_final: true
        },

        // === 特殊状态 ===
        {
          code: 'weight_updated',
          name: '重量更新',
          description: '订单重量已更新',
          color: 'info',
          is_final: false
        },
        {
          code: 'revived',
          name: '订单复活',
          description: '订单已复活',
          color: 'primary',
          is_final: false
        }
      ],
      providers: [
        {
          code: 'kuaidi100',
          name: '快递100',
          is_active: true,
          supported_companies: ['SF', 'ZTO', 'YTO', 'STO'],
          api_version: 'v1'
        },
        {
          code: 'yida',
          name: '易达',
          is_active: true,
          supported_companies: ['SF', 'ZTO', 'YTO'],
          api_version: 'v1'
        },
        {
          code: 'yuntong',
          name: '云通',
          is_active: true,
          supported_companies: ['ZTO', 'YTO', 'STO', 'YD'],
          api_version: 'v1'
        }
      ],
      payment_methods: [
        { code: 0, name: '寄付', description: '寄件人付费', is_active: true },
        { code: 1, name: '到付', description: '收件人付费', is_active: true },
        { code: 2, name: '月结', description: '月结账户', is_active: true }
      ],
      regions: [
        {
          code: '110000',
          name: '北京市',
          level: 1,
          children: [
            {
              code: '110100',
              name: '北京市',
              level: 2,
              parent_code: '110000',
              children: [
                { code: '110101', name: '东城区', level: 3, parent_code: '110100' },
                { code: '110102', name: '西城区', level: 3, parent_code: '110100' },
                { code: '110105', name: '朝阳区', level: 3, parent_code: '110100' },
                { code: '110106', name: '丰台区', level: 3, parent_code: '110100' }
              ]
            }
          ]
        },
        {
          code: '310000',
          name: '上海市',
          level: 1,
          children: [
            {
              code: '310100',
              name: '上海市',
              level: 2,
              parent_code: '310000',
              children: [
                { code: '310101', name: '黄浦区', level: 3, parent_code: '310100' },
                { code: '310104', name: '徐汇区', level: 3, parent_code: '310100' },
                { code: '310105', name: '长宁区', level: 3, parent_code: '310100' }
              ]
            }
          ]
        },
        {
          code: '330000',
          name: '浙江省',
          level: 1,
          children: [
            {
              code: '330100',
              name: '杭州市',
              level: 2,
              parent_code: '330000',
              children: [
                { code: '330102', name: '上城区', level: 3, parent_code: '330100' },
                { code: '330103', name: '下城区', level: 3, parent_code: '330100' },
                { code: '330106', name: '西湖区', level: 3, parent_code: '330100' }
              ]
            }
          ]
        }
      ]
    }
  }
}
