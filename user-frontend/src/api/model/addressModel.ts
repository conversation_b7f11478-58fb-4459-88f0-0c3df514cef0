// 地址相关数据模型
// 设计原则：只定义前端真正用到的字段，保持可选属性灵活兼容后端演进

export interface ContactInfo {
  name?: string
  mobile?: string
  phone?: string
}

export interface AddressInfo extends ContactInfo {
  provinceCode?: string
  provinceName?: string
  cityCode?: string
  cityName?: string
  districtCode?: string
  districtName?: string
  townCode?: string
  townName?: string
  detailAddress?: string
}

export interface AddressParseParams {
  text: string
  addressType?: number
  queryType?: number
  lat?: number
  lng?: number
}

export interface BatchParseAddressParams {
  addresses: AddressParseParams[]
  concurrency?: number
}

export interface ValidateAddressParams {
  provinceCode: string
  cityCode: string
  districtCode: string
  detailAddress: string
}

export interface SearchAreaParams {
  keyword: string
  limit?: number
}

export interface ParseHistoryParams {
  page?: number
  size?: number
} 