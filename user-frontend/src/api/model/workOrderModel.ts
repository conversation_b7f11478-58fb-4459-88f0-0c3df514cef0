/**
 * 工单管理相关数据模型
 * 基于后端 go-kuaidi/internal/model/workorder.go 定义
 */

import type { BaseResponse, PaginationRequest, PaginationResponse } from './kuaidiModel'

// 工单状态枚举
export enum WorkOrderStatus {
  PENDING = 1, // 待处理
  PROCESSING = 2, // 处理中
  REPLIED = 3, // 已回复
  COMPLETED = 4 // 已完结
}

// 工单状态名称映射
export const WorkOrderStatusNames: Record<WorkOrderStatus, string> = {
  [WorkOrderStatus.PENDING]: '待处理',
  [WorkOrderStatus.PROCESSING]: '处理中',
  [WorkOrderStatus.REPLIED]: '已回复',
  [WorkOrderStatus.COMPLETED]: '已完结'
}

// 工单优先级枚举
export enum WorkOrderPriority {
  HIGH = 1, // 高优先级
  MEDIUM = 2, // 中优先级
  LOW = 3 // 低优先级
}

// 工单优先级名称映射
export const WorkOrderPriorityNames: Record<WorkOrderPriority, string> = {
  [WorkOrderPriority.HIGH]: '高',
  [WorkOrderPriority.MEDIUM]: '中',
  [WorkOrderPriority.LOW]: '低'
}

// 回复类型枚举
export enum ReplyType {
  USER = 1, // 用户回复
  PROVIDER = 2, // 供应商回复
  SYSTEM = 3 // 系统回复
}

// 回复类型名称映射
export const ReplyTypeNames: Record<ReplyType, string> = {
  [ReplyType.USER]: '用户',
  [ReplyType.PROVIDER]: '供应商',
  [ReplyType.SYSTEM]: '系统'
}

// 附件上传类型枚举
export enum UploadType {
  WORKORDER = 1, // 工单创建时上传
  REPLY = 2 // 工单回复时上传
}

// 工单类型常量（统一化改造：调整为6种核心工单类型）
export enum WorkOrderType {
  PICKUP_REMINDER = 1, // 催取件
  WEIGHT_EXCEPTION = 2, // 重量异常
  DELIVERY_REMINDER = 12, // 催派送
  LOGISTICS_STALL = 16, // 物流停滞
  REASSIGN_COURIER = 17, // 重新分配快递员
  CANCEL_ORDER = 19 // 取消订单
}

// 工单类型名称映射（统一化改造：调整为6种核心工单类型）
export const WorkOrderTypeNames: Record<number, string> = {
  [WorkOrderType.PICKUP_REMINDER]: '催取件',
  [WorkOrderType.WEIGHT_EXCEPTION]: '重量异常',
  [WorkOrderType.DELIVERY_REMINDER]: '催派送',
  [WorkOrderType.LOGISTICS_STALL]: '物流停滞',
  [WorkOrderType.REASSIGN_COURIER]: '重新分配快递员',
  [WorkOrderType.CANCEL_ORDER]: '取消订单'
}

// 工单附件模型
export interface WorkOrderAttachment {
  id: string
  work_order_id?: string
  reply_id?: string
  file_name: string
  file_url: string
  file_type?: string
  file_size?: number
  upload_type: UploadType
  created_at: string
}

// 工单回复模型
export interface WorkOrderReply {
  id: string
  work_order_id: string
  reply_type: ReplyType
  committer?: string
  content: string
  raw_data?: string
  created_at: string
  attachments?: WorkOrderAttachment[]
}

// 工单模型
export interface WorkOrder {
  id: string
  user_id: string
  order_no?: string
  tracking_no?: string
  provider: string
  provider_work_order_id?: string
  work_order_type: WorkOrderType
  title: string
  content: string
  status: WorkOrderStatus
  priority: WorkOrderPriority
  version: number
  feedback_weight?: number
  goods_value?: number
  overweight_amount?: number
  callback_url?: string
  message_callback_url?: string
  raw_request_data?: string
  provider_response_data?: string
  created_at: string
  updated_at: string
  completed_at?: string
  deleted_at?: string

  // 关联数据
  replies?: WorkOrderReply[]
  attachments?: WorkOrderAttachment[]
  type_name?: string
  status_name?: string
  latest_reply?: string
}

// 工单列表项（列表视图不包含 replies/attachments 等重数据）
export interface WorkOrderListItem extends Omit<WorkOrder, 'replies' | 'attachments'> {}

// 工单类型映射模型
export interface WorkOrderTypeMapping {
  id: number
  unified_type: WorkOrderType
  unified_name: string
  provider: string
  provider_type?: number
  provider_name?: string
  is_supported: boolean
  description?: string
  created_at: string
  updated_at: string
}

// 创建工单请求（统一使用智能创建方式）
export interface CreateWorkOrderRequest {
  // 必填字段
  work_order_type: WorkOrderType // 6种核心工单类型之一
  content: string // 问题描述

  // 订单标识（二选一即可）
  order_no?: string // 客户订单号
  tracking_no?: string // 运单号

  // 可选字段（根据工单类型自动显示）
  feedback_weight?: number // 反馈重量（重量异常时使用）
  goods_value?: number // 商品价值
  overweight_amount?: number // 超重金额
  attachment_urls?: string[] // 附件URL列表

  // 系统自动处理字段（用户无需填写，前端不显示）
  // provider: string             // 系统自动识别
  // callback_url?: string        // 系统自动配置
  // message_callback_url?: string // 系统自动配置
  // provider_specific_data?: Record<string, any> // 系统自动处理
}

// 类型别名：为了保持向后兼容性
export type SmartCreateWorkOrderRequest = CreateWorkOrderRequest

// 工单列表请求
export interface WorkOrderListRequest extends PaginationRequest {
  provider?: string
  status?: WorkOrderStatus
  work_order_type?: WorkOrderType
  order_no?: string
  tracking_no?: string
  start_date?: string
  end_date?: string
  page_size?: number
}

// 工单列表响应
export interface WorkOrderListResponse extends PaginationResponse {
  items: WorkOrder[]
}

// 回复工单请求
export interface ReplyWorkOrderRequest {
  content: string
  attachment_urls?: string[]
}

// 上传附件响应
export interface UploadAttachmentResponse {
  file_url: string
  file_name: string
  file_size: number
}

// 工单响应基础类型
export type WorkOrderResponse<T = any> = BaseResponse<T>

// 工单统计数据
export interface WorkOrderStatistics {
  total: number
  pending: number
  processing: number
  replied: number
  completed: number
  today_created: number
  avg_response_time: number // 平均响应时间（小时）
}

// 工单趋势数据
export interface WorkOrderTrend {
  date: string
  created: number
  completed: number
}

// 工单类型统计
export interface WorkOrderTypeStatistics {
  type: WorkOrderType
  type_name: string
  count: number
  percentage: number
}
