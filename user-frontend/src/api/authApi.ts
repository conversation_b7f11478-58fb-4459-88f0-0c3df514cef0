import api from '@/utils/http'
import type {
  BaseResponse,
  TokenRequest,
  TokenResponse,
  RevokeTokenRequest
} from './model/kuaidiModel'

/**
 * 认证相关API服务
 */
export class AuthService {
  /**
   * 获取访问令牌 (OAuth)
   * @param params 令牌请求参数
   * @returns 令牌响应
   */
  static async getToken(params: TokenRequest): Promise<TokenResponse> {
    // OAuth接口使用 application/x-www-form-urlencoded 格式
    const formData = new URLSearchParams()
    formData.append('grant_type', params.grant_type)
    formData.append('client_id', params.client_id)
    formData.append('client_secret', params.client_secret)
    if (params.scope) {
      formData.append('scope', params.scope)
    }

    return api.post<TokenResponse>({
      url: '/oauth/token',
      data: formData.toString(),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  }

  /**
   * 撤销令牌
   * @param params 撤销令牌请求参数
   * @returns 空响应
   */
  static async revokeToken(params: RevokeTokenRequest): Promise<{}> {
    // OAuth接口使用 application/x-www-form-urlencoded 格式
    const formData = new URLSearchParams()
    formData.append('token', params.token)
    formData.append('client_id', params.client_id)
    formData.append('client_secret', params.client_secret)
    if (params.token_type_hint) {
      formData.append('token_type_hint', params.token_type_hint)
    }

    return api.post<{}>({
      url: '/oauth/revoke',
      data: formData.toString(),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  }
}
