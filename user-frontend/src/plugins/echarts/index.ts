import * as echarts from 'echarts/core'

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  PictorialBar<PERSON>hart,
  <PERSON>au<PERSON><PERSON>hart
} from 'echarts/charts'

import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  Aria<PERSON>omponent,
  ParallelComponent,
  LegendComponent,
  LegendPlainComponent
} from 'echarts/components'

import { SVGRenderer } from 'echarts/renderers'

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  PolarComponent,
  AriaComponent,
  ParallelComponent,
  LegendComponent,
  Legend<PERSON>lainComponent,
  BarChart,
  LineChart,
  <PERSON>Chart,
  MapChart,
  SVGRenderer,
  PictorialBarChart,
  GaugeChart
])

export default echarts
