/**
 * 工单类型管理 Composable
 * 确保前后端数据一致性，动态获取服务类型
 */

import { ref, computed } from 'vue'
import { WorkOrderService } from '@/api'
import type { WorkOrderTypeMapping } from '@/api/model/workOrderModel'
import { ErrorHandler } from '@/utils/errorHandler'
import { SecureStorage } from '@/utils/security'
import { logger } from '@/utils/logger'

// 全局状态管理
const workOrderTypes = ref<WorkOrderTypeMapping[]>([])
const loading = ref(false)
const lastFetchTime = ref<number>(0)

// 缓存时间：5分钟
const CACHE_DURATION = 5 * 60 * 1000

export function useWorkOrderTypes() {
  /**
   * 获取支持的工单类型
   * @param provider 供应商（可选，不传则获取所有供应商的类型）
   * @param forceRefresh 是否强制刷新
   */
  const fetchWorkOrderTypes = async (provider?: string, forceRefresh = false) => {
    // 检查缓存是否有效
    const now = Date.now()
    if (
      !forceRefresh &&
      workOrderTypes.value.length > 0 &&
      now - lastFetchTime.value < CACHE_DURATION
    ) {
      return workOrderTypes.value
    }

    loading.value = true
    try {
      // 检查是否有认证token
      const storedToken = SecureStorage.getToken()
      if (!storedToken) {
        throw new Error('用户未登录，请先登录后再获取工单类型')
      }

      const response = await WorkOrderService.getSupportedTypes(provider)

      if (response.success && response.data) {
        workOrderTypes.value = response.data
        lastFetchTime.value = now
        logger.business('成功获取工单类型', { count: workOrderTypes.value.length })
      } else {
        throw new Error(response.message || '获取工单类型失败')
      }
    } catch (error) {
      logger.error('获取工单类型失败', error)
      ErrorHandler.handleApiError(error, true) // 显示错误信息给用户
      workOrderTypes.value = [] // 清空数据，不使用备选
      throw error // 重新抛出错误，让调用方处理
    } finally {
      loading.value = false
    }

    return workOrderTypes.value
  }

  /**
   * 获取用户友好的服务类型选项
   * 将技术性的工单类型转换为用户理解的服务类型，并按业务场景分组
   */
  const getUserFriendlyTypes = computed(() => {
    // 获取去重的统一类型
    const uniqueTypes = getUniqueUnifiedTypes()

    return uniqueTypes
      .map((type) => ({
        value: type.unified_type,
        label: getUserFriendlyTypeName(type.unified_name),
        description: getUserFriendlyDescription(type.unified_name),
        originalName: type.unified_name,
        category: getServiceCategory(type.unified_type),
        isSupported: true, // 统一类型都是支持的
        priority: getTypePriority(type.unified_type) // 用于排序
      }))
      .sort((a, b) => a.priority - b.priority)
  })

  /**
   * 获取去重的统一工单类型
   */
  const getUniqueUnifiedTypes = () => {
    const typeMap = new Map<number, WorkOrderTypeMapping>()

    workOrderTypes.value.forEach((type) => {
      if (!typeMap.has(type.unified_type)) {
        typeMap.set(type.unified_type, type)
      }
    })

    return Array.from(typeMap.values())
  }

  /**
   * 将技术性的工单类型名称转换为用户友好的名称（统一化改造：调整为6种核心类型）
   */
  const getUserFriendlyTypeName = (originalName: string): string => {
    const friendlyNameMap: Record<string, string> = {
      // 🔥 基于统一化改造后的6种核心工单类型进行映射 - 使用统一名称
      催取件: '催取件',
      重量异常: '重量异常',
      催派送: '催派送',
      物流停滞: '物流停滞',
      重新分配快递员: '重新分配快递员',
      取消订单: '取消订单'
    }

    return friendlyNameMap[originalName] || originalName
  }

  /**
   * 获取用户友好的服务类型描述（统一化改造：调整为6种核心类型）
   */
  const getUserFriendlyDescription = (originalName: string): string => {
    const descriptionMap: Record<string, string> = {
      催取件: '催促快递员上门取件',
      重量异常: '包裹实际重量与下单重量不符',
      催派送: '催促快递员派送',
      物流停滞: '物流信息长时间未更新',
      重新分配快递员: '申请更换派送快递员',
      取消订单: '取消快递订单'
    }

    return descriptionMap[originalName] || '快递服务相关问题'
  }

  /**
   * 获取服务类型的业务分类（统一化改造：调整为6种核心类型）
   */
  const getServiceCategory = (typeId: number): string => {
    const categoryMap: Record<number, string> = {
      1: '取件服务', // 催取件
      2: '费用争议', // 重量异常
      12: '派送服务', // 催派送
      16: '物流服务', // 物流停滞
      17: '派送服务', // 重新分配快递员
      19: '订单管理' // 取消订单
    }

    return categoryMap[typeId] || '其他服务'
  }

  /**
   * 获取类型优先级（用于排序，数字越小优先级越高）（统一化改造：调整为6种核心类型）
   */
  const getTypePriority = (typeId: number): number => {
    // 按用户使用频率和重要性排序
    const priorityMap: Record<number, number> = {
      2: 1, // 重量异常 - 最常见
      12: 2, // 催派送 - 常见
      1: 3, // 催取件 - 常见
      16: 4, // 物流停滞 - 较常见
      17: 5, // 重新分配快递员 - 较少
      19: 6 // 取消订单 - 较少
    }

    return priorityMap[typeId] || 99
  }

  /**
   * 根据类型ID获取类型名称
   */
  const getTypeName = (typeId: number): string => {
    const type = workOrderTypes.value.find((t) => t.unified_type === typeId)
    return type ? getUserFriendlyTypeName(type.unified_name) : '未知类型'
  }

  /**
   * 根据类型ID获取原始类型名称
   */
  const getOriginalTypeName = (typeId: number): string => {
    const type = workOrderTypes.value.find((t) => t.unified_type === typeId)
    return type ? type.unified_name : '未知类型'
  }

  /**
   * 检查类型是否需要特定字段
   */
  const needsWeightInfo = (typeId: number): boolean => {
    // 重量异常相关的类型需要重量信息
    return [2].includes(typeId) // 重量异常
  }

  const needsValueInfo = (typeId: number): boolean => {
    // 破损、遗失相关的类型需要货物价值信息
    return [5, 6].includes(typeId) // 破损、遗失
  }

  const needsAmountInfo = (typeId: number): boolean => {
    // 费用相关的类型需要金额信息
    return [2, 4].includes(typeId) // 重量异常、线下收费
  }

  /**
   * 获取按分类分组的服务类型
   */
  const getTypesByCategory = computed(() => {
    const grouped: Record<string, any[]> = {}

    getUserFriendlyTypes.value.forEach((type) => {
      const category = type.category
      if (!grouped[category]) {
        grouped[category] = []
      }
      grouped[category].push(type)
    })

    return grouped
  })

  /**
   * 清除缓存
   */
  const clearCache = () => {
    workOrderTypes.value = []
    lastFetchTime.value = 0
  }

  return {
    // 状态
    workOrderTypes: computed(() => workOrderTypes.value),
    loading: computed(() => loading.value),

    // 计算属性
    userFriendlyTypes: getUserFriendlyTypes,

    // 方法
    fetchWorkOrderTypes,
    getTypeName,
    getOriginalTypeName,
    getUserFriendlyTypeName,
    needsWeightInfo,
    needsValueInfo,
    needsAmountInfo,
    clearCache
  }
}
