import { ref, computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { WorkOrderService } from '@/api/workOrderApi'
import { ExpressService } from '@/api/expressApi'
import type {
  SmartCreateWorkOrderRequest,
  WorkOrder,
  WorkOrderType
} from '@/api/model/workOrderModel'
import type { QueryOrderResponse } from '@/api/model/kuaidiModel'

/**
 * 工单创建组合式函数
 * 提供用户友好的工单创建体验，自动处理供应商识别和映射
 */
export function useWorkOrder() {
  // 响应式状态
  const loading = ref(false)
  const submitting = ref(false)
  const orderLoading = ref(false)

  // 表单数据
  const formData = reactive<SmartCreateWorkOrderRequest>({
    work_order_type: 2, // 默认选择重量异常（最常用）
    content: '',
    order_no: '',
    tracking_no: '',
    feedback_weight: undefined,
    goods_value: undefined,
    overweight_amount: undefined,
    attachment_urls: []
  })

  // 订单信息
  const orderInfo = ref<QueryOrderResponse | null>(null)
  const orderError = ref<string>('')

  // 6种核心工单类型选项
  const workOrderTypeOptions = [
    {
      value: 2,
      label: '重量异常',
      description: '包裹实际重量与下单重量不符',
      category: '费用争议',
      priority: 1
    },
    {
      value: 12,
      label: '催派送',
      description: '催促快递员派送',
      category: '派送服务',
      priority: 2
    },
    { value: 1, label: '催取件', description: '催促快递员取件', category: '取件服务', priority: 3 },
    {
      value: 16,
      label: '物流停滞',
      description: '物流信息长时间未更新',
      category: '物流服务',
      priority: 4
    },
    {
      value: 17,
      label: '重新分配快递员',
      description: '申请更换派送快递员',
      category: '派送服务',
      priority: 5
    },
    { value: 19, label: '取消订单', description: '取消快递订单', category: '订单管理', priority: 6 }
  ]

  // 计算属性
  const selectedTypeInfo = computed(() => {
    return workOrderTypeOptions.find((option) => option.value === formData.work_order_type)
  })

  const isWeightException = computed(() => {
    return formData.work_order_type === 2
  })

  const hasOrderIdentifier = computed(() => {
    return !!(formData.order_no?.trim() || formData.tracking_no?.trim())
  })

  const canSubmit = computed(() => {
    return !!(
      formData.work_order_type &&
      formData.content?.trim() &&
      hasOrderIdentifier.value &&
      !submitting.value
    )
  })

  // 表单验证规则
  const formRules = {
    work_order_type: [{ required: true, message: '请选择工单类型', trigger: 'change' }],
    content: [
      { required: true, message: '请填写问题描述', trigger: 'blur' },
      { min: 10, message: '问题描述至少需要10个字符', trigger: 'blur' },
      { max: 1000, message: '问题描述不能超过1000个字符', trigger: 'blur' }
    ],
    order_identifier: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!formData.order_no?.trim() && !formData.tracking_no?.trim()) {
            callback(new Error('请填写订单号或运单号'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    feedback_weight: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (isWeightException.value && value !== undefined && value <= 0) {
            callback(new Error('反馈重量必须大于0'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  /**
   * 自动查询订单信息
   */
  const queryOrderInfo = async () => {
    if (!hasOrderIdentifier.value) {
      orderInfo.value = null
      orderError.value = ''
      return
    }

    orderLoading.value = true
    orderError.value = ''

    try {
      const identifier = formData.order_no?.trim() || formData.tracking_no?.trim()
      if (!identifier) return

      // 调用订单查询API
      const response = await ExpressService.queryOrder({
        order_no: formData.order_no?.trim() || undefined,
        tracking_no: formData.tracking_no?.trim() || undefined
      })

      if (response.success && response.data) {
        orderInfo.value = response.data
        orderError.value = ''

        // 自动填充相关信息
        if (response.data.tracking_no && !formData.tracking_no) {
          formData.tracking_no = response.data.tracking_no
        }
        if (response.data.order_no && !formData.order_no) {
          formData.order_no = response.data.order_no
        }

        ElMessage.success('订单信息查询成功')
      } else {
        orderError.value = response.message || '未找到对应的订单'
        orderInfo.value = null
      }
    } catch (error: any) {
      console.error('查询订单信息失败:', error)
      orderError.value = error.message || '查询订单信息失败'
      orderInfo.value = null
    } finally {
      orderLoading.value = false
    }
  }

  /**
   * 智能创建工单
   */
  const createWorkOrder = async (): Promise<WorkOrder | null> => {
    if (!canSubmit.value) {
      ElMessage.warning('请完善工单信息')
      return null
    }

    submitting.value = true

    try {
      // 构建请求数据
      const requestData: SmartCreateWorkOrderRequest = {
        work_order_type: formData.work_order_type,
        content: formData.content.trim(),
        order_no: formData.order_no?.trim() || undefined,
        tracking_no: formData.tracking_no?.trim() || undefined,
        feedback_weight: formData.feedback_weight,
        goods_value: formData.goods_value,
        overweight_amount: formData.overweight_amount,
        attachment_urls: formData.attachment_urls?.filter((url) => url.trim()) || []
      }

      // 调用创建工单API（统一使用智能创建方式）
      const response = await WorkOrderService.createWorkOrder(requestData)

      if (response.success && response.data) {
        ElMessage.success('工单创建成功')
        resetForm()
        return response.data
      } else {
        throw new Error(response.message || '创建工单失败')
      }
    } catch (error: any) {
      console.error('创建工单失败:', error)
      ElMessage.error(error.message || '创建工单失败')
      return null
    } finally {
      submitting.value = false
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      work_order_type: 2,
      content: '',
      order_no: '',
      tracking_no: '',
      feedback_weight: undefined,
      goods_value: undefined,
      overweight_amount: undefined,
      attachment_urls: []
    })
    orderInfo.value = null
    orderError.value = ''
  }

  /**
   * 设置工单类型
   */
  const setWorkOrderType = (type: WorkOrderType) => {
    formData.work_order_type = type
  }

  /**
   * 设置订单标识符
   */
  const setOrderIdentifier = (orderNo?: string, trackingNo?: string) => {
    if (orderNo) formData.order_no = orderNo
    if (trackingNo) formData.tracking_no = trackingNo

    // 自动查询订单信息
    if (orderNo || trackingNo) {
      queryOrderInfo()
    }
  }

  /**
   * 添加附件URL
   */
  const addAttachmentUrl = (url: string) => {
    if (!formData.attachment_urls) {
      formData.attachment_urls = []
    }
    if (url.trim() && !formData.attachment_urls.includes(url.trim())) {
      formData.attachment_urls.push(url.trim())
    }
  }

  /**
   * 移除附件URL
   */
  const removeAttachmentUrl = (index: number) => {
    if (formData.attachment_urls && index >= 0 && index < formData.attachment_urls.length) {
      formData.attachment_urls.splice(index, 1)
    }
  }

  /**
   * 获取工单类型的用户友好描述
   */
  const getTypeDescription = (type: WorkOrderType): string => {
    const option = workOrderTypeOptions.find((opt) => opt.value === type)
    return option?.description || '未知工单类型'
  }

  /**
   * 获取工单类型的业务分类
   */
  const getTypeCategory = (type: WorkOrderType): string => {
    const option = workOrderTypeOptions.find((opt) => opt.value === type)
    return option?.category || '其他'
  }

  return {
    // 响应式状态
    loading,
    submitting,
    orderLoading,
    formData,
    orderInfo,
    orderError,

    // 计算属性
    selectedTypeInfo,
    isWeightException,
    hasOrderIdentifier,
    canSubmit,

    // 配置数据
    workOrderTypeOptions,
    formRules,

    // 方法
    queryOrderInfo,
    createWorkOrder,
    resetForm,
    setWorkOrderType,
    setOrderIdentifier,
    addAttachmentUrl,
    removeAttachmentUrl,
    getTypeDescription,
    getTypeCategory
  }
}
