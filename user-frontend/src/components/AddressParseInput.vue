<template>
  <div class="address-parse-input">
    <!-- 地址解析输入区域 -->
    <div class="parse-input-section">
      <div class="input-header">
        <span class="input-label">🧠 智能地址解析</span>
        <el-button type="text" size="small" @click="showExample = !showExample" class="example-btn">
          {{ showExample ? '隐藏' : '查看' }}示例
        </el-button>
      </div>

      <!-- 示例提示 -->
      <el-collapse-transition>
        <div v-show="showExample" class="example-section">
          <el-alert title="输入示例" type="info" :closable="false" show-icon>
            <template #default>
              <div class="example-content">
                <p
                  ><strong>格式1：</strong>李健<br />17099916606<br />湖南省永州市冷水滩区珊瑚街道
                  园丁山庄8栋19号</p
                >
                <p><strong>格式2：</strong>张三 13800138000 北京市海淀区西二旗 中关村软件园</p>
                <p><strong>格式3：</strong>王五，15988776655，上海市浦东新区陆家嘴金茂大厦20层</p>
              </div>
            </template>
          </el-alert>
        </div>
      </el-collapse-transition>

      <!-- 输入框 -->
      <el-input
        v-model="inputText"
        type="textarea"
        :rows="4"
        placeholder="请输入包含姓名、电话、地址的文本，支持多种格式..."
        :disabled="loading"
        class="parse-textarea"
        @keydown.ctrl.enter="handleParse"
      />

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          @click="handleParse"
          :loading="loading"
          :disabled="!inputText.trim()"
          size="small"
        >
          <el-icon><Search /></el-icon>
          智能解析
        </el-button>
        <el-button @click="clearInput" :disabled="loading || !inputText" size="small">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
        <el-button type="success" @click="loadExample" :disabled="loading" size="small">
          <el-icon><DocumentAdd /></el-icon>
          加载示例
        </el-button>
      </div>
    </div>

    <!-- 解析结果预览 -->
    <div v-if="parseResult" class="parse-result-preview">
      <div class="result-header">
        <span class="result-title">📋 解析结果预览</span>
        <div class="result-actions">
          <el-button
            type="primary"
            size="small"
            @click="handleApplyResult"
            :disabled="!parseResult"
          >
            <el-icon><Check /></el-icon>
            应用到表单
          </el-button>
          <el-button type="danger" size="small" @click="clearResult">
            <el-icon><Close /></el-icon>
            清除结果
          </el-button>
        </div>
      </div>

      <div class="result-content">
        <el-row :gutter="10">
          <el-col :span="8">
            <div class="result-item">
              <span class="item-label">👤 姓名：</span>
              <span class="item-value">{{ parseResult.name || '未识别' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="result-item">
              <span class="item-label">📱 手机：</span>
              <span class="item-value">{{ parseResult.mobile || '未识别' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="result-item">
              <span class="item-label">📞 电话：</span>
              <span class="item-value">{{ parseResult.phone || '未识别' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="10" style="margin-top: 10px">
          <el-col :span="24">
            <div class="result-item">
              <span class="item-label">📍 地址：</span>
              <span class="item-value">{{ getFullAddress() }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="10" style="margin-top: 10px">
          <el-col :span="24">
            <div class="result-item">
              <span class="item-label">🏠 详细：</span>
              <span class="item-value">{{ parseResult.detailAddress || '未识别' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Delete, DocumentAdd, Check, Close } from '@element-plus/icons-vue'
  import { addressAPI } from '@/api'

  // Props
  interface Props {
    placeholder?: string
    disabled?: boolean
  }

  // Emits
  interface Emits {
    (e: 'parsed', result: any): void
    (e: 'apply', result: any): void
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请输入包含姓名、电话、地址的文本...',
    disabled: false
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const inputText = ref('')
  const loading = ref(false)
  const showExample = ref(false)
  const parseResult = ref<any>(null)

  // 示例数据
  const exampleTexts = [
    '李健\n17099916606\n湖南省永州市冷水滩区珊瑚街道 园丁山庄8栋19号门面妈妈驿站',
    '张三\n13800138000\n北京市海淀区西二旗 中关村软件园 腾讯大厦B座',
    '王五\n15988776655\n上海市浦东新区陆家嘴 金茂大厦20层',
    '刘六\n18922334455\n广东省深圳市南山区科技园 腾讯大厦'
  ]

  // 获取完整地址
  const getFullAddress = () => {
    if (!parseResult.value) return ''

    const parts = []
    if (parseResult.value.provinceName) parts.push(parseResult.value.provinceName)
    if (parseResult.value.cityName) parts.push(parseResult.value.cityName)
    if (parseResult.value.districtName) parts.push(parseResult.value.districtName)
    if (parseResult.value.townName) parts.push(parseResult.value.townName)

    return parts.join('')
  }

  // 解析地址
  const handleParse = async () => {
    if (!inputText.value.trim()) {
      ElMessage.warning('请输入地址文本')
      return
    }

    loading.value = true

    try {
      console.log('🚀 开始地址解析:', inputText.value.trim())

      const response: any = await addressAPI.parseAddress({
        text: inputText.value.trim(),
        lat: 30.0,
        lng: 110.0
      })

      console.log('📥 地址解析响应:', response)

      if (response.success && response.data?.addressInfo) {
        parseResult.value = response.data.addressInfo

        // 🔥 增强验证：检查解析结果的完整性
        const validationResult = validateParseResult(parseResult.value)

        if (validationResult.isValid) {
          console.log('✅ 地址解析成功:', parseResult.value)
          emit('parsed', parseResult.value)

          // 自动应用解析结果
          setTimeout(() => {
            emit('apply', parseResult.value)
            ElMessage.success('地址信息已自动填充到表单')
          }, 500)
        } else {
          // 解析结果不完整，显示警告但仍然应用
          console.warn('⚠️ 地址解析不完整:', validationResult.message)
          emit('parsed', parseResult.value)

          // 仍然自动应用部分结果
          setTimeout(() => {
            emit('apply', parseResult.value)
            ElMessage.warning('地址信息已部分填充，请检查并补充缺失信息')
          }, 500)
        }
      } else {
        throw new Error(response.message || '解析失败')
      }
    } catch (error: any) {
      console.error('❌ 地址解析失败:', error)
      parseResult.value = null
      ElMessage.error('地址解析失败: ' + (error.message || '请检查输入格式或网络连接'))
    } finally {
      loading.value = false
    }
  }

  // 验证解析结果的完整性
  const validateParseResult = (result: any) => {
    if (!result) {
      return { isValid: false, message: '解析结果为空' }
    }

    const issues = []

    // 检查基本信息
    if (!result.name || result.name === '未识别') {
      issues.push('姓名')
    }
    if (!result.mobile && !result.phone) {
      issues.push('联系电话')
    }

    // 检查地址信息
    if (!result.provinceName || result.provinceName === '未识别省份') {
      issues.push('省份')
    }
    if (!result.cityName || result.cityName === '未识别城市') {
      issues.push('城市')
    }
    if (!result.districtName || result.districtName === '未识别区县') {
      issues.push('区县')
    }
    if (!result.detailAddress || result.detailAddress === '未识别详细地址') {
      issues.push('详细地址')
    }

    if (issues.length === 0) {
      return { isValid: true, message: '解析完整' }
    } else {
      return {
        isValid: false,
        message: `缺少${issues.join('、')}信息`
      }
    }
  }

  // 获取识别项目数量
  const getIdentifiedCount = () => {
    if (!parseResult.value) return 0

    let count = 0
    if (parseResult.value.name) count++
    if (parseResult.value.mobile) count++
    if (parseResult.value.phone) count++
    if (parseResult.value.provinceName) count++
    if (parseResult.value.cityName) count++
    if (parseResult.value.districtName) count++
    if (parseResult.value.detailAddress) count++

    return count
  }

  // 应用解析结果
  const handleApplyResult = () => {
    if (!parseResult.value) {
      ElMessage.warning('没有可应用的解析结果')
      return
    }

    emit('apply', parseResult.value)
    ElMessage.success('解析结果已应用到表单')
  }

  // 清空输入
  const clearInput = () => {
    inputText.value = ''
    clearResult()
  }

  // 清除结果
  const clearResult = () => {
    parseResult.value = null
  }

  // 加载示例
  const loadExample = () => {
    const randomExample = exampleTexts[Math.floor(Math.random() * exampleTexts.length)]
    inputText.value = randomExample
    ElMessage.success('已加载示例数据')
  }

  // 暴露方法给父组件
  defineExpose({
    parse: handleParse,
    clear: clearInput,
    clearResult,
    loadExample
  })
</script>

<style scoped>
  .address-parse-input {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 16px;
    background: #fafbfc;
    margin-bottom: 20px;
  }

  .parse-input-section {
    margin-bottom: 16px;
  }

  .input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .input-label {
    font-weight: 600;
    color: #409eff;
    font-size: 14px;
  }

  .example-btn {
    padding: 0;
    font-size: 12px;
  }

  .example-section {
    margin-bottom: 12px;
  }

  .example-content p {
    margin: 8px 0;
    font-size: 12px;
    line-height: 1.5;
  }

  .parse-textarea {
    margin-bottom: 12px;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .parse-result-preview {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background: white;
  }

  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
  }

  .result-title {
    font-weight: 600;
    color: #409eff;
    font-size: 14px;
  }

  .result-actions {
    display: flex;
    gap: 8px;
  }

  .result-content {
    padding: 16px;
  }

  .result-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
  }

  .item-label {
    font-weight: 500;
    color: #606266;
    min-width: 60px;
  }

  .item-value {
    color: #303133;
    flex: 1;
    word-break: break-all;
  }

  :deep(.el-textarea__inner) {
    font-family: inherit;
    resize: vertical;
  }

  :deep(.el-alert__content) {
    padding-right: 0;
  }

  @media (max-width: 768px) {
    .input-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .result-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .action-buttons {
      justify-content: center;
    }

    .result-actions {
      width: 100%;
      justify-content: center;
    }
  }
</style>
