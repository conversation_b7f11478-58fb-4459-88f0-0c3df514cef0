<template>
  <div class="address-parse-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>🧠 智能地址解析</h2>
      <p class="subtitle">使用AI技术快速解析地址信息，提取姓名、电话、详细地址等</p>
    </div>

    <!-- 功能选项卡 -->
    <el-tabs v-model="activeTab" class="parse-tabs">
      <!-- 单个地址解析 -->
      <el-tab-pane label="单个解析" name="single">
        <div class="single-parse-section">
          <el-card class="parse-card">
            <template #header>
              <div class="card-header">
                <span>📝 输入地址文本</span>
                <el-button type="primary" size="small" @click="loadExample" :disabled="loading">
                  加载示例
                </el-button>
              </div>
            </template>

            <el-form :model="singleForm" label-width="100px">
              <el-form-item label="地址文本">
                <el-input
                  v-model="singleForm.text"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入包含姓名、电话、地址的文本，例如：&#10;李健&#10;17099916606&#10;湖南省永州市冷水滩区珊瑚街道 园丁山庄8栋19号门面妈妈驿站"
                  :disabled="loading"
                />
              </el-form-item>

              <el-form-item label="坐标位置">
                <el-row :gutter="10">
                  <el-col :span="12">
                    <el-input
                      v-model.number="singleForm.lat"
                      placeholder="纬度"
                      :disabled="loading"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-input
                      v-model.number="singleForm.lng"
                      placeholder="经度"
                      :disabled="loading"
                    />
                  </el-col>
                </el-row>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  @click="parseSingleAddress"
                  :loading="loading"
                  size="large"
                >
                  🚀 开始解析
                </el-button>
                <el-button @click="clearSingleForm" :disabled="loading"> 清空 </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 解析结果 -->
          <el-card v-if="singleResult" class="result-card">
            <template #header>
              <span>✅ 解析结果</span>
            </template>
            <AddressResult :result="singleResult" />
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 批量地址解析 -->
      <el-tab-pane label="批量解析" name="batch">
        <div class="batch-parse-section">
          <el-card class="parse-card">
            <template #header>
              <div class="card-header">
                <span>📋 批量地址解析</span>
                <div>
                  <el-button
                    type="success"
                    size="small"
                    @click="loadBatchExample"
                    :disabled="loading"
                  >
                    加载示例
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    @click="addBatchAddress"
                    :disabled="loading"
                  >
                    添加地址
                  </el-button>
                </div>
              </div>
            </template>

            <el-form :model="batchForm" label-width="100px">
              <el-form-item label="并发数">
                <el-input-number
                  v-model="batchForm.concurrency"
                  :min="1"
                  :max="20"
                  :disabled="loading"
                />
                <span class="form-tip">建议设置为5-10，过高可能导致API限流</span>
              </el-form-item>

              <el-form-item label="地址列表">
                <div class="batch-addresses">
                  <div
                    v-for="(address, index) in batchForm.addresses"
                    :key="index"
                    class="batch-address-item"
                  >
                    <el-input
                      v-model="address.text"
                      type="textarea"
                      :rows="2"
                      :placeholder="`地址 ${index + 1}`"
                      :disabled="loading"
                    />
                    <el-button
                      type="danger"
                      size="small"
                      @click="removeBatchAddress(index)"
                      :disabled="loading"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  @click="parseBatchAddresses"
                  :loading="loading"
                  size="large"
                  :disabled="batchForm.addresses.length === 0"
                >
                  🚀 批量解析 ({{ batchForm.addresses.length }}个)
                </el-button>
                <el-button @click="clearBatchForm" :disabled="loading"> 清空全部 </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 批量解析结果 -->
          <el-card v-if="batchResult" class="result-card">
            <template #header>
              <div class="card-header">
                <span>📊 批量解析结果</span>
                <div class="statistics">
                  <el-tag type="success">成功: {{ batchResult.statistics.success }}</el-tag>
                  <el-tag type="danger">失败: {{ batchResult.statistics.failed }}</el-tag>
                  <el-tag type="info"
                    >成功率: {{ batchResult.statistics.successRate.toFixed(1) }}%</el-tag
                  >
                  <el-tag type="warning">耗时: {{ batchResult.processTime.toFixed(2) }}s</el-tag>
                </div>
              </div>
            </template>
            <BatchAddressResult :results="batchResult.results" />
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 地址验证 -->
      <el-tab-pane label="地址验证" name="validate">
        <div class="validate-section">
          <el-card class="parse-card">
            <template #header>
              <span>🔍 地址验证</span>
            </template>

            <el-form :model="validateForm" label-width="100px">
              <el-form-item label="省份">
                <AreaCascader
                  v-model="validateForm.codes"
                  @change="onAreaChange"
                  :disabled="loading"
                />
              </el-form-item>

              <el-form-item label="详细地址">
                <el-input
                  v-model="validateForm.detailAddress"
                  placeholder="请输入详细地址"
                  :disabled="loading"
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  @click="validateAddress"
                  :loading="loading"
                  :disabled="!validateForm.codes.length || !validateForm.detailAddress"
                >
                  🔍 验证地址
                </el-button>
                <el-button @click="clearValidateForm" :disabled="loading"> 清空 </el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 验证结果 -->
          <el-card v-if="validateResult" class="result-card">
            <template #header>
              <span>{{ validateResult.isValid ? '✅' : '❌' }} 验证结果</span>
            </template>
            <AddressValidationResult :result="validateResult" />
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import AddressResult from './address/AddressResult.vue'
  import BatchAddressResult from './address/BatchAddressResult.vue'
  import AddressValidationResult from './address/AddressValidationResult.vue'
  import AreaCascader from './AddressCascader.vue'
  import { addressAPI } from '../api/address'

  export default {
    name: 'AddressParse',
    components: {
      AddressResult,
      BatchAddressResult,
      AddressValidationResult,
      AreaCascader
    },
    setup() {
      const activeTab = ref('single')
      const loading = ref(false)

      // 单个地址解析表单
      const singleForm = reactive({
        text: '',
        lat: 30.0,
        lng: 110.0
      })

      // 批量地址解析表单
      const batchForm = reactive({
        addresses: [],
        concurrency: 5
      })

      // 地址验证表单
      const validateForm = reactive({
        codes: [],
        detailAddress: ''
      })

      // 结果数据
      const singleResult = ref(null)
      const batchResult = ref(null)
      const validateResult = ref(null)

      // 示例数据
      const exampleAddresses = [
        '李健\n17099916606\n湖南省永州市冷水滩区珊瑚街道 园丁山庄8栋19号门面妈妈驿站',
        '张三\n13800138000\n北京市海淀区西二旗 中关村软件园 腾讯大厦B座',
        '王五\n15988776655\n上海市浦东新区陆家嘴 金茂大厦20层',
        '刘六\n18922334455\n广东省深圳市南山区科技园 腾讯大厦'
      ]

      // 加载示例
      const loadExample = () => {
        singleForm.text = exampleAddresses[0]
        singleResult.value = null
      }

      // 加载批量示例
      const loadBatchExample = () => {
        batchForm.addresses = exampleAddresses.map((text) => ({ text, lat: 30.0, lng: 110.0 }))
        batchResult.value = null
      }

      // 添加批量地址
      const addBatchAddress = () => {
        batchForm.addresses.push({ text: '', lat: 30.0, lng: 110.0 })
      }

      // 删除批量地址
      const removeBatchAddress = (index) => {
        batchForm.addresses.splice(index, 1)
      }

      // 解析单个地址
      const parseSingleAddress = async () => {
        if (!singleForm.text.trim()) {
          ElMessage.warning('请输入地址文本')
          return
        }

        loading.value = true
        try {
          const response = await addressAPI.parseAddress(singleForm)
          if (response.success) {
            singleResult.value = response.data.addressInfo
            ElMessage.success('地址解析成功')
          } else {
            ElMessage.error(response.message || '解析失败')
          }
        } catch (error) {
          console.error('解析地址失败:', error)
          ElMessage.error('解析失败: ' + (error.message || '网络错误'))
        } finally {
          loading.value = false
        }
      }

      // 批量解析地址
      const parseBatchAddresses = async () => {
        const validAddresses = batchForm.addresses.filter((addr) => addr.text.trim())
        if (validAddresses.length === 0) {
          ElMessage.warning('请至少输入一个地址')
          return
        }

        loading.value = true
        try {
          const response = await addressAPI.batchParseAddress({
            addresses: validAddresses,
            concurrency: batchForm.concurrency
          })
          if (response.success) {
            batchResult.value = response.data
            ElMessage.success(
              `批量解析完成，成功率: ${response.data.statistics.successRate.toFixed(1)}%`
            )
          } else {
            ElMessage.error(response.message || '批量解析失败')
          }
        } catch (error) {
          console.error('批量解析失败:', error)
          ElMessage.error('批量解析失败: ' + (error.message || '网络错误'))
        } finally {
          loading.value = false
        }
      }

      // 验证地址
      const validateAddress = async () => {
        if (validateForm.codes.length < 3) {
          ElMessage.warning('请选择完整的省市区')
          return
        }

        loading.value = true
        try {
          const response = await addressAPI.validateAddress({
            provinceCode: validateForm.codes[0],
            cityCode: validateForm.codes[1],
            districtCode: validateForm.codes[2],
            detailAddress: validateForm.detailAddress
          })
          if (response.success) {
            validateResult.value = response.data
            ElMessage.success('地址验证完成')
          } else {
            ElMessage.error(response.message || '验证失败')
          }
        } catch (error) {
          console.error('验证地址失败:', error)
          ElMessage.error('验证失败: ' + (error.message || '网络错误'))
        } finally {
          loading.value = false
        }
      }

      // 地区选择变化
      const onAreaChange = (codes) => {
        validateForm.codes = codes
        validateResult.value = null
      }

      // 清空表单
      const clearSingleForm = () => {
        singleForm.text = ''
        singleForm.lat = 30.0
        singleForm.lng = 110.0
        singleResult.value = null
      }

      const clearBatchForm = () => {
        batchForm.addresses = []
        batchResult.value = null
      }

      const clearValidateForm = () => {
        validateForm.codes = []
        validateForm.detailAddress = ''
        validateResult.value = null
      }

      return {
        activeTab,
        loading,
        singleForm,
        batchForm,
        validateForm,
        singleResult,
        batchResult,
        validateResult,
        loadExample,
        loadBatchExample,
        addBatchAddress,
        removeBatchAddress,
        parseSingleAddress,
        parseBatchAddresses,
        validateAddress,
        onAreaChange,
        clearSingleForm,
        clearBatchForm,
        clearValidateForm
      }
    }
  }
</script>

<style scoped>
  .address-parse-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .page-header {
    text-align: center;
    margin-bottom: 30px;
  }

  .page-header h2 {
    color: #409eff;
    margin-bottom: 10px;
  }

  .subtitle {
    color: #666;
    font-size: 14px;
  }

  .parse-tabs {
    margin-bottom: 20px;
  }

  .parse-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .statistics {
    display: flex;
    gap: 10px;
  }

  .form-tip {
    color: #999;
    font-size: 12px;
    margin-left: 10px;
  }

  .batch-addresses {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    max-height: 400px;
    overflow-y: auto;
  }

  .batch-address-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: flex-start;
  }

  .batch-address-item:last-child {
    margin-bottom: 0;
  }

  .result-card {
    margin-top: 20px;
  }
</style>
