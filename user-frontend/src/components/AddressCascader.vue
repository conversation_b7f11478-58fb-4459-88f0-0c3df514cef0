<template>
  <div class="address-cascader">
    <el-cascader
      v-model="selectedValues"
      :options="processedOptions"
      :props="cascaderProps"
      :placeholder="placeholder"
      :size="size"
      :disabled="disabled"
      :clearable="clearable"
      :show-all-levels="showAllLevels"
      :collapse-tags="collapseTags"
      :separator="separator"
      :filterable="filterable"
      @change="handleChange"
      @expand-change="handleExpandChange"
      @blur="handleBlur"
      @focus="handleFocus"
      @visible-change="handleVisibleChange"
      @remove-tag="handleRemoveTag"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed, watch } from 'vue'
  import type { CascaderOption, CascaderProps, CascaderValue } from 'element-plus'

  // 地址数据接口
  interface AreaItem extends CascaderOption {
    value: string
    label: string
    children?: AreaItem[]
  }

  interface AreaResponse {
    code: number
    msg: string
    data: AreaItem[]
  }

  // Props 定义
  interface Props {
    modelValue?: CascaderValue
    placeholder?: string
    size?: 'large' | 'default' | 'small'
    disabled?: boolean
    clearable?: boolean
    showAllLevels?: boolean
    collapseTags?: boolean
    separator?: string
    filterable?: boolean
    level?: 1 | 2 | 3 // 选择级别：1-省，2-市，3-区
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择地址',
    size: 'default',
    disabled: false,
    clearable: true,
    showAllLevels: true,
    collapseTags: false,
    separator: ' / ',
    filterable: true,
    level: 3
  })

  // Emits 定义
  interface Emits {
    (e: 'update:modelValue', value: CascaderValue): void
    (e: 'change', value: CascaderValue, selectedData: AreaItem[]): void
    (e: 'expand-change', value: CascaderValue): void
    (e: 'blur', event: FocusEvent): void
    (e: 'focus', event: FocusEvent): void
    (e: 'visible-change', visible: boolean): void
    (e: 'remove-tag', value: CascaderValue): void
  }

  const emit = defineEmits<Emits>()

  // 响应式数据
  const areaOptions = ref<AreaItem[]>([])
  const selectedValues = ref<CascaderValue>([])
  const loading = ref(false)

  // 级联选择器配置
  const cascaderProps: CascaderProps = {
    value: 'value',
    label: 'label',
    children: 'children',
    disabled: 'disabled',
    leaf: 'leaf',
    expandTrigger: 'click',
    checkStrictly: props.level < 3, // 如果不是三级选择，允许选择任意级别
    emitPath: true
  }

  // 计算属性：处理不同级别的选择
  const processedOptions = computed<AreaItem[]>(() => {
    if (props.level === 1) {
      // 只选择省级
      return areaOptions.value.map((province) => ({
        ...province,
        children: undefined,
        leaf: true
      }))
    } else if (props.level === 2) {
      // 选择到市级
      return areaOptions.value.map((province) => ({
        ...province,
        children: province.children?.map((city) => ({
          ...city,
          children: undefined,
          leaf: true
        }))
      }))
    }
    // 默认三级选择
    return areaOptions.value
  })

  // 监听 modelValue 变化
  watch(
    () => props.modelValue,
    (newValue) => {
      selectedValues.value = newValue ?? []
    },
    { immediate: true }
  )

  // 加载地址数据
  const loadAreaData = async () => {
    try {
      loading.value = true
      // 尝试多个数据源路径
      const dataSources = [
        './getAreaCascaderVo.json',
        './data/getAreaCascaderVo.json',
        './data/areas.json'
      ]

      let data: AreaResponse | null = null

      for (const source of dataSources) {
        try {
          console.log('🔍 尝试加载地址数据:', source)
          const response = await fetch(source)
          if (response.ok) {
            data = await response.json()
            console.log('✅ 成功加载地址数据:', source, data)
            break
          }
        } catch (err) {
          console.log('❌ 数据源加载失败:', source, err)
          continue
        }
      }

      if (!data) {
        throw new Error('所有数据源都无法加载')
      }

      if (data.code === 200 && data.data) {
        areaOptions.value = data.data
        console.log('✅ 地址数据加载成功，共', data.data.length, '个省份')
      } else {
        console.error('地址数据格式错误:', data)
        throw new Error(data.msg || '地址数据格式错误')
      }
    } catch (error) {
      console.error('加载地址数据失败:', error)
      // 可以在这里添加错误提示
      // ElMessage.error('加载地址数据失败，请刷新页面重试')
    } finally {
      loading.value = false
    }
  }

  // 处理选择变化
  const toStrArr = (val: CascaderValue): string[] =>
    Array.isArray(val) ? (val as (string | number)[]).map(String) : [String(val as string | number)]

  const handleChange = (value: CascaderValue) => {
    const strArr = toStrArr(value)
    selectedValues.value = strArr
    emit('update:modelValue', strArr)

    // 获取选中的完整数据
    const selectedData = getSelectedData(strArr)
    emit('change', strArr, selectedData)
  }

  // 获取选中项的完整数据
  const getSelectedData = (values: string[]): AreaItem[] => {
    const result: AreaItem[] = []
    let currentOptions = areaOptions.value

    for (let i = 0; i < values.length; i++) {
      const value = values[i]
      const option = currentOptions.find((item) => item.value === value)
      if (option) {
        result.push(option)
        if (option.children && i < values.length - 1) {
          currentOptions = option.children
        }
      }
    }

    return result
  }

  // 其他事件处理
  const handleExpandChange = (value: any) => {
    emit('expand-change', value)
  }

  const handleBlur = (event: FocusEvent) => {
    emit('blur', event)
  }

  const handleFocus = (event: FocusEvent) => {
    emit('focus', event)
  }

  const handleVisibleChange = (visible: boolean) => {
    emit('visible-change', visible)
  }

  const handleRemoveTag = (value: CascaderValue) => {
    emit('remove-tag', value)
  }

  // 公开方法：获取选中的地址文本
  const getSelectedText = (): string => {
    const selectedData = getSelectedData(toStrArr(selectedValues.value))
    return selectedData.map((item) => item.label).join(props.separator)
  }

  // 公开方法：获取选中的地址代码
  const getSelectedCodes = (): string[] => {
    return toStrArr(selectedValues.value)
  }

  // 公开方法：清空选择
  const clearSelection = () => {
    selectedValues.value = []
    emit('update:modelValue', [])
    emit('change', [], [])
  }

  // 根据地区名称查找对应的地区代码
  const findAreaCodeByName = (
    name: string,
    level: 'province' | 'city' | 'district',
    parentCode?: string
  ): string | null => {
    console.log('🔍 根据名称查找地区代码:', { name, level, parentCode })

    if (level === 'province') {
      // 省份级别：直接在根级别查找
      const province = areaOptions.value.find((p) => {
        // 精确匹配或添加后缀匹配
        return (
          p.label === name ||
          p.label === name + '省' ||
          p.label === name + '市' ||
          p.label === name + '自治区' ||
          p.label === name + '特别行政区' ||
          // 反向匹配：去掉后缀
          (p.label.endsWith('省') && p.label.slice(0, -1) === name) ||
          (p.label.endsWith('市') && p.label.slice(0, -1) === name) ||
          (p.label.endsWith('自治区') && p.label.slice(0, -3) === name) ||
          (p.label.endsWith('特别行政区') && p.label.slice(0, -5) === name)
        )
      })
      console.log('🔍 找到省份:', province)
      return province?.value || null
    }

    if (level === 'city' && parentCode) {
      // 城市级别：在指定省份下查找
      const province = areaOptions.value.find((p) => p.value === parentCode)
      if (province?.children) {
        const city = province.children.find((c) => {
          // 🔥 增强匹配逻辑：特别处理直辖市
          // 对于北京、天津、上海、重庆，城市名称通常与省份名称相同
          if (
            province.label === '北京市' ||
            province.label === '天津市' ||
            province.label === '上海市' ||
            province.label === '重庆市'
          ) {
            // 直辖市的城市级别通常就是省份本身
            return (
              c.label === province.label ||
              c.label === name ||
              c.label === name + '市' ||
              (c.label.endsWith('市') && c.label.slice(0, -1) === name)
            )
          }

          // 普通省份的城市匹配
          return (
            c.label === name ||
            c.label === name + '市' ||
            c.label === name + '区' ||
            c.label === name + '县' ||
            c.label === name + '自治州' ||
            c.label === name + '地区' ||
            // 反向匹配：去掉后缀
            (c.label.endsWith('市') && c.label.slice(0, -1) === name) ||
            (c.label.endsWith('区') && c.label.slice(0, -1) === name) ||
            (c.label.endsWith('县') && c.label.slice(0, -1) === name) ||
            (c.label.endsWith('自治州') && c.label.slice(0, -3) === name) ||
            (c.label.endsWith('地区') && c.label.slice(0, -2) === name)
          )
        })
        console.log('🔍 找到城市:', city)
        return city?.value || null
      }
    }

    if (level === 'district' && parentCode) {
      // 区县级别：在指定城市下查找
      const province = areaOptions.value.find((p) =>
        p.children?.some((c) => c.value === parentCode)
      )
      const city = province?.children?.find((c) => c.value === parentCode)
      if (city?.children) {
        const district = city.children.find((d) => {
          // 精确匹配或添加/去除后缀匹配
          return (
            d.label === name ||
            d.label === name + '区' ||
            d.label === name + '县' ||
            d.label === name + '市' ||
            d.label === name + '旗' ||
            d.label === name + '自治县' ||
            // 反向匹配：去掉后缀
            (d.label.endsWith('区') && d.label.slice(0, -1) === name) ||
            (d.label.endsWith('县') && d.label.slice(0, -1) === name) ||
            (d.label.endsWith('市') && d.label.slice(0, -1) === name) ||
            (d.label.endsWith('旗') && d.label.slice(0, -1) === name) ||
            (d.label.endsWith('自治县') && d.label.slice(0, -3) === name)
          )
        })
        console.log('🔍 找到区县:', district)
        return district?.value || null
      }
    }

    return null
  }

  // 🔥 简化版：通过地区名称设置选择值
  const setSelectedValuesByNames = (
    provinceName: string,
    cityName?: string,
    districtName?: string
  ) => {
    console.log('📍 设置地区:', { provinceName, cityName, districtName })

    // 简单粗暴：直接查找匹配的地区
    const codes: string[] = []

    // 查找省份
    const province = areaOptions.value.find(
      (p) =>
        p.label === provinceName ||
        p.label.includes(provinceName) ||
        provinceName.includes(p.label.replace(/(省|市|自治区|特别行政区)$/, ''))
    )

    if (province) {
      codes.push(province.value)
      console.log('✅ 找到省份:', province.label)

      // 查找城市
      if (cityName && province.children) {
        const city = province.children.find(
          (c) =>
            c.label === cityName ||
            c.label.includes(cityName) ||
            cityName.includes(c.label.replace(/(市|区|县|地区|自治州|盟)$/, ''))
        )

        if (city) {
          codes.push(city.value)
          console.log('✅ 找到城市:', city.label)

          // 查找区县
          if (districtName && city.children) {
            const district = city.children.find(
              (d) =>
                d.label === districtName ||
                d.label.includes(districtName) ||
                districtName.includes(d.label.replace(/(区|县|市|旗|自治县)$/, ''))
            )

            if (district) {
              codes.push(district.value)
              console.log('✅ 找到区县:', district.label)
            }
          }
        }
      }
    }

    // 直接设置结果
    if (codes.length > 0) {
      selectedValues.value = codes
      emit('update:modelValue', codes)
      const selectedData = getSelectedData(codes)
      emit('change', codes, selectedData)
      console.log('✅ 地区设置成功:', codes)
    } else {
      console.warn('⚠️ 未找到匹配的地区')
    }
  }

  // 公开方法：直接设置选择值（兼容原有接口）
  const setSelectedValues = (values: string[]) => {
    console.log('🔍 AddressCascader.setSelectedValues 原始值:', values)

    if (!values || values.length === 0) {
      selectedValues.value = []
      emit('update:modelValue', [])
      return
    }

    // 直接设置值（假设传入的是正确的地区代码）
    selectedValues.value = [...values]
    emit('update:modelValue', values)

    // 获取选中的完整数据并触发change事件
    const selectedData = getSelectedData(values)
    emit('change', values, selectedData)
  }

  // 暴露方法给父组件
  defineExpose({
    getSelectedText,
    getSelectedCodes,
    clearSelection,
    setSelectedValues,
    setSelectedValuesByNames, // 新增：通过名称设置值
    selectedValues // 也暴露响应式数据供直接访问
  })

  // 组件挂载时加载数据
  onMounted(() => {
    loadAreaData()
  })
</script>

<style scoped>
  .address-cascader {
    width: 100%;
  }

  .address-cascader :deep(.el-cascader) {
    width: 100%;
  }
</style>
