<template>
  <div class="address-validation-result">
    <!-- 验证状态 -->
    <div class="validation-status">
      <el-result
        :icon="result.isValid ? 'success' : 'error'"
        :title="result.isValid ? '地址验证通过' : '地址验证失败'"
        :sub-title="getStatusMessage()"
      >
        <template #extra>
          <el-progress
            :percentage="Math.round(result.confidence * 100)"
            :color="getConfidenceColor()"
            :stroke-width="8"
          />
          <p class="confidence-text">置信度: {{ (result.confidence * 100).toFixed(1) }}%</p>
        </template>
      </el-result>
    </div>

    <!-- 标准化地址 -->
    <div v-if="result.standardizedAddress" class="standardized-section">
      <h4>📍 标准化地址</h4>
      <el-card class="address-card">
        <div class="standardized-address">
          {{ result.standardizedAddress }}
        </div>
        <div class="address-actions">
          <el-button type="primary" size="small" @click="copyAddress"> 📋 复制地址 </el-button>
          <el-button type="success" size="small" @click="useAddress"> ✅ 使用此地址 </el-button>
        </div>
      </el-card>
    </div>

    <!-- 建议信息 -->
    <div v-if="result.suggestions && result.suggestions.length > 0" class="suggestions-section">
      <h4>💡 建议</h4>
      <el-alert
        v-for="(suggestion, index) in result.suggestions"
        :key="index"
        :title="suggestion"
        type="info"
        :closable="false"
        show-icon
        class="suggestion-item"
      />
    </div>

    <!-- 验证详情 -->
    <div class="validation-details">
      <h4>📊 验证详情</h4>
      <el-descriptions border :column="2">
        <el-descriptions-item label="验证状态">
          <el-tag :type="result.isValid ? 'success' : 'danger'">
            {{ result.isValid ? '有效' : '无效' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="置信度">
          <el-tag :type="getConfidenceType()"> {{ (result.confidence * 100).toFixed(1) }}% </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="验证时间">
          <el-tag type="info">{{ new Date().toLocaleString() }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="地址长度">
          <el-tag type="info">
            {{ result.standardizedAddress ? result.standardizedAddress.length : 0 }} 字符
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button type="primary" @click="exportResult"> 📤 导出验证结果 </el-button>
      <el-button type="info" @click="validateAgain"> 🔄 重新验证 </el-button>
    </div>
  </div>
</template>

<script>
  import { ElMessage } from 'element-plus'

  export default {
    name: 'AddressValidationResult',
    props: {
      result: {
        type: Object,
        required: true
      }
    },
    emits: ['use-address', 'validate-again'],
    methods: {
      // 获取状态消息
      getStatusMessage() {
        if (this.result.isValid) {
          if (this.result.confidence >= 0.9) {
            return '地址格式正确，置信度很高'
          } else if (this.result.confidence >= 0.7) {
            return '地址格式正确，置信度较高'
          } else {
            return '地址格式正确，但置信度一般'
          }
        } else {
          return '地址格式不正确或不完整'
        }
      },

      // 获取置信度颜色
      getConfidenceColor() {
        const confidence = this.result.confidence
        if (confidence >= 0.9) return '#67C23A'
        if (confidence >= 0.7) return '#E6A23C'
        if (confidence >= 0.5) return '#F56C6C'
        return '#909399'
      },

      // 获取置信度类型
      getConfidenceType() {
        const confidence = this.result.confidence
        if (confidence >= 0.9) return 'success'
        if (confidence >= 0.7) return 'warning'
        if (confidence >= 0.5) return 'danger'
        return 'info'
      },

      // 复制地址
      async copyAddress() {
        if (!this.result.standardizedAddress) {
          ElMessage.warning('没有可复制的地址')
          return
        }

        try {
          await navigator.clipboard.writeText(this.result.standardizedAddress)
          ElMessage.success('地址已复制到剪贴板')
        } catch (error) {
          console.error('复制失败:', error)
          ElMessage.error('复制失败')
        }
      },

      // 使用地址
      useAddress() {
        if (!this.result.standardizedAddress) {
          ElMessage.warning('没有可使用的地址')
          return
        }

        this.$emit('use-address', this.result.standardizedAddress)
        ElMessage.success('地址已应用')
      },

      // 导出验证结果
      exportResult() {
        const data = {
          isValid: this.result.isValid,
          confidence: this.result.confidence,
          standardizedAddress: this.result.standardizedAddress || '',
          suggestions: this.result.suggestions || [],
          validationTime: new Date().toISOString(),
          status: this.result.isValid ? '有效' : '无效',
          confidenceLevel: this.getConfidenceLevel()
        }

        const jsonStr = JSON.stringify(data, null, 2)
        const blob = new Blob([jsonStr], { type: 'application/json' })
        const url = URL.createObjectURL(blob)

        const a = document.createElement('a')
        a.href = url
        a.download = `address_validation_${Date.now()}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        ElMessage.success('验证结果已导出')
      },

      // 获取置信度等级
      getConfidenceLevel() {
        const confidence = this.result.confidence
        if (confidence >= 0.9) return '很高'
        if (confidence >= 0.7) return '较高'
        if (confidence >= 0.5) return '一般'
        return '较低'
      },

      // 重新验证
      validateAgain() {
        this.$emit('validate-again')
      }
    }
  }
</script>

<style scoped>
  .address-validation-result {
    padding: 20px;
  }

  .validation-status {
    margin-bottom: 30px;
  }

  .confidence-text {
    margin-top: 10px;
    font-size: 14px;
    color: #666;
  }

  .standardized-section,
  .suggestions-section,
  .validation-details {
    margin-bottom: 25px;
  }

  .standardized-section h4,
  .suggestions-section h4,
  .validation-details h4 {
    color: #409eff;
    margin-bottom: 15px;
    font-size: 16px;
  }

  .address-card {
    background: #f8f9fa;
  }

  .standardized-address {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 4px;
    border-left: 4px solid #409eff;
  }

  .address-actions {
    text-align: right;
  }

  .suggestion-item {
    margin-bottom: 10px;
  }

  .suggestion-item:last-child {
    margin-bottom: 0;
  }

  .action-section {
    margin-top: 30px;
    text-align: center;
  }

  .action-section .el-button {
    margin: 0 5px;
  }

  :deep(.el-result__title) {
    font-size: 20px;
  }

  :deep(.el-result__subtitle) {
    font-size: 14px;
  }

  :deep(.el-progress-bar__outer) {
    border-radius: 10px;
  }

  :deep(.el-progress-bar__inner) {
    border-radius: 10px;
  }
</style>
