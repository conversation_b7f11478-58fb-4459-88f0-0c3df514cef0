<template>
  <div class="batch-address-result">
    <!-- 结果列表 -->
    <el-table
      :data="results"
      border
      stripe
      max-height="600"
      :default-sort="{ prop: 'index', order: 'ascending' }"
    >
      <el-table-column prop="index" label="序号" width="80" sortable>
        <template #default="{ row }">
          {{ row.index + 1 }}
        </template>
      </el-table-column>

      <el-table-column prop="success" label="状态" width="100" sortable>
        <template #default="{ row }">
          <el-tag :type="row.success ? 'success' : 'danger'">
            {{ row.success ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="联系信息" width="200">
        <template #default="{ row }">
          <div v-if="row.success && row.addressInfo">
            <div v-if="row.addressInfo.name" class="contact-item">
              <el-tag size="small" type="success">{{ row.addressInfo.name }}</el-tag>
            </div>
            <div v-if="row.addressInfo.mobile" class="contact-item">
              <el-tag size="small" type="primary">{{ row.addressInfo.mobile }}</el-tag>
            </div>
          </div>
          <span v-else class="error-text">{{ row.error || '解析失败' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="地址信息" min-width="300">
        <template #default="{ row }">
          <div v-if="row.success && row.addressInfo">
            <div class="address-line">
              <span class="address-part" v-if="row.addressInfo.provinceName">
                {{ row.addressInfo.provinceName }}
              </span>
              <span class="address-part" v-if="row.addressInfo.cityName">
                {{ row.addressInfo.cityName }}
              </span>
              <span class="address-part" v-if="row.addressInfo.districtName">
                {{ row.addressInfo.districtName }}
              </span>
            </div>
            <div class="detail-address" v-if="row.addressInfo.detailAddress">
              {{ row.addressInfo.detailAddress }}
            </div>
          </div>
          <span v-else class="error-text">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="processTime" label="耗时" width="100" sortable>
        <template #default="{ row }"> {{ (row.processTime * 1000).toFixed(0) }}ms </template>
      </el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button v-if="row.success" type="primary" size="small" @click="viewDetail(row)">
            查看详情
          </el-button>
          <el-button v-else type="danger" size="small" @click="viewError(row)">
            查看错误
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 批量操作 -->
    <div class="batch-actions">
      <el-button type="primary" @click="exportSuccessResults">
        📤 导出成功结果 ({{ successCount }})
      </el-button>
      <el-button type="warning" @click="exportFailedResults">
        📋 导出失败列表 ({{ failedCount }})
      </el-button>
      <el-button type="success" @click="exportAllResults"> 💾 导出全部结果 </el-button>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="解析详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <AddressResult v-if="selectedResult" :result="selectedResult" />
    </el-dialog>

    <!-- 错误对话框 -->
    <el-dialog v-model="errorDialogVisible" title="错误详情" width="600px">
      <div class="error-detail">
        <h4>错误信息：</h4>
        <el-alert :title="selectedError" type="error" :closable="false" show-icon />

        <h4 style="margin-top: 20px">原始文本：</h4>
        <el-input :value="selectedOriginalText" type="textarea" :rows="4" readonly />
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { ref, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import AddressResult from './AddressResult.vue'

  export default {
    name: 'BatchAddressResult',
    components: {
      AddressResult
    },
    props: {
      results: {
        type: Array,
        required: true
      }
    },
    setup(props) {
      const detailDialogVisible = ref(false)
      const errorDialogVisible = ref(false)
      const selectedResult = ref(null)
      const selectedError = ref('')
      const selectedOriginalText = ref('')

      // 计算成功和失败数量
      const successCount = computed(() => {
        return props.results.filter((r) => r.success).length
      })

      const failedCount = computed(() => {
        return props.results.filter((r) => !r.success).length
      })

      // 查看详情
      const viewDetail = (row) => {
        selectedResult.value = row.addressInfo
        detailDialogVisible.value = true
      }

      // 查看错误
      const viewError = (row) => {
        selectedError.value = row.error || '未知错误'
        selectedOriginalText.value = getOriginalText(row.index)
        errorDialogVisible.value = true
      }

      // 获取原始文本（这里需要从父组件传递或其他方式获取）
      const getOriginalText = (index) => {
        // 这里应该从父组件获取原始文本
        return `地址 ${index + 1} 的原始文本`
      }

      // 导出成功结果
      const exportSuccessResults = () => {
        const successResults = props.results
          .filter((r) => r.success)
          .map((r) => ({
            index: r.index + 1,
            name: r.addressInfo?.name || '',
            mobile: r.addressInfo?.mobile || '',
            phone: r.addressInfo?.phone || '',
            province: r.addressInfo?.provinceName || '',
            city: r.addressInfo?.cityName || '',
            district: r.addressInfo?.districtName || '',
            detailAddress: r.addressInfo?.detailAddress || '',
            fullAddress: getFullAddress(r.addressInfo),
            processTime: r.processTime
          }))

        exportToCSV(successResults, 'batch_address_success_results')
      }

      // 导出失败结果
      const exportFailedResults = () => {
        const failedResults = props.results
          .filter((r) => !r.success)
          .map((r) => ({
            index: r.index + 1,
            error: r.error || '未知错误',
            processTime: r.processTime
          }))

        exportToCSV(failedResults, 'batch_address_failed_results')
      }

      // 导出全部结果
      const exportAllResults = () => {
        const allResults = props.results.map((r) => ({
          index: r.index + 1,
          success: r.success,
          name: r.success ? r.addressInfo?.name || '' : '',
          mobile: r.success ? r.addressInfo?.mobile || '' : '',
          phone: r.success ? r.addressInfo?.phone || '' : '',
          province: r.success ? r.addressInfo?.provinceName || '' : '',
          city: r.success ? r.addressInfo?.cityName || '' : '',
          district: r.success ? r.addressInfo?.districtName || '' : '',
          detailAddress: r.success ? r.addressInfo?.detailAddress || '' : '',
          fullAddress: r.success ? getFullAddress(r.addressInfo) : '',
          error: r.success ? '' : r.error || '未知错误',
          processTime: r.processTime
        }))

        exportToCSV(allResults, 'batch_address_all_results')
      }

      // 获取完整地址
      const getFullAddress = (addressInfo) => {
        if (!addressInfo) return ''
        const parts = []
        if (addressInfo.provinceName) parts.push(addressInfo.provinceName)
        if (addressInfo.cityName) parts.push(addressInfo.cityName)
        if (addressInfo.districtName) parts.push(addressInfo.districtName)
        if (addressInfo.townName) parts.push(addressInfo.townName)
        if (addressInfo.detailAddress) parts.push(addressInfo.detailAddress)
        return parts.join('')
      }

      // 导出为CSV
      const exportToCSV = (data, filename) => {
        if (data.length === 0) {
          ElMessage.warning('没有数据可导出')
          return
        }

        const headers = Object.keys(data[0])
        const csvContent = [
          headers.join(','),
          ...data.map((row) =>
            headers
              .map((header) => {
                const value = row[header]
                // 处理包含逗号的值
                return typeof value === 'string' && value.includes(',') ? `"${value}"` : value
              })
              .join(',')
          )
        ].join('\n')

        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
        const url = URL.createObjectURL(blob)

        const a = document.createElement('a')
        a.href = url
        a.download = `${filename}_${Date.now()}.csv`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        ElMessage.success('数据已导出')
      }

      return {
        detailDialogVisible,
        errorDialogVisible,
        selectedResult,
        selectedError,
        selectedOriginalText,
        successCount,
        failedCount,
        viewDetail,
        viewError,
        exportSuccessResults,
        exportFailedResults,
        exportAllResults
      }
    }
  }
</script>

<style scoped>
  .batch-address-result {
    padding: 20px;
  }

  .contact-item {
    margin-bottom: 5px;
  }

  .contact-item:last-child {
    margin-bottom: 0;
  }

  .address-line {
    margin-bottom: 5px;
  }

  .address-part {
    margin-right: 5px;
    color: #409eff;
  }

  .detail-address {
    color: #666;
    font-size: 12px;
  }

  .error-text {
    color: #f56c6c;
    font-size: 12px;
  }

  .batch-actions {
    margin-top: 20px;
    text-align: center;
  }

  .batch-actions .el-button {
    margin: 0 5px;
  }

  .error-detail h4 {
    color: #409eff;
    margin-bottom: 10px;
  }

  :deep(.el-table .cell) {
    padding: 8px 0;
  }

  :deep(.el-table__row) {
    height: auto;
  }
</style>
