<template>
  <div class="address-result">
    <el-row :gutter="20">
      <!-- 联系信息 -->
      <el-col :span="12">
        <div class="info-section">
          <h4>👤 联系信息</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="姓名">
              <el-tag v-if="result.name" type="success">{{ result.name }}</el-tag>
              <span v-else class="empty-value">未识别</span>
            </el-descriptions-item>
            <el-descriptions-item label="手机号">
              <el-tag v-if="result.mobile" type="primary">{{ result.mobile }}</el-tag>
              <span v-else class="empty-value">未识别</span>
            </el-descriptions-item>
            <el-descriptions-item label="电话">
              <el-tag v-if="result.phone" type="info">{{ result.phone }}</el-tag>
              <span v-else class="empty-value">未识别</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-col>

      <!-- 地址信息 -->
      <el-col :span="12">
        <div class="info-section">
          <h4>📍 地址信息</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="省份">
              <el-tag v-if="result.provinceName" type="success">
                {{ result.provinceName }}
                <span v-if="result.provinceCode" class="code">({{ result.provinceCode }})</span>
              </el-tag>
              <span v-else class="empty-value">未识别</span>
            </el-descriptions-item>
            <el-descriptions-item label="城市">
              <el-tag v-if="result.cityName" type="primary">
                {{ result.cityName }}
                <span v-if="result.cityCode" class="code">({{ result.cityCode }})</span>
              </el-tag>
              <span v-else class="empty-value">未识别</span>
            </el-descriptions-item>
            <el-descriptions-item label="区县">
              <el-tag v-if="result.districtName" type="info">
                {{ result.districtName }}
                <span v-if="result.districtCode" class="code">({{ result.districtCode }})</span>
              </el-tag>
              <span v-else class="empty-value">未识别</span>
            </el-descriptions-item>
            <el-descriptions-item label="乡镇" v-if="result.townName">
              <el-tag type="warning">
                {{ result.townName }}
                <span v-if="result.townCode" class="code">({{ result.townCode }})</span>
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-col>
    </el-row>

    <!-- 详细地址 -->
    <div class="info-section">
      <h4>🏠 详细地址</h4>
      <el-card class="address-card">
        <div class="address-content">
          <div class="detail-address">
            <strong>详细地址：</strong>
            <span v-if="result.detailAddress">{{ result.detailAddress }}</span>
            <span v-else class="empty-value">未识别</span>
          </div>
          <div class="full-address">
            <strong>完整地址：</strong>
            <span v-if="result.fullAddress">{{ result.fullAddress }}</span>
            <span v-else>{{ getFullAddress() }}</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 坐标信息 -->
    <div class="info-section" v-if="result.latitude || result.longitude">
      <h4>🌍 坐标信息</h4>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-statistic title="纬度" :value="result.latitude" :precision="6" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="经度" :value="result.longitude" :precision="6" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="置信度" :value="result.confidence || 0" suffix="%" :precision="1" />
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button type="primary" @click="copyResult"> 📋 复制结果 </el-button>
      <el-button type="success" @click="exportResult"> 📤 导出数据 </el-button>
      <el-button type="info" @click="viewOnMap" v-if="result.latitude && result.longitude">
        🗺️ 查看地图
      </el-button>
    </div>
  </div>
</template>

<script>
  import { ElMessage } from 'element-plus'

  export default {
    name: 'AddressResult',
    props: {
      result: {
        type: Object,
        required: true
      }
    },
    methods: {
      // 获取完整地址
      getFullAddress() {
        const parts = []
        if (this.result.provinceName) parts.push(this.result.provinceName)
        if (this.result.cityName) parts.push(this.result.cityName)
        if (this.result.districtName) parts.push(this.result.districtName)
        if (this.result.townName) parts.push(this.result.townName)
        if (this.result.detailAddress) parts.push(this.result.detailAddress)
        return parts.join('')
      },

      // 复制结果
      async copyResult() {
        const text = this.formatResultText()
        try {
          await navigator.clipboard.writeText(text)
          ElMessage.success('结果已复制到剪贴板')
        } catch (error) {
          console.error('复制失败:', error)
          ElMessage.error('复制失败')
        }
      },

      // 格式化结果文本
      formatResultText() {
        const lines = []
        lines.push('=== 地址解析结果 ===')

        if (this.result.name) lines.push(`姓名：${this.result.name}`)
        if (this.result.mobile) lines.push(`手机：${this.result.mobile}`)
        if (this.result.phone) lines.push(`电话：${this.result.phone}`)

        lines.push(`地址：${this.getFullAddress()}`)

        if (this.result.latitude && this.result.longitude) {
          lines.push(`坐标：${this.result.latitude}, ${this.result.longitude}`)
        }

        return lines.join('\n')
      },

      // 导出结果
      exportResult() {
        const data = {
          name: this.result.name || '',
          mobile: this.result.mobile || '',
          phone: this.result.phone || '',
          province: this.result.provinceName || '',
          city: this.result.cityName || '',
          district: this.result.districtName || '',
          town: this.result.townName || '',
          detailAddress: this.result.detailAddress || '',
          fullAddress: this.getFullAddress(),
          latitude: this.result.latitude || 0,
          longitude: this.result.longitude || 0,
          confidence: this.result.confidence || 0
        }

        const jsonStr = JSON.stringify(data, null, 2)
        const blob = new Blob([jsonStr], { type: 'application/json' })
        const url = URL.createObjectURL(blob)

        const a = document.createElement('a')
        a.href = url
        a.download = `address_result_${Date.now()}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        ElMessage.success('结果已导出')
      },

      // 在地图上查看
      viewOnMap() {
        const lat = this.result.latitude
        const lng = this.result.longitude
        const url = `https://www.google.com/maps?q=${lat},${lng}`
        window.open(url, '_blank')
      }
    }
  }
</script>

<style scoped>
  .address-result {
    padding: 20px;
  }

  .info-section {
    margin-bottom: 20px;
  }

  .info-section h4 {
    color: #409eff;
    margin-bottom: 15px;
    font-size: 16px;
  }

  .empty-value {
    color: #999;
    font-style: italic;
  }

  .code {
    color: #999;
    font-size: 12px;
  }

  .address-card {
    background: #f8f9fa;
  }

  .address-content {
    line-height: 1.8;
  }

  .detail-address,
  .full-address {
    margin-bottom: 10px;
  }

  .detail-address:last-child,
  .full-address:last-child {
    margin-bottom: 0;
  }

  .action-section {
    margin-top: 20px;
    text-align: center;
  }

  .action-section .el-button {
    margin: 0 5px;
  }

  :deep(.el-descriptions-item__label) {
    width: 80px;
    font-weight: bold;
  }

  :deep(.el-statistic__content) {
    font-size: 18px;
  }
</style>
