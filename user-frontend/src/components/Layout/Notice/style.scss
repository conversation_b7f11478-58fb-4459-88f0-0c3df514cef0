@use '@styles/variables.scss' as *;
@use '@styles/mixin.scss' as *;

.notice {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 350px;
  height: 450px;
  overflow: hidden;
  background: var(--art-main-bg-color);
  border: 1px solid var(--art-border-color);
  border-radius: calc(var(--custom-radius) / 2 + 6px) !important;
  box-shadow:
    0 8px 26px -4px hsl(0deg 0% 8% / 15%),
    0 8px 9px -5px hsl(0deg 0% 8% / 6%);
  transition: all 0.2s;
  transform-origin: center top 0;
  will-change: top, left;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    margin-top: 15px;

    span {
      font-size: 12px;
    }

    .text {
      font-size: 16px;
      font-weight: 500;
      color: var(--art-gray-800);
    }

    .btn {
      padding: 4px 6px;
      cursor: pointer;
      user-select: none;
      border-radius: 6px;

      &:hover {
        background-color: var(--art-gray-200);
      }
    }
  }

  .bar {
    box-sizing: border-box;
    display: flex;
    width: 100%;
    height: 50px;
    padding: 0 15px;
    line-height: 50px;
    border-bottom: 1px solid var(--art-border-color);

    li {
      height: 48px;
      margin-right: 20px;
      overflow: hidden;
      font-size: 13px;
      color: var(--art-gray-700);
      cursor: pointer;
      transition: color 0.3s;

      @include userSelect;

      &:last-of-type {
        margin-right: 0;
      }

      &:hover {
        color: var(--art-gray-900);
      }

      &.active {
        color: var(--main-color) !important;
        border-bottom: 2px solid var(--main-color);
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 95px);

    .scroll {
      height: 100%;
      overflow-y: scroll;

      &::-webkit-scrollbar {
        width: 5px !important;
      }

      .notice-list {
        li {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          padding: 15px;
          cursor: pointer;

          &:hover {
            background-color: var(--art-gray-100);
          }

          &:last-of-type {
            border-bottom: 0;
          }

          .icon {
            width: 36px;
            height: 36px;
            line-height: 36px;
            text-align: center;
            border-radius: 8px;

            i {
              font-size: 18px;
              background: transparent !important;
            }
          }

          .text {
            width: calc(100% - 45px);
            margin-left: 15px;

            h4 {
              font-size: 14px;
              font-weight: 400;
              line-height: 22px;
              color: var(--art-gray-900);
            }

            p {
              margin-top: 5px;
              font-size: 12px;
              color: var(--art-gray-500);
            }
          }
        }
      }

      .user-list {
        li {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          padding: 15px;
          cursor: pointer;

          &:hover {
            background-color: var(--art-gray-100);
          }

          &:last-of-type {
            border-bottom: 0;
          }

          .avatar {
            width: 36px;
            height: 36px;

            img {
              width: 100%;
              height: 100%;
              border-radius: 8px;
            }
          }

          .text {
            width: calc(100% - 45px);
            margin-left: 15px;

            h4 {
              font-size: 13px;
              font-weight: 400;
              line-height: 22px;
              color: var(--art-gray-900);
            }

            p {
              margin-top: 5px;
              font-size: 12px;
              color: var(--art-gray-500);
            }
          }
        }
      }

      .base {
        li {
          box-sizing: border-box;
          padding: 15px 20px;

          &:last-of-type {
            border-bottom: 0;
          }

          p {
            font-size: 12px;
            color: var(--art-gray-500);
          }
        }
      }

      .empty-tips {
        position: relative;
        top: 100px;
        height: 100%;
        color: var(--art-gray-500);
        text-align: center;
        background: transparent !important;

        i {
          font-size: 60px;
        }

        p {
          margin-top: 15px;
          font-size: 12px;
          background: transparent !important;
        }
      }
    }
  }
}

.dark {
  .notice {
    ::-webkit-scrollbar-track {
      background-color: var(--art-main-bg-color);
    }

    ::-webkit-scrollbar-thumb {
      background-color: #222 !important;
    }
  }
}

@media only screen and (max-width: $device-phone) {
  .notice {
    top: 65px;
    right: 0;
    width: 100%;
    height: 80vh;
  }
}
