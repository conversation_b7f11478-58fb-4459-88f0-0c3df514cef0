<template>
  <div class="pickup-time-selector">
    <el-card class="pickup-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="header-icon">🕐</span>
          <span class="header-title">预约取件时间</span>
          <el-tooltip content="选择预约取件的日期和时间段" placement="top">
            <el-icon class="help-icon"><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>

      <el-form :model="pickupForm" :rules="rules" ref="formRef" label-width="100px">
        <!-- 预约时间选择 -->
        <el-form-item label="预约时间" prop="timeSlot">
          <div class="date-time-selector">
            <!-- 日期标签页 -->
            <el-tabs v-model="activeDate" @tab-change="handleDateTabChange" class="date-tabs">
              <el-tab-pane
                v-for="dateOption in availableDates"
                :key="dateOption.value"
                :label="dateOption.label"
                :name="dateOption.value"
                :disabled="dateOption.disabled"
              >
                <!-- 时间段选择 -->
                <div class="time-slots-container">
                  <el-radio-group
                    :model-value="getSelectedTimeSlot(dateOption.value)"
                    class="time-slot-group"
                    @update:model-value="(value) => handleTimeSlotChange(dateOption.value, value)"
                  >
                    <el-radio-button
                      v-for="slot in getAvailableTimeSlots(dateOption.value)"
                      :key="`${dateOption.value}-${slot.value}`"
                      :value="slot.value"
                      :disabled="slot.disabled"
                      class="time-slot-button"
                    >
                      <div class="slot-content">
                        <div class="slot-time">{{ slot.label }}</div>
                        <div class="slot-desc">{{ slot.description }}</div>
                        <div v-if="slot.disabled" class="slot-disabled-reason">{{ slot.disabledReason }}</div>
                      </div>
                    </el-radio-button>
                  </el-radio-group>

                  <!-- 无可用时间段提示 -->
                  <div v-if="getAvailableTimeSlots(dateOption.value).length === 0" class="no-slots-tip">
                    <el-empty description="今日已无可预约时间段" :image-size="60" />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-form-item>



        <!-- 预约信息预览 -->
        <div v-if="previewInfo" class="pickup-preview">
          <el-alert
            :title="previewInfo.title"
            :description="previewInfo.description"
            type="info"
            show-icon
            :closable="false"
          />
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'

// 组件属性定义
interface Props {
  modelValue?: {
    pickup_start_time?: string
    pickup_end_time?: string
  }
  disabled?: boolean
}

// 组件事件定义
interface Emits {
  (e: 'update:modelValue', value: {
    pickup_start_time: string
    pickup_end_time: string
  }): void
  (e: 'change', value: {
    pickup_start_time: string
    pickup_end_time: string
  }): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
})

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 基础时间段配置
const baseTimeSlots = [
  {
    value: '09:00-11:00',
    label: '09:00-11:00',
    description: '上午时段',
    startHour: 9
  },
  {
    value: '11:00-13:00',
    label: '11:00-13:00',
    description: '中午时段',
    startHour: 11
  },
  {
    value: '13:00-15:00',
    label: '13:00-15:00',
    description: '下午时段',
    startHour: 13
  },
  {
    value: '15:00-17:00',
    label: '15:00-17:00',
    description: '下午时段',
    startHour: 15
  },
  {
    value: '17:00-19:00',
    label: '17:00-19:00',
    description: '傍晚时段',
    startHour: 17
  }
]

// 表单数据
const pickupForm = reactive({
  timeSlot: ''
})

// 当前选中的日期
const activeDate = ref('')

// 可选日期配置
const availableDates = computed(() => {
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(today.getDate() + 1)
  const dayAfterTomorrow = new Date(today)
  dayAfterTomorrow.setDate(today.getDate() + 2)

  const formatDate = (date: Date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }
  const getWeekday = (date: Date) => {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return weekdays[date.getDay()]
  }

  return [
    {
      value: formatDate(today),
      label: `今天 ${getWeekday(today)} ${today.getMonth() + 1}/${today.getDate()}`,
      disabled: false,
      date: today
    },
    {
      value: formatDate(tomorrow),
      label: `明天 ${getWeekday(tomorrow)} ${tomorrow.getMonth() + 1}/${tomorrow.getDate()}`,
      disabled: false,
      date: tomorrow
    },
    {
      value: formatDate(dayAfterTomorrow),
      label: `后天 ${getWeekday(dayAfterTomorrow)} ${dayAfterTomorrow.getMonth() + 1}/${dayAfterTomorrow.getDate()}`,
      disabled: false,
      date: dayAfterTomorrow
    }
  ]
})

// 表单验证规则
const rules: FormRules = {
  timeSlot: [
    { required: true, message: '请选择预约时间', trigger: 'change' }
  ]
}

// 获取指定日期的可用时间段
const getAvailableTimeSlots = (dateStr: string) => {
  const selectedDate = new Date(dateStr)
  const now = new Date()
  const isToday = selectedDate.toDateString() === now.toDateString()

  return baseTimeSlots.map(slot => {
    let disabled = false
    let disabledReason = ''

    if (isToday) {
      const currentHour = now.getHours()
      const currentMinute = now.getMinutes()

      // 如果是今天，检查时间段是否已过
      if (currentHour > slot.startHour || (currentHour === slot.startHour && currentMinute > 0)) {
        disabled = true
        disabledReason = '时间已过'
      }
      // 如果距离时间段开始不足30分钟，也禁用
      else if (currentHour === slot.startHour - 1 && currentMinute > 30) {
        disabled = true
        disabledReason = '预约时间不足'
      }
    }

    return {
      ...slot,
      disabled,
      disabledReason
    }
  }).filter(slot => {
    // 如果是今天，只显示可用的时间段（过滤掉已过的时间）
    if (isToday) {
      return !slot.disabled
    }
    // 如果不是今天，显示所有时间段
    return true
  })
}

// 预约信息预览
const previewInfo = computed(() => {
  if (!pickupForm.timeSlot) {
    return null
  }

  const [dateStr, timeSlot] = pickupForm.timeSlot.split('|')
  console.log('previewInfo computed:', {
    pickupFormTimeSlot: pickupForm.timeSlot,
    dateStr,
    timeSlot,
    availableDates: availableDates.value.map(d => ({ value: d.value, label: d.label }))
  })

  const selectedDate = availableDates.value.find(d => d.value === dateStr)
  console.log('selectedDate found:', selectedDate)

  if (!selectedDate) return null

  const title = `预约取件时间：${selectedDate.label} ${timeSlot}`
  const description = '预约时间已设置'

  return { title, description }
})

// 获取当前日期标签页选中的时间段
const getSelectedTimeSlot = (dateStr: string) => {
  if (!pickupForm.timeSlot) return ''

  const [selectedDate, selectedSlot] = pickupForm.timeSlot.split('|')
  if (selectedDate === dateStr) {
    return selectedSlot
  }
  return ''
}

// 处理日期标签页变化
const handleDateTabChange = (dateValue: string | number) => {
  // 当切换日期标签页时，不清空已选择的时间段
  // 这样用户可以在不同日期间切换查看，而不会丢失已选择的时间
}

// 处理时间段变化
const handleTimeSlotChange = (dateStr: string, timeSlot: string | number | boolean | undefined) => {
  console.log('handleTimeSlotChange called:', { dateStr, timeSlot, activeDate: activeDate.value })

  if (timeSlot && typeof timeSlot === 'string') {
    // 更新选中的时间段，格式：日期|时间段
    pickupForm.timeSlot = `${dateStr}|${timeSlot}`
    console.log('Updated pickupForm.timeSlot:', pickupForm.timeSlot)
    // 不自动切换标签页，让用户手动控制
    // activeDate.value = dateStr  // 移除自动跳转逻辑
  } else {
    // 清空选择
    pickupForm.timeSlot = ''
  }
  emitValue()
}

// 发送值变化事件
const emitValue = () => {
  if (!pickupForm.timeSlot) {
    // 清空值
    const emptyValue = {
      pickup_start_time: '',
      pickup_end_time: ''
    }
    emit('update:modelValue', emptyValue)
    emit('change', emptyValue)
    return
  }

  const [dateStr, timeSlot] = pickupForm.timeSlot.split('|')
  const [startTime, endTime] = timeSlot.split('-')

  // 格式化为ISO 8601格式（保持用户选择的本地时间）
  const formatToISO = (dateStr: string, timeStr: string) => {
    // 直接构造ISO格式字符串，保持用户选择的时间不变
    return `${dateStr}T${timeStr}:00.000Z`
  }

  const value = {
    pickup_start_time: formatToISO(dateStr, startTime),
    pickup_end_time: formatToISO(dateStr, endTime)
  }

  emit('update:modelValue', value)
  emit('change', value)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue.pickup_start_time && newValue.pickup_end_time) {
    // 直接从ISO字符串中提取日期部分，避免时区转换问题
    const dateStr = newValue.pickup_start_time.split('T')[0]
    const startTime = newValue.pickup_start_time.split('T')[1].split(':')[0] + ':' + newValue.pickup_start_time.split('T')[1].split(':')[1]
    const endTime = newValue.pickup_end_time.split('T')[1].split(':')[0] + ':' + newValue.pickup_end_time.split('T')[1].split(':')[1]
    const timeSlot = `${startTime}-${endTime}`

    console.log('watch modelValue:', {
      pickup_start_time: newValue.pickup_start_time,
      pickup_end_time: newValue.pickup_end_time,
      extracted_dateStr: dateStr,
      timeSlot: timeSlot
    })

    pickupForm.timeSlot = `${dateStr}|${timeSlot}`
    activeDate.value = dateStr
  }
}, { immediate: true })



// 初始化默认选中今天
watch(availableDates, (dates) => {
  if (dates.length > 0 && !activeDate.value) {
    activeDate.value = dates[0].value
  }
}, { immediate: true })

// 暴露验证方法
const validate = async () => {
  if (!formRef.value) return false
  try {
    await formRef.value.validate()
    return true
  } catch {
    return false
  }
}

// 重置表单
const reset = () => {
  pickupForm.timeSlot = ''
  activeDate.value = availableDates.value[0]?.value || ''
  formRef.value?.resetFields()
}

defineExpose({
  validate,
  reset
})
</script>

<style scoped lang="scss">
.pickup-time-selector {
  .pickup-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;

      .header-icon {
        font-size: 18px;
      }

      .header-title {
        font-weight: 600;
        color: #303133;
      }

      .help-icon {
        margin-left: auto;
        color: #909399;
        cursor: help;

        &:hover {
          color: #409eff;
        }
      }
    }
  }

  .date-time-selector {
    .date-tabs {
      :deep(.el-tabs__header) {
        margin-bottom: 20px;
      }

      :deep(.el-tabs__nav-wrap) {
        padding: 0 10px;
      }

      :deep(.el-tabs__item) {
        font-weight: 500;
        padding: 0 20px;

        &.is-active {
          color: #409eff;
          font-weight: 600;
        }
      }
    }

    .time-slots-container {
      min-height: 120px;
    }

    .no-slots-tip {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 120px;
      color: #909399;
    }
  }

  .time-slot-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
    width: 100%;

    .time-slot-button {
      margin: 0;

      :deep(.el-radio-button__inner) {
        width: 100%;
        height: auto;
        padding: 12px 8px;
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        background: #fff;
        transition: all 0.3s;

        &:hover {
          border-color: #409eff;
          background: #ecf5ff;
        }
      }

      :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
        background: #409eff;
        border-color: #409eff;
        color: #fff;
        box-shadow: none;
      }

      :deep(.el-radio-button__original-radio:disabled + .el-radio-button__inner) {
        background: #f5f7fa;
        border-color: #e4e7ed;
        color: #c0c4cc;
        cursor: not-allowed;
      }

      .slot-content {
        text-align: center;

        .slot-time {
          font-weight: 600;
          font-size: 14px;
          margin-bottom: 4px;
        }

        .slot-desc {
          font-size: 12px;
          opacity: 0.8;
        }

        .slot-disabled-reason {
          font-size: 11px;
          color: #f56c6c;
          margin-top: 2px;
        }
      }
    }
  }

  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }

  .pickup-preview {
    margin-top: 16px;

    :deep(.el-alert) {
      border-radius: 6px;
    }
  }
}
</style>