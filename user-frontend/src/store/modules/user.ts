import { defineStore } from 'pinia'
import { LanguageEnum } from '@/enums/appEnum'
import { router, setPageTitle } from '@/router'
import { UserInfo } from '@/types/store'
import { useSettingStore } from './setting'
import { useWorktabStore } from './worktab'
import { getSysStorage } from '@/utils/storage'
import { MenuListType } from '@/types/menu'
import { AuthService } from '@/api/authApi'
import { ApiStatus } from '@/utils/http/status'
import { SecureStorage } from '@/utils/security'
import { logger } from '@/utils/logger'

interface UserState {
  language: LanguageEnum // 语言
  isLogin: boolean // 是否登录
  isLock: boolean // 是否锁屏
  lockPassword: string // 锁屏密码
  info: Partial<UserInfo> // 用户信息
  searchHistory: MenuListType[] // 搜索历史
}

export const useUserStore = defineStore({
  id: 'userStore',
  state: (): UserState => ({
    language: LanguageEnum.ZH,
    isLogin: false,
    isLock: false,
    lockPassword: '',
    info: {},
    searchHistory: []
  }),
  getters: {
    getUserInfo(): Partial<UserInfo> {
      return this.info
    },
    getSettingState() {
      return useSettingStore().$state
    },
    getWorktabState() {
      return useWorktabStore().$state
    }
  },
  actions: {
    // 登录方法
    async login(formData: { username: string; password: string }) {
      try {
        logger.userAction('用户登录请求', { username: formData.username })

        // 使用安全的用户名密码登录，通过代理访问后端
        const response = await fetch('/api/v1/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            username: formData.username,
            password: formData.password
          })
        })

        logger.debug('登录响应状态', response.status)
        logger.debug('登录响应头', response.headers)

        // 处理非200状态码
        if (!response.ok) {
          let errorMessage = '登录失败'
          
          try {
            const responseText = await response.text()
            logger.debug('错误响应原始文本', responseText)
            
            // 尝试解析JSON错误信息
            if (responseText.trim().startsWith('{')) {
              const errorData = JSON.parse(responseText)
              errorMessage = errorData.message || errorMessage
            } else {
              // 如果不是JSON，可能是HTML错误页面
              if (response.status === 403) {
                errorMessage = '访问被拒绝，请检查服务器配置或联系管理员'
              } else if (response.status === 401) {
                errorMessage = '用户名或密码错误'
              } else {
                errorMessage = `服务器错误 (${response.status})`
              }
            }
          } catch (parseError) {
            logger.error('解析错误响应失败', parseError)
            errorMessage = `服务器响应异常 (${response.status})`
          }
          
          return {
            status: ApiStatus.error,
            message: errorMessage
          }
        }

        const responseText = await response.text()
        logger.debug('登录响应原始文本', responseText)

        let loginResult
        try {
          loginResult = JSON.parse(responseText)
        } catch (parseError) {
          logger.error('JSON解析错误', parseError)
          return {
            status: ApiStatus.error,
            message: '服务器响应格式错误，请稍后重试'
          }
        }

        if (loginResult.success && loginResult.access_token) {
          // 构造用户信息
          const userInfo: UserInfo = {
            id: loginResult.user_info.id,
            name: loginResult.user_info.username,
            username: loginResult.user_info.username,
            nickname: loginResult.user_info.username,
            avatar: '',
            email: loginResult.user_info.email,
            token: loginResult.access_token
          }

          this.setUserInfo(userInfo)
          this.setLoginStatus(true)
          this.saveUserData()

          // 使用安全存储方式存储token，使用后端返回的实际过期时间
          const expiresIn = loginResult.expires_in || 3600 // 如果后端没有返回过期时间，默认1小时
          SecureStorage.setToken(loginResult.access_token, expiresIn)

          return { status: ApiStatus.success, token: loginResult.access_token }
        } else {
          return {
            status: ApiStatus.error,
            message: loginResult.message || '登录失败'
          }
        }
      } catch (error: any) {
        logger.error('登录失败', error)
        
        // 区分网络错误和其他错误
        let errorMessage = '登录失败，请检查网络连接'
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          errorMessage = '无法连接到服务器，请检查网络连接或服务器状态'
        } else if (error.response?.data?.message) {
          errorMessage = error.response.data.message
        } else if (error.message) {
          errorMessage = error.message
        }
        
        return {
          status: ApiStatus.error,
          message: errorMessage
        }
      }
    },
    initState() {
      let sys = getSysStorage()

      if (sys) {
        sys = JSON.parse(sys)
        const { info, isLogin, language, searchHistory, isLock, lockPassword } = sys.user

        this.info = info || {}
        this.isLogin = isLogin || false
        this.isLock = isLock || false
        this.language = language || LanguageEnum.ZH
        this.searchHistory = searchHistory || []
        this.lockPassword = lockPassword || ''
      }
    },
    // 用户数据持久化存储
    saveUserData() {
      saveStoreStorage({
        user: {
          info: this.info,
          isLogin: this.isLogin,
          language: this.language,
          isLock: this.isLock,
          lockPassword: this.lockPassword,
          searchHistory: this.searchHistory,
          worktab: this.getWorktabState,
          setting: this.getSettingState
        }
      })
    },
    setUserInfo(info: UserInfo) {
      this.info = info
    },
    setLoginStatus(isLogin: boolean) {
      this.isLogin = isLogin
    },
    setLanguage(lang: LanguageEnum) {
      setPageTitle(router.currentRoute.value)
      this.language = lang
    },
    setSearchHistory(list: Array<MenuListType>) {
      this.searchHistory = list
    },
    setLockStatus(isLock: boolean) {
      this.isLock = isLock
    },
    setLockPassword(password: string) {
      this.lockPassword = password
    },
    logOut() {
      setTimeout(() => {
        // 🔥 修复：清除所有认证相关的存储
        SecureStorage.clearToken()
        localStorage.removeItem('admin_token') // 清除可能存在的管理员token

        // document.getElementsByTagName('html')[0].removeAttribute('class') // 移除暗黑主题
        this.info = {}
        this.isLogin = false
        this.isLock = false
        this.lockPassword = ''
        useWorktabStore().opened = []
        this.saveUserData()
        sessionStorage.removeItem('iframeRoutes')

        // 🔥 修复：确保跳转到登录页面
        if (router.currentRoute.value.path !== '/login') {
          router.push('/login')
        }
      }, 300)
    }
  }
})

function initVersion(version: string) {
  const vs = localStorage.getItem('version')
  if (!vs) {
    localStorage.setItem('version', version)
  }
}

// 数据持久化存储
function saveStoreStorage<T>(newData: T) {
  const version = import.meta.env.VITE_VERSION
  initVersion(version)
  const vs = localStorage.getItem('version') || version
  const storedData = JSON.parse(localStorage.getItem(`sys-v${vs}`) || '{}')

  // 合并新数据与现有数据
  const mergedData = { ...storedData, ...newData }
  localStorage.setItem(`sys-v${vs}`, JSON.stringify(mergedData))
}
