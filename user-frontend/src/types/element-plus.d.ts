// 扩展 Element-Plus 组件类型，补充业务中使用的自定义 type="link"
import 'element-plus'

declare module 'element-plus/es/components/button' {
  export interface ButtonProps {
    type?: '' | 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text'
  }
}

declare module 'element-plus/es/components/tag' {
  export interface TagProps {
    type?: '' | 'success' | 'info' | 'warning' | 'danger' | 'link'
  }
}

// 兼容按需引入的路径
declare module 'element-plus/es/components/button/src/button' {
  export interface ButtonProps {
    type?: '' | 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger' | 'text'
  }
}

declare module 'element-plus/es/components/tag/src/tag' {
  export interface TagProps {
    type?: '' | 'success' | 'info' | 'warning' | 'danger' | 'link'
  }
}

declare module 'element-plus/es/components/input' {
  export interface InputProps {
    status?: '' | 'success' | 'warning' | 'danger' | 'info' | 'link' | string
  }
}

declare module 'element-plus' {
  export interface ButtonProps {
    type?: any
  }
  export interface TagProps {
    type?: any
  }
}

// 全局放宽模板属性类型检查，解决 EpPropMergeType 对自定义值的严格验证
declare module 'element-plus/es/utils' {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  export type EpPropMergeType<P, T, C = any> = any
} 