/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddressCascader: typeof import('./../components/AddressCascader.vue')['default']
    AddressParse: typeof import('./../components/AddressParse.vue')['default']
    AddressParseInput: typeof import('./../components/AddressParseInput.vue')['default']
    AddressResult: typeof import('./../components/address/AddressResult.vue')['default']
    AddressValidationResult: typeof import('./../components/address/AddressValidationResult.vue')['default']
    ArtTable: typeof import('./../components/Table/ArtTable.vue')['default']
    BackToTop: typeof import('./../components/Widgets/BackToTop.vue')['default']
    BarChart: typeof import('./../components/Widgets/Charts/BarChart.vue')['default']
    BarChartCard: typeof import('./../components/Widgets/Cards/BarChartCard.vue')['default']
    BasicBanner: typeof import('./../components/Widgets/Banners/BasicBanner.vue')['default']
    BatchAddressResult: typeof import('./../components/address/BatchAddressResult.vue')['default']
    Breadcrumb: typeof import('./../components/Layout/Breadcrumb/index.vue')['default']
    ButtonMore: typeof import('./../components/Form/ButtonMore.vue')['default']
    ButtonTable: typeof import('./../components/Form/ButtonTable.vue')['default']
    CainiaoPickupTimeSelector: typeof import('./../components/CainiaoPickupTimeSelector.vue')['default']
    CardBanner: typeof import('./../components/Widgets/Banners/CardBanner.vue')['default']
    Chat: typeof import('./../components/Layout/Chat/index.vue')['default']
    CommentItem: typeof import('./../components/Pages/CommentWidget/CommentItem.vue')['default']
    CommentWidget: typeof import('./../components/Pages/CommentWidget/index.vue')['default']
    CutterImg: typeof import('./../components/Widgets/CutterImg.vue')['default']
    DataListCard: typeof import('./../components/Widgets/Cards/DataListCard.vue')['default']
    DonutChartCard: typeof import('./../components/Widgets/Cards/DonutChartCard.vue')['default']
    Editor: typeof import('./../components/Form/Editor.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCalendar: typeof import('element-plus/es')['ElCalendar']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    ExcelExport: typeof import('./../components/Form/ExcelExport.vue')['default']
    ExcelImport: typeof import('./../components/Form/ExcelImport.vue')['default']
    Exception: typeof import('./../components/Views/Exception.vue')['default']
    FastEnter: typeof import('./../components/Layout/FastEnter/index.vue')['default']
    FestivalTextScroll: typeof import('./../components/Widgets/FestivalTextScroll.vue')['default']
    Fireworks: typeof import('./../components/Ceremony/Fireworks.vue')['default']
    FormInput: typeof import('./../components/Form/FormInput.vue')['default']
    FormSelect: typeof import('./../components/Form/FormSelect.vue')['default']
    HBarChart: typeof import('./../components/Widgets/Charts/HBarChart.vue')['default']
    IconSelector: typeof import('./../components/Icons/IconSelector.vue')['default']
    ImageCard: typeof import('./../components/Widgets/Cards/ImageCard.vue')['default']
    KLineChart: typeof import('./../components/Widgets/Charts/KLineChart.vue')['default']
    LeftView: typeof import('./../components/Pages/Login/LeftView.vue')['default']
    LineChart: typeof import('./../components/Widgets/Charts/LineChart.vue')['default']
    LineChartCard: typeof import('./../components/Widgets/Cards/LineChartCard.vue')['default']
    LockScreen: typeof import('./../components/Layout/LockScreen/index.vue')['default']
    MenuLeft: typeof import('./../components/Layout/MenuLeft/index.vue')['default']
    MenuRight: typeof import('./../components/Widgets/MenuRight.vue')['default']
    MenuTop: typeof import('./../components/Layout/MenuTop/index.vue')['default']
    MenuTopSubmenu: typeof import('./../components/Layout/MenuTop/MenuTopSubmenu.vue')['default']
    MixedMenu: typeof import('./../components/Layout/MixedMenu/index.vue')['default']
    Network: typeof import('./../components/Widgets/Network.vue')['default']
    Notice: typeof import('./../components/Layout/Notice/index.vue')['default']
    PickupTimeSelector: typeof import('./../components/PickupTimeSelector.vue')['default']
    ProgressCard: typeof import('./../components/Widgets/Cards/ProgressCard.vue')['default']
    RadarChart: typeof import('./../components/Widgets/Charts/RadarChart.vue')['default']
    RingChart: typeof import('./../components/Widgets/Charts/RingChart.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScatterChart: typeof import('./../components/Widgets/Charts/ScatterChart.vue')['default']
    Search: typeof import('./../components/Layout/Search/index.vue')['default']
    Setting: typeof import('./../components/Layout/Setting/index.vue')['default']
    StatsCard: typeof import('./../components/Widgets/Cards/StatsCard.vue')['default']
    Submenu: typeof import('./../components/Layout/Submenu/submenu.vue')['default']
    TableBar: typeof import('./../components/Table/TableBar.vue')['default']
    TextScroll: typeof import('./../components/Widgets/TextScroll.vue')['default']
    TimelineListCard: typeof import('./../components/Widgets/Cards/TimelineListCard.vue')['default']
    TopBar: typeof import('./../components/Layout/TopBar/index.vue')['default']
    UniversalPickupTimeSelector: typeof import('./../components/UniversalPickupTimeSelector.vue')['default']
    VideoPlayer: typeof import('./../components/Widgets/VideoPlayer.vue')['default']
    Watermark: typeof import('./../components/Widgets/Watermark.vue')['default']
    WorkTab: typeof import('./../components/Layout/WorkTab/index.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
