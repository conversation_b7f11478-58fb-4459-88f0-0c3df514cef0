/**
 * 环境配置检查工具
 */

interface EnvConfig {
  VITE_API_URL: string
  VITE_APP_TITLE: string
  MODE: string
  DEV: boolean
  PROD: boolean
}

/**
 * 检查必需的环境变量
 */
export function checkRequiredEnvVars(): void {
  const requiredVars = ['VITE_API_URL']
  const missing: string[] = []

  requiredVars.forEach((varName) => {
    if (!import.meta.env[varName]) {
      missing.push(varName)
    }
  })

  if (missing.length > 0) {
    const message = `缺少必需的环境变量: ${missing.join(', ')}`
    console.error(message)
    throw new Error(message)
  }
}

/**
 * 获取环境配置
 */
export function getEnvConfig(): EnvConfig {
  return {
    VITE_API_URL: import.meta.env.VITE_API_URL,
    VITE_APP_TITLE: import.meta.env.VITE_APP_TITLE || '快递管理系统',
    MODE: import.meta.env.MODE,
    DEV: import.meta.env.DEV,
    PROD: import.meta.env.PROD
  }
}

/**
 * 验证API URL格式
 */
export function validateApiUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 初始化环境检查
 */
export function initEnvCheck(): void {
  try {
    checkRequiredEnvVars()

    const config = getEnvConfig()

    if (!validateApiUrl(config.VITE_API_URL)) {
      throw new Error(`无效的API URL: ${config.VITE_API_URL}`)
    }

    console.log('环境配置检查通过:', config)
  } catch (error) {
    console.error('环境配置检查失败:', error)
    throw error
  }
}
