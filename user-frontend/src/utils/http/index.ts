import axios, { InternalAxiosRequestConfig, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { SecureStorage } from '@/utils/security'
import EmojiText from '../emojo'

const axiosInstance = axios.create({
  timeout: 15000, // 请求超时时间(毫秒)
  baseURL: '', // 使用相对路径，通过Vite代理转发到后端
  withCredentials: false, // 使用JWT token认证，不需要cookie
  transformRequest: [(data) => JSON.stringify(data)], // 请求数据转换为 JSON 字符串
  validateStatus: (status) => status >= 200 && status < 300, // 只接受 2xx 的状态码
  headers: {
    get: { 'Content-Type': 'application/json;charset=utf-8' },
    post: { 'Content-Type': 'application/json;charset=utf-8' }
  },
  transformResponse: [
    (data) => {
      // 响应数据转换
      try {
        return typeof data === 'string' && (data.startsWith('{') || data.startsWith('['))
          ? JSON.parse(data)
          : data
      } catch {
        return data // 解析失败时返回原数据
      }
    }
  ]
})

// 请求拦截器
axiosInstance.interceptors.request.use(
  (request: InternalAxiosRequestConfig) => {
    // 设置默认Content-Type
    if (!request.headers.get('Content-Type')) {
      request.headers.set('Content-Type', 'application/json')
    }

    // 对于不需要认证的接口，跳过token设置
    const noAuthUrls = ['/api/v1/users/register', '/api/v1/auth/login']
    const needsAuth = !noAuthUrls.some((url) => request.url?.includes(url))

    if (needsAuth) {
      // 使用安全存储获取token，其次从store获取
      const storedToken = SecureStorage.getToken()
      const { token } = useUserStore().info
      const accessToken = storedToken || token

      // 如果 token 存在，则设置请求头
      if (accessToken) {
        request.headers.set(
          'Authorization',
          accessToken.startsWith('Bearer ') ? accessToken : `Bearer ${accessToken}`
        )
      }
    }

    // 🔥 修复：为余额和交易相关的API请求添加禁用缓存头部
    if (request.url?.includes('/balance') ||
        request.url?.includes('/transactions') ||
        request.url?.includes('/orders') ||
        request.url?.includes('/callbacks')) {
      request.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
      request.headers.set('Pragma', 'no-cache')
      request.headers.set('Expires', '0')
      // 🔥 添加时间戳参数强制刷新
      if (request.url && !request.url.includes('_t=')) {
        const separator = request.url.includes('?') ? '&' : '?'
        request.url += `${separator}_t=${Date.now()}`
      }
    }

    return request // 返回修改后的配置
  },
  (error) => {
    ElMessage.error(`服务器异常！ ${EmojiText[500]}`) // 显示错误消息
    return Promise.reject(error) // 返回拒绝的 Promise
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error) => {
    if (axios.isCancel(error)) {
      console.log('repeated request: ' + error.message)
    } else {
      const { response } = error
      const status = response?.status

      // 🔥 修复：处理认证失效的情况
      if (status === 401 || status === 403) {
        const userStore = useUserStore()

        // 清除本地存储的认证信息
        SecureStorage.clearToken()
        userStore.logOut()

        // 显示认证失效消息
        ElMessage.error(
          status === 401
            ? `登录已失效，请重新登录 ${EmojiText[401] || '🔐'}`
            : `访问被拒绝 ${EmojiText[403] || '🚫'}`
        )

        // 避免在登录页面重复跳转
        if (window.location.hash !== '#/login') {
          // 延迟跳转，确保消息显示
          setTimeout(() => {
            window.location.hash = '#/login'
          }, 1000)
        }

        return Promise.reject(error)
      }

      // 🔥 修复：处理其他HTTP错误
      const errorMessage = response?.data?.message
      const statusText = response?.statusText

      let displayMessage = ''
      switch (status) {
        case 400:
          displayMessage = errorMessage || '请求参数错误'
          break
        case 404:
          displayMessage = '请求的资源不存在'
          break
        case 500:
          displayMessage = '服务器内部错误'
          break
        case 502:
          displayMessage = '网关错误'
          break
        case 503:
          displayMessage = '服务暂时不可用'
          break
        default:
          displayMessage = errorMessage || statusText || '请求失败'
      }

      ElMessage.error(`${displayMessage} ${EmojiText[status] || EmojiText[500]}`)
    }
    return Promise.reject(error)
  }
)

// 请求
async function request<T = any>(config: AxiosRequestConfig): Promise<T> {
  // 将 POST | PUT 请求的参数放入 data 中，并清空 params
  if (config.method === 'POST' || config.method === 'PUT') {
    config.data = config.params
    config.params = {}
  }
  try {
    const res = await axiosInstance.request<T>({ ...config })
    return res.data
  } catch (e) {
    if (axios.isAxiosError(e)) {
      // 可以在这里处理 Axios 错误
    }
    return Promise.reject(e)
  }
}

// API 方法集合
const api = {
  get<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'GET' }) // GET 请求
  },
  post<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'POST' }) // POST 请求
  },
  put<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'PUT' }) // PUT 请求
  },
  del<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'DELETE' }) // DELETE 请求
  }
}

export default api
