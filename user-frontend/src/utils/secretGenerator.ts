/**
 * 密钥生成工具
 */

/**
 * 生成安全的随机密钥
 * @param length 密钥长度（字节数），默认32字节
 * @returns 十六进制格式的密钥字符串
 */
export function generateSecureSecret(length: number = 32): string {
  // 检查浏览器是否支持 crypto.getRandomValues
  if (!window.crypto || !window.crypto.getRandomValues) {
    throw new Error('当前浏览器不支持安全随机数生成')
  }

  // 生成随机字节数组
  const array = new Uint8Array(length)
  window.crypto.getRandomValues(array)

  // 转换为十六进制字符串
  return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('')
}

/**
 * 生成Base64格式的密钥
 * @param length 密钥长度（字节数），默认32字节
 * @returns Base64格式的密钥字符串
 */
export function generateBase64Secret(length: number = 32): string {
  if (!window.crypto || !window.crypto.getRandomValues) {
    throw new Error('当前浏览器不支持安全随机数生成')
  }

  const array = new Uint8Array(length)
  window.crypto.getRandomValues(array)

  // 转换为Base64字符串
  return btoa(String.fromCharCode(...array))
}

/**
 * 生成可读的密钥（包含字母和数字）
 * @param length 密钥长度，默认32字符
 * @returns 可读的密钥字符串
 */
export function generateReadableSecret(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const array = new Uint8Array(length)
  window.crypto.getRandomValues(array)

  return Array.from(array, (byte) => chars[byte % chars.length]).join('')
}

/**
 * 验证密钥强度
 * @param secret 要验证的密钥
 * @returns 密钥强度信息
 */
export interface SecretStrength {
  score: number // 0-100分
  level: 'weak' | 'medium' | 'strong' | 'very_strong'
  suggestions: string[]
}

export function validateSecretStrength(secret: string): SecretStrength {
  const suggestions: string[] = []
  let score = 0

  // 长度检查
  if (secret.length >= 32) {
    score += 30
  } else if (secret.length >= 16) {
    score += 20
    suggestions.push('建议使用至少32位字符的密钥')
  } else {
    score += 10
    suggestions.push('密钥长度过短，建议至少16位字符')
  }

  // 字符复杂度检查
  const hasLower = /[a-z]/.test(secret)
  const hasUpper = /[A-Z]/.test(secret)
  const hasNumber = /[0-9]/.test(secret)
  const hasSpecial = /[^a-zA-Z0-9]/.test(secret)

  let complexity = 0
  if (hasLower) complexity++
  if (hasUpper) complexity++
  if (hasNumber) complexity++
  if (hasSpecial) complexity++

  score += complexity * 15

  if (complexity < 3) {
    suggestions.push('建议包含大小写字母、数字和特殊字符')
  }

  // 重复字符检查
  const repeatedChars = secret.match(/(.)\1{2,}/g)
  if (repeatedChars) {
    score -= 10
    suggestions.push('避免使用重复字符')
  }

  // 常见模式检查
  const commonPatterns = [/123456/, /abcdef/, /qwerty/, /password/i, /secret/i]

  for (const pattern of commonPatterns) {
    if (pattern.test(secret)) {
      score -= 15
      suggestions.push('避免使用常见的字符模式')
      break
    }
  }

  // 确保分数在0-100范围内
  score = Math.max(0, Math.min(100, score))

  // 确定强度等级
  let level: SecretStrength['level']
  if (score >= 80) {
    level = 'very_strong'
  } else if (score >= 60) {
    level = 'strong'
  } else if (score >= 40) {
    level = 'medium'
  } else {
    level = 'weak'
  }

  return { score, level, suggestions }
}

/**
 * 生成密钥的命令行示例
 */
export const SECRET_GENERATION_EXAMPLES = {
  openssl: 'openssl rand -hex 32',
  node: "node -e \"console.log(require('crypto').randomBytes(32).toString('hex'))\"",
  python: 'python -c "import secrets; print(secrets.token_hex(32))"',
  go: 'go run -c "package main; import (\"crypto/rand\"; \"encoding/hex\"; \"fmt\"); func main() { b := make([]byte, 32); rand.Read(b); fmt.Println(hex.EncodeToString(b)) }"'
}

/**
 * 密钥使用最佳实践
 */
export const SECRET_BEST_PRACTICES = [
  '使用至少32位字符的随机密钥',
  '定期更换密钥（建议每季度）',
  '不要在代码中硬编码密钥',
  '使用环境变量或配置文件存储密钥',
  '不要在日志中记录密钥',
  '每个环境使用不同的密钥',
  '使用密码管理器安全存储密钥'
]
