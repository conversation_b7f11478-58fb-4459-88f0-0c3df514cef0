import { useUserStore } from '@/store/modules/user'

/**
 * 权限类型枚举
 */
export enum PermissionType {
  // 快递相关权限
  EXPRESS_READ = 'express:read',
  EXPRESS_WRITE = 'express:write',

  // 余额相关权限
  BALANCE_READ = 'balance:read',
  BALANCE_WRITE = 'balance:write',

  // 计费相关权限
  BILLING_READ = 'billing:read',
  BILLING_WRITE = 'billing:write',

  // 管理员权限
  ADMIN_READ = 'admin:read',
  ADMIN_WRITE = 'admin:write',

  // 用户管理权限
  USER_READ = 'user:read',
  USER_WRITE = 'user:write',

  // 系统配置权限
  SYSTEM_READ = 'system:read',
  SYSTEM_WRITE = 'system:write'
}

/**
 * 权限管理器
 */
export class PermissionManager {
  private static userStore = useUserStore()

  /**
   * 检查用户是否有指定权限
   * @param permission 权限标识
   * @returns 是否有权限
   */
  static hasPermission(permission: PermissionType | string): boolean {
    try {
      const { isLogin, info } = this.userStore

      // 未登录用户没有任何权限
      if (!isLogin || !info?.token) {
        return false
      }

      // 解析JWT token获取权限信息
      const permissions = this.getPermissionsFromToken(info.token)

      // 检查是否包含指定权限
      return permissions.includes(permission)
    } catch (error) {
      console.error('权限检查失败:', error)
      return false
    }
  }

  /**
   * 检查用户是否有多个权限中的任意一个
   * @param permissions 权限列表
   * @returns 是否有权限
   */
  static hasAnyPermission(permissions: (PermissionType | string)[]): boolean {
    return permissions.some((permission) => this.hasPermission(permission))
  }

  /**
   * 检查用户是否有所有指定权限
   * @param permissions 权限列表
   * @returns 是否有权限
   */
  static hasAllPermissions(permissions: (PermissionType | string)[]): boolean {
    return permissions.every((permission) => this.hasPermission(permission))
  }

  /**
   * 检查用户是否是管理员
   * @returns 是否是管理员
   */
  static isAdmin(): boolean {
    return (
      this.hasPermission(PermissionType.ADMIN_READ) ||
      this.hasPermission(PermissionType.ADMIN_WRITE)
    )
  }

  /**
   * 获取用户所有权限
   * @returns 权限列表
   */
  static getUserPermissions(): string[] {
    try {
      const { isLogin, info } = this.userStore

      if (!isLogin || !info?.token) {
        return []
      }

      return this.getPermissionsFromToken(info.token)
    } catch (error) {
      console.error('获取用户权限失败:', error)
      return []
    }
  }

  /**
   * 从JWT token中解析权限信息
   * @param token JWT token
   * @returns 权限列表
   */
  private static getPermissionsFromToken(token: string): string[] {
    try {
      // 移除Bearer前缀
      const cleanToken = token.replace('Bearer ', '')

      // 解析JWT payload
      const payload = JSON.parse(atob(cleanToken.split('.')[1]))

      // 从scope字段获取权限
      const scope = payload.scope || ''
      return scope.split(' ').filter((s: string) => s.length > 0)
    } catch (error) {
      console.error('解析token权限失败:', error)
      return []
    }
  }

  /**
   * 权限装饰器 - 用于组件方法
   * @param permission 所需权限
   * @param fallback 无权限时的回调
   */
  static requirePermission(permission: PermissionType | string, fallback?: () => void) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
      const originalMethod = descriptor.value

      descriptor.value = function (...args: any[]) {
        if (PermissionManager.hasPermission(permission)) {
          return originalMethod.apply(this, args)
        } else {
          console.warn(`权限不足: 需要 ${permission} 权限`)
          if (fallback) {
            fallback()
          }
          return false
        }
      }

      return descriptor
    }
  }

  /**
   * 检查路由权限
   * @param routePath 路由路径
   * @returns 是否有访问权限
   */
  static canAccessRoute(routePath: string): boolean {
    // 路由权限映射
    const routePermissionMap: Record<string, PermissionType[]> = {
      '/express/orders': [PermissionType.EXPRESS_READ],
      '/express/tracking': [PermissionType.EXPRESS_READ],
      '/balance/management': [PermissionType.BALANCE_READ],
      '/billing/statistics': [PermissionType.BILLING_READ],
      '/billing/history': [PermissionType.BILLING_READ],
      '/admin': [PermissionType.ADMIN_READ],
      '/user/settings': [PermissionType.USER_READ]
    }

    const requiredPermissions = routePermissionMap[routePath]

    // 如果路由没有权限要求，允许访问
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true
    }

    // 检查是否有任意一个所需权限
    return this.hasAnyPermission(requiredPermissions)
  }

  /**
   * 获取用户可访问的菜单项
   * @param menuItems 所有菜单项
   * @returns 有权限的菜单项
   */
  static getAccessibleMenuItems(menuItems: any[]): any[] {
    return menuItems.filter((item) => {
      // 如果有子菜单，递归过滤
      if (item.children && item.children.length > 0) {
        item.children = this.getAccessibleMenuItems(item.children)
        return item.children.length > 0
      }

      // 检查路由权限
      return item.path ? this.canAccessRoute(item.path) : true
    })
  }

  /**
   * 权限检查中间件
   * @param to 目标路由
   * @param from 来源路由
   * @param next 下一步函数
   */
  static routeGuard(to: any, from: any, next: any) {
    // 检查是否需要登录
    if (to.meta?.requiresAuth && !this.userStore.isLogin) {
      next('/login')
      return
    }

    // 检查路由权限
    if (to.path && !this.canAccessRoute(to.path)) {
      console.warn(`访问被拒绝: 用户无权限访问 ${to.path}`)
      next('/403') // 跳转到无权限页面
      return
    }

    next()
  }

  /**
   * 清除权限缓存
   */
  static clearPermissionCache(): void {
    // 如果有权限缓存，在这里清除
    console.log('权限缓存已清除')
  }
}

/**
 * Vue 3 组合式API权限钩子
 */
export function usePermission() {
  return {
    hasPermission: PermissionManager.hasPermission,
    hasAnyPermission: PermissionManager.hasAnyPermission,
    hasAllPermissions: PermissionManager.hasAllPermissions,
    isAdmin: PermissionManager.isAdmin,
    getUserPermissions: PermissionManager.getUserPermissions,
    canAccessRoute: PermissionManager.canAccessRoute
  }
}

/**
 * 权限指令 - 用于模板中的权限控制
 */
export const vPermission = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding

    if (value && !PermissionManager.hasPermission(value)) {
      // 移除元素或隐藏元素
      if (binding.modifiers.hide) {
        el.style.display = 'none'
      } else {
        el.parentNode?.removeChild(el)
      }
    }
  },

  updated(el: HTMLElement, binding: any) {
    const { value } = binding

    if (value && !PermissionManager.hasPermission(value)) {
      if (binding.modifiers.hide) {
        el.style.display = 'none'
      } else {
        el.parentNode?.removeChild(el)
      }
    } else {
      if (binding.modifiers.hide) {
        el.style.display = ''
      }
    }
  }
}

/**
 * 权限常量定义
 */
export const PERMISSIONS = {
  // 快递管理权限
  EXPRESS: {
    READ: PermissionType.EXPRESS_READ,
    WRITE: PermissionType.EXPRESS_WRITE,
    CREATE_ORDER: PermissionType.EXPRESS_WRITE,
    CANCEL_ORDER: PermissionType.EXPRESS_WRITE,
    QUERY_PRICE: PermissionType.EXPRESS_READ,
    TRACK_ORDER: PermissionType.EXPRESS_READ
  },

  // 余额管理权限
  BALANCE: {
    READ: PermissionType.BALANCE_READ,
    WRITE: PermissionType.BALANCE_WRITE,
    DEPOSIT: PermissionType.BALANCE_WRITE,
    WITHDRAW: PermissionType.BALANCE_WRITE
  },

  // 计费管理权限
  BILLING: {
    READ: PermissionType.BILLING_READ,
    WRITE: PermissionType.BILLING_WRITE,
    VIEW_STATISTICS: PermissionType.BILLING_READ,
    EXPORT_DATA: PermissionType.BILLING_READ
  },

  // 管理员权限
  ADMIN: {
    READ: PermissionType.ADMIN_READ,
    WRITE: PermissionType.ADMIN_WRITE,
    MANAGE_USERS: PermissionType.ADMIN_WRITE,
    SYSTEM_CONFIG: PermissionType.ADMIN_WRITE
  }
} as const
