/**
 * 工单类型一致性验证工具
 * 确保前后端数据映射的一致性
 */

import { WorkOrderService } from '@/api'
import type { WorkOrderTypeMapping } from '@/api/model/workOrderModel'
import { WorkOrderType, WorkOrderTypeNames } from '@/api/model/workOrderModel'
import { SecureStorage } from '@/utils/security'
import { logger } from '@/utils/logger'

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  summary: {
    totalTypes: number
    supportedTypes: number
    missingTypes: number[]
    extraTypes: number[]
  }
}

/**
 * 验证前后端工单类型映射的一致性
 */
export class WorkOrderTypeValidator {
  /**
   * 验证工单类型一致性
   * @param provider 供应商（可选）
   * @returns 验证结果
   */
  static async validateTypeConsistency(provider?: string): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      summary: {
        totalTypes: 0,
        supportedTypes: 0,
        missingTypes: [],
        extraTypes: []
      }
    }

    try {
      // 1. 获取后端支持的工单类型
      const response = await WorkOrderService.getSupportedTypes(provider)

      if (!response.success || !response.data) {
        result.isValid = false
        result.errors.push('无法获取后端工单类型数据')
        return result
      }

      const backendTypes = response.data
      result.summary.totalTypes = backendTypes.length
      result.summary.supportedTypes = backendTypes.filter((t) => t.is_supported).length

      // 2. 获取前端定义的工单类型
      const frontendTypes = Object.keys(WorkOrderTypeNames).map(Number)

      // 3. 检查后端类型是否在前端有对应定义
      for (const backendType of backendTypes) {
        if (!frontendTypes.includes(backendType.unified_type)) {
          result.summary.missingTypes.push(backendType.unified_type)
          result.warnings.push(
            `后端类型 ${backendType.unified_type}(${backendType.unified_name}) 在前端没有对应定义`
          )
        }
      }

      // 4. 检查前端类型是否在后端有对应定义
      const backendTypeIds = backendTypes.map((t) => t.unified_type)
      for (const frontendTypeId of frontendTypes) {
        if (!backendTypeIds.includes(frontendTypeId)) {
          result.summary.extraTypes.push(frontendTypeId)
          result.warnings.push(
            `前端类型 ${frontendTypeId}(${WorkOrderTypeNames[frontendTypeId] || '未知'}) 在后端没有对应定义`
          )
        }
      }

      // 5. 检查名称一致性
      for (const backendType of backendTypes) {
        const frontendName = WorkOrderTypeNames[backendType.unified_type]
        if (frontendName && frontendName !== backendType.unified_name) {
          result.warnings.push(
            `类型 ${backendType.unified_type} 名称不一致: 前端="${frontendName}", 后端="${backendType.unified_name}"`
          )
        }
      }

      // 6. 判断整体验证结果
      if (result.summary.missingTypes.length > 0 || result.summary.extraTypes.length > 0) {
        result.isValid = false
        result.errors.push('前后端工单类型定义不一致')
      }
    } catch (error) {
      result.isValid = false
      // 优化错误信息，区分认证错误和其他错误
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any
        if (axiosError.response?.status === 401) {
          result.errors.push('未登录或登录已过期，请先登录')
        } else if (axiosError.response?.status === 400) {
          result.errors.push('请求参数错误或缺少必要参数')
        } else {
          result.errors.push(`验证过程中发生错误: ${axiosError.message || '未知错误'}`)
        }
      } else {
        result.errors.push(`验证过程中发生错误: ${error}`)
      }
    }

    return result
  }

  /**
   * 生成验证报告
   * @param validationResult 验证结果
   * @returns 格式化的报告字符串
   */
  static generateReport(validationResult: ValidationResult): string {
    const lines: string[] = []

    lines.push('=== 工单类型一致性验证报告 ===')
    lines.push(`验证时间: ${new Date().toLocaleString()}`)
    lines.push(`验证结果: ${validationResult.isValid ? '✅ 通过' : '❌ 失败'}`)
    lines.push('')

    // 摘要信息
    lines.push('📊 摘要信息:')
    lines.push(`  总类型数: ${validationResult.summary.totalTypes}`)
    lines.push(`  支持的类型数: ${validationResult.summary.supportedTypes}`)
    lines.push(`  缺失的类型数: ${validationResult.summary.missingTypes.length}`)
    lines.push(`  多余的类型数: ${validationResult.summary.extraTypes.length}`)
    lines.push('')

    // 错误信息
    if (validationResult.errors.length > 0) {
      lines.push('❌ 错误信息:')
      validationResult.errors.forEach((error) => {
        lines.push(`  - ${error}`)
      })
      lines.push('')
    }

    // 警告信息
    if (validationResult.warnings.length > 0) {
      lines.push('⚠️ 警告信息:')
      validationResult.warnings.forEach((warning) => {
        lines.push(`  - ${warning}`)
      })
      lines.push('')
    }

    // 缺失类型详情
    if (validationResult.summary.missingTypes.length > 0) {
      lines.push('🔍 缺失的类型:')
      validationResult.summary.missingTypes.forEach((typeId) => {
        lines.push(`  - 类型ID: ${typeId}`)
      })
      lines.push('')
    }

    // 多余类型详情
    if (validationResult.summary.extraTypes.length > 0) {
      lines.push('🔍 多余的类型:')
      validationResult.summary.extraTypes.forEach((typeId) => {
        lines.push(`  - 类型ID: ${typeId} (${WorkOrderTypeNames[typeId] || '未知'})`)
      })
      lines.push('')
    }

    // 建议
    lines.push('💡 建议:')
    if (validationResult.isValid) {
      lines.push('  - 前后端工单类型定义一致，无需修改')
    } else {
      lines.push('  - 建议使用 useWorkOrderTypes composable 动态获取工单类型')
      lines.push('  - 移除前端硬编码的工单类型定义')
      lines.push('  - 确保后端 work_order_type_mappings 表数据完整')
    }

    return lines.join('\n')
  }

  /**
   * 在开发环境下自动验证类型一致性
   */
  static async autoValidateInDev(): Promise<void> {
    if (process.env.NODE_ENV !== 'development') {
      return
    }

    try {
      // 检查用户是否已登录
      const storedToken = SecureStorage.getToken()
      if (!storedToken) {
        console.log('ℹ️ 用户未登录，跳过工单类型一致性验证')
        return
      }

      const result = await this.validateTypeConsistency()
      const report = this.generateReport(result)

      if (result.isValid) {
        console.log('✅ 工单类型一致性验证通过')
      } else {
        console.warn('⚠️ 工单类型一致性验证失败')
        console.log(report)
      }
    } catch (error) {
      console.warn('⚠️ 工单类型一致性验证失败 (可能是认证问题):', error)
    }
  }
}

/**
 * 开发环境下的类型检查器
 * 在应用启动时自动检查前后端类型一致性
 */
export const devTypeChecker = {
  /**
   * 初始化类型检查
   */
  async init() {
    if (process.env.NODE_ENV === 'development') {
      // 延迟执行，确保应用已完全启动
      setTimeout(() => {
        WorkOrderTypeValidator.autoValidateInDev()
      }, 3000)
    }
  }
}

// 导出验证函数供手动调用
export const validateWorkOrderTypes = WorkOrderTypeValidator.validateTypeConsistency
export const generateTypeReport = WorkOrderTypeValidator.generateReport
