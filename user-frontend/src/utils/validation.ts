/**
 * 企业级输入验证工具
 */

/**
 * 验证规则接口
 */
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean
  message?: string
}

/**
 * 验证器类
 */
export class Validator {
  /**
   * 验证手机号
   * @param phone 手机号
   * @returns 验证结果
   */
  static validatePhone(phone: string): ValidationResult {
    if (!phone) {
      return { valid: false, message: '手机号不能为空' }
    }

    // 中国大陆手机号正则
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      return { valid: false, message: '请输入正确的手机号格式' }
    }

    return { valid: true }
  }

  /**
   * 验证身份证号
   * @param idCard 身份证号
   * @returns 验证结果
   */
  static validateIdCard(idCard: string): ValidationResult {
    if (!idCard) {
      return { valid: false, message: '身份证号不能为空' }
    }

    // 18位身份证号正则
    const idCardRegex =
      /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
    if (!idCardRegex.test(idCard)) {
      return { valid: false, message: '请输入正确的身份证号格式' }
    }

    return { valid: true }
  }

  /**
   * 验证邮箱
   * @param email 邮箱
   * @returns 验证结果
   */
  static validateEmail(email: string): ValidationResult {
    if (!email) {
      return { valid: false, message: '邮箱不能为空' }
    }

    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    if (!emailRegex.test(email)) {
      return { valid: false, message: '请输入正确的邮箱格式' }
    }

    return { valid: true }
  }

  /**
   * 验证重量
   * @param weight 重量
   * @param maxWeight 最大重量限制
   * @returns 验证结果
   */
  static validateWeight(weight: number, maxWeight = 50): ValidationResult {
    if (weight === undefined || weight === null) {
      return { valid: false, message: '重量不能为空' }
    }

    if (weight <= 0) {
      return { valid: false, message: '重量必须大于0' }
    }

    if (weight > maxWeight) {
      return { valid: false, message: `重量不能超过${maxWeight}kg` }
    }

    return { valid: true }
  }

  /**
   * 验证地址
   * @param address 地址信息
   * @returns 验证结果
   */
  static validateAddress(address: {
    province?: string
    city?: string
    district?: string
    detail?: string
  }): ValidationResult {
    if (!address.province) {
      return { valid: false, message: '请选择省份' }
    }

    if (!address.city) {
      return { valid: false, message: '请选择城市' }
    }

    if (!address.district) {
      return { valid: false, message: '请选择区县' }
    }

    if (!address.detail || address.detail.trim().length < 2) {
      return { valid: false, message: '详细地址不能少于2个字符' }
    }

    // 检查是否包含敏感词或特殊字符
    const sensitiveWords = ['测试', 'test', '假地址']
    const hasSensitiveWord = sensitiveWords.some((word) =>
      address.detail!.toLowerCase().includes(word.toLowerCase())
    )

    if (hasSensitiveWord) {
      return { valid: false, message: '地址信息不能包含测试或虚假内容' }
    }

    return { valid: true }
  }

  /**
   * 验证金额
   * @param amount 金额
   * @param min 最小值
   * @param max 最大值
   * @returns 验证结果
   */
  static validateAmount(amount: number, min = 0.01, max = 999999): ValidationResult {
    if (amount === undefined || amount === null) {
      return { valid: false, message: '金额不能为空' }
    }

    if (amount < min) {
      return { valid: false, message: `金额不能小于${min}元` }
    }

    if (amount > max) {
      return { valid: false, message: `金额不能大于${max}元` }
    }

    // 检查小数位数
    const decimalPlaces = (amount.toString().split('.')[1] || '').length
    if (decimalPlaces > 2) {
      return { valid: false, message: '金额最多保留2位小数' }
    }

    return { valid: true }
  }

  /**
   * 验证订单号
   * @param orderNo 订单号
   * @returns 验证结果
   */
  static validateOrderNo(orderNo: string): ValidationResult {
    if (!orderNo) {
      return { valid: false, message: '订单号不能为空' }
    }

    // 订单号格式验证（字母数字下划线，长度6-50）
    const orderNoRegex = /^[A-Za-z0-9_-]{6,50}$/
    if (!orderNoRegex.test(orderNo)) {
      return { valid: false, message: '订单号格式不正确（6-50位字母数字）' }
    }

    return { valid: true }
  }

  /**
   * 验证运单号
   * @param trackingNo 运单号
   * @returns 验证结果
   */
  static validateTrackingNo(trackingNo: string): ValidationResult {
    if (!trackingNo) {
      return { valid: false, message: '运单号不能为空' }
    }

    // 运单号格式验证（数字字母，长度8-30）
    const trackingNoRegex = /^[A-Za-z0-9]{8,30}$/
    if (!trackingNoRegex.test(trackingNo)) {
      return { valid: false, message: '运单号格式不正确（8-30位字母数字）' }
    }

    return { valid: true }
  }

  /**
   * 防XSS处理
   * @param input 输入字符串
   * @returns 处理后的字符串
   */
  static sanitizeInput(input: string): string {
    if (!input) return ''

    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
  }

  /**
   * 验证文件上传
   * @param file 文件对象
   * @param options 验证选项
   * @returns 验证结果
   */
  static validateFile(
    file: File,
    options: {
      maxSize?: number // 最大文件大小（字节）
      allowedTypes?: string[] // 允许的文件类型
      allowedExtensions?: string[] // 允许的文件扩展名
    } = {}
  ): ValidationResult {
    const {
      maxSize = 5 * 1024 * 1024, // 默认5MB
      allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
      allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
    } = options

    if (!file) {
      return { valid: false, message: '请选择文件' }
    }

    // 检查文件大小
    if (file.size > maxSize) {
      return { valid: false, message: `文件大小不能超过${Math.round(maxSize / 1024 / 1024)}MB` }
    }

    // 检查文件类型
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, message: '不支持的文件类型' }
    }

    // 检查文件扩展名
    const fileName = file.name.toLowerCase()
    const hasValidExtension = allowedExtensions.some((ext) => fileName.endsWith(ext))
    if (!hasValidExtension) {
      return { valid: false, message: `只支持${allowedExtensions.join(', ')}格式的文件` }
    }

    return { valid: true }
  }

  /**
   * 批量验证
   * @param data 要验证的数据
   * @param rules 验证规则
   * @returns 验证结果
   */
  static validateBatch(
    data: Record<string, any>,
    rules: Record<string, ValidationRule>
  ): { valid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {}

    for (const [field, rule] of Object.entries(rules)) {
      const value = data[field]

      // 必填验证
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors[field] = rule.message || `${field}不能为空`
        continue
      }

      // 如果不是必填且值为空，跳过其他验证
      if (!rule.required && (value === undefined || value === null || value === '')) {
        continue
      }

      // 长度验证
      if (rule.min !== undefined && value.length < rule.min) {
        errors[field] = rule.message || `${field}长度不能少于${rule.min}个字符`
        continue
      }

      if (rule.max !== undefined && value.length > rule.max) {
        errors[field] = rule.message || `${field}长度不能超过${rule.max}个字符`
        continue
      }

      // 正则验证
      if (rule.pattern && !rule.pattern.test(value)) {
        errors[field] = rule.message || `${field}格式不正确`
        continue
      }

      // 自定义验证器
      if (rule.validator) {
        const result = rule.validator(value)
        if (result !== true) {
          errors[field] = typeof result === 'string' ? result : rule.message || `${field}验证失败`
        }
      }
    }

    return {
      valid: Object.keys(errors).length === 0,
      errors
    }
  }
}
