import DOMPurify from 'dompurify'

/**
 * 安全工具类
 */
export class SecurityUtils {
  /**
   * 清理HTML内容，防止XSS攻击
   * @param dirty 原始HTML内容
   * @returns 清理后的安全HTML
   */
  static sanitizeHtml(dirty: string): string {
    return DOMPurify.sanitize(dirty, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'span', 'div', 'p'],
      ALLOWED_ATTR: ['href', 'class', 'style'],
      ALLOW_DATA_ATTR: false
    })
  }

  /**
   * 清理文本内容，移除所有HTML标签
   * @param dirty 原始内容
   * @returns 纯文本内容
   */
  static sanitizeText(dirty: string): string {
    return DOMPurify.sanitize(dirty, { 
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: []
    })
  }

  /**
   * 验证URL是否安全
   * @param url 待验证的URL
   * @returns 是否为安全URL
   */
  static isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)
      return ['http:', 'https:'].includes(urlObj.protocol)
    } catch {
      return false
    }
  }

  /**
   * 生成安全的随机字符串
   * @param length 字符串长度
   * @returns 随机字符串
   */
  static generateRandomString(length: number = 16): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }
}

/**
 * 安全的本地存储管理
 */
export class SecureStorage {
  private static readonly TOKEN_KEY = 'secure_token'
  private static readonly EXPIRY_KEY = 'token_expiry'
  
  /**
   * 安全存储token
   * @param token JWT token
   * @param expiresIn 过期时间(秒)
   */
  static setToken(token: string, expiresIn: number = 3600): void {
    const expiry = Date.now() + (expiresIn * 1000)
    sessionStorage.setItem(this.TOKEN_KEY, token)
    sessionStorage.setItem(this.EXPIRY_KEY, expiry.toString())
  }

  /**
   * 获取token
   * @returns token或null
   */
  static getToken(): string | null {
    const token = sessionStorage.getItem(this.TOKEN_KEY)
    const expiry = sessionStorage.getItem(this.EXPIRY_KEY)
    
    if (!token || !expiry) return null
    
    if (Date.now() > parseInt(expiry)) {
      this.clearToken()
      return null
    }
    
    return token
  }

  /**
   * 清除token
   */
  static clearToken(): void {
    sessionStorage.removeItem(this.TOKEN_KEY)
    sessionStorage.removeItem(this.EXPIRY_KEY)
  }
}
