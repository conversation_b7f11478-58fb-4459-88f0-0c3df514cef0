/**
 * 统一日志管理工具
 * 根据环境变量控制日志输出，生产环境下不输出敏感信息
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error'

interface LoggerConfig {
  isDev: boolean
  enableConsoleInProd: boolean
  logLevel: LogLevel
}

class Logger {
  private config: LoggerConfig

  constructor() {
    this.config = {
      isDev: import.meta.env.DEV || process.env.NODE_ENV === 'development',
      enableConsoleInProd: false, // 生产环境默认关闭控制台输出
      logLevel: 'info'
    }
  }

  /**
   * 调试日志 - 仅开发环境输出
   */
  debug(message: string, ...args: any[]) {
    if (this.config.isDev) {
      console.debug(`[DEBUG] ${message}`, ...args)
    }
  }

  /**
   * 信息日志
   */
  info(message: string, ...args: any[]) {
    if (this.config.isDev || this.config.enableConsoleInProd) {
      console.info(`[INFO] ${message}`, ...args)
    }
  }

  /**
   * 警告日志
   */
  warn(message: string, ...args: any[]) {
    if (this.config.isDev || this.config.enableConsoleInProd) {
      console.warn(`[WARN] ${message}`, ...args)
    }
  }

  /**
   * 错误日志 - 始终输出，但在生产环境下不显示敏感信息
   */
  error(message: string, error?: any, sensitiveData?: any) {
    if (this.config.isDev) {
      console.error(`[ERROR] ${message}`, error, sensitiveData)
    } else {
      // 生产环境只输出基本错误信息，不输出敏感数据
      console.error(`[ERROR] ${message}`, error?.message || error)
      
      // 可以在这里发送到服务端日志系统
      this.sendToLogServer(message, error)
    }
  }

  /**
   * 业务日志 - 仅开发环境输出敏感业务数据
   */
  business(action: string, data?: any) {
    if (this.config.isDev) {
      console.log(`[BUSINESS] ${action}`, data)
    } else {
      // 生产环境只记录动作，不记录具体数据
      console.log(`[BUSINESS] ${action}`)
    }
  }

  /**
   * 性能日志
   */
  performance(label: string, startTime: number) {
    const duration = Date.now() - startTime
    if (this.config.isDev) {
      console.log(`[PERF] ${label}: ${duration}ms`)
    }
  }

  /**
   * 发送日志到服务端（生产环境）
   */
  private async sendToLogServer(message: string, error: any) {
    try {
      // 这里可以实现发送到服务端日志系统的逻辑
      // 例如发送到 /api/v1/system/error-log
      
      const logData = {
        timestamp: new Date().toISOString(),
        level: 'error',
        message: message,
        error: error?.message || String(error),
        userAgent: navigator.userAgent,
        url: window.location.href
      }

      // 使用 fetch 发送，避免依赖其他模块
      fetch('/api/v1/system/error-log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(logData)
      }).catch(() => {
        // 静默处理发送失败，避免递归错误
      })
    } catch (err) {
      // 静默处理，避免日志系统本身出错
    }
  }

  /**
   * 安全地记录用户操作（不包含敏感信息）
   */
  userAction(action: string, metadata?: Record<string, any>) {
    const safeMetadata = metadata ? this.sanitizeData(metadata) : undefined
    
    if (this.config.isDev) {
      console.log(`[USER_ACTION] ${action}`, safeMetadata)
    }
  }

  /**
   * 清理敏感数据
   */
  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data
    }

    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'authorization', 'cookie']
    const sanitized = { ...data }

    for (const key in sanitized) {
      if (sensitiveKeys.some(sensitiveKey => 
        key.toLowerCase().includes(sensitiveKey.toLowerCase())
      )) {
        sanitized[key] = '[REDACTED]'
      } else if (typeof sanitized[key] === 'object') {
        sanitized[key] = this.sanitizeData(sanitized[key])
      }
    }

    return sanitized
  }
}

// 导出单例实例
export const logger = new Logger()

// 兼容旧的console调用（开发期间的过渡方案）
export const secureConsole = {
  log: logger.debug.bind(logger),
  info: logger.info.bind(logger),
  warn: logger.warn.bind(logger),
  error: logger.error.bind(logger),
  debug: logger.debug.bind(logger)
}

export default logger 