import { ElMessage, ElNotification } from 'element-plus'
import type { AxiosError } from 'axios'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTH = 'AUTH',
  PERMISSION = 'PERMISSION',
  VALIDATION = 'VALIDATION',
  BUSINESS = 'BUSINESS',
  SYSTEM = 'SYSTEM',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  type: ErrorType
  code: string | number
  message: string
  details?: any
  timestamp: number
}

/**
 * 业务错误码映射
 */
const ERROR_CODE_MAP: Record<string | number, { type: ErrorType; message: string }> = {
  // 认证相关错误
  401: { type: ErrorType.AUTH, message: '登录已过期，请重新登录' },
  403: { type: ErrorType.PERMISSION, message: '权限不足，无法访问该资源' },

  // 网络相关错误
  NETWORK_ERROR: { type: ErrorType.NETWORK, message: '网络连接失败，请检查网络设置' },
  TIMEOUT: { type: ErrorType.NETWORK, message: '请求超时，请稍后重试' },

  // 业务错误码
  INSUFFICIENT_BALANCE: { type: ErrorType.BUSINESS, message: '账户余额不足，请联系客服' },
  ORDER_NOT_FOUND: { type: ErrorType.BUSINESS, message: '订单不存在或已被删除' },
  EXPRESS_COMPANY_NOT_SUPPORTED: { type: ErrorType.BUSINESS, message: '不支持该快递公司' },
  INVALID_ADDRESS: { type: ErrorType.VALIDATION, message: '地址信息不完整或格式错误' },
  WEIGHT_EXCEEDED: { type: ErrorType.VALIDATION, message: '包裹重量超出限制' },
  PRICE_EXPIRED: { type: ErrorType.BUSINESS, message: '价格已过期，请重新查询' },

  // 系统错误
  500: { type: ErrorType.SYSTEM, message: '服务器内部错误，请稍后重试' },
  502: { type: ErrorType.SYSTEM, message: '服务暂时不可用，请稍后重试' },
  503: { type: ErrorType.SYSTEM, message: '服务维护中，请稍后重试' }
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  /**
   * 处理API错误
   * @param error 错误对象
   * @param showNotification 是否显示通知
   * @returns 错误信息
   */
  static handleApiError(error: any, showNotification = true): ErrorInfo {
    const errorInfo = this.parseError(error)

    if (showNotification) {
      this.showErrorNotification(errorInfo)
    }

    // 记录错误日志
    this.logError(errorInfo, error)

    return errorInfo
  }

  /**
   * 解析错误对象
   * @param error 原始错误
   * @returns 错误信息
   */
  private static parseError(error: any): ErrorInfo {
    const timestamp = Date.now()

    // Axios错误
    if (error.isAxiosError) {
      const axiosError = error as AxiosError

      if (!axiosError.response) {
        // 网络错误
        return {
          type: ErrorType.NETWORK,
          code: 'NETWORK_ERROR',
          message: '网络连接失败，请检查网络设置',
          timestamp
        }
      }

      const { status, data } = axiosError.response
      const errorCode = (data as any)?.code || (data as any)?.error_code || status
      const errorMessage = (data as any)?.message || (data as any)?.error || axiosError.message

      const mappedError = ERROR_CODE_MAP[errorCode]
      if (mappedError) {
        return {
          type: mappedError.type,
          code: errorCode,
          message: mappedError.message,
          details: data,
          timestamp
        }
      }

      return {
        type: this.getErrorTypeByStatus(status),
        code: errorCode,
        message: errorMessage || '请求失败',
        details: data,
        timestamp
      }
    }

    // 业务错误
    if (error.code && ERROR_CODE_MAP[error.code]) {
      const mappedError = ERROR_CODE_MAP[error.code]
      return {
        type: mappedError.type,
        code: error.code,
        message: error.message || mappedError.message,
        details: error,
        timestamp
      }
    }

    // 未知错误
    return {
      type: ErrorType.UNKNOWN,
      code: 'UNKNOWN',
      message: error.message || '发生未知错误',
      details: error,
      timestamp
    }
  }

  /**
   * 根据HTTP状态码获取错误类型
   * @param status HTTP状态码
   * @returns 错误类型
   */
  private static getErrorTypeByStatus(status: number): ErrorType {
    if (status === 401) return ErrorType.AUTH
    if (status === 403) return ErrorType.PERMISSION
    if (status >= 400 && status < 500) return ErrorType.VALIDATION
    if (status >= 500) return ErrorType.SYSTEM
    return ErrorType.UNKNOWN
  }

  /**
   * 显示错误通知
   * @param errorInfo 错误信息
   */
  private static showErrorNotification(errorInfo: ErrorInfo): void {
    const { type, message } = errorInfo

    switch (type) {
      case ErrorType.AUTH:
        ElNotification({
          title: '认证失败',
          message,
          type: 'error',
          duration: 5000
        })
        // 可以在这里触发登出逻辑
        break

      case ErrorType.PERMISSION:
        ElMessage({
          message,
          type: 'warning',
          duration: 3000
        })
        break

      case ErrorType.VALIDATION:
        ElMessage({
          message,
          type: 'warning',
          duration: 3000
        })
        break

      case ErrorType.BUSINESS:
        ElMessage({
          message,
          type: 'error',
          duration: 4000
        })
        break

      case ErrorType.NETWORK:
        ElNotification({
          title: '网络错误',
          message,
          type: 'error',
          duration: 5000
        })
        break

      case ErrorType.SYSTEM:
        ElNotification({
          title: '系统错误',
          message,
          type: 'error',
          duration: 6000
        })
        break

      default:
        ElMessage({
          message,
          type: 'error',
          duration: 3000
        })
    }
  }

  /**
   * 记录错误日志
   * @param errorInfo 错误信息
   * @param originalError 原始错误对象
   */
  private static logError(errorInfo: ErrorInfo, originalError: any): void {
    const logData = {
      ...errorInfo,
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId(),
      stack: originalError?.stack
    }

    // 开发环境直接打印
    if (import.meta.env.DEV) {
      console.error('Error occurred:', logData)
      return
    }

    // 生产环境发送到日志服务
    this.sendErrorLog(logData).catch((err) => {
      console.error('Failed to send error log:', err)
    })
  }

  /**
   * 获取当前用户ID
   * @returns 用户ID
   */
  private static getCurrentUserId(): string | null {
    try {
      const userInfo = localStorage.getItem('userInfo')
      if (userInfo) {
        const user = JSON.parse(userInfo)
        return user.id || null
      }
    } catch {
      // 忽略解析错误
    }
    return null
  }

  /**
   * 发送错误日志到服务器
   * @param logData 日志数据
   */
  private static async sendErrorLog(logData: any): Promise<void> {
    try {
      await fetch('/api/v1/system/error-log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(logData)
      })
    } catch (error) {
      // 静默处理日志发送失败
      console.warn('Failed to send error log to server:', error)
    }
  }

  /**
   * 创建重试函数
   * @param fn 要重试的函数
   * @param maxRetries 最大重试次数
   * @param delay 重试延迟（毫秒）
   * @returns 带重试功能的函数
   */
  static createRetryFunction<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    maxRetries = 3,
    delay = 1000
  ): T {
    return (async (...args: Parameters<T>) => {
      let lastError: any

      for (let i = 0; i <= maxRetries; i++) {
        try {
          return await fn(...args)
        } catch (error) {
          lastError = error

          // 如果是最后一次重试或者是不可重试的错误，直接抛出
          if (i === maxRetries || !this.isRetryableError(error)) {
            throw error
          }

          // 等待后重试
          await new Promise((resolve) => setTimeout(resolve, delay * Math.pow(2, i)))
        }
      }

      throw lastError
    }) as T
  }

  /**
   * 判断错误是否可重试
   * @param error 错误对象
   * @returns 是否可重试
   */
  private static isRetryableError(error: any): boolean {
    // 网络错误可重试
    if (error.code === 'NETWORK_ERROR' || !error.response) {
      return true
    }

    // 5xx服务器错误可重试
    if (error.response?.status >= 500) {
      return true
    }

    // 超时错误可重试
    if (error.code === 'TIMEOUT') {
      return true
    }

    return false
  }
}
