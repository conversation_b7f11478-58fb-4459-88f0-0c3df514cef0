<template>
  <div class="callback-list">
    <!-- 用户信息展示 -->
    <UserInfoDisplay />

    <!-- API密钥管理 -->
    <ApiKeyManagement />

    <!-- 回调管理 -->
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>回调记录管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="showConfigDialog = true">
              <el-icon><Setting /></el-icon>
              回调配置
            </el-button>
            <el-button type="info" @click="showStatistics = !showStatistics">
              <el-icon><DataAnalysis /></el-icon>
              统计信息
            </el-button>
            <el-button @click="testCallback">
              <el-icon><Connection /></el-icon>
              测试回调
            </el-button>
          </div>
        </div>
      </template>

      <!-- 统计信息 -->
      <el-collapse-transition>
        <div v-show="showStatistics" class="statistics-section">
          <el-row :gutter="20" class="statistics-cards">
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-item">
                  <div class="stat-icon total">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ statistics?.total_callbacks || 0 }}</div>
                    <div class="stat-label">总转发数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-item">
                  <div class="stat-icon success">
                    <el-icon><SuccessFilled /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ statistics?.success_rate || 0 }}%</div>
                    <div class="stat-label">转发成功率</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-item">
                  <div class="stat-icon today">
                    <el-icon><Clock /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ statistics?.last_24h_stats?.total || 0 }}</div>
                    <div class="stat-label">24小时内</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-item">
                  <div class="stat-icon failed">
                    <el-icon><WarningFilled /></el-icon>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ statistics?.last_24h_stats?.failed || 0 }}</div>
                    <div class="stat-label">24小时失败</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>

      <!-- 搜索筛选区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="运单号">
            <el-input v-model="searchForm.tracking_no" placeholder="请输入运单号" clearable />
          </el-form-item>
          <el-form-item label="事件类型">
            <el-select v-model="searchForm.event_type" placeholder="请选择事件类型" clearable>
              <el-option label="状态变更" value="order_status_changed" />
              <el-option label="计费更新" value="billing_updated" />
              <el-option label="工单回复" value="ticket_replied" />
            </el-select>
          </el-form-item>
          <el-form-item label="转发状态">
            <el-select v-model="searchForm.status" placeholder="请选择转发状态" clearable>
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
              <el-option label="处理中" value="pending" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleRefresh" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 空状态提示 -->
      <div v-if="!loading && (!callbackList || callbackList.length === 0)" class="empty-state">
        <el-empty description="暂无回调记录">
          <template #image>
            <el-icon size="60" color="#c0c4cc"><Connection /></el-icon>
          </template>
          <div class="empty-actions">
            <p class="empty-tip">回调记录会在以下情况下产生：</p>
            <ul class="empty-list">
              <li>订单状态发生变更时</li>
              <li>计费信息更新时</li>
              <li>工单有新回复时</li>
            </ul>
            <div class="empty-buttons">
              <p class="empty-guide">请使用页面右上角的按钮进行配置和测试</p>
            </div>
          </div>
        </el-empty>
      </div>

      <!-- 批量操作区域 -->
      <div v-if="!loading && callbackList && callbackList.length > 0" class="batch-actions">
        <div class="batch-left">
          <el-checkbox
            v-model="selectAll"
            :indeterminate="isIndeterminate"
            @change="handleSelectAll"
          >
            全选
          </el-checkbox>
          <span class="selected-count">已选择 {{ selectedRecords.length }} 条记录</span>
        </div>
        <div class="batch-right">
          <el-button
            v-if="selectedRecords.length > 0 && canBatchRetry"
            type="warning"
            size="small"
            @click="batchRetryCallbacks"
            :loading="batchRetrying"
          >
            <el-icon><RefreshLeft /></el-icon>
            批量重推 ({{ selectedRecords.filter(record => canRetry(record)).length }}/{{ selectedRecords.length }})
          </el-button>
        </div>
      </div>

      <!-- 回调记录列表 -->
      <el-table
        v-if="loading || (callbackList && callbackList.length > 0)"
        :data="callbackList"
        v-loading="loading"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="记录ID" width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="record-id">{{ row.id.substring(0, 8) }}...</span>
          </template>
        </el-table-column>
        <el-table-column prop="event_type" label="状态分类" width="160">
          <template #default="{ row }">
            <div class="status-detail">
              <el-icon :color="getRecordStatusDetail(row).color" class="status-icon">
                <component :is="getRecordStatusDetail(row).icon" />
              </el-icon>
              <div class="status-info">
                <div class="status-name">{{ getRecordStatusDetail(row).name }}</div>
                <div class="status-category">{{ getRecordStatusDetail(row).category }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="tracking_no" label="运单号" width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.tracking_no" class="tracking-no">{{ row.tracking_no }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="customer_order_no"
          label="客户订单号"
          width="160"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span v-if="row.customer_order_no" class="order-no">{{ row.customer_order_no }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="转发状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="retry_count" label="重试次数" width="100" align="center">
          <template #default="{ row }">
            <span :class="{ 'retry-warning': row.retry_count > 0 }">{{ row.retry_count }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="request_at" label="转发时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.request_at || row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="viewDetail(row)">详情</el-button>
              <el-button
                v-if="canRetry(row)"
                type="warning"
                size="small"
                @click="retryCallback(row)"
                :loading="retryingRecords.has(row.id)"
              >
                重推
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="!loading && callbackList && callbackList.length > 0" class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 回调详情对话框 -->
    <CallbackDetailDialog v-model:visible="showDetailDialog" :callback-record="selectedRecord" />

    <!-- 回调配置对话框 -->
    <CallbackConfigDialog v-model:visible="showConfigDialog" @success="handleConfigSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Setting,
    DataAnalysis,
    Document,
    SuccessFilled,
    Clock,
    WarningFilled,
    Connection,
    User,
    Check,
    Box,
    LocationInformation,
    Van,
    Shop,
    CircleCloseFilled,
    RefreshLeft,
    QuestionFilled,
    CircleClose,
    AlarmClock,
    Money,
    Lightning,
    More,
    Bell,
    Timer,
    Refresh,
    ChatDotRound
  } from '@element-plus/icons-vue'
  import { CallbackService } from '@/api'
  import type { CallbackRecord, CallbackListRequest, CallbackStatistics } from '@/api'
  import { ErrorHandler } from '@/utils/errorHandler'
  import { SecureStorage } from '@/utils/security'
  import {
    getStatusDetail
  } from '@/utils/callbackStatusMapping'
  import { formatDateTimeDefault } from '@/utils/timezone'
  import CallbackDetailDialog from './components/CallbackDetailDialog.vue'
  import CallbackConfigDialog from './components/CallbackConfigDialog.vue'
  import ApiKeyManagement from './components/ApiKeyManagement.vue'
  import UserInfoDisplay from './components/UserInfoDisplay.vue'

  // 响应式数据
  const loading = ref(false)
  const showStatistics = ref(false) // 默认隐藏统计信息
  const showDetailDialog = ref(false)
  const showConfigDialog = ref(false)
  const callbackList = ref<CallbackRecord[]>([])
  const selectedRecord = ref<CallbackRecord | null>(null)
  const statistics = ref<CallbackStatistics | null>(null)
  const dateRange = ref<[string, string] | undefined>(undefined)

  // 🔥 新增：回调重推相关状态
  const selectedRecords = ref<CallbackRecord[]>([])
  const selectAll = ref(false)
  const retryingRecords = ref(new Set<string>())
  const batchRetrying = ref(false)

  // 搜索表单
  const searchForm = reactive<CallbackListRequest>({
    page: 1,
    page_size: 20,
    tracking_no: undefined,
    event_type: undefined,
    status: undefined
  })

  // 分页信息
  const pagination = reactive({
    page: 1,
    page_size: 20,
    total: 0
  })



  // 🔥 新增：重推相关计算属性
  const isIndeterminate = computed(() => {
    const selectedCount = selectedRecords.value.length
    const totalCount = callbackList.value.length
    return selectedCount > 0 && selectedCount < totalCount
  })

  const canBatchRetry = computed(() => {
    return selectedRecords.value.some(record => canRetry(record))
  })

  // 🔥 修复：获取回调记录列表（修复分页和性能问题）
  const getCallbackList = async (forceRefresh = false) => {
    loading.value = true
    try {
      // 🔥 修复：确保分页参数正确
      const params = {
        page: pagination.page,
        page_size: pagination.page_size,
        tracking_no: searchForm.tracking_no || undefined,
        event_type: searchForm.event_type || undefined,
        status: searchForm.status || undefined,
        start_time: dateRange.value?.[0] || undefined,
        end_time: dateRange.value?.[1] || undefined,
        // 🔥 新增：添加时间戳参数强制刷新，避免缓存
        _t: forceRefresh ? Date.now() : undefined
      }

      console.log('发送查询参数:', params)

      const response = await CallbackService.getCallbackRecords(params)

      if (response.success && response.data) {
        callbackList.value = response.data.records || []
        pagination.total = response.data.total || response.data.records?.length || 0
      } else {
        // 如果是认证失败，暂时使用空数据（后续需要实现认证）
        if (response.message?.includes('未认证')) {
          callbackList.value = []
          pagination.total = 0
          console.warn('用户未认证，显示空数据')
        } else {
          throw new Error(response.message || '获取回调记录失败')
        }
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
      callbackList.value = []
      pagination.total = 0
    } finally {
      loading.value = false
    }
  }

  // 获取统计信息
  const getStatistics = async () => {
    try {
      const response = await CallbackService.getCallbackStatistics(
        dateRange.value?.[0],
        dateRange.value?.[1]
      )
      if (response.success && response.data) {
        statistics.value = response.data
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
    }
  }

  // 🔥 修复：获取回调记录的状态详情（与后端保持一致）
  const getRecordStatusDetail = (record: CallbackRecord) => {
    // 尝试从不同字段提取状态信息
    let statusCode = ''
    let extraData: any = {}
    let pushType = 0

    // 从请求数据中提取状态信息
    if (record.request_data) {
      try {
        const requestData =
          typeof record.request_data === 'string'
            ? JSON.parse(record.request_data)
            : record.request_data

        // 提取推送类型
        if (requestData.pushType) {
          pushType = parseInt(requestData.pushType)
        }

        // 🔥 新增：从event_type提取推送类型（易达回调格式）
        if (requestData.event_type) {
          pushType = parseInt(requestData.event_type)
        }

        // 🔥 修复：完善各供应商状态提取逻辑

        // 1. 易达格式的状态提取
        if (requestData.contextObj) {
          if (requestData.contextObj.ydOrderStatus) {
            // 易达状态码映射（与后端一致）
            const yidaStatusMap: Record<string, string> = {
              '1': 'awaiting_pickup',
              '2': 'in_transit',
              '3': 'delivered',
              '4': 'exception',
              '5': 'cancelled',
              '11': 'picked_up'
            }
            statusCode = yidaStatusMap[requestData.contextObj.ydOrderStatus] || 'unknown'
          }
          extraData = requestData.contextObj
        }

        // 2. 云通格式的状态提取
        if (requestData.Data && Array.isArray(requestData.Data) && requestData.Data.length > 0) {
          const yunTongData = requestData.Data[0]
          if (yunTongData.State) {
            // 云通状态码映射（与后端一致）
            const yuntongStatusMap: Record<string, string> = {
              '100': 'created',
              '102': 'assigned',
              '103': 'assigned', // 🔥 关键修复：分配快递员
              '104': 'picked_up',
              '2': 'in_transit', // 🔥 新增：运输中状态
              '3': 'delivered', // 🔥 新增：已签收状态
              '203': 'cancelled',
              '204': 'pickup_failed',
              '208': 'weight_updated',
              '301': 'billed',
              '400': 'submit_failed',
              '401': 'ticket_replied',
              '500': 'exception',
              '501': 'forwarded'
            }
            statusCode = yuntongStatusMap[yunTongData.State.toString()] || 'unknown'
          }
          extraData = yunTongData
        }

        // 3. 快递100格式的状态提取
        if (requestData.status) {
          // 快递100状态映射
          const kuaidi100StatusMap: Record<string, string> = {
            '0': 'created',
            '1': 'assigned',
            '2': 'collecting',
            '10': 'picked_up',
            '11': 'pickup_failed',
            '13': 'delivered',
            '14': 'exception_delivered',
            '99': 'cancelled',
            '101': 'in_transit',
            '155': 'weight_updated'
          }
          statusCode = kuaidi100StatusMap[requestData.status.toString()] || 'unknown'
          extraData = requestData
        }

        // 4. 统一格式的状态提取（后端标准化后的格式）
        if (requestData.data && requestData.data.status) {
          statusCode = requestData.data.status.code || requestData.data.status
          extraData = requestData.data
        }

        // 5. 直接从标准化数据中提取状态
        if ((record as any).standardized_data) {
          try {
            const standardizedData = typeof (record as any).standardized_data === 'string'
              ? JSON.parse((record as any).standardized_data)
              : (record as any).standardized_data

            if (standardizedData.data && standardizedData.data.new_status) {
              statusCode = standardizedData.data.new_status
              extraData = standardizedData.data
            }
          } catch (error) {
            console.warn('解析标准化数据失败:', error)
          }
        }
      } catch (error) {
        console.warn('解析回调数据失败:', error)
      }
    }

    // 🔥 新增：根据推送类型确定状态分类
    let statusCategory = '其他'
    switch (pushType) {
      case 1:
        statusCategory = '状态更新'
        break
      case 2:
        statusCategory = '计费'
        break
      case 3:
        statusCategory = '揽收'
        // 🔥 揽件员推送特殊处理：设置为已分配揽件员状态
        if (!statusCode || statusCode === 'unknown') {
          statusCode = 'assigned'
        }
        break
      default:
        // 根据事件类型判断
        switch (record.event_type) {
          case 'order_status_changed':
            statusCategory = '状态更新'
            break
          case 'billing_updated':
            statusCategory = '计费'
            break
          case 'ticket_replied':
            statusCategory = '工单'
            break
          default:
            statusCategory = '其他'
        }
    }

    return getStatusDetail(record.event_type, statusCode, extraData, statusCategory)
  }

  // 搜索
  const handleSearch = () => {
    pagination.page = 1
    getCallbackList(true) // 强制刷新
    getStatistics()
  }

  // 🔥 优化：重置（包含新增的状态分类字段）
  const handleReset = () => {
    Object.assign(searchForm, {
      page: 1,
      page_size: 20,
      tracking_no: undefined,
      event_type: undefined,
      status: undefined,
      status_category: undefined,
      specific_status: undefined
    })
    dateRange.value = undefined
    pagination.page = 1
    getCallbackList(true) // 强制刷新
    getStatistics()
  }

  // 🔥 优化：分页处理（强制刷新数据）
  const handleSizeChange = (size: number) => {
    pagination.page_size = size
    pagination.page = 1
    getCallbackList(true) // 强制刷新
  }

  const handleCurrentChange = (page: number) => {
    pagination.page = page
    getCallbackList(true) // 强制刷新
  }

  // 查看详情
  const viewDetail = (record: CallbackRecord) => {
    selectedRecord.value = record
    showDetailDialog.value = true
  }

  // 移除重试回调功能 - 用户不应该有重试权限

  // 测试回调
  const testCallback = async () => {
    try {
      const response = await CallbackService.testCallback()
      if (response.success) {
        ElMessage.success('测试回调已发送，请检查您的回调地址')
      } else {
        ElMessage.error(response.message || '测试回调发送失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
    }
  }

  // 🔥 新增：手动刷新数据
  const handleRefresh = () => {
    ElMessage.info('正在刷新最新数据...')
    getCallbackList(true) // 强制刷新
    getStatistics()
  }

  // 🔥 优化：配置成功回调（强制刷新数据）
  const handleConfigSuccess = () => {
    ElMessage.success('回调配置更新成功')
    // 重新加载数据，强制刷新
    getCallbackList(true)
    getStatistics()
  }

  // 工具函数
  const getEventTypeName = (type: string) => {
    const nameMap: Record<string, string> = {
      order_status_changed: '订单状态变更',
      billing_updated: '计费更新',
      ticket_replied: '工单回复'
    }
    return nameMap[type] || type
  }

  const getStatusType = (status: string) => {
    // 🔥 修复：扩展状态类型映射，支持所有可能的状态值
    const typeMap: Record<string, string> = {
      // 回调转发状态
      success: 'success',
      failed: 'danger',
      pending: 'warning',
      processing: 'warning',

      // 成功状态（绿色）
      delivered: 'success',
      exception_delivered: 'success',
      // 🚨 已删除：settled - 结算功能已废弃
      billed: 'success',
      '3': 'success', // 易达已签收
      '100': 'success', // 云通下单成功
      '301': 'success', // 云通已计费

      // 处理中状态（蓝色/信息色）
      created: 'info',
      submitted: 'info',
      assigned: 'info',
      pending_pickup: 'info',
      awaiting_pickup: 'info',
      picked_up: 'info',
      accepted: 'info',
      collecting: 'info',
      in_transit: 'info',
      delivering: 'info',
      weight_updated: 'info',
      forwarded: 'info',
      '1': 'info', // 易达待取件
      '2': 'info', // 易达运输中
      '11': 'info', // 易达已取件
      '102': 'info', // 云通分配网点
      '103': 'info', // 云通分配快递员
      '104': 'info', // 云通已取件
      '208': 'info', // 云通重量更新
      '501': 'info', // 云通已转寄

      // 警告状态（橙色）
      exception: 'warning',
      '4': 'warning', // 易达异常
      '6': 'warning', // 易达异常
      '500': 'warning', // 云通异常
      '401': 'warning', // 云通工单回复

      // 失败状态（红色）
      cancelled: 'danger',
      pickup_failed: 'danger',
      returned: 'danger',
      voided: 'danger',
      '5': 'danger', // 易达已取消
      '10': 'danger', // 易达已取消
      '400': 'danger', // 云通下单失败
      '203': 'danger', // 云通已取消
      '204': 'danger', // 云通取件失败
      '205': 'danger' // 云通已作废
    }
    return typeMap[status] || 'info'
  }

  const getStatusName = (status: string) => {
    // 🔥 修复：扩展状态映射，支持所有可能的状态值
    const nameMap: Record<string, string> = {
      // 回调转发状态
      success: '成功',
      failed: '失败',
      pending: '处理中',
      processing: '处理中',

      // 订单状态
      created: '已下单',
      submitted: '已提交',
      assigned: '已分配',
      pending_pickup: '待取件',
      awaiting_pickup: '待取件',
      picked_up: '已取件',
      pickup_failed: '取件失败',
      accepted: '已接受',
      collecting: '收件中',
      in_transit: '运输中',
      delivering: '派送中',
      delivered: '已签收',
      exception_delivered: '异常签收',
      exception: '异常',
      cancelled: '已取消',
      returned: '已退回',
      forwarded: '已转寄',
      // 🚨 已删除：settled - 结算功能已废弃
      weight_updated: '重量更新',
      billed: '已计费',
      voided: '已作废',

      // 易达状态
      '1': '待取件',
      '2': '运输中',
      '3': '已签收',
      '4': '异常',
      '5': '已取消',
      '6': '异常',
      '10': '已取消',
      '11': '已取件',

      // 云通状态
      '100': '下单成功',
      '400': '下单失败',
      '102': '分配网点',
      '103': '分配快递员',
      '104': '已取件',
      '301': '已计费',
      '208': '重量更新',
      '203': '已取消',
      '204': '取件失败',
      '205': '已作废',
      '500': '异常',
      '501': '已转寄',
      '401': '工单回复'
    }
    return nameMap[status] || '未知状态'
  }

  const formatDate = (dateString: string) => {
    return formatDateTimeDefault(dateString)
  }

  // 🔥 新增：回调重推相关方法

  // 🔥 修复：判断是否可以重推 - 允许所有回调记录重推
  const canRetry = (record: CallbackRecord) => {
    // 🔥 修复：所有回调记录都可以重推，只要重试次数不超过10次
    // 这样用户可以重新推送成功的回调，也可以重试失败的回调
    return (record.retry_count || 0) < 10
  }

  // 单个回调重推
  const retryCallback = async (record: CallbackRecord) => {
    try {
      await ElMessageBox.confirm(
        `确定要重新推送回调记录吗？\n运单号：${record.tracking_no || '无'}\n当前重试次数：${record.retry_count || 0}\n注意：这将重新发送回调到您的回调地址`,
        '确认重推',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      retryingRecords.value.add(record.id)

      const response = await CallbackService.retryCallback(record.id)

      if (response.success) {
        ElMessage.success('回调重推已启动，请稍后查看结果')
        // 刷新列表
        await getCallbackList(true)
      } else {
        throw new Error(response.message || '重推失败')
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        ErrorHandler.handleApiError(error)
      }
    } finally {
      retryingRecords.value.delete(record.id)
    }
  }

  // 批量重推
  const batchRetryCallbacks = async () => {
    const retryableRecords = selectedRecords.value.filter(record => canRetry(record))

    if (retryableRecords.length === 0) {
      ElMessage.warning('所选记录中没有可重推的回调（可能重试次数已达上限）')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要批量重推 ${retryableRecords.length} 条回调记录吗？\n注意：这将重新发送回调到您的回调地址`,
        '确认批量重推',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      batchRetrying.value = true

      const recordIds = retryableRecords.map(record => record.id)
      const response = await CallbackService.batchRetryCallbacks(recordIds)

      if (response.success) {
        ElMessage.success(`批量重推已启动，共处理 ${retryableRecords.length} 条记录`)
        // 清空选择
        selectedRecords.value = []
        selectAll.value = false
        // 刷新列表
        await getCallbackList(true)
      } else {
        throw new Error(response.message || '批量重推失败')
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        ErrorHandler.handleApiError(error)
      }
    } finally {
      batchRetrying.value = false
    }
  }

  // 选择相关方法
  const handleSelectionChange = (selection: CallbackRecord[]) => {
    selectedRecords.value = selection
    // 更新全选状态：当选中数量等于总数量且总数量大于0时为全选
    selectAll.value = selection.length > 0 && selection.length === callbackList.value.length
  }

  const handleSelectAll = (checked: boolean | string | number) => {
    // 转换为boolean类型
    const isChecked = Boolean(checked)

    if (isChecked) {
      // 全选：选中所有记录
      selectedRecords.value = [...callbackList.value]
    } else {
      // 取消全选：清空选择
      selectedRecords.value = []
    }

    // 更新全选状态
    selectAll.value = isChecked
  }

  // 初始化
  onMounted(() => {
    // 检查用户认证状态
    const token = SecureStorage.getToken()
    if (!token) {
      ElMessage.warning('请先登录后再访问回调管理')
      // 可以在这里跳转到登录页面
      // router.push('/login')
      return
    }

    getCallbackList()
    getStatistics()
  })
</script>

<style scoped>
  .callback-list {
    padding: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-actions {
    display: flex;
    gap: 10px;
  }

  .statistics-section {
    margin-bottom: 20px;
  }

  .statistics-cards {
    margin-bottom: 20px;
  }

  .stat-card {
    height: 100px;
  }

  .stat-item {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
  }

  .stat-icon.total {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .stat-icon.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .stat-icon.today {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .stat-icon.failed {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 20px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 5px;
  }

  .stat-label {
    font-size: 12px;
    color: #909399;
  }

  .search-area {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }

  .record-id {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #606266;
  }

  .tracking-no {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #606266;
    font-weight: 500;
  }

  .order-no {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #409eff;
    font-weight: 500;
  }

  .no-data {
    color: #c0c4cc;
    font-style: italic;
  }

  /* 🔥 新增：状态详情样式 */
  .status-detail {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .status-icon {
    font-size: 16px;
    flex-shrink: 0;
  }

  .status-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    min-width: 0;
  }

  .status-name {
    font-weight: 500;
    font-size: 13px;
    line-height: 1.2;
    color: #303133;
  }

  .status-category {
    font-size: 11px;
    color: #909399;
    line-height: 1;
  }

  .retry-warning {
    color: #e6a23c;
    font-weight: bold;
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .empty-state {
    padding: 40px 20px;
    text-align: center;
  }

  .empty-actions {
    margin-top: 20px;
  }

  .empty-tip {
    font-size: 14px;
    color: #606266;
    margin-bottom: 15px;
  }

  .empty-list {
    text-align: left;
    display: inline-block;
    margin: 15px 0;
    padding-left: 20px;
  }

  .empty-list li {
    margin: 8px 0;
    color: #909399;
    font-size: 13px;
  }

  .empty-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
  }

  .empty-guide {
    color: #409eff;
    font-size: 13px;
    margin: 0;
    padding: 8px 16px;
    background-color: #ecf5ff;
    border-radius: 4px;
    border: 1px solid #d9ecff;
  }

  /* 🔥 新增：批量操作区域样式 */
  .batch-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 16px;
  }

  .batch-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .selected-count {
    font-size: 14px;
    color: #606266;
  }

  .batch-right {
    display: flex;
    gap: 8px;
  }

  /* 🔥 新增：操作按钮样式 */
  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .action-buttons .el-button {
    margin: 0;
  }
</style>
