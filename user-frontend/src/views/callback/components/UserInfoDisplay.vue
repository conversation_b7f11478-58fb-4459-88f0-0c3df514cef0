<template>
  <el-card class="user-info-card">
    <template #header>
      <div class="card-header">
        <span>用户信息</span>
        <el-button type="primary" size="small" @click="refreshUserInfo" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </template>

    <div v-loading="loading" class="user-info-content">
      <!-- 错误状态 -->
      <div v-if="error" class="error-state">
        <el-alert
          title="获取用户信息失败"
          type="error"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>{{ error }}</p>
            <el-button type="primary" size="small" @click="refreshUserInfo">
              重试
            </el-button>
          </template>
        </el-alert>
      </div>

      <!-- 用户未登录状态 -->
      <div v-else-if="!isLoggedIn" class="not-logged-in">
        <el-alert
          title="用户未登录"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>请先登录以查看用户信息</p>
          </template>
        </el-alert>
      </div>

      <!-- 用户已登录状态 -->
      <div v-else>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="用户ID">
            <div class="user-id-display">
              <code class="user-id">{{ userInfo?.id || '-' }}</code>
              <el-button
                v-if="userInfo?.id"
                type="text"
                size="small"
                @click="copyToClipboard(String(userInfo.id), '用户ID')"
                :icon="CopyDocument"
                title="复制用户ID"
              />
            </div>
          </el-descriptions-item>

          <el-descriptions-item label="用户名">
            <div class="username-display">
              <span class="username">{{ userInfo?.username || '-' }}</span>
              <span v-if="userInfo?.nickname && userInfo.nickname !== userInfo.username" class="nickname">
                ({{ userInfo.nickname }})
              </span>
            </div>
          </el-descriptions-item>

          <el-descriptions-item label="邮箱">
            <span class="email">{{ userInfo?.email || '-' }}</span>
          </el-descriptions-item>

          <el-descriptions-item label="登录状态">
            <el-tag :type="isLoggedIn ? 'success' : 'danger'">
              {{ isLoggedIn ? '已登录' : '未登录' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Refresh, CopyDocument } from '@element-plus/icons-vue'
  import { useUserStore } from '@/store/modules/user'
  import type { UserInfo } from '@/types/store'

  // 响应式数据
  const loading = ref(false)
  const error = ref<string | null>(null)
  const userStore = useUserStore()

  // 计算属性
  const isLoggedIn = computed(() => {
    try {
      return userStore.isLogin
    } catch (error) {
      console.error('获取登录状态失败:', error)
      return false
    }
  })

  const userInfo = computed(() => {
    try {
      return userStore.getUserInfo as UserInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  })

  // 刷新用户信息
  const refreshUserInfo = async () => {
    loading.value = true
    error.value = null
    try {
      // 这里可以调用API重新获取用户信息
      // 目前从store中获取最新信息
      await new Promise(resolve => setTimeout(resolve, 500)) // 模拟API调用

      // 验证用户信息是否有效
      if (!userStore.isLogin) {
        throw new Error('用户未登录')
      }

      ElMessage.success('用户信息已刷新')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '刷新用户信息失败'
      console.error('刷新用户信息失败:', err)
      error.value = errorMessage
      ElMessage.error(errorMessage)
    } finally {
      loading.value = false
    }
  }

  // 复制到剪贴板
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      ElMessage.success(`${label}已复制到剪贴板`)
    } catch {
      ElMessage.error('复制失败')
    }
  }

  // 初始化
  onMounted(() => {
    // 组件挂载时不需要特殊操作，数据通过计算属性自动获取
  })
</script>

<style scoped>
  .user-info-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .user-info-content {
    min-height: 200px;
  }

  .error-state {
    padding: 20px;
    text-align: center;
  }

  .not-logged-in {
    padding: 20px;
    text-align: center;
  }

  .user-id-display {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .user-id {
    font-family: 'Courier New', monospace;
    background-color: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
    color: #606266;
  }

  .username-display {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .username {
    font-weight: 500;
    color: #303133;
  }

  .nickname {
    color: #909399;
    font-size: 13px;
  }

  .email {
    color: #606266;
  }
</style>
