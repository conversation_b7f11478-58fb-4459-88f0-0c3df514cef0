<template>
  <el-dialog v-model="dialogVisible" title="回调配置" width="600px" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" v-loading="loading">
      <el-form-item label="回调URL" prop="callback_url">
        <el-input
          v-model="form.callback_url"
          placeholder="请输入回调URL，如：https://your-domain.com/callback"
        />
      </el-form-item>

      <el-form-item label="回调密钥" prop="callback_secret">
        <div class="secret-input-group">
          <el-input
            v-model="form.callback_secret"
            type="password"
            placeholder="可选，用于验证回调请求的安全性"
            show-password
            class="secret-input"
          />
          <el-button type="primary" @click="generateSecret" :icon="Refresh" title="生成随机密钥">
            生成
          </el-button>
        </div>
        <div class="form-tip">
          <p><strong>密钥用途：</strong>用于生成HMAC-SHA256签名，验证回调请求的真实性</p>
          <p><strong>获取方式：</strong></p>
          <ul>
            <li>点击"生成"按钮自动生成安全密钥</li>
            <li>使用 <code>openssl rand -hex 32</code> 命令生成</li>
            <li>使用密码管理器生成强密钥</li>
            <li>自定义密钥（建议至少32位字符）</li>
          </ul>
          <p><strong>安全建议：</strong>生产环境强烈建议设置密钥，并定期更换</p>
        </div>
      </el-form-item>

      <el-form-item label="启用状态" prop="enabled">
        <el-switch v-model="form.enabled" active-text="启用" inactive-text="禁用" />
      </el-form-item>

      <el-form-item label="重试次数" prop="retry_count">
        <el-input-number v-model="form.retry_count" :min="0" :max="10" controls-position="right" />
        <div class="form-tip">失败时的最大重试次数（0-10次）</div>
      </el-form-item>

      <el-form-item label="超时时间" prop="timeout_seconds">
        <el-input-number
          v-model="form.timeout_seconds"
          :min="5"
          :max="60"
          controls-position="right"
        />
        <div class="form-tip">回调请求超时时间（5-60秒）</div>
      </el-form-item>

      <el-form-item label="订阅事件" prop="subscribed_events">
        <div class="event-selection">
          <div class="form-tip" style="margin-bottom: 10px">
            <strong>建议全选：</strong>选择需要接收回调通知的事件类型
          </div>
          <el-checkbox-group v-model="form.subscribed_events" class="event-checkboxes">
            <el-checkbox label="order_status_changed">
              <div class="checkbox-content">
                <span class="checkbox-title">订单状态变更</span>
                <span class="checkbox-desc">订单状态发生变化时通知</span>
              </div>
            </el-checkbox>
            <el-checkbox label="billing_updated">
              <div class="checkbox-content">
                <span class="checkbox-title">计费更新</span>
                <span class="checkbox-desc">订单计费信息更新时通知</span>
              </div>
            </el-checkbox>
            <el-checkbox label="ticket_replied">
              <div class="checkbox-content">
                <span class="checkbox-title">工单回复</span>
                <span class="checkbox-desc">客服工单有新回复时通知</span>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="info" @click="handleTest" :loading="testing">测试回调</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存配置</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Refresh } from '@element-plus/icons-vue'
  import type { FormInstance, FormRules } from 'element-plus'
  import { CallbackService } from '@/api'
  import type { CallbackConfig, UpdateCallbackConfigRequest } from '@/api'
  import { ErrorHandler } from '@/utils/errorHandler'
  import { generateSecureSecret, validateSecretStrength } from '@/utils/secretGenerator'

  interface Props {
    visible: boolean
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'success'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const formRef = ref<FormInstance>()
  const loading = ref(false)
  const saving = ref(false)
  const testing = ref(false)

  // 表单数据
  const form = reactive<UpdateCallbackConfigRequest>({
    callback_url: '',
    callback_secret: '',
    enabled: true,
    retry_count: 3,
    timeout_seconds: 30,
    subscribed_events: ['order_status_changed', 'billing_updated', 'ticket_replied'] // 默认全选
  })

  // 表单验证规则
  const rules: FormRules = {
    callback_url: [
      { required: true, message: '请输入回调URL', trigger: 'blur' },
      {
        pattern: /^https?:\/\/.+/,
        message: '请输入有效的HTTP或HTTPS URL',
        trigger: 'blur'
      }
    ],
    callback_secret: [
      { required: true, message: '请输入回调密钥', trigger: 'blur' },
      { min: 16, message: '密钥长度至少16位，建议32位以上', trigger: 'blur' }
    ],
    retry_count: [{ required: true, message: '请设置重试次数', trigger: 'blur' }],
    timeout_seconds: [{ required: true, message: '请设置超时时间', trigger: 'blur' }],
    subscribed_events: [
      {
        type: 'array',
        min: 1,
        message: '请至少选择一个订阅事件',
        trigger: 'change'
      }
    ]
  }

  // 监听对话框显示，加载配置
  watch(dialogVisible, async (visible) => {
    if (visible) {
      await loadConfig()
    }
  })

  // 加载回调配置
  const loadConfig = async () => {
    loading.value = true
    try {
      const response = await CallbackService.getCallbackConfig()
      if (response.success && response.data) {
        const config = response.data
        Object.assign(form, {
          callback_url: config.callback_url,
          callback_secret: config.callback_secret || '',
          enabled: config.enabled,
          retry_count: config.retry_count,
          timeout_seconds: config.timeout_seconds,
          subscribed_events: config.subscribed_events
        })
      }
    } catch (error) {
      // 如果是首次配置，可能没有配置数据，这是正常的
      console.log('加载回调配置:', error)
    } finally {
      loading.value = false
    }
  }

  // 关闭对话框
  const handleClose = () => {
    dialogVisible.value = false
    // 重置表单
    formRef.value?.resetFields()
  }

  // 保存配置
  const handleSave = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      saving.value = true

      const response = await CallbackService.updateCallbackConfig(form)
      if (response.success) {
        ElMessage.success('回调配置保存成功')
        emit('success')
        handleClose()
      } else {
        throw new Error(response.message || '保存失败')
      }
    } catch (error) {
      if (error !== false) {
        // 表单验证失败时返回false
        ErrorHandler.handleApiError(error)
      }
    } finally {
      saving.value = false
    }
  }

  // 生成随机密钥
  const generateSecret = () => {
    try {
      const secret = generateSecureSecret(32)
      const strength = validateSecretStrength(secret)

      form.callback_secret = secret
      ElMessage.success(`已生成${strength.level === 'very_strong' ? '高强度' : ''}安全密钥`)
    } catch (error) {
      ElMessage.error('生成密钥失败：' + (error as Error).message)
    }
  }

  // 测试回调
  const handleTest = async () => {
    if (!form.callback_url) {
      ElMessage.warning('请先输入回调URL')
      return
    }

    try {
      await ElMessageBox.confirm(
        `将向 ${form.callback_url} 发送测试回调，确定继续吗？`,
        '确认测试',
        {
          confirmButtonText: '发送测试',
          cancelButtonText: '取消',
          type: 'info'
        }
      )

      testing.value = true
      const response = await CallbackService.testCallback()

      if (response.success) {
        ElMessage.success('测试回调已发送，请检查您的服务器日志')
      } else {
        throw new Error(response.message || '测试失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        ErrorHandler.handleApiError(error)
      }
    } finally {
      testing.value = false
    }
  }
</script>

<style scoped>
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    line-height: 1.5;
  }

  .form-tip p {
    margin: 8px 0;
  }

  .form-tip ul {
    margin: 5px 0;
    padding-left: 20px;
  }

  .form-tip li {
    margin: 3px 0;
  }

  .form-tip code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
  }

  .secret-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .secret-input {
    flex: 1;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  .event-selection {
    width: 100%;
  }

  .event-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .event-checkboxes .el-checkbox {
    margin-right: 0;
    align-items: flex-start;
  }

  .checkbox-content {
    display: flex;
    flex-direction: column;
    margin-left: 8px;
  }

  .checkbox-title {
    font-weight: 500;
    color: #303133;
    font-size: 14px;
  }

  .checkbox-desc {
    font-size: 12px;
    color: #909399;
    margin-top: 2px;
  }
</style>
