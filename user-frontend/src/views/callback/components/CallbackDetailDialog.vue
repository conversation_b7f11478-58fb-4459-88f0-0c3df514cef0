<template>
  <el-dialog v-model="dialogVisible" title="回调转发详情" width="80%" :before-close="handleClose">
    <div v-if="callbackRecord" class="callback-detail">
      <!-- 基本信息 -->
      <el-card class="detail-card">
        <template #header>
          <span>基本信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">
            <span class="record-id">{{ callbackRecord.id }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="事件类型">
            {{ getEventTypeName(callbackRecord.event_type) }}
          </el-descriptions-item>
          <el-descriptions-item label="运单号">
            <span class="tracking-no">{{ callbackRecord.tracking_no || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="客户订单号">
            <span class="order-no">{{ callbackRecord.customer_order_no || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="回调地址">
            <span class="callback-url">{{ callbackRecord.callback_url || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="转发状态">
            <el-tag :type="getStatusType(callbackRecord.status)">
              {{ getStatusName(callbackRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="HTTP状态码">
            <el-tag
              v-if="callbackRecord.http_status"
              :type="getHttpStatusType(callbackRecord.http_status)"
            >
              {{ callbackRecord.http_status }}
            </el-tag>
            <span v-else class="no-data">-</span>
          </el-descriptions-item>
          <el-descriptions-item label="重试次数">
            <span :class="{ 'retry-warning': callbackRecord.retry_count > 0 }">
              {{ callbackRecord.retry_count }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="转发时间">
            {{ formatDate(callbackRecord.request_at || callbackRecord.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="响应时间" v-if="callbackRecord.response_at">
            {{ formatDate(callbackRecord.response_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 转发错误信息 -->
      <el-card v-if="callbackRecord.error_message" class="detail-card">
        <template #header>
          <span>转发错误信息</span>
        </template>
        <div class="error-section">
          <el-alert
            :title="callbackRecord.error_message"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </el-card>

      <!-- 发送数据 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <span>发送数据</span>
            <el-button type="link" @click="copyRequestData">
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
          </div>
        </template>
        <el-input
          v-model="formattedRequestData"
          type="textarea"
          :rows="10"
          readonly
          class="request-data-textarea"
        />
      </el-card>

      <!-- 响应数据 -->
      <el-card v-if="callbackRecord.response_data" class="detail-card">
        <template #header>
          <div class="card-header">
            <span>响应数据</span>
            <el-button type="link" @click="copyResponseData">
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
          </div>
        </template>
        <el-input
          v-model="formattedResponseData"
          type="textarea"
          :rows="8"
          readonly
          class="response-data-textarea"
        />
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { CopyDocument } from '@element-plus/icons-vue'
  import type { CallbackRecord } from '@/api'
  import { CallbackService } from '@/api'
  import { ErrorHandler } from '@/utils/errorHandler'

  interface Props {
    visible: boolean
    callbackRecord: CallbackRecord | null
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  // 格式化的数据
  const formattedRequestData = ref('')
  const formattedResponseData = ref('')

  // 监听回调记录变化，格式化数据
  watch(
    () => props.callbackRecord,
    (record) => {
      if (record) {
        try {
          formattedRequestData.value = JSON.stringify(record.request_data, null, 2)
        } catch {
          formattedRequestData.value = JSON.stringify(record.request_data)
        }

        try {
          if (record.response_data) {
            formattedResponseData.value = JSON.stringify(record.response_data, null, 2)
          } else {
            formattedResponseData.value = ''
          }
        } catch {
          formattedResponseData.value = JSON.stringify(record.response_data)
        }
      }
    },
    { immediate: true }
  )

  // 关闭对话框
  const handleClose = () => {
    dialogVisible.value = false
  }

  // 复制请求数据
  const copyRequestData = async () => {
    try {
      await navigator.clipboard.writeText(formattedRequestData.value)
      ElMessage.success('请求数据已复制到剪贴板')
    } catch {
      ElMessage.error('复制失败')
    }
  }

  // 复制响应数据
  const copyResponseData = async () => {
    try {
      await navigator.clipboard.writeText(formattedResponseData.value)
      ElMessage.success('响应数据已复制到剪贴板')
    } catch {
      ElMessage.error('复制失败')
    }
  }

  // 工具函数
  const getEventTypeName = (type: string) => {
    const nameMap: Record<string, string> = {
      order_status_changed: '订单状态变更',
      billing_updated: '计费更新',
      ticket_replied: '工单回复'
    }
    return nameMap[type] || type
  }

  const getHttpStatusType = (status: number) => {
    if (status >= 200 && status < 300) return 'success'
    if (status >= 400 && status < 500) return 'warning'
    if (status >= 500) return 'danger'
    return 'info'
  }

  const getStatusType = (status: string) => {
    // 🔥 修复：扩展状态类型映射，与列表页保持一致
    const typeMap: Record<string, string> = {
      // 回调转发状态
      success: 'success',
      failed: 'danger',
      pending: 'warning',
      processing: 'warning',

      // 成功状态（绿色）
      delivered: 'success',
      exception_delivered: 'success',
      // 🚨 已删除：settled - 结算功能已废弃
      billed: 'success',
      '3': 'success', // 易达已签收
      '100': 'success', // 云通下单成功
      '301': 'success', // 云通已计费

      // 处理中状态（蓝色/信息色）
      created: 'info',
      submitted: 'info',
      assigned: 'info',
      pending_pickup: 'info',
      awaiting_pickup: 'info',
      picked_up: 'info',
      accepted: 'info',
      collecting: 'info',
      in_transit: 'info',
      delivering: 'info',
      weight_updated: 'info',
      forwarded: 'info',
      '1': 'info', // 易达待取件
      '2': 'info', // 易达运输中
      '11': 'info', // 易达已取件
      '102': 'info', // 云通分配网点
      '103': 'info', // 云通分配快递员
      '104': 'info', // 云通已取件
      '208': 'info', // 云通重量更新
      '501': 'info', // 云通已转寄

      // 警告状态（橙色）
      exception: 'warning',
      '4': 'warning', // 易达异常
      '6': 'warning', // 易达异常
      '500': 'warning', // 云通异常
      '401': 'warning', // 云通工单回复

      // 失败状态（红色）
      cancelled: 'danger',
      pickup_failed: 'danger',
      returned: 'danger',
      voided: 'danger',
      '5': 'danger', // 易达已取消
      '10': 'danger', // 易达已取消
      '400': 'danger', // 云通下单失败
      '203': 'danger', // 云通已取消
      '204': 'danger', // 云通取件失败
      '205': 'danger' // 云通已作废
    }
    return typeMap[status] || 'info'
  }

  const getStatusName = (status: string) => {
    // 🔥 修复：扩展状态名称映射，与列表页保持一致
    const nameMap: Record<string, string> = {
      // 回调转发状态
      success: '成功',
      failed: '失败',
      pending: '处理中',
      processing: '处理中',

      // 订单状态
      created: '已下单',
      submitted: '已提交',
      assigned: '已分配',
      pending_pickup: '待取件',
      awaiting_pickup: '待取件',
      picked_up: '已取件',
      pickup_failed: '取件失败',
      accepted: '已接受',
      collecting: '收件中',
      in_transit: '运输中',
      delivering: '派送中',
      delivered: '已签收',
      exception_delivered: '异常签收',
      exception: '异常',
      cancelled: '已取消',
      returned: '已退回',
      forwarded: '已转寄',
      // 🚨 已删除：settled - 结算功能已废弃
      weight_updated: '重量更新',
      billed: '已计费',
      voided: '已作废',

      // 易达状态
      '1': '待取件',
      '2': '运输中',
      '3': '已签收',
      '4': '异常',
      '5': '已取消',
      '6': '异常',
      '10': '已取消',
      '11': '已取件',

      // 云通状态
      '100': '下单成功',
      '400': '下单失败',
      '102': '分配网点',
      '103': '分配快递员',
      '104': '已取件',
      '301': '已计费',
      '208': '重量更新',
      '203': '已取消',
      '204': '取件失败',
      '205': '已作废',
      '500': '异常',
      '501': '已转寄',
      '401': '工单回复'
    }
    return nameMap[status] || '未知状态'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }
</script>

<style scoped>
  .callback-detail {
    max-height: 70vh;
    overflow-y: auto;
  }

  .detail-card {
    margin-bottom: 20px;
  }

  .detail-card:last-child {
    margin-bottom: 0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .record-id {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #606266;
    word-break: break-all;
  }

  .tracking-no {
    font-family: 'Courier New', monospace;
    font-weight: 500;
    color: #409eff;
  }

  .retry-warning {
    color: #e6a23c;
    font-weight: bold;
  }

  .error-section {
    margin-bottom: 15px;
  }

  .error-section:last-child {
    margin-bottom: 0;
  }

  .error-section h4 {
    margin: 0 0 10px 0;
    color: #303133;
    font-size: 14px;
  }

  .request-data-textarea,
  .response-data-textarea {
    font-family: 'Courier New', monospace;
    font-size: 12px;
  }

  .callback-url {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #606266;
    word-break: break-all;
  }

  .no-data {
    color: #c0c4cc;
    font-style: italic;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
</style>
