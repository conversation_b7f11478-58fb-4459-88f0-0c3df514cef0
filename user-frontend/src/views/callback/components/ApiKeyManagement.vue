<template>
  <el-card class="api-key-card">
    <template #header>
      <div class="card-header">
        <span>API密钥管理</span>
        <el-button type="danger" size="small" @click="showResetDialog = true">
          <el-icon><Refresh /></el-icon>
          重置密钥
        </el-button>
      </div>
    </template>

    <div v-loading="loading" class="api-key-content">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="客户端ID">
          <div class="key-display">
            <code class="client-id">{{ apiKeyInfo?.client_id || '-' }}</code>
            <el-button
              type="text"
              size="small"
              @click="copyToClipboard(apiKeyInfo?.client_id || '', 'Client ID')"
              :icon="CopyDocument"
              title="复制Client ID"
            />
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="状态">
          <el-tag type="success"> 活跃 </el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="创建时间">
          {{ formatDate(apiKeyInfo?.created_at) }}
        </el-descriptions-item>

        <el-descriptions-item label="最后使用">
          {{ apiKeyInfo?.last_used_at ? formatDate(apiKeyInfo.last_used_at) : '从未使用' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 重置密钥确认对话框 -->
    <el-dialog
      v-model="showResetDialog"
      title="重置API密钥"
      width="500px"
      :before-close="handleResetDialogClose"
    >
      <div class="reset-warning">
        <el-alert title="重要提醒" type="warning" :closable="false" show-icon>
          <p>重置API密钥将会：</p>
          <ul>
            <li>生成新的Client Secret</li>
            <li>使旧的Client Secret立即失效</li>
            <li>需要更新所有使用该密钥的应用</li>
          </ul>
          <p><strong>请确保您已准备好更新相关配置！</strong></p>
        </el-alert>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showResetDialog = false">取消</el-button>
          <el-button type="danger" @click="handleResetApiKey" :loading="resetting">
            确认重置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新密钥显示对话框 -->
    <el-dialog
      v-model="showNewSecretDialog"
      title="新的API密钥"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="new-secret-content">
        <el-alert title="请立即保存新密钥" type="success" :closable="false" show-icon>
          <p>新的Client Secret已生成，请立即复制并安全保存。</p>
          <p><strong>此密钥只会显示一次，关闭后将无法再次查看！</strong></p>
        </el-alert>

        <div class="secret-display">
          <label>新的Client Secret：</label>
          <div class="secret-value">
            <code class="new-secret">{{ newClientSecret }}</code>
            <el-button
              type="primary"
              @click="copyToClipboard(newClientSecret, 'Client Secret')"
              :icon="CopyDocument"
            >
              复制
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleNewSecretConfirm"> 我已保存，关闭 </el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Refresh, CopyDocument } from '@element-plus/icons-vue'
  import { CallbackService } from '@/api'
  import type { ApiKeyInfo } from '@/api'
  import { ErrorHandler } from '@/utils/errorHandler'
  import { useUserStore } from '@/store/modules/user'

  // 响应式数据
  const loading = ref(false)
  const resetting = ref(false)
  const showResetDialog = ref(false)
  const showNewSecretDialog = ref(false)
  const apiKeyInfo = ref<ApiKeyInfo | null>(null)
  const newClientSecret = ref('')

  // 获取API密钥信息
  const loadApiKeyInfo = async () => {
    loading.value = true
    try {
      const response = await CallbackService.getApiKeyInfo()
      if (response.success && response.data) {
        // 从用户信息中提取API密钥相关信息
        const userData = response.data as any
        apiKeyInfo.value = {
          client_id: userData.client_id || '',
          created_at: userData.created_at || '',
          last_used_at: userData.last_used_at
        }
      } else {
        throw new Error(response.message || '获取API密钥信息失败')
      }
    } catch (error) {
      console.error('获取API密钥信息失败:', error)
      ErrorHandler.handleApiError(error)
      // 重置API密钥信息
      apiKeyInfo.value = null
    } finally {
      loading.value = false
    }
  }

  // 重置API密钥
  const handleResetApiKey = async () => {
    try {
      resetting.value = true
      const response = await CallbackService.resetApiKey()

      if (response.success && response.data) {
        const newSecret = response.data.new_client_secret || response.data.client_secret || ''
        newClientSecret.value = newSecret
        showResetDialog.value = false
        showNewSecretDialog.value = true

        // 重新加载API密钥信息
        await loadApiKeyInfo()

        ElMessage.success('API密钥重置成功')
      } else {
        throw new Error(response.message || '重置失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
    } finally {
      resetting.value = false
    }
  }

  // 复制到剪贴板
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      ElMessage.success(`${label}已复制到剪贴板`)
    } catch {
      ElMessage.error('复制失败')
    }
  }

  // 格式化日期
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 处理重置对话框关闭
  const handleResetDialogClose = () => {
    showResetDialog.value = false
  }

  // 处理新密钥确认
  const handleNewSecretConfirm = () => {
    showNewSecretDialog.value = false
    newClientSecret.value = ''
    // 清除密钥信息，主界面不显示密钥
  }

  // 初始化
  onMounted(() => {
    // 检查用户是否已登录再加载API密钥信息
    const userStore = useUserStore()
    if (userStore.isLogin) {
      loadApiKeyInfo()
    } else {
      console.warn('用户未登录，跳过加载API密钥信息')
    }
  })
</script>

<style scoped>
  .api-key-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .api-key-content {
    min-height: 200px;
  }

  .key-display {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .client-id,
  .client-secret {
    font-family: 'Courier New', monospace;
    background-color: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
    word-break: break-all;
  }



  .reset-warning {
    margin-bottom: 20px;
  }

  .reset-warning ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  .reset-warning li {
    margin: 5px 0;
  }

  .new-secret-content {
    padding: 10px 0;
  }

  .secret-display {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
  }

  .secret-display label {
    display: block;
    margin-bottom: 10px;
    font-weight: bold;
    color: #303133;
  }

  .secret-value {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .new-secret {
    font-family: 'Courier New', monospace;
    background-color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    font-size: 13px;
    word-break: break-all;
    flex: 1;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
</style>
