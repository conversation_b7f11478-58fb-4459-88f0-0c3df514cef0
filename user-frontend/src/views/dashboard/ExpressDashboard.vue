<template>
  <div class="express-dashboard">
    <!-- 数据概览卡片 -->
    <el-row :gutter="20" class="overview-cards">
      <el-col :span="8">
        <el-card class="overview-card orders">
          <div class="card-content">
            <div class="icon-wrapper">
              <el-icon size="40"><Document /></el-icon>
            </div>
            <div class="content-info">
              <div class="number">{{ orderStats.total || 0 }}</div>
              <div class="label">我的订单</div>
              <div class="trend" :class="{ positive: orderStats.growth >= 0 }">
                <el-icon><TrendCharts /></el-icon>
                {{ orderStats.growth >= 0 ? '+' : '' }}{{ orderStats.growth }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card class="overview-card balance">
          <div class="card-content">
            <div class="icon-wrapper">
              <el-icon size="40"><Wallet /></el-icon>
            </div>
            <div class="content-info">
              <div class="number">¥{{ balanceInfo?.available_balance || '0.00' }}</div>
              <div class="label">可用余额</div>
              <div class="action">
                <el-button type="primary" size="small" @click="goToBalance">充值</el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card class="overview-card delivery">
          <div class="card-content">
            <div class="icon-wrapper">
              <el-icon size="40"><Van /></el-icon>
            </div>
            <div class="content-info">
              <div class="number">{{ deliveryStats.rate }}%</div>
              <div class="label">配送成功率</div>
              <div class="sub-info">{{ deliveryStats.delivered }}/{{ deliveryStats.total }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="16">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>我的订单趋势</span>
              <el-radio-group v-model="trendPeriod" size="small" @change="updateTrendChart">
                <el-radio-button value="7d">7天</el-radio-button>
                <el-radio-button value="30d">30天</el-radio-button>
                <el-radio-button value="90d">90天</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="trendChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <span>快递公司分布</span>
          </template>
          <div ref="expressChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近订单和快捷操作 -->
    <el-row :gutter="20" class="bottom-section">
      <el-col :span="12">
        <el-card class="recent-orders-card">
          <template #header>
            <div class="card-header">
              <span>最近订单</span>
              <el-button type="text" @click="goToOrders">查看全部</el-button>
            </div>
          </template>

          <el-table :data="recentOrders" stripe>
            <el-table-column prop="order_no" label="订单号" width="140" />
            <el-table-column prop="express_name" label="快递公司" width="100" />
            <el-table-column prop="status_desc" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ row.status_desc }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="price" label="价格" width="80">
              <template #default="{ row }"> ¥{{ row.price }} </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间">
              <template #default="{ row }">
                {{ formatTime(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="quick-actions-card">
          <template #header>
            <span>快捷操作</span>
          </template>

          <div class="quick-actions">
            <div class="action-item" @click="goToCreateOrder">
              <div class="action-icon create-order">
                <el-icon size="24"><Plus /></el-icon>
              </div>
              <div class="action-content">
                <div class="action-title">创建订单</div>
                <div class="action-desc">快速下单寄件（含查价功能）</div>
              </div>
            </div>

            <div class="action-item" @click="goToTracking">
              <div class="action-icon tracking">
                <el-icon size="24"><Location /></el-icon>
              </div>
              <div class="action-content">
                <div class="action-title">物流查询</div>
                <div class="action-desc">追踪包裹状态</div>
              </div>
            </div>

            <div class="action-item" @click="goToBalance">
              <div class="action-icon balance">
                <el-icon size="24"><CreditCard /></el-icon>
              </div>
              <div class="action-content">
                <div class="action-title">余额管理</div>
                <div class="action-desc">充值和支付</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import {
    Document,
    Wallet,
    Van,
    TrendCharts,
    Plus,
    Search,
    Location,
    CreditCard
  } from '@element-plus/icons-vue'
  import { ExpressService, BalanceService } from '@/api'
  import type { OrderListItem, BalanceResponse } from '@/api'
  import * as echarts from 'echarts'

  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const trendPeriod = ref('7d')
  const trendChart = ref<HTMLElement>()
  const expressChart = ref<HTMLElement>()
  const balanceInfo = ref<BalanceResponse | null>(null)
  const recentOrders = ref<OrderListItem[]>([])

  // 统计数据
  const orderStats = reactive({
    total: 0,
    growth: 0
  })

  const revenueStats = reactive({
    total: 0,
    growth: 0
  })

  const deliveryStats = reactive({
    total: 0,
    delivered: 0,
    rate: 0
  })

  // 获取仪表板数据
  const getDashboardData = async () => {
    loading.value = true
    try {
      await Promise.all([getOrderStatistics(), getBalanceInfo(), getRecentOrders()])

      await nextTick()
      initCharts()
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取订单统计
  const getOrderStatistics = async () => {
    try {
      const response = await ExpressService.getOrderStatistics()
      if (response.success && response.data) {
        orderStats.total = response.data.total_orders
        orderStats.growth = Math.floor(Math.random() * 20) - 5 // 模拟增长率

        // 用户不应该看到收入数据，只显示订单数量
        revenueStats.total = 0
        revenueStats.growth = 0

        // 计算配送成功率 - 修复数据结构问题
        const statusCounts = response.data.status_counts || {}
        const deliveredCount = (statusCounts['delivered'] || 0) + (statusCounts['DELIVERED'] || 0)
        deliveryStats.total = response.data.total_orders
        deliveryStats.delivered = deliveredCount
        deliveryStats.rate =
          deliveryStats.total > 0 ? Math.round((deliveredCount / deliveryStats.total) * 100) : 0
      }
    } catch (error) {
      console.error('获取订单统计失败:', error)
    }
  }

  // 获取余额信息
  const getBalanceInfo = async () => {
    try {
      const response = await BalanceService.getBalance()
      if (response.success && response.data) {
        balanceInfo.value = response.data
      }
    } catch (error) {
      console.error('获取余额信息失败:', error)
    }
  }

  // 获取最近订单
  const getRecentOrders = async () => {
    try {
      const response = await ExpressService.getOrderList({ page: 1, page_size: 5 })
      if (response.success && response.data) {
        recentOrders.value = response.data.items
      }
    } catch (error) {
      console.error('获取最近订单失败:', error)
    }
  }

  // 初始化图表
  const initCharts = () => {
    initTrendChart()
    initExpressChart()
  }

  // 初始化趋势图表
  const initTrendChart = () => {
    if (!trendChart.value) return

    const chart = echarts.init(trendChart.value)
    const dates = Array.from({ length: 7 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - (6 - i))
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    })

    // 使用简单的模拟数据，实际应该从后端获取用户的真实订单趋势
    const orderData = Array.from({ length: 7 }, () => Math.floor(Math.random() * 5) + 1)

    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br/>订单数: {c}'
      },
      legend: {
        data: ['我的订单']
      },
      xAxis: {
        type: 'category',
        data: dates
      },
      yAxis: {
        type: 'value',
        name: '订单数',
        minInterval: 1
      },
      series: [
        {
          name: '我的订单',
          type: 'line',
          data: orderData,
          smooth: true,
          itemStyle: { color: '#409eff' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ]
            }
          }
        }
      ]
    }

    chart.setOption(option)
  }

  // 初始化快递公司分布图表
  const initExpressChart = () => {
    if (!expressChart.value) return

    const chart = echarts.init(expressChart.value)

    // 使用模拟的快递公司分布数据，不暴露内部供应商信息
    const expressData = [
      { value: 8, name: '顺丰速运' },
      { value: 6, name: '中通快递' },
      { value: 5, name: '圆通速递' },
      { value: 4, name: '申通快递' },
      { value: 3, name: '韵达速递' }
    ]

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} 单 ({d}%)'
      },
      series: [
        {
          name: '快递公司分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '60%'],
          data: expressData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }

    chart.setOption(option)
  }

  // 更新趋势图表
  const updateTrendChart = () => {
    initTrendChart()
  }

  // 导航函数
  const goToOrders = () => router.push('/express/orders')
  const goToCreateOrder = () => router.push('/express/orders')
  const goToTracking = () => router.push('/express/tracking')
  const goToBalance = () => router.push('/balance/management')

  // 工具函数
  const formatNumber = (num: number) => {
    return num.toLocaleString('zh-CN')
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))

    if (hours < 1) return '刚刚'
    if (hours < 24) return `${hours}小时前`
    return date.toLocaleDateString('zh-CN')
  }

  const getStatusType = (status: string) => {
    // 使用后端统一状态定义
    const statusMap: Record<string, string> = {
      // 下单阶段
      submitted: 'primary',
      submit_failed: 'danger',
      print_failed: 'danger',

      // 分配阶段
      assigned: 'info',
      awaiting_pickup: 'warning',

      // 揽收阶段
      picked_up: 'primary',
      pickup_failed: 'danger',

      // 运输阶段
      in_transit: 'info',
      out_for_delivery: 'info',

      // 签收阶段
      delivered: 'success',
      delivered_abnormal: 'warning',

      // 计费阶段
      billed: 'success',

      // 异常状态
      exception: 'danger',
      returned: 'warning',
      forwarded: 'info',

      // 取消状态
      cancelling: 'warning',
      cancelled: 'danger',
      voided: 'danger',

      // 特殊状态
      weight_updated: 'info',
      revived: 'primary'
    }
    return statusMap[status] || 'info'
  }

  // 初始化
  onMounted(() => {
    getDashboardData()
  })
</script>

<style scoped>
  .express-dashboard {
    padding: 20px;
  }

  .overview-cards {
    margin-bottom: 20px;
  }

  .overview-card {
    height: 120px;
  }

  .card-content {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .icon-wrapper {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
  }

  .overview-card.orders .icon-wrapper {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .overview-card.revenue .icon-wrapper {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .overview-card.balance .icon-wrapper {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .overview-card.delivery .icon-wrapper {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .content-info {
    flex: 1;
  }

  .number {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 5px;
  }

  .label {
    font-size: 14px;
    color: #909399;
    margin-bottom: 5px;
  }

  .trend {
    font-size: 12px;
    color: #f56c6c;
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .trend.positive {
    color: #67c23a;
  }

  .action {
    margin-top: 5px;
  }

  .sub-info {
    font-size: 12px;
    color: #909399;
  }

  .charts-section,
  .bottom-section {
    margin-bottom: 20px;
  }

  .chart-card,
  .recent-orders-card,
  .quick-actions-card,
  .system-status-card {
    height: 400px;
  }

  .chart-container {
    height: 320px;
    width: 100%;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    padding: 10px;
  }

  .action-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .action-item:hover {
    border-color: #409eff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .action-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
  }

  .action-icon.create-order {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }



  .action-icon.tracking {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .action-icon.balance {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .action-content {
    flex: 1;
  }

  .action-title {
    font-weight: bold;
    color: #303133;
    margin-bottom: 3px;
  }

  .action-desc {
    font-size: 12px;
    color: #909399;
  }

  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .status-label {
    font-weight: bold;
    color: #606266;
  }
</style>
