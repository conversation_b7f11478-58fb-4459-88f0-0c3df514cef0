<template>
  <div class="console">
    <!-- 快递仪表板 -->
    <ExpressDashboard />
  </div>
</template>

<script setup lang="ts">
  import ExpressDashboard from '../ExpressDashboard.vue'
  import { scrollToTop } from '@/utils/utils'
  import { useSettingStore } from '@/store/modules/setting'

  const settingStore = useSettingStore()
  const currentGlopTheme = computed(() => settingStore.systemThemeType)

  // 系统主题风格变化时，刷新页面重写渲染 Echarts
  watch(currentGlopTheme, () => {
    settingStore.reload()
  })

  scrollToTop()
</script>

<style lang="scss" scoped>
  .console {
    padding: 0;
    background: transparent;
  }
</style>
