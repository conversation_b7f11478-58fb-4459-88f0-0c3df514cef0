<template>
  <div class="smart-create-workorder">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <h3>
            <el-icon><EditPen /></el-icon>
            创建工单
          </h3>
          <p class="subtitle">只需提供问题类型和订单信息，系统自动处理其他细节</p>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="smart-form"
        @submit.prevent="handleSubmit"
      >
        <!-- 工单类型选择 -->
        <el-form-item label="问题类型" prop="work_order_type" required>
          <div class="type-selection">
            <el-radio-group v-model="formData.work_order_type" class="type-radio-group">
              <el-radio
                v-for="option in workOrderTypeOptions"
                :key="option.value"
                :label="option.value"
                class="type-radio"
              >
                <div class="type-option">
                  <div class="type-name">{{ option.label }}</div>
                  <div class="type-desc">{{ option.description }}</div>
                  <el-tag :type="getCategoryTagType(option.category)" size="small">
                    {{ option.category }}
                  </el-tag>
                </div>
              </el-radio>
            </el-radio-group>
          </div>
        </el-form-item>

        <!-- 订单信息 -->
        <el-form-item label="订单信息" prop="order_identifier" required>
          <div class="order-input-group">
            <el-input
              v-model="formData.order_no"
              placeholder="请输入客户订单号"
              clearable
              @blur="queryOrderInfo"
              @clear="orderInfo = null"
            >
              <template #prepend>订单号</template>
            </el-input>
            <div class="or-divider">或</div>
            <el-input
              v-model="formData.tracking_no"
              placeholder="请输入运单号"
              clearable
              @blur="queryOrderInfo"
              @clear="orderInfo = null"
            >
              <template #prepend>运单号</template>
            </el-input>
            <el-button
              :loading="orderLoading"
              type="primary"
              @click="queryOrderInfo"
              :disabled="!hasOrderIdentifier"
            >
              查询订单
            </el-button>
          </div>

          <!-- 订单信息显示 -->
          <div v-if="orderInfo" class="order-info-display">
            <el-alert type="success" :closable="false">
              <template #title>
                <div class="order-details">
                  <div><strong>订单号:</strong> {{ orderInfo.order_no }}</div>
                  <div><strong>运单号:</strong> {{ orderInfo.tracking_no }}</div>
                  <div><strong>快递类型:</strong> {{ orderInfo.express_type || '-' }}</div>
                  <div><strong>订单状态:</strong> {{ orderInfo.status_desc || orderInfo.status || '-' }}</div>
                </div>
              </template>
            </el-alert>
          </div>

          <!-- 订单查询错误 -->
          <div v-if="orderError" class="order-error">
            <el-alert type="error" :title="orderError" :closable="false" />
          </div>
        </el-form-item>

        <!-- 问题描述 -->
        <el-form-item label="问题描述" prop="content" required>
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="4"
            placeholder="请详细描述您遇到的问题，以便我们更好地为您处理"
            maxlength="1000"
            show-word-limit
          />
          <div class="content-tips">
            <el-icon><InfoFilled /></el-icon>
            <span>建议包含：具体问题、期望解决方案、联系方式等信息</span>
          </div>
        </el-form-item>

        <!-- 重量异常特殊字段 -->
        <el-form-item v-if="isWeightException" label="反馈重量" prop="feedback_weight">
          <el-input-number
            v-model="formData.feedback_weight"
            :min="0.1"
            :max="1000"
            :precision="2"
            placeholder="请输入实际重量"
            style="width: 200px"
          />
          <span class="unit">kg</span>
          <div class="field-tip">
            <el-icon><QuestionFilled /></el-icon>
            <span>请填写包裹的实际重量，我们将核实重量差异</span>
          </div>
        </el-form-item>

        <!-- 商品价值 -->
        <el-form-item label="商品价值" prop="goods_value">
          <el-input-number
            v-model="formData.goods_value"
            :min="0"
            :max="100000"
            :precision="2"
            placeholder="请输入商品价值"
            style="width: 200px"
          />
          <span class="unit">元</span>
          <div class="field-tip">
            <el-icon><QuestionFilled /></el-icon>
            <span>选填，用于理赔参考</span>
          </div>
        </el-form-item>

        <!-- 附件上传 -->
        <el-form-item label="相关图片">
          <div class="attachment-section">
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="fileList"
              list-type="picture-card"
              accept="image/*"
            >
              <el-icon><Plus /></el-icon>
            </el-upload>
            <div class="upload-tips">
              <el-icon><InfoFilled /></el-icon>
              <span>支持上传相关图片，如包裹照片、重量凭证等（可选）</span>
            </div>
          </div>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <div class="submit-section">
            <el-button
              type="primary"
              size="large"
              :loading="submitting"
              :disabled="!canSubmit"
              @click="handleSubmit"
            >
              <el-icon><Check /></el-icon>
              {{ submitting ? '创建中...' : '创建工单' }}
            </el-button>
            <el-button size="large" @click="resetForm">
              <el-icon><RefreshLeft /></el-icon>
              重置表单
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 帮助信息 -->
    <el-card class="help-card">
      <template #header>
        <h4
          ><el-icon><QuestionFilled /></el-icon> 使用说明</h4
        >
      </template>
      <div class="help-content">
        <div class="help-item">
          <h5>1. 选择问题类型</h5>
          <p>根据您遇到的实际问题选择对应的工单类型，系统会自动匹配最合适的处理流程。</p>
        </div>
        <div class="help-item">
          <h5>2. 提供订单信息</h5>
          <p>输入客户订单号或运单号，系统会自动查询订单信息并识别对应的快递供应商。</p>
        </div>
        <div class="help-item">
          <h5>3. 详细描述问题</h5>
          <p>请尽可能详细地描述您遇到的问题，这有助于客服人员更快地为您解决问题。</p>
        </div>
        <div class="help-item">
          <h5>4. 系统自动处理</h5>
          <p>提交后，系统会自动将工单转发给对应的快递供应商，并为您跟踪处理进度。</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    EditPen,
    InfoFilled,
    QuestionFilled,
    Plus,
    Check,
    RefreshLeft
  } from '@element-plus/icons-vue'
  import { useWorkOrder } from '@/composables/useWorkOrder'

  // 路由
  const router = useRouter()

  // 工单创建逻辑
  const {
    // 响应式状态
    submitting,
    orderLoading,
    formData,
    orderInfo,
    orderError,

    // 计算属性
    isWeightException,
    hasOrderIdentifier,
    canSubmit,

    // 配置数据
    workOrderTypeOptions,
    formRules,

    // 方法
    queryOrderInfo,
    createWorkOrder,
    resetForm
  } = useWorkOrder()

  // 表单引用
  const formRef = ref()

  // 文件列表
  const fileList = ref([])

  /**
   * 获取供应商名称
   */
  const getProviderName = (provider: string): string => {
    const providerMap: Record<string, string> = {
      kuaidi100: '快递100',
      yida: '易达',
      yuntong: '云通'
    }
    return providerMap[provider] || provider
  }

  /**
   * 获取分类标签类型
   */
  const getCategoryTagType = (category: string): string => {
    const typeMap: Record<string, string> = {
      费用争议: 'warning',
      派送服务: 'primary',
      取件服务: 'success',
      物流服务: 'info',
      订单管理: 'danger'
    }
    return typeMap[category] || 'info'
  }

  /**
   * 处理文件变化
   */
  const handleFileChange = (file: any, fileList: any[]) => {
    // 这里可以实现文件上传逻辑
    console.log('文件变化:', file, fileList)
  }

  /**
   * 处理表单提交
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      await formRef.value?.validate()

      // 确认提交
      await ElMessageBox.confirm(
        '确认创建工单吗？系统将自动转发给对应的快递供应商处理。',
        '确认创建',
        {
          confirmButtonText: '确认创建',
          cancelButtonText: '取消',
          type: 'info'
        }
      )

      // 创建工单
      const workOrder = await createWorkOrder()
      if (workOrder) {
        // 跳转到工单详情页
        router.push(`/workorder/detail/${workOrder.id}`)
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交表单失败:', error)
      }
    }
  }
</script>

<style scoped lang="scss">
  .smart-create-workorder {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;

    .form-card {
      margin-bottom: 20px;

      .card-header {
        h3 {
          margin: 0 0 8px 0;
          color: #303133;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .subtitle {
          margin: 0;
          color: #909399;
          font-size: 14px;
        }
      }
    }

    .smart-form {
      .type-selection {
        .type-radio-group {
          width: 100%;

          .type-radio {
            width: 100%;
            margin-right: 0;
            margin-bottom: 12px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 16px;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
              background-color: #f0f9ff;
            }

            &.is-checked {
              border-color: #409eff;
              background-color: #ecf5ff;
            }

            .type-option {
              .type-name {
                font-weight: 600;
                color: #303133;
                margin-bottom: 4px;
              }

              .type-desc {
                color: #606266;
                font-size: 13px;
                margin-bottom: 8px;
              }
            }
          }
        }
      }

      .order-input-group {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: wrap;

        .or-divider {
          color: #909399;
          font-size: 14px;
          padding: 0 8px;
        }
      }

      .order-info-display {
        margin-top: 12px;

        .order-details {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;
          font-size: 14px;
        }
      }

      .order-error {
        margin-top: 12px;
      }

      .content-tips,
      .field-tip {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #909399;
        font-size: 12px;
        margin-top: 8px;
      }

      .unit {
        margin-left: 8px;
        color: #909399;
      }

      .attachment-section {
        .upload-tips {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #909399;
          font-size: 12px;
          margin-top: 8px;
        }
      }

      .submit-section {
        display: flex;
        gap: 16px;
        justify-content: center;
        margin-top: 32px;
      }
    }

    .help-card {
      .help-content {
        .help-item {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          h5 {
            margin: 0 0 8px 0;
            color: #303133;
            font-size: 14px;
          }

          p {
            margin: 0;
            color: #606266;
            font-size: 13px;
            line-height: 1.6;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .smart-create-workorder {
      padding: 12px;

      .order-input-group {
        flex-direction: column;
        align-items: stretch;

        .or-divider {
          text-align: center;
          padding: 8px 0;
        }
      }

      .order-details {
        grid-template-columns: 1fr !important;
      }

      .submit-section {
        flex-direction: column;
      }
    }
  }
</style>
