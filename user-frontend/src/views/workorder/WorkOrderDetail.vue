<template>
  <div class="workorder-detail-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="handleBack" type="text" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <div v-if="workOrder" class="header-info">
        <h2>{{ workOrder.title }}</h2>
        <div class="header-meta">
          <el-tag :type="getStatusType(workOrder.status)">
            {{ getStatusName(workOrder.status) }}
          </el-tag>
          <span class="create-time">{{ formatDateTime(workOrder.created_at) }}</span>
        </div>
      </div>
    </div>

    <!-- 工单详情内容 -->
    <div v-loading="loading" class="detail-section">
      <WorkOrderDetailDialog
        :visible="true"
        :work-order-id="workOrderId"
        :is-page-mode="true"
        @refresh="handleRefresh"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ArrowLeft } from '@element-plus/icons-vue'
  import { useWorkOrderStore } from '@/store/modules/workOrder'
  import { WorkOrderService } from '@/api'
  import type { WorkOrder } from '@/api/model/workOrderModel'
  import { ErrorHandler } from '@/utils/errorHandler'
  import WorkOrderDetailDialog from './components/WorkOrderDetailDialog.vue'

  // 路由
  const route = useRoute()
  const router = useRouter()

  // Store
  const workOrderStore = useWorkOrderStore()

  // 响应式数据
  const loading = ref(false)
  const workOrder = ref<WorkOrder | null>(null)

  // 计算属性
  const workOrderId = computed(() => route.params.id as string)

  // 方法
  const getWorkOrderDetail = async () => {
    if (!workOrderId.value) return

    loading.value = true
    try {
      workOrder.value = await workOrderStore.fetchWorkOrderDetail(workOrderId.value)
    } catch (error) {
      ErrorHandler.handleApiError(error)
      handleBack()
    } finally {
      loading.value = false
    }
  }

  const handleBack = () => {
    router.back()
  }

  const handleRefresh = () => {
    getWorkOrderDetail()
  }

  const getStatusName = (status: number) => {
    return WorkOrderService.formatWorkOrderStatus(status).text
  }

  const getStatusType = (status: number) => {
    return WorkOrderService.formatWorkOrderStatus(status).type
  }

  // 🔥 修复：格式化日期时间 - 明确指定北京时区
  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai', // 🔥 修复：明确指定北京时区
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // 生命周期
  onMounted(() => {
    getWorkOrderDetail()
  })
</script>

<style scoped lang="scss">
  .workorder-detail-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;

    .page-header {
      margin-bottom: 24px;

      .back-button {
        margin-bottom: 12px;
        padding: 0;

        .el-icon {
          margin-right: 4px;
        }
      }

      .header-info {
        h2 {
          margin: 0 0 8px 0;
          color: #303133;
          font-size: 24px;
          font-weight: 600;
        }

        .header-meta {
          display: flex;
          align-items: center;
          gap: 12px;

          .create-time {
            color: #909399;
            font-size: 14px;
          }
        }
      }
    }

    .detail-section {
      :deep(.el-dialog__header) {
        display: none;
      }

      :deep(.el-dialog__body) {
        padding: 0;
      }

      :deep(.el-dialog__footer) {
        display: none;
      }

      :deep(.workorder-detail) {
        .info-card,
        .content-card,
        .attachment-card,
        .reply-card {
          margin-bottom: 24px;
        }
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .workorder-detail-container {
      padding: 12px;

      .page-header {
        .header-info {
          h2 {
            font-size: 20px;
          }

          .header-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
        }
      }

      .detail-section {
        :deep(.workorder-detail) {
          .info-card,
          .content-card,
          .attachment-card,
          .reply-card {
            margin-bottom: 16px;
          }

          // 移动端优化描述列表
          :deep(.el-descriptions) {
            .el-descriptions__body {
              .el-descriptions__table {
                .el-descriptions__cell {
                  padding: 8px 12px;
                }
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .workorder-detail-container {
      padding: 8px;

      .detail-section {
        :deep(.workorder-detail) {
          // 在小屏幕上使用单列布局
          :deep(.el-descriptions) {
            .el-descriptions__body {
              .el-descriptions__table {
                .el-descriptions__row {
                  display: block;

                  .el-descriptions__cell {
                    display: block;
                    width: 100% !important;

                    &.el-descriptions__label {
                      font-weight: 600;
                      padding-bottom: 4px;
                    }

                    &.el-descriptions__content {
                      padding-top: 0;
                      padding-bottom: 12px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
</style>
