<template>
  <el-dialog
    v-model="dialogVisible"
    title="工单详情"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-loading="loading" class="workorder-detail">
      <template v-if="workOrder">
        <!-- 工单基本信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <el-tag :type="getStatusType(workOrder.status)">
                {{ getStatusName(workOrder.status) }}
              </el-tag>
            </div>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="工单标题">
              {{ workOrder.title }}
            </el-descriptions-item>
            <el-descriptions-item label="工单类型">
              <el-tag size="small">{{ getTypeName(workOrder.work_order_type) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityType(workOrder.priority)" size="small">
                {{ getPriorityName(workOrder.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="供应商">
              {{ getProviderName(workOrder.provider) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单号">
              {{ workOrder.order_no || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="运单号">
              {{ workOrder.tracking_no || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDateTime(workOrder.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDateTime(workOrder.updated_at) }}
            </el-descriptions-item>
            <el-descriptions-item v-if="workOrder.completed_at" label="完成时间">
              {{ formatDateTime(workOrder.completed_at) }}
            </el-descriptions-item>
            <el-descriptions-item v-if="workOrder.feedback_weight" label="反馈重量">
              {{ workOrder.feedback_weight }} kg
            </el-descriptions-item>
            <el-descriptions-item v-if="workOrder.goods_value" label="货物价值">
              ¥{{ workOrder.goods_value }}
            </el-descriptions-item>
            <el-descriptions-item v-if="workOrder.overweight_amount" label="超重金额">
              ¥{{ workOrder.overweight_amount }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 工单内容 -->
        <el-card class="content-card" shadow="never">
          <template #header>
            <span>工单内容</span>
          </template>
          <div class="content-text">
            {{ workOrder.content }}
          </div>
        </el-card>

        <!-- 工单附件 -->
        <el-card v-if="workOrder.attachments && workOrder.attachments.length > 0" class="attachment-card" shadow="never">
          <template #header>
            <span>工单附件</span>
          </template>
          <div class="attachment-list">
            <div
              v-for="attachment in workOrder.attachments"
              :key="attachment.id"
              class="attachment-item"
            >
              <el-link :href="attachment.file_url" target="_blank" type="primary">
                <el-icon><Document /></el-icon>
                {{ attachment.file_name }}
              </el-link>
              <span class="file-size">{{ formatFileSize(attachment.file_size) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 回复记录 -->
        <el-card class="reply-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>回复记录</span>
              <el-button
                v-if="canReply"
                type="primary"
                size="small"
                @click="showReplyForm = true"
              >
                <el-icon><ChatDotRound /></el-icon>
                添加回复
              </el-button>
            </div>
          </template>

          <!-- 回复表单 -->
          <div v-if="showReplyForm" class="reply-form">
            <el-form ref="replyFormRef" :model="replyForm" :rules="replyRules">
              <el-form-item prop="content">
                <el-input
                  v-model="replyForm.content"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入回复内容..."
                  maxlength="1000"
                  show-word-limit
                />
              </el-form-item>
              
              <!-- 附件上传 -->
              <el-form-item label="附件">
                <el-upload
                  ref="uploadRef"
                  :file-list="replyForm.fileList"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  multiple
                  drag
                >
                  <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持jpg/png/pdf/doc/docx文件，且不超过10MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" :loading="replyLoading" @click="handleSubmitReply">
                  提交回复
                </el-button>
                <el-button @click="handleCancelReply">取消</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 回复列表 -->
          <div class="reply-list">
            <div
              v-for="reply in workOrder.replies"
              :key="reply.id"
              class="reply-item"
            >
              <div class="reply-header">
                <div class="reply-info">
                  <el-tag :type="getReplyTypeColor(reply.reply_type)" size="small">
                    {{ getReplyTypeName(reply.reply_type) }}
                  </el-tag>
                  <span v-if="reply.committer" class="committer">{{ reply.committer }}</span>
                  <span class="reply-time">{{ formatDateTime(reply.created_at) }}</span>
                </div>
              </div>
              <div class="reply-content">
                {{ reply.content }}
              </div>
              <!-- 回复附件 -->
              <div v-if="reply.attachments && reply.attachments.length > 0" class="reply-attachments">
                <div
                  v-for="attachment in reply.attachments"
                  :key="attachment.id"
                  class="attachment-item"
                >
                  <el-link :href="attachment.file_url" target="_blank" type="primary">
                    <el-icon><Document /></el-icon>
                    {{ attachment.file_name }}
                  </el-link>
                </div>
              </div>
            </div>
            
            <div v-if="!workOrder.replies || workOrder.replies.length === 0" class="no-replies">
              暂无回复记录
            </div>
          </div>
        </el-card>
      </template>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          v-if="workOrder && canDelete.canDelete"
          type="danger"
          :loading="deleteLoading"
          @click="handleDelete"
        >
          删除工单
        </el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadFile } from 'element-plus'
import { Document, ChatDotRound, UploadFilled } from '@element-plus/icons-vue'
import { WorkOrderService } from '@/api'
import type { WorkOrder, ReplyWorkOrderRequest } from '@/api/model/workOrderModel'
import { ErrorHandler } from '@/utils/errorHandler'

// Props
interface Props {
  visible: boolean
  workOrderId: string
  isPageMode?: boolean // 是否为页面模式（非对话框模式）
}

const props = withDefaults(defineProps<Props>(), {
  isPageMode: false
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const replyLoading = ref(false)
const deleteLoading = ref(false)
const showReplyForm = ref(false)
const workOrder = ref<WorkOrder | null>(null)
const replyFormRef = ref<FormInstance>()
const uploadRef = ref()

// 回复表单
const replyForm = reactive({
  content: '',
  fileList: [] as UploadFile[]
})

// 表单验证规则
const replyRules: FormRules = {
  content: [
    { required: true, message: '请输入回复内容', trigger: 'blur' },
    { min: 10, message: '回复内容至少10个字符', trigger: 'blur' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const canReply = computed(() => {
  return workOrder.value ? WorkOrderService.canReplyWorkOrder(workOrder.value) : false
})

const canDelete = computed(() => {
  return workOrder.value ? WorkOrderService.canDeleteWorkOrder(workOrder.value) : { canDelete: false }
})

// 监听器
watch(() => props.workOrderId, (newId) => {
  if (newId && props.visible) {
    getWorkOrderDetail()
  }
}, { immediate: true })

watch(() => props.visible, (visible) => {
  if (visible && props.workOrderId) {
    getWorkOrderDetail()
  } else {
    resetForm()
  }
})

// 方法
const getWorkOrderDetail = async () => {
  if (!props.workOrderId) return
  
  loading.value = true
  try {
    workOrder.value = await WorkOrderService.getWorkOrderDetail(props.workOrderId)
  } catch (error) {
    ErrorHandler.handleApiError(error)
    handleClose()
  } finally {
    loading.value = false
  }
}

const handleSubmitReply = async () => {
  if (!replyFormRef.value || !workOrder.value) return

  try {
    await replyFormRef.value.validate()
  } catch {
    return
  }

  replyLoading.value = true
  try {
    // 上传附件
    let attachmentUrls: string[] = []
    if (replyForm.fileList.length > 0) {
      const files = replyForm.fileList.map(file => file.raw!).filter(Boolean)
      const uploadResults = await WorkOrderService.uploadMultipleAttachments(files)
      attachmentUrls = uploadResults.map(result => result.file_url)
    }

    // 提交回复
    const params: ReplyWorkOrderRequest = {
      content: replyForm.content,
      attachment_urls: attachmentUrls
    }

    await WorkOrderService.replyWorkOrder(workOrder.value.id, params)
    
    ElMessage.success('回复提交成功')
    showReplyForm.value = false
    resetForm()
    getWorkOrderDetail()
    emit('refresh')
  } catch (error) {
    ErrorHandler.handleApiError(error)
  } finally {
    replyLoading.value = false
  }
}

const handleCancelReply = () => {
  showReplyForm.value = false
  resetForm()
}

const handleFileChange = (file: UploadFile) => {
  // 文件大小检查
  if (file.size && file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }
  
  // 文件类型检查
  const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  if (file.raw && !allowedTypes.includes(file.raw.type)) {
    ElMessage.error('只支持jpg/png/pdf/doc/docx格式的文件')
    return false
  }
}

const handleFileRemove = (file: UploadFile) => {
  const index = replyForm.fileList.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    replyForm.fileList.splice(index, 1)
  }
}

const resetForm = () => {
  replyForm.content = ''
  replyForm.fileList = []
  showReplyForm.value = false
  replyFormRef.value?.resetFields()
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleDelete = async () => {
  if (!workOrder.value) return

  // 检查是否可以删除
  const deleteCheck = WorkOrderService.canDeleteWorkOrder(workOrder.value)
  if (!deleteCheck.canDelete) {
    ElMessage.error(deleteCheck.reason || '无法删除该工单')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除工单"${workOrder.value.title}"吗？删除后无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 执行删除
    deleteLoading.value = true
    const response = await WorkOrderService.deleteWorkOrder(workOrder.value.id)

    if (response.success) {
      ElMessage.success('工单删除成功')
      emit('refresh')
      handleClose()
    } else {
      throw new Error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') { // 用户取消操作不显示错误
      ErrorHandler.handleApiError(error)
    }
  } finally {
    deleteLoading.value = false
  }
}

// 工具方法
const getStatusName = (status: number) => {
  return WorkOrderService.formatWorkOrderStatus(status).text
}

const getStatusType = (status: number) => {
  return WorkOrderService.formatWorkOrderStatus(status).type
}

const getTypeName = (type: number) => {
  return WorkOrderService.getWorkOrderTypeName(type)
}

const getPriorityName = (priority: number) => {
  return WorkOrderService.formatWorkOrderPriority(priority).text
}

const getPriorityType = (priority: number) => {
  return WorkOrderService.formatWorkOrderPriority(priority).type
}

const getProviderName = (provider: string) => {
  const providerMap: Record<string, string> = {
    kuaidi100: '快递100',
    yida: '易达',
    yuntong: '云通'
  }
  return providerMap[provider] || provider
}

const getReplyTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '用户',
    2: '供应商',
    3: '系统'
  }
  return typeMap[type] || '未知'
}

const getReplyTypeColor = (type: number) => {
  const colorMap: Record<number, string> = {
    1: 'primary',
    2: 'success',
    3: 'info'
  }
  return colorMap[type] || 'default'
}

// 🔥 修复：格式化日期时间 - 明确指定北京时区
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai', // 🔥 修复：明确指定北京时区
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const formatFileSize = (size?: number) => {
  if (!size) return '-'
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}
</script>

<style scoped lang="scss">
.workorder-detail {
  .info-card,
  .content-card,
  .attachment-card,
  .reply-card {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .content-text {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 4px;
    line-height: 1.6;
    white-space: pre-wrap;
  }

  .attachment-list {
    .attachment-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #ebeef5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .file-size {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .reply-form {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .reply-list {
    .reply-item {
      margin-bottom: 16px;
      padding: 16px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .reply-header {
        margin-bottom: 8px;
        
        .reply-info {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .committer {
            font-weight: 500;
            color: #303133;
          }
          
          .reply-time {
            color: #909399;
            font-size: 12px;
          }
        }
      }
      
      .reply-content {
        margin-bottom: 8px;
        line-height: 1.6;
        white-space: pre-wrap;
      }
      
      .reply-attachments {
        .attachment-item {
          display: inline-block;
          margin-right: 16px;
          margin-bottom: 8px;
        }
      }
    }
    
    .no-replies {
      text-align: center;
      color: #909399;
      padding: 40px 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
