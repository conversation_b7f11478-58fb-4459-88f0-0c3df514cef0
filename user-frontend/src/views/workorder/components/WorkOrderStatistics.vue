<template>
  <div class="workorder-statistics">
    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card total">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics?.total || 0 }}</div>
                <div class="stat-label">总工单数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics?.pending || 0 }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card processing">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics?.processing || 0 }}</div>
                <div class="stat-label">处理中</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stat-card completed">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics?.completed || 0 }}</div>
                <div class="stat-label">已完结</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 工单趋势图 -->
        <el-col :span="16">
          <el-card>
            <template #header>
              <div class="chart-header">
                <span>工单趋势</span>
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="handleDateRangeChange"
                  size="small"
                />
              </div>
            </template>
            <div ref="trendChartRef" class="chart-container" />
          </el-card>
        </el-col>

        <!-- 工单类型分布 -->
        <el-col :span="8">
          <el-card>
            <template #header>
              <span>工单类型分布</span>
            </template>
            <div ref="typeChartRef" class="chart-container" />
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <span>快速操作</span>
        </template>
        <div class="action-buttons">
          <el-button type="primary" @click="$emit('create-workorder')">
            <el-icon><Plus /></el-icon>
            创建工单
          </el-button>
          <el-button @click="$emit('view-all')">
            <el-icon><List /></el-icon>
            查看全部
          </el-button>
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, nextTick, watch } from 'vue'
  import { Document, Clock, Loading, Check, Plus, List, Refresh } from '@element-plus/icons-vue'
  import * as echarts from 'echarts'
  import { useWorkOrderStore } from '@/store/modules/workOrder'
  import type {
    WorkOrderStatistics,
    WorkOrderTrend,
    WorkOrderTypeStatistics
  } from '@/api/model/workOrderModel'
  import { ErrorHandler } from '@/utils/errorHandler'

  // Emits
  const emit = defineEmits<{
    'create-workorder': []
    'view-all': []
  }>()

  // Store
  const workOrderStore = useWorkOrderStore()

  // 响应式数据
  const trendChartRef = ref<HTMLElement>()
  const typeChartRef = ref<HTMLElement>()
  const dateRange = ref<[string, string] | null>(null)
  const statistics = ref<WorkOrderStatistics | null>(null)
  const trend = ref<WorkOrderTrend[]>([])
  const typeStatistics = ref<WorkOrderTypeStatistics[]>([])

  let trendChart: echarts.ECharts | null = null
  let typeChart: echarts.ECharts | null = null

  // 监听器
  watch(
    () => workOrderStore.statistics,
    (newStats) => {
      statistics.value = newStats
    }
  )

  watch(
    () => workOrderStore.trend,
    (newTrend) => {
      trend.value = newTrend
      updateTrendChart()
    }
  )

  watch(
    () => workOrderStore.typeStatistics,
    (newTypeStats) => {
      typeStatistics.value = newTypeStats
      updateTypeChart()
    }
  )

  // 方法
  const initCharts = async () => {
    await nextTick()

    if (trendChartRef.value) {
      trendChart = echarts.init(trendChartRef.value)
      updateTrendChart()
    }

    if (typeChartRef.value) {
      typeChart = echarts.init(typeChartRef.value)
      updateTypeChart()
    }

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
  }

  const updateTrendChart = () => {
    if (!trendChart || !trend.value.length) return

    const dates = trend.value.map((item) => item.date)
    const created = trend.value.map((item) => item.created)
    const completed = trend.value.map((item) => item.completed)

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['创建', '完结']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '创建',
          type: 'line',
          stack: 'Total',
          smooth: true,
          data: created,
          itemStyle: {
            color: '#409EFF'
          }
        },
        {
          name: '完结',
          type: 'line',
          stack: 'Total',
          smooth: true,
          data: completed,
          itemStyle: {
            color: '#67C23A'
          }
        }
      ]
    }

    trendChart.setOption(option)
  }

  const updateTypeChart = () => {
    if (!typeChart || !typeStatistics.value.length) return

    const data = typeStatistics.value.map((item) => ({
      name: item.type_name,
      value: item.count
    }))

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: data.map((item) => item.name)
      },
      series: [
        {
          name: '工单类型',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        }
      ]
    }

    typeChart.setOption(option)
  }

  const handleResize = () => {
    trendChart?.resize()
    typeChart?.resize()
  }

  const handleDateRangeChange = (dates: [string, string] | null) => {
    const params = dates
      ? {
          start_date: dates[0],
          end_date: dates[1]
        }
      : undefined

    loadData(params)
  }

  const handleRefresh = () => {
    const params = dateRange.value
      ? {
          start_date: dateRange.value[0],
          end_date: dateRange.value[1]
        }
      : undefined

    loadData(params, true)
  }

  const loadData = async (
    params?: {
      start_date?: string
      end_date?: string
    },
    forceRefresh = false
  ) => {
    try {
      // 并行加载所有数据
      await Promise.all([
        workOrderStore.fetchStatistics(params, forceRefresh),
        workOrderStore.fetchTrend(params, forceRefresh),
        workOrderStore.fetchTypeStatistics(params, forceRefresh)
      ])
    } catch (error) {
      ErrorHandler.handleApiError(error)
    }
  }

  // 生命周期
  onMounted(async () => {
    await loadData()
    await initCharts()
  })

  // 组件卸载时清理
  const cleanup = () => {
    window.removeEventListener('resize', handleResize)
    trendChart?.dispose()
    typeChart?.dispose()
  }

  // 在组件卸载时清理
  import { onBeforeUnmount } from 'vue'
  onBeforeUnmount(cleanup)
</script>

<style scoped lang="scss">
  .workorder-statistics {
    .statistics-cards {
      margin-bottom: 20px;

      .stat-card {
        height: 120px;

        :deep(.el-card__body) {
          padding: 20px;
          height: 100%;
          display: flex;
          align-items: center;
        }

        .stat-content {
          display: flex;
          align-items: center;
          width: 100%;

          .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;

            .el-icon {
              font-size: 24px;
              color: white;
            }
          }

          .stat-info {
            flex: 1;

            .stat-value {
              font-size: 28px;
              font-weight: 600;
              line-height: 1;
              margin-bottom: 4px;
            }

            .stat-label {
              font-size: 14px;
              color: #909399;
            }
          }
        }

        &.total .stat-icon {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.pending .stat-icon {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.processing .stat-icon {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.completed .stat-icon {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }
    }

    .charts-section {
      margin-bottom: 20px;

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .chart-container {
        height: 300px;
        width: 100%;
      }
    }

    .quick-actions {
      .action-buttons {
        display: flex;
        gap: 12px;
      }
    }
  }
</style>
