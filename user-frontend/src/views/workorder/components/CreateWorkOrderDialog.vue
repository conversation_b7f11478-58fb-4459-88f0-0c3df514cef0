<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" v-loading="loading">
      <!-- 基本信息 -->
      <el-divider content-position="left">售后服务申请</el-divider>

      <!-- 🔥 使用动态获取的服务类型 -->
      <el-form-item label="服务类型" prop="work_order_type">
        <el-select
          v-model="form.work_order_type"
          placeholder="请选择需要的售后服务类型"
          :loading="typesLoading"
        >
          <el-option
            v-for="type in userFriendlyTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          >
            <div class="service-type-option">
              <span class="type-label">{{ type.label }}</span>
              <span class="type-description">{{ type.description }}</span>
            </div>
          </el-option>
        </el-select>

        <!-- 🔥 业务限制提示 -->
        <div v-if="selectedTypeRestrictions" class="business-restrictions">
          <el-alert type="info" :closable="false" :show-icon="true" class="restriction-alert">
            <template #title>
              <span class="restriction-title">{{ selectedTypeName }} 的申请条件</span>
            </template>
            <div class="restriction-content">
              <ul class="restriction-list">
                <li v-for="restriction in selectedTypeRestrictions" :key="restriction">
                  {{ restriction }}
                </li>
              </ul>
              <p class="restriction-note">
                💡 如您的订单不符合上述条件，工单仍会创建但供应商可能无法处理
              </p>
            </div>
          </el-alert>
        </div>
      </el-form-item>

      <el-form-item label="问题描述" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请详细描述您遇到的问题，我们将尽快为您处理..."
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>

      <!-- 关联订单信息 -->
      <el-divider content-position="left">关联订单</el-divider>

      <!-- 🔥 如果从订单页面跳转，自动填充订单信息 -->
      <el-form-item label="订单号" v-if="!prefilledOrder">
        <el-input
          v-model="form.order_no"
          placeholder="请输入订单号（可选）"
          clearable
          @blur="handleOrderNoChange"
        />
        <div class="form-tip"> 输入订单号可自动关联订单信息，便于快速处理 </div>
      </el-form-item>

      <!-- 预填充的订单信息展示 -->
      <div v-if="prefilledOrder" class="prefilled-order-info">
        <el-card class="order-info-card">
          <template #header>
            <div class="card-header">
              <span>关联订单信息</span>
              <el-button type="text" @click="clearPrefilledOrder">更换订单</el-button>
            </div>
          </template>
          <el-descriptions :column="2" size="small">
            <el-descriptions-item label="订单号">{{
              prefilledOrder.order_no
            }}</el-descriptions-item>
            <el-descriptions-item label="运单号">{{
              prefilledOrder.tracking_no
            }}</el-descriptions-item>
            <el-descriptions-item label="快递公司">{{
              prefilledOrder.express_name
            }}</el-descriptions-item>
            <el-descriptions-item label="订单状态">
              <el-tag :type="getOrderStatusType(prefilledOrder.status)">
                {{ prefilledOrder.status_desc }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>

      <el-form-item label="运单号" v-if="!prefilledOrder">
        <el-input
          v-model="form.tracking_no"
          placeholder="请输入运单号（可选）"
          clearable
          @blur="handleTrackingNoChange"
        />
        <div class="form-tip"> 输入运单号可自动关联订单信息 </div>
      </el-form-item>

      <!-- 补充信息 -->
      <el-divider content-position="left">补充信息</el-divider>

      <!-- 🔥 根据服务类型动态显示相关字段 -->
      <el-form-item label="实际重量" v-if="showWeightInfo">
        <el-input-number
          v-model="form.feedback_weight"
          :min="0"
          :max="999"
          :precision="2"
          placeholder="请输入实际重量"
          style="width: 200px"
        />
        <span style="margin-left: 8px; color: #909399">kg</span>
        <div class="form-tip"> 如实际重量与下单重量不符，请填写此项 </div>
      </el-form-item>

      <el-form-item label="货物价值" v-if="showValueInfo">
        <el-input-number
          v-model="form.goods_value"
          :min="0"
          :max="999999"
          :precision="2"
          placeholder="请输入货物价值"
          style="width: 200px"
        />
        <span style="margin-left: 8px; color: #909399">元</span>
        <div class="form-tip"> 用于理赔金额计算 </div>
      </el-form-item>

      <el-form-item label="费用金额" v-if="showAmountInfo">
        <el-input-number
          v-model="form.overweight_amount"
          :min="0"
          :max="999999"
          :precision="2"
          placeholder="请输入相关费用金额"
          style="width: 200px"
        />
        <span style="margin-left: 8px; color: #909399">元</span>
        <div class="form-tip"> 如涉及费用争议，请填写相关金额 </div>
      </el-form-item>

      <!-- 附件上传 -->
      <el-divider content-position="left">相关凭证</el-divider>

      <el-form-item label="上传凭证">
        <el-upload
          ref="uploadRef"
          :file-list="fileList"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          multiple
          drag
        >
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text"> 将文件拖到此处，或<em>点击上传</em> </div>
          <template #tip>
            <div class="el-upload__tip">
              可上传相关照片、截图或文档作为问题凭证<br />
              支持jpg/png/pdf/doc/docx文件，单个文件不超过20MB，最多上传10个文件
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
        {{ submitButtonText }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules, UploadFile } from 'element-plus'
  import { UploadFilled } from '@element-plus/icons-vue'
  import { WorkOrderService, ExpressService } from '@/api'
  import type { CreateWorkOrderRequest, WorkOrderTypeMapping } from '@/api/model/workOrderModel'
  import type { OrderListItem } from '@/api/model/kuaidiModel'
  import { ErrorHandler } from '@/utils/errorHandler'
  // 🔥 新增：使用统一的工单类型管理
  import { useWorkOrderTypes } from '@/composables/useWorkOrderTypes'

  // Props
  interface Props {
    visible: boolean
    isPageMode?: boolean // 是否为页面模式（非对话框模式）
    prefilledOrderData?: OrderListItem // 🔥 新增：预填充的订单数据
  }

  const props = withDefaults(defineProps<Props>(), {
    isPageMode: false,
    prefilledOrderData: undefined
  })

  // Emits
  const emit = defineEmits<{
    'update:visible': [value: boolean]
    success: []
    cancel: []
  }>()

  // 🔥 使用统一的工单类型管理
  const {
    userFriendlyTypes,
    loading: typesLoading,
    fetchWorkOrderTypes,
    getTypeName,
    needsWeightInfo,
    needsValueInfo,
    needsAmountInfo
  } = useWorkOrderTypes()

  // 响应式数据
  const loading = ref(false)
  const submitLoading = ref(false)
  const formRef = ref<FormInstance>()
  const uploadRef = ref()
  const fileList = ref<UploadFile[]>([])
  const prefilledOrder = ref<OrderListItem | null>(null)

  // 表单数据
  const form = reactive<CreateWorkOrderRequest>({
    work_order_type: undefined as any,
    content: '',
    order_no: '',
    tracking_no: '',
    feedback_weight: undefined,
    goods_value: undefined,
    overweight_amount: undefined,
    attachment_urls: []
  })

  // 表单验证规则
  const rules: FormRules = {
    work_order_type: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
    content: [
      { required: true, message: '请输入问题描述', trigger: 'blur' },
      { min: 5, message: '问题描述至少5个字符', trigger: 'blur' },
      { max: 2000, message: '问题描述不能超过2000个字符', trigger: 'blur' }
    ]
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const dialogTitle = computed(() => {
    return prefilledOrder.value ? '申请售后服务' : '创建售后工单'
  })

  const submitButtonText = computed(() => {
    return prefilledOrder.value ? '提交申请' : '创建工单'
  })

  // 🔥 根据服务类型判断需要显示的字段（使用动态判断）
  const showWeightInfo = computed(() => {
    return form.work_order_type ? needsWeightInfo(form.work_order_type) : false
  })

  const showValueInfo = computed(() => {
    return form.work_order_type ? needsValueInfo(form.work_order_type) : false
  })

  const showAmountInfo = computed(() => {
    return form.work_order_type ? needsAmountInfo(form.work_order_type) : false
  })

  // 🔥 业务限制信息
  const selectedTypeName = computed(() => {
    if (!form.work_order_type) return ''
    const type = userFriendlyTypes.value.find((t) => t.value === form.work_order_type)
    return type?.label || ''
  })

  const selectedTypeRestrictions = computed(() => {
    if (!form.work_order_type) return null

    // 根据工单类型返回对应的业务限制条件
    const restrictionsMap: Record<number, string[]> = {
      1: [
        // 催取件
        '订单状态：必须为"待取件"状态',
        '时间要求：下单时间超过30分钟',
        '适用场景：快递员未及时上门取件'
      ],
      2: [
        // 重量异常
        '订单状态：必须为"已结算"状态',
        '时间要求：下单后次月7号前提交',
        '适用场景：实际重量与下单重量不符需要重新核算费用'
      ],
      10: [
        // 催派送
        '时间要求：下单后1个月内',
        '订单状态：运输状态为"派件中"',
        '适用场景：包裹已到达目的地但未及时派送'
      ],
      14: [
        // 物流停滞
        '时间要求：下单后1天至次月7号前可提交',
        '停滞判断：24小时内无物流轨迹更新',
        '适用场景：包裹长时间停留在某个网点'
      ],
      15: [
        // 重新分配快递员
        '订单状态：运输状态为"待取件"',
        '时间要求：下单后1个月内',
        '适用场景：当前快递员无法提供服务需要更换'
      ],
      16: [
        // 取消运单
        '时间要求：下单后1天至次月7号前可提交',
        '状态要求：订单未完成配送',
        '适用场景：取消发货或订单变更'
      ]
    }

    return restrictionsMap[form.work_order_type] || null
  })

  // 生命周期
  onMounted(async () => {
    // 🔥 组件挂载时获取工单类型
    // 获取所有供应商的工单类型
    await fetchWorkOrderTypes()
  })

  // 监听器
  watch(
    () => props.visible,
    async (visible) => {
      if (visible) {
        resetForm()
        // 🔥 确保工单类型已加载
        if (userFriendlyTypes.value.length === 0) {
          await fetchWorkOrderTypes()
        }
        // 🔥 如果有预填充数据，设置到表单中
        if (props.prefilledOrderData) {
          prefilledOrder.value = props.prefilledOrderData
          form.order_no = props.prefilledOrderData.order_no
          form.tracking_no = props.prefilledOrderData.tracking_no
        }
      }
    }
  )

  watch(
    () => props.prefilledOrderData,
    (newData) => {
      if (newData) {
        prefilledOrder.value = newData
        form.order_no = newData.order_no
        form.tracking_no = newData.tracking_no
      }
    }
  )

  // 方法
  const handleOrderNoChange = async () => {
    if (form.order_no && !prefilledOrder.value) {
      await tryFetchOrderInfo(form.order_no, 'order_no')
    }
  }

  const handleTrackingNoChange = async () => {
    if (form.tracking_no && !prefilledOrder.value) {
      await tryFetchOrderInfo(form.tracking_no, 'tracking_no')
    }
  }

  // 🔥 尝试根据订单号或运单号获取订单信息
  const tryFetchOrderInfo = async (value: string, type: 'order_no' | 'tracking_no') => {
    try {
      loading.value = true
      const response = await ExpressService.queryOrder({
        [type]: value
      })

      if (response.success && response.data) {
        // 将查询到的订单信息转换为OrderListItem格式
        const orderInfo: OrderListItem = {
          id: 0, // 临时ID
          customer_order_no: response.data.order_no,
          order_no: response.data.order_no,
          tracking_no: response.data.tracking_no,
          express_type: response.data.express_type,
          express_name: response.data.express_type, // 简化处理
          provider: '', // 隐藏供应商信息
          provider_name: '快递服务商',
          status: response.data.status,
          status_desc: response.data.status_desc,
          weight: response.data.weight,
          price: response.data.price,
          actual_fee: response.data.actual_fee,
          insurance_fee: response.data.insurance_fee,
          overweight_fee: response.data.overweight_fee,
          underweight_fee: response.data.underweight_fee,
          weight_adjustment_reason: response.data.weight_adjustment_reason,
          billing_status: response.data.billing_status,
          sender_info: JSON.stringify(response.data.sender_info),
          receiver_info: JSON.stringify(response.data.receiver_info),
          order_volume: response.data.order_volume,
          actual_weight: response.data.actual_weight,
          actual_volume: response.data.actual_volume,
          charged_weight: response.data.charged_weight,
          created_at: response.data.created_at,
          updated_at: response.data.updated_at
        }

        prefilledOrder.value = orderInfo
        form.order_no = orderInfo.order_no
        form.tracking_no = orderInfo.tracking_no

        ElMessage.success('已自动关联订单信息')
      }
    } catch (error) {
      // 静默处理错误，不影响用户体验
      console.warn('获取订单信息失败:', error)
    } finally {
      loading.value = false
    }
  }

  const clearPrefilledOrder = () => {
    prefilledOrder.value = null
    form.order_no = ''
    form.tracking_no = ''
  }

  const getOrderStatusType = (status: string) => {
    // 简化状态类型映射
    const statusMap: Record<string, string> = {
      pending: 'warning',
      processing: 'primary',
      completed: 'success',
      cancelled: 'danger'
    }
    return statusMap[status] || 'info'
  }

  const handleFileChange = (file: UploadFile) => {
    // 文件数量检查
    if (fileList.value.length >= 10) {
      ElMessage.error('最多只能上传10个文件')
      return false
    }

    // 文件大小检查
    if (file.size && file.size > 20 * 1024 * 1024) {
      ElMessage.error('文件大小不能超过20MB')
      return false
    }

    // 文件类型检查
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
    if (file.raw && !allowedTypes.includes(file.raw.type)) {
      ElMessage.error('只支持jpg/png/pdf/doc/docx格式的文件')
      return false
    }
  }

  const handleFileRemove = (file: UploadFile) => {
    const index = fileList.value.findIndex((f) => f.uid === file.uid)
    if (index > -1) {
      fileList.value.splice(index, 1)
    }
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
    } catch {
      return
    }

    submitLoading.value = true
    try {
      // 上传附件
      let attachmentUrls: string[] = []
      if (fileList.value.length > 0) {
        const files = fileList.value.map((file) => file.raw!).filter(Boolean)
        const uploadResults = await WorkOrderService.uploadMultipleAttachments(files)
        attachmentUrls = uploadResults.map((result) => result.file_url)
      }

      // 🔥 准备提交数据 - 后端将自动检测供应商
      const submitData: CreateWorkOrderRequest = {
        ...form,
        attachment_urls: attachmentUrls,
        // 清理空值
        order_no: form.order_no || undefined,
        tracking_no: form.tracking_no || undefined
      }

      const response = await WorkOrderService.createWorkOrder(submitData)

      if (response.success) {
        ElMessage.success(
          prefilledOrder.value ? '售后申请提交成功，我们将尽快处理' : '工单创建成功'
        )
        emit('success')
      } else {
        throw new Error(response.message || '提交失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
    } finally {
      submitLoading.value = false
    }
  }

  const resetForm = () => {
    Object.assign(form, {
      work_order_type: undefined,
      content: '',
      order_no: '',
      tracking_no: '',
      feedback_weight: undefined,
      goods_value: undefined,
      overweight_amount: undefined,
      callback_url: '',
      message_callback_url: '',
      attachment_urls: []
    })

    fileList.value = []
    prefilledOrder.value = null
    formRef.value?.resetFields()
  }

  const handleClose = () => {
    if (props.isPageMode) {
      emit('cancel')
    } else {
      emit('update:visible', false)
    }
  }
</script>

<style scoped lang="scss">
  .form-tip {
    margin-top: 4px;
    color: #909399;
    font-size: 12px;
    line-height: 1.4;
  }

  .prefilled-order-info {
    margin-bottom: 16px;

    .order-info-card {
      border: 1px solid #e4e7ed;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }

  :deep(.el-divider__text) {
    font-weight: 500;
    color: #303133;
  }

  :deep(.el-upload-dragger) {
    width: 100%;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  :deep(.el-descriptions-item__label) {
    font-weight: 500;
    color: #606266;
  }

  /* 🔥 新增：服务类型选项样式 */
  .service-type-option {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .type-label {
      font-weight: 500;
      color: #303133;
    }

    .type-description {
      font-size: 12px;
      color: #909399;
      line-height: 1.2;
    }
  }

  :deep(.el-select-dropdown__item) {
    height: auto;
    padding: 8px 20px;
    line-height: 1.4;
  }

  /* 🔥 业务限制提示样式 */
  .business-restrictions {
    margin-top: 12px;

    .restriction-alert {
      border-radius: 6px;

      :deep(.el-alert__content) {
        padding-left: 0;
      }

      .restriction-title {
        font-weight: 600;
        color: #1890ff;
      }

      .restriction-content {
        margin-top: 8px;

        .restriction-list {
          margin: 0;
          padding-left: 16px;

          li {
            margin-bottom: 4px;
            line-height: 1.5;
            color: #606266;

            &:last-child {
              margin-bottom: 8px;
            }
          }
        }

        .restriction-note {
          margin: 8px 0 0 0;
          padding: 8px 12px;
          background: #f0f9ff;
          border-left: 3px solid #1890ff;
          border-radius: 4px;
          font-size: 13px;
          color: #0969da;
          line-height: 1.4;
        }
      }
    }
  }
</style>
