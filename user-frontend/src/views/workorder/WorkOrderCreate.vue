<template>
  <div class="workorder-create-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="handleBack" type="text" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <h2>创建工单</h2>
      <p class="subtitle">请详细描述您遇到的问题，我们将尽快为您处理</p>
    </div>

    <!-- 创建表单 -->
    <div class="form-section">
      <el-card>
        <CreateWorkOrderDialog
          :visible="true"
          :is-page-mode="true"
          @success="handleCreateSuccess"
          @cancel="handleBack"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import { ArrowLeft } from '@element-plus/icons-vue'
  import CreateWorkOrderDialog from './components/CreateWorkOrderDialog.vue'

  // 路由
  const router = useRouter()

  // 方法
  const handleBack = () => {
    router.back()
  }

  const handleCreateSuccess = () => {
    ElMessage.success('工单创建成功')
    router.push('/workorder/list')
  }
</script>

<style scoped lang="scss">
  .workorder-create-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;

    .page-header {
      margin-bottom: 24px;

      .back-button {
        margin-bottom: 12px;
        padding: 0;

        .el-icon {
          margin-right: 4px;
        }
      }

      h2 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 24px;
        font-weight: 600;
      }

      .subtitle {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }

    .form-section {
      :deep(.el-card__body) {
        padding: 0;
      }

      :deep(.el-dialog__header) {
        display: none;
      }

      :deep(.el-dialog__body) {
        padding: 24px;
      }

      :deep(.el-dialog__footer) {
        padding: 0 24px 24px;
        text-align: right;
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .workorder-create-container {
      padding: 12px;

      .page-header {
        h2 {
          font-size: 20px;
        }
      }

      .form-section {
        :deep(.el-dialog__body) {
          padding: 16px;
        }

        :deep(.el-dialog__footer) {
          padding: 0 16px 16px;
        }
      }
    }
  }
</style>
