<template>
  <div class="address-parse-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-card class="header-card">
        <div class="header-content">
          <div class="header-left">
            <h1 class="page-title">🧠 智能地址解析</h1>
            <p class="page-description">
              使用先进的AI技术快速解析地址信息，自动提取姓名、电话、详细地址等关键信息，
              支持单个解析、批量处理和地址验证功能。
            </p>
          </div>
          <div class="header-right">
            <el-card class="stat-card" shadow="hover">
              <template #header>
                <div style="display: inline-flex; align-items: center"> 📊 今日解析 </div>
              </template>
              <el-statistic :value="todayCount" />
            </el-card>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 功能介绍 -->
    <div class="feature-intro">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="feature-card">
            <div class="feature-item">
              <div class="feature-icon">🎯</div>
              <h3>智能识别</h3>
              <p>自动识别姓名、电话、地址等信息，准确率高达95%以上</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="feature-card">
            <div class="feature-item">
              <div class="feature-icon">⚡</div>
              <h3>批量处理</h3>
              <p>支持批量解析多个地址，并发处理，大幅提升工作效率</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="feature-card">
            <div class="feature-item">
              <div class="feature-icon">🔍</div>
              <h3>地址验证</h3>
              <p>验证地址的有效性，提供标准化建议，确保地址准确性</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要功能区域 -->
    <div class="main-content">
      <AddressParse />
    </div>

    <!-- 使用说明 -->
    <div class="usage-guide">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>📖 使用说明</span>
          </div>
        </template>

        <el-collapse v-model="activeGuide">
          <el-collapse-item title="🎯 单个地址解析" name="single">
            <div class="guide-content">
              <ol>
                <li>在文本框中输入包含姓名、电话、地址的文本</li>
                <li
                  >可以使用换行符分隔不同信息，如：<br />
                  <code
                    >李健<br />17099916606<br />湖南省永州市冷水滩区珊瑚街道 园丁山庄8栋19号</code
                  >
                </li>
                <li>点击"开始解析"按钮进行解析</li>
                <li>查看解析结果，可以复制或导出数据</li>
              </ol>
            </div>
          </el-collapse-item>

          <el-collapse-item title="📋 批量地址解析" name="batch">
            <div class="guide-content">
              <ol>
                <li>点击"批量解析"选项卡</li>
                <li>设置合适的并发数（建议5-10）</li>
                <li>添加多个地址文本</li>
                <li>点击"批量解析"开始处理</li>
                <li>查看统计信息和详细结果</li>
                <li>可以导出成功结果或失败列表</li>
              </ol>
            </div>
          </el-collapse-item>

          <el-collapse-item title="🔍 地址验证" name="validate">
            <div class="guide-content">
              <ol>
                <li>点击"地址验证"选项卡</li>
                <li>选择省市区信息</li>
                <li>输入详细地址</li>
                <li>点击"验证地址"查看结果</li>
                <li>根据验证结果和建议优化地址</li>
              </ol>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </div>

    <!-- 常见问题 -->
    <div class="faq-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>❓ 常见问题</span>
          </div>
        </template>

        <el-collapse v-model="activeFaq">
          <el-collapse-item title="解析准确率如何？" name="accuracy">
            <p>我们的AI地址解析系统准确率达到95%以上，对于标准格式的地址文本识别效果更佳。</p>
          </el-collapse-item>

          <el-collapse-item title="支持哪些地址格式？" name="format">
            <p>支持多种地址格式，包括：</p>
            <ul>
              <li>标准格式：省市区 + 详细地址</li>
              <li>混合格式：姓名、电话、地址混合</li>
              <li>简化格式：仅包含关键地址信息</li>
            </ul>
          </el-collapse-item>

          <el-collapse-item title="批量解析有数量限制吗？" name="limit">
            <p>单次批量解析最多支持100个地址，建议根据网络情况调整并发数。</p>
          </el-collapse-item>

          <el-collapse-item title="解析失败怎么办？" name="failure">
            <p>解析失败可能的原因：</p>
            <ul>
              <li>地址格式不规范</li>
              <li>信息不完整</li>
              <li>网络连接问题</li>
            </ul>
            <p>建议检查输入格式或联系技术支持。</p>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </div>
  </div>
</template>

<script>
  import { ref } from 'vue'
  import AddressParse from '@/components/AddressParse.vue'

  export default {
    name: 'AddressParsePage',
    components: {
      AddressParse
    },
    setup() {
      const activeGuide = ref(['single'])
      const activeFaq = ref([])
      const todayCount = ref(0)

      // 模拟今日解析数量
      const updateTodayCount = () => {
        // 这里可以调用API获取真实数据
        todayCount.value = Math.floor(Math.random() * 100) + 50
      }

      // 组件挂载时更新数据
      updateTodayCount()

      return {
        activeGuide,
        activeFaq,
        todayCount
      }
    }
  }
</script>

<style scoped>
  .address-parse-page {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .page-header {
    margin-bottom: 30px;
  }

  .header-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-left {
    flex: 1;
  }

  .page-title {
    font-size: 28px;
    margin: 0 0 10px 0;
    font-weight: bold;
  }

  .page-description {
    font-size: 16px;
    line-height: 1.6;
    margin: 0;
    opacity: 0.9;
  }

  .header-right {
    margin-left: 30px;
  }

  .stat-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
  }

  .feature-intro {
    margin-bottom: 30px;
  }

  .feature-card {
    height: 100%;
    transition: transform 0.3s ease;
  }

  .feature-card:hover {
    transform: translateY(-5px);
  }

  .feature-item {
    text-align: center;
    padding: 20px;
  }

  .feature-icon {
    font-size: 48px;
    margin-bottom: 15px;
  }

  .feature-item h3 {
    color: #409eff;
    margin-bottom: 10px;
    font-size: 18px;
  }

  .feature-item p {
    color: #666;
    line-height: 1.6;
    margin: 0;
  }

  .main-content {
    margin-bottom: 30px;
  }

  .usage-guide,
  .faq-section {
    margin-bottom: 30px;
  }

  .card-header {
    font-size: 18px;
    font-weight: bold;
    color: #409eff;
  }

  .guide-content {
    padding: 10px 0;
  }

  .guide-content ol,
  .guide-content ul {
    margin: 0;
    padding-left: 20px;
  }

  .guide-content li {
    margin-bottom: 8px;
    line-height: 1.6;
  }

  .guide-content code {
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
  }

  :deep(.el-card__header) {
    background: #f8f9fa;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-collapse-item__header) {
    font-weight: 500;
    color: #303133;
  }

  :deep(.el-statistic-card) {
    background: transparent;
    border: none;
  }

  :deep(.el-statistic__content) {
    color: white;
  }

  :deep(.el-statistic-card .el-card__body) {
    padding: 15px;
  }
</style>
