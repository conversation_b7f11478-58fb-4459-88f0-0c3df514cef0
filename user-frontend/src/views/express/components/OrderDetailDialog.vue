<template>
  <el-dialog
    v-model="dialogVisible"
    title="订单详情"
    width="800px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
  >
    <div v-loading="detailLoading" element-loading-text="正在加载订单详情...">
      <div v-if="orderDetail" class="order-detail">
        <!-- 基本信息 -->
        <el-card class="info-card">
          <template #header>
            <span>基本信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单号">{{ orderDetail.order_no }}</el-descriptions-item>
            <el-descriptions-item label="运单号">{{
              orderDetail.tracking_no
            }}</el-descriptions-item>
            <el-descriptions-item label="快递公司">{{
              getExpressName(orderDetail)
            }}</el-descriptions-item>
            <el-descriptions-item label="订单状态">
              <el-tag :type="getStatusType(orderDetail.status)">{{
                getStatusDesc(orderDetail.status)
              }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="下单重量"
              >{{ formatWeight(orderDetail.weight) }}kg</el-descriptions-item
            >
            <el-descriptions-item label="下单体积">{{
              formatVolume(orderDetail.order_volume)
            }}</el-descriptions-item>
            <el-descriptions-item label="实际重量"
              >{{ formatWeight(orderDetail.actual_weight) }}kg</el-descriptions-item
            >
            <el-descriptions-item label="实际体积">{{
              formatVolume(orderDetail.actual_volume)
            }}</el-descriptions-item>
            <el-descriptions-item label="计费重量"
              >{{ formatWeight(orderDetail.charged_weight) }}kg</el-descriptions-item
            >
            <el-descriptions-item label="预收"
              >¥{{ formatPrice(orderDetail.price || 0) }}</el-descriptions-item
            >
            <el-descriptions-item label="实收金额"
              >¥{{ formatPrice(orderDetail.actual_fee || 0) }}</el-descriptions-item
            >
            <el-descriptions-item label="保价费"
              >¥{{ formatPrice(orderDetail.insurance_fee || 0) }}</el-descriptions-item
            >
            <el-descriptions-item label="超重费用" v-if="orderDetail.overweight_fee > 0">
              <span class="fee-amount overweight"
                >¥{{ formatPrice(orderDetail.overweight_fee) }}</span
              >
            </el-descriptions-item>
            <el-descriptions-item label="超轻费用" v-if="orderDetail.underweight_fee > 0">
              <span class="fee-amount underweight"
                >¥{{ formatPrice(orderDetail.underweight_fee) }}</span
              >
            </el-descriptions-item>
            <el-descriptions-item label="重量调整原因" v-if="orderDetail.weight_adjustment_reason">
              <el-tag type="info" size="small">{{ orderDetail.weight_adjustment_reason }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="费用差额" v-if="shouldShowFeeDifference(orderDetail)">
              <span
                :class="[
                  'fee-difference',
                  getFeeDifference(orderDetail) > 0 ? 'positive' : 'negative'
                ]"
              >
                {{ getFeeDifference(orderDetail) > 0 ? '+' : '' }}¥{{
                  formatPrice(Math.abs(getFeeDifference(orderDetail)))
                }}
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{
              formatDate(orderDetail.created_at)
            }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{
              formatDate(orderDetail.updated_at)
            }}</el-descriptions-item>
            <!-- 🔥 新增：失败订单信息 -->
            <el-descriptions-item 
              v-if="orderDetail.status === 'failed' && orderDetail.failure_reason"
              label="失败原因"
              :span="2"
            >
              <div class="failure-info">
                <el-tag type="danger" size="small">{{ getFailureReasonDesc(orderDetail.failure_reason) }}</el-tag>
                <el-tag 
                  :type="getFailureSourceType(orderDetail.failure_source)" 
                  size="small" 
                  class="failure-source-tag"
                  v-if="orderDetail.failure_source"
                >
                  {{ getFailureSourceDesc(orderDetail.failure_source) }}
                </el-tag>
                <span class="failure-stage" v-if="orderDetail.failure_stage">
                  失败阶段: {{ getFailureStageDesc(orderDetail.failure_stage) }}
                </span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item 
              v-if="orderDetail.status === 'failed' && orderDetail.failure_message"
              label="失败详情"
              :span="2"
            >
              <div class="failure-message">
                {{ orderDetail.failure_message }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item 
              v-if="orderDetail.status === 'failed' && orderDetail.failure_time"
              label="失败时间"
            >
              {{ formatDate(orderDetail.failure_time) }}
            </el-descriptions-item>
            <el-descriptions-item 
              v-if="orderDetail.status === 'failed'"
              label="可重试"
            >
              <el-tag :type="orderDetail.can_retry ? 'success' : 'info'" size="small">
                {{ orderDetail.can_retry ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 🔥 新增：售后服务快捷操作 -->
          <div class="after-sales-section" v-if="canApplyAfterSales()">
            <el-divider content-position="left">售后服务</el-divider>
            <div class="after-sales-actions">
              <el-button type="warning" @click="handleApplyAfterSales" size="small">
                <el-icon><Service /></el-icon>
                申请售后服务
              </el-button>
              <span class="action-tip">如遇包裹丢失、损坏、派送异常等问题，可申请售后服务</span>
            </div>
          </div>
        </el-card>

        <!-- 寄件人信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header-with-icon">
              <el-icon class="header-icon sender-icon"><User /></el-icon>
              <span>寄件人信息</span>
            </div>
          </template>
          <div class="contact-info">
            <div class="contact-row">
              <el-icon class="contact-icon"><User /></el-icon>
              <span class="contact-label">姓名</span>
              <span class="contact-value">{{ getSenderName(orderDetail) }}</span>
            </div>
            <div class="contact-row">
              <el-icon class="contact-icon"><Phone /></el-icon>
              <span class="contact-label">电话</span>
              <span class="contact-value">{{ getSenderPhone(orderDetail) }}</span>
            </div>
            <div class="contact-row">
              <el-icon class="contact-icon"><Location /></el-icon>
              <span class="contact-label">地址</span>
              <span class="contact-value address-value">{{ getSenderAddress(orderDetail) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 收件人信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header-with-icon">
              <el-icon class="header-icon receiver-icon"><UserFilled /></el-icon>
              <span>收件人信息</span>
            </div>
          </template>
          <div class="contact-info">
            <div class="contact-row">
              <el-icon class="contact-icon"><UserFilled /></el-icon>
              <span class="contact-label">姓名</span>
              <span class="contact-value">{{ getReceiverName(orderDetail) }}</span>
            </div>
            <div class="contact-row">
              <el-icon class="contact-icon"><Phone /></el-icon>
              <span class="contact-label">电话</span>
              <span class="contact-value">{{ getReceiverPhone(orderDetail) }}</span>
            </div>
            <div class="contact-row">
              <el-icon class="contact-icon"><Location /></el-icon>
              <span class="contact-label">地址</span>
              <span class="contact-value address-value">{{ getReceiverAddress(orderDetail) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 🔥 新增：揽件员信息 -->
        <el-card v-if="hasCourierInfo(orderDetail)" class="info-card">
          <template #header>
            <div class="card-header-with-icon">
              <el-icon class="header-icon courier-icon"><Van /></el-icon>
              <span>揽件员信息</span>
            </div>
          </template>
          <div class="contact-info">
            <div v-if="(orderDetail as any)?.courier_name" class="contact-row">
              <el-icon class="contact-icon"><User /></el-icon>
              <span class="contact-label">姓名</span>
              <span class="contact-value">{{ (orderDetail as any).courier_name }}</span>
            </div>
            <div v-if="(orderDetail as any)?.courier_phone" class="contact-row">
              <el-icon class="contact-icon"><Phone /></el-icon>
              <span class="contact-label">电话</span>
              <span class="contact-value">{{ (orderDetail as any).courier_phone }}</span>
            </div>
            <div v-if="(orderDetail as any)?.station_name" class="contact-row">
              <el-icon class="contact-icon"><OfficeBuilding /></el-icon>
              <span class="contact-label">网点</span>
              <span class="contact-value">{{ (orderDetail as any).station_name }}</span>
            </div>
            <div v-if="(orderDetail as any)?.pickup_code" class="contact-row">
              <el-icon class="contact-icon"><Key /></el-icon>
              <span class="contact-label">取件码</span>
              <span class="contact-value pickup-code-value">
                <el-tag type="success" size="large">{{ (orderDetail as any).pickup_code }}</el-tag>
              </span>
            </div>
          </div>
        </el-card>

        <!-- 订单状态历史 -->
        <OrderStatusTimeline v-if="orderForTimeline" :order="orderForTimeline" :visible="dialogVisible" />

        <!-- 物流轨迹 -->
        <el-card class="info-card">
          <template #header>
            <span>物流轨迹</span>
            <el-button type="primary" size="small" @click="refreshTrack" :loading="trackLoading">
              刷新轨迹
            </el-button>
          </template>
          <el-timeline v-if="orderDetail.tracks && orderDetail.tracks.length > 0">
            <el-timeline-item
              v-for="(track, index) in orderDetail.tracks"
              :key="index"
              :timestamp="formatDate(track.time)"
              placement="top"
            >
              <div class="track-item">
                <div class="track-location">{{ track.location }}</div>
                <div class="track-description">{{ track.context || track.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
          <div v-else-if="detailLoading" class="track-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>正在加载物流轨迹...</span>
          </div>
          <el-empty v-else description="暂无物流轨迹信息" />
        </el-card>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="!detailLoading" class="no-data">
        <el-empty description="无法获取订单详情" />
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 🔥 新增：售后申请对话框 -->
  <CreateWorkOrderDialog
    v-model:visible="showAfterSalesDialog"
    :prefilled-order-data="orderForAfterSales || undefined"
    @success="handleAfterSalesSuccess"
  />
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { User, UserFilled, Phone, Location, Loading, Service, Van, OfficeBuilding, Key } from '@element-plus/icons-vue'
  import { ExpressService, ConfigService } from '@/api'
  import type { OrderListItem, QueryOrderResponse, OrderStatus } from '@/api'
  import { ErrorHandler } from '@/utils/errorHandler'
  // 🔥 新增：导入工单创建组件
  import CreateWorkOrderDialog from '../../workorder/components/CreateWorkOrderDialog.vue'
  // 🔥 新增：导入订单状态时间线组件
  import OrderStatusTimeline from './OrderStatusTimeline.vue'

  // Props
  interface Props {
    visible: boolean
    order: OrderListItem | null
  }

  // Emits
  interface Emits {
    (e: 'update:visible', value: boolean): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 响应式数据
  const trackLoading = ref(false)
  const detailLoading = ref(false)
  const orderDetail = ref<QueryOrderResponse | null>(null)
  const orderStatuses = ref<OrderStatus[]>([])
  // 🔥 新增：售后申请相关状态
  const showAfterSalesDialog = ref(false)
  const orderForAfterSales = ref<OrderListItem | null>(null)

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  // 为状态时间线组件提供订单数据
  const orderForTimeline = computed(() => props.order)

  // 加载订单状态配置
  const loadOrderStatuses = async () => {
    try {
      orderStatuses.value = await ConfigService.getOrderStatuses()
    } catch (error) {
      ErrorHandler.handleApiError(error, false)
    }
  }

  // 获取订单详情
  const getOrderDetail = async () => {
    if (!props.order) return

    detailLoading.value = true
    try {
      const response = await ExpressService.queryOrder({
        order_no: props.order.order_no
      })

      if (response.success && response.data) {
        orderDetail.value = response.data

        // 🚀 订单详情加载成功后，自动触发物流轨迹查询
        await autoRefreshTrack()
      } else {
        throw new Error(response.message || '获取订单详情失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
      orderDetail.value = null
    } finally {
      detailLoading.value = false
    }
  }

  // 自动刷新物流轨迹（静默执行，不显示成功消息）
  const autoRefreshTrack = async () => {
    if (!props.order || !orderDetail.value) return

    try {
      const response = await ExpressService.queryTrack({
        tracking_no: props.order.tracking_no,
        express_type: props.order.express_type
      })

      if (response.success && response.data) {
        if (orderDetail.value) {
          orderDetail.value.tracks = response.data.tracks
          orderDetail.value.status = response.data.status
          orderDetail.value.status_desc = response.data.status_desc
        }
      }
      // 自动刷新失败时不显示错误消息，避免干扰用户体验
    } catch (error) {
      // 静默处理错误，可以在控制台记录
      console.warn('自动刷新物流轨迹失败:', error)
    }
  }

  // 手动刷新物流轨迹（显示加载状态和消息提示）
  const refreshTrack = async () => {
    if (!props.order) return

    trackLoading.value = true
    try {
      const response = await ExpressService.queryTrack({
        tracking_no: props.order.tracking_no,
        express_type: props.order.express_type
      })

      if (response.success && response.data) {
        if (orderDetail.value) {
          orderDetail.value.tracks = response.data.tracks
          orderDetail.value.status = response.data.status
          orderDetail.value.status_desc = response.data.status_desc
        }
        ElMessage.success('轨迹信息已更新')
      } else {
        throw new Error(response.message || '刷新轨迹失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
    } finally {
      trackLoading.value = false
    }
  }

  // 获取状态标签类型
  const getStatusType = (status: string) => {
    const statusInfo = orderStatuses.value.find((s) => s.code === status)

    // 如果配置中找到了状态，使用配置的颜色
    if (statusInfo?.color) {
      return statusInfo.color
    }

    // 本地状态颜色映射作为备选
    const localStatusColors: Record<string, string> = {
      created: 'info',
      submitted: 'primary',
      assigned: 'info',
      awaiting_pickup: 'warning',
      picked_up: 'primary',
      in_transit: 'info',
      out_for_delivery: 'info',
      delivered: 'success',
      failed_delivery: 'danger',
      returned: 'warning',
      cancelling: 'warning', // 取消中 - 橙色
      cancelled: 'danger', // 英式拼写 - 红色
      canceled: 'danger', // 美式拼写 - 红色
      processing: 'primary',
      billed: 'success',
      voided: 'danger',
      exception: 'danger'
    }

    return localStatusColors[status] || 'info'
  }

  // 获取状态描述
  const getStatusDesc = (status: string) => {
    const statusMap: Record<string, string> = {
      // === 下单阶段 ===
      submitted: '已提交',
      submit_failed: '提交失败',
      print_failed: '面单生成失败',

      // === 分配阶段 ===
      assigned: '已分配',
      awaiting_pickup: '等待揽收',

      // === 揽收阶段 ===
      picked_up: '已揽收',
      pickup_failed: '揽收失败',

      // === 运输阶段 ===
      in_transit: '运输中',
      out_for_delivery: '派送中',

      // === 签收阶段 ===
      delivered: '已签收',
      delivered_abnormal: '异常签收',

      // === 计费阶段 ===
      billed: '已计费',

      // === 异常状态 ===
      exception: '异常',
      returned: '已退回',
      forwarded: '已转寄',

      // === 取消状态 ===
      cancelling: '取消中',
      cancelled: '已取消',
      voided: '已作废',

      // === 特殊状态 ===
      weight_updated: '重量更新',
      revived: '订单复活',

      // === 兼容状态 ===
      failed_delivery: '派送失败',
      canceled: '已取消', // 美式拼写
      processing: '处理中',
      created: '已创建'
    }
    return statusMap[status] || status
  }

  // 获取快递公司名称
  const getExpressName = (order: any) => {
    if (!order) return '-'

    // 如果有express_name且不是英文代码，直接返回
    if (order.express_name && !isExpressCode(order.express_name)) {
      return order.express_name
    }

    // 快递公司代码映射（包含所有供应商的代码）
    const expressMap: Record<string, string> = {
      // === 快递100供应商代码 ===
      shentong: '申通快递',
      yuantong: '圆通快递',
      zhongtong: '中通快递',
      yunda: '韵达快递',
      jtexpress: '极兔快递',
      jd: '京东快递',
      // 德邦物流已移除支持
      ems: 'EMS',
      sf: '顺丰快递',

      // === 云通供应商代码 ===
      ST: '申通快递', // 云通申通代码
      YT: '圆通快递', // 云通圆通代码
      ZT: '中通快递', // 云通中通代码
      YD: '韵达快递', // 云通韵达代码
      JT: '极兔快递', // 云通极兔代码
      JD: '京东物流', // 云通京东代码

      // === 易达供应商代码 ===
      'STO-INT': '申通快递', // 易达申通代码
      YTO: '圆通快递', // 易达圆通代码
      ZTO: '中通快递', // 易达中通代码
      YUND: '韵达快递', // 易达韵达代码
      // 注：JT、JD 已在上方云通映射中定义，易达同名代码无需重复

      // === 菜鸟供应商代码 ===
      YUNDA: '韵达快递', // 菜鸟韵达代码
      LE04284890: '京东物流', // 菜鸟京东代码
      DBKD: '德邦快递', // 菜鸟德邦代码
      HTKY: '极兔快递', // 菜鸟极兔代码（使用百世快递代码）
      NORMAL: '菜鸟裹裹', // 菜鸟标准快递代码

      // === 统一系统代码 ===
      SF: '顺丰速运',
      STO: '申通快递',
      CAINIAO: '菜鸟裹裹', // 🔧 修改：统一使用CAINIAO代码
      // 德邦物流已移除支持
      EMS: '中国邮政'
    }

    const expressCode = order.express_type || order.express_name
    return expressMap[expressCode] || expressCode || '-'
  }

  // 判断是否为快递公司代码
  const isExpressCode = (name: string) => {
    const codes = [
      // 快递100代码
      'shentong',
      'yuantong',
      'zhongtong',
      'yunda',
      'jtexpress',
      'jd',
      // 德邦物流已移除支持
      'ems',
      'sf',
      // 云通代码
      'ST',
      'YT',
      'ZT',
      'YD',
      'JT',
      'JD',
      // 易达代码
      'STO-INT',
      'YTO',
      'ZTO',
      'YUND',
      // 统一系统代码
      'SF',
      'STO',
      // 德邦物流已移除支持
      'EMS',
      'HTKY'
    ]
    return codes.includes(name) || codes.includes(name.toLowerCase())
  }

  // 格式化价格
  const formatPrice = (price: number | string) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price
    return isNaN(numPrice) ? '0.00' : numPrice.toFixed(2)
  }

  // 格式化重量
  const formatWeight = (weight: number | string | null | undefined) => {
    if (weight === null || weight === undefined) return '0.0'
    const numWeight = typeof weight === 'string' ? parseFloat(weight) : weight
    return isNaN(numWeight) ? '0.0' : numWeight.toFixed(1)
  }

  // 格式化体积（与易达系统保持一致，显示为cm³）
  const formatVolume = (volume: number | string | null | undefined) => {
    if (volume === null || volume === undefined) return '0.00cm³'
    const numVolume = typeof volume === 'string' ? parseFloat(volume) : volume
    if (isNaN(numVolume)) return '0.00cm³'
    // 将m³转换为cm³显示
    const volumeCm3 = numVolume * 1000000
    return `${volumeCm3.toFixed(2)}cm³`
  }

  // 计算费用差额
  const getFeeDifference = (order: any) => {
    if (!order) return 0
    const actualFee = order.actual_fee || 0
    const price = order.price || 0
    return actualFee - price
  }

  // 判断是否应该显示费用差额
  const shouldShowFeeDifference = (order: any) => {
    if (!order) return false
    // 只有当实际费用大于0且与预收费用不同时才显示差额
    const actualFee = order.actual_fee || 0
    const price = order.price || 0
    return actualFee > 0 && actualFee !== price
  }

  // 🔥 修复：格式化日期 - 明确指定北京时区
  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    try {
      return new Date(dateString).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai', // 🔥 修复：明确指定北京时区
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  // 🔥 修复：获取失败原因描述（支持中文原因）
  const getFailureReasonDesc = (reason: string) => {
    // 如果已经是中文，直接返回
    if (/[\u4e00-\u9fa5]/.test(reason)) {
      return reason
    }

    // 英文常量映射为中文
    const reasonMap: Record<string, string> = {
      validation_failed: '参数验证失败',
      balance_insufficient: '余额不足',
      provider_error: '供应商错误',
      price_validation_failed: '价格验证失败',
      network_error: '网络错误',
      system_error: '系统错误',
      timeout_error: '请求超时',
      unknown_error: '未知错误'
    }
    return reasonMap[reason] || reason
  }

  // 🔥 修复：获取失败阶段描述（支持中文阶段）
  const getFailureStageDesc = (stage: string) => {
    // 如果已经是中文，直接返回
    if (/[\u4e00-\u9fa5]/.test(stage)) {
      return stage
    }

    // 英文常量映射为中文
    const stageMap: Record<string, string> = {
      ORDER_VALIDATION: '订单验证',
      PRICE_VALIDATION: '价格验证',
      PROVIDER_API_CALL: '供应商接口调用',
      BALANCE_PRE_CHARGE: '余额预扣费',
      DB_SAVE: '数据库保存',
      ORDER_CREATE: '订单创建'
    }
    return stageMap[stage] || stage
  }

  // 🔥 新增：获取失败来源描述
  const getFailureSourceDesc = (source: string) => {
    if (!source) return ''

    const sourceMap: Record<string, string> = {
      provider: '供应商返回',
      system: '系统返回'
    }
    return sourceMap[source] || source
  }

  // 🔥 新增：获取失败来源标签类型
  const getFailureSourceType = (source: string) => {
    if (!source) return 'info'

    const typeMap: Record<string, string> = {
      provider: 'warning',  // 供应商错误用警告色
      system: 'info'        // 系统错误用信息色
    }
    return typeMap[source] || 'info'
  }

  // 解析联系人信息的通用函数
  const parseContactInfo = (contactInfo: any) => {
    if (!contactInfo) return null

    // 如果是JSON字符串，先解析
    if (typeof contactInfo === 'string') {
      try {
        return JSON.parse(contactInfo)
      } catch (e) {
        return null
      }
    }

    // 如果是对象格式，直接返回
    if (typeof contactInfo === 'object') {
      return contactInfo
    }

    return null
  }

  // 获取寄件人姓名
  const getSenderName = (order: any) => {
    const senderInfo = parseContactInfo(order?.sender_info)
    return senderInfo?.name || '-'
  }

  // 获取寄件人电话
  const getSenderPhone = (order: any) => {
    const senderInfo = parseContactInfo(order?.sender_info)
    return senderInfo?.mobile || senderInfo?.phone || '-'
  }

  // 获取寄件人地址
  const getSenderAddress = (order: any) => {
    const senderInfo = parseContactInfo(order?.sender_info)
    if (!senderInfo) return '-'

    const addressParts = []
    if (senderInfo.province) addressParts.push(senderInfo.province)
    if (senderInfo.city) addressParts.push(senderInfo.city)
    if (senderInfo.district) addressParts.push(senderInfo.district)
    if (senderInfo.address) addressParts.push(senderInfo.address)

    return addressParts.length > 0 ? addressParts.join(' ') : '-'
  }

  // 获取收件人姓名
  const getReceiverName = (order: any) => {
    const receiverInfo = parseContactInfo(order?.receiver_info)
    return receiverInfo?.name || '-'
  }

  // 获取收件人电话
  const getReceiverPhone = (order: any) => {
    const receiverInfo = parseContactInfo(order?.receiver_info)
    return receiverInfo?.mobile || receiverInfo?.phone || '-'
  }

  // 获取收件人地址
  const getReceiverAddress = (order: any) => {
    const receiverInfo = parseContactInfo(order?.receiver_info)
    if (!receiverInfo) return '-'

    const addressParts = []
    if (receiverInfo.province) addressParts.push(receiverInfo.province)
    if (receiverInfo.city) addressParts.push(receiverInfo.city)
    if (receiverInfo.district) addressParts.push(receiverInfo.district)
    if (receiverInfo.address) addressParts.push(receiverInfo.address)

    return addressParts.length > 0 ? addressParts.join(' ') : '-'
  }

  // 🔥 新增：判断订单是否可以申请售后
  const canApplyAfterSales = () => {
    if (!orderDetail.value) return false

    // 仅当订单处于"已取消 / 作废"相关状态时禁止申请售后，其余状态均可申请
    const forbiddenStatuses = ['cancelled', 'canceled', 'cancelling', 'voided']
    return !forbiddenStatuses.includes(orderDetail.value.status)
  }

  // 🔥 新增：申请售后服务
  const handleApplyAfterSales = () => {
    if (!props.order || !orderDetail.value) return

    // 将订单详情转换为OrderListItem格式
    orderForAfterSales.value = {
      id: 0, // 临时ID
      customer_order_no: orderDetail.value.order_no,
      order_no: orderDetail.value.order_no,
      tracking_no: orderDetail.value.tracking_no,
      express_type: orderDetail.value.express_type,
      express_name: getExpressName(orderDetail.value),
      provider: '', // 隐藏供应商信息
      provider_name: '快递服务商',
      status: orderDetail.value.status,
      status_desc: orderDetail.value.status_desc,
      weight: orderDetail.value.weight,
      price: orderDetail.value.price,
      actual_fee: orderDetail.value.actual_fee,
      insurance_fee: orderDetail.value.insurance_fee,
      overweight_fee: orderDetail.value.overweight_fee,
      underweight_fee: orderDetail.value.underweight_fee,
      weight_adjustment_reason: orderDetail.value.weight_adjustment_reason,
      billing_status: orderDetail.value.billing_status,
      sender_info: JSON.stringify(orderDetail.value.sender_info),
      receiver_info: JSON.stringify(orderDetail.value.receiver_info),
      order_volume: orderDetail.value.order_volume,
      actual_weight: orderDetail.value.actual_weight,
      actual_volume: orderDetail.value.actual_volume,
      charged_weight: orderDetail.value.charged_weight,
      created_at: orderDetail.value.created_at,
      updated_at: orderDetail.value.updated_at
    }

    showAfterSalesDialog.value = true
  }

  // 🔥 新增：售后申请成功回调
  const handleAfterSalesSuccess = () => {
    showAfterSalesDialog.value = false
    orderForAfterSales.value = null
    ElMessage.success('售后申请已提交，我们将尽快为您处理')
  }

  // 🔥 新增：检查是否有揽件员信息
  const hasCourierInfo = (order: any) => {
    if (!order) return false
    return order.courier_name || order.courier_phone || order.station_name || order.pickup_code
  }

  // 关闭对话框
  const handleClose = (done: any = null) => {
    orderDetail.value = null
    dialogVisible.value = false
    
    if (done && typeof done === 'function') {
      done()
    }
  }

  // 监听对话框显示状态和订单变化
  watch([dialogVisible, () => props.order], async ([visible, order]) => {
    if (visible && order) {
      await loadOrderStatuses()
      await getOrderDetail()
    }
  })
</script>

<style scoped>
  .order-detail {
    max-height: 600px;
    overflow-y: auto;
  }

  .info-card {
    margin-bottom: 20px;
  }

  .info-card:last-child {
    margin-bottom: 0;
  }

  .info-card .el-card__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  /* 卡片头部样式 */
  .card-header-with-icon {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-icon {
    font-size: 18px;
  }

  .sender-icon {
    color: #409eff;
  }

  .receiver-icon {
    color: #67c23a;
  }

  /* 联系人信息样式 */
  .contact-info {
    padding: 8px 0;
  }

  .contact-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    padding: 8px 0;
    border-bottom: 1px solid #f5f7fa;
  }

  .contact-row:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }

  .contact-icon {
    font-size: 16px;
    color: #909399;
    margin-right: 12px;
    min-width: 16px;
  }

  .contact-label {
    font-weight: 500;
    color: #606266;
    min-width: 40px;
    margin-right: 16px;
    font-size: 14px;
  }

  .contact-value {
    color: #303133;
    font-size: 14px;
    flex: 1;
    word-break: break-all;
  }

  .address-value {
    line-height: 1.5;
    max-width: 300px;
  }

  /* 物流轨迹样式 */
  .track-item {
    padding: 5px 0;
  }

  .track-location {
    font-weight: bold;
    color: #409eff;
    margin-bottom: 5px;
  }

  .track-description {
    color: #606266;
    line-height: 1.5;
  }

  /* 轨迹加载状态样式 */
  .track-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #909399;
    font-size: 14px;
  }

  .track-loading .el-icon {
    margin-right: 8px;
    font-size: 16px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .contact-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .contact-icon {
      margin-right: 8px;
    }

    .contact-label {
      margin-right: 0;
      margin-bottom: 4px;
    }

    .address-value {
      max-width: 100%;
    }
  }

  /* 费用相关样式 */
  .fee-amount {
    font-weight: 600;
  }

  .fee-amount.overweight {
    color: #f56c6c;
  }

  .fee-amount.underweight {
    color: #67c23a;
  }

  .fee-difference {
    font-weight: 600;
    font-size: 14px;
  }

  .fee-difference.positive {
    color: #f56c6c;
  }

  .fee-difference.negative {
    color: #67c23a;
  }

  /* 🔥 新增：揽件员信息样式 */
  .courier-icon {
    color: #409eff;
  }

  .pickup-code-value {
    display: flex;
    align-items: center;
  }

  .pickup-code-value .el-tag {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 16px;
    padding: 8px 12px;
    letter-spacing: 2px;
  }

  /* 🔥 新增：售后服务样式 */
  .after-sales-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  .after-sales-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
  }

  .action-tip {
    color: #909399;
    font-size: 12px;
    line-height: 1.4;
  }

  .after-sales-actions .el-button {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  /* 🔥 新增：失败订单信息样式 */
  .failure-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .failure-stage {
    color: #909399;
    font-size: 12px;
    background: #f5f7fa;
    padding: 2px 8px;
    border-radius: 4px;
  }

  /* 🔥 新增：失败来源标签样式 */
  .failure-source-tag {
    margin-left: 8px;
    font-weight: 500;
  }

  .failure-message {
    background: #fef0f0;
    border: 1px solid #fbc4c4;
    border-radius: 4px;
    padding: 8px 12px;
    color: #f56c6c;
    font-size: 13px;
    line-height: 1.4;
    word-break: break-word;
  }

  /* 响应式处理 */
  @media (max-width: 768px) {
    .after-sales-actions {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .action-tip {
      font-size: 11px;
    }
  }
</style>
