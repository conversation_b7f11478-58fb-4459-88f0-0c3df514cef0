<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建快递订单"
    width="1200px"
    :before-close="handleClose"
    class="create-order-dialog"
  >
    <!-- 左右分栏布局 -->
    <el-row :gutter="24" class="order-form-container">
      <!-- 左侧：寄件人信息 -->
      <el-col :span="12" class="sender-section">
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">📦 寄件人信息</h3>
            <el-button type="primary" size="small" @click="fillRandomData" class="random-btn">
              随机填充测试数据
            </el-button>
          </div>

          <!-- 寄件人地址解析 -->
          <AddressParseInput
            placeholder="请输入寄件人姓名、电话、地址信息..."
            @apply="handleSenderAddressParsed"
            ref="senderParseRef"
          />

          <el-form
            ref="senderFormRef"
            :model="form"
            :rules="senderRules"
            label-width="80px"
            class="sender-form"
          >
            <el-form-item label="姓名" prop="sender_name">
              <el-input
                v-model="form.sender_name"
                placeholder="请输入寄件人姓名"
                :prefix-icon="User"
              />
            </el-form-item>

            <el-form-item label="联系方式" prop="sender_mobile">
              <el-input
                v-model="form.sender_mobile"
                placeholder="请输入手机号或固定电话（如：13800138000 或 010-12345678）"
                :prefix-icon="Phone"
              />
            </el-form-item>

            <el-form-item label="寄件地址" prop="senderAddress">
              <AddressCascader
                v-model="form.senderAddress"
                placeholder="请选择寄件地址"
                @change="onSenderAddressChange"
                ref="senderCascaderRef"
              />
            </el-form-item>

            <el-form-item label="详细地址" prop="sender_address">
              <el-input
                v-model="form.sender_address"
                placeholder="请输入详细地址"
                :prefix-icon="Location"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-col>

      <!-- 右侧：收件人信息 -->
      <el-col :span="12" class="receiver-section">
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">📮 收件人信息</h3>
          </div>

          <!-- 收件人地址解析 -->
          <AddressParseInput
            placeholder="请输入收件人姓名、电话、地址信息..."
            @apply="handleReceiverAddressParsed"
            ref="receiverParseRef"
          />

          <el-form
            ref="receiverFormRef"
            :model="form"
            :rules="receiverRules"
            label-width="80px"
            class="receiver-form"
          >
            <el-form-item label="姓名" prop="receiver_name">
              <el-input
                v-model="form.receiver_name"
                placeholder="请输入收件人姓名"
                :prefix-icon="User"
              />
            </el-form-item>

            <el-form-item label="联系方式" prop="receiver_mobile">
              <el-input
                v-model="form.receiver_mobile"
                placeholder="请输入手机号或固定电话（如：13800138000 或 010-12345678）"
                :prefix-icon="Phone"
              />
            </el-form-item>

            <el-form-item label="收件地址" prop="receiverAddress">
              <AddressCascader
                v-model="form.receiverAddress"
                placeholder="请选择收件地址"
                @change="onReceiverAddressChange"
                ref="receiverCascaderRef"
              />
            </el-form-item>

            <el-form-item label="详细地址" prop="receiver_address">
              <el-input
                v-model="form.receiver_address"
                placeholder="请输入详细地址"
                :prefix-icon="Location"
              />
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>

    <!-- 包裹信息和查价区域 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="packageRules"
      label-width="100px"
      class="package-form"
    >
      <!-- 包裹信息 -->
      <el-divider content-position="left">包裹信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="重量(kg)" prop="weight">
            <el-input-number
              v-model="form.weight"
              :min="0.1"
              :step="0.1"
              :precision="1"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数量" prop="quantity">
            <el-input-number v-model="form.quantity" :min="1" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 🔥 长宽高信息 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="长度(cm)">
            <el-input-number
              v-model="form.length"
              :min="0.1"
              :step="0.1"
              :precision="1"
              style="width: 100%"
              placeholder="可选"
              @change="onDimensionChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="宽度(cm)">
            <el-input-number
              v-model="form.width"
              :min="0.1"
              :step="0.1"
              :precision="1"
              style="width: 100%"
              placeholder="可选"
              @change="onDimensionChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="高度(cm)">
            <el-input-number
              v-model="form.height"
              :min="0.1"
              :step="0.1"
              :precision="1"
              style="width: 100%"
              placeholder="可选"
              @change="onDimensionChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 🔥 计算结果显示（只读） -->
      <el-row v-if="showCalculatedInfo" :gutter="20" class="calculated-info">
        <el-col :span="8">
          <el-form-item label="下单体积">
            <el-input
              :value="calculatedVolumeDisplay"
              readonly
              style="width: 100%"
              placeholder="由长宽高自动计算"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="体积重量">
            <el-input
              :value="calculatedVolumeWeightDisplay"
              readonly
              style="width: 100%"
              placeholder="自动计算"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计费重量">
            <el-input
              :value="calculatedChargedWeightDisplay"
              readonly
              style="width: 100%"
              placeholder="自动计算"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 🔥 体积重量计算提示 -->
      <div v-if="volumeWeightInfo.show" class="volume-weight-info">
        <el-alert
          :title="volumeWeightInfo.title"
          :description="volumeWeightInfo.description"
          :type="volumeWeightInfo.type"
          show-icon
          :closable="false"
        />
      </div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="物品名称" prop="goods_name">
            <el-input v-model="form.goods_name" placeholder="请输入物品名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支付方式" prop="pay_method">
            <el-select v-model="form.pay_method" placeholder="请选择支付方式" style="width: 100%">
              <el-option label="寄付" :value="0" />
              <el-option label="到付" :value="1" />
              <el-option label="月结" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="备注" prop="remark">
        <el-input 
          v-model="form.remark" 
          type="textarea" 
          placeholder="请输入备注信息（可选，如：易碎物品、需要签收等特殊要求）"
          :rows="3"
          :maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <!-- 预约取件时间 -->
      <el-divider content-position="left">预约取件时间</el-divider>

      <!-- 菜鸟裹裹专用预约时间选择器 -->
      <CainiaoPickupTimeSelector
        v-if="selectedPriceItemData && selectedPriceItemData.pickup_time_info && isCainiaoProvider(selectedPriceItemData)"
        v-model="cainiaoPickupTime"
        :pickup-time-info="selectedPriceItemData.pickup_time_info"
        @update:modelValue="handleCainiaoPickupTimeChange"
      />

      <!-- 🔥 快递鸟等其他供应商的预约时间选择器 -->
      <UniversalPickupTimeSelector
        v-else-if="selectedPriceItemData && selectedPriceItemData.pickup_time_info && !isCainiaoProvider(selectedPriceItemData)"
        v-model="universalPickupTime"
        :pickup-time-info="selectedPriceItemData.pickup_time_info"
        @update:modelValue="handleUniversalPickupTimeChange"
      />

      <!-- 通用预约时间选择器（无预约时间信息的供应商） -->
      <PickupTimeSelector
        v-else
        v-model="pickupTimeData"
        @change="handlePickupTimeChange"
      />

      <!-- 查价结果 -->
      <el-divider content-position="left">选择快递公司</el-divider>

      <!-- 🚀 查价按钮组 -->
      <div class="price-query-buttons">
        <el-button
          type="primary"
          @click="queryPrice"
          :loading="priceLoading"
          :disabled="!canQueryPrice"
          size="large"
        >
          <Search />
          {{ priceLoading ? '查询中...' : '普通查价' }}
        </el-button>

        <!-- 🚀 实时查价按钮 -->
        <el-button
          type="success"
          @click="queryRealtimePrice"
          :loading="realtimePriceLoading"
          :disabled="!canQueryPrice"
          size="large"
        >
          <Search />
          {{ realtimePriceLoading ? '实时查价中...' : '实时查价' }}
        </el-button>
      </div>

      <div v-if="priceList.length > 0" class="price-list">
        <el-radio-group v-model="selectedPriceItem">
          <div
            v-for="(item, index) in priceList"
            :key="`${item.express_code}-${item.product_code || item.product_name || index}`"
            class="price-item"
          >
            <el-radio :value="item.order_code" class="price-radio">
              <div class="price-content">
                <div class="express-info">
                  <span class="express-name">{{ item.express_name }}</span>
                  <span class="product-type">{{ item.product_name }}</span>
                  <div class="calc-info">
                    计费重量: {{ item.calc_weight }}kg | 续重: ¥{{
                      item.continued_weight_per_kg
                    }}/kg
                  </div>
                </div>
                <div class="price-info">
                  <span class="price">¥{{ item.price.toFixed(2) }}</span>
                  <span class="delivery-time">预计时效</span>
                  <span class="expires-info">有效期: {{ item.expires_at ? new Date(item.expires_at).toLocaleString() : '无限制' }}</span>
                </div>
              </div>
            </el-radio>
          </div>
        </el-radio-group>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          创建订单
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, nextTick } from 'vue'
  import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import { User, Phone, Location } from '@element-plus/icons-vue'
  import { ExpressService } from '@/api'
  import { useUserStore } from '@/store/modules/user'
  import type { SimpleOrderRequest, SimplePriceRequest, PriceItem } from '@/api'
  import type { RealtimePriceRequest, RealtimePriceItem } from '@/api/unifiedGatewayApi'
  import AddressCascader from '@/components/AddressCascader.vue'
  import AddressParseInput from '@/components/AddressParseInput.vue'
  import PickupTimeSelector from '@/components/PickupTimeSelector.vue'
  import CainiaoPickupTimeSelector from '@/components/CainiaoPickupTimeSelector.vue'
  import UniversalPickupTimeSelector from '@/components/UniversalPickupTimeSelector.vue'

  // 随机数据生成工具
  const generateRandomData = () => {
    // 随机姓名
    const surnames = [
      '张',
      '王',
      '李',
      '赵',
      '刘',
      '陈',
      '杨',
      '黄',
      '周',
      '吴',
      '徐',
      '孙',
      '马',
      '朱',
      '胡',
      '林',
      '郭',
      '何',
      '高',
      '罗'
    ]
    const names = [
      '伟',
      '芳',
      '娜',
      '敏',
      '静',
      '丽',
      '强',
      '磊',
      '军',
      '洋',
      '勇',
      '艳',
      '杰',
      '娟',
      '涛',
      '明',
      '超',
      '秀英',
      '霞',
      '平',
      '刚',
      '桂英'
    ]

    const randomName = () => {
      const surname = surnames[Math.floor(Math.random() * surnames.length)]
      const name = names[Math.floor(Math.random() * names.length)]
      return surname + name
    }

    // 随机手机号
    const randomMobile = () => {
      const prefixes = [
        '130',
        '131',
        '132',
        '133',
        '134',
        '135',
        '136',
        '137',
        '138',
        '139',
        '150',
        '151',
        '152',
        '153',
        '155',
        '156',
        '157',
        '158',
        '159',
        '180',
        '181',
        '182',
        '183',
        '184',
        '185',
        '186',
        '187',
        '188',
        '189'
      ]
      const prefix = prefixes[Math.floor(Math.random() * prefixes.length)]
      const suffix = Math.floor(Math.random() * 100000000)
        .toString()
        .padStart(8, '0')
      return prefix + suffix
    }

    // 随机地址
    const provinces = [
      {
        value: '110000',
        label: '北京市',
        cities: [
          {
            value: '110100',
            label: '北京市',
            districts: [
              { value: '110101', label: '东城区' },
              { value: '110102', label: '西城区' },
              { value: '110105', label: '朝阳区' },
              { value: '110106', label: '丰台区' }
            ]
          }
        ]
      },
      {
        value: '120000',
        label: '天津市',
        cities: [
          {
            value: '120100',
            label: '天津市',
            districts: [
              { value: '120101', label: '和平区' },
              { value: '120102', label: '河东区' },
              { value: '120103', label: '河西区' },
              { value: '120104', label: '南开区' }
            ]
          }
        ]
      },
      {
        value: '440000',
        label: '广东省',
        cities: [
          {
            value: '440100',
            label: '广州市',
            districts: [
              { value: '440103', label: '荔湾区' },
              { value: '440104', label: '越秀区' },
              { value: '440105', label: '海珠区' },
              { value: '440106', label: '天河区' }
            ]
          },
          {
            value: '440300',
            label: '深圳市',
            districts: [
              { value: '440303', label: '罗湖区' },
              { value: '440304', label: '福田区' },
              { value: '440305', label: '南山区' },
              { value: '440306', label: '宝安区' }
            ]
          }
        ]
      },
      {
        value: '310000',
        label: '上海市',
        cities: [
          {
            value: '310100',
            label: '上海市',
            districts: [
              { value: '310101', label: '黄浦区' },
              { value: '310104', label: '徐汇区' },
              { value: '310105', label: '长宁区' },
              { value: '310106', label: '静安区' }
            ]
          }
        ]
      }
    ]

    const randomAddress = () => {
      const province = provinces[Math.floor(Math.random() * provinces.length)]

      // 🔥 修复：安全地访问城市和区县数据
      if (!province.cities || province.cities.length === 0) {
        console.error('Province has no cities:', province)
        return {
          address: [province.value],
          province: province.label,
          city: '',
          district: ''
        }
      }

      const city = province.cities[Math.floor(Math.random() * province.cities.length)]

      if (!city.districts || city.districts.length === 0) {
        console.error('City has no districts:', city)
        return {
          address: [province.value, city.value],
          province: province.label,
          city: city.label,
          district: ''
        }
      }

      const district = city.districts[Math.floor(Math.random() * city.districts.length)]

      return {
        address: [province.value, city.value, district.value],
        province: province.label,
        city: city.label,
        district: district.label
      }
    }

    // 随机详细地址
    const streets = [
      '中山路',
      '解放路',
      '人民路',
      '建设路',
      '新华路',
      '光明路',
      '胜利路',
      '东风路',
      '西湖路',
      '南京路'
    ]
    const buildings = [
      '大厦',
      '广场',
      '中心',
      '花园',
      '小区',
      '公寓',
      '写字楼',
      '商务中心',
      '科技园',
      '工业园'
    ]
    const randomDetailAddress = () => {
      const street = streets[Math.floor(Math.random() * streets.length)]
      const number = Math.floor(Math.random() * 999) + 1
      const building = buildings[Math.floor(Math.random() * buildings.length)]
      const unit = Math.floor(Math.random() * 20) + 1
      const room = Math.floor(Math.random() * 50) + 101
      return `${street}${number}号${building}${unit}单元${room}室`
    }

    return {
      randomName,
      randomMobile,
      randomAddress,
      randomDetailAddress
    }
  }

  // Props
  interface Props {
    visible: boolean
    orderCode?: string
  }

  // Emits
  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'success'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    visible: false,
    orderCode: ''
  })
  const emit = defineEmits<Emits>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const senderFormRef = ref<FormInstance>()
  const receiverFormRef = ref<FormInstance>()
  const senderParseRef = ref()
  const receiverParseRef = ref()
  const senderCascaderRef = ref()
  const receiverCascaderRef = ref()
  const submitLoading = ref(false)
  const priceLoading = ref(false)
  const priceList = ref<PriceItem[]>([])
  const selectedPriceItem = ref('')

  // 🚀 实时查价相关
  const realtimePriceLoading = ref(false)
  const isRealtimePrice = ref(false) // 标记当前价格是否来自实时查价接口

  // 预约时间数据
  const pickupTimeData = ref({
    pickup_start_time: '',
    pickup_end_time: ''
  })

  // 🔥 菜鸟裹裹专用预约时间数据
  const cainiaoPickupTime = ref<{
    startTime: string
    endTime: string
  } | undefined>(undefined)

  // 🔥 通用预约时间数据（快递鸟等其他供应商）
  const universalPickupTime = ref<{
    startTime: string
    endTime: string
  } | undefined>(undefined)

  // 🔥 体积重量计算相关
  const volumeWeightInfo = ref({
    show: false,
    title: '',
    description: '',
    type: 'info' as 'success' | 'warning' | 'info' | 'error'
  })

  // 🔥 计算属性：是否显示计算信息
  const showCalculatedInfo = computed(() => {
    return form.length && form.width && form.height
  })

  // 🔥 计算属性：获取选中价格项目的完整数据
  const selectedPriceItemData = computed(() => {
    if (!selectedPriceItem.value) return null
    return priceList.value.find(item => item.order_code === selectedPriceItem.value)
  })

  // 🔥 计算属性：计算体积显示（与易达系统格式保持一致）
  const calculatedVolumeDisplay = computed(() => {
    if (!form.length || !form.width || !form.height) return ''
    const volumeCm3 = form.length * form.width * form.height
    return `${volumeCm3.toFixed(2)}cm³`
  })

  // 🔥 计算属性：体积重量显示
  const calculatedVolumeWeightDisplay = computed(() => {
    if (!form.length || !form.width || !form.height) return ''
    const volumeCm3 = form.length * form.width * form.height
    const volumeWeight = volumeCm3 / 8000 // 默认抛比8000
    return `${volumeWeight.toFixed(2)}kg`
  })

  // 🔥 计算属性：计费重量显示
  const calculatedChargedWeightDisplay = computed(() => {
    if (!form.length || !form.width || !form.height) return ''
    const volumeCm3 = form.length * form.width * form.height
    const volumeWeight = volumeCm3 / 8000
    const chargedWeight = Math.max(form.weight || 0, volumeWeight)
    return `${chargedWeight.toFixed(2)}kg`
  })

  // 表单数据
  const form = reactive<
    Partial<SimpleOrderRequest> & {
      senderAddress: string[]
      receiverAddress: string[]
      length?: number
      width?: number
      height?: number
    }
  >({
    sender_name: '',
    sender_mobile: '',
    sender_province: '',
    sender_city: '',
    sender_district: '',
    sender_address: '',
    receiver_name: '',
    receiver_mobile: '',
    receiver_province: '',
    receiver_city: '',
    receiver_district: '',
    receiver_address: '',
    weight: 1,
    volume: undefined,
    length: undefined,
    width: undefined,
    height: undefined,
    quantity: 1,
    goods_name: '物品',
    pay_method: 0,
    remark: '',
    senderAddress: [],
    receiverAddress: [],
    // 预约时间相关字段
    pickup_start_time: '',
    pickup_end_time: ''
  })

  // 🔥 电话号码验证工具函数 - 支持宽松的固定电话格式
  const validatePhoneNumber = (value: string) => {
    if (!value) return { isValid: false, type: null, message: '请输入联系方式' }
    
    // 清理格式字符
    const cleanValue = value.replace(/[\s\-\+\(\)]/g, '')
    
    // 手机号验证：严格11位，1开头，第二位3-9
    const mobileRegex = /^1[3-9]\d{9}$/
    if (mobileRegex.test(cleanValue)) {
      return { isValid: true, type: 'mobile', message: '' }
    }
    
    // 固定电话验证：支持多种格式
    const telPatterns = [
      /^0\d{2,3}[\-\s]?\d{7,8}$/,     // 标准固定电话：010-12345678 或 0755-87654321
      /^400[\-\s]?\d{3}[\-\s]?\d{4}$/, // 400电话
      /^800[\-\s]?\d{3}[\-\s]?\d{4}$/, // 800电话
      /^95\d{3,4}$/,                   // 银行客服：95588
      /^10\d{3}$/,                     // 运营商客服：10086
      /^\d{7,8}$/                      // 🔥 新增：支持无区号的固定电话 (7-8位数字)
    ]
    
    for (const pattern of telPatterns) {
      if (pattern.test(value)) {
        return { isValid: true, type: 'tel', message: '' }
      }
    }
    
    return { 
      isValid: false, 
      type: null, 
      message: '请输入正确格式：手机号(13800138000) 或 固定电话(010-12345678 或 28698723)' 
    }
  }

  // 寄件人表单验证规则
  const senderRules: FormRules = {
    sender_name: [{ required: true, message: '请输入寄件人姓名', trigger: 'blur' }],
    sender_mobile: [
      { 
        required: true, 
        message: '请输入联系方式', 
        trigger: 'blur' 
      },
      { 
        validator: (rule, value, callback) => {
          if (value) {
            const validation = validatePhoneNumber(value)
            if (!validation.isValid) {
              callback(new Error(validation.message))
            } else {
              callback()
            }
          } else {
            callback()
          }
        }, 
        trigger: 'blur' 
      }
    ],
    senderAddress: [
      {
        required: true,
        message: '请选择寄件地址',
        trigger: 'change',
        validator: (rule, value, callback) => {
          if (!value || value.length === 0) {
            callback(new Error('请选择寄件地址'))
          } else {
            callback()
          }
        }
      }
    ],
    sender_address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }]
  }

  // 收件人表单验证规则
  const receiverRules: FormRules = {
    receiver_name: [{ required: true, message: '请输入收件人姓名', trigger: 'blur' }],
    receiver_mobile: [
      { 
        required: true, 
        message: '请输入联系方式', 
        trigger: 'blur' 
      },
      { 
        validator: (rule, value, callback) => {
          if (value) {
            const validation = validatePhoneNumber(value)
            if (!validation.isValid) {
              callback(new Error(validation.message))
            } else {
              callback()
            }
          } else {
            callback()
          }
        }, 
        trigger: 'blur' 
      }
    ],
    receiverAddress: [
      {
        required: true,
        message: '请选择收件地址',
        trigger: 'change',
        validator: (rule, value, callback) => {
          if (!value || value.length === 0) {
            callback(new Error('请选择收件地址'))
          } else {
            callback()
          }
        }
      }
    ],
    receiver_address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }]
  }

  // 包裹信息验证规则
  const packageRules: FormRules = {
    weight: [{ required: true, message: '请输入重量', trigger: 'blur' }],
    goods_name: [{ required: true, message: '请输入物品名称', trigger: 'blur' }]
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  // 🚀 计算属性：是否可以查价
  const canQueryPrice = computed(() => {
    return !!(
      form.sender_province &&
      form.sender_city &&
      form.receiver_province &&
      form.receiver_city &&
      form.weight
    )
  })

  // 随机填充测试数据
  const fillRandomData = () => {
    const { randomName, randomMobile, randomAddress, randomDetailAddress } = generateRandomData()

    // 生成寄件人信息
    const senderAddr = randomAddress()
    form.sender_name = randomName()
    form.sender_mobile = randomMobile()
    form.senderAddress = senderAddr.address
    form.sender_province = senderAddr.province
    form.sender_city = senderAddr.city
    form.sender_district = senderAddr.district
    form.sender_address = randomDetailAddress()

    // 生成收件人信息
    const receiverAddr = randomAddress()
    form.receiver_name = randomName()
    form.receiver_mobile = randomMobile()
    form.receiverAddress = receiverAddr.address
    form.receiver_province = receiverAddr.province
    form.receiver_city = receiverAddr.city
    form.receiver_district = receiverAddr.district
    form.receiver_address = randomDetailAddress()

    // 随机包裹信息
    form.weight = Math.round((Math.random() * 4 + 0.5) * 10) / 10 // 0.5-5.0kg
    form.quantity = 1 // 固定为1件

    // 🔥 新增：随机长宽高
    const dimensionTypes = [
      { length: 20, width: 15, height: 10 }, // 小包裹
      { length: 30, width: 25, height: 20 }, // 中包裹
      { length: 50, width: 40, height: 30 }, // 大包裹
      { length: 60, width: 50, height: 40 } // 超大包裹
    ]
    const randomDimension = dimensionTypes[Math.floor(Math.random() * dimensionTypes.length)]
    form.length = randomDimension.length
    form.width = randomDimension.width
    form.height = randomDimension.height

    const goodsNames = [
      '文件',
      '衣服',
      '书籍',
      '电子产品',
      '食品',
      '礼品',
      '日用品',
      '化妆品',
      '玩具',
      '工艺品'
    ]
    form.goods_name = goodsNames[Math.floor(Math.random() * goodsNames.length)]

    // 🔥 新增：触发体积重量计算
    onDimensionChange()

    // 清除验证错误
    formRef.value?.clearValidate()

    ElMessage.success('已随机填充测试数据')
  }

  // 🔥 长宽高变化处理
  const onDimensionChange = () => {
    // 如果长宽高都有值，自动计算体积并更新提示
    if (form.length && form.width && form.height) {
      // 长宽高单位是cm，体积单位是m³
      const volumeCm3 = form.length * form.width * form.height
      const volumeM3 = volumeCm3 / 1000000 // cm³ 转 m³
      form.volume = Math.round(volumeM3 * 1000000) / 1000000 // 保留6位小数

      // 计算体积重量（使用默认抛比8000）
      const volumeWeight = volumeCm3 / 8000
      const actualWeight = form.weight || 0

      // 显示体积重量信息
      if (volumeWeight > actualWeight) {
        volumeWeightInfo.value = {
          show: true,
          title: '体积重量计费',
          description: `包裹体积较大，将按体积重量 ${volumeWeight.toFixed(2)}kg 计费（实际重量 ${actualWeight.toFixed(2)}kg）`,
          type: 'warning'
        }
      } else {
        volumeWeightInfo.value = {
          show: true,
          title: '实际重量计费',
          description: `包裹体积适中，将按实际重量 ${actualWeight.toFixed(2)}kg 计费（体积重量 ${volumeWeight.toFixed(2)}kg）`,
          type: 'info'
        }
      }
    } else {
      // 如果长宽高不完整，隐藏提示和清除体积
      volumeWeightInfo.value.show = false
      form.volume = undefined
    }
  }

  // 🔥 全新重写：简单直接的寄件人地址解析处理
  const handleSenderAddressParsed = (result: any) => {
    console.log('📍 寄件人地址解析结果:', result)

    // 1. 直接填充基本信息
    if (result.name) form.sender_name = result.name
    if (result.mobile) form.sender_mobile = result.mobile
    if (result.detailAddress) form.sender_address = result.detailAddress

    // 2. 直接填充地区名称
    if (result.provinceName) form.sender_province = result.provinceName
    if (result.cityName) form.sender_city = result.cityName
    if (result.districtName) form.sender_district = result.districtName

    // 3. 简单直接：通过级联选择器设置地区代码
    if (result.provinceName && senderCascaderRef.value) {
      try {
        senderCascaderRef.value.setSelectedValuesByNames(
          result.provinceName,
          result.cityName,
          result.districtName
        )
        console.log('✅ 寄件人地区已设置')
      } catch (error) {
        console.warn('⚠️ 地区设置失败:', error)
      }
    }

    // 4. 清除验证错误并提示
    senderFormRef.value?.clearValidate()
    ElMessage.success('寄件人信息已自动填充')
  }

  // 🔥 全新重写：简单直接的收件人地址解析处理
  const handleReceiverAddressParsed = (result: any) => {
    console.log('📍 收件人地址解析结果:', result)

    // 1. 直接填充基本信息
    if (result.name) form.receiver_name = result.name
    if (result.mobile) form.receiver_mobile = result.mobile
    if (result.detailAddress) form.receiver_address = result.detailAddress

    // 2. 直接填充地区名称
    if (result.provinceName) form.receiver_province = result.provinceName
    if (result.cityName) form.receiver_city = result.cityName
    if (result.districtName) form.receiver_district = result.districtName

    // 3. 简单直接：通过级联选择器设置地区代码
    if (result.provinceName && receiverCascaderRef.value) {
      try {
        receiverCascaderRef.value.setSelectedValuesByNames(
          result.provinceName,
          result.cityName,
          result.districtName
        )
        console.log('✅ 收件人地区已设置')
      } catch (error) {
        console.warn('⚠️ 地区设置失败:', error)
      }
    }

    // 4. 清除验证错误并提示
    receiverFormRef.value?.clearValidate()
    ElMessage.success('收件人信息已自动填充')
  }

  // 地址选择处理
  const onSenderAddressChange = (values: any, selectedData: any[]) => {
    if (selectedData.length >= 1) {
      form.sender_province = selectedData[0]?.label || ''
    }
    if (selectedData.length >= 2) {
      form.sender_city = selectedData[1]?.label || ''
    }
    if (selectedData.length >= 3) {
      form.sender_district = selectedData[2]?.label || ''
    }
    // 触发表单验证
    senderFormRef.value?.validateField('senderAddress')
  }

  const onReceiverAddressChange = (values: any, selectedData: any[]) => {
    if (selectedData.length >= 1) {
      form.receiver_province = selectedData[0]?.label || ''
    }
    if (selectedData.length >= 2) {
      form.receiver_city = selectedData[1]?.label || ''
    }
    if (selectedData.length >= 3) {
      form.receiver_district = selectedData[2]?.label || ''
    }
    // 触发表单验证
    receiverFormRef.value?.validateField('receiverAddress')
  }

  // 查询价格
  const queryPrice = async () => {
    // 验证必要字段
    if (
      !form.sender_province ||
      !form.sender_city ||
      !form.receiver_province ||
      !form.receiver_city ||
      !form.weight
    ) {
      ElMessage.warning('请先填写完整的寄收件地址和重量信息')
      return
    }

    priceLoading.value = true
    isRealtimePrice.value = false // 重置实时查价标记

    try {
      // 转换为标准价格查询接口格式
      const priceRequest: SimplePriceRequest = {
        from_province: form.sender_province,
        from_city: form.sender_city,
        from_district: form.sender_district || '',
        to_province: form.receiver_province,
        to_city: form.receiver_city,
        to_district: form.receiver_district || '',
        weight: form.weight,
        volume: form.volume,
        length: form.length,
        width: form.width,
        height: form.height,
        quantity: form.quantity || 1,
        goods_name: form.goods_name || '物品',
        pay_method: form.pay_method || 0
      }

      console.log('创建订单页面发送价格查询请求:', priceRequest)

      const response = await ExpressService.queryPrice(priceRequest)

      console.log('创建订单页面价格查询响应:', response)

      // 处理嵌套的数据结构
      let priceData: PriceItem[] = []
      if (response.success && response.data) {
        // 检查是否存在嵌套的数据结构
        const responseData = response.data as any
        if (responseData && typeof responseData === 'object' && 'data' in responseData && Array.isArray(responseData.data)) {
          priceData = responseData.data
        } else if (Array.isArray(responseData)) {
          priceData = responseData
        } else {
          priceData = []
        }

        priceList.value = priceData
        selectedPriceItem.value = ''
        if (priceData.length === 0) {
          ElMessage.warning('未找到符合条件的快递服务')
        } else {
          ElMessage.success(`查询成功，找到 ${priceData.length} 个报价`)
        }
      } else {
        ElMessage.error(response.message || '查询失败')
        priceList.value = []
      }
    } catch (error) {
      console.error('查询价格失败:', error)
      ElMessage.error('查询价格失败，请检查网络连接')
      priceList.value = []
    } finally {
      priceLoading.value = false
    }
  }

  // 🚀 实时查价
  const queryRealtimePrice = async () => {
    // 验证必要字段
    if (
      !form.sender_name ||
      !form.sender_mobile ||
      !form.sender_province ||
      !form.sender_city ||
      !form.sender_district ||
      !form.sender_address ||
      !form.receiver_name ||
      !form.receiver_mobile ||
      !form.receiver_province ||
      !form.receiver_city ||
      !form.receiver_district ||
      !form.receiver_address ||
      !form.weight
    ) {
      ElMessage.warning('实时查价需要填写完整的寄收件人信息，包括姓名、手机号、详细地址等')
      return
    }

    realtimePriceLoading.value = true
    isRealtimePrice.value = true // 设置实时查价标记

    try {
      // 构建实时查价请求
      const realtimePriceRequest: RealtimePriceRequest = {
        sender: {
          name: form.sender_name,
          mobile: form.sender_mobile,
          province: form.sender_province,
          city: form.sender_city,
          district: form.sender_district || '',
          address: form.sender_address
        },
        receiver: {
          name: form.receiver_name,
          mobile: form.receiver_mobile,
          province: form.receiver_province,
          city: form.receiver_city,
          district: form.receiver_district || '',
          address: form.receiver_address
        },
        weight: form.weight,
        length: form.length,
        width: form.width,
        height: form.height,
        volume: form.volume,
        quantity: form.quantity || 1,
        goods_name: form.goods_name || '物品',
        pay_method: form.pay_method || 0
      }

      console.log('创建订单页面发送实时查价请求:', realtimePriceRequest)

      const response = await ExpressService.queryRealtimePrice(realtimePriceRequest)

      console.log('创建订单页面实时查价响应:', response)

      // 处理响应数据
      if (response.success && response.data && Array.isArray(response.data.data)) {
        // 将实时查价数据转换为标准格式
        const realtimePriceData = response.data.data.map((item: RealtimePriceItem) => {
          return {
            express_code: item.express_code,
            express_name: item.express_name,
            price: item.price,
            continued_weight_per_kg: item.continued_weight_per_kg,
            product_code: item.product_code,
            product_name: item.product_name,
            channel_id: item.channel_id,
            estimated_days: item.estimated_days,
            provider: item.provider,
            source: item.source,
            response_time: item.response_time,
            // 🚀 直接使用后端返回的正确订单代码
            order_code: item.order_code,
            calc_weight: item.calc_weight,
            expires_at: item.expires_at,
            // 🔥 新增：预约时间信息
            pickup_time_info: item.pickup_time_info
          } as PriceItem
        })

        priceList.value = realtimePriceData
        selectedPriceItem.value = ''

        if (realtimePriceData.length === 0) {
          ElMessage.warning('未找到符合条件的实时查价服务')
        } else {
          ElMessage.success(`实时查价成功，找到 ${realtimePriceData.length} 个报价`)
        }
      } else {
        ElMessage.error(response.message || '实时查价失败')
        priceList.value = []
      }
    } catch (error) {
      console.error('实时查价失败:', error)
      ElMessage.error('实时查价失败，请检查网络连接')
      priceList.value = []
    } finally {
      realtimePriceLoading.value = false
    }
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 验证所有表单
      const validationPromises = []

      if (senderFormRef.value) {
        validationPromises.push(senderFormRef.value.validate())
      }
      if (receiverFormRef.value) {
        validationPromises.push(receiverFormRef.value.validate())
      }
      if (formRef.value) {
        validationPromises.push(formRef.value.validate())
      }

      await Promise.all(validationPromises)

      if (!selectedPriceItem.value) {
        ElMessage.warning('请先查询价格并选择快递公司')
        return
      }

      submitLoading.value = true

      // 获取用户信息
      const userStore = useUserStore()
      const userInfo = userStore.getUserInfo
      
      if (!userInfo.id) {
        ElMessage.error('用户信息不完整，请重新登录')
        return
      }

      // 找到选中的价格项
      const selectedPrice = priceList.value.find(item => item.order_code === selectedPriceItem.value)
      if (!selectedPrice) {
        ElMessage.error('未找到选中的价格信息，请重新选择快递公司')
        return
      }

      // 构建订单数据
      const orderData: SimpleOrderRequest = {
        ...(form as SimpleOrderRequest),
        user_id: String(userInfo.id), // 添加用户ID，确保是字符串类型
        order_code: selectedPriceItem.value || props.orderCode || '',
        expected_price: selectedPrice.price, // 添加期望价格
        length: form.length,
        width: form.width,
        height: form.height
      }

      console.log('🔥 创建订单数据:', orderData)

      const response: any = await ExpressService.createOrder(orderData)

      if (response.success) {
        const orderInfo: any = response.data ?? response
        const orderNo = orderInfo.order_no || '未知'
        const waybillNo = orderInfo.waybill_no || orderInfo.tracking_no || '未知'
        ElMessage.success(`订单创建成功！订单号：${orderNo}，运单号：${waybillNo}`)
        emit('success')
        handleClose()
      } else {
        console.error('🚨 创建订单失败 - 响应:', response)

        // 特殊处理价格变动的情况
        if (response.code === 40008 || response.message?.includes('价格已变动') || response.message?.includes('价格验证失败')) {
          ElMessageBox.confirm(
            '价格已发生变动，请重新查询最新价格后再下单。',
            '价格变动提醒',
            {
              confirmButtonText: '我知道了',
              cancelButtonText: '取消',
              type: 'warning',
            }
          ).then(() => {
            handleClose()
          }).catch(() => {
            // 用户选择取消，不做任何操作
          })
        } else if (response.message?.includes('运力异常') || response.message?.includes('网点暂时无法提供服务')) {
          ElMessageBox.confirm(
            '当前快递网点暂时无法提供服务，请稍后重试或选择其他快递公司。',
            '服务暂时不可用',
            {
              confirmButtonText: '我知道了',
              cancelButtonText: '取消',
              type: 'warning',
            }
          ).then(() => {
            handleClose()
          }).catch(() => {
            // 用户选择取消，不做任何操作
          })
        } else {
          ElMessage.error(response.message || '创建订单失败')
        }
      }
    } catch (error) {
      console.error('🚨 创建订单异常:', error)
      ElMessage.error('创建订单失败，请检查网络连接')
    } finally {
      submitLoading.value = false
    }
  }

  // 处理预约时间变化
  const handlePickupTimeChange = (value: any) => {
    // 更新表单数据
    form.pickup_start_time = value.pickup_start_time
    form.pickup_end_time = value.pickup_end_time
  }

  // 🔥 处理菜鸟预约时间变化
  const handleCainiaoPickupTimeChange = (value: { startTime: string; endTime: string } | undefined) => {
    if (value) {
      // 更新表单数据
      form.pickup_start_time = value.startTime
      form.pickup_end_time = value.endTime

      console.log('菜鸟预约时间已选择:', {
        startTime: value.startTime,
        endTime: value.endTime
      })
    } else {
      // 清空预约时间
      form.pickup_start_time = ''
      form.pickup_end_time = ''
    }
  }

  // 🔥 处理通用预约时间变化（快递鸟等其他供应商）
  const handleUniversalPickupTimeChange = (value: { startTime: string; endTime: string } | undefined) => {
    if (value) {
      // 更新表单数据
      form.pickup_start_time = value.startTime
      form.pickup_end_time = value.endTime

      console.log('通用预约时间已选择:', {
        startTime: value.startTime,
        endTime: value.endTime,
        provider: selectedPriceItemData.value?.express_code || 'unknown'
      })
    } else {
      // 清空预约时间
      form.pickup_start_time = ''
      form.pickup_end_time = ''
    }
  }

  // 🔥 判断是否为菜鸟供应商
  const isCainiaoProvider = (priceItem: any) => {
    // 通过多种方式判断是否为菜鸟供应商
    return priceItem.express_code === 'CAINIAO' ||
           priceItem.provider === 'cainiao' ||
           priceItem.express_name?.includes('菜鸟') ||
           priceItem.product_name?.includes('裹裹')
  }

  // 关闭对话框
  const handleClose = () => {
    // 重置所有表单
    formRef.value?.resetFields()
    senderFormRef.value?.resetFields()
    receiverFormRef.value?.resetFields()

    // 清除地址解析结果
    senderParseRef.value?.clearResult()
    receiverParseRef.value?.clearResult()

    // 重置表单数据
    Object.assign(form, {
      sender_name: '',
      sender_mobile: '',
      sender_tel: '', // 🔥 新增：重置寄件人固定电话
      sender_province: '',
      sender_city: '',
      sender_district: '',
      sender_address: '',
      receiver_name: '',
      receiver_mobile: '',
      receiver_tel: '', // 🔥 新增：重置收件人固定电话
      receiver_province: '',
      receiver_city: '',
      receiver_district: '',
      receiver_address: '',
      weight: 1,
      volume: undefined,
      length: undefined,
      width: undefined,
      height: undefined,
      quantity: 1,
      goods_name: '物品',
      pay_method: 0,
      remark: '',
      senderAddress: [],
      receiverAddress: [],
      // 重置预约时间字段
      pickup_start_time: '',
      pickup_end_time: ''
    })
    // 重置价格数据和体积重量信息
    priceList.value = []
    selectedPriceItem.value = ''
    volumeWeightInfo.value.show = false

    // 重置预约时间数据
    pickupTimeData.value = {
      pickup_start_time: '',
      pickup_end_time: ''
    }

    // 🔥 重置菜鸟预约时间数据
    cainiaoPickupTime.value = undefined

    // 🔥 重置通用预约时间数据
    universalPickupTime.value = undefined

    emit('update:visible', false)
  }

  // 监听对话框显示状态
  watch(dialogVisible, (visible) => {
    if (!visible) {
      handleClose()
    }
  })
</script>

<style scoped>
  /* 对话框样式 */
  :deep(.create-order-dialog) {
    .el-dialog__body {
      padding: 20px;
      max-height: 80vh;
      overflow-y: auto;
    }
  }

  /* 主容器样式 */
  .order-form-container {
    margin-bottom: 20px;
  }

  /* 左右分栏样式 */
  .sender-section,
  .receiver-section {
    height: 100%;
  }

  .section-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    background: #fafbfc;
    height: 100%;
    min-height: 500px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e4e7ed;
  }

  .section-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #409eff;
  }

  .random-btn {
    font-size: 12px;
  }

  /* 表单样式 */
  .sender-form,
  .receiver-form {
    background: white;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
  }

  .package-form {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    margin-top: 20px;
  }

  /* 🔥 体积重量信息样式 */
  .volume-weight-info {
    margin: 15px 0;
  }

  /* 🔥 计算结果显示样式 */
  .calculated-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    margin: 15px 0;
  }

  .calculated-info .el-form-item {
    margin-bottom: 0;
  }

  .calculated-info .el-input__inner {
    background-color: #f5f7fa;
    border-color: #dcdfe6;
    color: #606266;
    font-weight: 500;
  }

  .price-list {
    margin-top: 20px;
  }

  .price-item {
    margin-bottom: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 10px;
  }

  .price-radio {
    width: 100%;
  }

  .price-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .express-info {
    display: flex;
    flex-direction: column;
  }

  .express-name {
    font-weight: bold;
    font-size: 16px;
  }

  .product-type {
    color: #909399;
    font-size: 12px;
    margin-bottom: 4px;
  }

  .calc-info {
    color: #909399;
    font-size: 11px;
  }

  .price-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .price {
    font-weight: bold;
    font-size: 18px;
    color: #f56c6c;
  }

  .delivery-time {
    color: #909399;
    font-size: 12px;
  }

  .expires-info {
    color: #e6a23c;
    font-size: 11px;
  }

  /* 🚀 查价按钮组样式 */
  .price-query-buttons {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }

  .price-query-buttons .el-button {
    flex: 1;
    min-width: 160px;
    height: 44px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .price-query-buttons .el-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .price-query-buttons .el-button.is-loading {
    transform: none;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .price-query-buttons {
      flex-direction: column;
    }

    .price-query-buttons .el-button {
      flex: none;
      width: 100%;
    }
  }
</style>
