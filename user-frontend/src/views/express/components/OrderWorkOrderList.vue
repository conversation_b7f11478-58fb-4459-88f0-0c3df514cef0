<template>
  <div class="order-work-order-list">
    <el-card v-if="workOrders.length > 0" class="work-order-card">
      <template #header>
        <div class="card-header">
          <span>相关售后工单</span>
          <el-tag :type="getOverallStatusType()" size="small">
            {{ getOverallStatusText() }}
          </el-tag>
        </div>
      </template>

      <div class="work-order-list">
        <div
          v-for="workOrder in workOrders"
          :key="workOrder.id"
          class="work-order-item"
          @click="viewWorkOrder(workOrder)"
        >
          <div class="work-order-header">
            <div class="work-order-info">
              <span class="work-order-id">工单 #{{ workOrder.id }}</span>
              <el-tag :type="getStatusType(workOrder.status)" size="small">
                {{ getStatusText(workOrder.status) }}
              </el-tag>
            </div>
            <div class="work-order-time">
              {{ formatDate(workOrder.created_at) }}
            </div>
          </div>

          <div class="work-order-content">
            <div class="service-type">
              <el-icon><Service /></el-icon>
              {{ getServiceTypeText(workOrder.work_order_type) }}
            </div>
            <div class="work-order-description">
              {{ workOrder.content || '暂无描述' }}
            </div>
          </div>

          <div class="work-order-footer" v-if="workOrder.latest_reply">
            <div class="latest-reply">
              <el-icon><ChatDotRound /></el-icon>
              <span class="reply-text">{{ workOrder.latest_reply }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 无工单状态 -->
    <div v-else class="no-work-orders">
      <el-empty description="暂无相关售后工单" :image-size="60">
        <template #description>
          <span class="empty-description"> 该订单暂无售后工单记录 </span>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Service, ChatDotRound } from '@element-plus/icons-vue'
  import { WorkOrderService } from '@/api'
  import type { WorkOrderListItem } from '@/api/model/workOrderModel'
  import { ErrorHandler } from '@/utils/errorHandler'
  // 🔥 新增：使用统一的工单类型管理
  import { useWorkOrderTypes } from '@/composables/useWorkOrderTypes'

  // Props
  interface Props {
    orderNo: string
    trackingNo?: string
  }

  const props = defineProps<Props>()

  // 🔥 使用统一的工单类型管理
  const { fetchWorkOrderTypes, getTypeName } = useWorkOrderTypes()

  // 响应式数据
  const loading = ref(false)
  const workOrders = ref<WorkOrderListItem[]>([])

  // 🔥 统一的状态映射（隐藏供应商差异）
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    waiting_customer: '等待用户回复',
    resolved: '已解决',
    closed: '已关闭',
    cancelled: '已取消'
  }

  // 获取工单列表
  const getWorkOrders = async () => {
    if (!props.orderNo) return

    loading.value = true
    try {
      const response = await WorkOrderService.getWorkOrderList({
        order_no: props.orderNo,
        tracking_no: props.trackingNo,
        page: 1,
        page_size: 10
      })

      if (response.success && response.data) {
        workOrders.value = response.data.items || []
      }
    } catch (error) {
      ErrorHandler.handleApiError(error, false) // 静默处理错误
    } finally {
      loading.value = false
    }
  }

  // 🔥 使用动态获取的服务类型文本
  const getServiceTypeText = (type: number) => {
    return getTypeName(type)
  }

  // 获取状态文本
  const getStatusText = (status: any) => {
    return statusMap[status as keyof typeof statusMap] || status
  }

  // 获取状态类型
  const getStatusType = (status: any) => {
    const typeMap: Record<string, string> = {
      pending: 'warning',
      processing: 'primary',
      waiting_customer: 'info',
      resolved: 'success',
      closed: 'info',
      cancelled: 'danger'
    }
    return typeMap[status] || 'info'
  }

  // 获取整体状态
  const getOverallStatusType = () => {
    if (workOrders.value.length === 0) return 'info'

    const hasProcessing = workOrders.value.some((wo) => String(wo.status) === 'processing')
    const hasPending = workOrders.value.some((wo) => String(wo.status) === 'pending')
    const hasWaiting = workOrders.value.some((wo) => String(wo.status) === 'waiting_customer')

    if (hasProcessing) return 'primary'
    if (hasPending) return 'warning'
    if (hasWaiting) return 'info'

    return 'success'
  }

  // 获取整体状态文本
  const getOverallStatusText = () => {
    if (workOrders.value.length === 0) return '无工单'

    const hasProcessing = workOrders.value.some((wo) => String(wo.status) === 'processing')
    const hasPending = workOrders.value.some((wo) => String(wo.status) === 'pending')
    const hasWaiting = workOrders.value.some((wo) => String(wo.status) === 'waiting_customer')

    if (hasProcessing) return '处理中'
    if (hasPending) return '待处理'
    if (hasWaiting) return '等待回复'

    return `${workOrders.value.length}个工单`
  }

  // 查看工单详情
  const viewWorkOrder = (workOrder: WorkOrderListItem) => {
    // 这里可以跳转到工单详情页面或打开工单详情对话框
    ElMessage.info('工单详情功能开发中...')
  }

  // 🔥 修复：使用统一的时间格式化工具（北京时间）
  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    try {
      return new Date(dateString).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  // 生命周期
  onMounted(async () => {
    // 🔥 组件挂载时获取工单类型
    await fetchWorkOrderTypes()
  })

  // 监听订单号变化
  watch(
    () => props.orderNo,
    (newOrderNo) => {
      if (newOrderNo) {
        getWorkOrders()
      }
    },
    { immediate: true }
  )
</script>

<style scoped lang="scss">
  .order-work-order-list {
    margin-top: 16px;
  }

  .work-order-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .work-order-list {
    .work-order-item {
      padding: 12px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .work-order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .work-order-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .work-order-id {
      font-weight: 500;
      color: #303133;
    }

    .work-order-time {
      color: #909399;
      font-size: 12px;
    }
  }

  .work-order-content {
    margin-bottom: 8px;

    .service-type {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #606266;
      font-size: 13px;
      margin-bottom: 4px;
    }

    .work-order-description {
      color: #606266;
      font-size: 12px;
      line-height: 1.4;
      max-height: 40px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

  .work-order-footer {
    .latest-reply {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #909399;
      font-size: 12px;

      .reply-text {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .no-work-orders {
    .empty-description {
      color: #909399;
      font-size: 13px;
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .work-order-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .work-order-info {
      width: 100%;
      justify-content: space-between;
    }
  }
</style>
