<template>
  <div class="balance-management">
    <!-- 🎨 简洁的余额概览 -->
    <div class="balance-overview-section">
      <el-row :gutter="20">
        <!-- 主余额卡片 -->
        <el-col :xs="24" :sm="8">
          <el-card class="balance-card primary-card" shadow="hover">
            <div class="balance-content">
              <div class="balance-header">
                <el-icon class="balance-icon"><Wallet /></el-icon>
                <span class="balance-label">可用余额</span>
              </div>
              <div class="balance-value">¥{{ formatBalance(balanceInfo?.available_balance) }}</div>
            </div>
          </el-card>
        </el-col>

        <!-- 今日支出卡片 -->
        <el-col :xs="24" :sm="8">
          <el-card class="balance-card expense-card" shadow="hover">
            <div class="balance-content">
              <div class="balance-header">
                <el-icon class="balance-icon"><ShoppingCart /></el-icon>
                <span class="balance-label">今日支出</span>
              </div>
              <div class="balance-value expense">¥{{ todayExpense }}</div>
              <div class="balance-extra">{{ todayTransactionCount }} 笔交易</div>
            </div>
          </el-card>
        </el-col>

        <!-- 本月充值卡片 -->
        <el-col :xs="24" :sm="8">
          <el-card class="balance-card income-card" shadow="hover">
            <div class="balance-content">
              <div class="balance-header">
                <el-icon class="balance-icon"><Plus /></el-icon>
                <span class="balance-label">本月充值</span>
              </div>
              <div class="balance-value income">¥{{ monthlyDeposit }}</div>
              <div class="balance-extra">{{ monthlyDepositCount }} 笔充值</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 🎨 简化的操作按钮 -->
    <div class="action-section">
      <el-button type="primary" size="large" @click="showDepositDialog = true">
        <el-icon><CreditCard /></el-icon>
        充值
      </el-button>
      <el-button @click="showTransactionChart = !showTransactionChart">
        <el-icon><DataAnalysis /></el-icon>
        {{ showTransactionChart ? '隐藏图表' : '显示图表' }}
      </el-button>
      <el-button @click="refreshAll">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 🎨 余额趋势图表 (可折叠) -->
    <el-card v-show="showTransactionChart" class="chart-section">
      <template #header>
        <div class="chart-header">
          <span>余额变化趋势</span>
          <el-button text @click="showTransactionChart = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      <div ref="chartContainer" class="chart-container"></div>
    </el-card>

    <!-- 🎨 简化的交易历史 -->
    <el-card class="transaction-section">
      <template #header>
        <div class="card-header">
          <span>交易记录</span>
          <div class="header-actions">
            <el-tag type="info">共 {{ pagination.total }} 条</el-tag>
            <el-button @click="refreshTransactions" :loading="transactionLoading" type="primary">
              <el-icon><Refresh /></el-icon>
              {{ transactionLoading ? '刷新中...' : '强制刷新' }}
            </el-button>
          </div>
        </div>
      </template>

      <!-- 🎨 简化的筛选条件 -->
      <div class="filter-section">
        <el-form :model="filterForm" inline class="filter-form">
          <el-form-item label="客户订单号">
            <el-input
              v-model="filterForm.customer_order_no"
              placeholder="请输入客户订单号"
              clearable
              style="width: 160px"
            />
          </el-form-item>
          <el-form-item label="平台订单号">
            <el-input
              v-model="filterForm.order_no"
              placeholder="请输入平台订单号"
              clearable
              style="width: 160px"
            />
          </el-form-item>
          <el-form-item label="运单号">
            <el-input
              v-model="filterForm.tracking_no"
              placeholder="请输入运单号"
              clearable
              style="width: 160px"
            />
          </el-form-item>
          <el-form-item label="交易类型">
            <el-select
              v-model="filterForm.type"
              placeholder="请选择类型"
              clearable
              style="width: 160px"
            >
              <!-- 充值类 -->
              <el-option label="用户充值" value="user_deposit" />
              <el-option label="管理员充值" value="admin_deposit" />

              <!-- 支付类 -->
              <el-option label="下单预收" value="order_pre_charge" />
              <el-option label="费用差额补收" value="billing_difference" />

              <!-- 退款类 -->
              <el-option label="订单取消退款" value="order_cancel_refund" />
              <el-option label="费用差额退款" value="billing_difference_refund" />

              <!-- 调整类 -->
              <el-option label="调账" value="balance_adjustment" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="filterForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="待处理" value="pending" />
              <el-option label="已完成" value="completed" />
              <el-option label="已失败" value="failed" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">筛选</el-button>
            <el-button @click="handleResetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 🎨 简化的表格 -->
      <el-table :data="transactionList" v-loading="transactionLoading" stripe>
        <el-table-column prop="id" label="交易ID" width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="transaction-id">{{ row.id.substring(0, 8) }}...</span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="交易类型" width="140">
          <template #default="{ row }">
            <el-tag :type="getTransactionTypeTag(row.type)" size="small">
              {{ getTransactionTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="platform_order_no" label="平台订单号" width="140" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.platform_order_no" class="order-no">{{ row.platform_order_no }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="customer_order_no"
          label="客户订单号"
          width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span v-if="row.customer_order_no" class="order-no">{{ row.customer_order_no }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="tracking_no" label="运单号" width="140" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.tracking_no" class="tracking-no">{{ row.tracking_no }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="balance_before" label="变更前余额" width="110" align="right">
          <template #default="{ row }">
            <span class="balance-amount">¥{{ row.balance_before }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="变更金额" width="100" align="right">
          <template #default="{ row }">
            <span :class="getAmountClass(row)">{{ formatAmount(row.amount, row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="balance_after" label="变更后余额" width="110" align="right">
          <template #default="{ row }">
            <span class="balance-amount">¥{{ row.balance_after }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusTypeTag(row.status)" size="small">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span :title="row.detail_description || row.description">
              {{ row.user_friendly_desc || row.detail_description || row.description }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="时间" width="140">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 充值对话框 -->
    <DepositDialog v-model:visible="showDepositDialog" @success="handleTransactionSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    CreditCard,
    Refresh,
    Wallet,
    ShoppingCart,
    Plus,
    TrendCharts,
    Bottom,
    Top,
    DataAnalysis,
    Download,
    Close,
    List,
    Grid,
    Postcard,
    Filter,
    Search,
    RefreshLeft,
    ArrowUp,
    Key,
    Document,
    Tickets,
    Van,
    Clock,
    Right,
    Check
  } from '@element-plus/icons-vue'
  import { BalanceService } from '@/api'
  import type {
    BalanceResponse,
    TransactionResponse,
    TransactionHistoryRequest,
    TransactionHistoryResponse
  } from '@/api'
  import DepositDialog from './components/DepositDialog.vue'

  import * as echarts from 'echarts'

  // 响应式数据
  const balanceLoading = ref(false)
  const transactionLoading = ref(false)
  const showDepositDialog = ref(false)
  const showTransactionChart = ref(false)
  const balanceInfo = ref<BalanceResponse | null>(null)
  const transactionList = ref<TransactionResponse[]>([])
  const dateRange = ref<[string, string] | null>(null)
  const chartContainer = ref<HTMLElement>()
  const chartInstance = ref<echarts.ECharts>()

  // 🎨 新增统计数据
  const todayExpense = ref('0.00')
  const todayTransactionCount = ref(0)
  const monthlyDeposit = ref('0.00')
  const monthlyDepositCount = ref(0)

  // 筛选表单
  const filterForm = reactive({
    customer_order_no: undefined as string | undefined,
    order_no: undefined as string | undefined,
    tracking_no: undefined as string | undefined,
    type: undefined as string | undefined,
    status: undefined as string | undefined
  })

  // 分页信息
  const pagination = reactive({
    page: 1,
    page_size: 20,
    total: 0
  })

  // 🎨 格式化余额显示
  const formatBalance = (balance: string | number | undefined) => {
    if (!balance) return '0.00'
    const num = typeof balance === 'string' ? parseFloat(balance) : balance
    return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
  }

  // 🎨 获取相对时间
  const getRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffMinutes = Math.floor(diffMs / (1000 * 60))

    if (diffMinutes < 1) return '刚刚'
    if (diffMinutes < 60) return `${diffMinutes}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays === 1) return '昨天'
    if (diffDays < 7) return `${diffDays}天前`
    return ''
  }

  // 获取余额信息
  const getBalanceInfo = async () => {
    balanceLoading.value = true
    try {
      const response = await BalanceService.getBalance()
      if (response.success && response.data) {
        balanceInfo.value = response.data
        // 🎨 计算统计数据
        await calculateStatistics()
      }
    } catch (error) {
      console.error('获取余额信息失败:', error)
      ElMessage.error('获取余额信息失败')
    } finally {
      balanceLoading.value = false
    }
  }

  // 🎨 计算统计数据
  const calculateStatistics = async () => {
    try {
      // 获取今日交易数据
      const today = new Date()
      const todayStart = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate()
      ).toISOString()
      const todayEnd = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate() + 1
      ).toISOString()

      const todayResponse = await BalanceService.getTransactionHistoryOptimized({
        start_time: todayStart,
        end_time: todayEnd,
        limit: 1000
      })

      if (todayResponse.success && todayResponse.data?.items) {
        const todayTransactions = todayResponse.data.items
        const paymentTypes = [
          'order_pre_charge',
          'billing_difference',
          'order_intercept_charge',
          'return_charge',
          'order_revive_recharge'
        ]
        const todayExpenseTransactions = todayTransactions.filter((t: any) =>
          paymentTypes.includes(t.type)
        )

        todayExpense.value = todayExpenseTransactions
          .reduce((sum: number, t: any) => sum + parseFloat(t.amount || '0'), 0)
          .toFixed(2)
        todayTransactionCount.value = todayExpenseTransactions.length
      }

      // 获取本月充值数据
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1).toISOString()
      const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 1).toISOString()

      const monthResponse = await BalanceService.getTransactionHistoryOptimized({
        start_time: monthStart,
        end_time: monthEnd,
        type: 'user_deposit',
        limit: 1000
      })

      if (monthResponse.success && monthResponse.data?.items) {
        const monthDeposits = monthResponse.data.items
        monthlyDeposit.value = monthDeposits
          .reduce((sum: number, t: any) => sum + parseFloat(t.amount || '0'), 0)
          .toFixed(2)
        monthlyDepositCount.value = monthDeposits.length
      }
    } catch (error) {
      console.error('计算统计数据失败:', error)
    }
  }

  // 🔥 增强：获取交易历史（强制刷新）
  const getTransactionHistory = async () => {
    transactionLoading.value = true
    try {
      // 后端使用 limit/offset 参数，需要转换
      const offset = (pagination.page - 1) * pagination.page_size
      const params = {
        limit: pagination.page_size,
        offset: offset,
        customer_order_no: filterForm.customer_order_no,
        order_no: filterForm.order_no,
        tracking_no: filterForm.tracking_no,
        type: filterForm.type,
        status: filterForm.status,
        start_time: dateRange.value?.[0],
        end_time: dateRange.value?.[1],
        // 🔥 强制刷新：添加时间戳参数
        _t: Date.now(),
        // 🔥 强制刷新：添加随机数防止缓存
        _r: Math.random().toString(36).substring(7)
      }

      console.log('🔍 获取交易历史参数:', params)
      const response = await BalanceService.getTransactionHistoryOptimized(params)
      console.log('📊 交易历史响应:', response)

      // 检查响应格式
      if (response.success && response.data) {
        // 如果后端返回标准格式
        if (response.data.items) {
          transactionList.value = response.data.items
          pagination.total = response.data.total
          console.log(`✅ 交易记录加载成功: ${response.data.items.length} 条记录，总计 ${response.data.total} 条`)

          // 🔥 检查是否包含退款记录
          const refundRecords = response.data.items.filter((item: any) =>
            item.type === 'order_cancel_refund' ||
            item.transaction_category === 'REFUND'
          )
          if (refundRecords.length > 0) {
            console.log(`💰 发现 ${refundRecords.length} 条退款记录:`, refundRecords)
          }
        } else {
          transactionList.value = []
          pagination.total = 0
          console.warn('⚠️ 响应数据格式异常: 缺少 items 字段')
        }
      } else if (response.items) {
        // 如果后端直接返回分页对象
        transactionList.value = response.items
        pagination.total = response.total
        console.log(`✅ 交易记录加载成功 (直接格式): ${response.items.length} 条记录`)
      } else {
        transactionList.value = []
        pagination.total = 0
        console.error('❌ 响应数据格式异常:', response)
      }
    } catch (error) {
      console.error('获取交易历史失败:', error)
      ElMessage.error('获取交易历史失败')
    } finally {
      transactionLoading.value = false
    }
  }

  // 🔥 增强：强制刷新交易记录（清除所有缓存）
  const refreshTransactions = async () => {
    try {
      // 1. 清除浏览器缓存
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        )
      }

      // 2. 强制重新获取数据
      await getTransactionHistory()

      // 3. 显示刷新成功消息
      ElMessage.success('交易记录已刷新')
    } catch (error) {
      console.error('刷新交易记录失败:', error)
      ElMessage.error('刷新失败，请重试')
    }
  }

  // 筛选处理
  const handleFilter = () => {
    pagination.page = 1
    getTransactionHistory()
  }

  const handleResetFilter = () => {
    filterForm.customer_order_no = undefined
    filterForm.order_no = undefined
    filterForm.tracking_no = undefined
    filterForm.type = undefined
    filterForm.status = undefined
    dateRange.value = null
    pagination.page = 1
    getTransactionHistory()
  }

  // 分页处理
  const handleSizeChange = (size: number) => {
    pagination.page_size = size
    pagination.page = 1
    getTransactionHistory()
  }

  const handleCurrentChange = (page: number) => {
    pagination.page = page
    getTransactionHistory()
  }

  // 交易成功回调
  const handleTransactionSuccess = () => {
    getBalanceInfo()
    getTransactionHistory()
  }

  // 🎨 新增功能方法
  const refreshAll = () => {
    getBalanceInfo()
    getTransactionHistory()
    ElMessage.success('数据已刷新')
  }

  // 🎨 初始化图表
  const initChart = () => {
    if (!chartContainer.value) return

    chartInstance.value = echarts.init(chartContainer.value)

    // 模拟数据 - 实际项目中应该从API获取
    const option = {
      title: {
        text: '近30天余额变化',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0]
          return `${data.name}<br/>余额: ¥${data.value}`
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: Array.from({ length: 30 }, (_, i) => {
          const date = new Date()
          date.setDate(date.getDate() - 29 + i)
          return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
        })
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '¥{value}'
        }
      },
      series: [
        {
          name: '余额',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            color: '#409EFF',
            width: 3
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.05)'
                }
              ]
            }
          },
          data: Array.from({ length: 30 }, () => Math.floor(Math.random() * 1000) + 500)
        }
      ]
    }

    chartInstance.value.setOption(option)
  }

  // 🎨 监听图表显示状态
  watch(showTransactionChart, (show) => {
    if (show) {
      nextTick(() => {
        initChart()
      })
    }
  })

  // 🔥 彻底重构：9种核心交易类型标签颜色映射
  const getTransactionTypeTag = (type: string) => {
    const typeMap: Record<string, string> = {
      // 充值类 - 绿色
      user_deposit: 'success',
      admin_deposit: 'success',

      // 支付类 - 橙色/警告色
      order_pre_charge: 'warning',
      billing_difference: 'warning',
      order_intercept_charge: 'warning',
      return_charge: 'warning',
      order_revive_recharge: 'warning',

      // 退款类 - 蓝色/信息色
      order_cancel_refund: 'info',
      billing_difference_refund: 'info',

      // 调整类 - 紫色/主要色
      balance_adjustment: 'primary'
    }
    return typeMap[type] || 'info'
  }

  // 🔥 彻底重构：9种核心交易类型名称映射
  const getTransactionTypeName = (type: string) => {
    const nameMap: Record<string, string> = {
      // 充值类
      user_deposit: '用户充值',
      admin_deposit: '管理员充值',

      // 支付类
      order_pre_charge: '下单预收',
      billing_difference: '费用差额补收',
      order_intercept_charge: '订单拦截_补收',
      return_charge: '退回收费',
      order_revive_recharge: '订单复活重计费',

      // 退款类
      order_cancel_refund: '订单取消_退款',
      billing_difference_refund: '费用差额退款',

      // 调整类
      balance_adjustment: '调账_多退少补'
    }
    return nameMap[type] || type
  }

  // 🔥 新增：智能描述显示函数 - 优先级：user_friendly_desc > detail_description > description > type
  const getDisplayDescription = (transaction: any) => {
    // 1. 优先使用用户友好描述
    if (transaction.user_friendly_desc && transaction.user_friendly_desc.trim()) {
      return transaction.user_friendly_desc
    }

    // 2. 其次使用详细描述
    if (transaction.detail_description && transaction.detail_description.trim()) {
      return transaction.detail_description
    }

    // 3. 再次使用基础描述
    if (transaction.description && transaction.description.trim()) {
      return transaction.description
    }

    // 4. 最后使用类型名称
    return getTransactionTypeName(transaction.transaction_type || transaction.type)
  }

  // 🔥 新增：获取交易金额显示文本
  const getAmountDisplayText = (transaction: any) => {
    const amount = Math.abs(transaction.amount || 0)
    const isPositive = (transaction.amount || 0) >= 0
    const prefix = isPositive ? '+' : '-'
    return `${prefix}${amount.toFixed(2)}`
  }

  // 🔥 新增：获取交易时间显示文本
  const getTimeDisplayText = (transaction: any) => {
    if (!transaction.created_at) return ''

    const date = new Date(transaction.created_at)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    } else if (diffDays === 1) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  }

  const getCategoryName = (category: string) => {
    const categoryMap: Record<string, string> = {
      DEPOSIT: '充值',
      PAYMENT: '支付',
      REFUND: '退款',
      ADJUSTMENT: '调整'
    }
    return categoryMap[category] || category
  }

  const getStatusTypeTag = (status: string) => {
    const statusMap: Record<string, string> = {
      pending: 'warning',
      completed: 'success',
      failed: 'danger',
      PENDING: 'warning',
      COMPLETED: 'success',
      FAILED: 'danger'
    }
    return statusMap[status] || 'info'
  }

  const getStatusName = (status: string) => {
    const nameMap: Record<string, string> = {
      pending: '待处理',
      completed: '已完成',
      failed: '已失败',
      PENDING: '待处理',
      COMPLETED: '已完成',
      FAILED: '已失败'
    }
    return nameMap[status] || status
  }

  const getAmountClass = (transaction: any) => {
    // 🔥 正确修复：根据transaction_category判断金额显示样式
    const category = transaction.transaction_category || transaction.category

    // PAYMENT = 扣费 = 红色负数
    if (category === 'PAYMENT') {
      return 'amount-negative'
    }

    // REFUND/DEPOSIT = 加钱 = 绿色正数
    if (category === 'REFUND' || category === 'DEPOSIT') {
      return 'amount-positive'
    }

    // ADJUSTMENT/ADMIN = 根据金额正负判断
    const amount = parseFloat(transaction.amount || '0')
    return amount >= 0 ? 'amount-positive' : 'amount-negative'
  }

  const formatAmount = (amount: string, transaction: any) => {
    // 🔥 正确修复：根据transaction_category判断金额符号
    const category = transaction.transaction_category || transaction.category
    const numAmount = parseFloat(amount || '0')

    // PAYMENT = 扣费 = 显示负数
    if (category === 'PAYMENT') {
      return `-¥${numAmount.toFixed(2)}`
    }

    // REFUND/DEPOSIT = 加钱 = 显示正数
    if (category === 'REFUND' || category === 'DEPOSIT') {
      return `+¥${numAmount.toFixed(2)}`
    }

    // ADJUSTMENT/ADMIN = 根据实际余额变化判断
    // 如果balance_after > balance_before，显示正数；否则显示负数
    const balanceBefore = parseFloat(transaction.balance_before || '0')
    const balanceAfter = parseFloat(transaction.balance_after || '0')
    const actualChange = balanceAfter - balanceBefore

    if (actualChange >= 0) {
      return `+¥${Math.abs(actualChange).toFixed(2)}`
    } else {
      return `-¥${Math.abs(actualChange).toFixed(2)}`
    }
  }

  // 🔥 简化：直接使用标准时间格式化（数据库已是北京时间）
  const formatDate = (dateString: string) => {
    if (!dateString) return '-'
    try {
      return new Date(dateString).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  // 初始化
  onMounted(() => {
    getBalanceInfo()
    getTransactionHistory()
  })
</script>

<style scoped>
  /* 🍎 苹果风格样式 */
  .balance-management {
    padding: 20px;
    background: #f2f2f7;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text',
      'Helvetica Neue', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 🍎 余额概览区域 - 苹果风格 */
  .balance-overview-section {
    margin-bottom: 24px;
  }

  .balance-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    border: 0.5px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.05),
      0 4px 16px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
  }

  .balance-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  }

  .balance-card:hover {
    transform: translateY(-2px);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.08),
      0 8px 32px rgba(0, 0, 0, 0.12);
  }

  .balance-card.primary-card {
    background: rgba(0, 122, 255, 0.05);
    border: 0.5px solid rgba(0, 122, 255, 0.1);
  }

  .balance-card.expense-card {
    background: rgba(255, 59, 48, 0.05);
    border: 0.5px solid rgba(255, 59, 48, 0.1);
  }

  .balance-card.income-card {
    background: rgba(52, 199, 89, 0.05);
    border: 0.5px solid rgba(52, 199, 89, 0.1);
  }

  .balance-content {
    padding: 24px 20px;
    position: relative;
  }

  .balance-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .balance-icon {
    margin-right: 12px;
    font-size: 20px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: rgba(0, 122, 255, 0.1);
    color: #007aff;
    transition: all 0.2s ease;
  }

  .balance-card.expense-card .balance-icon {
    background: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
  }

  .balance-card.income-card .balance-icon {
    background: rgba(52, 199, 89, 0.1);
    color: #34c759;
  }

  .balance-label {
    font-size: 13px;
    color: #8e8e93;
    font-weight: 600;
    letter-spacing: -0.08px;
    text-transform: uppercase;
  }

  .balance-value {
    font-size: 28px;
    font-weight: 700;
    color: #1d1d1f;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
    font-variant-numeric: tabular-nums;
  }

  .balance-value.expense {
    color: #ff3b30;
  }

  .balance-value.income {
    color: #34c759;
  }

  .balance-extra {
    font-size: 12px;
    color: #8e8e93;
    font-weight: 400;
    letter-spacing: -0.06px;
  }

  /* 🍎 操作按钮区域 - 苹果风格 */
  .action-section {
    text-align: center;
    margin-bottom: 24px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    border: 0.5px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .action-section :deep(.el-button) {
    margin: 0 8px 8px 8px;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: -0.08px;
    border: none;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    color: #1d1d1f;
  }

  .action-section :deep(.el-button:hover) {
    transform: translateY(-1px);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.9);
  }

  .action-section :deep(.el-button:active) {
    transform: translateY(0);
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  .action-section :deep(.el-button--primary) {
    background: #007aff;
    color: white;
    box-shadow:
      0 2px 8px rgba(0, 122, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .action-section :deep(.el-button--primary:hover) {
    background: #0056cc;
    box-shadow:
      0 4px 12px rgba(0, 122, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* 🍎 图表区域 - 苹果风格 */
  .chart-section {
    margin-bottom: 24px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 12px;
    border: 0.5px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.05),
      0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }

  .chart-section :deep(.el-card__header) {
    background: rgba(248, 248, 248, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.05);
    padding: 16px 20px;
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 17px;
    color: #1d1d1f;
    letter-spacing: -0.24px;
  }

  .chart-container {
    height: 300px;
    width: 100%;
    padding: 20px;
  }

  /* 🍎 交易历史区域 - 苹果风格 */
  .transaction-section {
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 0.5px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.05),
      0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
  }

  .transaction-section :deep(.el-card__header) {
    background: rgba(248, 248, 248, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.05);
    padding: 16px 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 17px;
    color: #1d1d1f;
    letter-spacing: -0.24px;
  }

  .header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  /* 🍎 筛选区域 - 苹果风格 */
  .filter-section {
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(248, 248, 248, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 0.5px solid rgba(0, 0, 0, 0.05);
  }

  .filter-form .el-form-item {
    margin-bottom: 12px;
  }

  .filter-form :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 0.5px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
  }

  .filter-form :deep(.el-input__wrapper:focus-within) {
    border-color: #007aff;
    box-shadow:
      inset 0 1px 2px rgba(0, 0, 0, 0.05),
      0 0 0 3px rgba(0, 122, 255, 0.1);
  }

  .filter-form :deep(.el-select .el-input__wrapper) {
    border-radius: 8px;
    border: 0.5px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  /* 🍎 表格视图样式 - 苹果风格 */
  .modern-table {
    border-radius: 12px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 0.5px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.05),
      0 4px 16px rgba(0, 0, 0, 0.08);
  }

  .modern-table :deep(.el-table) {
    background: transparent;
    font-size: 14px;
  }

  .modern-table :deep(.el-table th) {
    background: rgba(248, 248, 248, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: #8e8e93;
    font-weight: 600;
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.05);
    text-transform: uppercase;
    letter-spacing: -0.08px;
    font-size: 11px;
    padding: 12px 16px;
  }

  .modern-table :deep(.el-table td) {
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.05);
    background: transparent;
    padding: 12px 16px;
  }

  .modern-table :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
    background: rgba(248, 248, 248, 0.3);
  }

  .modern-table :deep(.el-table__body tr:hover td) {
    background: rgba(0, 122, 255, 0.05) !important;
  }

  .transaction-id-cell,
  .order-cell,
  .tracking-cell,
  .time-cell {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .type-cell {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .balance-cell,
  .amount-cell {
    font-family: 'SF Mono', Monaco, 'Menlo', monospace;
    font-weight: 600;
    font-size: 13px;
    padding: 6px 10px;
    border-radius: 6px;
    background: rgba(248, 248, 248, 0.6);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 0.5px solid rgba(0, 0, 0, 0.05);
    font-variant-numeric: tabular-nums;
  }

  .description-cell {
    line-height: 1.4;
    color: #48484a;
    font-weight: 400;
    font-size: 14px;
  }

  .time-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .time-date {
    font-weight: 600;
    color: #1d1d1f;
    font-size: 14px;
    font-variant-numeric: tabular-nums;
  }

  .time-relative {
    font-size: 11px;
    color: #8e8e93;
    font-weight: 400;
  }

  .id-icon,
  .order-icon,
  .tracking-icon,
  .time-icon {
    color: #8e8e93;
    font-size: 14px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background: rgba(248, 248, 248, 0.6);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .tag-icon,
  .status-icon {
    margin-right: 4px;
  }

  .transaction-id,
  .order-no,
  .tracking-no {
    font-family: 'SF Mono', Monaco, 'Menlo', monospace;
    font-size: 12px;
    font-weight: 500;
    padding: 3px 6px;
    border-radius: 4px;
    background: rgba(248, 248, 248, 0.6);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    font-variant-numeric: tabular-nums;
  }

  .order-no {
    color: #007aff;
    background: rgba(0, 122, 255, 0.1);
  }

  .tracking-no {
    color: #48484a;
    background: rgba(72, 72, 74, 0.1);
  }

  .transaction-id {
    color: #8e8e93;
    background: rgba(142, 142, 147, 0.1);
  }

  .no-data {
    color: #c7c7cc;
    font-style: italic;
    font-weight: 400;
  }

  .balance-amount.before {
    color: #8e8e93;
    opacity: 0.8;
  }

  .balance-amount.after {
    color: #007aff;
    font-weight: 600;
  }

  .amount-positive {
    color: #34c759;
    font-weight: 600;
    background: rgba(52, 199, 89, 0.1);
    padding: 3px 6px;
    border-radius: 4px;
    font-variant-numeric: tabular-nums;
  }

  .amount-negative {
    color: #ff3b30;
    font-weight: 600;
    background: rgba(255, 59, 48, 0.1);
    padding: 3px 6px;
    border-radius: 4px;
    font-variant-numeric: tabular-nums;
  }

  /* 🍎 分页样式 - 苹果风格 */
  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
    padding: 16px;
    background: rgba(248, 248, 248, 0.6);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 0.5px solid rgba(0, 0, 0, 0.05);
  }

  .pagination-wrapper :deep(.el-pagination) {
    justify-content: center;
  }

  .pagination-wrapper :deep(.el-pagination .el-pager li) {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 6px;
    margin: 0 2px;
    border: 0.5px solid rgba(0, 0, 0, 0.05);
    color: #1d1d1f;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 32px;
    height: 32px;
    line-height: 30px;
  }

  .pagination-wrapper :deep(.el-pagination .el-pager li:hover) {
    background: rgba(0, 122, 255, 0.1);
    border-color: rgba(0, 122, 255, 0.2);
    color: #007aff;
  }

  .pagination-wrapper :deep(.el-pagination .el-pager li.is-active) {
    background: #007aff;
    color: white;
    border-color: #007aff;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
  }

  .pagination-wrapper :deep(.btn-prev),
  .pagination-wrapper :deep(.btn-next) {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 6px;
    border: 0.5px solid rgba(0, 0, 0, 0.05);
    color: #1d1d1f;
    font-weight: 600;
    min-width: 32px;
    height: 32px;
  }

  .pagination-wrapper :deep(.btn-prev:hover),
  .pagination-wrapper :deep(.btn-next:hover) {
    background: rgba(0, 122, 255, 0.1);
    border-color: rgba(0, 122, 255, 0.2);
    color: #007aff;
  }

  /* 🍎 标签和状态样式 - 苹果风格 */
  :deep(.el-tag) {
    border-radius: 6px;
    border: none;
    font-weight: 600;
    font-size: 12px;
    letter-spacing: -0.08px;
    padding: 4px 8px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  :deep(.el-tag--success) {
    background: rgba(52, 199, 89, 0.15);
    color: #1b5e20;
  }

  :deep(.el-tag--warning) {
    background: rgba(255, 149, 0, 0.15);
    color: #e65100;
  }

  :deep(.el-tag--danger) {
    background: rgba(255, 59, 48, 0.15);
    color: #c62828;
  }

  :deep(.el-tag--info) {
    background: rgba(0, 122, 255, 0.15);
    color: #1565c0;
  }

  /* 🍎 响应式设计 - 苹果风格 */
  @media (max-width: 1200px) {
    .balance-management {
      padding: 16px;
    }

    .balance-content {
      padding: 20px 16px;
    }
  }

  @media (max-width: 768px) {
    .balance-management {
      padding: 12px;
    }

    .balance-value {
      font-size: 24px;
    }

    .balance-content {
      padding: 16px 12px;
    }

    .header-actions {
      flex-direction: column;
      gap: 8px;
    }

    .action-section {
      padding: 16px 12px;
    }

    .action-section :deep(.el-button) {
      margin: 4px 6px;
      width: calc(50% - 12px);
      font-size: 13px;
    }

    .filter-section {
      padding: 16px 12px;
    }

    .filter-form {
      flex-direction: column;
    }

    .filter-form .el-form-item {
      margin-bottom: 10px;
    }

    .chart-container {
      height: 240px;
      padding: 12px;
    }

    .modern-table :deep(.el-table th),
    .modern-table :deep(.el-table td) {
      padding: 8px 12px;
    }
  }

  @media (max-width: 480px) {
    .balance-management {
      padding: 8px;
    }

    .balance-value {
      font-size: 22px;
    }

    .balance-content {
      padding: 12px 8px;
    }

    .action-section :deep(.el-button) {
      width: 100%;
      margin: 3px 0;
      font-size: 14px;
    }

    .pagination-wrapper {
      padding: 12px 8px;
    }

    .pagination-wrapper :deep(.el-pagination) {
      justify-content: center;
      flex-wrap: wrap;
    }

    .pagination-wrapper :deep(.el-pagination .el-pager li) {
      min-width: 28px;
      height: 28px;
      line-height: 26px;
      font-size: 12px;
    }
  }

  /* 🍎 加载状态优化 - 苹果风格 */
  :deep(.el-loading-mask) {
    background: rgba(248, 248, 248, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  :deep(.el-loading-spinner) {
    color: #007aff;
  }

  :deep(.el-loading-text) {
    color: #1d1d1f;
    font-weight: 500;
    font-size: 14px;
  }

  /* 🍎 滚动条样式 - 苹果风格 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }

  /* 🍎 输入框焦点状态 */
  :deep(.el-input__wrapper:focus-within) {
    box-shadow:
      inset 0 1px 2px rgba(0, 0, 0, 0.05),
      0 0 0 3px rgba(0, 122, 255, 0.1);
  }

  /* 🍎 按钮禁用状态 */
  :deep(.el-button:disabled) {
    background: rgba(248, 248, 248, 0.5) !important;
    color: #c7c7cc !important;
    border-color: rgba(0, 0, 0, 0.05) !important;
    cursor: not-allowed;
  }

  /* 🍎 表格空状态 */
  :deep(.el-table__empty-block) {
    background: transparent;
  }

  :deep(.el-table__empty-text) {
    color: #8e8e93;
    font-size: 14px;
    font-weight: 400;
  }

  /* 🍎 卡片悬停效果优化 */
  .balance-card:active {
    transform: translateY(-1px);
    transition: transform 0.1s ease;
  }

  /* 🍎 文本选择样式 */
  ::selection {
    background: rgba(0, 122, 255, 0.2);
    color: #1d1d1f;
  }

  ::-moz-selection {
    background: rgba(0, 122, 255, 0.2);
    color: #1d1d1f;
  }

  /* 🍎 动画优化 */
  * {
    -webkit-tap-highlight-color: transparent;
  }

  .balance-card,
  .action-section :deep(.el-button),
  .modern-table :deep(.el-table__body tr) {
    will-change: transform;
  }
</style>
