<template>
  <el-dialog v-model="dialogVisible" title="账户充值" width="500px" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="deposit-form">
      <el-form-item label="充值金额" prop="amount">
        <el-input
          v-model="form.amount"
          placeholder="请输入充值金额"
          type="number"
          :min="0.01"
          :step="0.01"
        >
          <template #prepend>¥</template>
        </el-input>
      </el-form-item>

      <el-form-item label="支付方式" prop="payment_method">
        <el-radio-group v-model="form.payment_method">
          <el-radio value="alipay">
            <div class="payment-option">
              <el-icon size="20"><Money /></el-icon>
              <span>支付宝</span>
            </div>
          </el-radio>
          <el-radio value="wechatpay">
            <div class="payment-option">
              <el-icon size="20"><ChatDotRound /></el-icon>
              <span>微信支付</span>
            </div>
          </el-radio>
          <el-radio value="bankcard">
            <div class="payment-option">
              <el-icon size="20"><CreditCard /></el-icon>
              <span>银行卡</span>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 快捷金额选择 -->
      <el-form-item label="快捷选择">
        <div class="quick-amounts">
          <el-button
            v-for="amount in quickAmounts"
            :key="amount"
            :type="form.amount === amount.toString() ? 'primary' : 'default'"
            size="small"
            @click="selectQuickAmount(amount)"
          >
            ¥{{ amount }}
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="交易密码" prop="transaction_password" v-if="requirePassword">
        <el-input
          v-model="form.transaction_password"
          type="password"
          placeholder="请输入交易密码"
          show-password
        />
      </el-form-item>
    </el-form>

    <!-- 充值说明 -->
    <el-alert title="充值说明" type="info" :closable="false" show-icon>
      <ul class="deposit-tips">
        <li>充值金额将实时到账，可用于支付快递费用</li>
        <li>单次充值金额不得超过10,000元</li>
        <li>充值成功后，资金将进入您的账户余额</li>
        <li>如有疑问，请联系客服</li>
      </ul>
    </el-alert>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确认充值
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
  import { Money, ChatDotRound, CreditCard } from '@element-plus/icons-vue'
  import { BalanceService } from '@/api'
  import type { DepositRequest } from '@/api'

  // Props
  interface Props {
    visible: boolean
  }

  // Emits
  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'success'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const submitLoading = ref(false)
  const requirePassword = ref(false) // 是否需要交易密码

  // 表单数据
  const form = reactive({
    amount: '',
    payment_method: 'alipay',
    transaction_password: ''
  })

  // 快捷金额选项
  const quickAmounts = [50, 100, 200, 500, 1000, 2000]

  // 表单验证规则
  const rules: FormRules = {
    amount: [
      { required: true, message: '请输入充值金额', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          const amount = parseFloat(value)
          if (isNaN(amount) || amount <= 0) {
            callback(new Error('充值金额必须大于0'))
          } else if (amount > 10000) {
            callback(new Error('单次充值金额不得超过10,000元'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    payment_method: [{ required: true, message: '请选择支付方式', trigger: 'change' }],
    transaction_password: [
      {
        required: true,
        message: '请输入交易密码',
        trigger: 'blur',
        validator: (rule, value, callback) => {
          if (requirePassword.value && !value) {
            callback(new Error('请输入交易密码'))
          } else {
            callback()
          }
        }
      }
    ]
  }

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  // 选择快捷金额
  const selectQuickAmount = (amount: number) => {
    form.amount = amount.toString()
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()

      submitLoading.value = true

      // 生成模拟的交易ID（实际项目中应该由支付系统生成）
      const transactionId = `${form.payment_method.toUpperCase()}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      const depositData: DepositRequest = {
        amount: parseFloat(form.amount).toFixed(2),
        payment_method: form.payment_method,
        transaction_id: transactionId
      }

      const response = await BalanceService.deposit(depositData)

      ElMessage.success('充值成功')
      emit('success')
      handleClose()
    } catch (error) {
      console.error('充值失败:', error)
    } finally {
      submitLoading.value = false
    }
  }

  // 关闭对话框
  const handleClose = () => {
    formRef.value?.resetFields()
    form.amount = ''
    form.payment_method = 'alipay'
    form.transaction_password = ''
    emit('update:visible', false)
  }

  // 监听对话框显示状态
  watch(dialogVisible, (visible) => {
    if (!visible) {
      handleClose()
    }
  })

  // 监听充值金额变化，决定是否需要交易密码
  watch(
    () => form.amount,
    (amount) => {
      const amountNum = parseFloat(amount)
      // 充值金额超过1000元需要输入交易密码
      requirePassword.value = !isNaN(amountNum) && amountNum > 1000
    }
  )
</script>

<style scoped>
  .deposit-form {
    margin-bottom: 20px;
  }

  .payment-option {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .quick-amounts {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .deposit-tips {
    margin: 0;
    padding-left: 20px;
    color: #606266;
    font-size: 14px;
    line-height: 1.6;
  }

  .deposit-tips li {
    margin-bottom: 5px;
  }

  .el-radio {
    margin-bottom: 15px;
    width: 100%;
  }

  .el-radio:last-child {
    margin-bottom: 0;
  }
</style>
