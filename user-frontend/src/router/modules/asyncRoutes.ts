import { RoutesAlias } from './routesAlias'
import { MenuListType } from '@/types/menu'

/**
 * 菜单列表、异步路由
 *
 * 支持两种模式:
 * 1. 前端静态配置 - 直接使用本文件中定义的路由配置
 * 2. 后端动态配置 - 后端返回菜单数据，前端解析生成路由
 *
 * 菜单标题（title）:
 * 可以是 i18n 的 key，也可以是字符串，比如：'用户列表'
 */
export const asyncRoutes: MenuListType[] = [
  {
    id: 1,
    name: 'Dashboard',
    path: '/dashboard',
    component: RoutesAlias.Home,
    meta: {
      title: 'menus.dashboard.title',
      icon: '&#xe721;',
      keepAlive: false
    },
    children: [
      {
        id: 101,
        path: 'console',
        name: 'Console',
        component: RoutesAlias.Dashboard,
        meta: {
          title: 'menus.dashboard.console',
          keepAlive: true
        }
      }
    ]
  },

  {
    id: 5,
    path: '/result',
    name: 'Result',
    component: RoutesAlias.Home,
    meta: {
      title: 'menus.result.title',
      icon: '&#xe679;',
      keepAlive: false,
      isHide: true
    },
    children: [
      {
        id: 501,
        path: 'success',
        name: 'Success',
        component: RoutesAlias.Success,
        meta: {
          title: 'menus.result.success',
          keepAlive: true
        }
      },
      {
        id: 502,
        path: 'fail',
        name: 'Fail',
        component: RoutesAlias.Fail,
        meta: {
          title: 'menus.result.fail',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 6,
    name: 'Exception',
    path: '/exception',
    component: RoutesAlias.Home,
    meta: {
      title: 'menus.exception.title',
      icon: '&#xe649;',
      keepAlive: false,
      isHide: true
    },
    children: [
      {
        id: 601,
        path: '404',
        name: 'Exception404',
        component: RoutesAlias.Exception404,
        meta: {
          title: 'menus.exception.notFound',
          keepAlive: true
        }
      },
      {
        id: 602,
        path: '500',
        name: 'Exception500',
        component: RoutesAlias.Exception500,
        meta: {
          title: 'menus.exception.serverError',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 3,
    name: 'Express',
    path: '/express',
    component: RoutesAlias.Home,
    meta: {
      title: '快递管理',
      icon: '&#xe7a2;',
      keepAlive: false
    },
    children: [
      {
        id: 301,
        path: 'orders',
        name: 'ExpressOrderList',
        component: RoutesAlias.ExpressOrderList,
        meta: {
          title: '订单管理',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 4,
    name: 'Balance',
    path: '/balance',
    component: RoutesAlias.Home,
    meta: {
      title: '余额管理',
      icon: '&#xe7b8;',
      keepAlive: false
    },
    children: [
      {
        id: 401,
        path: 'management',
        name: 'BalanceManagement',
        component: RoutesAlias.BalanceManagement,
        meta: {
          title: '余额管理',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 7,
    name: 'Address',
    path: '/address',
    component: RoutesAlias.Home,
    meta: {
      title: '地址解析',
      icon: '&#xe7a3;',
      keepAlive: false
    },
    children: [
      {
        id: 701,
        path: 'parse',
        name: 'AddressParse',
        component: RoutesAlias.AddressParse,
        meta: {
          title: '智能解析',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 8,
    name: 'AfterSales',
    path: '/after-sales',
    component: RoutesAlias.Home,
    meta: {
      title: '售后服务',
      icon: '&#xe7a4;',
      keepAlive: false
    },
    children: [
      {
        id: 801,
        path: 'list',
        name: 'AfterSalesList',
        component: RoutesAlias.WorkOrderList,
        meta: {
          title: '售后申请',
          keepAlive: true
        }
      },
      {
        id: 802,
        path: 'create',
        name: 'AfterSalesCreate',
        component: RoutesAlias.WorkOrderCreate,
        meta: {
          title: '申请售后',
          keepAlive: false
        }
      },
      {
        id: 803,
        path: 'detail/:id',
        name: 'AfterSalesDetail',
        component: RoutesAlias.WorkOrderDetail,
        meta: {
          title: '售后详情',
          keepAlive: false,
          isHide: true
        }
      }
    ]
  },

  {
    id: 9,
    path: '/system',
    name: 'System',
    component: RoutesAlias.Home,
    meta: {
      title: 'menus.system.title',
      icon: '&#xe7b9;',
      keepAlive: false
    },
    children: [
      {
        id: 904,
        path: 'callback',
        name: 'CallbackList',
        component: RoutesAlias.CallbackList,
        meta: {
          title: 'API & 回调管理',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 10,
    path: '/safeguard',
    name: 'Safeguard',
    component: RoutesAlias.Home,
    meta: {
      title: 'menus.safeguard.title',
      icon: '&#xe816;',
      keepAlive: false,
      isHide: true
    },
    children: [
      {
        id: 1010,
        path: 'server',
        name: 'Server',
        component: RoutesAlias.Server,
        meta: {
          title: 'menus.safeguard.server',
          keepAlive: true
        }
      }
    ]
  }
]
