var e=Object.defineProperty,t=Object.defineProperties,r=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,i=(t,r,s)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[r]=s,n=(e,t,r)=>new Promise(((s,a)=>{var o=e=>{try{n(r.next(e))}catch(t){a(t)}},i=e=>{try{n(r.throw(e))}catch(t){a(t)}},n=e=>e.done?s(e.value):Promise.resolve(e.value).then(o,i);n((r=r.apply(e,t)).next())}));import{M as c,ax as p}from"./vendor-BVh5F9vp.js";const l={401:{type:"AUTH",message:"登录已过期，请重新登录"},403:{type:"PERMISSION",message:"权限不足，无法访问该资源"},NETWORK_ERROR:{type:"NETWORK",message:"网络连接失败，请检查网络设置"},TIMEOUT:{type:"NETWORK",message:"请求超时，请稍后重试"},INSUFFICIENT_BALANCE:{type:"BUSINESS",message:"余额不足，请先充值"},ORDER_NOT_FOUND:{type:"BUSINESS",message:"订单不存在或已被删除"},EXPRESS_COMPANY_NOT_SUPPORTED:{type:"BUSINESS",message:"不支持该快递公司"},INVALID_ADDRESS:{type:"VALIDATION",message:"地址信息不完整或格式错误"},WEIGHT_EXCEEDED:{type:"VALIDATION",message:"包裹重量超出限制"},PRICE_EXPIRED:{type:"BUSINESS",message:"价格已过期，请重新查询"},500:{type:"SYSTEM",message:"服务器内部错误，请稍后重试"},502:{type:"SYSTEM",message:"服务暂时不可用，请稍后重试"},503:{type:"SYSTEM",message:"服务维护中，请稍后重试"}};class E{static handleApiError(e,t=!0){const r=this.parseError(e);return t&&this.showErrorNotification(r),this.logError(r,e),r}static parseError(e){const t=Date.now();if(e.isAxiosError){const r=e;if(!r.response)return{type:"NETWORK",code:"NETWORK_ERROR",message:"网络连接失败，请检查网络设置",timestamp:t};const{status:s,data:a}=r.response,o=(null==a?void 0:a.code)||(null==a?void 0:a.error_code)||s,i=(null==a?void 0:a.message)||(null==a?void 0:a.error)||r.message,n=l[o];return n?{type:n.type,code:o,message:n.message,details:a,timestamp:t}:{type:this.getErrorTypeByStatus(s),code:o,message:i||"请求失败",details:a,timestamp:t}}if(e.code&&l[e.code]){const r=l[e.code];return{type:r.type,code:e.code,message:e.message||r.message,details:e,timestamp:t}}return{type:"UNKNOWN",code:"UNKNOWN",message:e.message||"发生未知错误",details:e,timestamp:t}}static getErrorTypeByStatus(e){return 401===e?"AUTH":403===e?"PERMISSION":e>=400&&e<500?"VALIDATION":e>=500?"SYSTEM":"UNKNOWN"}static showErrorNotification(e){const{type:t,message:r}=e;switch(t){case"AUTH":p({title:"认证失败",message:r,type:"error",duration:5e3});break;case"PERMISSION":case"VALIDATION":c({message:r,type:"warning",duration:3e3});break;case"BUSINESS":c({message:r,type:"error",duration:4e3});break;case"NETWORK":p({title:"网络错误",message:r,type:"error",duration:5e3});break;case"SYSTEM":p({title:"系统错误",message:r,type:"error",duration:6e3});break;default:c({message:r,type:"error",duration:3e3})}}static logError(e,n){const c=(p=((e,t)=>{for(var r in t||(t={}))a.call(t,r)&&i(e,r,t[r]);if(s)for(var r of s(t))o.call(t,r)&&i(e,r,t[r]);return e})({},e),l={userAgent:navigator.userAgent,url:window.location.href,userId:this.getCurrentUserId(),stack:null==n?void 0:n.stack},t(p,r(l)));var p,l;this.sendErrorLog(c).catch((e=>{}))}static getCurrentUserId(){try{const e=localStorage.getItem("userInfo");if(e){return JSON.parse(e).id||null}}catch(e){}return null}static sendErrorLog(e){return n(this,null,(function*(){try{yield fetch("/api/v1/system/error-log",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})}catch(t){}}))}static createRetryFunction(e,t=3,r=1e3){return(...s)=>n(this,null,(function*(){let a;for(let i=0;i<=t;i++)try{return yield e(...s)}catch(o){if(a=o,i===t||!this.isRetryableError(o))throw o;yield new Promise((e=>setTimeout(e,r*Math.pow(2,i))))}throw a}))}static isRetryableError(e){var t;return"NETWORK_ERROR"===e.code||!e.response||((null==(t=e.response)?void 0:t.status)>=500||"TIMEOUT"===e.code)}}export{E};
