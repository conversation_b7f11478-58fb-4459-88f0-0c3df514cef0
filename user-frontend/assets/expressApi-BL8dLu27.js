var r=(r,t,e)=>new Promise(((s,n)=>{var a=r=>{try{u(e.next(r))}catch(t){n(t)}},i=r=>{try{u(e.throw(r))}catch(t){n(t)}},u=r=>r.done?s(r.value):Promise.resolve(r.value).then(a,i);u((e=e.apply(r,t)).next())}));import{f as t}from"./index-F0f2Vd-3.js";class e{static createOrder(e){return r(this,null,(function*(){return t.post({url:"/api/v1/express/order",params:e})}))}static cancelOrder(e){return r(this,null,(function*(){return t.post({url:"/api/v1/express/order/cancel",params:e})}))}static queryOrder(e){return r(this,null,(function*(){return t.post({url:"/api/v1/express/order/query",params:e})}))}static queryTrack(e){return r(this,null,(function*(){return t.post({url:"/api/v1/express/track",params:e})}))}static getOrderList(e){return r(this,null,(function*(){const r=Object.fromEntries(Object.entries(e||{}).filter((([r,t])=>null!=t&&""!==t)));return t.get({url:"/api/v1/express/orders",params:r})}))}static getOrderStatistics(e){return r(this,null,(function*(){return t.get({url:"/api/v1/express/orders/statistics",params:e})}))}static queryPrice(e){return r(this,null,(function*(){return t.post({url:"/api/v1/hybrid/price",params:e})}))}}export{e as E};
