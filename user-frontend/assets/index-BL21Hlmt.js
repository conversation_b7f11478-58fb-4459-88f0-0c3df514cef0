var e=(e,s,a)=>new Promise(((l,o)=>{var r=e=>{try{n(a.next(e))}catch(s){o(s)}},t=e=>{try{n(a.throw(e))}catch(s){o(s)}},n=e=>e.done?l(e.value):Promise.resolve(e.value).then(r,t);n((a=a.apply(e,s)).next())}));import{a as s,b as a,c as l,u as o,L as r,S as t,d as n,A as i,H as d,_ as u}from"./index-F0f2Vd-3.js";import{d as c,r as m,X as p,j as g,c as f,e as v,b as h,x as w,u as y,w as b,ag as _,D as x,Z as k,ah as V,F as P,A as $,aj as D,v as T,a as j,_ as L,C as M,aD as z,K as C,z as H,H as A,ax as K,a1 as U,M as q,o as E}from"./vendor-BVh5F9vp.js";import{L as I}from"./LeftView-CLKZv8wr.js";const N={class:"login"},R={class:"left-wrap"},S={class:"right-wrap"},Z={class:"top-right-wrap"},F={class:"iconfont-sys"},G={class:"menu-txt"},X={key:0,class:"iconfont-sys icon-check"},B={class:"header"},J={class:"login-wrap"},O={class:"form"},Q={class:"title"},W={class:"sub-title"},Y={class:"forget-password"},ee=u(c({__name:"index",setup(u){const{t:c}=s(),ee=a(),se=U(),ae=m(),le=p({username:"",password:"",rememberPassword:!0}),oe=g((()=>({username:[{required:!0,message:c("login.placeholder[0]"),trigger:"blur"}],password:[{required:!0,message:c("login.placeholder[1]"),trigger:"blur"}]}))),re=m(!1);l();const te=o(),ne=g((()=>te.isDark)),ie=()=>e(this,null,(function*(){ae.value&&(yield ae.value.validate((s=>e(this,null,(function*(){if(s){re.value=!0;try{const{status:e,token:s}=yield ee.login(le);e===i.success&&(K({title:c("login.success.title"),message:c("login.success.message"),type:"success"}),se.push(d))}catch(e){q.error(e.message||c("errorMessage.failMessage"))}re.value=!1}})))))})),{locale:de}=s(),ue=e=>{de.value!==e&&(de.value=e,ee.setLanguage(e))},ce=()=>{let{LIGHT:e,DARK:s}=t;n().switchTheme(o().systemThemeType===e?s:e)},me=[{value:r.ZH,label:"简体中文"},{value:r.EN,label:"English"}];return(e,s)=>{const a=D,l=V,o=_,r=M,t=L,n=z,i=H("router-link"),d=A,u=k;return E(),f("div",N,[v("div",R,[h(I)]),v("div",S,[v("div",Z,[v("div",{class:"btn theme-btn",onClick:ce},[v("i",F,w(y(ne)?"":""),1)]),h(o,{onCommand:ue,"popper-class":"langDropDownStyle"},{dropdown:b((()=>[h(l,null,{default:b((()=>[(E(),f(P,null,$(me,(e=>v("div",{key:e.value,class:"lang-btn-item"},[h(a,{command:e.value,class:T({"is-selected":y(de)===e.value})},{default:b((()=>[v("span",G,w(e.label),1),y(de)===e.value?(E(),f("i",X,"")):j("",!0)])),_:2},1032,["command","class"])]))),64))])),_:1})])),default:b((()=>[s[3]||(s[3]=v("div",{class:"btn language-btn"},[v("i",{class:"iconfont-sys icon-language"},"")],-1))])),_:1})]),v("div",B,[s[4]||(s[4]=v("svg",{class:"icon","aria-hidden":"true"},[v("use",{"xlink:href":"#iconsys-zhaopian-copy"})],-1)),v("h1",null,w(e.systemName),1)]),v("div",J,[v("div",O,[v("h3",Q,w(e.$t("login.title")),1),v("p",W,w(e.$t("login.subTitle")),1),h(u,{ref_key:"formRef",ref:ae,model:y(le),rules:y(oe),onKeyup:x(ie,["enter"]),style:{"margin-top":"25px"}},{default:b((()=>[h(t,{prop:"username"},{default:b((()=>[h(r,{placeholder:e.$t("login.placeholder[0]"),size:"large",modelValue:y(le).username,"onUpdate:modelValue":s[0]||(s[0]=e=>y(le).username=e),modelModifiers:{trim:!0}},null,8,["placeholder","modelValue"])])),_:1}),h(t,{prop:"password"},{default:b((()=>[h(r,{placeholder:e.$t("login.placeholder[1]"),size:"large",modelValue:y(le).password,"onUpdate:modelValue":s[1]||(s[1]=e=>y(le).password=e),modelModifiers:{trim:!0},type:"password",radius:"8px",autocomplete:"off"},null,8,["placeholder","modelValue"])])),_:1}),v("div",Y,[h(n,{modelValue:y(le).rememberPassword,"onUpdate:modelValue":s[2]||(s[2]=e=>y(le).rememberPassword=e)},{default:b((()=>[C(w(e.$t("login.rememberPwd")),1)])),_:1},8,["modelValue"]),h(i,{to:"/forget-password"},{default:b((()=>[C(w(e.$t("login.forgetPwd")),1)])),_:1})]),h(d,{class:"login-btn",type:"primary",loading:y(re),onClick:ie},{default:b((()=>[C(w(e.$t("login.btnText")),1)])),_:1},8,["loading"])])),_:1},8,["model","rules"])])])])])}}}),[["__scopeId","data-v-4b5e6505"]]);export{ee as default};
