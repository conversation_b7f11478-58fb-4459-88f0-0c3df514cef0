var e=(e,a,l)=>new Promise(((t,r)=>{var s=e=>{try{d(l.next(e))}catch(a){r(a)}},i=e=>{try{d(l.throw(e))}catch(a){r(a)}},d=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,i);d((l=l.apply(e,a)).next())}));import{_ as a}from"./index-F0f2Vd-3.js";/* empty css                  *//* empty css                    *//* empty css                *//* empty css                             */import{d as l,r as t,X as r,j as s,p as i,N as d,o,w as u,a2 as n,c,a as p,F as f,b as v,aR as m,aY as y,aW as _,K as h,x as g,aX as k,e as b,A as w,bT as x,y as O,u as W,bd as j,Z as B,_ as D,C as z,bD as C,bE as L,H as I,bb as A,aG as E,Y as M,M as V,ai as F}from"./vendor-BVh5F9vp.js";import{W as P}from"./workOrderApi-4juIOmO-.js";import{E as R}from"./errorHandler-Dnd-hi0l.js";const T={class:"workorder-detail"},$={class:"card-header"},N={class:"content-text"},S={class:"attachment-list"},H={class:"file-size"},K={class:"card-header"},U={key:0,class:"reply-form"},X={class:"reply-list"},Y={class:"reply-header"},q={class:"reply-info"},G={key:0,class:"committer"},Z={class:"reply-time"},J={class:"reply-content"},Q={key:0,class:"reply-attachments"},ee={key:0,class:"no-replies"},ae={class:"dialog-footer"},le=a(l({__name:"WorkOrderDetailDialog",props:{visible:{type:Boolean},workOrderId:{},isPageMode:{type:Boolean,default:!1}},emits:["update:visible","refresh"],setup(a,{emit:l}){const le=a,te=l,re=t(!1),se=t(!1),ie=t(!1),de=t(!1),oe=t(null),ue=t(),ne=t(),ce=r({content:"",fileList:[]}),pe={content:[{required:!0,message:"请输入回复内容",trigger:"blur"},{min:10,message:"回复内容至少10个字符",trigger:"blur"}]},fe=s({get:()=>le.visible,set:e=>te("update:visible",e)}),ve=s((()=>!!oe.value&&P.canReplyWorkOrder(oe.value))),me=s((()=>oe.value?P.canDeleteWorkOrder(oe.value):{canDelete:!1}));i((()=>le.workOrderId),(e=>{e&&le.visible&&ye()}),{immediate:!0}),i((()=>le.visible),(e=>{e&&le.workOrderId?ye():be()}));const ye=()=>e(this,null,(function*(){if(le.workOrderId){re.value=!0;try{oe.value=yield P.getWorkOrderDetail(le.workOrderId)}catch(e){R.handleApiError(e),we()}finally{re.value=!1}}})),_e=()=>e(this,null,(function*(){if(ue.value&&oe.value){try{yield ue.value.validate()}catch(e){return}se.value=!0;try{let e=[];if(ce.fileList.length>0){const a=ce.fileList.map((e=>e.raw)).filter(Boolean);e=(yield P.uploadMultipleAttachments(a)).map((e=>e.file_url))}const a={content:ce.content,attachment_urls:e};yield P.replyWorkOrder(oe.value.id,a),V.success("回复提交成功"),de.value=!1,be(),ye(),te("refresh")}catch(a){R.handleApiError(a)}finally{se.value=!1}}})),he=()=>{de.value=!1,be()},ge=e=>{if(e.size&&e.size>10485760)return V.error("文件大小不能超过10MB"),!1;return e.raw&&!["image/jpeg","image/png","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(e.raw.type)?(V.error("只支持jpg/png/pdf/doc/docx格式的文件"),!1):void 0},ke=e=>{const a=ce.fileList.findIndex((a=>a.uid===e.uid));a>-1&&ce.fileList.splice(a,1)},be=()=>{var e;ce.content="",ce.fileList=[],de.value=!1,null==(e=ue.value)||e.resetFields()},we=()=>{te("update:visible",!1)},xe=()=>e(this,null,(function*(){if(!oe.value)return;const e=P.canDeleteWorkOrder(oe.value);if(e.canDelete)try{yield F.confirm(`确定要删除工单"${oe.value.title}"吗？删除后无法恢复。`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}),ie.value=!0;const e=yield P.deleteWorkOrder(oe.value.id);if(!e.success)throw new Error(e.message||"删除失败");V.success("工单删除成功"),te("refresh"),we()}catch(a){"cancel"!==a&&R.handleApiError(a)}finally{ie.value=!1}else V.error(e.reason||"无法删除该工单")})),Oe=e=>P.formatWorkOrderStatus(e).text,We=e=>P.formatWorkOrderPriority(e).text,je=e=>({1:"用户",2:"供应商",3:"系统"}[e]||"未知"),Be=e=>new Date(e).toLocaleString("zh-CN");return(e,a)=>{const l=k,t=_,r=y,s=m,i=O,V=x,F=I,R=z,le=D,te=C,ye=B,be=M,De=E;return o(),d(be,{modelValue:fe.value,"onUpdate:modelValue":a[2]||(a[2]=e=>fe.value=e),title:"工单详情",width:"80%","before-close":we,"destroy-on-close":""},{footer:u((()=>[b("div",ae,[oe.value&&me.value.canDelete?(o(),d(F,{key:0,type:"danger",loading:ie.value,onClick:xe},{default:u((()=>a[12]||(a[12]=[h(" 删除工单 ")]))),_:1},8,["loading"])):p("",!0),v(F,{onClick:we},{default:u((()=>a[13]||(a[13]=[h("关闭")]))),_:1})])])),default:u((()=>[n((o(),c("div",T,[oe.value?(o(),c(f,{key:0},[v(s,{class:"info-card",shadow:"never"},{header:u((()=>{return[b("div",$,[a[3]||(a[3]=b("span",null,"基本信息",-1)),v(l,{type:(e=oe.value.status,P.formatWorkOrderStatus(e).type)},{default:u((()=>[h(g(Oe(oe.value.status)),1)])),_:1},8,["type"])])];var e})),default:u((()=>[v(r,{column:2,border:""},{default:u((()=>[v(t,{label:"工单标题"},{default:u((()=>[h(g(oe.value.title),1)])),_:1}),v(t,{label:"工单类型"},{default:u((()=>[v(l,{size:"small"},{default:u((()=>{return[h(g((e=oe.value.work_order_type,P.getWorkOrderTypeName(e))),1)];var e})),_:1})])),_:1}),v(t,{label:"优先级"},{default:u((()=>{return[v(l,{type:(e=oe.value.priority,P.formatWorkOrderPriority(e).type),size:"small"},{default:u((()=>[h(g(We(oe.value.priority)),1)])),_:1},8,["type"])];var e})),_:1}),v(t,{label:"供应商"},{default:u((()=>{return[h(g((e=oe.value.provider,{kuaidi100:"快递100",yida:"易达",yuntong:"云通"}[e]||e)),1)];var e})),_:1}),v(t,{label:"订单号"},{default:u((()=>[h(g(oe.value.order_no||"-"),1)])),_:1}),v(t,{label:"运单号"},{default:u((()=>[h(g(oe.value.tracking_no||"-"),1)])),_:1}),v(t,{label:"创建时间"},{default:u((()=>[h(g(Be(oe.value.created_at)),1)])),_:1}),v(t,{label:"更新时间"},{default:u((()=>[h(g(Be(oe.value.updated_at)),1)])),_:1}),oe.value.completed_at?(o(),d(t,{key:0,label:"完成时间"},{default:u((()=>[h(g(Be(oe.value.completed_at)),1)])),_:1})):p("",!0),oe.value.feedback_weight?(o(),d(t,{key:1,label:"反馈重量"},{default:u((()=>[h(g(oe.value.feedback_weight)+" kg ",1)])),_:1})):p("",!0),oe.value.goods_value?(o(),d(t,{key:2,label:"货物价值"},{default:u((()=>[h(" ¥"+g(oe.value.goods_value),1)])),_:1})):p("",!0),oe.value.overweight_amount?(o(),d(t,{key:3,label:"超重金额"},{default:u((()=>[h(" ¥"+g(oe.value.overweight_amount),1)])),_:1})):p("",!0)])),_:1})])),_:1}),v(s,{class:"content-card",shadow:"never"},{header:u((()=>a[4]||(a[4]=[b("span",null,"工单内容",-1)]))),default:u((()=>[b("div",N,g(oe.value.content),1)])),_:1}),oe.value.attachments&&oe.value.attachments.length>0?(o(),d(s,{key:0,class:"attachment-card",shadow:"never"},{header:u((()=>a[5]||(a[5]=[b("span",null,"工单附件",-1)]))),default:u((()=>[b("div",S,[(o(!0),c(f,null,w(oe.value.attachments,(e=>{return o(),c("div",{key:e.id,class:"attachment-item"},[v(V,{href:e.file_url,target:"_blank",type:"primary"},{default:u((()=>[v(i,null,{default:u((()=>[v(W(j))])),_:1}),h(" "+g(e.file_name),1)])),_:2},1032,["href"]),b("span",H,g((a=e.file_size,a?a<1024?`${a}B`:a<1048576?`${(a/1024).toFixed(1)}KB`:`${(a/1048576).toFixed(1)}MB`:"-")),1)]);var a})),128))])])),_:1})):p("",!0),v(s,{class:"reply-card",shadow:"never"},{header:u((()=>[b("div",K,[a[7]||(a[7]=b("span",null,"回复记录",-1)),ve.value?(o(),d(F,{key:0,type:"primary",size:"small",onClick:a[0]||(a[0]=e=>de.value=!0)},{default:u((()=>[v(i,null,{default:u((()=>[v(W(A))])),_:1}),a[6]||(a[6]=h(" 添加回复 "))])),_:1})):p("",!0)])])),default:u((()=>[de.value?(o(),c("div",U,[v(ye,{ref_key:"replyFormRef",ref:ue,model:ce,rules:pe},{default:u((()=>[v(le,{prop:"content"},{default:u((()=>[v(R,{modelValue:ce.content,"onUpdate:modelValue":a[1]||(a[1]=e=>ce.content=e),type:"textarea",rows:4,placeholder:"请输入回复内容...",maxlength:"1000","show-word-limit":""},null,8,["modelValue"])])),_:1}),v(le,{label:"附件"},{default:u((()=>[v(te,{ref_key:"uploadRef",ref:ne,"file-list":ce.fileList,"auto-upload":!1,"on-change":ge,"on-remove":ke,multiple:"",drag:""},{tip:u((()=>a[8]||(a[8]=[b("div",{class:"el-upload__tip"}," 支持jpg/png/pdf/doc/docx文件，且不超过10MB ",-1)]))),default:u((()=>[v(i,{class:"el-icon--upload"},{default:u((()=>[v(W(L))])),_:1}),a[9]||(a[9]=b("div",{class:"el-upload__text"},[h(" 将文件拖到此处，或"),b("em",null,"点击上传")],-1))])),_:1},8,["file-list"])])),_:1}),v(le,null,{default:u((()=>[v(F,{type:"primary",loading:se.value,onClick:_e},{default:u((()=>a[10]||(a[10]=[h(" 提交回复 ")]))),_:1},8,["loading"]),v(F,{onClick:he},{default:u((()=>a[11]||(a[11]=[h("取消")]))),_:1})])),_:1})])),_:1},8,["model"])])):p("",!0),b("div",X,[(o(!0),c(f,null,w(oe.value.replies,(e=>{return o(),c("div",{key:e.id,class:"reply-item"},[b("div",Y,[b("div",q,[v(l,{type:(a=e.reply_type,{1:"primary",2:"success",3:"info"}[a]||"default"),size:"small"},{default:u((()=>[h(g(je(e.reply_type)),1)])),_:2},1032,["type"]),e.committer?(o(),c("span",G,g(e.committer),1)):p("",!0),b("span",Z,g(Be(e.created_at)),1)])]),b("div",J,g(e.content),1),e.attachments&&e.attachments.length>0?(o(),c("div",Q,[(o(!0),c(f,null,w(e.attachments,(e=>(o(),c("div",{key:e.id,class:"attachment-item"},[v(V,{href:e.file_url,target:"_blank",type:"primary"},{default:u((()=>[v(i,null,{default:u((()=>[v(W(j))])),_:1}),h(" "+g(e.file_name),1)])),_:2},1032,["href"])])))),128))])):p("",!0)]);var a})),128)),oe.value.replies&&0!==oe.value.replies.length?p("",!0):(o(),c("div",ee," 暂无回复记录 "))])])),_:1})],64)):p("",!0)])),[[De,re.value]])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-f2e463db"]]);export{le as default};
