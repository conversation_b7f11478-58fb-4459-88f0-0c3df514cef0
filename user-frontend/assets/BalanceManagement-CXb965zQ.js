var e=(e,a,t)=>new Promise(((l,r)=>{var o=e=>{try{n(t.next(e))}catch(a){r(a)}},s=e=>{try{n(t.throw(e))}catch(a){r(a)}},n=e=>e.done?l(e.value):Promise.resolve(e.value).then(o,s);n((t=t.apply(e,a)).next())}));import{_ as a}from"./index-F0f2Vd-3.js";/* empty css               *//* empty css                */import{d as t,r as l,X as r,p as o,g as s,f as n,c as i,o as d,e as c,a2 as u,b as p,w as _,aN as g,aR as m,y as v,u as f,b2 as h,x as b,b3 as y,b4 as w,aQ as x,K as D,b5 as F,H as k,b6 as z,b7 as S,a4 as C,am as j,Z as E,_ as V,C as M,ar as N,as as I,N as O,aJ as P,aH as U,aX as A,v as L,aG as T,aK as Y,M as $}from"./vendor-BVh5F9vp.js";import{B as H}from"./balanceApi-Bj92H6Oi.js";import G from"./DepositDialog-ByKuqoE_.js";import"./index-CdJNdp1H.js";import{i as B}from"./install-DmVoiIn1.js";/* empty css                 *//* empty css                       *//* empty css                 */const R={class:"balance-management"},K={class:"balance-overview-section"},X={class:"balance-content"},J={class:"balance-header"},Q={class:"balance-value"},W={class:"balance-content"},Z={class:"balance-header"},q={class:"balance-value expense"},ee={class:"balance-extra"},ae={class:"balance-content"},te={class:"balance-header"},le={class:"balance-value income"},re={class:"balance-extra"},oe={class:"action-section"},se={class:"chart-header"},ne={class:"card-header"},ie={class:"header-actions"},de={class:"filter-section"},ce={class:"transaction-id"},ue={key:0,class:"order-no"},pe={key:1,class:"no-data"},_e={key:0,class:"order-no"},ge={key:1,class:"no-data"},me={key:0,class:"tracking-no"},ve={key:1,class:"no-data"},fe={class:"balance-amount"},he={class:"balance-amount"},be=["title"],ye={class:"pagination-wrapper"},we=a(t({__name:"BalanceManagement",setup(a){const t=l(!1),we=l(!1),xe=l(!1),De=l(!1),Fe=l(null),ke=l([]),ze=l(null),Se=l(),Ce=l(),je=l("0.00"),Ee=l(0),Ve=l("0.00"),Me=l(0),Ne=r({customer_order_no:void 0,order_no:void 0,tracking_no:void 0,type:void 0,status:void 0}),Ie=r({page:1,page_size:20,total:0}),Oe=e=>{if(!e)return"0.00";return("string"==typeof e?parseFloat(e):e).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})},Pe=()=>e(this,null,(function*(){t.value=!0;try{const e=yield H.getBalance();e.success&&e.data&&(Fe.value=e.data,yield Ue())}catch(e){$.error("获取余额信息失败")}finally{t.value=!1}})),Ue=()=>e(this,null,(function*(){var e,a;try{const t=new Date,l=new Date(t.getFullYear(),t.getMonth(),t.getDate()).toISOString(),r=new Date(t.getFullYear(),t.getMonth(),t.getDate()+1).toISOString(),o=yield H.getTransactionHistoryOptimized({start_time:l,end_time:r,limit:1e3});if(o.success&&(null==(e=o.data)?void 0:e.items)){const e=o.data.items,a=["order_pre_charge","overweight_charge","order_intercept_charge","return_charge","order_revive_recharge"],t=e.filter((e=>a.includes(e.type)));je.value=t.reduce(((e,a)=>e+parseFloat(a.amount||"0")),0).toFixed(2),Ee.value=t.length}const s=new Date(t.getFullYear(),t.getMonth(),1).toISOString(),n=new Date(t.getFullYear(),t.getMonth()+1,1).toISOString(),i=yield H.getTransactionHistoryOptimized({start_time:s,end_time:n,type:"user_deposit",limit:1e3});if(i.success&&(null==(a=i.data)?void 0:a.items)){const e=i.data.items;Ve.value=e.reduce(((e,a)=>e+parseFloat(a.amount||"0")),0).toFixed(2),Me.value=e.length}}catch(t){}})),Ae=()=>e(this,null,(function*(){var e,a;we.value=!0;try{const t=(Ie.page-1)*Ie.page_size,l={limit:Ie.page_size,offset:t,customer_order_no:Ne.customer_order_no,order_no:Ne.order_no,tracking_no:Ne.tracking_no,type:Ne.type,status:Ne.status,start_time:null==(e=ze.value)?void 0:e[0],end_time:null==(a=ze.value)?void 0:a[1]},r=yield H.getTransactionHistoryOptimized(l);r.success&&r.data?r.data.items?(ke.value=r.data.items,Ie.total=r.data.total):(ke.value=[],Ie.total=0):r.items?(ke.value=r.items,Ie.total=r.total):(ke.value=[],Ie.total=0)}catch(t){$.error("获取交易历史失败")}finally{we.value=!1}})),Le=()=>{Ae()},Te=()=>{Ie.page=1,Ae()},Ye=()=>{Ne.customer_order_no=void 0,Ne.order_no=void 0,Ne.tracking_no=void 0,Ne.type=void 0,Ne.status=void 0,ze.value=null,Ie.page=1,Ae()},$e=e=>{Ie.page_size=e,Ie.page=1,Ae()},He=e=>{Ie.page=e,Ae()},Ge=()=>{Pe(),Ae()},Be=()=>{Pe(),Ae(),$.success("数据已刷新")};o(De,(e=>{e&&s((()=>{(()=>{if(!Se.value)return;Ce.value=B(Se.value);const e={title:{text:"近30天余额变化",left:"center",textStyle:{fontSize:16,fontWeight:"normal"}},tooltip:{trigger:"axis",formatter:e=>{const a=e[0];return`${a.name}<br/>余额: ¥${a.value}`}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:Array.from({length:30},((e,a)=>{const t=new Date;return t.setDate(t.getDate()-29+a),t.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})}))},yAxis:{type:"value",axisLabel:{formatter:"¥{value}"}},series:[{name:"余额",type:"line",smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{color:"#409EFF",width:3},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.05)"}]}},data:Array.from({length:30},(()=>Math.floor(1e3*Math.random())+500))}]};Ce.value.setOption(e)})()}))}));const Re=e=>({user_deposit:"用户充值",admin_deposit:"管理员充值",order_pre_charge:"下单预收",overweight_charge:"超重补收费用",order_intercept_charge:"订单拦截_补收",return_charge:"退回收费",order_revive_recharge:"订单复活重计费",order_cancel_refund:"订单取消_退款",underweight_refund:"超轻退款",balance_adjustment:"调账_多退少补"}[e]||e),Ke=e=>({pending:"待处理",completed:"已完成",failed:"已失败",PENDING:"待处理",COMPLETED:"已完成",FAILED:"已失败"}[e]||e),Xe=e=>{const a=e.transaction_category||e.category;if("PAYMENT"===a)return"amount-negative";if("REFUND"===a||"DEPOSIT"===a)return"amount-positive";return parseFloat(e.amount||"0")>=0?"amount-positive":"amount-negative"},Je=(e,a)=>{const t=a.transaction_category||a.category,l=parseFloat(e||"0");if("PAYMENT"===t)return`-¥${l.toFixed(2)}`;if("REFUND"===t||"DEPOSIT"===t)return`+¥${l.toFixed(2)}`;const r=parseFloat(a.balance_before||"0"),o=parseFloat(a.balance_after||"0")-r;return o>=0?`+¥${Math.abs(o).toFixed(2)}`:`-¥${Math.abs(o).toFixed(2)}`};return n((()=>{Pe(),Ae()})),(e,a)=>{const t=v,l=m,r=g,o=x,s=k,n=A,$=M,H=V,B=I,ze=N,Ce=E,Pe=U,Ue=P,Ae=Y,Qe=T;return d(),i("div",R,[c("div",K,[p(o,{gutter:20},{default:_((()=>[p(r,{xs:24,sm:8},{default:_((()=>[p(l,{class:"balance-card primary-card",shadow:"hover"},{default:_((()=>{var e;return[c("div",X,[c("div",J,[p(t,{class:"balance-icon"},{default:_((()=>[p(f(h))])),_:1}),a[11]||(a[11]=c("span",{class:"balance-label"},"可用余额",-1))]),c("div",Q,"¥"+b(Oe(null==(e=Fe.value)?void 0:e.available_balance)),1)])]})),_:1})])),_:1}),p(r,{xs:24,sm:8},{default:_((()=>[p(l,{class:"balance-card expense-card",shadow:"hover"},{default:_((()=>[c("div",W,[c("div",Z,[p(t,{class:"balance-icon"},{default:_((()=>[p(f(y))])),_:1}),a[12]||(a[12]=c("span",{class:"balance-label"},"今日支出",-1))]),c("div",q,"¥"+b(je.value),1),c("div",ee,b(Ee.value)+" 笔交易",1)])])),_:1})])),_:1}),p(r,{xs:24,sm:8},{default:_((()=>[p(l,{class:"balance-card income-card",shadow:"hover"},{default:_((()=>[c("div",ae,[c("div",te,[p(t,{class:"balance-icon"},{default:_((()=>[p(f(w))])),_:1}),a[13]||(a[13]=c("span",{class:"balance-label"},"本月充值",-1))]),c("div",le,"¥"+b(Ve.value),1),c("div",re,b(Me.value)+" 笔充值",1)])])),_:1})])),_:1})])),_:1})]),c("div",oe,[p(s,{type:"primary",size:"large",onClick:a[0]||(a[0]=e=>xe.value=!0)},{default:_((()=>[p(t,null,{default:_((()=>[p(f(F))])),_:1}),a[14]||(a[14]=D(" 充值 "))])),_:1}),p(s,{onClick:a[1]||(a[1]=e=>De.value=!De.value)},{default:_((()=>[p(t,null,{default:_((()=>[p(f(z))])),_:1}),D(" "+b(De.value?"隐藏图表":"显示图表"),1)])),_:1}),p(s,{onClick:Be},{default:_((()=>[p(t,null,{default:_((()=>[p(f(S))])),_:1}),a[15]||(a[15]=D(" 刷新 "))])),_:1})]),u(p(l,{class:"chart-section"},{header:_((()=>[c("div",se,[a[16]||(a[16]=c("span",null,"余额变化趋势",-1)),p(s,{text:"",onClick:a[2]||(a[2]=e=>De.value=!1)},{default:_((()=>[p(t,null,{default:_((()=>[p(f(j))])),_:1})])),_:1})])])),default:_((()=>[c("div",{ref_key:"chartContainer",ref:Se,class:"chart-container"},null,512)])),_:1},512),[[C,De.value]]),p(l,{class:"transaction-section"},{header:_((()=>[c("div",ne,[a[18]||(a[18]=c("span",null,"交易记录",-1)),c("div",ie,[p(n,{type:"info"},{default:_((()=>[D("共 "+b(Ie.total)+" 条",1)])),_:1}),p(s,{onClick:Le},{default:_((()=>[p(t,null,{default:_((()=>[p(f(S))])),_:1}),a[17]||(a[17]=D(" 刷新 "))])),_:1})])])])),default:_((()=>[c("div",de,[p(Ce,{model:Ne,inline:"",class:"filter-form"},{default:_((()=>[p(H,{label:"客户订单号"},{default:_((()=>[p($,{modelValue:Ne.customer_order_no,"onUpdate:modelValue":a[3]||(a[3]=e=>Ne.customer_order_no=e),placeholder:"请输入客户订单号",clearable:"",style:{width:"160px"}},null,8,["modelValue"])])),_:1}),p(H,{label:"平台订单号"},{default:_((()=>[p($,{modelValue:Ne.order_no,"onUpdate:modelValue":a[4]||(a[4]=e=>Ne.order_no=e),placeholder:"请输入平台订单号",clearable:"",style:{width:"160px"}},null,8,["modelValue"])])),_:1}),p(H,{label:"运单号"},{default:_((()=>[p($,{modelValue:Ne.tracking_no,"onUpdate:modelValue":a[5]||(a[5]=e=>Ne.tracking_no=e),placeholder:"请输入运单号",clearable:"",style:{width:"160px"}},null,8,["modelValue"])])),_:1}),p(H,{label:"交易类型"},{default:_((()=>[p(ze,{modelValue:Ne.type,"onUpdate:modelValue":a[6]||(a[6]=e=>Ne.type=e),placeholder:"请选择类型",clearable:"",style:{width:"160px"}},{default:_((()=>[p(B,{label:"用户充值",value:"user_deposit"}),p(B,{label:"管理员充值",value:"admin_deposit"}),p(B,{label:"下单预收",value:"order_pre_charge"}),p(B,{label:"订单取消退款",value:"order_cancel_refund"}),p(B,{label:"调账",value:"balance_adjustment"})])),_:1},8,["modelValue"])])),_:1}),p(H,{label:"状态"},{default:_((()=>[p(ze,{modelValue:Ne.status,"onUpdate:modelValue":a[7]||(a[7]=e=>Ne.status=e),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:_((()=>[p(B,{label:"待处理",value:"pending"}),p(B,{label:"已完成",value:"completed"}),p(B,{label:"已失败",value:"failed"})])),_:1},8,["modelValue"])])),_:1}),p(H,null,{default:_((()=>[p(s,{type:"primary",onClick:Te},{default:_((()=>a[19]||(a[19]=[D("筛选")]))),_:1}),p(s,{onClick:Ye},{default:_((()=>a[20]||(a[20]=[D("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),u((d(),O(Ue,{data:ke.value,stripe:""},{default:_((()=>[p(Pe,{prop:"id",label:"交易ID",width:"120","show-overflow-tooltip":""},{default:_((({row:e})=>[c("span",ce,b(e.id.substring(0,8))+"...",1)])),_:1}),p(Pe,{prop:"type",label:"交易类型",width:"140"},{default:_((({row:e})=>{return[p(n,{type:(a=e.type,{user_deposit:"success",admin_deposit:"success",order_pre_charge:"warning",order_intercept_charge:"warning",return_charge:"warning",order_revive_recharge:"warning",order_cancel_refund:"info",balance_adjustment:"primary"}[a]||"info"),size:"small"},{default:_((()=>[D(b(Re(e.type)),1)])),_:2},1032,["type"])];var a})),_:1}),p(Pe,{prop:"customer_order_no",label:"客户订单号",width:"120","show-overflow-tooltip":""},{default:_((({row:e})=>[e.customer_order_no?(d(),i("span",ue,b(e.customer_order_no),1)):(d(),i("span",pe,"-"))])),_:1}),p(Pe,{prop:"order_no",label:"平台订单号",width:"120","show-overflow-tooltip":""},{default:_((({row:e})=>[e.order_no?(d(),i("span",_e,b(e.order_no),1)):(d(),i("span",ge,"-"))])),_:1}),p(Pe,{prop:"tracking_no",label:"运单号",width:"140","show-overflow-tooltip":""},{default:_((({row:e})=>[e.tracking_no?(d(),i("span",me,b(e.tracking_no),1)):(d(),i("span",ve,"-"))])),_:1}),p(Pe,{prop:"balance_before",label:"变更前余额",width:"110",align:"right"},{default:_((({row:e})=>[c("span",fe,"¥"+b(e.balance_before),1)])),_:1}),p(Pe,{prop:"amount",label:"变更金额",width:"100",align:"right"},{default:_((({row:e})=>[c("span",{class:L(Xe(e))},b(Je(e.amount,e)),3)])),_:1}),p(Pe,{prop:"balance_after",label:"变更后余额",width:"110",align:"right"},{default:_((({row:e})=>[c("span",he,"¥"+b(e.balance_after),1)])),_:1}),p(Pe,{prop:"status",label:"状态",width:"80"},{default:_((({row:e})=>{return[p(n,{type:(a=e.status,{pending:"warning",completed:"success",failed:"danger",PENDING:"warning",COMPLETED:"success",FAILED:"danger"}[a]||"info"),size:"small"},{default:_((()=>[D(b(Ke(e.status)),1)])),_:2},1032,["type"])];var a})),_:1}),p(Pe,{prop:"description",label:"描述","min-width":"200","show-overflow-tooltip":""},{default:_((({row:e})=>[c("span",{title:e.detail_description||e.description},b(e.user_friendly_desc||e.detail_description||e.description),9,be)])),_:1}),p(Pe,{prop:"created_at",label:"时间",width:"140"},{default:_((({row:e})=>{return[D(b((a=e.created_at,new Date(a).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}))),1)];var a})),_:1})])),_:1},8,["data"])),[[Qe,we.value]]),c("div",ye,[p(Ae,{"current-page":Ie.page,"onUpdate:currentPage":a[8]||(a[8]=e=>Ie.page=e),"page-size":Ie.page_size,"onUpdate:pageSize":a[9]||(a[9]=e=>Ie.page_size=e),"page-sizes":[10,20,50,100],total:Ie.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:$e,onCurrentChange:He},null,8,["current-page","page-size","total"])])])),_:1}),p(G,{visible:xe.value,"onUpdate:visible":a[10]||(a[10]=e=>xe.value=e),onSuccess:Ge},null,8,["visible"])])}}}),[["__scopeId","data-v-bf95b025"]]);export{we as default};
