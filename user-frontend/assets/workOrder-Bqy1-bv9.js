var t=(t,e,r)=>new Promise(((a,s)=>{var i=t=>{try{o(r.next(t))}catch(e){s(e)}},l=t=>{try{o(r.throw(t))}catch(e){s(e)}},o=t=>t.done?a(t.value):Promise.resolve(t.value).then(i,l);o((r=r.apply(t,e)).next())}));import{L as e,r,j as a}from"./vendor-BVh5F9vp.js";import"./index-F0f2Vd-3.js";import{W as s}from"./workOrderApi-4juIOmO-.js";import{E as i}from"./errorHandler-Dnd-hi0l.js";const l=e("workOrder",(()=>{const e=r({workOrderList:[],workOrderTotal:0,workOrderLoading:!1,currentWorkOrder:null,workOrderDetailLoading:!1,supportedTypes:{},typesLoading:!1,statistics:null,trend:[],typeStatistics:[],statisticsLoading:!1,cacheExpiry:3e5,lastFetchTime:0}),l=a((()=>e.value.workOrderList)),o=a((()=>e.value.workOrderTotal)),d=a((()=>e.value.workOrderLoading)),u=a((()=>e.value.currentWorkOrder)),n=a((()=>e.value.workOrderDetailLoading)),c=a((()=>e.value.statistics)),v=a((()=>e.value.trend)),p=a((()=>e.value.typeStatistics)),k=a((()=>e.value.statisticsLoading)),w=a((()=>t=>e.value.supportedTypes[t]||[])),O=a((()=>Date.now()-e.value.lastFetchTime<e.value.cacheExpiry));return{workOrderList:l,workOrderTotal:o,workOrderLoading:d,currentWorkOrder:u,workOrderDetailLoading:n,statistics:c,trend:v,typeStatistics:p,statisticsLoading:k,getSupportedTypes:w,isCacheValid:O,fetchWorkOrderList:(r,a=!1)=>t(void 0,null,(function*(){if(!a&&O.value&&e.value.workOrderList.length>0)return{items:e.value.workOrderList,total:e.value.workOrderTotal};e.value.workOrderLoading=!0;try{const t=yield s.getWorkOrderList(r);if(t.success&&t.data)return e.value.workOrderList=t.data.items||[],e.value.workOrderTotal=t.data.total||0,e.value.lastFetchTime=Date.now(),t.data;throw new Error(t.message||"获取工单列表失败")}catch(t){throw i.handleApiError(t),e.value.workOrderList=[],e.value.workOrderTotal=0,t}finally{e.value.workOrderLoading=!1}})),fetchWorkOrderDetail:(r,a=!1)=>t(void 0,null,(function*(){var t;if(!a&&(null==(t=e.value.currentWorkOrder)?void 0:t.id)===r)return e.value.currentWorkOrder;e.value.workOrderDetailLoading=!0;try{const t=yield s.getWorkOrderDetail(r);return e.value.currentWorkOrder=t,t}catch(l){throw i.handleApiError(l),e.value.currentWorkOrder=null,l}finally{e.value.workOrderDetailLoading=!1}})),fetchSupportedTypes:(r,a=!1)=>t(void 0,null,(function*(){if(!a&&e.value.supportedTypes[r])return e.value.supportedTypes[r];e.value.typesLoading=!0;try{const t=yield s.getSupportedTypes(r);if(t.success&&t.data)return e.value.supportedTypes[r]=t.data,t.data;throw new Error(t.message||"获取工单类型失败")}catch(t){throw i.handleApiError(t),e.value.supportedTypes[r]=[],t}finally{e.value.typesLoading=!1}})),fetchStatistics:(r,a=!1)=>t(void 0,null,(function*(){if(!a&&e.value.statistics&&O.value)return e.value.statistics;e.value.statisticsLoading=!0;try{const t=yield s.getWorkOrderStatistics(r);if(t.success&&t.data)return e.value.statistics=t.data,t.data;throw new Error(t.message||"获取统计数据失败")}catch(t){throw i.handleApiError(t),e.value.statistics=null,t}finally{e.value.statisticsLoading=!1}})),fetchTrend:(r,a=!1)=>t(void 0,null,(function*(){if(!a&&e.value.trend.length>0&&O.value)return e.value.trend;try{const t=yield s.getWorkOrderTrend(r);if(t.success&&t.data)return e.value.trend=t.data,t.data;throw new Error(t.message||"获取趋势数据失败")}catch(t){throw i.handleApiError(t),e.value.trend=[],t}})),fetchTypeStatistics:(r,a=!1)=>t(void 0,null,(function*(){if(!a&&e.value.typeStatistics.length>0&&O.value)return e.value.typeStatistics;try{const t=yield s.getWorkOrderTypeStatistics(r);if(t.success&&t.data)return e.value.typeStatistics=t.data,t.data;throw new Error(t.message||"获取类型统计失败")}catch(t){throw i.handleApiError(t),e.value.typeStatistics=[],t}})),addWorkOrder:t=>{e.value.workOrderList.unshift(t),e.value.workOrderTotal+=1,e.value.statistics&&(e.value.statistics.total+=1,e.value.statistics.pending+=1,e.value.statistics.today_created+=1)},updateWorkOrderStatus:(t,r)=>{var a;const s=e.value.workOrderList.findIndex((e=>e.id===t));if(s>-1){const i=e.value.workOrderList[s].status;if(e.value.workOrderList[s].status=r,e.value.workOrderList[s].updated_at=(new Date).toISOString(),(null==(a=e.value.currentWorkOrder)?void 0:a.id)===t&&(e.value.currentWorkOrder.status=r,e.value.currentWorkOrder.updated_at=(new Date).toISOString()),e.value.statistics){switch(i){case 1:e.value.statistics.pending-=1;break;case 2:e.value.statistics.processing-=1;break;case 3:e.value.statistics.replied-=1;break;case 4:e.value.statistics.completed-=1}switch(r){case 1:e.value.statistics.pending+=1;break;case 2:e.value.statistics.processing+=1;break;case 3:e.value.statistics.replied+=1;break;case 4:e.value.statistics.completed+=1}}}},clearCache:()=>{e.value.workOrderList=[],e.value.workOrderTotal=0,e.value.currentWorkOrder=null,e.value.statistics=null,e.value.trend=[],e.value.typeStatistics=[],e.value.lastFetchTime=0},resetState:()=>{Object.assign(e.value,{workOrderList:[],workOrderTotal:0,workOrderLoading:!1,currentWorkOrder:null,workOrderDetailLoading:!1,supportedTypes:{},typesLoading:!1,statistics:null,trend:[],typeStatistics:[],statisticsLoading:!1,lastFetchTime:0})}}}));export{l as u};
