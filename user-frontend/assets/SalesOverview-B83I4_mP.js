import{u as e,e as a}from"./useECharts-BX0l7NOy.js";import{u as s,S as t,h as o,p as r,_ as i}from"./index-F0f2Vd-3.js";import{d as l,r as c,j as n,p as d,f as p,s as v,c as f,o as m,bn as h,e as b}from"./vendor-BVh5F9vp.js";import"./install-DmVoiIn1.js";const u={class:"region sales-overview art-custom-card"},y=i(l({__name:"SalesOverview",setup(i){const l=c(),{setOptions:y,removeResize:x,resize:w}=e(l),g=s(),L=n((()=>g.systemThemeType)),S=n((()=>L.value===t.LIGHT)),_=s(),j=n((()=>_.menuOpen));d(j,(()=>{[100,200,300].forEach((e=>{setTimeout(w,e)}))})),p((()=>{E()})),v((()=>{x()}));const E=()=>{y({grid:{left:"2.2%",right:"3%",bottom:"0%",top:"5px",containLabel:!0},tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],axisLabel:{show:!0,color:"#999",margin:20,interval:0,fontSize:13},axisLine:{show:!1}},yAxis:{type:"value",axisLabel:{show:!0,color:"#999",fontSize:13},axisLine:{show:!!S.value,lineStyle:{color:"#E8E8E8",width:1}},splitLine:{show:!0,lineStyle:{color:S.value?"#e8e8e8":"#444",width:1,type:"dashed"}}},series:[{name:"访客",color:o("--main-color"),type:"line",stack:"总量",data:[50,25,40,20,70,35,65,30,35,20,40,44],smooth:!0,symbol:"none",lineStyle:{width:2.6},areaStyle:{color:new a.graphic.LinearGradient(0,0,0,1,[{offset:0,color:r(o("--el-color-primary"),.2).rgba},{offset:1,color:r(o("--el-color-primary"),.01).rgba}])}}]})};return(e,a)=>(m(),f("div",u,[a[0]||(a[0]=h('<div class="card-header" data-v-257e8bfc><div class="title" data-v-257e8bfc><h4 class="box-title" data-v-257e8bfc>访问量</h4><p class="subtitle" data-v-257e8bfc>今年增长<span class="text-success" data-v-257e8bfc>+15%</span></p></div></div>',1)),b("div",{class:"chart",ref_key:"chartRef",ref:l},null,512)]))}}),[["__scopeId","data-v-257e8bfc"]]);export{y as default};
