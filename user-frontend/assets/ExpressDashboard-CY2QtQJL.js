var a=(a,e,t)=>new Promise(((s,l)=>{var r=a=>{try{d(t.next(a))}catch(e){l(e)}},i=a=>{try{d(t.throw(a))}catch(e){l(e)}},d=a=>a.done?s(a.value):Promise.resolve(a.value).then(r,i);d((t=t.apply(a,e)).next())}));import{_ as e}from"./index-F0f2Vd-3.js";/* empty css                       *//* empty css                        *//* empty css               *//* empty css                */import{d as t,r as s,X as l,f as r,c as i,b as d,aQ as n,w as o,g as c,aN as u,aR as p,e as v,y as f,u as m,bd as _,x as h,v as g,K as b,bp as y,b2 as w,H as x,a1 as k,bq as z,b8 as C,bo as j,aJ as D,aH as S,aX as A,b4 as M,a3 as q,br as E,b5 as O,o as L}from"./vendor-BVh5F9vp.js";import{E as V}from"./expressApi-BL8dLu27.js";import{B}from"./balanceApi-Bj92H6Oi.js";import"./index-CdJNdp1H.js";import{i as I}from"./install-DmVoiIn1.js";const N={class:"express-dashboard"},P={class:"card-content"},X={class:"icon-wrapper"},H={class:"content-info"},R={class:"number"},T={class:"card-content"},J={class:"icon-wrapper"},K={class:"content-info"},Q={class:"number"},U={class:"action"},$={class:"card-content"},F={class:"icon-wrapper"},G={class:"content-info"},W={class:"number"},Y={class:"sub-info"},Z={class:"card-header"},aa={class:"card-header"},ea={class:"quick-actions"},ta={class:"action-icon create-order"},sa={class:"action-icon price-query"},la={class:"action-icon tracking"},ra={class:"action-icon balance"},ia=e(t({__name:"ExpressDashboard",setup(e){const t=k(),ia=s(!1),da=s("7d"),na=s(),oa=s(),ca=s(null),ua=s([]),pa=l({total:0,growth:0}),va=l({total:0,growth:0}),fa=l({total:0,delivered:0,rate:0}),ma=()=>a(this,null,(function*(){ia.value=!0;try{yield Promise.all([_a(),ha(),ga()]),yield c(),ba()}catch(a){}finally{ia.value=!1}})),_a=()=>a(this,null,(function*(){try{const a=yield V.getOrderStatistics();if(a.success&&a.data){pa.total=a.data.total_orders,pa.growth=Math.floor(20*Math.random())-5,va.total=0,va.growth=0;const e=a.data.status_counts||{},t=(e.delivered||0)+(e.DELIVERED||0);fa.total=a.data.total_orders,fa.delivered=t,fa.rate=fa.total>0?Math.round(t/fa.total*100):0}}catch(a){}})),ha=()=>a(this,null,(function*(){try{const a=yield B.getBalance();a.success&&a.data&&(ca.value=a.data)}catch(a){}})),ga=()=>a(this,null,(function*(){try{const a=yield V.getOrderList({page:1,page_size:5});a.success&&a.data&&(ua.value=a.data.items)}catch(a){}})),ba=()=>{ya(),wa()},ya=()=>{if(!na.value)return;const a=I(na.value),e={tooltip:{trigger:"axis",formatter:"{b}<br/>订单数: {c}"},legend:{data:["我的订单"]},xAxis:{type:"category",data:Array.from({length:7},((a,e)=>{const t=new Date;return t.setDate(t.getDate()-(6-e)),t.toLocaleDateString("zh-CN",{month:"short",day:"numeric"})}))},yAxis:{type:"value",name:"订单数",minInterval:1},series:[{name:"我的订单",type:"line",data:Array.from({length:7},(()=>Math.floor(5*Math.random())+1)),smooth:!0,itemStyle:{color:"#409eff"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}}}]};a.setOption(e)},wa=()=>{if(!oa.value)return;const a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} 单 ({d}%)"},series:[{name:"快递公司分布",type:"pie",radius:["40%","70%"],center:["50%","60%"],data:[{value:8,name:"顺丰速运"},{value:6,name:"中通快递"},{value:5,name:"圆通速递"},{value:4,name:"申通快递"},{value:3,name:"韵达速递"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};I(oa.value).setOption(a)},xa=()=>{ya()},ka=()=>t.push("/express/orders"),za=()=>t.push("/express/orders"),Ca=()=>t.push("/express/price-query"),ja=()=>t.push("/express/tracking"),Da=()=>t.push("/balance/management"),Sa=a=>{const e=new Date(a),t=(new Date).getTime()-e.getTime(),s=Math.floor(t/36e5);return s<1?"刚刚":s<24?`${s}小时前`:e.toLocaleDateString("zh-CN")};return r((()=>{ma()})),(a,e)=>{const t=f,s=p,l=u,r=x,c=n,k=j,V=C,B=S,I=A,ia=D;return L(),i("div",N,[d(c,{gutter:20,class:"overview-cards"},{default:o((()=>[d(l,{span:8},{default:o((()=>[d(s,{class:"overview-card orders"},{default:o((()=>[v("div",P,[v("div",X,[d(t,{size:"40"},{default:o((()=>[d(m(_))])),_:1})]),v("div",H,[v("div",R,h(pa.total||0),1),e[1]||(e[1]=v("div",{class:"label"},"我的订单",-1)),v("div",{class:g(["trend",{positive:pa.growth>=0}])},[d(t,null,{default:o((()=>[d(m(y))])),_:1}),b(" "+h(pa.growth>=0?"+":"")+h(pa.growth)+"% ",1)],2)])])])),_:1})])),_:1}),d(l,{span:8},{default:o((()=>[d(s,{class:"overview-card balance"},{default:o((()=>{var a;return[v("div",T,[v("div",J,[d(t,{size:"40"},{default:o((()=>[d(m(w))])),_:1})]),v("div",K,[v("div",Q,"¥"+h((null==(a=ca.value)?void 0:a.available_balance)||"0.00"),1),e[3]||(e[3]=v("div",{class:"label"},"可用余额",-1)),v("div",U,[d(r,{type:"primary",size:"small",onClick:Da},{default:o((()=>e[2]||(e[2]=[b("充值")]))),_:1})])])])]})),_:1})])),_:1}),d(l,{span:8},{default:o((()=>[d(s,{class:"overview-card delivery"},{default:o((()=>[v("div",$,[v("div",F,[d(t,{size:"40"},{default:o((()=>[d(m(z))])),_:1})]),v("div",G,[v("div",W,h(fa.rate)+"%",1),e[4]||(e[4]=v("div",{class:"label"},"配送成功率",-1)),v("div",Y,h(fa.delivered)+"/"+h(fa.total),1)])])])),_:1})])),_:1})])),_:1}),d(c,{gutter:20,class:"charts-section"},{default:o((()=>[d(l,{span:16},{default:o((()=>[d(s,{class:"chart-card"},{header:o((()=>[v("div",Z,[e[8]||(e[8]=v("span",null,"我的订单趋势",-1)),d(V,{modelValue:da.value,"onUpdate:modelValue":e[0]||(e[0]=a=>da.value=a),size:"small",onChange:xa},{default:o((()=>[d(k,{value:"7d"},{default:o((()=>e[5]||(e[5]=[b("7天")]))),_:1}),d(k,{value:"30d"},{default:o((()=>e[6]||(e[6]=[b("30天")]))),_:1}),d(k,{value:"90d"},{default:o((()=>e[7]||(e[7]=[b("90天")]))),_:1})])),_:1},8,["modelValue"])])])),default:o((()=>[v("div",{ref_key:"trendChart",ref:na,class:"chart-container"},null,512)])),_:1})])),_:1}),d(l,{span:8},{default:o((()=>[d(s,{class:"chart-card"},{header:o((()=>e[9]||(e[9]=[v("span",null,"快递公司分布",-1)]))),default:o((()=>[v("div",{ref_key:"expressChart",ref:oa,class:"chart-container"},null,512)])),_:1})])),_:1})])),_:1}),d(c,{gutter:20,class:"bottom-section"},{default:o((()=>[d(l,{span:12},{default:o((()=>[d(s,{class:"recent-orders-card"},{header:o((()=>[v("div",aa,[e[11]||(e[11]=v("span",null,"最近订单",-1)),d(r,{type:"link",onClick:ka},{default:o((()=>e[10]||(e[10]=[b("查看全部")]))),_:1})])])),default:o((()=>[d(ia,{data:ua.value,stripe:""},{default:o((()=>[d(B,{prop:"order_no",label:"订单号",width:"140"}),d(B,{prop:"express_name",label:"快递公司",width:"100"}),d(B,{prop:"status_desc",label:"状态",width:"80"},{default:o((({row:a})=>{return[d(I,{type:(e=a.status,{submitted:"primary",submit_failed:"danger",print_failed:"danger",assigned:"info",awaiting_pickup:"warning",picked_up:"primary",pickup_failed:"danger",in_transit:"info",out_for_delivery:"info",delivered:"success",delivered_abnormal:"warning",billed:"success",exception:"danger",returned:"warning",forwarded:"info",cancelling:"warning",cancelled:"danger",voided:"danger",weight_updated:"info",revived:"primary"}[e]||"info"),size:"small"},{default:o((()=>[b(h(a.status_desc),1)])),_:2},1032,["type"])];var e})),_:1}),d(B,{prop:"price",label:"价格",width:"80"},{default:o((({row:a})=>[b(" ¥"+h(a.price),1)])),_:1}),d(B,{prop:"created_at",label:"创建时间"},{default:o((({row:a})=>[b(h(Sa(a.created_at)),1)])),_:1})])),_:1},8,["data"])])),_:1})])),_:1}),d(l,{span:12},{default:o((()=>[d(s,{class:"quick-actions-card"},{header:o((()=>e[12]||(e[12]=[v("span",null,"快捷操作",-1)]))),default:o((()=>[v("div",ea,[v("div",{class:"action-item",onClick:za},[v("div",ta,[d(t,{size:"24"},{default:o((()=>[d(m(M))])),_:1})]),e[13]||(e[13]=v("div",{class:"action-content"},[v("div",{class:"action-title"},"创建订单"),v("div",{class:"action-desc"},"快速下单寄件")],-1))]),v("div",{class:"action-item",onClick:Ca},[v("div",sa,[d(t,{size:"24"},{default:o((()=>[d(m(q))])),_:1})]),e[14]||(e[14]=v("div",{class:"action-content"},[v("div",{class:"action-title"},"价格查询"),v("div",{class:"action-desc"},"比较快递价格")],-1))]),v("div",{class:"action-item",onClick:ja},[v("div",la,[d(t,{size:"24"},{default:o((()=>[d(m(E))])),_:1})]),e[15]||(e[15]=v("div",{class:"action-content"},[v("div",{class:"action-title"},"物流查询"),v("div",{class:"action-desc"},"追踪包裹状态")],-1))]),v("div",{class:"action-item",onClick:Da},[v("div",ra,[d(t,{size:"24"},{default:o((()=>[d(m(O))])),_:1})]),e[16]||(e[16]=v("div",{class:"action-content"},[v("div",{class:"action-title"},"余额管理"),v("div",{class:"action-desc"},"充值和支付")],-1))])])])),_:1})])),_:1})])),_:1})])}}}),[["__scopeId","data-v-82081931"]]);export{ia as default};
