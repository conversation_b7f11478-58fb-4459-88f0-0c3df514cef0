var e=(e,a,l)=>new Promise(((t,c)=>{var s=e=>{try{d(l.next(e))}catch(a){c(a)}},r=e=>{try{d(l.throw(e))}catch(a){c(a)}},d=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,r);d((l=l.apply(e,a)).next())}));import{_ as a}from"./index-F0f2Vd-3.js";/* empty css                 *//* empty css                *//* empty css                             */import{d as l,j as t,r as c,p as s,N as r,o as d,w as n,c as i,a as o,b as u,aR as p,aY as _,aW as f,e as g,x as b,K as k,aX as v,v as y,aV as m,C as w,H as h,y as R,u as x,bk as V,Y as j,M as C}from"./vendor-BVh5F9vp.js";const N={key:0,class:"callback-detail"},S={class:"record-id"},q={class:"tracking-no"},D={class:"order-no"},J={class:"callback-url"},O={key:1,class:"no-data"},T={class:"error-section"},P={class:"card-header"},U={class:"card-header"},H={class:"dialog-footer"},I=a(l({__name:"CallbackDetailDialog",props:{visible:{type:Boolean},callbackRecord:{}},emits:["update:visible"],setup(a,{emit:l}){const I=a,Y=l,z=t({get:()=>I.visible,set:e=>Y("update:visible",e)}),B=c(""),K=c("");s((()=>I.callbackRecord),(e=>{if(e){try{B.value=JSON.stringify(e.request_data,null,2)}catch(a){B.value=JSON.stringify(e.request_data)}try{e.response_data?K.value=JSON.stringify(e.response_data,null,2):K.value=""}catch(a){K.value=JSON.stringify(e.response_data)}}}),{immediate:!0});const L=()=>{z.value=!1},M=()=>e(this,null,(function*(){try{yield navigator.clipboard.writeText(B.value),C.success("请求数据已复制到剪贴板")}catch(e){C.error("复制失败")}})),W=()=>e(this,null,(function*(){try{yield navigator.clipboard.writeText(K.value),C.success("响应数据已复制到剪贴板")}catch(e){C.error("复制失败")}})),X=e=>({success:"成功",failed:"失败",pending:"处理中",processing:"处理中",created:"已下单",submitted:"已提交",assigned:"已分配",pending_pickup:"待取件",awaiting_pickup:"待取件",picked_up:"已取件",pickup_failed:"取件失败",accepted:"已接受",collecting:"收件中",in_transit:"运输中",delivering:"派送中",delivered:"已签收",exception_delivered:"异常签收",exception:"异常",cancelled:"已取消",returned:"已退回",forwarded:"已转寄",weight_updated:"重量更新",billed:"已计费",voided:"已作废",1:"待取件",2:"运输中",3:"已签收",4:"异常",5:"已取消",6:"异常",10:"已取消",11:"已取件",100:"下单成功",400:"下单失败",102:"分配网点",103:"分配快递员",104:"已取件",301:"已计费",208:"重量更新",203:"已取消",204:"取件失败",205:"已作废",500:"异常",501:"已转寄",401:"工单回复"}[e]||"未知状态"),A=e=>new Date(e).toLocaleString("zh-CN");return(e,a)=>{const l=f,t=v,c=_,s=p,C=m,I=R,Y=h,E=w,F=j;return d(),r(F,{modelValue:z.value,"onUpdate:modelValue":a[2]||(a[2]=e=>z.value=e),title:"回调转发详情",width:"80%","before-close":L},{footer:n((()=>[g("div",H,[u(Y,{onClick:L},{default:n((()=>a[9]||(a[9]=[k("关闭")]))),_:1})])])),default:n((()=>[e.callbackRecord?(d(),i("div",N,[u(s,{class:"detail-card"},{header:n((()=>a[3]||(a[3]=[g("span",null,"基本信息",-1)]))),default:n((()=>[u(c,{column:2,border:""},{default:n((()=>[u(l,{label:"记录ID"},{default:n((()=>[g("span",S,b(e.callbackRecord.id),1)])),_:1}),u(l,{label:"事件类型"},{default:n((()=>{return[k(b((a=e.callbackRecord.event_type,{order_status_changed:"订单状态变更",billing_updated:"计费更新",ticket_replied:"工单回复"}[a]||a)),1)];var a})),_:1}),u(l,{label:"运单号"},{default:n((()=>[g("span",q,b(e.callbackRecord.tracking_no||"-"),1)])),_:1}),u(l,{label:"客户订单号"},{default:n((()=>[g("span",D,b(e.callbackRecord.customer_order_no||"-"),1)])),_:1}),u(l,{label:"回调地址"},{default:n((()=>[g("span",J,b(e.callbackRecord.callback_url||"-"),1)])),_:1}),u(l,{label:"转发状态"},{default:n((()=>{return[u(t,{type:(a=e.callbackRecord.status,{success:"success",failed:"danger",pending:"warning",processing:"warning",delivered:"success",exception_delivered:"success",billed:"success",3:"success",100:"success",301:"success",created:"info",submitted:"info",assigned:"info",pending_pickup:"info",awaiting_pickup:"info",picked_up:"info",accepted:"info",collecting:"info",in_transit:"info",delivering:"info",weight_updated:"info",forwarded:"info",1:"info",2:"info",11:"info",102:"info",103:"info",104:"info",208:"info",501:"info",exception:"warning",4:"warning",6:"warning",500:"warning",401:"warning",cancelled:"danger",pickup_failed:"danger",returned:"danger",voided:"danger",5:"danger",10:"danger",400:"danger",203:"danger",204:"danger",205:"danger"}[a]||"info")},{default:n((()=>[k(b(X(e.callbackRecord.status)),1)])),_:1},8,["type"])];var a})),_:1}),u(l,{label:"HTTP状态码"},{default:n((()=>{return[e.callbackRecord.http_status?(d(),r(t,{key:0,type:(a=e.callbackRecord.http_status,a>=200&&a<300?"success":a>=400&&a<500?"warning":a>=500?"danger":"info")},{default:n((()=>[k(b(e.callbackRecord.http_status),1)])),_:1},8,["type"])):(d(),i("span",O,"-"))];var a})),_:1}),u(l,{label:"重试次数"},{default:n((()=>[g("span",{class:y({"retry-warning":e.callbackRecord.retry_count>0})},b(e.callbackRecord.retry_count),3)])),_:1}),u(l,{label:"转发时间"},{default:n((()=>[k(b(A(e.callbackRecord.request_at||e.callbackRecord.created_at)),1)])),_:1}),e.callbackRecord.response_at?(d(),r(l,{key:0,label:"响应时间"},{default:n((()=>[k(b(A(e.callbackRecord.response_at)),1)])),_:1})):o("",!0)])),_:1})])),_:1}),e.callbackRecord.error_message?(d(),r(s,{key:0,class:"detail-card"},{header:n((()=>a[4]||(a[4]=[g("span",null,"转发错误信息",-1)]))),default:n((()=>[g("div",T,[u(C,{title:e.callbackRecord.error_message,type:"warning",closable:!1,"show-icon":""},null,8,["title"])])])),_:1})):o("",!0),u(s,{class:"detail-card"},{header:n((()=>[g("div",P,[a[6]||(a[6]=g("span",null,"发送数据",-1)),u(Y,{type:"link",onClick:M},{default:n((()=>[u(I,null,{default:n((()=>[u(x(V))])),_:1}),a[5]||(a[5]=k(" 复制 "))])),_:1})])])),default:n((()=>[u(E,{modelValue:B.value,"onUpdate:modelValue":a[0]||(a[0]=e=>B.value=e),type:"textarea",rows:10,readonly:"",class:"request-data-textarea"},null,8,["modelValue"])])),_:1}),e.callbackRecord.response_data?(d(),r(s,{key:1,class:"detail-card"},{header:n((()=>[g("div",U,[a[8]||(a[8]=g("span",null,"响应数据",-1)),u(Y,{type:"link",onClick:W},{default:n((()=>[u(I,null,{default:n((()=>[u(x(V))])),_:1}),a[7]||(a[7]=k(" 复制 "))])),_:1})])])),default:n((()=>[u(E,{modelValue:K.value,"onUpdate:modelValue":a[1]||(a[1]=e=>K.value=e),type:"textarea",rows:8,readonly:"",class:"response-data-textarea"},null,8,["modelValue"])])),_:1})):o("",!0)])):o("",!0)])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-6c111faa"]]);export{I as default};
