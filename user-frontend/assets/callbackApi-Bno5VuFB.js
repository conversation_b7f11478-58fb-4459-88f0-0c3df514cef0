var t=(t,r,e)=>new Promise(((a,l)=>{var s=t=>{try{u(e.next(t))}catch(r){l(r)}},n=t=>{try{u(e.throw(t))}catch(r){l(r)}},u=t=>t.done?a(t.value):Promise.resolve(t.value).then(s,n);u((e=e.apply(t,r)).next())}));import{f as r}from"./index-F0f2Vd-3.js";class e{static getCallbackRecords(e){return t(this,null,(function*(){return r.get({url:"/api/v1/user/callback/records",params:e})}))}static getCallbackRecord(e){return t(this,null,(function*(){return r.get({url:`/api/v1/user/callback/records/${e}`})}))}static getCallbackConfig(){return t(this,null,(function*(){return r.get({url:"/api/v1/user/callback/config"})}))}static updateCallbackConfig(e){return t(this,null,(function*(){return r.post({url:"/api/v1/user/callback/config",params:e})}))}static getCallbackStatistics(e,a){return t(this,null,(function*(){return r.get({url:"/api/v1/user/callback/statistics",params:{start_time:e,end_time:a}})}))}static testCallback(){return t(this,null,(function*(){return r.post({url:"/api/v1/user/callback/test"})}))}static getApiKeyInfo(){return t(this,null,(function*(){return r.get({url:"/api/v1/users/profile"})}))}static resetApiKey(){return t(this,null,(function*(){return r.post({url:"/api/v1/users/reset-client-secret"})}))}}export{e as C};
