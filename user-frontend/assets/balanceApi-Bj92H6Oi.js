var t=(t,a,n)=>new Promise(((r,e)=>{var i=t=>{try{s(n.next(t))}catch(a){e(a)}},u=t=>{try{s(n.throw(t))}catch(a){e(a)}},s=t=>t.done?r(t.value):Promise.resolve(t.value).then(i,u);s((n=n.apply(t,a)).next())}));import{f as a}from"./index-F0f2Vd-3.js";class n{static getBalance(){return t(this,null,(function*(){return a.get({url:"/api/v1/balance"})}))}static deposit(n){return t(this,null,(function*(){return a.post({url:"/api/v1/balance/deposit",params:n})}))}static payment(n){return t(this,null,(function*(){return a.post({url:"/api/v1/balance/payment",params:n})}))}static refund(n){return t(this,null,(function*(){return a.post({url:"/api/v1/balance/refund",params:n})}))}static getTransactionHistory(n){return t(this,null,(function*(){return a.get({url:"/api/v1/balance/transactions",params:n})}))}static getTransactionHistoryOptimized(n){return t(this,null,(function*(){return a.get({url:"/api/v1/balance/transactions/optimized",params:n})}))}static clearCache(){return t(this,null,(function*(){return a.post({url:"/api/v1/balance/cache/clear"})}))}static healthCheck(){return t(this,null,(function*(){return a.get({url:"/api/v1/balance/health"})}))}}export{n as B};
