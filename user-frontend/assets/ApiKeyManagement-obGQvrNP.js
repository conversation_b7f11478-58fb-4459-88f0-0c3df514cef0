var e=(e,l,a)=>new Promise(((t,s)=>{var n=e=>{try{c(a.next(e))}catch(l){s(l)}},i=e=>{try{c(a.throw(e))}catch(l){s(l)}},c=e=>e.done?t(e.value):Promise.resolve(e.value).then(n,i);c((a=a.apply(e,l)).next())}));import{_ as l}from"./index-F0f2Vd-3.js";/* empty css                *//* empty css                 *//* empty css                             */import{d as a,r as t,f as s,N as n,o as i,w as c,a2 as u,b as r,c as o,aY as d,aW as v,e as p,x as y,H as _,u as f,bk as m,a as k,bl as h,bm as C,aX as b,K as w,aG as g,Y as A,aV as I,y as x,b7 as S,aR as j,M as P}from"./vendor-BVh5F9vp.js";import{C as V}from"./callbackApi-Bno5VuFB.js";import{E as z}from"./errorHandler-Dnd-hi0l.js";const D={class:"card-header"},E={class:"api-key-content"},K={class:"key-display"},H={class:"client-id"},M={class:"key-display"},N={class:"secret-display"},U={key:0,class:"client-secret"},Y={key:1,class:"secret-hidden"},G={class:"secret-actions"},L={class:"secret-note"},R={key:0,class:"no-secret"},T={key:1,class:"hidden-note"},W={class:"reset-warning"},X={class:"dialog-footer"},$={class:"new-secret-content"},q={class:"secret-display"},B={class:"secret-value"},F={class:"new-secret"},J={class:"dialog-footer"},O=l(a({__name:"ApiKeyManagement",setup(l){const a=t(!1),O=t(!1),Q=t(!1),Z=t(!1),ee=t(!1),le=t(null),ae=t(""),te=t(""),se=()=>e(this,null,(function*(){a.value=!0;try{const e=yield V.getApiKeyInfo();if(e.success&&e.data){const l=e.data;le.value={client_id:l.client_id||"",created_at:l.created_at||"",last_used_at:l.last_used_at}}}catch(e){z.handleApiError(e)}finally{a.value=!1}})),ne=()=>e(this,null,(function*(){try{O.value=!0;const e=yield V.resetApiKey();if(!e.success||!e.data)throw new Error(e.message||"重置失败");{const l=e.data.new_client_secret||e.data.client_secret||"";ae.value=l,te.value=l,ee.value=!1,Q.value=!1,Z.value=!0,yield se(),P.success("API密钥重置成功")}}catch(e){z.handleApiError(e)}finally{O.value=!1}})),ie=()=>{ee.value=!ee.value},ce=(l,a)=>e(this,null,(function*(){try{yield navigator.clipboard.writeText(l),P.success(`${a}已复制到剪贴板`)}catch(e){P.error("复制失败")}})),ue=e=>e?new Date(e).toLocaleString("zh-CN"):"-",re=()=>{Q.value=!1},oe=()=>{Z.value=!1,ae.value=""};return s((()=>{se()})),(e,l)=>{const t=x,s=_,P=v,V=b,z=d,se=I,de=A,ve=j,pe=g;return i(),n(ve,{class:"api-key-card"},{header:c((()=>[p("div",D,[l[8]||(l[8]=p("span",null,"API密钥管理",-1)),r(s,{type:"danger",size:"small",onClick:l[0]||(l[0]=e=>Q.value=!0)},{default:c((()=>[r(t,null,{default:c((()=>[r(f(S))])),_:1}),l[7]||(l[7]=w(" 重置密钥 "))])),_:1})])])),default:c((()=>[u((i(),o("div",E,[r(z,{column:1,border:""},{default:c((()=>[r(P,{label:"客户端ID"},{default:c((()=>{var e;return[p("div",K,[p("code",H,y((null==(e=le.value)?void 0:e.client_id)||"-"),1),r(s,{type:"link",size:"small",onClick:l[1]||(l[1]=e=>{var l;return ce((null==(l=le.value)?void 0:l.client_id)||"","Client ID")}),icon:f(m),title:"复制Client ID"},null,8,["icon"])])]})),_:1}),r(P,{label:"客户端密钥"},{default:c((()=>[p("div",M,[p("div",N,[ee.value&&te.value?(i(),o("code",U,y(te.value),1)):(i(),o("span",Y,"••••••••••••••••••••••••••••••••")),p("div",G,[te.value?(i(),n(s,{key:0,type:"link",size:"small",onClick:ie,icon:ee.value?f(h):f(C),title:ee.value?"隐藏密钥":"显示密钥"},null,8,["icon","title"])):k("",!0),ee.value&&te.value?(i(),n(s,{key:1,type:"link",size:"small",onClick:l[2]||(l[2]=e=>ce(te.value,"Client Secret")),icon:f(m),title:"复制Client Secret"},null,8,["icon"])):k("",!0)])]),p("div",L,[te.value?ee.value?k("",!0):(i(),o("span",T,"点击眼睛图标显示密钥")):(i(),o("span",R,'密钥已隐藏，点击"重置密钥"获取新密钥'))])])])),_:1}),r(P,{label:"状态"},{default:c((()=>[r(V,{type:"success"},{default:c((()=>l[9]||(l[9]=[w(" 活跃 ")]))),_:1})])),_:1}),r(P,{label:"创建时间"},{default:c((()=>{var e;return[w(y(ue(null==(e=le.value)?void 0:e.created_at)),1)]})),_:1}),r(P,{label:"最后使用"},{default:c((()=>{var e;return[w(y((null==(e=le.value)?void 0:e.last_used_at)?ue(le.value.last_used_at):"从未使用"),1)]})),_:1})])),_:1})])),[[pe,a.value]]),r(de,{modelValue:Q.value,"onUpdate:modelValue":l[4]||(l[4]=e=>Q.value=e),title:"重置API密钥",width:"500px","before-close":re},{footer:c((()=>[p("div",X,[r(s,{onClick:l[3]||(l[3]=e=>Q.value=!1)},{default:c((()=>l[11]||(l[11]=[w("取消")]))),_:1}),r(s,{type:"danger",onClick:ne,loading:O.value},{default:c((()=>l[12]||(l[12]=[w(" 确认重置 ")]))),_:1},8,["loading"])])])),default:c((()=>[p("div",W,[r(se,{title:"重要提醒",type:"warning",closable:!1,"show-icon":""},{default:c((()=>l[10]||(l[10]=[p("p",null,"重置API密钥将会：",-1),p("ul",null,[p("li",null,"生成新的Client Secret"),p("li",null,"使旧的Client Secret立即失效"),p("li",null,"需要更新所有使用该密钥的应用")],-1),p("p",null,[p("strong",null,"请确保您已准备好更新相关配置！")],-1)]))),_:1})])])),_:1},8,["modelValue"]),r(de,{modelValue:Z.value,"onUpdate:modelValue":l[6]||(l[6]=e=>Z.value=e),title:"新的API密钥",width:"600px","close-on-click-modal":!1,"close-on-press-escape":!1},{footer:c((()=>[p("div",J,[r(s,{type:"primary",onClick:oe},{default:c((()=>l[16]||(l[16]=[w(" 我已保存，关闭 ")]))),_:1})])])),default:c((()=>[p("div",$,[r(se,{title:"请立即保存新密钥",type:"success",closable:!1,"show-icon":""},{default:c((()=>l[13]||(l[13]=[p("p",null,"新的Client Secret已生成，请立即复制并安全保存。",-1),p("p",null,[p("strong",null,"此密钥只会显示一次，关闭后将无法再次查看！")],-1)]))),_:1}),p("div",q,[l[15]||(l[15]=p("label",null,"新的Client Secret：",-1)),p("div",B,[p("code",F,y(ae.value),1),r(s,{type:"primary",onClick:l[5]||(l[5]=e=>ce(ae.value,"Client Secret")),icon:f(m)},{default:c((()=>l[14]||(l[14]=[w(" 复制 ")]))),_:1},8,["icon"])])])])])),_:1},8,["modelValue"])])),_:1})}}}),[["__scopeId","data-v-6e4eec75"]]);export{O as default};
