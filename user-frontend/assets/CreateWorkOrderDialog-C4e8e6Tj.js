var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,d=(a,l,r)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,n=(e,a,l)=>new Promise(((r,t)=>{var o=e=>{try{n(l.next(e))}catch(a){t(a)}},d=e=>{try{n(l.throw(e))}catch(a){t(a)}},n=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,d);n((l=l.apply(e,a)).next())}));import{_ as s}from"./index-F0f2Vd-3.js";/* empty css                  *//* empty css                    *//* empty css                *//* empty css                             *//* empty css                 *//* empty css                   */import{d as i,r as u,X as c,j as p,f as _,p as f,N as v,o as m,w as g,a2 as y,Z as h,b as k,a as w,c as b,bs as x,K as V,_ as j,ar as O,u as D,F as P,A as U,as as B,e as W,x as A,aV as E,C as I,aR as M,aY as q,aW as C,aX as z,H as F,aq as N,bD as R,y as S,bE as T,aG as H,Y as J,M as X}from"./vendor-BVh5F9vp.js";import{E as Y}from"./expressApi-BL8dLu27.js";import{W as G}from"./workOrderApi-4juIOmO-.js";import{E as K}from"./errorHandler-Dnd-hi0l.js";import{u as Z}from"./useWorkOrderTypes-B5Knz6RB.js";const L={class:"service-type-option"},Q={class:"type-label"},$={class:"type-description"},ee={key:0,class:"business-restrictions"},ae={class:"restriction-title"},le={class:"restriction-content"},re={class:"restriction-list"},te={key:1,class:"prefilled-order-info"},oe={class:"card-header"},de=s(i({__name:"CreateWorkOrderDialog",props:{visible:{type:Boolean},isPageMode:{type:Boolean,default:!1},prefilledOrderData:{default:void 0}},emits:["update:visible","success","cancel"],setup(e,{emit:s}){const i=e,de=s,{userFriendlyTypes:ne,loading:se,fetchWorkOrderTypes:ie,needsWeightInfo:ue,needsValueInfo:ce,needsAmountInfo:pe}=Z(),_e=u(!1),fe=u(!1),ve=u(),me=u(),ge=u([]),ye=u(null),he=c({work_order_type:void 0,content:"",order_no:"",tracking_no:"",feedback_weight:void 0,goods_value:void 0,overweight_amount:void 0,attachment_urls:[]}),ke={work_order_type:[{required:!0,message:"请选择服务类型",trigger:"change"}],content:[{required:!0,message:"请输入问题描述",trigger:"blur"},{min:5,message:"问题描述至少5个字符",trigger:"blur"},{max:2e3,message:"问题描述不能超过2000个字符",trigger:"blur"}]},we=p({get:()=>i.visible,set:e=>de("update:visible",e)}),be=p((()=>ye.value?"申请售后服务":"创建售后工单")),xe=p((()=>ye.value?"提交申请":"创建工单")),Ve=p((()=>!!he.work_order_type&&ue(he.work_order_type))),je=p((()=>!!he.work_order_type&&ce(he.work_order_type))),Oe=p((()=>!!he.work_order_type&&pe(he.work_order_type))),De=p((()=>{if(!he.work_order_type)return"";const e=ne.value.find((e=>e.value===he.work_order_type));return(null==e?void 0:e.label)||""})),Pe=p((()=>{if(!he.work_order_type)return null;return{1:['订单状态：必须为"待取件"状态',"时间要求：下单时间超过30分钟","适用场景：快递员未及时上门取件"],2:['订单状态：必须为"已结算"状态',"时间要求：下单后次月7号前提交","适用场景：实际重量与下单重量不符需要重新核算费用"],10:["时间要求：下单后1个月内",'订单状态：运输状态为"派件中"',"适用场景：包裹已到达目的地但未及时派送"],14:["时间要求：下单后1天至次月7号前可提交","停滞判断：24小时内无物流轨迹更新","适用场景：包裹长时间停留在某个网点"],15:['订单状态：运输状态为"待取件"',"时间要求：下单后1个月内","适用场景：当前快递员无法提供服务需要更换"],16:["时间要求：下单后1天至次月7号前可提交","状态要求：订单未完成配送","适用场景：取消发货或订单变更"]}[he.work_order_type]||null}));_((()=>n(this,null,(function*(){yield ie()})))),f((()=>i.visible),(e=>n(this,null,(function*(){e&&(qe(),0===ne.value.length&&(yield ie()),i.prefilledOrderData&&(ye.value=i.prefilledOrderData,he.order_no=i.prefilledOrderData.order_no,he.tracking_no=i.prefilledOrderData.tracking_no))})))),f((()=>i.prefilledOrderData),(e=>{e&&(ye.value=e,he.order_no=e.order_no,he.tracking_no=e.tracking_no)}));const Ue=()=>n(this,null,(function*(){he.order_no&&!ye.value&&(yield We(he.order_no,"order_no"))})),Be=()=>n(this,null,(function*(){he.tracking_no&&!ye.value&&(yield We(he.tracking_no,"tracking_no"))})),We=(e,a)=>n(this,null,(function*(){try{_e.value=!0;const l=yield Y.queryOrder({[a]:e});if(l.success&&l.data){const e={id:0,customer_order_no:l.data.order_no,order_no:l.data.order_no,tracking_no:l.data.tracking_no,express_type:l.data.express_type,express_name:l.data.express_type,provider:"",provider_name:"快递服务商",status:l.data.status,status_desc:l.data.status_desc,weight:l.data.weight,price:l.data.price,actual_fee:l.data.actual_fee,insurance_fee:l.data.insurance_fee,overweight_fee:l.data.overweight_fee,underweight_fee:l.data.underweight_fee,weight_adjustment_reason:l.data.weight_adjustment_reason,billing_status:l.data.billing_status,sender_info:JSON.stringify(l.data.sender_info),receiver_info:JSON.stringify(l.data.receiver_info),order_volume:l.data.order_volume,actual_weight:l.data.actual_weight,actual_volume:l.data.actual_volume,charged_weight:l.data.charged_weight,created_at:l.data.created_at,updated_at:l.data.updated_at};ye.value=e,he.order_no=e.order_no,he.tracking_no=e.tracking_no,X.success("已自动关联订单信息")}}catch(l){}finally{_e.value=!1}})),Ae=()=>{ye.value=null,he.order_no="",he.tracking_no=""},Ee=e=>{if(ge.value.length>=10)return X.error("最多只能上传10个文件"),!1;if(e.size&&e.size>20971520)return X.error("文件大小不能超过20MB"),!1;return e.raw&&!["image/jpeg","image/png","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(e.raw.type)?(X.error("只支持jpg/png/pdf/doc/docx格式的文件"),!1):void 0},Ie=e=>{const a=ge.value.findIndex((a=>a.uid===e.uid));a>-1&&ge.value.splice(a,1)},Me=()=>n(this,null,(function*(){if(ve.value){try{yield ve.value.validate()}catch(s){return}fe.value=!0;try{let s=[];if(ge.value.length>0){const e=ge.value.map((e=>e.raw)).filter(Boolean);s=(yield G.uploadMultipleAttachments(e)).map((e=>e.file_url))}const i=(e=((e,a)=>{for(var l in a||(a={}))t.call(a,l)&&d(e,l,a[l]);if(r)for(var l of r(a))o.call(a,l)&&d(e,l,a[l]);return e})({},he),n={attachment_urls:s,order_no:he.order_no||void 0,tracking_no:he.tracking_no||void 0},a(e,l(n))),u=yield G.createWorkOrder(i);if(!u.success)throw new Error(u.message||"提交失败");X.success(ye.value?"售后申请提交成功，我们将尽快处理":"工单创建成功"),de("success")}catch(i){K.handleApiError(i)}finally{fe.value=!1}var e,n}})),qe=()=>{var e;Object.assign(he,{work_order_type:void 0,content:"",order_no:"",tracking_no:"",feedback_weight:void 0,goods_value:void 0,overweight_amount:void 0,callback_url:"",message_callback_url:"",attachment_urls:[]}),ge.value=[],ye.value=null,null==(e=ve.value)||e.resetFields()},Ce=()=>{i.isPageMode?de("cancel"):de("update:visible",!1)};return(e,a)=>{const l=x,r=B,t=O,o=E,d=j,n=I,s=F,i=C,u=z,c=q,p=M,_=N,f=S,X=R,Y=h,G=J,K=H;return m(),v(G,{modelValue:we.value,"onUpdate:modelValue":a[7]||(a[7]=e=>we.value=e),title:be.value,width:"60%","before-close":Ce,"destroy-on-close":""},{footer:g((()=>[k(s,{onClick:Ce},{default:g((()=>a[25]||(a[25]=[V("取消")]))),_:1}),k(s,{type:"primary",loading:fe.value,onClick:Me},{default:g((()=>[V(A(xe.value),1)])),_:1},8,["loading"])])),default:g((()=>[y((m(),v(Y,{ref_key:"formRef",ref:ve,model:he,rules:ke,"label-width":"120px"},{default:g((()=>[k(l,{"content-position":"left"},{default:g((()=>a[8]||(a[8]=[V("售后服务申请")]))),_:1}),k(d,{label:"服务类型",prop:"work_order_type"},{default:g((()=>[k(t,{modelValue:he.work_order_type,"onUpdate:modelValue":a[0]||(a[0]=e=>he.work_order_type=e),placeholder:"请选择需要的售后服务类型",loading:D(se)},{default:g((()=>[(m(!0),b(P,null,U(D(ne),(e=>(m(),v(r,{key:e.value,label:e.label,value:e.value},{default:g((()=>[W("div",L,[W("span",Q,A(e.label),1),W("span",$,A(e.description),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"]),Pe.value?(m(),b("div",ee,[k(o,{type:"info",closable:!1,"show-icon":!0,class:"restriction-alert"},{title:g((()=>[W("span",ae,A(De.value)+" 的申请条件",1)])),default:g((()=>[W("div",le,[W("ul",re,[(m(!0),b(P,null,U(Pe.value,(e=>(m(),b("li",{key:e},A(e),1)))),128))]),a[9]||(a[9]=W("p",{class:"restriction-note"}," 💡 如您的订单不符合上述条件，工单仍会创建但供应商可能无法处理 ",-1))])])),_:1})])):w("",!0)])),_:1}),k(d,{label:"问题描述",prop:"content"},{default:g((()=>[k(n,{modelValue:he.content,"onUpdate:modelValue":a[1]||(a[1]=e=>he.content=e),type:"textarea",rows:4,placeholder:"请详细描述您遇到的问题，我们将尽快为您处理...",maxlength:"2000","show-word-limit":""},null,8,["modelValue"])])),_:1}),k(l,{"content-position":"left"},{default:g((()=>a[10]||(a[10]=[V("关联订单")]))),_:1}),ye.value?w("",!0):(m(),v(d,{key:0,label:"订单号"},{default:g((()=>[k(n,{modelValue:he.order_no,"onUpdate:modelValue":a[2]||(a[2]=e=>he.order_no=e),placeholder:"请输入订单号（可选）",clearable:"",onBlur:Ue},null,8,["modelValue"]),a[11]||(a[11]=W("div",{class:"form-tip"}," 输入订单号可自动关联订单信息，便于快速处理 ",-1))])),_:1})),ye.value?(m(),b("div",te,[k(p,{class:"order-info-card"},{header:g((()=>[W("div",oe,[a[13]||(a[13]=W("span",null,"关联订单信息",-1)),k(s,{type:"text",onClick:Ae},{default:g((()=>a[12]||(a[12]=[V("更换订单")]))),_:1})])])),default:g((()=>[k(c,{column:2,size:"small"},{default:g((()=>[k(i,{label:"订单号"},{default:g((()=>[V(A(ye.value.order_no),1)])),_:1}),k(i,{label:"运单号"},{default:g((()=>[V(A(ye.value.tracking_no),1)])),_:1}),k(i,{label:"快递公司"},{default:g((()=>[V(A(ye.value.express_name),1)])),_:1}),k(i,{label:"订单状态"},{default:g((()=>{return[k(u,{type:(e=ye.value.status,{pending:"warning",processing:"primary",completed:"success",cancelled:"danger"}[e]||"info")},{default:g((()=>[V(A(ye.value.status_desc),1)])),_:1},8,["type"])];var e})),_:1})])),_:1})])),_:1})])):w("",!0),ye.value?w("",!0):(m(),v(d,{key:2,label:"运单号"},{default:g((()=>[k(n,{modelValue:he.tracking_no,"onUpdate:modelValue":a[3]||(a[3]=e=>he.tracking_no=e),placeholder:"请输入运单号（可选）",clearable:"",onBlur:Be},null,8,["modelValue"]),a[14]||(a[14]=W("div",{class:"form-tip"}," 输入运单号可自动关联订单信息 ",-1))])),_:1})),k(l,{"content-position":"left"},{default:g((()=>a[15]||(a[15]=[V("补充信息")]))),_:1}),Ve.value?(m(),v(d,{key:3,label:"实际重量"},{default:g((()=>[k(_,{modelValue:he.feedback_weight,"onUpdate:modelValue":a[4]||(a[4]=e=>he.feedback_weight=e),min:0,max:999,precision:2,placeholder:"请输入实际重量",style:{width:"200px"}},null,8,["modelValue"]),a[16]||(a[16]=W("span",{style:{"margin-left":"8px",color:"#909399"}},"kg",-1)),a[17]||(a[17]=W("div",{class:"form-tip"}," 如实际重量与下单重量不符，请填写此项 ",-1))])),_:1})):w("",!0),je.value?(m(),v(d,{key:4,label:"货物价值"},{default:g((()=>[k(_,{modelValue:he.goods_value,"onUpdate:modelValue":a[5]||(a[5]=e=>he.goods_value=e),min:0,max:999999,precision:2,placeholder:"请输入货物价值",style:{width:"200px"}},null,8,["modelValue"]),a[18]||(a[18]=W("span",{style:{"margin-left":"8px",color:"#909399"}},"元",-1)),a[19]||(a[19]=W("div",{class:"form-tip"}," 用于理赔金额计算 ",-1))])),_:1})):w("",!0),Oe.value?(m(),v(d,{key:5,label:"费用金额"},{default:g((()=>[k(_,{modelValue:he.overweight_amount,"onUpdate:modelValue":a[6]||(a[6]=e=>he.overweight_amount=e),min:0,max:999999,precision:2,placeholder:"请输入相关费用金额",style:{width:"200px"}},null,8,["modelValue"]),a[20]||(a[20]=W("span",{style:{"margin-left":"8px",color:"#909399"}},"元",-1)),a[21]||(a[21]=W("div",{class:"form-tip"}," 如涉及费用争议，请填写相关金额 ",-1))])),_:1})):w("",!0),k(l,{"content-position":"left"},{default:g((()=>a[22]||(a[22]=[V("相关凭证")]))),_:1}),k(d,{label:"上传凭证"},{default:g((()=>[k(X,{ref_key:"uploadRef",ref:me,"file-list":ge.value,"auto-upload":!1,"on-change":Ee,"on-remove":Ie,multiple:"",drag:""},{tip:g((()=>a[23]||(a[23]=[W("div",{class:"el-upload__tip"},[V(" 可上传相关照片、截图或文档作为问题凭证"),W("br"),V(" 支持jpg/png/pdf/doc/docx文件，单个文件不超过20MB，最多上传10个文件 ")],-1)]))),default:g((()=>[k(f,{class:"el-icon--upload"},{default:g((()=>[k(D(T))])),_:1}),a[24]||(a[24]=W("div",{class:"el-upload__text"},[V(" 将文件拖到此处，或"),W("em",null,"点击上传")],-1))])),_:1},8,["file-list"])])),_:1})])),_:1},8,["model"])),[[K,_e.value]])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-d6bc843a"]]);export{de as default};
