var e=Object.defineProperty,a=Object.defineProperties,t=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,i=(a,t,l)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[t]=l,c=(e,a,t)=>new Promise(((l,o)=>{var s=e=>{try{c(t.next(e))}catch(a){o(a)}},i=e=>{try{c(t.throw(e))}catch(a){o(a)}},c=e=>e.done?l(e.value):Promise.resolve(e.value).then(s,i);c((t=t.apply(e,a)).next())}));import{_ as r}from"./index-F0f2Vd-3.js";/* empty css                       */import"./el-collapse-transition-l0sNRNKZ.js";/* empty css               *//* empty css                */import{d as n,r as d,X as u,j as p,f as g,M as v,c as f,o as _,b as y,w as m,e as b,a2 as h,bc as k,aQ as C,aN as w,aR as j,y as F,u as E,bd as x,x as O,be as A,bf as z,bg as V,a4 as D,Z as S,_ as M,C as U,ar as L,F as P,A as R,N as Y,as as H,bh as I,H as q,K as W,b7 as B,aI as N,bi as Q,aJ as K,aH as T,al as J,aX as X,v as G,aG as Z,aK as $,bj as ee,b6 as ae}from"./vendor-BVh5F9vp.js";import{C as te}from"./callbackApi-Bno5VuFB.js";import{E as le}from"./errorHandler-Dnd-hi0l.js";import oe from"./CallbackDetailDialog-CJ7TnMWJ.js";import se from"./CallbackConfigDialog-Cvr1KN6v.js";import ie from"./ApiKeyManagement-obGQvrNP.js";/* empty css                 *//* empty css                             */const ce={created:{category:"下单",name:"已下单",description:"订单已创建",color:"#409EFF",icon:"Document"},confirmed:{category:"下单",name:"已确认",description:"订单已确认",color:"#409EFF",icon:"Select"},assigned:{category:"揽收",name:"已分配快递员",description:"已分配快递员，等待揽收",color:"#E6A23C",icon:"User"},awaiting_pickup:{category:"揽收",name:"等待揽收",description:"等待快递员上门揽收",color:"#E6A23C",icon:"Clock"},picked_up:{category:"揽收",name:"已揽收",description:"快递员已上门取件",color:"#67C23A",icon:"Check"},collected:{category:"揽收",name:"已收件",description:"快递已收件",color:"#67C23A",icon:"Box"},in_transit:{category:"运输",name:"运输中",description:"包裹正在运输途中",color:"#409EFF",icon:"Van"},arrived_at_destination:{category:"运输",name:"到达目的地",description:"包裹已到达目的地城市",color:"#409EFF",icon:"LocationInformation"},out_for_delivery:{category:"派送",name:"派送中",description:"包裹正在派送中",color:"#E6A23C",icon:"Van"},delivered:{category:"签收",name:"已签收",description:"包裹已成功签收",color:"#67C23A",icon:"SuccessFilled"},self_pickup:{category:"签收",name:"自提",description:"包裹已放置自提点",color:"#67C23A",icon:"Shop"},exception:{category:"异常",name:"异常",description:"包裹运输异常",color:"#F56C6C",icon:"WarningFilled"},rejected:{category:"异常",name:"拒收",description:"收件人拒收包裹",color:"#F56C6C",icon:"CircleCloseFilled"},returned:{category:"异常",name:"退回",description:"包裹已退回发件人",color:"#F56C6C",icon:"RefreshLeft"},lost:{category:"异常",name:"丢失",description:"包裹丢失",color:"#F56C6C",icon:"QuestionFilled"},damaged:{category:"异常",name:"破损",description:"包裹破损",color:"#F56C6C",icon:"WarningFilled"},cancelled:{category:"取消",name:"已取消",description:"订单已取消",color:"#909399",icon:"CircleClose"},timeout_cancelled:{category:"取消",name:"超时取消",description:"订单超时自动取消",color:"#909399",icon:"AlarmClock"}},re={freight:{category:"计费",name:"运费",description:"基础运输费用",color:"#409EFF",icon:"Money"},insurance:{category:"计费",name:"保价费",description:"保价服务费用",color:"#E6A23C",icon:"Lock"},packaging:{category:"计费",name:"包装费",description:"包装材料费用",color:"#909399",icon:"Box"},fuel:{category:"计费",name:"燃油费",description:"燃油附加费",color:"#F56C6C",icon:"Lightning"},return:{category:"计费",name:"逆向费",description:"退货运输费用",color:"#909399",icon:"RefreshLeft"},other:{category:"计费",name:"其他费用",description:"其他杂项费用",color:"#909399",icon:"More"}},ne={pickup_urge:{category:"工单",name:"催揽收",description:"催促快递员揽收",color:"#E6A23C",icon:"Bell"},weight_exception:{category:"工单",name:"重量异常",description:"包裹重量异常处理",color:"#F56C6C",icon:"Warning"},fake_pickup:{category:"工单",name:"虚假揽收",description:"虚假揽收投诉",color:"#F56C6C",icon:"WarningFilled"},damage:{category:"工单",name:"破损",description:"包裹破损处理",color:"#F56C6C",icon:"WarningFilled"},loss:{category:"工单",name:"遗失",description:"包裹遗失处理",color:"#F56C6C",icon:"QuestionFilled"},delivery_urge:{category:"工单",name:"催派送",description:"催促快递员派送",color:"#E6A23C",icon:"Timer"},cancel_order:{category:"工单",name:"取消运单",description:"取消运单申请",color:"#909399",icon:"CircleClose"}};function de(e,c,r,n){if(n){if(c&&ce[c]){return d=((e,a)=>{for(var t in a||(a={}))o.call(a,t)&&i(e,t,a[t]);if(l)for(var t of l(a))s.call(a,t)&&i(e,t,a[t]);return e})({},ce[c]),a(d,t({category:n}))}switch(n){case"状态更新":return{category:"状态更新",name:ue(c)||"状态更新",description:"订单状态发生变更",color:"#409EFF",icon:"Refresh"};case"计费":return{category:"计费",name:"计费更新",description:"订单计费信息更新",color:"#E6A23C",icon:"Money"};case"揽收":return{category:"揽收",name:"揽收通知",description:"快递员揽收通知",color:"#67C23A",icon:"Check"};case"工单":return{category:"工单",name:"工单回复",description:"工单处理结果通知",color:"#909399",icon:"ChatDotRound"};default:return{category:n,name:n,description:`${n}相关通知`,color:"#909399",icon:"Bell"}}}var d;switch(e){case"order_status_changed":if(c&&ce[c])return ce[c];if(null==r?void 0:r.status){const e=r.status.toLowerCase();if(ce[e])return ce[e]}return{category:"状态更新",name:"状态更新",description:"订单状态发生变更",color:"#409EFF",icon:"Refresh"};case"billing_updated":return(null==r?void 0:r.fee_type)&&re[r.fee_type]?re[r.fee_type]:{category:"计费",name:"计费更新",description:"订单计费信息更新",color:"#E6A23C",icon:"Money"};case"ticket_replied":return(null==r?void 0:r.ticket_type)&&ne[r.ticket_type]?ne[r.ticket_type]:{category:"工单",name:"工单回复",description:"工单处理结果通知",color:"#909399",icon:"ChatDotRound"};default:return{category:"其他",name:"未知事件",description:"未知的回调事件",color:"#909399",icon:"QuestionFilled"}}}function ue(e){if(!e)return"未知状态";return{created:"已创建",in_transit:"运输中",delivered:"已签收",exception:"异常",cancelled:"已取消",picked_up:"已揽收",out_for_delivery:"派送中"}[e]||e}const pe={class:"callback-list"},ge={class:"card-header"},ve={class:"header-actions"},fe={class:"statistics-section"},_e={class:"stat-item"},ye={class:"stat-icon total"},me={class:"stat-content"},be={class:"stat-value"},he={class:"stat-item"},ke={class:"stat-icon success"},Ce={class:"stat-content"},we={class:"stat-value"},je={class:"stat-item"},Fe={class:"stat-icon today"},Ee={class:"stat-content"},xe={class:"stat-value"},Oe={class:"stat-item"},Ae={class:"stat-icon failed"},ze={class:"stat-content"},Ve={class:"stat-value"},De={class:"search-area"},Se={style:{float:"left"}},Me={style:{float:"right",color:"#8492a6","font-size":"13px"}},Ue={key:0,class:"empty-state"},Le={class:"record-id"},Pe={class:"status-detail"},Re={class:"status-info"},Ye={class:"status-name"},He={class:"status-category"},Ie={key:0,class:"tracking-no"},qe={key:1,class:"no-data"},We={key:0,class:"order-no"},Be={key:1,class:"no-data"},Ne={class:"pagination-wrapper"},Qe=r(n({__name:"CallbackList",setup(e){const a=d(!1),t=d(!1),l=d(!1),o=d(!1),s=d([]),i=d(null),r=d(null),n=d(void 0),ue=u({page:1,page_size:20,tracking_no:void 0,event_type:void 0,status:void 0,status_category:void 0,specific_status:void 0}),Qe=u({page:1,page_size:20,total:0}),Ke=p((()=>function(){const e=new Set;return Object.values(ce).forEach((a=>e.add(a.category))),Object.values(re).forEach((a=>e.add(a.category))),Object.values(ne).forEach((a=>e.add(a.category))),Array.from(e).map((e=>({label:e,value:e})))}())),Te=p((()=>function(){const e=[];return Object.entries(ce).forEach((([a,t])=>{e.push({label:t.name,value:a,category:t.category})})),Object.entries(re).forEach((([a,t])=>{e.push({label:t.name,value:a,category:t.category})})),Object.entries(ne).forEach((([a,t])=>{e.push({label:t.name,value:a,category:t.category})})),e.sort(((e,a)=>e.label.localeCompare(a.label)))}())),Je=p((()=>ue.status_category?Te.value.filter((e=>e.category===ue.status_category)):Te.value)),Xe=(e=!1)=>c(this,null,(function*(){var t,l,o,i;a.value=!0;try{const a={page:Qe.page,page_size:Qe.page_size,tracking_no:ue.tracking_no||void 0,event_type:ue.event_type||void 0,status:ue.status||void 0,status_category:ue.status_category||void 0,specific_status:ue.specific_status||void 0,start_time:(null==(t=n.value)?void 0:t[0])||void 0,end_time:(null==(l=n.value)?void 0:l[1])||void 0,_t:e?Date.now():void 0},c=yield te.getCallbackRecords(a);if(c.success&&c.data)s.value=c.data.records||[],Qe.total=c.data.total||(null==(o=c.data.records)?void 0:o.length)||0;else{if(!(null==(i=c.message)?void 0:i.includes("未认证")))throw new Error(c.message||"获取回调记录失败");s.value=[],Qe.total=0}}catch(c){le.handleApiError(c),s.value=[],Qe.total=0}finally{a.value=!1}})),Ge=()=>c(this,null,(function*(){var e,a;try{const t=yield te.getCallbackStatistics(null==(e=n.value)?void 0:e[0],null==(a=n.value)?void 0:a[1]);t.success&&t.data&&(r.value=t.data)}catch(t){}})),Ze=e=>{let a="",t={},l=0;if(e.request_data)try{const o="string"==typeof e.request_data?JSON.parse(e.request_data):e.request_data;if(o.pushType&&(l=parseInt(o.pushType)),o.contextObj){if(o.contextObj.ydOrderStatus){a={1:"created",2:"in_transit",3:"delivered",4:"exception",5:"cancelled"}[o.contextObj.ydOrderStatus]||"unknown"}t=o.contextObj}o.data&&o.data.status&&(a=o.data.status.code||o.data.status,t=o.data)}catch(s){}let o="其他";switch(l){case 1:o="状态更新";break;case 2:o="计费";break;case 3:o="揽收";break;default:switch(e.event_type){case"order_status_changed":o="状态更新";break;case"billing_updated":o="计费";break;case"ticket_replied":o="工单";break;default:o="其他"}}return de(e.event_type,a,t,o)},$e=()=>{Qe.page=1,Xe(!0),Ge()},ea=()=>{Object.assign(ue,{page:1,page_size:20,tracking_no:void 0,event_type:void 0,status:void 0,status_category:void 0,specific_status:void 0}),n.value=void 0,Qe.page=1,Xe(!0),Ge()},aa=e=>{Qe.page_size=e,Qe.page=1,Xe(!0)},ta=e=>{Qe.page=e,Xe(!0)},la=()=>c(this,null,(function*(){try{const e=yield te.testCallback();e.success?v.success("测试回调已发送，请检查您的回调地址"):v.error(e.message||"测试回调发送失败")}catch(e){le.handleApiError(e)}})),oa=()=>{v.info("正在刷新最新数据..."),Xe(!0),Ge()},sa=()=>{v.success("回调配置更新成功"),Xe(!0),Ge()},ia=e=>({success:"成功",failed:"失败",pending:"处理中",processing:"处理中",created:"已下单",submitted:"已提交",assigned:"已分配",pending_pickup:"待取件",awaiting_pickup:"待取件",picked_up:"已取件",pickup_failed:"取件失败",accepted:"已接受",collecting:"收件中",in_transit:"运输中",delivering:"派送中",delivered:"已签收",exception_delivered:"异常签收",exception:"异常",cancelled:"已取消",returned:"已退回",forwarded:"已转寄",weight_updated:"重量更新",billed:"已计费",voided:"已作废",1:"待取件",2:"运输中",3:"已签收",4:"异常",5:"已取消",6:"异常",10:"已取消",11:"已取件",100:"下单成功",400:"下单失败",102:"分配网点",103:"分配快递员",104:"已取件",301:"已计费",208:"重量更新",203:"已取消",204:"取件失败",205:"已作废",500:"异常",501:"已转寄",401:"工单回复"}[e]||"未知状态");return g((()=>{localStorage.getItem("access_token")?(Xe(),Ge()):v.warning("请先登录后再访问回调管理")})),(e,c)=>{const d=F,u=q,p=j,g=w,v=C,te=k,le=U,ce=M,re=H,ne=L,de=I,Te=S,Xe=N,Ge=T,ca=X,ra=K,na=$,da=Z;return _(),f("div",pe,[y(ie),y(p,{class:"box-card"},{header:m((()=>[b("div",ge,[c[14]||(c[14]=b("span",null,"回调记录管理",-1)),b("div",ve,[y(u,{type:"primary",onClick:c[0]||(c[0]=e=>o.value=!0)},{default:m((()=>[y(d,null,{default:m((()=>[y(E(ee))])),_:1}),c[11]||(c[11]=W(" 回调配置 "))])),_:1}),y(u,{type:"info",onClick:c[1]||(c[1]=e=>t.value=!t.value)},{default:m((()=>[y(d,null,{default:m((()=>[y(E(ae))])),_:1}),c[12]||(c[12]=W(" 统计信息 "))])),_:1}),y(u,{onClick:la},{default:m((()=>[y(d,null,{default:m((()=>[y(E(Q))])),_:1}),c[13]||(c[13]=W(" 测试回调 "))])),_:1})])])])),default:m((()=>[y(te,null,{default:m((()=>[h(b("div",fe,[y(v,{gutter:20,class:"statistics-cards"},{default:m((()=>[y(g,{span:6},{default:m((()=>[y(p,{class:"stat-card"},{default:m((()=>{var e;return[b("div",_e,[b("div",ye,[y(d,null,{default:m((()=>[y(E(x))])),_:1})]),b("div",me,[b("div",be,O((null==(e=r.value)?void 0:e.total_callbacks)||0),1),c[15]||(c[15]=b("div",{class:"stat-label"},"总转发数",-1))])])]})),_:1})])),_:1}),y(g,{span:6},{default:m((()=>[y(p,{class:"stat-card"},{default:m((()=>{var e;return[b("div",he,[b("div",ke,[y(d,null,{default:m((()=>[y(E(A))])),_:1})]),b("div",Ce,[b("div",we,O((null==(e=r.value)?void 0:e.success_rate)||0)+"%",1),c[16]||(c[16]=b("div",{class:"stat-label"},"转发成功率",-1))])])]})),_:1})])),_:1}),y(g,{span:6},{default:m((()=>[y(p,{class:"stat-card"},{default:m((()=>{var e,a;return[b("div",je,[b("div",Fe,[y(d,null,{default:m((()=>[y(E(z))])),_:1})]),b("div",Ee,[b("div",xe,O((null==(a=null==(e=r.value)?void 0:e.last_24h_stats)?void 0:a.total)||0),1),c[17]||(c[17]=b("div",{class:"stat-label"},"24小时内",-1))])])]})),_:1})])),_:1}),y(g,{span:6},{default:m((()=>[y(p,{class:"stat-card"},{default:m((()=>{var e,a;return[b("div",Oe,[b("div",Ae,[y(d,null,{default:m((()=>[y(E(V))])),_:1})]),b("div",ze,[b("div",Ve,O((null==(a=null==(e=r.value)?void 0:e.last_24h_stats)?void 0:a.failed)||0),1),c[18]||(c[18]=b("div",{class:"stat-label"},"24小时失败",-1))])])]})),_:1})])),_:1})])),_:1})],512),[[D,t.value]])])),_:1}),b("div",De,[y(Te,{model:ue,inline:""},{default:m((()=>[y(ce,{label:"运单号"},{default:m((()=>[y(le,{modelValue:ue.tracking_no,"onUpdate:modelValue":c[2]||(c[2]=e=>ue.tracking_no=e),placeholder:"请输入运单号",clearable:""},null,8,["modelValue"])])),_:1}),y(ce,{label:"状态分类"},{default:m((()=>[y(ne,{modelValue:ue.status_category,"onUpdate:modelValue":c[3]||(c[3]=e=>ue.status_category=e),placeholder:"请选择状态分类",clearable:""},{default:m((()=>[(_(!0),f(P,null,R(Ke.value,(e=>(_(),Y(re,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),y(ce,{label:"具体状态"},{default:m((()=>[y(ne,{modelValue:ue.specific_status,"onUpdate:modelValue":c[4]||(c[4]=e=>ue.specific_status=e),placeholder:"请选择具体状态",clearable:"",filterable:""},{default:m((()=>[(_(!0),f(P,null,R(Je.value,(e=>(_(),Y(re,{key:e.value,label:e.label,value:e.value},{default:m((()=>[b("span",Se,O(e.label),1),b("span",Me,O(e.category),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),y(ce,{label:"转发状态"},{default:m((()=>[y(ne,{modelValue:ue.status,"onUpdate:modelValue":c[5]||(c[5]=e=>ue.status=e),placeholder:"请选择转发状态",clearable:""},{default:m((()=>[y(re,{label:"成功",value:"success"}),y(re,{label:"失败",value:"failed"}),y(re,{label:"处理中",value:"pending"})])),_:1},8,["modelValue"])])),_:1}),y(ce,{label:"时间范围"},{default:m((()=>[y(de,{modelValue:n.value,"onUpdate:modelValue":c[6]||(c[6]=e=>n.value=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])])),_:1}),y(ce,null,{default:m((()=>[y(u,{type:"primary",onClick:$e},{default:m((()=>c[19]||(c[19]=[W("搜索")]))),_:1}),y(u,{onClick:ea},{default:m((()=>c[20]||(c[20]=[W("重置")]))),_:1}),y(u,{type:"success",onClick:oa,loading:a.value},{default:m((()=>[y(d,null,{default:m((()=>[y(E(B))])),_:1}),c[21]||(c[21]=W(" 刷新 "))])),_:1},8,["loading"])])),_:1})])),_:1},8,["model"])]),a.value||s.value&&0!==s.value.length?h((_(),Y(ra,{key:1,data:s.value,stripe:""},{default:m((()=>[y(Ge,{prop:"id",label:"记录ID",width:"120","show-overflow-tooltip":""},{default:m((({row:e})=>[b("span",Le,O(e.id.substring(0,8))+"...",1)])),_:1}),y(Ge,{prop:"event_type",label:"状态分类",width:"160"},{default:m((({row:e})=>[b("div",Pe,[y(d,{color:Ze(e).color,class:"status-icon"},{default:m((()=>[(_(),Y(J(Ze(e).icon)))])),_:2},1032,["color"]),b("div",Re,[b("div",Ye,O(Ze(e).name),1),b("div",He,O(Ze(e).category),1)])])])),_:1}),y(Ge,{prop:"tracking_no",label:"运单号",width:"180","show-overflow-tooltip":""},{default:m((({row:e})=>[e.tracking_no?(_(),f("span",Ie,O(e.tracking_no),1)):(_(),f("span",qe,"-"))])),_:1}),y(Ge,{prop:"customer_order_no",label:"客户订单号",width:"160","show-overflow-tooltip":""},{default:m((({row:e})=>[e.customer_order_no?(_(),f("span",We,O(e.customer_order_no),1)):(_(),f("span",Be,"-"))])),_:1}),y(Ge,{prop:"status",label:"转发状态",width:"100"},{default:m((({row:e})=>{return[y(ca,{type:(a=e.status,{success:"success",failed:"danger",pending:"warning",processing:"warning",delivered:"success",exception_delivered:"success",billed:"success",3:"success",100:"success",301:"success",created:"info",submitted:"info",assigned:"info",pending_pickup:"info",awaiting_pickup:"info",picked_up:"info",accepted:"info",collecting:"info",in_transit:"info",delivering:"info",weight_updated:"info",forwarded:"info",1:"info",2:"info",11:"info",102:"info",103:"info",104:"info",208:"info",501:"info",exception:"warning",4:"warning",6:"warning",500:"warning",401:"warning",cancelled:"danger",pickup_failed:"danger",returned:"danger",voided:"danger",5:"danger",10:"danger",400:"danger",203:"danger",204:"danger",205:"danger"}[a]||"info")},{default:m((()=>[W(O(ia(e.status)),1)])),_:2},1032,["type"])];var a})),_:1}),y(Ge,{prop:"retry_count",label:"重试次数",width:"100",align:"center"},{default:m((({row:e})=>[b("span",{class:G({"retry-warning":e.retry_count>0})},O(e.retry_count),3)])),_:1}),y(Ge,{prop:"request_at",label:"转发时间",width:"180"},{default:m((({row:e})=>{return[W(O((a=e.request_at||e.created_at,new Date(a).toLocaleString("zh-CN"))),1)];var a})),_:1}),y(Ge,{label:"操作",width:"120",fixed:"right"},{default:m((({row:e})=>[y(u,{type:"primary",size:"small",onClick:a=>{return t=e,i.value=t,void(l.value=!0);var t}},{default:m((()=>c[23]||(c[23]=[W("详情")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[da,a.value]]):(_(),f("div",Ue,[y(Xe,{description:"暂无回调记录"},{image:m((()=>[y(d,{size:"60",color:"#c0c4cc"},{default:m((()=>[y(E(Q))])),_:1})])),default:m((()=>[c[22]||(c[22]=b("div",{class:"empty-actions"},[b("p",{class:"empty-tip"},"回调记录会在以下情况下产生："),b("ul",{class:"empty-list"},[b("li",null,"订单状态发生变更时"),b("li",null,"计费信息更新时"),b("li",null,"工单有新回复时")]),b("div",{class:"empty-buttons"},[b("p",{class:"empty-guide"},"请使用页面右上角的按钮进行配置和测试")])],-1))])),_:1})])),b("div",Ne,[y(na,{"current-page":Qe.page,"onUpdate:currentPage":c[7]||(c[7]=e=>Qe.page=e),"page-size":Qe.page_size,"onUpdate:pageSize":c[8]||(c[8]=e=>Qe.page_size=e),"page-sizes":[10,20,50,100],total:Qe.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:aa,onCurrentChange:ta},null,8,["current-page","page-size","total"])])])),_:1}),y(oe,{visible:l.value,"onUpdate:visible":c[9]||(c[9]=e=>l.value=e),"callback-record":i.value},null,8,["visible","callback-record"]),y(se,{visible:o.value,"onUpdate:visible":c[10]||(c[10]=e=>o.value=e),onSuccess:sa},null,8,["visible"])])}}}),[["__scopeId","data-v-e3c6a0a6"]]);export{Qe as default};
