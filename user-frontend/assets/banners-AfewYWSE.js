import{d as t,c as o,o as e,e as l,x as n,a,n as r,b as s,w as c,aN as u,aQ as i,u as b}from"./vendor-BVh5F9vp.js";import{_ as d}from"./index-F0f2Vd-3.js";/* empty css               */const g={class:"card-banner art-custom-card"},C={class:"banner-content"},f={class:"banner-icon"},m=["src","alt"],p={class:"banner-text"},x={class:"banner-title"},_={class:"banner-description"},k={class:"banner-buttons"},T=d(t({__name:"CardBanner",props:{icon:{default:"/art-design-pro/assets/icon1-pO_ZZorG.png"},title:{default:""},description:{default:""},buttonText:{default:"重试"},buttonColor:{default:"var(--main-color)"},buttonTextColor:{default:"#fff"},showCancel:{type:Boolean,default:!1},cancelButtonText:{default:"取消"},cancelButtonColor:{default:"#f5f5f5"},cancelButtonTextColor:{default:"#666"}},emits:["click","cancel"],setup(t,{emit:s}){const c=t,u=s,i=()=>{u("click")},b=()=>{u("cancel")};return(t,s)=>(e(),o("div",g,[l("div",C,[l("div",f,[l("img",{src:c.icon,alt:c.title},null,8,m)]),l("div",p,[l("p",x,n(c.title),1),l("p",_,n(c.description),1)]),l("div",k,[t.showCancel?(e(),o("div",{key:0,class:"banner-button cancel-button",style:r({backgroundColor:t.cancelButtonColor,color:t.cancelButtonTextColor}),onClick:b},n(t.cancelButtonText),5)):a("",!0),l("div",{class:"banner-button",style:r({backgroundColor:t.buttonColor,color:t.buttonTextColor}),onClick:i},n(t.buttonText),5)])])]))}}),[["__scopeId","data-v-7881c0b0"]]),v={class:"basic-banner__content"},h=["src"],B=d(t({__name:"BasicBanner",props:{height:{default:"11rem"},title:{},subtitle:{},buttonText:{default:"查看"},buttonColor:{default:"#fff"},buttonTextColor:{default:"#333"},titleColor:{default:"white"},subtitleColor:{default:"white"},backgroundColor:{default:"var(--el-color-primary-light-2)"},backgroundImage:{default:""}},emits:["click"],setup(t,{emit:s}){const c=s,u=()=>{c("click")};return(t,s)=>(e(),o("div",{class:"basic-banner art-custom-card",style:r({backgroundColor:t.backgroundColor,height:t.height})},[l("div",v,[l("p",{class:"basic-banner__title",style:r({color:t.titleColor})},n(t.title),5),l("p",{class:"basic-banner__subtitle",style:r({color:t.subtitleColor})},n(t.subtitle),5),l("div",{class:"basic-banner__button",style:r({backgroundColor:t.buttonColor,color:t.buttonTextColor}),onClick:u},n(t.buttonText),5),t.backgroundImage?(e(),o("img",{key:0,class:"basic-banner__background-image",src:t.backgroundImage,alt:"背景图片"},null,8,h)):a("",!0)])],4))}}),[["__scopeId","data-v-63c168d5"]]),y="/art-design-pro/assets/icon3-RCDWTWW4.png",I={class:"banners"},F=d(t({__name:"banners",setup(t){const n=()=>{},a=()=>{},r=()=>{};return(t,d)=>{const g=B,C=u,f=i,m=T;return e(),o("div",I,[d[0]||(d[0]=l("h1",{class:"page-title"},"基础横幅",-1)),s(f,{gutter:20},{default:c((()=>[s(C,{xs:24,sm:12,md:12},{default:c((()=>[s(g,{title:"欢迎回来，管理员！",subtitle:"今日系统访问量增长了23%，运行状态良好。",buttonText:"查看详情",buttonColor:"rgb(var(--art-secondary))",buttonTextColor:"#fff",onClick:n})])),_:1}),s(C,{xs:24,sm:12,md:12},{default:c((()=>[s(g,{title:"欢迎使用本系统!",subtitle:"这是一个基于Vue3和Element Plus的后台管理系统模板。",buttonText:"开始使用",buttonColor:"rgb(var(--art-success))",buttonTextColor:"#fff",backgroundColor:"#D4F1F7",titleColor:"#333",subtitleColor:"#666",onClick:n})])),_:1})])),_:1}),d[1]||(d[1]=l("h1",{class:"page-title"},"基础横幅（图片）",-1)),s(f,{gutter:20},{default:c((()=>[s(C,{xs:24,sm:12,md:12},{default:c((()=>[s(g,{title:"欢迎回来，管理员！",subtitle:"今日系统访问量增长了23%，运行状态良好。",buttonText:"查看详情",buttonColor:"#FF3E76",backgroundColor:"#FF80A4",buttonTextColor:"#fff",backgroundImage:b(y),onClick:n},null,8,["backgroundImage"])])),_:1}),s(C,{xs:24,sm:12,md:12},{default:c((()=>[s(g,{title:"欢迎使用本系统!",subtitle:"这是一个基于Vue3和Element Plus的后台管理系统模板。",buttonText:"开始使用",buttonColor:"#1385FF",buttonTextColor:"#fff",backgroundColor:"#88A7FF",backgroundImage:b("/art-design-pro/assets/icon7-Za4ive_7.png"),onClick:n},null,8,["backgroundImage"])])),_:1})])),_:1}),d[2]||(d[2]=l("h1",{class:"page-title"},"卡片横幅",-1)),s(f,{gutter:20},{default:c((()=>[s(C,{xs:24,sm:12,md:12,lg:6},{default:c((()=>[s(m,{title:"系统状态良好",description:"所有服务运行正常，无异常情况。",buttonText:"查看详情"})])),_:1}),s(C,{xs:24,sm:12,md:12,lg:6},{default:c((()=>[s(m,{icon:b("/art-design-pro/assets/icon2-BIbSUQVD.png"),title:"新消息提醒",description:"您有3条未读消息需要处理。",buttonText:"立即查看",buttonColor:"rgb(var(--art-warning))"},null,8,["icon"])])),_:1}),s(C,{xs:24,sm:12,md:12,lg:6},{default:c((()=>[s(m,{icon:b(y),title:"数据分析报告",description:"本周数据分析报告已生成完毕。",buttonText:"下载报告",buttonColor:"rgb(var(--art-error))"},null,8,["icon"])])),_:1}),s(C,{xs:24,sm:12,md:12,lg:6},{default:c((()=>[s(m,{icon:b("/art-design-pro/assets/icon4-BVK-qoMN.png"),title:"系统更新提示",description:"新版本V2.1.0已发布，建议及时更新。",buttonText:"更新",buttonColor:"rgb(var(--art-primary))",showCancel:!0,cancelButtonText:"取消",cancelButtonColor:"#eee",cancelButtonTextColor:"#333",onClick:a,onCancel:r},null,8,["icon"])])),_:1})])),_:1})])}}}),[["__scopeId","data-v-0c045b4d"]]);export{F as default};
