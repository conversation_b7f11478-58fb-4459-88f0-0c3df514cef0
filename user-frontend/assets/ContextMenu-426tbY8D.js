import{M as e}from"./index-F0f2Vd-3.js";import{d as s,r as o,j as a,c as n,o as t,b as l,w as r,K as d,G as u,H as c,u as i}from"./vendor-BVh5F9vp.js";const m={class:"page-content"},f=s({__name:"ContextMenu",setup(s){const f=o(),p=a((()=>[{key:"export",label:"导出 Excel",icon:"Download"},{key:"exportPdf",label:"导出 PDF",icon:"Folder"},{key:"disabled",label:"禁用选项",icon:"CloseBold",disabled:!0}])),x=e=>{};return(s,o)=>{const a=c;return t(),n("div",m,[l(a,{onContextmenu:o[0]||(o[0]=u((e=>(e=>{f.value.show(e)})(e)),["prevent"]))},{default:r((()=>o[1]||(o[1]=[d("右键触发菜单")]))),_:1}),l(e,{ref_key:"menuRef",ref:f,"menu-items":i(p),onSelect:x},null,8,["menu-items"])])}}});export{f as default};
