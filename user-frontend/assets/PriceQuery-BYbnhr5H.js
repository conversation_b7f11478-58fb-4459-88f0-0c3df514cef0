import{_ as e}from"./index-F0f2Vd-3.js";/* empty css                *//* empty css                 *//* empty css               *//* empty css                   */import{d as l,r as a,X as t,j as i,c as r,o as s,b as d,N as o,a as n,w as u,Z as c,bs as v,K as h,H as p,aQ as m,aN as _,_ as g,aq as f,C as y,aV as b,y as w,u as V,a3 as x,e as A,b7 as M,aR as k,F as $,A as q,x as j,aX as C,aI as U,M as F}from"./vendor-BVh5F9vp.js";import{E as K}from"./expressApi-BL8dLu27.js";import{A as z}from"./AddressCascader-CQSS5hj1.js";/* empty css                 */const G={class:"price-query"},P={class:"card-header"},E={class:"card-header"},I={class:"price-list"},L=["onClick"],N={class:"express-info"},Q={class:"express-header"},R={class:"express-name"},S={class:"product-type"},X={class:"delivery-time"},H={class:"calc-info"},O={class:"order-code"},Z={class:"price-info"},B={class:"price-main"},D={class:"current-price"},J={class:"expires-info"},T=e(l({__name:"PriceQuery",setup(e){const l=a(),T=a(!1),W=a(!1),Y=a([]),ee=t({from_province:"",from_city:"",from_district:"",to_province:"",to_city:"",to_district:"",weight:1,volume:void 0,length:void 0,width:void 0,height:void 0,quantity:1,goods_name:"物品",pay_method:0,senderAddress:[],receiverAddress:[]}),le=a({title:"",description:"",type:"info"}),ae=i((()=>{if(!ee.length||!ee.width||!ee.height)return!1;const e=ee.length*ee.width*ee.height,l=e/8e3,a=ee.weight,t=Math.max(a,l),i=Math.ceil(t);return le.value=l>a?{title:"体积重量计算",description:`包裹体积：${ee.length}×${ee.width}×${ee.height}=${e.toLocaleString()}cm³，体积重量：${l.toFixed(2)}KG，将按体积重量${i}KG计费`,type:"warning"}:{title:"体积重量计算",description:`包裹体积：${ee.length}×${ee.width}×${ee.height}=${e.toLocaleString()}cm³，体积重量：${l.toFixed(2)}KG，将按实际重量${i}KG计费`,type:"info"},!0})),te={senderAddress:[{required:!0,message:"请选择寄件地址",trigger:"change",validator:(e,l,a)=>{l&&0!==l.length?a():a(new Error("请选择寄件地址"))}}],receiverAddress:[{required:!0,message:"请选择收件地址",trigger:"change",validator:(e,l,a)=>{l&&0!==l.length?a():a(new Error("请选择收件地址"))}}],weight:[{required:!0,message:"请输入包裹重量",trigger:"blur"}]},ie=()=>{var e;const a=[{value:"110000",label:"北京市",cities:[{value:"110100",label:"北京市",districts:[{value:"110101",label:"东城区"},{value:"110102",label:"西城区"},{value:"110105",label:"朝阳区"},{value:"110106",label:"丰台区"}]}]},{value:"120000",label:"天津市",cities:[{value:"120100",label:"天津市",districts:[{value:"120101",label:"和平区"},{value:"120102",label:"河东区"},{value:"120103",label:"河西区"},{value:"120104",label:"南开区"}]}]},{value:"440000",label:"广东省",cities:[{value:"440100",label:"广州市",districts:[{value:"440103",label:"荔湾区"},{value:"440104",label:"越秀区"},{value:"440105",label:"海珠区"},{value:"440106",label:"天河区"}]},{value:"440300",label:"深圳市",districts:[{value:"440303",label:"罗湖区"},{value:"440304",label:"福田区"},{value:"440305",label:"南山区"},{value:"440306",label:"宝安区"}]}]},{value:"310000",label:"上海市",cities:[{value:"310100",label:"上海市",districts:[{value:"310101",label:"黄浦区"},{value:"310104",label:"徐汇区"},{value:"310105",label:"长宁区"},{value:"310106",label:"静安区"}]}]}],t=()=>{const e=a[Math.floor(Math.random()*a.length)],l=e.cities[Math.floor(Math.random()*e.cities.length)],t=l.districts[Math.floor(Math.random()*l.districts.length)];return{address:[e.value,l.value,t.value],province:e.label,city:l.label,district:t.label}},i=t();ee.senderAddress=i.address,ee.from_province=i.province,ee.from_city=i.city,ee.from_district=i.district;const r=t();ee.receiverAddress=r.address,ee.to_province=r.province,ee.to_city=r.city,ee.to_district=r.district,ee.weight=Math.round(10*(4*Math.random()+.5))/10,ee.quantity=1,Math.random()>.5?(ee.length=Math.floor(40*Math.random()+10),ee.width=Math.floor(30*Math.random()+10),ee.height=Math.floor(20*Math.random()+5)):(ee.length=void 0,ee.width=void 0,ee.height=void 0);const s=["文件","衣服","书籍","电子产品","食品","礼品","日用品","化妆品","玩具","工艺品"];ee.goods_name=s[Math.floor(Math.random()*s.length)],null==(e=l.value)||e.clearValidate(),F.success("已随机填充测试数据")},re=(e,a)=>{var t,i,r,s;a.length>=1&&(ee.from_province=(null==(t=a[0])?void 0:t.label)||""),a.length>=2&&(ee.from_city=(null==(i=a[1])?void 0:i.label)||""),a.length>=3&&(ee.from_district=(null==(r=a[2])?void 0:r.label)||""),null==(s=l.value)||s.validateField("senderAddress")},se=(e,a)=>{var t,i,r,s;a.length>=1&&(ee.to_province=(null==(t=a[0])?void 0:t.label)||""),a.length>=2&&(ee.to_city=(null==(i=a[1])?void 0:i.label)||""),a.length>=3&&(ee.to_district=(null==(r=a[2])?void 0:r.label)||""),null==(s=l.value)||s.validateField("receiverAddress")},de=()=>{return e=this,a=null,t=function*(){if(l.value)try{yield l.value.validate(),T.value=!0,W.value=!0;const e={query_all:!0,sender:{province:ee.from_province,city:ee.from_city,district:ee.from_district||""},receiver:{province:ee.to_province,city:ee.to_city,district:ee.to_district||""},package:{weight:ee.weight,volume:ee.volume,length:ee.length,width:ee.width,height:ee.height},pay_method:ee.pay_method||0},a=yield K.queryPrice(e);a.success&&a.data?(Y.value=a.data,0===a.data.length?F.warning("未找到符合条件的快递服务"):F.success(`查询成功，找到 ${a.data.length} 个报价`)):(F.error(a.message||"查询失败"),Y.value=[])}catch(e){F.error("查询价格失败，请检查网络连接"),Y.value=[]}finally{T.value=!1}},new Promise(((l,i)=>{var r=e=>{try{d(t.next(e))}catch(l){i(l)}},s=e=>{try{d(t.throw(e))}catch(l){i(l)}},d=e=>e.done?l(e.value):Promise.resolve(e.value).then(r,s);d((t=t.apply(e,a)).next())}));var e,a,t},oe=a(!1),ne=a(""),ue=()=>{var e;null==(e=l.value)||e.resetFields(),Object.assign(ee,{from_province:"",from_city:"",from_district:"",to_province:"",to_city:"",to_district:"",weight:1,volume:void 0,length:void 0,width:void 0,height:void 0,quantity:1,goods_name:"物品",pay_method:0,senderAddress:[],receiverAddress:[]}),Y.value=[],W.value=!1};return(e,a)=>{const t=w,i=p,K=v,ce=g,ve=_,he=m,pe=f,me=y,_e=b,ge=c,fe=k,ye=C,be=U;return s(),r("div",G,[d(fe,{class:"query-card"},{header:u((()=>[A("div",P,[a[9]||(a[9]=A("span",null,"快递价格查询",-1)),d(i,{type:"link",onClick:ue},{default:u((()=>[d(t,null,{default:u((()=>[d(V(M))])),_:1}),a[8]||(a[8]=h(" 重置 "))])),_:1})])])),default:u((()=>[d(ge,{ref_key:"formRef",ref:l,model:ee,rules:te,"label-width":"100px",class:"query-form"},{default:u((()=>[d(K,{"content-position":"left"},{default:u((()=>[a[11]||(a[11]=h(" 寄件地址 ")),d(i,{type:"primary",size:"small",onClick:ie,style:{"margin-left":"10px"}},{default:u((()=>a[10]||(a[10]=[h(" 随机填充测试数据 ")]))),_:1})])),_:1}),d(he,{gutter:20},{default:u((()=>[d(ve,{span:24},{default:u((()=>[d(ce,{label:"寄件地址",prop:"senderAddress"},{default:u((()=>[d(z,{modelValue:ee.senderAddress,"onUpdate:modelValue":a[0]||(a[0]=e=>ee.senderAddress=e),placeholder:"请选择寄件地址",onChange:re},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(K,{"content-position":"left"},{default:u((()=>a[12]||(a[12]=[h("收件地址")]))),_:1}),d(he,{gutter:20},{default:u((()=>[d(ve,{span:24},{default:u((()=>[d(ce,{label:"收件地址",prop:"receiverAddress"},{default:u((()=>[d(z,{modelValue:ee.receiverAddress,"onUpdate:modelValue":a[1]||(a[1]=e=>ee.receiverAddress=e),placeholder:"请选择收件地址",onChange:se},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(K,{"content-position":"left"},{default:u((()=>a[13]||(a[13]=[h("包裹信息")]))),_:1}),d(he,{gutter:20},{default:u((()=>[d(ve,{span:8},{default:u((()=>[d(ce,{label:"重量(kg)",prop:"weight"},{default:u((()=>[d(pe,{modelValue:ee.weight,"onUpdate:modelValue":a[2]||(a[2]=e=>ee.weight=e),min:.1,step:.1,precision:1,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),d(ve,{span:8},{default:u((()=>[d(ce,{label:"体积(m³)"},{default:u((()=>[d(pe,{modelValue:ee.volume,"onUpdate:modelValue":a[3]||(a[3]=e=>ee.volume=e),min:.001,step:.001,precision:3,style:{width:"100%"},placeholder:"可选"},null,8,["modelValue"])])),_:1})])),_:1}),d(ve,{span:8},{default:u((()=>[d(ce,{label:"物品名称"},{default:u((()=>[d(me,{modelValue:ee.goods_name,"onUpdate:modelValue":a[4]||(a[4]=e=>ee.goods_name=e),placeholder:"物品",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(he,{gutter:20},{default:u((()=>[d(ve,{span:8},{default:u((()=>[d(ce,{label:"长度(cm)"},{default:u((()=>[d(pe,{modelValue:ee.length,"onUpdate:modelValue":a[5]||(a[5]=e=>ee.length=e),min:1,step:1,precision:0,style:{width:"100%"},placeholder:"可选"},null,8,["modelValue"])])),_:1})])),_:1}),d(ve,{span:8},{default:u((()=>[d(ce,{label:"宽度(cm)"},{default:u((()=>[d(pe,{modelValue:ee.width,"onUpdate:modelValue":a[6]||(a[6]=e=>ee.width=e),min:1,step:1,precision:0,style:{width:"100%"},placeholder:"可选"},null,8,["modelValue"])])),_:1})])),_:1}),d(ve,{span:8},{default:u((()=>[d(ce,{label:"高度(cm)"},{default:u((()=>[d(pe,{modelValue:ee.height,"onUpdate:modelValue":a[7]||(a[7]=e=>ee.height=e),min:1,step:1,precision:0,style:{width:"100%"},placeholder:"可选"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),ae.value?(s(),o(he,{key:0,gutter:20},{default:u((()=>[d(ve,{span:24},{default:u((()=>[d(_e,{title:le.value.title,description:le.value.description,type:le.value.type,"show-icon":"",closable:!1,style:{"margin-bottom":"15px"}},null,8,["title","description","type"])])),_:1})])),_:1})):n("",!0),d(ce,null,{default:u((()=>[d(i,{type:"primary",size:"large",onClick:de,loading:T.value},{default:u((()=>[d(t,null,{default:u((()=>[d(V(x))])),_:1}),a[14]||(a[14]=h(" 查询价格 "))])),_:1},8,["loading"])])),_:1})])),_:1},8,["model"])])),_:1}),Y.value.length>0?(s(),o(fe,{key:0,class:"result-card"},{header:u((()=>[A("div",E,[a[15]||(a[15]=A("span",null,"查询结果",-1)),d(ye,{type:"info"},{default:u((()=>[h("共找到 "+j(Y.value.length)+" 个报价",1)])),_:1})])])),default:u((()=>[A("div",I,[(s(!0),r($,null,q(Y.value,(e=>(s(),r("div",{key:e.express_code,class:"price-item",onClick:l=>(e=>{ne.value=e.order_code,oe.value=!0,F.success(`已选择 ${e.express_name}，请填写下单信息`)})(e)},[A("div",N,[A("div",Q,[A("span",R,j(e.express_name),1),A("span",S,j(e.product_name),1)]),A("div",X,j(e.delivery_time),1),A("div",H," 计费重量: "+j(e.calc_weight)+"kg | 续重: ¥"+j(e.continued_weight_per_kg)+"/kg ",1),A("div",O,"订单码: "+j(e.order_code),1)]),A("div",Z,[A("div",B,[A("span",D,"¥"+j(e.price.toFixed(2)),1)]),A("div",J," 有效期: "+j(e.valid_minutes)+"分钟 ",1),d(i,{type:"primary",size:"small"},{default:u((()=>a[16]||(a[16]=[h("选择下单")]))),_:1})])],8,L)))),128))])])),_:1})):W.value&&!T.value?(s(),o(be,{key:1,description:"未找到符合条件的快递服务","image-size":120})):n("",!0)])}}}),[["__scopeId","data-v-899bfd57"]]);export{T as default};
