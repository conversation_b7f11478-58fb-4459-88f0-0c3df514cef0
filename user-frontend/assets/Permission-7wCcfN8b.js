import{_ as n}from"./index-F0f2Vd-3.js";/* empty css               */import{bF as e,c as t,o as a,bn as u,e as d,b as s,w as l,a2 as i,N as o,H as c,K as r,aQ as m}from"./vendor-BVh5F9vp.js";const p={class:"page-content"},v={class:"row"};const h=n({},[["render",function(n,h){const f=c,_=m,b=e("auth");return a(),t("div",p,[h[7]||(h[7]=u('<div class="header" data-v-a407fece><h2 data-v-a407fece>权限</h2><p data-v-a407fece>本项目实现了菜单权限、路由权限和按钮权限</p></div><div class="row" data-v-a407fece><h3 data-v-a407fece>菜单权限</h3><p data-v-a407fece>菜单权限通过 src/router/modules/asyncRoutes.ts 配置，如果数据结构存在菜单就会显示并动态注册路由</p><p data-v-a407fece>注意：正式项目需要后端返回菜单数据结构</p><b data-v-a407fece>代码示例：</b><pre data-v-a407fece>        <code data-v-a407fece>\n          {\n            id: 3,\n            path: &#39;/menu&#39;,\n            name: &#39;Menu&#39;,\n            component: RoutesAlias.Home,\n            meta: {\n              title: &#39;menus.menu.title&#39;,\n              icon: &#39;&#39;,\n              keepAlive: false\n            },\n            children: [\n              {\n                id: 401,\n                path: &#39;menu&#39;,\n                name: &#39;Menus&#39;,\n                component: RoutesAlias.Menu,\n                meta: {\n                  title: &#39;menus.menu.menu&#39;,\n                  icon: &#39;&#39;,\n                  keepAlive: true,\n                  authList: [\n                    {\n                      id: 4011,\n                      title: &#39;新增&#39;,\n                      auth_mark: &#39;add&#39;\n                    },\n                    {\n                      id: 4012,\n                      title: &#39;编辑&#39;,\n                      auth_mark: &#39;edit&#39;\n                    },\n                    {\n                      id: 4013,\n                      title: &#39;删除&#39;,\n                      auth_mark: &#39;delete&#39;\n                    }\n                  ]\n                }\n              },\n            ]\n          },\n        </code>\n      </pre></div><div class="row" data-v-a407fece><h3 data-v-a407fece>路由权限</h3><p data-v-a407fece>通过菜单数据动态组成路由数据，动态注册路由，菜单中没有的路由不会注册</p><pre data-v-a407fece>        <code data-v-a407fece>\n          // 静态路由（不需要权限）\n          // src/router/index.ts\n          const staticRoutes = [];\n\n          // 动态路由（需要权限）\n          // src/router/modules/asyncRoutes.ts\n          const asyncRoutes = []; \n        </code>\n      </pre></div>',3)),d("div",v,[h[3]||(h[3]=d("h3",null,"按钮权限",-1)),h[4]||(h[4]=d("p",null,"通过配置菜单列表的 authList 配置按钮权限，如果 authList 的 auth_mark 与页面上的自定义指令 v-auth=\"'add'\" 相匹配，则显示该按钮",-1)),s(_,null,{default:l((()=>[i((a(),o(f,{type:"primary",size:"default"},{default:l((()=>h[0]||(h[0]=[r("新增")]))),_:1})),[[b,"add"]]),i((a(),o(f,{type:"default",size:"default"},{default:l((()=>h[1]||(h[1]=[r("编辑")]))),_:1})),[[b,"edit"]]),i((a(),o(f,{type:"danger",size:"default"},{default:l((()=>h[2]||(h[2]=[r("删除")]))),_:1})),[[b,"delete"]])])),_:1}),h[5]||(h[5]=d("b",null,"代码示例：",-1)),h[6]||(h[6]=d("pre",null,[r("        "),d("code",null,"\n          {\n            id: 3,\n            path: '/menu',\n            name: 'Menu',\n            component: RoutesAlias.Home,\n            meta: {\n              title: 'menus.menu.title',\n              icon: '',\n              keepAlive: false\n            },\n            children: [\n              {\n                id: 401,\n                path: 'menu',\n                name: 'Menus',\n                component: RoutesAlias.Menu,\n                meta: {\n                  title: 'menus.menu.menu',\n                  icon: '',\n                  keepAlive: true,\n                  authList: [\n                    {\n                      id: 4011,\n                      title: '新增',\n                      auth_mark: 'add'\n                    },\n                    {\n                      id: 4012,\n                      title: '编辑',\n                      auth_mark: 'edit'\n                    },\n                    {\n                      id: 4013,\n                      title: '删除',\n                      auth_mark: 'delete'\n                    }\n                  ]\n                }\n              },\n            ]\n          },\n        "),r("\n      ")],-1))]),h[8]||(h[8]=d("div",{class:"row"},[d("h3",null,"按钮权限指令"),d("p",null,"v-auth 指令，如果 authList 的 auth_mark 内容与页面上的自定义指令 v-auth=\"'add'\" 相匹配，则显示该按钮"),d("b",null,"代码示例："),d("pre",null,[r("        "),d("code",null,"\n          <el-button v-auth=\"'add'\">新增</el-button>\n          <el-button v-auth=\"'edit'\">编辑</el-button>\n          <el-button v-auth=\"'delete'\">删除</el-button>\n        "),r("\n      ")])],-1))])}],["__scopeId","data-v-a407fece"]]);export{h as default};
