import{i as a,j as e,k as r,l as s,m as t,n as o,o as l,_ as n}from"./index-F0f2Vd-3.js";/* empty css                    *//* empty css                       *//* empty css                        */import{d as p,r as c,X as i,f as u,c as m,o as v,e as d,b as g,K as b,w as x,bo as f,u as w,m as _,b8 as y,aH as h,x as j,aT as V}from"./vendor-BVh5F9vp.js";const k={class:"region new-user art-custom-card"},T={class:"card-header"},U={style:{display:"flex","align-items":"center"}},H=["src"],I={class:"user-name"},K={style:{display:"flex","align-items":"center"}},N={style:{"margin-left":"10px"}},X=n(p({__name:"NewUser",setup(n){const p=c("本月"),X=i([{username:"中小鱼",province:"北京",sex:0,age:22,percentage:60,pro:0,color:"rgb(var(--art-primary)) !important",avatar:a},{username:"何小荷",province:"深圳",sex:1,age:21,percentage:20,pro:0,color:"rgb(var(--art-secondary)) !important",avatar:e},{username:"誶誶淰",province:"上海",sex:1,age:23,percentage:60,pro:0,color:"rgb(var(--art-warning)) !important",avatar:r},{username:"发呆草",province:"长沙",sex:0,age:28,percentage:50,pro:0,color:"rgb(var(--art-info)) !important",avatar:s},{username:"甜筒",province:"浙江",sex:1,age:26,percentage:70,pro:0,color:"rgb(var(--art-error)) !important",avatar:t},{username:"冷月呆呆",province:"湖北",sex:1,age:25,percentage:90,pro:0,color:"rgb(var(--art-success)) !important",avatar:o}]);u((()=>{q()}));const q=()=>{setTimeout((()=>{for(let a=0;a<X.length;a++){let e=X[a];X[a].pro=e.percentage}}),100)};return(a,e)=>{const r=f,s=y,t=h,o=V,n=l;return v(),m("div",k,[d("div",T,[e[1]||(e[1]=d("div",{class:"title"},[d("h4",{class:"box-title"},"新用户"),d("p",{class:"subtitle"},[b("这个月增长"),d("span",{class:"text-success"},"+20%")])],-1)),g(s,{modelValue:w(p),"onUpdate:modelValue":e[0]||(e[0]=a=>_(p)?p.value=a:null)},{default:x((()=>[g(r,{value:"本月",label:"本月"}),g(r,{value:"上月",label:"上月"}),g(r,{value:"今年",label:"今年"})])),_:1},8,["modelValue"])]),g(n,{data:w(X),pagination:!1},{default:x((()=>[g(t,{label:"头像",prop:"avatar",width:"150px"},{default:x((a=>[d("div",U,[d("img",{class:"avatar",src:a.row.avatar},null,8,H),d("span",I,j(a.row.username),1)])])),_:1}),g(t,{label:"地区",prop:"province"}),g(t,{label:"性别",prop:"avatar"},{default:x((a=>[d("div",K,[d("span",N,j(1===a.row.sex?"男":"女"),1)])])),_:1}),g(t,{label:"进度",width:"240"},{default:x((a=>[g(o,{percentage:a.row.pro,color:a.row.color,"stroke-width":4},null,8,["percentage","color"])])),_:1})])),_:1},8,["data"])])}}}),[["__scopeId","data-v-097273c0"]]);export{X as default};
