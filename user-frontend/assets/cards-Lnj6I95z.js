import{_ as e,p as t,h as a}from"./index-F0f2Vd-3.js";/* empty css                */import{d as r,c as o,o as s,b as i,aR as l,w as c,e as n,a as d,bH as u,y as g,u as m,J as p,x as v,K as h,bm as f,bI as b,j as y,aa as C,n as _,bA as w,F as x,A as k,N as A,bB as B,v as S,H as z,p as T,f as j,r as D,aT as M,aN as I,aQ as L}from"./vendor-BVh5F9vp.js";/* empty css                         */import{u as R,a as U}from"./useChart-B-296zqU.js";import"./index-CdJNdp1H.js";import{L as H}from"./install-DmVoiIn1.js";/* empty css                    */import{C as Y}from"./vue3-count-to.esm-Cfo4Xkp7.js";/* empty css               */const V={class:"image-card"},W={class:"image-wrapper"},$={class:"image-placeholder"},q={key:0,class:"read-time"},F={class:"content"},J={key:0,class:"category"},K={class:"title"},N={class:"stats"},O={class:"views"},E={key:0,class:"comments"},P={class:"date"},Q=e(r({__name:"ImageCard",props:{imageUrl:{},title:{},category:{},readTime:{},views:{},comments:{},date:{}},setup(e){const t=e;return(e,a)=>{const r=g,y=u,C=l;return s(),o("div",V,[i(C,{"body-style":{padding:"0px"},shadow:"hover",class:"art-custom-card"},{default:c((()=>[n("div",W,[i(y,{src:t.imageUrl,fit:"cover",loading:"lazy"},{placeholder:c((()=>[n("div",$,[i(r,null,{default:c((()=>[i(m(p))])),_:1})])])),_:1},8,["src"]),t.readTime?(s(),o("div",q,v(t.readTime)+" 阅读 ",1)):d("",!0)]),n("div",F,[t.category?(s(),o("div",J,v(t.category),1)):d("",!0),n("p",K,v(t.title),1),n("div",N,[n("span",O,[i(r,null,{default:c((()=>[i(m(f))])),_:1}),h(" "+v(t.views),1)]),void 0!==t.comments?(s(),o("span",E,[i(r,null,{default:c((()=>[i(m(b))])),_:1}),h(" "+v(t.comments),1)])):d("",!0),n("span",P,v(t.date),1)])])])),_:1})])}}}),[["__scopeId","data-v-e7ddeac2"]]),G={class:"timeline-list-card"},X={class:"art-card art-custom-card"},Z={class:"card-header"},ee={class:"card-title"},te={class:"card-subtitle"},ae={class:"timeline-item"},re={class:"timeline-content"},oe={class:"timeline-text"},se={key:0,class:"timeline-code"},ie=e(r({__name:"TimelineListCard",props:{list:{},title:{default:""},subtitle:{default:""},maxCount:{default:5}},setup(e){const t=e,a=y((()=>65*t.maxCount+"px"));return(e,t)=>{const r=B,l=w,u=C;return s(),o("div",G,[n("div",X,[n("div",Z,[n("p",ee,v(e.title),1),n("p",te,v(e.subtitle),1)]),i(u,{style:_({height:a.value})},{default:c((()=>[i(l,null,{default:c((()=>[(s(!0),o(x,null,k(e.list,(e=>(s(),A(r,{key:e.time,timestamp:e.time,placement:"top",color:e.status,center:!0},{default:c((()=>[n("div",ae,[n("div",re,[n("span",oe,v(e.content),1),e.code?(s(),o("span",se," #"+v(e.code),1)):d("",!0)])])])),_:2},1032,["timestamp","color"])))),128))])),_:1})])),_:1},8,["style"])])])}}}),[["__scopeId","data-v-4a7e90af"]]),le={class:"basic-list-card"},ce={class:"art-card art-custom-card"},ne={class:"card-header"},de={class:"card-title"},ue={class:"card-subtitle"},ge=["innerHTML"],me={class:"item-content"},pe={class:"item-title"},ve={class:"item-status"},he={class:"item-time"},fe=e(r({__name:"DataListCard",props:{list:{},title:{default:""},subtitle:{default:""},maxCount:{default:5},showMoreButton:{type:Boolean,default:!1}},emits:["more"],setup(e,{emit:t}){const a=e,r=y((()=>66*(a.maxCount||5)+"px")),l=t,u=()=>{l("more")};return(e,t)=>{const a=C,l=z;return s(),o("div",le,[n("div",ce,[n("div",ne,[n("p",de,v(e.title),1),n("p",ue,v(e.subtitle),1)]),i(a,{style:_({height:r.value})},{default:c((()=>[(s(!0),o(x,null,k(e.list,((e,t)=>(s(),o("div",{key:t,class:"list-item"},[e.icon?(s(),o("div",{key:0,class:S(["item-icon",e.class])},[n("i",{class:"iconfont-sys",innerHTML:e.icon},null,8,ge)],2)):d("",!0),n("div",me,[n("div",pe,v(e.title),1),n("div",ve,v(e.status),1)]),n("div",he,v(e.time),1)])))),128))])),_:1},8,["style"]),e.showMoreButton?(s(),A(l,{key:0,class:"more-btn",onClick:u},{default:c((()=>t[0]||(t[0]=[h("查看更多")]))),_:1})):d("",!0)])])}}}),[["__scopeId","data-v-905ff6c2"]]),be={class:"card-body"},ye={class:"card-content"},Ce={class:"data-section"},_e={class:"title"},we={class:"value"},xe={class:"chart-legend"},ke={class:"legend-item current"},Ae={class:"legend-item previous"},Be={class:"chart-section"},Se=e(r({__name:"DonutChartCard",props:{value:{default:0},title:{default:""},percentage:{default:0},currentYear:{default:"2022"},previousYear:{default:"2021"},height:{default:9},color:{default:""},data:{default:()=>[0,0]}},setup(e){const{chartRef:t,isDark:a,initChart:r}=R(),i=e,l=()=>{const e=i.color||U().themeColor;return{series:[{type:"pie",radius:["70%","90%"],avoidLabelOverlap:!1,label:{show:!1},data:[{value:i.data[0],name:i.currentYear,itemStyle:{color:e}},{value:i.data[1],name:i.previousYear,itemStyle:{color:"#e6e8f7"}}]}]}};return T(a,(()=>r(l()))),j((()=>r(l()))),(e,a)=>{return s(),o("div",{class:"donut-chart-card art-custom-card",style:_({height:`${e.height}rem`})},[n("div",be,[n("div",ye,[n("div",Ce,[n("p",_e,v(e.title),1),n("div",null,[n("p",we,v((r=e.value,r.toLocaleString())),1),n("div",{class:S(["percentage",{"is-increase":e.percentage>0}])},v(e.percentage>0?"+":"")+v(e.percentage)+"% 较去年 ",3)]),n("div",xe,[n("span",ke,v(e.currentYear),1),n("span",Ae,v(e.previousYear),1)])]),n("div",Be,[n("div",{ref_key:"chartRef",ref:t,class:"chart-container"},null,512)])])])],4);var r}}}),[["__scopeId","data-v-6c16ae73"]]),ze={class:"card-body"},Te={class:"chart-header"},je={class:"metric"},De={class:"value"},Me={class:"label"},Ie=e(r({__name:"BarChartCard",props:{value:{default:0},label:{default:""},percentage:{default:0},height:{default:11},color:{default:""},chartData:{default:()=>[]},barWidth:{default:"26%"}},setup(e){const{chartRef:t,isDark:a,initChart:r}=R(),i=e,l=()=>{const e=i.color||U().themeColor;return{grid:{top:0,right:0,bottom:15,left:0},xAxis:{type:"category",show:!1},yAxis:{type:"value",show:!1},series:[{data:i.chartData,type:"bar",barWidth:i.barWidth,itemStyle:{color:e,borderRadius:2}}]}};return T(a,(()=>r(l()))),j((()=>r(l()))),(e,a)=>(s(),o("div",{class:"bar-chart-card art-custom-card",style:_({height:`${e.height}rem`})},[n("div",ze,[n("div",Te,[n("div",je,[n("p",De,v(e.value),1),n("p",Me,v(e.label),1)]),n("div",{class:S(["percentage",{"is-increase":e.percentage>0}])},v(e.percentage>0?"+":"")+v(e.percentage)+"% ",3)]),n("div",{ref_key:"chartRef",ref:t,class:"chart-container",style:_({height:`calc(${e.height}rem - 5rem)`})},null,4)])],4))}}),[["__scopeId","data-v-144b2c76"]]),Le={class:"card-body"},Re={class:"chart-header"},Ue={class:"metric"},He={class:"value"},Ye={class:"label"},Ve=e(r({__name:"LineChartCard",props:{value:{default:0},label:{default:""},percentage:{default:0},height:{default:11},color:{default:""},showAreaColor:{type:Boolean,default:!1},chartData:{default:()=>[]}},setup(e){const{chartRef:r,isDark:i,initChart:l}=R(),c=e,d=()=>{const e=c.color||U().themeColor;return{grid:{top:0,right:0,bottom:0,left:0},xAxis:{type:"category",show:!1},yAxis:{type:"value",show:!1},series:[{data:c.chartData,type:"line",smooth:!0,showSymbol:!1,lineStyle:{width:3,color:e},areaStyle:c.showAreaColor?{color:new H(0,0,0,1,[{offset:0,color:c.color?t(c.color,.2).rgba:t(a("--el-color-primary"),.2).rgba},{offset:1,color:c.color?t(c.color,.01).rgba:t(a("--el-color-primary"),.01).rgba}])}:void 0}]}};return T(i,(()=>l(d()))),j((()=>l(d()))),(e,t)=>(s(),o("div",{class:"line-chart-card art-custom-card",style:_({height:`${e.height}rem`})},[n("div",Le,[n("div",Re,[n("div",Ue,[n("p",He,v(e.value),1),n("p",Ye,v(e.label),1)]),n("div",{class:S(["percentage",{"is-increase":e.percentage>0}])},v(e.percentage>0?"+":"")+v(e.percentage)+"% ",3)]),n("div",{ref_key:"chartRef",ref:r,class:"chart-container",style:_({height:`calc(${e.height}rem - 5rem)`})},null,4)])],4))}}),[["__scopeId","data-v-614bf9d8"]]),We={class:"progress-card art-custom-card"},$e={class:"left"},qe=["innerHTML"],Fe={class:"right"},Je={class:"title"},Ke=e(r({__name:"ProgressCard",props:{percentage:{},title:{},color:{default:"#67C23A"},icon:{},iconColor:{},iconBgColor:{},iconSize:{},strokeWidth:{default:5}},setup(e){const t=e,a=D(0),r=()=>{const e=Date.now(),r=a.value,o=t.percentage,s=()=>{const t=Date.now()-e,i=Math.min(t/500,1);a.value=r+(o-r)*i,i<1&&requestAnimationFrame(s)};requestAnimationFrame(s)};return j((()=>{r()})),T((()=>t.percentage),(()=>{r()})),(e,t)=>{const r=M;return s(),o("div",We,[n("div",{class:"progress-info",style:_({justifyContent:e.icon?"space-between":"flex-start"})},[n("div",$e,[e.icon?(s(),o("i",{key:0,class:"iconfont-sys",innerHTML:e.icon,style:_({color:e.iconColor,backgroundColor:e.iconBgColor,fontSize:e.iconSize+"px"})},null,12,qe)):d("",!0)]),n("div",Fe,[(s(),A(m(Y),{key:e.percentage,class:"percentage",style:_({textAlign:e.icon?"right":"left"}),endVal:e.percentage,duration:2e3,suffix:"%"},null,8,["style","endVal"])),n("p",Je,v(e.title),1)])],4),i(r,{percentage:a.value,"stroke-width":e.strokeWidth,"show-text":!1,color:e.color},null,8,["percentage","stroke-width","color"])])}}}),[["__scopeId","data-v-9776623c"]]),Ne=["innerHTML"],Oe={class:"stats-card__content"},Ee={key:0,class:"stats-card__arrow"},Pe=e(r({__name:"StatsCard",props:{icon:{},title:{},count:{},description:{},iconColor:{},iconBgColor:{},iconSize:{},textColor:{},backgroundColor:{},showArrow:{type:Boolean}},setup:e=>(e,t)=>(s(),o("div",{class:"stats-card art-custom-card",style:_({backgroundColor:e.backgroundColor})},[n("div",{class:"stats-card__icon",style:_({backgroundColor:e.iconBgColor})},[n("i",{class:"iconfont-sys",innerHTML:e.icon,style:_({color:e.iconColor,fontSize:e.iconSize+"px"})},null,12,Ne)],4),n("div",Oe,[e.title?(s(),o("p",{key:0,class:"stats-card__title",style:_({color:e.textColor})},v(e.title),5)):d("",!0),e.count?(s(),A(m(Y),{key:1,class:"stats-card__count",endVal:e.count,duration:1e3},null,8,["endVal"])):d("",!0),e.description?(s(),o("p",{key:2,class:"stats-card__description",style:_({color:e.textColor})},v(e.description),5)):d("",!0)]),e.showArrow?(s(),o("div",Ee,t[0]||(t[0]=[n("i",{class:"iconfont-sys"},"",-1)]))):d("",!0)],4))}),[["__scopeId","data-v-baffba9a"]]),Qe={class:"cards"},Ge=e(r({__name:"cards",setup(e){const t=[{id:1,title:"销售产品",count:1235,description:"鞋子、牛仔裤、派对服装、手表",icon:"&#xe812;",iconColor:"rgb(var(--art-primary))",iconSize:20,iconBgColor:"rgb(var(--art-info))",textColor:"rgb(var(--art-primary))",backgroundColor:"rgb(var(--art-bg-primary))",showArrow:!1},{id:2,title:"活跃用户",count:5e3,description:"日活跃用户超过5,000+",icon:"&#xe724;",iconColor:"rgb(var(--art-warning))",iconSize:20,iconBgColor:"rgb(var(--art-success))",textColor:"rgb(var(--art-warning))",backgroundColor:"rgb(var(--art-bg-warning))",showArrow:!1},{id:3,title:"总收入",count:35e3,description:"月收入超过¥350,000+",icon:"&#xe70e;",iconColor:"rgb(var(--art-secondary))",iconSize:20,iconBgColor:"rgb(var(--art-secondary))",textColor:"rgb(var(--art-secondary))",backgroundColor:"rgb(var(--art-bg-secondary))",showArrow:!1},{id:4,title:"客户评价",count:4800,description:"平均评分4.8/5",icon:"&#xe82d;",iconColor:"rgb(var(--art-error))",iconSize:20,iconBgColor:"rgb(var(--art-error))",textColor:"rgb(var(--art-error))",backgroundColor:"rgb(var(--art-bg-error))",showArrow:!1}],a=[{id:1,title:"完成进度",percentage:75,color:"rgb(var(--art-success))",icon:"&#xe812;",iconColor:"rgb(var(--art-success))",iconBgColor:"rgb(var(--art-bg-success))",iconSize:20},{id:2,title:"项目进度",percentage:65,color:"rgb(var(--art-primary))",icon:"&#xe724;",iconColor:"rgb(var(--art-primary))",iconBgColor:"rgb(var(--art-bg-primary))",iconSize:20},{id:3,title:"学习进度",percentage:45,color:"rgb(var(--art-error))",icon:"&#xe724;",iconColor:"rgb(var(--art-error))",iconBgColor:"rgb(var(--art-bg-error))",iconSize:20},{id:4,title:"任务进度",percentage:90,color:"rgb(var(--art-secondary))",icon:"&#xe724;",iconColor:"rgb(var(--art-secondary))",iconBgColor:"rgb(var(--art-bg-secondary))",iconSize:20}],r=[{id:1,imageUrl:"/art-design-pro/assets/img1-DEo6e7AC.jpg",title:"AI技术在医疗领域的创新应用与发展前景",category:"社交",readTime:"2分钟",views:9125,comments:3,date:"12月19日 周一"},{id:2,imageUrl:"/art-design-pro/assets/img2-C5MMx-cI.jpg",title:"大数据分析助力企业决策的实践案例",category:"技术",readTime:"3分钟",views:7234,comments:5,date:"12月20日 周二"},{id:3,imageUrl:"/art-design-pro/assets/img3-BJ4acC6c.jpg",title:"区块链技术在供应链管理中的应用",category:"科技",readTime:"4分钟",views:5678,comments:8,date:"12月21日 周三"},{id:4,imageUrl:"/art-design-pro/assets/img4-BVK4Aq75.jpg",title:"云计算技术发展趋势与未来展望",category:"云技术",readTime:"5分钟",views:4321,comments:6,date:"12月22日 周四"}],l=[{title:"新加坡之行",status:"进行中",time:"5分钟",class:"bg-primary",icon:"&#xe6f2;"},{title:"归档数据",status:"进行中",time:"10分钟",class:"bg-secondary",icon:"&#xe806;"},{title:"客户会议",status:"待处理",time:"15分钟",class:"bg-warning",icon:"&#xe6fb;"},{title:"筛选任务团队",status:"进行中",time:"20分钟",class:"bg-danger",icon:"&#xe813;"},{title:"发送信封给小王",status:"已完成",time:"20分钟",class:"bg-success",icon:"&#xe70c;"}],d=[{time:"上午 09:30",status:"rgb(73, 190, 255)",content:"收到 John Doe 支付的 385.90 美元"},{time:"上午 10:00",status:"rgb(54, 158, 255)",content:"新销售记录",code:"ML-3467"},{time:"上午 12:00",status:"rgb(103, 232, 207)",content:"向 Michael 支付了 64.95 美元"},{time:"下午 14:30",status:"rgb(255, 193, 7)",content:"系统维护通知",code:"MT-2023"},{time:"下午 15:45",status:"rgb(255, 105, 105)",content:"紧急订单取消提醒",code:"OR-9876"},{time:"下午 17:00",status:"rgb(103, 232, 207)",content:"完成每日销售报表"}],u=()=>{};return(e,g)=>{const m=Pe,p=I,v=L,h=Ke,f=Ve,b=Ie,y=Se,C=fe,_=ie,w=Q;return s(),o("div",Qe,[g[0]||(g[0]=n("h1",{class:"page-title"},"统计卡片（文字）",-1)),i(v,{gutter:20},{default:c((()=>[(s(),o(x,null,k(t,(e=>i(p,{xs:24,sm:12,md:6,key:e.id},{default:c((()=>[i(m,{icon:e.icon,title:e.title,description:e.description,iconSize:e.iconSize,iconColor:"#fff",iconBgColor:e.iconBgColor,showArrow:e.showArrow},null,8,["icon","title","description","iconSize","iconBgColor","showArrow"])])),_:2},1024))),64))])),_:1}),g[1]||(g[1]=n("h1",{class:"page-title"},"统计卡片（数字滚动）",-1)),i(v,{gutter:20},{default:c((()=>[(s(),o(x,null,k(t,(e=>i(p,{xs:24,sm:12,md:6,key:e.id},{default:c((()=>[i(m,{icon:e.icon,count:e.count,description:e.description,iconSize:e.iconSize,iconColor:"#fff",iconBgColor:e.iconBgColor,showArrow:e.showArrow},null,8,["icon","count","description","iconSize","iconBgColor","showArrow"])])),_:2},1024))),64))])),_:1}),g[2]||(g[2]=n("h1",{class:"page-title"},"统计卡片（自定义样式）",-1)),i(v,{gutter:20},{default:c((()=>[(s(),o(x,null,k(t,(e=>i(p,{xs:24,sm:12,md:6,key:e.id},{default:c((()=>[i(m,{icon:e.icon,title:e.title,description:e.description,iconColor:e.iconColor,textColor:e.textColor,backgroundColor:e.backgroundColor,showArrow:e.showArrow},null,8,["icon","title","description","iconColor","textColor","backgroundColor","showArrow"])])),_:2},1024))),64))])),_:1}),g[3]||(g[3]=n("h1",{class:"page-title"},"进度卡片",-1)),i(v,{gutter:20},{default:c((()=>[(s(),o(x,null,k(a,(e=>i(p,{xs:24,sm:12,md:6,key:e.id},{default:c((()=>[i(h,{percentage:e.percentage,title:e.title,color:e.color},null,8,["percentage","title","color"])])),_:2},1024))),64))])),_:1}),g[4]||(g[4]=n("h1",{class:"page-title"},"进度卡片（icon）",-1)),i(v,{gutter:20},{default:c((()=>[(s(),o(x,null,k(a,(e=>i(p,{xs:24,sm:12,md:6,key:e.id},{default:c((()=>[i(h,{percentage:e.percentage,title:e.title,color:e.color,icon:e.icon,iconColor:e.iconColor,iconBgColor:e.iconBgColor,iconSize:e.iconSize},null,8,["percentage","title","color","icon","iconColor","iconBgColor","iconSize"])])),_:2},1024))),64))])),_:1}),g[5]||(g[5]=n("h1",{class:"page-title"},"图表卡片",-1)),i(v,{gutter:20},{default:c((()=>[i(p,{xs:24,sm:12,md:6},{default:c((()=>[i(f,{value:2545,label:"新用户",percentage:1.2,height:11,chartData:[120,132,101,134,90,230,210]})])),_:1}),i(p,{xs:24,sm:12,md:6},{default:c((()=>[i(b,{value:15480,label:"浏览量",percentage:-4.15,height:11,chartData:[120,100,150,140,90,120,130,110]})])),_:1}),i(p,{xs:24,sm:12,md:6},{default:c((()=>[i(f,{value:2545,label:"粉丝数",percentage:1.2,height:11,showAreaColor:!0,chartData:[120,132,101,134,90,230,210]})])),_:1}),i(p,{xs:24,sm:12,md:6},{default:c((()=>[i(y,{value:36358,title:"粉丝量",percentage:18,data:[70,30],height:11,currentYear:"2022",previousYear:"2021"})])),_:1})])),_:1}),g[6]||(g[6]=n("h1",{class:"page-title"},"数据列表卡片",-1)),i(v,{gutter:20},{default:c((()=>[i(p,{xs:24,sm:12,lg:8},{default:c((()=>[i(C,{list:l,title:"待办事项",subtitle:"今日待处理任务"})])),_:1}),i(p,{xs:24,sm:12,lg:8},{default:c((()=>[i(C,{maxCount:4,list:l,title:"最近活动",subtitle:"近期活动列表",showMoreButton:!0,onMore:u})])),_:1}),i(p,{xs:24,sm:12,lg:8},{default:c((()=>[i(_,{list:d,title:"最近交易",subtitle:"2024年12月20日"})])),_:1})])),_:1}),g[7]||(g[7]=n("h1",{class:"page-title"},"图片卡片",-1)),i(v,{gutter:20},{default:c((()=>[(s(),o(x,null,k(r,(e=>i(p,{xs:24,sm:12,md:6,key:e.id},{default:c((()=>[i(w,{imageUrl:e.imageUrl,title:e.title,category:e.category,readTime:e.readTime,views:e.views,comments:e.comments,date:e.date},null,8,["imageUrl","title","category","readTime","views","comments","date"])])),_:2},1024))),64))])),_:1})])}}}),[["__scopeId","data-v-0b70d2be"]]);export{Ge as default};
