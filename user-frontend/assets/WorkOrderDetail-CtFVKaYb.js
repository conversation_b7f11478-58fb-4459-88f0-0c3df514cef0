import{_ as a}from"./index-F0f2Vd-3.js";import{d as e,a8 as r,a1 as s,r as t,j as o,f as l,c as i,o as d,e as u,a2 as n,b as c,a as p,w as m,K as v,y as f,u as h,ad as k,H as y,x as j,aX as w,aG as _}from"./vendor-BVh5F9vp.js";import{u as x}from"./workOrder-Bqy1-bv9.js";import{W as O}from"./workOrderApi-4juIOmO-.js";import{E as W}from"./errorHandler-Dnd-hi0l.js";import b from"./WorkOrderDetailDialog-CuHhVgpx.js";/* empty css                  *//* empty css                    *//* empty css                *//* empty css                             */const g={class:"workorder-detail-container"},D={class:"page-header"},S={key:0,class:"header-info"},A={class:"header-meta"},C={class:"create-time"},E={class:"detail-section"},H=a(e({__name:"WorkOrderDetail",setup(a){const e=r(),H=s(),P=x(),z=t(!1),G=t(null),I=o((()=>e.params.id)),K=()=>{return a=this,e=null,r=function*(){if(I.value){z.value=!0;try{G.value=yield P.fetchWorkOrderDetail(I.value)}catch(a){W.handleApiError(a),L()}finally{z.value=!1}}},new Promise(((s,t)=>{var o=a=>{try{i(r.next(a))}catch(e){t(e)}},l=a=>{try{i(r.throw(a))}catch(e){t(e)}},i=a=>a.done?s(a.value):Promise.resolve(a.value).then(o,l);i((r=r.apply(a,e)).next())}));var a,e,r},L=()=>{H.back()},N=()=>{K()},R=a=>O.formatWorkOrderStatus(a).text;return l((()=>{K()})),(a,e)=>{const r=f,s=y,t=w,o=_;return d(),i("div",g,[u("div",D,[c(s,{onClick:L,type:"text",class:"back-button"},{default:m((()=>[c(r,null,{default:m((()=>[c(h(k))])),_:1}),e[0]||(e[0]=v(" 返回 "))])),_:1}),G.value?(d(),i("div",S,[u("h2",null,j(G.value.title),1),u("div",A,[c(t,{type:(x=G.value.status,O.formatWorkOrderStatus(x).type)},{default:m((()=>[v(j(R(G.value.status)),1)])),_:1},8,["type"]),u("span",C,j((l=G.value.created_at,new Date(l).toLocaleString("zh-CN"))),1)])])):p("",!0)]),n((d(),i("div",E,[c(b,{visible:!0,"work-order-id":I.value,"is-page-mode":!0,onRefresh:N},null,8,["work-order-id"])])),[[o,z.value]])]);var l,x}}}),[["__scopeId","data-v-4571ac13"]]);export{H as default};
