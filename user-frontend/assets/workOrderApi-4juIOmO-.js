var t=(t,r,e)=>new Promise(((s,a)=>{var n=t=>{try{u(e.next(t))}catch(r){a(r)}},i=t=>{try{u(e.throw(t))}catch(r){a(r)}},u=t=>t.done?s(t.value):Promise.resolve(t.value).then(n,i);u((e=e.apply(t,r)).next())}));import{f as r}from"./index-F0f2Vd-3.js";class e{static createWorkOrder(e){return t(this,null,(function*(){return r.post({url:"/api/v1/workorders",params:e})}))}static getWorkOrder(e){return t(this,null,(function*(){return r.get({url:`/api/v1/workorders/${e}`})}))}static getWorkOrderList(e){return t(this,null,(function*(){const t=Object.fromEntries(Object.entries(e||{}).filter((([t,r])=>null!=r&&""!==r)));return r.get({url:"/api/v1/workorders",params:t})}))}static replyWorkOrder(e,s){return t(this,null,(function*(){return r.post({url:`/api/v1/workorders/${e}/replies`,params:s})}))}static deleteWorkOrder(e){return t(this,null,(function*(){return r.del({url:`/api/v1/workorders/${e}`})}))}static uploadAttachment(e){return t(this,null,(function*(){const t=new FormData;return t.append("file",e),r.post({url:"/api/v1/workorders/attachments",data:t,headers:{"Content-Type":"multipart/form-data"}})}))}static getSupportedTypes(e){return t(this,null,(function*(){const t=e?{provider:e}:{};return r.get({url:"/api/v1/workorders/types",params:t})}))}static getWorkOrderStatistics(e){return t(this,null,(function*(){return r.get({url:"/api/v1/workorders/statistics",params:e})}))}static getWorkOrderTrend(e){return t(this,null,(function*(){return r.get({url:"/api/v1/workorders/trend",params:e})}))}static getWorkOrderTypeStatistics(e){return t(this,null,(function*(){return r.get({url:"/api/v1/workorders/type-statistics",params:e})}))}static uploadMultipleAttachments(r){return t(this,null,(function*(){const t=r.map((t=>this.uploadAttachment(t)));return(yield Promise.allSettled(t)).filter((t=>"fulfilled"===t.status&&t.value.success)).map((t=>t.value.data))}))}static getWorkOrderDetail(r){return t(this,null,(function*(){const t=yield this.getWorkOrder(r);if(!t.success||!t.data)throw new Error(t.message||"获取工单详情失败");return t.data}))}static canReplyWorkOrder(t){return[1,2,3].includes(t.status)}static canEditWorkOrder(t){return 1===t.status}static canDeleteWorkOrder(t){if(![1,2].includes(t.status)){return{canDelete:!1,reason:`工单状态不允许删除，当前状态: ${this.formatWorkOrderStatus(t.status).text}`}}const r=new Date(t.created_at).getTime();return((new Date).getTime()-r)/36e5>24?{canDelete:!1,reason:"工单创建超过24小时，无法删除"}:{canDelete:!0}}static formatWorkOrderStatus(t){return{1:{text:"待处理",type:"warning"},2:{text:"处理中",type:"primary"},3:{text:"已回复",type:"success"},4:{text:"已完结",type:"info"}}[t]||{text:"未知状态",type:"danger"}}static formatWorkOrderPriority(t){return{1:{text:"高",type:"danger"},2:{text:"中",type:"warning"},3:{text:"低",type:"success"}}[t]||{text:"未知",type:"info"}}static getWorkOrderTypeName(t){return{1:"催取件",2:"重量异常",12:"催派送",16:"物流停滞",17:"重新分配快递员",19:"取消订单"}[t]||"未知类型"}}export{e as W};
