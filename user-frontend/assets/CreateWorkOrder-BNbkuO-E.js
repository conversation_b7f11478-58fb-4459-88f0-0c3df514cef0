var e=(e,r,l)=>new Promise(((a,t)=>{var o=e=>{try{i(l.next(e))}catch(r){t(r)}},n=e=>{try{i(l.throw(e))}catch(r){t(r)}},i=e=>e.done?a(e.value):Promise.resolve(e.value).then(o,n);i((l=l.apply(e,r)).next())}));import{_ as r}from"./index-F0f2Vd-3.js";/* empty css                *//* empty css                  *//* empty css                    *//* empty css                 *//* empty css                       *//* empty css                 */import{r as l,X as a,j as t,M as o,d as n,a1 as i,c as d,o as s,b as u,w as c,Z as p,G as _,u as v,N as m,a as g,_ as f,e as y,b8 as h,F as k,A as b,b9 as w,x,aX as V,K as O,C as j,H as C,aV as q,y as I,bP as E,aq as T,bQ as U,bD as W,b4 as B,aS as A,bR as R,bS as S,aR as D,ai as z}from"./vendor-BVh5F9vp.js";import{W as F}from"./workOrderApi-4juIOmO-.js";import{E as P}from"./expressApi-BL8dLu27.js";const L={class:"smart-create-workorder"},X={class:"card-header"},G={class:"type-selection"},H={class:"type-option"},K={class:"type-name"},M={class:"type-desc"},N={class:"order-input-group"},Q={key:0,class:"order-info-display"},Z={class:"order-details"},$={key:1,class:"order-error"},J={class:"content-tips"},Y={class:"field-tip"},ee={class:"field-tip"},re={class:"attachment-section"},le={class:"upload-tips"},ae={class:"submit-section"},te=r(n({__name:"CreateWorkOrder",setup(r){const n=i(),{submitting:te,orderLoading:oe,formData:ne,orderInfo:ie,orderError:de,isWeightException:se,hasOrderIdentifier:ue,canSubmit:ce,workOrderTypeOptions:pe,formRules:_e,queryOrderInfo:ve,createWorkOrder:me,resetForm:ge}=function(){const r=l(!1),n=l(!1),i=l(!1),d=a({work_order_type:2,content:"",order_no:"",tracking_no:"",feedback_weight:void 0,goods_value:void 0,overweight_amount:void 0,attachment_urls:[]}),s=l(null),u=l(""),c=[{value:2,label:"重量异常",description:"包裹实际重量与下单重量不符",category:"费用争议",priority:1},{value:12,label:"催派送",description:"催促快递员派送",category:"派送服务",priority:2},{value:1,label:"催取件",description:"催促快递员取件",category:"取件服务",priority:3},{value:16,label:"物流停滞",description:"物流信息长时间未更新",category:"物流服务",priority:4},{value:17,label:"重新分配快递员",description:"申请更换派送快递员",category:"派送服务",priority:5},{value:19,label:"取消订单",description:"取消快递订单",category:"订单管理",priority:6}],p=t((()=>c.find((e=>e.value===d.work_order_type)))),_=t((()=>2===d.work_order_type)),v=t((()=>{var e,r;return!(!(null==(e=d.order_no)?void 0:e.trim())&&!(null==(r=d.tracking_no)?void 0:r.trim()))})),m=t((()=>{var e;return!(!(d.work_order_type&&(null==(e=d.content)?void 0:e.trim())&&v.value)||n.value)})),g=()=>e(this,null,(function*(){var e,r,l,a;if(!v.value)return s.value=null,void(u.value="");i.value=!0,u.value="";try{if(!(null==(e=d.order_no)?void 0:e.trim())&&!(null==(r=d.tracking_no)?void 0:r.trim()))return;const t=yield P.queryOrder({order_no:(null==(l=d.order_no)?void 0:l.trim())||void 0,tracking_no:(null==(a=d.tracking_no)?void 0:a.trim())||void 0});t.success&&t.data?(s.value=t.data,u.value="",t.data.tracking_no&&!d.tracking_no&&(d.tracking_no=t.data.tracking_no),t.data.order_no&&!d.order_no&&(d.order_no=t.data.order_no),o.success("订单信息查询成功")):(u.value=t.message||"未找到对应的订单",s.value=null)}catch(t){u.value=t.message||"查询订单信息失败",s.value=null}finally{i.value=!1}})),f=()=>{Object.assign(d,{work_order_type:2,content:"",order_no:"",tracking_no:"",feedback_weight:void 0,goods_value:void 0,overweight_amount:void 0,attachment_urls:[]}),s.value=null,u.value=""};return{loading:r,submitting:n,orderLoading:i,formData:d,orderInfo:s,orderError:u,selectedTypeInfo:p,isWeightException:_,hasOrderIdentifier:v,canSubmit:m,workOrderTypeOptions:c,formRules:{work_order_type:[{required:!0,message:"请选择工单类型",trigger:"change"}],content:[{required:!0,message:"请填写问题描述",trigger:"blur"},{min:10,message:"问题描述至少需要10个字符",trigger:"blur"},{max:1e3,message:"问题描述不能超过1000个字符",trigger:"blur"}],order_identifier:[{validator:(e,r,l)=>{var a,t;(null==(a=d.order_no)?void 0:a.trim())||(null==(t=d.tracking_no)?void 0:t.trim())?l():l(new Error("请填写订单号或运单号"))},trigger:"blur"}],feedback_weight:[{validator:(e,r,l)=>{_.value&&void 0!==r&&r<=0?l(new Error("反馈重量必须大于0")):l()},trigger:"blur"}]},queryOrderInfo:g,createWorkOrder:()=>e(this,null,(function*(){var e,r,l;if(!m.value)return o.warning("请完善工单信息"),null;n.value=!0;try{const a={work_order_type:d.work_order_type,content:d.content.trim(),order_no:(null==(e=d.order_no)?void 0:e.trim())||void 0,tracking_no:(null==(r=d.tracking_no)?void 0:r.trim())||void 0,feedback_weight:d.feedback_weight,goods_value:d.goods_value,overweight_amount:d.overweight_amount,attachment_urls:(null==(l=d.attachment_urls)?void 0:l.filter((e=>e.trim())))||[]},t=yield F.createWorkOrder(a);if(t.success&&t.data)return o.success("工单创建成功"),f(),t.data;throw new Error(t.message||"创建工单失败")}catch(a){return o.error(a.message||"创建工单失败"),null}finally{n.value=!1}})),resetForm:f,setWorkOrderType:e=>{d.work_order_type=e},setOrderIdentifier:(e,r)=>{e&&(d.order_no=e),r&&(d.tracking_no=r),(e||r)&&g()},addAttachmentUrl:e=>{d.attachment_urls||(d.attachment_urls=[]),e.trim()&&!d.attachment_urls.includes(e.trim())&&d.attachment_urls.push(e.trim())},removeAttachmentUrl:e=>{d.attachment_urls&&e>=0&&e<d.attachment_urls.length&&d.attachment_urls.splice(e,1)},getTypeDescription:e=>{const r=c.find((r=>r.value===e));return(null==r?void 0:r.description)||"未知工单类型"},getTypeCategory:e=>{const r=c.find((r=>r.value===e));return(null==r?void 0:r.category)||"其他"}}}(),fe=l(),ye=l([]),he=(e,r)=>{},ke=()=>e(this,null,(function*(){var e;try{yield null==(e=fe.value)?void 0:e.validate(),yield z.confirm("确认创建工单吗？系统将自动转发给对应的快递供应商处理。","确认创建",{confirmButtonText:"确认创建",cancelButtonText:"取消",type:"info"});const r=yield me();r&&n.push(`/workorder/detail/${r.id}`)}catch(r){}}));return(e,r)=>{const l=I,a=V,t=w,o=h,n=f,i=j,z=C,F=q,P=T,me=W,be=p,we=D;return s(),d("div",L,[u(we,{class:"form-card"},{header:c((()=>[y("div",X,[y("h3",null,[u(l,null,{default:c((()=>[u(v(S))])),_:1}),r[8]||(r[8]=O(" 创建工单 "))]),r[9]||(r[9]=y("p",{class:"subtitle"},"只需提供问题类型和订单信息，系统自动处理其他细节",-1))])])),default:c((()=>[u(be,{ref_key:"formRef",ref:fe,model:v(ne),rules:v(_e),"label-width":"120px",class:"smart-form",onSubmit:_(ke,["prevent"])},{default:c((()=>[u(n,{label:"问题类型",prop:"work_order_type",required:""},{default:c((()=>[y("div",G,[u(o,{modelValue:v(ne).work_order_type,"onUpdate:modelValue":r[0]||(r[0]=e=>v(ne).work_order_type=e),class:"type-radio-group"},{default:c((()=>[(s(!0),d(k,null,b(v(pe),(e=>(s(),m(t,{key:e.value,label:e.value,class:"type-radio"},{default:c((()=>{return[y("div",H,[y("div",K,x(e.label),1),y("div",M,x(e.description),1),u(a,{type:(r=e.category,{"费用争议":"warning","派送服务":"primary","取件服务":"success","物流服务":"info","订单管理":"danger"}[r]||"info"),size:"small"},{default:c((()=>[O(x(e.category),1)])),_:2},1032,["type"])])];var r})),_:2},1032,["label"])))),128))])),_:1},8,["modelValue"])])])),_:1}),u(n,{label:"订单信息",prop:"order_identifier",required:""},{default:c((()=>[y("div",N,[u(i,{modelValue:v(ne).order_no,"onUpdate:modelValue":r[1]||(r[1]=e=>v(ne).order_no=e),placeholder:"请输入客户订单号",clearable:"",onBlur:v(ve),onClear:r[2]||(r[2]=e=>ie.value=null)},{prepend:c((()=>r[10]||(r[10]=[O("订单号")]))),_:1},8,["modelValue","onBlur"]),r[13]||(r[13]=y("div",{class:"or-divider"},"或",-1)),u(i,{modelValue:v(ne).tracking_no,"onUpdate:modelValue":r[3]||(r[3]=e=>v(ne).tracking_no=e),placeholder:"请输入运单号",clearable:"",onBlur:v(ve),onClear:r[4]||(r[4]=e=>ie.value=null)},{prepend:c((()=>r[11]||(r[11]=[O("运单号")]))),_:1},8,["modelValue","onBlur"]),u(z,{loading:v(oe),type:"primary",onClick:v(ve),disabled:!v(ue)},{default:c((()=>r[12]||(r[12]=[O(" 查询订单 ")]))),_:1},8,["loading","onClick","disabled"])]),v(ie)?(s(),d("div",Q,[u(F,{type:"success",closable:!1},{title:c((()=>{return[y("div",Z,[y("div",null,[r[14]||(r[14]=y("strong",null,"订单号:",-1)),O(" "+x(v(ie).order_no),1)]),y("div",null,[r[15]||(r[15]=y("strong",null,"运单号:",-1)),O(" "+x(v(ie).tracking_no),1)]),y("div",null,[r[16]||(r[16]=y("strong",null,"供应商:",-1)),O(" "+x((e=v(ie).provider,{kuaidi100:"快递100",yida:"易达",yuntong:"云通"}[e]||e)),1)]),y("div",null,[r[17]||(r[17]=y("strong",null,"快递公司:",-1)),O(" "+x(v(ie).express_company_name),1)])])];var e})),_:1})])):g("",!0),v(de)?(s(),d("div",$,[u(F,{type:"error",title:v(de),closable:!1},null,8,["title"])])):g("",!0)])),_:1}),u(n,{label:"问题描述",prop:"content",required:""},{default:c((()=>[u(i,{modelValue:v(ne).content,"onUpdate:modelValue":r[5]||(r[5]=e=>v(ne).content=e),type:"textarea",rows:4,placeholder:"请详细描述您遇到的问题，以便我们更好地为您处理",maxlength:"1000","show-word-limit":""},null,8,["modelValue"]),y("div",J,[u(l,null,{default:c((()=>[u(v(E))])),_:1}),r[18]||(r[18]=y("span",null,"建议包含：具体问题、期望解决方案、联系方式等信息",-1))])])),_:1}),v(se)?(s(),m(n,{key:0,label:"反馈重量",prop:"feedback_weight"},{default:c((()=>[u(P,{modelValue:v(ne).feedback_weight,"onUpdate:modelValue":r[6]||(r[6]=e=>v(ne).feedback_weight=e),min:.1,max:1e3,precision:2,placeholder:"请输入实际重量",style:{width:"200px"}},null,8,["modelValue"]),r[20]||(r[20]=y("span",{class:"unit"},"kg",-1)),y("div",Y,[u(l,null,{default:c((()=>[u(v(U))])),_:1}),r[19]||(r[19]=y("span",null,"请填写包裹的实际重量，我们将核实重量差异",-1))])])),_:1})):g("",!0),u(n,{label:"商品价值",prop:"goods_value"},{default:c((()=>[u(P,{modelValue:v(ne).goods_value,"onUpdate:modelValue":r[7]||(r[7]=e=>v(ne).goods_value=e),min:0,max:1e5,precision:2,placeholder:"请输入商品价值",style:{width:"200px"}},null,8,["modelValue"]),r[22]||(r[22]=y("span",{class:"unit"},"元",-1)),y("div",ee,[u(l,null,{default:c((()=>[u(v(U))])),_:1}),r[21]||(r[21]=y("span",null,"选填，用于理赔参考",-1))])])),_:1}),u(n,{label:"相关图片"},{default:c((()=>[y("div",re,[u(me,{class:"upload-demo",action:"#","auto-upload":!1,"on-change":he,"file-list":ye.value,"list-type":"picture-card",accept:"image/*"},{default:c((()=>[u(l,null,{default:c((()=>[u(v(B))])),_:1})])),_:1},8,["file-list"]),y("div",le,[u(l,null,{default:c((()=>[u(v(E))])),_:1}),r[23]||(r[23]=y("span",null,"支持上传相关图片，如包裹照片、重量凭证等（可选）",-1))])])])),_:1}),u(n,null,{default:c((()=>[y("div",ae,[u(z,{type:"primary",size:"large",loading:v(te),disabled:!v(ce),onClick:ke},{default:c((()=>[u(l,null,{default:c((()=>[u(v(A))])),_:1}),O(" "+x(v(te)?"创建中...":"创建工单"),1)])),_:1},8,["loading","disabled"]),u(z,{size:"large",onClick:v(ge)},{default:c((()=>[u(l,null,{default:c((()=>[u(v(R))])),_:1}),r[24]||(r[24]=O(" 重置表单 "))])),_:1},8,["onClick"])])])),_:1})])),_:1},8,["model","rules"])])),_:1}),u(we,{class:"help-card"},{header:c((()=>[y("h4",null,[u(l,null,{default:c((()=>[u(v(U))])),_:1}),r[25]||(r[25]=O(" 使用说明"))])])),default:c((()=>[r[26]||(r[26]=y("div",{class:"help-content"},[y("div",{class:"help-item"},[y("h5",null,"1. 选择问题类型"),y("p",null,"根据您遇到的实际问题选择对应的工单类型，系统会自动匹配最合适的处理流程。")]),y("div",{class:"help-item"},[y("h5",null,"2. 提供订单信息"),y("p",null,"输入客户订单号或运单号，系统会自动查询订单信息并识别对应的快递供应商。")]),y("div",{class:"help-item"},[y("h5",null,"3. 详细描述问题"),y("p",null,"请尽可能详细地描述您遇到的问题，这有助于客服人员更快地为您解决问题。")]),y("div",{class:"help-item"},[y("h5",null,"4. 系统自动处理"),y("p",null,"提交后，系统会自动将工单转发给对应的快递供应商，并为您跟踪处理进度。")])],-1))])),_:1})])}}}),[["__scopeId","data-v-ddd9b44c"]]);export{te as default};
