import{T as t,_ as e}from"./index-F0f2Vd-3.js";import{d as s,c as a,o as r,b as n}from"./vendor-BVh5F9vp.js";const o={class:"page-content"},d=e(s({__name:"TextScroll",setup:e=>(e,s)=>(r(),a("div",o,[n(t,{text:"Art Design Pro 是一款专注于用户体验和视觉设计的后台管理系统模版 <a target='_blank' href='https://www.lingchen.kim/art-design-pro/docs/'>点击我 </a>访问官方文档"}),n(t,{type:"success",text:"这是一条成功类型的滚动公告"}),n(t,{type:"warning",text:"这是一条警告类型的滚动公告"}),n(t,{type:"danger",text:"这是一条危险类型的滚动公告"}),n(t,{type:"info",text:"这是一条信息类型的滚动公告"}),n(t,{text:"这是一条速度较慢、向右滚动的公告",speed:30,direction:"right"})]))}),[["__scopeId","data-v-d53ed20a"]]);export{d as default};
