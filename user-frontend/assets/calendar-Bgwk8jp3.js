var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,d=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,n=(e,n)=>{for(var u in n||(n={}))l.call(n,u)&&d(e,u,n[u]);if(a)for(var u of a(n))t.call(n,u)&&d(e,u,n[u]);return e};import{_ as u}from"./index-F0f2Vd-3.js";/* empty css                       *//* empty css                       *//* empty css                 */import{d as o,r,j as s,c as v,o as c,b as p,w as i,e as m,v as y,x as b,F as g,A as f,G as D,bG as V,Z as Y,_,C as w,b8 as j,b9 as h,K as k,bh as C,N as M,a as x,H as O,Y as U}from"./vendor-BVh5F9vp.js";const I={class:"page-content"},P=["onClick"],$={class:"calendar-date"},q={class:"calendar-events"},G=["onClick"],S={class:"dialog-footer"},A=u(o({__name:"calendar",setup(e){const a=[{label:"基本",value:"bg-primary"},{label:"成功",value:"bg-success"},{label:"警告",value:"bg-warning"},{label:"危险",value:"bg-danger"}],l=r(new Date("2025-02-07")),t=r([{date:"2025-02-01",content:"产品需求评审",type:"bg-primary"},{date:"2025-02-03",endDate:"2025-02-05",content:"项目周报会议（跨日期）",type:"bg-primary"},{date:"2025-02-10",content:"瑜伽课程",type:"bg-success"},{date:"2025-02-15",content:"团队建设活动",type:"bg-primary"},{date:"2025-02-20",content:"健身训练",type:"bg-success"},{date:"2025-02-20",content:"代码评审",type:"bg-danger"},{date:"2025-02-20",content:"团队午餐",type:"bg-primary"},{date:"2025-02-20",content:"项目进度汇报",type:"bg-warning"},{date:"2025-02-28",content:"月度总结会",type:"bg-warning"}]),d=r(!1),u=r("添加事件"),o=r(-1),A=r({date:"",endDate:"",content:"",type:"bg-primary"}),E=s((()=>o.value>=0)),F=()=>{A.value={date:"",endDate:"",content:"",type:"bg-primary"},o.value=-1},H=()=>{A.value.content&&A.value.date&&(E.value?t.value[o.value]=n({},A.value):t.value.push(n({},A.value)),d.value=!1,F())},K=()=>{E.value&&(t.value.splice(o.value,1),d.value=!1,F())};return(e,r)=>{const s=V,N=w,Z=_,z=h,B=j,J=C,L=Y,Q=O,R=U;return c(),v("div",I,[p(s,{modelValue:l.value,"onUpdate:modelValue":r[0]||(r[0]=e=>l.value=e)},{"date-cell":i((({data:e})=>{return[m("div",{class:y(["calendar-cell",{"is-selected":e.isSelected}]),onClick:a=>{return l=e.day,u.value="添加事件",A.value={date:l,content:"",type:"bg-primary"},o.value=-1,void(d.value=!0);var l}},[m("p",$,b((l=e.day,l.split("-")[2])),1),m("div",q,[(c(!0),v(g,null,f((a=e.day,t.value.filter((e=>{const l=new Date(e.date),t=new Date(a),d=e.endDate?new Date(e.endDate):new Date(e.date);return t>=l&&t<=d}))),(e=>(c(),v("div",{key:`${e.date}-${e.content}`,class:"calendar-event",onClick:D((a=>(e=>{u.value="编辑事件",A.value=n({},e),o.value=t.value.findIndex((a=>a.date===e.date&&a.content===e.content)),d.value=!0})(e)),["stop"])},[m("div",{class:y(["event-tag",[`${e.type||"bg-primary"}`]])},b(e.content),3)],8,G)))),128))])],10,P)];var a,l})),_:1},8,["modelValue"]),p(R,{modelValue:d.value,"onUpdate:modelValue":r[5]||(r[5]=e=>d.value=e),title:u.value,width:"600px",onClosed:F},{footer:i((()=>[m("span",S,[E.value?(c(),M(Q,{key:0,type:"danger",onClick:K},{default:i((()=>r[6]||(r[6]=[k(" 删除 ")]))),_:1})):x("",!0),p(Q,{type:"primary",onClick:H},{default:i((()=>[k(b(E.value?"更新":"添加"),1)])),_:1})])])),default:i((()=>[p(L,{model:A.value,"label-width":"80px"},{default:i((()=>[p(Z,{label:"活动标题",required:""},{default:i((()=>[p(N,{modelValue:A.value.content,"onUpdate:modelValue":r[1]||(r[1]=e=>A.value.content=e),placeholder:"请输入活动标题"},null,8,["modelValue"])])),_:1}),p(Z,{label:"事件颜色"},{default:i((()=>[p(B,{modelValue:A.value.type,"onUpdate:modelValue":r[2]||(r[2]=e=>A.value.type=e)},{default:i((()=>[(c(),v(g,null,f(a,(e=>p(z,{key:e.value,value:e.value},{default:i((()=>[k(b(e.label),1)])),_:2},1032,["value"]))),64))])),_:1},8,["modelValue"])])),_:1}),p(Z,{label:"开始日期",required:""},{default:i((()=>[p(J,{style:{width:"100%"},modelValue:A.value.date,"onUpdate:modelValue":r[3]||(r[3]=e=>A.value.date=e),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),p(Z,{label:"结束日期"},{default:i((()=>[p(J,{style:{width:"100%"},modelValue:A.value.endDate,"onUpdate:modelValue":r[4]||(r[4]=e=>A.value.endDate=e),type:"date",placeholder:"选择结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","min-date":A.value.date},null,8,["modelValue","min-date"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-e6077b79"]]);export{A as default};
