import{u as e,_ as a}from"./index-F0f2Vd-3.js";/* empty css                */import{d as t,r as s,c as r,o as l,b as o,w as n,E as d,e as i,K as c,aR as u,x as g,u as h,H as p}from"./vendor-BVh5F9vp.js";const f={class:"page-content"},m=a(t({__name:"Watermark",setup(a){const t=e(),m=s("https://element-plus.org/images/element-plus-logo.svg"),_=()=>{t.setWatermarkVisible(!t.watermarkVisible)};return(e,a)=>{const s=d,v=u,w=p;return l(),r("div",f,[o(v,{class:"card",shadow:"never"},{header:n((()=>a[0]||(a[0]=[c("基础文字水印")]))),default:n((()=>[o(s,{content:"Art Design Pro",font:{color:"rgba(128, 128, 128, 0.2)"}},{default:n((()=>a[1]||(a[1]=[i("div",{style:{height:"200px"}},null,-1)]))),_:1})])),_:1}),o(v,{class:"card",shadow:"never"},{header:n((()=>a[2]||(a[2]=[c("多行文字水印")]))),default:n((()=>[o(s,{content:["Art Design Pro","专注用户体验，视觉设计"],font:{fontSize:16,color:"rgba(128, 128, 128, 0.2)"}},{default:n((()=>a[3]||(a[3]=[i("div",{style:{height:"200px"}},null,-1)]))),_:1})])),_:1}),o(v,{class:"card",shadow:"never"},{header:n((()=>a[4]||(a[4]=[c("图片水印")]))),default:n((()=>[o(s,{image:m.value,opacity:.2,width:80,height:20},{default:n((()=>a[5]||(a[5]=[i("div",{style:{height:"200px"}},null,-1)]))),_:1},8,["image"])])),_:1}),o(v,{class:"card",shadow:"never"},{header:n((()=>a[6]||(a[6]=[c("自定义样式水印")]))),default:n((()=>[o(s,{content:"Art Design Pro",font:{fontSize:20,fontFamily:"Arial",color:"rgba(255, 0, 0, 0.3)"},rotate:-22,gap:[100,100]},{default:n((()=>a[7]||(a[7]=[i("div",{style:{height:"200px"}},null,-1)]))),_:1})])),_:1}),o(w,{type:h(t).watermarkVisible?"danger":"primary",onClick:_},{default:n((()=>[c(g(h(t).watermarkVisible?"隐藏全局水印":"显示全局水印"),1)])),_:1},8,["type"])])}}}),[["__scopeId","data-v-66a4edc1"]]);export{m as default};
