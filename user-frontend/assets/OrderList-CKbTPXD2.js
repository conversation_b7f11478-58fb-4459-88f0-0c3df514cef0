var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,s=(a,l,r)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,o=(e,a,l)=>new Promise(((r,t)=>{var i=e=>{try{o(l.next(e))}catch(a){t(a)}},s=e=>{try{o(l.throw(e))}catch(a){t(a)}},o=e=>e.done?r(e.value):Promise.resolve(e.value).then(i,s);o((l=l.apply(e,a)).next())}));import{_ as n}from"./index-F0f2Vd-3.js";/* empty css                */import{d,r as u,j as c,X as p,f as v,c as _,o as g,b as m,w as f,e as h,a2 as y,Z as w,_ as b,C as k,ar as j,F as x,A as O,N as T,as as z,H as C,K as S,aJ as D,aH as V,x as U,aX as E,a as N,aG as F,aK as L,y as P,u as Y,b4 as M,aR as A,M as B,ai as H}from"./vendor-BVh5F9vp.js";import{E as J}from"./expressApi-BL8dLu27.js";import{O as $,C as I}from"./OrderDetailDialog-oxR6WZlt.js";import{E as K}from"./errorHandler-Dnd-hi0l.js";import Z from"./CreateOrderDialog-C0JKSSUN.js";import q from"./CreateWorkOrderDialog-C4e8e6Tj.js";/* empty css                         *//* empty css                   *//* empty css                             *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css               */import"./AddressCascader-CQSS5hj1.js";import"./el-collapse-transition-l0sNRNKZ.js";import"./address-IlaM-Wag.js";/* empty css                  *//* empty css                    */import"./workOrderApi-4juIOmO-.js";import"./useWorkOrderTypes-B5Knz6RB.js";const W={class:"express-order-list"},X={class:"card-header"},G={class:"search-area"},R={class:"weight-volume-info"},Q={class:"info-row"},ee={class:"weight-value"},ae={class:"volume-value"},le={class:"info-row"},re={class:"weight-value"},te={class:"volume-value"},ie={class:"info-row"},se={class:"weight-value"},oe={class:"fee-value"},ne={key:0,class:"courier-info"},de={key:0,class:"courier-name"},ue={key:1,class:"courier-phone"},ce={key:1,class:"no-courier"},pe={class:"time-info"},ve={class:"full-time"},_e={class:"relative-time"},ge={class:"action-buttons"},me={class:"pagination-wrapper"},fe=n(d({__name:"OrderList",setup(e){const n=u(!1),d=u(!1),fe=u(!1),he=u(!1),ye=u(!1),we=u([]),be=u(null),ke=u(null),je=u([]),xe=u([]),Oe=c((()=>je.value.map((e=>({label:e.name,value:e.code}))))),Te=c((()=>xe.value.map((e=>({label:e.name,value:e.code}))))),ze=p({page:1,page_size:20,status:void 0,express_type:void 0,provider:void 0,customer_order_no:void 0,weight_anomaly:void 0,start_time:void 0,end_time:void 0,sort_by:"created_at",sort_order:"desc"}),Ce=p({page:1,page_size:20,total:0}),Se=()=>o(this,null,(function*(){d.value=!0;try{const[e,a]=yield Promise.all([I.getExpressCompanies(),I.getOrderStatuses()]);je.value=e,xe.value=a}catch(e){K.handleApiError(e,!1)}finally{d.value=!1}})),De=()=>o(this,null,(function*(){n.value=!0;try{const n=(e=((e,a)=>{for(var l in a||(a={}))t.call(a,l)&&s(e,l,a[l]);if(r)for(var l of r(a))i.call(a,l)&&s(e,l,a[l]);return e})({},ze),o={page:Ce.page,page_size:Ce.page_size},a(e,l(o))),d=yield J.getOrderList(n);if(!d.success||!d.data)throw new Error(d.message||"获取订单列表失败");we.value=d.data.items||[],Ce.total=d.data.total||0}catch(d){K.handleApiError(d),we.value=[],Ce.total=0}finally{n.value=!1}var e,o})),Ve=()=>{Ce.page=1,De()},Ue=()=>{Object.assign(ze,{page:1,page_size:20,order_no:void 0,tracking_no:void 0,status:void 0,express_type:void 0,provider:void 0,customer_order_no:void 0,weight_anomaly:void 0,start_time:void 0,end_time:void 0,sort_by:"created_at",sort_order:"desc"}),Ce.page=1,Ce.page_size=20,De(),B.success("搜索条件已重置")},Ee=e=>{Ce.page_size=e,Ce.page=1,De()},Ne=e=>{Ce.page=e,De()},Fe=e=>o(this,null,(function*(){try{yield H.confirm(`确定要取消订单 ${e.order_no} 吗？取消后将无法恢复。`,"确认取消订单",{confirmButtonText:"确定取消",cancelButtonText:"暂不取消",type:"warning",dangerouslyUseHTMLString:!1});const a=yield J.cancelOrder({order_no:e.order_no,reason:"用户主动取消"});if(!a.success)throw new Error(a.message||"取消订单失败");a.message&&a.message.includes("等待供应商确认")?B.success("取消请求已发起，等待供应商确认"):B.success(a.message||"订单取消成功"),yield De()}catch(a){"cancel"!==a&&K.handleApiError(a)}})),Le=()=>{Ce.page=1,ze.sort_by="created_at",ze.sort_order="desc",De(),B.success("订单创建成功，已为您刷新列表")},Pe=()=>{ye.value=!1,ke.value=null,B.success("售后申请已提交，我们将尽快为您处理"),De()},Ye=e=>!["cancelled","canceled","cancelling","voided"].includes(e.status),Me=e=>{const a=xe.value.find((a=>a.code===e));if(null==a?void 0:a.color)return a.color;return{created:"info",submitted:"primary",assigned:"info",awaiting_pickup:"warning",picked_up:"primary",in_transit:"info",out_for_delivery:"info",delivered:"success",failed_delivery:"danger",returned:"warning",cancelling:"warning",cancelled:"danger",canceled:"danger",processing:"primary",billed:"success",voided:"danger",exception:"danger"}[e]||"info"},Ae=e=>{if(!e)return"-";if(e.express_name&&!Be(e.express_name))return e.express_name;const a=e.express_type||e.express_name;return{shentong:"申通快递",yuantong:"圆通快递",zhongtong:"中通快递",yunda:"韵达快递",jtexpress:"极兔快递",jd:"京东快递",debangkuaidi:"德邦快递",debangwuliu:"德邦物流",ems:"EMS",sf:"顺丰快递",ST:"申通快递",YT:"圆通快递",ZT:"中通快递",YD:"韵达快递",JT:"极兔快递",JD:"京东物流","STO-INT":"申通快递",YUND:"韵达快递",SF:"顺丰速运",STO:"申通快递",DBL:"德邦物流",DBL360:"德邦大件360",EMS:"中国邮政",HTKY:"百世快递"}[a]||a||"-"},Be=e=>{const a=["shentong","yuantong","zhongtong","yunda","jtexpress","jd","debangkuaidi","debangwuliu","ems","sf","ST","YT","ZT","YD","JT","JD","STO-INT","YTO","ZTO","YUND","SF","STO","DBL","DBL360","EMS","HTKY"];return a.includes(e)||a.includes(e.toLowerCase())},He=e=>("string"==typeof e?parseFloat(e):e).toFixed(2),Je=e=>{if(null==e)return"0.0";const a="string"==typeof e?parseFloat(e):e;return isNaN(a)?"0.0":a.toFixed(1)},$e=e=>{if(null==e)return"0.00cm³";const a="string"==typeof e?parseFloat(e):e;if(isNaN(a))return"0.00cm³";return`${(1e6*a).toFixed(2)}cm³`},Ie=e=>{if(!e)return"-";try{return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return e}},Ke=e=>{if(!e)return"";try{const a=new Date(e),l=(new Date).getTime()-a.getTime(),r=Math.floor(l/6e4),t=Math.floor(l/36e5),i=Math.floor(l/864e5);return r<1?"刚刚":r<60?`${r}分钟前`:t<24?`${t}小时前`:i<7?`${i}天前`:Ie(e).split(" ")[0]}catch(a){return""}},Ze=e=>{const a=e.weight||0,l=e.actual_weight||0,r=e.actual_fee||0,t=e.price||0;return l<=0||r<=0?null:l>a&&r>t?"overweight":l<a&&r<t?"underweight":null},qe=e=>{const a=Ze(e);return"overweight"===a?"danger":"underweight"===a?"warning":"info"},We=e=>{const a=Ze(e);return"overweight"===a?"超重":"underweight"===a?"超轻":""};return v((()=>o(this,null,(function*(){yield Se(),yield De()})))),(e,a)=>{const l=P,r=C,t=k,i=b,s=z,o=j,u=w,c=V,p=E,v=D,B=L,H=A,J=F;return g(),_("div",W,[m(H,{class:"box-card"},{header:f((()=>[h("div",X,[a[13]||(a[13]=h("span",null,"快递订单管理",-1)),m(r,{type:"primary",onClick:a[0]||(a[0]=e=>fe.value=!0)},{default:f((()=>[m(l,null,{default:f((()=>[m(Y(M))])),_:1}),a[12]||(a[12]=S(" 创建订单 "))])),_:1})])])),default:f((()=>[h("div",G,[m(u,{model:ze,inline:""},{default:f((()=>[m(i,{label:"平台订单号"},{default:f((()=>[m(t,{modelValue:ze.order_no,"onUpdate:modelValue":a[1]||(a[1]=e=>ze.order_no=e),placeholder:"请输入平台订单号",clearable:""},null,8,["modelValue"])])),_:1}),m(i,{label:"客户订单号"},{default:f((()=>[m(t,{modelValue:ze.customer_order_no,"onUpdate:modelValue":a[2]||(a[2]=e=>ze.customer_order_no=e),placeholder:"请输入客户订单号",clearable:""},null,8,["modelValue"])])),_:1}),m(i,{label:"运单号"},{default:f((()=>[m(t,{modelValue:ze.tracking_no,"onUpdate:modelValue":a[3]||(a[3]=e=>ze.tracking_no=e),placeholder:"请输入运单号",clearable:""},null,8,["modelValue"])])),_:1}),m(i,{label:"订单状态"},{default:f((()=>[m(o,{modelValue:ze.status,"onUpdate:modelValue":a[4]||(a[4]=e=>ze.status=e),placeholder:"请选择状态",clearable:"",loading:d.value},{default:f((()=>[(g(!0),_(x,null,O(Te.value,(e=>(g(),T(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),m(i,{label:"快递公司"},{default:f((()=>[m(o,{modelValue:ze.express_type,"onUpdate:modelValue":a[5]||(a[5]=e=>ze.express_type=e),placeholder:"请选择快递公司",clearable:"",loading:d.value},{default:f((()=>[(g(!0),_(x,null,O(Oe.value,(e=>(g(),T(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),m(i,{label:"重量异常"},{default:f((()=>[m(o,{modelValue:ze.weight_anomaly,"onUpdate:modelValue":a[6]||(a[6]=e=>ze.weight_anomaly=e),placeholder:"请选择重量异常类型",clearable:"",style:{width:"150px"}},{default:f((()=>[m(s,{label:"超重订单",value:"overweight"}),m(s,{label:"超轻订单",value:"underweight"})])),_:1},8,["modelValue"])])),_:1}),m(i,null,{default:f((()=>[m(r,{type:"primary",onClick:Ve},{default:f((()=>a[14]||(a[14]=[S("搜索")]))),_:1}),m(r,{onClick:Ue},{default:f((()=>a[15]||(a[15]=[S("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),y((g(),T(v,{data:we.value,stripe:""},{default:f((()=>[m(c,{prop:"order_no",label:"平台订单号",width:"180"}),m(c,{prop:"customer_order_no",label:"客户订单号",width:"120","show-overflow-tooltip":""}),m(c,{prop:"tracking_no",label:"运单号",width:"180"}),m(c,{prop:"express_name",label:"快递公司",width:"140"},{default:f((({row:e})=>[S(U(Ae(e)),1)])),_:1}),m(c,{prop:"status_desc",label:"状态",width:"120"},{default:f((({row:e})=>[m(p,{type:Me(e.status)},{default:f((()=>{return[S(U((a=e.status,{submitted:"已提交",submit_failed:"提交失败",print_failed:"面单生成失败",assigned:"已分配",awaiting_pickup:"等待揽收",picked_up:"已揽收",pickup_failed:"揽收失败",in_transit:"运输中",out_for_delivery:"派送中",delivered:"已签收",delivered_abnormal:"异常签收",billed:"已计费",exception:"异常",returned:"已退回",forwarded:"已转寄",cancelling:"取消中",cancelled:"已取消",voided:"已作废",weight_updated:"重量更新",revived:"订单复活",failed_delivery:"派送失败",canceled:"已取消",processing:"处理中",created:"已创建"}[a]||a)),1)];var a})),_:2},1032,["type"])])),_:1}),m(c,{prop:"price",label:"预收",width:"100"},{default:f((({row:e})=>[S(" ¥"+U(He(e.price||0)),1)])),_:1}),m(c,{prop:"actual_fee",label:"实收",width:"100"},{default:f((({row:e})=>[S(" ¥"+U(He(e.actual_fee||0)),1)])),_:1}),m(c,{label:"重量体积",width:"250"},{default:f((({row:e})=>[h("div",R,[h("div",Q,[a[16]||(a[16]=h("span",{class:"label"},"下单:",-1)),h("span",ee,U(Je(e.weight))+"kg",1),h("span",ae,U($e(e.order_volume)),1),Ze(e)?(g(),T(p,{key:0,type:qe(e),size:"small",class:"weight-anomaly-tag"},{default:f((()=>[S(U(We(e)),1)])),_:2},1032,["type"])):N("",!0)]),h("div",le,[a[17]||(a[17]=h("span",{class:"label"},"实际:",-1)),h("span",re,U(Je(e.actual_weight))+"kg",1),h("span",te,U($e(e.actual_volume)),1)]),h("div",ie,[a[18]||(a[18]=h("span",{class:"label"},"计费:",-1)),h("span",se,U(Je(e.charged_weight))+"kg",1),h("span",oe,"¥"+U(He(e.actual_fee||0)),1)])])])),_:1}),m(c,{prop:"courier_info",label:"揽件员",width:"160"},{default:f((({row:e})=>[e.courier_name||e.courier_phone?(g(),_("div",ne,[e.courier_name?(g(),_("div",de,U(e.courier_name),1)):N("",!0),e.courier_phone?(g(),_("div",ue,U(e.courier_phone),1)):N("",!0)])):(g(),_("span",ce,"-"))])),_:1}),m(c,{prop:"created_at",label:"创建时间",width:"200"},{default:f((({row:e})=>[h("div",pe,[h("div",ve,U(Ie(e.created_at)),1),h("div",_e,U(Ke(e.created_at)),1)])])),_:1}),m(c,{label:"操作",width:"200",fixed:"right"},{default:f((({row:e})=>{return[h("div",ge,[m(r,{type:"primary",size:"small",onClick:a=>{return l=e,be.value=l,void(he.value=!0);var l}},{default:f((()=>a[19]||(a[19]=[S("详情")]))),_:2},1032,["onClick"]),(l=e,["picked_up","in_transit","out_for_delivery","delivered","delivered_abnormal","billed","cancelling","cancelled","canceled","voided","returned","forwarded"].includes(l.status)?N("",!0):(g(),T(r,{key:0,type:"danger",size:"small",onClick:a=>Fe(e)},{default:f((()=>a[20]||(a[20]=[S(" 取消 ")]))),_:2},1032,["onClick"]))),Ye(e)?(g(),T(r,{key:1,type:"warning",size:"small",onClick:a=>(e=>{ke.value=e,ye.value=!0})(e)},{default:f((()=>a[21]||(a[21]=[S(" 申请售后 ")]))),_:2},1032,["onClick"])):N("",!0)])];var l})),_:1})])),_:1},8,["data"])),[[J,n.value]]),h("div",me,[m(B,{"current-page":Ce.page,"onUpdate:currentPage":a[7]||(a[7]=e=>Ce.page=e),"page-size":Ce.page_size,"onUpdate:pageSize":a[8]||(a[8]=e=>Ce.page_size=e),"page-sizes":[10,20,50,100],total:Ce.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ee,onCurrentChange:Ne},null,8,["current-page","page-size","total"])])])),_:1}),m(Z,{visible:fe.value,"onUpdate:visible":a[9]||(a[9]=e=>fe.value=e),onSuccess:Le},null,8,["visible"]),m($,{visible:he.value,"onUpdate:visible":a[10]||(a[10]=e=>he.value=e),order:be.value},null,8,["visible","order"]),m(q,{visible:ye.value,"onUpdate:visible":a[11]||(a[11]=e=>ye.value=e),"prefilled-order-data":ke.value,onSuccess:Pe},null,8,["visible","prefilled-order-data"])])}}}),[["__scopeId","data-v-33d4f4fa"]]);export{fe as default};
