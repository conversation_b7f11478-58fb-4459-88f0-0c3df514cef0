import{_ as s}from"./index-F0f2Vd-3.js";/* empty css                */import{d as r,a1 as e,c as a,o,e as t,b as i,w as l,K as d,y as p,u as c,ad as m,H as n,aR as u,M as j}from"./vendor-BVh5F9vp.js";import f from"./CreateWorkOrderDialog-C4e8e6Tj.js";/* empty css                  *//* empty css                    *//* empty css                             *//* empty css                 *//* empty css                   */import"./expressApi-BL8dLu27.js";import"./workOrderApi-4juIOmO-.js";import"./errorHandler-Dnd-hi0l.js";import"./useWorkOrderTypes-B5Knz6RB.js";const k={class:"workorder-create-container"},_={class:"page-header"},b={class:"form-section"},v=s(r({__name:"WorkOrderCreate",setup(s){const r=e(),v=()=>{r.back()},g=()=>{j.success("工单创建成功"),r.push("/workorder/list")};return(s,r)=>{const e=p,j=n,w=u;return o(),a("div",k,[t("div",_,[i(j,{onClick:v,type:"text",class:"back-button"},{default:l((()=>[i(e,null,{default:l((()=>[i(c(m))])),_:1}),r[0]||(r[0]=d(" 返回 "))])),_:1}),r[1]||(r[1]=t("h2",null,"创建工单",-1)),r[2]||(r[2]=t("p",{class:"subtitle"},"请详细描述您遇到的问题，我们将尽快为您处理",-1))]),t("div",b,[i(w,null,{default:l((()=>[i(f,{visible:!0,"is-page-mode":!0,onSuccess:g,onCancel:v})])),_:1})])])}}}),[["__scopeId","data-v-4bc04920"]]);export{v as default};
