var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,s=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,n=(e,l)=>{for(var a in l||(l={}))r.call(l,a)&&s(e,a,l[a]);if(t)for(var a of t(l))o.call(l,a)&&s(e,a,l[a]);return e},d=(e,t)=>l(e,a(t));import{_ as c}from"./index-F0f2Vd-3.js";/* empty css                 */import{d as u,r as i,j as p,p as v,f as h,c as f,o as b,b as m,bx as g}from"./vendor-BVh5F9vp.js";const y={class:"address-cascader"},V=c(u({__name:"AddressCascader",props:{modelValue:{},placeholder:{default:"请选择地址"},size:{default:"default"},disabled:{type:Boolean,default:!1},clearable:{type:Boolean,default:!0},showAllLevels:{type:Boolean,default:!0},collapseTags:{type:Boolean,default:!1},separator:{default:" / "},filterable:{type:Boolean,default:!0},level:{default:3}},emits:["update:modelValue","change","expand-change","blur","focus","visible-change","remove-tag"],setup(e,{expose:l,emit:a}){const t=e,r=a,o=i([]),s=i([]),c=i(!1),u={value:"value",label:"label",children:"children",disabled:"disabled",leaf:"leaf",expandTrigger:"click",checkStrictly:t.level<3,emitPath:!0},V=p((()=>1===t.level?o.value.map((e=>d(n({},e),{children:void 0,leaf:!0}))):2===t.level?o.value.map((e=>{var l;return d(n({},e),{children:null==(l=e.children)?void 0:l.map((e=>d(n({},e),{children:void 0,leaf:!0})))})})):o.value));v((()=>t.modelValue),(e=>{Array.isArray(e)?s.value=[...e]:s.value="string"==typeof e&&e?[e]:[]}),{immediate:!0});const j=()=>{return e=this,l=null,a=function*(){try{c.value=!0;const l=["/getAreaCascaderVo.json","/data/getAreaCascaderVo.json","/data/areas.json"];let a=null;for(const t of l)try{const e=yield fetch(t);if(e.ok){a=yield e.json();break}}catch(e){continue}if(!a)throw new Error("所有数据源都无法加载");if(200!==a.code||!a.data)throw new Error(a.msg||"地址数据格式错误");o.value=a.data}catch(l){}finally{c.value=!1}},new Promise(((t,r)=>{var o=e=>{try{n(a.next(e))}catch(l){r(l)}},s=e=>{try{n(a.throw(e))}catch(l){r(l)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,s);n((a=a.apply(e,l)).next())}));var e,l,a},w=e=>{s.value=e,r("update:modelValue",e);const l=x(e);r("change",e,l)},x=e=>{const l=[];let a=o.value;for(let t=0;t<e.length;t++){const r=e[t],o=a.find((e=>e.value===r));o&&(l.push(o),o.children&&t<e.length-1&&(a=o.children))}return l},O=e=>{r("expand-change",e)},A=e=>{r("blur",e)},P=e=>{r("focus",e)},B=e=>{r("visible-change",e)},C=e=>{r("remove-tag",e)};return l({getSelectedText:()=>x(s.value).map((e=>e.label)).join(t.separator),getSelectedCodes:()=>[...s.value],clearSelection:()=>{s.value=[],r("update:modelValue",[]),r("change",[],[])},setSelectedValues:e=>{if(!e||0===e.length)return s.value=[],void r("update:modelValue",[]);s.value=[...e],r("update:modelValue",e);const l=x(e);r("change",e,l)},setSelectedValuesByNames:(e,l,a)=>{const t=[],n=o.value.find((l=>l.label===e||l.label.includes(e)||e.includes(l.label.replace(/(省|市|自治区|特别行政区)$/,""))));if(n&&(t.push(n.value),l&&n.children)){const e=n.children.find((e=>e.label===l||e.label.includes(l)||l.includes(e.label.replace(/(市|区|县|地区|自治州|盟)$/,""))));if(e&&(t.push(e.value),a&&e.children)){const l=e.children.find((e=>e.label===a||e.label.includes(a)||a.includes(e.label.replace(/(区|县|市|旗|自治县)$/,""))));l&&t.push(l.value)}}if(t.length>0){s.value=t,r("update:modelValue",t);const e=x(t);r("change",t,e)}},selectedValues:s}),h((()=>{j()})),(e,l)=>{const a=g;return b(),f("div",y,[m(a,{modelValue:s.value,"onUpdate:modelValue":l[0]||(l[0]=e=>s.value=e),options:V.value,props:u,placeholder:e.placeholder,size:e.size,disabled:e.disabled,clearable:e.clearable,"show-all-levels":e.showAllLevels,"collapse-tags":e.collapseTags,separator:e.separator,filterable:e.filterable,onChange:w,onExpandChange:O,onBlur:A,onFocus:P,onVisibleChange:B,onRemoveTag:C},null,8,["modelValue","options","placeholder","size","disabled","clearable","show-all-levels","collapse-tags","separator","filterable"])])}}}),[["__scopeId","data-v-a60c89f0"]]);export{V as A};
