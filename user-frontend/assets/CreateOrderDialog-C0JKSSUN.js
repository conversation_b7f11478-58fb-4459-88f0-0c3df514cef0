var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,i=(l,a,r)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):l[a]=r,d=(e,l,a)=>new Promise(((r,t)=>{var s=e=>{try{d(a.next(e))}catch(l){t(l)}},i=e=>{try{d(a.throw(e))}catch(l){t(l)}},d=e=>e.done?r(e.value):Promise.resolve(e.value).then(s,i);d((a=a.apply(e,l)).next())}));import{_ as n}from"./index-F0f2Vd-3.js";/* empty css                       *//* empty css                 *//* empty css                 *//* empty css                   *//* empty css               */import{d as o,r as u,c,o as v,e as p,a as m,b as h,w as _,K as g,x as f,H as b,a2 as y,a4 as w,aV as V,bc as x,D as k,G as N,C as M,y as A,u as j,a3 as q,bt as C,bu as $,aS as U,am as F,aN as O,aQ as P,M as R,j as z,X as E,p as I,N as S,Z as B,_ as D,bv as K,bw as T,br as G,bs as H,aq as Q,ar as X,as as Y,b8 as Z,F as J,A as L,b9 as W,Y as ee}from"./vendor-BVh5F9vp.js";import{E as le}from"./expressApi-BL8dLu27.js";import{A as ae}from"./AddressCascader-CQSS5hj1.js";import"./el-collapse-transition-l0sNRNKZ.js";import{a as re}from"./address-IlaM-Wag.js";const te={class:"address-parse-input"},se={class:"parse-input-section"},ie={class:"input-header"},de={class:"example-section"},ne={class:"action-buttons"},oe={key:0,class:"parse-result-preview"},ue={class:"result-header"},ce={class:"result-actions"},ve={class:"result-content"},pe={class:"result-item"},me={class:"item-value"},he={class:"result-item"},_e={class:"item-value"},ge={class:"result-item"},fe={class:"item-value"},be={class:"result-item"},ye={class:"item-value"},we={class:"result-item"},Ve={class:"item-value"},xe=n(o({__name:"AddressParseInput",props:{placeholder:{default:"请输入包含姓名、电话、地址的文本..."},disabled:{type:Boolean,default:!1}},emits:["parsed","apply"],setup(e,{expose:l,emit:a}){const r=a,t=u(""),s=u(!1),i=u(!1),n=u(null),o=["李健\n17099916606\n湖南省永州市冷水滩区珊瑚街道 园丁山庄8栋19号门面妈妈驿站","张三\n13800138000\n北京市海淀区西二旗 中关村软件园 腾讯大厦B座","王五\n15988776655\n上海市浦东新区陆家嘴 金茂大厦20层","刘六\n18922334455\n广东省深圳市南山区科技园 腾讯大厦"],z=()=>{if(!n.value)return"";const e=[];return n.value.provinceName&&e.push(n.value.provinceName),n.value.cityName&&e.push(n.value.cityName),n.value.districtName&&e.push(n.value.districtName),n.value.townName&&e.push(n.value.townName),e.join("")},E=()=>d(this,null,(function*(){var e;if(t.value.trim()){s.value=!0;try{const l=yield re.parseAddress({text:t.value.trim(),lat:30,lng:110});if(!l.success||!(null==(e=l.data)?void 0:e.addressInfo))throw new Error(l.message||"解析失败");n.value=l.data.addressInfo;I(n.value).isValid?(r("parsed",n.value),setTimeout((()=>{r("apply",n.value),R.success("地址信息已自动填充到表单")}),500)):(r("parsed",n.value),setTimeout((()=>{r("apply",n.value),R.warning("地址信息已部分填充，请检查并补充缺失信息")}),500))}catch(l){n.value=null,R.error("地址解析失败: "+(l.message||"请检查输入格式或网络连接"))}finally{s.value=!1}}else R.warning("请输入地址文本")})),I=e=>{if(!e)return{isValid:!1,message:"解析结果为空"};const l=[];return e.name&&"未识别"!==e.name||l.push("姓名"),e.mobile||e.phone||l.push("联系电话"),e.provinceName&&"未识别省份"!==e.provinceName||l.push("省份"),e.cityName&&"未识别城市"!==e.cityName||l.push("城市"),e.districtName&&"未识别区县"!==e.districtName||l.push("区县"),e.detailAddress&&"未识别详细地址"!==e.detailAddress||l.push("详细地址"),0===l.length?{isValid:!0,message:"解析完整"}:{isValid:!1,message:`缺少${l.join("、")}信息`}},S=()=>{n.value?(r("apply",n.value),R.success("解析结果已应用到表单")):R.warning("没有可应用的解析结果")},B=()=>{t.value="",D()},D=()=>{n.value=null},K=()=>{const e=o[Math.floor(Math.random()*o.length)];t.value=e,R.success("已加载示例数据")};return l({parse:E,clear:B,clearResult:D,loadExample:K}),(e,l)=>{const a=b,r=V,d=x,o=M,u=A,R=O,I=P;return v(),c("div",te,[p("div",se,[p("div",ie,[l[2]||(l[2]=p("span",{class:"input-label"},"🧠 智能地址解析",-1)),h(a,{type:"link",size:"small",onClick:l[0]||(l[0]=e=>i.value=!i.value),class:"example-btn"},{default:_((()=>[g(f(i.value?"隐藏":"查看")+"示例 ",1)])),_:1})]),h(d,null,{default:_((()=>[y(p("div",de,[h(r,{title:"输入示例",type:"info",closable:!1,"show-icon":""},{default:_((()=>l[3]||(l[3]=[p("div",{class:"example-content"},[p("p",null,[p("strong",null,"格式1："),g("李健"),p("br"),g("17099916606"),p("br"),g("湖南省永州市冷水滩区珊瑚街道 园丁山庄8栋19号")]),p("p",null,[p("strong",null,"格式2："),g("张三 13800138000 北京市海淀区西二旗 中关村软件园")]),p("p",null,[p("strong",null,"格式3："),g("王五，15988776655，上海市浦东新区陆家嘴金茂大厦20层")])],-1)]))),_:1})],512),[[w,i.value]])])),_:1}),h(o,{modelValue:t.value,"onUpdate:modelValue":l[1]||(l[1]=e=>t.value=e),type:"textarea",rows:4,placeholder:"请输入包含姓名、电话、地址的文本，支持多种格式...",disabled:s.value,class:"parse-textarea",onKeydown:k(N(E,["ctrl"]),["enter"])},null,8,["modelValue","disabled","onKeydown"]),p("div",ne,[h(a,{type:"primary",onClick:E,loading:s.value,disabled:!t.value.trim(),size:"small"},{default:_((()=>[h(u,null,{default:_((()=>[h(j(q))])),_:1}),l[4]||(l[4]=g(" 智能解析 "))])),_:1},8,["loading","disabled"]),h(a,{onClick:B,disabled:s.value||!t.value,size:"small"},{default:_((()=>[h(u,null,{default:_((()=>[h(j(C))])),_:1}),l[5]||(l[5]=g(" 清空 "))])),_:1},8,["disabled"]),h(a,{type:"success",onClick:K,disabled:s.value,size:"small"},{default:_((()=>[h(u,null,{default:_((()=>[h(j($))])),_:1}),l[6]||(l[6]=g(" 加载示例 "))])),_:1},8,["disabled"])])]),n.value?(v(),c("div",oe,[p("div",ue,[l[9]||(l[9]=p("span",{class:"result-title"},"📋 解析结果预览",-1)),p("div",ce,[h(a,{type:"primary",size:"small",onClick:S,disabled:!n.value},{default:_((()=>[h(u,null,{default:_((()=>[h(j(U))])),_:1}),l[7]||(l[7]=g(" 应用到表单 "))])),_:1},8,["disabled"]),h(a,{type:"danger",size:"small",onClick:D},{default:_((()=>[h(u,null,{default:_((()=>[h(j(F))])),_:1}),l[8]||(l[8]=g(" 清除结果 "))])),_:1})])]),p("div",ve,[h(I,{gutter:10},{default:_((()=>[h(R,{span:8},{default:_((()=>[p("div",pe,[l[10]||(l[10]=p("span",{class:"item-label"},"👤 姓名：",-1)),p("span",me,f(n.value.name||"未识别"),1)])])),_:1}),h(R,{span:8},{default:_((()=>[p("div",he,[l[11]||(l[11]=p("span",{class:"item-label"},"📱 手机：",-1)),p("span",_e,f(n.value.mobile||"未识别"),1)])])),_:1}),h(R,{span:8},{default:_((()=>[p("div",ge,[l[12]||(l[12]=p("span",{class:"item-label"},"📞 电话：",-1)),p("span",fe,f(n.value.phone||"未识别"),1)])])),_:1})])),_:1}),h(I,{gutter:10,style:{"margin-top":"10px"}},{default:_((()=>[h(R,{span:24},{default:_((()=>[p("div",be,[l[13]||(l[13]=p("span",{class:"item-label"},"📍 地址：",-1)),p("span",ye,f(z()),1)])])),_:1})])),_:1}),h(I,{gutter:10,style:{"margin-top":"10px"}},{default:_((()=>[h(R,{span:24},{default:_((()=>[p("div",we,[l[14]||(l[14]=p("span",{class:"item-label"},"🏠 详细：",-1)),p("span",Ve,f(n.value.detailAddress||"未识别"),1)])])),_:1})])),_:1})])])):m("",!0)])}}}),[["__scopeId","data-v-ce98747c"]]),ke={class:"section-card"},Ne={class:"section-header"},Me={class:"section-card"},Ae={key:1,class:"volume-weight-info"},je={key:2,class:"price-list"},qe={class:"price-content"},Ce={class:"express-info"},$e={class:"express-name"},Ue={class:"product-type"},Fe={class:"calc-info"},Oe={class:"price-info"},Pe={class:"price"},Re={class:"delivery-time"},ze={class:"expires-info"},Ee={class:"dialog-footer"},Ie=n(o({__name:"CreateOrderDialog",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(e,{emit:n}){const o=e,y=n,w=u(),x=u(),k=u(),N=u(),A=u(),q=u(),C=u(),$=u(!1),U=u(!1),F=u([]),re=u(""),te=u({show:!1,title:"",description:"",type:"info"}),se=z((()=>oe.length&&oe.width&&oe.height)),ie=z((()=>{if(!oe.length||!oe.width||!oe.height)return"";return`${(oe.length*oe.width*oe.height).toFixed(2)}cm³`})),de=z((()=>{if(!oe.length||!oe.width||!oe.height)return"";return`${(oe.length*oe.width*oe.height/8e3).toFixed(2)}kg`})),ne=z((()=>{if(!oe.length||!oe.width||!oe.height)return"";const e=oe.length*oe.width*oe.height/8e3;return`${Math.max(oe.weight||0,e).toFixed(2)}kg`})),oe=E({sender_name:"",sender_mobile:"",sender_province:"",sender_city:"",sender_district:"",sender_address:"",receiver_name:"",receiver_mobile:"",receiver_province:"",receiver_city:"",receiver_district:"",receiver_address:"",weight:1,volume:void 0,length:void 0,width:void 0,height:void 0,quantity:1,goods_name:"物品",pay_method:0,remark:"",senderAddress:[],receiverAddress:[]}),ue={sender_name:[{required:!0,message:"请输入寄件人姓名",trigger:"blur"}],sender_mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],senderAddress:[{required:!0,message:"请选择寄件地址",trigger:"change",validator:(e,l,a)=>{l&&0!==l.length?a():a(new Error("请选择寄件地址"))}}],sender_address:[{required:!0,message:"请输入详细地址",trigger:"blur"}]},ce={receiver_name:[{required:!0,message:"请输入收件人姓名",trigger:"blur"}],receiver_mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],receiverAddress:[{required:!0,message:"请选择收件地址",trigger:"change",validator:(e,l,a)=>{l&&0!==l.length?a():a(new Error("请选择收件地址"))}}],receiver_address:[{required:!0,message:"请输入详细地址",trigger:"blur"}]},ve={weight:[{required:!0,message:"请输入重量",trigger:"blur"}],goods_name:[{required:!0,message:"请输入物品名称",trigger:"blur"}]},pe=z({get:()=>o.visible,set:e=>y("update:visible",e)}),me=()=>{var e;const{randomName:l,randomMobile:a,randomAddress:r,randomDetailAddress:t}=(()=>{const e=["张","王","李","赵","刘","陈","杨","黄","周","吴","徐","孙","马","朱","胡","林","郭","何","高","罗"],l=["伟","芳","娜","敏","静","丽","强","磊","军","洋","勇","艳","杰","娟","涛","明","超","秀英","霞","平","刚","桂英"],a=[{value:"110000",label:"北京市",cities:[{value:"110100",label:"北京市",districts:[{value:"110101",label:"东城区"},{value:"110102",label:"西城区"},{value:"110105",label:"朝阳区"},{value:"110106",label:"丰台区"}]}]},{value:"120000",label:"天津市",cities:[{value:"120100",label:"天津市",districts:[{value:"120101",label:"和平区"},{value:"120102",label:"河东区"},{value:"120103",label:"河西区"},{value:"120104",label:"南开区"}]}]},{value:"440000",label:"广东省",cities:[{value:"440100",label:"广州市",districts:[{value:"440103",label:"荔湾区"},{value:"440104",label:"越秀区"},{value:"440105",label:"海珠区"},{value:"440106",label:"天河区"}]},{value:"440300",label:"深圳市",districts:[{value:"440303",label:"罗湖区"},{value:"440304",label:"福田区"},{value:"440305",label:"南山区"},{value:"440306",label:"宝安区"}]}]},{value:"310000",label:"上海市",cities:[{value:"310100",label:"上海市",districts:[{value:"310101",label:"黄浦区"},{value:"310104",label:"徐汇区"},{value:"310105",label:"长宁区"},{value:"310106",label:"静安区"}]}]}],r=["中山路","解放路","人民路","建设路","新华路","光明路","胜利路","东风路","西湖路","南京路"],t=["大厦","广场","中心","花园","小区","公寓","写字楼","商务中心","科技园","工业园"];return{randomName:()=>e[Math.floor(Math.random()*e.length)]+l[Math.floor(Math.random()*l.length)],randomMobile:()=>{const e=["130","131","132","133","134","135","136","137","138","139","150","151","152","153","155","156","157","158","159","180","181","182","183","184","185","186","187","188","189"];return e[Math.floor(Math.random()*e.length)]+Math.floor(1e8*Math.random()).toString().padStart(8,"0")},randomAddress:()=>{const e=a[Math.floor(Math.random()*a.length)];if(!e.cities||0===e.cities.length)return{address:[e.value],province:e.label,city:"",district:""};const l=e.cities[Math.floor(Math.random()*e.cities.length)];if(!l.districts||0===l.districts.length)return{address:[e.value,l.value],province:e.label,city:l.label,district:""};const r=l.districts[Math.floor(Math.random()*l.districts.length)];return{address:[e.value,l.value,r.value],province:e.label,city:l.label,district:r.label}},randomDetailAddress:()=>`${r[Math.floor(Math.random()*r.length)]}${Math.floor(999*Math.random())+1}号${t[Math.floor(Math.random()*t.length)]}${Math.floor(20*Math.random())+1}单元${Math.floor(50*Math.random())+101}室`}})(),s=r();oe.sender_name=l(),oe.sender_mobile=a(),oe.senderAddress=s.address,oe.sender_province=s.province,oe.sender_city=s.city,oe.sender_district=s.district,oe.sender_address=t();const i=r();oe.receiver_name=l(),oe.receiver_mobile=a(),oe.receiverAddress=i.address,oe.receiver_province=i.province,oe.receiver_city=i.city,oe.receiver_district=i.district,oe.receiver_address=t(),oe.weight=Math.round(10*(4*Math.random()+.5))/10,oe.quantity=1;const d=[{length:20,width:15,height:10},{length:30,width:25,height:20},{length:50,width:40,height:30},{length:60,width:50,height:40}],n=d[Math.floor(Math.random()*d.length)];oe.length=n.length,oe.width=n.width,oe.height=n.height;const o=["文件","衣服","书籍","电子产品","食品","礼品","日用品","化妆品","玩具","工艺品"];oe.goods_name=o[Math.floor(Math.random()*o.length)],he(),null==(e=w.value)||e.clearValidate(),R.success("已随机填充测试数据")},he=()=>{if(oe.length&&oe.width&&oe.height){const e=oe.length*oe.width*oe.height,l=e/1e6;oe.volume=Math.round(1e6*l)/1e6;const a=e/8e3,r=oe.weight||0;te.value=a>r?{show:!0,title:"体积重量计费",description:`包裹体积较大，将按体积重量 ${a.toFixed(2)}kg 计费（实际重量 ${r.toFixed(2)}kg）`,type:"warning"}:{show:!0,title:"实际重量计费",description:`包裹体积适中，将按实际重量 ${r.toFixed(2)}kg 计费（体积重量 ${a.toFixed(2)}kg）`,type:"info"}}else te.value.show=!1,oe.volume=void 0},_e=e=>{var l;if(e.name&&(oe.sender_name=e.name),e.mobile&&(oe.sender_mobile=e.mobile),e.detailAddress&&(oe.sender_address=e.detailAddress),e.provinceName&&(oe.sender_province=e.provinceName),e.cityName&&(oe.sender_city=e.cityName),e.districtName&&(oe.sender_district=e.districtName),e.provinceName&&q.value)try{q.value.setSelectedValuesByNames(e.provinceName,e.cityName,e.districtName)}catch(a){}null==(l=x.value)||l.clearValidate(),R.success("寄件人信息已自动填充")},ge=e=>{var l;if(e.name&&(oe.receiver_name=e.name),e.mobile&&(oe.receiver_mobile=e.mobile),e.detailAddress&&(oe.receiver_address=e.detailAddress),e.provinceName&&(oe.receiver_province=e.provinceName),e.cityName&&(oe.receiver_city=e.cityName),e.districtName&&(oe.receiver_district=e.districtName),e.provinceName&&C.value)try{C.value.setSelectedValuesByNames(e.provinceName,e.cityName,e.districtName)}catch(a){}null==(l=k.value)||l.clearValidate(),R.success("收件人信息已自动填充")},fe=(e,l)=>{var a,r,t,s;l.length>=1&&(oe.sender_province=(null==(a=l[0])?void 0:a.label)||""),l.length>=2&&(oe.sender_city=(null==(r=l[1])?void 0:r.label)||""),l.length>=3&&(oe.sender_district=(null==(t=l[2])?void 0:t.label)||""),null==(s=x.value)||s.validateField("senderAddress")},be=(e,l)=>{var a,r,t,s;l.length>=1&&(oe.receiver_province=(null==(a=l[0])?void 0:a.label)||""),l.length>=2&&(oe.receiver_city=(null==(r=l[1])?void 0:r.label)||""),l.length>=3&&(oe.receiver_district=(null==(t=l[2])?void 0:t.label)||""),null==(s=k.value)||s.validateField("receiverAddress")},ye=()=>d(this,null,(function*(){if(oe.sender_province&&oe.sender_city&&oe.receiver_province&&oe.receiver_city&&oe.weight){U.value=!0;try{const e={query_all:!0,sender:{province:oe.sender_province,city:oe.sender_city,district:oe.sender_district||""},receiver:{province:oe.receiver_province,city:oe.receiver_city,district:oe.receiver_district||""},package:{weight:oe.weight,volume:oe.volume,length:oe.length,width:oe.width,height:oe.height},pay_method:oe.pay_method||0},l=yield le.queryPrice(e);l.success&&l.data?(F.value=l.data,re.value="",0===l.data.length?R.warning("未找到符合条件的快递服务"):R.success(`查询成功，找到 ${l.data.length} 个报价`)):(R.error(l.message||"查询失败"),F.value=[])}catch(e){R.error("查询价格失败，请检查网络连接"),F.value=[]}finally{U.value=!1}}else R.warning("请先填写完整的寄收件地址和重量信息")})),we=()=>d(this,null,(function*(){try{const n=[];if(x.value&&n.push(x.value.validate()),k.value&&n.push(k.value.validate()),w.value&&n.push(w.value.validate()),yield Promise.all(n),!re.value)return void R.warning("请先查询价格并选择快递公司");$.value=!0;const o=(e=((e,l)=>{for(var a in l||(l={}))t.call(l,a)&&i(e,a,l[a]);if(r)for(var a of r(l))s.call(l,a)&&i(e,a,l[a]);return e})({},oe),d={order_code:re.value,length:oe.length,width:oe.width,height:oe.height},l(e,a(d))),u=yield le.createOrder(o);if(u.success){const e=u.data||u,l=e.order_no||"未知",a=e.waybill_no||e.tracking_no||"未知";R.success(`订单创建成功！订单号：${l}，运单号：${a}`),y("success"),Ve()}else R.error(u.message||"创建订单失败")}catch(n){R.error("创建订单失败，请检查网络连接")}finally{$.value=!1}var e,d})),Ve=()=>{var e,l,a,r,t;null==(e=w.value)||e.resetFields(),null==(l=x.value)||l.resetFields(),null==(a=k.value)||a.resetFields(),null==(r=N.value)||r.clearResult(),null==(t=A.value)||t.clearResult(),Object.assign(oe,{sender_name:"",sender_mobile:"",sender_province:"",sender_city:"",sender_district:"",sender_address:"",receiver_name:"",receiver_mobile:"",receiver_province:"",receiver_city:"",receiver_district:"",receiver_address:"",weight:1,volume:void 0,length:void 0,width:void 0,height:void 0,quantity:1,goods_name:"物品",pay_method:0,remark:"",senderAddress:[],receiverAddress:[]}),F.value=[],re.value="",te.value.show=!1,y("update:visible",!1)};return I(pe,(e=>{e||Ve()})),(e,l)=>{const a=b,r=M,t=D,s=B,i=O,d=P,n=H,o=Q,u=V,y=Y,R=X,z=W,E=Z,I=ee;return v(),S(I,{modelValue:pe.value,"onUpdate:modelValue":l[17]||(l[17]=e=>pe.value=e),title:"创建快递订单",width:"1200px","before-close":Ve,class:"create-order-dialog"},{footer:_((()=>[p("span",Ee,[h(a,{onClick:Ve},{default:_((()=>l[24]||(l[24]=[g("取消")]))),_:1}),h(a,{type:"primary",onClick:we,loading:$.value},{default:_((()=>l[25]||(l[25]=[g(" 创建订单 ")]))),_:1},8,["loading"])])])),default:_((()=>[h(d,{gutter:24,class:"order-form-container"},{default:_((()=>[h(i,{span:12,class:"sender-section"},{default:_((()=>[p("div",ke,[p("div",Ne,[l[19]||(l[19]=p("h3",{class:"section-title"},"📦 寄件人信息",-1)),h(a,{type:"primary",size:"small",onClick:me,class:"random-btn"},{default:_((()=>l[18]||(l[18]=[g(" 随机填充测试数据 ")]))),_:1})]),h(xe,{placeholder:"请输入寄件人姓名、电话、地址信息...",onApply:_e,ref_key:"senderParseRef",ref:N},null,512),h(s,{ref_key:"senderFormRef",ref:x,model:oe,rules:ue,"label-width":"80px",class:"sender-form"},{default:_((()=>[h(t,{label:"姓名",prop:"sender_name"},{default:_((()=>[h(r,{modelValue:oe.sender_name,"onUpdate:modelValue":l[0]||(l[0]=e=>oe.sender_name=e),placeholder:"请输入寄件人姓名","prefix-icon":j(K)},null,8,["modelValue","prefix-icon"])])),_:1}),h(t,{label:"手机号",prop:"sender_mobile"},{default:_((()=>[h(r,{modelValue:oe.sender_mobile,"onUpdate:modelValue":l[1]||(l[1]=e=>oe.sender_mobile=e),placeholder:"请输入手机号","prefix-icon":j(T)},null,8,["modelValue","prefix-icon"])])),_:1}),h(t,{label:"寄件地址",prop:"senderAddress"},{default:_((()=>[h(ae,{modelValue:oe.senderAddress,"onUpdate:modelValue":l[2]||(l[2]=e=>oe.senderAddress=e),placeholder:"请选择寄件地址",onChange:fe,ref_key:"senderCascaderRef",ref:q},null,8,["modelValue"])])),_:1}),h(t,{label:"详细地址",prop:"sender_address"},{default:_((()=>[h(r,{modelValue:oe.sender_address,"onUpdate:modelValue":l[3]||(l[3]=e=>oe.sender_address=e),placeholder:"请输入详细地址","prefix-icon":j(G)},null,8,["modelValue","prefix-icon"])])),_:1})])),_:1},8,["model"])])])),_:1}),h(i,{span:12,class:"receiver-section"},{default:_((()=>[p("div",Me,[l[20]||(l[20]=p("div",{class:"section-header"},[p("h3",{class:"section-title"},"📮 收件人信息")],-1)),h(xe,{placeholder:"请输入收件人姓名、电话、地址信息...",onApply:ge,ref_key:"receiverParseRef",ref:A},null,512),h(s,{ref_key:"receiverFormRef",ref:k,model:oe,rules:ce,"label-width":"80px",class:"receiver-form"},{default:_((()=>[h(t,{label:"姓名",prop:"receiver_name"},{default:_((()=>[h(r,{modelValue:oe.receiver_name,"onUpdate:modelValue":l[4]||(l[4]=e=>oe.receiver_name=e),placeholder:"请输入收件人姓名","prefix-icon":j(K)},null,8,["modelValue","prefix-icon"])])),_:1}),h(t,{label:"手机号",prop:"receiver_mobile"},{default:_((()=>[h(r,{modelValue:oe.receiver_mobile,"onUpdate:modelValue":l[5]||(l[5]=e=>oe.receiver_mobile=e),placeholder:"请输入手机号","prefix-icon":j(T)},null,8,["modelValue","prefix-icon"])])),_:1}),h(t,{label:"收件地址",prop:"receiverAddress"},{default:_((()=>[h(ae,{modelValue:oe.receiverAddress,"onUpdate:modelValue":l[6]||(l[6]=e=>oe.receiverAddress=e),placeholder:"请选择收件地址",onChange:be,ref_key:"receiverCascaderRef",ref:C},null,8,["modelValue"])])),_:1}),h(t,{label:"详细地址",prop:"receiver_address"},{default:_((()=>[h(r,{modelValue:oe.receiver_address,"onUpdate:modelValue":l[7]||(l[7]=e=>oe.receiver_address=e),placeholder:"请输入详细地址","prefix-icon":j(G)},null,8,["modelValue","prefix-icon"])])),_:1})])),_:1},8,["model"])])])),_:1})])),_:1}),h(s,{ref_key:"formRef",ref:w,model:oe,rules:ve,"label-width":"100px",class:"package-form"},{default:_((()=>[h(n,{"content-position":"left"},{default:_((()=>l[21]||(l[21]=[g("包裹信息")]))),_:1}),h(d,{gutter:20},{default:_((()=>[h(i,{span:12},{default:_((()=>[h(t,{label:"重量(kg)",prop:"weight"},{default:_((()=>[h(o,{modelValue:oe.weight,"onUpdate:modelValue":l[8]||(l[8]=e=>oe.weight=e),min:.1,step:.1,precision:1,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),h(i,{span:12},{default:_((()=>[h(t,{label:"数量",prop:"quantity"},{default:_((()=>[h(o,{modelValue:oe.quantity,"onUpdate:modelValue":l[9]||(l[9]=e=>oe.quantity=e),min:1,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),h(d,{gutter:20},{default:_((()=>[h(i,{span:8},{default:_((()=>[h(t,{label:"长度(cm)"},{default:_((()=>[h(o,{modelValue:oe.length,"onUpdate:modelValue":l[10]||(l[10]=e=>oe.length=e),min:.1,step:.1,precision:1,style:{width:"100%"},placeholder:"可选",onChange:he},null,8,["modelValue"])])),_:1})])),_:1}),h(i,{span:8},{default:_((()=>[h(t,{label:"宽度(cm)"},{default:_((()=>[h(o,{modelValue:oe.width,"onUpdate:modelValue":l[11]||(l[11]=e=>oe.width=e),min:.1,step:.1,precision:1,style:{width:"100%"},placeholder:"可选",onChange:he},null,8,["modelValue"])])),_:1})])),_:1}),h(i,{span:8},{default:_((()=>[h(t,{label:"高度(cm)"},{default:_((()=>[h(o,{modelValue:oe.height,"onUpdate:modelValue":l[12]||(l[12]=e=>oe.height=e),min:.1,step:.1,precision:1,style:{width:"100%"},placeholder:"可选",onChange:he},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),se.value?(v(),S(d,{key:0,gutter:20,class:"calculated-info"},{default:_((()=>[h(i,{span:8},{default:_((()=>[h(t,{label:"下单体积"},{default:_((()=>[h(r,{value:ie.value,readonly:"",style:{width:"100%"},placeholder:"由长宽高自动计算"},null,8,["value"])])),_:1})])),_:1}),h(i,{span:8},{default:_((()=>[h(t,{label:"体积重量"},{default:_((()=>[h(r,{value:de.value,readonly:"",style:{width:"100%"},placeholder:"自动计算"},null,8,["value"])])),_:1})])),_:1}),h(i,{span:8},{default:_((()=>[h(t,{label:"计费重量"},{default:_((()=>[h(r,{value:ne.value,readonly:"",style:{width:"100%"},placeholder:"自动计算"},null,8,["value"])])),_:1})])),_:1})])),_:1})):m("",!0),te.value.show?(v(),c("div",Ae,[h(u,{title:te.value.title,description:te.value.description,type:te.value.type,"show-icon":"",closable:!1},null,8,["title","description","type"])])):m("",!0),h(d,{gutter:20},{default:_((()=>[h(i,{span:12},{default:_((()=>[h(t,{label:"物品名称",prop:"goods_name"},{default:_((()=>[h(r,{modelValue:oe.goods_name,"onUpdate:modelValue":l[13]||(l[13]=e=>oe.goods_name=e),placeholder:"请输入物品名称"},null,8,["modelValue"])])),_:1})])),_:1}),h(i,{span:12},{default:_((()=>[h(t,{label:"支付方式",prop:"pay_method"},{default:_((()=>[h(R,{modelValue:oe.pay_method,"onUpdate:modelValue":l[14]||(l[14]=e=>oe.pay_method=e),placeholder:"请选择支付方式",style:{width:"100%"}},{default:_((()=>[h(y,{label:"寄付",value:0}),h(y,{label:"到付",value:1}),h(y,{label:"月结",value:2})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),h(t,{label:"备注"},{default:_((()=>[h(r,{modelValue:oe.remark,"onUpdate:modelValue":l[15]||(l[15]=e=>oe.remark=e),type:"textarea",placeholder:"请输入备注信息"},null,8,["modelValue"])])),_:1}),h(n,{"content-position":"left"},{default:_((()=>l[22]||(l[22]=[g("选择快递公司")]))),_:1}),h(a,{type:"primary",onClick:ye,loading:U.value},{default:_((()=>l[23]||(l[23]=[g("查询价格")]))),_:1},8,["loading"]),F.value.length>0?(v(),c("div",je,[h(E,{modelValue:re.value,"onUpdate:modelValue":l[16]||(l[16]=e=>re.value=e)},{default:_((()=>[(v(!0),c(J,null,L(F.value,((e,l)=>(v(),c("div",{key:`${e.express_code}-${e.product_code||e.product_name||l}`,class:"price-item"},[h(z,{value:e.order_code,class:"price-radio"},{default:_((()=>[p("div",qe,[p("div",Ce,[p("span",$e,f(e.express_name),1),p("span",Ue,f(e.product_name),1),p("div",Fe," 计费重量: "+f(e.calc_weight)+"kg | 续重: ¥"+f(e.continued_weight_per_kg)+"/kg ",1)]),p("div",Oe,[p("span",Pe,"¥"+f(e.price.toFixed(2)),1),p("span",Re,f(e.delivery_time),1),p("span",ze,"有效期: "+f(e.valid_minutes)+"分钟",1)])])])),_:2},1032,["value"])])))),128))])),_:1},8,["modelValue"])])):m("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-c7012255"]]);export{Ie as default};
