import{H as a,_ as s}from"./index-F0f2Vd-3.js";import{d as t,c as e,e as r,b as o,x as c,w as d,H as n,K as p,a1 as i,o as l}from"./vendor-BVh5F9vp.js";const u={class:"page-content state-page"},m={class:"tips"},_=["src"],g={class:"right-wrap"},v=s(t({__name:"Exception",props:{data:{type:Object,required:!0}},setup(s){const t=i(),v=()=>{t.push(a)};return(a,t)=>{const i=n;return l(),e("div",u,[r("div",m,[r("img",{src:s.data.imgUrl},null,8,_),r("div",g,[r("p",null,c(s.data.desc),1),o(i,{color:"#47A7FF",onClick:v},{default:d((()=>[p(c(s.data.btnText),1)])),_:1})])])])}}}),[["__scopeId","data-v-77685b32"]]);export{v as _};
