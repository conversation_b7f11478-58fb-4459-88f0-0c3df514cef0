import{f as e}from"./index-F0f2Vd-3.js";const s={parseAddress:s=>e.post({url:"/api/v1/address/parse",params:{text:s.text,addressType:s.addressType||0,queryType:s.queryType||0,lat:s.lat||30,lng:s.lng||110}}),batchParseAddress:s=>e.post({url:"/api/v1/address/batch-parse",params:{addresses:s.addresses.map((e=>({text:e.text,addressType:e.addressType||0,queryType:e.queryType||0,lat:e.lat||30,lng:e.lng||110}))),concurrency:s.concurrency||5}}),validateAddress:s=>e.post({url:"/api/v1/address/validate",params:{provinceCode:s.provinceCode,cityCode:s.cityCode,districtCode:s.districtCode,detailAddress:s.detailAddress}}),getAreaCascader:()=>e.get({url:"/api/v1/address/area-cascader"}),searchAreas:s=>e.get({url:"/api/v1/address/search-areas",params:{keyword:s.keyword,limit:s.limit||10}}),getParseHistory:s=>e.get({url:"/api/v1/address/history",params:{page:s.page||1,size:s.size||20}})};export{s as a};
