import{e as a,_ as s}from"./index-F0f2Vd-3.js";import{L as e}from"./LeftView-CLKZv8wr.js";import{d as t,r as l,c as r,e as i,b as o,x as n,u as d,a as c,m as p,C as u,w as m,H as g,K as h,a1 as f,o as v}from"./vendor-BVh5F9vp.js";const w={class:"login register"},x={class:"left-wrap"},y={class:"right-wrap"},b={class:"header"},P={class:"login-wrap"},_={class:"form"},k={class:"title"},$={class:"sub-title"},z={class:"input-wrap"},V={key:0,class:"input-label"},j={style:{"margin-top":"15px"}},C={style:{"margin-top":"15px"}},T=s(t({__name:"index",setup(s){const t=f(),T=l(!1),B=a.name,L=l(""),H=l(!1),I=()=>{return a=this,s=null,e=function*(){},new Promise(((t,l)=>{var r=a=>{try{o(e.next(a))}catch(s){l(s)}},i=a=>{try{o(e.throw(a))}catch(s){l(s)}},o=a=>a.done?t(a.value):Promise.resolve(a.value).then(r,i);o((e=e.apply(a,s)).next())}));var a,s,e},K=()=>{t.push("/login")};return(a,s)=>{const t=u,l=g;return v(),r("div",w,[i("div",x,[o(e)]),i("div",y,[i("div",b,[s[1]||(s[1]=i("svg",{class:"icon","aria-hidden":"true"},[i("use",{"xlink:href":"#iconsys-zhaopian-copy"})],-1)),i("h1",null,n(d(B)),1)]),i("div",P,[i("div",_,[i("h3",k,n(a.$t("forgetPassword.title")),1),i("p",$,n(a.$t("forgetPassword.subTitle")),1),i("div",z,[d(T)?(v(),r("span",V,"账号")):c("",!0),o(t,{placeholder:a.$t("forgetPassword.placeholder"),size:"large",modelValue:d(L),"onUpdate:modelValue":s[0]||(s[0]=a=>p(L)?L.value=a:null),modelModifiers:{trim:!0}},null,8,["placeholder","modelValue"])]),i("div",j,[o(l,{class:"login-btn",size:"large",type:"primary",onClick:I,loading:d(H)},{default:m((()=>[h(n(a.$t("forgetPassword.submitBtnText")),1)])),_:1},8,["loading"])]),i("div",C,[o(l,{style:{width:"100%",height:"46px"},size:"large",plain:"",onClick:K},{default:m((()=>[h(n(a.$t("forgetPassword.backBtnText")),1)])),_:1})])])])])])}}}),[["__scopeId","data-v-7426f3ab"]]);export{T as default};
