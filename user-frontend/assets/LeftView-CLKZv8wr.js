import{e as s,_ as e}from"./index-F0f2Vd-3.js";import{d as a,c as l,e as t,x as i,u as n,o}from"./vendor-BVh5F9vp.js";const c={class:"left-view"},r={class:"logo"},d={class:"title"},g={class:"text-wrap"},p=e(a({__name:"LeftView",setup(e){const a=s.name;return(s,e)=>(o(),l("div",c,[t("div",r,[e[0]||(e[0]=t("svg",{class:"icon","aria-hidden":"true"},[t("use",{"xlink:href":"#iconsys-zhaopian-copy"})],-1)),t("h1",d,i(n(a)),1)]),e[1]||(e[1]=t("img",{class:"left-bg",src:"/art-design-pro/assets/lf_bg-B6dBX6Tc.png"},null,-1)),e[2]||(e[2]=t("img",{class:"left-img",src:"/art-design-pro/assets/lf_icon2-D7wSVRI-.png"},null,-1)),t("div",g,[t("h1",null,i(s.$t("login.leftView.title")),1),t("p",null,i(s.$t("login.leftView.subTitle")),1)])]))}}),[["__scopeId","data-v-5522c958"]]);export{p as L};
