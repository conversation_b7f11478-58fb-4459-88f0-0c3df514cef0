import"./index-F0f2Vd-3.js";import{W as e}from"./workOrderApi-4juIOmO-.js";import{E as r}from"./errorHandler-Dnd-hi0l.js";import{j as n,r as t}from"./vendor-BVh5F9vp.js";const a=t([]),i=t(!1),o=t(0);function u(){const t=n((()=>u().map((e=>({value:e.unified_type,label:s(e.unified_name),description:l(e.unified_name),originalName:e.unified_name,category:d(e.unified_type),isSupported:!0,priority:p(e.unified_type)}))).sort(((e,r)=>e.priority-r.priority)))),u=()=>{const e=new Map;return a.value.forEach((r=>{e.has(r.unified_type)||e.set(r.unified_type,r)})),Array.from(e.values())},s=e=>({"催取件":"催取件","重量异常":"重量异常","催派送":"催派送","物流停滞":"物流停滞","重新分配快递员":"重新分配快递员","取消订单":"取消订单"}[e]||e),l=e=>({"催取件":"催促快递员上门取件","重量异常":"包裹实际重量与下单重量不符","催派送":"催促快递员派送","物流停滞":"物流信息长时间未更新","重新分配快递员":"申请更换派送快递员","取消订单":"取消快递订单"}[e]||"快递服务相关问题"),d=e=>({1:"取件服务",2:"费用争议",12:"派送服务",16:"物流服务",17:"派送服务",19:"订单管理"}[e]||"其他服务"),p=e=>({2:1,12:2,1:3,16:4,17:5,19:6}[e]||99);n((()=>{const e={};return t.value.forEach((r=>{const n=r.category;e[n]||(e[n]=[]),e[n].push(r)})),e}));return{workOrderTypes:n((()=>a.value)),loading:n((()=>i.value)),userFriendlyTypes:t,fetchWorkOrderTypes:(n,t=!1)=>{return u=this,s=null,l=function*(){const u=Date.now();if(!t&&a.value.length>0&&u-o.value<3e5)return a.value;i.value=!0;try{if(!localStorage.getItem("access_token"))throw new Error("用户未登录，请先登录后再获取工单类型");const r=yield e.getSupportedTypes(n);if(!r.success||!r.data)throw new Error(r.message||"获取工单类型失败");a.value=r.data,o.value=u}catch(s){throw r.handleApiError(s,!0),a.value=[],s}finally{i.value=!1}return a.value},new Promise(((e,r)=>{var n=e=>{try{a(l.next(e))}catch(n){r(n)}},t=e=>{try{a(l.throw(e))}catch(n){r(n)}},a=r=>r.done?e(r.value):Promise.resolve(r.value).then(n,t);a((l=l.apply(u,s)).next())}));var u,s,l},getTypeName:e=>{const r=a.value.find((r=>r.unified_type===e));return r?s(r.unified_name):"未知类型"},getOriginalTypeName:e=>{const r=a.value.find((r=>r.unified_type===e));return r?r.unified_name:"未知类型"},getUserFriendlyTypeName:s,needsWeightInfo:e=>[2].includes(e),needsValueInfo:e=>[5,6].includes(e),needsAmountInfo:e=>[2,4].includes(e),clearCache:()=>{a.value=[],o.value=0}}}export{u};
