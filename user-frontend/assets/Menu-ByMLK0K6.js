var e=(e,l,a)=>new Promise(((t,u)=>{var o=e=>{try{d(a.next(e))}catch(l){u(l)}},n=e=>{try{d(a.throw(e))}catch(l){u(l)}},d=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,n);d((a=a.apply(e,l)).next())}));import{_ as l,q as a,I as t,r as u,o}from"./index-F0f2Vd-3.js";import{I as n}from"./IconSelector-BeBdJTFy.js";/* empty css                       *//* empty css                        */import{d,j as r,c as s,o as i,v as p,a as m,x as c,r as f,X as b,bF as h,b as _,w as v,a2 as g,N as y,H as V,K as k,aQ as x,aH as w,u as C,F as I,A as U,ac as L,e as H,Z as M,_ as N,b8 as j,m as T,bo as A,aN as q,C as E,aq as S,ap as B,Y as F,M as $,g as z,ai as O}from"./vendor-BVh5F9vp.js";/* empty css               */import"./iconfont-DPUoc2h2.js";const P=["innerHTML"],D={key:1},K=l(d({__name:"ButtonTable",props:{text:{},icon:{},type:{},buttonClass:{},useColor:{type:Boolean,default:!0}},emits:["click"],setup(e,{emit:l}){const a=e,t=l,u=[{type:"add",icon:"&#xe602;",color:"bg-primary"},{type:"edit",icon:"&#xe642;",color:"bg-secondary"},{type:"delete",icon:"&#xe783;",color:"bg-error"},{type:"more",icon:"&#xe6df;",color:""}],o=r((()=>{return a.useColor?a.buttonClass||(a.type?(e=a.type,null==(l=u.find((l=>l.type===e)))?void 0:l.color):""):"";var e,l})),n=()=>{t("click")};return(e,l)=>{return i(),s("div",{class:p(["btn-text",`btn-${a.type}`,e.useColor?o.value:""]),onClick:n},[a.type?(i(),s("i",{key:0,class:"iconfont-sys",innerHTML:(t=a.type,null==(d=u.find((e=>e.type===t)))?void 0:d.icon)},null,8,P)):m("",!0),a.text?(i(),s("span",D,c(a.text),1)):m("",!0)],2);var t,d}}}),[["__scopeId","data-v-f461dac0"]]),Q={class:"page-content"},R={style:{margin:"0","text-align":"right"}},X={class:"dialog-footer"},Y=l(d({__name:"Menu",setup(l){const d=r((()=>a().getMenuList)),p=f(!1),P=b({name:"",path:"",label:"",icon:"",isEnable:!0,sort:1,isMenu:!0,keepAlive:!0,isHidden:!0,link:"",isIframe:!1,authName:"",authLabel:"",authIcon:"",authSort:1}),D=f(t.UNICODE),Y=f("menu"),Z=b({name:[{required:!0,message:"请输入菜单名称",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],path:[{required:!0,message:"请输入路由地址",trigger:"blur"}],label:[{required:!0,message:"输入权限标识",trigger:"blur"}],authName:[{required:!0,message:"请输入权限名称",trigger:"blur"}],authLabel:[{required:!0,message:"请输入权限权限标识",trigger:"blur"}]}),G=d.value,J=f(!1),W=f(),ee=r((()=>{const e="menu"===Y.value?"菜单":"权限";return J.value?`编辑${e}`:`新建${e}`})),le=()=>{},ae=()=>e(this,null,(function*(){W.value&&(yield W.value.validate((l=>e(this,null,(function*(){if(l)try{J.value,$.success((J.value?"编辑":"新增")+"成功"),p.value=!1}catch(e){$.error((J.value?"编辑":"新增")+"失败")}})))))})),te=(e,l,a=!1)=>{p.value=!0,Y.value=e,J.value=!1,re.value=a,ue(),l&&(J.value=!0,z((()=>{"menu"===e?(P.name=u(l.meta.title),P.path=l.path,P.label=l.name,P.icon=l.meta.icon,P.sort=l.meta.sort||1,P.isMenu=l.meta.isMenu,P.keepAlive=l.meta.keepAlive,P.isHidden=l.meta.isHidden||!0,P.isEnable=l.meta.isEnable||!0,P.link=l.meta.link,P.isIframe=l.meta.isIframe||!1):(P.authName=l.title,P.authLabel=l.auth_mark,P.authIcon=l.icon||"",P.authSort=l.sort||1)})))},ue=()=>{var e;null==(e=W.value)||e.resetFields(),Object.assign(P,{name:"",path:"",label:"",icon:"",sort:1,isMenu:!0,keepAlive:!0,isHidden:!0,link:"",isIframe:!1,authName:"",authLabel:"",authIcon:"",authSort:1})},oe=()=>e(this,null,(function*(){try{yield O.confirm("确定要删除该菜单吗？删除后无法恢复","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),$.success("删除成功")}catch(e){"cancel"!==e&&$.error("删除失败")}})),ne=()=>e(this,null,(function*(){try{yield O.confirm("确定要删除该权限吗？删除后无法恢复","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),$.success("删除成功")}catch(e){"cancel"!==e&&$.error("删除失败")}})),de=r((()=>!(!J.value||"button"!==Y.value)||(!(!J.value||"menu"!==Y.value)||!(J.value||"menu"!==Y.value||!re.value)))),re=f(!1);return(e,l)=>{const a=V,t=x,d=w,r=L,f=K,b=o,$=A,z=j,O=N,J=E,ue=q,re=n,se=S,ie=B,pe=M,me=F,ce=h("auth");return i(),s("div",Q,[_(t,{gutter:20,style:{"margin-left":"15px"}},{default:v((()=>[g((i(),y(a,{onClick:l[0]||(l[0]=e=>te("menu",null,!0))},{default:v((()=>l[19]||(l[19]=[k("添加菜单")]))),_:1})),[[ce,"add"]])])),_:1}),_(b,{data:C(G)},{default:v((()=>[_(d,{label:"菜单名称"},{default:v((e=>{var l;return[k(c(C(u)(null==(l=e.row.meta)?void 0:l.title)),1)]})),_:1}),_(d,{prop:"path",label:"路由"}),_(d,{prop:"meta.authList",label:"可操作权限"},{default:v((e=>[(i(!0),s(I,null,U(e.row.meta.authList,((e,t)=>(i(),y(r,{placement:"top-start",title:"操作",width:200,trigger:"click",key:t},{reference:v((()=>[_(a,{class:"small-btn"},{default:v((()=>[k(c(e.title),1)])),_:2},1024)])),default:v((()=>[H("div",R,[_(a,{size:"small",type:"primary",onClick:l=>te("button",e)},{default:v((()=>l[20]||(l[20]=[k("编辑")]))),_:2},1032,["onClick"]),_(a,{size:"small",type:"danger",onClick:l[1]||(l[1]=e=>ne())},{default:v((()=>l[21]||(l[21]=[k("删除")]))),_:1})])])),_:2},1024)))),128))])),_:1}),_(d,{label:"编辑时间",prop:"date"},{default:v((()=>l[22]||(l[22]=[k(" 2022-3-12 12:00:00 ")]))),_:1}),_(d,{fixed:"right",label:"操作",width:"180"},{default:v((e=>[g(_(f,{type:"add",onClick:l[2]||(l[2]=e=>te("menu"))},null,512),[[ce,"add"]]),g(_(f,{type:"edit",onClick:l=>{return a=e.row,void te("menu",a,!0);var a}},null,8,["onClick"]),[[ce,"edit"]]),g(_(f,{type:"delete",onClick:oe},null,512),[[ce,"delete"]])])),_:1})])),_:1},8,["data"]),_(me,{title:C(ee),modelValue:C(p),"onUpdate:modelValue":l[18]||(l[18]=e=>T(p)?p.value=e:null),width:"700px","align-center":""},{footer:v((()=>[H("span",X,[_(a,{onClick:l[16]||(l[16]=e=>p.value=!1)},{default:v((()=>l[25]||(l[25]=[k("取 消")]))),_:1}),_(a,{type:"primary",onClick:l[17]||(l[17]=e=>ae())},{default:v((()=>l[26]||(l[26]=[k(" 确 定 ")]))),_:1})])])),default:v((()=>[_(pe,{ref_key:"formRef",ref:W,model:C(P),rules:C(Z),"label-width":"85px"},{default:v((()=>[_(O,{label:"菜单类型"},{default:v((()=>[_(z,{modelValue:C(Y),"onUpdate:modelValue":l[3]||(l[3]=e=>T(Y)?Y.value=e:null),disabled:C(de)},{default:v((()=>[_($,{value:"menu",label:"menu"},{default:v((()=>l[23]||(l[23]=[k("菜单")]))),_:1}),_($,{value:"button",label:"button"},{default:v((()=>l[24]||(l[24]=[k("权限")]))),_:1})])),_:1},8,["modelValue","disabled"])])),_:1}),"menu"===C(Y)?(i(),s(I,{key:0},[_(t,{gutter:20},{default:v((()=>[_(ue,{span:12},{default:v((()=>[_(O,{label:"菜单名称",prop:"name"},{default:v((()=>[_(J,{modelValue:C(P).name,"onUpdate:modelValue":l[4]||(l[4]=e=>C(P).name=e),placeholder:"菜单名称"},null,8,["modelValue"])])),_:1})])),_:1}),_(ue,{span:12},{default:v((()=>[_(O,{label:"路由地址",prop:"path"},{default:v((()=>[_(J,{modelValue:C(P).path,"onUpdate:modelValue":l[5]||(l[5]=e=>C(P).path=e),placeholder:"路由地址"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),_(t,{gutter:20},{default:v((()=>[_(ue,{span:12},{default:v((()=>[_(O,{label:"权限标识",prop:"label"},{default:v((()=>[_(J,{modelValue:C(P).label,"onUpdate:modelValue":l[6]||(l[6]=e=>C(P).label=e),placeholder:"权限标识"},null,8,["modelValue"])])),_:1})])),_:1}),_(ue,{span:12},{default:v((()=>[_(O,{label:"图标",prop:"icon"},{default:v((()=>[_(re,{iconType:C(D),defaultIcon:C(P).icon,width:"229px"},null,8,["iconType","defaultIcon"])])),_:1})])),_:1})])),_:1}),_(t,{gutter:20},{default:v((()=>[_(ue,{span:12},{default:v((()=>[_(O,{label:"菜单排序",prop:"sort",style:{width:"100%"}},{default:v((()=>[_(se,{modelValue:C(P).sort,"onUpdate:modelValue":l[7]||(l[7]=e=>C(P).sort=e),style:{width:"100%"},onChange:le,min:1,"controls-position":"right"},null,8,["modelValue"])])),_:1})])),_:1}),_(ue,{span:12},{default:v((()=>[_(O,{label:"外部链接",prop:"link"},{default:v((()=>[_(J,{modelValue:C(P).link,"onUpdate:modelValue":l[8]||(l[8]=e=>C(P).link=e),placeholder:"外部链接/内嵌地址(https://www.baidu.com)"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),_(t,{gutter:20},{default:v((()=>[_(ue,{span:5},{default:v((()=>[_(O,{label:"是否启用",prop:"isEnable"},{default:v((()=>[_(ie,{modelValue:C(P).isEnable,"onUpdate:modelValue":l[9]||(l[9]=e=>C(P).isEnable=e)},null,8,["modelValue"])])),_:1})])),_:1}),_(ue,{span:5},{default:v((()=>[_(O,{label:"页面缓存",prop:"keepAlive"},{default:v((()=>[_(ie,{modelValue:C(P).keepAlive,"onUpdate:modelValue":l[10]||(l[10]=e=>C(P).keepAlive=e)},null,8,["modelValue"])])),_:1})])),_:1}),_(ue,{span:5},{default:v((()=>[_(O,{label:"是否显示",prop:"isHidden"},{default:v((()=>[_(ie,{modelValue:C(P).isHidden,"onUpdate:modelValue":l[11]||(l[11]=e=>C(P).isHidden=e)},null,8,["modelValue"])])),_:1})])),_:1}),_(ue,{span:5},{default:v((()=>[_(O,{label:"是否内嵌",prop:"isMenu"},{default:v((()=>[_(ie,{modelValue:C(P).isIframe,"onUpdate:modelValue":l[12]||(l[12]=e=>C(P).isIframe=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})],64)):m("",!0),"button"===C(Y)?(i(),s(I,{key:1},[_(t,{gutter:20},{default:v((()=>[_(ue,{span:12},{default:v((()=>[_(O,{label:"权限名称",prop:"authName"},{default:v((()=>[_(J,{modelValue:C(P).authName,"onUpdate:modelValue":l[13]||(l[13]=e=>C(P).authName=e),placeholder:"权限名称"},null,8,["modelValue"])])),_:1})])),_:1}),_(ue,{span:12},{default:v((()=>[_(O,{label:"权限标识",prop:"authLabel"},{default:v((()=>[_(J,{modelValue:C(P).authLabel,"onUpdate:modelValue":l[14]||(l[14]=e=>C(P).authLabel=e),placeholder:"权限标识"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),_(t,{gutter:20},{default:v((()=>[_(ue,{span:12},{default:v((()=>[_(O,{label:"权限排序",prop:"authSort",style:{width:"100%"}},{default:v((()=>[_(se,{modelValue:C(P).authSort,"onUpdate:modelValue":l[15]||(l[15]=e=>C(P).authSort=e),style:{width:"100%"},onChange:le,min:1,"controls-position":"right"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})],64)):m("",!0)])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])])}}}),[["__scopeId","data-v-b483a2f8"]]);export{Y as default};
