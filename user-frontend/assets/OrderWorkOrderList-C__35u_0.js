var e=(e,r,s)=>new Promise(((a,t)=>{var o=e=>{try{n(s.next(e))}catch(r){t(r)}},i=e=>{try{n(s.throw(e))}catch(r){t(r)}},n=e=>e.done?a(e.value):Promise.resolve(e.value).then(o,i);n((s=s.apply(e,r)).next())}));import{_ as r}from"./index-F0f2Vd-3.js";/* empty css                */import{d as s,r as a,f as t,p as o,c as i,o as n,N as d,w as l,e as c,F as u,A as p,a as m,b as v,x as g,aX as y,K as f,y as k,u as w,by as _,bb as h,aR as N,aI as j,M as x}from"./vendor-BVh5F9vp.js";import{W as O}from"./workOrderApi-4juIOmO-.js";import{E as z}from"./errorHandler-Dnd-hi0l.js";import{u as W}from"./useWorkOrderTypes-B5Knz6RB.js";const b={class:"order-work-order-list"},A={class:"card-header"},C={class:"work-order-list"},L=["onClick"],T={class:"work-order-header"},E={class:"work-order-info"},I={class:"work-order-id"},P={class:"work-order-time"},D={class:"work-order-content"},F={class:"service-type"},H={class:"work-order-description"},K={key:0,class:"work-order-footer"},M={class:"latest-reply"},R={class:"reply-text"},S={key:1,class:"no-work-orders"},X=r(s({__name:"OrderWorkOrderList",props:{orderNo:{},trackingNo:{}},setup(r){const s=r,{fetchWorkOrderTypes:X,getTypeName:$}=W(),q=a(!1),B=a([]),G={pending:"待处理",processing:"处理中",waiting_customer:"等待用户回复",resolved:"已解决",closed:"已关闭",cancelled:"已取消"},J=()=>e(this,null,(function*(){if(s.orderNo){q.value=!0;try{const e=yield O.getWorkOrderList({order_no:s.orderNo,tracking_no:s.trackingNo,page:1,page_size:10});e.success&&e.data&&(B.value=e.data.items||[])}catch(e){z.handleApiError(e,!1)}finally{q.value=!1}}})),Q=e=>G[e]||e,U=()=>{if(0===B.value.length)return"info";const e=B.value.some((e=>"processing"===e.status)),r=B.value.some((e=>"pending"===e.status)),s=B.value.some((e=>"waiting_customer"===e.status));return e?"primary":r?"warning":s?"info":"success"},V=()=>{if(0===B.value.length)return"无工单";const e=B.value.some((e=>"processing"===e.status)),r=B.value.some((e=>"pending"===e.status)),s=B.value.some((e=>"waiting_customer"===e.status));return e?"处理中":r?"待处理":s?"等待回复":`${B.value.length}个工单`},Y=e=>{if(!e)return"-";try{return new Date(e).toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(r){return e}};return t((()=>e(this,null,(function*(){yield X()})))),o((()=>s.orderNo),(e=>{e&&J()}),{immediate:!0}),(e,r)=>{const s=y,a=k,t=N,o=j;return n(),i("div",b,[B.value.length>0?(n(),d(t,{key:0,class:"work-order-card"},{header:l((()=>[c("div",A,[r[0]||(r[0]=c("span",null,"相关售后工单",-1)),v(s,{type:U(),size:"small"},{default:l((()=>[f(g(V()),1)])),_:1},8,["type"])])])),default:l((()=>[c("div",C,[(n(!0),i(u,null,p(B.value,(e=>{return n(),i("div",{key:e.id,class:"work-order-item",onClick:e=>{x.info("工单详情功能开发中...")}},[c("div",T,[c("div",E,[c("span",I,"工单 #"+g(e.id),1),v(s,{type:(t=e.status,{pending:"warning",processing:"primary",waiting_customer:"info",resolved:"success",closed:"info",cancelled:"danger"}[t]||"info"),size:"small"},{default:l((()=>[f(g(Q(e.status)),1)])),_:2},1032,["type"])]),c("div",P,g(Y(e.created_at)),1)]),c("div",D,[c("div",F,[v(a,null,{default:l((()=>[v(w(_))])),_:1}),f(" "+g((r=e.work_order_type,$(r))),1)]),c("div",H,g(e.content||"暂无描述"),1)]),e.latest_reply?(n(),i("div",K,[c("div",M,[v(a,null,{default:l((()=>[v(w(h))])),_:1}),c("span",R,g(e.latest_reply),1)])])):m("",!0)],8,L);var r,t})),128))])])),_:1})):(n(),i("div",S,[v(o,{description:"暂无相关售后工单","image-size":60},{description:l((()=>r[1]||(r[1]=[c("span",{class:"empty-description"}," 该订单暂无售后工单记录 ",-1)]))),_:1})]))])}}}),[["__scopeId","data-v-dd658386"]]);export{X as default};
