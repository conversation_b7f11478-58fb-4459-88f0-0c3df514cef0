var a=(a,t,e)=>new Promise(((l,s)=>{var i=a=>{try{r(e.next(a))}catch(t){s(t)}},n=a=>{try{r(e.throw(a))}catch(t){s(t)}},r=a=>a.done?l(a.value):Promise.resolve(a.value).then(i,n);r((e=e.apply(a,t)).next())}));import{_ as t}from"./index-F0f2Vd-3.js";/* empty css                       *//* empty css               *//* empty css                */import{d as e,r as l,p as s,f as i,bN as n,c as r,o as d,e as o,b as u,w as c,aN as v,aR as p,y as f,u as m,bd as _,x as h,bf as y,bC as b,aS as g,aQ as w,bh as k,H as j,K as x,b4 as C,bU as S,b7 as Y,g as z}from"./vendor-BVh5F9vp.js";import"./index-CdJNdp1H.js";import{u as O}from"./workOrder-Bqy1-bv9.js";import{E as A}from"./errorHandler-Dnd-hi0l.js";import{i as E}from"./install-DmVoiIn1.js";import"./workOrderApi-4juIOmO-.js";const L={class:"workorder-statistics"},D={class:"statistics-cards"},M={class:"stat-content"},P={class:"stat-icon"},T={class:"stat-info"},R={class:"stat-value"},V={class:"stat-content"},F={class:"stat-icon"},H={class:"stat-info"},N={class:"stat-value"},U={class:"stat-content"},W={class:"stat-icon"},$={class:"stat-info"},q={class:"stat-value"},G={class:"stat-content"},I={class:"stat-icon"},K={class:"stat-info"},Q={class:"stat-value"},B={class:"charts-section"},J={class:"chart-header"},X={class:"quick-actions"},Z={class:"action-buttons"},aa=t(e({__name:"WorkOrderStatistics",emits:["create-workorder","view-all"],setup(t,{emit:e}){const aa=O(),ta=l(),ea=l(),la=l(null),sa=l(null),ia=l([]),na=l([]);let ra=null,da=null;s((()=>aa.statistics),(a=>{sa.value=a})),s((()=>aa.trend),(a=>{ia.value=a,ua()})),s((()=>aa.typeStatistics),(a=>{na.value=a,ca()}));const oa=()=>a(this,null,(function*(){yield z(),ta.value&&(ra=E(ta.value),ua()),ea.value&&(da=E(ea.value),ca()),window.addEventListener("resize",va)})),ua=()=>{if(!ra||!ia.value.length)return;const a={tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{data:["创建","完结"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:ia.value.map((a=>a.date))},yAxis:{type:"value"},series:[{name:"创建",type:"line",stack:"Total",smooth:!0,data:ia.value.map((a=>a.created)),itemStyle:{color:"#409EFF"}},{name:"完结",type:"line",stack:"Total",smooth:!0,data:ia.value.map((a=>a.completed)),itemStyle:{color:"#67C23A"}}]};ra.setOption(a)},ca=()=>{if(!da||!na.value.length)return;const a=na.value.map((a=>({name:a.type_name,value:a.count}))),t={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:a.map((a=>a.name))},series:[{name:"工单类型",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:a}]};da.setOption(t)},va=()=>{null==ra||ra.resize(),null==da||da.resize()},pa=a=>{const t=a?{start_date:a[0],end_date:a[1]}:void 0;ma(t)},fa=()=>{const a=la.value?{start_date:la.value[0],end_date:la.value[1]}:void 0;ma(a,!0)},ma=(t,e=!1)=>a(this,null,(function*(){try{yield Promise.all([aa.fetchStatistics(t,e),aa.fetchTrend(t,e),aa.fetchTypeStatistics(t,e)])}catch(a){A.handleApiError(a)}}));i((()=>a(this,null,(function*(){yield ma(),yield oa()}))));return n((()=>{window.removeEventListener("resize",va),null==ra||ra.dispose(),null==da||da.dispose()})),(a,t)=>{const e=f,l=p,s=v,i=w,n=k,z=j;return d(),r("div",L,[o("div",D,[u(i,{gutter:20},{default:c((()=>[u(s,{span:6},{default:c((()=>[u(l,{class:"stat-card total"},{default:c((()=>{var a;return[o("div",M,[o("div",P,[u(e,null,{default:c((()=>[u(m(_))])),_:1})]),o("div",T,[o("div",R,h((null==(a=sa.value)?void 0:a.total)||0),1),t[3]||(t[3]=o("div",{class:"stat-label"},"总工单数",-1))])])]})),_:1})])),_:1}),u(s,{span:6},{default:c((()=>[u(l,{class:"stat-card pending"},{default:c((()=>{var a;return[o("div",V,[o("div",F,[u(e,null,{default:c((()=>[u(m(y))])),_:1})]),o("div",H,[o("div",N,h((null==(a=sa.value)?void 0:a.pending)||0),1),t[4]||(t[4]=o("div",{class:"stat-label"},"待处理",-1))])])]})),_:1})])),_:1}),u(s,{span:6},{default:c((()=>[u(l,{class:"stat-card processing"},{default:c((()=>{var a;return[o("div",U,[o("div",W,[u(e,null,{default:c((()=>[u(m(b))])),_:1})]),o("div",$,[o("div",q,h((null==(a=sa.value)?void 0:a.processing)||0),1),t[5]||(t[5]=o("div",{class:"stat-label"},"处理中",-1))])])]})),_:1})])),_:1}),u(s,{span:6},{default:c((()=>[u(l,{class:"stat-card completed"},{default:c((()=>{var a;return[o("div",G,[o("div",I,[u(e,null,{default:c((()=>[u(m(g))])),_:1})]),o("div",K,[o("div",Q,h((null==(a=sa.value)?void 0:a.completed)||0),1),t[6]||(t[6]=o("div",{class:"stat-label"},"已完结",-1))])])]})),_:1})])),_:1})])),_:1})]),o("div",B,[u(i,{gutter:20},{default:c((()=>[u(s,{span:16},{default:c((()=>[u(l,null,{header:c((()=>[o("div",J,[t[7]||(t[7]=o("span",null,"工单趋势",-1)),u(n,{modelValue:la.value,"onUpdate:modelValue":t[0]||(t[0]=a=>la.value=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:pa,size:"small"},null,8,["modelValue"])])])),default:c((()=>[o("div",{ref_key:"trendChartRef",ref:ta,class:"chart-container"},null,512)])),_:1})])),_:1}),u(s,{span:8},{default:c((()=>[u(l,null,{header:c((()=>t[8]||(t[8]=[o("span",null,"工单类型分布",-1)]))),default:c((()=>[o("div",{ref_key:"typeChartRef",ref:ea,class:"chart-container"},null,512)])),_:1})])),_:1})])),_:1})]),o("div",X,[u(l,null,{header:c((()=>t[9]||(t[9]=[o("span",null,"快速操作",-1)]))),default:c((()=>[o("div",Z,[u(z,{type:"primary",onClick:t[1]||(t[1]=t=>a.$emit("create-workorder"))},{default:c((()=>[u(e,null,{default:c((()=>[u(m(C))])),_:1}),t[10]||(t[10]=x(" 创建工单 "))])),_:1}),u(z,{onClick:t[2]||(t[2]=t=>a.$emit("view-all"))},{default:c((()=>[u(e,null,{default:c((()=>[u(m(S))])),_:1}),t[11]||(t[11]=x(" 查看全部 "))])),_:1}),u(z,{onClick:fa},{default:c((()=>[u(e,null,{default:c((()=>[u(m(Y))])),_:1}),t[12]||(t[12]=x(" 刷新数据 "))])),_:1})])])),_:1})])])}}}),[["__scopeId","data-v-92430152"]]);export{aa as default};
