import{_ as a}from"./index-F0f2Vd-3.js";import{C as l}from"./vue3-count-to.esm-Cfo4Xkp7.js";import{d as e,r as n,c as u,o as t,e as s,b as o,u as r,w as d,H as c,K as i,aF as v}from"./vendor-BVh5F9vp.js";const f={class:"page-content"},m={class:"mt-4"},p=a(e({__name:"CountTo",setup(a){const e=n(1e3),p=n(19999.99),V=n(2023.45),_=n(5e3),C=n(),h=()=>{var a;null==(a=C.value)||a.start()},k=()=>{var a;null==(a=C.value)||a.pause()},x=()=>{var a;null==(a=C.value)||a.reset()};return(a,n)=>{const j=c,y=v;return t(),u("div",f,[n[3]||(n[3]=s("h2",null,"基础用法",-1)),o(r(l),{endVal:e.value,duration:1e3},null,8,["endVal"]),n[4]||(n[4]=s("h2",null,"带前缀后缀",-1)),o(r(l),{prefix:"¥",suffix:"元",startVal:0,endVal:p.value,duration:2e3},null,8,["endVal"]),n[5]||(n[5]=s("h2",null,"小数点和分隔符",-1)),o(r(l),{startVal:0,endVal:V.value,decimals:2,decimal:".",separator:",",duration:2500},null,8,["endVal"]),n[6]||(n[6]=s("h2",null,"控制按钮",-1)),o(r(l),{ref_key:"countTo",ref:C,startVal:0,endVal:_.value,duration:3e3,autoplay:!1},null,8,["endVal"]),s("div",m,[o(y,null,{default:d((()=>[o(j,{onClick:h},{default:d((()=>n[0]||(n[0]=[i("开始")]))),_:1}),o(j,{onClick:k},{default:d((()=>n[1]||(n[1]=[i("暂停")]))),_:1}),o(j,{onClick:x},{default:d((()=>n[2]||(n[2]=[i("重置")]))),_:1})])),_:1})])])}}}),[["__scopeId","data-v-7ccf41ea"]]);export{p as default};
