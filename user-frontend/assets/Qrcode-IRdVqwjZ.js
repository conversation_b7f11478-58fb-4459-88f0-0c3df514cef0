import{_ as e}from"./index-F0f2Vd-3.js";/* empty css               *//* empty css                */import{d as t,T as r,r as n,bM as i,f as o,F as a,X as s,p as u,c as l,o as h,b as d,w as f,A as c,aN as g,aR as v,e as m,bL as p,x as w,aQ as E}from"./vendor-BVh5F9vp.js";
/*!
 * qrcode.vue v3.6.0
 * A Vue.js component to generate QRCode. Both support Vue 2 and Vue 3
 * © 2017-PRESENT @scopewu(https://github.com/scopewu)
 * MIT License.
 */var C,y,M,R,A=function(){return A=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},A.apply(this,arguments)};"function"==typeof SuppressedError&&SuppressedError,function(e){var t=function(){function t(e,r,n,o){if(this.version=e,this.errorCorrectionLevel=r,this.modules=[],this.isFunction=[],e<t.MIN_VERSION||e>t.MAX_VERSION)throw new RangeError("Version value out of range");if(o<-1||o>7)throw new RangeError("Mask value out of range");this.size=4*e+17;for(var a=[],s=0;s<this.size;s++)a.push(!1);for(s=0;s<this.size;s++)this.modules.push(a.slice()),this.isFunction.push(a.slice());this.drawFunctionPatterns();var u=this.addEccAndInterleave(n);if(this.drawCodewords(u),-1==o){var l=1e9;for(s=0;s<8;s++){this.applyMask(s),this.drawFormatBits(s);var h=this.getPenaltyScore();h<l&&(o=s,l=h),this.applyMask(s)}}i(0<=o&&o<=7),this.mask=o,this.applyMask(o),this.drawFormatBits(o),this.isFunction=[]}return t.encodeText=function(r,n){var i=e.QrSegment.makeSegments(r);return t.encodeSegments(i,n)},t.encodeBinary=function(r,n){var i=e.QrSegment.makeBytes(r);return t.encodeSegments([i],n)},t.encodeSegments=function(e,n,a,s,u,l){if(void 0===a&&(a=1),void 0===s&&(s=40),void 0===u&&(u=-1),void 0===l&&(l=!0),!(t.MIN_VERSION<=a&&a<=s&&s<=t.MAX_VERSION)||u<-1||u>7)throw new RangeError("Invalid value");var h,d;for(h=a;;h++){var f=8*t.getNumDataCodewords(h,n),c=o.getTotalBits(e,h);if(c<=f){d=c;break}if(h>=s)throw new RangeError("Data too long")}for(var g=0,v=[t.Ecc.MEDIUM,t.Ecc.QUARTILE,t.Ecc.HIGH];g<v.length;g++){var m=v[g];l&&d<=8*t.getNumDataCodewords(h,m)&&(n=m)}for(var p=[],w=0,E=e;w<E.length;w++){var C=E[w];r(C.mode.modeBits,4,p),r(C.numChars,C.mode.numCharCountBits(h),p);for(var y=0,M=C.getData();y<M.length;y++){var R=M[y];p.push(R)}}i(p.length==d);var A=8*t.getNumDataCodewords(h,n);i(p.length<=A),r(0,Math.min(4,A-p.length),p),r(0,(8-p.length%8)%8,p),i(p.length%8==0);for(var S=236;p.length<A;S^=253)r(S,8,p);for(var N=[];8*N.length<p.length;)N.push(0);return p.forEach((function(e,t){return N[t>>>3]|=e<<7-(7&t)})),new t(h,n,N,u)},t.prototype.getModule=function(e,t){return 0<=e&&e<this.size&&0<=t&&t<this.size&&this.modules[t][e]},t.prototype.getModules=function(){return this.modules},t.prototype.drawFunctionPatterns=function(){for(var e=0;e<this.size;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);var t=this.getAlignmentPatternPositions(),r=t.length;for(e=0;e<r;e++)for(var n=0;n<r;n++)0==e&&0==n||0==e&&n==r-1||e==r-1&&0==n||this.drawAlignmentPattern(t[e],t[n]);this.drawFormatBits(0),this.drawVersion()},t.prototype.drawFormatBits=function(e){for(var t=this.errorCorrectionLevel.formatBits<<3|e,r=t,o=0;o<10;o++)r=r<<1^1335*(r>>>9);var a=21522^(t<<10|r);i(a>>>15==0);for(o=0;o<=5;o++)this.setFunctionModule(8,o,n(a,o));this.setFunctionModule(8,7,n(a,6)),this.setFunctionModule(8,8,n(a,7)),this.setFunctionModule(7,8,n(a,8));for(o=9;o<15;o++)this.setFunctionModule(14-o,8,n(a,o));for(o=0;o<8;o++)this.setFunctionModule(this.size-1-o,8,n(a,o));for(o=8;o<15;o++)this.setFunctionModule(8,this.size-15+o,n(a,o));this.setFunctionModule(8,this.size-8,!0)},t.prototype.drawVersion=function(){if(!(this.version<7)){for(var e=this.version,t=0;t<12;t++)e=e<<1^7973*(e>>>11);var r=this.version<<12|e;i(r>>>18==0);for(t=0;t<18;t++){var o=n(r,t),a=this.size-11+t%3,s=Math.floor(t/3);this.setFunctionModule(a,s,o),this.setFunctionModule(s,a,o)}}},t.prototype.drawFinderPattern=function(e,t){for(var r=-4;r<=4;r++)for(var n=-4;n<=4;n++){var i=Math.max(Math.abs(n),Math.abs(r)),o=e+n,a=t+r;0<=o&&o<this.size&&0<=a&&a<this.size&&this.setFunctionModule(o,a,2!=i&&4!=i)}},t.prototype.drawAlignmentPattern=function(e,t){for(var r=-2;r<=2;r++)for(var n=-2;n<=2;n++)this.setFunctionModule(e+n,t+r,1!=Math.max(Math.abs(n),Math.abs(r)))},t.prototype.setFunctionModule=function(e,t,r){this.modules[t][e]=r,this.isFunction[t][e]=!0},t.prototype.addEccAndInterleave=function(e){var r=this.version,n=this.errorCorrectionLevel;if(e.length!=t.getNumDataCodewords(r,n))throw new RangeError("Invalid argument");for(var o=t.NUM_ERROR_CORRECTION_BLOCKS[n.ordinal][r],a=t.ECC_CODEWORDS_PER_BLOCK[n.ordinal][r],s=Math.floor(t.getNumRawDataModules(r)/8),u=o-s%o,l=Math.floor(s/o),h=[],d=t.reedSolomonComputeDivisor(a),f=0,c=0;f<o;f++){var g=e.slice(c,c+l-a+(f<u?0:1));c+=g.length;var v=t.reedSolomonComputeRemainder(g,d);f<u&&g.push(0),h.push(g.concat(v))}var m=[],p=function(e){h.forEach((function(t,r){(e!=l-a||r>=u)&&m.push(t[e])}))};for(f=0;f<h[0].length;f++)p(f);return i(m.length==s),m},t.prototype.drawCodewords=function(e){if(e.length!=Math.floor(t.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");for(var r=0,o=this.size-1;o>=1;o-=2){6==o&&(o=5);for(var a=0;a<this.size;a++)for(var s=0;s<2;s++){var u=o-s,l=!(o+1&2)?this.size-1-a:a;!this.isFunction[l][u]&&r<8*e.length&&(this.modules[l][u]=n(e[r>>>3],7-(7&r)),r++)}}i(r==8*e.length)},t.prototype.applyMask=function(e){if(e<0||e>7)throw new RangeError("Mask value out of range");for(var t=0;t<this.size;t++)for(var r=0;r<this.size;r++){var n=void 0;switch(e){case 0:n=(r+t)%2==0;break;case 1:n=t%2==0;break;case 2:n=r%3==0;break;case 3:n=(r+t)%3==0;break;case 4:n=(Math.floor(r/3)+Math.floor(t/2))%2==0;break;case 5:n=r*t%2+r*t%3==0;break;case 6:n=(r*t%2+r*t%3)%2==0;break;case 7:n=((r+t)%2+r*t%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[t][r]&&n&&(this.modules[t][r]=!this.modules[t][r])}},t.prototype.getPenaltyScore=function(){for(var e=0,r=0;r<this.size;r++){for(var n=!1,o=0,a=[0,0,0,0,0,0,0],s=0;s<this.size;s++)this.modules[r][s]==n?5==++o?e+=t.PENALTY_N1:o>5&&e++:(this.finderPenaltyAddHistory(o,a),n||(e+=this.finderPenaltyCountPatterns(a)*t.PENALTY_N3),n=this.modules[r][s],o=1);e+=this.finderPenaltyTerminateAndCount(n,o,a)*t.PENALTY_N3}for(s=0;s<this.size;s++){n=!1;var u=0;for(a=[0,0,0,0,0,0,0],r=0;r<this.size;r++)this.modules[r][s]==n?5==++u?e+=t.PENALTY_N1:u>5&&e++:(this.finderPenaltyAddHistory(u,a),n||(e+=this.finderPenaltyCountPatterns(a)*t.PENALTY_N3),n=this.modules[r][s],u=1);e+=this.finderPenaltyTerminateAndCount(n,u,a)*t.PENALTY_N3}for(r=0;r<this.size-1;r++)for(s=0;s<this.size-1;s++){var l=this.modules[r][s];l==this.modules[r][s+1]&&l==this.modules[r+1][s]&&l==this.modules[r+1][s+1]&&(e+=t.PENALTY_N2)}for(var h=0,d=0,f=this.modules;d<f.length;d++){h=f[d].reduce((function(e,t){return e+(t?1:0)}),h)}var c=this.size*this.size,g=Math.ceil(Math.abs(20*h-10*c)/c)-1;return i(0<=g&&g<=9),i(0<=(e+=g*t.PENALTY_N4)&&e<=2568888),e},t.prototype.getAlignmentPatternPositions=function(){if(1==this.version)return[];for(var e=Math.floor(this.version/7)+2,t=2*Math.floor((8*this.version+3*e+5)/(4*e-4)),r=[6],n=this.size-7;r.length<e;n-=t)r.splice(1,0,n);return r},t.getNumRawDataModules=function(e){if(e<t.MIN_VERSION||e>t.MAX_VERSION)throw new RangeError("Version number out of range");var r=(16*e+128)*e+64;if(e>=2){var n=Math.floor(e/7)+2;r-=(25*n-10)*n-55,e>=7&&(r-=36)}return i(208<=r&&r<=29648),r},t.getNumDataCodewords=function(e,r){return Math.floor(t.getNumRawDataModules(e)/8)-t.ECC_CODEWORDS_PER_BLOCK[r.ordinal][e]*t.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][e]},t.reedSolomonComputeDivisor=function(e){if(e<1||e>255)throw new RangeError("Degree out of range");for(var r=[],n=0;n<e-1;n++)r.push(0);r.push(1);var i=1;for(n=0;n<e;n++){for(var o=0;o<r.length;o++)r[o]=t.reedSolomonMultiply(r[o],i),o+1<r.length&&(r[o]^=r[o+1]);i=t.reedSolomonMultiply(i,2)}return r},t.reedSolomonComputeRemainder=function(e,r){for(var n=r.map((function(e){return 0})),i=function(e){var i=e^n.shift();n.push(0),r.forEach((function(e,r){return n[r]^=t.reedSolomonMultiply(e,i)}))},o=0,a=e;o<a.length;o++){i(a[o])}return n},t.reedSolomonMultiply=function(e,t){if(e>>>8!=0||t>>>8!=0)throw new RangeError("Byte out of range");for(var r=0,n=7;n>=0;n--)r=r<<1^285*(r>>>7),r^=(t>>>n&1)*e;return i(r>>>8==0),r},t.prototype.finderPenaltyCountPatterns=function(e){var t=e[1];i(t<=3*this.size);var r=t>0&&e[2]==t&&e[3]==3*t&&e[4]==t&&e[5]==t;return(r&&e[0]>=4*t&&e[6]>=t?1:0)+(r&&e[6]>=4*t&&e[0]>=t?1:0)},t.prototype.finderPenaltyTerminateAndCount=function(e,t,r){return e&&(this.finderPenaltyAddHistory(t,r),t=0),t+=this.size,this.finderPenaltyAddHistory(t,r),this.finderPenaltyCountPatterns(r)},t.prototype.finderPenaltyAddHistory=function(e,t){0==t[0]&&(e+=this.size),t.pop(),t.unshift(e)},t.MIN_VERSION=1,t.MAX_VERSION=40,t.PENALTY_N1=3,t.PENALTY_N2=3,t.PENALTY_N3=40,t.PENALTY_N4=10,t.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],t.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],t}();function r(e,t,r){if(t<0||t>31||e>>>t!=0)throw new RangeError("Value out of range");for(var n=t-1;n>=0;n--)r.push(e>>>n&1)}function n(e,t){return!!(e>>>t&1)}function i(e){if(!e)throw new Error("Assertion error")}e.QrCode=t;var o=function(){function e(e,t,r){if(this.mode=e,this.numChars=t,this.bitData=r,t<0)throw new RangeError("Invalid argument");this.bitData=r.slice()}return e.makeBytes=function(t){for(var n=[],i=0,o=t;i<o.length;i++){r(o[i],8,n)}return new e(e.Mode.BYTE,t.length,n)},e.makeNumeric=function(t){if(!e.isNumeric(t))throw new RangeError("String contains non-numeric characters");for(var n=[],i=0;i<t.length;){var o=Math.min(t.length-i,3);r(parseInt(t.substring(i,i+o),10),3*o+1,n),i+=o}return new e(e.Mode.NUMERIC,t.length,n)},e.makeAlphanumeric=function(t){if(!e.isAlphanumeric(t))throw new RangeError("String contains unencodable characters in alphanumeric mode");var n,i=[];for(n=0;n+2<=t.length;n+=2){var o=45*e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n));r(o+=e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n+1)),11,i)}return n<t.length&&r(e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(n)),6,i),new e(e.Mode.ALPHANUMERIC,t.length,i)},e.makeSegments=function(t){return""==t?[]:e.isNumeric(t)?[e.makeNumeric(t)]:e.isAlphanumeric(t)?[e.makeAlphanumeric(t)]:[e.makeBytes(e.toUtf8ByteArray(t))]},e.makeEci=function(t){var n=[];if(t<0)throw new RangeError("ECI assignment value out of range");if(t<128)r(t,8,n);else if(t<16384)r(2,2,n),r(t,14,n);else{if(!(t<1e6))throw new RangeError("ECI assignment value out of range");r(6,3,n),r(t,21,n)}return new e(e.Mode.ECI,0,n)},e.isNumeric=function(t){return e.NUMERIC_REGEX.test(t)},e.isAlphanumeric=function(t){return e.ALPHANUMERIC_REGEX.test(t)},e.prototype.getData=function(){return this.bitData.slice()},e.getTotalBits=function(e,t){for(var r=0,n=0,i=e;n<i.length;n++){var o=i[n],a=o.mode.numCharCountBits(t);if(o.numChars>=1<<a)return 1/0;r+=4+a+o.bitData.length}return r},e.toUtf8ByteArray=function(e){e=encodeURI(e);for(var t=[],r=0;r<e.length;r++)"%"!=e.charAt(r)?t.push(e.charCodeAt(r)):(t.push(parseInt(e.substring(r+1,r+3),16)),r+=2);return t},e.NUMERIC_REGEX=/^[0-9]*$/,e.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,e.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",e}();e.QrSegment=o}(C||(C={})),y=C||(C={}),M=y.QrCode||(y.QrCode={}),R=function(){function e(e,t){this.ordinal=e,this.formatBits=t}return e.LOW=new e(0,1),e.MEDIUM=new e(1,0),e.QUARTILE=new e(2,3),e.HIGH=new e(3,2),e}(),M.Ecc=R,function(e){var t,r;t=e.QrSegment||(e.QrSegment={}),r=function(){function e(e,t){this.modeBits=e,this.numBitsCharCount=t}return e.prototype.numCharCountBits=function(e){return this.numBitsCharCount[Math.floor((e+7)/17)]},e.NUMERIC=new e(1,[10,12,14]),e.ALPHANUMERIC=new e(2,[9,11,13]),e.BYTE=new e(4,[8,16,16]),e.KANJI=new e(8,[8,10,12]),e.ECI=new e(7,[0,0,0]),e}(),t.Mode=r}(C||(C={}));var S=C,N={L:S.QrCode.Ecc.LOW,M:S.QrCode.Ecc.MEDIUM,Q:S.QrCode.Ecc.QUARTILE,H:S.QrCode.Ecc.HIGH},P=function(){try{(new Path2D).addPath(new Path2D)}catch(e){return!1}return!0}();function I(e){return e in N}function _(e,t){void 0===t&&(t=0);var r=[];return e.forEach((function(e,n){var i=null;e.forEach((function(o,a){if(!o&&null!==i)return r.push("M".concat(i+t," ").concat(n+t,"h").concat(a-i,"v1H").concat(i+t,"z")),void(i=null);if(a!==e.length-1)o&&null===i&&(i=a);else{if(!o)return;null===i?r.push("M".concat(a+t,",").concat(n+t," h1v1H").concat(a+t,"z")):r.push("M".concat(i+t,",").concat(n+t," h").concat(a+1-i,"v1H").concat(i+t,"z"))}}))})),r.join("")}function z(e,t,r,n){var i=n.width,o=n.height,a=n.x,s=n.y,u=e.length+2*r,l=Math.floor(.1*t),h=u/t,d=(i||l)*h,f=(o||l)*h,c=null==a?e.length/2-d/2:a*h,g=null==s?e.length/2-f/2:s*h,v=null;if(n.excavate){var m=Math.floor(c),p=Math.floor(g);v={x:m,y:p,w:Math.ceil(d+c-m),h:Math.ceil(f+g-p)}}return{x:c,y:g,h:f,w:d,excavation:v}}function x(e,t){return e.slice().map((function(e,r){return r<t.y||r>=t.y+t.h?e:e.map((function(e,r){return(r<t.x||r>=t.x+t.w)&&e}))}))}var L={value:{type:String,required:!0,default:""},size:{type:Number,default:100},level:{type:String,default:"L",validator:function(e){return I(e)}},background:{type:String,default:"#fff"},foreground:{type:String,default:"#000"},margin:{type:Number,required:!1,default:0},imageSettings:{type:Object,required:!1,default:function(){return{}}},gradient:{type:Boolean,required:!1,default:!1},gradientType:{type:String,required:!1,default:"linear",validator:function(e){return["linear","radial"].indexOf(e)>-1}},gradientStartColor:{type:String,required:!1,default:"#000"},gradientEndColor:{type:String,required:!1,default:"#fff"}},b=A(A({},L),{renderAs:{type:String,required:!1,default:"canvas",validator:function(e){return["canvas","svg"].indexOf(e)>-1}}}),k=t({name:"QRCodeSvg",props:L,setup:function(e){var t,o=n(0),a=n(""),s=function(){var r=e.value,n=e.level,i=e.margin>>>0,s=I(n)?n:"L",u=S.QrCode.encodeText(r,N[s]).getModules();if(o.value=u.length+2*i,e.imageSettings.src){var l=z(u,e.size,i,e.imageSettings);t={x:l.x+i,y:l.y+i,width:l.w,height:l.h},l.excavation&&(u=x(u,l.excavation))}a.value=_(u,i)},u=function(){if(!e.gradient)return null;var t="linear"===e.gradientType?{x1:"0%",y1:"0%",x2:"100%",y2:"100%"}:{cx:"50%",cy:"50%",r:"50%",fx:"50%",fy:"50%"};return r("linear"===e.gradientType?"linearGradient":"radialGradient",A({id:"qr-gradient"},t),[r("stop",{offset:"0%",style:{stopColor:e.gradientStartColor}}),r("stop",{offset:"100%",style:{stopColor:e.gradientEndColor}})])};return s(),i(s),function(){return r("svg",{width:e.size,height:e.size,"shape-rendering":"crispEdges",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(o.value," ").concat(o.value)},[r("defs",{},[u()]),r("rect",{width:"100%",height:"100%",fill:e.background}),r("path",{fill:e.gradient?"url(#qr-gradient)":e.foreground,d:a.value}),e.imageSettings.src&&r("image",A({href:e.imageSettings.src},t))])}}}),O=t({name:"QRCodeCanvas",props:L,setup:function(e,t){var s=n(null),u=n(null),l=function(){var t=e.value,r=e.level,n=e.size,i=e.margin,o=e.background,a=e.foreground,l=e.gradient,h=e.gradientType,d=e.gradientStartColor,f=e.gradientEndColor,c=i>>>0,g=I(r)?r:"L",v=s.value;if(v){var m=v.getContext("2d");if(m){var p=S.QrCode.encodeText(t,N[g]).getModules(),w=p.length+2*c,E=u.value,C={x:0,y:0,width:0,height:0},y=e.imageSettings.src&&null!=E&&0!==E.naturalWidth&&0!==E.naturalHeight;if(y){var M=z(p,e.size,c,e.imageSettings);C={x:M.x+c,y:M.y+c,width:M.w,height:M.h},M.excavation&&(p=x(p,M.excavation))}var R=window.devicePixelRatio||1,A=n/w*R;if(v.height=v.width=n*R,m.scale(A,A),m.fillStyle=o,m.fillRect(0,0,w,w),l){var L=void 0;(L="linear"===h?m.createLinearGradient(0,0,w,w):m.createRadialGradient(w/2,w/2,0,w/2,w/2,w/2)).addColorStop(0,d),L.addColorStop(1,f),m.fillStyle=L}else m.fillStyle=a;P?m.fill(new Path2D(_(p,c))):p.forEach((function(e,t){e.forEach((function(e,r){e&&m.fillRect(r+c,t+c,1,1)}))})),y&&m.drawImage(E,C.x,C.y,C.width,C.height)}}};o(l),i(l);var h=t.attrs.style;return function(){return r(a,[r("canvas",A(A({},t.attrs),{ref:s,style:A(A({},h),{width:"".concat(e.size,"px"),height:"".concat(e.size,"px")})})),e.imageSettings.src&&r("img",{ref:u,src:e.imageSettings.src,style:{display:"none"},onLoad:l})])}}}),T=t({name:"Qrcode",render:function(){var e=this.$props,t=e.renderAs,n=e.value,i=e.size,o=e.margin,a=e.level,s=e.background,u=e.foreground,l=e.imageSettings,h=e.gradient,d=e.gradientType,f=e.gradientStartColor,c=e.gradientEndColor;return r("svg"===t?k:O,{value:n,size:i,margin:o,level:a,background:s,foreground:u,imageSettings:l,gradient:h,gradientType:d,gradientStartColor:f,gradientEndColor:c})},props:b});const F={class:"page-content"},D={class:"card-header"},H={class:"qrcode-preview"},B=e(t({__name:"Qrcode",setup(e){const t=n("https://www.lingchen.kim"),r=n(!1),i=[{title:"渲染成 img 标签",config:{size:160,level:"H",renderAs:"canvas",margin:0,background:"#ffffff",foreground:"#000000"}},{title:"渲染成 canvas 标签",config:{size:160,level:"H",renderAs:"canvas",margin:0,background:"#ffffff",foreground:"#000000"}},{title:"自定义颜色",config:{size:160,level:"H",renderAs:"canvas",margin:0,background:"#f0f0f0",foreground:"#4080ff"}},{title:"带有Logo",config:{size:160,level:"H",renderAs:"canvas",margin:0,background:"#ffffff",foreground:"#000000",imageSettings:{src:"https://www.lingchen.kim/art-design-pro/assets/avatar-DJIoI-3F.png",width:40,height:40,excavate:!0}}}],o=s({size:160,level:"H",renderAs:"canvas",margin:0,background:"#ffffff",foreground:"#000000",imageSettings:{src:"https://www.lingchen.kim/art-design-pro/assets/avatar-DJIoI-3F.png",width:40,height:40,excavate:!0}});return u(r,(e=>{o.imageSettings=e?{src:"https://www.lingchen.kim/art-design-pro/assets/avatar-DJIoI-3F.png",width:40,height:40,excavate:!0}:{}})),(e,r)=>{const n=v,o=g,s=E;return h(),l("div",F,[d(s,{gutter:20},{default:f((()=>[(h(),l(a,null,c(i,(e=>d(o,{span:6,key:e.title},{default:f((()=>[d(n,{class:"qrcode-card",shadow:"never"},{header:f((()=>[m("div",D,[m("span",null,w(e.title),1)])])),default:f((()=>[m("div",H,[d(T,p({value:t.value,ref_for:!0},e.config),null,16,["value"])])])),_:2},1024)])),_:2},1024))),64))])),_:1})])}}}),[["__scopeId","data-v-1f132d1c"]]);export{B as default};
