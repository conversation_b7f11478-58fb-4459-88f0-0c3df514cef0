import{_ as a}from"./index-F0f2Vd-3.js";import{d as e,X as s,c as t,o as d,bn as l,e as o,F as c,A as m,b as n,x as r,aD as p,u}from"./vendor-BVh5F9vp.js";const i={class:"region todo-list art-custom-card"},v={class:"list"},b={class:"title"},x={class:"date subtitle"},_=a(e({__name:"TodoList",setup(a){const e=s([{username:"查看今天工作内容",date:"上午 09:30",complate:!0},{username:"回复邮件",date:"上午 10:30",complate:!0},{username:"工作汇报整理",date:"上午 11:00",complate:!0},{username:"产品需求会议",date:"下午 02:00",complate:!1},{username:"整理会议内容",date:"下午 03:30",complate:!1},{username:"明天工作计划",date:"下午 06:30",complate:!1}]);return(a,s)=>{const _=p;return d(),t("div",i,[s[0]||(s[0]=l('<div class="card-header" data-v-32e88835><div class="title" data-v-32e88835><h4 class="box-title" data-v-32e88835>代办事项</h4><p class="subtitle" data-v-32e88835>待处理<span class="text-danger" data-v-32e88835>3</span></p></div></div>',1)),o("div",v,[(d(!0),t(c,null,m(u(e),((a,e)=>(d(),t("div",{key:e},[o("p",b,r(a.username),1),o("p",x,r(a.date),1),n(_,{modelValue:a.complate,"onUpdate:modelValue":e=>a.complate=e},null,8,["modelValue","onUpdate:modelValue"])])))),128))])])}}}),[["__scopeId","data-v-32e88835"]]);export{_ as default};
