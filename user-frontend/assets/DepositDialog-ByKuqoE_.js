import{_ as a}from"./index-F0f2Vd-3.js";/* empty css                 *//* empty css                       *//* empty css                 */import{d as e,r as l,X as t,j as s,p as o,N as r,o as n,w as i,b as u,Z as d,a as p,_ as m,C as c,K as _,b8 as f,b9 as y,e as v,y as b,u as h,ba as g,bb as w,b5 as V,c as k,F as x,A as j,H as C,x as F,aV as N,Y as U,M as q}from"./vendor-BVh5F9vp.js";import{B as z}from"./balanceApi-Bj92H6Oi.js";const D={class:"payment-option"},E={class:"payment-option"},S={class:"payment-option"},$={class:"quick-amounts"},A={class:"dialog-footer"},B=a(e({__name:"DepositDialog",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(a,{emit:e}){const B=a,M=e,P=l(),H=l(!1),I=l(!1),K=t({amount:"",payment_method:"alipay",transaction_password:""}),R=[50,100,200,500,1e3,2e3],X={amount:[{required:!0,message:"请输入充值金额",trigger:"blur"},{validator:(a,e,l)=>{const t=parseFloat(e);isNaN(t)||t<=0?l(new Error("充值金额必须大于0")):t>1e4?l(new Error("单次充值金额不得超过10,000元")):l()},trigger:"blur"}],payment_method:[{required:!0,message:"请选择支付方式",trigger:"change"}],transaction_password:[{required:!0,message:"请输入交易密码",trigger:"blur",validator:(a,e,l)=>{I.value&&!e?l(new Error("请输入交易密码")):l()}}]},Y=s({get:()=>B.visible,set:a=>M("update:visible",a)}),Z=()=>{return a=this,e=null,l=function*(){if(P.value)try{yield P.value.validate(),H.value=!0;const a=`${K.payment_method.toUpperCase()}_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,e={amount:parseFloat(K.amount).toFixed(2),payment_method:K.payment_method,transaction_id:a};yield z.deposit(e),q.success("充值成功"),M("success"),G()}catch(a){}finally{H.value=!1}},new Promise(((t,s)=>{var o=a=>{try{n(l.next(a))}catch(e){s(e)}},r=a=>{try{n(l.throw(a))}catch(e){s(e)}},n=a=>a.done?t(a.value):Promise.resolve(a.value).then(o,r);n((l=l.apply(a,e)).next())}));var a,e,l},G=()=>{var a;null==(a=P.value)||a.resetFields(),K.amount="",K.payment_method="alipay",K.transaction_password="",M("update:visible",!1)};return o(Y,(a=>{a||G()})),o((()=>K.amount),(a=>{const e=parseFloat(a);I.value=!isNaN(e)&&e>1e3})),(a,e)=>{const l=c,t=m,s=b,o=y,q=f,z=C,B=d,M=N,J=U;return n(),r(J,{modelValue:Y.value,"onUpdate:modelValue":e[3]||(e[3]=a=>Y.value=a),title:"账户充值",width:"500px","before-close":G},{footer:i((()=>[v("span",A,[u(z,{onClick:G},{default:i((()=>e[9]||(e[9]=[_("取消")]))),_:1}),u(z,{type:"primary",onClick:Z,loading:H.value},{default:i((()=>e[10]||(e[10]=[_(" 确认充值 ")]))),_:1},8,["loading"])])])),default:i((()=>[u(B,{ref_key:"formRef",ref:P,model:K,rules:X,"label-width":"100px",class:"deposit-form"},{default:i((()=>[u(t,{label:"充值金额",prop:"amount"},{default:i((()=>[u(l,{modelValue:K.amount,"onUpdate:modelValue":e[0]||(e[0]=a=>K.amount=a),placeholder:"请输入充值金额",type:"number",min:.01,step:.01},{prepend:i((()=>e[4]||(e[4]=[_("¥")]))),_:1},8,["modelValue"])])),_:1}),u(t,{label:"支付方式",prop:"payment_method"},{default:i((()=>[u(q,{modelValue:K.payment_method,"onUpdate:modelValue":e[1]||(e[1]=a=>K.payment_method=a)},{default:i((()=>[u(o,{value:"alipay"},{default:i((()=>[v("div",D,[u(s,{size:"20"},{default:i((()=>[u(h(g))])),_:1}),e[5]||(e[5]=v("span",null,"支付宝",-1))])])),_:1}),u(o,{value:"wechatpay"},{default:i((()=>[v("div",E,[u(s,{size:"20"},{default:i((()=>[u(h(w))])),_:1}),e[6]||(e[6]=v("span",null,"微信支付",-1))])])),_:1}),u(o,{value:"bankcard"},{default:i((()=>[v("div",S,[u(s,{size:"20"},{default:i((()=>[u(h(V))])),_:1}),e[7]||(e[7]=v("span",null,"银行卡",-1))])])),_:1})])),_:1},8,["modelValue"])])),_:1}),u(t,{label:"快捷选择"},{default:i((()=>[v("div",$,[(n(),k(x,null,j(R,(a=>u(z,{key:a,type:K.amount===a.toString()?"primary":"default",size:"small",onClick:e=>(a=>{K.amount=a.toString()})(a)},{default:i((()=>[_(" ¥"+F(a),1)])),_:2},1032,["type","onClick"]))),64))])])),_:1}),I.value?(n(),r(t,{key:0,label:"交易密码",prop:"transaction_password"},{default:i((()=>[u(l,{modelValue:K.transaction_password,"onUpdate:modelValue":e[2]||(e[2]=a=>K.transaction_password=a),type:"password",placeholder:"请输入交易密码","show-password":""},null,8,["modelValue"])])),_:1})):p("",!0)])),_:1},8,["model"]),u(M,{title:"充值说明",type:"info",closable:!1,"show-icon":""},{default:i((()=>e[8]||(e[8]=[v("ul",{class:"deposit-tips"},[v("li",null,"充值金额将实时到账，可用于支付快递费用"),v("li",null,"单次充值金额不得超过10,000元"),v("li",null,"充值成功后，资金将进入您的账户余额"),v("li",null,"如有疑问，请联系客服")],-1)]))),_:1})])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-9c9ff7f5"]]);export{B as default};
