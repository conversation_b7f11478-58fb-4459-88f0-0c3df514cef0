import s from"./ExpressDashboard-CY2QtQJL.js";import{u as o,s as r,_ as e}from"./index-F0f2Vd-3.js";import{d as a,j as t,p,c as i,b as m,o as d}from"./vendor-BVh5F9vp.js";/* empty css                       *//* empty css                        *//* empty css               *//* empty css                */import"./expressApi-BL8dLu27.js";import"./balanceApi-Bj92H6Oi.js";import"./index-CdJNdp1H.js";import"./install-DmVoiIn1.js";const j={class:"console"},n=e(a({__name:"index",setup(e){const a=o(),n=t((()=>a.systemThemeType));return p(n,(()=>{a.reload()})),r(),(o,r)=>(d(),i("div",j,[m(s)]))}}),[["__scopeId","data-v-088efe81"]]);export{n as default};
