var e=Object.defineProperty,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,s=(t,o,r)=>o in t?e(t,o,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[o]=r,i=(e,i)=>{for(var l in i||(i={}))o.call(i,l)&&s(e,l,i[l]);if(t)for(var l of t(i))r.call(i,l)&&s(e,l,i[l]);return e};import"./index-CdJNdp1H.js";import{h as l,u as n,S as a}from"./index-F0f2Vd-3.js";import{j as p,p as u,r as c,f as h,s as f}from"./vendor-BVh5F9vp.js";import{i as m}from"./install-DmVoiIn1.js";const y=()=>({chartHeight:"16rem",fontSize:13,fontColor:"#999",themeColor:l("--el-color-primary-light-1")});function d(e){const t=p((()=>n().systemThemeType)),o=p((()=>t.value===a.DARK)),r=n(),s=p((()=>r.menuOpen));u(s,(()=>{[100,200,300].forEach((e=>{setTimeout((()=>{v()}),e)}))}));const l=c();let d=null;const v=()=>{null==d||d.resize()};return h((()=>{window.addEventListener("resize",v)})),f((()=>{null==d||d.dispose(),window.removeEventListener("resize",v)})),{isDark:o,chartRef:l,initChart:(t={})=>{l.value&&(d=m(l.value),d.setOption(i(i({},e),t)))},updateChart:e=>{null==d||d.setOption(e)},handleResize:v,getAxisLineStyle:(e=!0)=>({show:e,lineStyle:{color:o.value?"#444":"#e8e8e8",width:1}}),getSplitLineStyle:(e=!0)=>({show:e,lineStyle:{color:o.value?"#444":"#e8e8e8",width:1,type:"dashed"}}),getAxisLabelStyle:()=>({show:!0,color:y().fontColor,fontSize:y().fontSize}),getAxisTickStyle:()=>({show:!1}),useChartOps:y}}export{y as a,d as u};
