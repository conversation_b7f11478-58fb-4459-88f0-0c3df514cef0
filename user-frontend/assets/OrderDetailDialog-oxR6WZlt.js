var e=Object.defineProperty,a=(a,t,i)=>((a,t,i)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):a[t]=i)(a,"symbol"!=typeof t?t+"":t,i),t=(e,a,t)=>new Promise(((i,s)=>{var n=e=>{try{r(t.next(e))}catch(a){s(a)}},l=e=>{try{r(t.throw(e))}catch(a){s(a)}},r=e=>e.done?i(e.value):Promise.resolve(e.value).then(n,l);r((t=t.apply(e,a)).next())}));import{f as i,_ as s}from"./index-F0f2Vd-3.js";/* empty css                         *//* empty css                *//* empty css                   *//* empty css                             */import{d as n,r as l,j as r,p as c,c as o,o as d,b as u,w as v,a2 as _,a as p,aR as f,aY as m,N as g,aW as h,K as y,x as b,aX as w,e as k,v as T,bs as x,H as O,y as S,u as C,by as D,bv as j,bw as Y,br as E,bz as N,bA as F,F as B,A,bB as J,bC as z,aI as L,aG as M,Y as Z,M as H}from"./vendor-BVh5F9vp.js";import{E as R}from"./expressApi-BL8dLu27.js";import{E as I}from"./errorHandler-Dnd-hi0l.js";import P from"./CreateWorkOrderDialog-C4e8e6Tj.js";class U{static getSystemConfig(e=!1){return t(this,null,(function*(){const a=Date.now();if(!e&&this.configCache&&a<this.cacheExpiry)return this.configCache;try{const e=yield i.get({url:"/api/v1/system/config"});return e.success&&e.data?(this.configCache=e.data,this.cacheExpiry=a+this.CACHE_DURATION,e.data):this.getDefaultConfig()}catch(t){return this.getDefaultConfig()}}))}static getExpressCompanies(){return t(this,null,(function*(){return(yield this.getSystemConfig()).express_companies.filter((e=>e.is_active))}))}static getOrderStatuses(){return t(this,null,(function*(){return(yield this.getSystemConfig()).order_statuses}))}static getProviders(){return t(this,null,(function*(){return(yield this.getSystemConfig()).providers.filter((e=>e.is_active))}))}static getPaymentMethods(){return t(this,null,(function*(){return(yield this.getSystemConfig()).payment_methods.filter((e=>e.is_active))}))}static getRegions(e){return t(this,null,(function*(){const a=yield this.getSystemConfig();if(!e)return a.regions.filter((e=>1===e.level));const t=this.findRegionByCode(a.regions,e);return(null==t?void 0:t.children)||[]}))}static findRegionByCode(e,a){for(const t of e){if(t.code===a)return t;if(t.children){const e=this.findRegionByCode(t.children,a);if(e)return e}}}static clearCache(){this.configCache=null,this.cacheExpiry=0}static getDefaultConfig(){return{express_companies:[{code:"SF",name:"顺丰速运",is_active:!0,supported_services:["标准快递","特快专递"],weight_limit:50},{code:"ZTO",name:"中通快递",is_active:!0,supported_services:["标准快递"],weight_limit:50},{code:"YTO",name:"圆通速递",is_active:!0,supported_services:["标准快递"],weight_limit:50},{code:"STO",name:"申通快递",is_active:!0,supported_services:["标准快递"],weight_limit:50},{code:"YD",name:"韵达速递",is_active:!0,supported_services:["标准快递"],weight_limit:50},{code:"JT",name:"极兔快递",is_active:!0,supported_services:["标准快递"],weight_limit:50},{code:"JD",name:"京东物流",is_active:!0,supported_services:["标准快递","次日达"],weight_limit:50},{code:"DBL",name:"德邦物流",is_active:!0,supported_services:["标准快递","大件快递"],weight_limit:100},{code:"DBL360",name:"德邦大件360",is_active:!0,supported_services:["大件快递"],weight_limit:500},{code:"EMS",name:"中国邮政",is_active:!0,supported_services:["标准快递","特快专递"],weight_limit:50},{code:"HTKY",name:"百世快递",is_active:!0,supported_services:["标准快递"],weight_limit:50}],order_statuses:[{code:"created",name:"已创建",description:"订单已创建",color:"info",is_final:!1},{code:"submitted",name:"已提交",description:"订单已提交",color:"primary",is_final:!1},{code:"submit_failed",name:"提交失败",description:"订单提交失败",color:"danger",is_final:!0},{code:"print_failed",name:"面单生成失败",description:"面单生成失败",color:"danger",is_final:!0},{code:"assigned",name:"已分配",description:"已分配网点或快递员",color:"info",is_final:!1},{code:"awaiting_pickup",name:"等待揽收",description:"等待快递员揽收",color:"warning",is_final:!1},{code:"picked_up",name:"已揽收",description:"快递员已揽收",color:"primary",is_final:!1},{code:"pickup_failed",name:"揽收失败",description:"快递员揽收失败",color:"danger",is_final:!1},{code:"in_transit",name:"运输中",description:"包裹正在运输",color:"info",is_final:!1},{code:"out_for_delivery",name:"派送中",description:"包裹正在派送",color:"info",is_final:!1},{code:"delivered",name:"已签收",description:"包裹已签收",color:"success",is_final:!1},{code:"delivered_abnormal",name:"异常签收",description:"异常签收",color:"warning",is_final:!1},{code:"billed",name:"已计费",description:"订单已计费完成",color:"success",is_final:!0},{code:"exception",name:"异常",description:"订单异常",color:"danger",is_final:!1},{code:"returned",name:"已退回",description:"包裹已退回",color:"warning",is_final:!1},{code:"forwarded",name:"已转寄",description:"包裹已转寄",color:"info",is_final:!1},{code:"cancelled",name:"已取消",description:"订单已取消",color:"danger",is_final:!0},{code:"canceled",name:"已取消",description:"订单已取消",color:"danger",is_final:!0},{code:"voided",name:"已作废",description:"订单已作废",color:"danger",is_final:!0},{code:"weight_updated",name:"重量更新",description:"订单重量已更新",color:"info",is_final:!1},{code:"revived",name:"订单复活",description:"订单已复活",color:"primary",is_final:!1}],providers:[{code:"kuaidi100",name:"快递100",is_active:!0,supported_companies:["SF","ZTO","YTO","STO"],api_version:"v1"},{code:"yida",name:"易达",is_active:!0,supported_companies:["SF","ZTO","YTO"],api_version:"v1"},{code:"yuntong",name:"云通",is_active:!0,supported_companies:["ZTO","YTO","STO","YD"],api_version:"v1"}],payment_methods:[{code:0,name:"寄付",description:"寄件人付费",is_active:!0},{code:1,name:"到付",description:"收件人付费",is_active:!0},{code:2,name:"月结",description:"月结账户",is_active:!0}],regions:[{code:"110000",name:"北京市",level:1,children:[{code:"110100",name:"北京市",level:2,parent_code:"110000",children:[{code:"110101",name:"东城区",level:3,parent_code:"110100"},{code:"110102",name:"西城区",level:3,parent_code:"110100"},{code:"110105",name:"朝阳区",level:3,parent_code:"110100"},{code:"110106",name:"丰台区",level:3,parent_code:"110100"}]}]},{code:"310000",name:"上海市",level:1,children:[{code:"310100",name:"上海市",level:2,parent_code:"310000",children:[{code:"310101",name:"黄浦区",level:3,parent_code:"310100"},{code:"310104",name:"徐汇区",level:3,parent_code:"310100"},{code:"310105",name:"长宁区",level:3,parent_code:"310100"}]}]},{code:"330000",name:"浙江省",level:1,children:[{code:"330100",name:"杭州市",level:2,parent_code:"330000",children:[{code:"330102",name:"上城区",level:3,parent_code:"330100"},{code:"330103",name:"下城区",level:3,parent_code:"330100"},{code:"330106",name:"西湖区",level:3,parent_code:"330100"}]}]}]}}}a(U,"configCache",null),a(U,"cacheExpiry",0),a(U,"CACHE_DURATION",18e5);const q={"element-loading-text":"正在加载订单详情..."},K={key:0,class:"order-detail"},V={class:"fee-amount overweight"},W={class:"fee-amount underweight"},G={key:0,class:"after-sales-section"},X={class:"after-sales-actions"},$={class:"card-header-with-icon"},Q={class:"contact-info"},ee={class:"contact-row"},ae={class:"contact-value"},te={class:"contact-row"},ie={class:"contact-value"},se={class:"contact-row"},ne={class:"contact-value address-value"},le={class:"card-header-with-icon"},re={class:"contact-info"},ce={class:"contact-row"},oe={class:"contact-value"},de={class:"contact-row"},ue={class:"contact-value"},ve={class:"contact-row"},_e={class:"contact-value address-value"},pe={class:"track-item"},fe={class:"track-location"},me={class:"track-description"},ge={key:1,class:"track-loading"},he={key:1,class:"no-data"},ye={class:"dialog-footer"},be=s(n({__name:"OrderDetailDialog",props:{visible:{type:Boolean},order:{}},emits:["update:visible"],setup(e,{emit:a}){const i=e,s=a,n=l(!1),be=l(!1),we=l(null),ke=l([]),Te=l(!1),xe=l(null),Oe=r({get:()=>i.visible,set:e=>s("update:visible",e)}),Se=()=>t(this,null,(function*(){try{ke.value=yield U.getOrderStatuses()}catch(e){I.handleApiError(e,!1)}})),Ce=()=>t(this,null,(function*(){if(i.order){be.value=!0;try{const e=yield R.queryOrder({order_no:i.order.order_no});if(!e.success||!e.data)throw new Error(e.message||"获取订单详情失败");we.value=e.data,yield De()}catch(e){I.handleApiError(e),we.value=null}finally{be.value=!1}}})),De=()=>t(this,null,(function*(){if(i.order&&we.value)try{const e=yield R.queryTrack({tracking_no:i.order.tracking_no,express_type:i.order.express_type});e.success&&e.data&&we.value&&(we.value.tracks=e.data.tracks,we.value.status=e.data.status,we.value.status_desc=e.data.status_desc)}catch(e){}})),je=()=>t(this,null,(function*(){if(i.order){n.value=!0;try{const e=yield R.queryTrack({tracking_no:i.order.tracking_no,express_type:i.order.express_type});if(!e.success||!e.data)throw new Error(e.message||"刷新轨迹失败");we.value&&(we.value.tracks=e.data.tracks,we.value.status=e.data.status,we.value.status_desc=e.data.status_desc),H.success("轨迹信息已更新")}catch(e){I.handleApiError(e)}finally{n.value=!1}}})),Ye=e=>{const a=ke.value.find((a=>a.code===e));if(null==a?void 0:a.color)return a.color;return{created:"info",submitted:"primary",assigned:"info",awaiting_pickup:"warning",picked_up:"primary",in_transit:"info",out_for_delivery:"info",delivered:"success",failed_delivery:"danger",returned:"warning",cancelling:"warning",cancelled:"danger",canceled:"danger",processing:"primary",billed:"success",voided:"danger",exception:"danger"}[e]||"info"},Ee=e=>{if(!e)return"-";if(e.express_name&&!Ne(e.express_name))return e.express_name;const a=e.express_type||e.express_name;return{shentong:"申通快递",yuantong:"圆通快递",zhongtong:"中通快递",yunda:"韵达快递",jtexpress:"极兔快递",jd:"京东快递",debangkuaidi:"德邦快递",debangwuliu:"德邦物流",ems:"EMS",sf:"顺丰快递",ST:"申通快递",YT:"圆通快递",ZT:"中通快递",YD:"韵达快递",JT:"极兔快递",JD:"京东物流","STO-INT":"申通快递",YTO:"圆通快递",ZTO:"中通快递",YUND:"韵达快递",SF:"顺丰速运",STO:"申通快递",DBL:"德邦物流",DBL360:"德邦大件360",EMS:"中国邮政",HTKY:"百世快递"}[a]||a||"-"},Ne=e=>{const a=["shentong","yuantong","zhongtong","yunda","jtexpress","jd","debangkuaidi","debangwuliu","ems","sf","ST","YT","ZT","YD","JT","JD","STO-INT","YTO","ZTO","YUND","SF","STO","DBL","DBL360","EMS","HTKY"];return a.includes(e)||a.includes(e.toLowerCase())},Fe=e=>{const a="string"==typeof e?parseFloat(e):e;return isNaN(a)?"0.00":a.toFixed(2)},Be=e=>{if(null==e)return"0.0";const a="string"==typeof e?parseFloat(e):e;return isNaN(a)?"0.0":a.toFixed(1)},Ae=e=>{if(null==e)return"0.00cm³";const a="string"==typeof e?parseFloat(e):e;if(isNaN(a))return"0.00cm³";return`${(1e6*a).toFixed(2)}cm³`},Je=e=>{if(!e)return 0;return(e.actual_fee||0)-(e.price||0)},ze=e=>{if(!e)return!1;const a=e.actual_fee||0,t=e.price||0;return a>0&&a!==t},Le=e=>{if(!e)return"-";try{return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return e}},Me=e=>{if(!e)return null;if("string"==typeof e)try{return JSON.parse(e)}catch(a){return null}return"object"==typeof e?e:null},Ze=e=>{const a=Me(null==e?void 0:e.sender_info);return(null==a?void 0:a.name)||"-"},He=e=>{const a=Me(null==e?void 0:e.sender_info);return(null==a?void 0:a.mobile)||(null==a?void 0:a.phone)||"-"},Re=e=>{const a=Me(null==e?void 0:e.sender_info);if(!a)return"-";const t=[];return a.province&&t.push(a.province),a.city&&t.push(a.city),a.district&&t.push(a.district),a.address&&t.push(a.address),t.length>0?t.join(" "):"-"},Ie=e=>{const a=Me(null==e?void 0:e.receiver_info);return(null==a?void 0:a.name)||"-"},Pe=e=>{const a=Me(null==e?void 0:e.receiver_info);return(null==a?void 0:a.mobile)||(null==a?void 0:a.phone)||"-"},Ue=e=>{const a=Me(null==e?void 0:e.receiver_info);if(!a)return"-";const t=[];return a.province&&t.push(a.province),a.city&&t.push(a.city),a.district&&t.push(a.district),a.address&&t.push(a.address),t.length>0?t.join(" "):"-"},qe=()=>{if(!we.value)return!1;return!["cancelled","canceled","cancelling","voided"].includes(we.value.status)},Ke=()=>{i.order&&we.value&&(xe.value={id:0,customer_order_no:we.value.order_no,order_no:we.value.order_no,tracking_no:we.value.tracking_no,express_type:we.value.express_type,express_name:Ee(we.value),provider:"",provider_name:"快递服务商",status:we.value.status,status_desc:we.value.status_desc,weight:we.value.weight,price:we.value.price,actual_fee:we.value.actual_fee,insurance_fee:we.value.insurance_fee,overweight_fee:we.value.overweight_fee,underweight_fee:we.value.underweight_fee,weight_adjustment_reason:we.value.weight_adjustment_reason,billing_status:we.value.billing_status,sender_info:JSON.stringify(we.value.sender_info),receiver_info:JSON.stringify(we.value.receiver_info),order_volume:we.value.order_volume,actual_weight:we.value.actual_weight,actual_volume:we.value.actual_volume,charged_weight:we.value.charged_weight,created_at:we.value.created_at,updated_at:we.value.updated_at},Te.value=!0)},Ve=()=>{Te.value=!1,xe.value=null,H.success("售后申请已提交，我们将尽快为您处理")},We=e=>{we.value=null,Te.value=!1,xe.value=null,s("update:visible",!1),e&&e()};return c([Oe,()=>i.order],(e=>t(this,[e],(function*([e,a]){e&&a&&(yield Se(),yield Ce())})))),(e,a)=>{const t=h,i=w,s=m,l=x,r=S,c=O,H=f,R=J,I=F,U=L,ke=Z,Se=M;return d(),o(B,null,[u(ke,{modelValue:Oe.value,"onUpdate:modelValue":a[0]||(a[0]=e=>Oe.value=e),title:"订单详情",width:"800px","before-close":We,"close-on-click-modal":!1,"close-on-press-escape":!0},{footer:v((()=>[k("span",ye,[u(c,{onClick:We},{default:v((()=>a[17]||(a[17]=[y("关闭")]))),_:1})])])),default:v((()=>[_((d(),o("div",q,[we.value?(d(),o("div",K,[u(H,{class:"info-card"},{header:v((()=>a[2]||(a[2]=[k("span",null,"基本信息",-1)]))),default:v((()=>[u(s,{column:2,border:""},{default:v((()=>[u(t,{label:"订单号"},{default:v((()=>[y(b(we.value.order_no),1)])),_:1}),u(t,{label:"运单号"},{default:v((()=>[y(b(we.value.tracking_no),1)])),_:1}),u(t,{label:"快递公司"},{default:v((()=>[y(b(Ee(we.value)),1)])),_:1}),u(t,{label:"订单状态"},{default:v((()=>[u(i,{type:Ye(we.value.status)},{default:v((()=>{return[y(b((e=we.value.status,{submitted:"已提交",submit_failed:"提交失败",print_failed:"面单生成失败",assigned:"已分配",awaiting_pickup:"等待揽收",picked_up:"已揽收",pickup_failed:"揽收失败",in_transit:"运输中",out_for_delivery:"派送中",delivered:"已签收",delivered_abnormal:"异常签收",billed:"已计费",exception:"异常",returned:"已退回",forwarded:"已转寄",cancelling:"取消中",cancelled:"已取消",voided:"已作废",weight_updated:"重量更新",revived:"订单复活",failed_delivery:"派送失败",canceled:"已取消",processing:"处理中",created:"已创建"}[e]||e)),1)];var e})),_:1},8,["type"])])),_:1}),u(t,{label:"下单重量"},{default:v((()=>[y(b(Be(we.value.weight))+"kg",1)])),_:1}),u(t,{label:"下单体积"},{default:v((()=>[y(b(Ae(we.value.order_volume)),1)])),_:1}),u(t,{label:"实际重量"},{default:v((()=>[y(b(Be(we.value.actual_weight))+"kg",1)])),_:1}),u(t,{label:"实际体积"},{default:v((()=>[y(b(Ae(we.value.actual_volume)),1)])),_:1}),u(t,{label:"计费重量"},{default:v((()=>[y(b(Be(we.value.charged_weight))+"kg",1)])),_:1}),u(t,{label:"预收"},{default:v((()=>[y("¥"+b(Fe(we.value.price||0)),1)])),_:1}),u(t,{label:"实收金额"},{default:v((()=>[y("¥"+b(Fe(we.value.actual_fee||0)),1)])),_:1}),u(t,{label:"保价费"},{default:v((()=>[y("¥"+b(Fe(we.value.insurance_fee||0)),1)])),_:1}),we.value.overweight_fee>0?(d(),g(t,{key:0,label:"超重费用"},{default:v((()=>[k("span",V,"¥"+b(Fe(we.value.overweight_fee)),1)])),_:1})):p("",!0),we.value.underweight_fee>0?(d(),g(t,{key:1,label:"超轻费用"},{default:v((()=>[k("span",W,"¥"+b(Fe(we.value.underweight_fee)),1)])),_:1})):p("",!0),we.value.weight_adjustment_reason?(d(),g(t,{key:2,label:"重量调整原因"},{default:v((()=>[u(i,{type:"info",size:"small"},{default:v((()=>[y(b(we.value.weight_adjustment_reason),1)])),_:1})])),_:1})):p("",!0),ze(we.value)?(d(),g(t,{key:3,label:"费用差额"},{default:v((()=>[k("span",{class:T(["fee-difference",Je(we.value)>0?"positive":"negative"])},b(Je(we.value)>0?"+":"")+"¥"+b(Fe(Math.abs(Je(we.value)))),3)])),_:1})):p("",!0),u(t,{label:"创建时间"},{default:v((()=>[y(b(Le(we.value.created_at)),1)])),_:1}),u(t,{label:"更新时间"},{default:v((()=>[y(b(Le(we.value.updated_at)),1)])),_:1})])),_:1}),qe()?(d(),o("div",G,[u(l,{"content-position":"left"},{default:v((()=>a[3]||(a[3]=[y("售后服务")]))),_:1}),k("div",X,[u(c,{type:"warning",onClick:Ke,size:"small"},{default:v((()=>[u(r,null,{default:v((()=>[u(C(D))])),_:1}),a[4]||(a[4]=y(" 申请售后服务 "))])),_:1}),a[5]||(a[5]=k("span",{class:"action-tip"},"如遇包裹丢失、损坏、派送异常等问题，可申请售后服务",-1))])])):p("",!0)])),_:1}),u(H,{class:"info-card"},{header:v((()=>[k("div",$,[u(r,{class:"header-icon sender-icon"},{default:v((()=>[u(C(j))])),_:1}),a[6]||(a[6]=k("span",null,"寄件人信息",-1))])])),default:v((()=>[k("div",Q,[k("div",ee,[u(r,{class:"contact-icon"},{default:v((()=>[u(C(j))])),_:1}),a[7]||(a[7]=k("span",{class:"contact-label"},"姓名",-1)),k("span",ae,b(Ze(we.value)),1)]),k("div",te,[u(r,{class:"contact-icon"},{default:v((()=>[u(C(Y))])),_:1}),a[8]||(a[8]=k("span",{class:"contact-label"},"电话",-1)),k("span",ie,b(He(we.value)),1)]),k("div",se,[u(r,{class:"contact-icon"},{default:v((()=>[u(C(E))])),_:1}),a[9]||(a[9]=k("span",{class:"contact-label"},"地址",-1)),k("span",ne,b(Re(we.value)),1)])])])),_:1}),u(H,{class:"info-card"},{header:v((()=>[k("div",le,[u(r,{class:"header-icon receiver-icon"},{default:v((()=>[u(C(N))])),_:1}),a[10]||(a[10]=k("span",null,"收件人信息",-1))])])),default:v((()=>[k("div",re,[k("div",ce,[u(r,{class:"contact-icon"},{default:v((()=>[u(C(N))])),_:1}),a[11]||(a[11]=k("span",{class:"contact-label"},"姓名",-1)),k("span",oe,b(Ie(we.value)),1)]),k("div",de,[u(r,{class:"contact-icon"},{default:v((()=>[u(C(Y))])),_:1}),a[12]||(a[12]=k("span",{class:"contact-label"},"电话",-1)),k("span",ue,b(Pe(we.value)),1)]),k("div",ve,[u(r,{class:"contact-icon"},{default:v((()=>[u(C(E))])),_:1}),a[13]||(a[13]=k("span",{class:"contact-label"},"地址",-1)),k("span",_e,b(Ue(we.value)),1)])])])),_:1}),u(H,{class:"info-card"},{header:v((()=>[a[15]||(a[15]=k("span",null,"物流轨迹",-1)),u(c,{type:"primary",size:"small",onClick:je,loading:n.value},{default:v((()=>a[14]||(a[14]=[y(" 刷新轨迹 ")]))),_:1},8,["loading"])])),default:v((()=>[we.value.tracks&&we.value.tracks.length>0?(d(),g(I,{key:0},{default:v((()=>[(d(!0),o(B,null,A(we.value.tracks,((e,a)=>(d(),g(R,{key:a,timestamp:Le(e.time),placement:"top"},{default:v((()=>[k("div",pe,[k("div",fe,b(e.location),1),k("div",me,b(e.context||e.description),1)])])),_:2},1032,["timestamp"])))),128))])),_:1})):be.value?(d(),o("div",ge,[u(r,{class:"is-loading"},{default:v((()=>[u(C(z))])),_:1}),a[16]||(a[16]=k("span",null,"正在加载物流轨迹...",-1))])):(d(),g(U,{key:2,description:"暂无物流轨迹信息"}))])),_:1})])):be.value?p("",!0):(d(),o("div",he,[u(U,{description:"无法获取订单详情"})]))])),[[Se,be.value]])])),_:1},8,["modelValue"]),u(P,{visible:Te.value,"onUpdate:visible":a[1]||(a[1]=e=>Te.value=e),"prefilled-order-data":xe.value,onSuccess:Ve},null,8,["visible","prefilled-order-data"])],64)}}}),[["__scopeId","data-v-9704006e"]]),we=Object.freeze(Object.defineProperty({__proto__:null,default:be},Symbol.toStringTag,{value:"Module"}));export{U as C,be as O,we as a};
