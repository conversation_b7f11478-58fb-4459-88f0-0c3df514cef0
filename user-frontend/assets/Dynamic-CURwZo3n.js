import{d as a,X as s,c as e,o as t,bn as r,e as c,F as d,A as n,x as p,u as i}from"./vendor-BVh5F9vp.js";import{_ as l}from"./index-F0f2Vd-3.js";const u={class:"region dynamic art-custom-card"},m={class:"list"},v={class:"user"},o={class:"type"},y={class:"target"},g=l(a({__name:"Dynamic",setup(a){const l=s([{username:"中小鱼",type:"关注了",target:"誶誶淰"},{username:"何小荷",type:"发表文章",target:"Vue3 + Typescript + Vite 项目实战笔记"},{username:"誶誶淰",type:"提出问题",target:"主题可以配置吗"},{username:"发呆草",type:"兑换了物品",target:"《奇特的一生》"},{username:"甜筒",type:"关闭了问题",target:"发呆草"},{username:"冷月呆呆",type:"兑换了物品",target:"《高效人士的七个习惯》"}]);return(a,s)=>(t(),e("div",u,[s[0]||(s[0]=r('<div class="card-header" data-v-25c4d2a6><div class="title" data-v-25c4d2a6><h4 class="box-title" data-v-25c4d2a6>动态</h4><p class="subtitle" data-v-25c4d2a6>新增<span class="text-success" data-v-25c4d2a6>+6</span></p></div></div>',1)),c("div",m,[(t(!0),e(d,null,n(i(l),((a,s)=>(t(),e("div",{key:s},[c("span",v,p(a.username),1),c("span",o,p(a.type),1),c("span",y,p(a.target),1)])))),128))])]))}}),[["__scopeId","data-v-25c4d2a6"]]);export{g as default};
