import{u as a,m as e,j as s,k as l,l as i,n,t,v as o,_ as r}from"./index-F0f2Vd-3.js";/* empty css               */import{d,r as c,j as m,f as u,z as v,c as p,o as f,b as g,e as h,w as y,aN as _,aQ as T,B as k,x as b,C as j,K as x,y as M,ah as w,aj as A,ag as B,F as C,A as V,v as z,aa as R,H as I,u as D,I as K,J as S,D as H,G as J,n as N}from"./vendor-BVh5F9vp.js";const U={class:"person-list"},q={class:"person-item-header"},F={class:"user-info"},G={class:"user-details"},L={class:"name"},Q={class:"email"},W={class:"search-box"},$={class:"sort-btn"},E=["onClick"],O={class:"avatar-wrapper"},P={class:"person-info"},X={class:"info-top"},Y={class:"person-name"},Z={class:"last-time"},aa={class:"info-bottom"},ea={class:"email"},sa={class:"chat-modal"},la={class:"header"},ia={class:"header-left"},na={class:"status"},ta={class:"status-text"},oa={class:"chat-container"},ra={class:"message-content"},da={class:"message-info"},ca={class:"sender-name"},ma={class:"message-time"},ua={class:"message-text"},va={class:"chat-input"},pa={class:"input-actions"},fa={class:"chat-input-actions"},ga=r(d({__name:"chat",setup(r){const d=c(""),ga=c(!1),ha=c(!0),ya=c(null),_a=a(),Ta=m((()=>_a.showWorkTab)),ka=m((()=>`calc(100vh - ${Ta.value?120:75}px)`)),ba=c([{id:1,name:"梅洛迪·梅西",email:"<EMAIL>",avatar:e,online:!0,lastTime:"20小时前",unread:0},{id:2,name:"马克·史密斯",email:"<EMAIL>",avatar:s,online:!0,lastTime:"2周前",unread:6},{id:3,name:"肖恩·宾",email:"<EMAIL>",avatar:l,online:!1,lastTime:"5小时前",unread:5},{id:4,name:"爱丽丝·约翰逊",email:"<EMAIL>",avatar:i,online:!0,lastTime:"1小时前",unread:2},{id:5,name:"鲍勃·布朗",email:"<EMAIL>",avatar:e,online:!1,lastTime:"3天前",unread:1},{id:6,name:"查理·戴维斯",email:"<EMAIL>",avatar:n,online:!0,lastTime:"10分钟前",unread:0},{id:7,name:"戴安娜·普林斯",email:"<EMAIL>",avatar:"/art-design-pro/assets/avatar7-CuqhjfmI.jpg",online:!0,lastTime:"15分钟前",unread:3},{id:8,name:"伊桑·亨特",email:"<EMAIL>",avatar:"/art-design-pro/assets/avatar8-BJnkVz00.jpg",online:!0,lastTime:"5分钟前",unread:0},{id:9,name:"杰西卡·琼斯",email:"<EMAIL>",avatar:"/art-design-pro/assets/avatar9-BnDTSgyN.jpg",online:!1,lastTime:"1天前",unread:4},{id:10,name:"彼得·帕克",email:"<EMAIL>",avatar:t,online:!0,lastTime:"2小时前",unread:1},{id:11,name:"克拉克·肯特",email:"<EMAIL>",avatar:l,online:!0,lastTime:"30分钟前",unread:2},{id:12,name:"布鲁斯·韦恩",email:"<EMAIL>",avatar:e,online:!1,lastTime:"3天前",unread:0},{id:13,name:"韦德·威尔逊",email:"<EMAIL>",avatar:n,online:!0,lastTime:"10分钟前",unread:5}]),ja=c(""),xa=c([{id:1,sender:"Art Bot",content:"你好！我是你的AI助手，有什么我可以帮你的吗？",time:"10:00",isMe:!1,avatar:t},{id:2,sender:"Ricky",content:"我想了解一下系统的使用方法。",time:"10:01",isMe:!0,avatar:e},{id:3,sender:"Art Bot",content:"好的，我来为您介绍系统的主要功能。首先，您可以通过左侧菜单访问不同的功能模块...",time:"10:02",isMe:!1,avatar:t},{id:4,sender:"Ricky",content:"听起来很不错，能具体讲讲数据分析部分吗？",time:"10:05",isMe:!0,avatar:e},{id:5,sender:"Art Bot",content:"当然可以。数据分析模块可以帮助您实时监控关键指标，并生成详细的报表...",time:"10:06",isMe:!1,avatar:t},{id:6,sender:"Ricky",content:"太好了，那我如何开始使用呢？",time:"10:08",isMe:!0,avatar:e},{id:7,sender:"Art Bot",content:"您可以先创建一个项目，然后在项目中添加相关的数据源，系统会自动进行分析。",time:"10:09",isMe:!1,avatar:t},{id:8,sender:"Ricky",content:"明白了，谢谢你的帮助！",time:"10:10",isMe:!0,avatar:e},{id:9,sender:"Art Bot",content:"不客气，有任何问题随时联系我。",time:"10:11",isMe:!1,avatar:t}]),Ma=c(10),wa=c(e),Aa=()=>{const a=ja.value.trim();a&&(xa.value.push({id:Ma.value++,sender:"Ricky",content:a,time:(new Date).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),isMe:!0,avatar:wa.value}),ja.value="",Ca())},Ba=c(null),Ca=()=>{setTimeout((()=>{Ba.value&&(Ba.value.scrollTop=Ba.value.scrollHeight)}),100)},Va=()=>{ga.value=!0};return u((()=>{Ca(),o.on("openChat",Va),ya.value=ba.value[0]})),(a,e)=>{var s,l,i;const n=_,t=T,o=k,r=j,c=v("arrow-down"),m=M,u=A,ga=w,_a=B,Ta=R,Ma=I;return f(),p("div",{class:"chat",style:N({height:D(ka)})},[g(t,null,{default:y((()=>[g(n,{span:12},{default:y((()=>e[2]||(e[2]=[h("div",{class:"grid-content ep-bg-purple"},null,-1)]))),_:1}),g(n,{span:12},{default:y((()=>e[3]||(e[3]=[h("div",{class:"grid-content ep-bg-purple-light"},null,-1)]))),_:1})])),_:1}),h("div",U,[h("div",q,[h("div",F,[g(o,{size:50,src:null==(s=ya.value)?void 0:s.avatar},null,8,["src"]),h("div",G,[h("div",L,b(null==(l=ya.value)?void 0:l.name),1),h("div",Q,b(null==(i=ya.value)?void 0:i.email),1)])]),h("div",W,[g(r,{modelValue:d.value,"onUpdate:modelValue":e[0]||(e[0]=a=>d.value=a),placeholder:"搜索联系人","prefix-icon":"Search",clearable:""},null,8,["modelValue"])]),g(_a,{trigger:"click",placement:"bottom-start"},{dropdown:y((()=>[g(ga,null,{default:y((()=>[g(u,null,{default:y((()=>e[5]||(e[5]=[x("按时间排序")]))),_:1}),g(u,null,{default:y((()=>e[6]||(e[6]=[x("按名称排序")]))),_:1}),g(u,null,{default:y((()=>e[7]||(e[7]=[x("全部标为已读")]))),_:1})])),_:1})])),default:y((()=>[h("span",$,[e[4]||(e[4]=x(" 排序方式 ")),g(m,{class:"el-icon--right"},{default:y((()=>[g(c)])),_:1})])])),_:1})]),g(Ta,null,{default:y((()=>[(f(!0),p(C,null,V(ba.value,(a=>{var e;return f(),p("div",{key:a.id,class:z(["person-item",{active:(null==(e=ya.value)?void 0:e.id)===a.id}]),onClick:e=>{return s=a,void(ya.value=s);var s}},[h("div",O,[g(o,{size:40,src:a.avatar},{default:y((()=>[x(b(a.name.charAt(0)),1)])),_:2},1032,["src"]),h("div",{class:z(["status-dot",{online:a.online}])},null,2)]),h("div",P,[h("div",X,[h("span",Y,b(a.name),1),h("span",Z,b(a.lastTime),1)]),h("div",aa,[h("span",ea,b(a.email),1)])])],10,E)})),128))])),_:1})]),h("div",sa,[h("div",la,[h("div",ia,[e[8]||(e[8]=h("span",{class:"name"},"Art Bot",-1)),h("div",na,[h("div",{class:z(["dot",{online:ha.value,offline:!ha.value}])},null,2),h("span",ta,b(ha.value?"在线":"离线"),1)])]),e[9]||(e[9]=h("div",{class:"header-right"},null,-1))]),h("div",oa,[h("div",{class:"chat-messages",ref_key:"messageContainer",ref:Ba},[(f(!0),p(C,null,V(xa.value,((a,e)=>(f(),p("div",{key:e,class:z(["message-item",a.isMe?"message-right":"message-left"])},[g(o,{size:32,src:a.avatar,class:"message-avatar"},null,8,["src"]),h("div",ra,[h("div",da,[h("span",ca,b(a.sender),1),h("span",ma,b(a.time),1)]),h("div",ua,b(a.content),1)])],2)))),128))],512),h("div",va,[g(r,{modelValue:ja.value,"onUpdate:modelValue":e[1]||(e[1]=a=>ja.value=a),type:"textarea",rows:3,placeholder:"输入消息",resize:"none",onKeyup:H(J(Aa,["prevent"]),["enter"])},{append:y((()=>[h("div",pa,[g(Ma,{icon:D(K),circle:"",plain:""},null,8,["icon"]),g(Ma,{icon:D(S),circle:"",plain:""},null,8,["icon"]),g(Ma,{type:"primary",onClick:Aa},{default:y((()=>e[10]||(e[10]=[x("发送")]))),_:1})])])),_:1},8,["modelValue","onKeyup"]),h("div",fa,[e[12]||(e[12]=h("div",{class:"left"},[h("i",{class:"iconfont-sys"},""),h("i",{class:"iconfont-sys"},"")],-1)),g(Ma,{type:"primary",onClick:Aa},{default:y((()=>e[11]||(e[11]=[x("发送")]))),_:1})])])])])],4)}}}),[["__scopeId","data-v-5785ac84"]]);export{ga as default};
