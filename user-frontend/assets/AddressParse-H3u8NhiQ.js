var e=(e,s,l)=>new Promise(((a,t)=>{var d=e=>{try{r(l.next(e))}catch(s){t(s)}},i=e=>{try{r(l.throw(e))}catch(s){t(s)}},r=e=>e.done?a(e.value):Promise.resolve(e.value).then(d,i);r((l=l.apply(e,s)).next())}));import{_ as s}from"./index-F0f2Vd-3.js";/* empty css               *//* empty css                *//* empty css                             *//* empty css                 *//* empty css                    */import{M as l,c as a,o as t,e as d,a as i,b as r,w as n,aT as u,x as o,aU as c,H as p,K as m,aR as f,F as h,A as v,N as y,aV as g,aW as b,aX as _,aY as k,aN as C,aQ as A,aZ as w,r as x,j as V,aH as R,aJ as F,Y as N,C as I,X as j,z as U,a_ as z,Z as T,_ as $,aq as B,a$ as L,b0 as O,b1 as D}from"./vendor-BVh5F9vp.js";import{A as S}from"./AddressCascader-CQSS5hj1.js";import{a as E}from"./address-IlaM-Wag.js";/* empty css                 */const M={class:"address-validation-result"},q={class:"validation-status"},P={class:"confidence-text"},G={key:0,class:"standardized-section"},J={class:"standardized-address"},H={class:"address-actions"},X={key:1,class:"suggestions-section"},Y={class:"validation-details"},Z={class:"action-section"};const K=s({name:"AddressValidationResult",props:{result:{type:Object,required:!0}},emits:["use-address","validate-again"],methods:{getStatusMessage(){return this.result.isValid?this.result.confidence>=.9?"地址格式正确，置信度很高":this.result.confidence>=.7?"地址格式正确，置信度较高":"地址格式正确，但置信度一般":"地址格式不正确或不完整"},getConfidenceColor(){const e=this.result.confidence;return e>=.9?"#67C23A":e>=.7?"#E6A23C":e>=.5?"#F56C6C":"#909399"},getConfidenceType(){const e=this.result.confidence;return e>=.9?"success":e>=.7?"warning":e>=.5?"danger":"info"},copyAddress(){return e(this,null,(function*(){if(this.result.standardizedAddress)try{yield navigator.clipboard.writeText(this.result.standardizedAddress),l.success("地址已复制到剪贴板")}catch(e){l.error("复制失败")}else l.warning("没有可复制的地址")}))},useAddress(){this.result.standardizedAddress?(this.$emit("use-address",this.result.standardizedAddress),l.success("地址已应用")):l.warning("没有可使用的地址")},exportResult(){const e={isValid:this.result.isValid,confidence:this.result.confidence,standardizedAddress:this.result.standardizedAddress||"",suggestions:this.result.suggestions||[],validationTime:(new Date).toISOString(),status:this.result.isValid?"有效":"无效",confidenceLevel:this.getConfidenceLevel()},s=JSON.stringify(e,null,2),a=new Blob([s],{type:"application/json"}),t=URL.createObjectURL(a),d=document.createElement("a");d.href=t,d.download=`address_validation_${Date.now()}.json`,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(t),l.success("验证结果已导出")},getConfidenceLevel(){const e=this.result.confidence;return e>=.9?"很高":e>=.7?"较高":e>=.5?"一般":"较低"},validateAgain(){this.$emit("validate-again")}}},[["render",function(e,s,l,C,A,w){const x=u,V=c,R=p,F=f,N=g,I=_,j=b,U=k;return t(),a("div",M,[d("div",q,[r(V,{icon:l.result.isValid?"success":"error",title:l.result.isValid?"地址验证通过":"地址验证失败","sub-title":w.getStatusMessage()},{extra:n((()=>[r(x,{percentage:Math.round(100*l.result.confidence),color:w.getConfidenceColor(),"stroke-width":8},null,8,["percentage","color"]),d("p",P,"置信度: "+o((100*l.result.confidence).toFixed(1))+"%",1)])),_:1},8,["icon","title","sub-title"])]),l.result.standardizedAddress?(t(),a("div",G,[s[2]||(s[2]=d("h4",null,"📍 标准化地址",-1)),r(F,{class:"address-card"},{default:n((()=>[d("div",J,o(l.result.standardizedAddress),1),d("div",H,[r(R,{type:"primary",size:"small",onClick:w.copyAddress},{default:n((()=>s[0]||(s[0]=[m(" 📋 复制地址 ")]))),_:1},8,["onClick"]),r(R,{type:"success",size:"small",onClick:w.useAddress},{default:n((()=>s[1]||(s[1]=[m(" ✅ 使用此地址 ")]))),_:1},8,["onClick"])])])),_:1})])):i("",!0),l.result.suggestions&&l.result.suggestions.length>0?(t(),a("div",X,[s[3]||(s[3]=d("h4",null,"💡 建议",-1)),(t(!0),a(h,null,v(l.result.suggestions,((e,s)=>(t(),y(N,{key:s,title:e,type:"info",closable:!1,"show-icon":"",class:"suggestion-item"},null,8,["title"])))),128))])):i("",!0),d("div",Y,[s[4]||(s[4]=d("h4",null,"📊 验证详情",-1)),r(U,{border:"",column:2},{default:n((()=>[r(j,{label:"验证状态"},{default:n((()=>[r(I,{type:l.result.isValid?"success":"danger"},{default:n((()=>[m(o(l.result.isValid?"有效":"无效"),1)])),_:1},8,["type"])])),_:1}),r(j,{label:"置信度"},{default:n((()=>[r(I,{type:w.getConfidenceType()},{default:n((()=>[m(o((100*l.result.confidence).toFixed(1))+"% ",1)])),_:1},8,["type"])])),_:1}),r(j,{label:"验证时间"},{default:n((()=>[r(I,{type:"info"},{default:n((()=>[m(o((new Date).toLocaleString()),1)])),_:1})])),_:1}),r(j,{label:"地址长度"},{default:n((()=>[r(I,{type:"info"},{default:n((()=>[m(o(l.result.standardizedAddress?l.result.standardizedAddress.length:0)+" 字符 ",1)])),_:1})])),_:1})])),_:1})]),d("div",Z,[r(R,{type:"primary",onClick:w.exportResult},{default:n((()=>s[5]||(s[5]=[m(" 📤 导出验证结果 ")]))),_:1},8,["onClick"]),r(R,{type:"info",onClick:w.validateAgain},{default:n((()=>s[6]||(s[6]=[m(" 🔄 重新验证 ")]))),_:1},8,["onClick"])])])}],["__scopeId","data-v-63d6c3f9"]]),Q={class:"address-result"},W={class:"info-section"},ee={key:1,class:"empty-value"},se={key:1,class:"empty-value"},le={key:1,class:"empty-value"},ae={class:"info-section"},te={key:0,class:"code"},de={key:1,class:"empty-value"},ie={key:0,class:"code"},re={key:1,class:"empty-value"},ne={key:0,class:"code"},ue={key:1,class:"empty-value"},oe={key:0,class:"code"},ce={class:"info-section"},pe={class:"address-content"},me={class:"detail-address"},fe={key:0},he={key:1,class:"empty-value"},ve={class:"full-address"},ye={key:0},ge={key:1},be={key:0,class:"info-section"},_e={class:"action-section"};const ke=s({name:"AddressResult",props:{result:{type:Object,required:!0}},methods:{getFullAddress(){const e=[];return this.result.provinceName&&e.push(this.result.provinceName),this.result.cityName&&e.push(this.result.cityName),this.result.districtName&&e.push(this.result.districtName),this.result.townName&&e.push(this.result.townName),this.result.detailAddress&&e.push(this.result.detailAddress),e.join("")},copyResult(){return e(this,null,(function*(){const e=this.formatResultText();try{yield navigator.clipboard.writeText(e),l.success("结果已复制到剪贴板")}catch(s){l.error("复制失败")}}))},formatResultText(){const e=[];return e.push("=== 地址解析结果 ==="),this.result.name&&e.push(`姓名：${this.result.name}`),this.result.mobile&&e.push(`手机：${this.result.mobile}`),this.result.phone&&e.push(`电话：${this.result.phone}`),e.push(`地址：${this.getFullAddress()}`),this.result.latitude&&this.result.longitude&&e.push(`坐标：${this.result.latitude}, ${this.result.longitude}`),e.join("\n")},exportResult(){const e={name:this.result.name||"",mobile:this.result.mobile||"",phone:this.result.phone||"",province:this.result.provinceName||"",city:this.result.cityName||"",district:this.result.districtName||"",town:this.result.townName||"",detailAddress:this.result.detailAddress||"",fullAddress:this.getFullAddress(),latitude:this.result.latitude||0,longitude:this.result.longitude||0,confidence:this.result.confidence||0},s=JSON.stringify(e,null,2),a=new Blob([s],{type:"application/json"}),t=URL.createObjectURL(a),d=document.createElement("a");d.href=t,d.download=`address_result_${Date.now()}.json`,document.body.appendChild(d),d.click(),document.body.removeChild(d),URL.revokeObjectURL(t),l.success("结果已导出")},viewOnMap(){const e=`https://www.google.com/maps?q=${this.result.latitude},${this.result.longitude}`;window.open(e,"_blank")}}},[["render",function(e,s,l,u,c,h){const v=_,g=b,x=k,V=C,R=A,F=f,N=w,I=p;return t(),a("div",Q,[r(R,{gutter:20},{default:n((()=>[r(V,{span:12},{default:n((()=>[d("div",W,[s[0]||(s[0]=d("h4",null,"👤 联系信息",-1)),r(x,{column:1,border:""},{default:n((()=>[r(g,{label:"姓名"},{default:n((()=>[l.result.name?(t(),y(v,{key:0,type:"success"},{default:n((()=>[m(o(l.result.name),1)])),_:1})):(t(),a("span",ee,"未识别"))])),_:1}),r(g,{label:"手机号"},{default:n((()=>[l.result.mobile?(t(),y(v,{key:0,type:"primary"},{default:n((()=>[m(o(l.result.mobile),1)])),_:1})):(t(),a("span",se,"未识别"))])),_:1}),r(g,{label:"电话"},{default:n((()=>[l.result.phone?(t(),y(v,{key:0,type:"info"},{default:n((()=>[m(o(l.result.phone),1)])),_:1})):(t(),a("span",le,"未识别"))])),_:1})])),_:1})])])),_:1}),r(V,{span:12},{default:n((()=>[d("div",ae,[s[1]||(s[1]=d("h4",null,"📍 地址信息",-1)),r(x,{column:1,border:""},{default:n((()=>[r(g,{label:"省份"},{default:n((()=>[l.result.provinceName?(t(),y(v,{key:0,type:"success"},{default:n((()=>[m(o(l.result.provinceName)+" ",1),l.result.provinceCode?(t(),a("span",te,"("+o(l.result.provinceCode)+")",1)):i("",!0)])),_:1})):(t(),a("span",de,"未识别"))])),_:1}),r(g,{label:"城市"},{default:n((()=>[l.result.cityName?(t(),y(v,{key:0,type:"primary"},{default:n((()=>[m(o(l.result.cityName)+" ",1),l.result.cityCode?(t(),a("span",ie,"("+o(l.result.cityCode)+")",1)):i("",!0)])),_:1})):(t(),a("span",re,"未识别"))])),_:1}),r(g,{label:"区县"},{default:n((()=>[l.result.districtName?(t(),y(v,{key:0,type:"info"},{default:n((()=>[m(o(l.result.districtName)+" ",1),l.result.districtCode?(t(),a("span",ne,"("+o(l.result.districtCode)+")",1)):i("",!0)])),_:1})):(t(),a("span",ue,"未识别"))])),_:1}),l.result.townName?(t(),y(g,{key:0,label:"乡镇"},{default:n((()=>[r(v,{type:"warning"},{default:n((()=>[m(o(l.result.townName)+" ",1),l.result.townCode?(t(),a("span",oe,"("+o(l.result.townCode)+")",1)):i("",!0)])),_:1})])),_:1})):i("",!0)])),_:1})])])),_:1})])),_:1}),d("div",ce,[s[4]||(s[4]=d("h4",null,"🏠 详细地址",-1)),r(F,{class:"address-card"},{default:n((()=>[d("div",pe,[d("div",me,[s[2]||(s[2]=d("strong",null,"详细地址：",-1)),l.result.detailAddress?(t(),a("span",fe,o(l.result.detailAddress),1)):(t(),a("span",he,"未识别"))]),d("div",ve,[s[3]||(s[3]=d("strong",null,"完整地址：",-1)),l.result.fullAddress?(t(),a("span",ye,o(l.result.fullAddress),1)):(t(),a("span",ge,o(h.getFullAddress()),1))])])])),_:1})]),l.result.latitude||l.result.longitude?(t(),a("div",be,[s[5]||(s[5]=d("h4",null,"🌍 坐标信息",-1)),r(R,{gutter:20},{default:n((()=>[r(V,{span:8},{default:n((()=>[r(N,{title:"纬度",value:l.result.latitude,precision:6},null,8,["value"])])),_:1}),r(V,{span:8},{default:n((()=>[r(N,{title:"经度",value:l.result.longitude,precision:6},null,8,["value"])])),_:1}),r(V,{span:8},{default:n((()=>[r(N,{title:"置信度",value:l.result.confidence||0,suffix:"%",precision:1},null,8,["value"])])),_:1})])),_:1})])):i("",!0),d("div",_e,[r(I,{type:"primary",onClick:h.copyResult},{default:n((()=>s[6]||(s[6]=[m(" 📋 复制结果 ")]))),_:1},8,["onClick"]),r(I,{type:"success",onClick:h.exportResult},{default:n((()=>s[7]||(s[7]=[m(" 📤 导出数据 ")]))),_:1},8,["onClick"]),l.result.latitude&&l.result.longitude?(t(),y(I,{key:0,type:"info",onClick:h.viewOnMap},{default:n((()=>s[8]||(s[8]=[m(" 🗺️ 查看地图 ")]))),_:1},8,["onClick"])):i("",!0)])])}],["__scopeId","data-v-2f105034"]]),Ce={class:"batch-address-result"},Ae={key:0},we={key:0,class:"contact-item"},xe={key:1,class:"contact-item"},Ve={key:1,class:"error-text"},Re={key:0},Fe={class:"address-line"},Ne={key:0,class:"address-part"},Ie={key:1,class:"address-part"},je={key:2,class:"address-part"},Ue={key:0,class:"detail-address"},ze={key:1,class:"error-text"},Te={class:"batch-actions"},$e={class:"error-detail"};const Be=s({name:"BatchAddressResult",components:{AddressResult:ke},props:{results:{type:Array,required:!0}},setup(e){const s=x(!1),a=x(!1),t=x(null),d=x(""),i=x(""),r=V((()=>e.results.filter((e=>e.success)).length)),n=V((()=>e.results.filter((e=>!e.success)).length)),u=e=>`地址 ${e+1} 的原始文本`,o=e=>{if(!e)return"";const s=[];return e.provinceName&&s.push(e.provinceName),e.cityName&&s.push(e.cityName),e.districtName&&s.push(e.districtName),e.townName&&s.push(e.townName),e.detailAddress&&s.push(e.detailAddress),s.join("")},c=(e,s)=>{if(0===e.length)return void l.warning("没有数据可导出");const a=Object.keys(e[0]),t=[a.join(","),...e.map((e=>a.map((s=>{const l=e[s];return"string"==typeof l&&l.includes(",")?`"${l}"`:l})).join(",")))].join("\n"),d=new Blob(["\ufeff"+t],{type:"text/csv;charset=utf-8;"}),i=URL.createObjectURL(d),r=document.createElement("a");r.href=i,r.download=`${s}_${Date.now()}.csv`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(i),l.success("数据已导出")};return{detailDialogVisible:s,errorDialogVisible:a,selectedResult:t,selectedError:d,selectedOriginalText:i,successCount:r,failedCount:n,viewDetail:e=>{t.value=e.addressInfo,s.value=!0},viewError:e=>{d.value=e.error||"未知错误",i.value=u(e.index),a.value=!0},exportSuccessResults:()=>{const s=e.results.filter((e=>e.success)).map((e=>{var s,l,a,t,d,i,r;return{index:e.index+1,name:(null==(s=e.addressInfo)?void 0:s.name)||"",mobile:(null==(l=e.addressInfo)?void 0:l.mobile)||"",phone:(null==(a=e.addressInfo)?void 0:a.phone)||"",province:(null==(t=e.addressInfo)?void 0:t.provinceName)||"",city:(null==(d=e.addressInfo)?void 0:d.cityName)||"",district:(null==(i=e.addressInfo)?void 0:i.districtName)||"",detailAddress:(null==(r=e.addressInfo)?void 0:r.detailAddress)||"",fullAddress:o(e.addressInfo),processTime:e.processTime}}));c(s,"batch_address_success_results")},exportFailedResults:()=>{const s=e.results.filter((e=>!e.success)).map((e=>({index:e.index+1,error:e.error||"未知错误",processTime:e.processTime})));c(s,"batch_address_failed_results")},exportAllResults:()=>{const s=e.results.map((e=>{var s,l,a,t,d,i,r;return{index:e.index+1,success:e.success,name:e.success&&(null==(s=e.addressInfo)?void 0:s.name)||"",mobile:e.success&&(null==(l=e.addressInfo)?void 0:l.mobile)||"",phone:e.success&&(null==(a=e.addressInfo)?void 0:a.phone)||"",province:e.success&&(null==(t=e.addressInfo)?void 0:t.provinceName)||"",city:e.success&&(null==(d=e.addressInfo)?void 0:d.cityName)||"",district:e.success&&(null==(i=e.addressInfo)?void 0:i.districtName)||"",detailAddress:e.success&&(null==(r=e.addressInfo)?void 0:r.detailAddress)||"",fullAddress:e.success?o(e.addressInfo):"",error:e.success?"":e.error||"未知错误",processTime:e.processTime}}));c(s,"batch_address_all_results")}}}},[["render",function(e,s,l,u,c,f){const h=R,v=_,b=p,k=F,C=ke,A=N,w=g,x=I;return t(),a("div",Ce,[r(k,{data:l.results,border:"",stripe:"","max-height":"600","default-sort":{prop:"index",order:"ascending"}},{default:n((()=>[r(h,{prop:"index",label:"序号",width:"80",sortable:""},{default:n((({row:e})=>[m(o(e.index+1),1)])),_:1}),r(h,{prop:"success",label:"状态",width:"100",sortable:""},{default:n((({row:e})=>[r(v,{type:e.success?"success":"danger"},{default:n((()=>[m(o(e.success?"成功":"失败"),1)])),_:2},1032,["type"])])),_:1}),r(h,{label:"联系信息",width:"200"},{default:n((({row:e})=>[e.success&&e.addressInfo?(t(),a("div",Ae,[e.addressInfo.name?(t(),a("div",we,[r(v,{size:"small",type:"success"},{default:n((()=>[m(o(e.addressInfo.name),1)])),_:2},1024)])):i("",!0),e.addressInfo.mobile?(t(),a("div",xe,[r(v,{size:"small",type:"primary"},{default:n((()=>[m(o(e.addressInfo.mobile),1)])),_:2},1024)])):i("",!0)])):(t(),a("span",Ve,o(e.error||"解析失败"),1))])),_:1}),r(h,{label:"地址信息","min-width":"300"},{default:n((({row:e})=>[e.success&&e.addressInfo?(t(),a("div",Re,[d("div",Fe,[e.addressInfo.provinceName?(t(),a("span",Ne,o(e.addressInfo.provinceName),1)):i("",!0),e.addressInfo.cityName?(t(),a("span",Ie,o(e.addressInfo.cityName),1)):i("",!0),e.addressInfo.districtName?(t(),a("span",je,o(e.addressInfo.districtName),1)):i("",!0)]),e.addressInfo.detailAddress?(t(),a("div",Ue,o(e.addressInfo.detailAddress),1)):i("",!0)])):(t(),a("span",ze,"-"))])),_:1}),r(h,{prop:"processTime",label:"耗时",width:"100",sortable:""},{default:n((({row:e})=>[m(o((1e3*e.processTime).toFixed(0))+"ms ",1)])),_:1}),r(h,{label:"操作",width:"150",fixed:"right"},{default:n((({row:e})=>[e.success?(t(),y(b,{key:0,type:"primary",size:"small",onClick:s=>u.viewDetail(e)},{default:n((()=>s[2]||(s[2]=[m(" 查看详情 ")]))),_:2},1032,["onClick"])):(t(),y(b,{key:1,type:"danger",size:"small",onClick:s=>u.viewError(e)},{default:n((()=>s[3]||(s[3]=[m(" 查看错误 ")]))),_:2},1032,["onClick"]))])),_:1})])),_:1},8,["data"]),d("div",Te,[r(b,{type:"primary",onClick:u.exportSuccessResults},{default:n((()=>[m(" 📤 导出成功结果 ("+o(u.successCount)+") ",1)])),_:1},8,["onClick"]),r(b,{type:"warning",onClick:u.exportFailedResults},{default:n((()=>[m(" 📋 导出失败列表 ("+o(u.failedCount)+") ",1)])),_:1},8,["onClick"]),r(b,{type:"success",onClick:u.exportAllResults},{default:n((()=>s[4]||(s[4]=[m(" 💾 导出全部结果 ")]))),_:1},8,["onClick"])]),r(A,{modelValue:u.detailDialogVisible,"onUpdate:modelValue":s[0]||(s[0]=e=>u.detailDialogVisible=e),title:"解析详情",width:"800px","close-on-click-modal":!1},{default:n((()=>[u.selectedResult?(t(),y(C,{key:0,result:u.selectedResult},null,8,["result"])):i("",!0)])),_:1},8,["modelValue"]),r(A,{modelValue:u.errorDialogVisible,"onUpdate:modelValue":s[1]||(s[1]=e=>u.errorDialogVisible=e),title:"错误详情",width:"600px"},{default:n((()=>[d("div",$e,[s[5]||(s[5]=d("h4",null,"错误信息：",-1)),r(w,{title:u.selectedError,type:"error",closable:!1,"show-icon":""},null,8,["title"]),s[6]||(s[6]=d("h4",{style:{"margin-top":"20px"}},"原始文本：",-1)),r(x,{value:u.selectedOriginalText,type:"textarea",rows:4,readonly:""},null,8,["value"])])])),_:1},8,["modelValue"])])}],["__scopeId","data-v-b4356c2e"]]),Le={class:"address-parse-container"},Oe={class:"single-parse-section"},De={class:"card-header"},Se={class:"batch-parse-section"},Ee={class:"card-header"},Me={class:"batch-addresses"},qe={class:"card-header"},Pe={class:"statistics"},Ge={class:"validate-section"};const Je={class:"address-parse-page"},He={class:"page-header"},Xe={class:"header-content"},Ye={class:"header-right"},Ze={class:"feature-intro"},Ke={class:"main-content"},Qe={class:"usage-guide"},We={class:"faq-section"};const es=s({name:"AddressParsePage",components:{AddressParse:s({name:"AddressParse",components:{AddressResult:ke,BatchAddressResult:Be,AddressValidationResult:K,AreaCascader:S},setup(){const s=x("single"),a=x(!1),t=j({text:"",lat:30,lng:110}),d=j({addresses:[],concurrency:5}),i=j({codes:[],detailAddress:""}),r=x(null),n=x(null),u=x(null),o=["李健\n17099916606\n湖南省永州市冷水滩区珊瑚街道 园丁山庄8栋19号门面妈妈驿站","张三\n13800138000\n北京市海淀区西二旗 中关村软件园 腾讯大厦B座","王五\n15988776655\n上海市浦东新区陆家嘴 金茂大厦20层","刘六\n18922334455\n广东省深圳市南山区科技园 腾讯大厦"];return{activeTab:s,loading:a,singleForm:t,batchForm:d,validateForm:i,singleResult:r,batchResult:n,validateResult:u,loadExample:()=>{t.text=o[0],r.value=null},loadBatchExample:()=>{d.addresses=o.map((e=>({text:e,lat:30,lng:110}))),n.value=null},addBatchAddress:()=>{d.addresses.push({text:"",lat:30,lng:110})},removeBatchAddress:e=>{d.addresses.splice(e,1)},parseSingleAddress:()=>e(this,null,(function*(){if(t.text.trim()){a.value=!0;try{const e=yield E.parseAddress(t);e.success?(r.value=e.data.addressInfo,l.success("地址解析成功")):l.error(e.message||"解析失败")}catch(e){l.error("解析失败: "+(e.message||"网络错误"))}finally{a.value=!1}}else l.warning("请输入地址文本")})),parseBatchAddresses:()=>e(this,null,(function*(){const e=d.addresses.filter((e=>e.text.trim()));if(0!==e.length){a.value=!0;try{const s=yield E.batchParseAddress({addresses:e,concurrency:d.concurrency});s.success?(n.value=s.data,l.success(`批量解析完成，成功率: ${s.data.statistics.successRate.toFixed(1)}%`)):l.error(s.message||"批量解析失败")}catch(s){l.error("批量解析失败: "+(s.message||"网络错误"))}finally{a.value=!1}}else l.warning("请至少输入一个地址")})),validateAddress:()=>e(this,null,(function*(){if(i.codes.length<3)l.warning("请选择完整的省市区");else{a.value=!0;try{const e=yield E.validateAddress({provinceCode:i.codes[0],cityCode:i.codes[1],districtCode:i.codes[2],detailAddress:i.detailAddress});e.success?(u.value=e.data,l.success("地址验证完成")):l.error(e.message||"验证失败")}catch(e){l.error("验证失败: "+(e.message||"网络错误"))}finally{a.value=!1}}})),onAreaChange:e=>{i.codes=e,u.value=null},clearSingleForm:()=>{t.text="",t.lat=30,t.lng=110,r.value=null},clearBatchForm:()=>{d.addresses=[],n.value=null},clearValidateForm:()=>{i.codes=[],i.detailAddress="",u.value=null}}}},[["render",function(e,s,l,u,c,g){const b=p,k=I,w=$,x=C,V=A,R=T,F=f,N=ke,j=z,O=B,D=_,S=Be,E=U("AreaCascader"),M=K,q=L;return t(),a("div",Le,[s[22]||(s[22]=d("div",{class:"page-header"},[d("h2",null,"🧠 智能地址解析"),d("p",{class:"subtitle"},"使用AI技术快速解析地址信息，提取姓名、电话、详细地址等")],-1)),r(q,{modelValue:u.activeTab,"onUpdate:modelValue":s[6]||(s[6]=e=>u.activeTab=e),class:"parse-tabs"},{default:n((()=>[r(j,{label:"单个解析",name:"single"},{default:n((()=>[d("div",Oe,[r(F,{class:"parse-card"},{header:n((()=>[d("div",De,[s[8]||(s[8]=d("span",null,"📝 输入地址文本",-1)),r(b,{type:"primary",size:"small",onClick:u.loadExample,disabled:u.loading},{default:n((()=>s[7]||(s[7]=[m(" 加载示例 ")]))),_:1},8,["onClick","disabled"])])])),default:n((()=>[r(R,{model:u.singleForm,"label-width":"100px"},{default:n((()=>[r(w,{label:"地址文本"},{default:n((()=>[r(k,{modelValue:u.singleForm.text,"onUpdate:modelValue":s[0]||(s[0]=e=>u.singleForm.text=e),type:"textarea",rows:4,placeholder:"请输入包含姓名、电话、地址的文本，例如：\n李健\n17099916606\n湖南省永州市冷水滩区珊瑚街道 园丁山庄8栋19号门面妈妈驿站",disabled:u.loading},null,8,["modelValue","disabled"])])),_:1}),r(w,{label:"坐标位置"},{default:n((()=>[r(V,{gutter:10},{default:n((()=>[r(x,{span:12},{default:n((()=>[r(k,{modelValue:u.singleForm.lat,"onUpdate:modelValue":s[1]||(s[1]=e=>u.singleForm.lat=e),modelModifiers:{number:!0},placeholder:"纬度",disabled:u.loading},null,8,["modelValue","disabled"])])),_:1}),r(x,{span:12},{default:n((()=>[r(k,{modelValue:u.singleForm.lng,"onUpdate:modelValue":s[2]||(s[2]=e=>u.singleForm.lng=e),modelModifiers:{number:!0},placeholder:"经度",disabled:u.loading},null,8,["modelValue","disabled"])])),_:1})])),_:1})])),_:1}),r(w,null,{default:n((()=>[r(b,{type:"primary",onClick:u.parseSingleAddress,loading:u.loading,size:"large"},{default:n((()=>s[9]||(s[9]=[m(" 🚀 开始解析 ")]))),_:1},8,["onClick","loading"]),r(b,{onClick:u.clearSingleForm,disabled:u.loading},{default:n((()=>s[10]||(s[10]=[m(" 清空 ")]))),_:1},8,["onClick","disabled"])])),_:1})])),_:1},8,["model"])])),_:1}),u.singleResult?(t(),y(F,{key:0,class:"result-card"},{header:n((()=>s[11]||(s[11]=[d("span",null,"✅ 解析结果",-1)]))),default:n((()=>[r(N,{result:u.singleResult},null,8,["result"])])),_:1})):i("",!0)])])),_:1}),r(j,{label:"批量解析",name:"batch"},{default:n((()=>[d("div",Se,[r(F,{class:"parse-card"},{header:n((()=>[d("div",Ee,[s[14]||(s[14]=d("span",null,"📋 批量地址解析",-1)),d("div",null,[r(b,{type:"success",size:"small",onClick:u.loadBatchExample,disabled:u.loading},{default:n((()=>s[12]||(s[12]=[m(" 加载示例 ")]))),_:1},8,["onClick","disabled"]),r(b,{type:"primary",size:"small",onClick:u.addBatchAddress,disabled:u.loading},{default:n((()=>s[13]||(s[13]=[m(" 添加地址 ")]))),_:1},8,["onClick","disabled"])])])])),default:n((()=>[r(R,{model:u.batchForm,"label-width":"100px"},{default:n((()=>[r(w,{label:"并发数"},{default:n((()=>[r(O,{modelValue:u.batchForm.concurrency,"onUpdate:modelValue":s[3]||(s[3]=e=>u.batchForm.concurrency=e),min:1,max:20,disabled:u.loading},null,8,["modelValue","disabled"]),s[15]||(s[15]=d("span",{class:"form-tip"},"建议设置为5-10，过高可能导致API限流",-1))])),_:1}),r(w,{label:"地址列表"},{default:n((()=>[d("div",Me,[(t(!0),a(h,null,v(u.batchForm.addresses,((e,l)=>(t(),a("div",{key:l,class:"batch-address-item"},[r(k,{modelValue:e.text,"onUpdate:modelValue":s=>e.text=s,type:"textarea",rows:2,placeholder:`地址 ${l+1}`,disabled:u.loading},null,8,["modelValue","onUpdate:modelValue","placeholder","disabled"]),r(b,{type:"danger",size:"small",onClick:e=>u.removeBatchAddress(l),disabled:u.loading},{default:n((()=>s[16]||(s[16]=[m(" 删除 ")]))),_:2},1032,["onClick","disabled"])])))),128))])])),_:1}),r(w,null,{default:n((()=>[r(b,{type:"primary",onClick:u.parseBatchAddresses,loading:u.loading,size:"large",disabled:0===u.batchForm.addresses.length},{default:n((()=>[m(" 🚀 批量解析 ("+o(u.batchForm.addresses.length)+"个) ",1)])),_:1},8,["onClick","loading","disabled"]),r(b,{onClick:u.clearBatchForm,disabled:u.loading},{default:n((()=>s[17]||(s[17]=[m(" 清空全部 ")]))),_:1},8,["onClick","disabled"])])),_:1})])),_:1},8,["model"])])),_:1}),u.batchResult?(t(),y(F,{key:0,class:"result-card"},{header:n((()=>[d("div",qe,[s[18]||(s[18]=d("span",null,"📊 批量解析结果",-1)),d("div",Pe,[r(D,{type:"success"},{default:n((()=>[m("成功: "+o(u.batchResult.statistics.success),1)])),_:1}),r(D,{type:"danger"},{default:n((()=>[m("失败: "+o(u.batchResult.statistics.failed),1)])),_:1}),r(D,{type:"info"},{default:n((()=>[m("成功率: "+o(u.batchResult.statistics.successRate.toFixed(1))+"%",1)])),_:1}),r(D,{type:"warning"},{default:n((()=>[m("耗时: "+o(u.batchResult.processTime.toFixed(2))+"s",1)])),_:1})])])])),default:n((()=>[r(S,{results:u.batchResult.results},null,8,["results"])])),_:1})):i("",!0)])])),_:1}),r(j,{label:"地址验证",name:"validate"},{default:n((()=>[d("div",Ge,[r(F,{class:"parse-card"},{header:n((()=>s[19]||(s[19]=[d("span",null,"🔍 地址验证",-1)]))),default:n((()=>[r(R,{model:u.validateForm,"label-width":"100px"},{default:n((()=>[r(w,{label:"省份"},{default:n((()=>[r(E,{modelValue:u.validateForm.codes,"onUpdate:modelValue":s[4]||(s[4]=e=>u.validateForm.codes=e),onChange:u.onAreaChange,disabled:u.loading},null,8,["modelValue","onChange","disabled"])])),_:1}),r(w,{label:"详细地址"},{default:n((()=>[r(k,{modelValue:u.validateForm.detailAddress,"onUpdate:modelValue":s[5]||(s[5]=e=>u.validateForm.detailAddress=e),placeholder:"请输入详细地址",disabled:u.loading},null,8,["modelValue","disabled"])])),_:1}),r(w,null,{default:n((()=>[r(b,{type:"primary",onClick:u.validateAddress,loading:u.loading,disabled:!u.validateForm.codes.length||!u.validateForm.detailAddress},{default:n((()=>s[20]||(s[20]=[m(" 🔍 验证地址 ")]))),_:1},8,["onClick","loading","disabled"]),r(b,{onClick:u.clearValidateForm,disabled:u.loading},{default:n((()=>s[21]||(s[21]=[m(" 清空 ")]))),_:1},8,["onClick","disabled"])])),_:1})])),_:1},8,["model"])])),_:1}),u.validateResult?(t(),y(F,{key:0,class:"result-card"},{header:n((()=>[d("span",null,o(u.validateResult.isValid?"✅":"❌")+" 验证结果",1)])),default:n((()=>[r(M,{result:u.validateResult},null,8,["result"])])),_:1})):i("",!0)])])),_:1})])),_:1},8,["modelValue"])])}],["__scopeId","data-v-6e9315dd"]])},setup(){const e=x(["single"]),s=x([]),l=x(0);return l.value=Math.floor(100*Math.random())+50,{activeGuide:e,activeFaq:s,todayCount:l}}},[["render",function(e,s,l,i,u,o){const c=w,p=f,h=C,v=A,y=U("AddressParse",!0),g=D,b=O;return t(),a("div",Je,[d("div",He,[r(p,{class:"header-card"},{default:n((()=>[d("div",Xe,[s[3]||(s[3]=d("div",{class:"header-left"},[d("h1",{class:"page-title"},"🧠 智能地址解析"),d("p",{class:"page-description"}," 使用先进的AI技术快速解析地址信息，自动提取姓名、电话、详细地址等关键信息， 支持单个解析、批量处理和地址验证功能。 ")],-1)),d("div",Ye,[r(p,{class:"stat-card",shadow:"hover"},{header:n((()=>s[2]||(s[2]=[d("div",{style:{display:"inline-flex","align-items":"center"}}," 📊 今日解析 ",-1)]))),default:n((()=>[r(c,{value:i.todayCount},null,8,["value"])])),_:1})])])])),_:1})]),d("div",Ze,[r(v,{gutter:20},{default:n((()=>[r(h,{span:8},{default:n((()=>[r(p,{class:"feature-card"},{default:n((()=>s[4]||(s[4]=[d("div",{class:"feature-item"},[d("div",{class:"feature-icon"},"🎯"),d("h3",null,"智能识别"),d("p",null,"自动识别姓名、电话、地址等信息，准确率高达95%以上")],-1)]))),_:1})])),_:1}),r(h,{span:8},{default:n((()=>[r(p,{class:"feature-card"},{default:n((()=>s[5]||(s[5]=[d("div",{class:"feature-item"},[d("div",{class:"feature-icon"},"⚡"),d("h3",null,"批量处理"),d("p",null,"支持批量解析多个地址，并发处理，大幅提升工作效率")],-1)]))),_:1})])),_:1}),r(h,{span:8},{default:n((()=>[r(p,{class:"feature-card"},{default:n((()=>s[6]||(s[6]=[d("div",{class:"feature-item"},[d("div",{class:"feature-icon"},"🔍"),d("h3",null,"地址验证"),d("p",null,"验证地址的有效性，提供标准化建议，确保地址准确性")],-1)]))),_:1})])),_:1})])),_:1})]),d("div",Ke,[r(y)]),d("div",Qe,[r(p,null,{header:n((()=>s[7]||(s[7]=[d("div",{class:"card-header"},[d("span",null,"📖 使用说明")],-1)]))),default:n((()=>[r(b,{modelValue:i.activeGuide,"onUpdate:modelValue":s[0]||(s[0]=e=>i.activeGuide=e)},{default:n((()=>[r(g,{title:"🎯 单个地址解析",name:"single"},{default:n((()=>s[8]||(s[8]=[d("div",{class:"guide-content"},[d("ol",null,[d("li",null,"在文本框中输入包含姓名、电话、地址的文本"),d("li",null,[m("可以使用换行符分隔不同信息，如："),d("br"),d("code",null,[m("李健"),d("br"),m("17099916606"),d("br"),m("湖南省永州市冷水滩区珊瑚街道 园丁山庄8栋19号")])]),d("li",null,'点击"开始解析"按钮进行解析'),d("li",null,"查看解析结果，可以复制或导出数据")])],-1)]))),_:1}),r(g,{title:"📋 批量地址解析",name:"batch"},{default:n((()=>s[9]||(s[9]=[d("div",{class:"guide-content"},[d("ol",null,[d("li",null,'点击"批量解析"选项卡'),d("li",null,"设置合适的并发数（建议5-10）"),d("li",null,"添加多个地址文本"),d("li",null,'点击"批量解析"开始处理'),d("li",null,"查看统计信息和详细结果"),d("li",null,"可以导出成功结果或失败列表")])],-1)]))),_:1}),r(g,{title:"🔍 地址验证",name:"validate"},{default:n((()=>s[10]||(s[10]=[d("div",{class:"guide-content"},[d("ol",null,[d("li",null,'点击"地址验证"选项卡'),d("li",null,"选择省市区信息"),d("li",null,"输入详细地址"),d("li",null,'点击"验证地址"查看结果'),d("li",null,"根据验证结果和建议优化地址")])],-1)]))),_:1})])),_:1},8,["modelValue"])])),_:1})]),d("div",We,[r(p,null,{header:n((()=>s[11]||(s[11]=[d("div",{class:"card-header"},[d("span",null,"❓ 常见问题")],-1)]))),default:n((()=>[r(b,{modelValue:i.activeFaq,"onUpdate:modelValue":s[1]||(s[1]=e=>i.activeFaq=e)},{default:n((()=>[r(g,{title:"解析准确率如何？",name:"accuracy"},{default:n((()=>s[12]||(s[12]=[d("p",null,"我们的AI地址解析系统准确率达到95%以上，对于标准格式的地址文本识别效果更佳。",-1)]))),_:1}),r(g,{title:"支持哪些地址格式？",name:"format"},{default:n((()=>s[13]||(s[13]=[d("p",null,"支持多种地址格式，包括：",-1),d("ul",null,[d("li",null,"标准格式：省市区 + 详细地址"),d("li",null,"混合格式：姓名、电话、地址混合"),d("li",null,"简化格式：仅包含关键地址信息")],-1)]))),_:1}),r(g,{title:"批量解析有数量限制吗？",name:"limit"},{default:n((()=>s[14]||(s[14]=[d("p",null,"单次批量解析最多支持100个地址，建议根据网络情况调整并发数。",-1)]))),_:1}),r(g,{title:"解析失败怎么办？",name:"failure"},{default:n((()=>s[15]||(s[15]=[d("p",null,"解析失败可能的原因：",-1),d("ul",null,[d("li",null,"地址格式不规范"),d("li",null,"信息不完整"),d("li",null,"网络连接问题")],-1),d("p",null,"建议检查输入格式或联系技术支持。",-1)]))),_:1})])),_:1},8,["modelValue"])])),_:1})])])}],["__scopeId","data-v-4095d523"]]);export{es as default};
