var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,s=(a,l,r)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r,i=(e,a,l)=>new Promise(((r,t)=>{var o=e=>{try{i(l.next(e))}catch(a){t(a)}},s=e=>{try{i(l.throw(e))}catch(a){t(a)}},i=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,s);i((l=l.apply(e,a)).next())}));import{_ as d}from"./index-F0f2Vd-3.js";/* empty css                *//* empty css                       */import{d as n,a1 as u,r as p,X as c,j as _,f as v,c as f,o as m,e as y,b as g,w as b,Z as w,_ as k,ar as h,F as j,A as O,N as z,as as C,u as V,C as D,bh as W,H as x,K as P,y as U,a3 as Y,b7 as E,aR as T,b4 as A,a2 as M,aJ as S,aH as N,aX as R,x as B,a as H,G as L,aG as F,aK as G,M as I,ai as K}from"./vendor-BVh5F9vp.js";import{W as X}from"./workOrderApi-4juIOmO-.js";import{E as q}from"./errorHandler-Dnd-hi0l.js";import J from"./WorkOrderDetailDialog-CuHhVgpx.js";import Z from"./CreateWorkOrderDialog-C4e8e6Tj.js";import{u as $}from"./useWorkOrderTypes-B5Knz6RB.js";/* empty css                  *//* empty css                    *//* empty css                             *//* empty css                 *//* empty css                   */import"./expressApi-BL8dLu27.js";const Q={1:"待处理",2:"处理中",3:"已回复",4:"已完结"},ee={1:"催取件",2:"重量异常",12:"催派送",16:"物流停滞",17:"重新分配快递员",19:"取消订单"},ae={class:"workorder-list-container"},le={class:"search-section"},re={class:"action-section"},te={class:"table-section"},oe={class:"pagination-section"},se=d(n({__name:"WorkOrderList",setup(e){u();const{userFriendlyTypes:d,fetchWorkOrderTypes:n,getTypeName:se}=$(),ie=p(!1),de=p(!1),ne=p(!1),ue=p(""),pe=p([]),ce=p(null),_e=c({page:1,page_size:20,status:void 0,work_order_type:void 0,provider:void 0,order_no:"",tracking_no:"",start_date:void 0,end_date:void 0}),ve=c({page:1,page_size:20,total:0}),fe=_((()=>({1:"待处理",2:"处理中",3:"等待回复",4:"已解决",5:"已关闭",6:"已取消"})));_((()=>Q)),_((()=>ee));const me=()=>i(this,null,(function*(){ie.value=!0;try{const d=(e=((e,a)=>{for(var l in a||(a={}))t.call(a,l)&&s(e,l,a[l]);if(r)for(var l of r(a))o.call(a,l)&&s(e,l,a[l]);return e})({},_e),i={page:ve.page,page_size:ve.page_size},a(e,l(i))),n=yield X.getWorkOrderList(d);if(!n.success||!n.data)throw new Error(n.message||"获取工单列表失败");pe.value=n.data.items||[],ve.total=n.data.total||0}catch(d){q.handleApiError(d),pe.value=[],ve.total=0}finally{ie.value=!1}var e,i})),ye=()=>{ve.page=1,me()},ge=()=>{Object.assign(_e,{page:1,page_size:20,status:void 0,work_order_type:void 0,provider:void 0,order_no:"",tracking_no:"",start_date:void 0,end_date:void 0}),ce.value=null,ve.page=1,me()},be=()=>{me()},we=()=>{ne.value=!0},ke=e=>{ue.value=e.id,de.value=!0},he=e=>{ke(e)},je=e=>{e?(_e.start_date=e[0],_e.end_date=e[1]):(_e.start_date=void 0,_e.end_date=void 0)},Oe=e=>{ve.page_size=e,ve.page=1,me()},ze=e=>{ve.page=e,me()},Ce=()=>{ne.value=!1,be(),I.success("售后申请提交成功")},Ve=e=>i(this,null,(function*(){const a=X.canDeleteWorkOrder(e);if(a.canDelete)try{yield K.confirm(`确定要删除工单"${e.title}"吗？删除后无法恢复。`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}),ie.value=!0;const a=yield X.deleteWorkOrder(e.id);if(!a.success)throw new Error(a.message||"删除失败");I.success("工单删除成功"),be()}catch(l){"cancel"!==l&&q.handleApiError(l)}finally{ie.value=!1}else I.error(a.reason||"无法删除该工单")})),De=e=>({1:"待处理",2:"处理中",3:"等待回复",4:"已解决",5:"已关闭",6:"已取消"}[e]||"未知状态"),We=e=>X.formatWorkOrderPriority(e).text,xe=e=>X.canDeleteWorkOrder(e);return v((()=>i(this,null,(function*(){yield n(),me()})))),(e,a)=>{const l=C,r=h,t=k,o=D,s=W,i=U,n=x,u=w,p=T,c=N,_=R,v=S,I=G,K=F;return m(),f("div",ae,[a[16]||(a[16]=y("div",{class:"page-header"},[y("h2",null,"售后服务"),y("p",{class:"subtitle"},"查看和管理您的售后服务申请")],-1)),y("div",le,[g(p,null,{default:b((()=>[g(u,{model:_e,inline:""},{default:b((()=>[g(t,{label:"处理状态"},{default:b((()=>[g(r,{modelValue:_e.status,"onUpdate:modelValue":a[0]||(a[0]=e=>_e.status=e),placeholder:"请选择状态",clearable:""},{default:b((()=>[(m(!0),f(j,null,O(fe.value,((e,a)=>(m(),z(l,{key:a,label:e,value:Number(a)},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),g(t,{label:"服务类型"},{default:b((()=>[g(r,{modelValue:_e.work_order_type,"onUpdate:modelValue":a[1]||(a[1]=e=>_e.work_order_type=e),placeholder:"请选择类型",clearable:""},{default:b((()=>[(m(!0),f(j,null,O(V(d),(e=>(m(),z(l,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),g(t,{label:"订单号"},{default:b((()=>[g(o,{modelValue:_e.order_no,"onUpdate:modelValue":a[2]||(a[2]=e=>_e.order_no=e),placeholder:"请输入订单号",clearable:""},null,8,["modelValue"])])),_:1}),g(t,{label:"运单号"},{default:b((()=>[g(o,{modelValue:_e.tracking_no,"onUpdate:modelValue":a[3]||(a[3]=e=>_e.tracking_no=e),placeholder:"请输入运单号",clearable:""},null,8,["modelValue"])])),_:1}),g(t,{label:"创建时间"},{default:b((()=>[g(s,{modelValue:ce.value,"onUpdate:modelValue":a[4]||(a[4]=e=>ce.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:je},null,8,["modelValue"])])),_:1}),g(t,null,{default:b((()=>[g(n,{type:"primary",onClick:ye},{default:b((()=>[g(i,null,{default:b((()=>[g(V(Y))])),_:1}),a[9]||(a[9]=P(" 搜索 "))])),_:1}),g(n,{onClick:ge},{default:b((()=>[g(i,null,{default:b((()=>[g(V(E))])),_:1}),a[10]||(a[10]=P(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])),_:1})]),y("div",re,[g(n,{type:"primary",onClick:we},{default:b((()=>[g(i,null,{default:b((()=>[g(V(A))])),_:1}),a[11]||(a[11]=P(" 申请售后服务 "))])),_:1}),g(n,{onClick:be},{default:b((()=>[g(i,null,{default:b((()=>[g(V(E))])),_:1}),a[12]||(a[12]=P(" 刷新 "))])),_:1})]),y("div",te,[g(p,null,{default:b((()=>[M((m(),z(v,{data:pe.value,stripe:"",onRowClick:he},{default:b((()=>[g(c,{prop:"title",label:"服务申请","min-width":"200","show-overflow-tooltip":""}),g(c,{prop:"work_order_type",label:"服务类型",width:"120"},{default:b((({row:e})=>[g(_,{size:"small"},{default:b((()=>[P(B(V(se)(e.work_order_type)),1)])),_:2},1024)])),_:1}),g(c,{prop:"status",label:"处理状态",width:"100"},{default:b((({row:e})=>{return[g(_,{type:(a=e.status,{1:"warning",2:"primary",3:"info",4:"success",5:"info",6:"danger"}[a]||"info"),size:"small"},{default:b((()=>[P(B(De(e.status)),1)])),_:2},1032,["type"])];var a})),_:1}),g(c,{prop:"priority",label:"紧急程度",width:"80"},{default:b((({row:e})=>{return[g(_,{type:(a=e.priority,X.formatWorkOrderPriority(a).type),size:"small"},{default:b((()=>[P(B(We(e.priority)),1)])),_:2},1032,["type"])];var a})),_:1}),g(c,{prop:"order_no",label:"关联订单",width:"150","show-overflow-tooltip":""}),g(c,{prop:"tracking_no",label:"运单号",width:"150","show-overflow-tooltip":""}),g(c,{prop:"created_at",label:"创建时间",width:"160"},{default:b((({row:e})=>{return[P(B((a=e.created_at,new Date(a).toLocaleString("zh-CN"))),1)];var a})),_:1}),g(c,{label:"操作",width:"200",fixed:"right"},{default:b((({row:e})=>{return[g(n,{type:"primary",size:"small",onClick:L((a=>ke(e)),["stop"])},{default:b((()=>a[13]||(a[13]=[P(" 查看 ")]))),_:2},1032,["onClick"]),(l=e,X.canReplyWorkOrder(l)?(m(),z(n,{key:0,type:"success",size:"small",onClick:L((a=>(e=>{ue.value=e.id,de.value=!0})(e)),["stop"])},{default:b((()=>a[14]||(a[14]=[P(" 回复 ")]))),_:2},1032,["onClick"])):H("",!0)),xe(e).canDelete?(m(),z(n,{key:1,type:"danger",size:"small",onClick:L((a=>Ve(e)),["stop"])},{default:b((()=>a[15]||(a[15]=[P(" 删除 ")]))),_:2},1032,["onClick"])):H("",!0)];var l})),_:1})])),_:1},8,["data"])),[[K,ie.value]]),y("div",oe,[g(I,{"current-page":ve.page,"onUpdate:currentPage":a[5]||(a[5]=e=>ve.page=e),"page-size":ve.page_size,"onUpdate:pageSize":a[6]||(a[6]=e=>ve.page_size=e),total:ve.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Oe,onCurrentChange:ze},null,8,["current-page","page-size","total"])])])),_:1})]),g(J,{visible:de.value,"onUpdate:visible":a[7]||(a[7]=e=>de.value=e),"work-order-id":ue.value,onRefresh:be},null,8,["visible","work-order-id"]),g(Z,{visible:ne.value,"onUpdate:visible":a[8]||(a[8]=e=>ne.value=e),onSuccess:Ce},null,8,["visible"])])}}}),[["__scopeId","data-v-130b3c7e"]]);export{se as default};
