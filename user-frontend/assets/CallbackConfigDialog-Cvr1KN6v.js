var e=(e,l,a)=>new Promise(((t,s)=>{var c=e=>{try{r(a.next(e))}catch(l){s(l)}},o=e=>{try{r(a.throw(e))}catch(l){s(l)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(c,o);r((a=a.apply(e,l)).next())}));import{_ as l}from"./index-F0f2Vd-3.js";import{d as a,j as t,r as s,X as c,p as o,N as r,o as n,w as u,a2 as i,Z as d,b as p,_ as b,C as m,e as _,H as g,u as f,b7 as v,K as h,ap as y,aq as k,aC as w,aD as x,aG as V,Y as C,M as U,ai as A}from"./vendor-BVh5F9vp.js";import{C as R}from"./callbackApi-Bno5VuFB.js";import{E as j}from"./errorHandler-Dnd-hi0l.js";const q={class:"secret-input-group"},E={class:"event-selection"},H={class:"dialog-footer"},T=l(a({__name:"CallbackConfigDialog",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(l,{emit:a}){const T=l,L=a,M=t({get:()=>T.visible,set:e=>L("update:visible",e)}),P=s(),S=s(!1),B=s(!1),Z=s(!1),z=c({callback_url:"",callback_secret:"",enabled:!0,retry_count:3,timeout_seconds:30,subscribed_events:["order_status_changed","billing_updated","ticket_replied"]}),D={callback_url:[{required:!0,message:"请输入回调URL",trigger:"blur"},{pattern:/^https?:\/\/.+/,message:"请输入有效的HTTP或HTTPS URL",trigger:"blur"}],callback_secret:[{required:!0,message:"请输入回调密钥",trigger:"blur"},{min:16,message:"密钥长度至少16位，建议32位以上",trigger:"blur"}],retry_count:[{required:!0,message:"请设置重试次数",trigger:"blur"}],timeout_seconds:[{required:!0,message:"请设置超时时间",trigger:"blur"}],subscribed_events:[{type:"array",min:1,message:"请至少选择一个订阅事件",trigger:"change"}]};o(M,(l=>e(this,null,(function*(){l&&(yield $())}))));const $=()=>e(this,null,(function*(){S.value=!0;try{const e=yield R.getCallbackConfig();if(e.success&&e.data){const l=e.data;Object.assign(z,{callback_url:l.callback_url,callback_secret:l.callback_secret||"",enabled:l.enabled,retry_count:l.retry_count,timeout_seconds:l.timeout_seconds,subscribed_events:l.subscribed_events})}}catch(e){}finally{S.value=!1}})),F=()=>{var e;M.value=!1,null==(e=P.value)||e.resetFields()},G=()=>e(this,null,(function*(){if(P.value)try{yield P.value.validate(),B.value=!0;const e=yield R.updateCallbackConfig(z);if(!e.success)throw new Error(e.message||"保存失败");U.success("回调配置保存成功"),L("success"),F()}catch(e){!1!==e&&j.handleApiError(e)}finally{B.value=!1}})),I=()=>{try{const e=function(e=32){if(!window.crypto||!window.crypto.getRandomValues)throw new Error("当前浏览器不支持安全随机数生成");const l=new Uint8Array(e);return window.crypto.getRandomValues(l),Array.from(l,(e=>e.toString(16).padStart(2,"0"))).join("")}(32),l=function(e){const l=[];let a=0;e.length>=32?a+=30:e.length>=16?(a+=20,l.push("建议使用至少32位字符的密钥")):(a+=10,l.push("密钥长度过短，建议至少16位字符"));let t=0;/[a-z]/.test(e)&&t++,/[A-Z]/.test(e)&&t++,/[0-9]/.test(e)&&t++,/[^a-zA-Z0-9]/.test(e)&&t++,a+=15*t,t<3&&l.push("建议包含大小写字母、数字和特殊字符"),e.match(/(.)\1{2,}/g)&&(a-=10,l.push("避免使用重复字符"));const s=[/123456/,/abcdef/,/qwerty/,/password/i,/secret/i];for(const o of s)if(o.test(e)){a-=15,l.push("避免使用常见的字符模式");break}let c;return a=Math.max(0,Math.min(100,a)),c=a>=80?"very_strong":a>=60?"strong":a>=40?"medium":"weak",{score:a,level:c,suggestions:l}}(e);z.callback_secret=e,U.success(`已生成${"very_strong"===l.level?"高强度":""}安全密钥`)}catch(e){U.error("生成密钥失败："+e.message)}},K=()=>e(this,null,(function*(){if(z.callback_url)try{yield A.confirm(`将向 ${z.callback_url} 发送测试回调，确定继续吗？`,"确认测试",{confirmButtonText:"发送测试",cancelButtonText:"取消",type:"info"}),Z.value=!0;const e=yield R.testCallback();if(!e.success)throw new Error(e.message||"测试失败");U.success("测试回调已发送，请检查您的服务器日志")}catch(e){"cancel"!==e&&j.handleApiError(e)}finally{Z.value=!1}else U.warning("请先输入回调URL")}));return(e,l)=>{const a=m,t=b,s=g,c=y,o=k,U=x,A=w,R=d,j=C,T=V;return n(),r(j,{modelValue:M.value,"onUpdate:modelValue":l[6]||(l[6]=e=>M.value=e),title:"回调配置",width:"600px","before-close":F},{footer:u((()=>[_("div",H,[p(s,{onClick:F},{default:u((()=>l[15]||(l[15]=[h("取消")]))),_:1}),p(s,{type:"info",onClick:K,loading:Z.value},{default:u((()=>l[16]||(l[16]=[h("测试回调")]))),_:1},8,["loading"]),p(s,{type:"primary",onClick:G,loading:B.value},{default:u((()=>l[17]||(l[17]=[h("保存配置")]))),_:1},8,["loading"])])])),default:u((()=>[i((n(),r(R,{ref_key:"formRef",ref:P,model:z,rules:D,"label-width":"120px"},{default:u((()=>[p(t,{label:"回调URL",prop:"callback_url"},{default:u((()=>[p(a,{modelValue:z.callback_url,"onUpdate:modelValue":l[0]||(l[0]=e=>z.callback_url=e),placeholder:"请输入回调URL，如：https://your-domain.com/callback"},null,8,["modelValue"])])),_:1}),p(t,{label:"回调密钥",prop:"callback_secret"},{default:u((()=>[_("div",q,[p(a,{modelValue:z.callback_secret,"onUpdate:modelValue":l[1]||(l[1]=e=>z.callback_secret=e),type:"password",placeholder:"可选，用于验证回调请求的安全性","show-password":"",class:"secret-input"},null,8,["modelValue"]),p(s,{type:"primary",onClick:I,icon:f(v),title:"生成随机密钥"},{default:u((()=>l[7]||(l[7]=[h(" 生成 ")]))),_:1},8,["icon"])]),l[8]||(l[8]=_("div",{class:"form-tip"},[_("p",null,[_("strong",null,"密钥用途："),h("用于生成HMAC-SHA256签名，验证回调请求的真实性")]),_("p",null,[_("strong",null,"获取方式：")]),_("ul",null,[_("li",null,'点击"生成"按钮自动生成安全密钥'),_("li",null,[h("使用 "),_("code",null,"openssl rand -hex 32"),h(" 命令生成")]),_("li",null,"使用密码管理器生成强密钥"),_("li",null,"自定义密钥（建议至少32位字符）")]),_("p",null,[_("strong",null,"安全建议："),h("生产环境强烈建议设置密钥，并定期更换")])],-1))])),_:1}),p(t,{label:"启用状态",prop:"enabled"},{default:u((()=>[p(c,{modelValue:z.enabled,"onUpdate:modelValue":l[2]||(l[2]=e=>z.enabled=e),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])])),_:1}),p(t,{label:"重试次数",prop:"retry_count"},{default:u((()=>[p(o,{modelValue:z.retry_count,"onUpdate:modelValue":l[3]||(l[3]=e=>z.retry_count=e),min:0,max:10,"controls-position":"right"},null,8,["modelValue"]),l[9]||(l[9]=_("div",{class:"form-tip"},"失败时的最大重试次数（0-10次）",-1))])),_:1}),p(t,{label:"超时时间",prop:"timeout_seconds"},{default:u((()=>[p(o,{modelValue:z.timeout_seconds,"onUpdate:modelValue":l[4]||(l[4]=e=>z.timeout_seconds=e),min:5,max:60,"controls-position":"right"},null,8,["modelValue"]),l[10]||(l[10]=_("div",{class:"form-tip"},"回调请求超时时间（5-60秒）",-1))])),_:1}),p(t,{label:"订阅事件",prop:"subscribed_events"},{default:u((()=>[_("div",E,[l[14]||(l[14]=_("div",{class:"form-tip",style:{"margin-bottom":"10px"}},[_("strong",null,"建议全选："),h("选择需要接收回调通知的事件类型 ")],-1)),p(A,{modelValue:z.subscribed_events,"onUpdate:modelValue":l[5]||(l[5]=e=>z.subscribed_events=e),class:"event-checkboxes"},{default:u((()=>[p(U,{label:"order_status_changed"},{default:u((()=>l[11]||(l[11]=[_("div",{class:"checkbox-content"},[_("span",{class:"checkbox-title"},"订单状态变更"),_("span",{class:"checkbox-desc"},"订单状态发生变化时通知")],-1)]))),_:1}),p(U,{label:"billing_updated"},{default:u((()=>l[12]||(l[12]=[_("div",{class:"checkbox-content"},[_("span",{class:"checkbox-title"},"计费更新"),_("span",{class:"checkbox-desc"},"订单计费信息更新时通知")],-1)]))),_:1}),p(U,{label:"ticket_replied"},{default:u((()=>l[13]||(l[13]=[_("div",{class:"checkbox-content"},[_("span",{class:"checkbox-title"},"工单回复"),_("span",{class:"checkbox-desc"},"客服工单有新回复时通知")],-1)]))),_:1})])),_:1},8,["modelValue"])])])),_:1})])),_:1},8,["model"])),[[T,S.value]])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-1d545c35"]]);export{T as default};
