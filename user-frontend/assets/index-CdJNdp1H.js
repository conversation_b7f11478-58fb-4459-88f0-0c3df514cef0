import{bx as t,K as e,by as n,bz as i,aw as o,bA as a,k as r,bB as s,bC as l,as as u,bD as d,bE as h,bF as c,aq as p,bG as f,bH as g,bI as y,bJ as v,bK as m,bL as x,a6 as _,at as b,ac as S,bM as I,bN as w,ax as M,bO as L,bP as D,bQ as C,bR as T,bS as A,bT as P,bU as k,bV as N,bW as V,bX as R,aV as z,bY as O,aQ as E,aA as B,bZ as G,b_ as H,ar as F,c as W,aE as Z,Q as Y,b$ as X,h as U,c0 as j,c1 as q,aF as K,bd as $,c2 as Q,c3 as J,av as tt,U as et,V as nt,c4 as it,ap as ot,c5 as at,R as rt,an as st,c6 as lt,c7 as ut,c8 as dt,az as ht,c9 as ct,ca as pt,cb as ft,am as gt,cc as yt,cd as vt,ce as mt,ay as xt,M as _t,m as bt,A as St,cf as It,aC as wt,cg as Mt,N as Lt,Y as Dt,a9 as Ct,aD as Tt,aG as At,ch as Pt,ci as kt,cj as Nt,ck as Vt,l as Rt,cl as zt,cm as Ot,cn as Et,co as Bt,cp as Gt,cq as Ht,cr as Ft,cs as Wt,ct as Zt,cu as Yt,cv as Xt,J as Ut,cw as jt,ao as qt,cx as Kt,cy as $t,cz as Qt,aH as Jt,cA as te,cB as ee,cC as ne,cD as ie,cE as oe,cF as ae,cG as re,Z as se,cH as le,d as ue,cI as de,cJ as he,cK as ce,cL as pe,cM as fe,cN as ge,cO as ye,cP as ve,cQ as me,cR as xe,cS as _e,X as be,w as Se,cT as Ie,cU as we,cV as Me,cW as Le,cX as De,cY as Ce,o as Te,cZ as Ae,c_ as Pe,c$ as ke,d0 as Ne,d1 as Ve,d2 as Re,d3 as ze,d4 as Oe,d5 as Ee,d6 as Be,T as Ge,d7 as He,G as Fe,d8 as We,d9 as Ze,da as Ye,db as Xe,dc as Ue,dd as je,de as qe,df as Ke,dg as $e,dh as Qe,di as Je,dj as tn,dk as en,y as nn,dl as on,bn as an,L as rn,dm as sn,dn as ln,dp as un,p as dn,B as hn,dq as cn,dr as pn,ds as fn,dt as gn,du as yn,dv as vn,dw as mn,dx as xn,dy as _n,dz as bn,dA as Sn,dB as In,dC as wn,dD as Mn,dE as Ln,dF as Dn,dG as Cn,v as Tn,dH as An,_ as Pn,dI as kn,dJ as Nn,dK as Vn,dL as Rn,dM as zn,dN as On,aB as En,dO as Bn,dP as Gn,dQ as Hn,dR as Fn,a8 as Wn,a4 as Zn,dS as Yn,dT as Xn,dU as Un,dV as jn,dW as qn,b as Kn,n as $n,dX as Qn,dY as Jn,dZ as ti,d_ as ei,d$ as ni,e0 as ii,e1 as oi,e2 as ai,e3 as ri,e4 as si,e5 as li,e6 as ui,e7 as di,z as hi,e8 as ci,e9 as pi,ea as fi,eb as gi,ec as yi,ed as vi,r as mi,ee as xi,ef as _i,eg as bi,a1 as Si,eh as Ii,ei as wi,ej as Mi,aY as Li,ek as Di,el as Ci,em as Ti,a5 as Ai,en as Pi,eo as ki,ep as Ni,eq as Vi,er as Ri,es as zi,et as Oi,eu as Ei,ev as Bi,ew as Gi,ex as Hi,ey as Fi,ez as Wi,eA as Zi,eB as Yi,j as Xi,eC as Ui,eD as ji,eE as qi,eF as Ki,eG as $i,a as Qi,s as Ji,eH as to,eI as eo,eJ as no,g as io,eK as oo,eL as ao,eM as ro,eN as so,aj as lo,eO as uo,eP as ho,E as co,eQ as po,eR as fo,eS as go,eT as yo,eU as vo,eV as mo,eW as xo,eX as _o,eY as bo,eZ as So,e_ as Io,e$ as wo,f0 as Mo,f1 as Lo,f2 as Do,bu as Co,br as To,bq as Ao,bs as Po,bt as ko,bw as No,bv as Vo,bk as Ro,bl as zo,f3 as Oo,bj as Eo,bi as Bo,bo as Go,bm as Ho}from"./install-DmVoiIn1.js";function Fo(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}function Wo(t,e){var n=t.label,i=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:Fo(i&&i.shape.points)}}var Zo=["align","verticalAlign","width","height","fontSize"],Yo=new n,Xo=t(),Uo=t();function jo(t,e,n){for(var i=0;i<n.length;i++){var o=n[i];null!=e[o]&&(t[o]=e[o])}}var qo=["x","y","rotation"],Ko=function(){function t(){this._labelList=[],this._chartViewList=[]}return t.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},t.prototype._addLabel=function(t,n,o,a,r){var s=a.style,l=a.__hostTarget.textConfig||{},u=a.getComputedTransform(),d=a.getBoundingRect().plain();e.applyTransform(d,d,u),u?Yo.setLocalTransform(u):(Yo.x=Yo.y=Yo.rotation=Yo.originX=Yo.originY=0,Yo.scaleX=Yo.scaleY=1),Yo.rotation=i(Yo.rotation);var h,c=a.__hostTarget;if(c){h=c.getBoundingRect().plain();var p=c.getComputedTransform();e.applyTransform(h,h,p)}var f=h&&c.getTextGuideLine();this._labelList.push({label:a,labelLine:f,seriesModel:o,dataIndex:t,dataType:n,layoutOption:r,computedLayoutOption:null,rect:d,hostRect:h,priority:h?h.width*h.height:0,defaultAttr:{ignore:a.ignore,labelGuideIgnore:f&&f.ignore,x:Yo.x,y:Yo.y,scaleX:Yo.scaleX,scaleY:Yo.scaleY,rotation:Yo.rotation,style:{x:s.x,y:s.y,align:s.align,verticalAlign:s.verticalAlign,width:s.width,height:s.height,fontSize:s.fontSize},cursor:a.cursor,attachedPos:l.position,attachedRot:l.rotation}})},t.prototype.addLabelsOfSeries=function(t){var e=this;this._chartViewList.push(t);var n=t.__model,i=n.get("labelLayout");(o(i)||a(i).length)&&t.group.traverse((function(t){if(t.ignore)return!0;var o=t.getTextContent(),a=r(t);o&&!o.disableLabelLayout&&e._addLabel(a.dataIndex,a.dataType,n,o,i)}))},t.prototype.updateLayoutConfig=function(t){var e=t.getWidth(),n=t.getHeight();function i(t,e){return function(){y(t,e)}}for(var a=0;a<this._labelList.length;a++){var r=this._labelList[a],l=r.label,u=l.__hostTarget,d=r.defaultAttr,h=void 0;h=(h=o(r.layoutOption)?r.layoutOption(Wo(r,u)):r.layoutOption)||{},r.computedLayoutOption=h;var c=Math.PI/180;u&&u.setTextConfig({local:!1,position:null!=h.x||null!=h.y?null:d.attachedPos,rotation:null!=h.rotate?h.rotate*c:d.attachedRot,offset:[h.dx||0,h.dy||0]});var p=!1;if(null!=h.x?(l.x=s(h.x,e),l.setStyle("x",0),p=!0):(l.x=d.x,l.setStyle("x",d.style.x)),null!=h.y?(l.y=s(h.y,n),l.setStyle("y",0),p=!0):(l.y=d.y,l.setStyle("y",d.style.y)),h.labelLinePoints){var f=u.getTextGuideLine();f&&(f.setShape({points:h.labelLinePoints}),p=!1)}Xo(l).needsUpdateLabelLine=p,l.rotation=null!=h.rotate?h.rotate*c:d.rotation,l.scaleX=d.scaleX,l.scaleY=d.scaleY;for(var g=0;g<Zo.length;g++){var v=Zo[g];l.setStyle(v,null!=h[v]?h[v]:d.style[v])}if(h.draggable){if(l.draggable=!0,l.cursor="move",u){var m=r.seriesModel;if(null!=r.dataIndex)m=r.seriesModel.getData(r.dataType).getItemModel(r.dataIndex);l.on("drag",i(u,m.getModel("labelLine")))}}else l.off("drag"),l.cursor=d.cursor}},t.prototype.layout=function(t){var e=t.getWidth(),n=t.getHeight(),i=l(this._labelList),o=u(i,(function(t){return"shiftX"===t.layoutOption.moveOverlap})),a=u(i,(function(t){return"shiftY"===t.layoutOption.moveOverlap}));d(o,0,e),h(a,0,n);var r=u(i,(function(t){return t.layoutOption.hideOverlap}));c(r)},t.prototype.processLabelsOverall=function(){var t=this;p(this._chartViewList,(function(e){var n=e.__model,i=e.ignoreLabelLineUpdate,o=n.isAnimationEnabled();e.group.traverse((function(e){if(e.ignore&&!e.forceLabelAnimation)return!0;var a=!i,r=e.getTextContent();!a&&r&&(a=Xo(r).needsUpdateLabelLine),a&&t._updateLabelLine(e,n),o&&t._animateLabels(e,n)}))}))},t.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),i=r(t),o=i.dataIndex;if(n&&null!=o){var a=e.getData(i.dataType),s=a.getItemModel(o),l={},u=a.getItemVisual(o,"style");if(u){var d=a.getVisual("drawType");l.stroke=u[d]}var h=s.getModel("labelLine");f(t,g(s),l),y(t,h)}},t.prototype._animateLabels=function(t,e){var n=t.getTextContent(),i=t.getTextGuideLine();if(n&&(t.forceLabelAnimation||!n.ignore&&!n.invisible&&!t.disableLabelAnimation&&!v(t))){var o=(f=Xo(n)).oldLayout,a=r(t),s=a.dataIndex,l={x:n.x,y:n.y,rotation:n.rotation},u=e.getData(a.dataType);if(o){n.attr(o);var d=t.prevStates;d&&(b(d,"select")>=0&&n.attr(f.oldLayoutSelect),b(d,"emphasis")>=0&&n.attr(f.oldLayoutEmphasis)),S(n,l,e,s)}else if(n.attr(l),!m(n).valueAnimation){var h=x(n.style.opacity,1);n.style.opacity=0,_(n,{style:{opacity:h}},e,s)}if(f.oldLayout=l,n.states.select){var c=f.oldLayoutSelect={};jo(c,l,qo),jo(c,n.states.select,qo)}if(n.states.emphasis){var p=f.oldLayoutEmphasis={};jo(p,l,qo),jo(p,n.states.emphasis,qo)}I(n,s,u,e,e)}if(i&&!i.ignore&&!i.invisible){o=(f=Uo(i)).oldLayout;var f,g={points:i.shape.points};o?(i.attr({shape:o}),S(i,{shape:g},e)):(i.setShape(g),i.style.strokePercent=0,_(i,{style:{strokePercent:1}},e)),f.oldLayout=g}},t}(),$o=t();function Qo(t,e,n){var i=A.createCanvas(),o=e.getWidth(),a=e.getHeight(),r=i.style;return r&&(r.position="absolute",r.left="0",r.top="0",r.width=o+"px",r.height=a+"px",i.setAttribute("data-zr-dom-id",t)),i.width=o*n,i.height=a*n,i}var Jo=function(t){function n(e,n,i){var o,a=t.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null,i=i||T,"string"==typeof e?o=Qo(e,n,i):M(e)&&(e=(o=e).id),a.id=e,a.dom=o;var r=o.style;return r&&(L(o),o.onselectstart=function(){return!1},r.padding="0",r.margin="0",r.borderWidth="0"),a.painter=n,a.dpr=i,a}return w(n,t),n.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},n.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},n.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},n.prototype.setUnpainted=function(){this.__firstTimePaint=!0},n.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=Qo("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},n.prototype.createRepaintRects=function(t,n,i,o){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var a,r=[],s=this.maxRepaintRectCount,l=!1,u=new e(0,0,0,0);function d(t){if(t.isFinite()&&!t.isZero())if(0===r.length){(n=new e(0,0,0,0)).copy(t),r.push(n)}else{for(var n,i=!1,o=1/0,a=0,d=0;d<r.length;++d){var h=r[d];if(h.intersect(t)){var c=new e(0,0,0,0);c.copy(h),c.union(t),r[d]=c,i=!0;break}if(l){u.copy(t),u.union(h);var p=t.width*t.height,f=h.width*h.height,g=u.width*u.height-p-f;g<o&&(o=g,a=d)}}if(l&&(r[a].union(t),i=!0),!i)(n=new e(0,0,0,0)).copy(t),r.push(n);l||(l=r.length>=s)}}for(var h=this.__startIndex;h<this.__endIndex;++h){if(f=t[h]){var c=f.shouldBePainted(i,o,!0,!0);(g=f.__isRendered&&(f.__dirty&D||!c)?f.getPrevPaintRect():null)&&d(g);var p=c&&(f.__dirty&D||!f.__isRendered)?f.getPaintRect():null;p&&d(p)}}for(h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var f,g;c=(f=n[h])&&f.shouldBePainted(i,o,!0,!0);if(f&&(!c||!f.__zr)&&f.__isRendered)(g=f.getPrevPaintRect())&&d(g)}do{a=!1;for(h=0;h<r.length;)if(r[h].isZero())r.splice(h,1);else{for(var y=h+1;y<r.length;)r[h].intersect(r[y])?(a=!0,r[h].union(r[y]),r.splice(y,1)):y++;h++}}while(a);return this._paintRects=r,r},n.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},n.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,o=i.style,a=this.domBack;o&&(o.width=t+"px",o.height=e+"px"),i.width=t*n,i.height=e*n,a&&(a.width=t*n,a.height=e*n,1!==n&&this.ctxBack.scale(n,n))},n.prototype.clear=function(t,e,n){var i=this.dom,o=this.ctx,a=i.width,r=i.height;e=e||this.clearColor;var s=this.motionBlur&&!t,l=this.lastFrameAlpha,u=this.dpr,d=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,a/u,r/u));var h=this.domBack;function c(t,n,i,a){if(o.clearRect(t,n,i,a),e&&"transparent"!==e){var r=void 0;if(P(e))r=(e.global||e.__width===i&&e.__height===a)&&e.__canvasGradient||k(o,e,{x:0,y:0,width:i,height:a}),e.__canvasGradient=r,e.__width=i,e.__height=a;else N(e)&&(e.scaleX=e.scaleX||u,e.scaleY=e.scaleY||u,r=V(o,e,{dirty:function(){d.setUnpainted(),d.painter.refresh()}}));o.save(),o.fillStyle=r||e,o.fillRect(t,n,i,a),o.restore()}s&&(o.save(),o.globalAlpha=l,o.drawImage(h,t,n,i,a),o.restore())}!n||s?c(0,0,a,r):n.length&&p(n,(function(t){c(t.x*u,t.y*u,t.width*u,t.height*u)}))},n}(C),ta=1e5,ea=314159,na=.01;var ia=function(){function t(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var o=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=F({},n||{}),this.dpr=n.devicePixelRatio||T,this._singleCanvas=o,this.root=t,t.style&&(L(t),t.innerHTML=""),this.storage=e;var a=this._zlevelList;this._prevDisplayList=[];var r=this._layers;if(o){var s=t,l=s.width,u=s.height;null!=n.width&&(l=n.width),null!=n.height&&(u=n.height),this.dpr=n.devicePixelRatio||1,s.width=l*this.dpr,s.height=u*this.dpr,this._width=l,this._height=u;var d=new Jo(s,this,this.dpr);d.__builtin__=!0,d.initContext(),r[314159]=d,d.zlevel=ea,a.push(ea),this._domRoot=t}else{this._width=H(t,0,n),this._height=H(t,1,n);var h=this._domRoot=function(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}(this._width,this._height);t.appendChild(h)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var o=0;o<i.length;o++){var a=i[o],r=this._layers[a];if(!r.__builtin__&&r.refresh){var s=0===o?this._backgroundColor:null;r.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,o={inHover:!0,viewWidth:this._width,viewHeight:this._height},a=0;a<e;a++){var r=t[a];r.__inHover&&(n||(n=this._hoverlayer=this.getLayer(ta)),i||(i=n.ctx).save(),R(i,r,o,a===e-1))}i&&i.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(ta)},t.prototype.paintOne=function(t,e){z(t,e)},t.prototype._paintList=function(t,e,n,i){if(this._redrawId===i){n=n||!1,this._updateLayerStatus(t);var o=this._doPaintList(t,e,n),a=o.finished,r=o.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),r&&this._paintHoverList(t),a)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;O((function(){s._paintList(t,e,n,i)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(ea).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)}))},t.prototype._doPaintList=function(t,e,n){for(var i=this,o=[],a=this._opts.useDirtyRect,r=0;r<this._zlevelList.length;r++){var s=this._zlevelList[r],l=this._layers[s];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||n)&&o.push(l)}for(var u=!0,d=!1,h=function(r){var s,l=o[r],h=l.ctx,p=a&&l.createRepaintRects(t,e,c._width,c._height),f=n?l.__startIndex:l.__drawIndex,g=!n&&l.incremental&&Date.now,y=g&&Date.now(),v=l.zlevel===c._zlevelList[0]?c._backgroundColor:null;if(l.__startIndex===l.__endIndex)l.clear(!1,v,p);else if(f===l.__startIndex){var m=t[f];m.incremental&&m.notClear&&!n||l.clear(!1,v,p)}-1===f&&(f=l.__startIndex);var x=function(e){var n={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(s=f;s<l.__endIndex;s++){var o=t[s];if(o.__inHover&&(d=!0),i._doPaintEl(o,l,a,e,n,s===l.__endIndex-1),g)if(Date.now()-y>15)break}n.prevElClipPaths&&h.restore()};if(p)if(0===p.length)s=l.__endIndex;else for(var _=c.dpr,b=0;b<p.length;++b){var S=p[b];h.save(),h.beginPath(),h.rect(S.x*_,S.y*_,S.width*_,S.height*_),h.clip(),x(S),h.restore()}else h.save(),x(),h.restore();l.__drawIndex=s,l.__drawIndex<l.__endIndex&&(u=!1)},c=this,f=0;f<o.length;f++)h(f);return E.wxa&&p(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:u,needsRefreshHover:d}},t.prototype._doPaintEl=function(t,e,n,i,o,a){var r=e.ctx;if(n){var s=t.getPaintRect();(!i||s&&s.intersect(i))&&(R(r,t,o,a),t.setPrevPaintRect(s))}else R(r,t,o,a)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=ea);var n=this._layers[t];return n||((n=new Jo("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?B(n,this._layerConfig[t],!0):this._layerConfig[t-na]&&B(n,this._layerConfig[t-na],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},t.prototype.insertLayer=function(t,e){var n=this._layers,i=this._zlevelList,o=i.length,a=this._domRoot,r=null,s=-1;if(!n[t]&&function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(o>0&&t>i[0]){for(s=0;s<o-1&&!(i[s]<t&&i[s+1]>t);s++);r=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(r){var l=r.dom;l.nextSibling?a.insertBefore(e.dom,l.nextSibling):a.appendChild(e.dom)}else a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom);e.painter||(e.painter=this)}},t.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var o=n[i];t.call(e,this._layers[o],o)}},t.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var o=n[i],a=this._layers[o];a.__builtin__&&t.call(e,a,o)}},t.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var o=n[i],a=this._layers[o];a.__builtin__||t.call(e,a,o)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){a&&(a.__endIndex!==t&&(a.__dirty=!0),a.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var n=1;n<t.length;n++){if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}}var i,o,a=null,r=0;for(o=0;o<t.length;o++){var s,l=(s=t[o]).zlevel,u=void 0;i!==l&&(i=l,r=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,r=1):u=this.getLayer(l+(r>0?na:0),this._needsManuallyCompositing),u.__builtin__||G("ZLevel "+l+" has been used by unkown layer "+u.id),u!==a&&(u.__used=!0,u.__startIndex!==o&&(u.__dirty=!0),u.__startIndex=o,u.incremental?u.__drawIndex=-1:u.__drawIndex=o,e(o),a=u),s.__dirty&D&&!s.__inHover&&(u.__dirty=!0,u.incremental&&u.__drawIndex<0&&(u.__drawIndex=o))}e(o),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,p(this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?B(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var o=this._zlevelList[i];if(o===t||o===t+na){var a=this._layers[o];B(a,n[t],!0)}}}},t.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(b(n,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts,o=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=H(o,0,i),e=H(o,1,i),n.style.display="",this._width!==t||e!==this._height){for(var a in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(a)&&this._layers[a].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(ea).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[314159].dom;var e=new Jo("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var n=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,o=e.dom.height;this.eachLayer((function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,o):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())}))}else for(var a={inHover:!1,viewWidth:this._width,viewHeight:this._height},r=this.storage.getDisplayList(!0),s=0,l=r.length;s<l;s++){var u=r[s];R(n,u,a,s===l-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}();var oa=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.hasSymbolVisual=!0,n}return w(e,t),e.prototype.getInitialData=function(t,e){return W(null,this,{useEncodeDefaulter:!0})},e.prototype.getProgressive=function(){var t=this.option.progressive;return null==t?this.option.large?5e3:this.get("progressive"):t},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return null==t?this.option.large?1e4:this.get("progressiveThreshold"):t},e.prototype.brushSelector=function(t,e,n){return n.point(e.getItemLayout(t))},e.prototype.getZLevelKey=function(){return this.getData().count()>this.getProgressiveThreshold()?this.id:""},e.type="series.scatter",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},emphasis:{scale:!0},clip:!0,select:{itemStyle:{borderColor:"#212121"}},universalTransition:{divideShape:"clone"}},e}(Z),aa=function(){return function(){}}(),ra=function(t){function n(e){var n=t.call(this,e)||this;return n._off=0,n.hoverDataIdx=-1,n}return w(n,t),n.prototype.getDefaultShape=function(){return new aa},n.prototype.reset=function(){this.notClear=!1,this._off=0},n.prototype.buildPath=function(t,e){var n,i=e.points,o=e.size,a=this.symbolProxy,r=a.shape,s=t.getContext?t.getContext():t,l=s&&o[0]<4,u=this.softClipShape;if(l)this._ctx=s;else{for(this._ctx=null,n=this._off;n<i.length;){var d=i[n++],h=i[n++];isNaN(d)||isNaN(h)||(u&&!u.contain(d,h)||(r.x=d-o[0]/2,r.y=h-o[1]/2,r.width=o[0],r.height=o[1],a.buildPath(t,r,!0)))}this.incremental&&(this._off=n,this.notClear=!0)}},n.prototype.afterBrush=function(){var t,e=this.shape,n=e.points,i=e.size,o=this._ctx,a=this.softClipShape;if(o){for(t=this._off;t<n.length;){var r=n[t++],s=n[t++];isNaN(r)||isNaN(s)||(a&&!a.contain(r,s)||o.fillRect(r-i[0]/2,s-i[1]/2,i[0],i[1]))}this.incremental&&(this._off=t,this.notClear=!0)}},n.prototype.findDataIndex=function(t,e){for(var n=this.shape,i=n.points,o=n.size,a=Math.max(o[0],4),r=Math.max(o[1],4),s=i.length/2-1;s>=0;s--){var l=2*s,u=i[l]-a/2,d=i[l+1]-r/2;if(t>=u&&e>=d&&t<=u+a&&e<=d+r)return s}return-1},n.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return t=n[0],e=n[1],i.contain(t,e)?(this.hoverDataIdx=this.findDataIndex(t,e))>=0:(this.hoverDataIdx=-1,!1)},n.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var n=this.shape,i=n.points,o=n.size,a=o[0],r=o[1],s=1/0,l=1/0,u=-1/0,d=-1/0,h=0;h<i.length;){var c=i[h++],p=i[h++];s=Math.min(c,s),u=Math.max(c,u),l=Math.min(p,l),d=Math.max(p,d)}t=this._rect=new e(s-a/2,l-r/2,u-s+a,d-l+r)}return t},n}(X),sa=function(){function t(){this.group=new Y}return t.prototype.updateData=function(t,e){this._clear();var n=this._create();n.setShape({points:t.getLayout("points")}),this._setCommon(n,t,e)},t.prototype.updateLayout=function(t){var e=t.getLayout("points");this.group.eachChild((function(t){if(null!=t.startIndex){var n=2*(t.endIndex-t.startIndex),i=4*t.startIndex*2;e=new Float32Array(e.buffer,i,n)}t.setShape("points",e),t.reset()}))},t.prototype.incrementalPrepareUpdate=function(t){this._clear()},t.prototype.incrementalUpdate=function(t,e,n){var i=this._newAdded[0],o=e.getLayout("points"),a=i&&i.shape.points;if(a&&a.length<2e4){var r=a.length,s=new Float32Array(r+o.length);s.set(a),s.set(o,r),i.endIndex=t.end,i.setShape({points:s})}else{this._newAdded=[];var l=this._create();l.startIndex=t.start,l.endIndex=t.end,l.incremental=!0,l.setShape({points:o}),this._setCommon(l,e,n)}},t.prototype.eachRendered=function(t){this._newAdded[0]&&t(this._newAdded[0])},t.prototype._create=function(){var t=new ra({cursor:"default"});return t.ignoreCoarsePointer=!0,this.group.add(t),this._newAdded.push(t),t},t.prototype._setCommon=function(t,e,n){var i=e.hostModel;n=n||{};var o=e.getVisual("symbolSize");t.setShape("size",o instanceof Array?o:[o,o]),t.softClipShape=n.clipShape||null,t.symbolProxy=U(e.getVisual("symbol"),0,0,0,0),t.setColor=t.symbolProxy.setColor;var a=t.shape.size[0]<4;t.useStyle(i.getModel("itemStyle").getItemStyle(a?["color","shadowBlur","shadowColor"]:["color"]));var s=e.getVisual("style"),l=s&&s.fill;l&&t.setColor(l);var u=r(t);u.seriesIndex=i.seriesIndex,t.on("mousemove",(function(e){u.dataIndex=null;var n=t.hoverDataIdx;n>=0&&(u.dataIndex=n+(t.startIndex||0))}))},t.prototype.remove=function(){this._clear()},t.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},t}(),la=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n){var i=t.getData();this._updateSymbolDraw(i,t).updateData(i,{clipShape:this._getClipShape(t)}),this._finished=!0},e.prototype.incrementalPrepareRender=function(t,e,n){var i=t.getData();this._updateSymbolDraw(i,t).incrementalPrepareUpdate(i),this._finished=!1},e.prototype.incrementalRender=function(t,e,n){this._symbolDraw.incrementalUpdate(t,e.getData(),{clipShape:this._getClipShape(e)}),this._finished=t.end===e.getData().count()},e.prototype.updateTransform=function(t,e,n){var i=t.getData();if(this.group.dirty(),!this._finished||i.count()>1e4)return{update:!0};var o=j("").reset(t,e,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout(i)},e.prototype.eachRendered=function(t){this._symbolDraw&&this._symbolDraw.eachRendered(t)},e.prototype._getClipShape=function(t){if(t.get("clip",!0)){var e=t.coordinateSystem;return e&&e.getArea&&e.getArea(.1)}},e.prototype._updateSymbolDraw=function(t,e){var n=this._symbolDraw,i=e.pipelineContext.large;return n&&i===this._isLargeDraw||(n&&n.remove(),n=this._symbolDraw=i?new sa:new q,this._isLargeDraw=i,this.group.removeAll()),this.group.add(n.group),n},e.prototype.remove=function(t,e){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},e.prototype.dispose=function(){},e.type="scatter",e}(K);function ua(t){t.eachSeriesByType("radar",(function(t){var e=t.getData(),n=[],i=t.coordinateSystem;if(i){var o=i.getIndicatorAxes();p(o,(function(t,a){e.each(e.mapDimension(o[a].dim),(function(t,e){n[e]=n[e]||[];var o=i.dataToPoint(t,a);n[e][a]=da(o)?o:ha(i)}))})),e.each((function(t){var o=J(n[t],(function(t){return da(t)}))||ha(i);n[t].push(o.slice()),e.setItemLayout(t,n[t])}))}}))}function da(t){return!isNaN(t[0])&&!isNaN(t[1])}function ha(t){return[t.cx,t.cy]}function ca(t){var e=t.polar;if(e){tt(e)||(e=[e]);var n=[];p(e,(function(e,i){e.indicator?(e.type&&!e.shape&&(e.shape=e.type),t.radar=t.radar||[],tt(t.radar)||(t.radar=[t.radar]),t.radar.push(e)):n.push(e)})),t.polar=n}p(t.series,(function(t){t&&"radar"===t.type&&t.polarIndex&&(t.radarIndex=t.polarIndex)}))}var pa=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n){var i=t.coordinateSystem,o=this.group,a=t.getData(),r=this._data;function s(t,e){var n=t.getItemVisual(e,"symbol")||"circle";if("none"!==n){var i=pt(t.getItemVisual(e,"symbolSize")),o=U(n,-1,-1,2,2),a=t.getItemVisual(e,"symbolRotate")||0;return o.attr({style:{strokeNoScale:!0},z2:100,scaleX:i[0]/2,scaleY:i[1]/2,rotation:a*Math.PI/180||0}),o}}function l(e,n,i,o,a,r){i.removeAll();for(var l=0;l<n.length-1;l++){var u=s(o,a);u&&(u.__dimIdx=l,e[l]?(u.setPosition(e[l]),ct[r?"initProps":"updateProps"](u,{x:n[l][0],y:n[l][1]},t,a)):u.setPosition(n[l]),i.add(u))}}function u(t){return ht(t,(function(t){return[i.cx,i.cy]}))}a.diff(r).add((function(e){var n=a.getItemLayout(e);if(n){var i=new et,o=new nt,r={shape:{points:n}};i.shape.points=u(n),o.shape.points=u(n),_(i,r,t,e),_(o,r,t,e);var s=new Y,d=new Y;s.add(o),s.add(i),s.add(d),l(o.shape.points,n,d,a,e,!0),a.setItemGraphicEl(e,s)}})).update((function(e,n){var i=r.getItemGraphicEl(n),o=i.childAt(0),s=i.childAt(1),u=i.childAt(2),d={shape:{points:a.getItemLayout(e)}};d.shape.points&&(l(o.shape.points,d.shape.points,u,a,e,!1),it(s),it(o),S(o,d,t),S(s,d,t),a.setItemGraphicEl(e,i))})).remove((function(t){o.remove(r.getItemGraphicEl(t))})).execute(),a.eachItemGraphicEl((function(t,e){var n=a.getItemModel(e),i=t.childAt(0),r=t.childAt(1),s=t.childAt(2),l=a.getItemVisual(e,"style"),u=l.fill;o.add(t),i.useStyle(ot(n.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:u})),at(i,n,"lineStyle"),at(r,n,"areaStyle");var d=n.getModel("areaStyle"),h=d.isEmpty()&&d.parentModel.isEmpty();r.ignore=h,p(["emphasis","select","blur"],(function(t){var e=n.getModel([t,"areaStyle"]),i=e.isEmpty()&&e.parentModel.isEmpty();r.ensureState(t).ignore=i&&h})),r.useStyle(ot(d.getAreaStyle(),{fill:u,opacity:.7,decal:l.decal}));var c=n.getModel("emphasis"),f=c.getModel("itemStyle").getItemStyle();s.eachChild((function(t){if(t instanceof rt){var i=t.style;t.useStyle(F({image:i.image,x:i.x,y:i.y,width:i.width,height:i.height},l))}else t.useStyle(l),t.setColor(u),t.style.strokeNoScale=!0;t.ensureState("emphasis").style=st(f);var o=a.getStore().get(a.getDimensionIndex(t.__dimIdx),e);(null==o||isNaN(o))&&(o=""),lt(t,ut(n),{labelFetcher:a.hostModel,labelDataIndex:e,labelDimIndex:t.__dimIdx,defaultText:o,inheritColor:u,defaultOpacity:l.opacity})})),dt(t,c.get("focus"),c.get("blurScope"),c.get("disabled"))})),this._data=a},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.type="radar",e}(K),fa=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.hasSymbolVisual=!0,n}return w(e,t),e.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new ft(gt(this.getData,this),gt(this.getRawData,this))},e.prototype.getInitialData=function(t,e){return yt(this,{generateCoord:"indicator_",generateCoordCount:1/0})},e.prototype.formatTooltip=function(t,e,n){var i=this.getData(),o=this.coordinateSystem.getIndicatorAxes(),a=this.getData().getName(t),r=""===a?this.name:a,s=vt(this,t);return mt("section",{header:r,sortBlocks:!0,blocks:ht(o,(function(e){var n=i.get(i.mapDimension(e.dim),t);return mt("nameValue",{markerType:"subItem",markerColor:s,name:e.name,value:n,sortParam:n})}))})},e.prototype.getTooltipPosition=function(t){if(null!=t)for(var e=this.getData(),n=this.coordinateSystem,i=e.getValues(ht(n.dimensions,(function(t){return e.mapDimension(t)})),t),o=0,a=i.length;o<a;o++)if(!isNaN(i[o])){var r=n.getIndicatorAxes();return n.coordToPoint(r[o].dataToCoord(i[o]),o)}},e.type="series.radar",e.dependencies=["radar"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid",join:"round"},label:{position:"top"},symbolSize:8},e}(Z),ga=It.value;function ya(t,e){return ot({show:e},t)}var va=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.optionUpdated=function(){var t=this.get("boundaryGap"),e=this.get("splitNumber"),n=this.get("scale"),i=this.get("axisLine"),a=this.get("axisTick"),r=this.get("axisLabel"),s=this.get("axisName"),l=this.get(["axisName","show"]),u=this.get(["axisName","formatter"]),d=this.get("axisNameGap"),h=this.get("triggerEvent"),c=ht(this.get("indicator")||[],(function(c){null!=c.max&&c.max>0&&!c.min?c.min=0:null!=c.min&&c.min<0&&!c.max&&(c.max=0);var p=s;null!=c.color&&(p=ot({color:c.color},s));var f=B(st(c),{boundaryGap:t,splitNumber:e,scale:n,axisLine:i,axisTick:a,axisLabel:r,name:c.text,showName:l,nameLocation:"end",nameGap:d,nameTextStyle:p,triggerEvent:h},!1);if(xt(u)){var g=f.name;f.name=u.replace("{value}",null!=g?g:"")}else o(u)&&(f.name=u(f.name,f));var y=new _t(f,null,this.ecModel);return bt(y,St.prototype),y.mainType="radar",y.componentIndex=this.componentIndex,y}),this);this._indicatorModels=c},e.prototype.getIndicatorModels=function(){return this._indicatorModels},e.type="radar",e.defaultOption={z:0,center:["50%","50%"],radius:"75%",startAngle:90,axisName:{show:!0},boundaryGap:[0,0],splitNumber:5,axisNameGap:15,scale:!1,shape:"polygon",axisLine:B({lineStyle:{color:"#bbb"}},ga.axisLine),axisLabel:ya(ga.axisLabel,!1),axisTick:ya(ga.axisTick,!1),splitLine:ya(ga.splitLine,!0),splitArea:ya(ga.splitArea,!0),indicator:[]},e}(wt),ma=["axisLine","axisTickLabel","axisName"],xa=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n){this.group.removeAll(),this._buildAxes(t),this._buildSplitLineAndArea(t)},e.prototype._buildAxes=function(t){var e=t.coordinateSystem,n=e.getIndicatorAxes(),i=ht(n,(function(t){var n=t.model.get("showName")?t.name:"";return new Mt(t.model,{axisName:n,position:[e.cx,e.cy],rotation:t.angle,labelDirection:-1,tickDirection:-1,nameDirection:1})}));p(i,(function(t){p(ma,t.add,t),this.group.add(t.getGroup())}),this)},e.prototype._buildSplitLineAndArea=function(t){var e=t.coordinateSystem,n=e.getIndicatorAxes();if(n.length){var i=t.get("shape"),o=t.getModel("splitLine"),a=t.getModel("splitArea"),r=o.getModel("lineStyle"),s=a.getModel("areaStyle"),l=o.get("show"),u=a.get("show"),d=r.get("color"),h=s.get("color"),c=tt(d)?d:[d],f=tt(h)?h:[h],g=[],y=[];if("circle"===i)for(var v=n[0].getTicksCoords(),m=e.cx,x=e.cy,_=0;_<v.length;_++){if(l)g[C(g,c,_)].push(new Lt({shape:{cx:m,cy:x,r:v[_].coord}}));if(u&&_<v.length-1)y[C(y,f,_)].push(new Dt({shape:{cx:m,cy:x,r0:v[_].coord,r:v[_+1].coord}}))}else{var b,S=ht(n,(function(t,n){var i=t.getTicksCoords();return b=null==b?i.length-1:Math.min(i.length-1,b),ht(i,(function(t){return e.coordToPoint(t.coord,n)}))})),I=[];for(_=0;_<=b;_++){for(var w=[],M=0;M<n.length;M++)w.push(S[M][_]);if(w[0]&&w.push(w[0].slice()),l)g[C(g,c,_)].push(new nt({shape:{points:w}}));if(u&&I)y[C(y,f,_-1)].push(new et({shape:{points:w.concat(I)}}));I=w.slice().reverse()}}var L=r.getLineStyle(),D=s.getAreaStyle();p(y,(function(t,e){this.group.add(Ct(t,{style:ot({stroke:"none",fill:f[e%f.length]},D),silent:!0}))}),this),p(g,(function(t,e){this.group.add(Ct(t,{style:ot({fill:"none",stroke:c[e%c.length]},L),silent:!0}))}),this)}function C(t,e,n){var i=n%e.length;return t[i]=t[i]||[],i}},e.type="radar",e}(Tt),_a=function(t){function e(e,n,i){var o=t.call(this,e,n,i)||this;return o.type="value",o.angle=0,o.name="",o}return w(e,t),e}(At),ba=function(){function t(t,e,n){this.dimensions=[],this._model=t,this._indicatorAxes=ht(t.getIndicatorModels(),(function(t,e){var n="indicator_"+e,i=new _a(n,new Pt);return i.name=t.get("name"),i.model=t,t.axis=i,this.dimensions.push(n),i}),this),this.resize(t,n)}return t.prototype.getIndicatorAxes=function(){return this._indicatorAxes},t.prototype.dataToPoint=function(t,e){var n=this._indicatorAxes[e];return this.coordToPoint(n.dataToCoord(t),e)},t.prototype.coordToPoint=function(t,e){var n=this._indicatorAxes[e].angle;return[this.cx+t*Math.cos(n),this.cy-t*Math.sin(n)]},t.prototype.pointToData=function(t){var e=t[0]-this.cx,n=t[1]-this.cy,i=Math.sqrt(e*e+n*n);e/=i,n/=i;for(var o,a=Math.atan2(-n,e),r=1/0,s=-1,l=0;l<this._indicatorAxes.length;l++){var u=this._indicatorAxes[l],d=Math.abs(a-u.angle);d<r&&(o=u,s=l,r=d)}return[s,+(o&&o.coordToData(i))]},t.prototype.resize=function(t,e){var n=t.get("center"),i=e.getWidth(),o=e.getHeight(),a=Math.min(i,o)/2;this.cx=s(n[0],i),this.cy=s(n[1],o),this.startAngle=t.get("startAngle")*Math.PI/180;var r=t.get("radius");(xt(r)||Nt(r))&&(r=[0,r]),this.r0=s(r[0],a),this.r=s(r[1],a),p(this._indicatorAxes,(function(t,e){t.setExtent(this.r0,this.r);var n=this.startAngle+e*Math.PI*2/this._indicatorAxes.length;n=Math.atan2(Math.sin(n),Math.cos(n)),t.angle=n}),this)},t.prototype.update=function(t,e){var n=this._indicatorAxes,i=this._model;p(n,(function(t){t.scale.setExtent(1/0,-1/0)})),t.eachSeriesByType("radar",(function(e,o){if("radar"===e.get("coordinateSystem")&&t.getComponent("radar",e.get("radarIndex"))===i){var a=e.getData();p(n,(function(t){t.scale.unionExtentFromData(a,a.mapDimension(t.dim))}))}}),this);var o=i.get("splitNumber"),a=new Pt;a.setExtent(0,o),a.setInterval(1),p(n,(function(t,e){kt(t.scale,t.model,a)}))},t.prototype.convertToPixel=function(t,e,n){return null},t.prototype.convertFromPixel=function(t,e,n){return null},t.prototype.containPoint=function(t){return!1},t.create=function(e,n){var i=[];return e.eachComponent("radar",(function(o){var a=new t(o,e,n);i.push(a),o.coordinateSystem=a})),e.eachSeriesByType("radar",(function(t){"radar"===t.get("coordinateSystem")&&(t.coordinateSystem=i[t.get("radarIndex")||0])})),i},t.dimensions=[],t}();function Sa(t){t.registerCoordinateSystem("radar",ba),t.registerComponentModel(va),t.registerComponentView(xa),t.registerVisual({seriesType:"radar",reset:function(t){var e=t.getData();e.each((function(t){e.setItemVisual(t,"legendIcon","roundRect")})),e.setVisual("legendIcon","roundRect")}})}function Ia(t,e){var n=t.isExpand?t.children:[],i=t.parentNode.children,o=t.hierNode.i?i[t.hierNode.i-1]:null;if(n.length){!function(t){var e=t.children,n=e.length,i=0,o=0;for(;--n>=0;){var a=e[n];a.hierNode.prelim+=i,a.hierNode.modifier+=i,o+=a.hierNode.change,i+=a.hierNode.shift+o}}(t);var a=(n[0].hierNode.prelim+n[n.length-1].hierNode.prelim)/2;o?(t.hierNode.prelim=o.hierNode.prelim+e(t,o),t.hierNode.modifier=t.hierNode.prelim-a):t.hierNode.prelim=a}else o&&(t.hierNode.prelim=o.hierNode.prelim+e(t,o));t.parentNode.hierNode.defaultAncestor=function(t,e,n,i){if(e){for(var o=t,a=t,r=a.parentNode.children[0],s=e,l=o.hierNode.modifier,u=a.hierNode.modifier,d=r.hierNode.modifier,h=s.hierNode.modifier;s=Da(s),a=Ca(a),s&&a;){o=Da(o),r=Ca(r),o.hierNode.ancestor=t;var c=s.hierNode.prelim+h-a.hierNode.prelim-u+i(s,a);c>0&&(Aa(Ta(s,t,n),t,c),u+=c,l+=c),h+=s.hierNode.modifier,u+=a.hierNode.modifier,l+=o.hierNode.modifier,d+=r.hierNode.modifier}s&&!Da(o)&&(o.hierNode.thread=s,o.hierNode.modifier+=h-l),a&&!Ca(r)&&(r.hierNode.thread=a,r.hierNode.modifier+=u-d,n=t)}return n}(t,o,t.parentNode.hierNode.defaultAncestor||i[0],e)}function wa(t){var e=t.hierNode.prelim+t.parentNode.hierNode.modifier;t.setLayout({x:e},!0),t.hierNode.modifier+=t.parentNode.hierNode.modifier}function Ma(t){return arguments.length?t:Pa}function La(t,e){return t-=Math.PI/2,{x:e*Math.cos(t),y:e*Math.sin(t)}}function Da(t){var e=t.children;return e.length&&t.isExpand?e[e.length-1]:t.hierNode.thread}function Ca(t){var e=t.children;return e.length&&t.isExpand?e[0]:t.hierNode.thread}function Ta(t,e,n){return t.hierNode.ancestor.parentNode===e.parentNode?t.hierNode.ancestor:n}function Aa(t,e,n){var i=n/(e.hierNode.i-t.hierNode.i);e.hierNode.change-=i,e.hierNode.shift+=n,e.hierNode.modifier+=n,e.hierNode.prelim+=n,t.hierNode.change+=i}function Pa(t,e){return t.parentNode===e.parentNode?1:2}var ka=function(){return function(){this.parentPoint=[],this.childPoints=[]}}(),Na=function(t){function e(e){return t.call(this,e)||this}return w(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new ka},e.prototype.buildPath=function(t,e){var n=e.childPoints,i=n.length,o=e.parentPoint,a=n[0],r=n[i-1];if(1===i)return t.moveTo(o[0],o[1]),void t.lineTo(a[0],a[1]);var l=e.orient,u="TB"===l||"BT"===l?0:1,d=1-u,h=s(e.forkPosition,1),c=[];c[u]=o[u],c[d]=o[d]+(r[d]-o[d])*h,t.moveTo(o[0],o[1]),t.lineTo(c[0],c[1]),t.moveTo(a[0],a[1]),c[u]=a[u],t.lineTo(c[0],c[1]),c[u]=r[u],t.lineTo(c[0],c[1]),t.lineTo(r[0],r[1]);for(var p=1;p<i-1;p++){var f=n[p];t.moveTo(f[0],f[1]),c[u]=f[u],t.lineTo(c[0],c[1])}},e}(X),Va=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n._mainGroup=new Y,n}return w(e,t),e.prototype.init=function(t,e){this._controller=new zt(e.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},e.prototype.render=function(t,e,n){var i=t.getData(),o=t.layoutInfo,a=this._mainGroup;"radial"===t.get("layout")?(a.x=o.x+o.width/2,a.y=o.y+o.height/2):(a.x=o.x,a.y=o.y),this._updateViewCoordSys(t,n),this._updateController(t,e,n);var r=this._data;i.diff(r).add((function(e){Ra(i,e)&&za(i,e,null,a,t)})).update((function(e,n){var o=r.getItemGraphicEl(n);Ra(i,e)?za(i,e,o,a,t):o&&Ba(r,n,o,a,t)})).remove((function(e){var n=r.getItemGraphicEl(e);n&&Ba(r,e,n,a,t)})).execute(),this._nodeScaleRatio=t.get("nodeScaleRatio"),this._updateNodeAndLinkScale(t),!0===t.get("expandAndCollapse")&&i.eachItemGraphicEl((function(e,i){e.off("click").on("click",(function(){n.dispatchAction({type:"treeExpandAndCollapse",seriesId:t.id,dataIndex:i})}))})),this._data=i},e.prototype._updateViewCoordSys=function(t,e){var n=t.getData(),i=[];n.each((function(t){var e=n.getItemLayout(t);!e||isNaN(e.x)||isNaN(e.y)||i.push([+e.x,+e.y])}));var o=[],a=[];Ot(i,o,a);var r=this._min,s=this._max;a[0]-o[0]==0&&(o[0]=r?r[0]:o[0]-1,a[0]=s?s[0]:a[0]+1),a[1]-o[1]==0&&(o[1]=r?r[1]:o[1]-1,a[1]=s?s[1]:a[1]+1);var l=t.coordinateSystem=new Et;l.zoomLimit=t.get("scaleLimit"),l.setBoundingRect(o[0],o[1],a[0]-o[0],a[1]-o[1]),l.setCenter(t.get("center"),e),l.setZoom(t.get("zoom")),this.group.attr({x:l.x,y:l.y,scaleX:l.scaleX,scaleY:l.scaleY}),this._min=o,this._max=a},e.prototype._updateController=function(t,e,n){var i=this,o=this._controller,a=this._controllerHost,r=this.group;o.setPointerChecker((function(e,i,o){var a=r.getBoundingRect();return a.applyTransform(r.transform),a.contain(i,o)&&!Bt(e,n,t)})),o.enable(t.get("roam")),a.zoomLimit=t.get("scaleLimit"),a.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",(function(e){Gt(a,e.dx,e.dy),n.dispatchAction({seriesId:t.id,type:"treeRoam",dx:e.dx,dy:e.dy})})).on("zoom",(function(e){Ht(a,e.scale,e.originX,e.originY),n.dispatchAction({seriesId:t.id,type:"treeRoam",zoom:e.scale,originX:e.originX,originY:e.originY}),i._updateNodeAndLinkScale(t),n.updateLabelLayout()}))},e.prototype._updateNodeAndLinkScale=function(t){var e=t.getData(),n=this._getNodeGlobalScale(t);e.eachItemGraphicEl((function(t,e){t.setSymbolScale(n)}))},e.prototype._getNodeGlobalScale=function(t){var e=t.coordinateSystem;if("view"!==e.type)return 1;var n=this._nodeScaleRatio,i=e.scaleX||1;return((e.getZoom()-1)*n+1)/i},e.prototype.dispose=function(){this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype.remove=function(){this._mainGroup.removeAll(),this._data=null},e.type="tree",e}(K);function Ra(t,e){var n=t.getItemLayout(e);return n&&!isNaN(n.x)&&!isNaN(n.y)}function za(t,e,n,i,o){var a=!n,s=t.tree.getNodeByDataIndex(e),l=s.getModel(),u=s.getVisual("style").fill,d=!1===s.isExpand&&0!==s.children.length?u:"#fff",h=t.tree.root,c=s.parentNode===h?s:s.parentNode||s,p=t.getItemGraphicEl(c.dataIndex),f=c.getLayout(),g=p?{x:p.__oldX,y:p.__oldY,rawX:p.__radialOldRawX,rawY:p.__radialOldRawY}:f,y=s.getLayout();a?((n=new Ft(t,e,null,{symbolInnerColor:d,useNameLabel:!0})).x=g.x,n.y=g.y):n.updateData(t,e,null,{symbolInnerColor:d,useNameLabel:!0}),n.__radialOldRawX=n.__radialRawX,n.__radialOldRawY=n.__radialRawY,n.__radialRawX=y.rawX,n.__radialRawY=y.rawY,i.add(n),t.setItemGraphicEl(e,n),n.__oldX=n.x,n.__oldY=n.y,S(n,{x:y.x,y:y.y},o);var v=n.getSymbolPath();if("radial"===o.get("layout")){var m=h.children[0],x=m.getLayout(),_=m.children.length,b=void 0,I=void 0;if(y.x===x.x&&!0===s.isExpand&&m.children.length){var w={x:(m.children[0].getLayout().x+m.children[_-1].getLayout().x)/2,y:(m.children[0].getLayout().y+m.children[_-1].getLayout().y)/2};(b=Math.atan2(w.y-x.y,w.x-x.x))<0&&(b=2*Math.PI+b),(I=w.x<x.x)&&(b-=Math.PI)}else(b=Math.atan2(y.y-x.y,y.x-x.x))<0&&(b=2*Math.PI+b),0===s.children.length||0!==s.children.length&&!1===s.isExpand?(I=y.x<x.x)&&(b-=Math.PI):(I=y.x>x.x)||(b-=Math.PI);var M=I?"left":"right",L=l.getModel("label"),D=L.get("rotate"),C=D*(Math.PI/180),T=v.getTextContent();T&&(v.setTextConfig({position:L.get("position")||M,rotation:null==D?-b:C,origin:"center"}),T.setStyle("verticalAlign","middle"))}var A=l.get(["emphasis","focus"]),P="relative"===A?Wt(s.getAncestorsIndices(),s.getDescendantIndices()):"ancestor"===A?s.getAncestorsIndices():"descendant"===A?s.getDescendantIndices():null;P&&(r(n).focus=P),function(t,e,n,i,o,a,r,s){var l=e.getModel(),u=t.get("edgeShape"),d=t.get("layout"),h=t.getOrient(),c=t.get(["lineStyle","curveness"]),p=t.get("edgeForkPosition"),f=l.getModel("lineStyle").getLineStyle(),g=i.__edge;if("curve"===u)e.parentNode&&e.parentNode!==n&&(g||(g=i.__edge=new Ut({shape:Ga(d,h,c,o,o)})),S(g,{shape:Ga(d,h,c,a,r)},t));else if("polyline"===u&&"orthogonal"===d&&e!==n&&e.children&&0!==e.children.length&&!0===e.isExpand){for(var y=e.children,v=[],m=0;m<y.length;m++){var x=y[m].getLayout();v.push([x.x,x.y])}g||(g=i.__edge=new Na({shape:{parentPoint:[r.x,r.y],childPoints:[[r.x,r.y]],orient:h,forkPosition:p}})),S(g,{shape:{parentPoint:[r.x,r.y],childPoints:v}},t)}g&&("polyline"!==u||e.isExpand)&&(g.useStyle(ot({strokeNoScale:!0,fill:null},f)),at(g,l,"lineStyle"),jt(g),s.add(g))}(o,s,h,n,g,f,y,i),n.__edge&&(n.onHoverStateChange=function(e){if("blur"!==e){var i=s.parentNode&&t.getItemGraphicEl(s.parentNode.dataIndex);i&&i.hoverState===Zt||Yt(n.__edge,e)}})}function Oa(t,e,n,i,o){var a=Ea(e.tree.root,t),r=a.source,s=a.sourceLayout,l=e.getItemGraphicEl(t.dataIndex);if(l){var u=e.getItemGraphicEl(r.dataIndex).__edge,d=l.__edge||(!1===r.isExpand||1===r.children.length?u:void 0),h=i.get("edgeShape"),c=i.get("layout"),p=i.get("orient"),f=i.get(["lineStyle","curveness"]);d&&("curve"===h?Xt(d,{shape:Ga(c,p,f,s,s),style:{opacity:0}},i,{cb:function(){n.remove(d)},removeOpt:o}):"polyline"===h&&"orthogonal"===i.get("layout")&&Xt(d,{shape:{parentPoint:[s.x,s.y],childPoints:[[s.x,s.y]]},style:{opacity:0}},i,{cb:function(){n.remove(d)},removeOpt:o}))}}function Ea(t,e){for(var n,i=e.parentNode===t?e:e.parentNode||e;null==(n=i.getLayout());)i=i.parentNode===t?i:i.parentNode||i;return{source:i,sourceLayout:n}}function Ba(t,e,n,i,o){var a=t.tree.getNodeByDataIndex(e),r=Ea(t.tree.root,a).sourceLayout,s={duration:o.get("animationDurationUpdate"),easing:o.get("animationEasingUpdate")};Xt(n,{x:r.x+1,y:r.y+1},o,{cb:function(){i.remove(n),t.setItemGraphicEl(e,null)},removeOpt:s}),n.fadeOut(null,t.hostModel,{fadeLabel:!0,animation:s}),a.children.forEach((function(e){Oa(e,t,i,o,s)})),Oa(a,t,i,o,s)}function Ga(t,e,n,i,o){var a,r,s,l,u,d,h,c;if("radial"===t){u=i.rawX,h=i.rawY,d=o.rawX,c=o.rawY;var p=La(u,h),f=La(u,h+(c-h)*n),g=La(d,c+(h-c)*n),y=La(d,c);return{x1:p.x||0,y1:p.y||0,x2:y.x||0,y2:y.y||0,cpx1:f.x||0,cpy1:f.y||0,cpx2:g.x||0,cpy2:g.y||0}}return u=i.x,h=i.y,d=o.x,c=o.y,"LR"!==e&&"RL"!==e||(a=u+(d-u)*n,r=h,s=d+(u-d)*n,l=c),"TB"!==e&&"BT"!==e||(a=u,r=h+(c-h)*n,s=d,l=c+(h-c)*n),{x1:u,y1:h,x2:d,y2:c,cpx1:a,cpy1:r,cpx2:s,cpy2:l}}var Ha=t();function Fa(t){var e=t.mainData,n=t.datas;n||(n={main:e},t.datasAttr={main:"data"}),t.datas=t.mainData=null,ja(e,n,t),p(n,(function(n){p(e.TRANSFERABLE_METHODS,(function(e){n.wrapMethod(e,qt(Wa,t))}))})),e.wrapMethod("cloneShallow",qt(Ya,t)),p(e.CHANGABLE_METHODS,(function(n){e.wrapMethod(n,qt(Za,t))})),Kt(n[e.dataType]===e)}function Wa(t,e){if(Ha(i=this).mainData===i){var n=F({},Ha(this).datas);n[this.dataType]=e,ja(e,n,t)}else qa(e,this.dataType,Ha(this).mainData,t);var i;return e}function Za(t,e){return t.struct&&t.struct.update(),e}function Ya(t,e){return p(Ha(e).datas,(function(n,i){n!==e&&qa(n.cloneShallow(),i,e,t)})),e}function Xa(t){var e=Ha(this).mainData;return null==t||null==e?e:Ha(e).datas[t]}function Ua(){var t=Ha(this).mainData;return null==t?[{data:t}]:ht(a(Ha(t).datas),(function(e){return{type:e,data:Ha(t).datas[e]}}))}function ja(t,e,n){Ha(t).datas={},p(e,(function(e,i){qa(e,i,t,n)}))}function qa(t,e,n,i){Ha(n).datas[e]=t,Ha(t).mainData=n,t.dataType=e,i.struct&&(t[i.structAttr]=i.struct,i.struct[i.datasAttr[e]]=t),t.getLinkedData=Xa,t.getLinkedDataAll=Ua}var Ka=function(){function t(t,e){this.depth=0,this.height=0,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.isExpand=!1,this.name=t||"",this.hostTree=e}return t.prototype.isRemoved=function(){return this.dataIndex<0},t.prototype.eachNode=function(t,e,n){o(t)&&(n=e,e=t,t=null),xt(t=t||{})&&(t={order:t});var i,a=t.order||"preorder",r=this[t.attr||"children"];"preorder"===a&&(i=e.call(n,this));for(var s=0;!i&&s<r.length;s++)r[s].eachNode(t,e,n);"postorder"===a&&e.call(n,this)},t.prototype.updateDepthAndHeight=function(t){var e=0;this.depth=t;for(var n=0;n<this.children.length;n++){var i=this.children[n];i.updateDepthAndHeight(t+1),i.height>e&&(e=i.height)}this.height=e+1},t.prototype.getNodeById=function(t){if(this.getId()===t)return this;for(var e=0,n=this.children,i=n.length;e<i;e++){var o=n[e].getNodeById(t);if(o)return o}},t.prototype.contains=function(t){if(t===this)return!0;for(var e=0,n=this.children,i=n.length;e<i;e++){var o=n[e].contains(t);if(o)return o}},t.prototype.getAncestors=function(t){for(var e=[],n=t?this:this.parentNode;n;)e.push(n),n=n.parentNode;return e.reverse(),e},t.prototype.getAncestorsIndices=function(){for(var t=[],e=this;e;)t.push(e.dataIndex),e=e.parentNode;return t.reverse(),t},t.prototype.getDescendantIndices=function(){var t=[];return this.eachNode((function(e){t.push(e.dataIndex)})),t},t.prototype.getValue=function(t){var e=this.hostTree.data;return e.getStore().get(e.getDimensionIndex(t||"value"),this.dataIndex)},t.prototype.setLayout=function(t,e){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,t,e)},t.prototype.getLayout=function(){return this.hostTree.data.getItemLayout(this.dataIndex)},t.prototype.getModel=function(t){if(!(this.dataIndex<0))return this.hostTree.data.getItemModel(this.dataIndex).getModel(t)},t.prototype.getLevelModel=function(){return(this.hostTree.levelModels||[])[this.depth]},t.prototype.setVisual=function(t,e){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,t,e)},t.prototype.getVisual=function(t){return this.hostTree.data.getItemVisual(this.dataIndex,t)},t.prototype.getRawIndex=function(){return this.hostTree.data.getRawIndex(this.dataIndex)},t.prototype.getId=function(){return this.hostTree.data.getId(this.dataIndex)},t.prototype.getChildIndex=function(){if(this.parentNode){for(var t=this.parentNode.children,e=0;e<t.length;++e)if(t[e]===this)return e;return-1}return-1},t.prototype.isAncestorOf=function(t){for(var e=t.parentNode;e;){if(e===this)return!0;e=e.parentNode}return!1},t.prototype.isDescendantOf=function(t){return t!==this&&t.isAncestorOf(this)},t}(),$a=function(){function t(t){this.type="tree",this._nodes=[],this.hostModel=t}return t.prototype.eachNode=function(t,e,n){this.root.eachNode(t,e,n)},t.prototype.getNodeByDataIndex=function(t){var e=this.data.getRawIndex(t);return this._nodes[e]},t.prototype.getNodeById=function(t){return this.root.getNodeById(t)},t.prototype.update=function(){for(var t=this.data,e=this._nodes,n=0,i=e.length;n<i;n++)e[n].dataIndex=-1;for(n=0,i=t.count();n<i;n++)e[t.getRawIndex(n)].dataIndex=n},t.prototype.clearLayouts=function(){this.data.clearItemLayouts()},t.createTree=function(e,n,i){var o=new t(n),a=[],r=1;!function t(e,n){var i=e.value;r=Math.max(r,tt(i)?i.length:1),a.push(e);var s=new Ka($t(e.name,""),o);n?function(t,e){var n=e.children;if(t.parentNode===e)return;n.push(t),t.parentNode=e}(s,n):o.root=s,o._nodes.push(s);var l=e.children;if(l)for(var u=0;u<l.length;u++)t(l[u],s)}(e),o.root.updateDepthAndHeight(0);var s=Qt(a,{coordDimensions:["value"],dimensionsCount:r}).dimensions,l=new Jt(s,n);return l.initData(a),i&&i(l),Fa({mainData:l,struct:o,structAttr:"tree"}),o.update(),o},t}();function Qa(t,e,n){if(t&&b(e,t.type)>=0){var i=n.getData().tree.root,o=t.targetNode;if(xt(o)&&(o=i.getNodeById(o)),o&&i.contains(o))return{node:o};var a=t.targetNodeId;if(null!=a&&(o=i.getNodeById(a)))return{node:o}}}function Ja(t){for(var e=[];t;)(t=t.parentNode)&&e.push(t);return e.reverse()}function tr(t,e){var n=Ja(t);return b(n,e)>=0}function er(t,e){for(var n=[];t;){var i=t.dataIndex;n.push({name:t.name,dataIndex:i,value:e.getRawValue(i)}),t=t.parentNode}return n.reverse(),n}var nr=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.hasSymbolVisual=!0,e.ignoreStyleOnData=!0,e}return w(e,t),e.prototype.getInitialData=function(t){var e={name:t.name,children:t.data},n=t.leaves||{},i=new _t(n,this,this.ecModel),o=$a.createTree(e,this,(function(t){t.wrapMethod("getItemModel",(function(t,e){var n=o.getNodeByDataIndex(e);return n&&n.children.length&&n.isExpand||(t.parentModel=i),t}))}));var a=0;o.eachNode("preorder",(function(t){t.depth>a&&(a=t.depth)}));var r=t.expandAndCollapse&&t.initialTreeDepth>=0?t.initialTreeDepth:a;return o.root.eachNode("preorder",(function(t){var e=t.hostTree.data.getRawDataItem(t.dataIndex);t.isExpand=e&&null!=e.collapsed?!e.collapsed:t.depth<=r})),o.data},e.prototype.getOrient=function(){var t=this.get("orient");return"horizontal"===t?t="LR":"vertical"===t&&(t="TB"),t},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.formatTooltip=function(t,e,n){for(var i=this.getData().tree,o=i.root.children[0],a=i.getNodeByDataIndex(t),r=a.getValue(),s=a.name;a&&a!==o;)s=a.parentNode.name+"."+s,a=a.parentNode;return mt("nameValue",{name:s,value:r,noValue:isNaN(r)||null==r})},e.prototype.getDataParams=function(e){var n=t.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(e);return n.treeAncestors=er(i,this),n.collapsed=!i.isExpand,n},e.type="series.tree",e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",edgeShape:"curve",edgeForkPosition:"50%",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderWidth:1.5},label:{show:!0},animationEasing:"linear",animationDuration:700,animationDurationUpdate:500},e}(Z);function ir(t,e){for(var n,i=[t];n=i.pop();)if(e(n),n.isExpand){var o=n.children;if(o.length)for(var a=o.length-1;a>=0;a--)i.push(o[a])}}function or(t,e){t.eachSeriesByType("tree",(function(t){!function(t,e){var n=function(t,e){return Rt(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}(t,e);t.layoutInfo=n;var i=t.get("layout"),o=0,a=0,r=null;"radial"===i?(o=2*Math.PI,a=Math.min(n.height,n.width)/2,r=Ma((function(t,e){return(t.parentNode===e.parentNode?1:2)/t.depth}))):(o=n.width,a=n.height,r=Ma());var s=t.getData().tree.root,l=s.children[0];if(l){!function(t){var e=t;e.hierNode={defaultAncestor:null,ancestor:e,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};for(var n,i,o=[e];n=o.pop();)if(i=n.children,n.isExpand&&i.length)for(var a=i.length-1;a>=0;a--){var r=i[a];r.hierNode={defaultAncestor:null,ancestor:r,prelim:0,modifier:0,change:0,shift:0,i:a,thread:null},o.push(r)}}(s),function(t,e,n){for(var i,o=[t],a=[];i=o.pop();)if(a.push(i),i.isExpand){var r=i.children;if(r.length)for(var s=0;s<r.length;s++)o.push(r[s])}for(;i=a.pop();)e(i,n)}(l,Ia,r),s.hierNode.modifier=-l.hierNode.prelim,ir(l,wa);var u=l,d=l,h=l;ir(l,(function(t){var e=t.getLayout().x;e<u.getLayout().x&&(u=t),e>d.getLayout().x&&(d=t),t.depth>h.depth&&(h=t)}));var c=u===d?1:r(u,d)/2,p=c-u.getLayout().x,f=0,g=0,y=0,v=0;if("radial"===i)f=o/(d.getLayout().x+c+p),g=a/(h.depth-1||1),ir(l,(function(t){y=(t.getLayout().x+p)*f,v=(t.depth-1)*g;var e=La(y,v);t.setLayout({x:e.x,y:e.y,rawX:y,rawY:v},!0)}));else{var m=t.getOrient();"RL"===m||"LR"===m?(g=a/(d.getLayout().x+c+p),f=o/(h.depth-1||1),ir(l,(function(t){v=(t.getLayout().x+p)*g,y="LR"===m?(t.depth-1)*f:o-(t.depth-1)*f,t.setLayout({x:y,y:v},!0)}))):"TB"!==m&&"BT"!==m||(f=o/(d.getLayout().x+c+p),g=a/(h.depth-1||1),ir(l,(function(t){y=(t.getLayout().x+p)*f,v="TB"===m?(t.depth-1)*g:a-(t.depth-1)*g,t.setLayout({x:y,y:v},!0)})))}}}(t,e)}))}function ar(t){t.eachSeriesByType("tree",(function(t){var e=t.getData();e.tree.eachNode((function(t){var n=t.getModel().getModel("itemStyle").getItemStyle(),i=e.ensureUniqueItemVisual(t.dataIndex,"style");F(i,n)}))}))}var rr=["treemapZoomToNode","treemapRender","treemapMove"];function sr(t){var e=t.getData().tree,n={};e.eachNode((function(e){for(var i=e;i&&i.depth>1;)i=i.parentNode;var o=ne(t.ecModel,i.name||i.dataIndex+"",n);e.setVisual("decal",o)}))}var lr=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.preventUsingHoverLayer=!0,n}return w(e,t),e.prototype.getInitialData=function(t,e){var n={name:t.name,children:t.data};ur(n);var i=t.levels||[],o=this.designatedVisualItemStyle={},a=new _t({itemStyle:o},this,e);i=t.levels=function(t,e){var n,i,o=oe(e.get("color")),a=oe(e.get(["aria","decal","decals"]));if(!o)return;t=t||[],p(t,(function(t){var e=new _t(t),o=e.get("color"),a=e.get("decal");(e.get(["itemStyle","color"])||o&&"none"!==o)&&(n=!0),(e.get(["itemStyle","decal"])||a&&"none"!==a)&&(i=!0)}));var r=t[0]||(t[0]={});n||(r.color=o.slice());!i&&a&&(r.decal=a.slice());return t}(i,e);var r=ht(i||[],(function(t){return new _t(t,a,e)}),this),s=$a.createTree(n,this,(function(t){t.wrapMethod("getItemModel",(function(t,e){var n=s.getNodeByDataIndex(e),i=n?r[n.depth]:null;return t.parentModel=i||a,t}))}));return s.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.formatTooltip=function(t,e,n){var i=this.getData(),o=this.getRawValue(t),a=i.getName(t);return mt("nameValue",{name:a,value:o})},e.prototype.getDataParams=function(e){var n=t.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(e);return n.treeAncestors=er(i,this),n.treePathInfo=n.treeAncestors,n},e.prototype.setLayoutInfo=function(t){this.layoutInfo=this.layoutInfo||{},F(this.layoutInfo,t)},e.prototype.mapIdToIndex=function(t){var e=this._idIndexMap;e||(e=this._idIndexMap=ie(),this._idIndexMapCount=0);var n=e.get(t);return null==n&&e.set(t,n=this._idIndexMapCount++),n},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var e=this.getRawData().tree.root;t&&(t===e||e.contains(t))||(this._viewRoot=e)},e.prototype.enableAriaDecal=function(){sr(this)},e.type="series.treemap",e.layoutMode="box",e.defaultOption={progressive:0,left:"center",top:"middle",width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.1024,scaleLimit:null,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",textStyle:{color:"#fff"}},emphasis:{itemStyle:{color:"rgba(0,0,0,0.9)"}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",overflow:"truncate"},upperLabel:{show:!1,position:[0,"50%"],height:20,overflow:"truncate",verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],overflow:"truncate",verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},e}(Z);function ur(t){var e=0;p(t.children,(function(t){ur(t);var n=t.value;tt(n)&&(n=n[0]),e+=n}));var n=t.value;tt(n)&&(n=n[0]),(null==n||isNaN(n))&&(n=e),n<0&&(n=0),tt(t.value)?t.value[0]=n:t.value=n}var dr=function(){function t(t){this.group=new Y,t.add(this.group)}return t.prototype.render=function(t,e,n,i){var o=t.getModel("breadcrumb"),a=this.group;if(a.removeAll(),o.get("show")&&n){var r=o.getModel("itemStyle"),s=o.getModel("emphasis"),l=r.getModel("textStyle"),u=s.getModel(["itemStyle","textStyle"]),d={pos:{left:o.get("left"),right:o.get("right"),top:o.get("top"),bottom:o.get("bottom")},box:{width:e.getWidth(),height:e.getHeight()},emptyItemWidth:o.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(n,d,l),this._renderContent(t,d,r,s,l,u,i),ae(a,d.pos,d.box)}},t.prototype._prepare=function(t,e,n){for(var i=t;i;i=i.parentNode){var o=$t(i.getModel().get("name"),""),a=n.getTextRect(o),r=Math.max(a.width+16,e.emptyItemWidth);e.totalWidth+=r+8,e.renderList.push({node:i,text:o,width:r})}},t.prototype._renderContent=function(t,e,n,i,o,a,r){for(var s=0,l=e.emptyItemWidth,u=t.get(["breadcrumb","height"]),d=re(e.pos,e.box),h=e.totalWidth,c=e.renderList,p=i.getModel("itemStyle").getItemStyle(),f=c.length-1;f>=0;f--){var g=c[f],y=g.node,v=g.width,m=g.text;h>d.width&&(h-=v-l,v=l,m=null);var x=new et({shape:{points:hr(s,0,v,u,f===c.length-1,0===f)},style:ot(n.getItemStyle(),{lineJoin:"bevel"}),textContent:new se({style:ue(o,{text:m})}),textConfig:{position:"inside"},z2:1e4*le,onclick:qt(r,y)});x.disableLabelAnimation=!0,x.getTextContent().ensureState("emphasis").style=ue(a,{text:m}),x.ensureState("emphasis").style=p,dt(x,i.get("focus"),i.get("blurScope"),i.get("disabled")),this.group.add(x),cr(x,t,y),s+=v+8}},t.prototype.remove=function(){this.group.removeAll()},t}();function hr(t,e,n,i,o,a){var r=[[o?t:t-5,e],[t+n,e],[t+n,e+i],[o?t:t-5,e+i]];return!a&&r.splice(2,0,[t+n+5,e+i/2]),!o&&r.push([t,e+i/2]),r}function cr(t,e,n){r(t).eventData={componentType:"series",componentSubType:"treemap",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:n&&n.dataIndex,name:n&&n.name},treePathInfo:n&&er(n,e)}}var pr=function(){function t(){this._storage=[],this._elExistsMap={}}return t.prototype.add=function(t,e,n,i,o){return!this._elExistsMap[t.id]&&(this._elExistsMap[t.id]=!0,this._storage.push({el:t,target:e,duration:n,delay:i,easing:o}),!0)},t.prototype.finished=function(t){return this._finishedCallback=t,this},t.prototype.start=function(){for(var t=this,e=this._storage.length,n=function(){--e<=0&&(t._storage.length=0,t._elExistsMap={},t._finishedCallback&&t._finishedCallback())},i=0,o=this._storage.length;i<o;i++){var a=this._storage[i];a.el.animateTo(a.target,{duration:a.duration,delay:a.delay,easing:a.easing,setToFinal:!0,done:n,aborted:n})}return this},t}();var fr=Y,gr=be,yr="label",vr="upperLabel",mr=10*le,xr=2*le,_r=3*le,br=me([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Sr=function(t){var e=br(t);return e.stroke=e.fill=e.lineWidth=null,e},Ir=t(),wr=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e._state="ready",e._storage={nodeGroup:[],background:[],content:[]},e}return w(n,t),n.prototype.render=function(t,e,n,i){var o=e.findComponents({mainType:"series",subType:"treemap",query:i});if(!(b(o,t)<0)){this.seriesModel=t,this.api=n,this.ecModel=e;var a=Qa(i,["treemapZoomToNode","treemapRootToNode"],t),r=i&&i.type,s=t.layoutInfo,l=!this._oldTree,u=this._storage,d="treemapRootToNode"===r&&a&&u?{rootNodeGroup:u.nodeGroup[a.node.getRawIndex()],direction:i.direction}:null,h=this._giveContainerGroup(s),c=t.get("animation"),p=this._doRender(h,t,d);!c||l||r&&"treemapZoomToNode"!==r&&"treemapRootToNode"!==r?p.renderFinally():this._doAnimation(h,p,t,d),this._resetController(n),this._renderBreadcrumb(t,n,a)}},n.prototype._giveContainerGroup=function(t){var e=this._containerGroup;return e||(e=this._containerGroup=new fr,this._initEvents(e),this.group.add(e)),e.x=t.x,e.y=t.y,e},n.prototype._doRender=function(t,e,n){var i=e.getData().tree,o=this._oldTree,a={nodeGroup:[],background:[],content:[]},s={nodeGroup:[],background:[],content:[]},l=this._storage,u=[];function d(t,i,o,d){return function(t,e,n,i,o,a,s,l,u,d){if(!s)return;var h=s.getLayout(),c=t.getData(),p=s.getModel();if(c.setItemGraphicEl(s.dataIndex,null),!h||!h.isInView)return;var f=h.width,g=h.height,y=h.borderWidth,v=h.invisible,m=s.getRawIndex(),x=l&&l.getRawIndex(),_=s.viewChildren,b=h.upperHeight,S=_&&_.length,I=p.getModel("itemStyle"),w=p.getModel(["emphasis","itemStyle"]),M=p.getModel(["blur","itemStyle"]),L=p.getModel(["select","itemStyle"]),D=I.get("borderRadius")||0,C=H("nodeGroup",fr);if(!C)return;if(u.add(C),C.x=h.x||0,C.y=h.y||0,C.markRedraw(),Ir(C).nodeWidth=f,Ir(C).nodeHeight=g,h.isAboveViewRoot)return C;var T=H("background",gr,d,xr);T&&z(C,T,S&&h.upperLabelHeight);var A=p.getModel("emphasis"),P=A.get("focus"),k=A.get("blurScope"),N=A.get("disabled"),V="ancestor"===P?s.getAncestorsIndices():"descendant"===P?s.getDescendantIndices():P;if(S)ge(C)&&ye(C,!1),T&&(ye(T,!N),c.setItemGraphicEl(s.dataIndex,T),ve(T,V,k));else{var R=H("content",gr,d,_r);R&&O(C,R),T.disableMorphing=!0,T&&ge(T)&&ye(T,!1),ye(C,!N),c.setItemGraphicEl(s.dataIndex,C),ve(C,V,k)}return C;function z(e,n,i){var o=r(n);if(o.dataIndex=s.dataIndex,o.seriesIndex=t.seriesIndex,n.setShape({x:0,y:0,width:f,height:g,r:D}),v)E(n);else{n.invisible=!1;var a=s.getVisual("style"),l=a.stroke,u=Sr(I);u.fill=l;var d=br(w);d.fill=w.get("borderColor");var h=br(M);h.fill=M.get("borderColor");var c=br(L);if(c.fill=L.get("borderColor"),i){var p=f-2*y;B(n,l,a.opacity,{x:y,y:0,width:p,height:b})}else n.removeTextContent();n.setStyle(u),n.ensureState("emphasis").style=d,n.ensureState("blur").style=h,n.ensureState("select").style=c,jt(n)}e.add(n)}function O(e,n){var i=r(n);i.dataIndex=s.dataIndex,i.seriesIndex=t.seriesIndex;var o=Math.max(f-2*y,0),a=Math.max(g-2*y,0);if(n.culling=!0,n.setShape({x:y,y:y,width:o,height:a,r:D}),v)E(n);else{n.invisible=!1;var l=s.getVisual("style"),u=l.fill,d=Sr(I);d.fill=u,d.decal=l.decal;var h=br(w),c=br(M),p=br(L);B(n,u,l.opacity,null),n.setStyle(d),n.ensureState("emphasis").style=h,n.ensureState("blur").style=c,n.ensureState("select").style=p,jt(n)}e.add(n)}function E(t){!t.invisible&&a.push(t)}function B(e,n,i,o){var a=p.getModel(o?vr:yr),r=$t(p.get("name"),null),l=a.getShallow("show");lt(e,ut(p,o?vr:yr),{defaultText:l?r:null,inheritColor:n,defaultOpacity:i,labelFetcher:t,labelDataIndex:s.dataIndex});var u=e.getTextContent();if(u){var d=u.style,c=xe(d.padding||0);o&&(e.setTextConfig({layoutRect:o}),u.disableLabelLayout=!0),u.beforeUpdate=function(){var t=Math.max((o?o.width:e.shape.width)-c[1]-c[3],0),n=Math.max((o?o.height:e.shape.height)-c[0]-c[2],0);d.width===t&&d.height===n||u.setStyle({width:t,height:n})},d.truncateMinChar=2,d.lineOverflow="truncate",G(d,o,h);var f=u.getState("emphasis");G(f?f.style:null,o,h)}}function G(e,n,i){var o=e?e.text:null;if(!n&&i.isLeafRoot&&null!=o){var a=t.get("drillDownIcon",!0);e.text=a?a+" "+o:o}}function H(t,i,a,r){var s=null!=x&&n[t][x],l=o[t];return s?(n[t][x]=null,W(l,s)):v||((s=new i)instanceof _e&&(s.z2=function(t,e){return t*mr+e}(a,r)),Z(l,s)),e[t][m]=s}function W(t,e){var n=t[m]={};e instanceof fr?(n.oldX=e.x,n.oldY=e.y):n.oldShape=F({},e.shape)}function Z(t,e){var n=t[m]={},a=s.parentNode,r=e instanceof Y;if(a&&(!i||"drillDown"===i.direction)){var l=0,u=0,d=o.background[a.getRawIndex()];!i&&d&&d.oldShape&&(l=d.oldShape.width,u=d.oldShape.height),r?(n.oldX=0,n.oldY=u):n.oldShape={x:l,y:u,width:0,height:0}}n.fadein=!r}}(e,s,l,n,a,u,t,i,o,d)}!function t(e,n,i,o,a){o?(n=e,p(e,(function(t,e){!t.isRemoved()&&s(e,e)}))):new de(n,e,r,r).add(s).update(s).remove(qt(s,null)).execute();function r(t){return t.getId()}function s(r,s){var l=null!=r?e[r]:null,u=null!=s?n[s]:null,h=d(l,u,i,a);h&&t(l&&l.viewChildren||[],u&&u.viewChildren||[],h,o,a+1)}}(i.root?[i.root]:[],o&&o.root?[o.root]:[],t,i===o||!o,0);var h,c,f=(c={nodeGroup:[],background:[],content:[]},(h=l)&&p(h,(function(t,e){var n=c[e];p(t,(function(t){t&&(n.push(t),Ir(t).willDelete=!0)}))})),c);if(this._oldTree=i,this._storage=s,this._controllerHost){var g=this.seriesModel.layoutInfo,y=i.root.getLayout();y.width===g.width&&y.height===g.height&&(this._controllerHost.zoom=1)}return{lastsForAnimation:a,willDeleteEls:f,renderFinally:function(){p(f,(function(t){p(t,(function(t){t.parent&&t.parent.remove(t)}))})),p(u,(function(t){t.invisible=!0,t.dirty()}))}}},n.prototype._doAnimation=function(t,e,n,i){var a=n.get("animationDurationUpdate"),r=n.get("animationEasing"),s=(o(a)?0:a)||0,l=(o(r)?null:r)||"cubicOut",u=new pr;p(e.willDeleteEls,(function(t,e){p(t,(function(t,n){if(!t.invisible){var o,a=t.parent,r=Ir(a);if(i&&"drillDown"===i.direction)o=a===i.rootNodeGroup?{shape:{x:0,y:0,width:r.nodeWidth,height:r.nodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var d=0,h=0;r.willDelete||(d=r.nodeWidth/2,h=r.nodeHeight/2),o="nodeGroup"===e?{x:d,y:h,style:{opacity:0}}:{shape:{x:d,y:h,width:0,height:0},style:{opacity:0}}}o&&u.add(t,o,s,0,l)}}))})),p(this._storage,(function(t,n){p(t,(function(t,i){var o=e.lastsForAnimation[n][i],a={};o&&(t instanceof Y?null!=o.oldX&&(a.x=t.x,a.y=t.y,t.x=o.oldX,t.y=o.oldY):(o.oldShape&&(a.shape=F({},t.shape),t.setShape(o.oldShape)),o.fadein?(t.setStyle("opacity",0),a.style={opacity:1}):1!==t.style.opacity&&(a.style={opacity:1})),u.add(t,a,s,0,l))}))}),this),this._state="animating",u.finished(gt((function(){this._state="ready",e.renderFinally()}),this)).start()},n.prototype._resetController=function(t){var n=this._controller,i=this._controllerHost;i||(this._controllerHost={target:this.group},i=this._controllerHost),n||((n=this._controller=new zt(t.getZr())).enable(this.seriesModel.get("roam")),i.zoomLimit=this.seriesModel.get("scaleLimit"),i.zoom=this.seriesModel.get("zoom"),n.on("pan",gt(this._onPan,this)),n.on("zoom",gt(this._onZoom,this)));var o=new e(0,0,t.getWidth(),t.getHeight());n.setPointerChecker((function(t,e,n){return o.contain(e,n)}))},n.prototype._clearController=function(){var t=this._controller;this._controllerHost=null,t&&(t.dispose(),t=null)},n.prototype._onPan=function(t){if("animating"!==this._state&&(Math.abs(t.dx)>3||Math.abs(t.dy)>3)){var e=this.seriesModel.getData().tree.root;if(!e)return;var n=e.getLayout();if(!n)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:n.x+t.dx,y:n.y+t.dy,width:n.width,height:n.height}})}},n.prototype._onZoom=function(t){var n=t.originX,i=t.originY,o=t.scale;if("animating"!==this._state){var a=this.seriesModel.getData().tree.root;if(!a)return;var r=a.getLayout();if(!r)return;var s,l=new e(r.x,r.y,r.width,r.height),u=this._controllerHost;s=u.zoomLimit;var d=u.zoom=u.zoom||1;if(d*=o,s){var h=s.min||0,c=s.max||1/0;d=Math.max(Math.min(c,d),h)}var p=d/u.zoom;u.zoom=d;var f=this.seriesModel.layoutInfo;n-=f.x,i-=f.y;var g=ce();he(g,g,[-n,-i]),pe(g,g,[p,p]),he(g,g,[n,i]),l.applyTransform(g),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:l.x,y:l.y,width:l.width,height:l.height}})}},n.prototype._initEvents=function(t){var e=this;t.on("click",(function(t){if("ready"===e._state){var n=e.seriesModel.get("nodeClick",!0);if(n){var i=e.findTarget(t.offsetX,t.offsetY);if(i){var o=i.node;if(o.getLayout().isLeafRoot)e._rootToNode(i);else if("zoomToNode"===n)e._zoomToNode(i);else if("link"===n){var a=o.hostTree.data.getItemModel(o.dataIndex),r=a.get("link",!0),s=a.get("target",!0)||"blank";r&&fe(r,s)}}}}}),this)},n.prototype._renderBreadcrumb=function(t,e,n){var i=this;n||(n=null!=t.get("leafDepth",!0)?{node:t.getViewRoot()}:this.findTarget(e.getWidth()/2,e.getHeight()/2))||(n={node:t.getData().tree.root}),(this._breadcrumb||(this._breadcrumb=new dr(this.group))).render(t,e,n.node,(function(e){"animating"!==i._state&&(tr(t.getViewRoot(),e)?i._rootToNode({node:e}):i._zoomToNode({node:e}))}))},n.prototype.remove=function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage={nodeGroup:[],background:[],content:[]},this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},n.prototype.dispose=function(){this._clearController()},n.prototype._zoomToNode=function(t){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},n.prototype._rootToNode=function(t){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},n.prototype.findTarget=function(t,e){var n;return this.seriesModel.getViewRoot().eachNode({attr:"viewChildren",order:"preorder"},(function(i){var o=this._storage.background[i.getRawIndex()];if(o){var a=o.transformCoordToLocal(t,e),r=o.shape;if(!(r.x<=a[0]&&a[0]<=r.x+r.width&&r.y<=a[1]&&a[1]<=r.y+r.height))return!1;n={node:i,offsetX:a[0],offsetY:a[1]}}}),this),n},n.type="treemap",n}(K);var Mr=p,Lr=M,Dr=-1,Cr=function(){function t(e){var n=e.mappingMethod,i=e.type,o=this.option=st(e);this.type=i,this.mappingMethod=n,this._normalizeData=Er[n];var a=t.visualHandlers[i];this.applyVisual=a.applyVisual,this.getColorMapper=a.getColorMapper,this._normalizedToVisual=a._normalizedToVisual[n],"piecewise"===n?(Tr(o),function(t){var e=t.pieceList;t.hasSpecialVisual=!1,p(e,(function(e,n){e.originIndex=n,null!=e.visual&&(t.hasSpecialVisual=!0)}))}(o)):"category"===n?o.categories?function(t){var e=t.categories,n=t.categoryMap={},i=t.visual;if(Mr(e,(function(t,e){n[t]=e})),!tt(i)){var o=[];M(i)?Mr(i,(function(t,e){var i=n[e];o[null!=i?i:Dr]=t})):o[-1]=i,i=Or(t,o)}for(var a=e.length-1;a>=0;a--)null==i[a]&&(delete n[e[a]],e.pop())}(o):Tr(o,!0):(Kt("linear"!==n||o.dataExtent),Tr(o))}return t.prototype.mapValueToVisual=function(t){var e=this._normalizeData(t);return this._normalizedToVisual(e,t)},t.prototype.getNormalizer=function(){return gt(this._normalizeData,this)},t.listVisualTypes=function(){return a(t.visualHandlers)},t.isValidType=function(e){return t.visualHandlers.hasOwnProperty(e)},t.eachVisual=function(t,e,n){M(t)?p(t,e,n):e.call(n,t)},t.mapVisual=function(e,n,i){var o,a=tt(e)?[]:M(e)?{}:(o=!0,null);return t.eachVisual(e,(function(t,e){var r=n.call(i,t,e);o?a=r:a[e]=r})),a},t.retrieveVisuals=function(e){var n,i={};return e&&Mr(t.visualHandlers,(function(t,o){e.hasOwnProperty(o)&&(i[o]=e[o],n=!0)})),n?i:null},t.prepareVisualTypes=function(t){if(tt(t))t=t.slice();else{if(!Lr(t))return[];var e=[];Mr(t,(function(t,n){e.push(n)})),t=e}return t.sort((function(t,e){return"color"===e&&"color"!==t&&0===t.indexOf("color")?1:-1})),t},t.dependsOn=function(t,e){return"color"===e?!(!t||0!==t.indexOf(e)):t===e},t.findPieceIndex=function(t,e,n){for(var i,o=1/0,a=0,r=e.length;a<r;a++){var s=e[a].value;if(null!=s){if(s===t||xt(s)&&s===t+"")return a;n&&h(s,a)}}for(a=0,r=e.length;a<r;a++){var l=e[a],u=l.interval,d=l.close;if(u){if(u[0]===-1/0){if(Br(d[1],t,u[1]))return a}else if(u[1]===1/0){if(Br(d[0],u[0],t))return a}else if(Br(d[0],u[0],t)&&Br(d[1],t,u[1]))return a;n&&h(u[0],a),n&&h(u[1],a)}}if(n)return t===1/0?e.length-1:t===-1/0?0:i;function h(e,n){var a=Math.abs(e-t);a<o&&(o=a,i=n)}},t.visualHandlers={color:{applyVisual:kr("color"),getColorMapper:function(){var t=this.option;return gt("category"===t.mappingMethod?function(t,e){return!e&&(t=this._normalizeData(t)),Nr.call(this,t)}:function(e,n,i){var o=!!i;return!n&&(e=this._normalizeData(e)),i=Me(e,t.parsedVisual,i),o?i:we(i,"rgba")},this)},_normalizedToVisual:{linear:function(t){return we(Me(t,this.option.parsedVisual),"rgba")},category:Nr,piecewise:function(t,e){var n=zr.call(this,e);return null==n&&(n=we(Me(t,this.option.parsedVisual),"rgba")),n},fixed:Vr}},colorHue:Ar((function(t,e){return De(t,e)})),colorSaturation:Ar((function(t,e){return De(t,null,e)})),colorLightness:Ar((function(t,e){return De(t,null,null,e)})),colorAlpha:Ar((function(t,e){return Le(t,e)})),decal:{applyVisual:kr("decal"),_normalizedToVisual:{linear:null,category:Nr,piecewise:null,fixed:null}},opacity:{applyVisual:kr("opacity"),_normalizedToVisual:Rr([0,1])},liftZ:{applyVisual:kr("liftZ"),_normalizedToVisual:{linear:Vr,category:Vr,piecewise:Vr,fixed:Vr}},symbol:{applyVisual:function(t,e,n){n("symbol",this.mapValueToVisual(t))},_normalizedToVisual:{linear:Pr,category:Nr,piecewise:function(t,e){var n=zr.call(this,e);return null==n&&(n=Pr.call(this,t)),n},fixed:Vr}},symbolSize:{applyVisual:kr("symbolSize"),_normalizedToVisual:Rr([0,1])}},t}();function Tr(t,e){var n=t.visual,i=[];M(n)?Mr(n,(function(t){i.push(t)})):null!=n&&i.push(n);e||1!==i.length||{color:1,symbol:1}.hasOwnProperty(t.type)||(i[1]=i[0]),Or(t,i)}function Ar(t){return{applyVisual:function(e,n,i){var o=this.mapValueToVisual(e);i("color",t(n("color"),o))},_normalizedToVisual:Rr([0,1])}}function Pr(t){var e=this.option.visual;return e[Math.round(Se(t,[0,1],[0,e.length-1],!0))]||{}}function kr(t){return function(e,n,i){i(t,this.mapValueToVisual(e))}}function Nr(t){var e=this.option.visual;return e[this.option.loop&&t!==Dr?t%e.length:t]}function Vr(){return this.option.visual[0]}function Rr(t){return{linear:function(e){return Se(e,t,this.option.visual,!0)},category:Nr,piecewise:function(e,n){var i=zr.call(this,n);return null==i&&(i=Se(e,t,this.option.visual,!0)),i},fixed:Vr}}function zr(t){var e=this.option,n=e.pieceList;if(e.hasSpecialVisual){var i=n[Cr.findPieceIndex(t,n)];if(i&&i.visual)return i.visual[this.type]}}function Or(t,e){return t.visual=e,"color"===t.type&&(t.parsedVisual=ht(e,(function(t){return Ie(t)||[0,0,0,1]}))),e}var Er={linear:function(t){return Se(t,this.option.dataExtent,[0,1],!0)},piecewise:function(t){var e=this.option.pieceList,n=Cr.findPieceIndex(t,e,!0);if(null!=n)return Se(n,[0,e.length-1],[0,1],!0)},category:function(t){var e=this.option.categories?this.option.categoryMap[t]:t;return null==e?Dr:e},fixed:ee};function Br(t,e,n){return t?e<=n:e<n}var Gr=t();const Hr={seriesType:"treemap",reset:function(t){var e=t.getData().tree.root;e.isRemoved()||Fr(e,{},t.getViewRoot().getAncestors(),t)}};function Fr(t,e,n,i){var o=t.getModel(),a=t.getLayout(),r=t.hostTree.data;if(a&&!a.invisible&&a.isInView){var s,l=o.getModel("itemStyle"),u=function(t,e,n){var i=F({},e),o=n.designatedVisualItemStyle;return p(["color","colorAlpha","colorSaturation"],(function(n){o[n]=e[n];var a=t.get(n);o[n]=null,null!=a&&(i[n]=a)})),i}(l,e,i),d=r.ensureUniqueItemVisual(t.dataIndex,"style"),h=l.get("borderColor"),c=l.get("borderColorSaturation");null!=c&&(h=function(t,e){return null!=e?De(e,null,null,t):null}(c,s=Wr(u))),d.stroke=h;var f=t.viewChildren;if(f&&f.length){var g=function(t,e,n,i,o,a){if(!a||!a.length)return;var r=Yr(e,"color")||null!=o.color&&"none"!==o.color&&(Yr(e,"colorAlpha")||Yr(e,"colorSaturation"));if(!r)return;var s=e.get("visualMin"),l=e.get("visualMax"),u=n.dataExtent.slice();null!=s&&s<u[0]&&(u[0]=s),null!=l&&l>u[1]&&(u[1]=l);var d=e.get("colorMappingBy"),h={type:r.name,dataExtent:u,visual:r.range};"color"!==h.type||"index"!==d&&"id"!==d?h.mappingMethod="linear":(h.mappingMethod="category",h.loop=!0);var c=new Cr(h);return Gr(c).drColorMappingBy=d,c}(0,o,a,0,u,f);p(f,(function(t,e){if(t.depth>=n.length||t===n[t.depth]){var a=function(t,e,n,i,o,a){var r=F({},e);if(o){var s=o.type,l="color"===s&&Gr(o).drColorMappingBy,u="index"===l?i:"id"===l?a.mapIdToIndex(n.getId()):n.getValue(t.get("visualDimension"));r[s]=o.mapValueToVisual(u)}return r}(o,u,t,e,g,i);Fr(t,a,n,i)}}))}else s=Wr(u),d.fill=s}}function Wr(t){var e=Zr(t,"color");if(e){var n=Zr(t,"colorAlpha"),i=Zr(t,"colorSaturation");return i&&(e=De(e,null,null,i)),n&&(e=Le(e,n)),e}}function Zr(t,e){var n=t[e];if(null!=n&&"none"!==n)return n}function Yr(t,e){var n=t.get(e);return tt(n)&&n.length?{name:e,range:n}:null}var Xr=Math.max,Ur=Math.min,jr=Ce,qr=p,Kr=["itemStyle","borderWidth"],$r=["itemStyle","gapWidth"],Qr=["upperLabel","show"],Jr=["upperLabel","height"];const ts={seriesType:"treemap",reset:function(t,n,i,o){var a=i.getWidth(),r=i.getHeight(),l=t.option,u=Rt(t.getBoxLayoutParams(),{width:i.getWidth(),height:i.getHeight()}),d=l.size||[],h=s(jr(u.width,d[0]),a),c=s(jr(u.height,d[1]),r),p=o&&o.type,f=Qa(o,["treemapZoomToNode","treemapRootToNode"],t),g="treemapRender"===p||"treemapMove"===p?o.rootRect:null,y=t.getViewRoot(),v=Ja(y);if("treemapMove"!==p){var m="treemapZoomToNode"===p?function(t,e,n,i,o){var a,r=(e||{}).node,s=[i,o];if(!r||r===n)return s;var l=i*o,u=l*t.option.zoomToNodeRatio;for(;a=r.parentNode;){for(var d=0,h=a.children,c=0,p=h.length;c<p;c++)d+=h[c].getValue();var f=r.getValue();if(0===f)return s;u*=d/f;var g=a.getModel(),y=g.get(Kr);(u+=4*y*y+(3*y+Math.max(y,as(g)))*Math.pow(u,.5))>Te&&(u=Te),r=a}u<l&&(u=l);var v=Math.pow(u/l,.5);return[i*v,o*v]}(t,f,y,h,c):g?[g.width,g.height]:[h,c],x=l.sort;x&&"asc"!==x&&"desc"!==x&&(x="desc");var _={squareRatio:l.squareRatio,sort:x,leafDepth:l.leafDepth};y.hostTree.clearLayouts();var b={x:0,y:0,width:m[0],height:m[1],area:m[0]*m[1]};y.setLayout(b),es(y,_,!1,0),b=y.getLayout(),qr(v,(function(t,e){var n=(v[e+1]||y).getValue();t.setLayout(F({dataExtent:[n,n],borderWidth:0,upperHeight:0},b))}))}var S=t.getData().tree.root;S.setLayout(function(t,e,n){if(e)return{x:e.x,y:e.y};var i={x:0,y:0};if(!n)return i;var o=n.node,a=o.getLayout();if(!a)return i;var r=[a.width/2,a.height/2],s=o;for(;s;){var l=s.getLayout();r[0]+=l.x,r[1]+=l.y,s=s.parentNode}return{x:t.width/2-r[0],y:t.height/2-r[1]}}(u,g,f),!0),t.setLayoutInfo(u),os(S,new e(-u.x,-u.y,a,r),v,y,0)}};function es(t,e,n,i){var o,a;if(!t.isRemoved()){var r=t.getLayout();o=r.width,a=r.height;var s=t.getModel(),l=s.get(Kr),d=s.get($r)/2,h=as(s),c=Math.max(l,h),p=l-d,f=c-d;t.setLayout({borderWidth:l,upperHeight:c,upperLabelHeight:h},!0);var g=(o=Xr(o-2*p,0))*(a=Xr(a-p-f,0)),y=function(t,e,n,i,o,a){var r=t.children||[],s=i.sort;"asc"!==s&&"desc"!==s&&(s=null);var l=null!=i.leafDepth&&i.leafDepth<=a;if(o&&!l)return t.viewChildren=[];r=u(r,(function(t){return!t.isRemoved()})),function(t,e){e&&t.sort((function(t,n){var i="asc"===e?t.getValue()-n.getValue():n.getValue()-t.getValue();return 0===i?"asc"===e?t.dataIndex-n.dataIndex:n.dataIndex-t.dataIndex:i}))}(r,s);var d=function(t,e,n){for(var i=0,o=0,a=e.length;o<a;o++)i+=e[o].getValue();var r,s=t.get("visualDimension");e&&e.length?"value"===s&&n?(r=[e[e.length-1].getValue(),e[0].getValue()],"asc"===n&&r.reverse()):(r=[1/0,-1/0],qr(e,(function(t){var e=t.getValue(s);e<r[0]&&(r[0]=e),e>r[1]&&(r[1]=e)}))):r=[NaN,NaN];return{sum:i,dataExtent:r}}(e,r,s);if(0===d.sum)return t.viewChildren=[];if(d.sum=function(t,e,n,i,o){if(!i)return n;for(var a=t.get("visibleMin"),r=o.length,s=r,l=r-1;l>=0;l--){var u=o["asc"===i?r-l-1:l].getValue();u/n*e<a&&(s=l,n-=u)}return"asc"===i?o.splice(0,r-s):o.splice(s,r-s),n}(e,n,d.sum,s,r),0===d.sum)return t.viewChildren=[];for(var h=0,c=r.length;h<c;h++){var p=r[h].getValue()/d.sum*n;r[h].setLayout({area:p})}l&&(r.length&&t.setLayout({isLeafRoot:!0},!0),r.length=0);return t.viewChildren=r,t.setLayout({dataExtent:d.dataExtent},!0),r}(t,s,g,e,n,i);if(y.length){var v={x:p,y:f,width:o,height:a},m=Ur(o,a),x=1/0,_=[];_.area=0;for(var b=0,S=y.length;b<S;){var I=y[b];_.push(I),_.area+=I.getLayout().area;var w=ns(_,m,e.squareRatio);w<=x?(b++,x=w):(_.area-=_.pop().getLayout().area,is(_,m,v,d,!1),m=Ur(v.width,v.height),_.length=_.area=0,x=1/0)}if(_.length&&is(_,m,v,d,!0),!n){var M=s.get("childrenVisibleMin");null!=M&&g<M&&(n=!0)}for(b=0,S=y.length;b<S;b++)es(y[b],e,n,i+1)}}}function ns(t,e,n){for(var i=0,o=1/0,a=0,r=void 0,s=t.length;a<s;a++)(r=t[a].getLayout().area)&&(r<o&&(o=r),r>i&&(i=r));var l=t.area*t.area,u=e*e*n;return l?Xr(u*i/l,l/(u*o)):1/0}function is(t,e,n,i,o){var a=e===n.width?0:1,r=1-a,s=["x","y"],l=["width","height"],u=n[s[a]],d=e?t.area/e:0;(o||d>n[l[r]])&&(d=n[l[r]]);for(var h=0,c=t.length;h<c;h++){var p=t[h],f={},g=d?p.getLayout().area/d:0,y=f[l[r]]=Xr(d-2*i,0),v=n[s[a]]+n[l[a]]-u,m=h===c-1||v<g?v:g,x=f[l[a]]=Xr(m-2*i,0);f[s[r]]=n[s[r]]+Ur(i,y/2),f[s[a]]=u+Ur(i,x/2),u+=m,p.setLayout(f,!0)}n[s[r]]+=d,n[l[r]]-=d}function os(t,n,i,o,a){var r=t.getLayout(),s=i[a],l=s&&s===t;if(!(s&&!l||a===i.length&&t!==o)){t.setLayout({isInView:!0,invisible:!l&&!n.intersect(r),isAboveViewRoot:l},!0);var u=new e(n.x-r.x,n.y-r.y,n.width,n.height);qr(t.viewChildren||[],(function(t){os(t,u,i,o,a+1)}))}}function as(t){return t.get(Qr)?t.get(Jr):0}function rs(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.eachSeriesByType("graph",(function(t){var n=t.getCategoriesData(),i=t.getGraph().data,o=n.mapArray(n.getName);i.filterSelf((function(t){var n=i.getItemModel(t).getShallow("category");if(null!=n){Nt(n)&&(n=o[n]);for(var a=0;a<e.length;a++)if(!e[a].isSelected(n))return!1}return!0}))}))}function ss(t){var e={};t.eachSeriesByType("graph",(function(t){var n=t.getCategoriesData(),i=t.getData(),o={};n.each((function(i){var a=n.getName(i);o["ec-"+a]=i;var r=n.getItemModel(i),s=r.getModel("itemStyle").getItemStyle();s.fill||(s.fill=t.getColorFromPalette(a,e)),n.setItemVisual(i,"style",s);for(var l=["symbol","symbolSize","symbolKeepAspect"],u=0;u<l.length;u++){var d=r.getShallow(l[u],!0);null!=d&&n.setItemVisual(i,l[u],d)}})),n.count()&&i.each((function(t){var e=i.getItemModel(t).getShallow("category");if(null!=e){xt(e)&&(e=o["ec-"+e]);var a=n.getItemVisual(e,"style"),r=i.ensureUniqueItemVisual(t,"style");F(r,a);for(var s=["symbol","symbolSize","symbolKeepAspect"],l=0;l<s.length;l++)i.setItemVisual(t,s[l],n.getItemVisual(e,s[l]))}}))}))}function ls(t){return t instanceof Array||(t=[t,t]),t}function us(t){t.eachSeriesByType("graph",(function(t){var e=t.getGraph(),n=t.getEdgeData(),i=ls(t.get("edgeSymbol")),o=ls(t.get("edgeSymbolSize"));n.setVisual("fromSymbol",i&&i[0]),n.setVisual("toSymbol",i&&i[1]),n.setVisual("fromSymbolSize",o&&o[0]),n.setVisual("toSymbolSize",o&&o[1]),n.setVisual("style",t.getModel("lineStyle").getLineStyle()),n.each((function(t){var i=n.getItemModel(t),o=e.getEdgeByIndex(t),a=ls(i.getShallow("symbol",!0)),r=ls(i.getShallow("symbolSize",!0)),s=i.getModel("lineStyle").getLineStyle(),l=n.ensureUniqueItemVisual(t,"style");switch(F(l,s),l.stroke){case"source":var u=o.node1.getVisual("style");l.stroke=u&&u.fill;break;case"target":u=o.node2.getVisual("style");l.stroke=u&&u.fill}a[0]&&o.setVisual("fromSymbol",a[0]),a[1]&&o.setVisual("toSymbol",a[1]),r[0]&&o.setVisual("fromSymbolSize",r[0]),r[1]&&o.setVisual("toSymbolSize",r[1])}))}))}var ds="--\x3e",hs=function(t){return t.get("autoCurveness")||null},cs=function(t,e){var n=hs(t),i=20,o=[];if(Nt(n))i=n;else if(tt(n))return void(t.__curvenessList=n);e>i&&(i=e);var a=i%2?i+2:i+3;o=[];for(var r=0;r<a;r++)o.push((r%2?r+1:r)/10*(r%2?-1:1));t.__curvenessList=o},ps=function(t,e,n){var i=[t.id,t.dataIndex].join("."),o=[e.id,e.dataIndex].join(".");return[n.uid,i,o].join(ds)},fs=function(t){var e=t.split(ds);return[e[0],e[2],e[1]].join(ds)},gs=function(t,e){var n=e.__edgeMap;return n[t]?n[t].length:0};function ys(t,e,n,i){var o=hs(e),a=tt(o);if(!o)return null;var r=function(t,e){var n=ps(t.node1,t.node2,e);return e.__edgeMap[n]}(t,e);if(!r)return null;for(var s=-1,l=0;l<r.length;l++)if(r[l]===n){s=l;break}var u=function(t,e){return gs(ps(t.node1,t.node2,e),e)+gs(ps(t.node2,t.node1,e),e)}(t,e);cs(e,u),t.lineStyle=t.lineStyle||{};var d=ps(t.node1,t.node2,e),h=e.__curvenessList,c=a||u%2?0:1;if(r.isForward)return h[c+s];var p=fs(d),f=gs(p,e),g=h[s+f+c];return i?a?o&&0===o[0]?(f+c)%2?g:-g:((f%2?0:1)+c)%2?g:-g:(f+c)%2?g:-g:h[s+f+c]}function vs(t){var e=t.coordinateSystem;if(!e||"view"===e.type){var n=t.getGraph();n.eachNode((function(t){var e=t.getModel();t.setLayout([+e.get("x"),+e.get("y")])})),ms(n,t)}}function ms(t,e){t.eachEdge((function(t,n){var i=Ae(t.getModel().get(["lineStyle","curveness"]),-ys(t,e,n,!0),0),o=Pe(t.node1.getLayout()),a=Pe(t.node2.getLayout()),r=[o,a];+i&&r.push([(o[0]+a[0])/2-(o[1]-a[1])*i,(o[1]+a[1])/2-(a[0]-o[0])*i]),t.setLayout(r)}))}function xs(t,e){t.eachSeriesByType("graph",(function(t){var e=t.get("layout"),n=t.coordinateSystem;if(n&&"view"!==n.type){var i=t.getData(),o=[];p(n.dimensions,(function(t){o=o.concat(i.mapDimensionsAll(t))}));for(var a=0;a<i.count();a++){for(var r=[],s=!1,l=0;l<o.length;l++){var u=i.get(o[l],a);isNaN(u)||(s=!0),r.push(u)}s?i.setItemLayout(a,n.dataToPoint(r)):i.setItemLayout(a,[NaN,NaN])}ms(i.graph,t)}else e&&"none"!==e||vs(t)}))}function _s(t){var e=t.coordinateSystem;if("view"!==e.type)return 1;var n=t.option.nodeScaleRatio,i=e.scaleX;return((e.getZoom()-1)*n+1)/i}function bs(t){var e=t.getVisual("symbolSize");return e instanceof Array&&(e=(e[0]+e[1])/2),+e}var Ss=Math.PI,Is=[];function ws(t,e,n,i){var o=t.coordinateSystem;if(!o||"view"===o.type){var a=o.getBoundingRect(),r=t.getData(),s=r.graph,l=a.width/2+a.x,u=a.height/2+a.y,d=Math.min(a.width,a.height)/2,h=r.count();if(r.setLayout({cx:l,cy:u}),h){if(n){var c=o.pointToData(i),p=c[0],f=c[1],g=[p-l,f-u];ke(g,g),Ne(g,g,d),n.setLayout([l+g[0],u+g[1]],!0),Ls(n,t.get(["circular","rotateLabel"]),l,u)}Ms[e](t,s,r,d,l,u,h),s.eachEdge((function(e,n){var i,o=Ae(e.getModel().get(["lineStyle","curveness"]),ys(e,t,n),0),a=Pe(e.node1.getLayout()),r=Pe(e.node2.getLayout()),s=(a[0]+r[0])/2,d=(a[1]+r[1])/2;+o&&(i=[l*(o*=3)+s*(1-o),u*o+d*(1-o)]),e.setLayout([a,r,i])}))}}}var Ms={value:function(t,e,n,i,o,a,r){var s=0,l=n.getSum("value"),u=2*Math.PI/(l||r);e.eachNode((function(t){var e=t.getValue("value"),n=u*(l?e:1)/2;s+=n,t.setLayout([i*Math.cos(s)+o,i*Math.sin(s)+a]),s+=n}))},symbolSize:function(t,e,n,i,o,a,r){var s=0;Is.length=r;var l=_s(t);e.eachNode((function(t){var e=bs(t);isNaN(e)&&(e=2),e<0&&(e=0),e*=l;var n=Math.asin(e/2/i);isNaN(n)&&(n=Ss/2),Is[t.dataIndex]=n,s+=2*n}));var u=(2*Ss-s)/r/2,d=0;e.eachNode((function(t){var e=u+Is[t.dataIndex];d+=e,(!t.getLayout()||!t.getLayout().fixed)&&t.setLayout([i*Math.cos(d)+o,i*Math.sin(d)+a]),d+=e}))}};function Ls(t,e,n,i){var o=t.getGraphicEl();if(o){var a=t.getModel().get(["label","rotate"])||0,r=o.getSymbolPath();if(e){var s=t.getLayout(),l=Math.atan2(s[1]-i,s[0]-n);l<0&&(l=2*Math.PI+l);var u=s[0]<n;u&&(l-=Math.PI);var d=u?"left":"right";r.setTextConfig({rotation:-l,position:d,origin:"center"});var h=r.ensureState("emphasis");F(h.textConfig||(h.textConfig={}),{position:d})}else r.setTextConfig({rotation:a*=Math.PI/180})}}function Ds(t){t.eachSeriesByType("graph",(function(t){"circular"===t.get("layout")&&ws(t,"symbolSize")}))}var Cs=Oe;function Ts(t){t.eachSeriesByType("graph",(function(t){var e=t.coordinateSystem;if(!e||"view"===e.type)if("force"===t.get("layout")){var n=t.preservedPoints||{},i=t.getGraph(),o=i.data,a=i.edgeData,r=t.getModel("force"),s=r.get("initLayout");t.preservedPoints?o.each((function(t){var e=o.getId(t);o.setItemLayout(t,n[e]||[NaN,NaN])})):s&&"none"!==s?"circular"===s&&ws(t,"value"):vs(t);var l=o.getDataExtent("value"),u=a.getDataExtent("value"),d=r.get("repulsion"),h=r.get("edgeLength"),c=tt(d)?d:[d,d],p=tt(h)?h:[h,h];p=[p[1],p[0]];var f=o.mapArray("value",(function(t,e){var n=o.getItemLayout(e),i=Se(t,l,c);return isNaN(i)&&(i=(c[0]+c[1])/2),{w:i,rep:i,fixed:o.getItemModel(e).get("fixed"),p:!n||isNaN(n[0])||isNaN(n[1])?null:n}})),g=a.mapArray("value",(function(e,n){var o=i.getEdgeByIndex(n),a=Se(e,u,p);isNaN(a)&&(a=(p[0]+p[1])/2);var r=o.getModel(),s=Ae(o.getModel().get(["lineStyle","curveness"]),-ys(o,t,n,!0),0);return{n1:f[o.node1.dataIndex],n2:f[o.node2.dataIndex],d:a,curveness:s,ignoreForceLayout:r.get("ignoreForceLayout")}})),y=e.getBoundingRect(),v=function(t,e,n){for(var i=t,o=e,a=n.rect,r=a.width,s=a.height,l=[a.x+r/2,a.y+s/2],u=null==n.gravity?.1:n.gravity,d=0;d<i.length;d++){var h=i[d];h.p||(h.p=Ve(r*(Math.random()-.5)+l[0],s*(Math.random()-.5)+l[1])),h.pp=Pe(h.p),h.edges=null}var c,p,f=null==n.friction?.6:n.friction,g=f;return{warmUp:function(){g=.8*f},setFixed:function(t){i[t].fixed=!0},setUnfixed:function(t){i[t].fixed=!1},beforeStep:function(t){c=t},afterStep:function(t){p=t},step:function(t){c&&c(i,o);for(var e=[],n=i.length,a=0;a<o.length;a++){var r=o[a];if(!r.ignoreForceLayout){var s=r.n1,d=r.n2;Re(e,d.p,s.p);var h=ze(e)-r.d,f=d.w/(s.w+d.w);isNaN(f)&&(f=0),ke(e,e),!s.fixed&&Cs(s.p,s.p,e,f*h*g),!d.fixed&&Cs(d.p,d.p,e,-(1-f)*h*g)}}for(a=0;a<n;a++)(x=i[a]).fixed||(Re(e,l,x.p),Cs(x.p,x.p,e,u*g));for(a=0;a<n;a++){s=i[a];for(var y=a+1;y<n;y++){d=i[y],Re(e,d.p,s.p),0===(h=ze(e))&&(Ee(e,Math.random()-.5,Math.random()-.5),h=1);var v=(s.rep+d.rep)/h/h;!s.fixed&&Cs(s.pp,s.pp,e,v),!d.fixed&&Cs(d.pp,d.pp,e,-v)}}var m=[];for(a=0;a<n;a++){var x;(x=i[a]).fixed||(Re(m,x.p,x.pp),Cs(x.p,x.p,m,g),Be(x.pp,x.p))}var _=(g*=.992)<.01;p&&p(i,o,_),t&&t(_)}}}(f,g,{rect:y,gravity:r.get("gravity"),friction:r.get("friction")});v.beforeStep((function(t,e){for(var n=0,o=t.length;n<o;n++)t[n].fixed&&Be(t[n].p,i.getNodeByIndex(n).getLayout())})),v.afterStep((function(t,e,a){for(var r=0,s=t.length;r<s;r++)t[r].fixed||i.getNodeByIndex(r).setLayout(t[r].p),n[o.getId(r)]=t[r].p;for(r=0,s=e.length;r<s;r++){var l=e[r],u=i.getEdgeByIndex(r),d=l.n1.p,h=l.n2.p,c=u.getLayout();(c=c?c.slice():[])[0]=c[0]||[],c[1]=c[1]||[],Be(c[0],d),Be(c[1],h),+l.curveness&&(c[2]=[(d[0]+h[0])/2-(d[1]-h[1])*l.curveness,(d[1]+h[1])/2-(h[0]-d[0])*l.curveness]),u.setLayout(c)}})),t.forceLayout=v,t.preservedPoints=n,v.step()}else t.forceLayout=null}))}function As(t,e){var n=[];return t.eachSeriesByType("graph",(function(t){var i=t.get("coordinateSystem");if(!i||"view"===i){var o=t.getData(),a=o.mapArray((function(t){var e=o.getItemModel(t);return[+e.get("x"),+e.get("y")]})),r=[],s=[];Ot(a,r,s),s[0]-r[0]==0&&(s[0]+=1,r[0]-=1),s[1]-r[1]==0&&(s[1]+=1,r[1]-=1);var l=(s[0]-r[0])/(s[1]-r[1]),u=function(t,e,n){var i=F(t.getBoxLayoutParams(),{aspect:n});return Rt(i,{width:e.getWidth(),height:e.getHeight()})}(t,e,l);isNaN(l)&&(r=[u.x,u.y],s=[u.x+u.width,u.y+u.height]);var d=s[0]-r[0],h=s[1]-r[1],c=u.width,p=u.height,f=t.coordinateSystem=new Et;f.zoomLimit=t.get("scaleLimit"),f.setBoundingRect(r[0],r[1],d,h),f.setViewRect(u.x,u.y,c,p),f.setCenter(t.get("center"),e),f.setZoom(t.get("zoom")),n.push(f)}})),n}var Ps=Ge.prototype,ks=Ut.prototype,Ns=function(){return function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}}();function Vs(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}!function(t){function e(){return null!==t&&t.apply(this,arguments)||this}w(e,t)}(Ns);var Rs=function(t){function e(e){var n=t.call(this,e)||this;return n.type="ec-line",n}return w(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Ns},e.prototype.buildPath=function(t,e){Vs(e)?Ps.buildPath.call(this,t,e):ks.buildPath.call(this,t,e)},e.prototype.pointAt=function(t){return Vs(this.shape)?Ps.pointAt.call(this,t):ks.pointAt.call(this,t)},e.prototype.tangentAt=function(t){var e=this.shape,n=Vs(e)?[e.x2-e.x1,e.y2-e.y1]:ks.tangentAt.call(this,t);return ke(n,n)},e}(X),zs=["fromSymbol","toSymbol"];function Os(t){return"_"+t+"Type"}function Es(t,e,n){var i=e.getItemVisual(n,t);if(!i||"none"===i)return i;var o=e.getItemVisual(n,t+"Size"),a=e.getItemVisual(n,t+"Rotate"),r=e.getItemVisual(n,t+"Offset"),s=e.getItemVisual(n,t+"KeepAspect"),l=pt(o);return i+l+Ye(r||0,l)+(a||"")+(s||"")}function Bs(t,e,n){var i=e.getItemVisual(n,t);if(i&&"none"!==i){var o=e.getItemVisual(n,t+"Size"),a=e.getItemVisual(n,t+"Rotate"),r=e.getItemVisual(n,t+"Offset"),s=e.getItemVisual(n,t+"KeepAspect"),l=pt(o),u=Ye(r||0,l),d=U(i,-l[0]/2+u[0],-l[1]/2+u[1],l[0],l[1],null,s);return d.__specifiedRotation=null==a||isNaN(a)?void 0:+a*Math.PI/180||0,d.name=t,d}}function Gs(t,e){t.x1=e[0][0],t.y1=e[0][1],t.x2=e[1][0],t.y2=e[1][1],t.percent=1;var n=e[2];n?(t.cpx1=n[0],t.cpy1=n[1]):(t.cpx1=NaN,t.cpy1=NaN)}var Hs=function(t){function e(e,n,i){var o=t.call(this)||this;return o._createLine(e,n,i),o}return w(e,t),e.prototype._createLine=function(t,e,n){var i=t.hostModel,o=function(t){var e=new Rs({name:"line",subPixelOptimize:!0});return Gs(e.shape,t),e}(t.getItemLayout(e));o.shape.percent=0,_(o,{shape:{percent:1}},i,e),this.add(o),p(zs,(function(n){var i=Bs(n,t,e);this.add(i),this[Os(n)]=Es(n,t,e)}),this),this._updateCommonStl(t,e,n)},e.prototype.updateData=function(t,e,n){var i=t.hostModel,o=this.childOfName("line"),a=t.getItemLayout(e),r={shape:{}};Gs(r.shape,a),S(o,r,i,e),p(zs,(function(n){var i=Es(n,t,e),o=Os(n);if(this[o]!==i){this.remove(this.childOfName(n));var a=Bs(n,t,e);this.add(a)}this[o]=i}),this),this._updateCommonStl(t,e,n)},e.prototype.getLinePath=function(){return this.childAt(0)},e.prototype._updateCommonStl=function(t,e,n){var i=t.hostModel,o=this.childOfName("line"),a=n&&n.emphasisLineStyle,r=n&&n.blurLineStyle,s=n&&n.selectLineStyle,l=n&&n.labelStatesModels,u=n&&n.emphasisDisabled,d=n&&n.focus,h=n&&n.blurScope;if(!n||t.hasItemOption){var c=t.getItemModel(e),f=c.getModel("emphasis");a=f.getModel("lineStyle").getLineStyle(),r=c.getModel(["blur","lineStyle"]).getLineStyle(),s=c.getModel(["select","lineStyle"]).getLineStyle(),u=f.get("disabled"),d=f.get("focus"),h=f.get("blurScope"),l=ut(c)}var g=t.getItemVisual(e,"style"),y=g.stroke;o.useStyle(g),o.style.fill=null,o.style.strokeNoScale=!0,o.ensureState("emphasis").style=a,o.ensureState("blur").style=r,o.ensureState("select").style=s,p(zs,(function(t){var e=this.childOfName(t);if(e){e.setColor(y),e.style.opacity=g.opacity;for(var n=0;n<He.length;n++){var i=He[n],a=o.getState(i);if(a){var r=a.style||{},s=e.ensureState(i),l=s.style||(s.style={});null!=r.stroke&&(l[e.__isEmptyBrush?"stroke":"fill"]=r.stroke),null!=r.opacity&&(l.opacity=r.opacity)}}e.markRedraw()}}),this);var v=i.getRawValue(e);lt(this,l,{labelDataIndex:e,labelFetcher:{getFormattedLabel:function(e,n){return i.getFormattedLabel(e,n,t.dataType)}},inheritColor:y||"#000",defaultOpacity:g.opacity,defaultText:(null==v?t.getName(e):isFinite(v)?Fe(v):v)+""});var m=this.getTextContent();if(m){var x=l.normal;m.__align=m.style.align,m.__verticalAlign=m.style.verticalAlign,m.__position=x.get("position")||"middle";var _=x.get("distance");tt(_)||(_=[_,_]),m.__labelDistance=_}this.setTextConfig({position:null,local:!0,inside:!1}),dt(this,d,h,u)},e.prototype.highlight=function(){We(this)},e.prototype.downplay=function(){Ze(this)},e.prototype.updateLayout=function(t,e){this.setLinePoints(t.getItemLayout(e))},e.prototype.setLinePoints=function(t){var e=this.childOfName("line");Gs(e.shape,t),e.dirty()},e.prototype.beforeUpdate=function(){var t=this,e=t.childOfName("fromSymbol"),n=t.childOfName("toSymbol"),i=t.getTextContent();if(e||n||i&&!i.ignore){for(var o=1,a=this.parent;a;)a.scaleX&&(o/=a.scaleX),a=a.parent;var r=t.childOfName("line");if(this.__dirty||r.__dirty){var s=r.shape.percent,l=r.pointAt(0),u=r.pointAt(s),d=Re([],u,l);if(ke(d,d),e&&(e.setPosition(l),I(e,0),e.scaleX=e.scaleY=o*s,e.markRedraw()),n&&(n.setPosition(u),I(n,1),n.scaleX=n.scaleY=o*s,n.markRedraw()),i&&!i.ignore){i.x=i.y=0,i.originX=i.originY=0;var h=void 0,c=void 0,p=i.__labelDistance,f=p[0]*o,g=p[1]*o,y=s/2,v=r.tangentAt(y),m=[v[1],-v[0]],x=r.pointAt(y);m[1]>0&&(m[0]=-m[0],m[1]=-m[1]);var _=v[0]<0?-1:1;if("start"!==i.__position&&"end"!==i.__position){var b=-Math.atan2(v[1],v[0]);u[0]<l[0]&&(b=Math.PI+b),i.rotation=b}var S=void 0;switch(i.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":S=-g,c="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":S=g,c="top";break;default:S=0,c="middle"}switch(i.__position){case"end":i.x=d[0]*f+u[0],i.y=d[1]*g+u[1],h=d[0]>.8?"left":d[0]<-.8?"right":"center",c=d[1]>.8?"top":d[1]<-.8?"bottom":"middle";break;case"start":i.x=-d[0]*f+l[0],i.y=-d[1]*g+l[1],h=d[0]>.8?"right":d[0]<-.8?"left":"center",c=d[1]>.8?"bottom":d[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":i.x=f*_+l[0],i.y=l[1]+S,h=v[0]<0?"right":"left",i.originX=-f*_,i.originY=-S;break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":i.x=x[0],i.y=x[1]+S,h="center",i.originY=-S;break;case"insideEndTop":case"insideEnd":case"insideEndBottom":i.x=-f*_+u[0],i.y=u[1]+S,h=v[0]>=0?"right":"left",i.originX=f*_,i.originY=-S}i.scaleX=i.scaleY=o,i.setStyle({verticalAlign:i.__verticalAlign||c,align:i.__align||h})}}}function I(t,e){var n=t.__specifiedRotation;if(null==n){var i=r.tangentAt(e);t.attr("rotation",(1===e?-1:1)*Math.PI/2-Math.atan2(i[1],i[0]))}else t.attr("rotation",n)}},e}(Y),Fs=function(){function t(t){this.group=new Y,this._LineCtor=t||Hs}return t.prototype.updateData=function(t){var e=this;this._progressiveEls=null;var n=this,i=n.group,o=n._lineData;n._lineData=t,o||i.removeAll();var a=Ws(t);t.diff(o).add((function(n){e._doAdd(t,n,a)})).update((function(n,i){e._doUpdate(o,t,i,n,a)})).remove((function(t){i.remove(o.getItemGraphicEl(t))})).execute()},t.prototype.updateLayout=function(){var t=this._lineData;t&&t.eachItemGraphicEl((function(e,n){e.updateLayout(t,n)}),this)},t.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=Ws(t),this._lineData=null,this.group.removeAll()},t.prototype.incrementalUpdate=function(t,e){function n(t){t.isGroup||function(t){return t.animators&&t.animators.length>0}(t)||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[];for(var i=t.start;i<t.end;i++){if(Ys(e.getItemLayout(i))){var o=new this._LineCtor(e,i,this._seriesScope);o.traverse(n),this.group.add(o),e.setItemGraphicEl(i,o),this._progressiveEls.push(o)}}},t.prototype.remove=function(){this.group.removeAll()},t.prototype.eachRendered=function(t){Xe(this._progressiveEls||this.group,t)},t.prototype._doAdd=function(t,e,n){if(Ys(t.getItemLayout(e))){var i=new this._LineCtor(t,e,n);t.setItemGraphicEl(e,i),this.group.add(i)}},t.prototype._doUpdate=function(t,e,n,i,o){var a=t.getItemGraphicEl(n);Ys(e.getItemLayout(i))?(a?a.updateData(e,i,o):a=new this._LineCtor(e,i,o),e.setItemGraphicEl(i,a),this.group.add(a)):this.group.remove(a)},t}();function Ws(t){var e=t.hostModel,n=e.getModel("emphasis");return{lineStyle:e.getModel("lineStyle").getLineStyle(),emphasisLineStyle:n.getModel(["lineStyle"]).getLineStyle(),blurLineStyle:e.getModel(["blur","lineStyle"]).getLineStyle(),selectLineStyle:e.getModel(["select","lineStyle"]).getLineStyle(),emphasisDisabled:n.get("disabled"),blurScope:n.get("blurScope"),focus:n.get("focus"),labelStatesModels:ut(e)}}function Zs(t){return isNaN(t[0])||isNaN(t[1])}function Ys(t){return t&&!Zs(t[0])&&!Zs(t[1])}var Xs=[],Us=[],js=[],qs=je,Ks=qe,$s=Math.abs;function Qs(t,e,n){for(var i,o=t[0],a=t[1],r=t[2],s=1/0,l=n*n,u=.1,d=.1;d<=.9;d+=.1){Xs[0]=qs(o[0],a[0],r[0],d),Xs[1]=qs(o[1],a[1],r[1],d),(p=$s(Ks(Xs,e)-l))<s&&(s=p,i=d)}for(var h=0;h<32;h++){var c=i+u;Us[0]=qs(o[0],a[0],r[0],i),Us[1]=qs(o[1],a[1],r[1],i),js[0]=qs(o[0],a[0],r[0],c),js[1]=qs(o[1],a[1],r[1],c);var p=Ks(Us,e)-l;if($s(p)<.01)break;var f=Ks(js,e)-l;u/=2,p<0?f>=0?i+=u:i-=u:f>=0?i-=u:i+=u}return i}function Js(t,e){var n=[],i=Ue,o=[[],[],[]],a=[[],[]],r=[];e/=2,t.eachEdge((function(t,s){var l=t.getLayout(),u=t.getVisual("fromSymbol"),d=t.getVisual("toSymbol");l.__original||(l.__original=[Pe(l[0]),Pe(l[1])],l[2]&&l.__original.push(Pe(l[2])));var h=l.__original;if(null!=l[2]){if(Be(o[0],h[0]),Be(o[1],h[2]),Be(o[2],h[1]),u&&"none"!==u){var c=bs(t.node1),p=Qs(o,h[0],c*e);i(o[0][0],o[1][0],o[2][0],p,n),o[0][0]=n[3],o[1][0]=n[4],i(o[0][1],o[1][1],o[2][1],p,n),o[0][1]=n[3],o[1][1]=n[4]}if(d&&"none"!==d){c=bs(t.node2),p=Qs(o,h[1],c*e);i(o[0][0],o[1][0],o[2][0],p,n),o[1][0]=n[1],o[2][0]=n[2],i(o[0][1],o[1][1],o[2][1],p,n),o[1][1]=n[1],o[2][1]=n[2]}Be(l[0],o[0]),Be(l[1],o[2]),Be(l[2],o[1])}else{if(Be(a[0],h[0]),Be(a[1],h[1]),Re(r,a[1],a[0]),ke(r,r),u&&"none"!==u){c=bs(t.node1);Oe(a[0],a[0],r,c*e)}if(d&&"none"!==d){c=bs(t.node2);Oe(a[1],a[1],r,-c*e)}Be(l[0],a[0]),Be(l[1],a[1])}}))}function tl(t){return"view"===t.type}var el=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.init=function(t,e){var n=new q,i=new Fs,o=this.group;this._controller=new zt(e.getZr()),this._controllerHost={target:o},o.add(n.group),o.add(i.group),this._symbolDraw=n,this._lineDraw=i,this._firstRender=!0},e.prototype.render=function(t,e,n){var i=this,o=t.coordinateSystem;this._model=t;var a=this._symbolDraw,s=this._lineDraw,l=this.group;if(tl(o)){var u={x:o.x,y:o.y,scaleX:o.scaleX,scaleY:o.scaleY};this._firstRender?l.attr(u):S(l,u,t)}Js(t.getGraph(),_s(t));var d=t.getData();a.updateData(d);var h=t.getEdgeData();s.updateData(h),this._updateNodeAndLinkScale(),this._updateController(t,e,n),clearTimeout(this._layoutTimeout);var c=t.forceLayout,p=t.get(["force","layoutAnimation"]);c&&this._startForceLayoutIteration(c,p);var f=t.get("layout");d.graph.eachNode((function(e){var n=e.dataIndex,o=e.getGraphicEl(),a=e.getModel();if(o){o.off("drag").off("dragend");var s=a.get("draggable");s&&o.on("drag",(function(a){switch(f){case"force":c.warmUp(),!i._layouting&&i._startForceLayoutIteration(c,p),c.setFixed(n),d.setItemLayout(n,[o.x,o.y]);break;case"circular":d.setItemLayout(n,[o.x,o.y]),e.setLayout({fixed:!0},!0),ws(t,"symbolSize",e,[a.offsetX,a.offsetY]),i.updateLayout(t);break;default:d.setItemLayout(n,[o.x,o.y]),ms(t.getGraph(),t),i.updateLayout(t)}})).on("dragend",(function(){c&&c.setUnfixed(n)})),o.setDraggable(s,!!a.get("cursor")),"adjacency"===a.get(["emphasis","focus"])&&(r(o).focus=e.getAdjacentDataIndices())}})),d.graph.eachEdge((function(t){var e=t.getGraphicEl(),n=t.getModel().get(["emphasis","focus"]);e&&"adjacency"===n&&(r(e).focus={edge:[t.dataIndex],node:[t.node1.dataIndex,t.node2.dataIndex]})}));var g="circular"===t.get("layout")&&t.get(["circular","rotateLabel"]),y=d.getLayout("cx"),v=d.getLayout("cy");d.graph.eachNode((function(t){Ls(t,g,y,v)})),this._firstRender=!1},e.prototype.dispose=function(){this.remove(),this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype._startForceLayoutIteration=function(t,e){var n=this;!function i(){t.step((function(t){n.updateLayout(n._model),(n._layouting=!t)&&(e?n._layoutTimeout=setTimeout(i,16):i())}))}()},e.prototype._updateController=function(t,e,n){var i=this,o=this._controller,a=this._controllerHost,r=this.group;o.setPointerChecker((function(e,i,o){var a=r.getBoundingRect();return a.applyTransform(r.transform),a.contain(i,o)&&!Bt(e,n,t)})),tl(t.coordinateSystem)?(o.enable(t.get("roam")),a.zoomLimit=t.get("scaleLimit"),a.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",(function(e){Gt(a,e.dx,e.dy),n.dispatchAction({seriesId:t.id,type:"graphRoam",dx:e.dx,dy:e.dy})})).on("zoom",(function(e){Ht(a,e.scale,e.originX,e.originY),n.dispatchAction({seriesId:t.id,type:"graphRoam",zoom:e.scale,originX:e.originX,originY:e.originY}),i._updateNodeAndLinkScale(),Js(t.getGraph(),_s(t)),i._lineDraw.updateLayout(),n.updateLabelLayout()}))):o.disable()},e.prototype._updateNodeAndLinkScale=function(){var t=this._model,e=t.getData(),n=_s(t);e.eachItemGraphicEl((function(t,e){t&&t.setSymbolScale(n)}))},e.prototype.updateLayout=function(t){Js(t.getGraph(),_s(t)),this._symbolDraw.updateLayout(),this._lineDraw.updateLayout()},e.prototype.remove=function(){clearTimeout(this._layoutTimeout),this._layouting=!1,this._layoutTimeout=null,this._symbolDraw&&this._symbolDraw.remove(),this._lineDraw&&this._lineDraw.remove()},e.type="graph",e}(K);function nl(t){return"_EC_"+t}var il=function(){function t(t){this.type="graph",this.nodes=[],this.edges=[],this._nodesMap={},this._edgesMap={},this._directed=t||!1}return t.prototype.isDirected=function(){return this._directed},t.prototype.addNode=function(t,e){t=null==t?""+e:""+t;var n=this._nodesMap;if(!n[nl(t)]){var i=new ol(t,e);return i.hostGraph=this,this.nodes.push(i),n[nl(t)]=i,i}},t.prototype.getNodeByIndex=function(t){var e=this.data.getRawIndex(t);return this.nodes[e]},t.prototype.getNodeById=function(t){return this._nodesMap[nl(t)]},t.prototype.addEdge=function(t,e,n){var i=this._nodesMap,o=this._edgesMap;if(Nt(t)&&(t=this.nodes[t]),Nt(e)&&(e=this.nodes[e]),t instanceof ol||(t=i[nl(t)]),e instanceof ol||(e=i[nl(e)]),t&&e){var a=t.id+"-"+e.id,r=new al(t,e,n);return r.hostGraph=this,this._directed&&(t.outEdges.push(r),e.inEdges.push(r)),t.edges.push(r),t!==e&&e.edges.push(r),this.edges.push(r),o[a]=r,r}},t.prototype.getEdgeByIndex=function(t){var e=this.edgeData.getRawIndex(t);return this.edges[e]},t.prototype.getEdge=function(t,e){t instanceof ol&&(t=t.id),e instanceof ol&&(e=e.id);var n=this._edgesMap;return this._directed?n[t+"-"+e]:n[t+"-"+e]||n[e+"-"+t]},t.prototype.eachNode=function(t,e){for(var n=this.nodes,i=n.length,o=0;o<i;o++)n[o].dataIndex>=0&&t.call(e,n[o],o)},t.prototype.eachEdge=function(t,e){for(var n=this.edges,i=n.length,o=0;o<i;o++)n[o].dataIndex>=0&&n[o].node1.dataIndex>=0&&n[o].node2.dataIndex>=0&&t.call(e,n[o],o)},t.prototype.breadthFirstTraverse=function(t,e,n,i){if(e instanceof ol||(e=this._nodesMap[nl(e)]),e){for(var o="out"===n?"outEdges":"in"===n?"inEdges":"edges",a=0;a<this.nodes.length;a++)this.nodes[a].__visited=!1;if(!t.call(i,e,null))for(var r=[e];r.length;){var s=r.shift(),l=s[o];for(a=0;a<l.length;a++){var u=l[a],d=u.node1===s?u.node2:u.node1;if(!d.__visited){if(t.call(i,d,s))return;r.push(d),d.__visited=!0}}}}},t.prototype.update=function(){for(var t=this.data,e=this.edgeData,n=this.nodes,i=this.edges,o=0,a=n.length;o<a;o++)n[o].dataIndex=-1;for(o=0,a=t.count();o<a;o++)n[t.getRawIndex(o)].dataIndex=o;e.filterSelf((function(t){var n=i[e.getRawIndex(t)];return n.node1.dataIndex>=0&&n.node2.dataIndex>=0}));for(o=0,a=i.length;o<a;o++)i[o].dataIndex=-1;for(o=0,a=e.count();o<a;o++)i[e.getRawIndex(o)].dataIndex=o},t.prototype.clone=function(){for(var e=new t(this._directed),n=this.nodes,i=this.edges,o=0;o<n.length;o++)e.addNode(n[o].id,n[o].dataIndex);for(o=0;o<i.length;o++){var a=i[o];e.addEdge(a.node1.id,a.node2.id,a.dataIndex)}return e},t}(),ol=function(){function t(t,e){this.inEdges=[],this.outEdges=[],this.edges=[],this.dataIndex=-1,this.id=null==t?"":t,this.dataIndex=null==e?-1:e}return t.prototype.degree=function(){return this.edges.length},t.prototype.inDegree=function(){return this.inEdges.length},t.prototype.outDegree=function(){return this.outEdges.length},t.prototype.getModel=function(t){if(!(this.dataIndex<0))return this.hostGraph.data.getItemModel(this.dataIndex).getModel(t)},t.prototype.getAdjacentDataIndices=function(){for(var t={edge:[],node:[]},e=0;e<this.edges.length;e++){var n=this.edges[e];n.dataIndex<0||(t.edge.push(n.dataIndex),t.node.push(n.node1.dataIndex,n.node2.dataIndex))}return t},t.prototype.getTrajectoryDataIndices=function(){for(var t=ie(),e=ie(),n=0;n<this.edges.length;n++){var i=this.edges[n];if(!(i.dataIndex<0)){t.set(i.dataIndex,!0);for(var o=[i.node1],a=[i.node2],r=0;r<o.length;){var s=o[r];r++,e.set(s.dataIndex,!0);for(var l=0;l<s.inEdges.length;l++)t.set(s.inEdges[l].dataIndex,!0),o.push(s.inEdges[l].node1)}for(r=0;r<a.length;){var u=a[r];r++,e.set(u.dataIndex,!0);for(l=0;l<u.outEdges.length;l++)t.set(u.outEdges[l].dataIndex,!0),a.push(u.outEdges[l].node2)}}}return{edge:t.keys(),node:e.keys()}},t}(),al=function(){function t(t,e,n){this.dataIndex=-1,this.node1=t,this.node2=e,this.dataIndex=null==n?-1:n}return t.prototype.getModel=function(t){if(!(this.dataIndex<0))return this.hostGraph.edgeData.getItemModel(this.dataIndex).getModel(t)},t.prototype.getAdjacentDataIndices=function(){return{edge:[this.dataIndex],node:[this.node1.dataIndex,this.node2.dataIndex]}},t.prototype.getTrajectoryDataIndices=function(){var t=ie(),e=ie();t.set(this.dataIndex,!0);for(var n=[this.node1],i=[this.node2],o=0;o<n.length;){var a=n[o];o++,e.set(a.dataIndex,!0);for(var r=0;r<a.inEdges.length;r++)t.set(a.inEdges[r].dataIndex,!0),n.push(a.inEdges[r].node1)}for(o=0;o<i.length;){var s=i[o];o++,e.set(s.dataIndex,!0);for(r=0;r<s.outEdges.length;r++)t.set(s.outEdges[r].dataIndex,!0),i.push(s.outEdges[r].node2)}return{edge:t.keys(),node:e.keys()}},t}();function rl(t,e){return{getValue:function(n){var i=this[t][e];return i.getStore().get(i.getDimensionIndex(n||"value"),this.dataIndex)},setVisual:function(n,i){this.dataIndex>=0&&this[t][e].setItemVisual(this.dataIndex,n,i)},getVisual:function(n){return this[t][e].getItemVisual(this.dataIndex,n)},setLayout:function(n,i){this.dataIndex>=0&&this[t][e].setItemLayout(this.dataIndex,n,i)},getLayout:function(){return this[t][e].getItemLayout(this.dataIndex)},getGraphicEl:function(){return this[t][e].getItemGraphicEl(this.dataIndex)},getRawIndex:function(){return this[t][e].getRawIndex(this.dataIndex)}}}function sl(t,e,n,i,o){for(var a=new il(i),r=0;r<t.length;r++)a.addNode(Ce(t[r].id,t[r].name,r),r);var s=[],l=[],u=0;for(r=0;r<e.length;r++){var d=e[r],h=d.source,c=d.target;a.addEdge(h,c,u)&&(l.push(d),s.push(Ce($t(d.id,null),h+" > "+c)),u++)}var p,f=n.get("coordinateSystem");if("cartesian2d"===f||"polar"===f)p=W(t,n);else{var g=Ke.get(f),y=g&&g.dimensions||[];b(y,"value")<0&&y.concat(["value"]);var v=Qt(t,{coordDimensions:y,encodeDefine:n.getEncode()}).dimensions;(p=new Jt(v,n)).initData(t)}var m=new Jt(["value"],n);return m.initData(l,s),o&&o(p,m),Fa({mainData:p,struct:a,structAttr:"graph",datas:{node:p,edge:m},datasAttr:{node:"data",edge:"edgeData"}}),a.update(),a}bt(ol,rl("hostGraph","data")),bt(al,rl("hostGraph","edgeData"));var ll=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.hasSymbolVisual=!0,n}return w(e,t),e.prototype.init=function(e){t.prototype.init.apply(this,arguments);var n=this;function i(){return n._categoriesData}this.legendVisualProvider=new ft(i,i),this.fillDataTextStyle(e.edges||e.links),this._updateCategoriesData()},e.prototype.mergeOption=function(e){t.prototype.mergeOption.apply(this,arguments),this.fillDataTextStyle(e.edges||e.links),this._updateCategoriesData()},e.prototype.mergeDefaultAndTheme=function(e){t.prototype.mergeDefaultAndTheme.apply(this,arguments),$e(e,"edgeLabel",["show"])},e.prototype.getInitialData=function(t,e){var n,i=t.edges||t.links||[],o=t.data||t.nodes||[],a=this;if(o&&i){hs(n=this)&&(n.__curvenessList=[],n.__edgeMap={},cs(n));var r=sl(o,i,this,!0,(function(t,e){t.wrapMethod("getItemModel",(function(t){var e=a._categoriesModels[t.getShallow("category")];return e&&(e.parentModel=t.parentModel,t.parentModel=e),t}));var n=_t.prototype.getModel;function i(t,e){var i=n.call(this,t,e);return i.resolveParentPath=o,i}function o(t){if(t&&("label"===t[0]||"label"===t[1])){var e=t.slice();return"label"===t[0]?e[0]="edgeLabel":"label"===t[1]&&(e[1]="edgeLabel"),e}return t}e.wrapMethod("getItemModel",(function(t){return t.resolveParentPath=o,t.getModel=i,t}))}));return p(r.edges,(function(t){!function(t,e,n,i){if(hs(n)){var o=ps(t,e,n),a=n.__edgeMap,r=a[fs(o)];a[o]&&!r?a[o].isForward=!0:r&&a[o]&&(r.isForward=!0,a[o].isForward=!1),a[o]=a[o]||[],a[o].push(i)}}(t.node1,t.node2,this,t.dataIndex)}),this),r.data}},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.getCategoriesData=function(){return this._categoriesData},e.prototype.formatTooltip=function(t,e,n){if("edge"===n){var i=this.getData(),o=this.getDataParams(t,n),a=i.graph.getEdgeByIndex(t),r=i.getName(a.node1.dataIndex),s=i.getName(a.node2.dataIndex),l=[];return null!=r&&l.push(r),null!=s&&l.push(s),mt("nameValue",{name:l.join(" > "),value:o.value,noValue:null==o.value})}return Qe({series:this,dataIndex:t,multipleSeries:e})},e.prototype._updateCategoriesData=function(){var t=ht(this.option.categories||[],(function(t){return null!=t.value?t:F({value:0},t)})),e=new Jt(["value"],this);e.initData(t),this._categoriesData=e,this._categoriesModels=e.mapArray((function(t){return e.getItemModel(t)}))},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.isAnimationEnabled=function(){return t.prototype.isAnimationEnabled.call(this)&&!("force"===this.get("layout")&&this.get(["force","layoutAnimation"]))},e.type="series.graph",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={z:2,coordinateSystem:"view",legendHoverLink:!0,layout:null,circular:{rotateLabel:!1},force:{initLayout:null,repulsion:[0,50],gravity:.1,friction:.6,edgeLength:30,layoutAnimation:!0},left:"center",top:"center",symbol:"circle",symbolSize:10,edgeSymbol:["none","none"],edgeSymbolSize:10,edgeLabel:{position:"middle",distance:5},draggable:!1,roam:!1,center:null,zoom:1,nodeScaleRatio:.6,label:{show:!1,formatter:"{b}"},itemStyle:{},lineStyle:{color:"#aaa",width:1,opacity:.5},emphasis:{scale:!0,label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(Z),ul={type:"graphRoam",event:"graphRoam",update:"none"};var dl=["itemStyle","opacity"],hl=function(t){function e(e,n){var i=t.call(this)||this,o=i,a=new nt,r=new se;return o.setTextContent(r),i.setTextGuideLine(a),i.updateData(e,n,!0),i}return w(e,t),e.prototype.updateData=function(t,e,n){var i=this,o=t.hostModel,a=t.getItemModel(e),r=t.getItemLayout(e),s=a.getModel("emphasis"),l=a.get(dl);l=null==l?1:l,n||it(i),i.useStyle(t.getItemVisual(e,"style")),i.style.lineJoin="round",n?(i.setShape({points:r.points}),i.style.opacity=0,_(i,{style:{opacity:l}},o,e)):S(i,{style:{opacity:l},shape:{points:r.points}},o,e),at(i,a),this._updateLabel(t,e),dt(this,s.get("focus"),s.get("blurScope"),s.get("disabled"))},e.prototype._updateLabel=function(t,e){var n=this,i=this.getTextGuideLine(),o=n.getTextContent(),a=t.hostModel,r=t.getItemModel(e),s=t.getItemLayout(e).label,l=t.getItemVisual(e,"style"),u=l.fill;lt(o,ut(r),{labelFetcher:t.hostModel,labelDataIndex:e,defaultOpacity:l.opacity,defaultText:t.getName(e)},{normal:{align:s.textAlign,verticalAlign:s.verticalAlign}}),n.setTextConfig({local:!0,inside:!!s.inside,insideStroke:u,outsideFill:u});var d=s.linePoints;i.setShape({points:d}),n.textGuideLineConfig={anchor:d?new tn(d[0][0],d[0][1]):null},S(o,{style:{x:s.x,y:s.y}},a,e),o.attr({rotation:s.rotation,originX:s.x,originY:s.y,z2:10}),f(n,g(r),{stroke:u})},e}(et),cl=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.ignoreLabelLineUpdate=!0,n}return w(e,t),e.prototype.render=function(t,e,n){var i=t.getData(),o=this._data,a=this.group;i.diff(o).add((function(t){var e=new hl(i,t);i.setItemGraphicEl(t,e),a.add(e)})).update((function(t,e){var n=o.getItemGraphicEl(e);n.updateData(i,t),a.add(n),i.setItemGraphicEl(t,n)})).remove((function(e){var n=o.getItemGraphicEl(e);Je(n,t,e)})).execute(),this._data=i},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.prototype.dispose=function(){},e.type="funnel",e}(K),pl=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new ft(gt(this.getData,this),gt(this.getRawData,this)),this._defaultLabelLine(e)},e.prototype.getInitialData=function(t,e){return yt(this,{coordDimensions:["value"],encodeDefaulter:qt(en,this)})},e.prototype._defaultLabelLine=function(t){$e(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},e.prototype.getDataParams=function(e){var n=this.getData(),i=t.prototype.getDataParams.call(this,e),o=n.mapDimension("value"),a=n.getSum(o);return i.percent=a?+(n.get(o,e)/a*100).toFixed(2):0,i.$vars.push("percent"),i},e.type="series.funnel",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",left:80,top:60,right:80,bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",orient:"vertical",gap:0,funnelAlign:"center",label:{show:!0,position:"outer"},labelLine:{show:!0,length:20,lineStyle:{width:1}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(Z);function fl(t,e){t.eachSeriesByType("funnel",(function(t){var n=t.getData(),i=n.mapDimension("value"),a=t.get("sort"),r=function(t,e){return Rt(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}(t,e),l=t.get("orient"),u=r.width,d=r.height,h=function(t,e){for(var n=t.mapDimension("value"),i=t.mapArray(n,(function(t){return t})),a=[],r="ascending"===e,s=0,l=t.count();s<l;s++)a[s]=s;return o(e)?a.sort(e):"none"!==e&&a.sort((function(t,e){return r?i[t]-i[e]:i[e]-i[t]})),a}(n,a),c=r.x,p=r.y,f="horizontal"===l?[s(t.get("minSize"),d),s(t.get("maxSize"),d)]:[s(t.get("minSize"),u),s(t.get("maxSize"),u)],g=n.getDataExtent(i),y=t.get("min"),v=t.get("max");null==y&&(y=Math.min(g[0],0)),null==v&&(v=g[1]);var m=t.get("funnelAlign"),x=t.get("gap"),_=(("horizontal"===l?u:d)-x*(n.count()-1))/n.count(),b=function(t,e){if("horizontal"===l){var o=n.get(i,t)||0,a=Se(o,[y,v],f,!0),r=void 0;switch(m){case"top":r=p;break;case"center":r=p+(d-a)/2;break;case"bottom":r=p+(d-a)}return[[e,r],[e,r+a]]}var s,h=n.get(i,t)||0,g=Se(h,[y,v],f,!0);switch(m){case"left":s=c;break;case"center":s=c+(u-g)/2;break;case"right":s=c+u-g}return[[s,e],[s+g,e]]};"ascending"===a&&(_=-_,x=-x,"horizontal"===l?c+=u:p+=d,h=h.reverse());for(var S=0;S<h.length;S++){var I=h[S],w=h[S+1],M=n.getItemModel(I);if("horizontal"===l){var L=M.get(["itemStyle","width"]);null==L?L=_:(L=s(L,u),"ascending"===a&&(L=-L));var D=b(I,c),C=b(w,c+L);c+=L+x,n.setItemLayout(I,{points:D.concat(C.slice().reverse())})}else{var T=M.get(["itemStyle","height"]);null==T?T=_:(T=s(T,d),"ascending"===a&&(T=-T));D=b(I,p),C=b(w,p+T);p+=T+x,n.setItemLayout(I,{points:D.concat(C.slice().reverse())})}}!function(t){var e=t.hostModel.get("orient");t.each((function(n){var i,o,a,r,s=t.getItemModel(n),l=s.getModel("label").get("position"),u=s.getModel("labelLine"),d=t.getItemLayout(n),h=d.points,c="inner"===l||"inside"===l||"center"===l||"insideLeft"===l||"insideRight"===l;if(c)"insideLeft"===l?(o=(h[0][0]+h[3][0])/2+5,a=(h[0][1]+h[3][1])/2,i="left"):"insideRight"===l?(o=(h[1][0]+h[2][0])/2-5,a=(h[1][1]+h[2][1])/2,i="right"):(o=(h[0][0]+h[1][0]+h[2][0]+h[3][0])/4,a=(h[0][1]+h[1][1]+h[2][1]+h[3][1])/4,i="center"),r=[[o,a],[o,a]];else{var p=void 0,f=void 0,g=void 0,y=void 0,v=u.get("length");"left"===l?(p=(h[3][0]+h[0][0])/2,f=(h[3][1]+h[0][1])/2,o=(g=p-v)-5,i="right"):"right"===l?(p=(h[1][0]+h[2][0])/2,f=(h[1][1]+h[2][1])/2,o=(g=p+v)+5,i="left"):"top"===l?(p=(h[3][0]+h[0][0])/2,a=(y=(f=(h[3][1]+h[0][1])/2)-v)-5,i="center"):"bottom"===l?(p=(h[1][0]+h[2][0])/2,a=(y=(f=(h[1][1]+h[2][1])/2)+v)+5,i="center"):"rightTop"===l?(p="horizontal"===e?h[3][0]:h[1][0],f="horizontal"===e?h[3][1]:h[1][1],"horizontal"===e?(a=(y=f-v)-5,i="center"):(o=(g=p+v)+5,i="top")):"rightBottom"===l?(p=h[2][0],f=h[2][1],"horizontal"===e?(a=(y=f+v)+5,i="center"):(o=(g=p+v)+5,i="bottom")):"leftTop"===l?(p=h[0][0],f="horizontal"===e?h[0][1]:h[1][1],"horizontal"===e?(a=(y=f-v)-5,i="center"):(o=(g=p-v)-5,i="right")):"leftBottom"===l?(p="horizontal"===e?h[1][0]:h[3][0],f="horizontal"===e?h[1][1]:h[2][1],"horizontal"===e?(a=(y=f+v)+5,i="center"):(o=(g=p-v)-5,i="right")):(p=(h[1][0]+h[2][0])/2,f=(h[1][1]+h[2][1])/2,"horizontal"===e?(a=(y=f+v)+5,i="center"):(o=(g=p+v)+5,i="left")),"horizontal"===e?o=g=p:a=y=f,r=[[p,f],[g,y]]}d.label={linePoints:r,x:o,y:a,verticalAlign:"middle",textAlign:i,inside:c}}))}(n)}))}var gl=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n._dataGroup=new Y,n._initialized=!1,n}return w(e,t),e.prototype.init=function(){this.group.add(this._dataGroup)},e.prototype.render=function(t,e,n,i){this._progressiveEls=null;var o=this._dataGroup,a=t.getData(),r=this._data,s=t.coordinateSystem,l=s.dimensions,u=ml(t);if(a.diff(r).add((function(t){xl(vl(a,o,t,l,s),a,t,u)})).update((function(e,n){var i=r.getItemGraphicEl(n),o=yl(a,e,l,s);a.setItemGraphicEl(e,i),S(i,{shape:{points:o}},t,e),it(i),xl(i,a,e,u)})).remove((function(t){var e=r.getItemGraphicEl(t);o.remove(e)})).execute(),!this._initialized){this._initialized=!0;var d=function(t,e,n){var i=t.model,o=t.getRect(),a=new be({shape:{x:o.x,y:o.y,width:o.width,height:o.height}}),r="horizontal"===i.get("layout")?"width":"height";return a.setShape(r,0),_(a,{shape:{width:o.width,height:o.height}},e,n),a}(s,t,(function(){setTimeout((function(){o.removeClipPath()}))}));o.setClipPath(d)}this._data=a},e.prototype.incrementalPrepareRender=function(t,e,n){this._initialized=!0,this._data=null,this._dataGroup.removeAll()},e.prototype.incrementalRender=function(t,e,n){for(var i=e.getData(),o=e.coordinateSystem,a=o.dimensions,r=ml(e),s=this._progressiveEls=[],l=t.start;l<t.end;l++){var u=vl(i,this._dataGroup,l,a,o);u.incremental=!0,xl(u,i,l,r),s.push(u)}},e.prototype.remove=function(){this._dataGroup&&this._dataGroup.removeAll(),this._data=null},e.type="parallel",e}(K);function yl(t,e,n,i){for(var o,a=[],r=0;r<n.length;r++){var s=n[r],l=t.get(t.mapDimension(s),e);o=l,("category"===i.getAxis(s).type?null==o:null==o||isNaN(o))||a.push(i.dataToPoint(l,s))}return a}function vl(t,e,n,i,o){var a=yl(t,n,i,o),r=new nt({shape:{points:a},z2:10});return e.add(r),t.setItemGraphicEl(n,r),r}function ml(t){var e=t.get("smooth",!0);return!0===e&&(e=.3),e=nn(e),on(e)&&(e=0),{smooth:e}}function xl(t,e,n,i){t.useStyle(e.getItemVisual(n,"style")),t.style.fill=null,t.setShape("smooth",i.smooth);var o=e.getItemModel(n),a=o.getModel("emphasis");at(t,o,"lineStyle"),dt(t,a.get("focus"),a.get("blurScope"),a.get("disabled"))}var _l=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.visualStyleAccessPath="lineStyle",n.visualDrawType="stroke",n}return w(e,t),e.prototype.getInitialData=function(t,e){return W(null,this,{useEncodeDefaulter:gt(bl,null,this)})},e.prototype.getRawIndicesByActiveState=function(t){var e=this.coordinateSystem,n=this.getData(),i=[];return e.eachActiveState(n,(function(e,o){t===e&&i.push(n.getRawIndex(o))})),i},e.type="series.parallel",e.dependencies=["parallel"],e.defaultOption={z:2,coordinateSystem:"parallel",parallelIndex:0,label:{show:!1},inactiveOpacity:.05,activeOpacity:1,lineStyle:{width:1,opacity:.45,type:"solid"},emphasis:{label:{show:!1}},progressive:500,smooth:!1,animationEasing:"linear"},e}(Z);function bl(t){var e=t.ecModel.getComponent("parallel",t.get("parallelIndex"));if(e){var n={};return p(e.dimensions,(function(t){var e=+t.replace("dim","");n[t]=e})),n}}var Sl=["lineStyle","opacity"],Il={seriesType:"parallel",reset:function(t,e){var n=t.coordinateSystem,i={normal:t.get(["lineStyle","opacity"]),active:t.get("activeOpacity"),inactive:t.get("inactiveOpacity")};return{progress:function(t,e){n.eachActiveState(e,(function(t,n){var o=i[t];if("normal"===t&&e.hasItemOption){var a=e.getItemModel(n).get(Sl,!0);null!=a&&(o=a)}e.ensureUniqueItemVisual(n,"style").opacity=o}),t.start,t.end)}}}};var wl=function(){return function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.cpx2=0,this.cpy2=0,this.extent=0}}(),Ml=function(t){function e(e){return t.call(this,e)||this}return w(e,t),e.prototype.getDefaultShape=function(){return new wl},e.prototype.buildPath=function(t,e){var n=e.extent;t.moveTo(e.x1,e.y1),t.bezierCurveTo(e.cpx1,e.cpy1,e.cpx2,e.cpy2,e.x2,e.y2),"vertical"===e.orient?(t.lineTo(e.x2+n,e.y2),t.bezierCurveTo(e.cpx2+n,e.cpy2,e.cpx1+n,e.cpy1,e.x1+n,e.y1)):(t.lineTo(e.x2,e.y2+n),t.bezierCurveTo(e.cpx2,e.cpy2+n,e.cpx1,e.cpy1+n,e.x1,e.y1+n)),t.closePath()},e.prototype.highlight=function(){We(this)},e.prototype.downplay=function(){Ze(this)},e}(X),Ll=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n._focusAdjacencyDisabled=!1,n}return w(e,t),e.prototype.render=function(t,e,n){var i=this,o=t.getGraph(),a=this.group,s=t.layoutInfo,l=s.width,u=s.height,d=t.getData(),h=t.getData("edge"),c=t.get("orient");this._model=t,a.removeAll(),a.x=s.x,a.y=s.y,o.eachEdge((function(e){var n=new Ml,i=r(n);i.dataIndex=e.dataIndex,i.seriesIndex=t.seriesIndex,i.dataType="edge";var o,s,d,p,f,g,y,v,m=e.getModel(),x=m.getModel("lineStyle"),_=x.get("curveness"),b=e.node1.getLayout(),S=e.node1.getModel(),I=S.get("localX"),w=S.get("localY"),M=e.node2.getLayout(),L=e.node2.getModel(),D=L.get("localX"),C=L.get("localY"),T=e.getLayout();n.shape.extent=Math.max(1,T.dy),n.shape.orient=c,"vertical"===c?(o=(null!=I?I*l:b.x)+T.sy,s=(null!=w?w*u:b.y)+b.dy,d=(null!=D?D*l:M.x)+T.ty,f=o,g=s*(1-_)+(p=null!=C?C*u:M.y)*_,y=d,v=s*_+p*(1-_)):(o=(null!=I?I*l:b.x)+b.dx,s=(null!=w?w*u:b.y)+T.sy,f=o*(1-_)+(d=null!=D?D*l:M.x)*_,g=s,y=o*_+d*(1-_),v=p=(null!=C?C*u:M.y)+T.ty),n.setShape({x1:o,y1:s,x2:d,y2:p,cpx1:f,cpy1:g,cpx2:y,cpy2:v}),n.useStyle(x.getItemStyle()),Dl(n.style,c,e);var A=""+m.get("value"),P=ut(m,"edgeLabel");lt(n,P,{labelFetcher:{getFormattedLabel:function(e,n,i,o,a,r){return t.getFormattedLabel(e,n,"edge",o,Ae(a,P.normal&&P.normal.get("formatter"),A),r)}},labelDataIndex:e.dataIndex,defaultText:A}),n.setTextConfig({position:"inside"});var k=m.getModel("emphasis");at(n,m,"lineStyle",(function(t){var n=t.getItemStyle();return Dl(n,c,e),n})),a.add(n),h.setItemGraphicEl(e.dataIndex,n);var N=k.get("focus");dt(n,"adjacency"===N?e.getAdjacentDataIndices():"trajectory"===N?e.getTrajectoryDataIndices():N,k.get("blurScope"),k.get("disabled"))})),o.eachNode((function(e){var n=e.getLayout(),i=e.getModel(),o=i.get("localX"),s=i.get("localY"),h=i.getModel("emphasis"),c=i.get(["itemStyle","borderRadius"])||0,p=new be({shape:{x:null!=o?o*l:n.x,y:null!=s?s*u:n.y,width:n.dx,height:n.dy,r:c},style:i.getModel("itemStyle").getItemStyle(),z2:10});lt(p,ut(i),{labelFetcher:{getFormattedLabel:function(e,n){return t.getFormattedLabel(e,n,"node")}},labelDataIndex:e.dataIndex,defaultText:e.id}),p.disableLabelAnimation=!0,p.setStyle("fill",e.getVisual("color")),p.setStyle("decal",e.getVisual("style").decal),at(p,i),a.add(p),d.setItemGraphicEl(e.dataIndex,p),r(p).dataType="node";var f=h.get("focus");dt(p,"adjacency"===f?e.getAdjacentDataIndices():"trajectory"===f?e.getTrajectoryDataIndices():f,h.get("blurScope"),h.get("disabled"))})),d.eachItemGraphicEl((function(e,o){d.getItemModel(o).get("draggable")&&(e.drift=function(e,a){i._focusAdjacencyDisabled=!0,this.shape.x+=e,this.shape.y+=a,this.dirty(),n.dispatchAction({type:"dragNode",seriesId:t.id,dataIndex:d.getRawIndex(o),localX:this.shape.x/l,localY:this.shape.y/u})},e.ondragend=function(){i._focusAdjacencyDisabled=!1},e.draggable=!0,e.cursor="move")})),!this._data&&t.isAnimationEnabled()&&a.setClipPath(function(t,e,n){var i=new be({shape:{x:t.x-10,y:t.y-10,width:0,height:t.height+20}});return _(i,{shape:{width:t.width+20}},e,n),i}(a.getBoundingRect(),t,(function(){a.removeClipPath()}))),this._data=t.getData()},e.prototype.dispose=function(){},e.type="sankey",e}(K);function Dl(t,e,n){switch(t.fill){case"source":t.fill=n.node1.getVisual("color"),t.decal=n.node1.getVisual("style").decal;break;case"target":t.fill=n.node2.getVisual("color"),t.decal=n.node2.getVisual("style").decal;break;case"gradient":var i=n.node1.getVisual("color"),o=n.node2.getVisual("color");xt(i)&&xt(o)&&(t.fill=new rn(0,0,+("horizontal"===e),+("vertical"===e),[{color:i,offset:0},{color:o,offset:1}]))}}var Cl=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.getInitialData=function(t,e){var n=t.edges||t.links,i=t.data||t.nodes,o=t.levels;this.levelModels=[];for(var a=this.levelModels,r=0;r<o.length;r++)null!=o[r].depth&&o[r].depth>=0&&(a[o[r].depth]=new _t(o[r],this,e));if(i&&n)return sl(i,n,this,!0,(function(t,e){t.wrapMethod("getItemModel",(function(t,e){var n=t.parentModel,i=n.getData().getItemLayout(e);if(i){var o=i.depth,a=n.levelModels[o];a&&(t.parentModel=a)}return t})),e.wrapMethod("getItemModel",(function(t,e){var n=t.parentModel,i=n.getGraph().getEdgeByIndex(e).node1.getLayout();if(i){var o=i.depth,a=n.levelModels[o];a&&(t.parentModel=a)}return t}))})).data},e.prototype.setNodePosition=function(t,e){var n=(this.option.data||this.option.nodes)[t];n.localX=e[0],n.localY=e[1]},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.formatTooltip=function(t,e,n){function i(t){return isNaN(t)||null==t}if("edge"===n){var o=this.getDataParams(t,n),a=o.data,r=o.value,s=a.source+" -- "+a.target;return mt("nameValue",{name:s,value:r,noValue:i(r)})}var l=this.getGraph().getNodeByIndex(t).getLayout().value,u=this.getDataParams(t,n).data.name;return mt("nameValue",{name:null!=u?u+"":null,value:l,noValue:i(l)})},e.prototype.optionUpdated=function(){},e.prototype.getDataParams=function(e,n){var i=t.prototype.getDataParams.call(this,e,n);if(null==i.value&&"node"===n){var o=this.getGraph().getNodeByIndex(e).getLayout().value;i.value=o}return i},e.type="series.sankey",e.defaultOption={z:2,coordinateSystem:"view",left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,layoutIterations:32,label:{show:!0,position:"right",fontSize:12},edgeLabel:{show:!1,fontSize:12},levels:[],nodeAlign:"justify",lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.5}},select:{itemStyle:{borderColor:"#212121"}},animationEasing:"linear",animationDuration:1e3},e}(Z);function Tl(t,e){t.eachSeriesByType("sankey",(function(t){var n=t.get("nodeWidth"),i=t.get("nodeGap"),o=function(t,e){return Rt(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}(t,e);t.layoutInfo=o;var a=o.width,r=o.height,s=t.getGraph(),l=s.nodes,d=s.edges;!function(t){p(t,(function(t){var e=Bl(t.outEdges,El),n=Bl(t.inEdges,El),i=t.getValue()||0,o=Math.max(e,n,i);t.setLayout({value:o},!0)}))}(l),function(t,e,n,i,o,a,r,s,l){(function(t,e,n,i,o,a,r){for(var s=[],l=[],u=[],d=[],h=0,c=0;c<e.length;c++)s[c]=1;for(c=0;c<t.length;c++)l[c]=t[c].inEdges.length,0===l[c]&&u.push(t[c]);var f=-1;for(;u.length;){for(var g=0;g<u.length;g++){var y=u[g],v=y.hostGraph.data.getRawDataItem(y.dataIndex),m=null!=v.depth&&v.depth>=0;m&&v.depth>f&&(f=v.depth),y.setLayout({depth:m?v.depth:h},!0),"vertical"===a?y.setLayout({dy:n},!0):y.setLayout({dx:n},!0);for(var x=0;x<y.outEdges.length;x++){var _=y.outEdges[x];s[e.indexOf(_)]=0;var b=_.node2;0==--l[t.indexOf(b)]&&d.indexOf(b)<0&&d.push(b)}}++h,u=d,d=[]}for(c=0;c<s.length;c++)if(1===s[c])throw new Error("Sankey is a DAG, the original data has cycle!");var S=f>h-1?f:h-1;r&&"left"!==r&&function(t,e,n,i){if("right"===e){for(var o=[],a=t,r=0;a.length;){for(var s=0;s<a.length;s++){var l=a[s];l.setLayout({skNodeHeight:r},!0);for(var u=0;u<l.inEdges.length;u++){var d=l.inEdges[u];o.indexOf(d.node1)<0&&o.push(d.node1)}}a=o,o=[],++r}p(t,(function(t){Al(t)||t.setLayout({depth:Math.max(0,i-t.getLayout().skNodeHeight)},!0)}))}else"justify"===e&&function(t,e){p(t,(function(t){Al(t)||t.outEdges.length||t.setLayout({depth:e},!0)}))}(t,i)}(t,r,0,S);var I="vertical"===a?(o-n)/S:(i-n)/S;!function(t,e,n){p(t,(function(t){var i=t.getLayout().depth*e;"vertical"===n?t.setLayout({y:i},!0):t.setLayout({x:i},!0)}))}(t,I,a)})(t,e,n,o,a,s,l),function(t,e,n,i,o,a,r){var s=function(t,e){var n=[],i="vertical"===e?"y":"x",o=sn(t,(function(t){return t.getLayout()[i]}));return o.keys.sort((function(t,e){return t-e})),p(o.keys,(function(t){n.push(o.buckets.get(t))})),n}(t,r);(function(t,e,n,i,o,a){var r=1/0;p(t,(function(t){var e=t.length,s=0;p(t,(function(t){s+=t.getLayout().value}));var l="vertical"===a?(i-(e-1)*o)/s:(n-(e-1)*o)/s;l<r&&(r=l)})),p(t,(function(t){p(t,(function(t,e){var n=t.getLayout().value*r;"vertical"===a?(t.setLayout({x:e},!0),t.setLayout({dx:n},!0)):(t.setLayout({y:e},!0),t.setLayout({dy:n},!0))}))})),p(e,(function(t){var e=+t.getValue()*r;t.setLayout({dy:e},!0)}))})(s,e,n,i,o,r),Pl(s,o,n,i,r);for(var l=1;a>0;a--)kl(s,l*=.99,r),Pl(s,o,n,i,r),Gl(s,l,r),Pl(s,o,n,i,r)}(t,e,a,o,i,r,s),function(t,e){var n="vertical"===e?"x":"y";p(t,(function(t){t.outEdges.sort((function(t,e){return t.node2.getLayout()[n]-e.node2.getLayout()[n]})),t.inEdges.sort((function(t,e){return t.node1.getLayout()[n]-e.node1.getLayout()[n]}))})),p(t,(function(t){var e=0,n=0;p(t.outEdges,(function(t){t.setLayout({sy:e},!0),e+=t.getLayout().dy})),p(t.inEdges,(function(t){t.setLayout({ty:n},!0),n+=t.getLayout().dy}))}))}(t,s)}(l,d,n,i,a,r,0!==u(l,(function(t){return 0===t.getLayout().value})).length?0:t.get("layoutIterations"),t.get("orient"),t.get("nodeAlign"))}))}function Al(t){var e=t.hostGraph.data.getRawDataItem(t.dataIndex);return null!=e.depth&&e.depth>=0}function Pl(t,e,n,i,o){var a="vertical"===o?"x":"y";p(t,(function(t){var r,s,l;t.sort((function(t,e){return t.getLayout()[a]-e.getLayout()[a]}));for(var u=0,d=t.length,h="vertical"===o?"dx":"dy",c=0;c<d;c++)(l=u-(s=t[c]).getLayout()[a])>0&&(r=s.getLayout()[a]+l,"vertical"===o?s.setLayout({x:r},!0):s.setLayout({y:r},!0)),u=s.getLayout()[a]+s.getLayout()[h]+e;if((l=u-e-("vertical"===o?i:n))>0){r=s.getLayout()[a]-l,"vertical"===o?s.setLayout({x:r},!0):s.setLayout({y:r},!0),u=r;for(c=d-2;c>=0;--c)(l=(s=t[c]).getLayout()[a]+s.getLayout()[h]+e-u)>0&&(r=s.getLayout()[a]-l,"vertical"===o?s.setLayout({x:r},!0):s.setLayout({y:r},!0)),u=s.getLayout()[a]}}))}function kl(t,e,n){p(t.slice().reverse(),(function(t){p(t,(function(t){if(t.outEdges.length){var i=Bl(t.outEdges,Nl,n)/Bl(t.outEdges,El);if(isNaN(i)){var o=t.outEdges.length;i=o?Bl(t.outEdges,Vl,n)/o:0}if("vertical"===n){var a=t.getLayout().x+(i-Ol(t,n))*e;t.setLayout({x:a},!0)}else{var r=t.getLayout().y+(i-Ol(t,n))*e;t.setLayout({y:r},!0)}}}))}))}function Nl(t,e){return Ol(t.node2,e)*t.getValue()}function Vl(t,e){return Ol(t.node2,e)}function Rl(t,e){return Ol(t.node1,e)*t.getValue()}function zl(t,e){return Ol(t.node1,e)}function Ol(t,e){return"vertical"===e?t.getLayout().x+t.getLayout().dx/2:t.getLayout().y+t.getLayout().dy/2}function El(t){return t.getValue()}function Bl(t,e,n){for(var i=0,o=t.length,a=-1;++a<o;){var r=+e(t[a],n);isNaN(r)||(i+=r)}return i}function Gl(t,e,n){p(t,(function(t){p(t,(function(t){if(t.inEdges.length){var i=Bl(t.inEdges,Rl,n)/Bl(t.inEdges,El);if(isNaN(i)){var o=t.inEdges.length;i=o?Bl(t.inEdges,zl,n)/o:0}if("vertical"===n){var a=t.getLayout().x+(i-Ol(t,n))*e;t.setLayout({x:a},!0)}else{var r=t.getLayout().y+(i-Ol(t,n))*e;t.setLayout({y:r},!0)}}}))}))}function Hl(t){t.eachSeriesByType("sankey",(function(t){var e=t.getGraph(),n=e.nodes,i=e.edges;if(n.length){var o=1/0,a=-1/0;p(n,(function(t){var e=t.getLayout().value;e<o&&(o=e),e>a&&(a=e)})),p(n,(function(e){var n=new Cr({type:"color",mappingMethod:"linear",dataExtent:[o,a],visual:t.get("color")}).mapValueToVisual(e.getLayout().value),i=e.getModel().get(["itemStyle","color"]);null!=i?(e.setVisual("color",i),e.setVisual("style",{fill:i})):(e.setVisual("color",n),e.setVisual("style",{fill:n}))}))}i.length&&p(i,(function(t){var e=t.getModel().get("lineStyle");t.setVisual("style",e)}))}))}var Fl=function(){function t(){}return t.prototype.getInitialData=function(t,e){var n,i,o=e.getComponent("xAxis",this.get("xAxisIndex")),a=e.getComponent("yAxis",this.get("yAxisIndex")),r=o.get("type"),s=a.get("type");"category"===r?(t.layout="horizontal",n=o.getOrdinalMeta(),i=!0):"category"===s?(t.layout="vertical",n=a.getOrdinalMeta(),i=!0):t.layout=t.layout||"horizontal";var l=["x","y"],u="horizontal"===t.layout?0:1,d=this._baseAxisDim=l[u],h=l[1-u],c=[o,a],f=c[u].get("type"),g=c[1-u].get("type"),y=t.data;if(y&&i){var v=[];p(y,(function(t,e){var n;tt(t)?(n=t.slice(),t.unshift(e)):tt(t.value)?((n=F({},t)).value=n.value.slice(),t.value.unshift(e)):n=t,v.push(n)})),t.data=v}var m=this.defaultValueDimensions,x=[{name:d,type:ln(f),ordinalMeta:n,otherDims:{tooltip:!1,itemName:0},dimsDef:["base"]},{name:h,type:ln(g),dimsDef:m.slice()}];return yt(this,{coordDimensions:x,dimensionsCount:m.length+1,encodeDefaulter:qt(un,x,this)})},t.prototype.getBaseAxis=function(){var t=this._baseAxisDim;return this.ecModel.getComponent(t+"Axis",this.get(t+"AxisIndex")).axis},t}(),Wl=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.defaultValueDimensions=[{name:"min",defaultTooltip:!0},{name:"Q1",defaultTooltip:!0},{name:"median",defaultTooltip:!0},{name:"Q3",defaultTooltip:!0},{name:"max",defaultTooltip:!0}],n.visualDrawType="stroke",n}return w(e,t),e.type="series.boxplot",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,boxWidth:[7,50],itemStyle:{color:"#fff",borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2,shadowBlur:5,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}},animationDuration:800},e}(Z);bt(Wl,Fl,!0);var Zl=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n){var i=t.getData(),o=this.group,a=this._data;this._data||o.removeAll();var r="horizontal"===t.get("layout")?1:0;i.diff(a).add((function(t){if(i.hasValue(t)){var e=Ul(i.getItemLayout(t),i,t,r,!0);i.setItemGraphicEl(t,e),o.add(e)}})).update((function(t,e){var n=a.getItemGraphicEl(e);if(i.hasValue(t)){var s=i.getItemLayout(t);n?(it(n),jl(s,n,i,t)):n=Ul(s,i,t,r),o.add(n),i.setItemGraphicEl(t,n)}else o.remove(n)})).remove((function(t){var e=a.getItemGraphicEl(t);e&&o.remove(e)})).execute(),this._data=i},e.prototype.remove=function(t){var e=this.group,n=this._data;this._data=null,n&&n.eachItemGraphicEl((function(t){t&&e.remove(t)}))},e.type="boxplot",e}(K),Yl=function(){return function(){}}(),Xl=function(t){function e(e){var n=t.call(this,e)||this;return n.type="boxplotBoxPath",n}return w(e,t),e.prototype.getDefaultShape=function(){return new Yl},e.prototype.buildPath=function(t,e){var n=e.points,i=0;for(t.moveTo(n[i][0],n[i][1]),i++;i<4;i++)t.lineTo(n[i][0],n[i][1]);for(t.closePath();i<n.length;i++)t.moveTo(n[i][0],n[i][1]),i++,t.lineTo(n[i][0],n[i][1])},e}(X);function Ul(t,e,n,i,o){var a=t.ends,r=new Xl({shape:{points:o?ql(a,i,t):a}});return jl(t,r,e,n,o),r}function jl(t,e,n,i,o){var a=n.hostModel;(0,ct[o?"initProps":"updateProps"])(e,{shape:{points:t.ends}},a,i),e.useStyle(n.getItemVisual(i,"style")),e.style.strokeNoScale=!0,e.z2=100;var r=n.getItemModel(i),s=r.getModel("emphasis");at(e,r),dt(e,s.get("focus"),s.get("blurScope"),s.get("disabled"))}function ql(t,e,n){return ht(t,(function(t){return(t=t.slice())[e]=n.initBaseline,t}))}var Kl=p;function $l(t){var e=function(t){var e=[],n=[];return t.eachSeriesByType("boxplot",(function(t){var i=t.getBaseAxis(),o=b(n,i);o<0&&(o=n.length,n[o]=i,e[o]={axis:i,seriesModels:[]}),e[o].seriesModels.push(t)})),e}(t);Kl(e,(function(t){var e=t.seriesModels;e.length&&(!function(t){var e,n=t.axis,i=t.seriesModels,o=i.length,a=t.boxWidthList=[],r=t.boxOffsetList=[],l=[];if("category"===n.type)e=n.getBandWidth();else{var u=0;Kl(i,(function(t){u=Math.max(u,t.getData().count())}));var d=n.getExtent();e=Math.abs(d[1]-d[0])/u}Kl(i,(function(t){var n=t.get("boxWidth");tt(n)||(n=[n,n]),l.push([s(n[0],e)||0,s(n[1],e)||0])}));var h=.8*e-2,c=h/o*.3,p=(h-c*(o-1))/o,f=p/2-h/2;Kl(i,(function(t,e){r.push(f),f+=c+p,a.push(Math.min(Math.max(p,l[e][0]),l[e][1]))}))}(t),Kl(e,(function(e,n){!function(t,e,n){var i=t.coordinateSystem,o=t.getData(),a=n/2,r="horizontal"===t.get("layout")?0:1,s=1-r,l=["x","y"],u=o.mapDimension(l[r]),d=o.mapDimensionsAll(l[s]);if(null==u||d.length<5)return;for(var h=0;h<o.count();h++){var c=o.get(u,h),p=x(c,d[2],h),f=x(c,d[0],h),g=x(c,d[1],h),y=x(c,d[3],h),v=x(c,d[4],h),m=[];_(m,g,!1),_(m,y,!0),m.push(f,g,v,y),b(m,f),b(m,v),b(m,p),o.setItemLayout(h,{initBaseline:p[s],ends:m})}function x(t,n,a){var l,u=o.get(n,a),d=[];return d[r]=t,d[s]=u,isNaN(t)||isNaN(u)?l=[NaN,NaN]:(l=i.dataToPoint(d))[r]+=e,l}function _(t,e,n){var i=e.slice(),o=e.slice();i[r]+=a,o[r]-=a,n?t.push(i,o):t.push(o,i)}function b(t,e){var n=e.slice(),i=e.slice();n[r]-=a,i[r]+=a,t.push(n,i)}}(e,t.boxOffsetList[n],t.boxWidthList[n])})))}))}var Ql={type:"echarts:boxplot",transform:function(t){var e=t.upstream;if(e.sourceFormat!==cn){pn("")}var n=function(t,e){for(var n=[],i=[],a=(e=e||{}).boundIQR,r="none"===a||0===a,s=0;s<t.length;s++){var l=dn(t[s].slice()),u=hn(l,.25),d=hn(l,.5),h=hn(l,.75),c=l[0],p=l[l.length-1],f=(null==a?1.5:a)*(h-u),g=r?c:Math.max(c,u-f),y=r?p:Math.min(p,h+f),v=e.itemNameFormatter,m=o(v)?v({value:s}):xt(v)?v.replace("{value}",s+""):s+"";n.push([m,g,u,d,h,y]);for(var x=0;x<l.length;x++){var _=l[x];if(_<g||_>y){var b=[m,_];i.push(b)}}}return{boxData:n,outliers:i}}(e.getRawData(),t.config);return[{dimensions:["ItemName","Low","Q1","Q2","Q3","High"],data:n.boxData},{data:n.outliers}]}};var Jl=["color","borderColor"],tu=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n){this.group.removeClipPath(),this._progressiveEls=null,this._updateDrawMode(t),this._isLargeDraw?this._renderLarge(t):this._renderNormal(t)},e.prototype.incrementalPrepareRender=function(t,e,n){this._clear(),this._updateDrawMode(t)},e.prototype.incrementalRender=function(t,e,n,i){this._progressiveEls=[],this._isLargeDraw?this._incrementalRenderLarge(t,e):this._incrementalRenderNormal(t,e)},e.prototype.eachRendered=function(t){Xe(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var e=t.pipelineContext.large;null!=this._isLargeDraw&&e===this._isLargeDraw||(this._isLargeDraw=e,this._clear())},e.prototype._renderNormal=function(t){var e=t.getData(),n=this._data,i=this.group,o=e.getLayout("isSimpleBox"),a=t.get("clip",!0),r=t.coordinateSystem,s=r.getArea&&r.getArea();this._data||i.removeAll(),e.diff(n).add((function(n){if(e.hasValue(n)){var r=e.getItemLayout(n);if(a&&ou(s,r))return;var l=iu(r,n,!0);_(l,{shape:{points:r.ends}},t,n),au(l,e,n,o),i.add(l),e.setItemGraphicEl(n,l)}})).update((function(r,l){var u=n.getItemGraphicEl(l);if(e.hasValue(r)){var d=e.getItemLayout(r);a&&ou(s,d)?i.remove(u):(u?(S(u,{shape:{points:d.ends}},t,r),it(u)):u=iu(d),au(u,e,r,o),i.add(u),e.setItemGraphicEl(r,u))}else i.remove(u)})).remove((function(t){var e=n.getItemGraphicEl(t);e&&i.remove(e)})).execute(),this._data=e},e.prototype._renderLarge=function(t){this._clear(),uu(t,this.group);var e=t.get("clip",!0)?fn(t.coordinateSystem,!1,t):null;e?this.group.setClipPath(e):this.group.removeClipPath()},e.prototype._incrementalRenderNormal=function(t,e){for(var n,i=e.getData(),o=i.getLayout("isSimpleBox");null!=(n=t.next());){var a=iu(i.getItemLayout(n));au(a,i,n,o),a.incremental=!0,this.group.add(a),this._progressiveEls.push(a)}},e.prototype._incrementalRenderLarge=function(t,e){uu(e,this.group,this._progressiveEls,!0)},e.prototype.remove=function(t){this._clear()},e.prototype._clear=function(){this.group.removeAll(),this._data=null},e.type="candlestick",e}(K),eu=function(){return function(){}}(),nu=function(t){function e(e){var n=t.call(this,e)||this;return n.type="normalCandlestickBox",n}return w(e,t),e.prototype.getDefaultShape=function(){return new eu},e.prototype.buildPath=function(t,e){var n=e.points;this.__simpleBox?(t.moveTo(n[4][0],n[4][1]),t.lineTo(n[6][0],n[6][1])):(t.moveTo(n[0][0],n[0][1]),t.lineTo(n[1][0],n[1][1]),t.lineTo(n[2][0],n[2][1]),t.lineTo(n[3][0],n[3][1]),t.closePath(),t.moveTo(n[4][0],n[4][1]),t.lineTo(n[5][0],n[5][1]),t.moveTo(n[6][0],n[6][1]),t.lineTo(n[7][0],n[7][1]))},e}(X);function iu(t,e,n){var i=t.ends;return new nu({shape:{points:n?ru(i,t):i},z2:100})}function ou(t,e){for(var n=!0,i=0;i<e.ends.length;i++)if(t.contain(e.ends[i][0],e.ends[i][1])){n=!1;break}return n}function au(t,e,n,i){var o=e.getItemModel(n);t.useStyle(e.getItemVisual(n,"style")),t.style.strokeNoScale=!0,t.__simpleBox=i,at(t,o)}function ru(t,e){return ht(t,(function(t){return(t=t.slice())[1]=e.initBaseline,t}))}var su=function(){return function(){}}(),lu=function(t){function e(e){var n=t.call(this,e)||this;return n.type="largeCandlestickBox",n}return w(e,t),e.prototype.getDefaultShape=function(){return new su},e.prototype.buildPath=function(t,e){for(var n=e.points,i=0;i<n.length;)if(this.__sign===n[i++]){var o=n[i++];t.moveTo(o,n[i++]),t.lineTo(o,n[i++])}else i+=3},e}(X);function uu(t,e,n,i){var o=t.getData().getLayout("largePoints"),a=new lu({shape:{points:o},__sign:1,ignoreCoarsePointer:!0});e.add(a);var r=new lu({shape:{points:o},__sign:-1,ignoreCoarsePointer:!0});e.add(r);var s=new lu({shape:{points:o},__sign:0,ignoreCoarsePointer:!0});e.add(s),du(1,a,t),du(-1,r,t),du(0,s,t),i&&(a.incremental=!0,r.incremental=!0),n&&n.push(a,r)}function du(t,e,n,i){var o=n.get(["itemStyle",t>0?"borderColor":"borderColor0"])||n.get(["itemStyle",t>0?"color":"color0"]);0===t&&(o=n.get(["itemStyle","borderColorDoji"]));var a=n.getModel("itemStyle").getItemStyle(Jl);e.useStyle(a),e.style.fill=null,e.style.stroke=o}var hu=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.defaultValueDimensions=[{name:"open",defaultTooltip:!0},{name:"close",defaultTooltip:!0},{name:"lowest",defaultTooltip:!0},{name:"highest",defaultTooltip:!0}],n}return w(e,t),e.prototype.getShadowDim=function(){return"open"},e.prototype.brushSelector=function(t,e,n){var i=e.getItemLayout(t);return i&&n.rect(i.brushRect)},e.type="series.candlestick",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,clip:!0,itemStyle:{color:"#eb5454",color0:"#47b262",borderColor:"#eb5454",borderColor0:"#47b262",borderColorDoji:null,borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2}},barMaxWidth:null,barMinWidth:null,barWidth:null,large:!0,largeThreshold:600,progressive:3e3,progressiveThreshold:1e4,progressiveChunkMode:"mod",animationEasing:"linear",animationDuration:300},e}(Z);function cu(t){t&&tt(t.series)&&p(t.series,(function(t){M(t)&&"k"===t.type&&(t.type="candlestick")}))}bt(hu,Fl,!0);var pu=["itemStyle","borderColor"],fu=["itemStyle","borderColor0"],gu=["itemStyle","borderColorDoji"],yu=["itemStyle","color"],vu=["itemStyle","color0"],mu={seriesType:"candlestick",plan:gn(),performRawSeries:!0,reset:function(t,e){function n(t,e){return e.get(t>0?yu:vu)}function i(t,e){return e.get(0===t?gu:t>0?pu:fu)}if(!e.isSeriesFiltered(t))return!t.pipelineContext.large&&{progress:function(t,e){for(var o;null!=(o=t.next());){var a=e.getItemModel(o),r=e.getItemLayout(o).sign,s=a.getItemStyle();s.fill=n(r,a),s.stroke=i(r,a)||s.fill;var l=e.ensureUniqueItemVisual(o,"style");F(l,s)}}}}},xu={seriesType:"candlestick",plan:gn(),reset:function(t){var e=t.coordinateSystem,n=t.getData(),i=function(t,e){var n,i=t.getBaseAxis(),o="category"===i.type?i.getBandWidth():(n=i.getExtent(),Math.abs(n[1]-n[0])/e.count()),a=s(x(t.get("barMaxWidth"),o),o),r=s(x(t.get("barMinWidth"),1),o),l=t.get("barWidth");return null!=l?s(l,o):Math.max(Math.min(o/2,a),r)}(t,n),o=["x","y"],a=n.getDimensionIndex(n.mapDimension(o[0])),r=ht(n.mapDimensionsAll(o[1]),n.getDimensionIndex,n),l=r[0],u=r[1],d=r[2],h=r[3];if(n.setLayout({candleWidth:i,isSimpleBox:i<=1.3}),!(a<0||r.length<4))return{progress:t.pipelineContext.large?function(n,i){var o,r,s=vn(4*n.count),c=0,p=[],f=[],g=i.getStore(),y=!!t.get(["itemStyle","borderColorDoji"]);for(;null!=(r=n.next());){var v=g.get(a,r),m=g.get(l,r),x=g.get(u,r),_=g.get(d,r),b=g.get(h,r);isNaN(v)||isNaN(_)||isNaN(b)?(s[c++]=NaN,c+=3):(s[c++]=_u(g,r,m,x,u,y),p[0]=v,p[1]=_,o=e.dataToPoint(p,null,f),s[c++]=o?o[0]:NaN,s[c++]=o?o[1]:NaN,p[1]=b,o=e.dataToPoint(p,null,f),s[c++]=o?o[1]:NaN)}i.setLayout("largePoints",s)}:function(t,n){var o,r=n.getStore();for(;null!=(o=t.next());){var s=r.get(a,o),c=r.get(l,o),p=r.get(u,o),f=r.get(d,o),g=r.get(h,o),y=Math.min(c,p),v=Math.max(c,p),m=w(y,s),x=w(v,s),_=w(f,s),b=w(g,s),S=[];M(S,x,0),M(S,m,1),S.push(D(b),D(x),D(_),D(m));var I=!!n.getItemModel(o).get(["itemStyle","borderColorDoji"]);n.setItemLayout(o,{sign:_u(r,o,c,p,u,I),initBaseline:c>p?x[1]:m[1],ends:S,brushRect:L(f,g,s)})}function w(t,n){var i=[];return i[0]=n,i[1]=t,isNaN(n)||isNaN(t)?[NaN,NaN]:e.dataToPoint(i)}function M(t,e,n){var o=e.slice(),a=e.slice();o[0]=yn(o[0]+i/2,1,!1),a[0]=yn(a[0]-i/2,1,!0),n?t.push(o,a):t.push(a,o)}function L(t,e,n){var o=w(t,n),a=w(e,n);return o[0]-=i/2,a[0]-=i/2,{x:o[0],y:o[1],width:i,height:a[1]-o[1]}}function D(t){return t[0]=yn(t[0],1),t}}}}};function _u(t,e,n,i,o,a){return n>i?-1:n<i?1:a?0:e>0?t.get(o,e-1)<=i?1:-1:1}function bu(t,e){var n=e.rippleEffectColor||e.color;t.eachChild((function(t){t.attr({z:e.z,zlevel:e.zlevel,style:{stroke:"stroke"===e.brushType?n:null,fill:"fill"===e.brushType?n:null}})}))}var Su=function(t){function e(e,n){var i=t.call(this)||this,o=new Ft(e,n),a=new Y;return i.add(o),i.add(a),i.updateData(e,n),i}return w(e,t),e.prototype.stopEffectAnimation=function(){this.childAt(1).removeAll()},e.prototype.startEffectAnimation=function(t){for(var e=t.symbolType,n=t.color,i=t.rippleNumber,o=this.childAt(1),a=0;a<i;a++){var r=U(e,-1,-1,2,2,n);r.attr({style:{strokeNoScale:!0},z2:99,silent:!0,scaleX:.5,scaleY:.5});var s=-a/i*t.period+t.effectOffset;r.animate("",!0).when(t.period,{scaleX:t.rippleScale/2,scaleY:t.rippleScale/2}).delay(s).start(),r.animateStyle(!0).when(t.period,{opacity:0}).delay(s).start(),o.add(r)}bu(o,t)},e.prototype.updateEffectAnimation=function(t){for(var e=this._effectCfg,n=this.childAt(1),i=["symbolType","period","rippleScale","rippleNumber"],o=0;o<i.length;o++){var a=i[o];if(e[a]!==t[a])return this.stopEffectAnimation(),void this.startEffectAnimation(t)}bu(n,t)},e.prototype.highlight=function(){We(this)},e.prototype.downplay=function(){Ze(this)},e.prototype.getSymbolType=function(){var t=this.childAt(0);return t&&t.getSymbolType()},e.prototype.updateData=function(t,e){var n=this,i=t.hostModel;this.childAt(0).updateData(t,e);var o=this.childAt(1),a=t.getItemModel(e),r=t.getItemVisual(e,"symbol"),s=pt(t.getItemVisual(e,"symbolSize")),l=t.getItemVisual(e,"style"),u=l&&l.fill,d=a.getModel("emphasis");o.setScale(s),o.traverse((function(t){t.setStyle("fill",u)}));var h=Ye(t.getItemVisual(e,"symbolOffset"),s);h&&(o.x=h[0],o.y=h[1]);var c=t.getItemVisual(e,"symbolRotate");o.rotation=(c||0)*Math.PI/180||0;var p={};p.showEffectOn=i.get("showEffectOn"),p.rippleScale=a.get(["rippleEffect","scale"]),p.brushType=a.get(["rippleEffect","brushType"]),p.period=1e3*a.get(["rippleEffect","period"]),p.effectOffset=e/t.count(),p.z=i.getShallow("z")||0,p.zlevel=i.getShallow("zlevel")||0,p.symbolType=r,p.color=u,p.rippleEffectColor=a.get(["rippleEffect","color"]),p.rippleNumber=a.get(["rippleEffect","number"]),"render"===p.showEffectOn?(this._effectCfg?this.updateEffectAnimation(p):this.startEffectAnimation(p),this._effectCfg=p):(this._effectCfg=null,this.stopEffectAnimation(),this.onHoverStateChange=function(t){"emphasis"===t?"render"!==p.showEffectOn&&n.startEffectAnimation(p):"normal"===t&&"render"!==p.showEffectOn&&n.stopEffectAnimation()}),this._effectCfg=p,dt(this,d.get("focus"),d.get("blurScope"),d.get("disabled"))},e.prototype.fadeOut=function(t){t&&t()},e}(Y),Iu=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.init=function(){this._symbolDraw=new q(Su)},e.prototype.render=function(t,e,n){var i=t.getData(),o=this._symbolDraw;o.updateData(i,{clipShape:this._getClipShape(t)}),this.group.add(o.group)},e.prototype._getClipShape=function(t){var e=t.coordinateSystem,n=e&&e.getArea&&e.getArea();return t.get("clip",!0)?n:null},e.prototype.updateTransform=function(t,e,n){var i=t.getData();this.group.dirty();var o=j("").reset(t,e,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout()},e.prototype._updateGroupTransform=function(t){var e=t.coordinateSystem;e&&e.getRoamTransform&&(this.group.transform=mn(e.getRoamTransform()),this.group.decomposeTransform())},e.prototype.remove=function(t,e){this._symbolDraw&&this._symbolDraw.remove(!0)},e.type="effectScatter",e}(K),wu=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.hasSymbolVisual=!0,n}return w(e,t),e.prototype.getInitialData=function(t,e){return W(null,this,{useEncodeDefaulter:!0})},e.prototype.brushSelector=function(t,e,n){return n.point(e.getItemLayout(t))},e.type="series.effectScatter",e.dependencies=["grid","polar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,effectType:"ripple",progressive:0,showEffectOn:"render",clip:!0,rippleEffect:{period:4,scale:2.5,brushType:"fill",number:3},universalTransition:{divideShape:"clone"},symbolSize:10},e}(Z);var Mu=function(t){function e(e,n,i){var o=t.call(this)||this;return o.add(o.createLine(e,n,i)),o._updateEffectSymbol(e,n),o}return w(e,t),e.prototype.createLine=function(t,e,n){return new Hs(t,e,n)},e.prototype._updateEffectSymbol=function(t,e){var n=t.getItemModel(e).getModel("effect"),i=n.get("symbolSize"),o=n.get("symbol");tt(i)||(i=[i,i]);var a=t.getItemVisual(e,"style"),r=n.get("color")||a&&a.stroke,s=this.childAt(1);this._symbolType!==o&&(this.remove(s),(s=U(o,-.5,-.5,1,1,r)).z2=100,s.culling=!0,this.add(s)),s&&(s.setStyle("shadowColor",r),s.setStyle(n.getItemStyle(["color"])),s.scaleX=i[0],s.scaleY=i[1],s.setColor(r),this._symbolType=o,this._symbolScale=i,this._updateEffectAnimation(t,n,e))},e.prototype._updateEffectAnimation=function(t,e,n){var i=this.childAt(1);if(i){var a=t.getItemLayout(n),r=1e3*e.get("period"),s=e.get("loop"),l=e.get("roundTrip"),u=e.get("constantSpeed"),d=Ce(e.get("delay"),(function(e){return e/t.count()*r/3}));if(i.ignore=!0,this._updateAnimationPoints(i,a),u>0&&(r=this._getLineLength(i)/u*1e3),r!==this._period||s!==this._loop||l!==this._roundTrip){i.stopAnimation();var h=void 0;h=o(d)?d(n):d,i.__t>0&&(h=-r*i.__t),this._animateSymbol(i,r,h,s,l)}this._period=r,this._loop=s,this._roundTrip=l}},e.prototype._animateSymbol=function(t,e,n,i,o){if(e>0){t.__t=0;var a=this,r=t.animate("",i).when(o?2*e:e,{__t:o?2:1}).delay(n).during((function(){a._updateSymbolPosition(t)}));i||r.done((function(){a.remove(t)})),r.start()}},e.prototype._getLineLength=function(t){return xn(t.__p1,t.__cp1)+xn(t.__cp1,t.__p2)},e.prototype._updateAnimationPoints=function(t,e){t.__p1=e[0],t.__p2=e[1],t.__cp1=e[2]||[(e[0][0]+e[1][0])/2,(e[0][1]+e[1][1])/2]},e.prototype.updateData=function(t,e,n){this.childAt(0).updateData(t,e,n),this._updateEffectSymbol(t,e)},e.prototype._updateSymbolPosition=function(t){var e=t.__p1,n=t.__p2,i=t.__cp1,o=t.__t<1?t.__t:2-t.__t,a=[t.x,t.y],r=a.slice(),s=je,l=_n;a[0]=s(e[0],i[0],n[0],o),a[1]=s(e[1],i[1],n[1],o);var u=t.__t<1?l(e[0],i[0],n[0],o):l(n[0],i[0],e[0],1-o),d=t.__t<1?l(e[1],i[1],n[1],o):l(n[1],i[1],e[1],1-o);t.rotation=-Math.atan2(d,u)-Math.PI/2,"line"!==this._symbolType&&"rect"!==this._symbolType&&"roundRect"!==this._symbolType||(void 0!==t.__lastT&&t.__lastT<t.__t?(t.scaleY=1.05*xn(r,a),1===o&&(a[0]=r[0]+(a[0]-r[0])/2,a[1]=r[1]+(a[1]-r[1])/2)):1===t.__lastT?t.scaleY=2*xn(e,a):t.scaleY=this._symbolScale[1]),t.__lastT=t.__t,t.ignore=!1,t.x=a[0],t.y=a[1]},e.prototype.updateLayout=function(t,e){this.childAt(0).updateLayout(t,e);var n=t.getItemModel(e).getModel("effect");this._updateEffectAnimation(t,n,e)},e}(Y),Lu=function(t){function e(e,n,i){var o=t.call(this)||this;return o._createPolyline(e,n,i),o}return w(e,t),e.prototype._createPolyline=function(t,e,n){var i=t.getItemLayout(e),o=new nt({shape:{points:i}});this.add(o),this._updateCommonStl(t,e,n)},e.prototype.updateData=function(t,e,n){var i=t.hostModel,o=this.childAt(0),a={shape:{points:t.getItemLayout(e)}};S(o,a,i,e),this._updateCommonStl(t,e,n)},e.prototype._updateCommonStl=function(t,e,n){var i=this.childAt(0),o=t.getItemModel(e),a=n&&n.emphasisLineStyle,r=n&&n.focus,s=n&&n.blurScope,l=n&&n.emphasisDisabled;if(!n||t.hasItemOption){var u=o.getModel("emphasis");a=u.getModel("lineStyle").getLineStyle(),l=u.get("disabled"),r=u.get("focus"),s=u.get("blurScope")}i.useStyle(t.getItemVisual(e,"style")),i.style.fill=null,i.style.strokeNoScale=!0,i.ensureState("emphasis").style=a,dt(this,r,s,l)},e.prototype.updateLayout=function(t,e){this.childAt(0).setShape("points",t.getItemLayout(e))},e}(Y),Du=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._lastFrame=0,e._lastFramePercent=0,e}return w(e,t),e.prototype.createLine=function(t,e,n){return new Lu(t,e,n)},e.prototype._updateAnimationPoints=function(t,e){this._points=e;for(var n=[0],i=0,o=1;o<e.length;o++){var a=e[o-1],r=e[o];i+=xn(a,r),n.push(i)}if(0!==i){for(o=0;o<n.length;o++)n[o]/=i;this._offsets=n,this._length=i}else this._length=0},e.prototype._getLineLength=function(){return this._length},e.prototype._updateSymbolPosition=function(t){var e=t.__t<1?t.__t:2-t.__t,n=this._points,i=this._offsets,o=n.length;if(i){var a,r=this._lastFrame;if(e<this._lastFramePercent){for(a=Math.min(r+1,o-1);a>=0&&!(i[a]<=e);a--);a=Math.min(a,o-2)}else{for(a=r;a<o&&!(i[a]>e);a++);a=Math.min(a-1,o-2)}var s=(e-i[a])/(i[a+1]-i[a]),l=n[a],u=n[a+1];t.x=l[0]*(1-s)+s*u[0],t.y=l[1]*(1-s)+s*u[1];var d=t.__t<1?u[0]-l[0]:l[0]-u[0],h=t.__t<1?u[1]-l[1]:l[1]-u[1];t.rotation=-Math.atan2(h,d)-Math.PI/2,this._lastFrame=a,this._lastFramePercent=e,t.ignore=!1}},e}(Mu),Cu=function(){return function(){this.polyline=!1,this.curveness=0,this.segs=[]}}(),Tu=function(t){function n(e){var n=t.call(this,e)||this;return n._off=0,n.hoverDataIdx=-1,n}return w(n,t),n.prototype.reset=function(){this.notClear=!1,this._off=0},n.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},n.prototype.getDefaultShape=function(){return new Cu},n.prototype.buildPath=function(t,e){var n,i=e.segs,o=e.curveness;if(e.polyline)for(n=this._off;n<i.length;){var a=i[n++];if(a>0){t.moveTo(i[n++],i[n++]);for(var r=1;r<a;r++)t.lineTo(i[n++],i[n++])}}else for(n=this._off;n<i.length;){var s=i[n++],l=i[n++],u=i[n++],d=i[n++];if(t.moveTo(s,l),o>0){var h=(s+u)/2-(l-d)*o,c=(l+d)/2-(u-s)*o;t.quadraticCurveTo(h,c,u,d)}else t.lineTo(u,d)}this.incremental&&(this._off=n,this.notClear=!0)},n.prototype.findDataIndex=function(t,e){var n=this.shape,i=n.segs,o=n.curveness,a=this.style.lineWidth;if(n.polyline)for(var r=0,s=0;s<i.length;){var l=i[s++];if(l>0)for(var u=i[s++],d=i[s++],h=1;h<l;h++){var c=i[s++],p=i[s++];if(bn(u,d,c,p,a,t,e))return r}r++}else for(r=0,s=0;s<i.length;){u=i[s++],d=i[s++],c=i[s++],p=i[s++];if(o>0){if(Sn(u,d,(u+c)/2-(d-p)*o,(d+p)/2-(c-u)*o,c,p,a,t,e))return r}else if(bn(u,d,c,p,a,t,e))return r;r++}return-1},n.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return t=n[0],e=n[1],i.contain(t,e)?(this.hoverDataIdx=this.findDataIndex(t,e))>=0:(this.hoverDataIdx=-1,!1)},n.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var n=this.shape.segs,i=1/0,o=1/0,a=-1/0,r=-1/0,s=0;s<n.length;){var l=n[s++],u=n[s++];i=Math.min(l,i),a=Math.max(l,a),o=Math.min(u,o),r=Math.max(u,r)}t=this._rect=new e(i,o,a,r)}return t},n}(X),Au=function(){function t(){this.group=new Y}return t.prototype.updateData=function(t){this._clear();var e=this._create();e.setShape({segs:t.getLayout("linesPoints")}),this._setCommon(e,t)},t.prototype.incrementalPrepareUpdate=function(t){this.group.removeAll(),this._clear()},t.prototype.incrementalUpdate=function(t,e){var n=this._newAdded[0],i=e.getLayout("linesPoints"),o=n&&n.shape.segs;if(o&&o.length<2e4){var a=o.length,r=new Float32Array(a+i.length);r.set(o),r.set(i,a),n.setShape({segs:r})}else{this._newAdded=[];var s=this._create();s.incremental=!0,s.setShape({segs:i}),this._setCommon(s,e),s.__startIndex=t.start}},t.prototype.remove=function(){this._clear()},t.prototype.eachRendered=function(t){this._newAdded[0]&&t(this._newAdded[0])},t.prototype._create=function(){var t=new Tu({cursor:"default",ignoreCoarsePointer:!0});return this._newAdded.push(t),this.group.add(t),t},t.prototype._setCommon=function(t,e,n){var i=e.hostModel;t.setShape({polyline:i.get("polyline"),curveness:i.get(["lineStyle","curveness"])}),t.useStyle(i.getModel("lineStyle").getLineStyle()),t.style.strokeNoScale=!0;var o=e.getVisual("style");o&&o.stroke&&t.setStyle("stroke",o.stroke),t.setStyle("fill",null);var a=r(t);a.seriesIndex=i.seriesIndex,t.on("mousemove",(function(e){a.dataIndex=null;var n=t.hoverDataIdx;n>0&&(a.dataIndex=n+t.__startIndex)}))},t.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},t}(),Pu={seriesType:"lines",plan:gn(),reset:function(t){var e=t.coordinateSystem;if(e){var n=t.get("polyline"),i=t.pipelineContext.large;return{progress:function(o,a){var r=[];if(i){var s=void 0,l=o.end-o.start;if(n){for(var u=0,d=o.start;d<o.end;d++)u+=t.getLineCoordsCount(d);s=new Float32Array(l+2*u)}else s=new Float32Array(4*l);var h=0,c=[];for(d=o.start;d<o.end;d++){var p=t.getLineCoords(d,r);n&&(s[h++]=p);for(var f=0;f<p;f++)c=e.dataToPoint(r[f],!1,c),s[h++]=c[0],s[h++]=c[1]}a.setLayout("linesPoints",s)}else for(d=o.start;d<o.end;d++){var g=a.getItemModel(d),y=(p=t.getLineCoords(d,r),[]);if(n)for(var v=0;v<p;v++)y.push(e.dataToPoint(r[v]));else{y[0]=e.dataToPoint(r[0]),y[1]=e.dataToPoint(r[1]);var m=g.get(["lineStyle","curveness"]);+m&&(y[2]=[(y[0][0]+y[1][0])/2-(y[0][1]-y[1][1])*m,(y[0][1]+y[1][1])/2-(y[1][0]-y[0][0])*m])}a.setItemLayout(d,y)}}}}}},ku=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n){var i=t.getData(),o=this._updateLineDraw(i,t),a=t.get("zlevel"),r=t.get(["effect","trailLength"]),s=n.getZr(),l="svg"===s.painter.getType();l||s.painter.getLayer(a).clear(!0),null==this._lastZlevel||l||s.configLayer(this._lastZlevel,{motionBlur:!1}),this._showEffect(t)&&r>0&&(l||s.configLayer(a,{motionBlur:!0,lastFrameAlpha:Math.max(Math.min(r/10+.9,1),0)})),o.updateData(i);var u=t.get("clip",!0)&&fn(t.coordinateSystem,!1,t);u?this.group.setClipPath(u):this.group.removeClipPath(),this._lastZlevel=a,this._finished=!0},e.prototype.incrementalPrepareRender=function(t,e,n){var i=t.getData();this._updateLineDraw(i,t).incrementalPrepareUpdate(i),this._clearLayer(n),this._finished=!1},e.prototype.incrementalRender=function(t,e,n){this._lineDraw.incrementalUpdate(t,e.getData()),this._finished=t.end===e.getData().count()},e.prototype.eachRendered=function(t){this._lineDraw&&this._lineDraw.eachRendered(t)},e.prototype.updateTransform=function(t,e,n){var i=t.getData(),o=t.pipelineContext;if(!this._finished||o.large||o.progressiveRender)return{update:!0};var a=Pu.reset(t,e,n);a.progress&&a.progress({start:0,end:i.count(),count:i.count()},i),this._lineDraw.updateLayout(),this._clearLayer(n)},e.prototype._updateLineDraw=function(t,e){var n=this._lineDraw,i=this._showEffect(e),o=!!e.get("polyline"),a=e.pipelineContext.large;return n&&i===this._hasEffet&&o===this._isPolyline&&a===this._isLargeDraw||(n&&n.remove(),n=this._lineDraw=a?new Au:new Fs(o?i?Du:Lu:i?Mu:Hs),this._hasEffet=i,this._isPolyline=o,this._isLargeDraw=a),this.group.add(n.group),n},e.prototype._showEffect=function(t){return!!t.get(["effect","show"])},e.prototype._clearLayer=function(t){var e=t.getZr();"svg"===e.painter.getType()||null==this._lastZlevel||e.painter.getLayer(this._lastZlevel).clear(!0)},e.prototype.remove=function(t,e){this._lineDraw&&this._lineDraw.remove(),this._lineDraw=null,this._clearLayer(e)},e.prototype.dispose=function(t,e){this.remove(t,e)},e.type="lines",e}(K),Nu="undefined"==typeof Uint32Array?Array:Uint32Array,Vu="undefined"==typeof Float64Array?Array:Float64Array;function Ru(t){var e=t.data;e&&e[0]&&e[0][0]&&e[0][0].coord&&(t.data=ht(e,(function(t){var e={coords:[t[0].coord,t[1].coord]};return t[0].name&&(e.fromName=t[0].name),t[1].name&&(e.toName=t[1].name),In([e,t[0],t[1]])})))}var zu=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.visualStyleAccessPath="lineStyle",n.visualDrawType="stroke",n}return w(e,t),e.prototype.init=function(e){e.data=e.data||[],Ru(e);var n=this._processFlatCoordsArray(e.data);this._flatCoords=n.flatCoords,this._flatCoordsOffset=n.flatCoordsOffset,n.flatCoords&&(e.data=new Float32Array(n.count)),t.prototype.init.apply(this,arguments)},e.prototype.mergeOption=function(e){if(Ru(e),e.data){var n=this._processFlatCoordsArray(e.data);this._flatCoords=n.flatCoords,this._flatCoordsOffset=n.flatCoordsOffset,n.flatCoords&&(e.data=new Float32Array(n.count))}t.prototype.mergeOption.apply(this,arguments)},e.prototype.appendData=function(t){var e=this._processFlatCoordsArray(t.data);e.flatCoords&&(this._flatCoords?(this._flatCoords=Wt(this._flatCoords,e.flatCoords),this._flatCoordsOffset=Wt(this._flatCoordsOffset,e.flatCoordsOffset)):(this._flatCoords=e.flatCoords,this._flatCoordsOffset=e.flatCoordsOffset),t.data=new Float32Array(e.count)),this.getRawData().appendData(t.data)},e.prototype._getCoordsFromItemModel=function(t){var e=this.getData().getItemModel(t);return e.option instanceof Array?e.option:e.getShallow("coords")},e.prototype.getLineCoordsCount=function(t){return this._flatCoordsOffset?this._flatCoordsOffset[2*t+1]:this._getCoordsFromItemModel(t).length},e.prototype.getLineCoords=function(t,e){if(this._flatCoordsOffset){for(var n=this._flatCoordsOffset[2*t],i=this._flatCoordsOffset[2*t+1],o=0;o<i;o++)e[o]=e[o]||[],e[o][0]=this._flatCoords[n+2*o],e[o][1]=this._flatCoords[n+2*o+1];return i}var a=this._getCoordsFromItemModel(t);for(o=0;o<a.length;o++)e[o]=e[o]||[],e[o][0]=a[o][0],e[o][1]=a[o][1];return a.length},e.prototype._processFlatCoordsArray=function(t){var e=0;if(this._flatCoords&&(e=this._flatCoords.length),Nt(t[0])){for(var n=t.length,i=new Nu(n),o=new Vu(n),a=0,r=0,s=0,l=0;l<n;){s++;var u=t[l++];i[r++]=a+e,i[r++]=u;for(var d=0;d<u;d++){var h=t[l++],c=t[l++];o[a++]=h,o[a++]=c}}return{flatCoordsOffset:new Uint32Array(i.buffer,0,r),flatCoords:o,count:s}}return{flatCoordsOffset:null,flatCoords:null,count:t.length}},e.prototype.getInitialData=function(t,e){var n=new Jt(["value"],this);return n.hasItemOption=!1,n.initData(t.data,[],(function(t,e,i,o){if(t instanceof Array)return NaN;n.hasItemOption=!0;var a=t.value;return null!=a?a instanceof Array?a[o]:a:void 0})),n},e.prototype.formatTooltip=function(t,e,n){var i=this.getData().getItemModel(t),o=i.get("name");if(o)return o;var a=i.get("fromName"),r=i.get("toName"),s=[];return null!=a&&s.push(a),null!=r&&s.push(r),mt("nameValue",{name:s.join(" > ")})},e.prototype.preventIncremental=function(){return!!this.get(["effect","show"])},e.prototype.getProgressive=function(){var t=this.option.progressive;return null==t?this.option.large?1e4:this.get("progressive"):t},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return null==t?this.option.large?2e4:this.get("progressiveThreshold"):t},e.prototype.getZLevelKey=function(){var t=this.getModel("effect"),e=t.get("trailLength");return this.getData().count()>this.getProgressiveThreshold()?this.id:t.get("show")&&e>0?e+"":""},e.type="series.lines",e.dependencies=["grid","polar","geo","calendar"],e.defaultOption={coordinateSystem:"geo",z:2,legendHoverLink:!0,xAxisIndex:0,yAxisIndex:0,symbol:["none","none"],symbolSize:[10,10],geoIndex:0,effect:{show:!1,period:4,constantSpeed:0,symbol:"circle",symbolSize:3,loop:!0,trailLength:.2},large:!1,largeThreshold:2e3,polyline:!1,clip:!0,label:{show:!1,position:"end"},lineStyle:{opacity:.5}},e}(Z);function Ou(t){return t instanceof Array||(t=[t,t]),t}var Eu={seriesType:"lines",reset:function(t){var e=Ou(t.get("symbol")),n=Ou(t.get("symbolSize")),i=t.getData();return i.setVisual("fromSymbol",e&&e[0]),i.setVisual("toSymbol",e&&e[1]),i.setVisual("fromSymbolSize",n&&n[0]),i.setVisual("toSymbolSize",n&&n[1]),{dataEach:i.hasItemOption?function(t,e){var n=t.getItemModel(e),i=Ou(n.getShallow("symbol",!0)),o=Ou(n.getShallow("symbolSize",!0));i[0]&&t.setItemVisual(e,"fromSymbol",i[0]),i[1]&&t.setItemVisual(e,"toSymbol",i[1]),o[0]&&t.setItemVisual(e,"fromSymbolSize",o[0]),o[1]&&t.setItemVisual(e,"toSymbolSize",o[1])}:null}}};var Bu=function(){function t(){this.blurSize=30,this.pointSize=20,this.maxOpacity=1,this.minOpacity=0,this._gradientPixels={inRange:null,outOfRange:null};var t=A.createCanvas();this.canvas=t}return t.prototype.update=function(t,e,n,i,o,a){var r=this._getBrush(),s=this._getGradient(o,"inRange"),l=this._getGradient(o,"outOfRange"),u=this.pointSize+this.blurSize,d=this.canvas,h=d.getContext("2d"),c=t.length;d.width=e,d.height=n;for(var p=0;p<c;++p){var f=t[p],g=f[0],y=f[1],v=i(f[2]);h.globalAlpha=v,h.drawImage(r,g-u,y-u)}if(!d.width||!d.height)return d;for(var m=h.getImageData(0,0,d.width,d.height),x=m.data,_=0,b=x.length,S=this.minOpacity,I=this.maxOpacity-S;_<b;){v=x[_+3]/256;var w=4*Math.floor(255*v);if(v>0){var M=a(v)?s:l;v>0&&(v=v*I+S),x[_++]=M[w],x[_++]=M[w+1],x[_++]=M[w+2],x[_++]=M[w+3]*v*256}else _+=4}return h.putImageData(m,0,0),d},t.prototype._getBrush=function(){var t=this._brushCanvas||(this._brushCanvas=A.createCanvas()),e=this.pointSize+this.blurSize,n=2*e;t.width=n,t.height=n;var i=t.getContext("2d");return i.clearRect(0,0,n,n),i.shadowOffsetX=n,i.shadowBlur=this.blurSize,i.shadowColor="#000",i.beginPath(),i.arc(-e,e,this.pointSize,0,2*Math.PI,!0),i.closePath(),i.fill(),t},t.prototype._getGradient=function(t,e){for(var n=this._gradientPixels,i=n[e]||(n[e]=new Uint8ClampedArray(1024)),o=[0,0,0,0],a=0,r=0;r<256;r++)t[e](r/255,!0,o),i[a++]=o[0],i[a++]=o[1],i[a++]=o[2],i[a++]=o[3];return i},t}();function Gu(t){var e=t.dimensions;return"lng"===e[0]&&"lat"===e[1]}var Hu=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n){var i;e.eachComponent("visualMap",(function(e){e.eachTargetSeries((function(n){n===t&&(i=e)}))})),this._progressiveEls=null,this.group.removeAll();var o=t.coordinateSystem;"cartesian2d"===o.type||"calendar"===o.type?this._renderOnCartesianAndCalendar(t,n,0,t.getData().count()):Gu(o)&&this._renderOnGeo(o,t,i,n)},e.prototype.incrementalPrepareRender=function(t,e,n){this.group.removeAll()},e.prototype.incrementalRender=function(t,e,n,i){var o=e.coordinateSystem;o&&(Gu(o)?this.render(e,n,i):(this._progressiveEls=[],this._renderOnCartesianAndCalendar(e,i,t.start,t.end,!0)))},e.prototype.eachRendered=function(t){Xe(this._progressiveEls||this.group,t)},e.prototype._renderOnCartesianAndCalendar=function(t,e,n,i,o){var a,r,s,l,u=t.coordinateSystem,d=wn(u,"cartesian2d");if(d){var h=u.getAxis("x"),c=u.getAxis("y");a=h.getBandWidth()+.5,r=c.getBandWidth()+.5,s=h.scale.getExtent(),l=c.scale.getExtent()}for(var p=this.group,f=t.getData(),g=t.getModel(["emphasis","itemStyle"]).getItemStyle(),y=t.getModel(["blur","itemStyle"]).getItemStyle(),v=t.getModel(["select","itemStyle"]).getItemStyle(),m=t.get(["itemStyle","borderRadius"]),x=ut(t),_=t.getModel("emphasis"),b=_.get("focus"),S=_.get("blurScope"),I=_.get("disabled"),w=d?[f.mapDimension("x"),f.mapDimension("y"),f.mapDimension("value")]:[f.mapDimension("time"),f.mapDimension("value")],M=n;M<i;M++){var L=void 0,D=f.getItemVisual(M,"style");if(d){var C=f.get(w[0],M),T=f.get(w[1],M);if(isNaN(f.get(w[2],M))||isNaN(C)||isNaN(T)||C<s[0]||C>s[1]||T<l[0]||T>l[1])continue;var A=u.dataToPoint([C,T]);L=new be({shape:{x:A[0]-a/2,y:A[1]-r/2,width:a,height:r},style:D})}else{if(isNaN(f.get(w[1],M)))continue;L=new be({z2:1,shape:u.dataToRect([f.get(w[0],M)]).contentShape,style:D})}if(f.hasItemOption){var P=f.getItemModel(M),k=P.getModel("emphasis");g=k.getModel("itemStyle").getItemStyle(),y=P.getModel(["blur","itemStyle"]).getItemStyle(),v=P.getModel(["select","itemStyle"]).getItemStyle(),m=P.get(["itemStyle","borderRadius"]),b=k.get("focus"),S=k.get("blurScope"),I=k.get("disabled"),x=ut(P)}L.shape.r=m;var N=t.getRawValue(M),V="-";N&&null!=N[2]&&(V=N[2]+""),lt(L,x,{labelFetcher:t,labelDataIndex:M,defaultOpacity:D.opacity,defaultText:V}),L.ensureState("emphasis").style=g,L.ensureState("blur").style=y,L.ensureState("select").style=v,dt(L,b,S,I),L.incremental=o,o&&(L.states.emphasis.hoverLayer=!0),p.add(L),f.setItemGraphicEl(M,L),this._progressiveEls&&this._progressiveEls.push(L)}},e.prototype._renderOnGeo=function(t,e,n,i){var o=n.targetVisuals.inRange,a=n.targetVisuals.outOfRange,r=e.getData(),s=this._hmLayer||this._hmLayer||new Bu;s.blurSize=e.get("blurSize"),s.pointSize=e.get("pointSize"),s.minOpacity=e.get("minOpacity"),s.maxOpacity=e.get("maxOpacity");var l=t.getViewRect().clone(),u=t.getRoamTransform();l.applyTransform(u);var d=Math.max(l.x,0),h=Math.max(l.y,0),c=Math.min(l.width+l.x,i.getWidth()),p=Math.min(l.height+l.y,i.getHeight()),f=c-d,g=p-h,y=[r.mapDimension("lng"),r.mapDimension("lat"),r.mapDimension("value")],v=r.mapArray(y,(function(e,n,i){var o=t.dataToPoint([e,n]);return o[0]-=d,o[1]-=h,o.push(i),o})),m=n.getExtent(),x="visualMap.continuous"===n.type?function(t,e){var n=t[1]-t[0];return e=[(e[0]-t[0])/n,(e[1]-t[0])/n],function(t){return t>=e[0]&&t<=e[1]}}(m,n.option.range):function(t,e,n){var i=t[1]-t[0],o=(e=ht(e,(function(e){return{interval:[(e.interval[0]-t[0])/i,(e.interval[1]-t[0])/i]}}))).length,a=0;return function(t){var i;for(i=a;i<o;i++)if((r=e[i].interval)[0]<=t&&t<=r[1]){a=i;break}if(i===o)for(i=a-1;i>=0;i--){var r;if((r=e[i].interval)[0]<=t&&t<=r[1]){a=i;break}}return i>=0&&i<o&&n[i]}}(m,n.getPieceList(),n.option.selected);s.update(v,f,g,o.color.getNormalizer(),{inRange:o.color.getColorMapper(),outOfRange:a.color.getColorMapper()},x);var _=new rt({style:{width:f,height:g,x:d,y:h,image:s.canvas},silent:!0});this.group.add(_)},e.type="heatmap",e}(K),Fu=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.getInitialData=function(t,e){return W(null,this,{generateCoord:"value"})},e.prototype.preventIncremental=function(){var t=Ke.get(this.get("coordinateSystem"));if(t&&t.dimensions)return"lng"===t.dimensions[0]&&"lat"===t.dimensions[1]},e.type="series.heatmap",e.dependencies=["grid","geo","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,geoIndex:0,blurSize:30,pointSize:20,maxOpacity:1,minOpacity:0,select:{itemStyle:{borderColor:"#212121"}}},e}(Z);var Wu=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n._layers=[],n}return w(e,t),e.prototype.render=function(t,e,n){var i=t.getData(),o=this,a=this.group,r=t.getLayerSeries(),s=i.getLayout("layoutInfo"),l=s.rect,u=s.boundaryGap;function d(t){return t.name}a.x=0,a.y=l.y+u[0];var h=new de(this._layersSeries||[],r,d,d),c=[];function p(e,n,s){var l=o._layers;if("remove"!==e){for(var u,d,h=[],p=[],f=r[n].indices,g=0;g<f.length;g++){var y=i.getItemLayout(f[g]),v=y.x,m=y.y0,x=y.y;h.push(v,m),p.push(v,m+x),u=i.getItemVisual(f[g],"style")}var b=i.getItemLayout(f[0]),I=t.getModel("label").get("margin"),w=t.getModel("emphasis");if("add"===e){var M=c[n]=new Y;d=new Mn({shape:{points:h,stackedOnPoints:p,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),M.add(d),a.add(M),t.isAnimationEnabled()&&d.setClipPath(function(t,e,n){var i=new be({shape:{x:t.x-10,y:t.y-10,width:0,height:t.height+20}});return _(i,{shape:{x:t.x-50,width:t.width+100,height:t.height+20}},e,n),i}(d.getBoundingRect(),t,(function(){d.removeClipPath()})))}else{M=l[s];d=M.childAt(0),a.add(M),c[n]=M,S(d,{shape:{points:h,stackedOnPoints:p}},t),it(d)}lt(d,ut(t),{labelDataIndex:f[g-1],defaultText:i.getName(f[g-1]),inheritColor:u.fill},{normal:{verticalAlign:"middle"}}),d.setTextConfig({position:null,local:!0});var L=d.getTextContent();L&&(L.x=b.x-I,L.y=b.y0+b.y/2),d.useStyle(u),i.setItemGraphicEl(n,d),at(d,t),dt(d,w.get("focus"),w.get("blurScope"),w.get("disabled"))}else a.remove(l[n])}h.add(gt(p,this,"add")).update(gt(p,this,"update")).remove(gt(p,this,"remove")).execute(),this._layersSeries=r,this._layers=c},e.type="themeRiver",e}(K);var Zu=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new ft(gt(this.getData,this),gt(this.getRawData,this))},e.prototype.fixData=function(t){var e=t.length,n={},i=sn(t,(function(t){return n.hasOwnProperty(t[0]+"")||(n[t[0]+""]=-1),t[2]})),o=[];i.buckets.each((function(t,e){o.push({name:e,dataList:t})}));for(var a=o.length,r=0;r<a;++r){for(var s=o[r].name,l=0;l<o[r].dataList.length;++l){var u=o[r].dataList[l][0]+"";n[u]=r}for(var u in n)n.hasOwnProperty(u)&&n[u]!==r&&(n[u]=r,t[e]=[u,0,s],e++)}return t},e.prototype.getInitialData=function(t,e){for(var n=this.getReferringComponents("singleAxis",Ln).models[0].get("type"),i=u(t.data,(function(t){return void 0!==t[2]})),o=this.fixData(i||[]),a=[],r=this.nameMap=ie(),s=0,l=0;l<o.length;++l)a.push(o[l][2]),r.get(o[l][2])||(r.set(o[l][2],s),s++);var d=Qt(o,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:ln(n)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}).dimensions,h=new Jt(d,this);return h.initData(o),h},e.prototype.getLayerSeries=function(){for(var t=this.getData(),e=t.count(),n=[],i=0;i<e;++i)n[i]=i;var o=t.mapDimension("single"),a=sn(n,(function(e){return t.get("name",e)})),r=[];return a.buckets.each((function(e,n){e.sort((function(e,n){return t.get(o,e)-t.get(o,n)})),r.push({name:n,indices:e})})),r},e.prototype.getAxisTooltipData=function(t,e,n){tt(t)||(t=t?[t]:[]);for(var i,o=this.getData(),a=this.getLayerSeries(),r=[],s=a.length,l=0;l<s;++l){for(var u=Number.MAX_VALUE,d=-1,h=a[l].indices.length,c=0;c<h;++c){var p=o.get(t[0],a[l].indices[c]),f=Math.abs(p-e);f<=u&&(i=p,u=f,d=a[l].indices[c])}r.push(d)}return{dataIndices:r,nestestValue:i}},e.prototype.formatTooltip=function(t,e,n){var i=this.getData(),o=i.getName(t),a=i.get(i.mapDimension("value"),t);return mt("nameValue",{name:o,value:a})},e.type="series.themeRiver",e.dependencies=["singleAxis"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",fontSize:11},emphasis:{label:{show:!0}}},e}(Z);function Yu(t,e){t.eachSeriesByType("themeRiver",(function(t){var e=t.getData(),n=t.coordinateSystem,i={},o=n.getRect();i.rect=o;var a=t.get("boundaryGap"),r=n.getAxis();(i.boundaryGap=a,"horizontal"===r.orient)?(a[0]=s(a[0],o.height),a[1]=s(a[1],o.height),Xu(e,t,o.height-a[0]-a[1])):(a[0]=s(a[0],o.width),a[1]=s(a[1],o.width),Xu(e,t,o.width-a[0]-a[1]));e.setLayout("layoutInfo",i)}))}function Xu(t,e,n){if(t.count())for(var i,o=e.coordinateSystem,a=e.getLayerSeries(),r=t.mapDimension("single"),s=t.mapDimension("value"),l=ht(a,(function(e){return ht(e.indices,(function(e){var n=o.dataToPoint(t.get(r,e));return n[1]=t.get(s,e),n}))})),u=function(t){for(var e=t.length,n=t[0].length,i=[],o=[],a=0,r=0;r<n;++r){for(var s=0,l=0;l<e;++l)s+=t[l][r][1];s>a&&(a=s),i.push(s)}for(var u=0;u<n;++u)o[u]=(a-i[u])/2;a=0;for(var d=0;d<n;++d){var h=i[d]+o[d];h>a&&(a=h)}return{y0:o,max:a}}(l),d=u.y0,h=n/u.max,c=a.length,p=a[0].indices.length,f=0;f<p;++f){i=d[f]*h,t.setItemLayout(a[0].indices[f],{layerIndex:0,x:l[0][f][0],y0:i,y:l[0][f][1]*h});for(var g=1;g<c;++g)i+=l[g-1][f][1]*h,t.setItemLayout(a[g].indices[f],{layerIndex:g,x:l[g][f][0],y0:i,y:l[g][f][1]*h})}}var Uu=function(t){function e(e,n,i,o){var a=t.call(this)||this;a.z2=2,a.textConfig={inside:!0},r(a).seriesIndex=n.seriesIndex;var s=new se({z2:4,silent:e.getModel().get(["label","silent"])});return a.setTextContent(s),a.updateData(!0,e,n,i,o),a}return w(e,t),e.prototype.updateData=function(t,e,n,i,o){this.node=e,e.piece=this,n=n||this._seriesModel,i=i||this._ecModel;var a=this;r(a).dataIndex=e.dataIndex;var s=e.getModel(),l=s.getModel("emphasis"),u=e.getLayout(),d=F({},u);d.label=null;var h=e.getVisual("style");h.lineJoin="bevel";var c=e.getVisual("decal");c&&(h.decal=Dn(c,o));var f=Cn(s.getModel("itemStyle"),d,!0);F(d,f),p(He,(function(t){var e=a.ensureState(t),n=s.getModel([t,"itemStyle"]);e.style=n.getItemStyle();var i=Cn(n,d);i&&(e.shape=i)})),t?(a.setShape(d),a.shape.r=u.r0,_(a,{shape:{r:u.r}},n,e.dataIndex)):(S(a,{shape:d},n),it(a)),a.useStyle(h),this._updateLabel(n);var g=s.getShallow("cursor");g&&a.attr("cursor",g),this._seriesModel=n||this._seriesModel,this._ecModel=i||this._ecModel;var y=l.get("focus"),v="ancestor"===y?e.getAncestorsIndices():"descendant"===y?e.getDescendantIndices():y;dt(this,v,l.get("blurScope"),l.get("disabled"))},e.prototype._updateLabel=function(t){var e=this,n=this.node.getModel(),o=n.getModel("label"),a=this.node.getLayout(),r=a.endAngle-a.startAngle,s=(a.startAngle+a.endAngle)/2,l=Math.cos(s),u=Math.sin(s),d=this,h=d.getTextContent(),c=this.node.dataIndex,f=o.get("minAngle")/180*Math.PI,g=o.get("show")&&!(null!=f&&Math.abs(r)<f);function y(t,e){var n=t.get(e);return null==n?o.get(e):n}h.ignore=!g,p(An,(function(o){var p="normal"===o?n.getModel("label"):n.getModel([o,"label"]),f="normal"===o,g=f?h:h.ensureState(o),v=t.getFormattedLabel(c,o);f&&(v=v||e.node.name),g.style=ue(p,{},null,"normal"!==o,!0),v&&(g.style.text=v);var m=p.get("show");null==m||f||(g.ignore=!m);var x,_=y(p,"position"),b=f?d:d.states[o],S=b.style.fill;b.textConfig={outsideFill:"inherit"===p.get("color")?S:null,inside:"outside"!==_};var I=y(p,"distance")||0,w=y(p,"align"),M=y(p,"rotate"),L=.5*Math.PI,D=1.5*Math.PI,C=i("tangential"===M?Math.PI/2-s:s),T=C>L&&!Tn(C-L)&&C<D;"outside"===_?(x=a.r+I,w=T?"right":"left"):w&&"center"!==w?"left"===w?(x=a.r0+I,w=T?"right":"left"):"right"===w&&(x=a.r-I,w=T?"left":"right"):(x=r===2*Math.PI&&0===a.r0?0:(a.r+a.r0)/2,w="center"),g.style.align=w,g.style.verticalAlign=y(p,"verticalAlign")||"middle",g.x=x*l+a.cx,g.y=x*u+a.cy;var A=0;"radial"===M?A=i(-s)+(T?Math.PI:0):"tangential"===M?A=i(Math.PI/2-s)+(T?Math.PI:0):Nt(M)&&(A=M*Math.PI/180),g.rotation=i(A)})),h.dirtyStyle()},e}(Pn),ju="sunburstRootToNode",qu="sunburstHighlight";var Ku=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n,i){var o=this;this.seriesModel=t,this.api=n,this.ecModel=e;var a=t.getData(),r=a.tree.root,s=t.getViewRoot(),l=this.group,u=t.get("renderLabelForZeroData"),d=[];s.eachNode((function(t){d.push(t)}));var h,c,p=this._oldChildren||[];!function(i,o){if(0===i.length&&0===o.length)return;function s(t){return t.getId()}function d(s,d){!function(i,o){u||!i||i.getValue()||(i=null);if(i!==r&&o!==r)if(o&&o.piece)i?(o.piece.updateData(!1,i,t,e,n),a.setItemGraphicEl(i.dataIndex,o.piece)):function(t){if(!t)return;t.piece&&(l.remove(t.piece),t.piece=null)}(o);else if(i){var s=new Uu(i,t,e,n);l.add(s),a.setItemGraphicEl(i.dataIndex,s)}}(null==s?null:i[s],null==d?null:o[d])}new de(o,i,s,s).add(d).update(d).remove(qt(d,null)).execute()}(d,p),h=r,(c=s).depth>0?(o.virtualPiece?o.virtualPiece.updateData(!1,h,t,e,n):(o.virtualPiece=new Uu(h,t,e,n),l.add(o.virtualPiece)),c.piece.off("click"),o.virtualPiece.on("click",(function(t){o._rootToNode(c.parentNode)}))):o.virtualPiece&&(l.remove(o.virtualPiece),o.virtualPiece=null),this._initEvents(),this._oldChildren=d},e.prototype._initEvents=function(){var t=this;this.group.off("click"),this.group.on("click",(function(e){var n=!1;t.seriesModel.getViewRoot().eachNode((function(i){if(!n&&i.piece&&i.piece===e.target){var o=i.getModel().get("nodeClick");if("rootToNode"===o)t._rootToNode(i);else if("link"===o){var a=i.getModel(),r=a.get("link");if(r){var s=a.get("target",!0)||"_blank";fe(r,s)}}n=!0}}))}))},e.prototype._rootToNode=function(t){t!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:ju,from:this.uid,seriesId:this.seriesModel.id,targetNode:t})},e.prototype.containPoint=function(t,e){var n=e.getData().getItemLayout(0);if(n){var i=t[0]-n.cx,o=t[1]-n.cy,a=Math.sqrt(i*i+o*o);return a<=n.r&&a>=n.r0}},e.type="sunburst",e}(K),$u=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.ignoreStyleOnData=!0,n}return w(e,t),e.prototype.getInitialData=function(t,e){var n={name:t.name,children:t.data};Qu(n);var i=this._levelModels=ht(t.levels||[],(function(t){return new _t(t,this,e)}),this),o=$a.createTree(n,this,(function(t){t.wrapMethod("getItemModel",(function(t,e){var n=o.getNodeByDataIndex(e),a=i[n.depth];return a&&(t.parentModel=a),t}))}));return o.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.getDataParams=function(e){var n=t.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(e);return n.treePathInfo=er(i,this),n},e.prototype.getLevelModel=function(t){return this._levelModels&&this._levelModels[t.depth]},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var e=this.getRawData().tree.root;t&&(t===e||e.contains(t))||(this._viewRoot=e)},e.prototype.enableAriaDecal=function(){sr(this)},e.type="series.sunburst",e.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},e}(Z);function Qu(t){var e=0;p(t.children,(function(t){Qu(t);var n=t.value;tt(n)&&(n=n[0]),e+=n}));var n=t.value;tt(n)&&(n=n[0]),(null==n||isNaN(n))&&(n=e),n<0&&(n=0),tt(t.value)?t.value[0]=n:t.value=n}var Ju=Math.PI/180;function td(t,e,n){e.eachSeriesByType(t,(function(t){var e=t.get("center"),i=t.get("radius");tt(i)||(i=[0,i]),tt(e)||(e=[e,e]);var o=n.getWidth(),a=n.getHeight(),r=Math.min(o,a),l=s(e[0],o),u=s(e[1],a),d=s(i[0],r/2),h=s(i[1],r/2),c=-t.get("startAngle")*Ju,f=t.get("minAngle")*Ju,g=t.getData().tree.root,y=t.getViewRoot(),v=y.depth,m=t.get("sort");null!=m&&ed(y,m);var x=0;p(y.children,(function(t){!isNaN(t.getValue())&&x++}));var _=y.getValue(),b=Math.PI/(_||x)*2,S=y.depth>0,I=y.height-(S?-1:1),w=(h-d)/(I||1),M=t.get("clockwise"),L=t.get("stillShowZeroSum"),D=M?1:-1,C=function(e,n){if(e){var i=n;if(e!==g){var o=e.getValue(),a=0===_&&L?b:o*b;a<f&&(a=f),i=n+D*a;var h=e.depth-v-(S?-1:1),c=d+w*h,y=d+w*(h+1),m=t.getLevelModel(e);if(m){var x=m.get("r0",!0),I=m.get("r",!0),T=m.get("radius",!0);null!=T&&(x=T[0],I=T[1]),null!=x&&(c=s(x,r/2)),null!=I&&(y=s(I,r/2))}e.setLayout({angle:a,startAngle:n,endAngle:i,clockwise:M,cx:l,cy:u,r0:c,r:y})}if(e.children&&e.children.length){var A=0;p(e.children,(function(t){A+=C(t,n+A)}))}return i-n}};if(S){var T=d,A=d+w,P=2*Math.PI;g.setLayout({angle:P,startAngle:c,endAngle:c+P,clockwise:M,cx:l,cy:u,r0:T,r:A})}C(y,c)}))}function ed(t,e){var n=t.children||[];t.children=function(t,e){if(o(e)){var n=ht(t,(function(t,e){var n=t.getValue();return{params:{depth:t.depth,height:t.height,dataIndex:t.dataIndex,getValue:function(){return n}},index:e}}));return n.sort((function(t,n){return e(t.params,n.params)})),ht(n,(function(e){return t[e.index]}))}var i="asc"===e;return t.sort((function(t,e){var n=(t.getValue()-e.getValue())*(i?1:-1);return 0===n?(t.dataIndex-e.dataIndex)*(i?-1:1):n}))}(n,e),n.length&&p(t.children,(function(t){ed(t,e)}))}function nd(t){var e={};t.eachSeriesByType("sunburst",(function(t){var n=t.getData(),i=n.tree;i.eachNode((function(o){var a=o.getModel().getModel("itemStyle").getItemStyle();a.fill||(a.fill=function(t,n,i){for(var o=t;o&&o.depth>1;)o=o.parentNode;var a=n.getColorFromPalette(o.name||o.dataIndex+"",e);return t.depth>1&&xt(a)&&(a=kn(a,(t.depth-1)/(i-1)*.5)),a}(o,t,i.root.height));var r=n.ensureUniqueItemVisual(o.dataIndex,"style");F(r,a)}))}))}var id={color:"fill",borderColor:"stroke"},od={symbol:1,symbolSize:1,symbolKeepAspect:1,legendIcon:1,visualMeta:1,liftZ:1,decal:1},ad=t(),rd=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.optionUpdated=function(){this.currentZLevel=this.get("zlevel",!0),this.currentZ=this.get("z",!0)},e.prototype.getInitialData=function(t,e){return W(null,this)},e.prototype.getDataParams=function(e,n,i){var o=t.prototype.getDataParams.call(this,e,n);return i&&(o.info=ad(i).info),o},e.type="series.custom",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,clip:!1},e}(Z);function sd(t,e){return e=e||[0,0],ht(["x","y"],(function(n,i){var o=this.getAxis(n),a=e[i],r=t[i]/2;return"category"===o.type?o.getBandWidth():Math.abs(o.dataToCoord(a-r)-o.dataToCoord(a+r))}),this)}function ld(t,e){return e=e||[0,0],ht([0,1],(function(n){var i=e[n],o=t[n]/2,a=[],r=[];return a[n]=i-o,r[n]=i+o,a[1-n]=r[1-n]=e[1-n],Math.abs(this.dataToPoint(a)[n]-this.dataToPoint(r)[n])}),this)}function ud(t,e){var n=this.getAxis(),i=e instanceof Array?e[0]:e,o=(t instanceof Array?t[0]:t)/2;return"category"===n.type?n.getBandWidth():Math.abs(n.dataToCoord(i-o)-n.dataToCoord(i+o))}function dd(t,e){return e=e||[0,0],ht(["Radius","Angle"],(function(n,i){var o=this["get"+n+"Axis"](),a=e[i],r=t[i]/2,s="category"===o.type?o.getBandWidth():Math.abs(o.dataToCoord(a-r)-o.dataToCoord(a+r));return"Angle"===n&&(s=s*Math.PI/180),s}),this)}function hd(t,e,n,i){return t&&(t.legacy||!1!==t.legacy&&!n&&!i&&"tspan"!==e&&("text"===e||Nn(t,"text")))}function cd(t,e,n){var i,o,a,r=t;if("text"===e)a=r;else{a={},Nn(r,"text")&&(a.text=r.text),Nn(r,"rich")&&(a.rich=r.rich),Nn(r,"textFill")&&(a.fill=r.textFill),Nn(r,"textStroke")&&(a.stroke=r.textStroke),Nn(r,"fontFamily")&&(a.fontFamily=r.fontFamily),Nn(r,"fontSize")&&(a.fontSize=r.fontSize),Nn(r,"fontStyle")&&(a.fontStyle=r.fontStyle),Nn(r,"fontWeight")&&(a.fontWeight=r.fontWeight),o={type:"text",style:a,silent:!0},i={};var s=Nn(r,"textPosition");n?i.position=s?r.textPosition:"inside":s&&(i.position=r.textPosition),Nn(r,"textPosition")&&(i.position=r.textPosition),Nn(r,"textOffset")&&(i.offset=r.textOffset),Nn(r,"textRotation")&&(i.rotation=r.textRotation),Nn(r,"textDistance")&&(i.distance=r.textDistance)}return pd(a,t),p(a.rich,(function(t){pd(t,t)})),{textConfig:i,textContent:o}}function pd(t,e){e&&(e.font=e.textFont||e.font,Nn(e,"textStrokeWidth")&&(t.lineWidth=e.textStrokeWidth),Nn(e,"textAlign")&&(t.align=e.textAlign),Nn(e,"textVerticalAlign")&&(t.verticalAlign=e.textVerticalAlign),Nn(e,"textLineHeight")&&(t.lineHeight=e.textLineHeight),Nn(e,"textWidth")&&(t.width=e.textWidth),Nn(e,"textHeight")&&(t.height=e.textHeight),Nn(e,"textBackgroundColor")&&(t.backgroundColor=e.textBackgroundColor),Nn(e,"textPadding")&&(t.padding=e.textPadding),Nn(e,"textBorderColor")&&(t.borderColor=e.textBorderColor),Nn(e,"textBorderWidth")&&(t.borderWidth=e.textBorderWidth),Nn(e,"textBorderRadius")&&(t.borderRadius=e.textBorderRadius),Nn(e,"textBoxShadowColor")&&(t.shadowColor=e.textBoxShadowColor),Nn(e,"textBoxShadowBlur")&&(t.shadowBlur=e.textBoxShadowBlur),Nn(e,"textBoxShadowOffsetX")&&(t.shadowOffsetX=e.textBoxShadowOffsetX),Nn(e,"textBoxShadowOffsetY")&&(t.shadowOffsetY=e.textBoxShadowOffsetY))}function fd(t,e,n){var i=t;i.textPosition=i.textPosition||n.position||"inside",null!=n.offset&&(i.textOffset=n.offset),null!=n.rotation&&(i.textRotation=n.rotation),null!=n.distance&&(i.textDistance=n.distance);var o=i.textPosition.indexOf("inside")>=0,a=t.fill||"#000";gd(i,e);var r=null==i.textFill;return o?r&&(i.textFill=n.insideFill||"#fff",!i.textStroke&&n.insideStroke&&(i.textStroke=n.insideStroke),!i.textStroke&&(i.textStroke=a),null==i.textStrokeWidth&&(i.textStrokeWidth=2)):(r&&(i.textFill=t.fill||n.outsideFill||"#000"),!i.textStroke&&n.outsideStroke&&(i.textStroke=n.outsideStroke)),i.text=e.text,i.rich=e.rich,p(e.rich,(function(t){gd(t,t)})),i}function gd(t,e){e&&(Nn(e,"fill")&&(t.textFill=e.fill),Nn(e,"stroke")&&(t.textStroke=e.fill),Nn(e,"lineWidth")&&(t.textStrokeWidth=e.lineWidth),Nn(e,"font")&&(t.font=e.font),Nn(e,"fontStyle")&&(t.fontStyle=e.fontStyle),Nn(e,"fontWeight")&&(t.fontWeight=e.fontWeight),Nn(e,"fontSize")&&(t.fontSize=e.fontSize),Nn(e,"fontFamily")&&(t.fontFamily=e.fontFamily),Nn(e,"align")&&(t.textAlign=e.align),Nn(e,"verticalAlign")&&(t.textVerticalAlign=e.verticalAlign),Nn(e,"lineHeight")&&(t.textLineHeight=e.lineHeight),Nn(e,"width")&&(t.textWidth=e.width),Nn(e,"height")&&(t.textHeight=e.height),Nn(e,"backgroundColor")&&(t.textBackgroundColor=e.backgroundColor),Nn(e,"padding")&&(t.textPadding=e.padding),Nn(e,"borderColor")&&(t.textBorderColor=e.borderColor),Nn(e,"borderWidth")&&(t.textBorderWidth=e.borderWidth),Nn(e,"borderRadius")&&(t.textBorderRadius=e.borderRadius),Nn(e,"shadowColor")&&(t.textBoxShadowColor=e.shadowColor),Nn(e,"shadowBlur")&&(t.textBoxShadowBlur=e.shadowBlur),Nn(e,"shadowOffsetX")&&(t.textBoxShadowOffsetX=e.shadowOffsetX),Nn(e,"shadowOffsetY")&&(t.textBoxShadowOffsetY=e.shadowOffsetY),Nn(e,"textShadowColor")&&(t.textShadowColor=e.textShadowColor),Nn(e,"textShadowBlur")&&(t.textShadowBlur=e.textShadowBlur),Nn(e,"textShadowOffsetX")&&(t.textShadowOffsetX=e.textShadowOffsetX),Nn(e,"textShadowOffsetY")&&(t.textShadowOffsetY=e.textShadowOffsetY))}var yd={position:["x","y"],scale:["scaleX","scaleY"],origin:["originX","originY"]},vd=a(yd);En(Rn,(function(t,e){return t[e]=1,t}),{}),Rn.join(", ");var md=["","style","shape","extra"],xd=t();function _d(t,e,n,i,o){var a=t+"Animation",r=Vn(t,i,o)||{},s=xd(e).userDuring;return r.duration>0&&(r.during=s?gt(Dd,{el:e,userDuring:s}):null,r.setToFinal=!0,r.scope=t),F(r,n[a]),r}function bd(t,e,n,i){var o=(i=i||{}).dataIndex,r=i.isInit,s=i.clearStyle,l=n.isAnimationEnabled(),u=xd(t),d=e.style;u.userDuring=e.during;var h={},c={};if(function(t,e,n){for(var i=0;i<vd.length;i++){var o=vd[i],a=yd[o],r=e[o];r&&(n[a[0]]=r[0],n[a[1]]=r[1])}for(i=0;i<Rn.length;i++){var s=Rn[i];null!=e[s]&&(n[s]=e[s])}}(0,e,c),Td("shape",e,c),Td("extra",e,c),!r&&l&&(function(t,e,n){for(var i=e.transition,o=wd(i)?Rn:oe(i||[]),a=0;a<o.length;a++){var r=o[a];if("style"!==r&&"shape"!==r&&"extra"!==r){var s=t[r];n[r]=s}}}(t,e,h),Cd("shape",t,e,h),Cd("extra",t,e,h),function(t,e,n,i){if(!n)return;var o,r=t.style;if(r){var s=n.transition,l=e.transition;if(s&&!wd(s)){var u=oe(s);!o&&(o=i.style={});for(var d=0;d<u.length;d++){var h=r[g=u[d]];o[g]=h}}else if(t.getAnimationStyleProps&&(wd(l)||wd(s)||b(l,"style")>=0)){var c=t.getAnimationStyleProps(),p=c?c.style:null;if(p){!o&&(o=i.style={});var f=a(n);for(d=0;d<f.length;d++){var g;if(p[g=f[d]]){h=r[g];o[g]=h}}}}}}(t,e,d,h)),c.style=d,function(t,e,n){var i=e.style;if(!t.isGroup&&i){if(n){t.useStyle({});for(var o=t.animators,a=0;a<o.length;a++){var r=o[a];"style"===r.targetName&&r.changeTarget(t.style)}}t.setStyle(i)}e&&(e.style=null,e&&t.attr(e),e.style=i)}(t,c,s),function(t,e){Nn(e,"silent")&&(t.silent=e.silent),Nn(e,"ignore")&&(t.ignore=e.ignore),t instanceof _e&&Nn(e,"invisible")&&(t.invisible=e.invisible);t instanceof X&&Nn(e,"autoBatch")&&(t.autoBatch=e.autoBatch)}(t,e),l)if(r){var f={};p(md,(function(t){var n=t?e[t]:e;n&&n.enterFrom&&(t&&(f[t]=f[t]||{}),F(t?f[t]:f,n.enterFrom))}));var g=_d("enter",t,e,n,o);g.duration>0&&t.animateFrom(f,g)}else!function(t,e,n,i,o){if(o){var a=_d("update",t,e,i,n);a.duration>0&&t.animateFrom(o,a)}}(t,e,o||0,n,h);Sd(t,e),d?t.dirty():t.markRedraw()}function Sd(t,e){for(var n=xd(t).leaveToProps,i=0;i<md.length;i++){var o=md[i],a=o?e[o]:e;a&&a.leaveTo&&(n||(n=xd(t).leaveToProps={}),o&&(n[o]=n[o]||{}),F(o?n[o]:n,a.leaveTo))}}function Id(t,e,n,i){if(t){var o=t.parent,a=xd(t).leaveToProps;if(a){var r=_d("update",t,e,n,0);r.done=function(){o.remove(t)},t.animateTo(a,r)}else o.remove(t)}}function wd(t){return"all"===t}var Md={},Ld={setTransform:function(t,e){return Md.el[t]=e,this},getTransform:function(t){return Md.el[t]},setShape:function(t,e){var n=Md.el;return(n.shape||(n.shape={}))[t]=e,n.dirtyShape&&n.dirtyShape(),this},getShape:function(t){var e=Md.el.shape;if(e)return e[t]},setStyle:function(t,e){var n=Md.el,i=n.style;return i&&(i[t]=e,n.dirtyStyle&&n.dirtyStyle()),this},getStyle:function(t){var e=Md.el.style;if(e)return e[t]},setExtra:function(t,e){return(Md.el.extra||(Md.el.extra={}))[t]=e,this},getExtra:function(t){var e=Md.el.extra;if(e)return e[t]}};function Dd(){var t=this,e=t.el;if(e){var n=xd(e).userDuring,i=t.userDuring;n===i?(Md.el=e,i(Ld)):t.el=t.userDuring=null}}function Cd(t,e,n,i){var o=n[t];if(o){var r,s=e[t];if(s){var l=n.transition,u=o.transition;if(u)if(!r&&(r=i[t]={}),wd(u))F(r,s);else for(var d=oe(u),h=0;h<d.length;h++){var c=s[f=d[h]];r[f]=c}else if(wd(l)||b(l,t)>=0){!r&&(r=i[t]={});var p=a(s);for(h=0;h<p.length;h++){var f;c=s[f=p[h]];Ad(o[f],c)&&(r[f]=c)}}}}}function Td(t,e,n){var i=e[t];if(i)for(var o=n[t]={},r=a(i),s=0;s<r.length;s++){var l=r[s];o[l]=zn(i[l])}}function Ad(t,e){return On(t)?t!==e:null!=t&&isFinite(t)}var Pd=t(),kd=["percent","easing","shape","style","extra"];function Nd(t){t.stopAnimation("keyframe"),t.attr(Pd(t))}function Vd(t,e,n){if(n.isAnimationEnabled()&&e)if(tt(e))p(e,(function(e){Vd(t,e,n)}));else{var i=e.keyframes,o=e.duration;if(n&&null==o){var r=Vn("enter",n,0);o=r&&r.duration}if(i&&o){var s=Pd(t);p(md,(function(n){var r;n&&!t[n]||(i.sort((function(t,e){return t.percent-e.percent})),p(i,(function(i){var l=t.animators,d=n?i[n]:i;if(d){var h=a(d);if(n||(h=u(h,(function(t){return b(kd,t)<0}))),h.length){r||((r=t.animate(n,e.loop,!0)).scope="keyframe");for(var c=0;c<l.length;c++)l[c]!==r&&l[c].targetName===r.targetName&&l[c].stopTracks(h);n&&(s[n]=s[n]||{});var f=n?s[n]:s;p(h,(function(e){f[e]=((n?t[n]:t)||{})[e]})),r.whenWithKeys(o*i.percent,d,h,i.easing)}}})),r&&r.delay(e.delay||0).duration(o).start(e.easing))}))}}}var Rd="emphasis",zd="normal",Od="blur",Ed="select",Bd=[zd,Rd,Od,Ed],Gd={normal:["itemStyle"],emphasis:[Rd,"itemStyle"],blur:[Od,"itemStyle"],select:[Ed,"itemStyle"]},Hd={normal:["label"],emphasis:[Rd,"label"],blur:[Od,"label"],select:[Ed,"label"]},Fd=["x","y"],Wd={normal:{},emphasis:{},blur:{},select:{}},Zd={cartesian2d:function(t){var e=t.master.getRect();return{coordSys:{type:"cartesian2d",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(e){return t.dataToPoint(e)},size:gt(sd,t)}}},geo:function(t){var e=t.getBoundingRect();return{coordSys:{type:"geo",x:e.x,y:e.y,width:e.width,height:e.height,zoom:t.getZoom()},api:{coord:function(e){return t.dataToPoint(e)},size:gt(ld,t)}}},single:function(t){var e=t.getRect();return{coordSys:{type:"singleAxis",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(e){return t.dataToPoint(e)},size:gt(ud,t)}}},polar:function(t){var e=t.getRadiusAxis(),n=t.getAngleAxis(),i=e.getExtent();return i[0]>i[1]&&i.reverse(),{coordSys:{type:"polar",cx:t.cx,cy:t.cy,r:i[1],r0:i[0]},api:{coord:function(i){var o=e.dataToRadius(i[0]),a=n.dataToAngle(i[1]),r=t.coordToPoint([o,a]);return r.push(o,a*Math.PI/180),r},size:gt(dd,t)}}},calendar:function(t){var e=t.getRect(),n=t.getRangeInfo();return{coordSys:{type:"calendar",x:e.x,y:e.y,width:e.width,height:e.height,cellWidth:t.getCellWidth(),cellHeight:t.getCellHeight(),rangeInfo:{start:n.start,end:n.end,weeks:n.weeks,dayCount:n.allDay}},api:{coord:function(e,n){return t.dataToPoint(e,n)}}}}};function Yd(t){return t instanceof X}function Xd(t){return t instanceof _e}var Ud=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n,i){this._progressiveEls=null;var o=this._data,a=t.getData(),r=this.group,s=Qd(t,a,e,n);o||r.removeAll(),a.diff(o).add((function(e){th(n,null,e,s(e,i),t,r,a)})).remove((function(e){var n=o.getItemGraphicEl(e);n&&Id(n,ad(n).option,t)})).update((function(e,l){var u=o.getItemGraphicEl(l);th(n,u,e,s(e,i),t,r,a)})).execute();var l=t.get("clip",!0)?fn(t.coordinateSystem,!1,t):null;l?r.setClipPath(l):r.removeClipPath(),this._data=a},e.prototype.incrementalPrepareRender=function(t,e,n){this.group.removeAll(),this._data=null},e.prototype.incrementalRender=function(t,e,n,i,o){var a=e.getData(),r=Qd(e,a,n,i),s=this._progressiveEls=[];function l(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}for(var u=t.start;u<t.end;u++){var d=th(null,null,u,r(u,o),e,this.group,a);d&&(d.traverse(l),s.push(d))}},e.prototype.eachRendered=function(t){Xe(this._progressiveEls||this.group,t)},e.prototype.filterForExposedEvent=function(t,e,n,i){var o=e.element;if(null==o||n.name===o)return!0;for(;(n=n.__hostTarget||n.parent)&&n!==this.group;)if(n.name===o)return!0;return!1},e.type="custom",e}(K);function jd(t){var e,n=t.type;if("path"===n){var i=t.shape,o=null!=i.width&&null!=i.height?{x:i.x||0,y:i.y||0,width:i.width,height:i.height}:null,a=dh(i);e=Wn(a,null,o,i.layout||"center"),ad(e).customPathData=a}else if("image"===n)e=new rt({}),ad(e).customImagePath=t.style.image;else if("text"===n)e=new se({});else if("group"===n)e=new Y;else{if("compoundPath"===n)throw new Error('"compoundPath" is not supported yet.');var r=Zn(n);if(!r){pn("")}e=new r}return ad(e).customGraphicType=n,e.name=t.name,e.z2EmphasisLift=1,e.z2SelectLift=1,e}function qd(t,e,n,i,o,a,r){Nd(e);var s=o&&o.normal.cfg;s&&e.setTextConfig(s),i&&null==i.transition&&(i.transition=Fd);var l=i&&i.style;if(l){if("text"===e.type){var u=l;Nn(u,"textFill")&&(u.fill=u.textFill),Nn(u,"textStroke")&&(u.stroke=u.textStroke)}var d=void 0,h=Yd(e)?l.decal:null;t&&h&&(h.dirty=!0,d=Dn(h,t)),l.__decalPattern=d}Xd(e)&&(l&&(d=l.__decalPattern)&&(l.decal=d));bd(e,i,a,{dataIndex:n,isInit:r,clearStyle:!0}),Vd(e,i.keyframeAnimation,a)}function Kd(t,e,n,i,o){var a=e.isGroup?null:e,r=o&&o[t].cfg;if(a){var s=a.ensureState(t);if(!1===i){var l=a.getState(t);l&&(l.style=null)}else s.style=i||null;r&&(s.textConfig=r),jt(a)}}function $d(t,e,n){var i=n===zd,o=i?e:oh(e,n),a=o?o.z2:null;null!=a&&((i?t:t.ensureState(n)).z2=a||0)}function Qd(t,e,n,i){var o=t.get("renderItem"),a=t.coordinateSystem,r={};a&&(r=a.prepareCustoms?a.prepareCustoms(a):Zd[a.type](a));for(var s,l,u=ot({getWidth:i.getWidth,getHeight:i.getHeight,getZr:i.getZr,getDevicePixelRatio:i.getDevicePixelRatio,value:function(t,n){return null==n&&(n=s),e.getStore().get(e.getDimensionIndex(t||0),n)},style:function(n,i){null==i&&(i=s);var o=e.getItemVisual(i,"style"),a=o&&o.fill,r=o&&o.opacity,l=m(i,zd).getItemStyle();null!=a&&(l.fill=a),null!=r&&(l.opacity=r);var u={inheritColor:xt(a)?a:"#000"},d=_(i,zd),h=ue(d,null,u,!1,!0);h.text=d.getShallow("show")?x(t.getFormattedLabel(i,zd),Bn(e,i)):null;var c=Gn(d,u,!1);return S(n,l),l=fd(l,h,c),n&&b(l,n),l.legacy=!0,l},ordinalRawValue:function(t,n){null==n&&(n=s),t=t||0;var i=e.getDimensionInfo(t);if(!i){var o=e.getDimensionIndex(t);return o>=0?e.getStore().get(o,n):void 0}var a=e.get(i.name,n),r=i&&i.ordinalMeta;return r?r.categories[a]:a},styleEmphasis:function(n,i){null==i&&(i=s);var o=m(i,Rd).getItemStyle(),a=_(i,Rd),r=ue(a,null,null,!0,!0);r.text=a.getShallow("show")?Ae(t.getFormattedLabel(i,Rd),t.getFormattedLabel(i,zd),Bn(e,i)):null;var l=Gn(a,null,!0);return S(n,o),o=fd(o,r,l),n&&b(o,n),o.legacy=!0,o},visual:function(t,n){if(null==n&&(n=s),Nn(id,t)){var i=e.getItemVisual(n,"style");return i?i[id[t]]:null}if(Nn(od,t))return e.getItemVisual(n,t)},barLayout:function(t){if("cartesian2d"===a.type){var e=a.getBaseAxis();return Hn(ot({axis:e},t))}},currentSeriesIndices:function(){return n.getCurrentSeriesIndices()},font:function(t){return Fn(t,n)}},r.api||{}),d={context:{},seriesId:t.id,seriesName:t.name,seriesIndex:t.seriesIndex,coordSys:r.coordSys,dataInsideLength:e.count(),encode:Jd(t.getData())},h={},c={},p={},f={},g=0;g<Bd.length;g++){var y=Bd[g];p[y]=t.getModel(Gd[y]),f[y]=t.getModel(Hd[y])}function v(t){return t===s?l||(l=e.getItemModel(t)):e.getItemModel(t)}function m(t,n){return e.hasItemOption?t===s?h[n]||(h[n]=v(t).getModel(Gd[n])):v(t).getModel(Gd[n]):p[n]}function _(t,n){return e.hasItemOption?t===s?c[n]||(c[n]=v(t).getModel(Hd[n])):v(t).getModel(Hd[n]):f[n]}return function(t,n){return s=t,l=null,h={},c={},o&&o(ot({dataIndexInside:t,dataIndex:e.getRawIndex(t),actionType:n?n.type:null},d),u)};function b(t,e){for(var n in e)Nn(e,n)&&(t[n]=e[n])}function S(t,e){t&&(t.textFill&&(e.textFill=t.textFill),t.textPosition&&(e.textPosition=t.textPosition))}}function Jd(t){var e={};return p(t.dimensions,(function(n){var i=t.getDimensionInfo(n);if(!i.isExtraCoord){var o=i.coordDim;(e[o]=e[o]||[])[i.coordDimIndex]=t.getDimensionIndex(n)}})),e}function th(t,e,n,i,o,a,r){if(i){var s=eh(t,e,n,i,o,a);return s&&r.setItemGraphicEl(n,s),s&&dt(s,i.focus,i.blurScope,i.emphasisDisabled),s}a.remove(e)}function eh(t,e,n,i,o,a){var r=-1,s=e;e&&nh(e,i,o)&&(r=b(a.childrenRef(),e),e=null);var l,u,d=!e,h=e;h?h.clearStates():(h=jd(i),s&&(l=s,(u=h).copyTransform(l),Xd(u)&&Xd(l)&&(u.setStyle(l.style),u.z=l.z,u.z2=l.z2,u.zlevel=l.zlevel,u.invisible=l.invisible,u.ignore=l.ignore,Yd(u)&&Yd(l)&&u.setShape(l.shape)))),!1===i.morph?h.disableMorphing=!0:h.disableMorphing&&(h.disableMorphing=!1),Wd.normal.cfg=Wd.normal.conOpt=Wd.emphasis.cfg=Wd.emphasis.conOpt=Wd.blur.cfg=Wd.blur.conOpt=Wd.select.cfg=Wd.select.conOpt=null,Wd.isLegacy=!1,function(t,e,n,i,o,a){if(t.isGroup)return;ih(n,null,a),ih(n,Rd,a);var r=a.normal.conOpt,s=a.emphasis.conOpt,l=a.blur.conOpt,u=a.select.conOpt;if(null!=r||null!=s||null!=u||null!=l){var d=t.getTextContent();if(!1===r)d&&t.removeTextContent();else{r=a.normal.conOpt=r||{type:"text"},d?d.clearStates():(d=jd(r),t.setTextContent(d)),qd(null,d,e,r,null,i,o);for(var h=r&&r.style,c=0;c<Bd.length;c++){var p=Bd[c];if(p!==zd){var f=a[p].conOpt;Kd(p,d,0,ah(r,f,p),null)}}h?d.dirty():d.markRedraw()}}}(h,n,i,o,d,Wd),function(t,e,n,i,o){var a=n.clipPath;if(!1===a)t&&t.getClipPath()&&t.removeClipPath();else if(a){var r=t.getClipPath();r&&nh(r,a,i)&&(r=null),r||(r=jd(a),t.setClipPath(r)),qd(null,r,e,a,null,i,o)}}(h,n,i,o,d),qd(t,h,n,i,Wd,o,d),Nn(i,"info")&&(ad(h).info=i.info);for(var c=0;c<Bd.length;c++){var p=Bd[c];if(p!==zd){var f=oh(i,p);Kd(p,h,0,ah(i,f,p),Wd)}}return function(t,e,n){if(!t.isGroup){var i=t,o=n.currentZ,a=n.currentZLevel;i.z=o,i.zlevel=a;var r=e.z2;null!=r&&(i.z2=r||0);for(var s=0;s<Bd.length;s++)$d(i,e,Bd[s])}}(h,i,o),"group"===i.type&&function(t,e,n,i,o){var a=i.children,r=a?a.length:0,s=i.$mergeChildren,l="byName"===s||i.diffChildrenByName,u=!1===s;if(!r&&!l&&!u)return;if(l)return d={api:t,oldChildren:e.children()||[],newChildren:a||[],dataIndex:n,seriesModel:o,group:e},void new de(d.oldChildren,d.newChildren,sh,sh,d).add(lh).update(lh).remove(uh).execute();var d;u&&e.removeAll();for(var h=0;h<r;h++){var c=a[h],p=e.childAt(h);c?(null==c.ignore&&(c.ignore=!1),eh(t,p,n,c,o,e)):p.ignore=!0}for(var f=e.childCount()-1;f>=h;f--){var g=e.childAt(f);rh(e,g,o)}}(t,h,n,i,o),r>=0?a.replaceAt(h,r):a.add(h),h}function nh(t,e,n){var i,o=ad(t),a=e.type,r=e.shape,s=e.style;return n.isUniversalTransitionEnabled()||null!=a&&a!==o.customGraphicType||"path"===a&&((i=r)&&(Nn(i,"pathData")||Nn(i,"d")))&&dh(r)!==o.customPathData||"image"===a&&Nn(s,"image")&&s.image!==o.customImagePath}function ih(t,e,n){var i=e?oh(t,e):t,o=e?ah(t,i,Rd):t.style,a=t.type,r=i?i.textConfig:null,s=t.textContent,l=s?e?oh(s,e):s:null;if(o&&(n.isLegacy||hd(o,a,!!r,!!l))){n.isLegacy=!0;var u=cd(o,a,!e);!r&&u.textConfig&&(r=u.textConfig),!l&&u.textContent&&(l=u.textContent)}if(!e&&l){var d=l;!d.type&&(d.type="text")}var h=e?n[e]:n.normal;h.cfg=r,h.conOpt=l}function oh(t,e){return e?t?t[e]:null:t}function ah(t,e,n){var i=e&&e.style;return null==i&&n===Rd&&t&&(i=t.styleEmphasis),i}function rh(t,e,n){e&&Id(e,ad(t).option,n)}function sh(t,e){var n=t&&t.name;return null!=n?n:"e\0\0"+e}function lh(t,e){var n=this.context,i=null!=t?n.newChildren[t]:null,o=null!=e?n.oldChildren[e]:null;eh(n.api,o,n.dataIndex,i,n.seriesModel,n.group)}function uh(t){var e=this.context,n=e.oldChildren[t];n&&Id(n,ad(n).option,e.seriesModel)}function dh(t){return t&&(t.pathData||t.d)}function hh(t,e){e=e||{};var n=t.coordinateSystem,i=t.axis,o={},a=i.position,r=i.orient,s=n.getRect(),l=[s.x,s.x+s.width,s.y,s.y+s.height],u={horizontal:{top:l[2],bottom:l[3]},vertical:{left:l[0],right:l[1]}};o.position=["vertical"===r?u.vertical[a]:l[0],"horizontal"===r?u.horizontal[a]:l[3]];o.rotation=Math.PI/2*{horizontal:0,vertical:1}[r];o.labelDirection=o.tickDirection=o.nameDirection={top:-1,bottom:1,right:1,left:-1}[a],t.get(["axisTick","inside"])&&(o.tickDirection=-o.tickDirection),Ce(e.labelInside,t.get(["axisLabel","inside"]))&&(o.labelDirection=-o.labelDirection);var d=e.rotate;return null==d&&(d=t.get(["axisLabel","rotate"])),o.labelRotation="top"===a?-d:d,o.z2=1,o}var ch=["axisLine","axisTickLabel","axisName"],ph=["splitArea","splitLine"],fh=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.axisPointerClass="SingleAxisPointer",n}return w(e,t),e.prototype.render=function(e,n,i,o){var a=this.group;a.removeAll();var r=this._axisGroup;this._axisGroup=new Y;var s=hh(e),l=new Mt(e,s);p(ch,l.add,l),a.add(this._axisGroup),a.add(l.getGroup()),p(ph,(function(t){e.get([t,"show"])&&gh[t](this,this.group,this._axisGroup,e)}),this),Yn(r,this._axisGroup,e),t.prototype.render.call(this,e,n,i,o)},e.prototype.remove=function(){Xn(this)},e.type="singleAxis",e}(Un),gh={splitLine:function(t,e,n,i){var o=i.axis;if(!o.scale.isBlank()){var a=i.getModel("splitLine"),r=a.getModel("lineStyle"),s=r.get("color");s=s instanceof Array?s:[s];for(var l=r.get("width"),u=i.coordinateSystem.getRect(),d=o.isHorizontal(),h=[],c=0,p=o.getTicksCoords({tickModel:a}),f=[],g=[],y=0;y<p.length;++y){var v=o.toGlobalCoord(p[y].coord);d?(f[0]=v,f[1]=u.y,g[0]=v,g[1]=u.y+u.height):(f[0]=u.x,f[1]=v,g[0]=u.x+u.width,g[1]=v);var m=new Ge({shape:{x1:f[0],y1:f[1],x2:g[0],y2:g[1]},silent:!0});qn(m.shape,l);var x=c++%s.length;h[x]=h[x]||[],h[x].push(m)}var _=r.getLineStyle(["color"]);for(y=0;y<h.length;++y)e.add(Ct(h[y],{style:ot({stroke:s[y%s.length]},_),silent:!0}))}},splitArea:function(t,e,n,i){jn(t,n,i,i)}},yh=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.getCoordSysModel=function(){return this},e.type="singleAxis",e.layoutMode="box",e.defaultOption={left:"5%",top:"5%",right:"5%",bottom:"5%",type:"value",position:"bottom",orient:"horizontal",axisLine:{show:!0,lineStyle:{width:1,type:"solid"}},tooltip:{show:!0},axisTick:{show:!0,length:6,lineStyle:{width:1}},axisLabel:{show:!0,interval:"auto"},splitLine:{show:!0,lineStyle:{type:"dashed",opacity:.2}}},e}(wt);bt(yh,St.prototype);var vh=function(t){function e(e,n,i,o,a){var r=t.call(this,e,n,i)||this;return r.type=o||"value",r.position=a||"bottom",r}return w(e,t),e.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},e.prototype.pointToData=function(t,e){return this.coordinateSystem.pointToData(t)[0]},e}(At),mh=["single"],xh=function(){function t(t,e,n){this.type="single",this.dimension="single",this.dimensions=mh,this.axisPointerEnabled=!0,this.model=t,this._init(t,e,n)}return t.prototype._init=function(t,e,n){var i=this.dimension,o=new vh(i,Kn(t),[0,0],t.get("type"),t.get("position")),a="category"===o.type;o.onBand=a&&t.get("boundaryGap"),o.inverse=t.get("inverse"),o.orient=t.get("orient"),t.axis=o,o.model=t,o.coordinateSystem=this,this._axis=o},t.prototype.update=function(t,e){t.eachSeries((function(t){if(t.coordinateSystem===this){var e=t.getData();p(e.mapDimensionsAll(this.dimension),(function(t){this._axis.scale.unionExtentFromData(e,t)}),this),$n(this._axis.scale,this._axis.model)}}),this)},t.prototype.resize=function(t,e){this._rect=Rt({left:t.get("left"),top:t.get("top"),right:t.get("right"),bottom:t.get("bottom"),width:t.get("width"),height:t.get("height")},{width:e.getWidth(),height:e.getHeight()}),this._adjustAxis()},t.prototype.getRect=function(){return this._rect},t.prototype._adjustAxis=function(){var t=this._rect,e=this._axis,n=e.isHorizontal(),i=n?[0,t.width]:[0,t.height],o=e.inverse?1:0;e.setExtent(i[o],i[1-o]),this._updateAxisTransform(e,n?t.x:t.y)},t.prototype._updateAxisTransform=function(t,e){var n=t.getExtent(),i=n[0]+n[1],o=t.isHorizontal();t.toGlobalCoord=o?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord=o?function(t){return t-e}:function(t){return i-t+e}},t.prototype.getAxis=function(){return this._axis},t.prototype.getBaseAxis=function(){return this._axis},t.prototype.getAxes=function(){return[this._axis]},t.prototype.getTooltipAxes=function(){return{baseAxes:[this.getAxis()],otherAxes:[]}},t.prototype.containPoint=function(t){var e=this.getRect(),n=this.getAxis();return"horizontal"===n.orient?n.contain(n.toLocalCoord(t[0]))&&t[1]>=e.y&&t[1]<=e.y+e.height:n.contain(n.toLocalCoord(t[1]))&&t[0]>=e.y&&t[0]<=e.y+e.height},t.prototype.pointToData=function(t){var e=this.getAxis();return[e.coordToData(e.toLocalCoord(t["horizontal"===e.orient?0:1]))]},t.prototype.dataToPoint=function(t){var e=this.getAxis(),n=this.getRect(),i=[],o="horizontal"===e.orient?0:1;return t instanceof Array&&(t=t[0]),i[o]=e.toGlobalCoord(e.dataToCoord(+t)),i[1-o]=0===o?n.y+n.height/2:n.x+n.width/2,i},t.prototype.convertToPixel=function(t,e,n){return _h(e)===this?this.dataToPoint(n):null},t.prototype.convertFromPixel=function(t,e,n){return _h(e)===this?this.pointToData(n):null},t}();function _h(t){var e=t.seriesModel,n=t.singleAxisModel;return n&&n.coordinateSystem||e&&e.coordinateSystem}var bh={create:function(t,e){var n=[];return t.eachComponent("singleAxis",(function(i,o){var a=new xh(i,t,e);a.name="single_"+o,a.resize(i,e),i.coordinateSystem=a,n.push(a)})),t.eachSeries((function(t){if("singleAxis"===t.get("coordinateSystem")){var e=t.getReferringComponents("singleAxis",Ln).models[0];t.coordinateSystem=e&&e.coordinateSystem}})),n},dimensions:mh},Sh=["x","y"],Ih=["width","height"],wh=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.makeElOption=function(t,e,n,i,o){var a=n.axis,r=a.coordinateSystem,s=Dh(r,1-Lh(a)),l=r.dataToPoint(e)[0],u=i.get("type");if(u&&"none"!==u){var d=Qn(i),h=Mh[u](a,l,s);h.style=d,t.graphicKey=h.type,t.pointer=h}var c=hh(n);Jn(e,t,c,n,i,o)},e.prototype.getHandleTransform=function(t,e,n){var i=hh(e,{labelInside:!1});i.labelMargin=n.get(["handle","margin"]);var o=ti(e.axis,t,i);return{x:o[0],y:o[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,e,n,i){var o=n.axis,a=o.coordinateSystem,r=Lh(o),s=Dh(a,r),l=[t.x,t.y];l[r]+=e[r],l[r]=Math.min(s[1],l[r]),l[r]=Math.max(s[0],l[r]);var u=Dh(a,1-r),d=(u[1]+u[0])/2,h=[d,d];return h[r]=l[r],{x:l[0],y:l[1],rotation:t.rotation,cursorPoint:h,tooltipOption:{verticalAlign:"middle"}}},e}(ei),Mh={line:function(t,e,n){return{type:"Line",subPixelOptimize:!0,shape:ii([e,n[0]],[e,n[1]],Lh(t))}},shadow:function(t,e,n){var i=t.getBandWidth(),o=n[1]-n[0];return{type:"Rect",shape:ni([e-i/2,n[0]],[i,o],Lh(t))}}};function Lh(t){return t.isHorizontal()?0:1}function Dh(t,e){var n=t.getRect();return[n[Sh[e]],n[Sh[e]]+n[Ih[e]]]}var Ch=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.type="single",e}(Tt);var Th=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.init=function(e,n,i){var o=ri(e);t.prototype.init.apply(this,arguments),Ah(e,o)},e.prototype.mergeOption=function(e){t.prototype.mergeOption.apply(this,arguments),Ah(this.option,e)},e.prototype.getCellSize=function(){return this.option.cellSize},e.type="calendar",e.defaultOption={z:2,left:80,top:60,cellSize:20,orient:"horizontal",splitLine:{show:!0,lineStyle:{color:"#000",width:1,type:"solid"}},itemStyle:{color:"#fff",borderWidth:1,borderColor:"#ccc"},dayLabel:{show:!0,firstDay:0,position:"start",margin:"50%",color:"#000"},monthLabel:{show:!0,position:"start",margin:5,align:"center",formatter:null,color:"#000"},yearLabel:{show:!0,position:null,margin:30,formatter:null,color:"#ccc",fontFamily:"sans-serif",fontWeight:"bolder",fontSize:20}},e}(wt);function Ah(t,e){var n,i=t.cellSize;1===(n=tt(i)?i:t.cellSize=[i,i]).length&&(n[1]=n[0]);var o=ht([0,1],(function(t){return si(e,t)&&(n[t]="auto"),null!=n[t]&&"auto"!==n[t]}));li(t,e,{type:"box",ignoreSize:o})}var Ph=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n){var i=this.group;i.removeAll();var o=t.coordinateSystem,a=o.getRangeInfo(),r=o.getOrient(),s=e.getLocaleModel();this._renderDayRect(t,a,i),this._renderLines(t,a,r,i),this._renderYearText(t,a,r,i),this._renderMonthText(t,s,r,i),this._renderWeekText(t,s,a,r,i)},e.prototype._renderDayRect=function(t,e,n){for(var i=t.coordinateSystem,o=t.getModel("itemStyle").getItemStyle(),a=i.getCellWidth(),r=i.getCellHeight(),s=e.start.time;s<=e.end.time;s=i.getNextNDay(s,1).time){var l=i.dataToRect([s],!1).tl,u=new be({shape:{x:l[0],y:l[1],width:a,height:r},cursor:"default",style:o});n.add(u)}},e.prototype._renderLines=function(t,e,n,i){var o=this,a=t.coordinateSystem,r=t.getModel(["splitLine","lineStyle"]).getLineStyle(),s=t.get(["splitLine","show"]),l=r.lineWidth;this._tlpoints=[],this._blpoints=[],this._firstDayOfMonth=[],this._firstDayPoints=[];for(var u=e.start,d=0;u.time<=e.end.time;d++){c(u.formatedDate),0===d&&(u=a.getDateInfo(e.start.y+"-"+e.start.m));var h=u.date;h.setMonth(h.getMonth()+1),u=a.getDateInfo(h)}function c(e){o._firstDayOfMonth.push(a.getDateInfo(e)),o._firstDayPoints.push(a.dataToRect([e],!1).tl);var l=o._getLinePointsOfOneWeek(t,e,n);o._tlpoints.push(l[0]),o._blpoints.push(l[l.length-1]),s&&o._drawSplitline(l,r,i)}c(a.getNextNDay(e.end.time,1).formatedDate),s&&this._drawSplitline(o._getEdgesPoints(o._tlpoints,l,n),r,i),s&&this._drawSplitline(o._getEdgesPoints(o._blpoints,l,n),r,i)},e.prototype._getEdgesPoints=function(t,e,n){var i=[t[0].slice(),t[t.length-1].slice()],o="horizontal"===n?0:1;return i[0][o]=i[0][o]-e/2,i[1][o]=i[1][o]+e/2,i},e.prototype._drawSplitline=function(t,e,n){var i=new nt({z2:20,shape:{points:t},style:e});n.add(i)},e.prototype._getLinePointsOfOneWeek=function(t,e,n){for(var i=t.coordinateSystem,o=i.getDateInfo(e),a=[],r=0;r<7;r++){var s=i.getNextNDay(o.time,r),l=i.dataToRect([s.time],!1);a[2*s.day]=l.tl,a[2*s.day+1]=l["horizontal"===n?"bl":"tr"]}return a},e.prototype._formatterLabel=function(t,e){return xt(t)&&t?ui(t,e):o(t)?t(e):e.nameMap},e.prototype._yearTextPositionControl=function(t,e,n,i,o){var a=e[0],r=e[1],s=["center","bottom"];"bottom"===i?(r+=o,s=["center","top"]):"left"===i?a-=o:"right"===i?(a+=o,s=["center","top"]):r-=o;var l=0;return"left"!==i&&"right"!==i||(l=Math.PI/2),{rotation:l,x:a,y:r,style:{align:s[0],verticalAlign:s[1]}}},e.prototype._renderYearText=function(t,e,n,i){var o=t.getModel("yearLabel");if(o.get("show")){var a=o.get("margin"),r=o.get("position");r||(r="horizontal"!==n?"top":"left");var s=[this._tlpoints[this._tlpoints.length-1],this._blpoints[0]],l=(s[0][0]+s[1][0])/2,u=(s[0][1]+s[1][1])/2,d="horizontal"===n?0:1,h={top:[l,s[d][1]],bottom:[l,s[1-d][1]],left:[s[1-d][0],u],right:[s[d][0],u]},c=e.start.y;+e.end.y>+e.start.y&&(c=c+"-"+e.end.y);var p=o.get("formatter"),f={start:e.start.y,end:e.end.y,nameMap:c},g=this._formatterLabel(p,f),y=new se({z2:30,style:ue(o,{text:g})});y.attr(this._yearTextPositionControl(y,h[r],n,r,a)),i.add(y)}},e.prototype._monthTextPositionControl=function(t,e,n,i,o){var a="left",r="top",s=t[0],l=t[1];return"horizontal"===n?(l+=o,e&&(a="center"),"start"===i&&(r="bottom")):(s+=o,e&&(r="middle"),"start"===i&&(a="right")),{x:s,y:l,align:a,verticalAlign:r}},e.prototype._renderMonthText=function(t,e,n,i){var o=t.getModel("monthLabel");if(o.get("show")){var a=o.get("nameMap"),r=o.get("margin"),s=o.get("position"),l=o.get("align"),u=[this._tlpoints,this._blpoints];a&&!xt(a)||(a&&(e=di(a)||e),a=e.get(["time","monthAbbr"])||[]);var d="start"===s?0:1,h="horizontal"===n?0:1;r="start"===s?-r:r;for(var c="center"===l,p=0;p<u[d].length-1;p++){var f=u[d][p].slice(),g=this._firstDayOfMonth[p];if(c){var y=this._firstDayPoints[p];f[h]=(y[h]+u[0][p+1][h])/2}var v=o.get("formatter"),m=a[+g.m-1],x={yyyy:g.y,yy:(g.y+"").slice(2),MM:g.m,M:+g.m,nameMap:m},_=this._formatterLabel(v,x),b=new se({z2:30,style:F(ue(o,{text:_}),this._monthTextPositionControl(f,c,n,s,r))});i.add(b)}}},e.prototype._weekTextPositionControl=function(t,e,n,i,o){var a="center",r="middle",s=t[0],l=t[1],u="start"===n;return"horizontal"===e?(s=s+i+(u?1:-1)*o[0]/2,a=u?"right":"left"):(l=l+i+(u?1:-1)*o[1]/2,r=u?"bottom":"top"),{x:s,y:l,align:a,verticalAlign:r}},e.prototype._renderWeekText=function(t,e,n,i,o){var a=t.getModel("dayLabel");if(a.get("show")){var r=t.coordinateSystem,l=a.get("position"),u=a.get("nameMap"),d=a.get("margin"),h=r.getFirstDayOfWeek();if(!u||xt(u))u&&(e=di(u)||e),u=e.get(["time","dayOfWeekShort"])||ht(e.get(["time","dayOfWeekAbbr"]),(function(t){return t[0]}));var c=r.getNextNDay(n.end.time,7-n.lweek).time,p=[r.getCellWidth(),r.getCellHeight()];d=s(d,Math.min(p[1],p[0])),"start"===l&&(c=r.getNextNDay(n.start.time,-(7+n.fweek)).time,d=-d);for(var f=0;f<7;f++){var g,y=r.getNextNDay(c,f),v=r.dataToRect([y.time],!1).center;g=Math.abs((f+h)%7);var m=new se({z2:30,style:F(ue(a,{text:u[g]}),this._weekTextPositionControl(v,i,l,d,p))});o.add(m)}}},e.type="calendar",e}(Tt),kh=864e5,Nh=function(){function t(e,n,i){this.type="calendar",this.dimensions=t.dimensions,this.getDimensionsInfo=t.getDimensionsInfo,this._model=e}return t.getDimensionsInfo=function(){return[{name:"time",type:"time"},"value"]},t.prototype.getRangeInfo=function(){return this._rangeInfo},t.prototype.getModel=function(){return this._model},t.prototype.getRect=function(){return this._rect},t.prototype.getCellWidth=function(){return this._sw},t.prototype.getCellHeight=function(){return this._sh},t.prototype.getOrient=function(){return this._orient},t.prototype.getFirstDayOfWeek=function(){return this._firstDayOfWeek},t.prototype.getDateInfo=function(t){var e=(t=hi(t)).getFullYear(),n=t.getMonth()+1,i=n<10?"0"+n:""+n,o=t.getDate(),a=o<10?"0"+o:""+o,r=t.getDay();return{y:e+"",m:i,d:a,day:r=Math.abs((r+7-this.getFirstDayOfWeek())%7),time:t.getTime(),formatedDate:e+"-"+i+"-"+a,date:t}},t.prototype.getNextNDay=function(t,e){return 0===(e=e||0)||(t=new Date(this.getDateInfo(t).time)).setDate(t.getDate()+e),this.getDateInfo(t)},t.prototype.update=function(t,e){this._firstDayOfWeek=+this._model.getModel("dayLabel").get("firstDay"),this._orient=this._model.get("orient"),this._lineWidth=this._model.getModel("itemStyle").getItemStyle().lineWidth||0,this._rangeInfo=this._getRangeInfo(this._initRangeOption());var n=this._rangeInfo.weeks||1,i=["width","height"],o=this._model.getCellSize().slice(),a=this._model.getBoxLayoutParams(),r="horizontal"===this._orient?[n,7]:[7,n];p([0,1],(function(t){u(o,t)&&(a[i[t]]=o[t]*r[t])}));var s={width:e.getWidth(),height:e.getHeight()},l=this._rect=Rt(a,s);function u(t,e){return null!=t[e]&&"auto"!==t[e]}p([0,1],(function(t){u(o,t)||(o[t]=l[i[t]]/r[t])})),this._sw=o[0],this._sh=o[1]},t.prototype.dataToPoint=function(t,e){tt(t)&&(t=t[0]),null==e&&(e=!0);var n=this.getDateInfo(t),i=this._rangeInfo,o=n.formatedDate;if(e&&!(n.time>=i.start.time&&n.time<i.end.time+kh))return[NaN,NaN];var a=n.day,r=this._getRangeInfo([i.start.time,o]).nthWeek;return"vertical"===this._orient?[this._rect.x+a*this._sw+this._sw/2,this._rect.y+r*this._sh+this._sh/2]:[this._rect.x+r*this._sw+this._sw/2,this._rect.y+a*this._sh+this._sh/2]},t.prototype.pointToData=function(t){var e=this.pointToDate(t);return e&&e.time},t.prototype.dataToRect=function(t,e){var n=this.dataToPoint(t,e);return{contentShape:{x:n[0]-(this._sw-this._lineWidth)/2,y:n[1]-(this._sh-this._lineWidth)/2,width:this._sw-this._lineWidth,height:this._sh-this._lineWidth},center:n,tl:[n[0]-this._sw/2,n[1]-this._sh/2],tr:[n[0]+this._sw/2,n[1]-this._sh/2],br:[n[0]+this._sw/2,n[1]+this._sh/2],bl:[n[0]-this._sw/2,n[1]+this._sh/2]}},t.prototype.pointToDate=function(t){var e=Math.floor((t[0]-this._rect.x)/this._sw)+1,n=Math.floor((t[1]-this._rect.y)/this._sh)+1,i=this._rangeInfo.range;return"vertical"===this._orient?this._getDateByWeeksAndDay(n,e-1,i):this._getDateByWeeksAndDay(e,n-1,i)},t.prototype.convertToPixel=function(t,e,n){var i=Vh(e);return i===this?i.dataToPoint(n):null},t.prototype.convertFromPixel=function(t,e,n){var i=Vh(e);return i===this?i.pointToData(n):null},t.prototype.containPoint=function(t){return!1},t.prototype._initRangeOption=function(){var t,e=this._model.get("range");if(tt(e)&&1===e.length&&(e=e[0]),tt(e))t=e;else{var n=e.toString();if(/^\d{4}$/.test(n)&&(t=[n+"-01-01",n+"-12-31"]),/^\d{4}[\/|-]\d{1,2}$/.test(n)){var i=this.getDateInfo(n),o=i.date;o.setMonth(o.getMonth()+1);var a=this.getNextNDay(o,-1);t=[i.formatedDate,a.formatedDate]}/^\d{4}[\/|-]\d{1,2}[\/|-]\d{1,2}$/.test(n)&&(t=[n,n])}if(!t)return e;var r=this._getRangeInfo(t);return r.start.time>r.end.time&&t.reverse(),t},t.prototype._getRangeInfo=function(t){var e,n=[this.getDateInfo(t[0]),this.getDateInfo(t[1])];n[0].time>n[1].time&&(e=!0,n.reverse());var i=Math.floor(n[1].time/kh)-Math.floor(n[0].time/kh)+1,o=new Date(n[0].time),a=o.getDate(),r=n[1].date.getDate();o.setDate(a+i-1);var s=o.getDate();if(s!==r)for(var l=o.getTime()-n[1].time>0?1:-1;(s=o.getDate())!==r&&(o.getTime()-n[1].time)*l>0;)i-=l,o.setDate(s-l);var u=Math.floor((i+n[0].day+6)/7),d=e?1-u:u-1;return e&&n.reverse(),{range:[n[0].formatedDate,n[1].formatedDate],start:n[0],end:n[1],allDay:i,weeks:u,nthWeek:d,fweek:n[0].day,lweek:n[1].day}},t.prototype._getDateByWeeksAndDay=function(t,e,n){var i=this._getRangeInfo(n);if(t>i.weeks||0===t&&e<i.fweek||t===i.weeks&&e>i.lweek)return null;var o=7*(t-1)-i.fweek+e,a=new Date(i.start.time);return a.setDate(+i.start.d+o),this.getDateInfo(a)},t.create=function(e,n){var i=[];return e.eachComponent("calendar",(function(e){var n=new t(e);i.push(n),e.coordinateSystem=n})),e.eachSeries((function(t){"calendar"===t.get("coordinateSystem")&&(t.coordinateSystem=i[t.get("calendarIndex")||0])})),i},t.dimensions=["time","value"],t}();function Vh(t){var e=t.calendarModel,n=t.seriesModel;return e?e.coordinateSystem:n?n.coordinateSystem:null}function Rh(t,e){var n;return p(e,(function(e){null!=t[e]&&"auto"!==t[e]&&(n=!0)})),n}var zh=["transition","enterFrom","leaveTo"],Oh=zh.concat(["enterAnimation","updateAnimation","leaveAnimation"]);function Eh(t,e,n){if(n&&(!t[n]&&e[n]&&(t[n]={}),t=t[n],e=e[n]),t&&e)for(var i=n?zh:Oh,o=0;o<i.length;o++){var a=i[o];null==t[a]&&null!=e[a]&&(t[a]=e[a])}}var Bh=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.preventAutoZ=!0,n}return w(e,t),e.prototype.mergeOption=function(e,n){var i=this.option.elements;this.option.elements=null,t.prototype.mergeOption.call(this,e,n),this.option.elements=i},e.prototype.optionUpdated=function(t,e){var n=this.option,i=(e?n:t).elements,o=n.elements=e?[]:n.elements,a=[];this._flatten(i,a,null);var r=ci(o,a,"normalMerge"),s=this._elOptionsToUpdate=[];p(r,(function(t,e){var n=t.newOption;n&&(s.push(n),function(t,e){var n=t.existing;if(e.id=t.keyInfo.id,!e.type&&n&&(e.type=n.type),null==e.parentId){var i=e.parentOption;i?e.parentId=i.id:n&&(e.parentId=n.parentId)}e.parentOption=null}(t,n),function(t,e,n){var i=F({},n),o=t[e],a=n.$action||"merge";"merge"===a?o?(B(o,i,!0),li(o,i,{ignoreSize:!0}),pi(n,o),Eh(n,o),Eh(n,o,"shape"),Eh(n,o,"style"),Eh(n,o,"extra"),n.clipPath=o.clipPath):t[e]=i:"replace"===a?t[e]=i:"remove"===a&&o&&(t[e]=null)}(o,e,n),function(t,e){if(t&&(t.hv=e.hv=[Rh(e,["left","right"]),Rh(e,["top","bottom"])],"group"===t.type)){var n=t,i=e;null==n.width&&(n.width=i.width=0),null==n.height&&(n.height=i.height=0)}}(o[e],n))}),this),n.elements=u(o,(function(t){return t&&delete t.$action,null!=t}))},e.prototype._flatten=function(t,e,n){p(t,(function(t){if(t){n&&(t.parentOption=n),e.push(t);var i=t.children;i&&i.length&&this._flatten(i,e,t),delete t.children}}),this)},e.prototype.useElOptionsToUpdate=function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t},e.type="graphic",e.defaultOption={elements:[]},e}(wt),Gh={path:null,compoundPath:null,group:Y,image:rt,text:se},Hh=t(),Fh=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.init=function(){this._elMap=ie()},e.prototype.render=function(t,e,n){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t),this._relocate(t,n)},e.prototype._updateElements=function(t){var e=t.useElOptionsToUpdate();if(e){var n=this._elMap,i=this.group,o=t.get("z"),a=t.get("zlevel");p(e,(function(e){var s=$t(e.id,null),l=null!=s?n.get(s):null,u=$t(e.parentId,null),d=null!=u?n.get(u):i,h=e.type,c=e.style;"text"===h&&c&&e.hv&&e.hv[1]&&(c.textVerticalAlign=c.textBaseline=c.verticalAlign=c.align=null);var f=e.textContent,g=e.textConfig;if(c&&hd(c,h,!!g,!!f)){var y=cd(c,h,!0);!g&&y.textConfig&&(g=e.textConfig=y.textConfig),!f&&y.textContent&&(f=y.textContent)}var v=function(t){return t=F({},t),p(["id","parentId","$action","hv","bounding","textContent","clipPath"].concat(gi),(function(e){delete t[e]})),t}(e),m=e.$action||"merge",x="merge"===m,_="replace"===m;if(x){var b=l;(C=!l)?b=Zh(s,d,e.type,n):(b&&(Hh(b).isNew=!1),Nd(b)),b&&(bd(b,v,t,{isInit:C}),Xh(b,e,o,a))}else if(_){Yh(l,e,n,t);var S=Zh(s,d,e.type,n);S&&(bd(S,v,t,{isInit:!0}),Xh(S,e,o,a))}else"remove"===m&&(Sd(l,e),Yh(l,e,n,t));var I=n.get(s);if(I&&f)if(x){var w=I.getTextContent();w?w.attr(f):I.setTextContent(new se(f))}else _&&I.setTextContent(new se(f));if(I){var M=e.clipPath;if(M){var L=M.type,D=void 0,C=!1;if(x){var T=I.getClipPath();D=(C=!T||Hh(T).type!==L)?Wh(L):T}else _&&(C=!0,D=Wh(L));I.setClipPath(D),bd(D,M,t,{isInit:C}),Vd(D,M.keyframeAnimation,t)}var A=Hh(I);I.setTextConfig(g),A.option=e,function(t,e,n){var i=r(t).eventData;t.silent||t.ignore||i||(i=r(t).eventData={componentType:"graphic",componentIndex:e.componentIndex,name:t.name});i&&(i.info=n.info)}(I,t,e),fi({el:I,componentModel:t,itemName:I.name,itemTooltipOption:e.tooltip}),Vd(I,e.keyframeAnimation,t)}}))}},e.prototype._relocate=function(t,e){for(var n=t.option.elements,i=this.group,o=this._elMap,a=e.getWidth(),r=e.getHeight(),l=["x","y"],u=0;u<n.length;u++){var d=n[u];if((g=null!=(f=$t(d.id,null))?o.get(f):null)&&g.isGroup){var h=(y=g.parent)===i,c=Hh(g),p=Hh(y);c.width=s(c.option.width,h?a:p.width)||0,c.height=s(c.option.height,h?r:p.height)||0}}for(u=n.length-1;u>=0;u--){var f,g;d=n[u];if(g=null!=(f=$t(d.id,null))?o.get(f):null){var y=g.parent,v=(p=Hh(y),y===i?{width:a,height:r}:{width:p.width,height:p.height}),m={},x=ae(g,d,v,null,{hv:d.hv,boundingMode:d.bounding},m);if(!Hh(g).isNew&&x){for(var _=d.transition,I={},w=0;w<l.length;w++){var M=l[w],L=m[M];_&&(wd(_)||b(_,M)>=0)?I[M]=L:g[M]=L}S(g,I,t,0)}else g.attr(m)}}},e.prototype._clear=function(){var t=this,e=this._elMap;e.each((function(n){Yh(n,Hh(n).option,e,t._lastGraphicModel)})),this._elMap=ie()},e.prototype.dispose=function(){this._clear()},e.type="graphic",e}(Tt);function Wh(t){var e=new(Nn(Gh,t)?Gh[t]:Zn(t))({});return Hh(e).type=t,e}function Zh(t,e,n,i){var o=Wh(n);return e.add(o),i.set(t,o),Hh(o).id=t,Hh(o).isNew=!0,o}function Yh(t,e,n,i){t&&t.parent&&("group"===t.type&&t.traverse((function(t){Yh(t,e,n,i)})),Id(t,e,i),n.removeKey(Hh(t).id))}function Xh(t,e,n,i){t.isGroup||p([["cursor",_e.prototype.cursor],["zlevel",i||0],["z",n||0],["z2",0]],(function(n){var i=n[0];Nn(e,i)?t[i]=x(e[i],n[1]):null==t[i]&&(t[i]=n[1])})),p(a(e),(function(n){if(0===n.indexOf("on")){var i=e[n];t[n]=o(i)?i:null}})),Nn(e,"draggable")&&(t.draggable=e.draggable),null!=e.name&&(t.name=e.name),null!=e.id&&(t.id=e.id)}var Uh=["x","y","radius","angle","single"],jh=["cartesian2d","polar","singleAxis"];function qh(t){return t+"Axis"}function Kh(t,e){var n,i=ie(),o=[],a=ie();t.eachComponent({mainType:"dataZoom",query:e},(function(t){a.get(t.uid)||s(t)}));do{n=!1,t.eachComponent("dataZoom",r)}while(n);function r(t){!a.get(t.uid)&&function(t){var e=!1;return t.eachTargetAxis((function(t,n){var o=i.get(t);o&&o[n]&&(e=!0)})),e}(t)&&(s(t),n=!0)}function s(t){a.set(t.uid,!0),o.push(t),t.eachTargetAxis((function(t,e){(i.get(t)||i.set(t,[]))[e]=!0}))}return o}function $h(t){var e=t.ecModel,n={infoList:[],infoMap:ie()};return t.eachTargetAxis((function(t,i){var o=e.getComponent(qh(t),i);if(o){var a=o.getCoordSysModel();if(a){var r=a.uid,s=n.infoMap.get(r);s||(s={model:a,axisModels:[]},n.infoList.push(s),n.infoMap.set(r,s)),s.axisModels.push(o)}}})),n}var Qh=function(){function t(){this.indexList=[],this.indexMap=[]}return t.prototype.add=function(t){this.indexMap[t]||(this.indexList.push(t),this.indexMap[t]=!0)},t}(),Jh=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n._autoThrottle=!0,n._noTarget=!0,n._rangePropMode=["percent","percent"],n}return w(e,t),e.prototype.init=function(t,e,n){var i=tc(t);this.settledOption=i,this.mergeDefaultAndTheme(t,n),this._doInit(i)},e.prototype.mergeOption=function(t){var e=tc(t);B(this.option,t,!0),B(this.settledOption,e,!0),this._doInit(e)},e.prototype._doInit=function(t){var e=this.option;this._setDefaultThrottle(t),this._updateRangeUse(t);var n=this.settledOption;p([["start","startValue"],["end","endValue"]],(function(t,i){"value"===this._rangePropMode[i]&&(e[t[0]]=n[t[0]]=null)}),this),this._resetTarget()},e.prototype._resetTarget=function(){var t=this.get("orient",!0),e=this._targetAxisInfoMap=ie();this._fillSpecifiedTargetAxis(e)?this._orient=t||this._makeAutoOrientByTargetAxis():(this._orient=t||"horizontal",this._fillAutoTargetAxisByOrient(e,this._orient)),this._noTarget=!0,e.each((function(t){t.indexList.length&&(this._noTarget=!1)}),this)},e.prototype._fillSpecifiedTargetAxis=function(t){var e=!1;return p(Uh,(function(n){var i=this.getReferringComponents(qh(n),yi);if(i.specified){e=!0;var o=new Qh;p(i.models,(function(t){o.add(t.componentIndex)})),t.set(n,o)}}),this),e},e.prototype._fillAutoTargetAxisByOrient=function(t,e){var n=this.ecModel,i=!0;if(i){var o="vertical"===e?"y":"x";a(n.findComponents({mainType:o+"Axis"}),o)}i&&a(n.findComponents({mainType:"singleAxis",filter:function(t){return t.get("orient",!0)===e}}),"single");function a(e,n){var o=e[0];if(o){var a=new Qh;if(a.add(o.componentIndex),t.set(n,a),i=!1,"x"===n||"y"===n){var r=o.getReferringComponents("grid",Ln).models[0];r&&p(e,(function(t){o.componentIndex!==t.componentIndex&&r===t.getReferringComponents("grid",Ln).models[0]&&a.add(t.componentIndex)}))}}}i&&p(Uh,(function(e){if(i){var o=n.findComponents({mainType:qh(e),filter:function(t){return"category"===t.get("type",!0)}});if(o[0]){var a=new Qh;a.add(o[0].componentIndex),t.set(e,a),i=!1}}}),this)},e.prototype._makeAutoOrientByTargetAxis=function(){var t;return this.eachTargetAxis((function(e){!t&&(t=e)}),this),"y"===t?"vertical":"horizontal"},e.prototype._setDefaultThrottle=function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var e=this.ecModel.option;this.option.throttle=e.animation&&e.animationDurationUpdate>0?100:20}},e.prototype._updateRangeUse=function(t){var e=this._rangePropMode,n=this.get("rangeMode");p([["start","startValue"],["end","endValue"]],(function(i,o){var a=null!=t[i[0]],r=null!=t[i[1]];a&&!r?e[o]="percent":!a&&r?e[o]="value":n?e[o]=n[o]:a&&(e[o]="percent")}))},e.prototype.noTarget=function(){return this._noTarget},e.prototype.getFirstTargetAxisModel=function(){var t;return this.eachTargetAxis((function(e,n){null==t&&(t=this.ecModel.getComponent(qh(e),n))}),this),t},e.prototype.eachTargetAxis=function(t,e){this._targetAxisInfoMap.each((function(n,i){p(n.indexList,(function(n){t.call(e,i,n)}))}))},e.prototype.getAxisProxy=function(t,e){var n=this.getAxisModel(t,e);if(n)return n.__dzAxisProxy},e.prototype.getAxisModel=function(t,e){var n=this._targetAxisInfoMap.get(t);if(n&&n.indexMap[e])return this.ecModel.getComponent(qh(t),e)},e.prototype.setRawRange=function(t){var e=this.option,n=this.settledOption;p([["start","startValue"],["end","endValue"]],(function(i){null==t[i[0]]&&null==t[i[1]]||(e[i[0]]=n[i[0]]=t[i[0]],e[i[1]]=n[i[1]]=t[i[1]])}),this),this._updateRangeUse(t)},e.prototype.setCalculatedRange=function(t){var e=this.option;p(["start","startValue","end","endValue"],(function(n){e[n]=t[n]}))},e.prototype.getPercentRange=function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},e.prototype.getValueRange=function(t,e){if(null!=t||null!=e)return this.getAxisProxy(t,e).getDataValueWindow();var n=this.findRepresentativeAxisProxy();return n?n.getDataValueWindow():void 0},e.prototype.findRepresentativeAxisProxy=function(t){if(t)return t.__dzAxisProxy;for(var e,n=this._targetAxisInfoMap.keys(),i=0;i<n.length;i++)for(var o=n[i],a=this._targetAxisInfoMap.get(o),r=0;r<a.indexList.length;r++){var s=this.getAxisProxy(o,a.indexList[r]);if(s.hostedBy(this))return s;e||(e=s)}return e},e.prototype.getRangePropMode=function(){return this._rangePropMode.slice()},e.prototype.getOrient=function(){return this._orient},e.type="dataZoom",e.dependencies=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","series","toolbox"],e.defaultOption={z:4,filterMode:"filter",start:0,end:100},e}(wt);function tc(t){var e={};return p(["start","end","startValue","endValue","throttle"],(function(n){t.hasOwnProperty(n)&&(e[n]=t[n])})),e}var ec=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.type="dataZoom.select",e}(Jh),nc=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.render=function(t,e,n,i){this.dataZoomModel=t,this.ecModel=e,this.api=n},e.type="dataZoom",e}(Tt),ic=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.type="dataZoom.select",e}(nc),oc=p,ac=dn,rc=function(){function t(t,e,n,i){this._dimName=t,this._axisIndex=e,this.ecModel=i,this._dataZoomModel=n}return t.prototype.hostedBy=function(t){return this._dataZoomModel===t},t.prototype.getDataValueWindow=function(){return this._valueWindow.slice()},t.prototype.getDataPercentWindow=function(){return this._percentWindow.slice()},t.prototype.getTargetSeriesModels=function(){var t=[];return this.ecModel.eachSeries((function(e){if(function(t){var e=t.get("coordinateSystem");return b(jh,e)>=0}(e)){var n=qh(this._dimName),i=e.getReferringComponents(n,Ln).models[0];i&&this._axisIndex===i.componentIndex&&t.push(e)}}),this),t},t.prototype.getAxisModel=function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},t.prototype.getMinMaxSpan=function(){return st(this._minMaxSpan)},t.prototype.calculateDataWindow=function(t){var e,n=this._dataExtent,i=this.getAxisModel().axis.scale,o=this._dataZoomModel.getRangePropMode(),a=[0,100],r=[],s=[];oc(["start","end"],(function(l,u){var d=t[l],h=t[l+"Value"];"percent"===o[u]?(null==d&&(d=a[u]),h=i.parse(Se(d,a,n))):(e=!0,h=null==h?n[u]:i.parse(h),d=Se(h,n,a)),s[u]=null==h||isNaN(h)?n[u]:h,r[u]=null==d||isNaN(d)?a[u]:d})),ac(s),ac(r);var l=this._minMaxSpan;function u(t,e,n,o,a){var r=a?"Span":"ValueSpan";vi(0,t,n,"all",l["min"+r],l["max"+r]);for(var s=0;s<2;s++)e[s]=Se(t[s],n,o,!0),a&&(e[s]=i.parse(e[s]))}return e?u(s,r,n,a,!1):u(r,s,a,n,!0),{valueWindow:s,percentWindow:r}},t.prototype.reset=function(t){if(t===this._dataZoomModel){var e=this.getTargetSeriesModels();this._dataExtent=function(t,e,n){var i=[1/0,-1/0];oc(n,(function(t){xi(i,t.getData(),e)}));var o=t.getAxisModel(),a=_i(o.axis.scale,o,i).calculate();return[a.min,a.max]}(this,this._dimName,e),this._updateMinMaxSpan();var n=this.calculateDataWindow(t.settledOption);this._valueWindow=n.valueWindow,this._percentWindow=n.percentWindow,this._setAxisModel()}},t.prototype.filterData=function(t,e){if(t===this._dataZoomModel){var n=this._dimName,i=this.getTargetSeriesModels(),o=t.get("filterMode"),a=this._valueWindow;"none"!==o&&oc(i,(function(t){var e=t.getData(),i=e.mapDimensionsAll(n);if(i.length){if("weakFilter"===o){var r=e.getStore(),s=ht(i,(function(t){return e.getDimensionIndex(t)}),e);e.filterSelf((function(t){for(var e,n,o,l=0;l<i.length;l++){var u=r.get(s[l],t),d=!isNaN(u),h=u<a[0],c=u>a[1];if(d&&!h&&!c)return!0;d&&(o=!0),h&&(e=!0),c&&(n=!0)}return o&&e&&n}))}else oc(i,(function(n){if("empty"===o)t.setData(e=e.map(n,(function(t){return function(t){return t>=a[0]&&t<=a[1]}(t)?t:NaN})));else{var i={};i[n]=a,e.selectRange(i)}}));oc(i,(function(t){e.setApproximateExtent(a,t)}))}}))}},t.prototype._updateMinMaxSpan=function(){var t=this._minMaxSpan={},e=this._dataZoomModel,n=this._dataExtent;oc(["min","max"],(function(i){var o=e.get(i+"Span"),a=e.get(i+"ValueSpan");null!=a&&(a=this.getAxisModel().axis.scale.parse(a)),null!=a?o=Se(n[0]+a,n,[0,100],!0):null!=o&&(a=Se(o,[0,100],n,!0)-n[0]),t[i+"Span"]=o,t[i+"ValueSpan"]=a}),this)},t.prototype._setAxisModel=function(){var t=this.getAxisModel(),e=this._percentWindow,n=this._valueWindow;if(e){var i=mi(n,[0,500]);i=Math.min(i,20);var o=t.axis.scale.rawExtentInfo;0!==e[0]&&o.setDeterminedMinMax("min",+n[0].toFixed(i)),100!==e[1]&&o.setDeterminedMinMax("max",+n[1].toFixed(i)),o.freeze()}},t}();var sc={getTargetSeries:function(t){function e(e){t.eachComponent("dataZoom",(function(n){n.eachTargetAxis((function(i,o){var a=t.getComponent(qh(i),o);e(i,o,a,n)}))}))}e((function(t,e,n,i){n.__dzAxisProxy=null}));var n=[];e((function(e,i,o,a){o.__dzAxisProxy||(o.__dzAxisProxy=new rc(e,i,a,t),n.push(o.__dzAxisProxy))}));var i=ie();return p(n,(function(t){p(t.getTargetSeriesModels(),(function(t){i.set(t.uid,t)}))})),i},overallReset:function(t,e){t.eachComponent("dataZoom",(function(t){t.eachTargetAxis((function(e,n){t.getAxisProxy(e,n).reset(t)})),t.eachTargetAxis((function(n,i){t.getAxisProxy(n,i).filterData(t,e)}))})),t.eachComponent("dataZoom",(function(t){var e=t.findRepresentativeAxisProxy();if(e){var n=e.getDataPercentWindow(),i=e.getDataValueWindow();t.setCalculatedRange({start:n[0],end:n[1],startValue:i[0],endValue:i[1]})}}))}};var lc=!1;function uc(t){lc||(lc=!0,t.registerProcessor(t.PRIORITY.PROCESSOR.FILTER,sc),function(t){t.registerAction("dataZoom",(function(t,e){var n=Kh(e,t);p(n,(function(e){e.setRawRange({start:t.start,end:t.end,startValue:t.startValue,endValue:t.endValue})}))}))}(t),t.registerSubTypeDefaulter("dataZoom",(function(){return"slider"})))}function dc(t){t.registerComponentModel(ec),t.registerComponentView(ic),uc(t)}var hc=function(){return function(){}}(),cc={};function pc(t,e){cc[t]=e}function fc(t){return cc[t]}var gc=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.optionUpdated=function(){t.prototype.optionUpdated.apply(this,arguments);var e=this.ecModel;p(this.option.feature,(function(t,n){var i=fc(n);i&&(i.getDefaultOption&&(i.defaultOption=i.getDefaultOption(e)),B(t,i.defaultOption))}))},e.type="toolbox",e.layoutMode={type:"box",ignoreSize:!0},e.defaultOption={show:!0,z:6,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1,position:"bottom"}},e}(wt),yc=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.render=function(t,e,n,i){var a=this.group;if(a.removeAll(),t.get("show")){var r=+t.get("itemSize"),s="vertical"===t.get("orient"),l=t.get("feature")||{},u=this._features||(this._features={}),d=[];p(l,(function(t,e){d.push(e)})),new de(this._featureNames||[],d).add(h).update(h).remove(qt(h,null)).execute(),this._featureNames=d,Ii(a,t,n),a.add(wi(a.getBoundingRect(),t)),s||a.eachChild((function(t){var e=t.__title,i=t.ensureState("emphasis"),s=i.textConfig||(i.textConfig={}),l=t.getTextContent(),u=l&&l.ensureState("emphasis");if(u&&!o(u)&&e){var d=u.style||(u.style={}),h=Mi(e,se.makeFont(d)),c=t.x+a.x,p=!1;t.y+a.y+r+h.height>n.getHeight()&&(s.position="top",p=!0);var f=p?-5-h.height:r+10;c+h.width/2>n.getWidth()?(s.position=["100%",f],d.align="right"):c-h.width/2<0&&(s.position=[0,f],d.align="left")}}))}function h(o,h){var c,f=d[o],g=d[h],y=l[f],v=new _t(y,t,t.ecModel);if(i&&null!=i.newTitle&&i.featureName===f&&(y.title=i.newTitle),f&&!g){if(function(t){return 0===t.indexOf("my")}(f))c={onclick:v.option.onclick,featureName:f};else{var m=fc(f);if(!m)return;c=new m}u[f]=c}else if(!(c=u[g]))return;c.uid=bi("toolbox-feature"),c.model=v,c.ecModel=e,c.api=n;var x=c instanceof hc;f||!g?!v.get("show")||x&&c.unusable?x&&c.remove&&c.remove(e,n):(!function(i,o,l){var u,d,h=i.getModel("iconStyle"),c=i.getModel(["emphasis","iconStyle"]),f=o instanceof hc&&o.getIcons?o.getIcons():i.get("icon"),g=i.get("title")||{};xt(f)?(u={})[l]=f:u=f;xt(g)?(d={})[l]=g:d=g;var y=i.iconPaths={};p(u,(function(l,u){var p=Si(l,{},{x:-r/2,y:-r/2,width:r,height:r});p.setStyle(h.getItemStyle()),p.ensureState("emphasis").style=c.getItemStyle();var f=new se({style:{text:d[u],align:c.get("textAlign"),borderRadius:c.get("textBorderRadius"),padding:c.get("textPadding"),fill:null,font:Fn({fontStyle:c.get("textFontStyle"),fontFamily:c.get("textFontFamily"),fontSize:c.get("textFontSize"),fontWeight:c.get("textFontWeight")},e)},ignore:!0});p.setTextContent(f),fi({el:p,componentModel:t,itemName:u,formatterParamsExtra:{title:d[u]}}),p.__title=d[u],p.on("mouseover",(function(){var e=c.getItemStyle(),i=s?null==t.get("right")&&"right"!==t.get("left")?"right":"left":null==t.get("bottom")&&"bottom"!==t.get("top")?"bottom":"top";f.setStyle({fill:c.get("textFill")||e.fill||e.stroke||"#000",backgroundColor:c.get("textBackgroundColor")}),p.setTextConfig({position:c.get("textPosition")||i}),f.ignore=!t.get("showTitle"),n.enterEmphasis(this)})).on("mouseout",(function(){"emphasis"!==i.get(["iconStatus",u])&&n.leaveEmphasis(this),f.hide()})),("emphasis"===i.get(["iconStatus",u])?We:Ze)(p),a.add(p),p.on("click",gt(o.onclick,o,e,n,u)),y[u]=p}))}(v,c,f),v.setIconStatus=function(t,e){var n=this.option,i=this.iconPaths;n.iconStatus=n.iconStatus||{},n.iconStatus[t]=e,i[t]&&("emphasis"===e?We:Ze)(i[t])},c instanceof hc&&c.render&&c.render(v,e,n,i)):x&&c.dispose&&c.dispose(e,n)}},e.prototype.updateView=function(t,e,n,i){p(this._features,(function(t){t instanceof hc&&t.updateView&&t.updateView(t.model,e,n,i)}))},e.prototype.remove=function(t,e){p(this._features,(function(n){n instanceof hc&&n.remove&&n.remove(t,e)})),this.group.removeAll()},e.prototype.dispose=function(t,e){p(this._features,(function(n){n instanceof hc&&n.dispose&&n.dispose(t,e)}))},e.type="toolbox",e}(Tt);var vc=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.onclick=function(t,e){var n=this.model,i=n.get("name")||t.get("title.0.text")||"echarts",o="svg"===e.getZr().painter.getType(),a=o?"svg":n.get("type",!0)||"png",r=e.getConnectedDataURL({type:a,backgroundColor:n.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",connectedBackgroundColor:n.get("connectedBackgroundColor"),excludeComponents:n.get("excludeComponents"),pixelRatio:n.get("pixelRatio")}),s=E.browser;if("function"!=typeof MouseEvent||!s.newEdge&&(s.ie||s.edge))if(window.navigator.msSaveOrOpenBlob||o){var l=r.split(","),u=l[0].indexOf("base64")>-1,d=o?decodeURIComponent(l[1]):l[1];u&&(d=window.atob(d));var h=i+"."+a;if(window.navigator.msSaveOrOpenBlob){for(var c=d.length,p=new Uint8Array(c);c--;)p[c]=d.charCodeAt(c);var f=new Blob([p]);window.navigator.msSaveOrOpenBlob(f,h)}else{var g=document.createElement("iframe");document.body.appendChild(g);var y=g.contentWindow,v=y.document;v.open("image/svg+xml","replace"),v.write(d),v.close(),y.focus(),v.execCommand("SaveAs",!0,h),document.body.removeChild(g)}}else{var m=n.get("lang"),x='<body style="margin:0;"><img src="'+r+'" style="max-width:100%;" title="'+(m&&m[0]||"")+'" /></body>',_=window.open();_.document.write(x),_.document.title=i}else{var b=document.createElement("a");b.download=i+"."+a,b.target="_blank",b.href=r;var S=new MouseEvent("click",{view:document.defaultView,bubbles:!0,cancelable:!1});b.dispatchEvent(S)}},e.getDefaultOption=function(t){return{show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:t.getLocaleModel().get(["toolbox","saveAsImage","title"]),type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],lang:t.getLocaleModel().get(["toolbox","saveAsImage","lang"])}},e}(hc),mc="__ec_magicType_stack__",xc=[["line","bar"],["stack"]],_c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.getIcons=function(){var t=this.model,e=t.get("icon"),n={};return p(t.get("type"),(function(t){e[t]&&(n[t]=e[t])})),n},e.getDefaultOption=function(t){return{show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:t.getLocaleModel().get(["toolbox","magicType","title"]),option:{},seriesIndex:{}}},e.prototype.onclick=function(t,e,n){var i=this.model,o=i.get(["seriesIndex",n]);if(bc[n]){var a,r={series:[]};p(xc,(function(t){b(t,n)>=0&&p(t,(function(t){i.setIconStatus(t,"normal")}))})),i.setIconStatus(n,"emphasis"),t.eachComponent({mainType:"series",query:null==o?null:{seriesIndex:o}},(function(t){var e=t.subType,o=t.id,a=bc[n](e,o,t,i);a&&(ot(a,t.option),r.series.push(a));var s=t.coordinateSystem;if(s&&"cartesian2d"===s.type&&("line"===n||"bar"===n)){var l=s.getAxesByScale("ordinal")[0];if(l){var u=l.dim+"Axis",d=t.getReferringComponents(u,Ln).models[0].componentIndex;r[u]=r[u]||[];for(var h=0;h<=d;h++)r[u][d]=r[u][d]||{};r[u][d].boundaryGap="bar"===n}}}));var s=n;"stack"===n&&(a=B({stack:i.option.title.tiled,tiled:i.option.title.stack},i.option.title),"emphasis"!==i.get(["iconStatus",n])&&(s="tiled")),e.dispatchAction({type:"changeMagicType",currentType:s,newOption:r,newTitle:a,featureName:"magicType"})}},e}(hc),bc={line:function(t,e,n,i){if("bar"===t)return B({id:e,type:"line",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get(["option","line"])||{},!0)},bar:function(t,e,n,i){if("line"===t)return B({id:e,type:"bar",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get(["option","bar"])||{},!0)},stack:function(t,e,n,i){var o=n.get("stack")===mc;if("line"===t||"bar"===t)return i.setIconStatus("stack",o?"normal":"emphasis"),B({id:e,stack:o?"":mc},i.get(["option","stack"])||{},!0)}};Li({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},(function(t,e){e.mergeOption(t.newOption)}));var Sc=new Array(60).join("-"),Ic="\t";function wc(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}var Mc=new RegExp("[\t]+","g");function Lc(t,e){var n=t.split(new RegExp("\n*"+Sc+"\n*","g")),i={series:[]};return p(n,(function(t,n){if(function(t){if(t.slice(0,t.indexOf("\n")).indexOf(Ic)>=0)return!0}(t)){var o=function(t){for(var e=t.split(/\n+/g),n=wc(e.shift()).split(Mc),i=[],o=ht(n,(function(t){return{name:t,data:[]}})),a=0;a<e.length;a++){var r=wc(e[a]).split(Mc);i.push(r.shift());for(var s=0;s<r.length;s++)o[s]&&(o[s].data[a]=r[s])}return{series:o,categories:i}}(t),a=e[n],r=a.axisDim+"Axis";a&&(i[r]=i[r]||[],i[r][a.axisIndex]={data:o.categories},i.series=i.series.concat(o.series))}else{o=function(t){for(var e=t.split(/\n+/g),n=wc(e.shift()),i=[],o=0;o<e.length;o++){var a=wc(e[o]);if(a){var r=a.split(Mc),s="",l=void 0,u=!1;isNaN(r[0])?(u=!0,s=r[0],r=r.slice(1),i[o]={name:s,value:[]},l=i[o].value):l=i[o]=[];for(var d=0;d<r.length;d++)l.push(+r[d]);1===l.length&&(u?i[o].value=l[0]:i[o]=l[0])}}return{name:n,data:i}}(t);i.series.push(o)}})),i}var Dc=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.onclick=function(t,e){setTimeout((function(){e.dispatchAction({type:"hideTip"})}));var n=e.getDom(),i=this.model;this._dom&&n.removeChild(this._dom);var a=document.createElement("div");a.style.cssText="position:absolute;top:0;bottom:0;left:0;right:0;padding:5px",a.style.backgroundColor=i.get("backgroundColor")||"#fff";var r=document.createElement("h4"),s=i.get("lang")||[];r.innerHTML=s[0]||i.get("title"),r.style.cssText="margin:10px 20px",r.style.color=i.get("textColor");var l=document.createElement("div"),d=document.createElement("textarea");l.style.cssText="overflow:auto";var h=i.get("optionToContent"),c=i.get("contentToOption"),f=function(t){var e,n,i,o=function(t){var e={},n=[],i=[];return t.eachRawSeries((function(t){var o=t.coordinateSystem;if(!o||"cartesian2d"!==o.type&&"polar"!==o.type)n.push(t);else{var a=o.getBaseAxis();if("category"===a.type){var r=a.dim+"_"+a.index;e[r]||(e[r]={categoryAxis:a,valueAxis:o.getOtherAxis(a),series:[]},i.push({axisDim:a.dim,axisIndex:a.index})),e[r].series.push(t)}else n.push(t)}})),{seriesGroupByCategoryAxis:e,other:n,meta:i}}(t);return{value:u([(n=o.seriesGroupByCategoryAxis,i=[],p(n,(function(t,e){var n=t.categoryAxis,o=t.valueAxis.dim,a=[" "].concat(ht(t.series,(function(t){return t.name}))),r=[n.model.getCategories()];p(t.series,(function(t){var e=t.getRawData();r.push(t.getRawData().mapArray(e.mapDimension(o),(function(t){return t})))}));for(var s=[a.join(Ic)],l=0;l<r[0].length;l++){for(var u=[],d=0;d<r.length;d++)u.push(r[d][l]);s.push(u.join(Ic))}i.push(s.join("\n"))})),i.join("\n\n"+Sc+"\n\n")),(e=o.other,ht(e,(function(t){var e=t.getRawData(),n=[t.name],i=[];return e.each(e.dimensions,(function(){for(var t=arguments.length,o=arguments[t-1],a=e.getName(o),r=0;r<t-1;r++)i[r]=arguments[r];n.push((a?a+Ic:"")+i.join(Ic))})),n.join("\n")})).join("\n\n"+Sc+"\n\n"))],(function(t){return!!t.replace(/[\n\t\s]/g,"")})).join("\n\n"+Sc+"\n\n"),meta:o.meta}}(t);if(o(h)){var g=h(e.getOption());xt(g)?l.innerHTML=g:Di(g)&&l.appendChild(g)}else{d.readOnly=i.get("readOnly");var y=d.style;y.cssText="display:block;width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;resize:none;box-sizing:border-box;outline:none",y.color=i.get("textColor"),y.borderColor=i.get("textareaBorderColor"),y.backgroundColor=i.get("textareaColor"),d.value=f.value,l.appendChild(d)}var v=f.meta,m=document.createElement("div");m.style.cssText="position:absolute;bottom:5px;left:0;right:0";var x="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",_=document.createElement("div"),b=document.createElement("div");x+=";background-color:"+i.get("buttonColor"),x+=";color:"+i.get("buttonTextColor");var S=this;function I(){n.removeChild(a),S._dom=null}Ci(_,"click",I),Ci(b,"click",(function(){if(null==c&&null!=h||null!=c&&null==h)I();else{var t;try{t=o(c)?c(l,e.getOption()):Lc(d.value,v)}catch(n){throw I(),new Error("Data view format error "+n)}t&&e.dispatchAction({type:"changeDataView",newOption:t}),I()}})),_.innerHTML=s[1],b.innerHTML=s[2],b.style.cssText=_.style.cssText=x,!i.get("readOnly")&&m.appendChild(b),m.appendChild(_),a.appendChild(r),a.appendChild(l),a.appendChild(m),l.style.height=n.clientHeight-80+"px",n.appendChild(a),this._dom=a},e.prototype.remove=function(t,e){this._dom&&e.getDom().removeChild(this._dom)},e.prototype.dispose=function(t,e){this.remove(t,e)},e.getDefaultOption=function(t){return{show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:t.getLocaleModel().get(["toolbox","dataView","title"]),lang:t.getLocaleModel().get(["toolbox","dataView","lang"]),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"}},e}(hc);function Cc(t,e){return ht(t,(function(t,n){var i=e&&e[n];if(M(i)&&!tt(i)){M(t)&&!tt(t)||(t={value:t});var o=null!=i.name&&null==t.name;return t=ot(t,i),o&&delete t.name,t}return t}))}Li({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},(function(t,e){var n=[];p(t.newOption.series,(function(t){var i=e.getSeriesByName(t.name)[0];if(i){var o=i.get("data");n.push({name:t.name,data:Cc(t.data,o)})}else n.push(F({type:"scatter"},t))})),e.mergeOption(ot({series:n},t.newOption))}));var Tc=p,Ac=t();function Pc(t){var e=Ac(t);return e.snapshots||(e.snapshots=[{}]),e.snapshots}var kc=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.onclick=function(t,e){!function(t){Ac(t).snapshots=null}(t),e.dispatchAction({type:"restore",from:this.uid})},e.getDefaultOption=function(t){return{show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:t.getLocaleModel().get(["toolbox","restore","title"])}},e}(hc);Li({type:"restore",event:"restore",update:"prepareAndUpdate"},(function(t,e){e.resetOption("recreate")}));var Nc=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"],Vc=function(){function t(t,e,n){var i=this;this._targetInfoList=[];var o=zc(e,t);p(Oc,(function(t,e){(!n||!n.include||b(n.include,e)>=0)&&t(o,i._targetInfoList)}))}return t.prototype.setOutputRanges=function(t,e){return this.matchOutputRanges(t,e,(function(t,e,n){if((t.coordRanges||(t.coordRanges=[])).push(e),!t.coordRange){t.coordRange=e;var i=Gc[t.brushType](0,n,e);t.__rangeOffset={offset:Fc[t.brushType](i.values,t.range,[1,1]),xyMinMax:i.xyMinMax}}})),t},t.prototype.matchOutputRanges=function(t,e,n){p(t,(function(t){var i=this.findTargetInfo(t,e);i&&!0!==i&&p(i.coordSyses,(function(i){var o=Gc[t.brushType](1,i,t.range,!0);n(t,o.values,i,e)}))}),this)},t.prototype.setInputRanges=function(t,e){p(t,(function(t){var n,i,o,a,r,s=this.findTargetInfo(t,e);if(t.range=t.range||[],s&&!0!==s){t.panelId=s.panelId;var l=Gc[t.brushType](0,s.coordSys,t.coordRange),u=t.__rangeOffset;t.range=u?Fc[t.brushType](l.values,u.offset,(n=l.xyMinMax,i=u.xyMinMax,o=Zc(n),a=Zc(i),r=[o[0]/a[0],o[1]/a[1]],isNaN(r[0])&&(r[0]=1),isNaN(r[1])&&(r[1]=1),r)):l.values}}),this)},t.prototype.makePanelOpts=function(t,e){return ht(this._targetInfoList,(function(n){var i=n.getPanelRect();return{panelId:n.panelId,defaultBrushType:e?e(n):null,clipPath:Ni(i),isTargetByCursor:ki(i,t,n.coordSysModel),getLinearBrushOtherExtent:Pi(i)}}))},t.prototype.controlSeries=function(t,e,n){var i=this.findTargetInfo(t,n);return!0===i||i&&b(i.coordSyses,e.coordinateSystem)>=0},t.prototype.findTargetInfo=function(t,e){for(var n=this._targetInfoList,i=zc(e,t),o=0;o<n.length;o++){var a=n[o],r=t.panelId;if(r){if(a.panelId===r)return a}else for(var s=0;s<Ec.length;s++)if(Ec[s](i,a))return a}return!0},t}();function Rc(t){return t[0]>t[1]&&t.reverse(),t}function zc(t,e){return Ti(t,e,{includeMainTypes:Nc})}var Oc={grid:function(t,e){var n=t.xAxisModels,i=t.yAxisModels,o=t.gridModels,a=ie(),r={},s={};(n||i||o)&&(p(n,(function(t){var e=t.axis.grid.model;a.set(e.id,e),r[e.id]=!0})),p(i,(function(t){var e=t.axis.grid.model;a.set(e.id,e),s[e.id]=!0})),p(o,(function(t){a.set(t.id,t),r[t.id]=!0,s[t.id]=!0})),a.each((function(t){var o=t.coordinateSystem,a=[];p(o.getCartesians(),(function(t,e){(b(n,t.getAxis("x").model)>=0||b(i,t.getAxis("y").model)>=0)&&a.push(t)})),e.push({panelId:"grid--"+t.id,gridModel:t,coordSysModel:t,coordSys:a[0],coordSyses:a,getPanelRect:Bc.grid,xAxisDeclared:r[t.id],yAxisDeclared:s[t.id]})})))},geo:function(t,e){p(t.geoModels,(function(t){var n=t.coordinateSystem;e.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:n,coordSyses:[n],getPanelRect:Bc.geo})}))}},Ec=[function(t,e){var n=t.xAxisModel,i=t.yAxisModel,o=t.gridModel;return!o&&n&&(o=n.axis.grid.model),!o&&i&&(o=i.axis.grid.model),o&&o===e.gridModel},function(t,e){var n=t.geoModel;return n&&n===e.geoModel}],Bc={grid:function(){return this.coordSys.master.getRect().clone()},geo:function(){var t=this.coordSys,e=t.getBoundingRect().clone();return e.applyTransform(Ai(t)),e}},Gc={lineX:qt(Hc,0),lineY:qt(Hc,1),rect:function(t,e,n,i){var o=t?e.pointToData([n[0][0],n[1][0]],i):e.dataToPoint([n[0][0],n[1][0]],i),a=t?e.pointToData([n[0][1],n[1][1]],i):e.dataToPoint([n[0][1],n[1][1]],i),r=[Rc([o[0],a[0]]),Rc([o[1],a[1]])];return{values:r,xyMinMax:r}},polygon:function(t,e,n,i){var o=[[1/0,-1/0],[1/0,-1/0]];return{values:ht(n,(function(n){var a=t?e.pointToData(n,i):e.dataToPoint(n,i);return o[0][0]=Math.min(o[0][0],a[0]),o[1][0]=Math.min(o[1][0],a[1]),o[0][1]=Math.max(o[0][1],a[0]),o[1][1]=Math.max(o[1][1],a[1]),a})),xyMinMax:o}}};function Hc(t,e,n,i){var o=n.getAxis(["x","y"][t]),a=Rc(ht([0,1],(function(t){return e?o.coordToData(o.toLocalCoord(i[t]),!0):o.toGlobalCoord(o.dataToCoord(i[t]))}))),r=[];return r[t]=a,r[1-t]=[NaN,NaN],{values:a,xyMinMax:r}}var Fc={lineX:qt(Wc,0),lineY:qt(Wc,1),rect:function(t,e,n){return[[t[0][0]-n[0]*e[0][0],t[0][1]-n[0]*e[0][1]],[t[1][0]-n[1]*e[1][0],t[1][1]-n[1]*e[1][1]]]},polygon:function(t,e,n){return ht(t,(function(t,i){return[t[0]-n[0]*e[i][0],t[1]-n[1]*e[i][1]]}))}};function Wc(t,e,n,i){return[e[0]-i[t]*n[0],e[1]-i[t]*n[1]]}function Zc(t){return t?[t[0][1]-t[0][0],t[1][1]-t[1][0]]:[NaN,NaN]}var Yc=p,Xc=zi("toolbox-dataZoom_"),Uc=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.render=function(t,e,n,i){this._brushController||(this._brushController=new Vi(n.getZr()),this._brushController.on("brush",gt(this._onBrush,this)).mount()),function(t,e,n,i,o){var a=n._isZoomActive;i&&"takeGlobalCursor"===i.type&&(a="dataZoomSelect"===i.key&&i.dataZoomSelectActive);n._isZoomActive=a,t.setIconStatus("zoom",a?"emphasis":"normal");var r=new Vc(qc(t),e,{include:["grid"]}),s=r.makePanelOpts(o,(function(t){return t.xAxisDeclared&&!t.yAxisDeclared?"lineX":!t.xAxisDeclared&&t.yAxisDeclared?"lineY":"rect"}));n._brushController.setPanels(s).enableBrush(!(!a||!s.length)&&{brushType:"auto",brushStyle:t.getModel("brushStyle").getItemStyle()})}(t,e,this,i,n),function(t,e){t.setIconStatus("back",function(t){return Pc(t).length}(e)>1?"emphasis":"normal")}(t,e)},e.prototype.onclick=function(t,e,n){jc[n].call(this)},e.prototype.remove=function(t,e){this._brushController&&this._brushController.unmount()},e.prototype.dispose=function(t,e){this._brushController&&this._brushController.dispose()},e.prototype._onBrush=function(t){var e=t.areas;if(t.isEnd&&e.length){var n={},i=this.ecModel;this._brushController.updateCovers([]),new Vc(qc(this.model),i,{include:["grid"]}).matchOutputRanges(e,i,(function(t,e,n){if("cartesian2d"===n.type){var i=t.brushType;"rect"===i?(o("x",n,e[0]),o("y",n,e[1])):o({lineX:"x",lineY:"y"}[i],n,e)}})),function(t,e){var n=Pc(t);Tc(e,(function(e,i){for(var o=n.length-1;o>=0&&!n[o][i];o--);if(o<0){var a=t.queryComponents({mainType:"dataZoom",subType:"select",id:i})[0];if(a){var r=a.getPercentRange();n[0][i]={dataZoomId:i,start:r[0],end:r[1]}}}})),n.push(e)}(i,n),this._dispatchZoomAction(n)}function o(t,e,o){var a=e.getAxis(t),r=a.model,s=function(t,e,n){var i;return n.eachComponent({mainType:"dataZoom",subType:"select"},(function(n){n.getAxisModel(t,e.componentIndex)&&(i=n)})),i}(t,r,i),l=s.findRepresentativeAxisProxy(r).getMinMaxSpan();null==l.minValueSpan&&null==l.maxValueSpan||(o=vi(0,o.slice(),a.scale.getExtent(),0,l.minValueSpan,l.maxValueSpan)),s&&(n[s.id]={dataZoomId:s.id,startValue:o[0],endValue:o[1]})}},e.prototype._dispatchZoomAction=function(t){var e=[];Yc(t,(function(t,n){e.push(st(t))})),e.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:e})},e.getDefaultOption=function(t){return{show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:t.getLocaleModel().get(["toolbox","dataZoom","title"]),brushStyle:{borderWidth:0,color:"rgba(210,219,238,0.2)"}}},e}(hc),jc={zoom:function(){var t=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:t})},back:function(){this._dispatchZoomAction(function(t){var e=Pc(t),n=e[e.length-1];e.length>1&&e.pop();var i={};return Tc(n,(function(t,n){for(var o=e.length-1;o>=0;o--)if(t=e[o][n]){i[n]=t;break}})),i}(this.ecModel))}};function qc(t){var e={xAxisIndex:t.get("xAxisIndex",!0),yAxisIndex:t.get("yAxisIndex",!0),xAxisId:t.get("xAxisId",!0),yAxisId:t.get("yAxisId",!0)};return null==e.xAxisIndex&&null==e.xAxisId&&(e.xAxisIndex="all"),null==e.yAxisIndex&&null==e.yAxisId&&(e.yAxisIndex="all"),e}Ri("dataZoom",(function(t){var e=t.getComponent("toolbox",0),n=["feature","dataZoom"];if(e&&null!=e.get(n)){var i=e.getModel(n),o=[],a=qc(i),r=Ti(t,a);return Yc(r.xAxisModels,(function(t){return s(t,"xAxis","xAxisIndex")})),Yc(r.yAxisModels,(function(t){return s(t,"yAxis","yAxisIndex")})),o}function s(t,e,n){var a=t.componentIndex,r={type:"select",$fromToolbox:!0,filterMode:i.get("filterMode",!0)||"filter",id:Xc+e+a};r[n]=a,o.push(r)}}));var Kc=["rect","polygon","keep","clear"];function $c(t,e){var n=oe(t?t.brush:[]);if(n.length){var i=[];p(n,(function(t){var e=t.hasOwnProperty("toolbox")?t.toolbox:[];e instanceof Array&&(i=i.concat(e))}));var o=t&&t.toolbox;tt(o)&&(o=o[0]),o||(o={feature:{}},t.toolbox=[o]);var a,r,s=o.feature||(o.feature={}),l=s.brush||(s.brush={}),u=l.type||(l.type=[]);u.push.apply(u,i),r={},p(a=u,(function(t){r[t]=1})),a.length=0,p(r,(function(t,e){a.push(e)})),e&&!u.length&&u.push.apply(u,Kc)}}var Qc=p;function Jc(t){if(t)for(var e in t)if(t.hasOwnProperty(e))return!0}function tp(t,e,n){var i={};return Qc(e,(function(e){var o,a=i[e]=((o=function(){}).prototype.__hidden=o.prototype,new o);Qc(t[e],(function(t,i){if(Cr.isValidType(i)){var o={type:i,visual:t};n&&n(o,e),a[i]=new Cr(o),"opacity"===i&&((o=st(o)).type="colorAlpha",a.__hidden.__alphaForOpacity=new Cr(o))}}))})),i}function ep(t,e,n){var i;p(n,(function(t){e.hasOwnProperty(t)&&Jc(e[t])&&(i=!0)})),i&&p(n,(function(n){e.hasOwnProperty(n)&&Jc(e[n])?t[n]=st(e[n]):delete t[n]}))}var np={lineX:ip(0),lineY:ip(1),rect:{point:function(t,e,n){return t&&n.boundingRect.contain(t[0],t[1])},rect:function(t,e,n){return t&&n.boundingRect.intersect(t)}},polygon:{point:function(t,e,n){return t&&n.boundingRect.contain(t[0],t[1])&&Bi(n.range,t[0],t[1])},rect:function(t,n,i){var o=i.range;if(!t||o.length<=1)return!1;var a=t.x,r=t.y,s=t.width,l=t.height,u=o[0];return!!(Bi(o,a,r)||Bi(o,a+s,r)||Bi(o,a,r+l)||Bi(o,a+s,r+l)||e.create(t).contain(u[0],u[1])||Gi(a,r,a+s,r,o)||Gi(a,r,a,r+l,o)||Gi(a+s,r,a+s,r+l,o)||Gi(a,r+l,a+s,r+l,o))||void 0}}};function ip(t){var e=["x","y"],n=["width","height"];return{point:function(e,n,i){if(e){var o=i.range;return op(e[t],o)}},rect:function(i,o,a){if(i){var r=a.range,s=[i[e[t]],i[e[t]]+i[n[t]]];return s[1]<s[0]&&s.reverse(),op(s[0],r)||op(s[1],r)||op(r[0],s)||op(r[1],s)}}}}function op(t,e){return e[0]<=t&&t<=e[1]}var ap=["inBrush","outOfBrush"],rp="__ecBrushSelect",sp="__ecInBrushSelectEvent";function lp(t){t.eachComponent({mainType:"brush"},(function(e){(e.brushTargetManager=new Vc(e.option,t)).setInputRanges(e.areas,t)}))}function up(t,e,n){var i,o,a=[];t.eachComponent({mainType:"brush"},(function(t){n&&"takeGlobalCursor"===n.type&&t.setBrushOption("brush"===n.key?n.brushOption:{brushType:!1})})),lp(t),t.eachComponent({mainType:"brush"},(function(e,n){var r={brushId:e.id,brushIndex:n,brushName:e.name,areas:st(e.areas),selected:[]};a.push(r);var s=e.option,l=s.brushLink,u=[],d=[],h=[],c=!1;n||(i=s.throttleType,o=s.throttleDelay);var f=ht(e.areas,(function(t){var e=cp[t.brushType],n=ot({boundingRect:e?e(t):void 0},t);return n.selectors=function(t){var e=t.brushType,n={point:function(i){return np[e].point(i,n,t)},rect:function(i){return np[e].rect(i,n,t)}};return n}(n),n})),g=tp(e.option,ap,(function(t){t.mappingMethod="fixed"}));function y(t){return"all"===l||!!u[t]}function v(t){return!!t.length}tt(l)&&p(l,(function(t){u[t]=1})),t.eachSeries((function(n,i){var o=h[i]=[];"parallel"===n.subType?function(t,e){var n=t.coordinateSystem;c=c||n.hasAxisBrushed(),y(e)&&n.eachActiveState(t.getData(),(function(t,e){"active"===t&&(d[e]=1)}))}(n,i):function(n,i,o){if(!n.brushSelector||function(t,e){var n=t.option.seriesIndex;return null!=n&&"all"!==n&&(tt(n)?b(n,e)<0:e!==n)}(e,i))return;if(p(f,(function(i){e.brushTargetManager.controlSeries(i,n,t)&&o.push(i),c=c||v(o)})),y(i)&&v(o)){var a=n.getData();a.each((function(t){hp(n,o,a,t)&&(d[t]=1)}))}}(n,i,o)})),t.eachSeries((function(t,e){var n={seriesId:t.id,seriesIndex:e,seriesName:t.name,dataIndex:[]};r.selected.push(n);var i=h[e],o=t.getData(),a=y(e)?function(t){return d[t]?(n.dataIndex.push(o.getRawIndex(t)),"inBrush"):"outOfBrush"}:function(e){return hp(t,i,o,e)?(n.dataIndex.push(o.getRawIndex(e)),"inBrush"):"outOfBrush"};(y(e)?c:v(i))&&function(t,e,n,i,o){var a,r={};function s(t){return Oi(n,a,t)}function l(t,e){Ei(n,a,t,e)}p(t,(function(t){var n=Cr.prepareVisualTypes(e[t]);r[t]=n})),n.each((function(t,u){a=t;var d=n.getRawDataItem(a);if(!d||!1!==d.visualMap)for(var h=i.call(o,t),c=e[h],p=r[h],f=0,g=p.length;f<g;f++){var y=p[f];c[y]&&c[y].applyVisual(t,s,l)}}))}(ap,g,o,a)}))})),function(t,e,n,i,o){if(!o)return;var a=t.getZr();if(a[sp])return;a[rp]||(a[rp]=dp);var r=Hi(a,rp,n,e);r(t,i)}(e,i,o,a,n)}function dp(t,e){if(!t.isDisposed()){var n=t.getZr();n[sp]=!0,t.dispatchAction({type:"brushSelect",batch:e}),n[sp]=!1}}function hp(t,e,n,i){for(var o=0,a=e.length;o<a;o++){var r=e[o];if(t.brushSelector(i,n,r.selectors,r))return!0}}var cp={rect:function(t){return pp(t.range)},polygon:function(t){for(var e,n=t.range,i=0,o=n.length;i<o;i++){e=e||[[1/0,-1/0],[1/0,-1/0]];var a=n[i];a[0]<e[0][0]&&(e[0][0]=a[0]),a[0]>e[0][1]&&(e[0][1]=a[0]),a[1]<e[1][0]&&(e[1][0]=a[1]),a[1]>e[1][1]&&(e[1][1]=a[1])}return e&&pp(e)}};function pp(t){return new e(t[0][0],t[1][0],t[0][1]-t[0][0],t[1][1]-t[1][0])}var fp=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.init=function(t,e){this.ecModel=t,this.api=e,this.model,(this._brushController=new Vi(e.getZr())).on("brush",gt(this._onBrush,this)).mount()},e.prototype.render=function(t,e,n,i){this.model=t,this._updateController(t,e,n,i)},e.prototype.updateTransform=function(t,e,n,i){lp(e),this._updateController(t,e,n,i)},e.prototype.updateVisual=function(t,e,n,i){this.updateTransform(t,e,n,i)},e.prototype.updateView=function(t,e,n,i){this._updateController(t,e,n,i)},e.prototype._updateController=function(t,e,n,i){(!i||i.$from!==t.id)&&this._brushController.setPanels(t.brushTargetManager.makePanelOpts(n)).enableBrush(t.brushOption).updateCovers(t.areas.slice())},e.prototype.dispose=function(){this._brushController.dispose()},e.prototype._onBrush=function(t){var e=this.model.id,n=this.model.brushTargetManager.setOutputRanges(t.areas,this.ecModel);(!t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"brush",brushId:e,areas:st(n),$from:e}),t.isEnd&&this.api.dispatchAction({type:"brushEnd",brushId:e,areas:st(n),$from:e})},e.type="brush",e}(Tt),gp=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.areas=[],n.brushOption={},n}return w(e,t),e.prototype.optionUpdated=function(t,e){var n=this.option;!e&&ep(n,t,["inBrush","outOfBrush"]);var i=n.inBrush=n.inBrush||{};n.outOfBrush=n.outOfBrush||{color:"#ddd"},i.hasOwnProperty("liftZ")||(i.liftZ=5)},e.prototype.setAreas=function(t){t&&(this.areas=ht(t,(function(t){return yp(this.option,t)}),this))},e.prototype.setBrushOption=function(t){this.brushOption=yp(this.option,t),this.brushType=this.brushOption.brushType},e.type="brush",e.dependencies=["geo","grid","xAxis","yAxis","parallel","series"],e.defaultOption={seriesIndex:"all",brushType:"rect",brushMode:"single",transformable:!0,brushStyle:{borderWidth:1,color:"rgba(210,219,238,0.3)",borderColor:"#D2DBEE"},throttleType:"fixRate",throttleDelay:0,removeOnClick:!0,z:1e4},e}(wt);function yp(t,e){return B({brushType:t.brushType,brushMode:t.brushMode,transformable:t.transformable,brushStyle:new _t(t.brushStyle).getItemStyle(),removeOnClick:t.removeOnClick,z:t.z},e,!0)}var vp=["rect","polygon","lineX","lineY","keep","clear"],mp=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.render=function(t,e,n){var i,o,a;e.eachComponent({mainType:"brush"},(function(t){i=t.brushType,o=t.brushOption.brushMode||"single",a=a||!!t.areas.length})),this._brushType=i,this._brushMode=o,p(t.get("type",!0),(function(e){t.setIconStatus(e,("keep"===e?"multiple"===o:"clear"===e?a:e===i)?"emphasis":"normal")}))},e.prototype.updateView=function(t,e,n){this.render(t,e,n)},e.prototype.getIcons=function(){var t=this.model,e=t.get("icon",!0),n={};return p(t.get("type",!0),(function(t){e[t]&&(n[t]=e[t])})),n},e.prototype.onclick=function(t,e,n){var i=this._brushType,o=this._brushMode;"clear"===n?(e.dispatchAction({type:"axisAreaSelect",intervals:[]}),e.dispatchAction({type:"brush",command:"clear",areas:[]})):e.dispatchAction({type:"takeGlobalCursor",key:"brush",brushOption:{brushType:"keep"===n?i:i!==n&&n,brushMode:"keep"===n?"multiple"===o?"single":"multiple":o}})},e.getDefaultOption=function(t){return{show:!0,type:vp.slice(),icon:{rect:"M7.3,34.7 M0.4,10V-0.2h9.8 M89.6,10V-0.2h-9.8 M0.4,60v10.2h9.8 M89.6,60v10.2h-9.8 M12.3,22.4V10.5h13.1 M33.6,10.5h7.8 M49.1,10.5h7.8 M77.5,22.4V10.5h-13 M12.3,31.1v8.2 M77.7,31.1v8.2 M12.3,47.6v11.9h13.1 M33.6,59.5h7.6 M49.1,59.5 h7.7 M77.5,47.6v11.9h-13",polygon:"M55.2,34.9c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1 s-3.1-1.4-3.1-3.1S53.5,34.9,55.2,34.9z M50.4,51c1.7,0,3.1,1.4,3.1,3.1c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1 C47.3,52.4,48.7,51,50.4,51z M55.6,37.1l1.5-7.8 M60.1,13.5l1.6-8.7l-7.8,4 M59,19l-1,5.3 M24,16.1l6.4,4.9l6.4-3.3 M48.5,11.6 l-5.9,3.1 M19.1,12.8L9.7,5.1l1.1,7.7 M13.4,29.8l1,7.3l6.6,1.6 M11.6,18.4l1,6.1 M32.8,41.9 M26.6,40.4 M27.3,40.2l6.1,1.6 M49.9,52.1l-5.6-7.6l-4.9-1.2",lineX:"M15.2,30 M19.7,15.6V1.9H29 M34.8,1.9H40.4 M55.3,15.6V1.9H45.9 M19.7,44.4V58.1H29 M34.8,58.1H40.4 M55.3,44.4 V58.1H45.9 M12.5,20.3l-9.4,9.6l9.6,9.8 M3.1,29.9h16.5 M62.5,20.3l9.4,9.6L62.3,39.7 M71.9,29.9H55.4",lineY:"M38.8,7.7 M52.7,12h13.2v9 M65.9,26.6V32 M52.7,46.3h13.2v-9 M24.9,12H11.8v9 M11.8,26.6V32 M24.9,46.3H11.8v-9 M48.2,5.1l-9.3-9l-9.4,9.2 M38.9-3.9V12 M48.2,53.3l-9.3,9l-9.4-9.2 M38.9,62.3V46.4",keep:"M4,10.5V1h10.3 M20.7,1h6.1 M33,1h6.1 M55.4,10.5V1H45.2 M4,17.3v6.6 M55.6,17.3v6.6 M4,30.5V40h10.3 M20.7,40 h6.1 M33,40h6.1 M55.4,30.5V40H45.2 M21,18.9h62.9v48.6H21V18.9z",clear:"M22,14.7l30.9,31 M52.9,14.7L22,45.7 M4.7,16.8V4.2h13.1 M26,4.2h7.8 M41.6,4.2h7.8 M70.3,16.8V4.2H57.2 M4.7,25.9v8.6 M70.3,25.9v8.6 M4.7,43.2v12.6h13.1 M26,55.8h7.8 M41.6,55.8h7.8 M70.3,43.2v12.6H57.2"},title:t.getLocaleModel().get(["toolbox","brush","title"])}},e}(hc);var xp=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.layoutMode="box",n}return w(e,t),e.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n),this._initData()},e.prototype.mergeOption=function(e){t.prototype.mergeOption.apply(this,arguments),this._initData()},e.prototype.setCurrentIndex=function(t){null==t&&(t=this.option.currentIndex);var e=this._data.count();this.option.loop?t=(t%e+e)%e:(t>=e&&(t=e-1),t<0&&(t=0)),this.option.currentIndex=t},e.prototype.getCurrentIndex=function(){return this.option.currentIndex},e.prototype.isIndexMax=function(){return this.getCurrentIndex()>=this._data.count()-1},e.prototype.setPlayState=function(t){this.option.autoPlay=!!t},e.prototype.getPlayState=function(){return!!this.option.autoPlay},e.prototype._initData=function(){var t,e=this.option,n=e.data||[],i=e.axisType,o=this._names=[];"category"===i?(t=[],p(n,(function(e,n){var i,a=$t(Fi(e),"");M(e)?(i=st(e)).value=n:i=n,t.push(i),o.push(a)}))):t=n;var a={category:"ordinal",time:"time",value:"number"}[i]||"number";(this._data=new Jt([{name:"value",type:a}],this)).initData(t,o)},e.prototype.getData=function(){return this._data},e.prototype.getCategories=function(){if("category"===this.get("axisType"))return this._names.slice()},e.type="timeline",e.defaultOption={z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},e}(wt),_p=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.type="timeline.slider",e.defaultOption=Wi(xp.defaultOption,{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"circle",symbolSize:12,lineStyle:{show:!0,width:2,color:"#DAE1F5"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#A4B1D7"},itemStyle:{color:"#A4B1D7",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:15,color:"#316bf3",borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0, 0, 0, 0.3)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:24,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z",prevIcon:"M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z",prevBtnSize:18,nextBtnSize:18,color:"#A4B1D7",borderColor:"#A4B1D7",borderWidth:1},emphasis:{label:{show:!0,color:"#6f778d"},itemStyle:{color:"#316BF3"},controlStyle:{color:"#316BF3",borderColor:"#316BF3",borderWidth:2}},progress:{lineStyle:{color:"#316BF3"},itemStyle:{color:"#316BF3"},label:{color:"#6f778d"}},data:[]}),e}(xp);bt(_p,Zi.prototype);var bp=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.type="timeline",e}(Tt),Sp=function(t){function e(e,n,i,o){var a=t.call(this,e,n,i)||this;return a.type=o||"value",a}return w(e,t),e.prototype.getLabelModel=function(){return this.model.getModel("label")},e.prototype.isHorizontal=function(){return"horizontal"===this.model.get("orient")},e}(At),Ip=Math.PI,wp=t(),Mp=function(t){function n(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=n.type,e}return w(n,t),n.prototype.init=function(t,e){this.api=e},n.prototype.render=function(t,e,n){if(this.model=t,this.api=n,this.ecModel=e,this.group.removeAll(),t.get("show",!0)){var i=this._layout(t,n),o=this._createGroup("_mainGroup"),a=this._createGroup("_labelGroup"),r=this._axis=this._createAxis(i,t);t.formatTooltip=function(t){var e=r.scale.getLabel({value:t});return mt("nameValue",{noName:!0,value:e})},p(["AxisLine","AxisTick","Control","CurrentPointer"],(function(e){this["_render"+e](i,o,r,t)}),this),this._renderAxisLabel(i,a,r,t),this._position(i,t)}this._doPlayStop(),this._updateTicksStatus()},n.prototype.remove=function(){this._clearTimer(),this.group.removeAll()},n.prototype.dispose=function(){this._clearTimer()},n.prototype._layout=function(t,e){var n,i,o,a,r=t.get(["label","position"]),s=t.get("orient"),l=function(t,e){return Rt(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()},t.get("padding"))}(t,e),u={horizontal:"center",vertical:(n=null==r||"auto"===r?"horizontal"===s?l.y+l.height/2<e.getHeight()/2?"-":"+":l.x+l.width/2<e.getWidth()/2?"+":"-":xt(r)?{horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[s][r]:r)>=0||"+"===n?"left":"right"},d={horizontal:n>=0||"+"===n?"top":"bottom",vertical:"middle"},h={horizontal:0,vertical:Ip/2},c="vertical"===s?l.height:l.width,p=t.getModel("controlStyle"),f=p.get("show",!0),g=f?p.get("itemSize"):0,y=f?p.get("itemGap"):0,v=g+y,m=t.get(["label","rotate"])||0;m=m*Ip/180;var x=p.get("position",!0),_=f&&p.get("showPlayBtn",!0),b=f&&p.get("showPrevBtn",!0),S=f&&p.get("showNextBtn",!0),I=0,w=c;"left"===x||"bottom"===x?(_&&(i=[0,0],I+=v),b&&(o=[I,0],I+=v),S&&(a=[w-g,0],w-=v)):(_&&(i=[w-g,0],w-=v),b&&(o=[0,0],I+=v),S&&(a=[w-g,0],w-=v));var M=[I,w];return t.get("inverse")&&M.reverse(),{viewRect:l,mainLength:c,orient:s,rotation:h[s],labelRotation:m,labelPosOpt:n,labelAlign:t.get(["label","align"])||u[s],labelBaseline:t.get(["label","verticalAlign"])||t.get(["label","baseline"])||d[s],playPosition:i,prevBtnPosition:o,nextBtnPosition:a,axisExtent:M,controlSize:g,controlGap:y}},n.prototype._position=function(t,e){var n=this._mainGroup,i=this._labelGroup,o=t.viewRect;if("vertical"===t.orient){var a=ce(),r=o.x,s=o.y+o.height;he(a,a,[-r,-s]),Yi(a,a,-Ip/2),he(a,a,[r,s]),(o=o.clone()).applyTransform(a)}var l=y(o),u=y(n.getBoundingRect()),d=y(i.getBoundingRect()),h=[n.x,n.y],c=[i.x,i.y];c[0]=h[0]=l[0][0];var p,f=t.labelPosOpt;null==f||xt(f)?(v(h,u,l,1,p="+"===f?0:1),v(c,d,l,1,1-p)):(v(h,u,l,1,p=f>=0?0:1),c[1]=h[1]+f);function g(t){t.originX=l[0][0]-t.x,t.originY=l[1][0]-t.y}function y(t){return[[t.x,t.x+t.width],[t.y,t.y+t.height]]}function v(t,e,n,i,o){t[i]+=n[i][o]-e[i][o]}n.setPosition(h),i.setPosition(c),n.rotation=i.rotation=t.rotation,g(n),g(i)},n.prototype._createAxis=function(t,e){var n=e.getData(),i=e.get("axisType"),o=function(t,e){if(e=e||t.get("type"),e)switch(e){case"category":return new Ki({ordinalMeta:t.getCategories(),extent:[1/0,-1/0]});case"time":return new qi({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new Pt}}(e,i);o.getTicks=function(){return n.mapArray(["value"],(function(t){return{value:t}}))};var a=n.getDataExtent("value");o.setExtent(a[0],a[1]),o.calcNiceTicks();var r=new Sp("value",o,t.axisExtent,i);return r.model=e,r},n.prototype._createGroup=function(t){var e=this[t]=new Y;return this.group.add(e),e},n.prototype._renderAxisLine=function(t,e,n,i){var o=n.getExtent();if(i.get(["lineStyle","show"])){var a=new Ge({shape:{x1:o[0],y1:0,x2:o[1],y2:0},style:F({lineCap:"round"},i.getModel("lineStyle").getLineStyle()),silent:!0,z2:1});e.add(a);var r=this._progressLine=new Ge({shape:{x1:o[0],x2:this._currentPointer?this._currentPointer.x:o[0],y1:0,y2:0},style:ot({lineCap:"round",lineWidth:a.style.lineWidth},i.getModel(["progress","lineStyle"]).getLineStyle()),silent:!0,z2:1});e.add(r)}},n.prototype._renderAxisTick=function(t,e,n,i){var o=this,a=i.getData(),s=n.scale.getTicks();this._tickSymbols=[],p(s,(function(t){var s=n.dataToCoord(t.value),l=a.getItemModel(t.value),u=l.getModel("itemStyle"),d=l.getModel(["emphasis","itemStyle"]),h=l.getModel(["progress","itemStyle"]),c={x:s,y:0,onclick:gt(o._changeTimeline,o,t.value)},p=Lp(l,u,e,c);p.ensureState("emphasis").style=d.getItemStyle(),p.ensureState("progress").style=h.getItemStyle(),Xi(p);var f=r(p);l.get("tooltip")?(f.dataIndex=t.value,f.dataModel=i):f.dataIndex=f.dataModel=null,o._tickSymbols.push(p)}))},n.prototype._renderAxisLabel=function(t,e,n,i){var o=this;if(n.getLabelModel().get("show")){var a=i.getData(),r=n.getViewLabels();this._tickLabels=[],p(r,(function(i){var r=i.tickValue,s=a.getItemModel(r),l=s.getModel("label"),u=s.getModel(["emphasis","label"]),d=s.getModel(["progress","label"]),h=n.dataToCoord(i.tickValue),c=new se({x:h,y:0,rotation:t.labelRotation-t.rotation,onclick:gt(o._changeTimeline,o,r),silent:!1,style:ue(l,{text:i.formattedLabel,align:t.labelAlign,verticalAlign:t.labelBaseline})});c.ensureState("emphasis").style=ue(u),c.ensureState("progress").style=ue(d),e.add(c),Xi(c),wp(c).dataIndex=r,o._tickLabels.push(c)}))}},n.prototype._renderControl=function(t,n,i,o){var a=t.controlSize,r=t.rotation,s=o.getModel("controlStyle").getItemStyle(),l=o.getModel(["emphasis","controlStyle"]).getItemStyle(),u=o.getPlayState(),d=o.get("inverse",!0);function h(t,i,u,d){if(t){var h=Ui(x(o.get(["controlStyle",i+"BtnSize"]),a),a),c=function(t,n,i,o){var a=o.style,r=Si(t.get(["controlStyle",n]),o||{},new e(i[0],i[1],i[2],i[3]));a&&r.setStyle(a);return r}(o,i+"Icon",[0,-h/2,h,h],{x:t[0],y:t[1],originX:a/2,originY:0,rotation:d?-r:0,rectHover:!0,style:s,onclick:u});c.ensureState("emphasis").style=l,n.add(c),Xi(c)}}h(t.nextBtnPosition,"next",gt(this._changeTimeline,this,d?"-":"+")),h(t.prevBtnPosition,"prev",gt(this._changeTimeline,this,d?"+":"-")),h(t.playPosition,u?"stop":"play",gt(this._handlePlayClick,this,!u),!0)},n.prototype._renderCurrentPointer=function(t,e,n,i){var o=i.getData(),a=i.getCurrentIndex(),r=o.getItemModel(a).getModel("checkpointStyle"),s=this,l={onCreate:function(t){t.draggable=!0,t.drift=gt(s._handlePointerDrag,s),t.ondragend=gt(s._handlePointerDragend,s),Dp(t,s._progressLine,a,n,i,!0)},onUpdate:function(t){Dp(t,s._progressLine,a,n,i)}};this._currentPointer=Lp(r,r,this._mainGroup,{},this._currentPointer,l)},n.prototype._handlePlayClick=function(t){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:t,from:this.uid})},n.prototype._handlePointerDrag=function(t,e,n){this._clearTimer(),this._pointerChangeTimeline([n.offsetX,n.offsetY])},n.prototype._handlePointerDragend=function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},n.prototype._pointerChangeTimeline=function(t,e){var n=this._toAxisCoord(t)[0],i=this._axis,o=dn(i.getExtent().slice());n>o[1]&&(n=o[1]),n<o[0]&&(n=o[0]),this._currentPointer.x=n,this._currentPointer.markRedraw();var a=this._progressLine;a&&(a.shape.x2=n,a.dirty());var r=this._findNearestTick(n),s=this.model;(e||r!==s.getCurrentIndex()&&s.get("realtime"))&&this._changeTimeline(r)},n.prototype._doPlayStop=function(){var t=this;this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout((function(){var e=t.model;t._changeTimeline(e.getCurrentIndex()+(e.get("rewind",!0)?-1:1))}),this.model.get("playInterval")))},n.prototype._toAxisCoord=function(t){var e=this._mainGroup.getLocalTransform();return ji(t,e,!0)},n.prototype._findNearestTick=function(t){var e,n=this.model.getData(),i=1/0,o=this._axis;return n.each(["value"],(function(n,a){var r=o.dataToCoord(n),s=Math.abs(r-t);s<i&&(i=s,e=a)})),e},n.prototype._clearTimer=function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},n.prototype._changeTimeline=function(t){var e=this.model.getCurrentIndex();"+"===t?t=e+1:"-"===t&&(t=e-1),this.api.dispatchAction({type:"timelineChange",currentIndex:t,from:this.uid})},n.prototype._updateTicksStatus=function(){var t=this.model.getCurrentIndex(),e=this._tickSymbols,n=this._tickLabels;if(e)for(var i=0;i<e.length;i++)e&&e[i]&&e[i].toggleState("progress",i<t);if(n)for(i=0;i<n.length;i++)n&&n[i]&&n[i].toggleState("progress",wp(n[i]).dataIndex<=t)},n.type="timeline.slider",n}(bp);function Lp(t,e,n,i,o,a){var r=e.get("color");if(o)o.setColor(r),n.add(o),a&&a.onUpdate(o);else{var s=t.get("symbol");(o=U(s,-1,-1,2,2,r)).setStyle("strokeNoScale",!0),n.add(o),a&&a.onCreate(o)}var l=e.getItemStyle(["color"]);o.setStyle(l),i=B({rectHover:!0,z2:100},i,!0);var u=pt(t.get("symbolSize"));i.scaleX=u[0]/2,i.scaleY=u[1]/2;var d=Ye(t.get("symbolOffset"),u);d&&(i.x=(i.x||0)+d[0],i.y=(i.y||0)+d[1]);var h=t.get("symbolRotate");return i.rotation=(h||0)*Math.PI/180||0,o.attr(i),o.updateTransform(),o}function Dp(t,e,n,i,o,a){if(!t.dragging){var r=o.getModel("checkpointStyle"),s=i.dataToCoord(o.getData().get("value",n));if(a||!r.get("animation",!0))t.attr({x:s,y:0}),e&&e.attr({shape:{x2:s}});else{var l={duration:r.get("animationDuration",!0),easing:r.get("animationEasing",!0)};t.stopAnimation(null,!0),t.animateTo({x:s,y:0},l),e&&e.animateTo({shape:{x2:s}},l)}}}function Cp(t){var e=t&&t.timeline;tt(e)||(e=e?[e]:[]),p(e,(function(t){t&&function(t){var e=t.type,n={number:"value",time:"time"};n[e]&&(t.axisType=n[e],delete t.type);if(Tp(t),Ap(t,"controlPosition")){var i=t.controlStyle||(t.controlStyle={});Ap(i,"position")||(i.position=t.controlPosition),"none"!==i.position||Ap(i,"show")||(i.show=!1,delete i.position),delete t.controlPosition}p(t.data||[],(function(t){M(t)&&!tt(t)&&(!Ap(t,"value")&&Ap(t,"name")&&(t.value=t.name),Tp(t))}))}(t)}))}function Tp(t){var e=t.itemStyle||(t.itemStyle={}),n=e.emphasis||(e.emphasis={}),i=t.label||t.label||{},o=i.normal||(i.normal={}),a={normal:1,emphasis:1};p(i,(function(t,e){a[e]||Ap(o,e)||(o[e]=t)})),n.label&&!Ap(i,"emphasis")&&(i.emphasis=n.label,delete n.label)}function Ap(t,e){return t.hasOwnProperty(e)}function Pp(t,e){if(!t)return!1;for(var n=tt(t)?t:[t],i=0;i<n.length;i++)if(n[i]&&n[i][e])return!0;return!1}function kp(t){$e(t,"label",["show"])}var Np=t(),Vp=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.createdBySelf=!1,n}return w(e,t),e.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n),this._mergeOption(t,n,!1,!0)},e.prototype.isAnimationEnabled=function(){if(E.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},e.prototype.mergeOption=function(t,e){this._mergeOption(t,e,!1,!1)},e.prototype._mergeOption=function(t,e,n,i){var o=this.mainType;n||e.eachSeries((function(t){var n=t.get(this.mainType,!0),a=Np(t)[o];n&&n.data?(a?a._mergeOption(n,e,!0):(i&&kp(n),p(n.data,(function(t){t instanceof Array?(kp(t[0]),kp(t[1])):kp(t)})),a=this.createMarkerModelFromSeries(n,this,e),F(a,{mainType:this.mainType,seriesIndex:t.seriesIndex,name:t.name,createdBySelf:!0}),a.__hostSeries=t),Np(t)[o]=a):Np(t)[o]=null}),this)},e.prototype.formatTooltip=function(t,e,n){var i=this.getData(),o=this.getRawValue(t),a=i.getName(t);return mt("section",{header:this.name,blocks:[mt("nameValue",{name:a,value:o,noName:!a,noValue:null==o})]})},e.prototype.getData=function(){return this._data},e.prototype.setData=function(t){this._data=t},e.prototype.getDataParams=function(t,e){var n=Zi.prototype.getDataParams.call(this,t,e),i=this.__hostSeries;return i&&(n.seriesId=i.id,n.seriesName=i.name,n.seriesType=i.subType),n},e.getMarkerModelFromSeries=function(t,e){return Np(t)[e]},e.type="marker",e.dependencies=["series","grid","polar","geo"],e}(wt);bt(Vp,Zi.prototype);var Rp=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.createMarkerModelFromSeries=function(t,n,i){return new e(t,n,i)},e.type="markPoint",e.defaultOption={z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}},e}(Vp);function zp(t){return!(isNaN(parseFloat(t.x))&&isNaN(parseFloat(t.y)))}function Op(t,e,n,i,o,a){var r=[],s=Qi(e,i)?e.getCalculationInfo("stackResultDimension"):i,l=Wp(e,s,t),u=e.indicesOfNearest(s,l)[0];r[o]=e.get(n,u),r[a]=e.get(s,u);var d=e.get(i,u),h=Ji(e.get(i,u));return(h=Math.min(h,20))>=0&&(r[a]=+r[a].toFixed(h)),[r,d]}var Ep={min:qt(Op,"min"),max:qt(Op,"max"),average:qt(Op,"average"),median:qt(Op,"median")};function Bp(t,e){if(e){var n=t.getData(),i=t.coordinateSystem,o=i&&i.dimensions;if(!function(t){return!isNaN(parseFloat(t.x))&&!isNaN(parseFloat(t.y))}(e)&&!tt(e.coord)&&tt(o)){var a=Gp(e,n,i,t);if((e=st(e)).type&&Ep[e.type]&&a.baseAxis&&a.valueAxis){var r=b(o,a.baseAxis.dim),s=b(o,a.valueAxis.dim),l=Ep[e.type](n,a.baseDataDim,a.valueDataDim,r,s);e.coord=l[0],e.value=l[1]}else e.coord=[null!=e.xAxis?e.xAxis:e.radiusAxis,null!=e.yAxis?e.yAxis:e.angleAxis]}if(null!=e.coord&&tt(o))for(var u=e.coord,d=0;d<2;d++)Ep[u[d]]&&(u[d]=Wp(n,n.mapDimension(o[d]),u[d]));else e.coord=[];return e}}function Gp(t,e,n,i){var o={};return null!=t.valueIndex||null!=t.valueDim?(o.valueDataDim=null!=t.valueIndex?e.getDimension(t.valueIndex):t.valueDim,o.valueAxis=n.getAxis(function(t,e){var n=t.getData().getDimensionInfo(e);return n&&n.coordDim}(i,o.valueDataDim)),o.baseAxis=n.getOtherAxis(o.valueAxis),o.baseDataDim=e.mapDimension(o.baseAxis.dim)):(o.baseAxis=i.getBaseAxis(),o.valueAxis=n.getOtherAxis(o.baseAxis),o.baseDataDim=e.mapDimension(o.baseAxis.dim),o.valueDataDim=e.mapDimension(o.valueAxis.dim)),o}function Hp(t,e){return!(t&&t.containData&&e.coord&&!zp(e))||t.containData(e.coord)}function Fp(t,e){return t?function(t,n,i,o){var a=o<2?t.coord&&t.coord[o]:t.value;return $i(a,e[o])}:function(t,n,i,o){return $i(t.value,e[o])}}function Wp(t,e,n){if("average"===n){var i=0,o=0;return t.each(e,(function(t,e){isNaN(t)||(i+=t,o++)})),i/o}return"median"===n?t.getMedian(e):t.getDataExtent(e)["max"===n?1:0]}var Zp=t(),Yp=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.init=function(){this.markerGroupMap=ie()},e.prototype.render=function(t,e,n){var i=this,o=this.markerGroupMap;o.each((function(t){Zp(t).keep=!1})),e.eachSeries((function(t){var o=Vp.getMarkerModelFromSeries(t,i.type);o&&i.renderSeries(t,o,e,n)})),o.each((function(t){!Zp(t).keep&&i.group.remove(t.group)}))},e.prototype.markKeep=function(t){Zp(t).keep=!0},e.prototype.toggleBlurSeries=function(t,e){var n=this;p(t,(function(t){var i=Vp.getMarkerModelFromSeries(t,n.type);i&&i.getData().eachItemGraphicEl((function(t){t&&(e?to(t):eo(t))}))}))},e.type="marker",e}(Tt);function Xp(t,e,n){var i=e.coordinateSystem;t.each((function(o){var a,r=t.getItemModel(o),l=s(r.get("x"),n.getWidth()),u=s(r.get("y"),n.getHeight());if(isNaN(l)||isNaN(u)){if(e.getMarkerPosition)a=e.getMarkerPosition(t.getValues(t.dimensions,o));else if(i){var d=t.get(i.dimensions[0],o),h=t.get(i.dimensions[1],o);a=i.dataToPoint([d,h])}}else a=[l,u];isNaN(l)||(a[0]=l),isNaN(u)||(a[1]=u),t.setItemLayout(o,a)}))}var Up=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.updateTransform=function(t,e,n){e.eachSeries((function(t){var e=Vp.getMarkerModelFromSeries(t,"markPoint");e&&(Xp(e.getData(),t,n),this.markerGroupMap.get(t.id).updateLayout())}),this)},e.prototype.renderSeries=function(t,e,n,i){var a=t.coordinateSystem,s=t.id,l=t.getData(),d=this.markerGroupMap,h=d.get(s)||d.set(s,new q),c=function(t,e,n){var i;i=t?ht(t&&t.dimensions,(function(t){var n=e.getData().getDimensionInfo(e.getData().mapDimension(t))||{};return F(F({},n),{name:t,ordinalMeta:null})})):[{name:"value",type:"float"}];var o=new Jt(i,n),a=ht(n.get("data"),qt(Bp,e));t&&(a=u(a,qt(Hp,t)));var r=Fp(!!t,i);return o.initData(a,null,r),o}(a,t,e);e.setData(c),Xp(e.getData(),t,i),c.each((function(t){var n=c.getItemModel(t),i=n.getShallow("symbol"),a=n.getShallow("symbolSize"),r=n.getShallow("symbolRotate"),s=n.getShallow("symbolOffset"),u=n.getShallow("symbolKeepAspect");if(o(i)||o(a)||o(r)||o(s)){var d=e.getRawValue(t),h=e.getDataParams(t);o(i)&&(i=i(d,h)),o(a)&&(a=a(d,h)),o(r)&&(r=r(d,h)),o(s)&&(s=s(d,h))}var p=n.getModel("itemStyle").getItemStyle(),f=no(l,"color");p.fill||(p.fill=f),c.setItemVisual(t,{symbol:i,symbolSize:a,symbolRotate:r,symbolOffset:s,symbolKeepAspect:u,style:p})})),h.updateData(c),this.group.add(h.group),c.eachItemGraphicEl((function(t){t.traverse((function(t){r(t).dataModel=e}))})),this.markKeep(h),h.group.silent=e.get("silent")||t.get("silent")},e.type="markPoint",e}(Yp);var jp=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.createMarkerModelFromSeries=function(t,n,i){return new e(t,n,i)},e.type="markLine",e.defaultOption={z:5,symbol:["circle","arrow"],symbolSize:[8,16],symbolOffset:0,precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"},e}(Vp),qp=t(),Kp=function(t,e,n,i){var o,a=t.getData();if(tt(i))o=i;else{var r=i.type;if("min"===r||"max"===r||"average"===r||"median"===r||null!=i.xAxis||null!=i.yAxis){var s=void 0,l=void 0;if(null!=i.yAxis||null!=i.xAxis)s=e.getAxis(null!=i.yAxis?"y":"x"),l=Ce(i.yAxis,i.xAxis);else{var u=Gp(i,a,e,t);s=u.valueAxis,l=Wp(a,io(a,u.valueDataDim),r)}var d="x"===s.dim?0:1,h=1-d,c=st(i),p={coord:[]};c.type=null,c.coord=[],c.coord[h]=-1/0,p.coord[h]=1/0;var f=n.get("precision");f>=0&&Nt(l)&&(l=+l.toFixed(Math.min(f,20))),c.coord[d]=p.coord[d]=l,o=[c,p,{type:r,valueIndex:i.valueIndex,value:l}]}else o=[]}var g=[Bp(t,o[0]),Bp(t,o[1]),F({},o[2])];return g[2].type=g[2].type||null,B(g[2],g[0]),B(g[2],g[1]),g};function $p(t){return!isNaN(t)&&!isFinite(t)}function Qp(t,e,n,i){var o=1-t,a=i.dimensions[t];return $p(e[o])&&$p(n[o])&&e[t]===n[t]&&i.getAxis(a).containData(e[t])}function Jp(t,e){if("cartesian2d"===t.type){var n=e[0].coord,i=e[1].coord;if(n&&i&&(Qp(1,n,i,t)||Qp(0,n,i,t)))return!0}return Hp(t,e[0])&&Hp(t,e[1])}function tf(t,e,n,i,o){var a,r=i.coordinateSystem,l=t.getItemModel(e),u=s(l.get("x"),o.getWidth()),d=s(l.get("y"),o.getHeight());if(isNaN(u)||isNaN(d)){if(i.getMarkerPosition)a=i.getMarkerPosition(t.getValues(t.dimensions,e));else{var h=r.dimensions,c=t.get(h[0],e),p=t.get(h[1],e);a=r.dataToPoint([c,p])}if(wn(r,"cartesian2d")){var f=r.getAxis("x"),g=r.getAxis("y");h=r.dimensions;$p(t.get(h[0],e))?a[0]=f.toGlobalCoord(f.getExtent()[n?0:1]):$p(t.get(h[1],e))&&(a[1]=g.toGlobalCoord(g.getExtent()[n?0:1]))}isNaN(u)||(a[0]=u),isNaN(d)||(a[1]=d)}else a=[u,d];t.setItemLayout(e,a)}var ef=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.updateTransform=function(t,e,n){e.eachSeries((function(t){var e=Vp.getMarkerModelFromSeries(t,"markLine");if(e){var i=e.getData(),o=qp(e).from,a=qp(e).to;o.each((function(e){tf(o,e,!0,t,n),tf(a,e,!1,t,n)})),i.each((function(t){i.setItemLayout(t,[o.getItemLayout(t),a.getItemLayout(t)])})),this.markerGroupMap.get(t.id).updateLayout()}}),this)},e.prototype.renderSeries=function(t,e,n,i){var o=t.coordinateSystem,a=t.id,s=t.getData(),l=this.markerGroupMap,d=l.get(a)||l.set(a,new Fs);this.group.add(d.group);var h=function(t,e,n){var i;i=t?ht(t&&t.dimensions,(function(t){var n=e.getData().getDimensionInfo(e.getData().mapDimension(t))||{};return F(F({},n),{name:t,ordinalMeta:null})})):[{name:"value",type:"float"}];var o=new Jt(i,n),a=new Jt(i,n),r=new Jt([],n),s=ht(n.get("data"),qt(Kp,e,t,n));t&&(s=u(s,qt(Jp,t)));var l=Fp(!!t,i);return o.initData(ht(s,(function(t){return t[0]})),null,l),a.initData(ht(s,(function(t){return t[1]})),null,l),r.initData(ht(s,(function(t){return t[2]}))),r.hasItemOption=!0,{from:o,to:a,line:r}}(o,t,e),c=h.from,p=h.to,f=h.line;qp(e).from=c,qp(e).to=p,e.setData(f);var g=e.get("symbol"),y=e.get("symbolSize"),v=e.get("symbolRotate"),m=e.get("symbolOffset");function _(e,n,o){var a=e.getItemModel(n);tf(e,n,o,t,i);var r=a.getModel("itemStyle").getItemStyle();null==r.fill&&(r.fill=no(s,"color")),e.setItemVisual(n,{symbolKeepAspect:a.get("symbolKeepAspect"),symbolOffset:x(a.get("symbolOffset",!0),m[o?0:1]),symbolRotate:x(a.get("symbolRotate",!0),v[o?0:1]),symbolSize:x(a.get("symbolSize"),y[o?0:1]),symbol:x(a.get("symbol",!0),g[o?0:1]),style:r})}tt(g)||(g=[g,g]),tt(y)||(y=[y,y]),tt(v)||(v=[v,v]),tt(m)||(m=[m,m]),h.from.each((function(t){_(c,t,!0),_(p,t,!1)})),f.each((function(t){var e=f.getItemModel(t).getModel("lineStyle").getLineStyle();f.setItemLayout(t,[c.getItemLayout(t),p.getItemLayout(t)]),null==e.stroke&&(e.stroke=c.getItemVisual(t,"style").fill),f.setItemVisual(t,{fromSymbolKeepAspect:c.getItemVisual(t,"symbolKeepAspect"),fromSymbolOffset:c.getItemVisual(t,"symbolOffset"),fromSymbolRotate:c.getItemVisual(t,"symbolRotate"),fromSymbolSize:c.getItemVisual(t,"symbolSize"),fromSymbol:c.getItemVisual(t,"symbol"),toSymbolKeepAspect:p.getItemVisual(t,"symbolKeepAspect"),toSymbolOffset:p.getItemVisual(t,"symbolOffset"),toSymbolRotate:p.getItemVisual(t,"symbolRotate"),toSymbolSize:p.getItemVisual(t,"symbolSize"),toSymbol:p.getItemVisual(t,"symbol"),style:e})})),d.updateData(f),h.line.eachItemGraphicEl((function(t){r(t).dataModel=e,t.traverse((function(t){r(t).dataModel=e}))})),this.markKeep(d),d.group.silent=e.get("silent")||t.get("silent")},e.type="markLine",e}(Yp);var nf=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.createMarkerModelFromSeries=function(t,n,i){return new e(t,n,i)},e.type="markArea",e.defaultOption={z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}},e}(Vp),of=t(),af=function(t,e,n,i){var o=i[0],a=i[1];if(o&&a){var r=Bp(t,o),s=Bp(t,a),l=r.coord,u=s.coord;l[0]=Ce(l[0],-1/0),l[1]=Ce(l[1],-1/0),u[0]=Ce(u[0],1/0),u[1]=Ce(u[1],1/0);var d=In([{},r,s]);return d.coord=[r.coord,s.coord],d.x0=r.x,d.y0=r.y,d.x1=s.x,d.y1=s.y,d}};function rf(t){return!isNaN(t)&&!isFinite(t)}function sf(t,e,n,i){var o=1-t;return rf(e[o])&&rf(n[o])}function lf(t,e){var n=e.coord[0],i=e.coord[1],o={coord:n,x:e.x0,y:e.y0},a={coord:i,x:e.x1,y:e.y1};return wn(t,"cartesian2d")?!(!n||!i||!sf(1,n,i)&&!sf(0,n,i))||function(t,e,n){return!(t&&t.containZone&&e.coord&&n.coord&&!zp(e)&&!zp(n))||t.containZone(e.coord,n.coord)}(t,o,a):Hp(t,o)||Hp(t,a)}function uf(t,e,n,i,o){var a,r=i.coordinateSystem,l=t.getItemModel(e),u=s(l.get(n[0]),o.getWidth()),d=s(l.get(n[1]),o.getHeight());if(isNaN(u)||isNaN(d)){if(i.getMarkerPosition){var h=t.getValues(["x0","y0"],e),c=t.getValues(["x1","y1"],e),p=r.clampData(h),f=r.clampData(c),g=[];"x0"===n[0]?g[0]=p[0]>f[0]?c[0]:h[0]:g[0]=p[0]>f[0]?h[0]:c[0],"y0"===n[1]?g[1]=p[1]>f[1]?c[1]:h[1]:g[1]=p[1]>f[1]?h[1]:c[1],a=i.getMarkerPosition(g,n,!0)}else{var y=[x=t.get(n[0],e),_=t.get(n[1],e)];r.clampData&&r.clampData(y,y),a=r.dataToPoint(y,!0)}if(wn(r,"cartesian2d")){var v=r.getAxis("x"),m=r.getAxis("y"),x=t.get(n[0],e),_=t.get(n[1],e);rf(x)?a[0]=v.toGlobalCoord(v.getExtent()["x0"===n[0]?0:1]):rf(_)&&(a[1]=m.toGlobalCoord(m.getExtent()["y0"===n[1]?0:1]))}isNaN(u)||(a[0]=u),isNaN(d)||(a[1]=d)}else a=[u,d];return a}var df=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]],hf=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.updateTransform=function(t,e,n){e.eachSeries((function(t){var e=Vp.getMarkerModelFromSeries(t,"markArea");if(e){var i=e.getData();i.each((function(e){var o=ht(df,(function(o){return uf(i,e,o,t,n)}));i.setItemLayout(e,o),i.getItemGraphicEl(e).setShape("points",o)}))}}),this)},e.prototype.renderSeries=function(t,e,n,i){var o=t.coordinateSystem,a=t.id,s=t.getData(),l=this.markerGroupMap,d=l.get(a)||l.set(a,{group:new Y});this.group.add(d.group),this.markKeep(d);var h=function(t,e,n){var i,o,a=["x0","y0","x1","y1"];if(t){var r=ht(t&&t.dimensions,(function(t){var n=e.getData(),i=n.getDimensionInfo(n.mapDimension(t))||{};return F(F({},i),{name:t,ordinalMeta:null})}));o=ht(a,(function(t,e){return{name:t,type:r[e%2].type}})),i=new Jt(o,n)}else i=new Jt(o=[{name:"value",type:"float"}],n);var s=ht(n.get("data"),qt(af,e,t,n));t&&(s=u(s,qt(lf,t)));var l=t?function(t,e,n,i){var a=t.coord[Math.floor(i/2)][i%2];return $i(a,o[i])}:function(t,e,n,i){return $i(t.value,o[i])};return i.initData(s,null,l),i.hasItemOption=!0,i}(o,t,e);e.setData(h),h.each((function(e){var n=ht(df,(function(n){return uf(h,e,n,t,i)})),a=o.getAxis("x").scale,r=o.getAxis("y").scale,l=a.getExtent(),u=r.getExtent(),d=[a.parse(h.get("x0",e)),a.parse(h.get("x1",e))],c=[r.parse(h.get("y0",e)),r.parse(h.get("y1",e))];dn(d),dn(c);var p=!!(l[0]>d[1]||l[1]<d[0]||u[0]>c[1]||u[1]<c[0]);h.setItemLayout(e,{points:n,allClipped:p});var f=h.getItemModel(e).getModel("itemStyle").getItemStyle(),g=no(s,"color");f.fill||(f.fill=g,xt(f.fill)&&(f.fill=Le(f.fill,.4))),f.stroke||(f.stroke=g),h.setItemVisual(e,"style",f)})),h.diff(of(d).data).add((function(t){var e=h.getItemLayout(t);if(!e.allClipped){var n=new et({shape:{points:e.points}});h.setItemGraphicEl(t,n),d.group.add(n)}})).update((function(t,n){var i=of(d).data.getItemGraphicEl(n),o=h.getItemLayout(t);o.allClipped?i&&d.group.remove(i):(i?S(i,{shape:{points:o.points}},e,t):i=new et({shape:{points:o.points}}),h.setItemGraphicEl(t,i),d.group.add(i))})).remove((function(t){var e=of(d).data.getItemGraphicEl(t);d.group.remove(e)})).execute(),h.eachItemGraphicEl((function(t,n){var i=h.getItemModel(n),o=h.getItemVisual(n,"style");t.useStyle(h.getItemVisual(n,"style")),lt(t,ut(i),{labelFetcher:e,labelDataIndex:n,defaultText:h.getName(n)||"",inheritColor:xt(o.fill)?Le(o.fill,1):"#000"}),at(t,i),dt(t,null,null,i.get(["emphasis","disabled"])),r(t).dataModel=e})),of(d).data=h,d.group.silent=e.get("silent")||t.get("silent")},e.type="markArea",e}(Yp);var cf=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.type="dataZoom.inside",e.defaultOption=Wi(Jh.defaultOption,{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),e}(Jh),pf=t();function ff(t,e){if(e){t.removeKey(e.model.uid);var n=e.controller;n&&n.dispose()}}function gf(t,e){t.isDisposed()||t.dispatchAction({type:"dataZoom",animation:{easing:"cubicOut",duration:100},batch:e})}function yf(t,e,n,i){return t.coordinateSystem.containPoint([n,i])}function vf(t){t.registerProcessor(t.PRIORITY.PROCESSOR.FILTER,(function(t,e){var n=pf(e),i=n.coordSysRecordMap||(n.coordSysRecordMap=ie());i.each((function(t){t.dataZoomInfoMap=null})),t.eachComponent({mainType:"dataZoom",subType:"inside"},(function(t){var n=$h(t);p(n.infoList,(function(n){var o=n.model.uid,a=i.get(o)||i.set(o,function(t,e){var n={model:e,containsPoint:qt(yf,e),dispatchAction:qt(gf,t),dataZoomInfoMap:null,controller:null},i=n.controller=new zt(t.getZr());return p(["pan","zoom","scrollMove"],(function(t){i.on(t,(function(e){var i=[];n.dataZoomInfoMap.each((function(o){if(e.isAvailableBehavior(o.model.option)){var a=(o.getRange||{})[t],r=a&&a(o.dzReferCoordSysInfo,n.model.mainType,n.controller,e);!o.model.get("disabled",!0)&&r&&i.push({dataZoomId:o.model.id,start:r[0],end:r[1]})}})),i.length&&n.dispatchAction(i)}))})),n}(e,n.model));(a.dataZoomInfoMap||(a.dataZoomInfoMap=ie())).set(t.uid,{dzReferCoordSysInfo:n,model:t,getRange:null})}))})),i.each((function(t){var e,n=t.controller,o=t.dataZoomInfoMap;if(o){var a=o.keys()[0];null!=a&&(e=o.get(a))}if(e){var r=function(t){var e,n="type_",i={type_true:2,type_move:1,type_false:0,type_undefined:-1},o=!0;return t.each((function(t){var a=t.model,r=!a.get("disabled",!0)&&(!a.get("zoomLock",!0)||"move");i[n+r]>i[n+e]&&(e=r),o=o&&a.get("preventDefaultMouseMove",!0)})),{controlType:e,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!o}}}(o);n.enable(r.controlType,r.opt),n.setPointerChecker(t.containsPoint),Hi(t,"dispatchAction",e.model.get("throttle",!0),"fixRate")}else ff(i,t)}))}))}var mf=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataZoom.inside",e}return w(e,t),e.prototype.render=function(e,n,i){t.prototype.render.apply(this,arguments),e.noTarget()?this._clear():(this.range=e.getPercentRange(),function(t,e,n){pf(t).coordSysRecordMap.each((function(t){var i=t.dataZoomInfoMap.get(e.uid);i&&(i.getRange=n)}))}(i,e,{pan:gt(xf.pan,this),zoom:gt(xf.zoom,this),scrollMove:gt(xf.scrollMove,this)}))},e.prototype.dispose=function(){this._clear(),t.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){!function(t,e){for(var n=pf(t).coordSysRecordMap,i=n.keys(),o=0;o<i.length;o++){var a=i[o],r=n.get(a),s=r.dataZoomInfoMap;if(s){var l=e.uid;s.get(l)&&(s.removeKey(l),s.keys().length||ff(n,r))}}}(this.api,this.dataZoomModel),this.range=null},e.type="dataZoom.inside",e}(nc),xf={zoom:function(t,e,n,i){var o=this.range,a=o.slice(),r=t.axisModels[0];if(r){var s=bf[e](null,[i.originX,i.originY],r,n,t),l=(s.signal>0?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(a[1]-a[0])+a[0],u=Math.max(1/i.scale,0);a[0]=(a[0]-l)*u+l,a[1]=(a[1]-l)*u+l;var d=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();return vi(0,a,[0,100],0,d.minSpan,d.maxSpan),this.range=a,o[0]!==a[0]||o[1]!==a[1]?a:void 0}},pan:_f((function(t,e,n,i,o,a){var r=bf[i]([a.oldX,a.oldY],[a.newX,a.newY],e,o,n);return r.signal*(t[1]-t[0])*r.pixel/r.pixelLength})),scrollMove:_f((function(t,e,n,i,o,a){return bf[i]([0,0],[a.scrollDelta,a.scrollDelta],e,o,n).signal*(t[1]-t[0])*a.scrollDelta}))};function _f(t){return function(e,n,i,o){var a=this.range,r=a.slice(),s=e.axisModels[0];if(s){var l=t(r,s,e,n,i,o);return vi(l,r,[0,100],"all"),this.range=r,a[0]!==r[0]||a[1]!==r[1]?r:void 0}}}var bf={grid:function(t,e,n,i,o){var a=n.axis,r={},s=o.model.coordinateSystem.getRect();return t=t||[0,0],"x"===a.dim?(r.pixel=e[0]-t[0],r.pixelLength=s.width,r.pixelStart=s.x,r.signal=a.inverse?1:-1):(r.pixel=e[1]-t[1],r.pixelLength=s.height,r.pixelStart=s.y,r.signal=a.inverse?-1:1),r},polar:function(t,e,n,i,o){var a=n.axis,r={},s=o.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return t=t?s.pointToCoord(t):[0,0],e=s.pointToCoord(e),"radiusAxis"===n.mainType?(r.pixel=e[0]-t[0],r.pixelLength=l[1]-l[0],r.pixelStart=l[0],r.signal=a.inverse?1:-1):(r.pixel=e[1]-t[1],r.pixelLength=u[1]-u[0],r.pixelStart=u[0],r.signal=a.inverse?-1:1),r},singleAxis:function(t,e,n,i,o){var a=n.axis,r=o.model.coordinateSystem.getRect(),s={};return t=t||[0,0],"horizontal"===a.orient?(s.pixel=e[0]-t[0],s.pixelLength=r.width,s.pixelStart=r.x,s.signal=a.inverse?1:-1):(s.pixel=e[1]-t[1],s.pixelLength=r.height,s.pixelStart=r.y,s.signal=a.inverse?-1:1),s}};function Sf(t){uc(t),t.registerComponentModel(cf),t.registerComponentView(mf),vf(t)}var If=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.type="dataZoom.slider",e.layoutMode="box",e.defaultOption=Wi(Jh.defaultOption,{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,borderColor:"#d2dbee",borderRadius:3,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#d2dbee",width:.5},areaStyle:{color:"#d2dbee",opacity:.2}},selectedDataBackground:{lineStyle:{color:"#8fb0f7",width:.5},areaStyle:{color:"#8fb0f7",opacity:.2}},fillerColor:"rgba(135,175,274,0.2)",handleIcon:"path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",handleSize:"100%",handleStyle:{color:"#fff",borderColor:"#ACB8D1"},moveHandleSize:7,moveHandleIcon:"path://M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z",moveHandleStyle:{color:"#D2DBEE",opacity:.7},showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#6E7079"},brushSelect:!0,brushStyle:{color:"rgba(135,175,274,0.15)"},emphasis:{handleStyle:{borderColor:"#8FB0F7"},moveHandleStyle:{color:"#8FB0F7"}}}),e}(Jh),wf=be,Mf="horizontal",Lf="vertical",Df=["line","bar","candlestick","scatter"],Cf={easing:"cubicOut",duration:100,delay:0},Tf=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n._displayables={},n}return w(e,t),e.prototype.init=function(t,e){this.api=e,this._onBrush=gt(this._onBrush,this),this._onBrushEnd=gt(this._onBrushEnd,this)},e.prototype.render=function(e,n,i,o){if(t.prototype.render.apply(this,arguments),Hi(this,"_dispatchZoomAction",e.get("throttle"),"fixRate"),this._orient=e.getOrient(),!1!==e.get("show")){if(e.noTarget())return this._clear(),void this.group.removeAll();o&&"dataZoom"===o.type&&o.from===this.uid||this._buildView(),this._updateView()}else this.group.removeAll()},e.prototype.dispose=function(){this._clear(),t.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){oo(this,"_dispatchZoomAction");var t=this.api.getZr();t.off("mousemove",this._onBrush),t.off("mouseup",this._onBrushEnd)},e.prototype._buildView=function(){var t=this.group;t.removeAll(),this._brushing=!1,this._displayables.brushRect=null,this._resetLocation(),this._resetInterval();var e=this._displayables.sliderGroup=new Y;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(e),this._positionGroup()},e.prototype._resetLocation=function(){var t=this.dataZoomModel,e=this.api,n=t.get("brushSelect")?7:0,i=this._findCoordRect(),o={width:e.getWidth(),height:e.getHeight()},a=this._orient===Mf?{right:o.width-i.x-i.width,top:o.height-30-7-n,width:i.width,height:30}:{right:7,top:i.y,width:30,height:i.height},r=ri(t.option);p(["right","top","width","height"],(function(t){"ph"===r[t]&&(r[t]=a[t])}));var s=Rt(r,o);this._location={x:s.x,y:s.y},this._size=[s.width,s.height],this._orient===Lf&&this._size.reverse()},e.prototype._positionGroup=function(){var t=this.group,e=this._location,n=this._orient,i=this.dataZoomModel.getFirstTargetAxisModel(),o=i&&i.get("inverse"),a=this._displayables.sliderGroup,r=(this._dataShadowInfo||{}).otherAxisInverse;a.attr(n!==Mf||o?n===Mf&&o?{scaleY:r?1:-1,scaleX:-1}:n!==Lf||o?{scaleY:r?-1:1,scaleX:-1,rotation:Math.PI/2}:{scaleY:r?-1:1,scaleX:1,rotation:Math.PI/2}:{scaleY:r?1:-1,scaleX:1});var s=t.getBoundingRect([a]);t.x=e.x-s.x,t.y=e.y-s.y,t.markRedraw()},e.prototype._getViewExtent=function(){return[0,this._size[0]]},e.prototype._renderBackground=function(){var t=this.dataZoomModel,e=this._size,n=this._displayables.sliderGroup,i=t.get("brushSelect");n.add(new wf({silent:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:t.get("backgroundColor")},z2:-40}));var o=new wf({shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:"transparent"},z2:0,onclick:gt(this._onClickPanel,this)}),a=this.api.getZr();i?(o.on("mousedown",this._onBrushStart,this),o.cursor="crosshair",a.on("mousemove",this._onBrush),a.on("mouseup",this._onBrushEnd)):(a.off("mousemove",this._onBrush),a.off("mouseup",this._onBrushEnd)),n.add(o)},e.prototype._renderDataShadow=function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(this._displayables.dataShadowSegs=[],t){var e=this._size,n=this._shadowSize||[],i=t.series,o=i.getRawData(),a=i.getShadowDim&&i.getShadowDim(),r=a&&o.getDimensionInfo(a)?i.getShadowDim():t.otherDim;if(null!=r){var s=this._shadowPolygonPts,l=this._shadowPolylinePts;if(o!==this._shadowData||r!==this._shadowDim||e[0]!==n[0]||e[1]!==n[1]){var u=o.getDataExtent(r),d=.3*(u[1]-u[0]);u=[u[0]-d,u[1]+d];var h,c=[0,e[1]],p=[0,e[0]],f=[[e[0],0],[0,0]],g=[],y=p[1]/(o.count()-1),v=0,m=Math.round(o.count()/e[0]);o.each([r],(function(t,e){if(m>0&&e%m)v+=y;else{var n=null==t||isNaN(t)||""===t,i=n?0:Se(t,u,c,!0);n&&!h&&e?(f.push([f[f.length-1][0],0]),g.push([g[g.length-1][0],0])):!n&&h&&(f.push([v,0]),g.push([v,0])),f.push([v,i]),g.push([v,i]),v+=y,h=n}})),s=this._shadowPolygonPts=f,l=this._shadowPolylinePts=g}this._shadowData=o,this._shadowDim=r,this._shadowSize=[e[0],e[1]];for(var x,_,b,S,I,w=this.dataZoomModel,M=0;M<3;M++){var L=(x=1===M,_=void 0,b=void 0,S=void 0,I=void 0,_=w.getModel(x?"selectedDataBackground":"dataBackground"),b=new Y,S=new et({shape:{points:s},segmentIgnoreThreshold:1,style:_.getModel("areaStyle").getAreaStyle(),silent:!0,z2:-20}),I=new nt({shape:{points:l},segmentIgnoreThreshold:1,style:_.getModel("lineStyle").getLineStyle(),silent:!0,z2:-19}),b.add(S),b.add(I),b);this._displayables.sliderGroup.add(L),this._displayables.dataShadowSegs.push(L)}}}},e.prototype._prepareDataShadowInfo=function(){var t=this.dataZoomModel,e=t.get("showDataShadow");if(!1!==e){var n,i=this.ecModel;return t.eachTargetAxis((function(o,a){var r=t.getAxisProxy(o,a).getTargetSeriesModels();p(r,(function(t){if(!(n||!0!==e&&b(Df,t.get("type"))<0)){var r,s=i.getComponent(qh(o),a).axis,l={x:"y",y:"x",radius:"angle",angle:"radius"}[o],u=t.coordinateSystem;null!=l&&u.getOtherAxis&&(r=u.getOtherAxis(s).inverse),l=t.getData().mapDimension(l),n={thisAxis:s,series:t,thisDim:o,otherDim:l,otherAxisInverse:r}}}),this)}),this),n}},e.prototype._renderHandle=function(){var t=this.group,e=this._displayables,n=e.handles=[null,null],i=e.handleLabels=[null,null],o=this._displayables.sliderGroup,a=this._size,r=this.dataZoomModel,l=this.api,u=r.get("borderRadius")||0,d=r.get("brushSelect"),h=e.filler=new wf({silent:d,style:{fill:r.get("fillerColor")},textConfig:{position:"inside"}});o.add(h),o.add(new wf({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:a[0],height:a[1],r:u},style:{stroke:r.get("dataBackgroundColor")||r.get("borderColor"),lineWidth:1,fill:"rgba(0,0,0,0)"}})),p([0,1],(function(e){var a=r.get("handleIcon");!ao[a]&&a.indexOf("path://")<0&&a.indexOf("image://")<0&&(a="path://"+a);var l=U(a,-1,0,2,2,null,!0);l.attr({cursor:Af(this._orient),draggable:!0,drift:gt(this._onDragMove,this,e),ondragend:gt(this._onDragEnd,this),onmouseover:gt(this._showDataInfo,this,!0),onmouseout:gt(this._showDataInfo,this,!1),z2:5});var u=l.getBoundingRect(),d=r.get("handleSize");this._handleHeight=s(d,this._size[1]),this._handleWidth=u.width/u.height*this._handleHeight,l.setStyle(r.getModel("handleStyle").getItemStyle()),l.style.strokeNoScale=!0,l.rectHover=!0,l.ensureState("emphasis").style=r.getModel(["emphasis","handleStyle"]).getItemStyle(),Xi(l);var h=r.get("handleColor");null!=h&&(l.style.fill=h),o.add(n[e]=l);var c=r.getModel("textStyle");t.add(i[e]=new se({silent:!0,invisible:!0,style:ue(c,{x:0,y:0,text:"",verticalAlign:"middle",align:"center",fill:c.getTextColor(),font:c.getFont()}),z2:10}))}),this);var c=h;if(d){var f=s(r.get("moveHandleSize"),a[1]),g=e.moveHandle=new be({style:r.getModel("moveHandleStyle").getItemStyle(),silent:!0,shape:{r:[0,0,2,2],y:a[1]-.5,height:f}}),y=.8*f,v=e.moveHandleIcon=U(r.get("moveHandleIcon"),-y/2,-y/2,y,y,"#fff",!0);v.silent=!0,v.y=a[1]+f/2-.5,g.ensureState("emphasis").style=r.getModel(["emphasis","moveHandleStyle"]).getItemStyle();var m=Math.min(a[1]/2,Math.max(f,10));(c=e.moveZone=new be({invisible:!0,shape:{y:a[1]-m,height:f+m}})).on("mouseover",(function(){l.enterEmphasis(g)})).on("mouseout",(function(){l.leaveEmphasis(g)})),o.add(g),o.add(v),o.add(c)}c.attr({draggable:!0,cursor:Af(this._orient),drift:gt(this._onDragMove,this,"all"),ondragstart:gt(this._showDataInfo,this,!0),ondragend:gt(this._onDragEnd,this),onmouseover:gt(this._showDataInfo,this,!0),onmouseout:gt(this._showDataInfo,this,!1)})},e.prototype._resetInterval=function(){var t=this._range=this.dataZoomModel.getPercentRange(),e=this._getViewExtent();this._handleEnds=[Se(t[0],[0,100],e,!0),Se(t[1],[0,100],e,!0)]},e.prototype._updateInterval=function(t,e){var n=this.dataZoomModel,i=this._handleEnds,o=this._getViewExtent(),a=n.findRepresentativeAxisProxy().getMinMaxSpan(),r=[0,100];vi(e,i,o,n.get("zoomLock")?"all":t,null!=a.minSpan?Se(a.minSpan,r,o,!0):null,null!=a.maxSpan?Se(a.maxSpan,r,o,!0):null);var s=this._range,l=this._range=dn([Se(i[0],o,r,!0),Se(i[1],o,r,!0)]);return!s||s[0]!==l[0]||s[1]!==l[1]},e.prototype._updateView=function(t){var e=this._displayables,n=this._handleEnds,i=dn(n.slice()),o=this._size;p([0,1],(function(t){var i=e.handles[t],a=this._handleHeight;i.attr({scaleX:a/2,scaleY:a/2,x:n[t]+(t?-1:1),y:o[1]/2-a/2})}),this),e.filler.setShape({x:i[0],y:0,width:i[1]-i[0],height:o[1]});var a={x:i[0],width:i[1]-i[0]};e.moveHandle&&(e.moveHandle.setShape(a),e.moveZone.setShape(a),e.moveZone.getBoundingRect(),e.moveHandleIcon&&e.moveHandleIcon.attr("x",a.x+a.width/2));for(var r=e.dataShadowSegs,s=[0,i[0],i[1],o[0]],l=0;l<r.length;l++){var u=r[l],d=u.getClipPath();d||(d=new be,u.setClipPath(d)),d.setShape({x:s[l],y:0,width:s[l+1]-s[l],height:o[1]})}this._updateDataInfo(t)},e.prototype._updateDataInfo=function(t){var e=this.dataZoomModel,n=this._displayables,i=n.handleLabels,o=this._orient,a=["",""];if(e.get("showDetail")){var r=e.findRepresentativeAxisProxy();if(r){var s=r.getAxisModel().axis,l=this._range,u=t?r.calculateDataWindow({start:l[0],end:l[1]}).valueWindow:r.getDataValueWindow();a=[this._formatLabel(u[0],s),this._formatLabel(u[1],s)]}}var d=dn(this._handleEnds.slice());function h(t){var e=Ai(n.handles[t].parent,this.group),r=ro(0===t?"right":"left",e),s=this._handleWidth/2+5,l=ji([d[t]+(0===t?-s:s),this._size[1]/2],e);i[t].setStyle({x:l[0],y:l[1],verticalAlign:o===Mf?"middle":r,align:o===Mf?r:"center",text:a[t]})}h.call(this,0),h.call(this,1)},e.prototype._formatLabel=function(t,e){var n=this.dataZoomModel,i=n.get("labelFormatter"),a=n.get("labelPrecision");null!=a&&"auto"!==a||(a=e.getPixelPrecision());var r=null==t||isNaN(t)?"":"category"===e.type||"time"===e.type?e.scale.getLabel({value:Math.round(t)}):t.toFixed(Math.min(a,20));return o(i)?i(t,r):xt(i)?i.replace("{value}",r):r},e.prototype._showDataInfo=function(t){t=this._dragging||t;var e=this._displayables,n=e.handleLabels;n[0].attr("invisible",!t),n[1].attr("invisible",!t),e.moveHandle&&this.api[t?"enterEmphasis":"leaveEmphasis"](e.moveHandle,1)},e.prototype._onDragMove=function(t,e,n,i){this._dragging=!0,so(i.event);var o=this._displayables.sliderGroup.getLocalTransform(),a=ji([e,n],o,!0),r=this._updateInterval(t,a[0]),s=this.dataZoomModel.get("realtime");this._updateView(!s),r&&s&&this._dispatchZoomAction(!0)},e.prototype._onDragEnd=function(){this._dragging=!1,this._showDataInfo(!1),!this.dataZoomModel.get("realtime")&&this._dispatchZoomAction(!1)},e.prototype._onClickPanel=function(t){var e=this._size,n=this._displayables.sliderGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(n[0]<0||n[0]>e[0]||n[1]<0||n[1]>e[1])){var i=this._handleEnds,o=(i[0]+i[1])/2,a=this._updateInterval("all",n[0]-o);this._updateView(),a&&this._dispatchZoomAction(!1)}},e.prototype._onBrushStart=function(t){var e=t.offsetX,n=t.offsetY;this._brushStart=new tn(e,n),this._brushing=!0,this._brushStartTime=+new Date},e.prototype._onBrushEnd=function(t){if(this._brushing){var e=this._displayables.brushRect;if(this._brushing=!1,e){e.attr("ignore",!0);var n=e.shape;if(!(+new Date-this._brushStartTime<200&&Math.abs(n.width)<5)){var i=this._getViewExtent(),o=[0,100];this._range=dn([Se(n.x,i,o,!0),Se(n.x+n.width,i,o,!0)]),this._handleEnds=[n.x,n.x+n.width],this._updateView(),this._dispatchZoomAction(!1)}}}},e.prototype._onBrush=function(t){this._brushing&&(so(t.event),this._updateBrushRect(t.offsetX,t.offsetY))},e.prototype._updateBrushRect=function(t,e){var n=this._displayables,i=this.dataZoomModel,o=n.brushRect;o||(o=n.brushRect=new wf({silent:!0,style:i.getModel("brushStyle").getItemStyle()}),n.sliderGroup.add(o)),o.attr("ignore",!1);var a=this._brushStart,r=this._displayables.sliderGroup,s=r.transformCoordToLocal(t,e),l=r.transformCoordToLocal(a.x,a.y),u=this._size;s[0]=Math.max(Math.min(u[0],s[0]),0),o.setShape({x:l[0],y:0,width:s[0]-l[0],height:u[1]})},e.prototype._dispatchZoomAction=function(t){var e=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,animation:t?Cf:null,start:e[0],end:e[1]})},e.prototype._findCoordRect=function(){var t,e=$h(this.dataZoomModel).infoList;if(!t&&e.length){var n=e[0].model.coordinateSystem;t=n.getRect&&n.getRect()}if(!t){var i=this.api.getWidth(),o=this.api.getHeight();t={x:.2*i,y:.2*o,width:.6*i,height:.6*o}}return t},e.type="dataZoom.slider",e}(nc);function Af(t){return"vertical"===t?"ns-resize":"ew-resize"}function Pf(t){t.registerComponentModel(If),t.registerComponentView(Tf),uc(t)}var kf=function(t,e,n){var i=st((Nf[t]||{})[e]);return n&&tt(i)?i[i.length-1]:i},Nf={color:{active:["#006edd","#e0ffff"],inactive:["rgba(0,0,0,0)"]},colorHue:{active:[0,360],inactive:[0,0]},colorSaturation:{active:[.3,1],inactive:[0,0]},colorLightness:{active:[.9,.5],inactive:[0,0]},colorAlpha:{active:[.3,1],inactive:[0,0]},opacity:{active:[.3,1],inactive:[0,0]},symbol:{active:["circle","roundRect","diamond"],inactive:["none"]},symbolSize:{active:[10,50],inactive:[0,0]}},Vf=Cr.mapVisual,Rf=Cr.eachVisual,zf=tt,Of=p,Ef=dn,Bf=Se,Gf=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.stateList=["inRange","outOfRange"],n.replacableOptionKeys=["inRange","outOfRange","target","controller","color"],n.layoutMode={type:"box",ignoreSize:!0},n.dataBound=[-1/0,1/0],n.targetVisuals={},n.controllerVisuals={},n}return w(e,t),e.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},e.prototype.optionUpdated=function(t,e){var n=this.option;!e&&ep(n,t,this.replacableOptionKeys),this.textStyleModel=this.getModel("textStyle"),this.resetItemSize(),this.completeVisualOption()},e.prototype.resetVisual=function(t){var e=this.stateList;t=gt(t,this),this.controllerVisuals=tp(this.option.controller,e,t),this.targetVisuals=tp(this.option.target,e,t)},e.prototype.getItemSymbol=function(){return null},e.prototype.getTargetSeriesIndices=function(){var t=this.option.seriesIndex,e=[];return null==t||"all"===t?this.ecModel.eachSeries((function(t,n){e.push(n)})):e=oe(t),e},e.prototype.eachTargetSeries=function(t,e){p(this.getTargetSeriesIndices(),(function(n){var i=this.ecModel.getSeriesByIndex(n);i&&t.call(e,i)}),this)},e.prototype.isTargetSeries=function(t){var e=!1;return this.eachTargetSeries((function(n){n===t&&(e=!0)})),e},e.prototype.formatValueText=function(t,e,n){var i,a=this.option,r=a.precision,s=this.dataBound,l=a.formatter;n=n||["<",">"],tt(t)&&(t=t.slice(),i=!0);var u=e?t:i?[d(t[0]),d(t[1])]:d(t);return xt(l)?l.replace("{value}",i?u[0]:u).replace("{value2}",i?u[1]:u):o(l)?i?l(t[0],t[1]):l(t):i?t[0]===s[0]?n[0]+" "+u[1]:t[1]===s[1]?n[1]+" "+u[0]:u[0]+" - "+u[1]:u;function d(t){return t===s[0]?"min":t===s[1]?"max":(+t).toFixed(Math.min(r,20))}},e.prototype.resetExtent=function(){var t=this.option,e=Ef([t.min,t.max]);this._dataExtent=e},e.prototype.getDataDimensionIndex=function(t){var e=this.option.dimension;if(null!=e)return t.getDimensionIndex(e);for(var n=t.dimensions,i=n.length-1;i>=0;i--){var o=n[i],a=t.getDimensionInfo(o);if(!a.isCalculationCoord)return a.storeDimIndex}},e.prototype.getExtent=function(){return this._dataExtent.slice()},e.prototype.completeVisualOption=function(){var t=this.ecModel,e=this.option,n={inRange:e.inRange,outOfRange:e.outOfRange},i=e.target||(e.target={}),o=e.controller||(e.controller={});B(i,n),B(o,n);var a=this.isCategory();function r(n){zf(e.color)&&!n.inRange&&(n.inRange={color:e.color.slice().reverse()}),n.inRange=n.inRange||{color:t.get("gradientColor")}}r.call(this,i),r.call(this,o),function(t,e,n){var i=t[e],o=t[n];i&&!o&&(o=t[n]={},Of(i,(function(t,e){if(Cr.isValidType(e)){var n=kf(e,"inactive",a);null!=n&&(o[e]=n,"color"!==e||o.hasOwnProperty("opacity")||o.hasOwnProperty("colorAlpha")||(o.opacity=[0,0]))}})))}.call(this,i,"inRange","outOfRange"),function(t){var e=(t.inRange||{}).symbol||(t.outOfRange||{}).symbol,n=(t.inRange||{}).symbolSize||(t.outOfRange||{}).symbolSize,i=this.get("inactiveColor"),o=this.getItemSymbol()||"roundRect";Of(this.stateList,(function(r){var s=this.itemSize,l=t[r];l||(l=t[r]={color:a?i:[i]}),null==l.symbol&&(l.symbol=e&&st(e)||(a?o:[o])),null==l.symbolSize&&(l.symbolSize=n&&st(n)||(a?s[0]:[s[0],s[0]])),l.symbol=Vf(l.symbol,(function(t){return"none"===t?o:t}));var u=l.symbolSize;if(null!=u){var d=-1/0;Rf(u,(function(t){t>d&&(d=t)})),l.symbolSize=Vf(u,(function(t){return Bf(t,[0,d],[0,s[0]],!0)}))}}),this)}.call(this,o)},e.prototype.resetItemSize=function(){this.itemSize=[parseFloat(this.get("itemWidth")),parseFloat(this.get("itemHeight"))]},e.prototype.isCategory=function(){return!!this.option.categories},e.prototype.setSelected=function(t){},e.prototype.getSelected=function(){return null},e.prototype.getValueState=function(t){return null},e.prototype.getVisualMeta=function(t){return null},e.type="visualMap",e.dependencies=["series"],e.defaultOption={show:!0,z:4,seriesIndex:"all",min:0,max:200,left:0,right:null,top:null,bottom:0,itemWidth:null,itemHeight:null,inverse:!1,orient:"vertical",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",contentColor:"#5793f3",inactiveColor:"#aaa",borderWidth:0,padding:5,textGap:10,precision:0,textStyle:{color:"#333"}},e}(wt),Hf=[20,140],Ff=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.optionUpdated=function(e,n){t.prototype.optionUpdated.apply(this,arguments),this.resetExtent(),this.resetVisual((function(t){t.mappingMethod="linear",t.dataExtent=this.getExtent()})),this._resetRange()},e.prototype.resetItemSize=function(){t.prototype.resetItemSize.apply(this,arguments);var e=this.itemSize;(null==e[0]||isNaN(e[0]))&&(e[0]=Hf[0]),(null==e[1]||isNaN(e[1]))&&(e[1]=Hf[1])},e.prototype._resetRange=function(){var t=this.getExtent(),e=this.option.range;!e||e.auto?(t.auto=1,this.option.range=t):tt(e)&&(e[0]>e[1]&&e.reverse(),e[0]=Math.max(e[0],t[0]),e[1]=Math.min(e[1],t[1]))},e.prototype.completeVisualOption=function(){t.prototype.completeVisualOption.apply(this,arguments),p(this.stateList,(function(t){var e=this.option.controller[t].symbolSize;e&&e[0]!==e[1]&&(e[0]=e[1]/3)}),this)},e.prototype.setSelected=function(t){this.option.range=t.slice(),this._resetRange()},e.prototype.getSelected=function(){var t=this.getExtent(),e=dn((this.get("range")||[]).slice());return e[0]>t[1]&&(e[0]=t[1]),e[1]>t[1]&&(e[1]=t[1]),e[0]<t[0]&&(e[0]=t[0]),e[1]<t[0]&&(e[1]=t[0]),e},e.prototype.getValueState=function(t){var e=this.option.range,n=this.getExtent();return(e[0]<=n[0]||e[0]<=t)&&(e[1]>=n[1]||t<=e[1])?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var e=[];return this.eachTargetSeries((function(n){var i=[],o=n.getData();o.each(this.getDataDimensionIndex(o),(function(e,n){t[0]<=e&&e<=t[1]&&i.push(n)}),this),e.push({seriesId:n.id,dataIndex:i})}),this),e},e.prototype.getVisualMeta=function(t){var e=Wf(this,"outOfRange",this.getExtent()),n=Wf(this,"inRange",this.option.range.slice()),i=[];function o(e,n){i.push({value:e,color:t(e,n)})}for(var a=0,r=0,s=n.length,l=e.length;r<l&&(!n.length||e[r]<=n[0]);r++)e[r]<n[a]&&o(e[r],"outOfRange");for(var u=1;a<s;a++,u=0)u&&i.length&&o(n[a],"outOfRange"),o(n[a],"inRange");for(u=1;r<l;r++)(!n.length||n[n.length-1]<e[r])&&(u&&(i.length&&o(i[i.length-1].value,"outOfRange"),u=0),o(e[r],"outOfRange"));var d=i.length;return{stops:i,outerColors:[d?i[0].color:"transparent",d?i[d-1].color:"transparent"]}},e.type="visualMap.continuous",e.defaultOption=Wi(Gf.defaultOption,{align:"auto",calculable:!1,hoverLink:!0,realtime:!0,handleIcon:"path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z",handleSize:"120%",handleStyle:{borderColor:"#fff",borderWidth:1},indicatorIcon:"circle",indicatorSize:"50%",indicatorStyle:{borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}}),e}(Gf);function Wf(t,e,n){if(n[0]===n[1])return n.slice();for(var i=(n[1]-n[0])/200,o=n[0],a=[],r=0;r<=200&&o<n[1];r++)a.push(o),o+=i;return a.push(n[1]),a}var Zf=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.autoPositionValues={left:1,right:1,top:1,bottom:1},n}return w(e,t),e.prototype.init=function(t,e){this.ecModel=t,this.api=e},e.prototype.render=function(t,e,n,i){this.visualMapModel=t,!1!==t.get("show")?this.doRender(t,e,n,i):this.group.removeAll()},e.prototype.renderBackground=function(t){var e=this.visualMapModel,n=lo(e.get("padding")||0),i=t.getBoundingRect();t.add(new be({z2:-1,silent:!0,shape:{x:i.x-n[3],y:i.y-n[0],width:i.width+n[3]+n[1],height:i.height+n[0]+n[2]},style:{fill:e.get("backgroundColor"),stroke:e.get("borderColor"),lineWidth:e.get("borderWidth")}}))},e.prototype.getControllerVisual=function(t,e,n){var i=(n=n||{}).forceState,o=this.visualMapModel,a={};if("color"===e){var r=o.get("contentColor");a.color=r}function s(t){return a[t]}function l(t,e){a[t]=e}var u=o.controllerVisuals[i||o.getValueState(t)],d=Cr.prepareVisualTypes(u);return p(d,(function(i){var o=u[i];n.convertOpacityToAlpha&&"opacity"===i&&(i="colorAlpha",o=u.__alphaForOpacity),Cr.dependsOn(i,e)&&o&&o.applyVisual(t,s,l)})),a[e]},e.prototype.positionGroup=function(t){var e=this.visualMapModel,n=this.api;ae(t,e.getBoxLayoutParams(),{width:n.getWidth(),height:n.getHeight()})},e.prototype.doRender=function(t,e,n,i){},e.type="visualMap",e}(Tt),Yf=[["left","right","width"],["top","bottom","height"]];function Xf(t,e,n){var i=t.option,o=i.align;if(null!=o&&"auto"!==o)return o;for(var a={width:e.getWidth(),height:e.getHeight()},r="horizontal"===i.orient?1:0,s=Yf[r],l=[0,null,10],u={},d=0;d<3;d++)u[Yf[1-r][d]]=l[d],u[s[d]]=2===d?n[0]:i[s[d]];var h=[["x","width",3],["y","height",0]][r],c=Rt(u,a,i.padding);return s[(c.margin[h[2]]||0)+c[h[0]]+.5*c[h[1]]<.5*a[h[1]]?0:1]}function Uf(t,e){return p(t||[],(function(t){null!=t.dataIndex&&(t.dataIndexInside=t.dataIndex,t.dataIndex=null),t.highlightKey="visualMap"+(e?e.componentIndex:"")})),t}var jf=Se,qf=p,Kf=Math.min,$f=Math.max,Qf=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n._shapes={},n._dataInterval=[],n._handleEnds=[],n._hoverLinkDataIndices=[],n}return w(e,t),e.prototype.init=function(e,n){t.prototype.init.call(this,e,n),this._hoverLinkFromSeriesMouseOver=gt(this._hoverLinkFromSeriesMouseOver,this),this._hideIndicator=gt(this._hideIndicator,this)},e.prototype.doRender=function(t,e,n,i){i&&"selectDataRange"===i.type&&i.from===this.uid||this._buildView()},e.prototype._buildView=function(){this.group.removeAll();var t=this.visualMapModel,e=this.group;this._orient=t.get("orient"),this._useHandle=t.get("calculable"),this._resetInterval(),this._renderBar(e);var n=t.get("text");this._renderEndsText(e,n,0),this._renderEndsText(e,n,1),this._updateView(!0),this.renderBackground(e),this._updateView(),this._enableHoverLinkToSeries(),this._enableHoverLinkFromSeries(),this.positionGroup(e)},e.prototype._renderEndsText=function(t,e,n){if(e){var i=e[1-n];i=null!=i?i+"":"";var o=this.visualMapModel,a=o.get("textGap"),r=o.itemSize,s=this._shapes.mainGroup,l=this._applyTransform([r[0]/2,0===n?-a:r[1]+a],s),u=this._applyTransform(0===n?"bottom":"top",s),d=this._orient,h=this.visualMapModel.textStyleModel;this.group.add(new se({style:ue(h,{x:l[0],y:l[1],verticalAlign:"horizontal"===d?"middle":u,align:"horizontal"===d?u:"center",text:i})}))}},e.prototype._renderBar=function(t){var e=this.visualMapModel,n=this._shapes,i=e.itemSize,o=this._orient,a=this._useHandle,r=Xf(e,this.api,i),s=n.mainGroup=this._createBarGroup(r),l=new Y;s.add(l),l.add(n.outOfRange=Jf()),l.add(n.inRange=Jf(null,a?eg(this._orient):null,gt(this._dragHandle,this,"all",!1),gt(this._dragHandle,this,"all",!0))),l.setClipPath(new be({shape:{x:0,y:0,width:i[0],height:i[1],r:3}}));var u=e.textStyleModel.getTextRect("国"),d=$f(u.width,u.height);a&&(n.handleThumbs=[],n.handleLabels=[],n.handleLabelPoints=[],this._createHandle(e,s,0,i,d,o),this._createHandle(e,s,1,i,d,o)),this._createIndicator(e,s,i,d,o),t.add(s)},e.prototype._createHandle=function(t,e,n,i,o,a){var r=gt(this._dragHandle,this,n,!1),s=gt(this._dragHandle,this,n,!0),l=Ui(t.get("handleSize"),i[0]),u=U(t.get("handleIcon"),-l/2,-l/2,l,l,null,!0),d=eg(this._orient);u.attr({cursor:d,draggable:!0,drift:r,ondragend:s,onmousemove:function(t){so(t.event)}}),u.x=i[0]/2,u.useStyle(t.getModel("handleStyle").getItemStyle()),u.setStyle({strokeNoScale:!0,strokeFirst:!0}),u.style.lineWidth*=2,u.ensureState("emphasis").style=t.getModel(["emphasis","handleStyle"]).getItemStyle(),ye(u,!0),e.add(u);var h=this.visualMapModel.textStyleModel,c=new se({cursor:d,draggable:!0,drift:r,onmousemove:function(t){so(t.event)},ondragend:s,style:ue(h,{x:0,y:0,text:""})});c.ensureState("blur").style={opacity:.1},c.stateTransition={duration:200},this.group.add(c);var p=[l,0],f=this._shapes;f.handleThumbs[n]=u,f.handleLabelPoints[n]=p,f.handleLabels[n]=c},e.prototype._createIndicator=function(t,e,n,i,o){var a=Ui(t.get("indicatorSize"),n[0]),r=U(t.get("indicatorIcon"),-a/2,-a/2,a,a,null,!0);r.attr({cursor:"move",invisible:!0,silent:!0,x:n[0]/2});var s=t.getModel("indicatorStyle").getItemStyle();if(r instanceof rt){var l=r.style;r.useStyle(F({image:l.image,x:l.x,y:l.y,width:l.width,height:l.height},s))}else r.useStyle(s);e.add(r);var u=this.visualMapModel.textStyleModel,d=new se({silent:!0,invisible:!0,style:ue(u,{x:0,y:0,text:""})});this.group.add(d);var h=[("horizontal"===o?i/2:6)+n[0]/2,0],c=this._shapes;c.indicator=r,c.indicatorLabel=d,c.indicatorLabelPoint=h,this._firstShowIndicator=!0},e.prototype._dragHandle=function(t,e,n,i){if(this._useHandle){if(this._dragging=!e,!e){var o=this._applyTransform([n,i],this._shapes.mainGroup,!0);this._updateInterval(t,o[1]),this._hideIndicator(),this._updateView()}e===!this.visualMapModel.get("realtime")&&this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:this._dataInterval.slice()}),e?!this._hovering&&this._clearHoverLinkToSeries():tg(this.visualMapModel)&&this._doHoverLinkToSeries(this._handleEnds[t],!1)}},e.prototype._resetInterval=function(){var t=this.visualMapModel,e=this._dataInterval=t.getSelected(),n=t.getExtent(),i=[0,t.itemSize[1]];this._handleEnds=[jf(e[0],n,i,!0),jf(e[1],n,i,!0)]},e.prototype._updateInterval=function(t,e){e=e||0;var n=this.visualMapModel,i=this._handleEnds,o=[0,n.itemSize[1]];vi(e,i,o,t,0);var a=n.getExtent();this._dataInterval=[jf(i[0],o,a,!0),jf(i[1],o,a,!0)]},e.prototype._updateView=function(t){var e=this.visualMapModel,n=e.getExtent(),i=this._shapes,o=[0,e.itemSize[1]],a=t?o:this._handleEnds,r=this._createBarVisual(this._dataInterval,n,a,"inRange"),s=this._createBarVisual(n,n,o,"outOfRange");i.inRange.setStyle({fill:r.barColor}).setShape("points",r.barPoints),i.outOfRange.setStyle({fill:s.barColor}).setShape("points",s.barPoints),this._updateHandle(a,r)},e.prototype._createBarVisual=function(t,e,n,i){var o={forceState:i,convertOpacityToAlpha:!0},a=this._makeColorGradient(t,o),r=[this.getControllerVisual(t[0],"symbolSize",o),this.getControllerVisual(t[1],"symbolSize",o)],s=this._createBarPoints(n,r);return{barColor:new rn(0,0,0,1,a),barPoints:s,handlesColor:[a[0].color,a[a.length-1].color]}},e.prototype._makeColorGradient=function(t,e){var n=[],i=(t[1]-t[0])/100;n.push({color:this.getControllerVisual(t[0],"color",e),offset:0});for(var o=1;o<100;o++){var a=t[0]+i*o;if(a>t[1])break;n.push({color:this.getControllerVisual(a,"color",e),offset:o/100})}return n.push({color:this.getControllerVisual(t[1],"color",e),offset:1}),n},e.prototype._createBarPoints=function(t,e){var n=this.visualMapModel.itemSize;return[[n[0]-e[0],t[0]],[n[0],t[0]],[n[0],t[1]],[n[0]-e[1],t[1]]]},e.prototype._createBarGroup=function(t){var e=this._orient,n=this.visualMapModel.get("inverse");return new Y("horizontal"!==e||n?"horizontal"===e&&n?{scaleX:"bottom"===t?-1:1,rotation:-Math.PI/2}:"vertical"!==e||n?{scaleX:"left"===t?1:-1}:{scaleX:"left"===t?1:-1,scaleY:-1}:{scaleX:"bottom"===t?1:-1,rotation:Math.PI/2})},e.prototype._updateHandle=function(t,e){if(this._useHandle){var n=this._shapes,i=this.visualMapModel,o=n.handleThumbs,a=n.handleLabels,r=i.itemSize,s=i.getExtent();qf([0,1],(function(l){var u=o[l];u.setStyle("fill",e.handlesColor[l]),u.y=t[l];var d=jf(t[l],[0,r[1]],s,!0),h=this.getControllerVisual(d,"symbolSize");u.scaleX=u.scaleY=h/r[0],u.x=r[0]-h/2;var c=ji(n.handleLabelPoints[l],Ai(u,this.group));a[l].setStyle({x:c[0],y:c[1],text:i.formatValueText(this._dataInterval[l]),verticalAlign:"middle",align:"vertical"===this._orient?this._applyTransform("left",n.mainGroup):"center"})}),this)}},e.prototype._showIndicator=function(t,e,n,i){var o=this.visualMapModel,a=o.getExtent(),r=o.itemSize,s=[0,r[1]],l=this._shapes,u=l.indicator;if(u){u.attr("invisible",!1);var d=this.getControllerVisual(t,"color",{convertOpacityToAlpha:!0}),h=this.getControllerVisual(t,"symbolSize"),c=jf(t,a,s,!0),p=r[0]-h/2,f={x:u.x,y:u.y};u.y=c,u.x=p;var g=ji(l.indicatorLabelPoint,Ai(u,this.group)),y=l.indicatorLabel;y.attr("invisible",!1);var v=this._applyTransform("left",l.mainGroup),m="horizontal"===this._orient;y.setStyle({text:(n||"")+o.formatValueText(e),verticalAlign:m?v:"middle",align:m?"center":v});var x={x:p,y:c,style:{fill:d}},_={style:{x:g[0],y:g[1]}};if(o.ecModel.isAnimationEnabled()&&!this._firstShowIndicator){var b={duration:100,easing:"cubicInOut",additive:!0};u.x=f.x,u.y=f.y,u.animateTo(x,b),y.animateTo(_,b)}else u.attr(x),y.attr(_);this._firstShowIndicator=!1;var S=this._shapes.handleLabels;if(S)for(var I=0;I<S.length;I++)this.api.enterBlur(S[I])}},e.prototype._enableHoverLinkToSeries=function(){var t=this;this._shapes.mainGroup.on("mousemove",(function(e){if(t._hovering=!0,!t._dragging){var n=t.visualMapModel.itemSize,i=t._applyTransform([e.offsetX,e.offsetY],t._shapes.mainGroup,!0,!0);i[1]=Kf($f(0,i[1]),n[1]),t._doHoverLinkToSeries(i[1],0<=i[0]&&i[0]<=n[0])}})).on("mouseout",(function(){t._hovering=!1,!t._dragging&&t._clearHoverLinkToSeries()}))},e.prototype._enableHoverLinkFromSeries=function(){var t=this.api.getZr();this.visualMapModel.option.hoverLink?(t.on("mouseover",this._hoverLinkFromSeriesMouseOver,this),t.on("mouseout",this._hideIndicator,this)):this._clearHoverLinkFromSeries()},e.prototype._doHoverLinkToSeries=function(t,e){var n=this.visualMapModel,i=n.itemSize;if(n.option.hoverLink){var o=[0,i[1]],a=n.getExtent();t=Kf($f(o[0],t),o[1]);var r=function(t,e,n){var i=6,o=t.get("hoverLinkDataSize");o&&(i=jf(o,e,n,!0)/2);return i}(n,a,o),s=[t-r,t+r],l=jf(t,o,a,!0),u=[jf(s[0],o,a,!0),jf(s[1],o,a,!0)];s[0]<o[0]&&(u[0]=-1/0),s[1]>o[1]&&(u[1]=1/0),e&&(u[0]===-1/0?this._showIndicator(l,u[1],"< ",r):u[1]===1/0?this._showIndicator(l,u[0],"> ",r):this._showIndicator(l,l,"≈ ",r));var d=this._hoverLinkDataIndices,h=[];(e||tg(n))&&(h=this._hoverLinkDataIndices=n.findTargetDataIndices(u));var c=uo(d,h);this._dispatchHighDown("downplay",Uf(c[0],n)),this._dispatchHighDown("highlight",Uf(c[1],n))}},e.prototype._hoverLinkFromSeriesMouseOver=function(t){var e;if(ho(t.target,(function(t){var n=r(t);if(null!=n.dataIndex)return e=n,!0}),!0),e){var n=this.ecModel.getSeriesByIndex(e.seriesIndex),i=this.visualMapModel;if(i.isTargetSeries(n)){var o=n.getData(e.dataType),a=o.getStore().get(i.getDataDimensionIndex(o),e.dataIndex);isNaN(a)||this._showIndicator(a,a)}}},e.prototype._hideIndicator=function(){var t=this._shapes;t.indicator&&t.indicator.attr("invisible",!0),t.indicatorLabel&&t.indicatorLabel.attr("invisible",!0);var e=this._shapes.handleLabels;if(e)for(var n=0;n<e.length;n++)this.api.leaveBlur(e[n])},e.prototype._clearHoverLinkToSeries=function(){this._hideIndicator();var t=this._hoverLinkDataIndices;this._dispatchHighDown("downplay",Uf(t,this.visualMapModel)),t.length=0},e.prototype._clearHoverLinkFromSeries=function(){this._hideIndicator();var t=this.api.getZr();t.off("mouseover",this._hoverLinkFromSeriesMouseOver),t.off("mouseout",this._hideIndicator)},e.prototype._applyTransform=function(t,e,n,i){var o=Ai(e,i?null:this.group);return tt(t)?ji(t,o,n):ro(t,o,n)},e.prototype._dispatchHighDown=function(t,e){e&&e.length&&this.api.dispatchAction({type:t,batch:e})},e.prototype.dispose=function(){this._clearHoverLinkFromSeries(),this._clearHoverLinkToSeries()},e.type="visualMap.continuous",e}(Zf);function Jf(t,e,n,i){return new et({shape:{points:t},draggable:!!n,cursor:e,drift:n,onmousemove:function(t){so(t.event)},ondragend:i})}function tg(t){var e=t.get("hoverLinkOnHandle");return!!(null==e?t.get("realtime"):e)}function eg(t){return"vertical"===t?"ns-resize":"ew-resize"}var ng={type:"selectDataRange",event:"dataRangeSelected",update:"update"},ig=function(t,e){e.eachComponent({mainType:"visualMap",query:t},(function(e){e.setSelected(t.selected)}))},og=[{createOnAllSeries:!0,reset:function(t,e){var n=[];return e.eachComponent("visualMap",(function(e){var i,o,a,r,s,l=t.pipelineContext;!e.isTargetSeries(t)||l&&l.large||n.push((i=e.stateList,o=e.targetVisuals,a=gt(e.getValueState,e),r=e.getDataDimensionIndex(t.getData()),s={},p(i,(function(t){var e=Cr.prepareVisualTypes(o[t]);s[t]=e})),{progress:function(t,e){var n,i;function l(t){return Oi(e,i,t)}function u(t,n){Ei(e,i,t,n)}null!=r&&(n=e.getDimensionIndex(r));for(var d=e.getStore();null!=(i=t.next());){var h=e.getRawDataItem(i);if(!h||!1!==h.visualMap)for(var c=null!=r?d.get(n,i):i,p=a(c),f=o[p],g=s[p],y=0,v=g.length;y<v;y++){var m=g[y];f[m]&&f[m].applyVisual(c,l,u)}}}}))})),n}},{createOnAllSeries:!0,reset:function(t,e){var n=t.getData(),i=[];e.eachComponent("visualMap",(function(e){if(e.isTargetSeries(t)){var o=e.getVisualMeta(gt(ag,null,t,e))||{stops:[],outerColors:[]},a=e.getDataDimensionIndex(n);a>=0&&(o.dimension=a,i.push(o))}})),t.getData().setVisual("visualMeta",i)}}];function ag(t,e,n,i){for(var o=e.targetVisuals[i],a=Cr.prepareVisualTypes(o),r={color:no(t.getData(),"color")},s=0,l=a.length;s<l;s++){var u=a[s],d=o["opacity"===u?"__alphaForOpacity":u];d&&d.applyVisual(n,h,c)}return r.color;function h(t){return r[t]}function c(t,e){r[t]=e}}var rg=p;function sg(t){var e=t&&t.visualMap;tt(e)||(e=e?[e]:[]),rg(e,(function(t){if(t){lg(t,"splitList")&&!lg(t,"pieces")&&(t.pieces=t.splitList,delete t.splitList);var e=t.pieces;e&&tt(e)&&rg(e,(function(t){M(t)&&(lg(t,"start")&&!lg(t,"min")&&(t.min=t.start),lg(t,"end")&&!lg(t,"max")&&(t.max=t.end))}))}}))}function lg(t,e){return t&&t.hasOwnProperty&&t.hasOwnProperty(e)}var ug=!1;function dg(t){ug||(ug=!0,t.registerSubTypeDefaulter("visualMap",(function(t){return t.categories||(t.pieces?t.pieces.length>0:t.splitNumber>0)&&!t.calculable?"piecewise":"continuous"})),t.registerAction(ng,ig),p(og,(function(e){t.registerVisual(t.PRIORITY.VISUAL.COMPONENT,e)})),t.registerPreprocessor(sg))}function hg(t){t.registerComponentModel(Ff),t.registerComponentView(Qf),dg(t)}var cg=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n._pieceList=[],n}return w(e,t),e.prototype.optionUpdated=function(e,n){t.prototype.optionUpdated.apply(this,arguments),this.resetExtent();var i=this._mode=this._determineMode();this._pieceList=[],pg[this._mode].call(this,this._pieceList),this._resetSelected(e,n);var o=this.option.categories;this.resetVisual((function(t,e){"categories"===i?(t.mappingMethod="category",t.categories=st(o)):(t.dataExtent=this.getExtent(),t.mappingMethod="piecewise",t.pieceList=ht(this._pieceList,(function(t){return t=st(t),"inRange"!==e&&(t.visual=null),t})))}))},e.prototype.completeVisualOption=function(){var e=this.option,n={},i=Cr.listVisualTypes(),o=this.isCategory();function a(t,e,n){return t&&t[e]&&t[e].hasOwnProperty(n)}p(e.pieces,(function(t){p(i,(function(e){t.hasOwnProperty(e)&&(n[e]=1)}))})),p(n,(function(t,n){var i=!1;p(this.stateList,(function(t){i=i||a(e,t,n)||a(e.target,t,n)}),this),!i&&p(this.stateList,(function(t){(e[t]||(e[t]={}))[n]=kf(n,"inRange"===t?"active":"inactive",o)}))}),this),t.prototype.completeVisualOption.apply(this,arguments)},e.prototype._resetSelected=function(t,e){var n=this.option,i=this._pieceList,o=(e?n:t).selected||{};if(n.selected=o,p(i,(function(t,e){var n=this.getSelectedMapKey(t);o.hasOwnProperty(n)||(o[n]=!0)}),this),"single"===n.selectedMode){var a=!1;p(i,(function(t,e){var n=this.getSelectedMapKey(t);o[n]&&(a?o[n]=!1:a=!0)}),this)}},e.prototype.getItemSymbol=function(){return this.get("itemSymbol")},e.prototype.getSelectedMapKey=function(t){return"categories"===this._mode?t.value+"":t.index+""},e.prototype.getPieceList=function(){return this._pieceList},e.prototype._determineMode=function(){var t=this.option;return t.pieces&&t.pieces.length>0?"pieces":this.option.categories?"categories":"splitNumber"},e.prototype.setSelected=function(t){this.option.selected=st(t)},e.prototype.getValueState=function(t){var e=Cr.findPieceIndex(t,this._pieceList);return null!=e&&this.option.selected[this.getSelectedMapKey(this._pieceList[e])]?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var e=[],n=this._pieceList;return this.eachTargetSeries((function(i){var o=[],a=i.getData();a.each(this.getDataDimensionIndex(a),(function(e,i){Cr.findPieceIndex(e,n)===t&&o.push(i)}),this),e.push({seriesId:i.id,dataIndex:o})}),this),e},e.prototype.getRepresentValue=function(t){var e;if(this.isCategory())e=t.value;else if(null!=t.value)e=t.value;else{var n=t.interval||[];e=n[0]===-1/0&&n[1]===1/0?0:(n[0]+n[1])/2}return e},e.prototype.getVisualMeta=function(t){if(!this.isCategory()){var e=[],n=["",""],i=this,o=this._pieceList.slice();if(o.length){var a=o[0].interval[0];a!==-1/0&&o.unshift({interval:[-1/0,a]}),(a=o[o.length-1].interval[1])!==1/0&&o.push({interval:[a,1/0]})}else o.push({interval:[-1/0,1/0]});var r=-1/0;return p(o,(function(t){var e=t.interval;e&&(e[0]>r&&s([r,e[0]],"outOfRange"),s(e.slice()),r=e[1])}),this),{stops:e,outerColors:n}}function s(o,a){var r=i.getRepresentValue({interval:o});a||(a=i.getValueState(r));var s=t(r,a);o[0]===-1/0?n[0]=s:o[1]===1/0?n[1]=s:e.push({value:o[0],color:s},{value:o[1],color:s})}},e.type="visualMap.piecewise",e.defaultOption=Wi(Gf.defaultOption,{selected:null,minOpen:!1,maxOpen:!1,align:"auto",itemWidth:20,itemHeight:14,itemSymbol:"roundRect",pieces:null,categories:null,splitNumber:5,selectedMode:"multiple",itemGap:10,hoverLink:!0}),e}(Gf),pg={splitNumber:function(t){var e=this.option,n=Math.min(e.precision,20),i=this.getExtent(),o=e.splitNumber;o=Math.max(parseInt(o,10),1),e.splitNumber=o;for(var a=(i[1]-i[0])/o;+a.toFixed(n)!==a&&n<5;)n++;e.precision=n,a=+a.toFixed(n),e.minOpen&&t.push({interval:[-1/0,i[0]],close:[0,0]});for(var r=0,s=i[0];r<o;s+=a,r++){var l=r===o-1?i[1]:s+a;t.push({interval:[s,l],close:[1,1]})}e.maxOpen&&t.push({interval:[i[1],1/0],close:[0,0]}),co(t),p(t,(function(t,e){t.index=e,t.text=this.formatValueText(t.interval)}),this)},categories:function(t){var e=this.option;p(e.categories,(function(e){t.push({text:this.formatValueText(e,!0),value:e})}),this),fg(e,t)},pieces:function(t){var e=this.option;p(e.pieces,(function(e,n){M(e)||(e={value:e});var i={text:"",index:n};if(null!=e.label&&(i.text=e.label),e.hasOwnProperty("value")){var o=i.value=e.value;i.interval=[o,o],i.close=[1,1]}else{for(var a=i.interval=[],r=i.close=[0,0],s=[1,0,1],l=[-1/0,1/0],u=[],d=0;d<2;d++){for(var h=[["gte","gt","min"],["lte","lt","max"]][d],c=0;c<3&&null==a[d];c++)a[d]=e[h[c]],r[d]=s[c],u[d]=2===c;null==a[d]&&(a[d]=l[d])}u[0]&&a[1]===1/0&&(r[0]=0),u[1]&&a[0]===-1/0&&(r[1]=0),a[0]===a[1]&&r[0]&&r[1]&&(i.value=a[0])}i.visual=Cr.retrieveVisuals(e),t.push(i)}),this),fg(e,t),co(t),p(t,(function(t){var e=t.close,n=[["<","≤"][e[1]],[">","≥"][e[0]]];t.text=t.text||this.formatValueText(null!=t.value?t.value:t.interval,!1,n)}),this)}};function fg(t,e){var n=t.inverse;("vertical"===t.orient?!n:n)&&e.reverse()}var gg=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return w(e,t),e.prototype.doRender=function(){var t=this.group;t.removeAll();var e=this.visualMapModel,n=e.get("textGap"),i=e.textStyleModel,o=i.getFont(),a=i.getTextColor(),r=this._getItemAlign(),s=e.itemSize,l=this._getViewData(),u=l.endsText,d=Ce(e.get("showLabel",!0),!u);u&&this._renderEndsText(t,u[0],s,d,r),p(l.viewPieceList,(function(i){var l=i.piece,u=new Y;u.onclick=gt(this._onItemClick,this,l),this._enableHoverLink(u,i.indexInModelPieceList);var h=e.getRepresentValue(l);if(this._createItemSymbol(u,h,[0,0,s[0],s[1]]),d){var c=this.visualMapModel.getValueState(h);u.add(new se({style:{x:"right"===r?-n:s[0]+n,y:s[1]/2,text:l.text,verticalAlign:"middle",align:r,font:o,fill:a,opacity:"outOfRange"===c?.5:1}}))}t.add(u)}),this),u&&this._renderEndsText(t,u[1],s,d,r),po(e.get("orient"),t,e.get("itemGap")),this.renderBackground(t),this.positionGroup(t)},e.prototype._enableHoverLink=function(t,e){var n=this;t.on("mouseover",(function(){return i("highlight")})).on("mouseout",(function(){return i("downplay")}));var i=function(t){var i=n.visualMapModel;i.option.hoverLink&&n.api.dispatchAction({type:t,batch:Uf(i.findTargetDataIndices(e),i)})}},e.prototype._getItemAlign=function(){var t=this.visualMapModel,e=t.option;if("vertical"===e.orient)return Xf(t,this.api,t.itemSize);var n=e.align;return n&&"auto"!==n||(n="left"),n},e.prototype._renderEndsText=function(t,e,n,i,o){if(e){var a=new Y,r=this.visualMapModel.textStyleModel;a.add(new se({style:ue(r,{x:i?"right"===o?n[0]:0:n[0]/2,y:n[1]/2,verticalAlign:"middle",align:i?o:"center",text:e})})),t.add(a)}},e.prototype._getViewData=function(){var t=this.visualMapModel,e=ht(t.getPieceList(),(function(t,e){return{piece:t,indexInModelPieceList:e}})),n=t.get("text"),i=t.get("orient"),o=t.get("inverse");return("horizontal"===i?o:!o)?e.reverse():n&&(n=n.slice().reverse()),{viewPieceList:e,endsText:n}},e.prototype._createItemSymbol=function(t,e,n){t.add(U(this.getControllerVisual(e,"symbol"),n[0],n[1],n[2],n[3],this.getControllerVisual(e,"color")))},e.prototype._onItemClick=function(t){var e=this.visualMapModel,n=e.option,i=n.selectedMode;if(i){var o=st(n.selected),a=e.getSelectedMapKey(t);"single"===i||!0===i?(o[a]=!0,p(o,(function(t,e){o[e]=e===a}))):o[a]=!o[a],this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:o})}},e.type="visualMap.piecewise",e}(Zf);function yg(t){t.registerComponentModel(cg),t.registerComponentView(gg),dg(t)}var vg={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},mg=function(){function t(t){if(null==(this._condVal=xt(t)?new RegExp(t):yo(t)?t:null)){pn("")}}return t.prototype.evaluate=function(t){var e=typeof t;return xt(e)?this._condVal.test(t):!!Nt(e)&&this._condVal.test(t+"")},t}(),xg=function(){function t(){}return t.prototype.evaluate=function(){return this.value},t}(),_g=function(){function t(){}return t.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(!t[e].evaluate())return!1;return!0},t}(),bg=function(){function t(){}return t.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(t[e].evaluate())return!0;return!1},t}(),Sg=function(){function t(){}return t.prototype.evaluate=function(){return!this.child.evaluate()},t}(),Ig=function(){function t(){}return t.prototype.evaluate=function(){for(var t=!!this.valueParser,e=(0,this.getValue)(this.valueGetterParam),n=t?this.valueParser(e):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(t?n:e))return!1;return!0},t}();function wg(t,e){if(!0===t||!1===t){var n=new xg;return n.value=t,n}return Lg(t)||pn(""),t.and?Mg("and",t,e):t.or?Mg("or",t,e):t.not?function(t,e){var n=t.not,i="";Lg(n)||pn(i);var o=new Sg;o.child=wg(n,e),o.child||pn(i);return o}(t,e):function(t,e){for(var n="",i=e.prepareGetValue(t),o=[],r=a(t),s=t.parser,l=s?fo(s):null,u=0;u<r.length;u++){var d=r[u];if("parser"!==d&&!e.valueGetterAttrMap.get(d)){var h=Nn(vg,d)?vg[d]:d,c=t[d],p=l?l(c):c,f=go(h,p)||"reg"===h&&new mg(p);f||pn(n),o.push(f)}}o.length||pn(n);var g=new Ig;return g.valueGetterParam=i,g.valueParser=l,g.getValue=e.getValue,g.subCondList=o,g}(t,e)}function Mg(t,e,n){var i=e[t];tt(i)||pn(""),i.length||pn("");var o="and"===t?new _g:new bg;return o.children=ht(i,(function(t){return wg(t,n)})),o.children.length||pn(""),o}function Lg(t){return M(t)&&!On(t)}var Dg=function(){function t(t,e){this._cond=wg(t,e)}return t.prototype.evaluate=function(){return this._cond.evaluate()},t}();var Cg={type:"echarts:filter",transform:function(t){for(var e,n,i,o=t.upstream,a=(n=t.config,i={valueGetterAttrMap:ie({dimension:!0}),prepareGetValue:function(t){var e=t.dimension;Nn(t,"dimension")||pn("");var n=o.getDimensionInfo(e);return n||pn(""),{dimIdx:n.index}},getValue:function(t){return o.retrieveValueFromItem(e,t.dimIdx)}},new Dg(n,i)),r=[],s=0,l=o.count();s<l;s++)e=o.getRawDataItem(s),a.evaluate()&&r.push(e);return{data:r}}},Tg={type:"echarts:sort",transform:function(t){var e=t.upstream,n=t.config,i="",o=oe(n);o.length||pn(i);var a=[];p(o,(function(t){var n=t.dimension,o=t.order,r=t.parser,s=t.incomparable;if(null==n&&pn(i),"asc"!==o&&"desc"!==o&&pn(i),s&&"min"!==s&&"max"!==s){pn("")}if("asc"!==o&&"desc"!==o){pn("")}var l=e.getDimensionInfo(n);l||pn(i);var u=r?fo(r):null;r&&!u&&pn(i),a.push({dimIdx:l.index,parser:u,comparator:new vo(o,s)})}));var r=e.sourceFormat;r!==cn&&r!==mo&&pn(i);for(var s=[],l=0,u=e.count();l<u;l++)s.push(e.getRawDataItem(l));return s.sort((function(t,n){for(var i=0;i<a.length;i++){var o=a[i],r=e.retrieveValueFromItem(t,o.dimIdx),s=e.retrieveValueFromItem(n,o.dimIdx);o.parser&&(r=o.parser(r),s=o.parser(s));var l=o.comparator.evaluate(r,s);if(0!==l)return l}return 0})),{data:s}}};var Ag=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return w(e,t),e.prototype.init=function(e,n,i){t.prototype.init.call(this,e,n,i),this._sourceManager=new xo(this),_o(this)},e.prototype.mergeOption=function(e,n){t.prototype.mergeOption.call(this,e,n),_o(this)},e.prototype.optionUpdated=function(){this._sourceManager.dirty()},e.prototype.getSourceManager=function(){return this._sourceManager},e.type="dataset",e.defaultOption={seriesLayoutBy:bo},e}(wt),Pg=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return w(e,t),e.type="dataset",e}(Tt);var kg=So.CMD;function Ng(t,e){return Math.abs(t-e)<1e-5}function Vg(t){var e,n,i,o,a,r,s,l,u,d,h,c,p,f,g,y,v,m,x,_,b,S,I,w,M=t.data,L=t.len(),D=[],C=0,T=0,A=0,P=0;function k(t,n){e&&e.length>2&&D.push(e),e=[t,n]}function N(t,n,i,o){Ng(t,i)&&Ng(n,o)||e.push(t,n,i,o,i,o)}for(var V=0;V<L;){var R=M[V++],z=1===V;switch(z&&(A=C=M[V],P=T=M[V+1],R!==kg.L&&R!==kg.C&&R!==kg.Q||(e=[A,P])),R){case kg.M:C=A=M[V++],T=P=M[V++],k(A,P);break;case kg.L:N(C,T,n=M[V++],i=M[V++]),C=n,T=i;break;case kg.C:e.push(M[V++],M[V++],M[V++],M[V++],C=M[V++],T=M[V++]);break;case kg.Q:n=M[V++],i=M[V++],o=M[V++],a=M[V++],e.push(C+2/3*(n-C),T+2/3*(i-T),o+2/3*(n-o),a+2/3*(i-a),o,a),C=o,T=a;break;case kg.A:var O=M[V++],E=M[V++],B=M[V++],G=M[V++],H=M[V++],F=M[V++]+H;V+=1;var W=!M[V++];n=Math.cos(H)*B+O,i=Math.sin(H)*G+E,z?k(A=n,P=i):N(C,T,n,i),C=Math.cos(F)*B+O,T=Math.sin(F)*G+E;for(var Z=(W?-1:1)*Math.PI/2,Y=H;W?Y>F:Y<F;Y+=Z){var X=W?Math.max(Y+Z,F):Math.min(Y+Z,F);r=Y,s=X,l=O,u=E,d=B,h=G,c=void 0,p=void 0,f=void 0,g=void 0,y=void 0,v=void 0,m=void 0,x=void 0,_=void 0,b=void 0,S=void 0,I=void 0,w=void 0,c=Math.abs(s-r),p=4*Math.tan(c/4)/3,f=s<r?-1:1,g=Math.cos(r),y=Math.sin(r),v=Math.cos(s),m=Math.sin(s),x=g*d+l,_=y*h+u,b=v*d+l,S=m*h+u,I=d*p*f,w=h*p*f,e.push(x-I*y,_+w*g,b+I*m,S-w*v,b,S)}break;case kg.R:A=C=M[V++],P=T=M[V++],n=A+M[V++],i=P+M[V++],k(n,P),N(n,P,n,i),N(n,i,A,i),N(A,i,A,P),N(A,P,n,P);break;case kg.Z:e&&N(C,T,A,P),C=A,T=P}}return e&&e.length>2&&D.push(e),D}function Rg(t,e,n,i,o,a,r,s,l,u){if(Ng(t,n)&&Ng(e,i)&&Ng(o,r)&&Ng(a,s))l.push(r,s);else{var d=2/u,h=d*d,c=r-t,p=s-e,f=Math.sqrt(c*c+p*p);c/=f,p/=f;var g=n-t,y=i-e,v=o-r,m=a-s,x=g*g+y*y,_=v*v+m*m;if(x<h&&_<h)l.push(r,s);else{var b=c*g+p*y,S=-c*v-p*m;if(x-b*b<h&&b>=0&&_-S*S<h&&S>=0)l.push(r,s);else{var I=[],w=[];Io(t,n,o,r,.5,I),Io(e,i,a,s,.5,w),Rg(I[0],w[0],I[1],w[1],I[2],w[2],I[3],w[3],l,u),Rg(I[4],w[4],I[5],w[5],I[6],w[6],I[7],w[7],l,u)}}}}function zg(t,e,n){var i=t[e],o=t[1-e],a=Math.abs(i/o),r=Math.ceil(Math.sqrt(a*n)),s=Math.floor(n/r);0===s&&(s=1,r=n);for(var l=[],u=0;u<r;u++)l.push(s);var d=n-r*s;if(d>0)for(u=0;u<d;u++)l[u%r]+=1;return l}function Og(t,e,n){for(var i=t.r0,o=t.r,a=t.startAngle,r=t.endAngle,s=Math.abs(r-a),l=s*o,u=o-i,d=l>Math.abs(u),h=zg([l,u],d?0:1,e),c=(d?s:u)/h.length,p=0;p<h.length;p++)for(var f=(d?u:s)/h[p],g=0;g<h[p];g++){var y={};d?(y.startAngle=a+c*p,y.endAngle=a+c*(p+1),y.r0=i+f*g,y.r=i+f*(g+1)):(y.startAngle=a+f*g,y.endAngle=a+f*(g+1),y.r0=i+c*p,y.r=i+c*(p+1)),y.clockwise=t.clockwise,y.cx=t.cx,y.cy=t.cy,n.push(y)}}function Eg(t,e,n,i){return t*i-n*e}function Bg(t,e,n,i,o,a,r,s){var l=n-t,u=i-e,d=r-o,h=s-a,c=Eg(d,h,l,u);if(Math.abs(c)<1e-6)return null;var p=Eg(t-o,e-a,d,h)/c;return p<0||p>1?null:new tn(p*l+t,p*u+e)}function Gg(t,e,n){var i=new tn;tn.sub(i,n,e),i.normalize();var o=new tn;return tn.sub(o,t,e),o.dot(i)}function Hg(t,e){var n=t[t.length-1];n&&n[0]===e[0]&&n[1]===e[1]||t.push(e)}function Fg(t){var n=t.points,i=[],o=[];Ot(n,i,o);var a=new e(i[0],i[1],o[0]-i[0],o[1]-i[1]),r=a.width,s=a.height,l=a.x,u=a.y,d=new tn,h=new tn;return r>s?(d.x=h.x=l+r/2,d.y=u,h.y=u+s):(d.y=h.y=u+s/2,d.x=l,h.x=l+r),function(t,e,n){for(var i=t.length,o=[],a=0;a<i;a++){var r=t[a],s=t[(a+1)%i],l=Bg(r[0],r[1],s[0],s[1],e.x,e.y,n.x,n.y);l&&o.push({projPt:Gg(l,e,n),pt:l,idx:a})}if(o.length<2)return[{points:t},{points:t}];o.sort((function(t,e){return t.projPt-e.projPt}));var u=o[0],d=o[o.length-1];if(d.idx<u.idx){var h=u;u=d,d=h}var c=[u.pt.x,u.pt.y],p=[d.pt.x,d.pt.y],f=[c],g=[p];for(a=u.idx+1;a<=d.idx;a++)Hg(f,t[a].slice());for(Hg(f,p),Hg(f,c),a=d.idx+1;a<=u.idx+i;a++)Hg(g,t[a%i].slice());return Hg(g,c),Hg(g,p),[{points:f},{points:g}]}(n,d,h)}function Wg(t,e,n,i){if(1===n)i.push(e);else{var o=Math.floor(n/2),a=t(e);Wg(t,a[0],o,i),Wg(t,a[1],n-o,i)}return i}function Zg(t,e){var n,i=[],o=t.shape;switch(t.type){case"rect":!function(t,e,n){for(var i=t.width,o=t.height,a=i>o,r=zg([i,o],a?0:1,e),s=a?"width":"height",l=a?"height":"width",u=a?"x":"y",d=a?"y":"x",h=t[s]/r.length,c=0;c<r.length;c++)for(var p=t[l]/r[c],f=0;f<r[c];f++){var g={};g[u]=c*h,g[d]=f*p,g[s]=h,g[l]=p,g.x+=t.x,g.y+=t.y,n.push(g)}}(o,e,i),n=be;break;case"sector":Og(o,e,i),n=Pn;break;case"circle":Og({r0:0,r:o.r,startAngle:0,endAngle:2*Math.PI,cx:o.cx,cy:o.cy},e,i),n=Pn;break;default:var a=t.getComputedTransform(),r=a?Math.sqrt(Math.max(a[0]*a[0]+a[1]*a[1],a[2]*a[2]+a[3]*a[3])):1,s=ht(function(t,e){var n=Vg(t),i=[];e=e||1;for(var o=0;o<n.length;o++){var a=n[o],r=[],s=a[0],l=a[1];r.push(s,l);for(var u=2;u<a.length;){var d=a[u++],h=a[u++],c=a[u++],p=a[u++],f=a[u++],g=a[u++];Rg(s,l,d,h,c,p,f,g,r,e),s=f,l=g}i.push(r)}return i}(t.getUpdatedPathProxy(),r),(function(t){return function(t){for(var e=[],n=0;n<t.length;)e.push([t[n++],t[n++]]);return e}(t)})),l=s.length;if(0===l)Wg(Fg,{points:s[0]},e,i);else if(l===e)for(var u=0;u<l;u++)i.push({points:s[u]});else{var d=0,h=ht(s,(function(t){var e=[],n=[];Ot(t,e,n);var i=(n[1]-e[1])*(n[0]-e[0]);return d+=i,{poly:t,area:i}}));h.sort((function(t,e){return e.area-t.area}));var c=e;for(u=0;u<l;u++){var p=h[u];if(c<=0)break;var f=u===l-1?c:Math.ceil(p.area/d*e);f<0||(Wg(Fg,{points:p.poly},f,i),c-=f)}}n=et}if(!n)return function(t,e){for(var n=[],i=0;i<e;i++)n.push(wo(t));return n}(t,e);var g,y,v=[];for(u=0;u<i.length;u++){var m=new n;m.setShape(i[u]),g=t,(y=m).setStyle(g.style),y.z=g.z,y.z2=g.z2,y.zlevel=g.zlevel,v.push(m)}return v}function Yg(t,e){var n=t.length,i=e.length;if(n===i)return[t,e];for(var o=[],a=[],r=n<i?t:e,s=Math.min(n,i),l=Math.abs(i-n)/6,u=(s-2)/6,d=Math.ceil(l/u)+1,h=[r[0],r[1]],c=l,p=2;p<s;){var f=r[p-2],g=r[p-1],y=r[p++],v=r[p++],m=r[p++],x=r[p++],_=r[p++],b=r[p++];if(c<=0)h.push(y,v,m,x,_,b);else{for(var S=Math.min(c,d-1)+1,I=1;I<=S;I++){var w=I/S;Io(f,y,m,_,w,o),Io(g,v,x,b,w,a),f=o[3],g=a[3],h.push(o[1],a[1],o[2],a[2],f,g),y=o[5],v=a[5],m=o[6],x=a[6]}c-=S-1}}return r===t?[h,e]:[t,h]}function Xg(t,e){for(var n=t.length,i=t[n-2],o=t[n-1],a=[],r=0;r<e.length;)a[r++]=i,a[r++]=o;return a}function Ug(t){for(var e=0,n=0,i=0,o=t.length,a=0,r=o-2;a<o;r=a,a+=2){var s=t[r],l=t[r+1],u=t[a],d=t[a+1],h=s*d-u*l;e+=h,n+=(s+u)*h,i+=(l+d)*h}return 0===e?[t[0]||0,t[1]||0]:[n/e/3,i/e/3,e]}function jg(t,e,n,i){for(var o=(t.length-2)/6,a=1/0,r=0,s=t.length,l=s-2,u=0;u<o;u++){for(var d=6*u,h=0,c=0;c<s;c+=2){var p=0===c?d:(d+c-2)%l+2,f=t[p]-n[0],g=t[p+1]-n[1],y=e[c]-i[0]-f,v=e[c+1]-i[1]-g;h+=y*y+v*v}h<a&&(a=h,r=u)}return r}function qg(t){for(var e=[],n=t.length,i=0;i<n;i+=2)e[i]=t[n-i-2],e[i+1]=t[n-i-1];return e}function Kg(t){return t.__isCombineMorphing}var $g="__mOriginal_";function Qg(t,e,n){var i=$g+e,o=t[i]||t[e];t[i]||(t[i]=t[e]);var a=n.replace,r=n.after,s=n.before;t[e]=function(){var t,e=arguments;return s&&s.apply(this,e),t=a?a.apply(this,e):o.apply(this,e),r&&r.apply(this,e),t}}function Jg(t,e){var n=$g+e;t[n]&&(t[e]=t[n],t[n]=null)}function ty(t,e){for(var n=0;n<t.length;n++)for(var i=t[n],o=0;o<i.length;){var a=i[o],r=i[o+1];i[o++]=e[0]*a+e[2]*r+e[4],i[o++]=e[1]*a+e[3]*r+e[5]}}function ey(t,e){var n=t.getUpdatedPathProxy(),i=e.getUpdatedPathProxy(),o=function(t,e){for(var n,i,o,a=[],r=[],s=0;s<Math.max(t.length,e.length);s++){var l=t[s],u=e[s],d=void 0,h=void 0;l?u?(i=d=(n=Yg(l,u))[0],o=h=n[1]):(h=Xg(o||l,l),d=l):(d=Xg(i||u,u),h=u),a.push(d),r.push(h)}return[a,r]}(Vg(n),Vg(i)),a=o[0],r=o[1],s=t.getComputedTransform(),l=e.getComputedTransform();s&&ty(a,s),l&&ty(r,l),Qg(e,"updateTransform",{replace:function(){this.transform=null}}),e.transform=null;var u=function(t,e,n,i){for(var o,a=[],r=0;r<t.length;r++){var s=t[r],l=e[r],u=Ug(s),d=Ug(l);null==o&&(o=u[2]<0!=d[2]<0);var h=[],c=[],p=0,f=1/0,g=[],y=s.length;o&&(s=qg(s));for(var v=6*jg(s,l,u,d),m=y-2,x=0;x<m;x+=2){var _=(v+x)%m+2;h[x+2]=s[_]-u[0],h[x+3]=s[_+1]-u[1]}h[0]=s[v]-u[0],h[1]=s[v+1]-u[1];for(var b=i/n,S=-i/2;S<=i/2;S+=b){var I=Math.sin(S),w=Math.cos(S),M=0;for(x=0;x<s.length;x+=2){var L=h[x],D=h[x+1],C=l[x]-d[0],T=l[x+1]-d[1],A=C*w-T*I,P=C*I+T*w;g[x]=A,g[x+1]=P;var k=A-L,N=P-D;M+=k*k+N*N}if(M<f){f=M,p=S;for(var V=0;V<g.length;V++)c[V]=g[V]}}a.push({from:h,to:c,fromCp:u,toCp:d,rotation:-p})}return a}(a,r,10,Math.PI),d=[];Qg(e,"buildPath",{replace:function(t){for(var n=e.__morphT,i=1-n,o=[],a=0;a<u.length;a++){var r=u[a],s=r.from,l=r.to,h=r.rotation*n,c=r.fromCp,p=r.toCp,f=Math.sin(h),g=Math.cos(h);Mo(o,c,p,n);for(var y=0;y<s.length;y+=2){var v=s[y],m=s[y+1],x=v*i+(I=l[y])*n,_=m*i+(w=l[y+1])*n;d[y]=x*g-_*f+o[0],d[y+1]=x*f+_*g+o[1]}var b=d[0],S=d[1];t.moveTo(b,S);for(y=2;y<s.length;){var I=d[y++],w=d[y++],M=d[y++],L=d[y++],D=d[y++],C=d[y++];b===I&&S===w&&M===D&&L===C?t.lineTo(D,C):t.bezierCurveTo(I,w,M,L,D,C),b=D,S=C}}}})}function ny(t,e,n){if(!t||!e)return e;var i=n.done,o=n.during;return ey(t,e),e.__morphT=0,e.animateTo({__morphT:1},ot({during:function(t){e.dirtyShape(),o&&o(t)},done:function(){Jg(e,"buildPath"),Jg(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape(),i&&i()}},n)),e}function iy(t,e,n,i,o,a){t=o===n?0:Math.round(32767*(t-n)/(o-n)),e=a===i?0:Math.round(32767*(e-i)/(a-i));for(var r,s=0,l=32768;l>0;l/=2){var u=0,d=0;(t&l)>0&&(u=1),(e&l)>0&&(d=1),s+=l*l*(3*u^d),0===d&&(1===u&&(t=l-1-t,e=l-1-e),r=t,t=e,e=r)}return s}function oy(t){var e=1/0,n=1/0,i=-1/0,o=-1/0,a=ht(t,(function(t){var a=t.getBoundingRect(),r=t.getComputedTransform(),s=a.x+a.width/2+(r?r[4]:0),l=a.y+a.height/2+(r?r[5]:0);return e=Math.min(s,e),n=Math.min(l,n),i=Math.max(s,i),o=Math.max(l,o),[s,l]}));return ht(a,(function(a,r){return{cp:a,z:iy(a[0],a[1],e,n,i,o),path:t[r]}})).sort((function(t,e){return t.z-e.z})).map((function(t){return t.path}))}function ay(t){return Zg(t.path,t.count)}function ry(t){return tt(t[0])}function sy(t,e){for(var n=[],i=t.length,o=0;o<i;o++)n.push({one:t[o],many:[]});for(o=0;o<e.length;o++){var a=e[o].length,r=void 0;for(r=0;r<a;r++)n[r%i].many.push(e[o][r])}var s=0;for(o=i-1;o>=0;o--)if(!n[o].many.length){var l=n[s].many;if(l.length<=1){if(!s)return n;s=0}a=l.length;var u=Math.ceil(a/2);n[o].many=l.slice(u,a),n[s].many=l.slice(0,u),s++}return n}var ly={clone:function(t){for(var e=[],n=1-Math.pow(1-t.path.style.opacity,1/t.count),i=0;i<t.count;i++){var o=wo(t.path);o.setStyle("opacity",n),e.push(o)}return e},split:null};function uy(t,e,i,o,a,r){if(t.length&&e.length){var s=Vn("update",o,a);if(s&&s.duration>0){var l,u,d=o.getModel("universalTransition").get("delay"),h=Object.assign({setToFinal:!0},s);ry(t)&&(l=t,u=e),ry(e)&&(l=e,u=t);for(var c=l?l===t:t.length>e.length,p=l?sy(u,l):sy(c?e:t,[c?t:e]),f=0,g=0;g<p.length;g++)f+=p[g].many.length;var y=0;for(g=0;g<p.length;g++)v(p[g],c,y,f),y+=p[g].many.length}}function v(t,e,o,a,s){var l=t.many,u=t.one;if(1!==l.length||s)for(var c=ot({dividePath:ly[i],individualDelay:d&&function(t,e,n,i){return d(t+o,a)}},h),p=e?function(t,e,i){var o=[];!function t(e){for(var n=0;n<e.length;n++){var i=e[n];Kg(i)?t(i.childrenRef()):i instanceof X&&o.push(i)}}(t);var a=o.length;if(!a)return{fromIndividuals:[],toIndividuals:[],count:0};var r=(i.dividePath||ay)({path:e,count:a});if(r.length!==a)return{fromIndividuals:[],toIndividuals:[],count:0};o=oy(o),r=oy(r);for(var s=i.done,l=i.during,u=i.individualDelay,d=new n,h=0;h<a;h++){var c=o[h],p=r[h];p.parent=e,p.copyTransform(d),u||ey(c,p)}function f(t){for(var e=0;e<r.length;e++)r[e].addSelfToZr(t)}function g(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,Jg(e,"addSelfToZr"),Jg(e,"removeSelfFromZr")}e.__isCombineMorphing=!0,e.childrenRef=function(){return r},Qg(e,"addSelfToZr",{after:function(t){f(t)}}),Qg(e,"removeSelfFromZr",{after:function(t){for(var e=0;e<r.length;e++)r[e].removeSelfFromZr(t)}});var y=r.length;if(u){var v=y,m=function(){0==--v&&(g(),s&&s())};for(h=0;h<y;h++){var x=u?ot({delay:(i.delay||0)+u(h,y,o[h],r[h]),done:m},i):i;ny(o[h],r[h],x)}}else e.__morphT=0,e.animateTo({__morphT:1},ot({during:function(t){for(var n=0;n<y;n++){var i=r[n];i.__morphT=e.__morphT,i.dirtyShape()}l&&l(t)},done:function(){g();for(var e=0;e<t.length;e++)Jg(t[e],"updateTransform");s&&s()}},i));return e.__zr&&f(e.__zr),{fromIndividuals:o,toIndividuals:r,count:y}}(l,u,c):function(t,e,n){var i=e.length,o=[],a=n.dividePath||ay;if(Kg(t)){!function t(e){for(var n=0;n<e.length;n++){var i=e[n];Kg(i)?t(i.childrenRef()):i instanceof X&&o.push(i)}}(t.childrenRef());var r=o.length;if(r<i)for(var s=0,l=r;l<i;l++)o.push(wo(o[s++%r]));o.length=i}else{o=a({path:t,count:i});var u=t.getComputedTransform();for(l=0;l<o.length;l++)o[l].setLocalTransform(u);if(o.length!==i)return{fromIndividuals:[],toIndividuals:[],count:0}}o=oy(o),e=oy(e);var d=n.individualDelay;for(l=0;l<i;l++){var h=d?ot({delay:(n.delay||0)+d(l,i,o[l],e[l])},n):n;ny(o[l],e[l],h)}return{fromIndividuals:o,toIndividuals:e,count:e.length}}(u,l,c),f=p.fromIndividuals,g=p.toIndividuals,y=f.length,m=0;m<y;m++){x=d?ot({delay:d(m,y)},h):h;r(f[m],g[m],e?l[m]:t.one,e?t.one:l[m],x)}else{var x,_=e?l[0]:u,b=e?u:l[0];if(Kg(_))v({many:[_],one:b},!0,o,a,!0);else ny(_,b,x=d?ot({delay:d(o,a)},h):h),r(_,b,_,b,x)}}}function dy(t){if(!t)return[];if(tt(t)){for(var e=[],n=0;n<t.length;n++)e.push(dy(t[n]));return e}var i=[];return t.traverse((function(t){t instanceof X&&!t.disableMorphing&&!t.invisible&&!t.ignore&&i.push(t)})),i}var hy=t();function cy(t,e,n,i){var o=function(t,e){for(var n=t.dimensions,i=0;i<n.length;i++){var o=t.getDimensionInfo(n[i]);if(o&&0===o.otherDims[e])return n[i]}}(t,i?"itemChildGroupId":"itemGroupId");if(o){var a=function(t,e,n){var i=t.getDimensionInfo(n),o=i&&i.ordinalMeta;if(i){var a=t.get(i.name,e);return o&&o.categories[a]||a+""}}(t,e,o);return a}var r=t.getRawDataItem(e),s=i?"childGroupId":"groupId";return r&&r[s]?r[s]+"":i?void 0:n||t.getId(e)}function py(t){var e=[];return p(t,(function(t){var n=t.data,i=t.dataGroupId;if(!(n.count()>1e4))for(var o=n.getIndices(),a=0;a<o.length;a++)e.push({data:n,groupId:cy(n,a,i,!1),childGroupId:cy(n,a,i,!0),divide:t.divide,dataIndex:a})})),e}function fy(t,e,n){t.traverse((function(t){t instanceof X&&_(t,{style:{opacity:0}},e,{dataIndex:n,isFrom:!0})}))}function gy(t){if(t.parent){var e=t.getComputedTransform();t.setLocalTransform(e),t.parent.remove(t)}}function yy(t){t.stopAnimation(),t.isGroup&&t.traverse((function(t){t.stopAnimation()}))}function vy(t,e,n){var i=py(t),o=py(e);function a(t,e,n,i,o){(n||t)&&e.animateFrom({style:n&&n!==t?F(F({},n.style),t.style):t.style},o)}var r=!1,s=0,l=ie(),d=ie();i.forEach((function(t){t.groupId&&l.set(t.groupId,!0),t.childGroupId&&d.set(t.childGroupId,!0)}));for(var h=0;h<o.length;h++){var c=o[h].groupId;if(d.get(c)){s=1;break}var f=o[h].childGroupId;if(f&&l.get(f)){s=2;break}}function g(t,e){return function(n){var i=n.data,o=n.dataIndex;return e?i.getId(o):t?1===s?n.childGroupId:n.groupId:2===s?n.childGroupId:n.groupId}}var y=function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++){var o=t[i],a=e[i];if(o.data.getId(o.dataIndex)!==a.data.getId(a.dataIndex))return!1}return!0}(i,o),v={};if(!y)for(h=0;h<o.length;h++){var m=o[h],x=m.data.getItemGraphicEl(m.dataIndex);x&&(v[x.id]=!0)}function _(t,e){var n=i[e],s=o[t],l=s.data.hostModel,u=n.data.getItemGraphicEl(n.dataIndex),d=s.data.getItemGraphicEl(s.dataIndex);u!==d?u&&v[u.id]||d&&(yy(d),u?(yy(u),gy(u),r=!0,uy(dy(u),dy(d),s.divide,l,t,a)):fy(d,l,t)):d&&function(t,e,n){var i=Vn("update",n,e);i&&t.traverse((function(t){if(t instanceof _e){var e=Do(t);e&&t.animateFrom({style:e},i)}}))}(d,s.dataIndex,l)}new de(i,o,g(!0,y),g(!1,y),null,"multiple").update(_).updateManyToOne((function(t,e){var n=o[t],s=n.data,l=s.hostModel,d=s.getItemGraphicEl(n.dataIndex),h=u(ht(e,(function(t){return i[t].data.getItemGraphicEl(i[t].dataIndex)})),(function(t){return t&&t!==d&&!v[t.id]}));d&&(yy(d),h.length?(p(h,(function(t){yy(t),gy(t)})),r=!0,uy(dy(h),dy(d),n.divide,l,t,a)):fy(d,l,n.dataIndex))})).updateOneToMany((function(t,e){var n=i[e],s=n.data.getItemGraphicEl(n.dataIndex);if(!s||!v[s.id]){var l=u(ht(t,(function(t){return o[t].data.getItemGraphicEl(o[t].dataIndex)})),(function(t){return t&&t!==s})),d=o[t[0]].data.hostModel;l.length&&(p(l,(function(t){return yy(t)})),s?(yy(s),gy(s),r=!0,uy(dy(s),dy(l),n.divide,d,t[0],a)):p(l,(function(e){return fy(e,d,t[0])})))}})).updateManyToMany((function(t,e){new de(e,t,(function(t){return i[t].data.getId(i[t].dataIndex)}),(function(t){return o[t].data.getId(o[t].dataIndex)})).update((function(n,i){_(t[n],e[i])})).execute()})).execute(),r&&p(e,(function(t){var e=t.data.hostModel,i=e&&n.getViewOfSeriesModel(e),o=Vn("update",e,0);i&&e.isAnimationEnabled()&&o&&o.duration>0&&i.group.traverse((function(t){t instanceof X&&!t.animators.length&&t.animateFrom({style:{opacity:0}},o)}))}))}function my(t){var e=t.getModel("universalTransition").get("seriesKey");return e||t.id}function xy(t){return tt(t)?t.sort().join(","):t}function _y(t){if(t.hostModel)return t.hostModel.getModel("universalTransition").get("divideShape")}function by(t,e){for(var n=0;n<t.length;n++){if(null!=e.seriesIndex&&e.seriesIndex===t[n].seriesIndex||null!=e.seriesId&&e.seriesId===t[n].id)return n}}$([function(t){t.registerPainter("canvas",ia)}]),$([Co]),$([To,Ao,Po,function(t){$(Q),t.registerSeriesModel(oa),t.registerChartView(la),t.registerLayout(j("scatter"))},function(t){$(Sa),t.registerChartView(pa),t.registerSeriesModel(fa),t.registerLayout(ua),t.registerProcessor(Vt("radar")),t.registerPreprocessor(ca)},ko,function(t){t.registerChartView(Va),t.registerSeriesModel(nr),t.registerLayout(or),t.registerVisual(ar),function(t){t.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},(function(t,e){e.eachComponent({mainType:"series",subType:"tree",query:t},(function(e){var n=t.dataIndex,i=e.getData().tree.getNodeByDataIndex(n);i.isExpand=!i.isExpand}))})),t.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},(function(t,e,n){e.eachComponent({mainType:"series",subType:"tree",query:t},(function(e){var i=e.coordinateSystem,o=te(i,t,void 0,n);e.setCenter&&e.setCenter(o.center),e.setZoom&&e.setZoom(o.zoom)}))}))}(t)},function(t){t.registerSeriesModel(lr),t.registerChartView(wr),t.registerVisual(Hr),t.registerLayout(ts),function(t){for(var e=0;e<rr.length;e++)t.registerAction({type:rr[e],update:"updateView"},ee);t.registerAction({type:"treemapRootToNode",update:"updateView"},(function(t,e){e.eachComponent({mainType:"series",subType:"treemap",query:t},(function(e,n){var i=Qa(t,["treemapZoomToNode","treemapRootToNode"],e);if(i){var o=e.getViewRoot();o&&(t.direction=tr(o,i.node)?"rollUp":"drillDown"),e.resetViewRoot(i.node)}}))}))}(t)},function(t){t.registerChartView(el),t.registerSeriesModel(ll),t.registerProcessor(rs),t.registerVisual(ss),t.registerVisual(us),t.registerLayout(xs),t.registerLayout(t.PRIORITY.VISUAL.POST_CHART_LAYOUT,Ds),t.registerLayout(Ts),t.registerCoordinateSystem("graphView",{dimensions:Et.dimensions,create:As}),t.registerAction({type:"focusNodeAdjacency",event:"focusNodeAdjacency",update:"series:focusNodeAdjacency"},ee),t.registerAction({type:"unfocusNodeAdjacency",event:"unfocusNodeAdjacency",update:"series:unfocusNodeAdjacency"},ee),t.registerAction(ul,(function(t,e,n){e.eachComponent({mainType:"series",query:t},(function(e){var i=e.coordinateSystem,o=te(i,t,void 0,n);e.setCenter&&e.setCenter(o.center),e.setZoom&&e.setZoom(o.zoom)}))}))},No,function(t){t.registerChartView(cl),t.registerSeriesModel(pl),t.registerLayout(fl),t.registerProcessor(Vt("funnel"))},function(t){$(an),t.registerChartView(gl),t.registerSeriesModel(_l),t.registerVisual(t.PRIORITY.VISUAL.BRUSH,Il)},function(t){t.registerChartView(Ll),t.registerSeriesModel(Cl),t.registerLayout(Tl),t.registerVisual(Hl),t.registerAction({type:"dragNode",event:"dragnode",update:"update"},(function(t,e){e.eachComponent({mainType:"series",subType:"sankey",query:t},(function(e){e.setNodePosition(t.dataIndex,[t.localX,t.localY])}))}))},function(t){t.registerSeriesModel(Wl),t.registerChartView(Zl),t.registerLayout($l),t.registerTransform(Ql)},function(t){t.registerChartView(tu),t.registerSeriesModel(hu),t.registerPreprocessor(cu),t.registerVisual(mu),t.registerLayout(xu)},function(t){t.registerChartView(Iu),t.registerSeriesModel(wu),t.registerLayout(j("effectScatter"))},function(t){t.registerChartView(ku),t.registerSeriesModel(zu),t.registerLayout(Pu),t.registerVisual(Eu)},function(t){t.registerChartView(Hu),t.registerSeriesModel(Fu)},Vo,function(t){t.registerChartView(Wu),t.registerSeriesModel(Zu),t.registerLayout(Yu),t.registerProcessor(Vt("themeRiver"))},function(t){t.registerChartView(Ku),t.registerSeriesModel($u),t.registerLayout(qt(td,"sunburst")),t.registerProcessor(qt(Vt,"sunburst")),t.registerVisual(nd),function(t){t.registerAction({type:ju,update:"updateView"},(function(t,e){e.eachComponent({mainType:"series",subType:"sunburst",query:t},(function(e,n){var i=Qa(t,[ju],e);if(i){var o=e.getViewRoot();o&&(t.direction=tr(o,i.node)?"rollUp":"drillDown"),e.resetViewRoot(i.node)}}))})),t.registerAction({type:qu,update:"none"},(function(t,e,n){t=F({},t),e.eachComponent({mainType:"series",subType:"sunburst",query:t},(function(e){var n=Qa(t,[qu],e);n&&(t.dataIndex=n.node.dataIndex)})),n.dispatchAction(F(t,{type:"highlight"}))})),t.registerAction({type:"sunburstUnhighlight",update:"updateView"},(function(t,e,n){t=F({},t),n.dispatchAction(F(t,{type:"downplay"}))}))}(t)},function(t){t.registerChartView(Ud),t.registerSeriesModel(rd)}]),$(Ro),$(zo),$(Oo),$((function(t){$(oi),Un.registerAxisPointerClass("SingleAxisPointer",wh),t.registerComponentView(Ch),t.registerComponentView(fh),t.registerComponentModel(yh),ai(t,"single",yh,yh.defaultOption),t.registerCoordinateSystem("single",bh)})),$(an),$((function(t){t.registerComponentModel(Th),t.registerComponentView(Ph),t.registerCoordinateSystem("calendar",Nh)})),$((function(t){t.registerComponentModel(Bh),t.registerComponentView(Fh),t.registerPreprocessor((function(t){var e=t.graphic;tt(e)?e[0]&&e[0].elements?t.graphic=[t.graphic[0]]:t.graphic=[{elements:e}]:e&&!e.elements&&(t.graphic=[{elements:[e]}])}))})),$((function(t){t.registerComponentModel(gc),t.registerComponentView(yc),pc("saveAsImage",vc),pc("magicType",_c),pc("dataView",Dc),pc("dataZoom",Uc),pc("restore",kc),$(dc)})),$(Eo),$(oi),$((function(t){t.registerComponentView(fp),t.registerComponentModel(gp),t.registerPreprocessor($c),t.registerVisual(t.PRIORITY.VISUAL.BRUSH,up),t.registerAction({type:"brush",event:"brush",update:"updateVisual"},(function(t,e){e.eachComponent({mainType:"brush",query:t},(function(e){e.setAreas(t.areas)}))})),t.registerAction({type:"brushSelect",event:"brushSelected",update:"none"},ee),t.registerAction({type:"brushEnd",event:"brushEnd",update:"none"},ee),pc("brush",mp)})),$(Bo),$((function(t){t.registerComponentModel(_p),t.registerComponentView(Mp),t.registerSubTypeDefaulter("timeline",(function(){return"slider"})),function(t){t.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},(function(t,e,n){var i=e.getComponent("timeline");return i&&null!=t.currentIndex&&(i.setCurrentIndex(t.currentIndex),!i.get("loop",!0)&&i.isIndexMax()&&i.getPlayState()&&(i.setPlayState(!1),n.dispatchAction({type:"timelinePlayChange",playState:!1,from:t.from}))),e.resetOption("timeline",{replaceMerge:i.get("replaceMerge",!0)}),ot({currentIndex:i.option.currentIndex},t)})),t.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},(function(t,e){var n=e.getComponent("timeline");n&&null!=t.playState&&n.setPlayState(t.playState)}))}(t),t.registerPreprocessor(Cp)})),$((function(t){t.registerComponentModel(Rp),t.registerComponentView(Up),t.registerPreprocessor((function(t){Pp(t.series,"markPoint")&&(t.markPoint=t.markPoint||{})}))})),$((function(t){t.registerComponentModel(jp),t.registerComponentView(ef),t.registerPreprocessor((function(t){Pp(t.series,"markLine")&&(t.markLine=t.markLine||{})}))})),$((function(t){t.registerComponentModel(nf),t.registerComponentView(hf),t.registerPreprocessor((function(t){Pp(t.series,"markArea")&&(t.markArea=t.markArea||{})}))})),$(Go),$((function(t){$(Sf),$(Pf)})),$(Sf),$(Pf),$((function(t){$(hg),$(yg)})),$(hg),$(yg),$(Ho),$((function(t){t.registerTransform(Cg),t.registerTransform(Tg)})),$((function(t){t.registerComponentModel(Ag),t.registerComponentView(Pg)})),$((function(t){t.registerUpdateLifecycle("series:beforeupdate",(function(t,e,n){p(oe(n.seriesTransition),(function(t){p(oe(t.to),(function(t){for(var e=n.updatedSeries,i=0;i<e.length;i++)(null!=t.seriesIndex&&t.seriesIndex===e[i].seriesIndex||null!=t.seriesId&&t.seriesId===e[i].id)&&(e[i][Lo]=!0)}))}))})),t.registerUpdateLifecycle("series:transition",(function(t,e,n){var i=hy(e);if(i.oldSeries&&n.updatedSeries&&n.optionChanged){var o=n.seriesTransition;if(o)p(oe(o),(function(t){!function(t,e,n,i){var o=[],a=[];p(oe(t.from),(function(t){var n=by(e.oldSeries,t);n>=0&&o.push({dataGroupId:e.oldDataGroupIds[n],data:e.oldData[n],divide:_y(e.oldData[n]),groupIdDim:t.dimension})})),p(oe(t.to),(function(t){var i=by(n.updatedSeries,t);if(i>=0){var o=n.updatedSeries[i].getData();a.push({dataGroupId:e.oldDataGroupIds[i],data:o,divide:_y(o),groupIdDim:t.dimension})}})),o.length>0&&a.length>0&&vy(o,a,i)}(t,i,n,e)}));else{var a=function(t,e){var n=ie(),i=ie(),o=ie();return p(t.oldSeries,(function(e,n){var a=t.oldDataGroupIds[n],r=t.oldData[n],s=my(e),l=xy(s);i.set(l,{dataGroupId:a,data:r}),tt(s)&&p(s,(function(t){o.set(t,{key:l,dataGroupId:a,data:r})}))})),p(e.updatedSeries,(function(t){if(t.isUniversalTransitionEnabled()&&t.isAnimationEnabled()){var e=t.get("dataGroupId"),a=t.getData(),r=my(t),s=xy(r),l=i.get(s);if(l)n.set(s,{oldSeries:[{dataGroupId:l.dataGroupId,divide:_y(l.data),data:l.data}],newSeries:[{dataGroupId:e,divide:_y(a),data:a}]});else if(tt(r)){var u=[];p(r,(function(t){var e=i.get(t);e.data&&u.push({dataGroupId:e.dataGroupId,divide:_y(e.data),data:e.data})})),u.length&&n.set(s,{oldSeries:u,newSeries:[{dataGroupId:e,data:a,divide:_y(a)}]})}else{var d=o.get(r);if(d){var h=n.get(d.key);h||(h={oldSeries:[{dataGroupId:d.dataGroupId,data:d.data,divide:_y(d.data)}],newSeries:[]},n.set(d.key,h)),h.newSeries.push({dataGroupId:e,data:a,divide:_y(a)})}}}})),n}(i,n);p(a.keys(),(function(t){var n=a.get(t);vy(n.oldSeries,n.newSeries,e)}))}p(n.updatedSeries,(function(t){t[Lo]&&(t[Lo]=!1)}))}for(var r=t.getSeries(),s=i.oldSeries=[],l=i.oldDataGroupIds=[],u=i.oldData=[],d=0;d<r.length;d++){var h=r[d].getData();h.count()<1e4&&(s.push(r[d]),l.push(r[d].get("dataGroupId")),u.push(h))}}))})),$((function(t){t.registerUpdateLifecycle("series:beforeupdate",(function(t,e,n){var i=$o(e).labelManager;i||(i=$o(e).labelManager=new Ko),i.clearLabels()})),t.registerUpdateLifecycle("series:layoutlabels",(function(t,e,n){var i=$o(e).labelManager;n.updatedSeries.forEach((function(t){i.addLabelsOfSeries(e.getViewOfSeriesModel(t))})),i.updateLayoutConfig(e),i.layout(e),i.processLabelsOverall()}))}));
