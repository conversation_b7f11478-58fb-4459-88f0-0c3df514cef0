<!DOCTYPE html>
<html>
<head>
    <title>设置Token</title>
</head>
<body>
    <h1>设置用户Token</h1>
    <script>
        // 设置token到localStorage
        const token = '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
        localStorage.setItem('access_token', token);
        alert('Token已设置到localStorage: ' + token.substring(0, 50) + '...');
        
        // 可选：重定向到主页
        setTimeout(() => {
            window.location.href = '/';
        }, 2000);
    </script>
</body>
</html>
