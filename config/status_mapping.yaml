# 统一订单状态映射配置
# 基于三家供应商官方文档设计

# 统一状态定义
unified_statuses:
  # 下单阶段
  submitted: "已提交"
  submit_failed: "提交失败"
  print_failed: "面单生成失败"
  
  # 分配阶段
  assigned: "已分配"
  awaiting_pickup: "等待揽收"
  
  # 揽收阶段
  picked_up: "已揽收"
  pickup_failed: "揽收失败"
  
  # 运输阶段
  in_transit: "运输中"
  out_for_delivery: "派送中"
  
  # 签收阶段
  delivered: "已签收"
  delivered_abnormal: "异常签收"
  
  # 计费阶段
  billed: "已计费"
  
  # 异常状态
  exception: "异常"
  returned: "已退回"
  forwarded: "已转寄"
  
  # 取消状态
  cancelled: "已取消"
  voided: "已作废"
  
  # 特殊状态
  weight_updated: "重量更新"
  revived: "订单复活"

# 状态分组
status_groups:
  active:
    - submitted
    - assigned
    - awaiting_pickup
    - picked_up
    - in_transit
    - out_for_delivery
  
  completed:
    - delivered
    - delivered_abnormal
    - billed
  
  failed:
    - submit_failed
    - print_failed
    - pickup_failed
  
  cancelled:
    - cancelled
    - voided
  
  exception:
    - exception
    - returned
    - forwarded
  
  special:
    - weight_updated
    - revived

# 终态状态
terminal_statuses:
  - billed
  - voided
  - submit_failed
  - print_failed

# 状态优先级（数字越小优先级越高）
status_priority:
  submit_failed: 1
  print_failed: 2
  cancelled: 3
  voided: 4
  pickup_failed: 5
  exception: 6
  returned: 7
  forwarded: 8
  weight_updated: 9
  revived: 10
  submitted: 11
  assigned: 12
  awaiting_pickup: 13
  picked_up: 14
  in_transit: 15
  out_for_delivery: 16
  delivered: 17
  delivered_abnormal: 18
  billed: 19

# 合法状态转换
valid_transitions:
  submitted:
    - assigned
    - submit_failed
    - print_failed
    - cancelled
  
  assigned:
    - awaiting_pickup
    - cancelled
  
  awaiting_pickup:
    - picked_up
    - pickup_failed
    - cancelled
  
  picked_up:
    - in_transit
    - billed
    - exception
    - cancelled
  
  in_transit:
    - out_for_delivery
    - delivered
    - exception
    - returned
    - forwarded
  
  out_for_delivery:
    - delivered
    - delivered_abnormal
    - exception
    - returned
  
  delivered:
    - billed
  
  delivered_abnormal:
    - billed
  
  exception:
    - in_transit
    - returned
    - cancelled
  
  pickup_failed:
    - awaiting_pickup
    - cancelled
  
  returned:
    - cancelled
  
  cancelled:
    - revived  # 快递100特有
  
  weight_updated:
    # 可以转换回调整前的状态，具体由业务逻辑决定

# 供应商状态映射
provider_mappings:
  # 快递100状态映射
  kuaidi100:
    0: submitted          # 下单成功
    1: assigned           # 已接单
    2: awaiting_pickup    # 收件中
    9: cancelled          # 用户主动取消
    10: picked_up         # 已取件
    11: pickup_failed     # 揽货失败
    12: returned          # 已退回
    13: delivered         # 已签收
    14: delivered_abnormal # 异常签收
    15: billed            # 已结算
    99: cancelled         # 订单已取消
    101: in_transit       # 运输中
    155: weight_updated   # 修改重量
    166: revived          # 订单复活
    200: submitted        # 已出单
    201: print_failed     # 出单失败
    400: out_for_delivery # 派送中
    610: submit_failed    # 下单失败
  
  # 易达状态映射
  yida:
    "1": awaiting_pickup  # 待取件
    "11": picked_up       # 已取件
    "2": in_transit       # 运输中
    "3": delivered        # 已签收
    "6": exception        # 异常
    "10": cancelled       # 已取消

  # 云通状态映射
  yuntong:
    100: submitted          # 下单成功
    400: submit_failed      # 下单失败
    102: assigned           # 分配网点
    103: assigned           # 分配快递员
    104: picked_up          # 已取件
    301: billed             # 计费/已揽件
    208: weight_updated     # 更新重量
    203: cancelled          # 取消订单
    204: pickup_failed      # 揽收失败
    205: voided             # 作废
    2: in_transit           # 在途中
    3: delivered            # 签收
    500: exception          # 异常
    501: forwarded          # 已转寄

  # 菜鸟裹裹状态映射
  cainiao:
    # === 订单状态映射（官方数字编码）===
    "-1": cancelled         # 订单已取消
    "0": submitted          # 订单已创建
    "20": assigned          # 已分配运力
    "30": picked_up         # 已取件
    "40": billed            # 已完结

    # === 物流状态映射（官方事件编码）===
    "ACCEPT": picked_up           # 已揽件
    "TRANSPORT": in_transit       # 运输中
    "DELIVERING": out_for_delivery # 派送中
    "SIGN": delivered             # 已签收
    "FAILED": exception           # 异常提醒
    "REJECT": delivered_abnormal  # 拒签
    "AGENT_SIGN": awaiting_pickup # 待取件
    "STA_DELIVERING": out_for_delivery # 驿站派送中
    "ORDER_TRANSER": forwarded    # 已转单
    "REVERSE_RETURN": returned    # 退货返回

    # === 兼容字符串状态（向后兼容）===
    "CREATED": submitted    # 兼容
    "CANCELLED": cancelled  # 兼容
    "COMPLETED": billed     # 兼容

  # 快递鸟状态映射
  kuaidiniao:
    # === 下单状态映射 ===
    100: submitted          # 下单成功
    101: submit_failed      # 下单失败
    102: submitted          # 已分配网点（仍为已提交状态）
    103: assigned           # 已分配快递员
    104: picked_up          # 已取件
    105: in_transit         # 运输中
    106: out_for_delivery   # 派送中
    107: delivered          # 已签收
    108: exception          # 异常

    # === 调度和取消状态映射 ===
    99: pickup_failed       # 🔥 修复：调度失败（快递鸟官方文档状态码99）
    203: cancelled          # 订单已取消
    204: pickup_failed      # 揽收失败
    205: voided             # 作废
    209: cancelled          # 其他取消原因

    # === 计费状态映射 ===
    301: billed             # 已计费
    302: weight_updated     # 重量更新

    # === 异常状态映射 ===
    401: exception          # 异常
    402: returned           # 已退回
    403: forwarded          # 已转寄

    # === 物流轨迹状态映射（基于State字段）===
    "0": submitted          # 暂无物流信息
    "1": picked_up          # 已揽收
    "2": in_transit         # 在途中
    "3": delivered          # 已签收
    "4": exception          # 异常

# 易达推送类型映射
yida_push_types:
  1: status_change      # 状态变更推送
  2: billing_update     # 计费信息推送
  3: pickup_info        # 揽收信息推送
  5: order_change       # 订单变更推送

# 状态业务规则
business_rules:
  # 需要触发结算的状态
  settlement_triggers:
    - delivered
    - delivered_abnormal
  
  # 需要触发退款的状态
  refund_triggers:
    - cancelled
    - voided
  
  # 需要人工关注的状态
  attention_required:
    - exception
    - pickup_failed
    - submit_failed
    - print_failed
  
  # 可以重新安排的状态
  reschedulable:
    - pickup_failed
    - exception
  
  # 包含计费信息的状态
  billing_statuses:
    - billed
    - weight_updated

# 监控配置
monitoring:
  # 状态停留时间阈值（小时）
  status_timeout_hours:
    submitted: 24         # 提交后24小时未分配
    assigned: 48          # 分配后48小时未揽收
    awaiting_pickup: 72   # 等待揽收72小时
    picked_up: 168        # 揽收后7天未运输
    in_transit: 240       # 运输中10天
    out_for_delivery: 48  # 派送中48小时
  
  # 异常状态监控
  exception_monitoring:
    enabled: true
    alert_threshold: 10   # 异常订单数量阈值
    check_interval: 300   # 检查间隔（秒）
  
  # 状态转换监控
  transition_monitoring:
    enabled: true
    invalid_transition_alert: true
    log_all_transitions: true

# 缓存配置
cache:
  # 状态映射缓存
  status_mapping_ttl: 3600  # 1小时
  
  # 状态历史缓存
  status_history_ttl: 1800  # 30分钟
  
  # 状态统计缓存
  status_stats_ttl: 900     # 15分钟
