未使用常量检查:
-: # github.com/your-org/go-kuaidi/scripts
scripts/test_balance_check_integration.go:138:6: main redeclared in this block
	scripts/test_admin_deposit_limit.go:130:6: other declaration of main
scripts/test_balance_fix.go:13:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_balance_fix.go:122:6: main redeclared in this block
	scripts/test_admin_deposit_limit.go:130:6: other declaration of main
scripts/test_balance_service_fix.go:13:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_balance_service_fix.go:25:6: MockBalanceService redeclared in this block
	scripts/test_balance_fix.go:25:6: other declaration of MockBalanceService
scripts/test_balance_service_fix.go:30:6: NewMockBalanceService redeclared in this block
	scripts/test_balance_fix.go:30:6: other declaration of NewMockBalanceService
scripts/test_balance_service_fix.go:39:30: method MockBalanceService.CheckBalanceForOrder already declared at scripts/test_balance_fix.go:42:30
scripts/test_balance_service_fix.go:114:6: main redeclared in this block
	scripts/test_admin_deposit_limit.go:130:6: other declaration of main
scripts/test_prometheus_fix.go:14:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_prometheus_fix.go:157:6: main redeclared in this block
	scripts/test_admin_deposit_limit.go:130:6: other declaration of main
scripts/test_balance_service_fix.go:39:30: too many errors (compile)
internal/service/balance_service_test.go:13:2: package go-kuaidi/internal/model is not in std (/usr/local/Cellar/go/1.24.4/libexec/src/go-kuaidi/internal/model) (compile)
internal/service/balance_service_test.go:14:2: package go-kuaidi/internal/repository is not in std (/usr/local/Cellar/go/1.24.4/libexec/src/go-kuaidi/internal/repository) (compile)
internal/service/balance_service_test.go:15:2: package go-kuaidi/internal/util is not in std (/usr/local/Cellar/go/1.24.4/libexec/src/go-kuaidi/internal/util) (compile)
internal/service/order_balance_integration_test.go:13:2: package go-kuaidi/internal/errors is not in std (/usr/local/Cellar/go/1.24.4/libexec/src/go-kuaidi/internal/errors) (compile)
