golangci-lint 完整分析:
cmd/stress_test/main.go:11:2: could not import sync/atomic (-: could not load export data: internal error in importing "sync/atomic" (unsupported version: 2); please report an issue) (typecheck)
	"sync/atomic"
	^
internal/adapter/kuaidiniao.go:42:23: undefined: validator (typecheck)
		validator:          validator.New(),
		                    ^
internal/adapter/kuaidiniao_types.go:28:22: undefined: validator (typecheck)
	validator          *validator.Validate
	                    ^
internal/adapter/workorder/kuaidiniao_adapter_test.go:25:12: m.Called undefined (type *MockWorkOrderRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, workOrder)
	          ^
internal/adapter/workorder/kuaidiniao_adapter_test.go:30:12: m.Called undefined (type *MockWorkOrderRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, id)
	          ^
internal/adapter/workorder/kuaidiniao_adapter_test.go:35:12: m.Called undefined (type *MockWorkOrderRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, provider, providerWorkOrderID)
	          ^
internal/auth/controller.go:138:23: claims.ID undefined (type *TokenClaims has no field or method ID) (typecheck)
		TokenID:     claims.ID,
		                    ^
internal/auth/middleware.go:56:13: claims.Subject undefined (type *TokenClaims has no field or method Subject) (typecheck)
		if claims.Subject != "" {
		          ^
internal/auth/middleware.go:58:28: claims.Subject undefined (type *TokenClaims has no field or method Subject) (typecheck)
			c.Set("user_id", claims.Subject)
			                        ^
internal/auth/middleware.go:59:27: claims.Subject undefined (type *TokenClaims has no field or method Subject) (typecheck)
			c.Set("userID", claims.Subject) // 兼容其他中间件
			                       ^
internal/auth/service.go:30:2: undefined: jwt (typecheck)
	jwt.RegisteredClaims
	^
internal/auth/service.go:42:16: undefined: jwt (typecheck)
	signingMethod jwt.SigningMethod
	              ^
internal/auth/service.go:61:18: undefined: jwt (typecheck)
		signingMethod: jwt.SigningMethodRS256,
		               ^
internal/benchmark/performance_benchmark.go:8:2: could not import sync/atomic (-: could not load export data: internal error in importing "sync/atomic" (unsupported version: 2); please report an issue) (typecheck)
	"sync/atomic"
	^
internal/config/migration.go:175:19: undefined: yaml (typecheck)
	yamlData, err := yaml.Marshal(unifiedConfig)
	                 ^
internal/logging/manager.go:189:13: undefined: lumberjack (typecheck)
	writer := &lumberjack.Logger{
	           ^
internal/security/nonce_manager.go:83:15: undefined: redis (typecheck)
	redisClient *redis.Client
	             ^
internal/security/nonce_manager.go:90:55: undefined: redis (typecheck)
func NewNonceManager(config NonceConfig, redisClient *redis.Client, logger *zap.Logger) NonceManager {
                                                      ^
internal/security/rate_limit.go:75:15: undefined: redis (typecheck)
	redisClient *redis.Client
	             ^
internal/service/balance_monitoring_service.go:6:2: could not import sync/atomic (-: could not load export data: internal error in importing "sync/atomic" (unsupported version: 2); please report an issue) (typecheck)
	"sync/atomic"
	^
internal/service/balance_service_test.go:24:12: m.Called undefined (type *MockBalanceRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, userID)
	          ^
internal/service/balance_service_test.go:32:12: m.Called undefined (type *MockBalanceRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, balance)
	          ^
internal/service/balance_service_test.go:37:12: m.Called undefined (type *MockBalanceRepository has no field or method Called) (typecheck)
	args := m.Called(ctx, userID, newBalance, frozenBalance, version)
	          ^
internal/service/balance_service_test.go:103:11: mockRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
	mockRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)
	         ^
internal/service/balance_service_test.go:119:11: mockRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
	mockRepo.AssertExpectations(t)
	         ^
internal/service/balance_service_test.go:146:11: mockRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
	mockRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)
	         ^
internal/service/balance_service_test.go:163:11: mockRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
	mockRepo.AssertExpectations(t)
	         ^
internal/service/balance_service_test.go:188:11: mockRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
	mockRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)
	         ^
internal/service/balance_service_test.go:201:11: mockRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
	mockRepo.AssertExpectations(t)
	         ^
internal/service/enhanced_balance_manager.go:94:12: undefined: yaml (typecheck)
	if err := yaml.Unmarshal(configData, &config); err != nil {
	          ^
internal/service/order_balance_integration_test.go:22:12: m.Called undefined (type *MockOrderService has no field or method Called) (typecheck)
	args := m.Called(ctx, req)
	          ^
internal/service/order_balance_integration_test.go:87:20: mockBalanceRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
			mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil)
			                ^
internal/service/order_balance_integration_test.go:107:20: mockBalanceRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
			mockBalanceRepo.AssertExpectations(t)
			                ^
internal/service/order_balance_integration_test.go:135:18: mockBalanceRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
	mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(mockBalance, nil).Times(5)
	                ^
internal/service/order_balance_integration_test.go:165:18: mockBalanceRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
	mockBalanceRepo.AssertExpectations(t)
	                ^
internal/service/order_balance_integration_test.go:183:18: mockBalanceRepo.On undefined (type *MockBalanceRepository has no field or method On) (typecheck)
	mockBalanceRepo.On("GetBalance", mock.Anything, userID).Return(nil, errors.NewBusinessError(errors.ErrCodeNotFound, "用户余额不存在"))
	                ^
internal/service/order_balance_integration_test.go:194:18: mockBalanceRepo.AssertExpectations undefined (type *MockBalanceRepository has no field or method AssertExpectations) (typecheck)
	mockBalanceRepo.AssertExpectations(t)
	                ^
internal/user/validation_utils.go:7:2: could not import unicode (-: could not load export data: internal error in importing "unicode" (unsupported version: 2); please report an issue) (typecheck)
	"unicode"
	^
internal/util/json_optimizer.go:21:10: undefined: jsoniter (typecheck)
	jsonAPI jsoniter.API
	        ^
internal/util/json_optimizer.go:50:13: undefined: jsoniter (typecheck)
	jsonAPI := jsoniter.Config{
	           ^
internal/util/query_validator.go:7:2: could not import unicode/utf8 (-: could not load export data: internal error in importing "unicode/utf8" (unsupported version: 2); please report an issue) (typecheck)
	"unicode/utf8"
	^
internal/util/response.go:50:14: undefined: validator (typecheck)
	validate := validator.New()
	            ^
internal/validator/input_validator.go:8:2: could not import unicode (-: could not load export data: internal error in importing "unicode" (unsupported version: 2); please report an issue) (typecheck)
	"unicode"
	^
internal/validator/input_validator.go:9:2: could not import unicode/utf8 (-: could not load export data: internal error in importing "unicode/utf8" (unsupported version: 2); please report an issue) (typecheck)
	"unicode/utf8"
	^
scripts/test_admin_deposit_limit.go:1: : # github.com/your-org/go-kuaidi/scripts
scripts/test_balance_check_integration.go:138:6: main redeclared in this block
	scripts/test_admin_deposit_limit.go:130:6: other declaration of main
scripts/test_balance_fix.go:13:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_balance_fix.go:122:6: main redeclared in this block
	scripts/test_admin_deposit_limit.go:130:6: other declaration of main
scripts/test_balance_service_fix.go:13:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_balance_service_fix.go:25:6: MockBalanceService redeclared in this block
	scripts/test_balance_fix.go:25:6: other declaration of MockBalanceService
scripts/test_balance_service_fix.go:30:6: NewMockBalanceService redeclared in this block
	scripts/test_balance_fix.go:30:6: other declaration of NewMockBalanceService
scripts/test_balance_service_fix.go:39:30: method MockBalanceService.CheckBalanceForOrder already declared at scripts/test_balance_fix.go:42:30
scripts/test_balance_service_fix.go:114:6: main redeclared in this block
	scripts/test_admin_deposit_limit.go:130:6: other declaration of main
scripts/test_prometheus_fix.go:14:6: BalanceCheckResult redeclared in this block
	scripts/test_balance_check_integration.go:13:6: other declaration of BalanceCheckResult
scripts/test_prometheus_fix.go:157:6: main redeclared in this block
	scripts/test_admin_deposit_limit.go:130:6: other declaration of main
scripts/test_balance_service_fix.go:39:30: too many errors (typecheck)
package main
