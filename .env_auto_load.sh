#!/bin/bash
# 自动加载Go-Kuaidi环境变量脚本
# 用法：source .env_auto_load.sh 或在.bashrc中添加自动加载

# 检查是否在go-kuaidi目录
if [[ $(basename "$PWD") == "go-kuaidi" ]] && [[ -f "config/development.env" ]]; then
    echo "🔄 自动加载Go-Kuaidi开发环境变量..."
    
    # 加载环境变量
    set -a  # 自动导出所有变量
    source config/development.env 2>/dev/null
    set +a  # 关闭自动导出
    
    echo "✅ 环境变量已自动加载"
    echo "   CALLBACK_BASE_URL: $CALLBACK_BASE_URL"
    echo "   DATABASE_URL: ${DATABASE_URL:0:50}..."
    echo "   REDIS_URL: ${REDIS_URL:0:30}..."
fi 