<!DOCTYPE html>
<html>
<head>
    <title>Go Kuaidi API Documentation</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 1200px; margin: 0 auto; }
        h1 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        .api-section { margin: 20px 0; }
        .endpoint { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .method { font-weight: bold; color: #007cba; }
        .path { font-family: monospace; background: #e9ecef; padding: 2px 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Go Kuaidi API 文档</h1>
        <p>性能优化版本已启用</p>
        
        <div class="api-section">
            <h2>💰 余额相关接口</h2>
            
            <div class="endpoint">
                <div><span class="method">GET</span> <span class="path">/api/v1/balance/health</span></div>
                <p>优化后的健康检查接口，包含性能监控信息</p>
            </div>
            
            <div class="endpoint">
                <div><span class="method">GET</span> <span class="path">/api/v1/balance/transactions/optimized</span></div>
                <p>优化后的交易记录查询接口，支持高性能查询和缓存</p>
            </div>
            
            <div class="endpoint">
                <div><span class="method">GET</span> <span class="path">/api/v1/balance/statistics/optimized</span></div>
                <p>优化后的余额统计接口，提供实时数据分析</p>
            </div>
        </div>
        
        <div class="api-section">
            <h2>⚡ 性能优化功能</h2>
            <div class="endpoint">
                <div><span class="method">POST</span> <span class="path">/api/v1/balance/cache/clear</span></div>
                <p>清除缓存接口，用于性能调优</p>
            </div>
        </div>
        
        <footer style="margin-top: 50px; color: #666; text-align: center;">
            <p>Go Kuaidi Performance Optimized API v1.0</p>
        </footer>
    </div>
</body>
</html>
