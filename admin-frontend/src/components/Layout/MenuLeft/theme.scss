@use '@styles/variables.scss' as *;

// 重新修改菜单样式

.el-popper.is-pure {
  border: 0.5px solid var(--art-border-dashed-color) !important;
  border-radius: 12px;
}

// 菜单折叠 hover 弹窗样式
.el-menu--vertical,
.el-menu--popup-container {
  .el-menu--popup {
    padding: 8px;

    .el-sub-menu__title:hover,
    .el-menu-item:hover {
      background-color: var(--art-gray-200) !important;
      border-radius: 6px;
    }

    .el-menu-item {
      height: 40px;
      margin-bottom: 10px;
      border-radius: 6px;

      &:last-of-type {
        margin-bottom: 0;
      }
    }

    .el-menu-item.is-active {
      color: var(--art-gray-900) !important;
      background-color: var(--art-gray-200) !important;
    }
  }
}

// 菜单折叠 hover 弹窗样式（黑色菜单）
.menu-left-dark-popper {
  .el-menu--vertical,
  .el-menu--popup-container {
    .el-menu--popup {
      .el-sub-menu__title:hover,
      .el-menu-item:hover {
        background-color: rgb(255 255 255 / 8%) !important;
      }

      .el-menu-item.is-active {
        color: #eee !important;
        background-color: rgb(255 255 255 / 8%) !important;
      }
    }
  }
}

.menu-left-dark {
  // 菜单折叠 hover 弹窗样式
  .el-menu--vertical,
  .el-menu--popup-container {
    .el-menu--popup {
      padding: 8px;

      .el-sub-menu__title:hover,
      .el-menu-item:hover {
        background-color: red !important;
        border-radius: 6px;
      }

      .el-menu-item {
        height: 40px;
        margin-bottom: 10px;
        border-radius: 6px;

        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }
  }
}

.menu-left {
  // ---------------------- Modify default style ----------------------

  &.menu-left-dark,
  &.menu-left-light {
    .header {
      .svg-icon {
        margin-left: 22px !important;
      }

      p:not(.is-dual-menu-name) {
        left: 60px !important;
      }
    }
  }

  &.menu-left-design.menu-left-close {
    .header {
      .svg-icon {
        margin-left: 16px !important;
      }
    }

    .el-sub-menu__title,
    .el-menu-item {
      width: 100% !important;
    }
  }

  &.menu-left-dark.menu-left-close,
  &.menu-left-light.menu-left-close {
    .header {
      .svg-icon {
        margin-left: 20px !important;
      }
    }
  }

  &.menu-left-dark {
    .header {
      .svg-icon {
        margin-left: 28px;
      }
    }
  }

  .iconfont-sys {
    font-size: 20px;
  }

  // 菜单图标
  .menu-icon {
    margin-right: 10px;
  }

  // 菜单高度
  .el-sub-menu__title,
  .el-menu-item {
    height: 52px !important;
    line-height: 52px !important;
    transition: background-color 0s !important;

    span {
      font-size: 14px !important;
    }
  }

  // 右侧箭头
  .el-sub-menu__icon-arrow {
    right: 20px;
    width: 15px !important;
    font-weight: bold;
  }

  // ---------------------- Dark theme menu ----------------------
  .el-menu-dark {
    .el-sub-menu__icon-arrow {
      color: var(--art-gray-800);
    }

    .el-sub-menu__title {
      .iconfont-sys {
        margin-left: 3px !important;
      }

      .el-icon {
        color: var(--art-gray-300);
      }
    }

    // 选中颜色
    .el-menu-item.is-active {
      background-color: var(--el-color-primary-light-1);
    }

    // 鼠标移入背景色
    .el-sub-menu__title:hover,
    .el-menu-item:not(.is-active):hover {
      background: #0f1015 !important;
    }
  }

  .el-menu--collapse {
    .el-sub-menu.is-active {
      .el-sub-menu__title {
        .iconfont-sys {
          color: var(--main-color) !important;
        }
      }
    }
  }

  // ---------------------- Light theme menu ----------------------
  .el-menu-light {
    .el-sub-menu__title,
    .el-menu-item {
      width: 100%;
      border-radius: 0;
    }

    .el-sub-menu__title {
      .iconfont-sys {
        margin-left: 3px !important;
      }
    }

    .el-sub-menu__icon-arrow {
      color: var(--art-gray-600);
    }

    // 选中颜色
    .el-menu-item.is-active {
      color: var(--main-color) !important;
      background-color: var(--el-color-primary-light-9);
      background-image: var(--el-color-primary-custom-14);

      &::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        content: '';
        background: var(--main-color);
      }
    }

    // 鼠标移入背景色
    .el-sub-menu__title:hover,
    .el-menu-item:not(.is-active):hover {
      background: var(--art-gray-100) !important;
    }
  }

  .el-menu-light.el-menu--collapse {
    .el-sub-menu.is-active {
      .el-sub-menu__title {
        .iconfont-sys {
          color: var(--main-color) !important;
        }
      }
    }
  }

  // ---------------------- Design theme menu ----------------------

  .el-menu-design {
    .el-sub-menu__title,
    .el-menu-item {
      width: calc(100% - 16px);
      margin: 0 auto;
      margin-bottom: 5px;
      border-radius: 6px;
    }

    .el-sub-menu__icon-arrow {
      color: var(--art-gray-600);
    }

    // 选中颜色
    .el-menu-item.is-active {
      color: var(--main-color) !important;
      background-color: var(--el-color-primary-light-9);
      background-image: var(--el-color-primary-custom-14);
    }

    // 鼠标移入背景色
    .el-sub-menu__title:hover,
    .el-menu-item:not(.is-active):hover {
      background: var(--art-gray-100) !important;
    }
  }
}

.dark {
  .menu-left {
    .el-menu-item.is-active {
      span {
        // 暗黑主题模式，选中菜单文字颜色
        color: var(--main-color) !important;
      }
    }
  }
}

@media only screen and (max-width: $device-phone) {
  .menu-left {
    .el-menu-design {
      > .el-sub-menu {
        margin-left: 0;
      }

      .el-sub-menu {
        width: 100% !important;
      }
    }
  }
}
