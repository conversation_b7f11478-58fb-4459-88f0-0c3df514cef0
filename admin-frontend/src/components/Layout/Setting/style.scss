@use '@styles/variables.scss' as *;

// 去除火狐浏览器滚动条
:deep(.el-drawer__body) {
  scrollbar-width: none;
}

.drawer-con {
  $box-shadow: 0 2px 8px 0 rgb(0 0 0 / 20%);
  $box-radius: 8px;

  padding: 0 5px 30px;

  .close-wrap {
    display: flex;
    justify-content: flex-end;

    i {
      display: block;
      padding: 8px;
      font-size: 15px;
      font-weight: bold;
      color: var(--art-gray-600);
      cursor: pointer;
      border-radius: 5px;

      &:hover {
        color: var(--art-gray-700);
        background-color: rgb(var(--art-gray-300-rgb), 0.5);
      }
    }
  }

  .title {
    position: relative;
    font-size: 14px;
    color: var(--art-text-gray-800);
    text-align: center;

    &:first-of-type {
      margin-top: 20px;
    }

    &::before,
    &::after {
      position: absolute;
      top: 10px;
      width: 50px;
      margin: auto;
      content: '';
      border-bottom: 1px solid rgba(var(--art-gray-300-rgb), 0.8);
    }

    &::before {
      left: 0;
    }

    &::after {
      right: 0;
    }
  }

  .menu-type {
    padding-bottom: 20px;
    margin-top: 20px;

    // 隐藏滚动条
    :deep(.el-scrollbar__bar.is-vertical) {
      display: none;
    }

    :deep(.el-scrollbar__bar.is-horizontal) {
      height: 3px;
    }

    .menu-type-wrap {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      width: calc(100% + 15px);
      padding-bottom: 10px;

      .item {
        width: calc(33.333% - 15px);
        margin-right: 15px;
        text-align: center;

        &:nth-child(3n) {
          margin-right: 0;
        }

        &:nth-child(4n) {
          margin-top: 20px;
        }

        .box {
          box-sizing: border-box;
          height: 50px;
          cursor: pointer;
          background-color: #f5f7f9;
          border: 2px solid transparent;
          border-radius: $box-radius;
          box-shadow: $box-shadow;

          &.is-active {
            border: 2px solid var(--main-color);
          }

          &.bl {
            display: flex;
            justify-content: space-between;

            .bl-menu {
              box-sizing: border-box;
              width: 16px;
              height: calc(100% - 4px);
              padding: 0 3px;
              margin: 2px 0 0 2px;
              overflow: hidden;
              background-color: #ddd;
              border-radius: 2px;

              .line {
                width: 100%;
                height: 2px;
                margin-top: 4.4px;
                background: #fff;
                border-radius: 1px;
              }
            }

            .bl-content {
              box-sizing: border-box;
              width: calc(100% - 16px);
              height: 100%;
              padding: 4px 5px;

              .header {
                height: 6px;
                margin: auto;
                background-color: #edeef0;
                border-radius: 2px;
              }

              .row1 {
                display: flex;
                justify-content: space-between;
                margin-top: 4px;

                div {
                  height: 12px;
                  background-color: #edeef0;
                  border-radius: 2px;

                  &:first-of-type {
                    width: 35%;
                  }

                  &:last-of-type {
                    width: 55%;
                  }
                }
              }

              .row2 {
                height: 12px;
                margin-top: 4px;
                background-color: #edeef0;
              }
            }
          }

          &.bt {
            padding: 0 5px;

            .bt-menu {
              box-sizing: border-box;
              display: flex;
              align-items: center;
              height: 10px;
              padding: 0 3px;
              margin: 2px auto;
              overflow: hidden;
              background-color: #ddd;
              border-radius: 2px;

              .line {
                width: 7px;
                height: 2px;
                margin-right: 2px;
                background: #fff;
              }
            }

            .bl-content {
              box-sizing: border-box;
              height: 100%;

              .row1 {
                display: flex;
                justify-content: space-between;
                margin-top: 4px;

                div {
                  height: 12px;
                  background-color: #edeef0;
                  border-radius: 2px;

                  &:first-of-type {
                    width: 37%;
                  }

                  &:last-of-type {
                    width: 55%;
                  }
                }
              }

              .row2 {
                height: 12px;
                margin-top: 4px;
                background-color: #edeef0;
              }
            }
          }

          &.tl {
            display: flex;
            justify-content: space-between;
            padding: 0 5px;

            .tl-left {
              min-width: 10px;
              margin: 2px 0;
              background-color: #ddd;
              border-radius: 2px;

              > div {
                width: 4px;
                height: 2px;
                margin: 4px auto;
                background: #fff;
              }
            }

            .tl-right {
              width: calc(100% - 14px);

              .bt-menu {
                box-sizing: border-box;
                display: flex;
                align-items: center;
                height: 10px;
                padding: 0 3px;
                margin: 2px auto;
                overflow: hidden;
                background-color: #ddd;
                border-radius: 2px;

                .line {
                  width: 7px;
                  height: 2px;
                  margin-right: 2px;
                  background: #fff;
                }
              }

              .bl-content {
                box-sizing: border-box;
                height: 100%;

                .row1 {
                  display: flex;
                  justify-content: space-between;
                  margin-top: 4px;

                  div {
                    height: 12px;
                    background-color: #edeef0;
                    border-radius: 2px;

                    &:first-of-type {
                      width: 37%;
                    }

                    &:last-of-type {
                      width: 55%;
                    }
                  }
                }

                .row2 {
                  height: 12px;
                  margin-top: 4px;
                  background-color: #edeef0;
                }
              }
            }
          }

          &.dl {
            display: flex;
            justify-content: space-between;
            padding: 0 5px;

            .tl1-left {
              min-width: 6px;
              margin: 2px 0;
              margin-right: 2px;
              background-color: #edeef0;
              border-radius: 2px;

              > div {
                width: 4px;
                height: 2px;
                margin: 4px auto;
                background: #fff;
              }
            }

            .tl2-left {
              min-width: 10px;
              margin: 2px 0;
              margin-right: 4px;
              background-color: #ddd;
              border-radius: 2px;

              > div {
                width: 4px;
                height: 2px;
                margin: 4px auto;
                background: #fff;
              }
            }

            .tl-right {
              width: calc(100% - 22px);

              .bt-menu {
                box-sizing: border-box;
                display: flex;
                align-items: center;
                height: 6px;
                padding: 0 3px;
                margin: 2px auto;
                overflow: hidden;
                background-color: #edeef0;
                border-radius: 2px;
              }

              .bl-content {
                box-sizing: border-box;
                height: 100%;

                .row1 {
                  display: flex;
                  justify-content: space-between;
                  margin-top: 4px;

                  div {
                    height: 13px;
                    background-color: #edeef0;
                    border-radius: 2px;

                    &:first-of-type {
                      width: 37%;
                    }

                    &:last-of-type {
                      width: 55%;
                    }
                  }
                }

                .row2 {
                  height: 13px;
                  margin-top: 4px;
                  background-color: #edeef0;
                }
              }
            }
          }
        }

        .name {
          display: block;
          margin-top: 8px;
          font-size: 13px;
          line-height: 1;
          color: var(--art-gray-700);
        }
      }
    }
  }

  .theme-wrap {
    display: flex;
    flex-wrap: wrap;
    width: calc(100% + 15px);
    margin-top: 25px;

    .item {
      box-sizing: border-box;
      width: calc(33.333% - 15px);
      margin-right: 15px;

      .box {
        position: relative;
        box-sizing: border-box;
        display: flex;
        height: 50px;
        overflow: hidden;
        cursor: pointer;
        border: 2px solid var(--art-gray-100);
        border-radius: $box-radius;
        box-shadow: $box-shadow;
        transition: box-shadow 0.1s;

        &.is-active {
          border: 2px solid var(--main-color);
        }

        > div {
          position: relative;
          width: 50%;
          height: 100%;

          &:first-of-type {
            > div {
              width: 15px;
              height: 2px;
              margin: 5px 0 0 10px;

              &.line0 {
                margin-top: 13px;
              }

              &.line1 {
                width: 10px;
              }

              &.line2 {
                width: 13px;
              }
            }
          }

          &:last-of-type {
            > div {
              height: 5px;
              margin: 6px 0 0 5px;

              &.line0 {
                width: calc(100% - 15px);
                margin-top: 12px;
              }

              &.line1 {
                width: calc(50% - 5px);
              }

              &.line2 {
                width: calc(52%);
              }
            }
          }
        }
      }

      .name {
        margin-top: 6px;
        font-size: 14px;
        text-align: center;
      }

      .active {
        position: relative;
        right: 0;
        bottom: -5px;
        left: 0;
        width: 6px;
        height: 6px;
        margin: auto;
        background: var(--el-color-success) !important;
        border-radius: 50%;
      }
    }
  }

  .menu-theme-wrap {
    margin-top: 20px;

    > div {
      display: flex;
      flex-wrap: wrap;
      width: calc(100% + 15px);

      .item {
        width: calc(33.333% - 15px);
        margin-right: 15px;
        margin-bottom: 15px;

        &:last-of-type {
          margin-right: 0;
        }

        .box {
          position: relative;
          box-sizing: border-box;
          height: 50px;
          overflow: hidden;
          cursor: pointer;
          background: #f5f7f9 !important;
          border: 2px solid var(--art-gray-100);
          border-radius: $box-radius;
          box-shadow: $box-shadow;
          transition: box-shadow 0.1s;

          &.is-active {
            border: 2px solid var(--main-color);
          }

          &:nth-child(even) {
            margin-right: 0;
          }

          .top {
            width: 100%;
            height: 8px;
          }

          .left {
            position: absolute;
            top: 0;
            left: 0;
            width: 22px;
            height: 100%;

            > div {
              width: 15px;
              height: 2px;
              margin: 5px 0 0 4px;

              &.line0 {
                margin-top: 15px;
              }

              &.line1 {
                width: 10px;
              }

              &.line2 {
                width: 13px;
              }
            }
          }

          .right {
            position: absolute;
            top: 0;
            right: 0;
            width: 46px;
            height: 100%;

            > div {
              height: 6px;
              margin: 5px 0 0 5px;

              &.line0 {
                width: calc(100% - 15px);
                margin-top: 12px;
              }

              &.line1 {
                width: calc(50% - 5px);
              }

              &.line2 {
                width: calc(52%);
              }
            }
          }
        }

        .active {
          width: 6px;
          height: 6px;
          margin: auto;
          margin-top: 8px;
          background: var(--el-color-success) !important;
          border-radius: 50%;
        }
      }
    }
  }

  .main-color-wrap {
    padding-top: 20px;

    .offset {
      display: flex;
      flex-wrap: wrap;
      width: calc(100% + 12.5px);

      $size: 23px;

      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: $size;
        height: $size;
        margin: 0 13px 10px 0;
        cursor: pointer;
        border-radius: $size;

        &:last-of-type {
          margin-right: 0;
        }

        i {
          font-size: 14px;
          color: #fff !important;
        }
      }
    }
  }

  .box-style {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px;
    margin-top: 20px;
    background-color: var(--art-gray-200);
    border-radius: 7px;

    .button {
      width: calc(50% - 3px);
      height: 34px;
      font-size: 14px;
      line-height: 34px;
      text-align: center;
      cursor: pointer;
      user-select: none;
      border-radius: 6px;
      transition: all 0.2s !important;

      &.is-active {
        color: var(--art-gray-800);
        background-color: var(--art-main-bg-color);
      }

      &:hover:not(.is-active) {
        color: var(--art-gray-800);
        background-color: rgba($color: #000, $alpha: 4%);
      }
    }
  }

  .container-width {
    display: flex;

    .item {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      height: 60px;
      margin-top: 20px;
      margin-right: 15px;
      margin-bottom: 15px;
      cursor: pointer;
      border: 2px solid var(--art-border-color);
      border-radius: 10px;

      &:last-of-type {
        margin-right: 0;
      }

      &.is-active {
        border-color: var(--main-color);

        i {
          color: var(--main-color) !important;
        }
      }

      i {
        margin-right: 10px;
        font-size: 22px;
      }

      span {
        font-size: 14px;
        background: transparent !important;
      }
    }
  }

  .basic-box {
    position: relative;
    z-index: 10;
    background: transparent !important;

    .item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 35px;
      background: transparent !important;

      span {
        font-size: 14px;
        background: transparent !important;
      }
    }
  }

  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.dark {
  .drawer-con {
    .box-style {
      .button {
        &.is-active {
          color: #fff !important;
          background-color: rgba(var(--art-gray-400-rgb), 0.7);
        }

        &:hover:not(.is-active) {
          background-color: rgba($color: #000, $alpha: 20%);
        }
      }
    }
  }
}

@media screen and (max-width: $device-ipad) {
  .mobile-hide {
    display: none !important;
  }
}
