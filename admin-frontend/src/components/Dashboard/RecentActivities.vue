<template>
  <div class="recent-activities">
    <div class="activities-header">
      <h3>最近活动</h3>
      <el-button size="small" text @click="refreshActivities" :loading="loading">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>
    
    <div class="activities-content" v-loading="loading">
      <div v-if="activities && activities.length > 0" class="activities-list">
        <div 
          v-for="activity in activities" 
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon" :class="getActivityTypeClass(activity.type)">
            <el-icon>
              <component :is="getActivityIconComponent(activity.type)" />
            </el-icon>
          </div>
          
          <div class="activity-content">
            <div class="activity-main">
              <span class="activity-description">{{ activity.description }}</span>
              <span class="activity-action" :class="getActionClass(activity.action)">
                {{ getActionText(activity.action) }}
              </span>
            </div>
            
            <div class="activity-meta">
              <span class="activity-user">{{ activity.username }}</span>
              <span class="activity-time">{{ formatTime(activity.timestamp) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else-if="!loading" class="empty-state">
        <el-icon><DocumentCopy /></el-icon>
        <p>暂无活动记录</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Refresh,
  DocumentCopy,
  User,
  ShoppingCart,
  Coin,
  Setting,
  InfoFilled
} from '@element-plus/icons-vue'
import type { RecentActivity } from '@/api/dashboardApi'

interface Props {
  activities?: RecentActivity[]
  loading?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  refresh: []
}>()

// 获取活动类型样式类
function getActivityTypeClass(type: string) {
  switch (type) {
    case 'user':
      return 'type-user'
    case 'order':
      return 'type-order'
    case 'balance':
      return 'type-balance'
    case 'system':
      return 'type-system'
    default:
      return 'type-default'
  }
}

// 获取活动图标组件
function getActivityIconComponent(type: string) {
  switch (type) {
    case 'user':
      return User
    case 'order':
      return ShoppingCart
    case 'balance':
      return Coin
    case 'system':
      return Setting
    default:
      return InfoFilled
  }
}

// 获取操作样式类
function getActionClass(action: string) {
  switch (action) {
    case 'create':
    case 'add':
      return 'action-create'
    case 'update':
    case 'modify':
      return 'action-update'
    case 'delete':
    case 'remove':
      return 'action-delete'
    case 'login':
    case 'logout':
      return 'action-auth'
    default:
      return 'action-default'
  }
}

// 获取操作文本
function getActionText(action: string) {
  const actionMap: Record<string, string> = {
    'create': '创建',
    'update': '更新',
    'delete': '删除',
    'login': '登录',
    'logout': '登出',
    'add': '添加',
    'remove': '移除',
    'modify': '修改'
  }
  return actionMap[action] || action
}

import { formatRelativeTime } from '@/utils/timezone'

// 格式化时间（使用北京时间）
function formatTime(timeStr: string) {
  try {
    return formatRelativeTime(timeStr)
  } catch {
    return timeStr
  }
}

// 刷新活动
function refreshActivities() {
  emit('refresh')
}
</script>

<style lang="scss" scoped>
.recent-activities {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.activities-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.activities-content {
  flex: 1;
  overflow: hidden;
}

.activities-list {
  max-height: 400px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: #fafafa;
    margin: 0 -12px;
    padding: 12px;
    border-radius: 6px;
  }
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  
  i {
    font-size: 14px;
  }
  
  &.type-user {
    background: #e6f7ff;
    color: #1890ff;
  }
  
  &.type-order {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.type-balance {
    background: #fff7e6;
    color: #fa8c16;
  }
  
  &.type-system {
    background: #f9f0ff;
    color: #722ed1;
  }
  
  &.type-default {
    background: #f5f5f5;
    color: #999;
  }
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-main {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  
  .activity-description {
    color: #333;
    font-size: 14px;
    margin-right: 8px;
  }
  
  .activity-action {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    
    &.action-create {
      background: #f6ffed;
      color: #52c41a;
    }
    
    &.action-update {
      background: #e6f7ff;
      color: #1890ff;
    }
    
    &.action-delete {
      background: #fff2f0;
      color: #ff4d4f;
    }
    
    &.action-auth {
      background: #f9f0ff;
      color: #722ed1;
    }
    
    &.action-default {
      background: #f5f5f5;
      color: #666;
    }
  }
}

.activity-meta {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
  
  .activity-user {
    margin-right: 12px;
    font-weight: 500;
  }
  
  .activity-time {
    color: #ccc;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  
  i {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

// 深色主题适配
.dark .recent-activities {
  background: #1f1f1f;
  color: #fff;

  .activity-item {
    border-bottom-color: #333;

    &:hover {
      background: #2a2a2a;
    }
  }

  .activity-main .activity-description {
    color: #fff;
  }

  .activities-list {
    &::-webkit-scrollbar-track {
      background: #333;
    }

    &::-webkit-scrollbar-thumb {
      background: #555;

      &:hover {
        background: #666;
      }
    }
  }
}
</style>
