<template>
  <div class="system-health">
    <div class="health-header">
      <h3>系统健康状态</h3>
      <div class="overall-status" :class="overallStatusClass">
        <el-icon>
          <component :is="overallStatusIconComponent" />
        </el-icon>
        <span>{{ overallStatusText }}</span>
      </div>
    </div>
    
    <div class="health-content" v-loading="loading">
      <div class="health-grid">
        <!-- 数据库状态 -->
        <div class="health-item">
          <div class="health-item-header">
            <el-icon><Coin /></el-icon>
            <span>数据库</span>
          </div>
          <div class="health-status" :class="getStatusClass(healthData?.database?.status)">
            <span class="status-dot"></span>
            <span>{{ healthData?.database?.status || 'unknown' }}</span>
          </div>
          <div class="health-details">
            <div class="detail-item">
              <span class="label">响应时间:</span>
              <span class="value">{{ healthData?.database?.response_time || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">消息:</span>
              <span class="value">{{ healthData?.database?.message || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- Redis状态 -->
        <div class="health-item">
          <div class="health-item-header">
            <el-icon><Cpu /></el-icon>
            <span>Redis</span>
          </div>
          <div class="health-status" :class="getStatusClass(healthData?.redis?.status)">
            <span class="status-dot"></span>
            <span>{{ healthData?.redis?.status || 'unknown' }}</span>
          </div>
          <div class="health-details">
            <div class="detail-item">
              <span class="label">响应时间:</span>
              <span class="value">{{ healthData?.redis?.response_time || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">消息:</span>
              <span class="value">{{ healthData?.redis?.message || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- API状态 -->
        <div class="health-item">
          <div class="health-item-header">
            <el-icon><Connection /></el-icon>
            <span>API服务</span>
          </div>
          <div class="health-status" :class="getStatusClass(healthData?.api?.status)">
            <span class="status-dot"></span>
            <span>{{ healthData?.api?.status || 'unknown' }}</span>
          </div>
          <div class="health-details">
            <div class="detail-item">
              <span class="label">响应时间:</span>
              <span class="value">{{ healthData?.api?.response_time || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">消息:</span>
              <span class="value">{{ healthData?.api?.message || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 服务状态 -->
        <div class="health-item" v-for="(service, name) in healthData?.services" :key="name">
          <div class="health-item-header">
            <el-icon><Setting /></el-icon>
            <span>{{ getServiceDisplayName(name) }}</span>
          </div>
          <div class="health-status" :class="getStatusClass(service.status)">
            <span class="status-dot"></span>
            <span>{{ service.status || 'unknown' }}</span>
          </div>
          <div class="health-details">
            <div class="detail-item">
              <span class="label">响应时间:</span>
              <span class="value">{{ service.response_time || '-' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">消息:</span>
              <span class="value">{{ service.message || '-' }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="health-footer">
        <div class="last-check">
          最后检查时间: {{ formatTime(healthData?.last_check) }}
        </div>
        <el-button size="small" @click="refreshHealth" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Coin,
  Cpu,
  Connection,
  Setting,
  Refresh,
  SuccessFilled,
  WarningFilled,
  CircleCloseFilled,
  QuestionFilled
} from '@element-plus/icons-vue'
import type { SystemHealthStatus } from '@/api/dashboardApi'

interface Props {
  healthData?: SystemHealthStatus
  loading?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  refresh: []
}>()

// 整体状态
const overallStatusClass = computed(() => {
  return getStatusClass(props.healthData?.overall)
})

const overallStatusIconComponent = computed(() => {
  const status = props.healthData?.overall
  switch (status) {
    case 'healthy':
      return SuccessFilled
    case 'warning':
      return WarningFilled
    case 'error':
      return CircleCloseFilled
    default:
      return QuestionFilled
  }
})

const overallStatusText = computed(() => {
  const status = props.healthData?.overall
  switch (status) {
    case 'healthy':
      return '健康'
    case 'warning':
      return '警告'
    case 'error':
      return '错误'
    default:
      return '未知'
  }
})

// 获取状态样式类
function getStatusClass(status?: string) {
  switch (status) {
    case 'healthy':
      return 'status-healthy'
    case 'warning':
      return 'status-warning'
    case 'error':
    case 'unhealthy':
      return 'status-error'
    default:
      return 'status-unknown'
  }
}

// 获取服务显示名称
function getServiceDisplayName(name: string) {
  const nameMap: Record<string, string> = {
    'balance_service': '余额服务',
    'express_service': '快递服务',
    'order_service': '订单服务',
    'user_service': '用户服务'
  }
  return nameMap[name] || name
}

// 格式化时间
function formatTime(timeStr?: string) {
  if (!timeStr) return '-'
  try {
    return new Date(timeStr).toLocaleString('zh-CN')
  } catch {
    return timeStr
  }
}

// 刷新健康状态
function refreshHealth() {
  emit('refresh')
}
</script>

<style lang="scss" scoped>
.system-health {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.health-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.overall-status {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  
  i {
    margin-right: 4px;
  }
  
  &.status-healthy {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.status-warning {
    background: #fffbe6;
    color: #faad14;
  }
  
  &.status-error {
    background: #fff2f0;
    color: #ff4d4f;
  }
  
  &.status-unknown {
    background: #f5f5f5;
    color: #999;
  }
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.health-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  
  &:hover {
    border-color: #d9d9d9;
  }
}

.health-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  
  i {
    margin-right: 8px;
    font-size: 16px;
    color: #666;
  }
  
  span {
    font-weight: 500;
    color: #333;
  }
}

.health-status {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
  }
  
  &.status-healthy .status-dot {
    background: #52c41a;
  }
  
  &.status-warning .status-dot {
    background: #faad14;
  }
  
  &.status-error .status-dot {
    background: #ff4d4f;
  }
  
  &.status-unknown .status-dot {
    background: #d9d9d9;
  }
}

.health-details {
  .detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 12px;
    
    .label {
      color: #666;
    }
    
    .value {
      color: #333;
      font-weight: 500;
    }
  }
}

.health-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  
  .last-check {
    font-size: 12px;
    color: #666;
  }
}

// 深色主题适配
.dark .system-health {
  background: #1f1f1f;
  color: #fff;
  
  .health-item {
    border-color: #333;
    
    &:hover {
      border-color: #555;
    }
  }
  
  .health-item-header span {
    color: #fff;
  }
  
  .health-details .detail-item .value {
    color: #fff;
  }
  
  .health-footer {
    border-top-color: #333;
  }
}
</style>
