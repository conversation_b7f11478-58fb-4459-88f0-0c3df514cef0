<template>
  <div class="stat-card" :class="{ 'loading': loading }">
    <div class="stat-card-header">
      <div class="stat-icon" :style="{ backgroundColor: iconBgColor }">
        <el-icon :style="{ color: iconColor }" :size="24">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      <div class="stat-info">
        <h3 class="stat-title">{{ title }}</h3>
        <div class="stat-value">
          <span class="value">{{ formattedValue }}</span>
          <span v-if="unit" class="unit">{{ unit }}</span>
        </div>
      </div>
    </div>
    
    <div class="stat-card-footer" v-if="trend !== undefined">
      <div class="trend" :class="trendClass">
        <el-icon :size="12">
          <component :is="trendIconComponent" />
        </el-icon>
        <span>{{ trendText }}</span>
      </div>
      <div class="description">{{ description }}</div>
    </div>
    
    <div class="loading-overlay" v-if="loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Loading,
  User,
  UserFilled,
  ShoppingCart,
  ShoppingBag,
  Clock,
  Coin,
  Wallet,
  Connection,
  ArrowUp,
  ArrowDown,
  Minus,
  Document,
  OfficeBuilding,
  SuccessFilled
} from '@element-plus/icons-vue'

interface Props {
  title: string
  value: number | string
  unit?: string
  icon: string
  iconColor?: string
  iconBgColor?: string
  trend?: number
  description?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  iconColor: '#409EFF',
  iconBgColor: '#E6F7FF',
  loading: false
})

// 图标映射
const iconMap: Record<string, any> = {
  'el-icon-user': User,
  'el-icon-user-solid': UserFilled,
  'el-icon-shopping-cart-2': ShoppingCart,
  'el-icon-shopping-bag-2': ShoppingBag,
  'el-icon-clock': Clock,
  'el-icon-coin': Coin,
  'el-icon-wallet': Wallet,
  'el-icon-connection': Connection,
  'el-icon-document': Document,
  'el-icon-office-building': OfficeBuilding,
  'el-icon-success': SuccessFilled
}

// 图标组件
const iconComponent = computed(() => {
  return iconMap[props.icon] || User
})

// 趋势图标组件
const trendIconComponent = computed(() => {
  if (props.trend === undefined) return Minus
  return props.trend > 0 ? ArrowUp : props.trend < 0 ? ArrowDown : Minus
})

// 格式化数值
const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    if (props.value >= 1000000) {
      return (props.value / 1000000).toFixed(1) + 'M'
    } else if (props.value >= 1000) {
      return (props.value / 1000).toFixed(1) + 'K'
    }
    return props.value.toLocaleString()
  }
  return props.value
})

// 趋势相关计算
const trendClass = computed(() => {
  if (props.trend === undefined) return ''
  return props.trend > 0 ? 'trend-up' : props.trend < 0 ? 'trend-down' : 'trend-stable'
})



const trendText = computed(() => {
  if (props.trend === undefined) return ''
  const absValue = Math.abs(props.trend)
  return `${absValue.toFixed(1)}%`
})
</script>

<style lang="scss" scoped>
.stat-card {
  position: relative;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  &.loading {
    pointer-events: none;
  }
}

.stat-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  
  i {
    font-size: 24px;
  }
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 16px;
  color: #666;
  margin: 0 0 10px 0;
  font-weight: 500;
}

.stat-value {
  display: flex;
  align-items: baseline;

  .value {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    line-height: 1;
  }

  .unit {
    font-size: 16px;
    color: #999;
    margin-left: 6px;
  }
}

.stat-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.trend {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  
  i {
    margin-right: 4px;
  }
  
  &.trend-up {
    color: #52c41a;
  }
  
  &.trend-down {
    color: #ff4d4f;
  }
  
  &.trend-stable {
    color: #999;
  }
}

.description {
  font-size: 12px;
  color: #999;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  
  .el-icon {
    font-size: 24px;
    color: #409EFF;
  }
}

// 深色主题适配
.dark .stat-card {
  background: #1f1f1f;
  color: #fff;
  
  .stat-title {
    color: #ccc;
  }
  
  .stat-value .value {
    color: #fff;
  }
  
  .stat-card-footer {
    border-top-color: #333;
  }
}
</style>
