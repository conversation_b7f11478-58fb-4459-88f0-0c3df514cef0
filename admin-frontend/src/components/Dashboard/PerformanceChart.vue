<template>
  <div class="performance-chart">
    <div class="chart-header">
      <h3>性能监控</h3>
      <div class="chart-controls">
        <el-button size="small" text @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <div class="chart-content" v-loading="loading">
      <div class="metrics-grid">
        <!-- 内存使用率 -->
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-title">内存使用</span>
            <span class="metric-value">{{ memoryUsagePercent }}%</span>
          </div>
          <div class="metric-chart">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: memoryUsagePercent + '%', backgroundColor: getProgressColor(memoryUsagePercent) }"
              ></div>
            </div>
            <div class="metric-details">
              <span>{{ performanceData?.memory_usage_mb || 0 }}MB / {{ performanceData?.memory_total_mb || 0 }}MB</span>
            </div>
          </div>
        </div>

        <!-- 数据库连接 -->
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-title">数据库连接</span>
            <span class="metric-value">{{ performanceData?.db_open_connections || 0 }}</span>
          </div>
          <div class="metric-chart">
            <div class="connection-stats">
              <div class="stat-item">
                <span class="stat-label">使用中:</span>
                <span class="stat-value">{{ performanceData?.db_in_use || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">空闲:</span>
                <span class="stat-value">{{ performanceData?.db_idle || 0 }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Goroutine数量 -->
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-title">Goroutine</span>
            <span class="metric-value">{{ performanceData?.goroutine_count || 0 }}</span>
          </div>
          <div class="metric-chart">
            <div class="goroutine-indicator" :class="getGoroutineStatus(performanceData?.goroutine_count)">
              <el-icon>
                <component :is="getGoroutineIconComponent(performanceData?.goroutine_count)" />
              </el-icon>
              <span>{{ getGoroutineStatusText(performanceData?.goroutine_count) }}</span>
            </div>
          </div>
        </div>

        <!-- GC统计 -->
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-title">垃圾回收</span>
            <span class="metric-value">{{ performanceData?.gc_count || 0 }}</span>
          </div>
          <div class="metric-chart">
            <div class="gc-stats">
              <div class="stat-item">
                <span class="stat-label">堆对象:</span>
                <span class="stat-value">{{ formatNumber(performanceData?.heap_objects) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最后GC:</span>
                <span class="stat-value">{{ formatGCTime(performanceData?.last_gc_time) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- CPU核心数 -->
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-title">CPU核心</span>
            <span class="metric-value">{{ performanceData?.cpu_count || 0 }}</span>
          </div>
          <div class="metric-chart">
            <div class="cpu-info">
              <div class="cpu-cores">
                <el-icon><Cpu /></el-icon>
                <span>{{ performanceData?.cpu_count || 0 }} 核心可用</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统状态 -->
        <div class="metric-item">
          <div class="metric-header">
            <span class="metric-title">系统状态</span>
            <span class="metric-value status-badge" :class="getSystemStatusClass(performanceData?.status)">
              {{ getSystemStatusText(performanceData?.status) }}
            </span>
          </div>
          <div class="metric-chart">
            <div class="system-info">
              <div class="stat-item">
                <span class="stat-label">堆内存:</span>
                <span class="stat-value">{{ performanceData?.heap_alloc_mb || 0 }}MB</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">栈内存:</span>
                <span class="stat-value">{{ performanceData?.stack_inuse_mb || 0 }}MB</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="chart-footer">
        <div class="last-update">
          最后更新: {{ formatTimestamp(performanceData?.timestamp) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Refresh,
  Cpu,
  SuccessFilled,
  WarningFilled,
  CircleCloseFilled,
  QuestionFilled
} from '@element-plus/icons-vue'
import type { PerformanceMetrics } from '@/api/dashboardApi'

interface Props {
  performanceData?: PerformanceMetrics
  loading?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  refresh: []
}>()

// 计算内存使用百分比
const memoryUsagePercent = computed(() => {
  if (!props.performanceData?.memory_usage_mb || !props.performanceData?.memory_total_mb) {
    return 0
  }
  return Math.round((props.performanceData.memory_usage_mb / props.performanceData.memory_total_mb) * 100)
})

// 获取进度条颜色
function getProgressColor(percent: number) {
  if (percent < 50) return '#52c41a'
  if (percent < 80) return '#faad14'
  return '#ff4d4f'
}

// 获取Goroutine状态
function getGoroutineStatus(count?: number) {
  if (!count) return 'status-unknown'
  if (count < 100) return 'status-good'
  if (count < 500) return 'status-warning'
  return 'status-danger'
}

function getGoroutineIconComponent(count?: number) {
  if (!count) return QuestionFilled
  if (count < 100) return SuccessFilled
  if (count < 500) return WarningFilled
  return CircleCloseFilled
}

function getGoroutineStatusText(count?: number) {
  if (!count) return '未知'
  if (count < 100) return '正常'
  if (count < 500) return '警告'
  return '过高'
}

// 获取系统状态样式
function getSystemStatusClass(status?: string) {
  switch (status) {
    case 'healthy':
      return 'status-healthy'
    case 'warning':
      return 'status-warning'
    case 'error':
      return 'status-error'
    default:
      return 'status-unknown'
  }
}

function getSystemStatusText(status?: string) {
  switch (status) {
    case 'healthy':
      return '健康'
    case 'warning':
      return '警告'
    case 'error':
      return '错误'
    default:
      return '未知'
  }
}

// 格式化数字
function formatNumber(num?: number) {
  if (!num) return '0'
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toLocaleString()
}

// 格式化GC时间
function formatGCTime(timeStr?: string) {
  if (!timeStr) return '-'
  try {
    const time = new Date(timeStr)
    return time.toLocaleTimeString('zh-CN')
  } catch {
    return timeStr
  }
}

// 格式化时间戳
function formatTimestamp(timestamp?: number) {
  if (!timestamp) return '-'
  try {
    return new Date(timestamp * 1000).toLocaleString('zh-CN')
  } catch {
    return '-'
  }
}

// 刷新数据
function refreshData() {
  emit('refresh')
}
</script>

<style lang="scss" scoped>
.performance-chart {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.metric-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  
  &:hover {
    border-color: #d9d9d9;
  }
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  .metric-title {
    font-weight: 500;
    color: #333;
  }
  
  .metric-value {
    font-size: 18px;
    font-weight: 600;
    color: #1890ff;
    
    &.status-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      
      &.status-healthy {
        background: #f6ffed;
        color: #52c41a;
      }
      
      &.status-warning {
        background: #fffbe6;
        color: #faad14;
      }
      
      &.status-error {
        background: #fff2f0;
        color: #ff4d4f;
      }
      
      &.status-unknown {
        background: #f5f5f5;
        color: #999;
      }
    }
  }
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
  
  .progress-fill {
    height: 100%;
    transition: width 0.3s ease;
  }
}

.metric-details {
  font-size: 12px;
  color: #666;
}

.connection-stats,
.gc-stats,
.system-info {
  .stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 12px;
    
    .stat-label {
      color: #666;
    }
    
    .stat-value {
      color: #333;
      font-weight: 500;
    }
  }
}

.goroutine-indicator {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  
  i {
    margin-right: 6px;
  }
  
  &.status-good {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.status-warning {
    background: #fffbe6;
    color: #faad14;
  }
  
  &.status-danger {
    background: #fff2f0;
    color: #ff4d4f;
  }
  
  &.status-unknown {
    background: #f5f5f5;
    color: #999;
  }
}

.cpu-info {
  .cpu-cores {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 12px;
    
    i {
      margin-right: 6px;
      font-size: 16px;
    }
  }
}

.chart-footer {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  
  .last-update {
    font-size: 12px;
    color: #666;
    text-align: center;
  }
}

// 深色主题适配
.dark .performance-chart {
  background: #1f1f1f;
  color: #fff;
  
  .metric-item {
    border-color: #333;
    
    &:hover {
      border-color: #555;
    }
  }
  
  .metric-header .metric-title {
    color: #fff;
  }
  
  .progress-bar {
    background: #333;
  }
  
  .connection-stats,
  .gc-stats,
  .system-info {
    .stat-item .stat-value {
      color: #fff;
    }
  }
  
  .chart-footer {
    border-top-color: #333;
  }
}
</style>
