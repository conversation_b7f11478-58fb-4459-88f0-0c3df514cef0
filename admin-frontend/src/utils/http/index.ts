import axios, { InternalAxiosRequestConfig, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import EmojiText from '../emojo'

// 调试环境变量
console.log('Environment variables:', {
  VITE_API_URL: import.meta.env.VITE_API_URL,
  NODE_ENV: import.meta.env.NODE_ENV,
  MODE: import.meta.env.MODE
})

const axiosInstance = axios.create({
  timeout: 15000, // 请求超时时间(毫秒)
  // 统一使用相对路径，通过前端服务器或 Nginx 反向代理到后端，彻底消除跨域
  baseURL: '',
  withCredentials: false, // 使用JWT token认证，不需要cookie
  transformRequest: [(data) => JSON.stringify(data)], // 请求数据转换为 JSON 字符串
  validateStatus: (status) => status >= 200 && status < 300, // 只接受 2xx 的状态码
  headers: {
    get: { 'Content-Type': 'application/json;charset=utf-8' },
    post: { 'Content-Type': 'application/json;charset=utf-8' }
  },
  transformResponse: [
    (data) => {
      // 响应数据转换
      try {
        // 如果是字符串且看起来像JSON，尝试解析
        if (typeof data === 'string') {
          // 检查是否是JSON字符串（以{或[开头）
          const trimmed = data.trim()
          if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
            return JSON.parse(data)
          }
        }
        return data
      } catch (error) {
        console.warn('JSON parse error:', error)
        return data // 解析失败时返回原数据
      }
    }
  ]
})

// 请求拦截器
axiosInstance.interceptors.request.use(
  (request: InternalAxiosRequestConfig) => {
    // 调试请求信息
    console.log('Request interceptor:', {
      url: request.url,
      baseURL: request.baseURL,
      fullURL: `${request.baseURL}${request.url}`,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries ? request.headers.entries() : [])
    })

    // 设置默认Content-Type
    if (!request.headers.get('Content-Type')) {
      request.headers.set('Content-Type', 'application/json')
    }

    // 对于登录接口，不需要设置token
    const isLoginRequest = request.url?.includes('/auth/login') || request.url?.includes('/admin/auth/login')

    if (!isLoginRequest) {
      // 优先使用管理员令牌
      const adminToken = localStorage.getItem('admin_token')
      const { token } = useUserStore().info

      // 设置认证令牌
      const authToken = adminToken || token
      if (authToken) {
        request.headers.set('Authorization', authToken.startsWith('Bearer ') ? authToken : `Bearer ${authToken}`)
      }
    }

    // 🔧 智能缓存策略：根据请求类型决定缓存策略
    const isCacheable = isCacheableRequest(request)
    const isRealTime = isRealTimeRequest(request)

    if (isRealTime) {
      // 实时数据请求：禁用缓存
      request.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
      request.headers.set('Pragma', 'no-cache')
      request.headers.set('Expires', '0')
      console.log(`🚫 实时请求禁用缓存: ${request.url}`)
    } else if (isCacheable) {
      // 可缓存请求：设置合适的缓存头
      request.headers.set('Cache-Control', 'public, max-age=300') // 5分钟缓存
      console.log(`💾 可缓存请求: ${request.url}`)
    }

    // 添加请求ID用于追踪（如果不会导致CORS问题）
    if (!request.url?.includes('external-api')) {
      request.headers.set('X-Request-ID', generateRequestId())
    }

    return request // 返回修改后的配置
  },
  (error) => {
    ElMessage.error(`服务器异常！ ${EmojiText[500]}`) // 显示错误消息
    return Promise.reject(error) // 返回拒绝的 Promise
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    // 调试响应数据
    console.log('Response interceptor:', {
      url: response.config.url,
      status: response.status,
      dataType: typeof response.data,
      dataPreview: typeof response.data === 'string' ? response.data.substring(0, 100) + '...' : response.data
    })
    return response
  },
  (error) => {
    console.error('Response error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method,
      baseURL: error.config?.baseURL,
      data: error.response?.data
    })

    if (axios.isCancel(error)) {
      console.log('repeated request: ' + error.message)
    } else {
      const errorMessage = error.response?.data.message
      ElMessage.error(
        errorMessage
          ? `${errorMessage} ${EmojiText[500]}`
          : `请求超时或服务器异常！${EmojiText[500]}`
      )
    }
    return Promise.reject(error)
  }
)

// 请求
async function request<T = any>(config: AxiosRequestConfig): Promise<T> {
  // 将 POST | PUT 请求的参数放入 data 中，并清空 params（仅当data为空且params有值时）
  if (config.method === 'POST' || config.method === 'PUT') {
    if (!config.data && config.params) {
      config.data = config.params
      config.params = {}
    }
  }
  try {
    const res = await axiosInstance.request<T>({ ...config })
    return res.data
  } catch (e) {
    if (axios.isAxiosError(e)) {
      // 可以在这里处理 Axios 错误
    }
    return Promise.reject(e)
  }
}

// 🔧 判断是否为可缓存请求
function isCacheableRequest(request: InternalAxiosRequestConfig): boolean {
  const url = request.url || ''
  const method = request.method?.toUpperCase() || 'GET'

  // 只有GET请求才考虑缓存
  if (method !== 'GET') return false

  // 可缓存的API路径模式
  const cacheablePatterns = [
    '/api/v1/express/companies',
    '/api/v1/express/providers',
    '/api/v1/system/config',
    '/api/v1/system/regions',
    '/api/v1/public/',
    '/api/v1/admin/express/companies',
    '/api/v1/admin/express/providers'
  ]

  return cacheablePatterns.some(pattern => url.includes(pattern))
}

// 🔧 判断是否为实时数据请求
function isRealTimeRequest(request: InternalAxiosRequestConfig): boolean {
  const url = request.url || ''

  // 需要实时数据的API路径模式
  const realTimePatterns = [
    '/admin/balance',
    '/admin/orders',
    '/balance/transactions',
    '/orders/statistics',
    '/dashboard/',
    '/metrics',
    '/status',
    '/health'
  ]

  return realTimePatterns.some(pattern => url.includes(pattern))
}

// 生成请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// API 方法集合
const api = {
  get<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'GET' }) // GET 请求
  },
  post<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'POST' }) // POST 请求
  },
  put<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'PUT' }) // PUT 请求
  },
  del<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'DELETE' }) // DELETE 请求
  },
  patch<T>(config: AxiosRequestConfig): Promise<T> {
    return request({ ...config, method: 'PATCH' }) // PATCH 请求
  },
  // 直接请求方法
  request<T>(config: AxiosRequestConfig): Promise<T> {
    return request(config)
  },
  // 设置认证令牌
  setAuthToken(token: string): void {
    axiosInstance.defaults.headers.common['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`
  },
  // 清除认证令牌
  clearAuthToken(): void {
    delete axiosInstance.defaults.headers.common['Authorization']
  },
  // 获取axios实例
  getInstance() {
    return axiosInstance
  }
}

// 导出http工具
export const http = api
export default api
