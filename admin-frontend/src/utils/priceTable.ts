/**
 * 价格表相关工具函数
 */

/**
 * 格式化货币金额
 * @param amount 金额
 * @param currency 货币符号，默认为 ¥
 * @param precision 小数位数，默认为 2
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(
  amount: string | number | undefined | null,
  currency: string = '¥',
  precision: number = 2
): string {
  if (amount === undefined || amount === null || amount === '') {
    return '-'
  }

  const num = typeof amount === 'string' ? parseFloat(amount) : amount

  if (isNaN(num)) {
    return '-'
  }

  return `${currency}${num.toFixed(precision)}`
}

/**
 * 检查价格数据是否有效
 * 单一职责原则：专门负责数据有效性检查
 * 优化：更宽松的验证逻辑，允许显示更多数据
 */
export function isValidPriceData(value: string | number | null | undefined): boolean {
  if (value === null || value === undefined) {
    return false
  }

  if (typeof value === 'string') {
    const cleanValue = value.trim()
    if (cleanValue === '' || cleanValue === 'null' || cleanValue === 'undefined') {
      return false
    }
    const num = parseFloat(cleanValue)
    return !isNaN(num) && num >= 0
  }

  if (typeof value === 'number') {
    return !isNaN(value) && value >= 0
  }

  return false
}

/**
 * 检查价格数据是否存在（更宽松的检查）
 * 用于显示目的，即使是0也认为是有效的
 */
export function hasPriceData(value: string | number | null | undefined): boolean {
  if (value === null || value === undefined) {
    return false
  }

  if (typeof value === 'string') {
    const cleanValue = value.trim()
    if (cleanValue === '' || cleanValue === 'null' || cleanValue === 'undefined') {
      return false
    }
    const num = parseFloat(cleanValue)
    return !isNaN(num) && num >= 0
  }

  if (typeof value === 'number') {
    return !isNaN(value) && value >= 0
  }

  return false
}

/**
 * 检查重量数据是否有效
 */
export function isValidWeightData(value: number | null | undefined): boolean {
  return value !== null && value !== undefined && !isNaN(value) && value > 0
}

/**
 * 检查抛比数据是否有效
 */
export function isValidVolumeRatio(value: number | null | undefined): boolean {
  return value !== null && value !== undefined && !isNaN(value) && value > 0
}

/**
 * 格式化重量显示（带单位）
 * @param weight 重量值
 * @param unit 单位，默认为 kg
 * @returns 格式化后的重量字符串
 */
export function formatWeight(
  weight: number | null | undefined,
  unit: string = 'kg'
): string {
  if (weight === null || weight === undefined || isNaN(weight)) {
    return '-'
  }
  return `${weight}${unit}`
}

/**
 * 格式化体积比显示
 * @param ratio 体积比值
 * @returns 格式化后的体积比字符串
 */
export function formatVolumeRatio(ratio: number | null | undefined): string {
  if (ratio === null || ratio === undefined || isNaN(ratio)) {
    return '-'
  }
  return `1:${ratio}`
}

/**
 * 格式化天数显示
 * @param days 天数
 * @returns 格式化后的天数字符串
 */
export function formatDays(days: number | null | undefined): string {
  if (days === null || days === undefined || isNaN(days)) {
    return '-'
  }
  return `${days}天`
}

/**
 * 格式化重量范围显示
 * @param firstWeight 首重
 * @param maxWeight 最大重量
 * @returns 格式化后的重量范围字符串
 */
export function formatWeightRange(
  firstWeight: number | null | undefined,
  maxWeight: number | null | undefined
): string {
  const first = firstWeight || 1
  const max = maxWeight || 50
  return `${first}-${max}kg`
}

import { formatDateTime as formatDateTimeWithTimezone, DateTimeFormats } from './timezone'

/**
 * 格式化日期时间（使用北京时间）
 * @param dateTime 日期时间字符串或Date对象
 * @param format 格式类型
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(
  dateTime: string | Date | undefined | null,
  format: 'full' | 'date' | 'time' = 'full'
): string {
  if (!dateTime) return '-'

  let options: Intl.DateTimeFormatOptions

  switch (format) {
    case 'date':
      options = DateTimeFormats.DATE_ONLY
      break
    case 'time':
      options = DateTimeFormats.TIME_ONLY
      break
    default:
      options = DateTimeFormats.FULL
      break
  }

  return formatDateTimeWithTimezone(dateTime, options)
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number | undefined | null): string {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

/**
 * 格式化响应时间
 * @param ms 毫秒数
 * @returns 格式化后的响应时间字符串
 */
export function formatResponseTime(ms: number | undefined | null): string {
  if (!ms && ms !== 0) return '-'
  
  if (ms < 1000) {
    return `${ms}ms`
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

/**
 * 格式化百分比
 * @param value 数值（0-100）
 * @param precision 小数位数，默认为 1
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(
  value: number | undefined | null,
  precision: number = 1
): string {
  if (value === undefined || value === null || isNaN(value)) {
    return '-'
  }
  
  return `${value.toFixed(precision)}%`
}

/**
 * 获取供应商标签类型
 * @param provider 供应商代码
 * @returns Element Plus 标签类型
 */
export function getProviderTagType(provider: string): string {
  const typeMap: Record<string, string> = {
    'yida': 'primary',
    'kuaidi100': 'success',
    'yuntong': 'warning'
  }
  return typeMap[provider] || 'info'
}

/**
 * 获取供应商显示名称
 * @param provider 供应商代码
 * @returns 供应商显示名称
 */
export function getProviderLabel(provider: string): string {
  const labelMap: Record<string, string> = {
    'yida': '易达',
    'kuaidi100': '快递100',
    'yuntong': '云通'
  }
  return labelMap[provider] || provider
}

/**
 * 获取快递公司显示名称
 * @param expressCode 快递公司代码
 * @returns 快递公司显示名称
 */
export function getExpressCompanyLabel(expressCode: string): string {
  const labelMap: Record<string, string> = {
    'YTO': '圆通速递',
    'STO': '申通快递',
    'YD': '韵达速递',
    'ZTO': '中通快递',
    'JT': '极兔速递',
    'DBL': '德邦物流',
    'SF': '顺丰速运',
    'JD': '京东物流',
    'HTKY': '百世汇通',
    'YZPY': '邮政包裹'
  }
  return labelMap[expressCode] || expressCode
}

/**
 * 获取任务状态标签类型
 * @param status 任务状态
 * @returns Element Plus 标签类型
 */
export function getTaskStatusTagType(status: string): string {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'running': 'primary',
    'completed': 'success',
    'failed': 'danger',
    'cancelled': 'warning'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取任务状态显示名称
 * @param status 任务状态
 * @returns 任务状态显示名称
 */
export function getTaskStatusLabel(status: string): string {
  const labelMap: Record<string, string> = {
    'pending': '等待中',
    'running': '运行中',
    'completed': '已完成',
    'failed': '已失败',
    'cancelled': '已取消'
  }
  return labelMap[status] || status
}

/**
 * 获取服务类型显示名称
 * @param serviceType 服务类型
 * @returns 服务类型显示名称
 */
export function getServiceTypeLabel(serviceType: string): string {
  const labelMap: Record<string, string> = {
    'standard': '标准服务',
    'express': '快递服务',
    'economy': '经济服务',
    'premium': '高端服务'
  }
  return labelMap[serviceType] || serviceType
}

/**
 * 计算预估查询时间
 * @param queryCount 查询数量
 * @param avgTimePerQuery 每个查询的平均时间（毫秒），默认200ms
 * @returns 预估时间字符串
 */
export function estimateQueryTime(
  queryCount: number,
  avgTimePerQuery: number = 200
): string {
  if (queryCount === 0) return '0秒'
  
  const totalMs = queryCount * avgTimePerQuery
  const totalSeconds = Math.ceil(totalMs / 1000)
  
  if (totalSeconds < 60) {
    return `约${totalSeconds}秒`
  } else if (totalSeconds < 3600) {
    const minutes = Math.floor(totalSeconds / 60)
    const seconds = totalSeconds % 60
    return seconds > 0 ? `约${minutes}分${seconds}秒` : `约${minutes}分钟`
  } else {
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    return minutes > 0 ? `约${hours}小时${minutes}分钟` : `约${hours}小时`
  }
}

/**
 * 生成路线显示文本
 * @param fromProvince 发货省份
 * @param fromCity 发货城市
 * @param toProvince 收货省份
 * @param toCity 收货城市
 * @param showCity 是否显示城市，默认为true
 * @returns 路线显示文本
 */
export function formatRoute(
  fromProvince: string,
  fromCity: string,
  toProvince: string,
  toCity: string,
  showCity: boolean = true
): string {
  if (showCity) {
    return `${fromProvince} ${fromCity} → ${toProvince} ${toCity}`
  } else {
    return `${fromProvince} → ${toProvince}`
  }
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns Promise<boolean> 是否复制成功
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (error) {
    console.error('Copy to clipboard failed:', error)
    
    // 降级方案：使用传统的复制方法
    try {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      const result = document.execCommand('copy')
      document.body.removeChild(textArea)
      return result
    } catch (fallbackError) {
      console.error('Fallback copy method failed:', fallbackError)
      return false
    }
  }
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func.apply(null, args)
    }, wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  let previous = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    const remaining = wait - (now - previous)
    
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      func.apply(null, args)
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now()
        timeout = null
        func.apply(null, args)
      }, remaining)
    }
  }
}
