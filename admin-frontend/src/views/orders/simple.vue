<template>
  <div class="simple-order-page">
    <h1>订单管理 - 简单版本</h1>
    <p>当前路由: {{ $route.path }}</p>
    <p>当前时间: {{ currentTime }}</p>
    
    <el-card>
      <h3>路由测试成功！</h3>
      <p>如果您能看到这个页面，说明订单管理路由已经正常工作。</p>
      
      <el-button type="primary" @click="testApi">测试API连接</el-button>
      <el-button type="success" @click="loadFullVersion">加载完整版本</el-button>
    </el-card>

    <el-card v-if="apiResult" style="margin-top: 20px;">
      <h4>API测试结果:</h4>
      <pre>{{ apiResult }}</pre>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const currentTime = ref('')
const apiResult = ref('')

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

// 测试API连接
const testApi = async () => {
  try {
    const response = await fetch('/api/v1/admin/orders?page=1&page_size=5')
    const data = await response.json()
    apiResult.value = JSON.stringify(data, null, 2)
  } catch (error) {
    apiResult.value = `API测试失败: ${(error as any)?.message || '未知错误'}`
  }
}

// 加载完整版本
const loadFullVersion = () => {
  // 这里可以动态加载完整的订单管理组件
  console.log('准备加载完整版本...')
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
.simple-order-page {
  padding: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 300px;
}
</style>
