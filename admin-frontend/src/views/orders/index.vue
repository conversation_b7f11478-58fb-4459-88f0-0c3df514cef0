<template>
  <div class="order-management">


    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <i class="iconfont-sys">&#xe76c;</i>
          订单管理
        </h1>
        <p class="page-description">管理和查看所有订单信息</p>
      </div>
      <div class="page-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="forceRefreshLatest" :loading="loading" type="primary">
          <el-icon><Refresh /></el-icon>
          最新订单
        </el-button>
        <el-button type="primary" @click="exportOrders" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards" v-if="statistics">
      <el-row :gutter="20">
        <el-col :span="6">
          <StatCard
            title="总订单数"
            :value="statistics.total_orders"
            icon="el-icon-shopping-cart-2"
            icon-color="#409EFF"
            icon-bg-color="#E6F7FF"
          />
        </el-col>
        <el-col :span="6">
          <StatCard
            title="今日订单"
            :value="statistics.today_orders"
            icon="el-icon-clock"
            icon-color="#67C23A"
            icon-bg-color="#F0F9FF"
          />
        </el-col>
        <el-col :span="6">
          <StatCard
            title="总金额"
            :value="formatCurrency(statistics.total_amount)"
            icon="el-icon-coin"
            icon-color="#E6A23C"
            icon-bg-color="#FDF6EC"
          />
        </el-col>
        <el-col :span="6">
          <StatCard
            title="今日金额"
            :value="formatCurrency(statistics.today_amount)"
            icon="el-icon-wallet"
            icon-color="#F56C6C"
            icon-bg-color="#FEF0F0"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-card>
        <el-form :model="searchForm" label-width="80px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="订单号">
                <el-input
                  v-model="searchForm.search_keyword"
                  placeholder="请输入订单号"
                  clearable
                  @keyup.enter="handleSearch"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订单状态">
                <el-select
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in ORDER_STATUS_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="快递公司">
                <el-select
                  v-model="searchForm.express_type"
                  placeholder="请选择快递公司"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="顺丰" value="SF" />
                  <el-option label="圆通" value="YTO" />
                  <el-option label="中通" value="ZTO" />
                  <el-option label="申通" value="STO" />
                  <el-option label="韵达" value="YD" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="用户名">
                <el-input
                  v-model="searchForm.username"
                  placeholder="请输入用户名"
                  clearable
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="计费状态">
                <el-select
                  v-model="searchForm.billing_status"
                  placeholder="请选择计费状态"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in BILLING_STATUS_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  @change="handleDateRangeChange"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label=" ">
                <el-button-group>
                  <el-button type="primary" @click="handleSearch" :loading="loading">
                    <el-icon><Search /></el-icon>
                    搜索
                  </el-button>
                  <el-button @click="handleReset">
                    <el-icon><RefreshLeft /></el-icon>
                    重置
                  </el-button>
                  <el-button type="info" @click="toggleAdvancedSearch">
                    {{ showAdvancedSearch ? '收起' : '展开' }}
                    <el-icon><ArrowDown v-if="!showAdvancedSearch" /><ArrowUp v-else /></el-icon>
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 高级搜索 -->
          <div v-show="showAdvancedSearch">
            <el-divider content-position="left">高级搜索</el-divider>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="价格范围">
                  <el-input-number
                    v-model="searchForm.price_min"
                    placeholder="最小价格"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="　">
                  <el-input-number
                    v-model="searchForm.price_max"
                    placeholder="最大价格"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="重量范围">
                  <el-input-number
                    v-model="searchForm.weight_min"
                    placeholder="最小重量(kg)"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="　">
                  <el-input-number
                    v-model="searchForm.weight_max"
                    placeholder="最大重量(kg)"
                    :min="0"
                    :precision="2"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 批量操作 -->
    <div class="batch-operations" v-if="selectedOrders.length > 0">
      <el-card>
        <div class="batch-actions">
          <span class="selected-info">已选择 {{ selectedOrders.length }} 个订单</span>
          <el-button-group>
            <el-button type="warning" @click="showBatchStatusDialog = true">
              <el-icon><Edit /></el-icon>
              批量更新状态
            </el-button>
            <el-button type="danger" @click="clearSelection">
              <el-icon><Close /></el-icon>
              取消选择
            </el-button>
          </el-button-group>
        </div>
      </el-card>
    </div>

    <!-- 订单列表 -->
    <div class="order-list">
      <el-card>
        <el-table
          ref="orderTable"
          :data="orderList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="80" sortable="custom" />
          <el-table-column prop="order_no" label="订单号" width="180" show-overflow-tooltip />
          <el-table-column prop="customer_order_no" label="客户订单号" width="150" show-overflow-tooltip />
          <el-table-column label="用户信息" width="120">
            <template #default="{ row }">
              <div>
                <div class="user-name">{{ row.user?.username || '-' }}</div>
                <div class="user-email">{{ row.user?.email || '-' }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="express_type" label="快递公司" width="100" />
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="weight" label="重量(kg)" width="100" sortable="custom" />
          <el-table-column label="费用信息" width="120">
            <template #default="{ row }">
              <div>
                <div class="price">¥{{ row.price?.toFixed(2) || '0.00' }}</div>
                <div class="billing-status">
                  <el-tag size="small" :type="getBillingStatusTagType(row.billing_status)">
                    {{ getBillingStatusLabel(row.billing_status) }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 价格验证列 -->
          <el-table-column label="价格验证" width="280">
            <template #header>
              <div style="display: flex; align-items: center; gap: 4px; flex-wrap: wrap;">
                <span style="white-space: nowrap;">价格验证</span>
                <el-button
                  type="primary"
                  size="small"
                  @click="openBatchPriceValidationDialog"
                  title="批量价格验证"
                  style="font-size: 11px; padding: 2px 6px;"
                >
                  批量验证
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="openBatchStatusSyncDialog"
                  title="批量状态同步"
                  style="font-size: 11px; padding: 2px 6px;"
                >
                  状态同步
                </el-button>
              </div>
            </template>
            <template #default="{ row }">
              <div class="price-validation">
                <!-- 🔥 企业级修复：区分未验证和正在验证状态 -->
                <div v-if="!row.price_validation || (!row.price_validation.query_time && row.price_validation.query_status === 'pending')" class="validation-not-started">
                  <el-tag type="info" size="small">未验证</el-tag>
                </div>
                <div v-else-if="row.price_validation.query_status === 'pending'" class="validation-loading">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  <span>查询中...</span>
                </div>
                <div v-else-if="row.price_validation.query_status === 'failed'" class="validation-failed">
                  <el-tag type="danger" size="small">查询失败</el-tag>
                  <div class="error-msg" v-if="row.price_validation.error_message">
                    {{ row.price_validation.error_message }}
                  </div>
                </div>
                <div v-else-if="!row.price_validation.supported" class="validation-unsupported">
                  <el-tag type="info" size="small">不支持</el-tag>
                  <div class="provider-info">{{ row.provider }}</div>
                </div>
                <div v-else-if="row.price_validation.query_status === 'success'" class="validation-result">
                  <div class="price-comparison">
                    <div class="system-price">系统: ¥{{ row.price_validation.system_price?.toFixed(2) || '0.00' }}</div>
                    <div class="provider-price">供应商: ¥{{ row.price_validation.provider_price?.toFixed(2) || '0.00' }}</div>
                  </div>
                  <div class="profit-status">
                    <el-tag
                      :type="getProfitStatusTagType(row.price_validation.profit_status)"
                      size="small"
                    >
                      {{ getProfitStatusLabel(row.price_validation.profit_status) }}
                    </el-tag>
                  </div>
                  <div class="query-time" v-if="row.price_validation.query_time">
                    {{ formatQueryTime(row.price_validation.query_time) }}
                  </div>
                </div>
                <div v-else class="validation-unknown">
                  <el-tag type="warning" size="small">未知状态</el-tag>
                  <div class="status-info">{{ row.price_validation.query_status }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160" sortable="custom">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button type="primary" size="small" @click="viewOrderDetail(row.id)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="warning" size="small" @click="showUpdateStatusDialogHandler(row)">
                  <el-icon><Edit /></el-icon>
                  状态
                </el-button>
                <el-button type="info" size="small" @click="syncOrderStatus(row)" :loading="row.syncing">
                  <el-icon><Refresh /></el-icon>
                  同步
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 更新状态对话框 -->
    <UpdateStatusDialog
      v-model:visible="showUpdateStatusDialog"
      :order="currentOrder"
      @success="handleUpdateSuccess"
    />

    <!-- 批量更新状态对话框 -->
    <BatchUpdateStatusDialog
      v-model:visible="showBatchStatusDialog"
      :order-ids="selectedOrderIds"
      @success="handleBatchUpdateSuccess"
    />

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model:visible="showOrderDetailDialog"
      :order-id="currentOrderId"
    />

    <!-- 批量价格验证对话框 -->
    <el-dialog
      v-model="showBatchPriceValidationDialog"
      title="批量价格验证"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="batchValidationForm" label-width="100px">
        <el-form-item label="供应商">
          <el-select
            v-model="batchValidationForm.provider"
            placeholder="选择供应商（可选）"
            clearable
            style="width: 100%"
          >
            <el-option label="快递100" value="kuaidi100" />
            <el-option label="云通" value="yuntong" />
            <el-option label="易达" value="yida" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="batchValidationForm.start_time"
            type="datetime"
            placeholder="开始时间（可选）"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ss.000Z"
            style="width: 48%; margin-right: 4%"
          />
          <el-date-picker
            v-model="batchValidationForm.end_time"
            type="datetime"
            placeholder="结束时间（可选）"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ss.000Z"
            style="width: 48%"
          />
        </el-form-item>

        <el-form-item label="最大数量">
          <el-input-number
            v-model="batchValidationForm.max_count"
            :min="1"
            :max="500"
            placeholder="最大处理订单数量"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <!-- 验证结果展示 -->
      <div v-if="batchValidationResults" class="validation-results">
        <el-divider content-position="left">验证结果</el-divider>

        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="总订单数" :value="batchValidationResults.total_orders" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="已处理" :value="batchValidationResults.processed_orders" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="成功" :value="batchValidationResults.success_orders" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="失败" :value="batchValidationResults.failed_orders" />
          </el-col>
        </el-row>

        <!-- 🔥 企业级修复：添加汇总信息展示 -->
        <div v-if="batchValidationResults.summary" class="validation-summary">
          <el-divider content-position="left">盈亏汇总</el-divider>
          <el-row :gutter="16">
            <el-col :span="6">
              <el-statistic title="盈利订单" :value="batchValidationResults.summary.profit_count">
                <template #suffix>
                  <span style="color: #67C23A;">个</span>
                </template>
              </el-statistic>
              <div class="summary-amount profit">+¥{{ batchValidationResults.summary.total_profit?.toFixed(2) || '0.00' }}</div>
            </el-col>
            <el-col :span="6">
              <el-statistic title="亏损订单" :value="batchValidationResults.summary.loss_count">
                <template #suffix>
                  <span style="color: #F56C6C;">个</span>
                </template>
              </el-statistic>
              <div class="summary-amount loss">-¥{{ batchValidationResults.summary.total_loss?.toFixed(2) || '0.00' }}</div>
            </el-col>
            <el-col :span="6">
              <el-statistic title="持平订单" :value="batchValidationResults.summary.break_even_count">
                <template #suffix>
                  <span style="color: #E6A23C;">个</span>
                </template>
              </el-statistic>
            </el-col>
            <el-col :span="6">
              <el-statistic title="不支持" :value="batchValidationResults.summary.unsupported_count">
                <template #suffix>
                  <span style="color: #909399;">个</span>
                </template>
              </el-statistic>
            </el-col>
          </el-row>
        </div>

        <div v-if="batchValidationResults.summary" class="summary-info" style="margin-top: 16px;">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-statistic title="盈利订单" :value="batchValidationResults.summary.profit_count" />
              <div style="color: #67C23A; font-size: 12px;">
                +¥{{ batchValidationResults.summary.total_profit?.toFixed(2) || '0.00' }}
              </div>
            </el-col>
            <el-col :span="8">
              <el-statistic title="亏损订单" :value="batchValidationResults.summary.loss_count" />
              <div style="color: #F56C6C; font-size: 12px;">
                -¥{{ batchValidationResults.summary.total_loss?.toFixed(2) || '0.00' }}
              </div>
            </el-col>
            <el-col :span="8">
              <el-statistic title="持平订单" :value="batchValidationResults.summary.break_even_count" />
            </el-col>
          </el-row>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeBatchPriceValidationDialog">取消</el-button>
          <el-button
            type="primary"
            @click="performBatchPriceValidation"
            :loading="batchValidationLoading"
          >
            开始验证
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量状态同步对话框 -->
    <el-dialog
      v-model="showBatchStatusSyncDialog"
      title="批量状态同步"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="batchStatusSyncForm" label-width="100px">
        <el-form-item label="供应商">
          <el-select v-model="batchStatusSyncForm.provider" placeholder="选择供应商（可选）" clearable>
            <el-option label="快递100" value="kuaidi100" />
            <el-option label="云通" value="yuntong" />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="batchStatusSyncForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            @change="handleStatusSyncDateRangeChange"
          />
        </el-form-item>

        <el-form-item label="最大数量">
          <el-input-number
            v-model="batchStatusSyncForm.max_count"
            :min="1"
            :max="500"
            placeholder="最大处理数量"
          />
        </el-form-item>

        <el-form-item label="同步选项">
          <el-checkbox v-model="batchStatusSyncForm.dry_run">试运行模式</el-checkbox>
          <el-checkbox v-model="batchStatusSyncForm.only_failed">只同步失败订单</el-checkbox>
          <el-checkbox v-model="batchStatusSyncForm.force_sync">强制同步</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="closeBatchStatusSyncDialog">取消</el-button>
        <el-button type="primary" @click="performBatchStatusSync" :loading="batchStatusSyncLoading">
          {{ batchStatusSyncForm.dry_run ? '试运行' : '开始同步' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Download,
  Search,
  RefreshLeft,
  ArrowDown,
  ArrowUp,
  Edit,
  Close,
  View,
  Loading
} from '@element-plus/icons-vue'

import StatCard from '@/components/Dashboard/StatCard.vue'
import UpdateStatusDialog from './components/UpdateStatusDialog.vue'
import BatchUpdateStatusDialog from './components/BatchUpdateStatusDialog.vue'
import OrderDetailDialog from './components/OrderDetailDialog.vue'

import {
  OrderApi,
  type AdminOrderListItem,
  type AdminOrderListRequest,
  type AdminOrderStatistics,
  ORDER_STATUS_OPTIONS,
  BILLING_STATUS_OPTIONS
} from '@/api/orderApi'

// ==================== 响应式数据 ====================
const loading = ref(false)
const exporting = ref(false)
const showAdvancedSearch = ref(false)
const showUpdateStatusDialog = ref(false)
const showBatchStatusDialog = ref(false)
const showOrderDetailDialog = ref(false)

// 订单列表数据
const orderList = ref<AdminOrderListItem[]>([])
const statistics = ref<AdminOrderStatistics | null>(null)
const selectedOrders = ref<AdminOrderListItem[]>([])
const currentOrder = ref<AdminOrderListItem | null>(null)
const currentOrderId = ref<number | null>(null)

// 批量价格验证相关
const showBatchPriceValidationDialog = ref(false)
const batchValidationLoading = ref(false)
const batchValidationForm = reactive({
  provider: '',
  start_time: '',
  end_time: '',
  max_count: 100
})
const batchValidationResults = ref<any>(null)

// 批量状态同步相关
const showBatchStatusSyncDialog = ref(false)
const batchStatusSyncLoading = ref(false)
const batchStatusSyncForm = reactive({
  provider: '',
  dateRange: null as [string, string] | null,
  start_time: '',
  end_time: '',
  max_count: 100,
  dry_run: false,
  only_failed: false,
  force_sync: false
})
const batchStatusSyncResults = ref<any>(null)

// 搜索表单
const searchForm = reactive<AdminOrderListRequest>({
  page: 1,
  page_size: 20,
  sort_by: 'created_at',
  sort_order: 'desc',
  // 搜索字段
  search_keyword: undefined,
  status: undefined,
  express_type: undefined,
  username: undefined,
  billing_status: undefined,
  start_time: undefined,
  end_time: undefined,
  price_min: undefined,
  price_max: undefined,
  weight_min: undefined,
  weight_max: undefined
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// ==================== 计算属性 ====================
const selectedOrderIds = computed(() => selectedOrders.value.map(order => order.id))

// ==================== 方法 ====================

/**
 * 加载订单列表
 */
const loadOrderList = async () => {
  try {
    loading.value = true
    
    const params: AdminOrderListRequest = {
      ...searchForm,
      page: pagination.page,
      page_size: pagination.pageSize
    }

    const response = await OrderApi.getAdminOrderList(params)
    
    if (response.success) {
      orderList.value = response.data.items
      pagination.total = response.data.total
      statistics.value = response.data.statistics || null
    } else {
      ElMessage.error(response.message || '获取订单列表失败')
    }
  } catch (error) {
    console.error('Load order list error:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新数据
 */
const refreshData = async () => {
  // 🔥 强制清除可能的缓存
  pagination.page = 1
  
  // 确保排序参数正确
  searchForm.sort_by = 'created_at'
  searchForm.sort_order = 'desc'
  
  // 强制刷新
  await loadOrderList()
  
  ElMessage.success('数据已刷新')
}

/**
 * 强制刷新最新订单
 */
const forceRefreshLatest = async () => {
  try {
    loading.value = true
    
    // 重置所有筛选条件
    Object.assign(searchForm, {
      page: 1,
      page_size: 20,
      sort_by: 'created_at',
      sort_order: 'desc',
      search_keyword: undefined,
      status: undefined,
      express_type: undefined,
      username: undefined,
      billing_status: undefined,
      start_time: undefined,
      end_time: undefined,
      price_min: undefined,
      price_max: undefined,
      weight_min: undefined,
      weight_max: undefined
    })
    
    // 重置分页
    pagination.page = 1
    pagination.pageSize = 20
    
    // 重置日期范围
    dateRange.value = null
    
    // 强制刷新
    await loadOrderList()
    
    ElMessage.success('已显示最新订单列表')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.page = 1
  loadOrderList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  // 重置搜索表单到初始状态
  Object.assign(searchForm, {
    page: 1,
    page_size: 20,
    sort_by: 'created_at',
    sort_order: 'desc',
    // 清空所有搜索字段
    search_keyword: undefined,
    status: undefined,
    express_type: undefined,
    username: undefined,
    billing_status: undefined,
    start_time: undefined,
    end_time: undefined,
    price_min: undefined,
    price_max: undefined,
    weight_min: undefined,
    weight_max: undefined
  })

  // 重置日期范围
  dateRange.value = null

  // 重置分页
  pagination.page = 1
  pagination.pageSize = 20

  // 重新加载数据
  loadOrderList()

  // 提示用户
  ElMessage.success('搜索条件已重置')
}

/**
 * 切换高级搜索
 */
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

/**
 * 处理日期范围变化
 */
const handleDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchForm.start_time = value[0]
    searchForm.end_time = value[1]
  } else {
    searchForm.start_time = undefined
    searchForm.end_time = undefined
  }
}

/**
 * 处理选择变化
 */
const handleSelectionChange = (selection: AdminOrderListItem[]) => {
  selectedOrders.value = selection
}

/**
 * 清除选择
 */
const clearSelection = () => {
  selectedOrders.value = []
}

/**
 * 处理排序变化
 */
const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
  if (order) {
    searchForm.sort_by = prop
    searchForm.sort_order = order === 'ascending' ? 'asc' : 'desc'
  } else {
    searchForm.sort_by = 'created_at'
    searchForm.sort_order = 'desc'
  }
  loadOrderList()
}

/**
 * 处理页面大小变化
 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadOrderList()
}

/**
 * 处理当前页变化
 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadOrderList()
}

/**
 * 查看订单详情
 */
const viewOrderDetail = (orderId: number) => {
  currentOrderId.value = orderId
  showOrderDetailDialog.value = true
}

/**
 * 显示更新状态对话框
 */
const showUpdateStatusDialogHandler = (order: AdminOrderListItem) => {
  currentOrder.value = order
  showUpdateStatusDialog.value = true
}

/**
 * 处理更新成功
 */
const handleUpdateSuccess = () => {
  showUpdateStatusDialog.value = false
  loadOrderList()
  ElMessage.success('订单状态更新成功')
}

/**
 * 处理批量更新成功
 */
const handleBatchUpdateSuccess = () => {
  showBatchStatusDialog.value = false
  clearSelection()
  loadOrderList()
  ElMessage.success('批量更新订单状态成功')
}

/**
 * 导出订单
 */
const exportOrders = async () => {
  try {
    exporting.value = true
    
    const params: AdminOrderListRequest = {
      ...searchForm,
      page: 1,
      page_size: 10000 // 导出时获取更多数据
    }

    const blob = await OrderApi.exportOrders(params)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `orders_${new Date().getTime()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('订单导出成功')
  } catch (error) {
    console.error('Export orders error:', error)
    ElMessage.error('订单导出失败')
  } finally {
    exporting.value = false
  }
}

// ==================== 批量价格验证方法 ====================

/**
 * 打开批量价格验证对话框
 */
const openBatchPriceValidationDialog = () => {
  // 重置表单
  batchValidationForm.provider = ''
  batchValidationForm.start_time = ''
  batchValidationForm.end_time = ''
  batchValidationForm.max_count = 100
  batchValidationResults.value = null

  showBatchPriceValidationDialog.value = true
}

/**
 * 执行批量价格验证
 */
const performBatchPriceValidation = async () => {
  try {
    batchValidationLoading.value = true

    const params: any = {
      max_count: batchValidationForm.max_count
    }

    if (batchValidationForm.provider) {
      params.provider = batchValidationForm.provider
    }

    if (batchValidationForm.start_time) {
      params.start_time = batchValidationForm.start_time
    }

    if (batchValidationForm.end_time) {
      params.end_time = batchValidationForm.end_time
    }

    console.log('批量价格验证参数:', params)

    const response = await OrderApi.batchValidatePrices(params)

    if (response.success) {
      batchValidationResults.value = response.data

      // 🔥 企业级修复：将批量验证结果应用到当前订单列表
      if (response.data?.results) {
        updateOrderListWithValidationResults(response.data.results)
      }

      ElMessage.success(`批量价格验证完成！处理了 ${response.data?.processed_orders || 0} 个订单`)

      // 刷新订单列表以显示最新的价格验证结果
      await loadOrderList()
    } else {
      ElMessage.error(response.message || '批量价格验证失败')
    }
  } catch (error) {
    console.error('批量价格验证失败:', error)
    ElMessage.error('批量价格验证失败')
  } finally {
    batchValidationLoading.value = false
  }
}

/**
 * 将批量验证结果应用到当前订单列表
 */
const updateOrderListWithValidationResults = (results: any[]) => {
  console.log('应用批量验证结果到订单列表:', results)

  // 创建结果映射表，以order_id为键
  const resultMap = new Map()
  results.forEach(result => {
    resultMap.set(result.order_id, {
      provider_price: result.provider_price,
      system_price: result.system_price,
      profit_status: result.profit_status,
      query_status: result.query_status,
      query_time: result.query_time,
      error_message: result.error_message,
      supported: result.supported
    })
  })

  // 更新当前订单列表中的价格验证信息
  orderList.value.forEach(order => {
    const validationResult = resultMap.get(order.id)
    if (validationResult) {
      // 确保price_validation对象存在
      if (!order.price_validation) {
        order.price_validation = {} as any
      }

      // 更新验证结果
      Object.assign(order.price_validation || {}, validationResult || {})

      console.log(`订单 ${order.id} 价格验证结果已更新:`, order.price_validation)
    }
  })
}

/**
 * 关闭批量价格验证对话框
 */
const closeBatchPriceValidationDialog = () => {
  showBatchPriceValidationDialog.value = false
  batchValidationResults.value = null
}

// ==================== 批量状态同步方法 ====================

/**
 * 打开批量状态同步对话框
 */
const openBatchStatusSyncDialog = () => {
  // 重置表单
  batchStatusSyncForm.provider = ''
  batchStatusSyncForm.dateRange = null
  batchStatusSyncForm.start_time = ''
  batchStatusSyncForm.end_time = ''
  batchStatusSyncForm.max_count = 100
  batchStatusSyncForm.dry_run = false
  batchStatusSyncForm.only_failed = false
  batchStatusSyncForm.force_sync = false
  batchStatusSyncResults.value = null

  showBatchStatusSyncDialog.value = true
}

/**
 * 处理状态同步日期范围变化
 */
const handleStatusSyncDateRangeChange = (value: [string, string] | null) => {
  if (value) {
    batchStatusSyncForm.start_time = value[0]
    batchStatusSyncForm.end_time = value[1]
  } else {
    batchStatusSyncForm.start_time = ''
    batchStatusSyncForm.end_time = ''
  }
}

/**
 * 执行批量状态同步
 */
const performBatchStatusSync = async () => {
  try {
    batchStatusSyncLoading.value = true

    const params: any = {
      max_count: batchStatusSyncForm.max_count,
      dry_run: batchStatusSyncForm.dry_run,
      only_failed: batchStatusSyncForm.only_failed,
      force_sync: batchStatusSyncForm.force_sync
    }

    if (batchStatusSyncForm.provider) {
      params.provider = batchStatusSyncForm.provider
    }

    if (batchStatusSyncForm.start_time) {
      params.start_time = batchStatusSyncForm.start_time
    }

    if (batchStatusSyncForm.end_time) {
      params.end_time = batchStatusSyncForm.end_time
    }

    console.log('批量状态同步参数:', params)

    const response = await OrderApi.batchSyncOrderStatus(params)

    if (response.success) {
      batchStatusSyncResults.value = response.data

      const message = batchStatusSyncForm.dry_run
        ? `试运行完成！检查了 ${response.data?.processed_orders || 0} 个订单`
        : `批量状态同步完成！处理了 ${response.data?.processed_orders || 0} 个订单，${response.data?.changed_orders || 0} 个订单状态发生变化`

      ElMessage.success(message)

      // 如果不是试运行，刷新订单列表
      if (!batchStatusSyncForm.dry_run) {
        await loadOrderList()
      }

      // 显示详细结果
      if (response.data?.results && response.data.results.length > 0) {
        showBatchStatusSyncResults(response.data)
      }
    } else {
      ElMessage.error(response.message || '批量状态同步失败')
    }
  } catch (error) {
    console.error('批量状态同步失败:', error)
    ElMessage.error('批量状态同步失败')
  } finally {
    batchStatusSyncLoading.value = false
  }
}

/**
 * 显示批量状态同步结果
 */
const showBatchStatusSyncResults = (data: any) => {
  const summary = data.summary || {}
  const results = data.results || []

  let resultText = `同步结果汇总：
总订单数：${data.total_orders}
处理订单数：${data.processed_orders}
成功订单数：${data.success_orders}
失败订单数：${data.failed_orders}
状态变更订单数：${data.changed_orders}
处理耗时：${data.duration}

供应商统计：`

  Object.entries(summary.provider_stats || {}).forEach(([provider, count]) => {
    resultText += `\n${provider}: ${count}个`
  })

  if (summary.status_change_stats && Object.keys(summary.status_change_stats).length > 0) {
    resultText += '\n\n状态变更统计：'
    Object.entries(summary.status_change_stats).forEach(([oldStatus, changes]) => {
      Object.entries(changes as Record<string, number>).forEach(([newStatus, count]) => {
        resultText += `\n${oldStatus} → ${newStatus}: ${count}个`
      })
    })
  }

  ElMessageBox.alert(resultText, '同步结果', { type: 'success' })
}

/**
 * 关闭批量状态同步对话框
 */
const closeBatchStatusSyncDialog = () => {
  showBatchStatusSyncDialog.value = false
  batchStatusSyncResults.value = null
}

// ==================== 工具方法 ====================

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    // === 下单阶段 ===
    submitted: 'primary',
    submit_failed: 'danger',
    print_failed: 'danger',

    // === 分配阶段 ===
    assigned: 'info',
    awaiting_pickup: 'warning',

    // === 揽收阶段 ===
    picked_up: 'primary',
    pickup_failed: 'danger',

    // === 运输阶段 ===
    in_transit: 'info',
    out_for_delivery: 'info',

    // === 签收阶段 ===
    delivered: 'success',
    delivered_abnormal: 'warning',

    // === 计费阶段 ===
    billed: 'success',

    // === 异常状态 ===
    exception: 'danger',
    returned: 'warning',
    forwarded: 'info',

    // === 取消状态 ===
    cancelling: 'warning',
    cancelled: 'danger',
    voided: 'danger',

    // === 特殊状态 ===
    weight_updated: 'info',
    revived: 'primary'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取状态标签文本
 */
const getStatusLabel = (status: string) => {
  const option = ORDER_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || status
}

/**
 * 获取计费状态标签类型
 */
const getBillingStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    calculated: 'primary',
    paid: 'success',
    refunded: 'info'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取计费状态标签文本
 */
const getBillingStatusLabel = (status: string) => {
  const option = BILLING_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || status
}

/**
 * 格式化货币
 */
const formatCurrency = (amount: number) => {
  return `¥${amount?.toFixed(2) || '0.00'}`
}

/**
 * 格式化日期时间 - 明确指定北京时区
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  try {
    return new Date(dateTime).toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai', // 明确指定北京时区
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return dateTime
  }
}

/**
 * 获取盈亏状态标签类型
 */
const getProfitStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    profit: 'success',      // 盈利 - 绿色
    loss: 'danger',         // 亏损 - 红色
    break_even: 'warning',  // 持平 - 黄色
    unknown: 'info'         // 未知 - 灰色
  }
  return typeMap[status] || 'info'
}

/**
 * 获取盈亏状态标签文本
 */
const getProfitStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    profit: '盈利',
    loss: '亏损',
    break_even: '持平',
    unknown: '未知'
  }
  return labelMap[status] || status
}

/**
 * 格式化查询时间 - 明确指定北京时区
 */
const formatQueryTime = (timeStr: string) => {
  try {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai', // 明确指定北京时区
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return timeStr
  }
}

// ==================== 订单状态同步 ====================
/**
 * 同步订单状态
 */
const syncOrderStatus = async (row: any) => {
  try {
    // 设置同步状态
    row.syncing = true

    const response = await OrderApi.syncOrderStatus(row.id)

    if (response.success) {
      ElMessage.success('订单状态同步成功')
      // 刷新订单列表
      await loadOrderList()
    } else {
      ElMessage.error(response.message || '订单状态同步失败')
    }
  } catch (error) {
    console.error('同步订单状态失败:', error)
    ElMessage.error('同步订单状态失败')
  } finally {
    // 清除同步状态
    row.syncing = false
  }
}

// ==================== 生命周期 ====================
onMounted(() => {
  loadOrderList()
  
  // 监听页面可见性变化，当页面重新可见时刷新数据
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      // 页面重新可见时，强制刷新以显示最新数据
      refreshData()
    }
  })
})

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('visibilitychange', () => {})
})
</script>

<style scoped>
.order-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  color: white;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .iconfont-sys {
  font-size: 32px;
  opacity: 0.9;
}

.page-description {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.search-filters {
  margin-bottom: 20px;
}

.search-filters :deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: none;
}

.search-filters :deep(.el-card__body) {
  padding: 24px;
}

.search-filters :deep(.el-form-item) {
  margin-bottom: 18px;
}

.search-filters :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.search-filters :deep(.el-input),
.search-filters :deep(.el-select),
.search-filters :deep(.el-date-picker) {
  width: 100% !important;
}

.search-filters :deep(.el-button-group) {
  display: flex;
  gap: 8px;
}

.search-filters :deep(.el-button-group .el-button) {
  margin-left: 0 !important;
}

.batch-operations {
  margin-bottom: 20px;
}

.batch-operations :deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8f4fd;
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-info {
  font-weight: 600;
  color: #409EFF;
  font-size: 16px;
}

.order-list {
  margin-bottom: 20px;
}

.order-list :deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: none;
}

.order-list :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

.order-list :deep(.el-table th) {
  background: #f8fafc;
  color: #475569;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
}

.order-list :deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
}

.order-list :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #fafbfc;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.user-email {
  font-size: 12px;
  color: #909399;
}

.price {
  font-weight: 600;
  color: #E6A23C;
  font-size: 14px;
}

.billing-status {
  margin-top: 4px;
}

/* 价格验证样式 */
.price-validation {
  font-size: 12px;
}

.validation-not-started {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
}

.validation-loading {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
}

.validation-loading .el-icon {
  font-size: 14px;
}

.validation-failed {
  color: #F56C6C;
}

.validation-failed .error-msg {
  margin-top: 2px;
  font-size: 10px;
  color: #909399;
  word-break: break-all;
  max-width: 140px;
}

.validation-unsupported {
  color: #909399;
}

.validation-unsupported .provider-info {
  margin-top: 2px;
  font-size: 10px;
  color: #C0C4CC;
}

.validation-unknown {
  color: #E6A23C;
}

.validation-unknown .status-info {
  margin-top: 2px;
  font-size: 10px;
  color: #909399;
}

.validation-result {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.price-comparison {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.system-price {
  color: #606266;
  font-size: 11px;
}

.provider-price {
  color: #303133;
  font-size: 11px;
  font-weight: 500;
}

.profit-status {
  margin-top: 2px;
}

.query-time {
  margin-top: 2px;
  font-size: 10px;
  color: #C0C4CC;
}

/* 批量验证结果样式 */
.validation-summary {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-amount {
  margin-top: 4px;
  font-size: 14px;
  font-weight: 600;
}

.summary-amount.profit {
  color: #67C23A;
}

.summary-amount.loss {
  color: #F56C6C;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-management {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .page-title {
    font-size: 24px;
  }
}
</style>
