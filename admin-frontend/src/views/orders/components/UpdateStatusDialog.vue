<template>
  <el-dialog
    v-model="dialogVisible"
    title="更新订单状态"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="订单号">
        <el-input :value="order?.order_no" disabled />
      </el-form-item>
      
      <el-form-item label="当前状态">
        <el-tag :type="getStatusType(order?.status)">
          {{ getStatusText(order?.status) }}
        </el-tag>
      </el-form-item>
      
      <el-form-item label="新状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择新状态">
          <el-option label="待支付" value="pending" />
          <el-option label="已支付" value="paid" />
          <el-option label="处理中" value="processing" />
          <el-option label="已发货" value="shipped" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
          <el-option label="已退款" value="refunded" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入状态更新备注"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  visible: boolean
  order?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  order: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

const form = reactive({
  status: '',
  remark: ''
})

const rules: FormRules = {
  status: [
    { required: true, message: '请选择新状态', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听订单变化
watch(() => props.order, (newOrder) => {
  if (newOrder) {
    form.status = newOrder.status || ''
    form.remark = ''
  }
}, { immediate: true })

// 方法
const getStatusType = (status?: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    paid: 'success',
    processing: 'primary',
    shipped: 'info',
    completed: 'success',
    cancelled: 'danger',
    refunded: 'warning'
  }
  return typeMap[status || ''] || 'info'
}

const getStatusText = (status?: string) => {
  const textMap: Record<string, string> = {
    pending: '待支付',
    paid: '已支付',
    processing: '处理中',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消',
    refunded: '已退款'
  }
  return textMap[status || ''] || '未知'
}

const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 这里应该调用API更新订单状态
    // await OrderApi.updateStatus(props.order?.id, form)
    
    ElMessage.success('订单状态更新成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('更新订单状态失败:', error)
    ElMessage.error('更新订单状态失败')
  } finally {
    loading.value = false
  }
}
</script>
