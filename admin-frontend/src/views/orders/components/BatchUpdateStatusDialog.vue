<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量更新订单状态"
    width="600px"
    @close="handleClose"
  >
    <div class="batch-update-content">
      <el-alert
        :title="`已选择 ${orderIds.length} 个订单`"
        type="info"
        :closable="false"
        style="margin-bottom: 20px"
      />
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="新状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择新状态">
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="处理中" value="processing" />
            <el-option label="已发货" value="shipped" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="已退款" value="refunded" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入批量更新备注"
          />
        </el-form-item>
      </el-form>
      
      <el-divider />
      
      <div class="order-list">
        <h4>订单列表</h4>
        <el-table
          :data="orderIds.slice(0, 10)"
          size="small"
          max-height="200"
        >
          <el-table-column prop="id" label="订单ID" width="120" />
          <el-table-column label="订单号" width="200">
            <template #default="{ row }">
              {{ `ORDER_${row}` }}
            </template>
          </el-table-column>
          <el-table-column label="状态">
            <template #default>
              <el-tag type="info">待更新</el-tag>
            </template>
          </el-table-column>
        </el-table>
        
        <div v-if="orderIds.length > 10" class="more-orders">
          还有 {{ orderIds.length - 10 }} 个订单...
        </div>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          批量更新
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  visible: boolean
  orderIds: (string | number)[]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderIds: () => []
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

const form = reactive({
  status: '',
  remark: ''
})

const rules: FormRules = {
  status: [
    { required: true, message: '请选择新状态', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    await ElMessageBox.confirm(
      `确定要批量更新 ${props.orderIds.length} 个订单的状态吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    
    // 这里应该调用API批量更新订单状态
    // await OrderApi.batchUpdateStatus({
    //   order_ids: props.orderIds,
    //   status: form.status,
    //   remark: form.remark
    // })
    
    ElMessage.success(`成功更新 ${props.orderIds.length} 个订单状态`)
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量更新订单状态失败:', error)
      ElMessage.error('批量更新订单状态失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.batch-update-content {
  max-height: 500px;
  overflow-y: auto;
}

.order-list {
  margin-top: 20px;
}

.order-list h4 {
  margin: 0 0 10px 0;
  color: #606266;
}

.more-orders {
  text-align: center;
  color: #909399;
  font-size: 12px;
  margin-top: 10px;
}
</style>
