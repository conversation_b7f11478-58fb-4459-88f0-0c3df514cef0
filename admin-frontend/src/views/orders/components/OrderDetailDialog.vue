<template>
  <el-dialog
    v-model="dialogVisible"
    title="订单详情"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="order-detail-dialog">
      <div v-if="orderDetail" class="order-detail-content">
        <!-- 基础信息 -->
        <div class="detail-section">
          <h3>基础信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="订单ID">
                  {{ orderDetail.id }}
                </el-descriptions-item>
                <el-descriptions-item label="订单号">
                  {{ orderDetail.order_no }}
                </el-descriptions-item>
                <el-descriptions-item label="客户订单号">
                  {{ orderDetail.customer_order_no }}
                </el-descriptions-item>
                <el-descriptions-item label="运单号">
                  {{ orderDetail.tracking_no || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="订单状态">
                  <el-tag :type="getStatusTagType(orderDetail.status)">
                    {{ getStatusLabel(orderDetail.status) }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
            <el-col :span="12">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="快递公司">
                  {{ orderDetail.express_company?.name || orderDetail.express_type }}
                </el-descriptions-item>
                <el-descriptions-item label="产品类型">
                  {{ orderDetail.product_type }}
                </el-descriptions-item>
                <el-descriptions-item label="供应商">
                  {{ orderDetail.provider }}
                </el-descriptions-item>
                <el-descriptions-item label="创建时间">
                  {{ formatDateTime(orderDetail.created_at) }}
                </el-descriptions-item>
                <el-descriptions-item label="更新时间">
                  {{ formatDateTime(orderDetail.updated_at) }}
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
        </div>

        <!-- 用户信息 -->
        <div class="detail-section">
          <h3>用户信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户ID">
              {{ orderDetail.user.id }}
            </el-descriptions-item>
            <el-descriptions-item label="用户名">
              {{ orderDetail.user.username }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ orderDetail.user.email }}
            </el-descriptions-item>
            <el-descriptions-item label="账户状态">
              <el-tag :type="orderDetail.user.is_active ? 'success' : 'danger'">
                {{ orderDetail.user.is_active ? '活跃' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatDateTime(orderDetail.user.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="默认角色">
              {{ orderDetail.user.default_role }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 费用信息 -->
        <div class="detail-section">
          <h3>费用信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="订单价格">
                  <span class="price-amount">¥{{ orderDetail.price?.toFixed(2) || '0.00' }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="实际费用">
                  <span class="price-amount">¥{{ orderDetail.actual_fee?.toFixed(2) || '0.00' }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="保价费">
                  <span class="price-amount">¥{{ orderDetail.insurance_fee?.toFixed(2) || '0.00' }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="超重费用" v-if="orderDetail.overweight_fee > 0">
                  <span class="fee-amount overweight">¥{{ orderDetail.overweight_fee?.toFixed(2) || '0.00' }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="超轻费用" v-if="orderDetail.underweight_fee > 0">
                  <span class="fee-amount underweight">¥{{ orderDetail.underweight_fee?.toFixed(2) || '0.00' }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="费用差额" v-if="shouldShowFeeDifference(orderDetail)">
                  <span :class="['fee-difference', getFeeDifference(orderDetail) > 0 ? 'positive' : 'negative']">
                    {{ getFeeDifference(orderDetail) > 0 ? '+' : '' }}¥{{ Math.abs(getFeeDifference(orderDetail)).toFixed(2) }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="计费状态">
                  <el-tag :type="getBillingStatusTagType(orderDetail.billing_status)">
                    {{ getBillingStatusLabel(orderDetail.billing_status) }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
            <el-col :span="12">
              <el-descriptions :column="1" border>
                <el-descriptions-item label="下单重量">
                  {{ orderDetail.weight }} kg
                </el-descriptions-item>
                <el-descriptions-item label="实际重量">
                  {{ orderDetail.actual_weight || 0 }} kg
                </el-descriptions-item>
                <el-descriptions-item label="计费重量">
                  {{ orderDetail.charged_weight || 0 }} kg
                </el-descriptions-item>
                <el-descriptions-item label="下单体积">
                  {{ orderDetail.order_volume || 0 }} m³
                </el-descriptions-item>
                <el-descriptions-item label="实际体积">
                  {{ orderDetail.actual_volume || 0 }} m³
                </el-descriptions-item>
                <el-descriptions-item label="重量调整原因" v-if="orderDetail.weight_adjustment_reason">
                  <el-tag type="info" size="small">{{ orderDetail.weight_adjustment_reason }}</el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
        </div>

        <!-- 地址信息 -->
        <div class="detail-section">
          <h3>地址信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <h4>寄件人信息</h4>
              <div class="address-info">
                <pre>{{ formatAddressInfo(orderDetail.sender_info) }}</pre>
              </div>
            </el-col>
            <el-col :span="12">
              <h4>收件人信息</h4>
              <div class="address-info">
                <pre>{{ formatAddressInfo(orderDetail.receiver_info) }}</pre>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <h4>包裹信息</h4>
              <div class="address-info">
                <pre>{{ formatAddressInfo(orderDetail.package_info) }}</pre>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 操作历史 -->
        <div class="detail-section">
          <h3>操作历史</h3>
          <el-table
            :data="orderDetail.operation_history"
            border
            stripe
            style="width: 100%"
            empty-text="暂无操作记录"
          >
            <el-table-column prop="created_at" label="操作时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column prop="operator" label="操作人" width="120" />
            <el-table-column prop="operation" label="操作类型" width="120" />
            <el-table-column prop="old_status" label="原状态" width="100">
              <template #default="{ row }">
                <el-tag v-if="row.old_status" size="small" :type="getStatusTagType(row.old_status)">
                  {{ getStatusLabel(row.old_status) }}
                </el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="new_status" label="新状态" width="100">
              <template #default="{ row }">
                <el-tag v-if="row.new_status" size="small" :type="getStatusTagType(row.new_status)">
                  {{ getStatusLabel(row.new_status) }}
                </el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="操作原因" show-overflow-tooltip />
            <el-table-column prop="ip_address" label="IP地址" width="120" />
          </el-table>
        </div>

        <!-- 物流轨迹 -->
        <div class="detail-section">
          <h3>物流轨迹</h3>
          <el-timeline>
            <el-timeline-item
              v-for="event in orderDetail.tracking_history"
              :key="event.id"
              :timestamp="formatDateTime(event.timestamp)"
              placement="top"
            >
              <el-card>
                <div class="tracking-event">
                  <div class="event-header">
                    <el-tag :type="getStatusTagType(event.status)" size="small">
                      {{ event.status }}
                    </el-tag>
                    <span class="event-source">{{ event.source }}</span>
                  </div>
                  <div class="event-content">
                    <p class="event-description">{{ event.description }}</p>
                    <p v-if="event.location" class="event-location">
                      <el-icon><Location /></el-icon>
                      {{ event.location }}
                    </p>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
          
          <div v-if="!orderDetail.tracking_history || orderDetail.tracking_history.length === 0" class="empty-tracking">
            <el-empty description="暂无物流轨迹信息" />
          </div>
        </div>

        <!-- 审计信息 -->
        <div class="detail-section">
          <h3>审计信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="创建人">
              {{ orderDetail.audit_info?.created_by || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="最后修改人">
              {{ orderDetail.audit_info?.updated_by || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="最后修改时间">
              {{ orderDetail.audit_info?.last_modified ? formatDateTime(orderDetail.audit_info.last_modified) : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="修改次数">
              {{ orderDetail.audit_info?.modification_count || 0 }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="refreshDetail" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Location, Refresh } from '@element-plus/icons-vue'
import { OrderApi, ORDER_STATUS_OPTIONS, BILLING_STATUS_OPTIONS, type AdminOrderDetail } from '@/api/orderApi'

// ==================== Props & Emits ====================
interface Props {
  visible: boolean
  orderId: number | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const loading = ref(false)
const orderDetail = ref<AdminOrderDetail | null>(null)

// ==================== 计算属性 ====================
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// ==================== 方法 ====================

/**
 * 加载订单详情
 */
const loadOrderDetail = async () => {
  if (!props.orderId) return
  
  try {
    loading.value = true
    orderDetail.value = await OrderApi.getAdminOrderDetail(props.orderId)
  } catch (error) {
    console.error('Load order detail error:', error)
    ElMessage.error('获取订单详情失败')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新详情
 */
const refreshDetail = () => {
  loadOrderDetail()
}

/**
 * 处理关闭
 */
const handleClose = () => {
  orderDetail.value = null
  dialogVisible.value = false
}

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    // === 下单阶段 ===
    submitted: 'primary',
    submit_failed: 'danger',
    print_failed: 'danger',

    // === 分配阶段 ===
    assigned: 'info',
    awaiting_pickup: 'warning',

    // === 揽收阶段 ===
    picked_up: 'primary',
    pickup_failed: 'danger',

    // === 运输阶段 ===
    in_transit: 'info',
    out_for_delivery: 'info',

    // === 签收阶段 ===
    delivered: 'success',
    delivered_abnormal: 'warning',

    // === 计费阶段 ===
    billed: 'success',

    // === 异常状态 ===
    exception: 'danger',
    returned: 'warning',
    forwarded: 'info',

    // === 取消状态 ===
    cancelled: 'danger',
    voided: 'danger',

    // === 特殊状态 ===
    weight_updated: 'info',
    revived: 'primary'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取状态标签文本
 */
const getStatusLabel = (status: string) => {
  const option = ORDER_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || status
}

/**
 * 获取计费状态标签类型
 */
const getBillingStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    calculated: 'primary',
    paid: 'success',
    refunded: 'info'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取计费状态标签文本
 */
const getBillingStatusLabel = (status: string) => {
  const option = BILLING_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || status
}

/**
 * 获取支付状态标签类型
 */
const getPaymentStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    paid: 'success',
    failed: 'danger',
    refunded: 'info'
  }
  return typeMap[status] || 'info'
}

/**
 * 格式化地址信息
 */
const formatAddressInfo = (info: string) => {
  if (!info) return '暂无信息'
  
  try {
    const parsed = JSON.parse(info)
    return JSON.stringify(parsed, null, 2)
  } catch {
    return info
  }
}

/**
 * 格式化日期时间 - 明确指定北京时区
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai', // 明确指定北京时区
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 计算费用差额
 */
const getFeeDifference = (order: AdminOrderDetail) => {
  if (!order) return 0
  const actualFee = order.actual_fee || 0
  const price = order.price || 0
  return actualFee - price
}

/**
 * 判断是否应该显示费用差额
 */
const shouldShowFeeDifference = (order: AdminOrderDetail) => {
  if (!order) return false
  // 只有当实际费用大于0且与预收费用不同时才显示差额
  const actualFee = order.actual_fee || 0
  const price = order.price || 0
  return actualFee > 0 && actualFee !== price
}

// ==================== 监听器 ====================
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.orderId) {
      loadOrderDetail()
    }
  }
)
</script>

<style scoped>
.order-detail-dialog {
  max-height: 80vh;
  overflow-y: auto;
}

.order-detail-content {
  padding: 10px 0;
}

.detail-section {
  margin-bottom: 32px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.price-amount {
  font-weight: 600;
  color: #E6A23C;
}

.address-info {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.address-info pre {
  margin: 0;
  font-family: inherit;
  font-size: 13px;
  line-height: 1.5;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-all;
}

.tracking-event {
  padding: 8px 0;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.event-source {
  font-size: 12px;
  color: #909399;
}

.event-content {
  font-size: 14px;
}

.event-description {
  margin: 0 0 4px 0;
  color: #303133;
}

.event-location {
  margin: 0;
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.empty-tracking {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #909399;
}

/* 费用相关样式 */
.fee-amount {
  font-weight: 600;
}

.fee-amount.overweight {
  color: #f56c6c;
}

.fee-amount.underweight {
  color: #67c23a;
}

.fee-difference {
  font-weight: 600;
  font-size: 14px;
}

.fee-difference.positive {
  color: #f56c6c;
}

.fee-difference.negative {
  color: #67c23a;
}
</style>
