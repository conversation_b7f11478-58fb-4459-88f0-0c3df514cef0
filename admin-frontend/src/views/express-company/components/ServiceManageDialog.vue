<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${company?.name} - 服务管理`"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" :icon="Plus" @click="handleCreate">
        新增服务
      </el-button>
      <el-button :icon="Refresh" @click="loadServices">
        刷新
      </el-button>
    </div>

    <!-- 服务列表 -->
    <el-table
      v-loading="loading"
      :data="serviceList"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column prop="service_code" label="服务代码" width="120" />
      <el-table-column prop="service_name" label="服务名称" width="150" />
      <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
      <el-table-column prop="estimated_days" label="预计天数" width="100">
        <template #default="{ row }">
          {{ row.estimated_days || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80">
        <template #default="{ row }">
          <el-switch
            v-model="row.is_active"
            @change="handleToggleStatus(row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="sort_order" label="排序" width="80" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 服务编辑对话框 -->
    <el-dialog
      v-model="showServiceEditDialog"
      :title="isEditService ? '编辑服务' : '新增服务'"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form
        ref="serviceFormRef"
        :model="serviceFormData"
        :rules="serviceFormRules"
        label-width="120px"
      >
        <el-form-item label="服务代码" prop="service_code">
          <el-input
            v-model="serviceFormData.service_code"
            placeholder="请输入服务代码"
            :disabled="isEditService"
            maxlength="50"
          />
          <div class="form-tip">服务的唯一标识，创建后不可修改</div>
        </el-form-item>

        <el-form-item label="服务名称" prop="service_name">
          <el-input
            v-model="serviceFormData.service_name"
            placeholder="请输入服务名称"
            maxlength="100"
          />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="serviceFormData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入服务描述（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="预计天数" prop="estimated_days">
          <el-input-number
            v-model="serviceFormData.estimated_days"
            :min="1"
            :max="30"
            placeholder="预计配送天数"
            style="width: 200px"
          />
          <div class="form-tip">预计配送天数，用于时效预估</div>
        </el-form-item>

        <el-form-item label="状态" prop="is_active">
          <el-switch
            v-model="serviceFormData.is_active"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort_order">
          <el-input-number
            v-model="serviceFormData.sort_order"
            :min="0"
            :max="9999"
            placeholder="排序值"
            style="width: 200px"
          />
          <div class="form-tip">数值越小排序越靠前</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showServiceEditDialog = false">取消</el-button>
          <el-button type="primary" :loading="submittingService" @click="handleSubmitService">
            {{ isEditService ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import {
  ExpressCompanyApi,
  type ExpressCompany,
  type ExpressCompanyService,
  type CreateExpressServiceRequest,
  type UpdateExpressServiceRequest
} from '@/api/expressCompanyApi'

// ==================== Props & Emits ====================

interface Props {
  visible: boolean
  company?: ExpressCompany | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  company: null
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const loading = ref(false)
const serviceList = ref<ExpressCompanyService[]>([])

// 服务编辑对话框
const showServiceEditDialog = ref(false)
const serviceFormRef = ref<FormInstance>()
const submittingService = ref(false)
const currentService = ref<ExpressCompanyService | null>(null)

// 服务表单数据
const serviceFormData = reactive<CreateExpressServiceRequest>({
  company_id: '',
  service_code: '',
  service_name: '',
  description: '',
  estimated_days: undefined,
  is_active: true,
  sort_order: 0
})

// 服务表单验证规则
const serviceFormRules: FormRules = {
  service_code: [
    { required: true, message: '请输入服务代码', trigger: 'blur' },
    { max: 50, message: '代码长度不能超过50个字符', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '代码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  service_name: [
    { required: true, message: '请输入服务名称', trigger: 'blur' },
    { max: 100, message: '名称长度不能超过100个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' }
  ],
  estimated_days: [
    { type: 'number', min: 1, max: 30, message: '预计天数应在1-30天之间', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', min: 0, max: 9999, message: '排序值应在0-9999之间', trigger: 'blur' }
  ]
}

// ==================== 计算属性 ====================

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEditService = computed(() => !!currentService.value?.id)

// ==================== 监听器 ====================

watch(
  () => props.visible,
  (visible) => {
    if (visible && props.company) {
      loadServices()
    }
  }
)

// ==================== 方法 ====================

/**
 * 加载服务列表
 */
const loadServices = async () => {
  if (!props.company?.id) return

  try {
    loading.value = true
    const response = await ExpressCompanyApi.getServicesByCompany(props.company.id)

    if (response.success) {
      serviceList.value = response.data
    } else {
      ElMessage.error(response.message || '获取服务列表失败')
    }
  } catch (error) {
    console.error('Load services error:', error)
    ElMessage.error('获取服务列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

/**
 * 创建服务
 */
const handleCreate = () => {
  currentService.value = null
  resetServiceForm()
  showServiceEditDialog.value = true
}

/**
 * 编辑服务
 */
const handleEdit = (service: ExpressCompanyService) => {
  currentService.value = service
  Object.assign(serviceFormData, {
    company_id: service.company_id,
    service_code: service.service_code,
    service_name: service.service_name,
    description: service.description || '',
    estimated_days: service.estimated_days,
    is_active: service.is_active,
    sort_order: service.sort_order
  })
  showServiceEditDialog.value = true
}

/**
 * 切换状态
 */
const handleToggleStatus = async (service: ExpressCompanyService) => {
  try {
    const response = await ExpressCompanyApi.updateService(service.id, {
      is_active: service.is_active
    })

    if (response.success) {
      ElMessage.success(`${service.is_active ? '启用' : '禁用'}成功`)
      emit('success')
    } else {
      // 恢复状态
      service.is_active = !service.is_active
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    // 恢复状态
    service.is_active = !service.is_active
    console.error('Toggle status error:', error)
    ElMessage.error('操作失败')
  }
}

/**
 * 删除服务
 */
const handleDelete = async (service: ExpressCompanyService) => {
  try {
    await ElMessageBox.confirm(
      `确认删除服务 "${service.service_name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    const response = await ExpressCompanyApi.deleteService(service.id)
    if (response.success) {
      ElMessage.success('删除成功')
      loadServices()
      emit('success')
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Delete service error:', error)
      ElMessage.error('删除失败')
    }
  }
}

/**
 * 重置服务表单
 */
const resetServiceForm = () => {
  Object.assign(serviceFormData, {
    company_id: props.company?.id || '',
    service_code: '',
    service_name: '',
    description: '',
    estimated_days: undefined,
    is_active: true,
    sort_order: 0
  })
  serviceFormRef.value?.clearValidate()
}

/**
 * 提交服务表单
 */
const handleSubmitService = async () => {
  if (!serviceFormRef.value) return

  try {
    const valid = await serviceFormRef.value.validate()
    if (!valid) return

    submittingService.value = true

    if (isEditService.value) {
      // 编辑模式
      const updateData: UpdateExpressServiceRequest = {
        service_name: serviceFormData.service_name,
        description: serviceFormData.description || undefined,
        estimated_days: serviceFormData.estimated_days,
        is_active: serviceFormData.is_active,
        sort_order: serviceFormData.sort_order
      }

      const response = await ExpressCompanyApi.updateService(currentService.value!.id, updateData)
      if (response.success) {
        ElMessage.success('服务更新成功')
        showServiceEditDialog.value = false
        loadServices()
        emit('success')
      } else {
        ElMessage.error(response.message || '更新失败')
      }
    } else {
      // 创建模式
      const createData: CreateExpressServiceRequest = {
        ...serviceFormData,
        description: serviceFormData.description || undefined
      }

      const response = await ExpressCompanyApi.createService(createData)
      if (response.success) {
        ElMessage.success('服务创建成功')
        showServiceEditDialog.value = false
        loadServices()
        emit('success')
      } else {
        ElMessage.error(response.message || '创建失败')
      }
    }
  } catch (error) {
    console.error('Submit service error:', error)
    ElMessage.error('操作失败')
  } finally {
    submittingService.value = false
  }
}
</script>

<style scoped>
.action-bar {
  margin-bottom: 16px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
