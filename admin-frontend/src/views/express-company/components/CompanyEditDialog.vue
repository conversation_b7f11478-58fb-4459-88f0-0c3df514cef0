<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑快递公司' : '新增快递公司'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="快递公司代码" prop="code">
        <el-input
          v-model="formData.code"
          placeholder="请输入快递公司代码（如：ZTO、YTO等）"
          :disabled="isEdit"
          maxlength="20"
          show-word-limit
        />
        <div class="form-tip">快递公司的唯一标识，创建后不可修改</div>
      </el-form-item>

      <el-form-item label="快递公司名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入快递公司名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>



      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入快递公司描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="状态" prop="is_active">
        <el-switch
          v-model="formData.is_active"
          active-text="启用"
          inactive-text="禁用"
        />
        <div class="form-tip">禁用后，该快递公司将不会在前端显示</div>
      </el-form-item>

      <el-form-item label="排序" prop="sort_order">
        <el-input-number
          v-model="formData.sort_order"
          :min="0"
          :max="9999"
          placeholder="排序值"
          style="width: 200px"
        />
        <div class="form-tip">数值越小排序越靠前</div>
      </el-form-item>

      <el-form-item label="抛比" prop="volume_weight_ratio">
        <el-input-number
          v-model="formData.volume_weight_ratio"
          :min="1000"
          :max="12000"
          :step="100"
          placeholder="体积重量系数"
          style="width: 200px"
        />
        <div class="form-tip">体积重量计算系数（cm³/kg），常用值：德邦6000，其他快递8000</div>
      </el-form-item>

      <el-form-item label="限重(KG)" prop="max_weight_kg">
        <el-input-number
          v-model="formData.max_weight_kg"
          :min="1"
          :max="500"
          :step="0.5"
          :precision="2"
          placeholder="最大承重"
          style="width: 200px"
        />
        <div class="form-tip">该快递公司支持的最大重量限制，常用值：德邦100KG，其他快递50KG</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  ExpressCompanyApi,
  type ExpressCompany,
  type CreateExpressCompanyRequest,
  type UpdateExpressCompanyRequest
} from '@/api/expressCompanyApi'

// ==================== Props & Emits ====================

interface Props {
  visible: boolean
  company?: ExpressCompany | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  company: null
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = reactive<CreateExpressCompanyRequest>({
  code: '',
  name: '',
  description: '',
  is_active: true,
  sort_order: 0,
  volume_weight_ratio: undefined,
  max_weight_kg: undefined
})

// 表单验证规则
const formRules: FormRules = {
  code: [
    { required: true, message: '请输入快递公司代码', trigger: 'blur' },
    { min: 2, max: 20, message: '代码长度应在2-20个字符之间', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '代码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入快递公司名称', trigger: 'blur' },
    { min: 2, max: 100, message: '名称长度应在2-100个字符之间', trigger: 'blur' }
  ],

  description: [
    { max: 500, message: '描述长度不能超过500个字符', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', min: 0, max: 9999, message: '排序值应在0-9999之间', trigger: 'blur' }
  ],
  volume_weight_ratio: [
    { required: true, message: '请输入抛比值', trigger: 'blur' },
    { type: 'number', min: 1000, max: 12000, message: '抛比值应在1000-12000之间', trigger: 'blur' }
  ],
  max_weight_kg: [
    { required: true, message: '请输入限重值', trigger: 'blur' },
    { type: 'number', min: 1, max: 500, message: '限重值应在1-500KG之间', trigger: 'blur' }
  ]
}

// ==================== 计算属性 ====================

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.company?.id)

// ==================== 监听器 ====================

watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      resetForm()
      if (props.company) {
        // 编辑模式，填充表单数据
        Object.assign(formData, {
          code: props.company.code,
          name: props.company.name,
          description: props.company.description || '',
          is_active: props.company.is_active,
          sort_order: props.company.sort_order,
          volume_weight_ratio: props.company.volume_weight_ratio,
          max_weight_kg: props.company.max_weight_kg
        })
      }
    }
  }
)

// ==================== 方法 ====================

/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(formData, {
    code: '',
    name: '',
    description: '',
    is_active: true,
    sort_order: 0,
    volume_weight_ratio: isEdit.value ? undefined : 8000,
    max_weight_kg: isEdit.value ? undefined : 50.00
  })
  formRef.value?.clearValidate()
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    if (isEdit.value) {
      // 编辑模式
      const updateData: UpdateExpressCompanyRequest = {
        name: formData.name,
        description: formData.description || undefined,
        is_active: formData.is_active,
        sort_order: formData.sort_order,
        volume_weight_ratio: formData.volume_weight_ratio,
        max_weight_kg: formData.max_weight_kg
      }

      const response = await ExpressCompanyApi.updateCompany(props.company!.id, updateData)
      if (response.success) {
        ElMessage.success('快递公司更新成功')
        emit('success')
        handleClose()
      } else {
        ElMessage.error(response.message || '更新失败')
      }
    } else {
      // 创建模式
      const createData: CreateExpressCompanyRequest = {
        ...formData,
        description: formData.description || undefined
      }

      const response = await ExpressCompanyApi.createCompany(createData)
      if (response.success) {
        ElMessage.success('快递公司创建成功')
        emit('success')
        handleClose()
      } else {
        ElMessage.error(response.message || '创建失败')
      }
    }
  } catch (error) {
    console.error('Submit error:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.logo-preview {
  margin-top: 8px;
}

.dialog-footer {
  text-align: right;
}
</style>
