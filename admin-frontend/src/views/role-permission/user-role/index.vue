<template>
  <div class="user-role-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">用户角色管理</h1>
        <p class="page-description">管理用户的角色分配和权限设置</p>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="选择用户">
          <el-select
            v-model="searchForm.selectedUserId"
            placeholder="请选择用户"
            clearable
            filterable
            style="width: 200px"
            @change="handleUserChange"
          >
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="`${user.username} (${user.email})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择角色">
          <el-select
            v-model="searchForm.selectedRoleId"
            placeholder="请选择角色"
            clearable
            style="width: 200px"
            @change="handleRoleChange"
          >
            <el-option
              v-for="role in roleList"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleRefresh">
            刷新数据
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户角色信息 -->
    <el-row :gutter="20" v-if="searchForm.selectedUserId">
      <!-- 用户信息 -->
      <el-col :span="12">
        <el-card class="user-info-card">
          <template #header>
            <div class="card-header">
              <span>用户信息</span>
            </div>
          </template>
          <div v-if="selectedUser" class="user-info">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="用户名">
                {{ selectedUser.username }}
              </el-descriptions-item>
              <el-descriptions-item label="邮箱">
                {{ selectedUser.email }}
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="selectedUser.is_active ? 'success' : 'danger'" size="small">
                  {{ selectedUser.is_active ? '活跃' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDateTime(selectedUser.created_at) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-col>

      <!-- 用户角色列表 -->
      <el-col :span="12">
        <el-card class="user-roles-card">
          <template #header>
            <div class="card-header">
              <span>用户角色</span>
              <el-button type="primary" size="small" @click="handleAssignRole">
                分配角色
              </el-button>
            </div>
          </template>
          <div class="user-roles">
            <el-table
              :data="userRoles"
              v-loading="userRolesLoading"
              stripe
              border
              max-height="300"
            >
              <el-table-column prop="name" label="角色名称" min-width="120" />
              <el-table-column prop="description" label="角色描述" min-width="150" show-overflow-tooltip />
              <el-table-column prop="is_system" label="类型" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.is_system ? 'warning' : 'success'" size="small">
                    {{ row.is_system ? '系统' : '自定义' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleRemoveRole(row)"
                  >
                    移除
                  </el-button>
                  <el-button
                    type="warning"
                    size="small"
                    @click="handleSetDefaultRole(row)"
                  >
                    设为默认
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 角色用户信息 -->
    <el-row :gutter="20" v-if="searchForm.selectedRoleId && !searchForm.selectedUserId">
      <el-col :span="24">
        <el-card class="role-users-card">
          <template #header>
            <div class="card-header">
              <span>角色用户列表</span>
            </div>
          </template>
          <div class="role-users">
            <el-table
              :data="roleUsers"
              v-loading="roleUsersLoading"
              stripe
              border
            >
              <el-table-column prop="username" label="用户名" min-width="120" />
              <el-table-column prop="email" label="邮箱" min-width="180" />
              <el-table-column prop="is_active" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
                    {{ row.is_active ? '活跃' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="{ row }">
                  {{ formatDateTime(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleViewUserRoles(row)"
                  >
                    查看角色
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 角色分配对话框 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="分配角色"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedUser" class="assign-role">
        <p>为用户 <strong>{{ selectedUser.username }}</strong> 分配角色：</p>
        <el-select
          v-model="selectedRoleForAssign"
          placeholder="请选择要分配的角色"
          style="width: 100%"
          multiple
        >
          <el-option
            v-for="role in availableRoles"
            :key="role.id"
            :label="role.name"
            :value="role.id"
          >
            <span>{{ role.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ role.description }}
            </span>
          </el-option>
        </el-select>
      </div>
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="assignLoading" @click="handleConfirmAssign">
          确认分配
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  RoleManagementService,
  UserRoleManagementService,
  type Role,
  type Permission
} from '@/api/rolePermissionApi'
import { UserManagementService, type User } from '@/api/userManagementApi'

// 响应式数据
const userRolesLoading = ref(false)
const roleUsersLoading = ref(false)
const assignLoading = ref(false)
const userList = ref<User[]>([])
const roleList = ref<Role[]>([])
const selectedUser = ref<User | null>(null)
const selectedRole = ref<Role | null>(null)
const userRoles = ref<Role[]>([])
const roleUsers = ref<User[]>([])

// 搜索表单
const searchForm = reactive({
  selectedUserId: '',
  selectedRoleId: ''
})

// 对话框状态
const assignDialogVisible = ref(false)
const selectedRoleForAssign = ref<string[]>([])

// 计算属性
const availableRoles = computed(() => {
  const assignedRoleIds = userRoles.value.map(role => role.id)
  return roleList.value.filter(role => !assignedRoleIds.includes(role.id))
})

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 加载用户列表
const loadUserList = async () => {
  try {
    const result = await UserManagementService.getUserList({
      page: 1,
      page_size: 1000 // 获取所有用户
    })
    userList.value = result.items
  } catch (error: any) {
    console.error('Load user list failed:', error)
    ElMessage.error(error.message || '加载用户列表失败')
  }
}

// 加载角色列表
const loadRoleList = async () => {
  try {
    const result = await RoleManagementService.getRoleList({
      page: 1,
      page_size: 1000 // 获取所有角色
    })
    roleList.value = result.items
  } catch (error: any) {
    console.error('Load role list failed:', error)
    ElMessage.error(error.message || '加载角色列表失败')
  }
}

// 加载用户角色
const loadUserRoles = async (userId: string) => {
  try {
    userRolesLoading.value = true
    userRoles.value = await UserRoleManagementService.getUserRoles(userId)
  } catch (error: any) {
    console.error('Load user roles failed:', error)
    ElMessage.error(error.message || '加载用户角色失败')
  } finally {
    userRolesLoading.value = false
  }
}

// 加载角色用户
const loadRoleUsers = async (roleId: string) => {
  try {
    roleUsersLoading.value = true
    roleUsers.value = await UserRoleManagementService.getUsersByRole(roleId)
  } catch (error: any) {
    console.error('Load role users failed:', error)
    ElMessage.error(error.message || '加载角色用户失败')
  } finally {
    roleUsersLoading.value = false
  }
}

// 用户选择处理
const handleUserChange = async (userId: string) => {
  if (!userId) {
    selectedUser.value = null
    userRoles.value = []
    return
  }

  selectedUser.value = userList.value.find(user => user.id === userId) || null
  if (selectedUser.value) {
    await loadUserRoles(userId)
  }

  // 清空角色选择
  searchForm.selectedRoleId = ''
  roleUsers.value = []
}

// 角色选择处理
const handleRoleChange = async (roleId: string) => {
  if (!roleId) {
    selectedRole.value = null
    roleUsers.value = []
    return
  }

  selectedRole.value = roleList.value.find(role => role.id === roleId) || null
  if (selectedRole.value) {
    await loadRoleUsers(roleId)
  }

  // 清空用户选择
  searchForm.selectedUserId = ''
  selectedUser.value = null
  userRoles.value = []
}

// 刷新数据
const handleRefresh = async () => {
  await Promise.all([
    loadUserList(),
    loadRoleList()
  ])

  // 重新加载当前选中的数据
  if (searchForm.selectedUserId) {
    await handleUserChange(searchForm.selectedUserId)
  }
  if (searchForm.selectedRoleId) {
    await handleRoleChange(searchForm.selectedRoleId)
  }
}

// 角色操作
const handleAssignRole = () => {
  if (!selectedUser.value) return
  selectedRoleForAssign.value = []
  assignDialogVisible.value = true
}

const handleConfirmAssign = async () => {
  if (!selectedUser.value || selectedRoleForAssign.value.length === 0) {
    ElMessage.warning('请选择要分配的角色')
    return
  }

  try {
    assignLoading.value = true

    const promises = selectedRoleForAssign.value.map(roleId =>
      UserRoleManagementService.addRoleToUser(selectedUser.value!.id, roleId)
    )

    await Promise.all(promises)
    ElMessage.success(`成功分配 ${selectedRoleForAssign.value.length} 个角色`)

    // 重新加载用户角色
    await loadUserRoles(selectedUser.value.id)
    assignDialogVisible.value = false
  } catch (error: any) {
    console.error('Assign roles failed:', error)
    ElMessage.error(error.message || '分配角色失败')
  } finally {
    assignLoading.value = false
  }
}

const handleRemoveRole = async (role: Role) => {
  if (!selectedUser.value) return

  try {
    await ElMessageBox.confirm(
      `确定要从用户 "${selectedUser.value.username}" 中移除角色 "${role.name}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await UserRoleManagementService.removeRoleFromUser(selectedUser.value.id, role.id)
    ElMessage.success('角色移除成功')

    // 重新加载用户角色
    await loadUserRoles(selectedUser.value.id)
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Remove role failed:', error)
      ElMessage.error(error.message || '移除角色失败')
    }
  }
}

const handleSetDefaultRole = async (role: Role) => {
  if (!selectedUser.value) return

  try {
    await ElMessageBox.confirm(
      `确定要将角色 "${role.name}" 设置为用户 "${selectedUser.value.username}" 的默认角色吗？`,
      '确认设置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await UserRoleManagementService.setDefaultRoleForUser(selectedUser.value.id, role.id)
    ElMessage.success('默认角色设置成功')

    // 重新加载用户角色
    await loadUserRoles(selectedUser.value.id)
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Set default role failed:', error)
      ElMessage.error(error.message || '设置默认角色失败')
    }
  }
}

const handleViewUserRoles = async (user: User) => {
  searchForm.selectedUserId = user.id
  searchForm.selectedRoleId = ''
  await handleUserChange(user.id)
}

// 初始化
onMounted(async () => {
  await Promise.all([
    loadUserList(),
    loadRoleList()
  ])
})
</script>

<style scoped>
.user-role-management {
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
  width: 100%;
}

.search-card :deep(.el-card__body) {
  padding: 20px;
}

.user-info-card,
.user-roles-card,
.role-users-card {
  width: 100%;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  padding: 0;
}

.user-roles {
  padding: 0;
}

.role-users {
  padding: 0;
}

.assign-role {
  padding: 0;
}

.assign-role p {
  margin-bottom: 16px;
  font-size: 14px;
  color: #606266;
}

/* 确保表格占满全宽 */
:deep(.el-table) {
  width: 100% !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .search-card :deep(.el-form) {
    flex-direction: column;
  }

  .search-card :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .el-col {
    margin-bottom: 20px;
  }
}
</style>
