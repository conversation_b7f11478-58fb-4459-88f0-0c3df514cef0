<template>
  <div class="role-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">角色管理</h1>
        <p class="page-description">管理系统中的所有角色和权限分配</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="handleCreateRole">
          新增角色
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="角色名称">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入角色名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="角色类型">
          <el-select
            v-model="searchForm.is_system"
            placeholder="请选择角色类型"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="系统角色" :value="true" />
            <el-option label="自定义角色" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序方式">
          <el-select
            v-model="searchForm.sort_by"
            style="width: 120px"
          >
            <el-option label="创建时间" value="created_at" />
            <el-option label="更新时间" value="updated_at" />
            <el-option label="角色名称" value="name" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序">
          <el-select
            v-model="searchForm.sort_order"
            style="width: 100px"
          >
            <el-option label="降序" value="desc" />
            <el-option label="升序" value="asc" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            搜索
          </el-button>
          <el-button @click="handleResetSearch">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 角色列表 -->
    <el-card class="table-card">
      <el-table
        :data="roleList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="name" label="角色名称" min-width="150">
          <template #default="{ row }">
            <div class="role-info">
              <el-tag v-if="row.is_system" type="warning" size="small">系统</el-tag>
              <span class="role-name">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="角色描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="is_system" label="角色类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_system ? 'warning' : 'success'" size="small">
              {{ row.is_system ? '系统角色' : '自定义' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="permissions" label="权限数量" width="100">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ row.permissions ? row.permissions.length : 0 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewRole(row)"
            >
              查看
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleManagePermissions(row)"
            >
              权限管理
            </el-button>
            <el-button
              v-if="!row.is_system"
              type="warning"
              size="small"
              @click="handleEditRole(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="!row.is_system"
              type="danger"
              size="small"
              @click="handleDeleteRole(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 角色编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isCreateMode ? '新增角色' : '编辑角色'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
        <el-form-item label="系统角色" prop="is_system">
          <el-switch
            v-model="editForm.is_system"
            :disabled="!isCreateMode"
          />
          <span class="form-tip">系统角色不可删除和修改核心属性</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSaveRole">
          {{ isCreateMode ? '创建' : '更新' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 角色详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="角色详情"
      width="800px"
    >
      <div v-if="selectedRole" class="role-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="角色名称">
            {{ selectedRole.name }}
          </el-descriptions-item>
          <el-descriptions-item label="角色类型">
            <el-tag :type="selectedRole.is_system ? 'warning' : 'success'" size="small">
              {{ selectedRole.is_system ? '系统角色' : '自定义角色' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="角色描述" :span="2">
            {{ selectedRole.description || '暂无描述' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(selectedRole.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(selectedRole.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="permissions-section" style="margin-top: 20px;">
          <h4>角色权限</h4>
          <el-table
            :data="rolePermissions"
            v-loading="permissionsLoading"
            stripe
            border
            max-height="300"
          >
            <el-table-column prop="name" label="权限名称" min-width="150" />
            <el-table-column prop="description" label="权限描述" min-width="200" show-overflow-tooltip />
            <el-table-column prop="resource" label="资源" width="120" />
            <el-table-column prop="action" label="操作" width="120" />
            <el-table-column prop="is_system" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="row.is_system ? 'warning' : 'success'" size="small">
                  {{ row.is_system ? '系统' : '自定义' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 权限管理对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="权限管理"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedRole" class="permission-management">
        <div class="role-info-header">
          <h4>为角色 "{{ selectedRole.name }}" 分配权限</h4>
        </div>
        
        <el-row :gutter="20">
          <!-- 可用权限列表 -->
          <el-col :span="12">
            <div class="permission-section">
              <h5>可用权限</h5>
              <el-input
                v-model="availablePermissionSearch"
                placeholder="搜索权限"
                clearable
                style="margin-bottom: 10px"
              />
              <el-table
                :data="filteredAvailablePermissions"
                v-loading="allPermissionsLoading"
                stripe
                border
                max-height="400"
                @selection-change="handleAvailablePermissionSelectionChange"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="name" label="权限名称" min-width="120" show-overflow-tooltip />
                <el-table-column prop="resource" label="资源" width="80" />
                <el-table-column prop="action" label="操作" width="80" />
              </el-table>
              <div style="margin-top: 10px; text-align: center;">
                <el-button
                  type="primary"
                  :disabled="selectedAvailablePermissions.length === 0"
                  @click="handleAddPermissions"
                >
                  添加权限 →
                </el-button>
              </div>
            </div>
          </el-col>

          <!-- 已分配权限列表 -->
          <el-col :span="12">
            <div class="permission-section">
              <h5>已分配权限</h5>
              <el-input
                v-model="assignedPermissionSearch"
                placeholder="搜索权限"
                clearable
                style="margin-bottom: 10px"
              />
              <el-table
                :data="filteredAssignedPermissions"
                v-loading="permissionsLoading"
                stripe
                border
                max-height="400"
                @selection-change="handleAssignedPermissionSelectionChange"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="name" label="权限名称" min-width="120" show-overflow-tooltip />
                <el-table-column prop="resource" label="资源" width="80" />
                <el-table-column prop="action" label="操作" width="80" />
              </el-table>
              <div style="margin-top: 10px; text-align: center;">
                <el-button
                  type="danger"
                  :disabled="selectedAssignedPermissions.length === 0"
                  @click="handleRemovePermissions"
                >
                  ← 移除权限
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <el-button @click="permissionDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh
} from '@element-plus/icons-vue'
import {
  RoleManagementService,
  PermissionManagementService,
  type Role,
  type Permission,
  type RoleListResult
} from '@/api/rolePermissionApi'

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const permissionsLoading = ref(false)
const allPermissionsLoading = ref(false)
const roleList = ref<Role[]>([])
const selectedRole = ref<Role | null>(null)
const rolePermissions = ref<Permission[]>([])
const allPermissions = ref<Permission[]>([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  is_system: '',
  sort_by: 'created_at',
  sort_order: 'desc' as 'asc' | 'desc'
})

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 对话框状态
const editDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const isCreateMode = ref(false)

// 编辑表单
const editFormRef = ref()
const editForm = reactive({
  name: '',
  description: '',
  is_system: false
})

// 表单验证规则
const editRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 100, message: '角色名称长度在 2 到 100 个字符', trigger: 'blur' }
  ]
}

// 权限管理相关
const availablePermissionSearch = ref('')
const assignedPermissionSearch = ref('')
const selectedAvailablePermissions = ref<Permission[]>([])
const selectedAssignedPermissions = ref<Permission[]>([])

// 计算属性
const filteredAvailablePermissions = computed(() => {
  const assigned = rolePermissions.value.map(p => p.id)
  return allPermissions.value
    .filter(p => !assigned.includes(p.id))
    .filter(p => {
      if (!availablePermissionSearch.value) return true
      const search = availablePermissionSearch.value.toLowerCase()
      return p.name.toLowerCase().includes(search) ||
             p.resource.toLowerCase().includes(search) ||
             p.action.toLowerCase().includes(search)
    })
})

const filteredAssignedPermissions = computed(() => {
  return rolePermissions.value.filter(p => {
    if (!assignedPermissionSearch.value) return true
    const search = assignedPermissionSearch.value.toLowerCase()
    return p.name.toLowerCase().includes(search) ||
           p.resource.toLowerCase().includes(search) ||
           p.action.toLowerCase().includes(search)
  })
})

// 格式化日期时间 - 明确指定北京时区
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai', // 明确指定北京时区
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 加载角色列表 - 使用真实后端API
const loadRoleList = async () => {
  try {
    loading.value = true

    const result = await RoleManagementService.getRoleList({
      page: pagination.page,
      page_size: pagination.page_size,
      keyword: searchForm.keyword,
      is_system: searchForm.is_system === '' ? undefined : searchForm.is_system === 'true',
      order_by: searchForm.sort_by,
      order: searchForm.sort_order
    })

    roleList.value = result.items
    pagination.total = result.total
    pagination.page = result.page
    pagination.page_size = result.page_size

  } catch (error: any) {
    console.error('Load role list failed:', error)
    ElMessage.error(error.message || '加载角色列表失败')
  } finally {
    loading.value = false
  }
}

// 加载所有权限
const loadAllPermissions = async () => {
  try {
    allPermissionsLoading.value = true
    const result = await PermissionManagementService.getPermissionList({
      page: 1,
      page_size: 1000 // 获取所有权限
    })
    allPermissions.value = result.items
  } catch (error: any) {
    console.error('Load all permissions failed:', error)
    ElMessage.error(error.message || '加载权限列表失败')
  } finally {
    allPermissionsLoading.value = false
  }
}

// 加载角色权限
const loadRolePermissions = async (roleId: string) => {
  try {
    permissionsLoading.value = true
    rolePermissions.value = await PermissionManagementService.getPermissionsByRole(roleId)
  } catch (error: any) {
    console.error('Load role permissions failed:', error)
    ElMessage.error(error.message || '加载角色权限失败')
  } finally {
    permissionsLoading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadRoleList()
}

const handleResetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    is_system: '',
    sort_by: 'created_at',
    sort_order: 'desc'
  })
  handleSearch()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.page_size = size
  pagination.page = 1
  loadRoleList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadRoleList()
}

// 角色操作
const handleCreateRole = () => {
  isCreateMode.value = true
  Object.assign(editForm, {
    name: '',
    description: '',
    is_system: false
  })
  editDialogVisible.value = true
}

const handleViewRole = async (role: Role) => {
  selectedRole.value = role
  detailDialogVisible.value = true
  await loadRolePermissions(role.id)
}

const handleEditRole = (role: Role) => {
  isCreateMode.value = false
  Object.assign(editForm, {
    name: role.name,
    description: role.description,
    is_system: role.is_system
  })
  selectedRole.value = role
  editDialogVisible.value = true
}

const handleDeleteRole = async (role: Role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${role.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await RoleManagementService.deleteRole(role.id)
    ElMessage.success('角色删除成功')
    await loadRoleList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Delete role failed:', error)
      ElMessage.error(error.message || '删除角色失败')
    }
  }
}

// 保存角色 - 使用真实后端API
const handleSaveRole = async () => {
  try {
    const valid = await editFormRef.value.validate()
    if (!valid) return

    saveLoading.value = true

    if (isCreateMode.value) {
      // 创建角色 - 调用真实API
      const createRequest = {
        name: editForm.name,
        description: editForm.description,
        is_system: editForm.is_system
      }

      await RoleManagementService.createRole(createRequest)
      ElMessage.success('角色创建成功')
    } else {
      // 更新角色 - 调用真实API
      const updateRequest = {
        name: editForm.name,
        description: editForm.description,
        is_system: editForm.is_system
      }

      await RoleManagementService.updateRole(selectedRole.value!.id, updateRequest)
      ElMessage.success('角色更新成功')
    }

    editDialogVisible.value = false
    await loadRoleList()
  } catch (error: any) {
    console.error('Save role failed:', error)
    ElMessage.error(error.message || '保存角色失败')
  } finally {
    saveLoading.value = false
  }
}

// 权限管理
const handleManagePermissions = async (role: Role) => {
  selectedRole.value = role
  permissionDialogVisible.value = true

  // 并行加载权限数据
  await Promise.all([
    loadRolePermissions(role.id),
    loadAllPermissions()
  ])

  // 清空选择
  selectedAvailablePermissions.value = []
  selectedAssignedPermissions.value = []
}

// 权限选择处理
const handleAvailablePermissionSelectionChange = (selection: Permission[]) => {
  selectedAvailablePermissions.value = selection
}

const handleAssignedPermissionSelectionChange = (selection: Permission[]) => {
  selectedAssignedPermissions.value = selection
}

// 添加权限
const handleAddPermissions = async () => {
  if (!selectedRole.value || selectedAvailablePermissions.value.length === 0) return

  try {
    const promises = selectedAvailablePermissions.value.map(permission =>
      RoleManagementService.addPermissionToRole(selectedRole.value!.id, permission.id)
    )

    await Promise.all(promises)
    ElMessage.success(`成功添加 ${selectedAvailablePermissions.value.length} 个权限`)

    // 重新加载角色权限
    await loadRolePermissions(selectedRole.value.id)
    selectedAvailablePermissions.value = []
  } catch (error: any) {
    console.error('Add permissions failed:', error)
    ElMessage.error(error.message || '添加权限失败')
  }
}

// 移除权限
const handleRemovePermissions = async () => {
  if (!selectedRole.value || selectedAssignedPermissions.value.length === 0) return

  try {
    const promises = selectedAssignedPermissions.value.map(permission =>
      RoleManagementService.removePermissionFromRole(selectedRole.value!.id, permission.id)
    )

    await Promise.all(promises)
    ElMessage.success(`成功移除 ${selectedAssignedPermissions.value.length} 个权限`)

    // 重新加载角色权限
    await loadRolePermissions(selectedRole.value.id)
    selectedAssignedPermissions.value = []
  } catch (error: any) {
    console.error('Remove permissions failed:', error)
    ElMessage.error(error.message || '移除权限失败')
  }
}

// 初始化
onMounted(() => {
  loadRoleList()
})
</script>

<style scoped>
.role-management {
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.search-card {
  margin-bottom: 20px;
  width: 100%;
}

.search-card :deep(.el-card__body) {
  padding: 20px;
}

.table-card {
  width: 100%;
}

.table-card :deep(.el-card__body) {
  padding: 0;
}

.table-card :deep(.el-table) {
  border-radius: 0;
}

.role-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.role-name {
  font-weight: 500;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.role-detail {
  padding: 0;
}

.permissions-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.permission-management {
  padding: 0;
}

.role-info-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.role-info-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.permission-section {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.permission-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

/* 确保表格和分页组件占满全宽 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-pagination) {
  width: 100%;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .search-card :deep(.el-form) {
    flex-direction: column;
  }

  .search-card :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>
