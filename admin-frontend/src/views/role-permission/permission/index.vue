<template>
  <div class="permission-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">权限管理</h1>
        <p class="page-description">管理系统中的所有权限和资源访问控制</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="handleCreatePermission">
          新增权限
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="权限名称">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入权限名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="资源类型">
          <el-select
            v-model="searchForm.resource"
            placeholder="请选择资源类型"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="用户管理" value="user" />
            <el-option label="角色管理" value="role" />
            <el-option label="权限管理" value="permission" />
            <el-option label="订单管理" value="order" />
            <el-option label="余额管理" value="balance" />
            <el-option label="系统配置" value="system" />
            <el-option label="快递公司" value="express_company" />
            <el-option label="快递价格" value="express_price" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select
            v-model="searchForm.action"
            placeholder="请选择操作类型"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="创建" value="create" />
            <el-option label="读取" value="read" />
            <el-option label="更新" value="update" />
            <el-option label="删除" value="delete" />
          </el-select>
        </el-form-item>
        <el-form-item label="权限类型">
          <el-select
            v-model="searchForm.is_system"
            placeholder="请选择权限类型"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="系统权限" :value="true" />
            <el-option label="自定义权限" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序方式">
          <el-select
            v-model="searchForm.sort_by"
            style="width: 120px"
          >
            <el-option label="创建时间" value="created_at" />
            <el-option label="更新时间" value="updated_at" />
            <el-option label="权限名称" value="name" />
            <el-option label="资源类型" value="resource" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序">
          <el-select
            v-model="searchForm.sort_order"
            style="width: 100px"
          >
            <el-option label="降序" value="desc" />
            <el-option label="升序" value="asc" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            搜索
          </el-button>
          <el-button @click="handleResetSearch">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 权限列表 -->
    <el-card class="table-card">
      <el-table
        :data="permissionList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="name" label="权限名称" min-width="180">
          <template #default="{ row }">
            <div class="permission-info">
              <el-tag v-if="row.is_system" type="warning" size="small">系统</el-tag>
              <span class="permission-name">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="权限描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="resource" label="资源类型" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.resource }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="action" label="操作类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getActionTagType(row.action)" size="small">
              {{ getActionLabel(row.action) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_system" label="权限类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_system ? 'warning' : 'success'" size="small">
              {{ row.is_system ? '系统权限' : '自定义' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewPermission(row)"
            >
              查看
            </el-button>
            <el-button
              v-if="!row.is_system"
              type="warning"
              size="small"
              @click="handleEditPermission(row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="!row.is_system"
              type="danger"
              size="small"
              @click="handleDeletePermission(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 权限编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isCreateMode ? '新增权限' : '编辑权限'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="权限描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入权限描述"
          />
        </el-form-item>
        <el-form-item label="资源类型" prop="resource">
          <el-select
            v-model="editForm.resource"
            placeholder="请选择资源类型"
            style="width: 100%"
          >
            <el-option label="用户管理" value="user" />
            <el-option label="角色管理" value="role" />
            <el-option label="权限管理" value="permission" />
            <el-option label="订单管理" value="order" />
            <el-option label="余额管理" value="balance" />
            <el-option label="系统配置" value="system" />
            <el-option label="快递公司" value="express_company" />
            <el-option label="快递价格" value="express_price" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作类型" prop="action">
          <el-select
            v-model="editForm.action"
            placeholder="请选择操作类型"
            style="width: 100%"
          >
            <el-option label="创建" value="create" />
            <el-option label="读取" value="read" />
            <el-option label="更新" value="update" />
            <el-option label="删除" value="delete" />
          </el-select>
        </el-form-item>
        <el-form-item label="系统权限" prop="is_system">
          <el-switch
            v-model="editForm.is_system"
            :disabled="!isCreateMode"
          />
          <span class="form-tip">系统权限不可删除和修改核心属性</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="saveLoading" @click="handleSavePermission">
          {{ isCreateMode ? '创建' : '更新' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 权限详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="权限详情"
      width="600px"
    >
      <div v-if="selectedPermission" class="permission-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="权限名称">
            {{ selectedPermission.name }}
          </el-descriptions-item>
          <el-descriptions-item label="权限类型">
            <el-tag :type="selectedPermission.is_system ? 'warning' : 'success'" size="small">
              {{ selectedPermission.is_system ? '系统权限' : '自定义权限' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="资源类型">
            <el-tag type="info" size="small">{{ selectedPermission.resource }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getActionTagType(selectedPermission.action)" size="small">
              {{ getActionLabel(selectedPermission.action) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="权限描述" :span="2">
            {{ selectedPermission.description || '暂无描述' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(selectedPermission.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(selectedPermission.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  Refresh
} from '@element-plus/icons-vue'
import {
  PermissionManagementService,
  type Permission,
  type PermissionListResult
} from '@/api/rolePermissionApi'

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const permissionList = ref<Permission[]>([])
const selectedPermission = ref<Permission | null>(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  resource: '',
  action: '',
  is_system: '',
  sort_by: 'created_at',
  sort_order: 'desc' as 'asc' | 'desc'
})

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 对话框状态
const editDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isCreateMode = ref(false)

// 编辑表单
const editFormRef = ref()
const editForm = reactive({
  name: '',
  description: '',
  resource: '',
  action: '',
  is_system: false
})

// 表单验证规则
const editRules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 100, message: '权限名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  resource: [
    { required: true, message: '请选择资源类型', trigger: 'change' }
  ],
  action: [
    { required: true, message: '请选择操作类型', trigger: 'change' }
  ]
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取操作类型标签样式
const getActionTagType = (action: string) => {
  const typeMap: Record<string, string> = {
    create: 'success',
    read: 'info',
    update: 'warning',
    delete: 'danger'
  }
  return typeMap[action] || 'info'
}

// 获取操作类型标签文本
const getActionLabel = (action: string) => {
  const labelMap: Record<string, string> = {
    create: '创建',
    read: '读取',
    update: '更新',
    delete: '删除'
  }
  return labelMap[action] || action
}

// 加载权限列表 - 使用真实后端API
const loadPermissionList = async () => {
  try {
    loading.value = true

    const result = await PermissionManagementService.getPermissionList({
      page: pagination.page,
      page_size: pagination.page_size,
      keyword: searchForm.keyword,
      resource: searchForm.resource,
      action: searchForm.action,
      is_system: searchForm.is_system === '' ? undefined : searchForm.is_system === 'true',
      order_by: searchForm.sort_by,
      order: searchForm.sort_order
    })

    permissionList.value = result.items
    pagination.total = result.total
    pagination.page = result.page
    pagination.page_size = result.page_size

  } catch (error: any) {
    console.error('Load permission list failed:', error)
    ElMessage.error(error.message || '加载权限列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadPermissionList()
}

const handleResetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    resource: '',
    action: '',
    is_system: '',
    sort_by: 'created_at',
    sort_order: 'desc'
  })
  handleSearch()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.page_size = size
  pagination.page = 1
  loadPermissionList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadPermissionList()
}

// 权限操作
const handleCreatePermission = () => {
  isCreateMode.value = true
  Object.assign(editForm, {
    name: '',
    description: '',
    resource: '',
    action: '',
    is_system: false
  })
  editDialogVisible.value = true
}

const handleViewPermission = (permission: Permission) => {
  selectedPermission.value = permission
  detailDialogVisible.value = true
}

const handleEditPermission = (permission: Permission) => {
  isCreateMode.value = false
  Object.assign(editForm, {
    name: permission.name,
    description: permission.description,
    resource: permission.resource,
    action: permission.action,
    is_system: permission.is_system
  })
  selectedPermission.value = permission
  editDialogVisible.value = true
}

const handleDeletePermission = async (permission: Permission) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限 "${permission.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await PermissionManagementService.deletePermission(permission.id)
    ElMessage.success('权限删除成功')
    await loadPermissionList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Delete permission failed:', error)
      ElMessage.error(error.message || '删除权限失败')
    }
  }
}

// 保存权限 - 使用真实后端API
const handleSavePermission = async () => {
  try {
    const valid = await editFormRef.value.validate()
    if (!valid) return

    saveLoading.value = true

    if (isCreateMode.value) {
      // 创建权限 - 调用真实API
      const createRequest = {
        name: editForm.name,
        description: editForm.description,
        resource: editForm.resource,
        action: editForm.action,
        is_system: editForm.is_system
      }

      await PermissionManagementService.createPermission(createRequest)
      ElMessage.success('权限创建成功')
    } else {
      // 更新权限 - 调用真实API
      const updateRequest = {
        name: editForm.name,
        description: editForm.description,
        resource: editForm.resource,
        action: editForm.action,
        is_system: editForm.is_system
      }

      await PermissionManagementService.updatePermission(selectedPermission.value!.id, updateRequest)
      ElMessage.success('权限更新成功')
    }

    editDialogVisible.value = false
    await loadPermissionList()
  } catch (error: any) {
    console.error('Save permission failed:', error)
    ElMessage.error(error.message || '保存权限失败')
  } finally {
    saveLoading.value = false
  }
}

// 初始化
onMounted(() => {
  loadPermissionList()
})
</script>

<style scoped>
.permission-management {
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.search-card {
  margin-bottom: 20px;
  width: 100%;
}

.search-card :deep(.el-card__body) {
  padding: 20px;
}

.table-card {
  width: 100%;
}

.table-card :deep(.el-card__body) {
  padding: 0;
}

.table-card :deep(.el-table) {
  border-radius: 0;
}

.permission-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-name {
  font-weight: 500;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.permission-detail {
  padding: 0;
}

/* 确保表格和分页组件占满全宽 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-pagination) {
  width: 100%;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .search-card :deep(.el-form) {
    flex-direction: column;
  }

  .search-card :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>
