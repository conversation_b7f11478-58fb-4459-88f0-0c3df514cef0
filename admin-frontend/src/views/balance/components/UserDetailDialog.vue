<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户余额详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <div v-if="userDetail">
        <!-- 用户基本信息 -->
        <el-card class="user-basic-info" style="margin-bottom: 20px;">
          <template #header>
            <span>用户基本信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">用户ID:</span>
                <span class="value">{{ userDetail.user_id }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">用户名:</span>
                <span class="value">{{ userDetail.username }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">邮箱:</span>
                <span class="value">{{ userDetail.email }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 余额信息 -->
        <el-card class="balance-info" style="margin-bottom: 20px;">
          <template #header>
            <span>余额信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="balance-item">
                <div class="balance-label">总余额</div>
                <div class="balance-value primary">{{ formatCurrency(userDetail.balance) }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="balance-item">
                <div class="balance-label">可用余额</div>
                <div class="balance-value success">{{ formatCurrency(userDetail.available_balance) }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="balance-item">
                <div class="balance-label">交易次数</div>
                <div class="balance-value info">{{ userDetail.transaction_count }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="balance-item">
                <div class="balance-label">状态</div>
                <div class="balance-value">
                  <el-tag :type="getStatusTagType(userDetail.status)">
                    {{ getStatusLabel(userDetail.status) }}
                  </el-tag>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 最近交易记录 -->
        <el-card class="recent-transactions">
          <template #header>
            <span>最近交易记录</span>
          </template>
          <el-table
            :data="userDetail.recent_transactions"
            style="width: 100%"
            max-height="300"
          >
            <el-table-column prop="id" label="交易ID" width="120" show-overflow-tooltip />
            <el-table-column label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getTransactionTypeTag(row.transaction_type)" size="small">
                  {{ getTransactionTypeLabel(row.transaction_type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="金额" width="120">
              <template #default="{ row }">
                <span :style="{ color: getAmountColor(row.transaction_type) }">
                  {{ formatTransactionAmount(row.amount, row.transaction_type) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="platform_order_no" label="平台订单号" width="140" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="row.platform_order_no" class="order-no">{{ row.platform_order_no }}</span>
                <span v-else class="no-data">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="balance_after" label="余额" width="120">
              <template #default="{ row }">
                {{ formatCurrency(row.balance_after) }}
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" show-overflow-tooltip />
            <el-table-column prop="created_at" label="时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="refreshDetail" :loading="loading">
          刷新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  BalanceApi, 
  type UserBalanceDetail,
  BALANCE_STATUS_OPTIONS,
  TRANSACTION_TYPE_OPTIONS
} from '@/api/balanceApi'

interface Props {
  visible: boolean
  userId: string | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const userDetail = ref<UserBalanceDetail | null>(null)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.userId) {
    loadUserDetail()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 加载用户详情
 */
const loadUserDetail = async () => {
  if (!props.userId) return

  try {
    loading.value = true
    const response = await BalanceApi.getUserBalanceDetail(props.userId)
    
    if (response.success) {
      userDetail.value = response.data || null
    } else {
      ElMessage.error(response.message || '获取用户详情失败')
    }
  } catch (error) {
    console.error('Load user detail error:', error)
    ElMessage.error('获取用户详情失败')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新详情
 */
const refreshDetail = () => {
  loadUserDetail()
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
  userDetail.value = null
}

// ==================== 工具方法 ====================

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    frozen: 'warning',
    disabled: 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取状态标签文本
 */
const getStatusLabel = (status: string) => {
  const option = BALANCE_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || status
}

/**
 * 获取交易类型标签
 */
const getTransactionTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    // 充值类
    user_deposit: 'success',
    admin_deposit: 'success',
    // 支付类
    order_pre_charge: 'warning',
    billing_difference: 'warning',
    order_intercept_charge: 'warning',
    return_charge: 'warning',
    order_revive_recharge: 'warning',
    // 退款类
    order_cancel_refund: 'info',
    billing_difference_refund: 'info',
    // 调整类
    balance_adjustment: 'primary'
  }
  return typeMap[type] || 'default'
}

/**
 * 获取交易类型文本
 */
const getTransactionTypeLabel = (type: string) => {
  const option = TRANSACTION_TYPE_OPTIONS.find(opt => opt.value === type)
  return option?.label || type
}

/**
 * 获取金额颜色
 */
const getAmountColor = (type: string) => {
  const colorMap: Record<string, string> = {
    // 充值类 - 绿色
    user_deposit: '#67C23A',
    admin_deposit: '#67C23A',
    // 退款类 - 绿色
    order_cancel_refund: '#67C23A',
    billing_difference_refund: '#67C23A',
    // 支付类 - 红色
    order_pre_charge: '#F56C6C',
    billing_difference: '#F56C6C',
    order_intercept_charge: '#F56C6C',
    return_charge: '#F56C6C',
    order_revive_recharge: '#F56C6C',
    // 调整类 - 橙色
    balance_adjustment: '#E6A23C'
  }
  return colorMap[type] || '#303133'
}

/**
 * 格式化交易金额
 */
const formatTransactionAmount = (amount: string, type: string) => {
  const num = parseFloat(amount)
  const positiveTypes = ['user_deposit', 'admin_deposit', 'order_cancel_refund']
  const prefix = positiveTypes.includes(type) ? '+' : '-'
  return `${prefix}¥${Math.abs(num).toFixed(2)}`
}

/**
 * 格式化货币
 */
const formatCurrency = (amount: string | number) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return `¥${num?.toFixed(2) || '0.00'}`
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.info-item {
  margin-bottom: 12px;
}

.label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
}

.value {
  color: #303133;
}

.balance-item {
  text-align: center;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
}

.balance-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.balance-value {
  font-size: 20px;
  font-weight: 600;
}

.balance-value.primary {
  color: #409EFF;
}

.balance-value.success {
  color: #67C23A;
}

.balance-value.info {
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-card__header) {
  background: #f8fafc;
  font-weight: 600;
}
</style>
