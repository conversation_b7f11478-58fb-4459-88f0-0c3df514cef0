<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量充值"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="选中用户">
        <div class="selected-users">
          <el-tag
            v-for="userId in userIds"
            :key="userId"
            type="info"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ userId }}
          </el-tag>
          <div class="user-count">共 {{ userIds.length }} 个用户</div>
        </div>
      </el-form-item>

      <el-form-item label="充值金额" prop="amount">
        <el-input
          v-model="form.amount"
          placeholder="请输入每个用户的充值金额"
          clearable
        >
          <template #prepend>¥</template>
        </el-input>
        <div class="amount-tip">
          总充值金额: ¥{{ totalAmount }}
        </div>
      </el-form-item>

      <el-form-item label="充值原因" prop="reason">
        <el-select
          v-model="form.reason"
          placeholder="请选择充值原因"
          style="width: 100%"
        >
          <el-option label="批量管理员充值" value="batch_admin_deposit" />
          <el-option label="批量新用户赠送" value="batch_new_user_gift" />
          <el-option label="批量VIP客户充值" value="batch_vip_customer_deposit" />
          <el-option label="批量客服补偿" value="batch_customer_service_compensation" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <el-form-item label="备注说明">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-alert
        title="批量操作提醒"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div>
            <p>• 将为 {{ userIds.length }} 个用户各充值 ¥{{ form.amount || '0.00' }}</p>
            <p>• 总计充值金额: ¥{{ totalAmount }}</p>
            <p>• 操作不可撤销，请仔细确认</p>
          </div>
        </template>
      </el-alert>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认批量充值
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { BalanceApi, type BatchOperationRequest } from '@/api/balanceApi'

interface Props {
  visible: boolean
  userIds: string[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  amount: '',
  reason: '',
  description: ''
})

// 计算总金额
const totalAmount = computed(() => {
  const amount = parseFloat(form.amount) || 0
  return (amount * props.userIds.length).toFixed(2)
})

// 表单验证规则
const rules: FormRules = {
  amount: [
    { required: true, message: '请输入充值金额', trigger: 'blur' },
    { 
      pattern: /^\d+(\.\d{1,2})?$/, 
      message: '请输入有效的金额（最多两位小数）', 
      trigger: 'blur' 
    },
    {
      validator: (rule, value, callback) => {
        const amount = parseFloat(value)
        if (amount <= 0) {
          callback(new Error('充值金额必须大于0'))
        } else if (amount > 10000) {
          callback(new Error('单个用户充值金额不能超过1万'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: '请选择充值原因', trigger: 'change' }
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(form, {
    amount: '',
    reason: '',
    description: ''
  })
  formRef.value?.clearValidate()
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 确认对话框
    const confirmText = `确认为 ${props.userIds.length} 个用户各充值 ¥${form.amount}，总计 ¥${totalAmount.value} 吗？`
    
    await ElMessageBox.confirm(confirmText, '确认批量充值', {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })

    loading.value = true

    // 构建批量操作请求
    const batchRequest: BatchOperationRequest = {
      operation_type: 'batch_deposit',
      user_ids: props.userIds,
      amount: form.amount,
      reason: form.reason,
      operations: props.userIds.map((userId: any) => ({
        user_id: userId,
        amount: form.amount,
        description: form.description
      }))
    }

    const response = await BalanceApi.batchOperation(batchRequest)
    
    if (response.success) {
      const { success_count, failure_count } = response.data
      if (failure_count === 0) {
        ElMessage.success(`批量充值成功，共处理 ${success_count} 个用户`)
      } else {
        ElMessage.warning(`批量充值完成，成功 ${success_count} 个，失败 ${failure_count} 个`)
      }
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || '批量充值失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Batch deposit error:', error)
      ElMessage.error('批量充值失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.selected-users {
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.user-count {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.amount-tip {
  margin-top: 8px;
  font-size: 14px;
  color: #E6A23C;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-input-group__prepend) {
  background: #f5f7fa;
  color: #909399;
  font-weight: 500;
}

:deep(.el-alert) {
  margin-top: 16px;
}

:deep(.el-alert p) {
  margin: 4px 0;
}
</style>
