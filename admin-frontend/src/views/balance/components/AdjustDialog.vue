<template>
  <el-dialog
    v-model="dialogVisible"
    title="余额调整"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="用户信息" v-if="user">
        <div class="user-info">
          <div class="user-name">{{ user.username }}</div>
          <div class="user-email">{{ user.email }}</div>
          <div class="current-balance">当前余额: {{ formatCurrency(user.balance) }}</div>
        </div>
      </el-form-item>
      
      <el-form-item label="用户ID" prop="user_id" v-if="!user">
        <el-input
          v-model="form.user_id"
          placeholder="请输入用户ID"
          clearable
        />
      </el-form-item>

      <el-form-item label="调整类型" prop="adjustment_type">
        <el-radio-group v-model="form.adjustment_type">
          <el-radio value="increase">
            <span style="color: #67C23A;">增加余额</span>
          </el-radio>
          <el-radio value="decrease">
            <span style="color: #F56C6C;">减少余额</span>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="调整金额" prop="amount">
        <el-input
          v-model="form.amount"
          placeholder="请输入调整金额"
          clearable
        >
          <template #prepend>
            <span :style="{ color: form.adjustment_type === 'increase' ? '#67C23A' : '#F56C6C' }">
              {{ form.adjustment_type === 'increase' ? '+¥' : '-¥' }}
            </span>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="调整后余额" v-if="user && form.amount">
        <div class="balance-preview">
          <span class="original-balance">{{ formatCurrency(user.balance) }}</span>
          <i class="el-icon-arrow-right" style="margin: 0 8px; color: #909399;"></i>
          <span 
            class="new-balance"
            :style="{ color: newBalance >= 0 ? '#67C23A' : '#F56C6C' }"
          >
            {{ formatCurrency(newBalance) }}
          </span>
        </div>
      </el-form-item>

      <el-form-item label="调整原因" prop="reason">
        <el-select
          v-model="form.reason"
          placeholder="请选择调整原因"
          style="width: 100%"
        >
          <el-option label="系统错误修正" value="system_error_correction" />
          <el-option label="用户申诉处理" value="user_complaint_handling" />
          <el-option label="数据异常修复" value="data_anomaly_fix" />
          <el-option label="业务规则调整" value="business_rule_adjustment" />
          <el-option label="管理员手动调整" value="admin_manual_adjustment" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <el-form-item label="备注说明" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          :type="form.adjustment_type === 'increase' ? 'success' : 'danger'" 
          @click="handleSubmit" 
          :loading="loading"
        >
          确认调整
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { BalanceApi, type BalanceAdjustmentRequest, type UserBalanceListItem } from '@/api/balanceApi'

interface Props {
  visible: boolean
  user?: UserBalanceListItem | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<BalanceAdjustmentRequest>({
  user_id: '',
  amount: '',
  adjustment_type: 'increase',
  reason: '',
  description: ''
})

// 计算调整后余额
const newBalance = computed(() => {
  if (!props.user || !form.amount) return 0
  
  const currentBalance = parseFloat(props.user.balance)
  const adjustAmount = parseFloat(form.amount)
  
  if (form.adjustment_type === 'increase') {
    return currentBalance + adjustAmount
  } else {
    return currentBalance - adjustAmount
  }
})

// 表单验证规则
const rules: FormRules = {
  user_id: [
    { required: true, message: '请输入用户ID', trigger: 'blur' }
  ],
  adjustment_type: [
    { required: true, message: '请选择调整类型', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入调整金额', trigger: 'blur' },
    { 
      pattern: /^\d+(\.\d{1,2})?$/, 
      message: '请输入有效的金额（最多两位小数）', 
      trigger: 'blur' 
    },
    {
      validator: (rule, value, callback) => {
        const amount = parseFloat(value)
        if (amount <= 0) {
          callback(new Error('调整金额必须大于0'))
        } else if (amount > 100000) {
          callback(new Error('单次调整金额不能超过10万'))
        } else if (form.adjustment_type === 'decrease' && props.user) {
          const currentBalance = parseFloat(props.user.balance)
          if (amount > currentBalance) {
            callback(new Error('减少金额不能超过当前余额'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: '请选择调整原因', trigger: 'change' }
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    if (props.user) {
      form.user_id = props.user.user_id
    }
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(form, {
    user_id: '',
    amount: '',
    adjustment_type: 'increase',
    reason: '',
    description: ''
  })
  formRef.value?.clearValidate()
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 确认对话框
    const actionText = form.adjustment_type === 'increase' ? '增加' : '减少'
    const confirmText = props.user 
      ? `确认为用户 ${props.user.username} ${actionText} ¥${form.amount} 吗？`
      : `确认为用户 ${form.user_id} ${actionText} ¥${form.amount} 吗？`
    
    await ElMessageBox.confirm(confirmText, '确认调整', {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })

    loading.value = true

    const response = await BalanceApi.adjustBalance(form)
    
    if (response.success) {
      ElMessage.success('调整成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || '调整失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Adjust error:', error)
      ElMessage.error('调整失败')
    }
  } finally {
    loading.value = false
  }
}

/**
 * 格式化货币
 */
const formatCurrency = (amount: string | number) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return `¥${num?.toFixed(2) || '0.00'}`
}
</script>

<style scoped>
.user-info {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.user-email {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.current-balance {
  font-size: 14px;
  color: #E6A23C;
  font-weight: 500;
}

.balance-preview {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.original-balance {
  font-size: 16px;
  font-weight: 500;
  color: #606266;
}

.new-balance {
  font-size: 16px;
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-input-group__prepend) {
  background: #f5f7fa;
  font-weight: 500;
}

:deep(.el-radio__label) {
  font-weight: 500;
}
</style>
