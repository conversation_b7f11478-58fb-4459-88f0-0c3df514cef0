<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量调整"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="选中用户">
        <div class="selected-users">
          <el-tag
            v-for="userId in userIds"
            :key="userId"
            type="info"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ userId }}
          </el-tag>
          <div class="user-count">共 {{ userIds.length }} 个用户</div>
        </div>
      </el-form-item>

      <el-form-item label="调整类型" prop="adjustment_type">
        <el-radio-group v-model="form.adjustment_type">
          <el-radio value="increase">
            <span style="color: #67C23A;">批量增加余额</span>
          </el-radio>
          <el-radio value="decrease">
            <span style="color: #F56C6C;">批量减少余额</span>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="调整金额" prop="amount">
        <el-input
          v-model="form.amount"
          placeholder="请输入每个用户的调整金额"
          clearable
        >
          <template #prepend>
            <span :style="{ color: form.adjustment_type === 'increase' ? '#67C23A' : '#F56C6C' }">
              {{ form.adjustment_type === 'increase' ? '+¥' : '-¥' }}
            </span>
          </template>
        </el-input>
        <div class="amount-tip">
          总调整金额: {{ form.adjustment_type === 'increase' ? '+' : '-' }}¥{{ totalAmount }}
        </div>
      </el-form-item>

      <el-form-item label="调整原因" prop="reason">
        <el-select
          v-model="form.reason"
          placeholder="请选择调整原因"
          style="width: 100%"
        >
          <el-option label="批量系统错误修正" value="batch_system_error_correction" />
          <el-option label="批量用户申诉处理" value="batch_user_complaint_handling" />
          <el-option label="批量数据异常修复" value="batch_data_anomaly_fix" />
          <el-option label="批量业务规则调整" value="batch_business_rule_adjustment" />
          <el-option label="批量管理员调整" value="batch_admin_adjustment" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <el-form-item label="备注说明">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-alert
        title="批量操作提醒"
        :type="form.adjustment_type === 'increase' ? 'success' : 'warning'"
        :closable="false"
        show-icon
      >
        <template #default>
          <div>
            <p>• 将为 {{ userIds.length }} 个用户各{{ form.adjustment_type === 'increase' ? '增加' : '减少' }} ¥{{ form.amount || '0.00' }}</p>
            <p>• 总计{{ form.adjustment_type === 'increase' ? '增加' : '减少' }}金额: ¥{{ totalAmount }}</p>
            <p>• 操作不可撤销，请仔细确认</p>
          </div>
        </template>
      </el-alert>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          :type="form.adjustment_type === 'increase' ? 'success' : 'danger'" 
          @click="handleSubmit" 
          :loading="loading"
        >
          确认批量调整
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { BalanceApi, type BatchOperationRequest } from '@/api/balanceApi'

interface Props {
  visible: boolean
  userIds: string[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  adjustment_type: 'increase' as 'increase' | 'decrease',
  amount: '',
  reason: '',
  description: ''
})

// 计算总金额
const totalAmount = computed(() => {
  const amount = parseFloat(form.amount) || 0
  return (amount * props.userIds.length).toFixed(2)
})

// 表单验证规则
const rules: FormRules = {
  adjustment_type: [
    { required: true, message: '请选择调整类型', trigger: 'change' }
  ],
  amount: [
    { required: true, message: '请输入调整金额', trigger: 'blur' },
    { 
      pattern: /^\d+(\.\d{1,2})?$/, 
      message: '请输入有效的金额（最多两位小数）', 
      trigger: 'blur' 
    },
    {
      validator: (rule, value, callback) => {
        const amount = parseFloat(value)
        if (amount <= 0) {
          callback(new Error('调整金额必须大于0'))
        } else if (amount > 10000) {
          callback(new Error('单个用户调整金额不能超过1万'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: '请选择调整原因', trigger: 'change' }
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(form, {
    adjustment_type: 'increase',
    amount: '',
    reason: '',
    description: ''
  })
  formRef.value?.clearValidate()
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 确认对话框
    const actionText = form.adjustment_type === 'increase' ? '增加' : '减少'
    const confirmText = `确认为 ${props.userIds.length} 个用户各${actionText} ¥${form.amount}，总计${actionText} ¥${totalAmount.value} 吗？`
    
    await ElMessageBox.confirm(confirmText, '确认批量调整', {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })

    loading.value = true

    // 构建批量操作请求
    const batchRequest: BatchOperationRequest = {
      operation_type: `batch_${form.adjustment_type}`,
      user_ids: props.userIds,
      amount: form.amount,
      reason: form.reason,
      operations: props.userIds.map((userId: any) => ({
        user_id: userId,
        amount: form.amount,
        description: form.description
      }))
    }

    const response = await BalanceApi.batchOperation(batchRequest)
    
    if (response.success) {
      const { success_count, failure_count } = response.data
      if (failure_count === 0) {
        ElMessage.success(`批量调整成功，共处理 ${success_count} 个用户`)
      } else {
        ElMessage.warning(`批量调整完成，成功 ${success_count} 个，失败 ${failure_count} 个`)
      }
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || '批量调整失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Batch adjust error:', error)
      ElMessage.error('批量调整失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.selected-users {
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.user-count {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.amount-tip {
  margin-top: 8px;
  font-size: 14px;
  color: #E6A23C;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-input-group__prepend) {
  background: #f5f7fa;
  font-weight: 500;
}

:deep(.el-alert) {
  margin-top: 16px;
}

:deep(.el-alert p) {
  margin: 4px 0;
}

:deep(.el-radio__label) {
  font-weight: 500;
}
</style>
