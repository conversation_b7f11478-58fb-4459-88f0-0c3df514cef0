<template>
  <el-dialog
    v-model="dialogVisible"
    title="强制退款"
    width="500px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="用户信息" v-if="user">
        <div class="user-info">
          <div class="user-name">{{ user.username }}</div>
          <div class="user-email">{{ user.email }}</div>
          <div class="current-balance">当前余额: {{ formatCurrency(user.balance) }}</div>
        </div>
      </el-form-item>
      
      <el-form-item label="用户ID" prop="user_id" v-if="!user">
        <el-input
          v-model="form.user_id"
          placeholder="请输入用户ID"
          clearable
        />
      </el-form-item>

      <el-form-item label="退款金额" prop="amount">
        <el-input
          v-model="form.amount"
          placeholder="请输入退款金额"
          clearable
        >
          <template #prepend>¥</template>
        </el-input>
      </el-form-item>

      <el-form-item label="订单号" prop="order_no">
        <el-input
          v-model="form.order_no"
          placeholder="请输入相关订单号（可选）"
          clearable
        />
      </el-form-item>

      <el-form-item label="参考ID" prop="reference_id">
        <el-input
          v-model="form.reference_id"
          placeholder="请输入相关参考ID（可选）"
          clearable
        />
      </el-form-item>

      <el-form-item label="退款原因" prop="reason">
        <el-select
          v-model="form.reason"
          placeholder="请选择退款原因"
          style="width: 100%"
        >
          <el-option label="订单取消退款" value="order_cancellation" />
          <el-option label="服务异常退款" value="service_exception" />
          <el-option label="用户申请退款" value="user_request" />
          <el-option label="系统错误退款" value="system_error" />
          <el-option label="重复扣费退款" value="duplicate_charge" />
          <el-option label="管理员强制退款" value="admin_force_refund" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>

      <el-form-item label="备注说明" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入备注说明（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-alert
        title="注意"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <div>强制退款将直接增加用户余额，请确认退款金额和原因正确。</div>
        </template>
      </el-alert>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="danger" @click="handleSubmit" :loading="loading">
          确认退款
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { BalanceApi, type ForceRefundRequest, type UserBalanceListItem } from '@/api/balanceApi'

interface Props {
  visible: boolean
  user?: UserBalanceListItem | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<ForceRefundRequest>({
  user_id: '',
  amount: '',
  order_no: '',
  reference_id: '',
  reason: '',
  description: ''
})

// 表单验证规则
const rules: FormRules = {
  user_id: [
    { required: true, message: '请输入用户ID', trigger: 'blur' }
  ],
  amount: [
    { required: true, message: '请输入退款金额', trigger: 'blur' },
    { 
      pattern: /^\d+(\.\d{1,2})?$/, 
      message: '请输入有效的金额（最多两位小数）', 
      trigger: 'blur' 
    },
    {
      validator: (rule, value, callback) => {
        const amount = parseFloat(value)
        if (amount <= 0) {
          callback(new Error('退款金额必须大于0'))
        } else if (amount > 100000) {
          callback(new Error('单次退款金额不能超过10万'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  reason: [
    { required: true, message: '请选择退款原因', trigger: 'change' }
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    if (props.user) {
      form.user_id = props.user.user_id
    }
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(form, {
    user_id: '',
    amount: '',
    order_no: '',
    reference_id: '',
    reason: '',
    description: ''
  })
  formRef.value?.clearValidate()
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    // 确认对话框
    const confirmText = props.user 
      ? `确认为用户 ${props.user.username} 退款 ¥${form.amount} 吗？`
      : `确认为用户 ${form.user_id} 退款 ¥${form.amount} 吗？`
    
    await ElMessageBox.confirm(confirmText, '确认退款', {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消'
    })

    loading.value = true

    const response = await BalanceApi.forceRefund(form)
    
    if (response.success) {
      ElMessage.success('退款成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || '退款失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Refund error:', error)
      ElMessage.error('退款失败')
    }
  } finally {
    loading.value = false
  }
}

/**
 * 格式化货币
 */
const formatCurrency = (amount: string | number) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return `¥${num?.toFixed(2) || '0.00'}`
}
</script>

<style scoped>
.user-info {
  padding: 12px;
  background: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.user-email {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.current-balance {
  font-size: 14px;
  color: #E6A23C;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-input-group__prepend) {
  background: #f5f7fa;
  color: #909399;
  font-weight: 500;
}

:deep(.el-alert) {
  margin-top: 16px;
}
</style>
