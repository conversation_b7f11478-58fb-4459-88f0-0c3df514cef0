<template>
  <div class="balance-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <i class="iconfont-sys">&#xe6e8;</i>
          余额管理
        </h1>
        <p class="page-description">管理用户余额、充值、调整和统计</p>
      </div>
      <div class="page-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showDepositDialog = true">
          <el-icon><Plus /></el-icon>
          手动充值
        </el-button>
        <el-button type="success" @click="handleExport" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 余额总览卡片 -->
    <div class="overview-cards" v-if="overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <StatCard
            title="总用户数"
            :value="overview.total_users"
            icon="el-icon-user"
            icon-color="#409EFF"
            icon-bg-color="#E6F7FF"
          />
        </el-col>
        <el-col :span="6">
          <StatCard
            title="活跃用户"
            :value="overview.active_users"
            icon="el-icon-user-solid"
            icon-color="#67C23A"
            icon-bg-color="#F0F9FF"
          />
        </el-col>
        <el-col :span="6">
          <StatCard
            title="总余额"
            :value="formatCurrency(overview.total_balance)"
            icon="el-icon-coin"
            icon-color="#E6A23C"
            icon-bg-color="#FDF6EC"
          />
        </el-col>
        <el-col :span="6">
          <StatCard
            title="平均余额"
            :value="formatCurrency(overview.avg_balance)"
            icon="el-icon-wallet"
            icon-color="#F56C6C"
            icon-bg-color="#FEF0F0"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
      <el-card>
        <el-form :model="searchForm" label-width="80px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="关键词">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="用户名/邮箱"
                  clearable
                  @keyup.enter="handleSearch"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="余额状态">
                <el-select 
                  v-model="searchForm.status" 
                  placeholder="请选择状态" 
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in BALANCE_STATUS_OPTIONS"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="排序方式">
                <el-select 
                  v-model="searchForm.order_by" 
                  placeholder="请选择排序" 
                  clearable
                  style="width: 100%"
                >
                  <el-option label="余额" value="balance" />
                  <el-option label="创建时间" value="created_at" />
                  <el-option label="更新时间" value="updated_at" />
                  <el-option label="交易次数" value="transaction_count" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label=" ">
                <el-button-group>
                  <el-button type="primary" @click="handleSearch" :loading="loading">
                    <el-icon><Search /></el-icon>
                    搜索
                  </el-button>
                  <el-button @click="handleReset">
                    <el-icon><RefreshLeft /></el-icon>
                    重置
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>

    <!-- 批量操作 -->
    <div class="batch-operations" v-if="selectedUsers.length > 0">
      <el-card>
        <div class="batch-actions">
          <span class="selected-info">已选择 {{ selectedUsers.length }} 个用户</span>
          <el-button-group>
            <el-button type="primary" @click="showBatchDepositDialog = true">
              <el-icon><Plus /></el-icon>
              批量充值
            </el-button>
            <el-button type="warning" @click="showBatchAdjustDialog = true">
              <el-icon><Edit /></el-icon>
              批量调整
            </el-button>
            <el-button type="danger" @click="clearSelection">
              <el-icon><Close /></el-icon>
              取消选择
            </el-button>
          </el-button-group>
        </div>
      </el-card>
    </div>

    <!-- 用户余额列表 -->
    <div class="balance-list">
      <el-card>
        <el-table
          ref="balanceTable"
          :data="balanceList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="user_id" label="用户ID" width="120" show-overflow-tooltip />
          <el-table-column label="用户信息" width="200">
            <template #default="{ row }">
              <div>
                <div class="user-name">{{ row.username }}</div>
                <div class="user-email">{{ row.email }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="balance" label="余额" width="120" sortable="custom">
            <template #default="{ row }">
              <div class="balance-amount">{{ formatCurrency(row.balance) }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="available_balance" label="可用余额" width="120">
            <template #default="{ row }">
              <div class="available-balance">{{ formatCurrency(row.available_balance) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="transaction_count" label="交易次数" width="100" sortable="custom" />
          <el-table-column prop="last_transaction_at" label="最后交易" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.last_transaction_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160" sortable="custom">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="{ row }">
              <el-button-group>
                <el-button type="primary" size="small" @click="viewUserDetail(row.user_id)">
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
                <el-button type="success" size="small" @click="showDepositDialogForUser(row)">
                  <el-icon><Plus /></el-icon>
                  充值
                </el-button>
                <el-button type="warning" size="small" @click="showAdjustDialogForUser(row)">
                  <el-icon><Edit /></el-icon>
                  调整
                </el-button>
                <el-button type="danger" size="small" @click="showRefundDialogForUser(row)">
                  <el-icon><RefreshLeft /></el-icon>
                  退款
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 手动充值对话框 -->
    <DepositDialog
      v-model:visible="showDepositDialog"
      :user="currentUser"
      @success="handleOperationSuccess"
    />

    <!-- 余额调整对话框 -->
    <AdjustDialog
      v-model:visible="showAdjustDialog"
      :user="currentUser"
      @success="handleOperationSuccess"
    />

    <!-- 强制退款对话框 -->
    <RefundDialog
      v-model:visible="showRefundDialog"
      :user="currentUser"
      @success="handleOperationSuccess"
    />

    <!-- 批量充值对话框 -->
    <BatchDepositDialog
      v-model:visible="showBatchDepositDialog"
      :user-ids="selectedUserIds"
      @success="handleBatchOperationSuccess"
    />

    <!-- 批量调整对话框 -->
    <BatchAdjustDialog
      v-model:visible="showBatchAdjustDialog"
      :user-ids="selectedUserIds"
      @success="handleBatchOperationSuccess"
    />

    <!-- 用户详情对话框 -->
    <UserDetailDialog
      v-model:visible="showUserDetailDialog"
      :user-id="currentUserId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Download,
  Search,
  RefreshLeft,
  Plus,
  Edit,
  Close,
  View
} from '@element-plus/icons-vue'

import StatCard from '@/components/Dashboard/StatCard.vue'
import DepositDialog from './components/DepositDialog.vue'
import AdjustDialog from './components/AdjustDialog.vue'
import RefundDialog from './components/RefundDialog.vue'
import BatchDepositDialog from './components/BatchDepositDialog.vue'
import BatchAdjustDialog from './components/BatchAdjustDialog.vue'
import UserDetailDialog from './components/UserDetailDialog.vue'

import {
  BalanceApi,
  type AdminBalanceOverview,
  type UserBalanceListItem,
  type UserBalanceListRequest,
  BALANCE_STATUS_OPTIONS
} from '@/api/balanceApi'

// ==================== 响应式数据 ====================
const loading = ref(false)
const exporting = ref(false)
const showDepositDialog = ref(false)
const showAdjustDialog = ref(false)
const showRefundDialog = ref(false)
const showBatchDepositDialog = ref(false)
const showBatchAdjustDialog = ref(false)
const showUserDetailDialog = ref(false)

// 数据
const overview = ref<AdminBalanceOverview | null>(null)
const balanceList = ref<UserBalanceListItem[]>([])
const selectedUsers = ref<UserBalanceListItem[]>([])
const currentUser = ref<UserBalanceListItem | null>(null)
const currentUserId = ref<string | null>(null)

// 搜索表单
const searchForm = reactive<UserBalanceListRequest>({
  page: 1,
  page_size: 20,
  keyword: undefined,
  status: undefined,
  order_by: 'created_at',
  order: 'desc'
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// ==================== 计算属性 ====================
const selectedUserIds = computed(() => selectedUsers.value.map(user => user.user_id))

// ==================== 方法 ====================

/**
 * 加载余额总览
 */
const loadOverview = async () => {
  try {
    const response = await BalanceApi.getBalanceOverview()
    if (response.success) {
      overview.value = response.data || null
    }
  } catch (error) {
    console.error('Load overview error:', error)
  }
}

/**
 * 加载用户余额列表
 */
const loadBalanceList = async () => {
  try {
    loading.value = true
    
    const params: UserBalanceListRequest = {
      ...searchForm,
      page: pagination.page,
      page_size: pagination.pageSize
    }

    const response = await BalanceApi.getUserBalanceList(params)
    
    if (response.success) {
      balanceList.value = response.data.items
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取余额列表失败')
    }
  } catch (error) {
    console.error('Load balance list error:', error)
    ElMessage.error('获取余额列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 刷新数据
 */
const refreshData = () => {
  loadOverview()
  loadBalanceList()
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.page = 1
  loadBalanceList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.assign(searchForm, {
    page: 1,
    page_size: 20,
    keyword: undefined,
    status: undefined,
    order_by: 'created_at',
    order: 'desc'
  })
  
  pagination.page = 1
  pagination.pageSize = 20
  
  loadBalanceList()
  ElMessage.success('搜索条件已重置')
}

/**
 * 处理选择变化
 */
const handleSelectionChange = (selection: UserBalanceListItem[]) => {
  selectedUsers.value = selection
}

/**
 * 清除选择
 */
const clearSelection = () => {
  selectedUsers.value = []
}

/**
 * 处理排序变化
 */
const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
  if (order) {
    searchForm.order_by = prop
    searchForm.order = order === 'ascending' ? 'asc' : 'desc'
  } else {
    searchForm.order_by = 'created_at'
    searchForm.order = 'desc'
  }
  loadBalanceList()
}

/**
 * 处理页面大小变化
 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadBalanceList()
}

/**
 * 处理当前页变化
 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadBalanceList()
}

/**
 * 查看用户详情
 */
const viewUserDetail = (userId: string) => {
  currentUserId.value = userId
  showUserDetailDialog.value = true
}

/**
 * 显示充值对话框
 */
const showDepositDialogForUser = (user: UserBalanceListItem) => {
  currentUser.value = user
  showDepositDialog.value = true
}

/**
 * 显示调整对话框
 */
const showAdjustDialogForUser = (user: UserBalanceListItem) => {
  currentUser.value = user
  showAdjustDialog.value = true
}

/**
 * 显示退款对话框
 */
const showRefundDialogForUser = (user: UserBalanceListItem) => {
  currentUser.value = user
  showRefundDialog.value = true
}

/**
 * 处理操作成功
 */
const handleOperationSuccess = () => {
  showDepositDialog.value = false
  showAdjustDialog.value = false
  showRefundDialog.value = false
  refreshData()
  ElMessage.success('操作成功')
}

/**
 * 处理批量操作成功
 */
const handleBatchOperationSuccess = () => {
  showBatchDepositDialog.value = false
  showBatchAdjustDialog.value = false
  clearSelection()
  refreshData()
  ElMessage.success('批量操作成功')
}

/**
 * 导出数据
 */
const handleExport = async () => {
  try {
    exporting.value = true

    const params = {
      ...searchForm,
      page: 1,
      page_size: 10000
    }

    const blob = await BalanceApi.exportUserBalances(params)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `user_balances_${new Date().getTime()}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Export error:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

// ==================== 工具方法 ====================

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    active: 'success',
    frozen: 'warning',
    disabled: 'danger'
  }
  return typeMap[status] || 'info'
}

/**
 * 获取状态标签文本
 */
const getStatusLabel = (status: string) => {
  const option = BALANCE_STATUS_OPTIONS.find(opt => opt.value === status)
  return option?.label || status
}

/**
 * 格式化货币
 */
const formatCurrency = (amount: string | number) => {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return `¥${num?.toFixed(2) || '0.00'}`
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.balance-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  color: white;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .iconfont-sys {
  font-size: 32px;
  opacity: 0.9;
}

.page-description {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.overview-cards {
  margin-bottom: 20px;
}

.search-filters {
  margin-bottom: 20px;
}

.search-filters :deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: none;
}

.search-filters :deep(.el-card__body) {
  padding: 24px;
}

.search-filters :deep(.el-form-item) {
  margin-bottom: 18px;
}

.search-filters :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.search-filters :deep(.el-input),
.search-filters :deep(.el-select) {
  width: 100% !important;
}

.search-filters :deep(.el-button-group) {
  display: flex;
  gap: 8px;
}

.search-filters :deep(.el-button-group .el-button) {
  margin-left: 0 !important;
}

.batch-operations {
  margin-bottom: 20px;
}

.batch-operations :deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8f4fd;
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-info {
  font-weight: 600;
  color: #409EFF;
  font-size: 16px;
}

.balance-list {
  margin-bottom: 20px;
}

.balance-list :deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: none;
}

.balance-list :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

.balance-list :deep(.el-table th) {
  background: #f8fafc;
  color: #475569;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
}

.balance-list :deep(.el-table td) {
  border-bottom: 1px solid #f1f5f9;
}

.balance-list :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: #fafbfc;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.user-email {
  font-size: 12px;
  color: #909399;
}

.balance-amount {
  font-weight: 600;
  color: #E6A23C;
  font-size: 14px;
}

.available-balance {
  font-weight: 500;
  color: #67C23A;
  font-size: 14px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .balance-management {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .page-title {
    font-size: 24px;
  }
}
</style>
