<template>
  <div class="users-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">用户管理</h1>
        <p class="page-description">管理系统中的所有用户账户</p>
      </div>
      <div class="header-right">
        <el-button type="primary" :icon="Plus" @click="handleCreateUser">
          新增用户
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-loading="statsLoading">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon total">
                <el-icon size="24"><UserIcon /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.total_users || 0 }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">
                <el-icon size="24"><UserFilled /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.active_users || 0 }}</div>
                <div class="stat-label">活跃用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon new">
                <el-icon size="24"><Plus /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.new_users_today || 0 }}</div>
                <div class="stat-label">今日新增</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon login">
                <el-icon size="24"><Connection /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ statistics.login_users_today || 0 }}</div>
                <div class="stat-label">今日登录</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名或邮箱"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable style="width: 120px">
            <el-option label="全部" value="all" />
            <el-option label="活跃" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序">
          <el-select v-model="searchForm.sort_by" style="width: 120px">
            <el-option label="创建时间" value="created_at" />
            <el-option label="最后登录" value="last_login" />
            <el-option label="用户名" value="username" />
          </el-select>
        </el-form-item>

        <el-form-item label="显示软删除用户">
          <el-switch
            v-model="searchForm.include_deleted"
            active-text="显示"
            inactive-text="隐藏"
            @change="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table
        :data="userList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="username" label="用户名" min-width="120">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="32">
                {{ row.username.charAt(0).toUpperCase() }}
              </el-avatar>
              <span class="username">{{ row.username }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '活跃' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="roles" label="角色" min-width="120">
          <template #default="{ row }">
            <el-tag
              v-for="role in row.roles"
              :key="role.id"
              size="small"
              class="role-tag"
            >
              {{ role.name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="login_count" label="登录次数" width="100" />
        <el-table-column prop="last_login" label="最后登录" width="160">
          <template #default="{ row }">
            <span v-if="row.last_login">
              {{ formatDateTime(row.last_login) }}
            </span>
            <span v-else class="text-muted">从未登录</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <!-- 软删除用户的操作 -->
            <template v-if="row.deleted_at">
              <el-tag type="danger" size="small" style="margin-right: 8px;">已删除</el-tag>
              <el-button type="danger" size="small" @click="handleForceDeleteUser(row)">
                永久删除
              </el-button>
            </template>
            <!-- 正常用户的操作 -->
            <template v-else>
              <el-button type="primary" size="small" @click="handleViewUser(row)">
                查看
              </el-button>
              <el-button type="warning" size="small" @click="handleEditUser(row)">
                编辑
              </el-button>
              <el-dropdown @command="(command: any) => handleDropdownCommand(command, row)">
                <el-button type="info" size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="toggle-status">
                      {{ row.is_active ? '禁用' : '启用' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="reset-password">
                      重置密码
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      删除用户
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="用户详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户名">{{ selectedUser.username }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ selectedUser.email }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedUser.is_active ? 'success' : 'danger'">
              {{ selectedUser.is_active ? '活跃' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag v-for="role in selectedUser.roles" :key="role.id" class="role-tag">
              {{ role.name }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="登录次数">{{ selectedUser.login_count || 0 }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">
            {{ selectedUser.last_login ? formatDateTime(selectedUser.last_login) : '从未登录' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(selectedUser.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(selectedUser.updated_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 用户编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isCreateMode ? '新增用户' : '编辑用户'"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" :disabled="!isCreateMode" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="isCreateMode">
          <el-input v-model="editForm.password" type="password" show-password />
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="editForm.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveUser" :loading="saveLoading">
          {{ isCreateMode ? '创建' : '保存' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  User as UserIcon,
  UserFilled,
  Connection,
  ArrowDown
} from '@element-plus/icons-vue'
import { UserManagementService, type User, type UserStatistics } from '@/api/userManagementApi'

// 响应式数据
const loading = ref(false)
const statsLoading = ref(false)
const saveLoading = ref(false)
const userList = ref<User[]>([])
const statistics = ref<UserStatistics>({} as UserStatistics)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: 'all',
  sort_by: 'created_at',
  sort_order: 'desc' as 'desc' | 'asc',
  include_deleted: false
})

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 对话框状态
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const selectedUser = ref<any>(null)
const isCreateMode = ref(false)

// 编辑表单
const editFormRef = ref()
const editForm = reactive({
  username: '',
  email: '',
  password: '',
  is_active: true
})

const editRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能少于8位', trigger: 'blur' }
  ]
}

// 加载用户列表 - 使用真实后端API
const loadUserList = async () => {
  try {
    loading.value = true

    const result = await UserManagementService.getUserList({
      page: pagination.page,
      page_size: pagination.page_size,
      keyword: searchForm.keyword,
      status: searchForm.status === 'all' ? '' : searchForm.status,
      order_by: searchForm.sort_by,
      order: searchForm.sort_order,
      include_deleted: searchForm.include_deleted
    })

    userList.value = result.items  // 使用后端返回的 items 字段
    pagination.total = result.total
    pagination.page = result.page
    pagination.page_size = result.page_size

  } catch (error: any) {
    console.error('Load user list failed:', error)
    ElMessage.error(error.message || '加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据 - 使用真实后端API
const loadStatistics = async () => {
  try {
    statsLoading.value = true

    const result = await UserManagementService.getUserStatistics()
    statistics.value = result

  } catch (error: any) {
    console.error('Load statistics failed:', error)
    ElMessage.error(error.message || '加载统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadUserList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: 'all',
    sort_by: 'created_at',
    sort_order: 'desc',
    include_deleted: false
  })
  handleSearch()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.page_size = size
  pagination.page = 1
  loadUserList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadUserList()
}

// 用户操作
const handleCreateUser = () => {
  isCreateMode.value = true
  Object.assign(editForm, {
    username: '',
    email: '',
    password: '',
    is_active: true
  })
  editDialogVisible.value = true
}

const handleViewUser = (user: any) => {
  selectedUser.value = user
  detailDialogVisible.value = true
}

const handleEditUser = (user: any) => {
  isCreateMode.value = false
  Object.assign(editForm, {
    username: user.username,
    email: user.email,
    password: '',
    is_active: user.is_active
  })
  selectedUser.value = user
  editDialogVisible.value = true
}

// 下拉菜单命令处理
const handleDropdownCommand = async (command: string, user: any) => {
  switch (command) {
    case 'toggle-status':
      await handleToggleUserStatus(user)
      break
    case 'reset-password':
      await handleResetPassword(user)
      break
    case 'delete':
      await handleDeleteUser(user)
      break
  }
}

// 切换用户状态 - 使用真实后端API
const handleToggleUserStatus = async (user: any) => {
  try {
    const action = user.is_active ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}用户 "${user.username}" 吗？`,
      `${action}用户`,
      { type: 'warning' }
    )

    // 调用真实API更新用户状态
    const updateRequest = {
      is_active: !user.is_active
    }

    await UserManagementService.updateUserStatus(user.id, updateRequest)
    ElMessage.success(`${action}成功`)

    // 重新加载用户列表
    await loadUserList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Toggle user status failed:', error)
      ElMessage.error(error.message || '操作失败')
    }
  }
}

// 重置密码 - 使用真实后端API
const handleResetPassword = async (user: any) => {
  try {
    const { value: newPassword } = await ElMessageBox.prompt(
      `请输入用户 "${user.username}" 的新密码`,
      '重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'password',
        inputValidator: (value: any) => {
          if (!value || value.length < 8) {
            return '密码长度不能少于8位'
          }
          return true
        }
      }
    )

    // 调用真实API重置密码
    const resetRequest = {
      new_password: newPassword
    }

    await UserManagementService.resetUserPassword(user.id, resetRequest)
    ElMessage.success('密码重置成功')
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Reset password failed:', error)
      ElMessage.error(error.message || '重置密码失败')
    }
  }
}

// 删除用户（软删除）- 使用真实后端API
const handleDeleteUser = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？删除后可以恢复。`,
      '删除用户',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消'
      }
    )

    // 调用真实API删除用户（软删除）
    await UserManagementService.deleteUser(user.id)
    ElMessage.success('删除成功')

    // 重新加载用户列表和统计数据
    await loadUserList()
    await loadStatistics()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Delete user failed:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 永久删除用户（硬删除）- 使用真实后端API
const handleForceDeleteUser = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要永久删除用户 "${user.username}" 吗？此操作不可恢复！`,
      '永久删除用户',
      {
        type: 'error',
        confirmButtonText: '确定永久删除',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        message: `<p>用户: <strong>${user.username}</strong></p><p style="color: red;">⚠️ 警告：此操作将永久删除用户及其所有相关数据，无法恢复！</p>`
      }
    )

    // 调用真实API永久删除用户
    await UserManagementService.forceDeleteUser(user.id)
    ElMessage.success('永久删除成功')

    // 重新加载用户列表和统计数据
    await loadUserList()
    await loadStatistics()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Force delete user failed:', error)
      ElMessage.error(error.message || '永久删除失败')
    }
  }
}

// 保存用户 - 使用真实后端API
const handleSaveUser = async () => {
  try {
    const valid = await editFormRef.value.validate()
    if (!valid) return

    saveLoading.value = true

    if (isCreateMode.value) {
      // 创建用户 - 调用真实API
      const createRequest = {
        username: editForm.username,
        email: editForm.email,
        password: editForm.password,
        is_active: editForm.is_active
      }

      await UserManagementService.createUser(createRequest)
      ElMessage.success('用户创建成功')

      // 重新加载用户列表和统计数据
      await loadUserList()
      await loadStatistics()
    } else {
      // 更新用户 - 调用真实API
      const updateRequest = {
        username: editForm.username,
        email: editForm.email,
        is_active: editForm.is_active
      }

      await UserManagementService.updateUser(selectedUser.value.id, updateRequest)
      ElMessage.success('用户更新成功')

      // 重新加载用户列表
      await loadUserList()
    }

    editDialogVisible.value = false
  } catch (error: any) {
    console.error('Save user failed:', error)
    ElMessage.error(error.message || (isCreateMode.value ? '创建用户失败' : '更新用户失败'))
  } finally {
    saveLoading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 组件挂载
onMounted(() => {
  loadUserList()
  loadStatistics()
})
</script>

<style scoped lang="scss">
.users-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-left {
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 4px 0;
    }

    .page-description {
      color: #7f8c8d;
      margin: 0;
      font-size: 14px;
    }
  }
}

.stats-cards {
  margin-bottom: 20px;

  .stat-card {
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    .stat-content {
      display: flex;
      align-items: center;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }

        &.active {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          color: white;
        }

        &.new {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          color: white;
        }

        &.login {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          color: white;
        }
      }

      .stat-info {
        .stat-value {
          font-size: 24px;
          font-weight: 600;
          color: #2c3e50;
          line-height: 1;
        }

        .stat-label {
          font-size: 12px;
          color: #7f8c8d;
          margin-top: 4px;
        }
      }
    }
  }
}

.search-card,
.table-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;

  .username {
    font-weight: 500;
  }
}

.role-tag {
  margin-right: 4px;
}

.text-muted {
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.user-detail {
  .role-tag {
    margin-right: 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .users-management {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .stats-cards {
    :deep(.el-col) {
      margin-bottom: 16px;
    }
  }

  .search-form {
    :deep(.el-form-item) {
      display: block;
      margin-bottom: 16px;
    }
  }
}
</style>
