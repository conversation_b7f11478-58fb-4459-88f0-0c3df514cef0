<template>
  <el-dialog
    v-model="dialogVisible"
    title="配置变更日志"
    width="1000px"
    :before-close="handleClose"
  >
    <!-- 搜索筛选区域 -->
    <div class="filter-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="配置组">
          <el-select
            v-model="searchForm.config_group"
            placeholder="选择配置组"
            clearable
            style="width: 150px"
            @change="handleSearch"
          >
            <el-option
              v-for="group in configGroups"
              :key="group"
              :label="group"
              :value="group"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配置键">
          <el-input
            v-model="searchForm.config_key"
            placeholder="输入配置键"
            clearable
            style="width: 180px"
            @keyup.enter="handleSearch"
            @clear="handleSearch"
          />
        </el-form-item>
        <el-form-item label="操作员">
          <el-input
            v-model="searchForm.operator_id"
            placeholder="输入操作员ID"
            clearable
            style="width: 150px"
            @keyup.enter="handleSearch"
            @clear="handleSearch"
          />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="handleReset" :icon="RefreshRight">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 变更日志列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="logList"
        @sort-change="handleSortChange"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="config_group" label="配置组" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.config_group }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="config_key" label="配置键" width="180" show-overflow-tooltip />
        <el-table-column label="变更内容" min-width="300">
          <template #default="{ row }">
            <div class="change-content">
              <div class="old-value">
                <span class="label">原值:</span>
                <span class="value old">{{ formatValue(row.old_value) }}</span>
              </div>
              <div class="new-value">
                <span class="label">新值:</span>
                <span class="value new">{{ formatValue(row.new_value) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="change_reason" label="变更原因" width="150" show-overflow-tooltip />
        <el-table-column prop="operator_name" label="操作员" width="120">
          <template #default="{ row }">
            {{ row.operator_name || row.operator_id }}
          </template>
        </el-table-column>
        <el-table-column prop="ip_address" label="IP地址" width="120" />
        <el-table-column prop="created_at" label="变更时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="handleViewDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="变更详情"
      width="600px"
      append-to-body
    >
      <div v-if="currentLog" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="配置组">{{ currentLog.config_group }}</el-descriptions-item>
          <el-descriptions-item label="配置键">{{ currentLog.config_key }}</el-descriptions-item>
          <el-descriptions-item label="操作员">{{ currentLog.operator_name || currentLog.operator_id }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ currentLog.ip_address }}</el-descriptions-item>
          <el-descriptions-item label="变更时间" :span="2">{{ formatDateTime(currentLog.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="变更原因" :span="2">{{ currentLog.change_reason }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="value-comparison">
          <h4>值变更对比</h4>
          <div class="comparison-content">
            <div class="old-section">
              <h5>原值</h5>
              <pre class="value-pre old">{{ currentLog.old_value }}</pre>
            </div>
            <div class="new-section">
              <h5>新值</h5>
              <pre class="value-pre new">{{ currentLog.new_value }}</pre>
            </div>
          </div>
        </div>
        
        <div class="user-agent-info">
          <h4>用户代理信息</h4>
          <p class="user-agent">{{ currentLog.user_agent }}</p>
        </div>
      </div>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport" :loading="exporting">
          导出日志
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, RefreshRight } from '@element-plus/icons-vue'
import {
  SystemConfigApi,
  type ConfigChangeLog,
  type ConfigChangeLogListRequest
} from '@/api/systemConfigApi'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const exporting = ref(false)
const logList = ref<ConfigChangeLog[]>([])
const configGroups = ref<string[]>([])
const dateRange = ref<[string, string] | null>(null)

// 搜索表单
const searchForm = reactive<ConfigChangeLogListRequest>({
  page: 1,
  page_size: 20,
  config_group: '',
  config_key: '',
  operator_id: '',
  start_date: '',
  end_date: '',
  order_by: 'created_at',
  order: 'desc'
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0,
  total_pages: 0
})

// 详情对话框
const showDetailDialog = ref(false)
const currentLog = ref<ConfigChangeLog | null>(null)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    loadChangeLogList()
    loadConfigGroups()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 加载变更日志列表
 */
const loadChangeLogList = async () => {
  try {
    loading.value = true
    const response = await SystemConfigApi.getChangeLogList({
      ...searchForm,
      page: pagination.page,
      page_size: pagination.page_size
    })

    if (response.success) {
      logList.value = response.data.logs
      pagination.total = response.data.total
      pagination.total_pages = response.data.total_pages
    } else {
      ElMessage.error(response.message || '获取变更日志失败')
    }
  } catch (error) {
    console.error('Load change log list error:', error)
    ElMessage.error('获取变更日志失败')
  } finally {
    loading.value = false
  }
}

/**
 * 加载配置组列表
 */
const loadConfigGroups = async () => {
  try {
    const response = await SystemConfigApi.getConfigGroups()
    if (response.success) {
      configGroups.value = response.data
    }
  } catch (error) {
    console.error('Load config groups error:', error)
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.page = 1
  loadChangeLogList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.assign(searchForm, {
    page: 1,
    page_size: 20,
    config_group: '',
    config_key: '',
    operator_id: '',
    start_date: '',
    end_date: '',
    order_by: 'created_at',
    order: 'desc'
  })
  dateRange.value = null
  pagination.page = 1
  loadChangeLogList()
}

/**
 * 日期范围变化
 */
const handleDateRangeChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.start_date = dates[0]
    searchForm.end_date = dates[1]
  } else {
    searchForm.start_date = ''
    searchForm.end_date = ''
  }
  handleSearch()
}

/**
 * 分页变化
 */
const handlePageChange = (page: number) => {
  pagination.page = page
  loadChangeLogList()
}

/**
 * 页大小变化
 */
const handlePageSizeChange = (pageSize: number) => {
  pagination.page_size = pageSize
  pagination.page = 1
  loadChangeLogList()
}

/**
 * 排序变化
 */
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  searchForm.order_by = prop
  searchForm.order = order === 'ascending' ? 'asc' : 'desc'
  loadChangeLogList()
}

/**
 * 查看详情
 */
const handleViewDetail = (log: ConfigChangeLog) => {
  currentLog.value = log
  showDetailDialog.value = true
}

/**
 * 导出日志
 */
const handleExport = () => {
  try {
    exporting.value = true
    
    const data = logList.value.map(log => ({
      配置组: log.config_group,
      配置键: log.config_key,
      原值: log.old_value,
      新值: log.new_value,
      变更原因: log.change_reason,
      操作员: log.operator_name || log.operator_id,
      IP地址: log.ip_address,
      变更时间: formatDateTime(log.created_at)
    }))

    // 简单的CSV导出
    const csvContent = [
      Object.keys(data[0]).join(','),
      ...data.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `config_change_logs_${new Date().toISOString().slice(0, 10)}.csv`
    link.click()
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('Export error:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

// ==================== 工具方法 ====================

/**
 * 格式化值显示
 */
const formatValue = (value: string) => {
  if (!value) return '-'
  if (value.length > 50) {
    return value.substring(0, 50) + '...'
  }
  return value
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.search-form {
  margin: 0;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.change-content {
  font-size: 12px;
}

.change-content .label {
  font-weight: 500;
  color: #606266;
  margin-right: 4px;
}

.change-content .value {
  font-family: monospace;
}

.change-content .value.old {
  color: #f56c6c;
}

.change-content .value.new {
  color: #67c23a;
}

.old-value {
  margin-bottom: 4px;
}

.pagination-wrapper {
  padding: 16px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 详情对话框样式 */
.detail-content {
  max-height: 500px;
  overflow-y: auto;
}

.value-comparison {
  margin-top: 20px;
}

.value-comparison h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.comparison-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.old-section h5,
.new-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
}

.old-section h5 {
  color: #f56c6c;
}

.new-section h5 {
  color: #67c23a;
}

.value-pre {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  margin: 0;
  font-size: 12px;
  font-family: monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
}

.value-pre.old {
  border-color: #f56c6c;
  background: #fef0f0;
}

.value-pre.new {
  border-color: #67c23a;
  background: #f0f9ff;
}

.user-agent-info {
  margin-top: 20px;
}

.user-agent-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.user-agent {
  margin: 0;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  word-break: break-all;
}
</style>
