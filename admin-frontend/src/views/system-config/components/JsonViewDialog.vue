<template>
  <el-dialog
    v-model="dialogVisible"
    title="JSON数据查看"
    width="700px"
    :before-close="handleClose"
  >
    <div class="json-viewer">
      <div class="json-toolbar">
        <el-button size="small" @click="copyToClipboard" :icon="DocumentCopy">
          复制
        </el-button>
        <el-button size="small" @click="formatJson" :icon="Tools">
          格式化
        </el-button>
        <el-button size="small" @click="validateJson" :icon="Check">
          验证
        </el-button>
      </div>
      
      <div class="json-content">
        <pre><code>{{ formattedJson }}</code></pre>
      </div>
      
      <div v-if="validationMessage" class="validation-result">
        <el-alert
          :title="validationMessage"
          :type="isValidJson ? 'success' : 'error'"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Tools, Check } from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  jsonData: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const formattedJson = ref('')
const validationMessage = ref('')
const isValidJson = ref(true)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    loadJsonData()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 加载JSON数据
 */
const loadJsonData = () => {
  formattedJson.value = props.jsonData
  validationMessage.value = ''
  
  // 自动格式化
  formatJson()
}

/**
 * 格式化JSON
 */
const formatJson = () => {
  try {
    const parsed = JSON.parse(props.jsonData)
    formattedJson.value = JSON.stringify(parsed, null, 2)
    isValidJson.value = true
    validationMessage.value = ''
  } catch (error) {
    formattedJson.value = props.jsonData
    isValidJson.value = false
    validationMessage.value = 'JSON格式不正确'
  }
}

/**
 * 验证JSON
 */
const validateJson = () => {
  try {
    JSON.parse(props.jsonData)
    isValidJson.value = true
    validationMessage.value = 'JSON格式正确'
    ElMessage.success('JSON格式验证通过')
  } catch (error: any) {
    isValidJson.value = false
    validationMessage.value = `JSON格式错误: ${error.message}`
    ElMessage.error('JSON格式验证失败')
  }
}

/**
 * 复制到剪贴板
 */
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(formattedJson.value)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = formattedJson.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('已复制到剪贴板')
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.json-viewer {
  max-height: 600px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.json-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.json-content {
  flex: 1;
  overflow: auto;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.json-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.json-content code {
  font-size: 13px;
  line-height: 1.5;
  color: #2c3e50;
}

.validation-result {
  margin-top: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* JSON语法高亮样式 */
.json-content :deep(.json-key) {
  color: #d73a49;
  font-weight: bold;
}

.json-content :deep(.json-string) {
  color: #032f62;
}

.json-content :deep(.json-number) {
  color: #005cc5;
}

.json-content :deep(.json-boolean) {
  color: #d73a49;
}

.json-content :deep(.json-null) {
  color: #6f42c1;
}
</style>
