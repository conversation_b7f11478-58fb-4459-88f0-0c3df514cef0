<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量更新配置"
    width="800px"
    :before-close="handleClose"
  >
    <div class="batch-update-content">
      <!-- 选中配置列表 -->
      <div class="selected-configs">
        <h4>选中的配置项 ({{ configs.length }})</h4>
        <el-table :data="configs" size="small" max-height="200">
          <el-table-column prop="config_group" label="配置组" width="120" />
          <el-table-column prop="config_key" label="配置键" width="180" />
          <el-table-column prop="config_value" label="当前值" min-width="150" show-overflow-tooltip />
          <el-table-column prop="config_type" label="类型" width="80" />
        </el-table>
      </div>

      <!-- 批量操作选项 -->
      <div class="batch-operations">
        <h4>批量操作</h4>
        <el-form :model="batchForm" label-width="120px">
          <el-form-item label="操作类型">
            <el-radio-group v-model="batchForm.operation_type" @change="handleOperationTypeChange">
              <el-radio value="update_value">批量更新值</el-radio>
              <el-radio value="update_status">批量更新状态</el-radio>
              <el-radio value="update_order">批量更新排序</el-radio>
              <el-radio value="update_description">批量更新描述</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 批量更新值 -->
          <template v-if="batchForm.operation_type === 'update_value'">
            <el-form-item label="新值">
              <el-input
                v-model="batchForm.new_value"
                placeholder="请输入新的配置值"
                clearable
              />
              <div class="form-tip">注意：不同类型的配置项请确保值的格式正确</div>
            </el-form-item>
          </template>

          <!-- 批量更新状态 -->
          <template v-if="batchForm.operation_type === 'update_status'">
            <el-form-item label="新状态">
              <el-radio-group v-model="batchForm.new_status">
                <el-radio :value="true">启用</el-radio>
                <el-radio :value="false">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>

          <!-- 批量更新排序 -->
          <template v-if="batchForm.operation_type === 'update_order'">
            <el-form-item label="排序方式">
              <el-radio-group v-model="batchForm.order_method">
                <el-radio value="increment">递增排序</el-radio>
                <el-radio value="custom">自定义起始值</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="batchForm.order_method === 'custom'" label="起始值">
              <el-input-number
                v-model="batchForm.start_order"
                :min="0"
                :max="9999"
                style="width: 200px"
              />
              <div class="form-tip">从此值开始，每个配置项递增1</div>
            </el-form-item>
          </template>

          <!-- 批量更新描述 -->
          <template v-if="batchForm.operation_type === 'update_description'">
            <el-form-item label="操作方式">
              <el-radio-group v-model="batchForm.description_method">
                <el-radio value="replace">替换描述</el-radio>
                <el-radio value="append">追加描述</el-radio>
                <el-radio value="prepend">前置描述</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="描述内容">
              <el-input
                v-model="batchForm.description_content"
                type="textarea"
                :rows="3"
                placeholder="请输入描述内容"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </template>

          <el-form-item label="变更原因">
            <el-input
              v-model="batchForm.change_reason"
              placeholder="请输入本次批量变更的原因"
              clearable
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 预览变更 -->
      <div v-if="previewList.length > 0" class="preview-section">
        <h4>变更预览</h4>
        <el-table :data="previewList" size="small" max-height="250">
          <el-table-column prop="config_key" label="配置键" width="180" />
          <el-table-column label="变更内容" min-width="300">
            <template #default="{ row }">
              <div class="change-preview">
                <div class="old-value">
                  <span class="label">原值:</span>
                  <span class="value old">{{ row.old_value }}</span>
                </div>
                <div class="new-value">
                  <span class="label">新值:</span>
                  <span class="value new">{{ row.new_value }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-alert
        title="批量更新提醒"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <div>
            <p>• 批量更新将同时修改 {{ configs.length }} 个配置项</p>
            <p>• 请仔细检查变更内容，确保操作正确</p>
            <p>• 建议在操作前创建配置备份</p>
            <p>• 操作完成后系统缓存将自动刷新</p>
          </div>
        </template>
      </el-alert>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handlePreview" :disabled="!canPreview">
          预览变更
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="loading"
          :disabled="!canSubmit"
        >
          确认更新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  SystemConfigApi,
  type SystemConfig,
  type BatchUpdateConfigsRequest
} from '@/api/systemConfigApi'

interface Props {
  visible: boolean
  configs: SystemConfig[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)

// 批量操作表单
const batchForm = reactive({
  operation_type: 'update_value',
  new_value: '',
  new_status: true,
  order_method: 'increment',
  start_order: 0,
  description_method: 'replace',
  description_content: '',
  change_reason: ''
})

// 预览列表
const previewList = ref<Array<{
  config_key: string
  old_value: string
  new_value: string
}>>([])

// 计算属性
const canPreview = computed(() => {
  switch (batchForm.operation_type) {
    case 'update_value':
      return !!batchForm.new_value
    case 'update_status':
      return batchForm.new_status !== undefined
    case 'update_order':
      return true
    case 'update_description':
      return !!batchForm.description_content
    default:
      return false
  }
})

const canSubmit = computed(() => {
  return canPreview.value && !!batchForm.change_reason && previewList.value.length > 0
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(batchForm, {
    operation_type: 'update_value',
    new_value: '',
    new_status: true,
    order_method: 'increment',
    start_order: 0,
    description_method: 'replace',
    description_content: '',
    change_reason: ''
  })
  previewList.value = []
}

/**
 * 操作类型变化
 */
const handleOperationTypeChange = () => {
  previewList.value = []
}

/**
 * 预览变更
 */
const handlePreview = () => {
  const previews: Array<{
    config_key: string
    old_value: string
    new_value: string
  }> = []

  props.configs.forEach((config: any, index: number) => {
    let newValue = ''
    let oldValue = ''

    switch (batchForm.operation_type) {
      case 'update_value':
        oldValue = config.config_value
        newValue = batchForm.new_value
        break
      case 'update_status':
        oldValue = config.is_active ? '启用' : '禁用'
        newValue = batchForm.new_status ? '启用' : '禁用'
        break
      case 'update_order':
        oldValue = String(config.display_order)
        if (batchForm.order_method === 'increment') {
          newValue = String(index + 1)
        } else {
          newValue = String(batchForm.start_order + index)
        }
        break
      case 'update_description':
        oldValue = config.description
        if (batchForm.description_method === 'replace') {
          newValue = batchForm.description_content
        } else if (batchForm.description_method === 'append') {
          newValue = config.description + ' ' + batchForm.description_content
        } else if (batchForm.description_method === 'prepend') {
          newValue = batchForm.description_content + ' ' + config.description
        }
        break
    }

    previews.push({
      config_key: config.config_key,
      old_value: oldValue,
      new_value: newValue
    })
  })

  previewList.value = previews
  ElMessage.success('预览生成成功')
}

/**
 * 提交批量更新
 */
const handleSubmit = async () => {
  try {
    await ElMessageBox.confirm(
      `确认批量更新 ${props.configs.length} 个配置项吗？此操作不可撤销。`,
      '确认批量更新',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    loading.value = true

    // 构建更新后的配置列表
    const updatedConfigs = props.configs.map((config: any, index: number) => {
      const updatedConfig = { ...config }

      switch (batchForm.operation_type) {
        case 'update_value':
          updatedConfig.config_value = batchForm.new_value
          break
        case 'update_status':
          updatedConfig.is_active = batchForm.new_status
          break
        case 'update_order':
          if (batchForm.order_method === 'increment') {
            updatedConfig.display_order = index + 1
          } else {
            updatedConfig.display_order = batchForm.start_order + index
          }
          break
        case 'update_description':
          if (batchForm.description_method === 'replace') {
            updatedConfig.description = batchForm.description_content
          } else if (batchForm.description_method === 'append') {
            updatedConfig.description = config.description + ' ' + batchForm.description_content
          } else if (batchForm.description_method === 'prepend') {
            updatedConfig.description = batchForm.description_content + ' ' + config.description
          }
          break
      }

      return updatedConfig
    })

    const batchRequest: BatchUpdateConfigsRequest = {
      configs: updatedConfigs
    }

    const response = await SystemConfigApi.batchUpdateConfigs(batchRequest)
    
    if (response.success) {
      ElMessage.success('批量更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || '批量更新失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Batch update error:', error)
      ElMessage.error('批量更新失败')
    }
  } finally {
    loading.value = false
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.batch-update-content {
  max-height: 600px;
  overflow-y: auto;
}

.selected-configs,
.batch-operations,
.preview-section {
  margin-bottom: 20px;
}

.selected-configs h4,
.batch-operations h4,
.preview-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.change-preview {
  font-size: 12px;
}

.change-preview .label {
  font-weight: 500;
  color: #606266;
  margin-right: 4px;
}

.change-preview .value {
  font-family: monospace;
}

.change-preview .value.old {
  color: #f56c6c;
}

.change-preview .value.new {
  color: #67c23a;
}

.old-value {
  margin-bottom: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-alert) {
  margin-top: 16px;
}

:deep(.el-alert p) {
  margin: 4px 0;
}
</style>
