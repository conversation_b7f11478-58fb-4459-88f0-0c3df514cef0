<template>
  <el-dialog
    v-model="dialogVisible"
    title="配置备份管理"
    width="900px"
    :before-close="handleClose"
  >
    <!-- 操作工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleCreateBackup" :icon="Plus">
        创建备份
      </el-button>
      <el-button @click="loadBackupList" :icon="Refresh">
        刷新
      </el-button>
    </div>

    <!-- 备份列表 -->
    <div class="backup-list">
      <el-table
        v-loading="loading"
        :data="backupList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="backup_name" label="备份名称" width="200" />
        <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip />
        <el-table-column label="文件大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.file_size) }}
          </template>
        </el-table-column>
        <el-table-column label="配置数量" width="100">
          <template #default="{ row }">
            {{ row.backup_data?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="handleViewBackup(row)">
              查看
            </el-button>
            <el-button type="success" link size="small" @click="handleRestoreBackup(row)">
              恢复
            </el-button>
            <el-button type="info" link size="small" @click="handleDownloadBackup(row)">
              下载
            </el-button>
            <el-button type="danger" link size="small" @click="handleDeleteBackup(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 备份详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="备份详情"
      width="700px"
      append-to-body
    >
      <div v-if="currentBackup" class="backup-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="备份名称">{{ currentBackup.backup_name }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(currentBackup.file_size) }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ currentBackup.description }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ formatDateTime(currentBackup.created_at) }}</el-descriptions-item>
        </el-descriptions>

        <div class="config-list">
          <h4>包含的配置项 ({{ currentBackup.backup_data?.length || 0 }})</h4>
          <el-table :data="currentBackup.backup_data" size="small" max-height="300">
            <el-table-column prop="config_group" label="配置组" width="120" />
            <el-table-column prop="config_key" label="配置键" width="180" />
            <el-table-column prop="config_value" label="配置值" min-width="150" show-overflow-tooltip />
            <el-table-column prop="config_type" label="类型" width="80" />
            <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 创建备份对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建配置备份"
      width="500px"
      append-to-body
    >
      <el-form
        ref="backupFormRef"
        :model="backupForm"
        :rules="backupRules"
        label-width="100px"
      >
        <el-form-item label="备份名称" prop="backup_name">
          <el-input
            v-model="backupForm.backup_name"
            placeholder="请输入备份名称"
            clearable
          />
          <div class="form-tip">建议使用有意义的名称，如：production_backup_20240101</div>
        </el-form-item>
        <el-form-item label="备份描述" prop="backup_description">
          <el-input
            v-model="backupForm.backup_description"
            type="textarea"
            :rows="3"
            placeholder="请输入备份描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <el-alert
        title="备份说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div>
            <p>• 备份将包含当前所有系统配置</p>
            <p>• 备份文件可用于配置恢复和迁移</p>
            <p>• 建议定期创建备份以防数据丢失</p>
          </div>
        </template>
      </el-alert>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSaveBackup" :loading="creating">
            创建备份
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import {
  SystemConfigApi,
  type ConfigBackup,
  type CreateBackupRequest
} from '@/api/systemConfigApi'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'restore-backup'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const creating = ref(false)
const backupList = ref<ConfigBackup[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 对话框状态
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const currentBackup = ref<ConfigBackup | null>(null)

// 备份表单
const backupFormRef = ref<FormInstance>()
const backupForm = reactive<CreateBackupRequest>({
  backup_name: '',
  backup_description: ''
})

// 表单验证规则
const backupRules: FormRules = {
  backup_name: [
    { required: true, message: '请输入备份名称', trigger: 'blur' },
    { min: 2, max: 100, message: '备份名称长度在 2 到 100 个字符', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/, 
      message: '备份名称只能包含字母、数字、下划线、横线和中文', 
      trigger: 'blur' 
    }
  ],
  backup_description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    loadBackupList()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 加载备份列表
 */
const loadBackupList = async () => {
  try {
    loading.value = true
    const response = await SystemConfigApi.getBackupList({
      page: pagination.page,
      page_size: pagination.page_size
    })

    if (response.success) {
      backupList.value = response.data.backups
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取备份列表失败')
    }
  } catch (error) {
    console.error('Load backup list error:', error)
    ElMessage.error('获取备份列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 分页变化
 */
const handlePageChange = (page: number) => {
  pagination.page = page
  loadBackupList()
}

/**
 * 页大小变化
 */
const handlePageSizeChange = (pageSize: number) => {
  pagination.page_size = pageSize
  pagination.page = 1
  loadBackupList()
}

/**
 * 查看备份
 */
const handleViewBackup = async (backup: ConfigBackup) => {
  try {
    const response = await SystemConfigApi.getBackup(backup.id)
    if (response.success) {
      currentBackup.value = response.data
      showDetailDialog.value = true
    } else {
      ElMessage.error(response.message || '获取备份详情失败')
    }
  } catch (error) {
    console.error('Get backup detail error:', error)
    ElMessage.error('获取备份详情失败')
  }
}

/**
 * 恢复备份
 */
const handleRestoreBackup = async (backup: ConfigBackup) => {
  try {
    await ElMessageBox.confirm(
      `确认恢复备份 "${backup.backup_name}" 吗？这将覆盖当前所有配置。`,
      '确认恢复备份',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    const response = await SystemConfigApi.restoreBackup(backup.id)
    if (response.success) {
      ElMessage.success('备份恢复成功')
      emit('restore-backup')
    } else {
      ElMessage.error(response.message || '备份恢复失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Restore backup error:', error)
      ElMessage.error('备份恢复失败')
    }
  }
}

/**
 * 下载备份
 */
const handleDownloadBackup = async (backup: ConfigBackup) => {
  try {
    const response = await SystemConfigApi.getBackup(backup.id)
    if (response.success) {
      const data = response.data.backup_data
      const jsonContent = JSON.stringify(data, null, 2)
      
      const blob = new Blob([jsonContent], { type: 'application/json' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `${backup.backup_name}.json`
      link.click()
      
      ElMessage.success('下载成功')
    } else {
      ElMessage.error(response.message || '下载失败')
    }
  } catch (error) {
    console.error('Download backup error:', error)
    ElMessage.error('下载失败')
  }
}

/**
 * 创建备份
 */
const handleCreateBackup = () => {
  Object.assign(backupForm, {
    backup_name: `backup_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}`,
    backup_description: ''
  })
  showCreateDialog.value = true
}

/**
 * 保存备份
 */
const handleSaveBackup = async () => {
  if (!backupFormRef.value) return

  try {
    const valid = await backupFormRef.value.validate()
    if (!valid) return

    creating.value = true

    const response = await SystemConfigApi.createBackup(backupForm)
    if (response.success) {
      ElMessage.success('备份创建成功')
      showCreateDialog.value = false
      loadBackupList()
    } else {
      ElMessage.error(response.message || '备份创建失败')
    }
  } catch (error) {
    console.error('Create backup error:', error)
    ElMessage.error('备份创建失败')
  } finally {
    creating.value = false
  }
}

/**
 * 删除备份
 */
const handleDeleteBackup = async (backup: ConfigBackup) => {
  try {
    await ElMessageBox.confirm(
      `确认删除备份 "${backup.backup_name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    const response = await SystemConfigApi.deleteBackup(backup.id)
    if (response.success) {
      ElMessage.success('删除成功')
      loadBackupList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Delete backup error:', error)
      ElMessage.error('删除失败')
    }
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

// ==================== 工具方法 ====================

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.backup-list {
  min-height: 400px;
}

.pagination-wrapper {
  padding: 16px 0;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 备份详情样式 */
.backup-detail {
  max-height: 500px;
  overflow-y: auto;
}

.config-list {
  margin-top: 20px;
}

.config-list h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

:deep(.el-alert) {
  margin-top: 16px;
}

:deep(.el-alert p) {
  margin: 4px 0;
}
</style>
