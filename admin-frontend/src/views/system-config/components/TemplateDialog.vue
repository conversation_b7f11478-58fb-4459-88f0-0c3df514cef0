<template>
  <el-dialog
    v-model="dialogVisible"
    title="配置模板管理"
    width="900px"
    :before-close="handleClose"
  >
    <!-- 操作工具栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleCreateTemplate" :icon="Plus">
        创建模板
      </el-button>
      <el-button @click="loadTemplateList" :icon="Refresh">
        刷新
      </el-button>
    </div>

    <!-- 模板列表 -->
    <div class="template-list">
      <el-table
        v-loading="loading"
        :data="templateList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="template_name" label="模板名称" width="200" />
        <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip />
        <el-table-column label="配置数量" width="100">
          <template #default="{ row }">
            {{ row.template_data?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="handleViewTemplate(row)">
              查看
            </el-button>
            <el-button type="success" link size="small" @click="handleApplyTemplate(row)">
              应用
            </el-button>
            <el-button type="warning" link size="small" @click="handleEditTemplate(row)">
              编辑
            </el-button>
            <el-button type="danger" link size="small" @click="handleDeleteTemplate(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 模板详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="模板详情"
      width="700px"
      append-to-body
    >
      <div v-if="currentTemplate" class="template-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模板名称">{{ currentTemplate.template_name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentTemplate.is_active ? 'success' : 'danger'" size="small">
              {{ currentTemplate.is_active ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ currentTemplate.description }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ formatDateTime(currentTemplate.created_at) }}</el-descriptions-item>
        </el-descriptions>

        <div class="config-list">
          <h4>包含的配置项 ({{ currentTemplate.template_data?.length || 0 }})</h4>
          <el-table :data="currentTemplate.template_data" size="small" max-height="300">
            <el-table-column prop="config_group" label="配置组" width="120" />
            <el-table-column prop="config_key" label="配置键" width="180" />
            <el-table-column prop="config_value" label="配置值" min-width="150" show-overflow-tooltip />
            <el-table-column prop="config_type" label="类型" width="80" />
            <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 创建/编辑模板对话框 -->
    <el-dialog
      v-model="showEditDialog"
      :title="isEditTemplate ? '编辑模板' : '创建模板'"
      width="500px"
      append-to-body
    >
      <el-form
        ref="templateFormRef"
        :model="templateForm"
        :rules="templateRules"
        label-width="100px"
      >
        <el-form-item label="模板名称" prop="template_name">
          <el-input
            v-model="templateForm.template_name"
            placeholder="请输入模板名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="templateForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="templateForm.is_active" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleSaveTemplate" :loading="saving">
            {{ isEditTemplate ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import {
  SystemConfigApi,
  type ConfigTemplate
} from '@/api/systemConfigApi'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'apply-template'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const saving = ref(false)
const templateList = ref<ConfigTemplate[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 对话框状态
const showDetailDialog = ref(false)
const showEditDialog = ref(false)
const currentTemplate = ref<ConfigTemplate | null>(null)

// 模板表单
const templateFormRef = ref<FormInstance>()
const templateForm = reactive({
  template_name: '',
  description: '',
  is_active: true,
  template_data: []
})

const isEditTemplate = computed(() => !!currentTemplate.value?.id)

// 表单验证规则
const templateRules: FormRules = {
  template_name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入模板描述', trigger: 'blur' },
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    loadTemplateList()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 加载模板列表
 */
const loadTemplateList = async () => {
  try {
    loading.value = true
    const response = await SystemConfigApi.getTemplateList({
      page: pagination.page,
      page_size: pagination.page_size
    })

    if (response.success) {
      templateList.value = response.data.templates
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取模板列表失败')
    }
  } catch (error) {
    console.error('Load template list error:', error)
    ElMessage.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 分页变化
 */
const handlePageChange = (page: number) => {
  pagination.page = page
  loadTemplateList()
}

/**
 * 页大小变化
 */
const handlePageSizeChange = (pageSize: number) => {
  pagination.page_size = pageSize
  pagination.page = 1
  loadTemplateList()
}

/**
 * 查看模板
 */
const handleViewTemplate = (template: ConfigTemplate) => {
  currentTemplate.value = template
  showDetailDialog.value = true
}

/**
 * 应用模板
 */
const handleApplyTemplate = async (template: ConfigTemplate) => {
  try {
    await ElMessageBox.confirm(
      `确认应用模板 "${template.template_name}" 吗？这将覆盖现有的相关配置。`,
      '确认应用模板',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    const response = await SystemConfigApi.applyTemplate(template.template_name)
    if (response.success) {
      ElMessage.success('模板应用成功')
      emit('apply-template')
    } else {
      ElMessage.error(response.message || '模板应用失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Apply template error:', error)
      ElMessage.error('模板应用失败')
    }
  }
}

/**
 * 创建模板
 */
const handleCreateTemplate = () => {
  currentTemplate.value = null
  Object.assign(templateForm, {
    template_name: '',
    description: '',
    is_active: true,
    template_data: []
  })
  showEditDialog.value = true
}

/**
 * 编辑模板
 */
const handleEditTemplate = (template: ConfigTemplate) => {
  currentTemplate.value = template
  Object.assign(templateForm, {
    template_name: template.template_name,
    description: template.description,
    is_active: template.is_active,
    template_data: template.template_data
  })
  showEditDialog.value = true
}

/**
 * 保存模板
 */
const handleSaveTemplate = async () => {
  if (!templateFormRef.value) return

  try {
    const valid = await templateFormRef.value.validate()
    if (!valid) return

    saving.value = true

    let response
    if (isEditTemplate.value && currentTemplate.value) {
      response = await SystemConfigApi.updateTemplate(currentTemplate.value.id, templateForm)
    } else {
      response = await SystemConfigApi.createTemplate(templateForm as any)
    }

    if (response.success) {
      ElMessage.success(isEditTemplate.value ? '更新成功' : '创建成功')
      showEditDialog.value = false
      loadTemplateList()
    } else {
      ElMessage.error(response.message || (isEditTemplate.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('Save template error:', error)
    ElMessage.error(isEditTemplate.value ? '更新失败' : '创建失败')
  } finally {
    saving.value = false
  }
}

/**
 * 删除模板
 */
const handleDeleteTemplate = async (template: ConfigTemplate) => {
  try {
    await ElMessageBox.confirm(
      `确认删除模板 "${template.template_name}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    const response = await SystemConfigApi.deleteTemplate(template.id)
    if (response.success) {
      ElMessage.success('删除成功')
      loadTemplateList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Delete template error:', error)
      ElMessage.error('删除失败')
    }
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.toolbar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.template-list {
  min-height: 400px;
}

.pagination-wrapper {
  padding: 16px 0;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 模板详情样式 */
.template-detail {
  max-height: 500px;
  overflow-y: auto;
}

.config-list {
  margin-top: 20px;
}

.config-list h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}
</style>
