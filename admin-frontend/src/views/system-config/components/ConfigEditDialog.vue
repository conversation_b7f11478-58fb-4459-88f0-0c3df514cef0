<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑配置' : '新增配置'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="配置组" prop="config_group">
        <el-select
          v-model="form.config_group"
          placeholder="请选择或输入配置组"
          filterable
          allow-create
          style="width: 100%"
        >
          <el-option
            v-for="group in configGroups"
            :key="group"
            :label="group"
            :value="group"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="配置键" prop="config_key">
        <el-input
          v-model="form.config_key"
          placeholder="请输入配置键，如：max_connections"
          clearable
          :disabled="isEdit"
        />
        <div class="form-tip">配置键应使用小写字母和下划线，创建后不可修改</div>
      </el-form-item>

      <el-form-item label="配置类型" prop="config_type">
        <el-select
          v-model="form.config_type"
          placeholder="请选择配置类型"
          style="width: 100%"
          @change="handleTypeChange"
        >
          <el-option
            v-for="type in CONFIG_TYPE_OPTIONS"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="配置值" prop="config_value">
        <!-- 字符串类型 -->
        <el-input
          v-if="form.config_type === 'string'"
          v-model="form.config_value"
          placeholder="请输入配置值"
          clearable
        />
        
        <!-- 整数类型 -->
        <el-input-number
          v-else-if="form.config_type === 'integer'"
          v-model.number="form.config_value"
          placeholder="请输入整数"
          style="width: 100%"
          :precision="0"
        />
        
        <!-- 浮点数类型 -->
        <el-input-number
          v-else-if="form.config_type === 'float'"
          v-model.number="form.config_value"
          placeholder="请输入浮点数"
          style="width: 100%"
          :precision="2"
        />
        
        <!-- 布尔类型 -->
        <el-radio-group
          v-else-if="form.config_type === 'boolean'"
          v-model="form.config_value"
        >
          <el-radio value="true">是</el-radio>
          <el-radio value="false">否</el-radio>
        </el-radio-group>
        
        <!-- JSON类型 -->
        <div v-else-if="form.config_type === 'json'" class="json-editor">
          <el-input
            v-model="form.config_value"
            type="textarea"
            :rows="6"
            placeholder="请输入有效的JSON格式"
          />
          <div class="json-actions">
            <el-button size="small" @click="formatJson">格式化</el-button>
            <el-button size="small" @click="validateJson">验证</el-button>
          </div>
        </div>
        
        <!-- 数组类型 -->
        <div v-else-if="form.config_type === 'array'" class="array-editor">
          <el-input
            v-model="form.config_value"
            type="textarea"
            :rows="4"
            placeholder="请输入数组，每行一个值"
          />
          <div class="form-tip">每行输入一个数组元素</div>
        </div>
        
        <!-- 其他类型 -->
        <el-input
          v-else
          v-model="form.config_value"
          placeholder="请输入配置值"
          clearable
        />
      </el-form-item>

      <el-form-item label="默认值" prop="default_value">
        <el-input
          v-model="form.default_value"
          placeholder="请输入默认值（可选）"
          clearable
        />
        <div class="form-tip">当配置值为空时使用的默认值</div>
      </el-form-item>

      <el-form-item label="验证规则" prop="validation_rule">
        <el-input
          v-model="form.validation_rule"
          placeholder="请输入验证规则（可选）"
          clearable
        />
        <div class="form-tip">
          支持正则表达式或范围验证，如：^[0-9]+$ 或 1-100
        </div>
      </el-form-item>

      <el-form-item label="排序" prop="display_order">
        <el-input-number
          v-model="form.display_order"
          placeholder="排序值"
          style="width: 100%"
          :min="0"
          :max="9999"
        />
        <div class="form-tip">数值越小排序越靠前</div>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入配置描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="变更原因" prop="change_reason" v-if="isEdit">
        <el-input
          v-model="form.change_reason"
          placeholder="请输入本次变更的原因"
          clearable
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-alert
        v-if="isEdit"
        title="注意"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <div>修改系统配置可能会影响系统运行，请谨慎操作并填写变更原因。</div>
        </template>
      </el-alert>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  SystemConfigApi,
  type SystemConfig,
  type SystemConfigRequest,
  CONFIG_TYPE_OPTIONS
} from '@/api/systemConfigApi'

interface Props {
  visible: boolean
  config?: SystemConfig | null
  configGroups: string[]
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<SystemConfigRequest>({
  config_group: '',
  config_key: '',
  config_value: '',
  config_type: 'string',
  description: '',
  validation_rule: '',
  default_value: '',
  display_order: 0,
  change_reason: ''
})

// 计算属性
const isEdit = computed(() => !!props.config?.id)

// 表单验证规则
const rules: FormRules = {
  config_group: [
    { required: true, message: '请选择配置组', trigger: 'change' }
  ],
  config_key: [
    { required: true, message: '请输入配置键', trigger: 'blur' },
    { 
      pattern: /^[a-z][a-z0-9_]*$/, 
      message: '配置键只能包含小写字母、数字和下划线，且以字母开头', 
      trigger: 'blur' 
    },
    { min: 2, max: 100, message: '配置键长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  config_type: [
    { required: true, message: '请选择配置类型', trigger: 'change' }
  ],
  config_value: [
    { required: true, message: '请输入配置值', trigger: 'blur' },
    { validator: validateConfigValue, trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入配置描述', trigger: 'blur' },
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ],
  validation_rule: [
    { max: 200, message: '验证规则长度不能超过 200 个字符', trigger: 'blur' }
  ],
  default_value: [
    { max: 500, message: '默认值长度不能超过 500 个字符', trigger: 'blur' }
  ],
  display_order: [
    { type: 'number', min: 0, max: 9999, message: '排序值必须在 0 到 9999 之间', trigger: 'blur' }
  ],
  change_reason: [
    { required: true, message: '请输入变更原因', trigger: 'blur', validator: (rule, value, callback) => {
      if (isEdit.value && !value) {
        callback(new Error('请输入变更原因'))
      } else {
        callback()
      }
    }}
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    if (props.config) {
      loadConfigData()
    }
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

/**
 * 配置值验证器
 */
function validateConfigValue(rule: any, value: any, callback: any) {
  if (!value && value !== 0 && value !== false) {
    callback(new Error('请输入配置值'))
    return
  }

  const type = form.config_type
  
  try {
    switch (type) {
      case 'integer':
        if (!/^-?\d+$/.test(String(value))) {
          callback(new Error('请输入有效的整数'))
          return
        }
        break
      case 'float':
        if (!/^-?\d+(\.\d+)?$/.test(String(value))) {
          callback(new Error('请输入有效的浮点数'))
          return
        }
        break
      case 'boolean':
        if (!['true', 'false'].includes(String(value))) {
          callback(new Error('布尔值只能是 true 或 false'))
          return
        }
        break
      case 'json':
        try {
          JSON.parse(String(value))
        } catch {
          callback(new Error('请输入有效的JSON格式'))
          return
        }
        break
      case 'array':
        // 简单验证数组格式
        if (typeof value === 'string' && value.trim()) {
          const lines = value.trim().split('\n')
          if (lines.length === 0) {
            callback(new Error('数组不能为空'))
            return
          }
        }
        break
    }
    callback()
  } catch (error) {
    callback(new Error('配置值格式不正确'))
  }
}

/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(form, {
    config_group: '',
    config_key: '',
    config_value: '',
    config_type: 'string',
    description: '',
    validation_rule: '',
    default_value: '',
    display_order: 0,
    change_reason: ''
  })
  formRef.value?.clearValidate()
}

/**
 * 加载配置数据
 */
const loadConfigData = () => {
  if (props.config) {
    Object.assign(form, {
      config_group: props.config.config_group,
      config_key: props.config.config_key,
      config_value: props.config.config_value,
      config_type: props.config.config_type,
      description: props.config.description,
      validation_rule: props.config.validation_rule || '',
      default_value: props.config.default_value || '',
      display_order: props.config.display_order,
      change_reason: ''
    })
  }
}

/**
 * 配置类型变化处理
 */
const handleTypeChange = (type: string) => {
  // 根据类型设置默认值
  switch (type) {
    case 'boolean':
      form.config_value = 'false'
      break
    case 'integer':
      form.config_value = '0'
      break
    case 'float':
      form.config_value = '0.0'
      break
    case 'json':
      form.config_value = '{}'
      break
    case 'array':
      form.config_value = ''
      break
    default:
      form.config_value = ''
  }
}

/**
 * 格式化JSON
 */
const formatJson = () => {
  try {
    const parsed = JSON.parse(form.config_value)
    form.config_value = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式不正确')
  }
}

/**
 * 验证JSON
 */
const validateJson = () => {
  try {
    JSON.parse(form.config_value)
    ElMessage.success('JSON格式正确')
  } catch (error) {
    ElMessage.error('JSON格式不正确')
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    // 处理数组类型的值
    let configValue = form.config_value
    if (form.config_type === 'array' && typeof configValue === 'string') {
      const lines = configValue.trim().split('\n').filter(line => line.trim())
      configValue = JSON.stringify(lines)
    }

    const submitData = {
      ...form,
      config_value: String(configValue)
    }

    let response
    if (isEdit.value && props.config) {
      response = await SystemConfigApi.updateConfig(props.config.id, submitData)
    } else {
      response = await SystemConfigApi.createConfig(submitData)
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error: any) {
    console.error('Submit config error:', error)
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.json-editor {
  width: 100%;
}

.json-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.array-editor {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-alert) {
  margin-top: 16px;
}
</style>
