<template>
  <div class="system-config-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">系统配置管理</h2>
      <p class="page-description">管理系统运行时配置参数，支持分组管理、版本控制和模板应用</p>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" @click="handleCreate" :icon="Plus">
          新增配置
        </el-button>
        <el-button @click="handleRefreshCache" :icon="Refresh" :loading="refreshingCache">
          刷新缓存
        </el-button>
        <el-button @click="handleCreateBackup" :icon="Download">
          创建备份
        </el-button>
        <el-dropdown @command="handleBatchAction">
          <el-button :icon="Operation">
            批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="batchUpdate" :disabled="selectedConfigs.length === 0">
                批量更新
              </el-dropdown-item>
              <el-dropdown-item command="batchDelete" :disabled="selectedConfigs.length === 0">
                批量删除
              </el-dropdown-item>
              <el-dropdown-item command="export" :disabled="selectedConfigs.length === 0">
                导出配置
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="toolbar-right">
        <el-button @click="showChangeLogDialog = true" :icon="Document">
          变更日志
        </el-button>
        <el-button @click="showTemplateDialog = true" :icon="Files">
          配置模板
        </el-button>
        <el-button @click="showBackupDialog = true" :icon="FolderOpened">
          配置备份
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="配置组">
          <el-select
            v-model="searchForm.config_group"
            placeholder="选择配置组"
            clearable
            style="width: 180px"
            @change="handleSearch"
          >
            <el-option
              v-for="group in configGroups"
              :key="group"
              :label="group"
              :value="group"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配置类型">
          <el-select
            v-model="searchForm.config_type"
            placeholder="选择配置类型"
            clearable
            style="width: 150px"
            @change="handleSearch"
          >
            <el-option
              v-for="type in CONFIG_TYPE_OPTIONS"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.is_active"
            placeholder="选择状态"
            clearable
            style="width: 120px"
            @change="handleSearch"
          >
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索配置键或描述"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
            @clear="handleSearch"
          >
            <template #append>
              <el-button :icon="Search" @click="handleSearch" />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleReset" :icon="RefreshRight">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 配置列表 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="configList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="config_group" label="配置组" width="120" sortable="custom">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.config_group }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="config_key" label="配置键" width="200" sortable="custom" show-overflow-tooltip />
        <el-table-column prop="config_value" label="配置值" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="config-value">
              <span v-if="row.config_type === 'boolean'">
                <el-tag :type="row.config_value === 'true' ? 'success' : 'danger'" size="small">
                  {{ row.config_value === 'true' ? '是' : '否' }}
                </el-tag>
              </span>
              <span v-else-if="row.config_type === 'json'">
                <el-button type="primary" link size="small" @click="showJsonDialog(row.config_value)">
                  查看JSON
                </el-button>
              </span>
              <span v-else class="value-text">{{ row.config_value }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="config_type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getConfigTypeTag(row.config_type)" size="small">
              {{ getConfigTypeLabel(row.config_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
        <el-table-column prop="display_order" label="排序" width="80" sortable="custom" />
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleToggleStatus(row)"
              :disabled="row.is_readonly"
            />
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="primary" link size="small" @click="handleViewHistory(row)">
              历史
            </el-button>
            <el-button type="primary" link size="small" @click="handleCopy(row)">
              复制
            </el-button>
            <el-button 
              type="danger" 
              link 
              size="small" 
              @click="handleDelete(row)"
              :disabled="row.is_readonly"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 配置编辑对话框 -->
    <ConfigEditDialog
      v-model:visible="showEditDialog"
      :config="currentConfig"
      :config-groups="configGroups"
      @success="handleEditSuccess"
    />

    <!-- JSON查看对话框 -->
    <JsonViewDialog
      v-model:visible="showJsonViewDialog"
      :json-data="currentJsonData"
    />

    <!-- 变更日志对话框 -->
    <ChangeLogDialog
      v-model:visible="showChangeLogDialog"
    />

    <!-- 配置模板对话框 -->
    <TemplateDialog
      v-model:visible="showTemplateDialog"
      @apply-template="handleApplyTemplate"
    />

    <!-- 配置备份对话框 -->
    <BackupDialog
      v-model:visible="showBackupDialog"
      @restore-backup="handleRestoreBackup"
    />

    <!-- 批量更新对话框 -->
    <BatchUpdateDialog
      v-model:visible="showBatchUpdateDialog"
      :configs="selectedConfigs"
      @success="handleBatchUpdateSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Download,
  Operation,
  ArrowDown,
  Document,
  Files,
  FolderOpened,
  Search,
  RefreshRight
} from '@element-plus/icons-vue'

import {
  SystemConfigApi,
  type SystemConfig,
  type SystemConfigListRequest,
  CONFIG_TYPE_OPTIONS
} from '@/api/systemConfigApi'

// 导入对话框组件
import ConfigEditDialog from './components/ConfigEditDialog.vue'
import JsonViewDialog from './components/JsonViewDialog.vue'
import ChangeLogDialog from './components/ChangeLogDialog.vue'
import TemplateDialog from './components/TemplateDialog.vue'
import BackupDialog from './components/BackupDialog.vue'
import BatchUpdateDialog from './components/BatchUpdateDialog.vue'

// ==================== 响应式数据 ====================

const loading = ref(false)
const refreshingCache = ref(false)
const configList = ref<SystemConfig[]>([])
const selectedConfigs = ref<SystemConfig[]>([])
const configGroups = ref<string[]>([])

// 搜索表单
const searchForm = reactive<SystemConfigListRequest>({
  page: 1,
  page_size: 20,
  config_group: '',
  keyword: '',
  config_type: undefined,
  is_active: undefined,
  order_by: 'display_order',
  order: 'asc'
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0,
  total_pages: 0
})

// 对话框状态
const showEditDialog = ref(false)
const showJsonViewDialog = ref(false)
const showChangeLogDialog = ref(false)
const showTemplateDialog = ref(false)
const showBackupDialog = ref(false)
const showBatchUpdateDialog = ref(false)

// 当前操作的数据
const currentConfig = ref<SystemConfig | null>(null)
const currentJsonData = ref('')

// ==================== 计算属性 ====================

const hasSelectedConfigs = computed(() => selectedConfigs.value.length > 0)

// ==================== 生命周期 ====================

onMounted(() => {
  loadConfigList()
  loadConfigGroups()
})

// ==================== 方法 ====================

/**
 * 加载配置列表
 */
const loadConfigList = async () => {
  try {
    loading.value = true
    const response = await SystemConfigApi.getConfigList({
      ...searchForm,
      page: pagination.page,
      page_size: pagination.page_size
    })

    if (response.success) {
      configList.value = response.data.configs
      pagination.total = response.data.total
      pagination.total_pages = response.data.total_pages
    } else {
      ElMessage.error(response.message || '获取配置列表失败')
    }
  } catch (error) {
    console.error('Load config list error:', error)
    ElMessage.error('获取配置列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 加载配置组列表
 */
const loadConfigGroups = async () => {
  try {
    const response = await SystemConfigApi.getConfigGroups()
    if (response.success) {
      configGroups.value = response.data
    }
  } catch (error) {
    console.error('Load config groups error:', error)
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.page = 1
  loadConfigList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.assign(searchForm, {
    page: 1,
    page_size: 20,
    config_group: '',
    keyword: '',
    config_type: undefined,
    is_active: undefined,
    order_by: 'display_order',
    order: 'asc'
  })
  pagination.page = 1
  loadConfigList()
}

/**
 * 分页变化
 */
const handlePageChange = (page: number) => {
  pagination.page = page
  loadConfigList()
}

/**
 * 页大小变化
 */
const handlePageSizeChange = (pageSize: number) => {
  pagination.page_size = pageSize
  pagination.page = 1
  loadConfigList()
}

/**
 * 排序变化
 */
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  searchForm.order_by = prop
  searchForm.order = order === 'ascending' ? 'asc' : 'desc'
  loadConfigList()
}

/**
 * 选择变化
 */
const handleSelectionChange = (selection: SystemConfig[]) => {
  selectedConfigs.value = selection
}

/**
 * 创建配置
 */
const handleCreate = () => {
  currentConfig.value = null
  showEditDialog.value = true
}

/**
 * 编辑配置
 */
const handleEdit = (config: SystemConfig) => {
  currentConfig.value = { ...config }
  showEditDialog.value = true
}

/**
 * 编辑成功
 */
const handleEditSuccess = () => {
  loadConfigList()
}

/**
 * 复制配置
 */
const handleCopy = (config: SystemConfig) => {
  const newConfig = {
    ...config,
    config_key: `${config.config_key}_copy`,
    description: `${config.description} (副本)`
  }
  delete (newConfig as any).id
  delete (newConfig as any).created_at
  delete (newConfig as any).updated_at
  
  currentConfig.value = newConfig as SystemConfig
  showEditDialog.value = true
}

/**
 * 删除配置
 */
const handleDelete = async (config: SystemConfig) => {
  try {
    await ElMessageBox.confirm(
      `确认删除配置 "${config.config_key}" 吗？此操作不可撤销。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    const response = await SystemConfigApi.deleteConfig(config.id)
    if (response.success) {
      ElMessage.success('删除成功')
      loadConfigList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Delete config error:', error)
      ElMessage.error('删除失败')
    }
  }
}

/**
 * 切换状态
 */
const handleToggleStatus = async (config: SystemConfig) => {
  try {
    const response = await SystemConfigApi.updateConfig(config.id, {
      config_group: config.config_group,
      config_key: config.config_key,
      config_value: config.config_value,
      config_type: config.config_type,
      description: config.description,
      validation_rule: config.validation_rule,
      default_value: config.default_value,
      display_order: config.display_order,
      change_reason: `${config.is_active ? '启用' : '禁用'}配置`
    })

    if (response.success) {
      ElMessage.success(`${config.is_active ? '启用' : '禁用'}成功`)
    } else {
      // 恢复状态
      config.is_active = !config.is_active
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    // 恢复状态
    config.is_active = !config.is_active
    console.error('Toggle status error:', error)
    ElMessage.error('操作失败')
  }
}

/**
 * 查看历史
 */
const handleViewHistory = (config: SystemConfig) => {
  // 打开变更日志对话框，并筛选该配置的记录
  showChangeLogDialog.value = true
  // TODO: 传递配置信息给变更日志组件
}

/**
 * 显示JSON对话框
 */
const showJsonDialog = (jsonData: string) => {
  currentJsonData.value = jsonData
  showJsonViewDialog.value = true
}

/**
 * 刷新缓存
 */
const handleRefreshCache = async () => {
  try {
    refreshingCache.value = true
    const response = await SystemConfigApi.refreshCache()
    
    if (response.success) {
      ElMessage.success('缓存刷新成功')
    } else {
      ElMessage.error(response.message || '缓存刷新失败')
    }
  } catch (error) {
    console.error('Refresh cache error:', error)
    ElMessage.error('缓存刷新失败')
  } finally {
    refreshingCache.value = false
  }
}

/**
 * 创建备份
 */
const handleCreateBackup = () => {
  showBackupDialog.value = true
}

/**
 * 批量操作
 */
const handleBatchAction = (command: string) => {
  switch (command) {
    case 'batchUpdate':
      showBatchUpdateDialog.value = true
      break
    case 'batchDelete':
      handleBatchDelete()
      break
    case 'export':
      handleExportConfigs()
      break
  }
}

/**
 * 批量删除
 */
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedConfigs.value.length} 个配置吗？此操作不可撤销。`,
      '确认批量删除',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }
    )

    // 逐个删除（后端可能没有批量删除接口）
    const deletePromises = selectedConfigs.value.map(config => 
      SystemConfigApi.deleteConfig(config.id)
    )

    await Promise.all(deletePromises)
    ElMessage.success('批量删除成功')
    loadConfigList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Batch delete error:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

/**
 * 导出配置
 */
const handleExportConfigs = () => {
  const data = selectedConfigs.value.map(config => ({
    配置组: config.config_group,
    配置键: config.config_key,
    配置值: config.config_value,
    配置类型: getConfigTypeLabel(config.config_type),
    描述: config.description,
    默认值: config.default_value,
    排序: config.display_order,
    状态: config.is_active ? '启用' : '禁用',
    更新时间: formatDateTime(config.updated_at)
  }))

  // 简单的CSV导出
  const csvContent = [
    Object.keys(data[0]).join(','),
    ...data.map(row => Object.values(row).join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `system_configs_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
}

/**
 * 批量更新成功
 */
const handleBatchUpdateSuccess = () => {
  loadConfigList()
}

/**
 * 应用模板
 */
const handleApplyTemplate = () => {
  loadConfigList()
}

/**
 * 恢复备份
 */
const handleRestoreBackup = () => {
  loadConfigList()
}

// ==================== 工具方法 ====================

/**
 * 获取配置类型标签
 */
const getConfigTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    string: 'primary',
    integer: 'success',
    float: 'warning',
    boolean: 'info',
    json: 'danger',
    array: 'default'
  }
  return typeMap[type] || 'default'
}

/**
 * 获取配置类型标签文本
 */
const getConfigTypeLabel = (type: string) => {
  const option = CONFIG_TYPE_OPTIONS.find(opt => opt.value === type)
  return option?.label || type
}

/**
 * 格式化日期时间
 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}
</script>

<style scoped>
.system-config-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 12px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.config-value {
  max-width: 200px;
}

.value-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-wrapper {
  padding: 16px;
  display: flex;
  justify-content: center;
}

:deep(.el-table) {
  border-radius: 0;
}

:deep(.el-table__header) {
  background: #f8fafc;
}

:deep(.el-table th) {
  background: #f8fafc !important;
}
</style>
