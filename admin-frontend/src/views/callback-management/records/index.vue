<template>
  <div class="callback-records-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">回调记录管理</h1>
        <p class="page-description">管理和监控系统中的所有回调记录</p>
      </div>
      <div class="header-right">
        <el-button type="success" :icon="Refresh" @click="handleRefresh">
          刷新数据
        </el-button>
        <el-button type="primary" :icon="Download" @click="handleExport">
          导出记录
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户ID/用户名">
          <el-input
            v-model="searchForm.user_id"
            placeholder="请输入用户ID或用户名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="供应商">
          <el-select
            v-model="searchForm.provider"
            placeholder="请选择供应商"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="云通" value="yuntong" />
            <el-option label="易达" value="yida" />
            <el-option label="快递100" value="kuaidi100" />
          </el-select>
        </el-form-item>
        <el-form-item label="事件类型">
          <el-select
            v-model="searchForm.event_type"
            placeholder="请选择事件类型"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="订单状态变更" value="order_status_changed" />
            <el-option label="计费更新" value="billing_updated" />
            <el-option label="工单回复" value="ticket_replied" />
          </el-select>
        </el-form-item>
        <el-form-item label="内部状态">
          <el-select
            v-model="searchForm.internal_status"
            placeholder="请选择内部状态"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="处理中" value="pending" />
          </el-select>
        </el-form-item>
        <el-form-item label="外部状态">
          <el-select
            v-model="searchForm.external_status"
            placeholder="请选择外部状态"
            clearable
            style="width: 120px"
          >
            <el-option label="全部" value="" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="处理中" value="pending" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.order_no"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="快递单号">
          <el-input
            v-model="searchForm.tracking_no"
            placeholder="请输入快递单号"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item label="排序方式">
          <el-select
            v-model="searchForm.sort_by"
            style="width: 120px"
          >
            <el-option label="接收时间" value="received_at" />
            <el-option label="创建时间" value="created_at" />
            <el-option label="更新时间" value="updated_at" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序">
          <el-select
            v-model="searchForm.sort_order"
            style="width: 100px"
          >
            <el-option label="降序" value="desc" />
            <el-option label="升序" value="asc" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            搜索
          </el-button>
          <el-button @click="handleResetSearch">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 回调记录列表 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">回调记录列表</div>
        <div class="table-actions">
          <el-button
            type="warning"
            size="small"
            :disabled="selectedRecords.length === 0"
            @click="handleBatchRetry"
          >
            批量重试 ({{ selectedRecords.length }})
          </el-button>
        </div>
      </div>
      
      <el-table
        :data="recordList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="provider" label="供应商" width="100">
          <template #default="{ row }">
            <el-tag :type="getProviderTagType(row.provider)" size="small">
              {{ getProviderLabel(row.provider) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="event_type" label="事件类型" width="120">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ getEventTypeLabel(row.event_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="order_no" label="订单号" min-width="150" show-overflow-tooltip />
        <el-table-column prop="tracking_no" label="快递单号" min-width="150" show-overflow-tooltip />
        <el-table-column label="用户" width="120" show-overflow-tooltip>
          <template #default="{ row }">
            <span :title="`用户名: ${row.username || '未知'}\n用户ID: ${row.user_id}`">
              {{ row.username || row.user_id }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="internal_status" label="内部状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.internal_status)" size="small">
              {{ getStatusLabel(row.internal_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="external_status" label="外部状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.external_status)" size="small">
              {{ getStatusLabel(row.external_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="retry_count" label="重试次数" width="100">
          <template #default="{ row }">
            <el-tag :type="row.retry_count > 0 ? 'warning' : 'info'" size="small">
              {{ row.retry_count }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="received_at" label="接收时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.received_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewRecord(row)"
            >
              查看详情
            </el-button>
            <el-button
              v-if="row.internal_status === 'failed' || row.external_status === 'failed'"
              type="warning"
              size="small"
              @click="handleRetryRecord(row)"
            >
              重试
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 回调记录详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="回调记录详情"
      width="1000px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="记录ID">
            {{ selectedRecord.id }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商">
            <el-tag :type="getProviderTagType(selectedRecord.provider)" size="small">
              {{ getProviderLabel(selectedRecord.provider) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="回调类型">
            {{ selectedRecord.callback_type }}
          </el-descriptions-item>
          <el-descriptions-item label="事件类型">
            <el-tag type="info" size="small">
              {{ getEventTypeLabel(selectedRecord.event_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="订单号">
            {{ selectedRecord.order_no }}
          </el-descriptions-item>
          <el-descriptions-item label="客户订单号">
            {{ selectedRecord.customer_order_no }}
          </el-descriptions-item>
          <el-descriptions-item label="快递单号">
            {{ selectedRecord.tracking_no }}
          </el-descriptions-item>
          <el-descriptions-item label="用户">
            <div>
              <div><strong>用户名:</strong> {{ selectedRecord.username || '未知' }}</div>
              <div><strong>用户ID:</strong> {{ selectedRecord.user_id }}</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="内部状态">
            <el-tag :type="getStatusTagType(selectedRecord.internal_status)" size="small">
              {{ getStatusLabel(selectedRecord.internal_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="外部状态">
            <el-tag :type="getStatusTagType(selectedRecord.external_status)" size="small">
              {{ getStatusLabel(selectedRecord.external_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="重试次数">
            <el-tag :type="selectedRecord.retry_count > 0 ? 'warning' : 'info'" size="small">
              {{ selectedRecord.retry_count }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="接收时间">
            {{ formatDateTime(selectedRecord.received_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="内部处理时间">
            {{ selectedRecord.internal_processed_at ? formatDateTime(selectedRecord.internal_processed_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="外部处理时间">
            {{ selectedRecord.external_processed_at ? formatDateTime(selectedRecord.external_processed_at) : '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="detail-section" style="margin-top: 20px;">
          <h4>错误信息</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="error-info">
                <h5>内部错误</h5>
                <el-input
                  v-model="selectedRecord.internal_error"
                  type="textarea"
                  :rows="3"
                  readonly
                  placeholder="无错误信息"
                />
              </div>
            </el-col>
            <el-col :span="12">
              <div class="error-info">
                <h5>外部错误</h5>
                <el-input
                  v-model="selectedRecord.external_error"
                  type="textarea"
                  :rows="3"
                  readonly
                  placeholder="无错误信息"
                />
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="detail-section" style="margin-top: 20px;">
          <h4>原始数据</h4>
          <el-input
            :model-value="formatJSON(selectedRecord.raw_data)"
            type="textarea"
            :rows="8"
            readonly
            placeholder="无原始数据"
          />
        </div>

        <div class="detail-section" style="margin-top: 20px;">
          <h4>标准化数据</h4>
          <el-input
            :model-value="formatJSON(selectedRecord.standardized_data)"
            type="textarea"
            :rows="8"
            readonly
            placeholder="无标准化数据"
          />
        </div>
      </div>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="selectedRecord && (selectedRecord.internal_status === 'failed' || selectedRecord.external_status === 'failed')"
          type="warning"
          @click="handleRetryRecord(selectedRecord)"
        >
          重试回调
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Download,
  Search
} from '@element-plus/icons-vue'
import {
  AdminCallbackManagementService,
  type AdminCallbackRecord,
  type CallbackRecordListParams
} from '@/api/adminCallbackApi'

// 响应式数据
const loading = ref(false)
const recordList = ref<AdminCallbackRecord[]>([])
const selectedRecord = ref<AdminCallbackRecord | null>(null)
const selectedRecords = ref<AdminCallbackRecord[]>([])
const dateRange = ref<[string, string] | null>(null)

// 搜索表单
const searchForm = reactive({
  user_id: '',
  provider: '',
  event_type: '',
  order_no: '',
  tracking_no: '',
  internal_status: '',
  external_status: '',
  sort_by: 'received_at',
  sort_order: 'desc' as 'asc' | 'desc'
})

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 对话框状态
const detailDialogVisible = ref(false)

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 格式化JSON
const formatJSON = (data: any) => {
  if (!data) return ''
  try {
    return JSON.stringify(data, null, 2)
  } catch {
    return String(data)
  }
}

// 获取供应商标签样式
const getProviderTagType = (provider: string) => {
  const typeMap: Record<string, string> = {
    yuntong: 'primary',
    yida: 'success',
    kuaidi100: 'warning'
  }
  return typeMap[provider] || 'info'
}

// 获取供应商标签文本
const getProviderLabel = (provider: string) => {
  const labelMap: Record<string, string> = {
    yuntong: '云通',
    yida: '易达',
    kuaidi100: '快递100'
  }
  return labelMap[provider] || provider
}

// 获取事件类型标签文本
const getEventTypeLabel = (eventType: string) => {
  const labelMap: Record<string, string> = {
    order_status_changed: '订单状态变更',
    billing_updated: '计费更新',
    ticket_replied: '工单回复'
  }
  return labelMap[eventType] || eventType
}

// 获取状态标签样式
const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    success: 'success',
    failed: 'danger',
    pending: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    success: '成功',
    failed: '失败',
    pending: '处理中'
  }
  return labelMap[status] || status
}

// 加载回调记录列表 - 使用真实后端API
const loadRecordList = async () => {
  try {
    loading.value = true

    const params: CallbackRecordListParams = {
      page: pagination.page,
      page_size: pagination.page_size,
      user_id: searchForm.user_id,
      provider: searchForm.provider,
      event_type: searchForm.event_type,
      order_no: searchForm.order_no,
      tracking_no: searchForm.tracking_no,
      internal_status: searchForm.internal_status,
      external_status: searchForm.external_status,
      order_by: searchForm.sort_by,
      order: searchForm.sort_order
    }

    // 处理时间范围
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = dateRange.value[0]
      params.end_time = dateRange.value[1]
    }

    const result = await AdminCallbackManagementService.getCallbackRecords(params)

    recordList.value = result.records
    pagination.total = result.total
    pagination.page = result.page
    pagination.page_size = result.page_size

  } catch (error: any) {
    console.error('Load callback records failed:', error)
    ElMessage.error(error.message || '加载回调记录失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadRecordList()
}

const handleResetSearch = () => {
  Object.assign(searchForm, {
    user_id: '',
    provider: '',
    event_type: '',
    order_no: '',
    tracking_no: '',
    internal_status: '',
    external_status: '',
    sort_by: 'received_at',
    sort_order: 'desc'
  })
  dateRange.value = null
  handleSearch()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.page_size = size
  pagination.page = 1
  loadRecordList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadRecordList()
}

// 回调记录操作
const handleViewRecord = (record: AdminCallbackRecord) => {
  selectedRecord.value = record
  detailDialogVisible.value = true
}

const handleRetryRecord = async (record: AdminCallbackRecord) => {
  try {
    await ElMessageBox.confirm(
      `确定要重试回调记录 "${record.id}" 吗？`,
      '确认重试',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await AdminCallbackManagementService.retryCallback(record.id)
    ElMessage.success('回调重试已启动')

    // 重新加载列表
    await loadRecordList()

    // 如果详情对话框打开，关闭它
    if (detailDialogVisible.value) {
      detailDialogVisible.value = false
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Retry callback failed:', error)
      ElMessage.error(error.message || '重试回调失败')
    }
  }
}

const handleSelectionChange = (selection: AdminCallbackRecord[]) => {
  selectedRecords.value = selection
}

const handleBatchRetry = async () => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请选择要重试的回调记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要批量重试选中的 ${selectedRecords.value.length} 条回调记录吗？`,
      '确认批量重试',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const recordIds = selectedRecords.value.map(record => record.id)
    await AdminCallbackManagementService.batchRetryCallbacks(recordIds)
    ElMessage.success(`成功启动 ${recordIds.length} 条回调记录的重试`)

    // 重新加载列表
    await loadRecordList()

    // 清空选择
    selectedRecords.value = []
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Batch retry callbacks failed:', error)
      ElMessage.error(error.message || '批量重试回调失败')
    }
  }
}

const handleRefresh = () => {
  loadRecordList()
}

const handleExport = async () => {
  try {
    const params: CallbackRecordListParams = {
      user_id: searchForm.user_id,
      provider: searchForm.provider,
      event_type: searchForm.event_type,
      order_no: searchForm.order_no,
      tracking_no: searchForm.tracking_no,
      internal_status: searchForm.internal_status,
      external_status: searchForm.external_status
    }

    // 处理时间范围
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = dateRange.value[0]
      params.end_time = dateRange.value[1]
    }

    const blob = await AdminCallbackManagementService.exportCallbackRecords(params)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `callback_records_${new Date().toISOString().slice(0, 10)}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('回调记录导出成功')
  } catch (error: any) {
    console.error('Export callback records failed:', error)
    ElMessage.error(error.message || '导出回调记录失败')
  }
}

// 初始化
onMounted(() => {
  loadRecordList()
})
</script>

<style scoped>
.callback-records-management {
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.search-card {
  margin-bottom: 20px;
  width: 100%;
}

.search-card :deep(.el-card__body) {
  padding: 20px;
}

.table-card {
  width: 100%;
}

.table-card :deep(.el-card__body) {
  padding: 0;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.table-card :deep(.el-table) {
  border-radius: 0;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #ebeef5;
}

.record-detail {
  padding: 0;
}

.detail-section {
  margin-top: 20px;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.detail-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.error-info {
  margin-bottom: 16px;
}

/* 确保表格和分页组件占满全宽 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-pagination) {
  width: 100%;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .search-card :deep(.el-form) {
    flex-direction: column;
  }

  .search-card :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .table-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
