<template>
  <div class="callback-statistics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">回调统计监控</h1>
        <p class="page-description">监控回调系统的运行状态和性能指标</p>
      </div>
      <div class="header-right">
        <el-button type="success" :icon="Refresh" @click="handleRefresh">
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <el-form :model="filterForm" inline>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item label="供应商">
          <el-select
            v-model="filterForm.provider"
            placeholder="请选择供应商"
            clearable
            style="width: 150px"
            @change="handleFilterChange"
          >
            <el-option label="全部" value="" />
            <el-option label="云通" value="yuntong" />
            <el-option label="易达" value="yida" />
            <el-option label="快递100" value="kuaidi100" />
          </el-select>
        </el-form-item>
        <el-form-item label="事件类型">
          <el-select
            v-model="filterForm.event_type"
            placeholder="请选择事件类型"
            clearable
            style="width: 150px"
            @change="handleFilterChange"
          >
            <el-option label="全部" value="" />
            <el-option label="订单状态变更" value="order_status_changed" />
            <el-option label="计费更新" value="billing_updated" />
            <el-option label="工单回复" value="ticket_replied" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input
            v-model="filterForm.user_id"
            placeholder="请输入用户ID"
            clearable
            style="width: 200px"
            @keyup.enter="handleFilterChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilterChange">
            查询
          </el-button>
          <el-button @click="handleResetFilter">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计概览卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.total_records || 0 }}</div>
              <div class="stat-label">总回调数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.success_records || 0 }}</div>
              <div class="stat-label">成功回调</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon danger">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ statistics.failed_records || 0 }}</div>
              <div class="stat-label">失败回调</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon warning">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatSuccessRate(statistics.success_rate) }}</div>
              <div class="stat-label">成功率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 供应商统计 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>供应商回调统计</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="providerChartRef" class="chart" style="height: 300px;"></div>
          </div>
        </el-card>
      </el-col>

      <!-- 事件类型统计 -->
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>事件类型统计</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="eventTypeChartRef" class="chart" style="height: 300px;"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 趋势图表 -->
    <el-row :gutter="20" class="trend-section">
      <el-col :span="24">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>回调趋势图</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="trendChartRef" class="chart" style="height: 400px;"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>详细统计数据</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="供应商统计" name="provider">
          <el-table :data="providerStatsData" stripe border>
            <el-table-column prop="provider" label="供应商" width="120">
              <template #default="{ row }">
                <el-tag :type="getProviderTagType(row.provider)" size="small">
                  {{ getProviderLabel(row.provider) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="total" label="总数" width="100" />
            <el-table-column prop="success" label="成功" width="100" />
            <el-table-column prop="failed" label="失败" width="100" />
            <el-table-column prop="pending" label="处理中" width="100" />
            <el-table-column label="成功率" width="120">
              <template #default="{ row }">
                {{ formatSuccessRate(row.total > 0 ? (row.success / row.total) * 100 : 0) }}
              </template>
            </el-table-column>
            <el-table-column label="状态分布" min-width="200">
              <template #default="{ row }">
                <div class="status-progress">
                  <el-progress
                    :percentage="row.total > 0 ? (row.success / row.total) * 100 : 0"
                    :stroke-width="8"
                    :show-text="false"
                    status="success"
                  />
                  <span class="progress-text">
                    成功: {{ row.success }} | 失败: {{ row.failed }} | 处理中: {{ row.pending }}
                  </span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="事件类型统计" name="event_type">
          <el-table :data="eventTypeStatsData" stripe border>
            <el-table-column prop="event_type" label="事件类型" width="150">
              <template #default="{ row }">
                <el-tag type="info" size="small">
                  {{ getEventTypeLabel(row.event_type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="total" label="总数" width="100" />
            <el-table-column prop="success" label="成功" width="100" />
            <el-table-column prop="failed" label="失败" width="100" />
            <el-table-column prop="pending" label="处理中" width="100" />
            <el-table-column label="成功率" width="120">
              <template #default="{ row }">
                {{ formatSuccessRate(row.total > 0 ? (row.success / row.total) * 100 : 0) }}
              </template>
            </el-table-column>
            <el-table-column label="状态分布" min-width="200">
              <template #default="{ row }">
                <div class="status-progress">
                  <el-progress
                    :percentage="row.total > 0 ? (row.success / row.total) * 100 : 0"
                    :stroke-width="8"
                    :show-text="false"
                    status="success"
                  />
                  <span class="progress-text">
                    成功: {{ row.success }} | 失败: {{ row.failed }} | 处理中: {{ row.pending }}
                  </span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="每日统计" name="daily">
          <el-table :data="dailyStatsData" stripe border max-height="400">
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="total" label="总数" width="100" />
            <el-table-column prop="success" label="成功" width="100" />
            <el-table-column prop="failed" label="失败" width="100" />
            <el-table-column prop="pending" label="处理中" width="100" />
            <el-table-column label="成功率" width="120">
              <template #default="{ row }">
                {{ formatSuccessRate(row.total > 0 ? (row.success / row.total) * 100 : 0) }}
              </template>
            </el-table-column>
            <el-table-column label="状态分布" min-width="200">
              <template #default="{ row }">
                <div class="status-progress">
                  <el-progress
                    :percentage="row.total > 0 ? (row.success / row.total) * 100 : 0"
                    :stroke-width="8"
                    :show-text="false"
                    status="success"
                  />
                  <span class="progress-text">
                    成功: {{ row.success }} | 失败: {{ row.failed }} | 处理中: {{ row.pending }}
                  </span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Check,
  CircleCheck,
  CircleClose,
  Clock
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  AdminCallbackManagementService,
  type CallbackStatistics,
  type CallbackStatisticsParams
} from '@/api/adminCallbackApi'

// 响应式数据
const loading = ref(false)
const statistics = ref<CallbackStatistics>({
  total_records: 0,
  success_records: 0,
  failed_records: 0,
  pending_records: 0,
  success_rate: 0,
  avg_processing_time: 0,
  provider_stats: {},
  event_type_stats: {},
  daily_stats: []
})

const dateRange = ref<[string, string] | null>(null)
const activeTab = ref('provider')

// 筛选表单
const filterForm = reactive({
  provider: '',
  event_type: '',
  user_id: ''
})

// 图表引用
const providerChartRef = ref<HTMLDivElement>()
const eventTypeChartRef = ref<HTMLDivElement>()
const trendChartRef = ref<HTMLDivElement>()

// 图表实例
let providerChart: echarts.ECharts | null = null
let eventTypeChart: echarts.ECharts | null = null
let trendChart: echarts.ECharts | null = null

// 计算属性
const providerStatsData = computed(() => {
  return Object.entries(statistics.value.provider_stats || {}).map(([provider, stats]) => ({
    provider,
    ...stats
  }))
})

const eventTypeStatsData = computed(() => {
  return Object.entries(statistics.value.event_type_stats || {}).map(([event_type, stats]) => ({
    event_type,
    ...stats
  }))
})

const dailyStatsData = computed(() => {
  return statistics.value.daily_stats || []
})

// 工具函数
const formatSuccessRate = (rate: number) => {
  return `${rate.toFixed(2)}%`
}

const getProviderTagType = (provider: string) => {
  const typeMap: Record<string, string> = {
    yuntong: 'primary',
    yida: 'success',
    kuaidi100: 'warning'
  }
  return typeMap[provider] || 'info'
}

const getProviderLabel = (provider: string) => {
  const labelMap: Record<string, string> = {
    yuntong: '云通',
    yida: '易达',
    kuaidi100: '快递100'
  }
  return labelMap[provider] || provider
}

const getEventTypeLabel = (eventType: string) => {
  const labelMap: Record<string, string> = {
    order_status_changed: '订单状态变更',
    billing_updated: '计费更新',
    ticket_replied: '工单回复'
  }
  return labelMap[eventType] || eventType
}

// 加载统计数据
const loadStatistics = async () => {
  try {
    loading.value = true

    const params: CallbackStatisticsParams = {
      provider: filterForm.provider,
      event_type: filterForm.event_type,
      user_id: filterForm.user_id
    }

    // 处理时间范围
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_time = dateRange.value[0]
      params.end_time = dateRange.value[1]
    }

    const result = await AdminCallbackManagementService.getCallbackStatistics(params)
    statistics.value = result

    // 更新图表
    await nextTick()
    updateCharts()

  } catch (error: any) {
    console.error('Load callback statistics failed:', error)
    ElMessage.error(error.message || '加载回调统计失败')
  } finally {
    loading.value = false
  }
}

// 初始化图表
const initCharts = () => {
  if (providerChartRef.value) {
    providerChart = echarts.init(providerChartRef.value)
  }
  if (eventTypeChartRef.value) {
    eventTypeChart = echarts.init(eventTypeChartRef.value)
  }
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    providerChart?.resize()
    eventTypeChart?.resize()
    trendChart?.resize()
  })
}

// 更新图表
const updateCharts = () => {
  updateProviderChart()
  updateEventTypeChart()
  updateTrendChart()
}

// 更新供应商图表
const updateProviderChart = () => {
  if (!providerChart) return

  const data = providerStatsData.value.map(item => ({
    name: getProviderLabel(item.provider),
    value: item.total
  }))

  const option = {
    title: {
      text: '供应商分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.map(item => item.name)
    },
    series: [
      {
        name: '回调数量',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  providerChart.setOption(option)
}

// 更新事件类型图表
const updateEventTypeChart = () => {
  if (!eventTypeChart) return

  const data = eventTypeStatsData.value.map(item => ({
    name: getEventTypeLabel(item.event_type),
    value: item.total
  }))

  const option = {
    title: {
      text: '事件类型分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: data.map(item => item.name)
    },
    series: [
      {
        name: '回调数量',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  eventTypeChart.setOption(option)
}

// 更新趋势图表
const updateTrendChart = () => {
  if (!trendChart) return

  const dates = dailyStatsData.value.map(item => item.date)
  const totalData = dailyStatsData.value.map(item => item.total)
  const successData = dailyStatsData.value.map(item => item.success)
  const failedData = dailyStatsData.value.map(item => item.failed)

  const option = {
    title: {
      text: '回调趋势',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['总数', '成功', '失败'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '总数',
        type: 'line',
        stack: 'Total',
        data: totalData,
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '成功',
        type: 'line',
        stack: 'Total',
        data: successData,
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '失败',
        type: 'line',
        stack: 'Total',
        data: failedData,
        itemStyle: {
          color: '#F56C6C'
        }
      }
    ]
  }

  trendChart.setOption(option)
}

// 事件处理
const handleDateRangeChange = () => {
  loadStatistics()
}

const handleFilterChange = () => {
  loadStatistics()
}

const handleResetFilter = () => {
  Object.assign(filterForm, {
    provider: '',
    event_type: '',
    user_id: ''
  })
  dateRange.value = null
  loadStatistics()
}

const handleRefresh = () => {
  loadStatistics()
}

const handleTabChange = () => {
  // Tab切换时可以做一些特殊处理
}

// 初始化
onMounted(async () => {
  await nextTick()
  initCharts()
  await loadStatistics()
})
</script>

<style scoped>
.callback-statistics {
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 20px;
  width: 100%;
}

.filter-card :deep(.el-card__body) {
  padding: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-icon.danger {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.charts-section {
  margin-bottom: 20px;
}

.trend-section {
  margin-bottom: 20px;
}

.chart-card {
  width: 100%;
}

.card-header {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.chart-container {
  padding: 10px 0;
}

.chart {
  width: 100%;
}

.table-card {
  width: 100%;
}

.status-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

/* 确保表格占满全宽 */
:deep(.el-table) {
  width: 100% !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-card :deep(.el-form) {
    flex-direction: column;
  }

  .filter-card :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .stats-cards .el-col {
    margin-bottom: 20px;
  }

  .charts-section .el-col {
    margin-bottom: 20px;
  }

  .stat-content {
    flex-direction: column;
    text-align: center;
  }

  .stat-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .status-progress {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
