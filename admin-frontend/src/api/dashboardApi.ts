import { http } from '@/utils/http'

// 仪表盘数据类型定义
export interface UserStats {
  total_users: number
  active_users: number
  inactive_users: number
  new_users_today: number
  new_users_this_week: number
  new_users_this_month: number
  online_users: number
}

export interface OrderStats {
  total_orders: number
  today_orders: number
  pending_orders: number
  completed_orders: number
  cancelled_orders: number
  revenue_today: number
  revenue_month: number
}

export interface BalanceOverview {
  total_users: number
  active_users: number
  inactive_users: number
  total_balance: number
  avg_balance: number
  recent_transactions: number
}

export interface PerformanceMetrics {
  memory_usage_mb: number
  memory_total_mb: number
  gc_count: number
  goroutine_count: number
  cpu_count: number
  heap_objects: number
  heap_alloc_mb: number
  heap_sys_mb: number
  stack_inuse_mb: number
  last_gc_time: string
  db_open_connections: number
  db_in_use: number
  db_idle: number
  timestamp: number
  status: string
}

export interface RecentActivity {
  id: string
  type: string
  action: string
  description: string
  username: string
  timestamp: string
}

export interface SystemHealthStatus {
  overall: string
  database: {
    status: string
    message: string
    response_time: string
    last_check: string
  }
  redis: {
    status: string
    message: string
    response_time: string
    last_check: string
  }
  api: {
    status: string
    message: string
    response_time: string
    last_check: string
  }
  services: {
    [key: string]: {
      status: string
      message: string
      response_time: string
      last_check: string
    }
  }
  last_check: string
}

export interface DashboardOverview {
  user_stats: UserStats
  balance_overview: BalanceOverview
  order_stats: OrderStats
  system_info: any
  performance_metrics: PerformanceMetrics
  recent_activities: RecentActivity[]
  health_status: SystemHealthStatus
  updated_at: string
}

export interface RealtimeStats {
  memory_usage_mb: number
  cpu_count: number
  goroutine_count: number
  db_open_connections: number
  db_in_use: number
  db_idle: number
  gc_count: number
  redis_status: string
  status: string
  last_updated: number
}

export interface ApiResponse<T> {
  success: boolean
  code: number
  message: string
  data: T
}

export class DashboardService {
  /**
   * 获取仪表盘概览数据
   */
  static async getOverview(): Promise<ApiResponse<DashboardOverview>> {
    return await http.get<ApiResponse<DashboardOverview>>({
      url: '/api/v1/admin/dashboard/overview'
    })
  }

  /**
   * 获取用户统计数据
   */
  static async getUserStats(): Promise<ApiResponse<UserStats>> {
    return await http.get<ApiResponse<UserStats>>({
      url: '/api/v1/admin/dashboard/users/stats'
    })
  }

  /**
   * 获取订单统计数据
   */
  static async getOrderStats(): Promise<ApiResponse<OrderStats>> {
    return await http.get<ApiResponse<OrderStats>>({
      url: '/api/v1/admin/dashboard/orders/stats'
    })
  }

  /**
   * 获取余额统计数据
   */
  static async getBalanceStats(): Promise<ApiResponse<BalanceOverview>> {
    return await http.get<ApiResponse<BalanceOverview>>({
      url: '/api/v1/admin/dashboard/balance/stats'
    })
  }

  /**
   * 获取系统健康状态
   */
  static async getSystemHealth(): Promise<ApiResponse<SystemHealthStatus>> {
    return await http.get<ApiResponse<SystemHealthStatus>>({
      url: '/api/v1/admin/dashboard/health'
    })
  }

  /**
   * 获取性能指标
   */
  static async getPerformanceMetrics(): Promise<ApiResponse<PerformanceMetrics>> {
    return await http.get<ApiResponse<PerformanceMetrics>>({
      url: '/api/v1/admin/dashboard/performance'
    })
  }

  /**
   * 获取最近活动
   */
  static async getRecentActivities(): Promise<ApiResponse<RecentActivity[]>> {
    return await http.get<ApiResponse<RecentActivity[]>>({
      url: '/api/v1/admin/dashboard/activities'
    })
  }

  /**
   * 获取实时统计数据
   */
  static async getRealtimeStats(): Promise<ApiResponse<RealtimeStats>> {
    return await http.get<ApiResponse<RealtimeStats>>({
      url: '/api/v1/admin/dashboard/realtime'
    })
  }
}
