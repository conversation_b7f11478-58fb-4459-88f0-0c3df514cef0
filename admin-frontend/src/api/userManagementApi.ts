import { http } from '@/utils/http'

/**
 * 用户管理API服务 - 对接真实后端
 */

// 用户数据类型定义（对应后端 UserWithRoles）
export interface User {
  id: string
  username: string
  email: string
  is_active: boolean
  client_id: string
  created_at: string
  updated_at: string
  deleted_at?: string | null  // 软删除时间
  last_login?: string
  login_count?: number
  roles?: Role[]
  // 后端可能包含的其他字段
  password_hash?: string
  callback_url?: string
  default_role_id?: string
}

export interface Role {
  id: string
  name: string
  description?: string
  is_system: boolean
  created_at: string
  updated_at: string
}

export interface UserStatistics {
  total_users: number
  active_users: number
  inactive_users: number
  new_users_today: number
  new_users_this_week: number
  new_users_this_month: number
  login_users_today: number
  login_users_this_week: number
  login_users_this_month: number
}

export interface UserListParams {
  page?: number
  page_size?: number
  keyword?: string
  status?: string
  role_id?: string
  order_by?: string
  order?: 'asc' | 'desc'
  include_deleted?: boolean
}

export interface UserListResult {
  items: User[]     // 对应后端的 items 字段
  total: number
  page: number
  page_size: number
  total_pages: number
}

export interface CreateUserRequest {
  username: string
  email: string
  password: string
  role_id?: string
  is_active?: boolean
}

export interface UpdateUserRequest {
  username?: string
  email?: string
  is_active?: boolean
}

export interface UpdateUserStatusRequest {
  is_active: boolean
}

export interface ResetPasswordRequest {
  new_password: string
}

export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data?: T
}

export class UserManagementService {
  private static readonly BASE_URL = '/api/v1/admin/users'

  /**
   * 获取用户列表
   */
  static async getUserList(params: UserListParams = {}): Promise<UserListResult> {
    try {
      const response = await http.get<ApiResponse<UserListResult>>({
        url: this.BASE_URL,
        params: {
          page: params.page || 1,
          page_size: params.page_size || 20,
          keyword: params.keyword || '',
          status: params.status || '',
          role_id: params.role_id || '',
          order_by: params.order_by || 'created_at',
          order: params.order || 'desc',
          include_deleted: params.include_deleted || false,
          _t: Date.now() // 添加时间戳防止缓存
        },
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })

      if (response.success && response.data) {
        return response.data
      }

      throw new Error(response.message || '获取用户列表失败')
    } catch (error: any) {
      console.error('Get user list failed:', error)
      throw new Error(error.message || '获取用户列表失败')
    }
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStatistics(): Promise<UserStatistics> {
    try {
      const response = await http.get<ApiResponse<UserStatistics>>({
        url: `${this.BASE_URL}/statistics`,
        params: {
          _t: Date.now() // 添加时间戳防止缓存
        },
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      })

      if (response.success && response.data) {
        return response.data
      }

      throw new Error(response.message || '获取用户统计失败')
    } catch (error: any) {
      console.error('Get user statistics failed:', error)
      throw new Error(error.message || '获取用户统计失败')
    }
  }

  /**
   * 获取用户详情
   */
  static async getUserDetail(userId: string): Promise<User> {
    try {
      const response = await http.get<ApiResponse<User>>({
        url: `${this.BASE_URL}/${userId}`
      })

      if (response.success && response.data) {
        return response.data
      }

      throw new Error(response.message || '获取用户详情失败')
    } catch (error: any) {
      console.error('Get user detail failed:', error)
      throw new Error(error.message || '获取用户详情失败')
    }
  }

  /**
   * 创建用户
   */
  static async createUser(userData: CreateUserRequest): Promise<User> {
    try {
      const response = await http.post<ApiResponse<User>>({
        url: this.BASE_URL,
        data: userData
      })

      if (response.success && response.data) {
        return response.data
      }

      throw new Error(response.message || '创建用户失败')
    } catch (error: any) {
      console.error('Create user failed:', error)
      throw new Error(error.message || '创建用户失败')
    }
  }

  /**
   * 更新用户信息
   */
  static async updateUser(userId: string, userData: UpdateUserRequest): Promise<User> {
    try {
      const response = await http.put<ApiResponse<User>>({
        url: `${this.BASE_URL}/${userId}`,
        data: userData
      })

      if (response.success && response.data) {
        return response.data
      }

      throw new Error(response.message || '更新用户失败')
    } catch (error: any) {
      console.error('Update user failed:', error)
      throw new Error(error.message || '更新用户失败')
    }
  }

  /**
   * 更新用户状态
   */
  static async updateUserStatus(userId: string, statusData: UpdateUserStatusRequest): Promise<void> {
    try {
      const response = await http.put<ApiResponse<void>>({
        url: `${this.BASE_URL}/${userId}/status`,
        data: statusData
      })

      if (!response.success) {
        throw new Error(response.message || '更新用户状态失败')
      }
    } catch (error: any) {
      console.error('Update user status failed:', error)
      throw new Error(error.message || '更新用户状态失败')
    }
  }

  /**
   * 重置用户密码
   */
  static async resetUserPassword(userId: string, passwordData: ResetPasswordRequest): Promise<void> {
    try {
      const response = await http.put<ApiResponse<void>>({
        url: `${this.BASE_URL}/${userId}/password`,
        data: passwordData
      })

      if (!response.success) {
        throw new Error(response.message || '重置密码失败')
      }
    } catch (error: any) {
      console.error('Reset password failed:', error)
      throw new Error(error.message || '重置密码失败')
    }
  }

  /**
   * 删除用户（软删除）
   */
  static async deleteUser(userId: string): Promise<void> {
    try {
      const response = await http.del<ApiResponse<void>>({
        url: `${this.BASE_URL}/${userId}`
      })

      if (!response.success) {
        throw new Error(response.message || '删除用户失败')
      }
    } catch (error: any) {
      console.error('Delete user failed:', error)
      throw new Error(error.message || '删除用户失败')
    }
  }

  /**
   * 永久删除用户（硬删除）
   */
  static async forceDeleteUser(userId: string): Promise<void> {
    try {
      const response = await http.del<ApiResponse<void>>({
        url: `${this.BASE_URL}/${userId}/force`
      })

      if (!response.success) {
        throw new Error(response.message || '永久删除用户失败')
      }
    } catch (error: any) {
      console.error('Force delete user failed:', error)
      throw new Error(error.message || '永久删除用户失败')
    }
  }

  /**
   * 批量更新用户状态
   */
  static async batchUpdateUserStatus(userIds: string[], isActive: boolean): Promise<void> {
    try {
      const response = await http.patch<ApiResponse<void>>({
        url: '/api/v1/system/admin/users/batch/status',
        data: {
          user_ids: userIds,
          is_active: isActive
        }
      })

      if (!response.success) {
        throw new Error(response.message || '批量更新失败')
      }
    } catch (error: any) {
      console.error('Batch update user status failed:', error)
      throw new Error(error.message || '批量更新失败')
    }
  }
}

export default UserManagementService
