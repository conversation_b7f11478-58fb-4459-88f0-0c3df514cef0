import { http } from '@/utils/http'

/**
 * 角色权限管理API服务 - 对接真实后端
 */

// 角色数据类型定义（对应后端 Role）
export interface Role {
  id: string
  name: string
  description: string
  is_system: boolean
  permissions?: string[] // 权限ID列表
  created_at: string
  updated_at: string
}

// 权限数据类型定义（对应后端 Permission）
export interface Permission {
  id: string
  name: string
  description: string
  resource: string // 资源，如user, role, express_price等
  action: string   // 操作，如create, read, update, delete等
  is_system: boolean
  created_at: string
  updated_at: string
}

// 用户角色数据类型定义
export interface UserRole {
  user_id: string
  role_id: string
  created_at: string
}

// 角色列表查询参数
export interface RoleListParams {
  page?: number
  page_size?: number
  keyword?: string
  is_system?: boolean
  order_by?: string
  order?: 'asc' | 'desc'
}

// 创建角色请求
export interface CreateRoleRequest {
  name: string
  description?: string
  is_system?: boolean
}

// 更新角色请求
export interface UpdateRoleRequest {
  name: string
  description?: string
  is_system?: boolean
}

// 权限列表查询参数
export interface PermissionListParams {
  page?: number
  page_size?: number
  keyword?: string
  resource?: string
  action?: string
  is_system?: boolean
  order_by?: string
  order?: 'asc' | 'desc'
}

// 权限列表响应
export interface PermissionListResult {
  items: Permission[]
  total: number
  page: number
  page_size: number
}

// 角色列表响应
export interface RoleListResult {
  items: Role[]
  total: number
  page: number
  page_size: number
}

// 创建权限请求
export interface CreatePermissionRequest {
  name: string
  description?: string
  resource: string
  action: string
  is_system?: boolean
}

// 更新权限请求
export interface UpdatePermissionRequest {
  name: string
  description?: string
  resource: string
  action: string
  is_system?: boolean
}

/**
 * 角色管理API服务
 */
export class RoleManagementService {
  private static readonly BASE_URL = '/api/v1/admin/roles'

  /**
   * 获取角色列表
   */
  static async getRoleList(params: RoleListParams = {}): Promise<{ items: Role[], total: number, page: number, page_size: number }> {
    try {
      // 后端直接返回角色数组，不是包装格式
      const roles = await http.get<Role[]>({
        url: this.BASE_URL,
        params: {
          page: params.page || 1,
          page_size: params.page_size || 20,
          keyword: params.keyword || '',
          is_system: params.is_system,
          order_by: params.order_by || 'created_at',
          order: params.order || 'desc',
          _t: Date.now() // 添加时间戳防止缓存
        }
      })

      // 模拟分页响应格式，因为后端目前返回所有数据
      return {
        items: roles || [],
        total: roles?.length || 0,
        page: params.page || 1,
        page_size: params.page_size || 20
      }
    } catch (error: any) {
      console.error('Get role list failed:', error)
      throw new Error(error.message || '获取角色列表失败')
    }
  }

  /**
   * 获取角色详情
   */
  static async getRoleById(roleId: string): Promise<Role> {
    try {
      const role = await http.get<Role>({
        url: `${this.BASE_URL}/${roleId}`
      })

      return role
    } catch (error: any) {
      console.error('Get role by id failed:', error)
      throw new Error(error.message || '获取角色详情失败')
    }
  }

  /**
   * 创建角色
   */
  static async createRole(roleData: CreateRoleRequest): Promise<Role> {
    try {
      const role = await http.post<Role>({
        url: this.BASE_URL,
        data: roleData
      })

      return role
    } catch (error: any) {
      console.error('Create role failed:', error)
      throw new Error(error.message || '创建角色失败')
    }
  }

  /**
   * 更新角色信息
   */
  static async updateRole(roleId: string, roleData: UpdateRoleRequest): Promise<Role> {
    try {
      const role = await http.put<Role>({
        url: `${this.BASE_URL}/${roleId}`,
        data: roleData
      })

      return role
    } catch (error: any) {
      console.error('Update role failed:', error)
      throw new Error(error.message || '更新角色失败')
    }
  }

  /**
   * 删除角色
   */
  static async deleteRole(roleId: string): Promise<void> {
    try {
      await http.del<void>({
        url: `${this.BASE_URL}/${roleId}`
      })
    } catch (error: any) {
      console.error('Delete role failed:', error)
      throw new Error(error.message || '删除角色失败')
    }
  }

  /**
   * 为角色添加权限
   */
  static async addPermissionToRole(roleId: string, permissionId: string): Promise<void> {
    try {
      await http.post<void>({
        url: `${this.BASE_URL}/${roleId}/permissions`,
        data: {
          permission_id: permissionId
        }
      })
    } catch (error: any) {
      console.error('Add permission to role failed:', error)
      throw new Error(error.message || '添加权限失败')
    }
  }

  /**
   * 从角色中移除权限
   */
  static async removePermissionFromRole(roleId: string, permissionId: string): Promise<void> {
    try {
      await http.del<void>({
        url: `${this.BASE_URL}/${roleId}/permissions/${permissionId}`
      })
    } catch (error: any) {
      console.error('Remove permission from role failed:', error)
      throw new Error(error.message || '移除权限失败')
    }
  }
}

/**
 * 权限管理API服务
 */
export class PermissionManagementService {
  private static readonly BASE_URL = '/api/v1/admin/permissions'

  /**
   * 获取权限列表
   */
  static async getPermissionList(params: PermissionListParams = {}): Promise<{ items: Permission[], total: number, page: number, page_size: number }> {
    try {
      // 后端直接返回权限数组，不是包装格式
      const permissions = await http.get<Permission[]>({
        url: this.BASE_URL,
        params: {
          page: params.page || 1,
          page_size: params.page_size || 20,
          keyword: params.keyword || '',
          resource: params.resource || '',
          action: params.action || '',
          is_system: params.is_system,
          order_by: params.order_by || 'created_at',
          order: params.order || 'desc',
          _t: Date.now() // 添加时间戳防止缓存
        }
      })

      // 模拟分页响应格式，因为后端目前返回所有数据
      return {
        items: permissions || [],
        total: permissions?.length || 0,
        page: params.page || 1,
        page_size: params.page_size || 20
      }
    } catch (error: any) {
      console.error('Get permission list failed:', error)
      throw new Error(error.message || '获取权限列表失败')
    }
  }

  /**
   * 获取权限详情
   */
  static async getPermissionById(permissionId: string): Promise<Permission> {
    try {
      const permission = await http.get<Permission>({
        url: `${this.BASE_URL}/${permissionId}`
      })

      return permission
    } catch (error: any) {
      console.error('Get permission by id failed:', error)
      throw new Error(error.message || '获取权限详情失败')
    }
  }

  /**
   * 创建权限
   */
  static async createPermission(permissionData: CreatePermissionRequest): Promise<Permission> {
    try {
      const permission = await http.post<Permission>({
        url: this.BASE_URL,
        data: permissionData
      })

      return permission
    } catch (error: any) {
      console.error('Create permission failed:', error)
      throw new Error(error.message || '创建权限失败')
    }
  }

  /**
   * 更新权限信息
   */
  static async updatePermission(permissionId: string, permissionData: UpdatePermissionRequest): Promise<Permission> {
    try {
      const permission = await http.put<Permission>({
        url: `${this.BASE_URL}/${permissionId}`,
        data: permissionData
      })

      return permission
    } catch (error: any) {
      console.error('Update permission failed:', error)
      throw new Error(error.message || '更新权限失败')
    }
  }

  /**
   * 删除权限
   */
  static async deletePermission(permissionId: string): Promise<void> {
    try {
      await http.del<void>({
        url: `${this.BASE_URL}/${permissionId}`
      })
    } catch (error: any) {
      console.error('Delete permission failed:', error)
      throw new Error(error.message || '删除权限失败')
    }
  }

  /**
   * 获取角色的所有权限
   */
  static async getPermissionsByRole(roleId: string): Promise<Permission[]> {
    try {
      const permissions = await http.get<Permission[]>({
        url: `/api/v1/admin/roles/${roleId}/permissions`
      })

      return permissions || []
    } catch (error: any) {
      console.error('Get permissions by role failed:', error)
      throw new Error(error.message || '获取角色权限失败')
    }
  }
}

/**
 * 用户角色管理API服务
 */
export class UserRoleManagementService {
  private static readonly BASE_URL = '/api/v1/users'

  /**
   * 获取用户的所有角色
   */
  static async getUserRoles(userId: string): Promise<Role[]> {
    try {
      const roles = await http.get<Role[]>({
        url: `${this.BASE_URL}/${userId}/roles`
      })

      return roles || []
    } catch (error: any) {
      console.error('Get user roles failed:', error)
      throw new Error(error.message || '获取用户角色失败')
    }
  }

  /**
   * 为用户添加角色
   */
  static async addRoleToUser(userId: string, roleId: string): Promise<void> {
    try {
      await http.post<void>({
        url: `${this.BASE_URL}/${userId}/roles/${roleId}`
      })
    } catch (error: any) {
      console.error('Add role to user failed:', error)
      throw new Error(error.message || '添加角色失败')
    }
  }

  /**
   * 从用户中移除角色
   */
  static async removeRoleFromUser(userId: string, roleId: string): Promise<void> {
    try {
      await http.del<void>({
        url: `${this.BASE_URL}/${userId}/roles/${roleId}`
      })
    } catch (error: any) {
      console.error('Remove role from user failed:', error)
      throw new Error(error.message || '移除角色失败')
    }
  }

  /**
   * 设置用户的默认角色
   */
  static async setDefaultRoleForUser(userId: string, roleId: string): Promise<void> {
    try {
      await http.put<void>({
        url: `${this.BASE_URL}/${userId}/default-role/${roleId}`
      })
    } catch (error: any) {
      console.error('Set default role for user failed:', error)
      throw new Error(error.message || '设置默认角色失败')
    }
  }

  /**
   * 获取拥有指定角色的所有用户
   */
  static async getUsersByRole(roleId: string): Promise<any[]> {
    try {
      const users = await http.get<any[]>({
        url: `/api/v1/admin/roles-users/${roleId}`
      })

      return users || []
    } catch (error: any) {
      console.error('Get users by role failed:', error)
      throw new Error(error.message || '获取角色用户失败')
    }
  }
}

export default {
  RoleManagementService,
  PermissionManagementService,
  UserRoleManagementService
}
