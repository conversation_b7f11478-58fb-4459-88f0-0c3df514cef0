import { http } from '@/utils/http'

// ==================== 类型定义 ====================

// 订单状态枚举 - 基于统一状态体系
export enum OrderStatus {
  // === 下单阶段 ===
  SUBMITTED = 'submitted',
  SUBMIT_FAILED = 'submit_failed',
  PRINT_FAILED = 'print_failed',

  // === 分配阶段 ===
  ASSIGNED = 'assigned',
  AWAITING_PICKUP = 'awaiting_pickup',

  // === 揽收阶段 ===
  PICKED_UP = 'picked_up',
  PICKUP_FAILED = 'pickup_failed',

  // === 运输阶段 ===
  IN_TRANSIT = 'in_transit',
  OUT_FOR_DELIVERY = 'out_for_delivery',

  // === 签收阶段 ===
  DELIVERED = 'delivered',
  DELIVERED_ABNORMAL = 'delivered_abnormal',

  // === 计费阶段 ===
  BILLED = 'billed',

  // === 异常状态 ===
  EXCEPTION = 'exception',
  RETURNED = 'returned',
  FORWARDED = 'forwarded',

  // === 取消状态 ===
  CANCELLING = 'cancelling',
  CANCELLED = 'cancelled',
  VOIDED = 'voided',

  // === 特殊状态 ===
  WEIGHT_UPDATED = 'weight_updated',
  REVIVED = 'revived'
}

// 计费状态枚举
export enum BillingStatus {
  PENDING = 'pending',
  CALCULATED = 'calculated',
  PAID = 'paid',
  REFUNDED = 'refunded'
}

// 用户信息
export interface OrderUser {
  id: string
  username: string
  email: string
  is_active: boolean
}

// 费用信息
export interface OrderBilling {
  estimated_fee: number
  actual_fee: number
  insurance_fee: number
  billing_status: string
  fee_difference: number
}

// 快递公司信息
export interface ExpressCompany {
  id: string
  code: string
  name: string
  english_name: string
  logo_url: string
  official_website: string
  customer_service_phone: string
}

// 价格验证信息
export interface PriceValidation {
  provider_price: number      // 供应商实际价格
  system_price: number        // 系统订单价格
  profit_status: string       // 盈亏状态：profit, loss, break_even
  query_status: string        // 查询状态：pending, success, failed, cached
  query_time: string          // 查询时间
  error_message: string       // 错误信息
  supported: boolean          // 供应商是否支持查询
}

// 批量价格验证请求
export interface BatchPriceValidationRequest {
  provider?: string           // 供应商筛选
  start_time?: string         // 开始时间
  end_time?: string           // 结束时间
  order_ids?: number[]        // 指定订单ID列表
  max_count?: number          // 最大处理数量
}

// 价格验证结果
export interface PriceValidationResult {
  order_id: number
  order_no: string
  provider: string
  system_price: number
  provider_price: number
  profit_status: string
  query_status: string
  error_message?: string
  supported: boolean
  query_time: string
}

// 价格验证汇总
export interface PriceValidationSummary {
  total_profit: number        // 总盈利金额
  total_loss: number          // 总亏损金额
  profit_count: number        // 盈利订单数
  loss_count: number          // 亏损订单数
  break_even_count: number    // 持平订单数
  unsupported_count: number   // 不支持订单数
}

// 批量价格验证响应数据
export interface BatchPriceValidationData {
  total_orders: number        // 总订单数
  processed_orders: number    // 已处理订单数
  success_orders: number      // 成功订单数
  failed_orders: number       // 失败订单数
  skipped_orders: number      // 跳过订单数
  results: PriceValidationResult[]  // 验证结果列表
  summary: PriceValidationSummary   // 汇总信息
}

// 批量价格验证响应
export interface BatchPriceValidationResponse {
  success: boolean
  code: number
  message: string
  data?: BatchPriceValidationData
}

// 订单列表项
export interface AdminOrderListItem {
  id: number
  customer_order_no: string
  order_no: string
  tracking_no: string
  express_type: string
  product_type: string
  provider: string
  status: string
  weight: number
  price: number
  actual_fee: number
  insurance_fee: number
  billing_status: string
  sender_info: string
  receiver_info: string
  package_info: string
  created_at: string
  updated_at: string
  user: OrderUser
  billing: OrderBilling
  express_company?: ExpressCompany
  price_validation?: PriceValidation  // 价格验证信息
}

// 订单统计信息
export interface AdminOrderStatistics {
  total_orders: number
  today_orders: number
  total_amount: number
  today_amount: number
  status_counts: Record<string, number>
  provider_counts: Record<string, number>
  company_counts: Record<string, number>
  billing_counts: Record<string, number>
  active_users: number
  new_users: number
}

// 订单列表请求参数
export interface AdminOrderListRequest {
  page?: number
  page_size?: number
  
  // 基础过滤
  status?: string
  express_type?: string
  product_type?: string
  provider?: string
  
  // 管理员特有过滤
  user_id?: string
  username?: string
  user_email?: string
  company_code?: string
  billing_status?: string
  
  // 价格范围
  price_min?: number
  price_max?: number
  
  // 重量范围
  weight_min?: number
  weight_max?: number
  
  // 时间范围
  start_time?: string
  end_time?: string
  
  // 搜索
  search_keyword?: string
  search_fields?: string[]
  
  // 排序
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// 订单列表响应
export interface AdminOrderListResponse {
  success: boolean
  code: number
  message: string
  data: {
    items: AdminOrderListItem[]
    total: number
    page: number
    page_size: number
    total_pages: number
    has_next: boolean
    has_prev: boolean
    statistics?: AdminOrderStatistics
  }
}

// 订单操作记录
export interface AdminOrderOperation {
  id: string
  order_id: number
  operator_id: string
  operator: string
  operation: string
  old_status: string
  new_status: string
  reason: string
  details: Record<string, any>
  ip_address: string
  user_agent: string
  created_at: string
}

// 物流轨迹事件
export interface AdminTrackingEvent {
  id: string
  order_id: number
  status: string
  description: string
  location: string
  timestamp: string
  source: string
  raw_data: string
  created_at: string
}

// 订单详情
export interface AdminOrderDetail {
  // 基础订单信息
  id: number
  customer_order_no: string
  order_no: string
  tracking_no: string
  express_type: string
  product_type: string
  provider: string
  status: string
  weight: number
  price: number
  actual_fee: number
  insurance_fee: number
  overweight_fee: number
  underweight_fee: number
  weight_adjustment_reason: string
  billing_status: string
  order_volume: number
  actual_weight: number
  actual_volume: number
  charged_weight: number
  sender_info: string
  receiver_info: string
  package_info: string
  created_at: string
  updated_at: string
  
  // 用户详细信息
  user: {
    id: string
    username: string
    email: string
    is_active: boolean
    created_at: string
    default_role: string
  }
  
  // 快递公司详细信息
  express_company: ExpressCompany
  
  // 支付信息
  payment: {
    method: string
    status: string
    amount: number
    transaction_id: string
    paid_at?: string
  }
  
  // 操作历史
  operation_history: AdminOrderOperation[]
  
  // 物流轨迹
  tracking_history: AdminTrackingEvent[]
  
  // 审计信息
  audit_info: {
    created_by: string
    updated_by: string
    last_modified: string
    modification_count: number
  }
}

// 更新订单状态请求
export interface UpdateOrderStatusRequest {
  new_status: string
  reason: string
}

// 批量更新订单状态请求
export interface BatchUpdateOrderStatusRequest {
  order_ids: number[]
  new_status: string
  reason: string
}

// 批量操作结果
export interface BatchOperationResult {
  total: number
  success: number
  failed: number
  errors: Array<{
    order_id: number
    error: string
  }>
}

// ==================== API 接口 ====================

/**
 * 订单管理 API 类
 */
export class OrderApi {
  /**
   * 获取管理员订单列表
   */
  static async getAdminOrderList(params: AdminOrderListRequest): Promise<AdminOrderListResponse> {
    return http.get({
      url: '/api/v1/admin/orders',
      params
    })
  }

  /**
   * 获取管理员订单详情
   */
  static async getAdminOrderDetail(orderId: number): Promise<AdminOrderDetail> {
    const response = await http.get<{data: AdminOrderDetail}>({
      url: `/api/v1/admin/orders/${orderId}`
    })
    return response.data
  }

  /**
   * 更新订单状态
   */
  static async updateOrderStatus(orderId: number, data: UpdateOrderStatusRequest): Promise<void> {
    return http.put({
      url: `/api/v1/admin/orders/${orderId}/status`,
      data
    })
  }

  /**
   * 批量更新订单状态
   */
  static async batchUpdateOrderStatus(data: BatchUpdateOrderStatusRequest): Promise<BatchOperationResult> {
    const response = await http.put<{data: BatchOperationResult}>({
      url: '/api/v1/admin/orders/batch/status',
      data
    })
    return response.data
  }

  /**
   * 获取订单统计信息
   */
  static async getOrderStatistics(filter?: any): Promise<AdminOrderStatistics> {
    const response = await http.get<{data: AdminOrderStatistics}>({
      url: '/api/v1/admin/orders/statistics',
      params: filter
    })
    return response.data
  }

  /**
   * 导出订单数据
   */
  static async exportOrders(params: AdminOrderListRequest): Promise<Blob> {
    return http.get({
      url: '/api/v1/admin/orders/export',
      params,
      responseType: 'blob'
    })
  }

  /**
   * 查询供应商运费信息
   */
  static async queryProviderShippingFee(orderId: number): Promise<{
    success: boolean
    code: number
    message: string
    data?: {
      provider: string
      shipping_fee: number
      currency: string
      query_time: string
      supported: boolean
    }
  }> {
    return http.get({
      url: `/api/v1/admin/orders/${orderId}/shipping-fee`
    })
  }

  /**
   * 批量查询供应商运费信息
   */
  static async batchQueryProviderShippingFee(orderIds: number[]): Promise<{
    success: boolean
    code: number
    message: string
    data?: Array<{
      order_id: number
      provider: string
      shipping_fee: number
      currency: string
      query_time: string
      supported: boolean
      error?: string
    }>
  }> {
    return http.post({
      url: '/api/v1/admin/orders/batch/shipping-fee',
      data: { order_ids: orderIds }
    })
  }

  /**
   * 批量验证订单价格
   */
  static async batchValidatePrices(params: BatchPriceValidationRequest): Promise<BatchPriceValidationResponse> {
    return http.post({
      url: '/api/v1/admin/orders/batch/validate-prices',
      data: params
    })
  }

  /**
   * 同步订单状态
   */
  static async syncOrderStatus(orderId: number): Promise<{
    success: boolean
    code: number
    message: string
    data?: {
      order_id: number
      order_no: string
      provider: string
      old_status: string
      new_status: string
      provider_status: string
      provider_status_desc: string
      status_changed: boolean
      validation_passed: boolean
      validation_errors?: string[]
      sync_time: string
    }
  }> {
    return http.post({
      url: `/api/v1/admin/orders/${orderId}/sync-status`
    })
  }

  /**
   * 批量同步订单状态
   */
  static async batchSyncOrderStatus(params: {
    order_ids?: number[]
    provider?: string
    start_time?: string
    end_time?: string
    max_count?: number
    force_sync?: boolean
    dry_run?: boolean
    only_failed?: boolean
    skip_recent?: boolean
    recent_hours?: number
  }): Promise<{
    success: boolean
    code: number
    message: string
    data?: {
      total_orders: number
      processed_orders: number
      success_orders: number
      failed_orders: number
      changed_orders: number
      skipped_orders: number
      results: Array<{
        order_id: number
        order_no: string
        provider: string
        old_status: string
        new_status: string
        provider_status: string
        provider_status_desc: string
        status_changed: boolean
        validation_passed: boolean
        validation_errors?: string[]
        success: boolean
        error_message?: string
        sync_time: string
      }>
      summary: {
        provider_stats: Record<string, number>
        status_change_stats: Record<string, Record<string, number>>
        error_stats: Record<string, number>
        processing_time: Record<string, number>
      }
      duration: string
    }
  }> {
    return http.post({
      url: '/api/v1/admin/orders/batch/sync-status',
      data: params
    })
  }
}

// 导出常用的状态选项 - 基于统一状态体系
export const ORDER_STATUS_OPTIONS = [
  // === 下单阶段 ===
  { label: '已提交', value: OrderStatus.SUBMITTED },
  { label: '提交失败', value: OrderStatus.SUBMIT_FAILED },
  { label: '面单生成失败', value: OrderStatus.PRINT_FAILED },

  // === 分配阶段 ===
  { label: '已分配', value: OrderStatus.ASSIGNED },
  { label: '等待揽收', value: OrderStatus.AWAITING_PICKUP },

  // === 揽收阶段 ===
  { label: '已揽收', value: OrderStatus.PICKED_UP },
  { label: '揽收失败', value: OrderStatus.PICKUP_FAILED },

  // === 运输阶段 ===
  { label: '运输中', value: OrderStatus.IN_TRANSIT },
  { label: '派送中', value: OrderStatus.OUT_FOR_DELIVERY },

  // === 签收阶段 ===
  { label: '已签收', value: OrderStatus.DELIVERED },
  { label: '异常签收', value: OrderStatus.DELIVERED_ABNORMAL },

  // === 计费阶段 ===
  { label: '已计费', value: OrderStatus.BILLED },

  // === 异常状态 ===
  { label: '异常', value: OrderStatus.EXCEPTION },
  { label: '已退回', value: OrderStatus.RETURNED },
  { label: '已转寄', value: OrderStatus.FORWARDED },

  // === 取消状态 ===
  { label: '取消中', value: OrderStatus.CANCELLING },
  { label: '已取消', value: OrderStatus.CANCELLED },
  { label: '已作废', value: OrderStatus.VOIDED },

  // === 特殊状态 ===
  { label: '重量更新', value: OrderStatus.WEIGHT_UPDATED },
  { label: '订单复活', value: OrderStatus.REVIVED }
]

export const BILLING_STATUS_OPTIONS = [
  { label: '待计费', value: BillingStatus.PENDING },
  { label: '已计费', value: BillingStatus.CALCULATED },
  { label: '已支付', value: BillingStatus.PAID },
  { label: '已退款', value: BillingStatus.REFUNDED }
]
