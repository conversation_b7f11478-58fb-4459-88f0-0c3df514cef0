import { http } from '@/utils/http'

// ==================== 类型定义 ====================

/**
 * 余额总览
 */
export interface AdminBalanceOverview {
  total_users: number
  active_users: number
  inactive_users: number
  total_balance: string
  avg_balance: string
  recent_transactions: number
  updated_at: string
}

/**
 * 用户余额列表项
 */
export interface UserBalanceListItem {
  user_id: string
  username: string
  email: string
  balance: string
  available_balance: string
  currency: string
  status: string
  last_transaction_at?: string
  transaction_count: number
  created_at: string
  updated_at: string
}

/**
 * 用户余额列表请求
 */
export interface UserBalanceListRequest {
  page?: number
  page_size?: number
  keyword?: string
  status?: string
  order_by?: string
  order?: string
}

/**
 * 用户余额列表响应
 */
export interface UserBalanceListResponse {
  success: boolean
  code: number
  message: string
  data: {
    items: UserBalanceListItem[]
    total: number
    page: number
    page_size: number
    total_pages: number
  }
}

/**
 * 余额交易记录
 */
export interface BalanceTransaction {
  id: string
  user_id: string
  transaction_type: string
  amount: string
  currency: string
  balance_before: string
  balance_after: string
  order_no?: string
  reference_id?: string
  status: string
  description: string
  metadata?: Record<string, any>
  operator_id: string
  created_at: string
}

/**
 * 用户余额详情
 */
export interface UserBalanceDetail {
  user_id: string
  username: string
  email: string
  balance: string
  available_balance: string
  currency: string
  status: string
  last_transaction_at?: string
  transaction_count: number
  recent_transactions: BalanceTransaction[]
  created_at: string
  updated_at: string
}

/**
 * 余额异常记录
 */
export interface BalanceAnomaly {
  id: string
  user_id: string
  username: string
  anomaly_type: string
  description: string
  amount: string
  status: string
  created_at: string
  resolved_at?: string
}

/**
 * 余额异常请求
 */
export interface AnomalyRequest {
  page?: number
  page_size?: number
  anomaly_type?: string
  status?: string
  start_time?: string
  end_time?: string
}

/**
 * 余额异常响应
 */
export interface AnomalyResponse {
  success: boolean
  code: number
  message: string
  data: {
    items: BalanceAnomaly[]
    total: number
    page: number
    page_size: number
    total_pages: number
  }
}

/**
 * 管理员充值请求
 */
export interface AdminDepositRequest {
  user_id: string
  amount: string
  reason: string
  description?: string
  admin_id?: string
}

/**
 * 余额调整请求
 */
export interface BalanceAdjustmentRequest {
  user_id: string
  amount: string
  adjustment_type: 'increase' | 'decrease'
  reason: string
  description?: string
  admin_id?: string
}

/**
 * 强制退款请求
 */
export interface ForceRefundRequest {
  user_id: string
  amount: string
  order_no?: string
  reference_id?: string
  reason: string
  description?: string
  admin_id?: string
}

/**
 * 批量操作项
 */
export interface BatchOperationItem {
  user_id: string
  amount: string
  description?: string
}

/**
 * 批量操作请求
 */
export interface BatchOperationRequest {
  operation_type: string
  user_ids?: string[]
  amount?: string
  reason: string
  description?: string
  operations?: BatchOperationItem[]
  admin_id?: string
}

/**
 * 管理员操作响应
 */
export interface AdminOperationResponse {
  success: boolean
  transaction_id?: string
  new_balance?: string
  message: string
}

/**
 * 批量操作结果
 */
export interface BatchOperationResult {
  user_id: string
  success: boolean
  message: string
  transaction_id?: string
  new_balance?: string
}

/**
 * 批量操作响应
 */
export interface BatchOperationResponse {
  success: boolean
  code: number
  message: string
  data: {
    total_count: number
    success_count: number
    failure_count: number
    results: BatchOperationResult[]
  }
}

/**
 * 余额统计
 */
export interface BalanceStatistics {
  total_balance: string
  avg_balance: string
  median_balance: string
  max_balance: string
  min_balance: string
  total_users: number
  active_users: number
  zero_balance_users: number
  high_balance_users: number
  updated_at: string
}

/**
 * 交易统计
 */
export interface TransactionStatistics {
  total_transactions: number
  total_amount: string
  deposit_count: number
  deposit_amount: string
  withdraw_count: number
  withdraw_amount: string
  refund_count: number
  refund_amount: string
  adjustment_count: number
  adjustment_amount: string
  avg_transaction_amount: string
  updated_at: string
}

/**
 * 审计日志
 */
export interface AdminBalanceAuditLog {
  id: string
  admin_id: string
  target_user_id: string
  operation_type: string
  amount: string
  reason: string
  before_balance: string
  after_balance: string
  metadata?: Record<string, any>
  ip_address: string
  user_agent: string
  created_at: string
}

/**
 * 通用API响应
 */
export interface ApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data?: T
}

// ==================== API 类 ====================

/**
 * 余额管理 API 类
 */
export class BalanceApi {
  /**
   * 获取余额总览
   */
  static async getBalanceOverview(): Promise<ApiResponse<AdminBalanceOverview>> {
    return http.get({
      url: '/api/v1/admin/balance/overview',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }

  /**
   * 获取用户余额列表
   */
  static async getUserBalanceList(params: UserBalanceListRequest = {}): Promise<UserBalanceListResponse> {
    return http.get({
      url: '/api/v1/admin/balance/users',
      params
    })
  }

  /**
   * 获取用户余额详情
   */
  static async getUserBalanceDetail(userId: string): Promise<ApiResponse<UserBalanceDetail>> {
    return http.get({
      url: `/api/v1/admin/balance/users/${userId}`
    })
  }

  /**
   * 获取余额异常记录
   */
  static async getBalanceAnomalies(params: AnomalyRequest = {}): Promise<AnomalyResponse> {
    return http.get({
      url: '/api/v1/admin/balance/anomalies',
      params
    })
  }

  /**
   * 手动充值
   */
  static async manualDeposit(data: AdminDepositRequest): Promise<ApiResponse<AdminOperationResponse>> {
    return http.post({
      url: '/api/v1/admin/balance/manual-deposit',
      data
    })
  }

  /**
   * 余额调整
   */
  static async adjustBalance(data: BalanceAdjustmentRequest): Promise<ApiResponse<AdminOperationResponse>> {
    return http.post({
      url: '/api/v1/admin/balance/adjustment',
      data
    })
  }

  /**
   * 强制退款
   */
  static async forceRefund(data: ForceRefundRequest): Promise<ApiResponse<AdminOperationResponse>> {
    return http.post({
      url: '/api/v1/admin/balance/force-refund',
      data
    })
  }

  /**
   * 批量操作
   */
  static async batchOperation(data: BatchOperationRequest): Promise<BatchOperationResponse> {
    return http.post({
      url: '/api/v1/admin/balance/batch-operation',
      data
    })
  }

  /**
   * 获取余额统计
   */
  static async getBalanceStatistics(params?: any): Promise<ApiResponse<BalanceStatistics>> {
    return http.get({
      url: '/api/v1/admin/balance/statistics',
      params
    })
  }

  /**
   * 获取交易统计
   */
  static async getTransactionStatistics(params?: any): Promise<ApiResponse<TransactionStatistics>> {
    return http.get({
      url: '/api/v1/admin/balance/transaction-statistics',
      params
    })
  }

  /**
   * 获取审计日志
   */
  static async getAuditLogs(params?: any): Promise<ApiResponse<AdminBalanceAuditLog[]>> {
    return http.get({
      url: '/api/v1/admin/balance/audit-logs',
      params
    })
  }

  /**
   * 导出用户余额数据
   */
  static async exportUserBalances(params?: any): Promise<Blob> {
    return http.get({
      url: '/api/v1/admin/balance/users/export',
      params,
      responseType: 'blob'
    })
  }

  /**
   * 导出交易记录
   */
  static async exportTransactions(params?: any): Promise<Blob> {
    return http.get({
      url: '/api/v1/admin/balance/transactions/export',
      params,
      responseType: 'blob'
    })
  }

  /**
   * 导出审计日志
   */
  static async exportAuditLogs(params?: any): Promise<Blob> {
    return http.get({
      url: '/api/v1/admin/balance/audit-logs/export',
      params,
      responseType: 'blob'
    })
  }
}

// 导出常用的状态选项
export const BALANCE_STATUS_OPTIONS = [
  { label: '正常', value: 'active' },
  { label: '冻结', value: 'frozen' },
  { label: '禁用', value: 'disabled' }
]

export const TRANSACTION_TYPE_OPTIONS = [
  // 充值类
  { label: '用户充值', value: 'user_deposit' },
  { label: '管理员充值', value: 'admin_deposit' },
  // 支付类
  { label: '下单预收', value: 'order_pre_charge' },
  { label: '费用差额补收', value: 'billing_difference' },
  { label: '订单拦截_补收', value: 'order_intercept_charge' },
  { label: '退回收费', value: 'return_charge' },
  { label: '订单复活重计费', value: 'order_revive_recharge' },
  // 退款类
  { label: '订单取消_退款', value: 'order_cancel_refund' },
  { label: '费用差额退款', value: 'billing_difference_refund' },
  // 调整类
  { label: '调账_多退少补', value: 'balance_adjustment' }
]

export const ANOMALY_TYPE_OPTIONS = [
  { label: '余额异常', value: 'balance_anomaly' },
  { label: '交易异常', value: 'transaction_anomaly' },
  { label: '重复交易', value: 'duplicate_transaction' },
  { label: '金额异常', value: 'amount_anomaly' }
]

export const ADJUSTMENT_TYPE_OPTIONS = [
  { label: '增加', value: 'increase' },
  { label: '减少', value: 'decrease' }
]
