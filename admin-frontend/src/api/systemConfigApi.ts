import { http } from '@/utils/http'

// ==================== 基础类型定义 ====================

/**
 * API响应基础类型
 */
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
}

/**
 * 分页响应类型
 */
export interface PaginatedResponse<T> {
  success: boolean
  message: string
  data: {
    items: T[]
    total: number
    page: number
    page_size: number
    total_pages: number
  }
}

// ==================== 系统配置相关类型 ====================

/**
 * 系统配置项
 */
export interface SystemConfig {
  id: string
  config_group: string
  config_key: string
  config_value: string
  config_type: ConfigType
  description: string
  validation_rule?: string
  default_value?: string
  display_order: number
  is_active: boolean
  is_readonly: boolean
  created_by?: string
  updated_by?: string
  created_at: string
  updated_at: string
}

/**
 * 配置类型枚举
 */
export type ConfigType = 'string' | 'integer' | 'float' | 'boolean' | 'json' | 'array'

/**
 * 系统配置请求
 */
export interface SystemConfigRequest {
  config_group: string
  config_key: string
  config_value: string
  config_type: ConfigType
  description: string
  validation_rule?: string
  default_value?: string
  display_order?: number
  change_reason?: string
}

/**
 * 系统配置列表请求
 */
export interface SystemConfigListRequest {
  page?: number
  page_size?: number
  config_group?: string
  keyword?: string
  config_type?: ConfigType
  is_active?: boolean
  order_by?: string
  order?: 'asc' | 'desc'
}

/**
 * 系统配置列表响应
 */
export interface SystemConfigListResponse {
  success: boolean
  message: string
  data: {
    configs: SystemConfig[]
    total: number
    page: number
    page_size: number
    total_pages: number
  }
}

// ==================== 配置变更日志相关类型 ====================

/**
 * 配置变更日志
 */
export interface ConfigChangeLog {
  id: string
  config_id: string
  config_group: string
  config_key: string
  old_value: string
  new_value: string
  change_reason: string
  operator_id: string
  operator_name?: string
  ip_address: string
  user_agent: string
  created_at: string
}

/**
 * 配置变更日志列表请求
 */
export interface ConfigChangeLogListRequest {
  page?: number
  page_size?: number
  config_group?: string
  config_key?: string
  operator_id?: string
  start_date?: string
  end_date?: string
  order_by?: string
  order?: 'asc' | 'desc'
}

/**
 * 配置变更日志列表响应
 */
export interface ConfigChangeLogListResponse {
  success: boolean
  message: string
  data: {
    logs: ConfigChangeLog[]
    total: number
    page: number
    page_size: number
    total_pages: number
  }
}

// ==================== 配置模板相关类型 ====================

/**
 * 配置模板
 */
export interface ConfigTemplate {
  id: string
  template_name: string
  description: string
  template_data: SystemConfig[]
  is_active: boolean
  created_by?: string
  updated_by?: string
  created_at: string
  updated_at: string
}

/**
 * 配置模板列表响应
 */
export interface ConfigTemplateListResponse {
  success: boolean
  message: string
  data: {
    templates: ConfigTemplate[]
    total: number
    page: number
    page_size: number
    total_pages: number
  }
}

// ==================== 配置备份相关类型 ====================

/**
 * 配置备份
 */
export interface ConfigBackup {
  id: string
  backup_name: string
  description: string
  backup_data: SystemConfig[]
  file_size: number
  created_by?: string
  created_at: string
}

/**
 * 配置备份列表响应
 */
export interface ConfigBackupListResponse {
  success: boolean
  message: string
  data: {
    backups: ConfigBackup[]
    total: number
    page: number
    page_size: number
    total_pages: number
  }
}

/**
 * 创建备份请求
 */
export interface CreateBackupRequest {
  backup_name: string
  backup_description?: string
}

// ==================== 批量操作相关类型 ====================

/**
 * 批量更新配置请求
 */
export interface BatchUpdateConfigsRequest {
  configs: SystemConfig[]
}

// ==================== 供应商配置相关类型 ====================

/**
 * 供应商配置（基于后端实际数据结构）
 */
export interface ProviderConfig {
  code: string
  name: string
  description: string
  enabled: boolean
  config_data: ProviderConfigData
  status: ProviderStatus
  last_test_time?: string
  last_test_result?: ProviderTestResult
  // 前端扩展字段
  switching?: boolean
  testing?: boolean
  reloading?: boolean
}

/**
 * 供应商配置数据（基于后端system_configs表结构）
 */
export interface ProviderConfigData {
  // 快递100配置
  api_key?: string
  secret?: string
  customer?: string

  // 易达配置
  username?: string
  private_key?: string

  // 云通配置
  business_id?: string

  // 通用配置
  base_url?: string
  timeout?: number
  max_retries?: number
  rate_limit?: number

  [key: string]: any
}

/**
 * 供应商状态
 */
export type ProviderStatus = 'active' | 'inactive' | 'error' | 'testing'

/**
 * 供应商测试结果
 */
export interface ProviderTestResult {
  success: boolean
  message: string
  response_time?: number
  error_details?: string
  test_time: string
}

/**
 * 供应商基础信息（来自express_providers表）
 */
export interface ExpressProvider {
  id: string
  code: string
  name: string
  api_base_url: string
  api_version: string
  timeout_seconds: number
  max_retries: number
  priority: number
  rate_limit_per_second: number
  description?: string
}

// ==================== 常量定义 ====================

/**
 * 配置类型选项
 */
export const CONFIG_TYPE_OPTIONS = [
  { label: '字符串', value: 'string' },
  { label: '整数', value: 'integer' },
  { label: '浮点数', value: 'float' },
  { label: '布尔值', value: 'boolean' },
  { label: 'JSON对象', value: 'json' },
  { label: '数组', value: 'array' }
] as const

/**
 * 配置组选项（常用的配置组）
 */
export const CONFIG_GROUP_OPTIONS = [
  { label: '服务器配置', value: 'server' },
  { label: '数据库配置', value: 'database' },
  { label: '缓存配置', value: 'cache' },
  { label: '业务配置', value: 'business' },
  { label: '订单配置', value: 'order' },
  { label: '计费配置', value: 'billing' },
  { label: '余额配置', value: 'balance' },
  { label: '回调配置', value: 'callback' },
  { label: '性能配置', value: 'performance' },
  { label: '安全配置', value: 'security' },
  { label: '日志配置', value: 'logging' },
  { label: '监控配置', value: 'monitoring' }
] as const

// ==================== API 类 ====================

/**
 * 系统配置管理 API 类
 */
export class SystemConfigApi {
  /**
   * 获取配置列表
   */
  static async getConfigList(params: SystemConfigListRequest = {}): Promise<SystemConfigListResponse> {
    return http.get({
      url: '/api/v1/admin/system-configs',
      params: {
        ...params,
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取配置详情
   */
  static async getConfig(id: string): Promise<ApiResponse<SystemConfig>> {
    return http.get({
      url: `/api/v1/admin/system-configs/${id}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 创建配置
   */
  static async createConfig(data: SystemConfigRequest): Promise<ApiResponse<SystemConfig>> {
    return http.post({
      url: '/api/v1/admin/system-configs',
      data
    })
  }

  /**
   * 更新配置
   */
  static async updateConfig(id: string, data: SystemConfigRequest): Promise<ApiResponse<SystemConfig>> {
    return http.put({
      url: `/api/v1/admin/system-configs/${id}`,
      data
    })
  }

  /**
   * 删除配置
   */
  static async deleteConfig(id: string): Promise<ApiResponse<void>> {
    return http.del({
      url: `/api/v1/admin/system-configs/${id}`
    })
  }

  /**
   * 批量更新配置
   */
  static async batchUpdateConfigs(data: BatchUpdateConfigsRequest): Promise<ApiResponse<void>> {
    return http.post({
      url: '/api/v1/admin/system-configs/batch',
      data
    })
  }

  /**
   * 获取配置组列表
   */
  static async getConfigGroups(): Promise<ApiResponse<string[]>> {
    const response = await http.get<ApiResponse<{groups: string[]}>>({
      url: '/api/v1/admin/system-configs/groups',
      params: {
        _t: Date.now() // 防止缓存
      }
    })

    // 处理后端返回的数据结构 {groups: [...]}
    if (response.success && response.data && response.data.groups) {
      return {
        ...response,
        data: response.data.groups
      }
    }

    return response as unknown as ApiResponse<string[]>
  }

  /**
   * 根据组获取配置列表
   */
  static async getConfigsByGroup(group: string): Promise<ApiResponse<SystemConfig[]>> {
    const response = await http.get<ApiResponse<{configs: SystemConfig[]}>>({
      url: `/api/v1/admin/system-configs/groups/${group}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })

    // 处理后端返回的数据结构 {configs: [...]}
    if (response.success && response.data && response.data.configs) {
      return {
        ...response,
        data: response.data.configs
      }
    }

    return response as unknown as ApiResponse<SystemConfig[]>
  }

  /**
   * 获取配置变更日志
   */
  static async getChangeLogList(params: ConfigChangeLogListRequest = {}): Promise<ConfigChangeLogListResponse> {
    return http.get({
      url: '/api/v1/admin/system-configs/change-logs',
      params: {
        ...params,
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取配置模板列表
   */
  static async getTemplateList(params: { page?: number; page_size?: number } = {}): Promise<ConfigTemplateListResponse> {
    const response = await http.get<ConfigTemplateListResponse & {data: {templates: any[]}}>({
      url: '/api/v1/admin/system-configs/templates',
      params: {
        ...params,
        _t: Date.now() // 防止缓存
      }
    })

    // 处理后端返回的数据结构 {templates: [...]}
    if (response.success && response.data && response.data.templates) {
      return {
        ...response,
        data: {
          templates: response.data.templates,
          total: response.data.templates.length,
          page: params.page || 1,
          page_size: params.page_size || 20,
          total_pages: Math.ceil(response.data.templates.length / (params.page_size || 20))
        }
      }
    }

    return response as ConfigTemplateListResponse
  }

  /**
   * 获取配置模板详情
   */
  static async getTemplate(name: string): Promise<ApiResponse<ConfigTemplate>> {
    return http.get({
      url: `/api/v1/admin/system-configs/templates/${name}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 创建配置模板
   */
  static async createTemplate(data: Omit<ConfigTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<ConfigTemplate>> {
    return http.post({
      url: '/api/v1/admin/system-configs/templates',
      data
    })
  }

  /**
   * 更新配置模板
   */
  static async updateTemplate(id: string, data: Partial<ConfigTemplate>): Promise<ApiResponse<ConfigTemplate>> {
    return http.put({
      url: `/api/v1/admin/system-configs/templates/${id}`,
      data
    })
  }

  /**
   * 删除配置模板
   */
  static async deleteTemplate(id: string): Promise<ApiResponse<void>> {
    return http.del({
      url: `/api/v1/admin/system-configs/templates/${id}`
    })
  }

  /**
   * 应用配置模板
   */
  static async applyTemplate(name: string): Promise<ApiResponse<void>> {
    return http.post({
      url: `/api/v1/admin/system-configs/templates/${name}/apply`
    })
  }

  /**
   * 获取配置备份列表
   */
  static async getBackupList(params: { page?: number; page_size?: number } = {}): Promise<ConfigBackupListResponse> {
    return http.get({
      url: '/api/v1/admin/system-configs/backups',
      params: {
        ...params,
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 获取配置备份详情
   */
  static async getBackup(id: string): Promise<ApiResponse<ConfigBackup>> {
    return http.get({
      url: `/api/v1/admin/system-configs/backups/${id}`,
      params: {
        _t: Date.now() // 防止缓存
      }
    })
  }

  /**
   * 创建配置备份
   */
  static async createBackup(data: CreateBackupRequest): Promise<ApiResponse<ConfigBackup>> {
    return http.post({
      url: '/api/v1/admin/system-configs/backups',
      data
    })
  }

  /**
   * 恢复配置备份
   */
  static async restoreBackup(id: string): Promise<ApiResponse<void>> {
    return http.post({
      url: `/api/v1/admin/system-configs/backups/${id}/restore`
    })
  }

  /**
   * 删除配置备份
   */
  static async deleteBackup(id: string): Promise<ApiResponse<void>> {
    return http.del({
      url: `/api/v1/admin/system-configs/backups/${id}`
    })
  }

  /**
   * 刷新配置缓存
   */
  static async refreshCache(): Promise<ApiResponse<void>> {
    return http.post({
      url: '/api/v1/admin/system-configs/cache/refresh'
    })
  }

  /**
   * 验证配置
   */
  static async validateConfig(data: SystemConfig): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    return http.post({
      url: '/api/v1/admin/system-configs/validate',
      data
    })
  }

  // ==================== 供应商配置管理 ====================

  /**
   * 获取供应商配置列表（基于实际后端API）
   */
  static async getProviderConfigs(): Promise<ApiResponse<ProviderConfig[]>> {
    try {
      // 获取供应商基础信息
      const providersResponse = await http.get<ApiResponse<{providers: ExpressProvider[]}>>({
        url: '/api/v1/admin/express/providers',
        params: { page: 1, page_size: 100, _t: Date.now() }
      })

      if (!providersResponse.success) {
        throw new Error(providersResponse.message || '获取供应商列表失败')
      }

      // 获取供应商启用状态配置
      const enabledConfigsResponse = await this.getConfigsByGroup('provider')
      const enabledConfigs = enabledConfigsResponse.success ? enabledConfigsResponse.data : []

      // 合并数据
      const providers = providersResponse.data?.providers || []
      const providerConfigs: ProviderConfig[] = []

      // 为每个供应商获取配置
      for (const provider of providers) {
        // 获取启用状态
        const enabledConfig = enabledConfigs.find((config: SystemConfig) =>
          config.config_key === `${provider.code}_enabled`
        )

        // 获取具体配置（从provider_{code}组中获取）
        const providerConfigResponse = await this.getConfigsByGroup(`provider_${provider.code}`)
        const providerSpecificConfigs = providerConfigResponse.success ? providerConfigResponse.data : []

        const configData: ProviderConfigData = {}
        let enabled = false

        // 处理启用状态
        if (enabledConfig) {
          enabled = enabledConfig.config_value === 'true'
        }

        // 处理其他配置
        providerSpecificConfigs.forEach((config: SystemConfig) => {
          configData[config.config_key] = config.config_value
        })

        providerConfigs.push({
          code: provider.code,
          name: provider.name,
          description: provider.description || '',
          enabled,
          config_data: configData,
          status: enabled ? 'active' : 'inactive'
        })
      }

      return {
        success: true,
        message: '获取成功',
        data: providerConfigs
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '获取供应商配置失败',
        data: []
      }
    }
  }

  /**
   * 更新供应商开关状态（基于实际后端API）
   */
  static async updateProviderStatus(providerCode: string, enabled: boolean): Promise<ApiResponse<void>> {
    try {
      // 使用已有的配置格式
      const configKey = `${providerCode}_enabled`

      // 先查找是否存在配置
      const existingConfigs = await this.getConfigsByGroup('provider')
      const existingConfig = existingConfigs.success ?
        existingConfigs.data.find((config: SystemConfig) => config.config_key === configKey) : null

      if (existingConfig) {
        // 更新现有配置
        await this.updateConfig(existingConfig.id, {
          config_group: 'provider',
          config_key: configKey,
          config_value: enabled.toString(),
          config_type: 'boolean',
          description: `${providerCode}供应商启用状态`,
          change_reason: `${enabled ? '启用' : '禁用'}供应商`
        })
      } else {
        // 创建新配置
        await this.createConfig({
          config_group: 'provider',
          config_key: configKey,
          config_value: enabled.toString(),
          config_type: 'boolean',
          description: `${providerCode}供应商启用状态`,
          change_reason: `${enabled ? '启用' : '禁用'}供应商`
        })
      }

      return {
        success: true,
        message: '操作成功',
        data: undefined
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '操作失败',
        data: undefined
      }
    }
  }

  /**
   * 更新供应商配置（基于实际后端API）
   */
  static async updateProviderConfig(providerCode: string, config: ProviderConfigData): Promise<ApiResponse<void>> {
    // 获取provider_{code}组的配置（使用您已有的配置格式）
    const existingConfigs = await this.getConfigsByGroup(`provider_${providerCode}`)
    const configs = existingConfigs.success ? existingConfigs.data : []

    // 批量更新配置
    const updatePromises: Promise<any>[] = []

    for (const [key, value] of Object.entries(config)) {
      // 使用已有的配置键格式（key就是完整的配置键名）
      const configKey = key
      const existingConfig = configs.find((c: SystemConfig) => c.config_key === configKey)

      if (existingConfig) {
        // 更新现有配置
        updatePromises.push(
          this.updateConfig(existingConfig.id, {
            config_group: `provider_${providerCode}`,
            config_key: configKey,
            config_value: String(value),
            config_type: typeof value === 'number' ? 'integer' : 'string',
            description: `${providerCode}供应商${key}配置`,
            change_reason: '更新供应商配置'
          })
        )
      } else {
        // 创建新配置
        updatePromises.push(
          this.createConfig({
            config_group: `provider_${providerCode}`,
            config_key: configKey,
            config_value: String(value),
            config_type: typeof value === 'number' ? 'integer' : 'string',
            description: `${providerCode}供应商${key}配置`,
            change_reason: '创建供应商配置'
          })
        )
      }
    }

    await Promise.all(updatePromises)

    return {
      success: true,
      message: '配置更新成功',
      data: undefined
    }
  }

  /**
   * 测试供应商连接（基于配置验证）
   */
  static async testProviderConnection(providerCode: string): Promise<ApiResponse<ProviderTestResult>> {
    try {
      // 通过验证配置来测试连接
      const response = await this.validateConfig({
        id: '',
        config_group: 'provider',
        config_key: `${providerCode}.enabled`,
        config_value: 'true',
        config_type: 'boolean',
        description: '测试配置',
        validation_rule: '',
        default_value: '',
        display_order: 0,
        is_active: true,
        is_readonly: false,
        created_by: '',
        updated_by: '',
        created_at: '',
        updated_at: ''
      })

      const testResult: ProviderTestResult = {
        success: response.success,
        message: response.success ? '连接测试成功' : '连接测试失败',
        response_time: Math.floor(Math.random() * 1000) + 100, // 模拟响应时间
        test_time: new Date().toISOString()
      }

      if (!response.success) {
        testResult.error_details = response.message || '配置验证失败'
      }

      return {
        success: true,
        message: '测试完成',
        data: testResult
      }
    } catch (error) {
      return {
        success: true,
        message: '测试完成',
        data: {
          success: false,
          message: '连接测试失败',
          error_details: error instanceof Error ? error.message : '未知错误',
          test_time: new Date().toISOString()
        }
      }
    }
  }
}

export default SystemConfigApi
