import { http } from '@/utils/http'

/**
 * 管理员回调管理API服务 - 对接真实后端
 */

// 回调记录数据类型定义（对应后端 UnifiedCallbackRecord）
export interface AdminCallbackRecord {
  id: string
  provider: string
  callback_type: string
  order_no: string
  customer_order_no: string
  tracking_no: string
  user_id: string
  username?: string // 用户名，仅在管理员查询时返回
  raw_data: any
  raw_signature: string
  standardized_data: any
  event_type: string
  internal_status: 'success' | 'failed' | 'pending'
  external_status: 'success' | 'failed' | 'pending'
  received_at: string
  internal_processed_at?: string
  external_processed_at?: string
  internal_error?: string
  external_error?: string
  retry_count: number
  created_at: string
  updated_at: string
}

// 回调统计数据类型
export interface CallbackStatistics {
  total_records: number
  success_records: number
  failed_records: number
  pending_records: number
  success_rate: number
  avg_processing_time: number
  provider_stats: {
    [provider: string]: {
      total: number
      success: number
      failed: number
      pending: number
    }
  }
  event_type_stats: {
    [event_type: string]: {
      total: number
      success: number
      failed: number
      pending: number
    }
  }
  daily_stats: {
    date: string
    total: number
    success: number
    failed: number
    pending: number
  }[]
}

// 回调记录查询参数
export interface CallbackRecordListParams {
  page?: number
  page_size?: number
  user_id?: string
  provider?: string
  event_type?: string
  order_no?: string
  tracking_no?: string
  internal_status?: string
  external_status?: string
  start_time?: string
  end_time?: string
  order_by?: string
  order?: 'asc' | 'desc'
}

// 回调记录列表响应
export interface CallbackRecordListResult {
  records: AdminCallbackRecord[]
  total: number
  page: number
  page_size: number
}

// 回调统计查询参数
export interface CallbackStatisticsParams {
  start_time?: string
  end_time?: string
  provider?: string
  event_type?: string
  user_id?: string
}

/**
 * 管理员回调管理API服务
 */
export class AdminCallbackManagementService {
  private static readonly BASE_URL = '/api/v1/admin/callbacks'

  /**
   * 获取回调记录列表
   */
  static async getCallbackRecords(params: CallbackRecordListParams = {}): Promise<{ records: AdminCallbackRecord[], total: number, page: number, page_size: number }> {
    try {
      // 后端返回的格式：{ success: true, data: { records: [], total: 331, page: 1, page_size: 20 } }
      const response = await http.get<{ success: boolean, data: { records: AdminCallbackRecord[], total: number, page: number, page_size: number } }>({
        url: `${this.BASE_URL}/records`,
        params: {
          page: params.page || 1,
          page_size: params.page_size || 20,
          user_id: params.user_id || '',
          provider: params.provider || '',
          event_type: params.event_type || '',
          order_no: params.order_no || '',
          tracking_no: params.tracking_no || '',
          internal_status: params.internal_status || '',
          external_status: params.external_status || '',
          start_time: params.start_time || '',
          end_time: params.end_time || '',
          order_by: params.order_by || 'received_at',
          order: params.order || 'desc',
          _t: Date.now() // 添加时间戳防止缓存
        }
      })

      // 检查响应格式
      if (response && typeof response === 'object') {
        // 如果response有success和data属性，说明是包装格式
        if ('success' in response && 'data' in response && response.success && response.data) {
          return {
            records: response.data.records || [],
            total: response.data.total || 0,
            page: response.data.page || 1,
            page_size: response.data.page_size || 20
          }
        }
        // 如果response直接有records属性，说明是直接格式
        else if ('records' in response) {
          const directResponse = response as any
          return {
            records: directResponse.records || [],
            total: directResponse.total || directResponse.records?.length || 0,
            page: directResponse.page || 1,
            page_size: directResponse.page_size || 20
          }
        }
      }

      throw new Error('获取回调记录失败')
    } catch (error: any) {
      console.error('Get callback records failed:', error)
      throw new Error(error.message || '获取回调记录失败')
    }
  }

  /**
   * 获取回调记录详情
   */
  static async getCallbackRecordById(recordId: string): Promise<AdminCallbackRecord> {
    try {
      const response = await http.get<any>({
        url: `${this.BASE_URL}/records/${recordId}`
      })

      // 检查响应格式
      if (response && typeof response === 'object') {
        if ('success' in response && 'data' in response && response.success && response.data) {
          return response.data
        } else if ('id' in response) {
          return response as AdminCallbackRecord
        }
      }

      throw new Error('获取回调记录详情失败')
    } catch (error: any) {
      console.error('Get callback record by id failed:', error)
      throw new Error(error.message || '获取回调记录详情失败')
    }
  }

  /**
   * 重试回调
   */
  static async retryCallback(recordId: string): Promise<void> {
    try {
      const response = await http.post<any>({
        url: `${this.BASE_URL}/retry/${recordId}`
      })

      // 检查响应格式
      if (response && typeof response === 'object') {
        if ('success' in response && !response.success) {
          throw new Error(response.message || '重试回调失败')
        }
      }
    } catch (error: any) {
      console.error('Retry callback failed:', error)
      throw new Error(error.message || '重试回调失败')
    }
  }

  /**
   * 批量重试回调
   */
  static async batchRetryCallbacks(recordIds: string[]): Promise<void> {
    try {
      const promises = recordIds.map(id => this.retryCallback(id))
      await Promise.all(promises)
    } catch (error: any) {
      console.error('Batch retry callbacks failed:', error)
      throw new Error(error.message || '批量重试回调失败')
    }
  }

  /**
   * 获取回调统计信息
   */
  static async getCallbackStatistics(params: CallbackStatisticsParams = {}): Promise<CallbackStatistics> {
    try {
      const response = await http.get<any>({
        url: `${this.BASE_URL}/statistics`,
        params: {
          start_time: params.start_time || '',
          end_time: params.end_time || '',
          provider: params.provider || '',
          event_type: params.event_type || '',
          user_id: params.user_id || '',
          _t: Date.now()
        }
      })

      // 检查响应格式
      if (response && typeof response === 'object') {
        let data: any = null

        // 如果response有success和data属性，说明是包装格式
        if ('success' in response && 'data' in response && response.success && response.data) {
          data = response.data
        }
        // 如果response直接有统计数据属性，说明是直接格式
        else if ('total_records' in response) {
          data = response
        }

        if (data) {
          return {
            total_records: data.total_records || 0,
            success_records: data.success_records || 0,
            failed_records: data.failed_records || 0,
            pending_records: data.pending_records || 0,
            success_rate: data.success_rate || 0,
            avg_processing_time: data.avg_processing_time || 0,
            provider_stats: data.provider_stats || {},
            event_type_stats: data.event_type_stats || {},
            daily_stats: data.daily_stats || []
          }
        }
      }

      throw new Error('获取回调统计失败')
    } catch (error: any) {
      console.error('Get callback statistics failed:', error)
      throw new Error(error.message || '获取回调统计失败')
    }
  }

  /**
   * 导出回调记录
   */
  static async exportCallbackRecords(params: CallbackRecordListParams = {}): Promise<Blob> {
    try {
      const response = await http.get<Blob>({
        url: `${this.BASE_URL}/export`,
        params: {
          user_id: params.user_id || '',
          provider: params.provider || '',
          event_type: params.event_type || '',
          order_no: params.order_no || '',
          tracking_no: params.tracking_no || '',
          internal_status: params.internal_status || '',
          external_status: params.external_status || '',
          start_time: params.start_time || '',
          end_time: params.end_time || '',
          format: 'excel'
        },
        responseType: 'blob'
      })

      return response
    } catch (error: any) {
      console.error('Export callback records failed:', error)
      throw new Error(error.message || '导出回调记录失败')
    }
  }

  /**
   * 获取回调配置（管理员查看用户配置）
   */
  static async getUserCallbackConfig(userId: string): Promise<any> {
    try {
      const response = await http.get<{ success: boolean, data: any }>({
        url: `${this.BASE_URL}/user-config/${userId}`
      })

      if (response.success && response.data) {
        return response.data
      }

      throw new Error('获取用户回调配置失败')
    } catch (error: any) {
      console.error('Get user callback config failed:', error)
      throw new Error(error.message || '获取用户回调配置失败')
    }
  }

  /**
   * 更新用户回调配置（管理员操作）
   */
  static async updateUserCallbackConfig(userId: string, config: any): Promise<void> {
    try {
      const response = await http.post<{ success: boolean, message?: string }>({
        url: `${this.BASE_URL}/user-config/${userId}`,
        data: config
      })

      if (!response.success) {
        throw new Error(response.message || '更新用户回调配置失败')
      }
    } catch (error: any) {
      console.error('Update user callback config failed:', error)
      throw new Error(error.message || '更新用户回调配置失败')
    }
  }
}

export default AdminCallbackManagementService
