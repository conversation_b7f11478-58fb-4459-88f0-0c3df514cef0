import { http } from '@/utils/http'
import type { AdminLoginRequest, AdminLoginResponse } from './model/loginModel'

export class UserService {
  /**
   * 用户登录 (管理员也使用此接口)
   */
  static async login(params: AdminLoginRequest): Promise<AdminLoginResponse> {
    try {
      const response = await http.request<AdminLoginResponse>({
        url: '/api/v1/admin/auth/login',
        method: 'POST',
        data: params,
        timeout: 10000
      })

      // 登录成功后存储令牌和用户信息
      if (response.success && response.access_token) {
        localStorage.setItem('admin_token', response.access_token)
        localStorage.setItem('admin_info', JSON.stringify(response.admin_info))
        localStorage.setItem('admin_permissions', JSON.stringify(response.permissions || []))

        // 设置HTTP请求头
        http.setAuthToken(response.access_token)
      }

      return response
    } catch (error: any) {
      console.error('Admin login failed:', error)

      // 统一错误处理
      if (error.response?.data) {
        return error.response.data as AdminLoginResponse
      }

      return {
        success: false,
        code: 500,
        message: error.message || '登录失败，请稍后重试',
        request_id: 'unknown'
      }
    }
  }

  /**
   * 管理员登出
   */
  static async logout(): Promise<void> {
    try {
      // 清除本地存储
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_info')
      localStorage.removeItem('admin_permissions')

      // 清除HTTP请求头
      http.clearAuthToken()
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  /**
   * 获取当前管理员信息
   */
  static getCurrentAdmin() {
    const adminInfo = localStorage.getItem('admin_info')
    return adminInfo ? JSON.parse(adminInfo) : null
  }

  /**
   * 检查是否已登录
   */
  static isLoggedIn(): boolean {
    const token = localStorage.getItem('admin_token')
    const adminInfo = localStorage.getItem('admin_info')
    return !!(token && adminInfo)
  }
}
