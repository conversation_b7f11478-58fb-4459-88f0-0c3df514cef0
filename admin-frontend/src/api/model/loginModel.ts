// 兼容旧版本登录参数
export interface LoginParams {
  username: string
  password: string
}

// 管理员登录请求模型
export interface AdminLoginRequest {
  username: string
  password: string
}

// 管理员信息模型
export interface AdminInfo {
  id: string
  username: string
  email: string
  roles: string[]
  is_admin: boolean
}

// 管理员登录响应模型
export interface AdminLoginResponse {
  success: boolean
  code: number
  message: string
  access_token?: string
  token_type?: string
  expires_in?: number
  admin_info?: AdminInfo
  permissions?: string[]
  request_id?: string
}

// 通用API响应模型
export interface ApiResponse<T = any> {
  success: boolean
  code: number | string
  message: string
  data?: T
  request_id?: string
  timestamp?: string
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  page_size?: number
  keyword?: string
  status?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// 分页响应数据
export interface PaginationResult<T> {
  items: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}
