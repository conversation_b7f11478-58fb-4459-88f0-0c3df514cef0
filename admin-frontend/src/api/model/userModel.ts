// 用户基础信息模型
export interface User {
  id: string
  username: string
  email: string
  is_active: boolean
  default_role_id: string
  created_at: string
  updated_at: string
  last_login?: string
  login_count?: number
  roles?: Role[]
}

// 角色信息模型
export interface Role {
  id: string
  name: string
  description?: string
  is_system: boolean
  created_at: string
  updated_at: string
}

// 用户创建请求模型
export interface CreateUserRequest {
  username: string
  email: string
  password: string
  role_id: string
  is_active: boolean
}

// 用户更新请求模型
export interface UpdateUserRequest {
  username?: string
  email?: string
  role_id?: string
  is_active?: boolean
}

// 用户状态更新请求模型
export interface UpdateUserStatusRequest {
  is_active: boolean
  reason?: string
}

// 用户密码重置请求模型
export interface ResetPasswordRequest {
  new_password: string
  reason?: string
}

// 用户统计信息模型
export interface UserStatistics {
  total_users: number
  active_users: number
  inactive_users: number
  new_users_today: number
  new_users_this_week: number
  new_users_this_month: number
  login_users_today: number
  login_users_this_week: number
  login_users_this_month: number
}

// 用户列表查询参数
export interface UserListParams {
  page?: number
  page_size?: number
  keyword?: string
  status?: 'active' | 'inactive' | 'all'
  role_id?: string
  sort_by?: 'created_at' | 'last_login' | 'login_count' | 'username'
  sort_order?: 'asc' | 'desc'
  date_from?: string
  date_to?: string
}

// 批量操作请求模型
export interface BatchUserOperationRequest {
  user_ids: string[]
  operation: 'activate' | 'deactivate' | 'delete'
  reason?: string
}

// 用户详情响应模型
export interface UserDetailResponse {
  user: User
  roles: Role[]
  permissions: string[]
  login_history: LoginHistory[]
  audit_logs: AuditLog[]
}

// 登录历史记录
export interface LoginHistory {
  id: string
  user_id: string
  login_time: string
  logout_time?: string
  ip_address: string
  user_agent: string
  login_status: 'success' | 'failed'
  failure_reason?: string
}

// 审计日志
export interface AuditLog {
  id: string
  user_id: string
  operator_id: string
  action: string
  resource_type: string
  resource_id: string
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  ip_address: string
  user_agent: string
  created_at: string
}

// 用户导出参数
export interface UserExportParams {
  format: 'excel' | 'csv'
  fields?: string[]
  filters?: UserListParams
}

// 用户导入结果
export interface UserImportResult {
  total: number
  success: number
  failed: number
  errors: Array<{
    row: number
    field: string
    message: string
  }>
}
