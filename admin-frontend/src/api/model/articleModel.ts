// 文章类型定义
export interface ArticleType {
  id: number | string
  title?: string
  content?: string
  html_content?: string // HTML内容
  cover?: string
  home_img?: string // 首页图片
  brief?: string // 简介
  type_name?: string // 类型名称
  author?: string
  created_at?: string
  updated_at?: string
  create_time?: string // 兼容字段
  status?: 'draft' | 'published' | 'archived'
  tags?: string[]
  category?: string
  blog_class?: string
  count?: number
}
