import { router } from '@/router'
type App = any
type Directive = any

const authDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const authList = (router.currentRoute.value.meta.authList as Array<{ auth_mark: string }>) || []

    const hasPermission = authList.some((item) => item.auth_mark === binding.value)

    if (!hasPermission) {
      el.parentNode?.removeChild(el)
    }
  }
}

export function setupPermissionDirective(app: App) {
  app.directive('auth', authDirective)
}
