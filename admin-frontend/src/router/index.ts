type App = any
import {
  createRouter,
  createWebHashHistory,
  RouteLocationNormalized,
  RouteRecordRaw
} from 'vue-router'
import { ref } from 'vue'
import Home from '@views/index/index.vue'
import { SystemInfo } from '@/config/setting'
import { useUserStore } from '@/store/modules/user'
import { menuService } from '@/api/menuApi'
import { useMenuStore } from '@/store/modules/menu'
import { useSettingStore } from '@/store/modules/setting'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { useTheme } from '@/composables/useTheme'
import { RoutesAlias } from './modules/routesAlias'
import { setWorktab } from '@/utils/worktab'
import { registerAsyncRoutes } from './modules/dynamicRoutes'
import { formatMenuTitle } from '@/utils/menu'

/** 顶部进度条配置 */
NProgress.configure({
  easing: 'ease',
  speed: 600,
  showSpinner: false,
  trickleSpeed: 200,
  parent: 'body'
})

/** 扩展的路由配置类型 */
export type AppRouteRecordRaw = RouteRecordRaw & {
  hidden?: boolean
}

/** 首页路径常量 */
export const HOME_PAGE = '/dashboard/console'

/** 静态路由配置 */
const staticRoutes: AppRouteRecordRaw[] = [
  { path: '/', redirect: HOME_PAGE },
  {
    path: '/dashboard',
    component: Home,
    name: 'Dashboard',
    meta: { title: 'menus.dashboard.title' },
    children: [
      {
        path: RoutesAlias.Dashboard,
        name: 'Console',
        component: () => import('@views/dashboard/Console/index.vue'),
        meta: { title: 'menus.dashboard.console', keepAlive: false }
      }
    ]
  },
  {
    path: RoutesAlias.Login,
    name: 'Login',
    component: () => import('@views/login/index.vue'),
    meta: { title: '管理员登录', isHideTab: true, setTheme: true }
  },
  {
    path: '/exception',
    component: Home,
    name: 'Exception',
    meta: { title: '异常页面' },
    children: [
      {
        path: RoutesAlias.Exception403,
        name: 'Exception403',
        component: () => import('@/views/exception/403.vue'),
        meta: { title: '403' }
      },
      {
        path: RoutesAlias.Exception404,
        name: 'Exception404',
        component: () => import('@views/exception/404.vue'),
        meta: { title: '404' }
      },
      {
        path: RoutesAlias.Exception500,
        name: 'Exception500',
        component: () => import('@views/exception/500.vue'),
        meta: { title: '500' }
      }
    ]
  },
  {
    path: '/outside',
    component: Home,
    name: 'Outside',
    meta: { title: 'menus.outside.title' },
    children: [
      {
        path: '/outside/iframe/:path',
        name: 'Iframe',
        component: () => import('@/views/outside/Iframe.vue'),
        meta: { title: 'iframe' }
      }
    ]
  }
]

/** 创建路由实例 */
export const router = createRouter({
  history: createWebHashHistory(),
  routes: staticRoutes,
  scrollBehavior: () => ({ left: 0, top: 0 })
})

// 路由守卫状态管理
const routeGuardState = {
  isRouteRegistered: ref(false),
  isRegistering: ref(false),
  isRedirecting: ref(false),
  lastRedirectTime: 0,
  redirectCooldown: 1000 // 1秒冷却时间，防止频繁重定向
}

/**
 * 路由全局前置守卫
 * 处理进度条、获取菜单列表、动态路由注册、404 检查、工作标签页及页面标题设置
 *
 * 🔧 修复内容：
 * 1. 添加状态锁机制，防止无限循环
 * 2. 优化重定向逻辑，避免频繁跳转
 * 3. 完善错误处理和日志记录
 */
router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: any) => {
  const settingStore = useSettingStore()
  const userStore = useUserStore()

  // 启动进度条
  if (settingStore.showNprogress) NProgress.start()

  try {
    // 设置登录注册页面主题
    setSystemTheme(to)

    // 🔧 防止频繁重定向的冷却机制
    const now = Date.now()
    if (routeGuardState.isRedirecting.value && (now - routeGuardState.lastRedirectTime) < routeGuardState.redirectCooldown) {
      console.warn('🚨 路由重定向过于频繁，跳过本次处理')
      return next()
    }

    // 🔧 检查登录状态，优化登出逻辑
    if (!userStore.isLogin && to.path !== '/login' && !to.meta?.noLogin) {
      console.log('🔐 用户未登录，准备跳转到登录页')

      // 避免在登出过程中触发页面刷新
      routeGuardState.isRedirecting.value = true
      routeGuardState.lastRedirectTime = now

      // 异步执行登出逻辑，避免阻塞路由跳转
      setTimeout(() => {
        userStore.logOut()
        routeGuardState.isRedirecting.value = false
      }, 100)

      return next('/login')
    }

    // 🔧 动态路由注册逻辑优化
    if (!routeGuardState.isRouteRegistered.value && userStore.isLogin && !routeGuardState.isRegistering.value) {
      // 防止并发注册
      routeGuardState.isRegistering.value = true

      try {
        console.log('📋 开始注册动态路由')
        await getMenuData()
        routeGuardState.isRouteRegistered.value = true
        console.log('✅ 动态路由注册成功')

        // 使用更安全的重定向方式
        return next({ ...to, replace: true })
      } catch (error) {
        console.error('❌ 动态路由注册失败:', error)
        routeGuardState.isRouteRegistered.value = false
        return next('/exception/500')
      } finally {
        routeGuardState.isRegistering.value = false
      }
    }

    // 检查路由是否存在，若不存在则跳转至404页面
    if (to.matched.length === 0) {
      console.warn('🔍 路由不存在:', to.path)
      return next('/exception/404')
    }

    // 设置工作标签页和页面标题
    setWorktab(to)
    setPageTitle(to)

    next()
  } catch (error) {
    console.error('🚨 路由守卫执行异常:', error)
    next('/exception/500')
  }
})

/**
 * 根据接口返回的菜单列表注册动态路由
 *
 * 🔧 修复内容：
 * 1. 添加重试机制和超时控制
 * 2. 完善错误处理和日志记录
 * 3. 优化菜单数据验证逻辑
 *
 * @throws 若菜单列表为空或获取失败则抛出错误
 */
async function getMenuData(): Promise<void> {
  const maxRetries = 3
  let retryCount = 0

  while (retryCount < maxRetries) {
    try {
      console.log(`📋 获取菜单数据 (尝试 ${retryCount + 1}/${maxRetries})`)

      // 设置超时控制
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('获取菜单数据超时')), 10000)
      })

      const menuPromise = menuService.getMenuList()
      const { menuList, closeLoading } = await Promise.race([menuPromise, timeoutPromise]) as any

      // 验证菜单数据
      if (!Array.isArray(menuList)) {
        throw new Error('菜单数据格式错误：不是数组类型')
      }

      if (menuList.length === 0) {
        console.warn('⚠️ 菜单列表为空，使用默认菜单')
        // 可以在这里设置默认菜单或继续使用空菜单
      }

      // 设置菜单列表
      const menuStore = useMenuStore()
      menuStore.setMenuList(menuList as [])

      // 注册异步路由
      registerAsyncRoutes(router, menuList)

      // 关闭加载动画
      if (typeof closeLoading === 'function') {
        closeLoading()
      }

      console.log('✅ 菜单数据获取成功，路由注册完成')
      return

    } catch (error) {
      retryCount++
      console.error(`❌ 获取菜单列表失败 (尝试 ${retryCount}/${maxRetries}):`, error)

      if (retryCount >= maxRetries) {
        // 最后一次重试失败，抛出错误
        throw new Error(`获取菜单数据失败，已重试 ${maxRetries} 次: ${error instanceof Error ? error.message : String(error)}`)
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
    }
  }
}

/* ============================
   路由守卫辅助函数
============================ */

/**
 * 根据路由元信息设置系统主题
 * @param to 当前路由对象
 */
const setSystemTheme = (to: RouteLocationNormalized): void => {
  if (to.meta.setTheme) {
    useTheme().switchTheme(useSettingStore().systemThemeType)
  }
}

/**
 * 设置页面标题，根据路由元信息和系统信息拼接标题
 * @param to 当前路由对象
 */
export const setPageTitle = (to: RouteLocationNormalized): void => {
  const { title } = to.meta
  if (title) {
    document.title = `${formatMenuTitle(String(title))} - ${SystemInfo.name}`
  }
}

/** 路由全局后置守卫 */
router.afterEach(() => {
  if (useSettingStore().showNprogress) NProgress.done()
})

/**
 * 初始化路由，将 Vue Router 实例挂载到 Vue 应用中
 * @param app Vue 应用实例
 */
export function initRouter(app: App): void {
  app.use(router)
}
