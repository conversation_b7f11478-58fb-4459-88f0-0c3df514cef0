// 路由别名
export enum RoutesAlias {
  Home = '/index/index', // 首页
  Login = '/login', // 登录
  Exception403 = '/exception/403', // 403
  Exception404 = '/exception/404', // 404
  Exception500 = '/exception/500', // 500
  Success = '/result/Success', // 成功
  Fail = '/result/Fail', // 失败
  Dashboard = '/dashboard/console', // 工作台
  Server = '/safeguard/Server', // 服务器
  UserManagement = '/users/index', // 用户管理
  OrderManagement = '/orders/index', // 订单管理

  // 角色权限管理
  RoleManagement = '/role-permission/role/index', // 角色管理
  PermissionManagement = '/role-permission/permission/index', // 权限管理
  UserRoleManagement = '/role-permission/user-role/index', // 用户角色管理

  // 回调管理
  CallbackRecords = '/callback-management/records/index', // 回调记录管理
  CallbackStatistics = '/callback-management/statistics/index', // 回调统计监控

  // 其他功能
  Analysis = '/dashboard/analysis', // 数据分析
  Fireworks = '/ceremony/fireworks', // 烟花庆祝
  Chat = '/layout/chat', // 聊天
  PlanLog = '/system/plan-log' // 计划日志
}
