import { defineStore } from 'pinia'
import { LanguageEnum } from '@/enums/appEnum'
import { router, setPageTitle } from '@/router'
import { UserInfo } from '@/types/store'
import { useSettingStore } from './setting'
import { useWorktabStore } from './worktab'
import { getSysStorage } from '@/utils/storage'
import { MenuListType } from '@/types/menu'
import { UserService } from '@/api/usersApi'
import { ApiStatus } from '@/utils/http/status'

interface UserState {
  language: LanguageEnum // 语言
  isLogin: boolean // 是否登录
  isLock: boolean // 是否锁屏
  lockPassword: string // 锁屏密码
  info: Partial<UserInfo> // 用户信息
  searchHistory: MenuListType[] // 搜索历史
}

export const useUserStore = defineStore({
  id: 'userStore',
  state: (): UserState => ({
    language: LanguageEnum.ZH,
    isLogin: false,
    isLock: false,
    lockPassword: '',
    info: {},
    searchHistory: []
  }),
  getters: {
    getUserInfo(): Partial<UserInfo> {
      return this.info
    },
    // 🔧 修复循环依赖问题：使用懒加载方式获取其他store状态
    getSettingState() {
      try {
        const settingStore = useSettingStore()
        return settingStore ? settingStore.$state : {}
      } catch (error) {
        console.warn('⚠️ 获取设置状态失败:', error)
        return {}
      }
    },
    getWorktabState() {
      try {
        const worktabStore = useWorktabStore()
        return worktabStore ? worktabStore.$state : {}
      } catch (error) {
        console.warn('⚠️ 获取工作台状态失败:', error)
        return {}
      }
    }
  },
  actions: {
    // 登录方法 - 使用真实后端接口
    async login(formData: { username: string; password: string }) {
      try {
        const response = await UserService.login({
          username: formData.username,
          password: formData.password
        })

        // 处理管理员登录响应格式
        if (response.success && response.admin_info && response.access_token) {
          // 转换后端数据格式为前端格式
          const userInfo = {
            id: response.admin_info.id,
            name: response.admin_info.username,
            username: response.admin_info.username,
            email: response.admin_info.email,
            avatar: '',
            token: response.access_token,
            roles: response.admin_info.roles,
            permissions: response.permissions || []
          }

          this.setUserInfo(userInfo)
          this.setLoginStatus(true)
          this.saveUserData()

          return {
            status: ApiStatus.success,
            token: response.access_token,
            message: response.message
          }
        } else {
          return {
            status: response.code,
            message: response.message || '登录失败'
          }
        }
      } catch (error: any) {
        console.error('Login error:', error)
        return {
          status: 500,
          message: error.message || '登录失败，请稍后重试'
        }
      }
    },
    initState() {
      let sys = getSysStorage()

      if (sys) {
        sys = JSON.parse(sys)
        const { info, isLogin, language, searchHistory, isLock, lockPassword } = sys.user

        this.info = info || {}
        this.isLogin = isLogin || false
        this.isLock = isLock || false
        this.language = language || LanguageEnum.ZH
        this.searchHistory = searchHistory || []
        this.lockPassword = lockPassword || ''
      }
    },
    // 🔧 用户数据持久化存储（添加防抖机制）
    saveUserData() {
      // 防抖机制：避免频繁保存
      const now = Date.now()
      const lastSaveTime = (this as any)._lastSaveTime || 0
      const saveCooldown = 1000 // 1秒冷却时间

      if (now - lastSaveTime < saveCooldown) {
        // 延迟保存
        clearTimeout((this as any)._saveTimer)
        ;(this as any)._saveTimer = setTimeout(() => {
          this._performSave()
        }, saveCooldown)
        return
      }

      this._performSave()
    },

    // 执行实际的保存操作
    _performSave() {
      try {
        ;(this as any)._lastSaveTime = Date.now()

        saveStoreStorage({
          user: {
            info: this.info,
            isLogin: this.isLogin,
            language: this.language,
            isLock: this.isLock,
            lockPassword: this.lockPassword,
            searchHistory: this.searchHistory,
            worktab: this.getWorktabState,
            setting: this.getSettingState
          }
        })

        console.log('💾 用户数据已保存')
      } catch (error) {
        console.error('❌ 用户数据保存失败:', error)
      }
    },
    setUserInfo(info: UserInfo) {
      this.info = info
    },
    setLoginStatus(isLogin: boolean) {
      this.isLogin = isLogin
    },
    setLanguage(lang: LanguageEnum) {
      setPageTitle(router.currentRoute.value)
      this.language = lang
    },
    setSearchHistory(list: Array<MenuListType>) {
      this.searchHistory = list
    },
    setLockStatus(isLock: boolean) {
      this.isLock = isLock
    },
    setLockPassword(password: string) {
      this.lockPassword = password
    },
    // 🔧 优化登出逻辑，避免触发页面刷新
    async logOut() {
      console.log('🚪 开始执行登出操作')

      try {
        // 调用后端登出接口（设置超时）
        const logoutPromise = UserService.logout()
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('登出请求超时')), 5000)
        })

        await Promise.race([logoutPromise, timeoutPromise])
        console.log('✅ 后端登出成功')
      } catch (error) {
        console.warn('⚠️ 后端登出失败，继续清理前端状态:', error)
      }

      // 🔧 同步清理前端状态，避免异步延迟
      try {
        // 清理用户信息
        this.info = {}
        this.isLogin = false
        this.isLock = false
        this.lockPassword = ''

        // 清理工作台状态
        const worktabStore = useWorktabStore()
        if (worktabStore) {
          worktabStore.opened = []
        }

        // 清理会话存储
        sessionStorage.removeItem('iframeRoutes')
        localStorage.removeItem('admin_token')

        // 保存状态变更
        this._performSave()

        console.log('🧹 前端状态清理完成')

        // 🔧 使用路由替换而不是推送，避免历史记录问题
        await router.replace('/login')
        console.log('🔄 已跳转到登录页')

      } catch (error) {
        console.error('❌ 登出过程中发生异常:', error)
        // 强制跳转到登录页
        window.location.hash = '#/login'
      }
    }
  }
})

function initVersion(version: string) {
  const vs = localStorage.getItem('version')
  if (!vs) {
    localStorage.setItem('version', version)
  }
}

// 数据持久化存储
function saveStoreStorage<T>(newData: T) {
  const version = import.meta.env.VITE_VERSION
  initVersion(version)
  const vs = localStorage.getItem('version') || version
  const storedData = JSON.parse(localStorage.getItem(`sys-v${vs}`) || '{}')

  // 合并新数据与现有数据
  const mergedData = { ...storedData, ...newData }
  localStorage.setItem(`sys-v${vs}`, JSON.stringify(mergedData))
}
