/// <reference types="vue/macros-global" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 扩展Performance接口以支持memory属性
declare global {
  interface Performance {
    memory?: {
      usedJSHeapSize: number
      totalJSHeapSize: number
      jsHeapSizeLimit: number
    }
  }
}

// Vue模块类型声明
declare module 'vue' {
  export * from '@vue/runtime-dom'
  export * from '@vue/reactivity'
  export * from '@vue/shared'
  export { default } from '@vue/runtime-dom'

  // 确保组合式API函数可用
  export declare function onMounted(hook: () => any): void
  export declare function onUnmounted(hook: () => any): void
  export declare function onErrorCaptured(hook: (err: any, instance: any, info: any) => boolean | void): void
  export declare function nextTick(): Promise<void>
  export declare function nextTick<T = void>(fn?: () => T): Promise<T>
}

// 重新导出vue-router模块
declare module 'vue-router' {
  import type { App } from 'vue'
  import type { RouteLocationNormalized, RouteRecordRaw } from 'vue-router/dist/vue-router'
  
  export interface Router {
    readonly options: RouterOptions
    readonly currentRoute: Ref<RouteLocationNormalized>
    addRoute(parentName: string | symbol, route: RouteRecordRaw): () => void
    addRoute(route: RouteRecordRaw): () => void
    removeRoute(name: string | symbol): void
    hasRoute(name: string | symbol): boolean
    getRoutes(): RouteRecordRaw[]
    resolve(raw: RouteLocationRaw, currentLocation?: RouteLocationNormalized): RouteLocation & { href: string }
    push(to: RouteLocationRaw): Promise<NavigationFailure | void | undefined>
    replace(to: RouteLocationRaw): Promise<NavigationFailure | void | undefined>
    go(delta: number): void
    back(): ReturnType<Router['go']>
    forward(): ReturnType<Router['go']>
    beforeEach(guard: NavigationGuardWithThis<undefined>): () => void
    beforeResolve(guard: NavigationGuardWithThis<undefined>): () => void
    afterEach(guard: NavigationHookAfter): () => void
    onError(handler: (error: any, to: RouteLocationNormalized, from: RouteLocationNormalized) => any): () => void
    isReady(): Promise<void>
    install(app: App): void
  }
  
  export * from 'vue-router/dist/vue-router'
} 