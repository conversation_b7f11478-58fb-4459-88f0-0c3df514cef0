{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "lib": ["esnext", "dom"], "types": ["vite/client", "node"], "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@views/*": ["src/views/*"], "@components/*": ["src/components/*"], "@imgs/*": ["src/assets/img/*"]}}, "include": ["src/**/*", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist", "**/*.js"]}