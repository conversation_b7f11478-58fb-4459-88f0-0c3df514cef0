var e=Object.defineProperty,o=Object.defineProperties,l=Object.getOwnPropertyDescriptors,r=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,t=(o,l,r)=>l in o?e(o,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[l]=r;import{_ as i}from"./index-rNRt1EuS.js";import{E as d}from"./expressCompanyApi-DSXUWAhW.js";import{d as c,r as u,X as n,j as p,p as m,N as _,o as v,w as g,b,Z as y,_ as h,e as f,C as x,c as V,a as w,bb as O,ap as j,aq as U,H as C,K as P,x as k,Y as L,M as R}from"./vendor-CAPBtMef.js";const q={key:0,class:"logo-preview"},E={class:"dialog-footer"},Z=i(c({__name:"CompanyEditDialog",props:{visible:{type:Boolean},company:{default:null}},emits:["update:visible","success"],setup(e,{emit:i}){const c=e,Z=i,A=u(),D=u(!1),I=n({code:"",name:"",logo_url:"",customer_service_phone:"",description:"",is_active:!0,sort_order:0}),T={code:[{required:!0,message:"请输入快递公司代码",trigger:"blur"},{min:2,max:20,message:"代码长度应在2-20个字符之间",trigger:"blur"},{pattern:/^[A-Z0-9_]+$/,message:"代码只能包含大写字母、数字和下划线",trigger:"blur"}],name:[{required:!0,message:"请输入快递公司名称",trigger:"blur"},{min:2,max:100,message:"名称长度应在2-100个字符之间",trigger:"blur"}],logo_url:[{type:"url",message:"请输入有效的URL地址",trigger:"blur"}],customer_service_phone:[{pattern:/^[\d\-\+\(\)\s]+$/,message:"请输入有效的电话号码",trigger:"blur"}],description:[{max:500,message:"描述长度不能超过500个字符",trigger:"blur"}],sort_order:[{type:"number",min:0,max:9999,message:"排序值应在0-9999之间",trigger:"blur"}]},Y=p({get:()=>c.visible,set:e=>Z("update:visible",e)}),$=p((()=>{var e;return!!(null==(e=c.company)?void 0:e.id)}));m((()=>c.visible),(e=>{e&&(B(),c.company&&Object.assign(I,{code:c.company.code,name:c.company.name,logo_url:c.company.logo_url||"",customer_service_phone:c.company.customer_service_phone||"",description:c.company.description||"",is_active:c.company.is_active,sort_order:c.company.sort_order}))}));const B=()=>{var e;Object.assign(I,{code:"",name:"",logo_url:"",customer_service_phone:"",description:"",is_active:!0,sort_order:0}),null==(e=A.value)||e.clearValidate()},H=()=>{Y.value=!1},K=()=>{return e=this,i=null,u=function*(){var e,i;if(A.value)try{if(!(yield A.value.validate()))return;if(D.value=!0,$.value){const e={name:I.name,logo_url:I.logo_url||void 0,customer_service_phone:I.customer_service_phone||void 0,description:I.description||void 0,is_active:I.is_active,sort_order:I.sort_order},o=yield d.updateCompany(c.company.id,e);o.success?(R.success("快递公司更新成功"),Z("success"),H()):R.error(o.message||"更新失败")}else{const c=(e=((e,o)=>{for(var l in o||(o={}))a.call(o,l)&&t(e,l,o[l]);if(r)for(var l of r(o))s.call(o,l)&&t(e,l,o[l]);return e})({},I),i={logo_url:I.logo_url||void 0,customer_service_phone:I.customer_service_phone||void 0,description:I.description||void 0},o(e,l(i))),u=yield d.createCompany(c);u.success?(R.success("快递公司创建成功"),Z("success"),H()):R.error(u.message||"创建失败")}}catch(u){R.error("操作失败")}finally{D.value=!1}},new Promise(((o,l)=>{var r=e=>{try{s(u.next(e))}catch(o){l(o)}},a=e=>{try{s(u.throw(e))}catch(o){l(o)}},s=e=>e.done?o(e.value):Promise.resolve(e.value).then(r,a);s((u=u.apply(e,i)).next())}));var e,i,u};return(e,o)=>{const l=x,r=h,a=O,s=j,t=U,i=y,d=C,c=L;return v(),_(c,{modelValue:Y.value,"onUpdate:modelValue":o[7]||(o[7]=e=>Y.value=e),title:$.value?"编辑快递公司":"新增快递公司",width:"600px","close-on-click-modal":!1,onClose:H},{footer:g((()=>[f("div",E,[b(d,{onClick:H},{default:g((()=>o[11]||(o[11]=[P("取消")]))),_:1}),b(d,{type:"primary",loading:D.value,onClick:K},{default:g((()=>[P(k($.value?"更新":"创建"),1)])),_:1},8,["loading"])])])),default:g((()=>[b(i,{ref_key:"formRef",ref:A,model:I,rules:T,"label-width":"120px"},{default:g((()=>[b(r,{label:"快递公司代码",prop:"code"},{default:g((()=>[b(l,{modelValue:I.code,"onUpdate:modelValue":o[0]||(o[0]=e=>I.code=e),placeholder:"请输入快递公司代码（如：ZTO、YTO等）",disabled:$.value,maxlength:"20","show-word-limit":""},null,8,["modelValue","disabled"]),o[8]||(o[8]=f("div",{class:"form-tip"},"快递公司的唯一标识，创建后不可修改",-1))])),_:1}),b(r,{label:"快递公司名称",prop:"name"},{default:g((()=>[b(l,{modelValue:I.name,"onUpdate:modelValue":o[1]||(o[1]=e=>I.name=e),placeholder:"请输入快递公司名称",maxlength:"100","show-word-limit":""},null,8,["modelValue"])])),_:1}),b(r,{label:"Logo URL",prop:"logo_url"},{default:g((()=>[b(l,{modelValue:I.logo_url,"onUpdate:modelValue":o[2]||(o[2]=e=>I.logo_url=e),placeholder:"请输入Logo图片URL（可选）",maxlength:"500"},null,8,["modelValue"]),I.logo_url?(v(),V("div",q,[b(a,{src:I.logo_url,fit:"cover",style:{width:"60px",height:"60px","border-radius":"4px","margin-top":"8px"},"preview-src-list":[I.logo_url]},null,8,["src","preview-src-list"])])):w("",!0)])),_:1}),b(r,{label:"客服电话",prop:"customer_service_phone"},{default:g((()=>[b(l,{modelValue:I.customer_service_phone,"onUpdate:modelValue":o[3]||(o[3]=e=>I.customer_service_phone=e),placeholder:"请输入客服电话（可选）",maxlength:"50"},null,8,["modelValue"])])),_:1}),b(r,{label:"描述",prop:"description"},{default:g((()=>[b(l,{modelValue:I.description,"onUpdate:modelValue":o[4]||(o[4]=e=>I.description=e),type:"textarea",rows:3,placeholder:"请输入快递公司描述（可选）",maxlength:"500","show-word-limit":""},null,8,["modelValue"])])),_:1}),b(r,{label:"状态",prop:"is_active"},{default:g((()=>[b(s,{modelValue:I.is_active,"onUpdate:modelValue":o[5]||(o[5]=e=>I.is_active=e),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"]),o[9]||(o[9]=f("div",{class:"form-tip"},"禁用后，该快递公司将不会在前端显示",-1))])),_:1}),b(r,{label:"排序",prop:"sort_order"},{default:g((()=>[b(t,{modelValue:I.sort_order,"onUpdate:modelValue":o[6]||(o[6]=e=>I.sort_order=e),min:0,max:9999,placeholder:"排序值",style:{width:"200px"}},null,8,["modelValue"]),o[10]||(o[10]=f("div",{class:"form-tip"},"数值越小排序越靠前",-1))])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-8752a3b0"]]);export{Z as default};
