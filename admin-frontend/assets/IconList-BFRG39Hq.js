import{_ as e}from"./index-rNRt1EuS.js";import{e as l}from"./iconfont-DPUoc2h2.js";import{d as a,r as s,f as o,c as n,o as c,e as t,b as u,w as d,F as i,A as r,as as m,u as v,m as f,ar as p,aD as y,n as b,v as h,x as C,M as k}from"./vendor-CAPBtMef.js";const x={class:"page-content"},V={class:"form"},_={class:"colors-icon"},g={class:"list"},M={class:"icon-list"},N=["onClick"],j=["innerHTML"],L=e(a({__name:"IconList",setup(e){const a=s("unicode"),L=[{value:"unicode",label:"Unicode"},{value:"fontClass",label:"Font class"}],U=s([]),w=s(!1);o((()=>{U.value=l()}));const z=()=>{const e=["#2d8cf0","#19be6b","#ff9900","#f24965","#9463f7"];return e[Math.floor(Math.random()*e.length)]},A=()=>w.value?{color:z()}:{color:"var(--art-text-gray-700)"};return(e,l)=>{const s=m,o=p,z=y;return c(),n("div",x,[t("div",V,[u(o,{modelValue:v(a),"onUpdate:modelValue":l[0]||(l[0]=e=>f(a)?a.value=e:null),placeholder:"Select",size:"large",style:{width:"240px"}},{default:d((()=>[(c(),n(i,null,r(L,(e=>u(s,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),t("div",_,[u(z,{modelValue:v(w),"onUpdate:modelValue":l[1]||(l[1]=e=>f(w)?w.value=e:null),label:"彩色图标",size:"large"},null,8,["modelValue"])])]),t("div",g,[t("ul",M,[(c(!0),n(i,null,r(v(U),(e=>(c(),n("li",{key:e.className,onClick:l=>(e=>{if(!e)return;let l=document.createElement("input");l.setAttribute("value",("unicode"===a.value?e.unicode:e.className)||""),document.body.appendChild(l),l.select(),document.execCommand("copy"),document.body.removeChild(l),k.success("已复制")})(e)},["unicode"===v(a)?(c(),n("i",{key:0,class:"iconfont-sys",innerHTML:e.unicode,style:b(A())},null,12,j)):(c(),n("i",{key:1,class:h(`iconfont-sys ${e.className}`),style:b(A())},null,6)),t("span",null,C("unicode"===v(a)?e.unicode:e.className),1)],8,N)))),128))])])])}}}),[["__scopeId","data-v-5e48dd3d"]]);export{L as default};
