var e=Object.defineProperty,a=Object.defineProperties,s=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,r=(a,s,l)=>s in a?e(a,s,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[s]=l,i=(e,a)=>{for(var s in a||(a={}))t.call(a,s)&&r(e,s,a[s]);if(l)for(var s of l(a))o.call(a,s)&&r(e,s,a[s]);return e},c=(e,l)=>a(e,s(l)),u=(e,a,s)=>new Promise(((l,t)=>{var o=e=>{try{i(s.next(e))}catch(a){t(a)}},r=e=>{try{i(s.throw(e))}catch(a){t(a)}},i=e=>e.done?l(e.value):Promise.resolve(e.value).then(o,r);i((s=s.apply(e,a)).next())}));import{_ as n}from"./index-rNRt1EuS.js";/* empty css                    *//* empty css                *//* empty css               */import{d,r as p,X as _,j as v,p as m,M as f,c as g,o as y,F as h,b,Y as w,u as k,w as x,e as C,a as j,aT as q,aN as z,x as S,aV as $,Z as O,_ as U,ar as V,A as P,N as R,as as T,H as D,K as I,y as L,a3 as N,aU as B,aS as E,aQ as F,bs as M,a2 as A,aI as H,aJ as X,aX as G,aG as J,aH as K,b8 as Q,ai as Y}from"./vendor-CAPBtMef.js";import Z from"./EnhancedRouteUpdateDialog-DfXZIVhN.js";import{F as W,a as ee}from"./flexiblePriceTableApi-BBM6dCg8.js";import{a as ae,d as se,i as le,e as te,f as oe,h as re,c as ie}from"./priceTable-dC3_lJoq.js";/* empty css                       *//* empty css                 */const ce={class:"route-details-dialog"},ue={class:"summary-info"},ne={class:"summary-item"},de={class:"value"},pe={class:"summary-item"},_e={class:"value success"},ve={class:"summary-item"},me={class:"value error"},fe={class:"summary-item"},ge={class:"value"},ye={class:"filters"},he={class:"routes-table"},be={class:"province-name"},we={class:"province-name"},ke={key:0,class:"weight-value"},xe={key:1,class:"no-data"},Ce={key:0,class:"price-amount"},je={key:1,class:"no-data"},qe={key:0,class:"weight-value"},ze={key:1,class:"no-data"},Se={key:0,class:"price-amount"},$e={key:1,class:"no-data"},Oe={key:0,class:"volume-ratio"},Ue={key:1,class:"no-data"},Ve={key:0,class:"weight-range"},Pe={key:1,class:"no-data"},Re={key:0,class:"query-time"},Te={key:1,class:"no-data"},De={key:0,class:"error-message"},Ie={key:1,class:"no-error"},Le={class:"pagination-wrapper"},Ne={key:0,class:"batch-update-progress"},Be={class:"progress-header"},Ee={class:"progress-content"},Fe={class:"progress-stats"},Me={key:0,class:"current-route"},Ae={class:"dialog-footer"},He=n(d({__name:"RouteDetailsDialog",props:{visible:{type:Boolean,default:!1},provider:{default:""},expressCode:{default:""},expressName:{default:""}},emits:["update:visible"],setup(e,{emit:a}){const s=e,l=a,t=p(!1),o=p(!1),r=p(!1),n=p(!1),d=p([]),He=p([]),Xe=_({total_routes:0,success_routes:0,failed_routes:0,success_rate:0}),Ge=_({visible:!1,percentage:0,status:"active",total:0,completed_count:0,success_count:0,failed_count:0,current_route:"",completed:!1}),Je=_({page:1,page_size:20,from_provinces:void 0,to_provinces:void 0,query_success:void 0}),Ke=_({page:1,pageSize:20,total:0}),Qe=v({get:()=>s.visible,set:e=>l("update:visible",e)});m((()=>s.visible),(e=>{e&&s.provider&&s.expressCode&&Ye()}));const Ye=()=>u(this,null,(function*(){try{t.value=!0;const e=c(i({},Je),{page:Ke.page,page_size:Ke.pageSize}),a=yield W.queryRouteDetails(s.provider,s.expressCode,e);a.success?(d.value=a.data.records,Ke.total=a.data.total,Ke.page=a.data.page,Ke.pageSize=a.data.page_size,yield Ze()):f.error(a.message||"获取路线详情失败")}catch(e){f.error("获取路线详情失败")}finally{t.value=!1}})),Ze=()=>u(this,null,(function*(){try{const e={providers:[s.provider],express_codes:[s.expressCode],from_provinces:Je.from_provinces,to_provinces:Je.to_provinces,query_success:Je.query_success},a=yield W.getRouteSummary(e);a.success&&Object.assign(Xe,a.data)}catch(e){We()}})),We=()=>{const e=Ke.total,a=d.value.filter((e=>e.query_success)).length;d.value.length;const s=d.value.length>0?a/d.value.length:0,l=Math.round(e*s),t=e-l;Xe.total_routes=e,Xe.success_routes=l,Xe.failed_routes=t,Xe.success_rate=e>0?l/e*100:0},ea=()=>{Ke.page=1,Ye()},aa=()=>{Object.assign(Je,{page:1,page_size:20,from_provinces:void 0,to_provinces:void 0,query_success:void 0}),Ke.page=1,Ke.pageSize=20,Ye(),f.success("搜索条件已重置")},sa=({prop:e,order:a})=>{if(a&&e){const s="ascending"===a?"asc":"desc";Je.sort_by=[`${e}_${s}`]}else Je.sort_by=void 0;Ye()},la=e=>{Ke.pageSize=e,Ke.page=1,Ye()},ta=e=>{Ke.page=e,Ye()},oa=e=>{if(!e.query_success)return"-";return`${e.first_weight||1}-${e.max_weight||50}kg`},ra=e=>u(this,null,(function*(){try{e._updating=!0;const a={provider:s.provider,express_code:s.expressCode,from_province:e.from_province,to_province:e.to_province},l=yield W.updateSingleRoute(a);if(l.success){const a=l.data,s=d.value.findIndex((a=>a.from_province===e.from_province&&a.to_province===e.to_province));-1!==s&&(d.value[s]=c(i({},a),{_updating:!1})),f.success(`${e.from_province} → ${e.to_province} 路线更新成功`),yield Ze()}else f.error(l.message||"路线更新失败")}catch(a){f.error("路线更新失败："+((null==a?void 0:a.message)||"未知错误"))}finally{e._updating=!1}})),ia=e=>{He.value=e},ca=()=>u(this,null,(function*(){if(0!==He.value.length)try{yield Y.confirm(`确定要批量更新选中的 ${He.value.length} 条路线吗？`,"批量更新确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),r.value=!0,Ge.visible=!0,Ge.percentage=0,Ge.status="active",Ge.total=He.value.length,Ge.completed_count=0,Ge.success_count=0,Ge.failed_count=0,Ge.completed=!1;for(let a=0;a<He.value.length;a++){const l=He.value[a];Ge.current_route=`${l.from_province} → ${l.to_province}`;try{const e={provider:s.provider,express_code:s.expressCode,from_province:l.from_province,to_province:l.to_province},a=yield W.updateSingleRoute(e);if(a.success){Ge.success_count++;const e=d.value.findIndex((e=>e.from_province===l.from_province&&e.to_province===l.to_province));-1!==e&&(d.value[e]=i({},a.data))}else Ge.failed_count++}catch(e){Ge.failed_count++}Ge.completed_count++,Ge.percentage=Math.round(Ge.completed_count/Ge.total*100),a<He.value.length-1&&(yield new Promise((e=>setTimeout(e,500))))}Ge.status=Ge.failed_count>0?"exception":"success",Ge.completed=!0,Ge.current_route="",f.success(`批量更新完成！成功: ${Ge.success_count}, 失败: ${Ge.failed_count}`),yield Ze(),He.value=[]}catch(e){"cancel"!==e&&f.error("批量更新失败")}finally{r.value=!1}else f.warning("请先选择要更新的路线")})),ua=()=>{Ge.visible=!1},na=()=>{n.value=!0},da=e=>u(this,null,(function*(){yield Ye(),yield Ze(),f.success("增强更新完成")})),pa=()=>u(this,null,(function*(){try{o.value=!0;const e={providers:[s.provider],express_codes:[s.expressCode],from_provinces:Je.from_provinces,to_provinces:Je.to_provinces,query_success:Je.query_success,format:"csv",include_failed:!0},a=yield W.downloadFile(e),l=(new Date).toISOString().slice(0,19).replace(/[-:]/g,"").replace("T","_"),t=`${s.provider}_${s.expressCode}_routes_${l}.csv`,r=window.URL.createObjectURL(a),i=document.createElement("a");i.href=r,i.download=t,i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(r),f.success(`导出成功：${t}`)}catch(e){f.error("导出失败："+(e.message||"未知错误"))}finally{o.value=!1}}));return(e,a)=>{const s=z,l=q,i=T,c=V,u=U,p=L,_=D,v=O,m=$,f=X,Y=G,W=H,Ye=K,Ze=Q,We=w,_a=J;return y(),g(h,null,[b(We,{modelValue:Qe.value,"onUpdate:modelValue":a[6]||(a[6]=e=>Qe.value=e),title:`${k(ae)(e.provider)} - ${e.expressName} 价格明细`,width:"90%",top:"5vh","close-on-click-modal":!1,"destroy-on-close":""},{footer:x((()=>[C("div",Ae,[b(_,{onClick:a[5]||(a[5]=e=>Qe.value=!1)},{default:x((()=>a[19]||(a[19]=[I("关闭")]))),_:1})])])),default:x((()=>[C("div",ce,[C("div",ue,[b(l,{gutter:20},{default:x((()=>[b(s,{span:6},{default:x((()=>[C("div",ne,[a[8]||(a[8]=C("div",{class:"label"},"总路线数",-1)),C("div",de,S(Xe.total_routes),1)])])),_:1}),b(s,{span:6},{default:x((()=>[C("div",pe,[a[9]||(a[9]=C("div",{class:"label"},"成功路线",-1)),C("div",_e,S(Xe.success_routes),1)])])),_:1}),b(s,{span:6},{default:x((()=>[C("div",ve,[a[10]||(a[10]=C("div",{class:"label"},"失败路线",-1)),C("div",me,S(Xe.failed_routes),1)])])),_:1}),b(s,{span:6},{default:x((()=>[C("div",fe,[a[11]||(a[11]=C("div",{class:"label"},"成功率",-1)),C("div",ge,S(k(se)(Xe.success_rate)),1)])])),_:1})])),_:1})]),C("div",ye,[b(m,null,{default:x((()=>[b(v,{model:Je,"label-width":"100px",inline:""},{default:x((()=>[b(u,{label:"发货省份"},{default:x((()=>[b(c,{modelValue:Je.from_provinces,"onUpdate:modelValue":a[0]||(a[0]=e=>Je.from_provinces=e),placeholder:"请选择发货省份",multiple:"",clearable:"",style:{width:"200px"}},{default:x((()=>[(y(!0),g(h,null,P(k(ee),(e=>(y(),R(i,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),b(u,{label:"收货省份"},{default:x((()=>[b(c,{modelValue:Je.to_provinces,"onUpdate:modelValue":a[1]||(a[1]=e=>Je.to_provinces=e),placeholder:"请选择收货省份",multiple:"",clearable:"",style:{width:"200px"}},{default:x((()=>[(y(!0),g(h,null,P(k(ee),(e=>(y(),R(i,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),b(u,{label:"查询状态"},{default:x((()=>[b(c,{modelValue:Je.query_success,"onUpdate:modelValue":a[2]||(a[2]=e=>Je.query_success=e),placeholder:"请选择查询状态",clearable:"",style:{width:"120px"}},{default:x((()=>[b(i,{label:"成功",value:!0}),b(i,{label:"失败",value:!1})])),_:1},8,["modelValue"])])),_:1}),b(u,null,{default:x((()=>[b(_,{type:"primary",onClick:ea,loading:t.value},{default:x((()=>[b(p,null,{default:x((()=>[b(k(N))])),_:1}),a[12]||(a[12]=I(" 搜索 "))])),_:1},8,["loading"]),b(_,{onClick:aa},{default:x((()=>[b(p,null,{default:x((()=>[b(k(B))])),_:1}),a[13]||(a[13]=I(" 重置 "))])),_:1}),b(_,{type:"success",onClick:pa,loading:o.value},{default:x((()=>[b(p,null,{default:x((()=>[b(k(E))])),_:1}),a[14]||(a[14]=I(" 导出 "))])),_:1},8,["loading"]),b(_,{type:"warning",onClick:ca,loading:r.value,disabled:0===He.value.length},{default:x((()=>[b(p,null,{default:x((()=>[b(k(F))])),_:1}),I(" 批量更新 ("+S(He.value.length)+") ",1)])),_:1},8,["loading","disabled"]),b(_,{type:"primary",onClick:na,disabled:0===d.value.length},{default:x((()=>[b(p,null,{default:x((()=>[b(k(M))])),_:1}),a[15]||(a[15]=I(" 高级更新 "))])),_:1},8,["disabled"])])),_:1})])),_:1},8,["model"])])),_:1})]),C("div",he,[A((y(),R(W,{data:d.value,onSortChange:sa,onSelectionChange:ia,stripe:"",border:"",style:{width:"100%"},"max-height":"500"},{default:x((()=>[b(f,{type:"selection",width:"55"}),b(f,{prop:"from_province",label:"始发地",width:"100",sortable:"custom"},{default:x((({row:e})=>[C("span",be,S(e.from_province),1)])),_:1}),b(f,{prop:"to_province",label:"目的地",width:"100",sortable:"custom"},{default:x((({row:e})=>[C("span",we,S(e.to_province),1)])),_:1}),b(f,{prop:"first_weight",label:"首重量",width:"100",sortable:"custom"},{default:x((({row:e})=>[e.query_success&&k(le)(e.first_weight)?(y(),g("span",ke,S(e.first_weight)+"kg ",1)):(y(),g("span",xe,"-"))])),_:1}),b(f,{prop:"first_weight_price",label:"首重价格",width:"120",sortable:"custom"},{default:x((({row:e})=>[e.query_success&&k(te)(e.first_weight_price)?(y(),g("span",Ce,S(k(oe)(e.first_weight_price)),1)):(y(),g("span",je,"-"))])),_:1}),b(f,{prop:"continue_weight",label:"续重量",width:"100",sortable:"custom"},{default:x((({row:e})=>[e.query_success&&k(le)(e.continue_weight)?(y(),g("span",qe,S(e.continue_weight)+"kg ",1)):(y(),g("span",ze,"-"))])),_:1}),b(f,{prop:"continue_weight_price",label:"续重价格",width:"120",sortable:"custom"},{default:x((({row:e})=>[e.query_success&&k(te)(e.continue_weight_price)?(y(),g("span",Se,S(k(oe)(e.continue_weight_price))+"/kg ",1)):(y(),g("span",$e,"-"))])),_:1}),b(f,{prop:"volume_ratio",label:"抛比",width:"100",sortable:"custom"},{default:x((({row:e})=>[e.query_success&&k(re)(e.volume_ratio)?(y(),g("span",Oe,S(e.volume_ratio),1)):(y(),g("span",Ue,"-"))])),_:1}),b(f,{label:"重量范围",width:"120"},{default:x((({row:e})=>[e.query_success?(y(),g("span",Ve,S(oa(e)),1)):(y(),g("span",Pe,"-"))])),_:1}),b(f,{label:"状态",width:"100"},{default:x((({row:e})=>[b(Y,{type:e.query_success?"success":"danger",size:"small"},{default:x((()=>[I(S(e.query_success?"成功":"失败"),1)])),_:2},1032,["type"])])),_:1}),b(f,{label:"查询时间",width:"160",sortable:"custom"},{default:x((({row:e})=>[e.query_time?(y(),g("span",Re,S(k(ie)(e.query_time)),1)):(y(),g("span",Te,"-"))])),_:1}),b(f,{label:"错误信息","min-width":"200","show-overflow-tooltip":""},{default:x((({row:e})=>[!e.query_success&&e.error_message?(y(),g("span",De,S(e.error_message),1)):(y(),g("span",Ie,"-"))])),_:1}),b(f,{label:"操作",width:"120",fixed:"right"},{default:x((({row:e})=>[b(_,{type:"primary",size:"small",onClick:a=>ra(e),loading:e._updating,disabled:e._updating},{default:x((()=>[b(p,null,{default:x((()=>[b(k(F))])),_:1}),a[16]||(a[16]=I(" 更新 "))])),_:2},1032,["onClick","loading","disabled"])])),_:1})])),_:1},8,["data"])),[[_a,t.value]]),C("div",Le,[b(Ye,{"current-page":Ke.page,"onUpdate:currentPage":a[3]||(a[3]=e=>Ke.page=e),"page-size":Ke.pageSize,"onUpdate:pageSize":a[4]||(a[4]=e=>Ke.pageSize=e),"page-sizes":[20,50,100,200],total:Ke.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:la,onCurrentChange:ta},null,8,["current-page","page-size","total"])])]),Ge.visible?(y(),g("div",Ne,[b(m,null,{header:x((()=>[C("div",Be,[a[18]||(a[18]=C("span",null,"批量更新进度",-1)),Ge.completed?(y(),R(_,{key:0,type:"success",size:"small",onClick:ua},{default:x((()=>a[17]||(a[17]=[I(" 完成 ")]))),_:1})):j("",!0)])])),default:x((()=>[C("div",Ee,[b(Ze,{percentage:Ge.percentage,status:Ge.status,"stroke-width":8},null,8,["percentage","status"]),C("div",Fe,[C("span",null,"总计: "+S(Ge.total),1),C("span",null,"已完成: "+S(Ge.completed_count),1),C("span",null,"成功: "+S(Ge.success_count),1),C("span",null,"失败: "+S(Ge.failed_count),1)]),Ge.current_route?(y(),g("div",Me," 正在更新: "+S(Ge.current_route),1)):j("",!0)])])),_:1})])):j("",!0)])])),_:1},8,["modelValue","title"]),b(Z,{visible:n.value,"onUpdate:visible":a[7]||(a[7]=e=>n.value=e),provider:e.provider,"express-code":e.expressCode,"selected-routes":He.value,"filtered-routes":d.value,onUpdateCompleted:da},null,8,["visible","provider","express-code","selected-routes","filtered-routes"])],64)}}}),[["__scopeId","data-v-9c12e450"]]);export{He as default};
