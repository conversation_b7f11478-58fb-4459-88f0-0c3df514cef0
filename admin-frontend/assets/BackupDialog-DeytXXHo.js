var e=(e,a,l)=>new Promise(((t,o)=>{var i=e=>{try{r(l.next(e))}catch(a){o(a)}},s=e=>{try{r(l.throw(e))}catch(a){o(a)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(i,s);r((l=l.apply(e,a)).next())}));import{_ as a}from"./index-rNRt1EuS.js";/* empty css                 *//* empty css                             */import{d as l,r as t,X as o,p as i,M as s,N as r,o as u,w as n,e as c,b as p,H as d,u as m,aR as f,K as _,aQ as g,a2 as b,aI as v,aJ as k,x as h,aG as y,aH as w,Y as x,c as z,a as B,b1 as C,b2 as V,Z as j,_ as U,C as S,a$ as M,ai as $}from"./vendor-CAPBtMef.js";import{S as L}from"./systemConfigApi-Cw3xPQeV.js";const O={class:"toolbar"},R={class:"backup-list"},T={class:"pagination-wrapper"},D={key:0,class:"backup-detail"},F={class:"config-list"},I={class:"dialog-footer"},N={class:"dialog-footer"},P=a(l({__name:"BackupDialog",props:{visible:{type:Boolean}},emits:["update:visible","restore-backup"],setup(a,{emit:l}){const P=a,A=l,G=t(!1),H=t(!1),J=t(!1),K=t([]),Z=o({page:1,page_size:20,total:0}),q=t(!1),E=t(!1),Q=t(null),X=t(),Y=o({backup_name:"",backup_description:""}),W={backup_name:[{required:!0,message:"请输入备份名称",trigger:"blur"},{min:2,max:100,message:"备份名称长度在 2 到 100 个字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/,message:"备份名称只能包含字母、数字、下划线、横线和中文",trigger:"blur"}],backup_description:[{max:500,message:"描述长度不能超过 500 个字符",trigger:"blur"}]};i((()=>P.visible),(e=>{G.value=e,e&&ee()})),i(G,(e=>{A("update:visible",e)}));const ee=()=>e(this,null,(function*(){try{H.value=!0;const e=yield L.getBackupList({page:Z.page,page_size:Z.page_size});e.success?(K.value=e.data.backups,Z.total=e.data.total):s.error(e.message||"获取备份列表失败")}catch(e){s.error("获取备份列表失败")}finally{H.value=!1}})),ae=e=>{Z.page=e,ee()},le=e=>{Z.page_size=e,Z.page=1,ee()},te=a=>e(this,null,(function*(){try{const e=yield L.getBackup(a.id);e.success?(Q.value=e.data,q.value=!0):s.error(e.message||"获取备份详情失败")}catch(e){s.error("获取备份详情失败")}})),oe=a=>e(this,null,(function*(){try{yield $.confirm(`确认恢复备份 "${a.backup_name}" 吗？这将覆盖当前所有配置。`,"确认恢复备份",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"});const e=yield L.restoreBackup(a.id);e.success?(s.success("备份恢复成功"),A("restore-backup")):s.error(e.message||"备份恢复失败")}catch(e){"cancel"!==e&&s.error("备份恢复失败")}})),ie=a=>e(this,null,(function*(){try{const e=yield L.getBackup(a.id);if(e.success){const l=e.data.backup_data,t=JSON.stringify(l,null,2),o=new Blob([t],{type:"application/json"}),i=document.createElement("a");i.href=URL.createObjectURL(o),i.download=`${a.backup_name}.json`,i.click(),s.success("下载成功")}else s.error(e.message||"下载失败")}catch(e){s.error("下载失败")}})),se=()=>{Object.assign(Y,{backup_name:`backup_${(new Date).toISOString().slice(0,19).replace(/[:-]/g,"")}`,backup_description:""}),E.value=!0},re=()=>e(this,null,(function*(){if(X.value)try{if(!(yield X.value.validate()))return;J.value=!0;const e=yield L.createBackup(Y);e.success?(s.success("备份创建成功"),E.value=!1,ee()):s.error(e.message||"备份创建失败")}catch(e){s.error("备份创建失败")}finally{J.value=!1}})),ue=a=>e(this,null,(function*(){try{yield $.confirm(`确认删除备份 "${a.backup_name}" 吗？此操作不可撤销。`,"确认删除",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"});const e=yield L.deleteBackup(a.id);e.success?(s.success("删除成功"),ee()):s.error(e.message||"删除失败")}catch(e){"cancel"!==e&&s.error("删除失败")}})),ne=()=>{G.value=!1},ce=e=>{if(!e)return"0 B";const a=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,a)).toFixed(2))+" "+["B","KB","MB","GB"][a]},pe=e=>e?new Date(e).toLocaleString("zh-CN"):"-";return(e,a)=>{const l=d,t=k,o=v,i=w,s=V,$=C,L=x,P=S,A=U,de=j,me=M,fe=y;return u(),r(L,{modelValue:G.value,"onUpdate:modelValue":a[7]||(a[7]=e=>G.value=e),title:"配置备份管理",width:"900px","before-close":ne},{footer:n((()=>[c("div",N,[p(l,{onClick:ne},{default:n((()=>a[18]||(a[18]=[_("关闭")]))),_:1})])])),default:n((()=>[c("div",O,[p(l,{type:"primary",onClick:se,icon:m(f)},{default:n((()=>a[8]||(a[8]=[_(" 创建备份 ")]))),_:1},8,["icon"]),p(l,{onClick:ee,icon:m(g)},{default:n((()=>a[9]||(a[9]=[_(" 刷新 ")]))),_:1},8,["icon"])]),c("div",R,[b((u(),r(o,{data:K.value,stripe:"",style:{width:"100%"}},{default:n((()=>[p(t,{prop:"backup_name",label:"备份名称",width:"200"}),p(t,{prop:"description",label:"描述","min-width":"250","show-overflow-tooltip":""}),p(t,{label:"文件大小",width:"100"},{default:n((({row:e})=>[_(h(ce(e.file_size)),1)])),_:1}),p(t,{label:"配置数量",width:"100"},{default:n((({row:e})=>{var a;return[_(h((null==(a=e.backup_data)?void 0:a.length)||0),1)]})),_:1}),p(t,{prop:"created_at",label:"创建时间",width:"160"},{default:n((({row:e})=>[_(h(pe(e.created_at)),1)])),_:1}),p(t,{label:"操作",width:"200",fixed:"right"},{default:n((({row:e})=>[p(l,{type:"primary",link:"",size:"small",onClick:a=>te(e)},{default:n((()=>a[10]||(a[10]=[_(" 查看 ")]))),_:2},1032,["onClick"]),p(l,{type:"success",link:"",size:"small",onClick:a=>oe(e)},{default:n((()=>a[11]||(a[11]=[_(" 恢复 ")]))),_:2},1032,["onClick"]),p(l,{type:"info",link:"",size:"small",onClick:a=>ie(e)},{default:n((()=>a[12]||(a[12]=[_(" 下载 ")]))),_:2},1032,["onClick"]),p(l,{type:"danger",link:"",size:"small",onClick:a=>ue(e)},{default:n((()=>a[13]||(a[13]=[_(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[fe,H.value]]),c("div",T,[p(i,{"current-page":Z.page,"onUpdate:currentPage":a[0]||(a[0]=e=>Z.page=e),"page-size":Z.page_size,"onUpdate:pageSize":a[1]||(a[1]=e=>Z.page_size=e),total:Z.total,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:le,onCurrentChange:ae},null,8,["current-page","page-size","total"])])]),p(L,{modelValue:q.value,"onUpdate:modelValue":a[2]||(a[2]=e=>q.value=e),title:"备份详情",width:"700px","append-to-body":""},{default:n((()=>{var e;return[Q.value?(u(),z("div",D,[p($,{column:2,border:""},{default:n((()=>[p(s,{label:"备份名称"},{default:n((()=>[_(h(Q.value.backup_name),1)])),_:1}),p(s,{label:"文件大小"},{default:n((()=>[_(h(ce(Q.value.file_size)),1)])),_:1}),p(s,{label:"描述",span:2},{default:n((()=>[_(h(Q.value.description),1)])),_:1}),p(s,{label:"创建时间",span:2},{default:n((()=>[_(h(pe(Q.value.created_at)),1)])),_:1})])),_:1}),c("div",F,[c("h4",null,"包含的配置项 ("+h((null==(e=Q.value.backup_data)?void 0:e.length)||0)+")",1),p(o,{data:Q.value.backup_data,size:"small","max-height":"300"},{default:n((()=>[p(t,{prop:"config_group",label:"配置组",width:"120"}),p(t,{prop:"config_key",label:"配置键",width:"180"}),p(t,{prop:"config_value",label:"配置值","min-width":"150","show-overflow-tooltip":""}),p(t,{prop:"config_type",label:"类型",width:"80"}),p(t,{prop:"description",label:"描述","min-width":"150","show-overflow-tooltip":""})])),_:1},8,["data"])])])):B("",!0)]})),_:1},8,["modelValue"]),p(L,{modelValue:E.value,"onUpdate:modelValue":a[6]||(a[6]=e=>E.value=e),title:"创建配置备份",width:"500px","append-to-body":""},{footer:n((()=>[c("div",I,[p(l,{onClick:a[5]||(a[5]=e=>E.value=!1)},{default:n((()=>a[16]||(a[16]=[_("取消")]))),_:1}),p(l,{type:"primary",onClick:re,loading:J.value},{default:n((()=>a[17]||(a[17]=[_(" 创建备份 ")]))),_:1},8,["loading"])])])),default:n((()=>[p(de,{ref_key:"backupFormRef",ref:X,model:Y,rules:W,"label-width":"100px"},{default:n((()=>[p(A,{label:"备份名称",prop:"backup_name"},{default:n((()=>[p(P,{modelValue:Y.backup_name,"onUpdate:modelValue":a[3]||(a[3]=e=>Y.backup_name=e),placeholder:"请输入备份名称",clearable:""},null,8,["modelValue"]),a[14]||(a[14]=c("div",{class:"form-tip"},"建议使用有意义的名称，如：production_backup_20240101",-1))])),_:1}),p(A,{label:"备份描述",prop:"backup_description"},{default:n((()=>[p(P,{modelValue:Y.backup_description,"onUpdate:modelValue":a[4]||(a[4]=e=>Y.backup_description=e),type:"textarea",rows:3,placeholder:"请输入备份描述",maxlength:"500","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),p(me,{title:"备份说明",type:"info",closable:!1,"show-icon":""},{default:n((()=>a[15]||(a[15]=[c("div",null,[c("p",null,"• 备份将包含当前所有系统配置"),c("p",null,"• 备份文件可用于配置恢复和迁移"),c("p",null,"• 建议定期创建备份以防数据丢失")],-1)]))),_:1})])),_:1},8,["modelValue"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-20a2e875"]]);export{P as default};
