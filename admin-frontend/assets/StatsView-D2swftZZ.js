import{h as e,_ as a}from"./index-rNRt1EuS.js";/* empty css                *//* empty css               */import{d as s,r as l,f as t,c as i,o as r,b as d,aT as c,w as u,aN as o,aV as v,a2 as n,aG as p,N as _,F as m,A as f,e as y,x as h,aX as x,K as g,aK as w,aI as b,aJ as k,a as F,M as j}from"./vendor-CAPBtMef.js";const I={class:"stats-view"},K={key:0,class:"provider-stats"},N={class:"provider-header"},P={class:"provider-metrics"},V={class:"metric"},$={class:"metric-value"},z={class:"metric"},A={class:"metric-value"},G={class:"metric"},J={class:"metric-value"},M={key:0,class:"company-stats"},S={class:"company-header"},T={class:"company-metrics"},X={class:"metric"},q={class:"metric-value"},B={class:"metric"},C={class:"metric-value"},D={class:"metric"},E={class:"metric-value"},H={key:0,class:"route-stats"},L={class:"route-info"},O={class:"price-stats"},Q={class:"companies-in-route"},R={key:0},U=a(s({__name:"StatsView",setup(a){const s=l(!1),U=l(null),W=()=>{return a=this,l=null,t=function*(){try{s.value=!0;const a=yield e.get({url:"/api/v1/admin/warmup/stats"});a.success?U.value=a.data:j.error(a.message||"获取统计数据失败")}catch(a){j.error("获取统计数据失败")}finally{s.value=!1}},new Promise(((e,s)=>{var i=e=>{try{d(t.next(e))}catch(a){s(a)}},r=e=>{try{d(t.throw(e))}catch(a){s(a)}},d=a=>a.done?e(a.value):Promise.resolve(a.value).then(i,r);d((t=t.apply(a,l)).next())}));var a,l,t};return t((()=>{W()})),(e,a)=>{const l=x,t=o,j=c,W=w,Y=v,Z=k,ee=b,ae=p;return r(),i("div",I,[d(j,{gutter:20},{default:u((()=>[d(t,{span:12},{default:u((()=>[d(Y,null,{header:u((()=>a[0]||(a[0]=[y("div",{class:"card-header"},[y("span",null,"供应商统计")],-1)]))),default:u((()=>{var e,c;return[n((r(),i("div",null,[(null==(c=null==(e=U.value)?void 0:e.provider_stats)?void 0:c.length)?(r(),i("div",K,[(r(!0),i(m,null,f(U.value.provider_stats,(e=>(r(),i("div",{key:e.provider,class:"provider-item"},[y("div",N,[y("h4",null,h(e.provider_name),1),d(l,null,{default:u((()=>[g(h(e.total_records)+" 条记录",1)])),_:2},1024)]),y("div",P,[d(j,{gutter:10},{default:u((()=>[d(t,{span:8},{default:u((()=>[y("div",V,[a[1]||(a[1]=y("div",{class:"metric-label"},"成功率",-1)),y("div",$,h((100*e.success_rate).toFixed(1))+"%",1)])])),_:2},1024),d(t,{span:8},{default:u((()=>[y("div",z,[a[2]||(a[2]=y("div",{class:"metric-label"},"平均价格",-1)),y("div",A,"¥"+h(e.avg_price.toFixed(2)),1)])])),_:2},1024),d(t,{span:8},{default:u((()=>[y("div",G,[a[3]||(a[3]=y("div",{class:"metric-label"},"缓存命中率",-1)),y("div",J,h((100*e.cache_hit_rate).toFixed(1))+"%",1)])])),_:2},1024)])),_:2},1024)])])))),128))])):(r(),_(W,{key:1,description:"暂无供应商统计数据"}))])),[[ae,s.value]])]})),_:1})])),_:1}),d(t,{span:12},{default:u((()=>[d(Y,null,{header:u((()=>a[4]||(a[4]=[y("div",{class:"card-header"},[y("span",null,"快递公司统计")],-1)]))),default:u((()=>{var e,c;return[n((r(),i("div",null,[(null==(c=null==(e=U.value)?void 0:e.company_stats)?void 0:c.length)?(r(),i("div",M,[(r(!0),i(m,null,f(U.value.company_stats,(e=>(r(),i("div",{key:e.express_code,class:"company-item"},[y("div",S,[y("h4",null,h(e.express_name),1),d(l,{type:"info"},{default:u((()=>[g(h(e.express_code),1)])),_:2},1024)]),y("div",T,[d(j,{gutter:10},{default:u((()=>[d(t,{span:8},{default:u((()=>[y("div",X,[a[5]||(a[5]=y("div",{class:"metric-label"},"记录数",-1)),y("div",q,h(e.total_records),1)])])),_:2},1024),d(t,{span:8},{default:u((()=>[y("div",B,[a[6]||(a[6]=y("div",{class:"metric-label"},"平均价格",-1)),y("div",C,"¥"+h(e.avg_price.toFixed(2)),1)])])),_:2},1024),d(t,{span:8},{default:u((()=>[y("div",D,[a[7]||(a[7]=y("div",{class:"metric-label"},"价格区间",-1)),y("div",E,"¥"+h(e.min_price.toFixed(2))+"-"+h(e.max_price.toFixed(2)),1)])])),_:2},1024)])),_:2},1024)])])))),128))])):(r(),_(W,{key:1,description:"暂无快递公司统计数据"}))])),[[ae,s.value]])]})),_:1})])),_:1})])),_:1}),d(j,{gutter:20,style:{"margin-top":"20px"}},{default:u((()=>[d(t,{span:24},{default:u((()=>[d(Y,null,{header:u((()=>a[8]||(a[8]=[y("div",{class:"card-header"},[y("span",null,"热门路线统计")],-1)]))),default:u((()=>{var e,t;return[n((r(),i("div",null,[(null==(t=null==(e=U.value)?void 0:e.route_stats)?void 0:t.length)?(r(),i("div",H,[d(ee,{data:U.value.route_stats,stripe:""},{default:u((()=>[d(Z,{prop:"route_key",label:"路线",width:"200"},{default:u((({row:e})=>[y("div",L,[y("div",null,h(e.from_province)+" "+h(e.from_city),1),a[9]||(a[9]=y("div",{class:"arrow"},"→",-1)),y("div",null,h(e.to_province)+" "+h(e.to_city),1)])])),_:1}),d(Z,{prop:"total_records",label:"记录数",width:"100"}),d(Z,{label:"价格统计",width:"200"},{default:u((({row:e})=>[y("div",O,[y("div",null,"平均: ¥"+h(e.avg_price.toFixed(2)),1),y("div",null,"区间: ¥"+h(e.min_price.toFixed(2))+"-"+h(e.max_price.toFixed(2)),1)])])),_:1}),d(Z,{label:"快递公司","min-width":"300"},{default:u((({row:e})=>[y("div",Q,[(r(!0),i(m,null,f(e.companies,(e=>(r(),_(l,{key:`${e.express_code}-${e.provider}`,type:e.is_cheapest?"success":"info",size:"small",style:{"margin-right":"5px","margin-bottom":"5px"}},{default:u((()=>[g(h(e.express_name)+" ("+h(e.provider)+") ¥"+h(e.price.toFixed(2))+" ",1),e.is_cheapest?(r(),i("span",R," 🏆")):F("",!0)])),_:2},1032,["type"])))),128))])])),_:1})])),_:1},8,["data"])])):(r(),_(W,{key:1,description:"暂无路线统计数据"}))])),[[ae,s.value]])]})),_:1})])),_:1})])),_:1})])}}}),[["__scopeId","data-v-b6ba25dd"]]);export{U as default};
