import{_ as a}from"./index-rNRt1EuS.js";import{d as e,j as s,bc as o,bd as t,be as l,bf as n,bg as i,bh as d,b5 as c,bi as r,bj as u,bk as v,bl as b,bm as p,bn as f,an as g,c as y,o as m,v as F,e as _,a as h,n as k,b as x,y as C,w as j,N as w,al as B,x as S,u as z,bo as E}from"./vendor-CAPBtMef.js";const M={class:"stat-card-header"},I={class:"stat-info"},K={class:"stat-title"},L={class:"stat-value"},N={class:"value"},$={key:0,class:"unit"},q={key:0,class:"stat-card-footer"},A={class:"description"},D={key:1,class:"loading-overlay"},G=a(e({__name:"StatCard",props:{title:{},value:{},unit:{},icon:{},iconColor:{default:"#409EFF"},iconBgColor:{default:"#E6F7FF"},trend:{},description:{},loading:{type:Boolean,default:!1}},setup(a){const e=a,G={"el-icon-user":b,"el-icon-user-solid":v,"el-icon-shopping-cart-2":u,"el-icon-shopping-bag-2":r,"el-icon-clock":c,"el-icon-coin":d,"el-icon-wallet":i,"el-icon-connection":n,"el-icon-document":l,"el-icon-office-building":t,"el-icon-success":o},H=s((()=>G[e.icon]||b)),J=s((()=>void 0===e.trend?p:e.trend>0?f:e.trend<0?g:p)),O=s((()=>"number"==typeof e.value?e.value>=1e6?(e.value/1e6).toFixed(1)+"M":e.value>=1e3?(e.value/1e3).toFixed(1)+"K":e.value.toLocaleString():e.value)),P=s((()=>void 0===e.trend?"":e.trend>0?"trend-up":e.trend<0?"trend-down":"trend-stable")),Q=s((()=>{if(void 0===e.trend)return"";return`${Math.abs(e.trend).toFixed(1)}%`}));return(a,e)=>{const s=C;return m(),y("div",{class:F(["stat-card",{loading:a.loading}])},[_("div",M,[_("div",{class:"stat-icon",style:k({backgroundColor:a.iconBgColor})},[x(s,{style:k({color:a.iconColor}),size:24},{default:j((()=>[(m(),w(B(H.value)))])),_:1},8,["style"])],4),_("div",I,[_("h3",K,S(a.title),1),_("div",L,[_("span",N,S(O.value),1),a.unit?(m(),y("span",$,S(a.unit),1)):h("",!0)])])]),void 0!==a.trend?(m(),y("div",q,[_("div",{class:F(["trend",P.value])},[x(s,{size:12},{default:j((()=>[(m(),w(B(J.value)))])),_:1}),_("span",null,S(Q.value),1)],2),_("div",A,S(a.description),1)])):h("",!0),a.loading?(m(),y("div",D,[x(s,{class:"is-loading"},{default:j((()=>[x(z(E))])),_:1})])):h("",!0)],2)}}}),[["__scopeId","data-v-44e9a240"]]);export{G as S};
