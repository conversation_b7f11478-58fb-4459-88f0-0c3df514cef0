import{i as a,j as e,k as r,l as s,m as t,n as o,o as l,_ as n}from"./index-rNRt1EuS.js";/* empty css                    *//* empty css                       */import{d as p,r as c,X as i,f as u,c as v,e as m,b as d,K as g,aZ as b,u as x,m as f,w,ba as _,aJ as h,x as y,b8 as j,o as V}from"./vendor-CAPBtMef.js";const k={class:"region new-user art-custom-card"},U={class:"card-header"},I={style:{display:"flex","align-items":"center"}},J=["src"],K={class:"user-name"},N={style:{display:"flex","align-items":"center"}},T={style:{"margin-left":"10px"}},X=n(p({__name:"NewUser",setup(n){const p=c("本月"),X=i([{username:"中小鱼",province:"北京",sex:0,age:22,percentage:60,pro:0,color:"rgb(var(--art-primary)) !important",avatar:a},{username:"何小荷",province:"深圳",sex:1,age:21,percentage:20,pro:0,color:"rgb(var(--art-secondary)) !important",avatar:e},{username:"誶誶淰",province:"上海",sex:1,age:23,percentage:60,pro:0,color:"rgb(var(--art-warning)) !important",avatar:r},{username:"发呆草",province:"长沙",sex:0,age:28,percentage:50,pro:0,color:"rgb(var(--art-info)) !important",avatar:s},{username:"甜筒",province:"浙江",sex:1,age:26,percentage:70,pro:0,color:"rgb(var(--art-error)) !important",avatar:t},{username:"冷月呆呆",province:"湖北",sex:1,age:25,percentage:90,pro:0,color:"rgb(var(--art-success)) !important",avatar:o}]);u((()=>{Z()}));const Z=()=>{setTimeout((()=>{for(let a=0;a<X.length;a++){let e=X[a];X[a].pro=e.percentage}}),100)};return(a,e)=>{const r=_,s=b,t=h,o=j,n=l;return V(),v("div",k,[m("div",U,[e[1]||(e[1]=m("div",{class:"title"},[m("h4",{class:"box-title"},"新用户"),m("p",{class:"subtitle"},[g("这个月增长"),m("span",{class:"text-success"},"+20%")])],-1)),d(s,{modelValue:x(p),"onUpdate:modelValue":e[0]||(e[0]=a=>f(p)?p.value=a:null)},{default:w((()=>[d(r,{value:"本月",label:"本月"}),d(r,{value:"上月",label:"上月"}),d(r,{value:"今年",label:"今年"})])),_:1},8,["modelValue"])]),d(n,{data:x(X),pagination:!1},{default:w((()=>[d(t,{label:"头像",prop:"avatar",width:"150px"},{default:w((a=>[m("div",I,[m("img",{class:"avatar",src:a.row.avatar},null,8,J),m("span",K,y(a.row.username),1)])])),_:1}),d(t,{label:"地区",prop:"province"}),d(t,{label:"性别",prop:"avatar"},{default:w((a=>[m("div",N,[m("span",T,y(1===a.row.sex?"男":"女"),1)])])),_:1}),d(t,{label:"进度",width:"240"},{default:w((a=>[d(o,{percentage:a.row.pro,color:a.row.color,"stroke-width":4},null,8,["percentage","color"])])),_:1})])),_:1},8,["data"])])}}}),[["__scopeId","data-v-097273c0"]]);export{X as default};
