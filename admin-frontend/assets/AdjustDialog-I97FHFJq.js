import{_ as e}from"./index-rNRt1EuS.js";/* empty css                       *//* empty css                 */import{d as a,r as l,X as s,j as r,p as t,N as u,o,w as n,b as d,Z as i,G as c,a as m,_ as p,e as _,x as v,C as b,aZ as f,a_ as y,n as g,ar as j,as as h,H as V,K as w,Y as x,ai as C,M as F}from"./vendor-CAPBtMef.js";import{a as $}from"./balanceApi-B8_MfzSO.js";const k={class:"user-info"},U={class:"user-name"},A={class:"user-email"},q={class:"current-balance"},B={class:"balance-preview"},D={class:"original-balance"},I={class:"dialog-footer"},E=e(a({__name:"AdjustDialog",props:{visible:{type:Boolean},user:{}},emits:["update:visible","success"],setup(e,{emit:a}){const E=e,P=a,T=l(!1),Z=l(!1),G=l(),H=s({user_id:"",amount:"",adjustment_type:"increase",reason:"",description:""}),K=r((()=>{if(!E.user||!H.amount)return 0;const e=parseFloat(E.user.balance),a=parseFloat(H.amount);return"increase"===H.adjustment_type?e+a:e-a})),M={user_id:[{required:!0,message:"请输入用户ID",trigger:"blur"}],adjustment_type:[{required:!0,message:"请选择调整类型",trigger:"change"}],amount:[{required:!0,message:"请输入调整金额",trigger:"blur"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入有效的金额（最多两位小数）",trigger:"blur"},{validator:(e,a,l)=>{const s=parseFloat(a);if(s<=0)l(new Error("调整金额必须大于0"));else if(s>1e5)l(new Error("单次调整金额不能超过10万"));else if("decrease"===H.adjustment_type&&E.user){s>parseFloat(E.user.balance)?l(new Error("减少金额不能超过当前余额")):l()}else l()},trigger:"blur"}],reason:[{required:!0,message:"请选择调整原因",trigger:"change"}]};t((()=>E.visible),(e=>{T.value=e,e&&(N(),E.user&&(H.user_id=E.user.user_id))})),t(T,(e=>{P("update:visible",e)}));const N=()=>{var e;Object.assign(H,{user_id:"",amount:"",adjustment_type:"increase",reason:"",description:""}),null==(e=G.value)||e.clearValidate()},O=()=>{T.value=!1},R=()=>{return e=this,a=null,l=function*(){if(G.value)try{if(!(yield G.value.validate()))return;const e="increase"===H.adjustment_type?"增加":"减少",a=E.user?`确认为用户 ${E.user.username} ${e} ¥${H.amount} 吗？`:`确认为用户 ${H.user_id} ${e} ¥${H.amount} 吗？`;yield C.confirm(a,"确认调整",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"}),Z.value=!0;const l=yield $.adjustBalance(H);l.success?(F.success("调整成功"),P("success"),O()):F.error(l.message||"调整失败")}catch(e){"cancel"!==e&&F.error("调整失败")}finally{Z.value=!1}},new Promise(((s,r)=>{var t=e=>{try{o(l.next(e))}catch(a){r(a)}},u=e=>{try{o(l.throw(e))}catch(a){r(a)}},o=e=>e.done?s(e.value):Promise.resolve(e.value).then(t,u);o((l=l.apply(e,a)).next())}));var e,a,l},S=e=>{const a="string"==typeof e?parseFloat(e):e;return`¥${(null==a?void 0:a.toFixed(2))||"0.00"}`};return(e,a)=>{const l=p,s=b,r=y,t=f,C=h,F=j,$=i,E=V,P=x;return o(),u(P,{modelValue:T.value,"onUpdate:modelValue":a[6]||(a[6]=e=>T.value=e),title:"余额调整",width:"500px","before-close":O},{footer:n((()=>[_("div",I,[d(E,{onClick:O},{default:n((()=>a[10]||(a[10]=[w("取消")]))),_:1}),d(E,{type:"increase"===H.adjustment_type?"success":"danger",onClick:R,loading:Z.value},{default:n((()=>a[11]||(a[11]=[w(" 确认调整 ")]))),_:1},8,["type","loading"])])])),default:n((()=>[d($,{ref_key:"formRef",ref:G,model:H,rules:M,"label-width":"100px",onSubmit:a[5]||(a[5]=c((()=>{}),["prevent"]))},{default:n((()=>[e.user?(o(),u(l,{key:0,label:"用户信息"},{default:n((()=>[_("div",k,[_("div",U,v(e.user.username),1),_("div",A,v(e.user.email),1),_("div",q,"当前余额: "+v(S(e.user.balance)),1)])])),_:1})):m("",!0),e.user?m("",!0):(o(),u(l,{key:1,label:"用户ID",prop:"user_id"},{default:n((()=>[d(s,{modelValue:H.user_id,"onUpdate:modelValue":a[0]||(a[0]=e=>H.user_id=e),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])])),_:1})),d(l,{label:"调整类型",prop:"adjustment_type"},{default:n((()=>[d(t,{modelValue:H.adjustment_type,"onUpdate:modelValue":a[1]||(a[1]=e=>H.adjustment_type=e)},{default:n((()=>[d(r,{value:"increase"},{default:n((()=>a[7]||(a[7]=[_("span",{style:{color:"#67C23A"}},"增加余额",-1)]))),_:1}),d(r,{value:"decrease"},{default:n((()=>a[8]||(a[8]=[_("span",{style:{color:"#F56C6C"}},"减少余额",-1)]))),_:1})])),_:1},8,["modelValue"])])),_:1}),d(l,{label:"调整金额",prop:"amount"},{default:n((()=>[d(s,{modelValue:H.amount,"onUpdate:modelValue":a[2]||(a[2]=e=>H.amount=e),placeholder:"请输入调整金额",clearable:""},{prepend:n((()=>[_("span",{style:g({color:"increase"===H.adjustment_type?"#67C23A":"#F56C6C"})},v("increase"===H.adjustment_type?"+¥":"-¥"),5)])),_:1},8,["modelValue"])])),_:1}),e.user&&H.amount?(o(),u(l,{key:2,label:"调整后余额"},{default:n((()=>[_("div",B,[_("span",D,v(S(e.user.balance)),1),a[9]||(a[9]=_("i",{class:"el-icon-arrow-right",style:{margin:"0 8px",color:"#909399"}},null,-1)),_("span",{class:"new-balance",style:g({color:K.value>=0?"#67C23A":"#F56C6C"})},v(S(K.value)),5)])])),_:1})):m("",!0),d(l,{label:"调整原因",prop:"reason"},{default:n((()=>[d(F,{modelValue:H.reason,"onUpdate:modelValue":a[3]||(a[3]=e=>H.reason=e),placeholder:"请选择调整原因",style:{width:"100%"}},{default:n((()=>[d(C,{label:"系统错误修正",value:"system_error_correction"}),d(C,{label:"用户申诉处理",value:"user_complaint_handling"}),d(C,{label:"数据异常修复",value:"data_anomaly_fix"}),d(C,{label:"业务规则调整",value:"business_rule_adjustment"}),d(C,{label:"管理员手动调整",value:"admin_manual_adjustment"}),d(C,{label:"其他",value:"other"})])),_:1},8,["modelValue"])])),_:1}),d(l,{label:"备注说明",prop:"description"},{default:n((()=>[d(s,{modelValue:H.description,"onUpdate:modelValue":a[4]||(a[4]=e=>H.description=e),type:"textarea",rows:3,placeholder:"请输入备注说明（可选）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-512b04eb"]]);export{E as default};
