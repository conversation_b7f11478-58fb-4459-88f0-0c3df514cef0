var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,r=(a,l,s)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[l]=s,o=(e,a,l)=>new Promise(((s,i)=>{var t=e=>{try{o(l.next(e))}catch(a){i(a)}},r=e=>{try{o(l.throw(e))}catch(a){i(a)}},o=e=>e.done?s(e.value):Promise.resolve(e.value).then(t,r);o((l=l.apply(e,a)).next())}));import{_ as d}from"./index-rNRt1EuS.js";import{d as c,r as n,X as u,j as m,p,M as v,N as _,o as y,w as f,e as g,a2 as b,b as h,H as w,u as V,aR as x,K as C,aQ as j,aI as k,aJ as O,x as U,ap as P,aG as S,Y as B,Z as q,_ as I,C as $,aq as z,ai as A}from"./vendor-CAPBtMef.js";import{E as D}from"./expressCompanyApi-DSXUWAhW.js";const E={class:"action-bar"},M={class:"dialog-footer"},R={class:"dialog-footer"},T=d(c({__name:"ServiceManageDialog",props:{visible:{type:Boolean},company:{default:null}},emits:["update:visible","success"],setup(e,{emit:d}){const c=e,T=d,Z=n(!1),F=n([]),G=n(!1),H=n(),J=n(!1),K=n(null),N=u({company_id:"",service_code:"",service_name:"",description:"",estimated_days:void 0,is_active:!0,sort_order:0}),Q={service_code:[{required:!0,message:"请输入服务代码",trigger:"blur"},{max:50,message:"代码长度不能超过50个字符",trigger:"blur"},{pattern:/^[A-Z0-9_]+$/,message:"代码只能包含大写字母、数字和下划线",trigger:"blur"}],service_name:[{required:!0,message:"请输入服务名称",trigger:"blur"},{max:100,message:"名称长度不能超过100个字符",trigger:"blur"}],description:[{max:500,message:"描述长度不能超过500个字符",trigger:"blur"}],estimated_days:[{type:"number",min:1,max:30,message:"预计天数应在1-30天之间",trigger:"blur"}],sort_order:[{type:"number",min:0,max:9999,message:"排序值应在0-9999之间",trigger:"blur"}]},X=m({get:()=>c.visible,set:e=>T("update:visible",e)}),Y=m((()=>{var e;return!!(null==(e=K.value)?void 0:e.id)}));p((()=>c.visible),(e=>{e&&c.company&&L()}));const L=()=>o(this,null,(function*(){var e;if(null==(e=c.company)?void 0:e.id)try{Z.value=!0;const e=yield D.getServicesByCompany(c.company.id);e.success?F.value=e.data:v.error(e.message||"获取服务列表失败")}catch(a){v.error("获取服务列表失败")}finally{Z.value=!1}})),W=()=>{X.value=!1},ee=()=>{K.value=null,se(),G.value=!0},ae=e=>o(this,null,(function*(){try{const a=yield D.updateService(e.id,{is_active:e.is_active});a.success?(v.success((e.is_active?"启用":"禁用")+"成功"),T("success")):(e.is_active=!e.is_active,v.error(a.message||"操作失败"))}catch(a){e.is_active=!e.is_active,v.error("操作失败")}})),le=e=>o(this,null,(function*(){try{yield A.confirm(`确认删除服务 "${e.service_name}" 吗？此操作不可撤销。`,"确认删除",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"});const a=yield D.deleteService(e.id);a.success?(v.success("删除成功"),L(),T("success")):v.error(a.message||"删除失败")}catch(a){"cancel"!==a&&v.error("删除失败")}})),se=()=>{var e,a;Object.assign(N,{company_id:(null==(e=c.company)?void 0:e.id)||"",service_code:"",service_name:"",description:"",estimated_days:void 0,is_active:!0,sort_order:0}),null==(a=H.value)||a.clearValidate()},ie=()=>o(this,null,(function*(){var e,o;if(H.value)try{if(!(yield H.value.validate()))return;if(J.value=!0,Y.value){const e={service_name:N.service_name,description:N.description||void 0,estimated_days:N.estimated_days,is_active:N.is_active,sort_order:N.sort_order},a=yield D.updateService(K.value.id,e);a.success?(v.success("服务更新成功"),G.value=!1,L(),T("success")):v.error(a.message||"更新失败")}else{const d=(e=((e,a)=>{for(var l in a||(a={}))i.call(a,l)&&r(e,l,a[l]);if(s)for(var l of s(a))t.call(a,l)&&r(e,l,a[l]);return e})({},N),o={description:N.description||void 0},a(e,l(o))),c=yield D.createService(d);c.success?(v.success("服务创建成功"),G.value=!1,L(),T("success")):v.error(c.message||"创建失败")}}catch(d){v.error("操作失败")}finally{J.value=!1}}));return(e,a)=>{var l;const s=w,i=O,t=P,r=k,o=$,d=I,c=z,n=q,u=B,m=S;return y(),_(u,{modelValue:X.value,"onUpdate:modelValue":a[8]||(a[8]=e=>X.value=e),title:`${null==(l=e.company)?void 0:l.name} - 服务管理`,width:"800px","close-on-click-modal":!1,onClose:W},{footer:f((()=>[g("div",R,[h(s,{onClick:W},{default:f((()=>a[17]||(a[17]=[C("关闭")]))),_:1})])])),default:f((()=>[g("div",E,[h(s,{type:"primary",icon:V(x),onClick:ee},{default:f((()=>a[9]||(a[9]=[C(" 新增服务 ")]))),_:1},8,["icon"]),h(s,{icon:V(j),onClick:L},{default:f((()=>a[10]||(a[10]=[C(" 刷新 ")]))),_:1},8,["icon"])]),b((y(),_(r,{data:F.value,stripe:"",border:"",style:{width:"100%"}},{default:f((()=>[h(i,{prop:"service_code",label:"服务代码",width:"120"}),h(i,{prop:"service_name",label:"服务名称",width:"150"}),h(i,{prop:"description",label:"描述","min-width":"200","show-overflow-tooltip":""}),h(i,{prop:"estimated_days",label:"预计天数",width:"100"},{default:f((({row:e})=>[C(U(e.estimated_days||"-"),1)])),_:1}),h(i,{label:"状态",width:"80"},{default:f((({row:e})=>[h(t,{modelValue:e.is_active,"onUpdate:modelValue":a=>e.is_active=a,onChange:a=>ae(e)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),h(i,{prop:"sort_order",label:"排序",width:"80"}),h(i,{label:"操作",width:"150",fixed:"right"},{default:f((({row:e})=>[h(s,{type:"primary",size:"small",onClick:a=>{return l=e,K.value=l,Object.assign(N,{company_id:l.company_id,service_code:l.service_code,service_name:l.service_name,description:l.description||"",estimated_days:l.estimated_days,is_active:l.is_active,sort_order:l.sort_order}),void(G.value=!0);var l}},{default:f((()=>a[11]||(a[11]=[C(" 编辑 ")]))),_:2},1032,["onClick"]),h(s,{type:"danger",size:"small",onClick:a=>le(e)},{default:f((()=>a[12]||(a[12]=[C(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[m,Z.value]]),h(u,{modelValue:G.value,"onUpdate:modelValue":a[7]||(a[7]=e=>G.value=e),title:Y.value?"编辑服务":"新增服务",width:"500px","close-on-click-modal":!1,"append-to-body":""},{footer:f((()=>[g("div",M,[h(s,{onClick:a[6]||(a[6]=e=>G.value=!1)},{default:f((()=>a[16]||(a[16]=[C("取消")]))),_:1}),h(s,{type:"primary",loading:J.value,onClick:ie},{default:f((()=>[C(U(Y.value?"更新":"创建"),1)])),_:1},8,["loading"])])])),default:f((()=>[h(n,{ref_key:"serviceFormRef",ref:H,model:N,rules:Q,"label-width":"120px"},{default:f((()=>[h(d,{label:"服务代码",prop:"service_code"},{default:f((()=>[h(o,{modelValue:N.service_code,"onUpdate:modelValue":a[0]||(a[0]=e=>N.service_code=e),placeholder:"请输入服务代码",disabled:Y.value,maxlength:"50"},null,8,["modelValue","disabled"]),a[13]||(a[13]=g("div",{class:"form-tip"},"服务的唯一标识，创建后不可修改",-1))])),_:1}),h(d,{label:"服务名称",prop:"service_name"},{default:f((()=>[h(o,{modelValue:N.service_name,"onUpdate:modelValue":a[1]||(a[1]=e=>N.service_name=e),placeholder:"请输入服务名称",maxlength:"100"},null,8,["modelValue"])])),_:1}),h(d,{label:"描述",prop:"description"},{default:f((()=>[h(o,{modelValue:N.description,"onUpdate:modelValue":a[2]||(a[2]=e=>N.description=e),type:"textarea",rows:3,placeholder:"请输入服务描述（可选）",maxlength:"500","show-word-limit":""},null,8,["modelValue"])])),_:1}),h(d,{label:"预计天数",prop:"estimated_days"},{default:f((()=>[h(c,{modelValue:N.estimated_days,"onUpdate:modelValue":a[3]||(a[3]=e=>N.estimated_days=e),min:1,max:30,placeholder:"预计配送天数",style:{width:"200px"}},null,8,["modelValue"]),a[14]||(a[14]=g("div",{class:"form-tip"},"预计配送天数，用于时效预估",-1))])),_:1}),h(d,{label:"状态",prop:"is_active"},{default:f((()=>[h(t,{modelValue:N.is_active,"onUpdate:modelValue":a[4]||(a[4]=e=>N.is_active=e),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])])),_:1}),h(d,{label:"排序",prop:"sort_order"},{default:f((()=>[h(c,{modelValue:N.sort_order,"onUpdate:modelValue":a[5]||(a[5]=e=>N.sort_order=e),min:0,max:9999,placeholder:"排序值",style:{width:"200px"}},null,8,["modelValue"]),a[15]||(a[15]=g("div",{class:"form-tip"},"数值越小排序越靠前",-1))])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-d1abaaf8"]]);export{T as default};
