import{_ as e}from"./index-rNRt1EuS.js";import{d as a,r as l,X as r,p as s,N as o,o as t,w as u,b as i,Z as n,G as d,a as c,_ as m,e as p,x as v,C as _,K as f,ar as b,as as g,H as y,Y as h,ai as V,M as w}from"./vendor-CAPBtMef.js";import{a as x}from"./balanceApi-B8_MfzSO.js";const D={class:"user-info"},$={class:"user-name"},k={class:"user-email"},I={class:"current-balance"},U={class:"dialog-footer"},j=e(a({__name:"DepositDialog",props:{visible:{type:Boolean},user:{}},emits:["update:visible","success"],setup(e,{emit:a}){const j=e,q=a,B=l(!1),C=l(!1),F=l(),P=r({user_id:"",amount:"",reason:"",description:""}),E={user_id:[{required:!0,message:"请输入用户ID",trigger:"blur"}],amount:[{required:!0,message:"请输入充值金额",trigger:"blur"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入有效的金额（最多两位小数）",trigger:"blur"},{validator:(e,a,l)=>{const r=parseFloat(a);r<=0?l(new Error("充值金额必须大于0")):r>1e5?l(new Error("单次充值金额不能超过10万")):l()},trigger:"blur"}],reason:[{required:!0,message:"请选择充值原因",trigger:"change"}]};s((()=>j.visible),(e=>{B.value=e,e&&(T(),j.user&&(P.user_id=j.user.user_id))})),s(B,(e=>{q("update:visible",e)}));const T=()=>{var e;Object.assign(P,{user_id:"",amount:"",reason:"",description:""}),null==(e=F.value)||e.clearValidate()},A=()=>{B.value=!1},G=()=>{return e=this,a=null,l=function*(){if(F.value)try{if(!(yield F.value.validate()))return;const e=j.user?`确认为用户 ${j.user.username} 充值 ¥${P.amount} 吗？`:`确认为用户 ${P.user_id} 充值 ¥${P.amount} 吗？`;yield V.confirm(e,"确认充值",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"}),C.value=!0;const a=yield x.manualDeposit(P);a.success?(w.success("充值成功"),q("success"),A()):w.error(a.message||"充值失败")}catch(e){"cancel"!==e&&w.error("充值失败")}finally{C.value=!1}},new Promise(((r,s)=>{var o=e=>{try{u(l.next(e))}catch(a){s(a)}},t=e=>{try{u(l.throw(e))}catch(a){s(a)}},u=e=>e.done?r(e.value):Promise.resolve(e.value).then(o,t);u((l=l.apply(e,a)).next())}));var e,a,l},H=e=>{const a="string"==typeof e?parseFloat(e):e;return`¥${(null==a?void 0:a.toFixed(2))||"0.00"}`};return(e,a)=>{const l=m,r=_,s=g,V=b,w=n,x=y,j=h;return t(),o(j,{modelValue:B.value,"onUpdate:modelValue":a[5]||(a[5]=e=>B.value=e),title:"手动充值",width:"500px","before-close":A},{footer:u((()=>[p("div",U,[i(x,{onClick:A},{default:u((()=>a[7]||(a[7]=[f("取消")]))),_:1}),i(x,{type:"primary",onClick:G,loading:C.value},{default:u((()=>a[8]||(a[8]=[f(" 确认充值 ")]))),_:1},8,["loading"])])])),default:u((()=>[i(w,{ref_key:"formRef",ref:F,model:P,rules:E,"label-width":"100px",onSubmit:a[4]||(a[4]=d((()=>{}),["prevent"]))},{default:u((()=>[e.user?(t(),o(l,{key:0,label:"用户信息"},{default:u((()=>[p("div",D,[p("div",$,v(e.user.username),1),p("div",k,v(e.user.email),1),p("div",I,"当前余额: "+v(H(e.user.balance)),1)])])),_:1})):c("",!0),e.user?c("",!0):(t(),o(l,{key:1,label:"用户ID",prop:"user_id"},{default:u((()=>[i(r,{modelValue:P.user_id,"onUpdate:modelValue":a[0]||(a[0]=e=>P.user_id=e),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])])),_:1})),i(l,{label:"充值金额",prop:"amount"},{default:u((()=>[i(r,{modelValue:P.amount,"onUpdate:modelValue":a[1]||(a[1]=e=>P.amount=e),placeholder:"请输入充值金额",clearable:""},{prepend:u((()=>a[6]||(a[6]=[f("¥")]))),_:1},8,["modelValue"])])),_:1}),i(l,{label:"充值原因",prop:"reason"},{default:u((()=>[i(V,{modelValue:P.reason,"onUpdate:modelValue":a[2]||(a[2]=e=>P.reason=e),placeholder:"请选择充值原因",style:{width:"100%"}},{default:u((()=>[i(s,{label:"管理员手动充值",value:"admin_manual_deposit"}),i(s,{label:"新用户赠送",value:"new_user_gift"}),i(s,{label:"VIP客户充值",value:"vip_customer_deposit"}),i(s,{label:"客服补偿",value:"customer_service_compensation"}),i(s,{label:"其他",value:"other"})])),_:1},8,["modelValue"])])),_:1}),i(l,{label:"备注说明",prop:"description"},{default:u((()=>[i(r,{modelValue:P.description,"onUpdate:modelValue":a[3]||(a[3]=e=>P.description=e),type:"textarea",rows:3,placeholder:"请输入备注说明（可选）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-081ef13a"]]);export{j as default};
