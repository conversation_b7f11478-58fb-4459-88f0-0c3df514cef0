import{_ as e}from"./index-rNRt1EuS.js";/* empty css                 */import{d as a,r as l,X as t,j as s,p as o,N as r,o as n,w as u,b as i,Z as d,G as c,_ as p,e as m,c as v,F as _,A as f,aX as b,K as g,x as h,C as y,ar as w,as as x,a$ as V,H as I,Y as $,ai as j,M as k}from"./vendor-CAPBtMef.js";import{a as B}from"./balanceApi-B8_MfzSO.js";const F={class:"selected-users"},U={class:"user-count"},C={class:"amount-tip"},P={class:"dialog-footer"},q=e(a({__name:"BatchDepositDialog",props:{visible:{type:Boolean},userIds:{}},emits:["update:visible","success"],setup(e,{emit:a}){const q=e,A=a,D=l(!1),E=l(!1),O=l(),T=t({amount:"",reason:"",description:""}),X=s((()=>((parseFloat(T.amount)||0)*q.userIds.length).toFixed(2))),G={amount:[{required:!0,message:"请输入充值金额",trigger:"blur"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入有效的金额（最多两位小数）",trigger:"blur"},{validator:(e,a,l)=>{const t=parseFloat(a);t<=0?l(new Error("充值金额必须大于0")):t>1e4?l(new Error("单个用户充值金额不能超过1万")):l()},trigger:"blur"}],reason:[{required:!0,message:"请选择充值原因",trigger:"change"}]};o((()=>q.visible),(e=>{D.value=e,e&&H()})),o(D,(e=>{A("update:visible",e)}));const H=()=>{var e;Object.assign(T,{amount:"",reason:"",description:""}),null==(e=O.value)||e.clearValidate()},K=()=>{D.value=!1},M=()=>{return e=this,a=null,l=function*(){if(O.value)try{if(!(yield O.value.validate()))return;const e=`确认为 ${q.userIds.length} 个用户各充值 ¥${T.amount}，总计 ¥${X.value} 吗？`;yield j.confirm(e,"确认批量充值",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"}),E.value=!0;const a={operation_type:"batch_deposit",user_ids:q.userIds,amount:T.amount,reason:T.reason,operations:q.userIds.map((e=>({user_id:e,amount:T.amount,description:T.description})))},l=yield B.batchOperation(a);if(l.success){const{success_count:e,failure_count:a}=l.data;0===a?k.success(`批量充值成功，共处理 ${e} 个用户`):k.warning(`批量充值完成，成功 ${e} 个，失败 ${a} 个`),A("success"),K()}else k.error(l.message||"批量充值失败")}catch(e){"cancel"!==e&&k.error("批量充值失败")}finally{E.value=!1}},new Promise(((t,s)=>{var o=e=>{try{n(l.next(e))}catch(a){s(a)}},r=e=>{try{n(l.throw(e))}catch(a){s(a)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,r);n((l=l.apply(e,a)).next())}));var e,a,l};return(e,a)=>{const l=b,t=p,s=y,o=x,j=w,k=V,B=d,q=I,A=$;return n(),r(A,{modelValue:D.value,"onUpdate:modelValue":a[4]||(a[4]=e=>D.value=e),title:"批量充值",width:"600px","before-close":K},{footer:u((()=>[m("div",P,[i(q,{onClick:K},{default:u((()=>a[7]||(a[7]=[g("取消")]))),_:1}),i(q,{type:"primary",onClick:M,loading:E.value},{default:u((()=>a[8]||(a[8]=[g(" 确认批量充值 ")]))),_:1},8,["loading"])])])),default:u((()=>[i(B,{ref_key:"formRef",ref:O,model:T,rules:G,"label-width":"100px",onSubmit:a[3]||(a[3]=c((()=>{}),["prevent"]))},{default:u((()=>[i(t,{label:"选中用户"},{default:u((()=>[m("div",F,[(n(!0),v(_,null,f(e.userIds,(e=>(n(),r(l,{key:e,type:"info",style:{"margin-right":"8px","margin-bottom":"8px"}},{default:u((()=>[g(h(e),1)])),_:2},1024)))),128)),m("div",U,"共 "+h(e.userIds.length)+" 个用户",1)])])),_:1}),i(t,{label:"充值金额",prop:"amount"},{default:u((()=>[i(s,{modelValue:T.amount,"onUpdate:modelValue":a[0]||(a[0]=e=>T.amount=e),placeholder:"请输入每个用户的充值金额",clearable:""},{prepend:u((()=>a[5]||(a[5]=[g("¥")]))),_:1},8,["modelValue"]),m("div",C," 总充值金额: ¥"+h(X.value),1)])),_:1}),i(t,{label:"充值原因",prop:"reason"},{default:u((()=>[i(j,{modelValue:T.reason,"onUpdate:modelValue":a[1]||(a[1]=e=>T.reason=e),placeholder:"请选择充值原因",style:{width:"100%"}},{default:u((()=>[i(o,{label:"批量管理员充值",value:"batch_admin_deposit"}),i(o,{label:"批量新用户赠送",value:"batch_new_user_gift"}),i(o,{label:"批量VIP客户充值",value:"batch_vip_customer_deposit"}),i(o,{label:"批量客服补偿",value:"batch_customer_service_compensation"}),i(o,{label:"其他",value:"other"})])),_:1},8,["modelValue"])])),_:1}),i(t,{label:"备注说明"},{default:u((()=>[i(s,{modelValue:T.description,"onUpdate:modelValue":a[2]||(a[2]=e=>T.description=e),type:"textarea",rows:3,placeholder:"请输入备注说明（可选）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])),_:1}),i(k,{title:"批量操作提醒",type:"info",closable:!1,"show-icon":""},{default:u((()=>[m("div",null,[m("p",null,"• 将为 "+h(e.userIds.length)+" 个用户各充值 ¥"+h(T.amount||"0.00"),1),m("p",null,"• 总计充值金额: ¥"+h(X.value),1),a[6]||(a[6]=m("p",null,"• 操作不可撤销，请仔细确认",-1))])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-7c77a14e"]]);export{q as default};
