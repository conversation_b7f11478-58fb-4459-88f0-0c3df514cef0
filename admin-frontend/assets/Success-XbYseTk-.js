import{_ as s}from"./index-rNRt1EuS.js";import{c as a,o as e,e as t,b as n,w as c,K as l,H as o}from"./vendor-CAPBtMef.js";const r={class:"page-content success"},d={class:"btn-group"};const i=s({},[["render",function(s,i){const u=o;return e(),a("div",r,[i[3]||(i[3]=t("i",{class:"iconfont-sys icon"},"",-1)),i[4]||(i[4]=t("h1",{class:"title"},"提交成功",-1)),i[5]||(i[5]=t("p",{class:"msg"},"提交结果页用于反馈一系列操作任务的处理结果，如果仅是简单操作，使用 Message 全局提示反馈即可。灰色区域可以显示一些补充的信息。",-1)),i[6]||(i[6]=t("div",{class:"res"},[t("p",null,"已提交申请，等待部门审核。")],-1)),t("div",d,[n(u,{type:"primary"},{default:c((()=>i[0]||(i[0]=[l("返回修改")]))),_:1}),n(u,null,{default:c((()=>i[1]||(i[1]=[l("查看")]))),_:1}),n(u,null,{default:c((()=>i[2]||(i[2]=[l("打印")]))),_:1})])])}],["__scopeId","data-v-80f4e96f"]]);export{i as default};
