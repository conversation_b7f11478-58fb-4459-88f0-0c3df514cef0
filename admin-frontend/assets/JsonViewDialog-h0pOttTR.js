import{_ as e}from"./index-rNRt1EuS.js";/* empty css                 */import{d as a,r as s,p as l,N as t,o,w as n,e as c,c as u,a as i,b as r,H as v,u as d,bq as m,K as p,bG as b,b3 as f,x as y,a$ as h,Y as j,M as J}from"./vendor-CAPBtMef.js";const N={class:"json-viewer"},_={class:"json-toolbar"},x={class:"json-content"},O={key:0,class:"validation-result"},S={class:"dialog-footer"},w=e(a({__name:"JsonViewDialog",props:{visible:{type:Boolean},jsonData:{}},emits:["update:visible"],setup(e,{emit:a}){const w=e,C=a,D=s(!1),g=s(""),k=s(""),V=s(!0);l((()=>w.visible),(e=>{D.value=e,e&&z()})),l(D,(e=>{C("update:visible",e)}));const z=()=>{g.value=w.jsonData,k.value="",P()},P=()=>{try{const e=JSON.parse(w.jsonData);g.value=JSON.stringify(e,null,2),V.value=!0,k.value=""}catch(e){g.value=w.jsonData,V.value=!1,k.value="JSON格式不正确"}},$=()=>{try{JSON.parse(w.jsonData),V.value=!0,k.value="JSON格式正确",J.success("JSON格式验证通过")}catch(e){V.value=!1,k.value=`JSON格式错误: ${e.message}`,J.error("JSON格式验证失败")}},q=()=>{return e=this,a=null,s=function*(){try{yield navigator.clipboard.writeText(g.value),J.success("已复制到剪贴板")}catch(e){const a=document.createElement("textarea");a.value=g.value,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a),J.success("已复制到剪贴板")}},new Promise(((l,t)=>{var o=e=>{try{c(s.next(e))}catch(a){t(a)}},n=e=>{try{c(s.throw(e))}catch(a){t(a)}},c=e=>e.done?l(e.value):Promise.resolve(e.value).then(o,n);c((s=s.apply(e,a)).next())}));var e,a,s},B=()=>{D.value=!1};return(e,a)=>{const s=v,l=h,J=j;return o(),t(J,{modelValue:D.value,"onUpdate:modelValue":a[0]||(a[0]=e=>D.value=e),title:"JSON数据查看",width:"700px","before-close":B},{footer:n((()=>[c("div",S,[r(s,{onClick:B},{default:n((()=>a[4]||(a[4]=[p("关闭")]))),_:1})])])),default:n((()=>[c("div",N,[c("div",_,[r(s,{size:"small",onClick:q,icon:d(m)},{default:n((()=>a[1]||(a[1]=[p(" 复制 ")]))),_:1},8,["icon"]),r(s,{size:"small",onClick:P,icon:d(b)},{default:n((()=>a[2]||(a[2]=[p(" 格式化 ")]))),_:1},8,["icon"]),r(s,{size:"small",onClick:$,icon:d(f)},{default:n((()=>a[3]||(a[3]=[p(" 验证 ")]))),_:1},8,["icon"])]),c("div",x,[c("pre",null,[c("code",null,y(g.value),1)])]),k.value?(o(),u("div",O,[r(l,{title:k.value,type:V.value?"success":"error",closable:!1,"show-icon":""},null,8,["title","type"])])):i("",!0)])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-0f54f9b9"]]);export{w as default};
