import{_ as e}from"./index-rNRt1EuS.js";/* empty css                 *//* empty css                       *//* empty css                 */import{d as a,r as t,X as s,j as l,p as r,N as n,o,w as u,b as d,Z as i,G as c,_ as p,e as m,c as _,F as v,A as y,aX as f,K as g,x as b,aZ as h,a_ as j,C as V,n as w,ar as x,as as $,a$ as C,H as I,Y as F,ai as A,M as U}from"./vendor-CAPBtMef.js";import{a as k}from"./balanceApi-B8_MfzSO.js";const B={class:"selected-users"},q={class:"user-count"},E={class:"amount-tip"},O={class:"dialog-footer"},P=e(a({__name:"BatchAdjustDialog",props:{visible:{type:Boolean},userIds:{}},emits:["update:visible","success"],setup(e,{emit:a}){const P=e,T=a,X=t(!1),Z=t(!1),D=t(),G=s({adjustment_type:"increase",amount:"",reason:"",description:""}),H=l((()=>((parseFloat(G.amount)||0)*P.userIds.length).toFixed(2))),K={adjustment_type:[{required:!0,message:"请选择调整类型",trigger:"change"}],amount:[{required:!0,message:"请输入调整金额",trigger:"blur"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入有效的金额（最多两位小数）",trigger:"blur"},{validator:(e,a,t)=>{const s=parseFloat(a);s<=0?t(new Error("调整金额必须大于0")):s>1e4?t(new Error("单个用户调整金额不能超过1万")):t()},trigger:"blur"}],reason:[{required:!0,message:"请选择调整原因",trigger:"change"}]};r((()=>P.visible),(e=>{X.value=e,e&&M()})),r(X,(e=>{T("update:visible",e)}));const M=()=>{var e;Object.assign(G,{adjustment_type:"increase",amount:"",reason:"",description:""}),null==(e=D.value)||e.clearValidate()},N=()=>{X.value=!1},R=()=>{return e=this,a=null,t=function*(){if(D.value)try{if(!(yield D.value.validate()))return;const e="increase"===G.adjustment_type?"增加":"减少",a=`确认为 ${P.userIds.length} 个用户各${e} ¥${G.amount}，总计${e} ¥${H.value} 吗？`;yield A.confirm(a,"确认批量调整",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"}),Z.value=!0;const t={operation_type:`batch_${G.adjustment_type}`,user_ids:P.userIds,amount:G.amount,reason:G.reason,operations:P.userIds.map((e=>({user_id:e,amount:G.amount,description:G.description})))},s=yield k.batchOperation(t);if(s.success){const{success_count:e,failure_count:a}=s.data;0===a?U.success(`批量调整成功，共处理 ${e} 个用户`):U.warning(`批量调整完成，成功 ${e} 个，失败 ${a} 个`),T("success"),N()}else U.error(s.message||"批量调整失败")}catch(e){"cancel"!==e&&U.error("批量调整失败")}finally{Z.value=!1}},new Promise(((s,l)=>{var r=e=>{try{o(t.next(e))}catch(a){l(a)}},n=e=>{try{o(t.throw(e))}catch(a){l(a)}},o=e=>e.done?s(e.value):Promise.resolve(e.value).then(r,n);o((t=t.apply(e,a)).next())}));var e,a,t};return(e,a)=>{const t=f,s=p,l=j,r=h,A=V,U=$,k=x,P=C,T=i,M=I,S=F;return o(),n(S,{modelValue:X.value,"onUpdate:modelValue":a[5]||(a[5]=e=>X.value=e),title:"批量调整",width:"600px","before-close":N},{footer:u((()=>[m("div",O,[d(M,{onClick:N},{default:u((()=>a[9]||(a[9]=[g("取消")]))),_:1}),d(M,{type:"increase"===G.adjustment_type?"success":"danger",onClick:R,loading:Z.value},{default:u((()=>a[10]||(a[10]=[g(" 确认批量调整 ")]))),_:1},8,["type","loading"])])])),default:u((()=>[d(T,{ref_key:"formRef",ref:D,model:G,rules:K,"label-width":"100px",onSubmit:a[4]||(a[4]=c((()=>{}),["prevent"]))},{default:u((()=>[d(s,{label:"选中用户"},{default:u((()=>[m("div",B,[(o(!0),_(v,null,y(e.userIds,(e=>(o(),n(t,{key:e,type:"info",style:{"margin-right":"8px","margin-bottom":"8px"}},{default:u((()=>[g(b(e),1)])),_:2},1024)))),128)),m("div",q,"共 "+b(e.userIds.length)+" 个用户",1)])])),_:1}),d(s,{label:"调整类型",prop:"adjustment_type"},{default:u((()=>[d(r,{modelValue:G.adjustment_type,"onUpdate:modelValue":a[0]||(a[0]=e=>G.adjustment_type=e)},{default:u((()=>[d(l,{value:"increase"},{default:u((()=>a[6]||(a[6]=[m("span",{style:{color:"#67C23A"}},"批量增加余额",-1)]))),_:1}),d(l,{value:"decrease"},{default:u((()=>a[7]||(a[7]=[m("span",{style:{color:"#F56C6C"}},"批量减少余额",-1)]))),_:1})])),_:1},8,["modelValue"])])),_:1}),d(s,{label:"调整金额",prop:"amount"},{default:u((()=>[d(A,{modelValue:G.amount,"onUpdate:modelValue":a[1]||(a[1]=e=>G.amount=e),placeholder:"请输入每个用户的调整金额",clearable:""},{prepend:u((()=>[m("span",{style:w({color:"increase"===G.adjustment_type?"#67C23A":"#F56C6C"})},b("increase"===G.adjustment_type?"+¥":"-¥"),5)])),_:1},8,["modelValue"]),m("div",E," 总调整金额: "+b("increase"===G.adjustment_type?"+":"-")+"¥"+b(H.value),1)])),_:1}),d(s,{label:"调整原因",prop:"reason"},{default:u((()=>[d(k,{modelValue:G.reason,"onUpdate:modelValue":a[2]||(a[2]=e=>G.reason=e),placeholder:"请选择调整原因",style:{width:"100%"}},{default:u((()=>[d(U,{label:"批量系统错误修正",value:"batch_system_error_correction"}),d(U,{label:"批量用户申诉处理",value:"batch_user_complaint_handling"}),d(U,{label:"批量数据异常修复",value:"batch_data_anomaly_fix"}),d(U,{label:"批量业务规则调整",value:"batch_business_rule_adjustment"}),d(U,{label:"批量管理员调整",value:"batch_admin_adjustment"}),d(U,{label:"其他",value:"other"})])),_:1},8,["modelValue"])])),_:1}),d(s,{label:"备注说明"},{default:u((()=>[d(A,{modelValue:G.description,"onUpdate:modelValue":a[3]||(a[3]=e=>G.description=e),type:"textarea",rows:3,placeholder:"请输入备注说明（可选）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])),_:1}),d(P,{title:"批量操作提醒",type:"increase"===G.adjustment_type?"success":"warning",closable:!1,"show-icon":""},{default:u((()=>[m("div",null,[m("p",null,"• 将为 "+b(e.userIds.length)+" 个用户各"+b("increase"===G.adjustment_type?"增加":"减少")+" ¥"+b(G.amount||"0.00"),1),m("p",null,"• 总计"+b("increase"===G.adjustment_type?"增加":"减少")+"金额: ¥"+b(H.value),1),a[8]||(a[8]=m("p",null,"• 操作不可撤销，请仔细确认",-1))])])),_:1},8,["type"])])),_:1},8,["model"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-3a1031ae"]]);export{P as default};
