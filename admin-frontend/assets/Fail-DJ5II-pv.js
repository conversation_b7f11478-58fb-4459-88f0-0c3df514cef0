import{_ as a}from"./index-rNRt1EuS.js";import{c as s,o as t,b9 as c,e as d,b as i,w as o,K as f,H as n}from"./vendor-CAPBtMef.js";const e={class:"page-content fail"},p={class:"btn-group"};const l=a({},[["render",function(a,l){const v=n;return t(),s("div",e,[l[2]||(l[2]=c('<i class="iconfont-sys icon" data-v-f9c01549></i><h1 class="title" data-v-f9c01549>提交失败</h1><p class="msg" data-v-f9c01549>请核对并修改以下信息后，再重新提交。</p><div class="res" data-v-f9c01549><p data-v-f9c01549>您提交的内容有如下错误：</p><p data-v-f9c01549><i class="iconfont-sys" data-v-f9c01549></i>您的账户已被冻结</p><p data-v-f9c01549><i class="iconfont-sys" data-v-f9c01549></i>您的账户还不具备申请资格</p></div>',4)),d("div",p,[i(v,{type:"primary"},{default:o((()=>l[0]||(l[0]=[f("返回修改")]))),_:1}),i(v,null,{default:o((()=>l[1]||(l[1]=[f("查看")]))),_:1})])])}],["__scopeId","data-v-f9c01549"]]);export{l as default};
