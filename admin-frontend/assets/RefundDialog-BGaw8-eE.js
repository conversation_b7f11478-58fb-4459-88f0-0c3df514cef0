import{_ as e}from"./index-rNRt1EuS.js";/* empty css                 */import{d as a,r as l,X as r,p as o,N as s,o as u,w as t,b as d,Z as n,G as i,a as c,_ as m,e as p,x as _,C as f,K as v,ar as b,as as g,a$ as V,H as h,Y as y,ai as w,M as x}from"./vendor-CAPBtMef.js";import{a as U}from"./balanceApi-B8_MfzSO.js";const $={class:"user-info"},D={class:"user-name"},I={class:"user-email"},j={class:"current-balance"},k={class:"dialog-footer"},q=e(a({__name:"RefundDialog",props:{visible:{type:Boolean},user:{}},emits:["update:visible","success"],setup(e,{emit:a}){const q=e,B=a,C=l(!1),F=l(!1),R=l(),E=r({user_id:"",amount:"",order_no:"",reference_id:"",reason:"",description:""}),P={user_id:[{required:!0,message:"请输入用户ID",trigger:"blur"}],amount:[{required:!0,message:"请输入退款金额",trigger:"blur"},{pattern:/^\d+(\.\d{1,2})?$/,message:"请输入有效的金额（最多两位小数）",trigger:"blur"},{validator:(e,a,l)=>{const r=parseFloat(a);r<=0?l(new Error("退款金额必须大于0")):r>1e5?l(new Error("单次退款金额不能超过10万")):l()},trigger:"blur"}],reason:[{required:!0,message:"请选择退款原因",trigger:"change"}]};o((()=>q.visible),(e=>{C.value=e,e&&(T(),q.user&&(E.user_id=q.user.user_id))})),o(C,(e=>{B("update:visible",e)}));const T=()=>{var e;Object.assign(E,{user_id:"",amount:"",order_no:"",reference_id:"",reason:"",description:""}),null==(e=R.value)||e.clearValidate()},A=()=>{C.value=!1},G=()=>{return e=this,a=null,l=function*(){if(R.value)try{if(!(yield R.value.validate()))return;const e=q.user?`确认为用户 ${q.user.username} 退款 ¥${E.amount} 吗？`:`确认为用户 ${E.user_id} 退款 ¥${E.amount} 吗？`;yield w.confirm(e,"确认退款",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"}),F.value=!0;const a=yield U.forceRefund(E);a.success?(x.success("退款成功"),B("success"),A()):x.error(a.message||"退款失败")}catch(e){"cancel"!==e&&x.error("退款失败")}finally{F.value=!1}},new Promise(((r,o)=>{var s=e=>{try{t(l.next(e))}catch(a){o(a)}},u=e=>{try{t(l.throw(e))}catch(a){o(a)}},t=e=>e.done?r(e.value):Promise.resolve(e.value).then(s,u);t((l=l.apply(e,a)).next())}));var e,a,l},H=e=>{const a="string"==typeof e?parseFloat(e):e;return`¥${(null==a?void 0:a.toFixed(2))||"0.00"}`};return(e,a)=>{const l=m,r=f,o=g,w=b,x=V,U=n,q=h,B=y;return u(),s(B,{modelValue:C.value,"onUpdate:modelValue":a[7]||(a[7]=e=>C.value=e),title:"强制退款",width:"500px","before-close":A},{footer:t((()=>[p("div",k,[d(q,{onClick:A},{default:t((()=>a[10]||(a[10]=[v("取消")]))),_:1}),d(q,{type:"danger",onClick:G,loading:F.value},{default:t((()=>a[11]||(a[11]=[v(" 确认退款 ")]))),_:1},8,["loading"])])])),default:t((()=>[d(U,{ref_key:"formRef",ref:R,model:E,rules:P,"label-width":"100px",onSubmit:a[6]||(a[6]=i((()=>{}),["prevent"]))},{default:t((()=>[e.user?(u(),s(l,{key:0,label:"用户信息"},{default:t((()=>[p("div",$,[p("div",D,_(e.user.username),1),p("div",I,_(e.user.email),1),p("div",j,"当前余额: "+_(H(e.user.balance)),1)])])),_:1})):c("",!0),e.user?c("",!0):(u(),s(l,{key:1,label:"用户ID",prop:"user_id"},{default:t((()=>[d(r,{modelValue:E.user_id,"onUpdate:modelValue":a[0]||(a[0]=e=>E.user_id=e),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])])),_:1})),d(l,{label:"退款金额",prop:"amount"},{default:t((()=>[d(r,{modelValue:E.amount,"onUpdate:modelValue":a[1]||(a[1]=e=>E.amount=e),placeholder:"请输入退款金额",clearable:""},{prepend:t((()=>a[8]||(a[8]=[v("¥")]))),_:1},8,["modelValue"])])),_:1}),d(l,{label:"订单号",prop:"order_no"},{default:t((()=>[d(r,{modelValue:E.order_no,"onUpdate:modelValue":a[2]||(a[2]=e=>E.order_no=e),placeholder:"请输入相关订单号（可选）",clearable:""},null,8,["modelValue"])])),_:1}),d(l,{label:"参考ID",prop:"reference_id"},{default:t((()=>[d(r,{modelValue:E.reference_id,"onUpdate:modelValue":a[3]||(a[3]=e=>E.reference_id=e),placeholder:"请输入相关参考ID（可选）",clearable:""},null,8,["modelValue"])])),_:1}),d(l,{label:"退款原因",prop:"reason"},{default:t((()=>[d(w,{modelValue:E.reason,"onUpdate:modelValue":a[4]||(a[4]=e=>E.reason=e),placeholder:"请选择退款原因",style:{width:"100%"}},{default:t((()=>[d(o,{label:"订单取消退款",value:"order_cancellation"}),d(o,{label:"服务异常退款",value:"service_exception"}),d(o,{label:"用户申请退款",value:"user_request"}),d(o,{label:"系统错误退款",value:"system_error"}),d(o,{label:"重复扣费退款",value:"duplicate_charge"}),d(o,{label:"管理员强制退款",value:"admin_force_refund"}),d(o,{label:"其他",value:"other"})])),_:1},8,["modelValue"])])),_:1}),d(l,{label:"备注说明",prop:"description"},{default:t((()=>[d(r,{modelValue:E.description,"onUpdate:modelValue":a[5]||(a[5]=e=>E.description=e),type:"textarea",rows:3,placeholder:"请输入备注说明（可选）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])),_:1}),d(x,{title:"注意",type:"warning",closable:!1,"show-icon":""},{default:t((()=>a[9]||(a[9]=[p("div",null,"强制退款将直接增加用户余额，请确认退款金额和原因正确。",-1)]))),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-b3132e96"]]);export{q as default};
