var e=Object.defineProperty,l=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,u=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,o=(e,l,a)=>new Promise(((t,u)=>{var o=e=>{try{i(a.next(e))}catch(l){u(l)}},s=e=>{try{i(a.throw(e))}catch(l){u(l)}},i=e=>e.done?t(e.value):Promise.resolve(e.value).then(o,s);i((a=a.apply(e,l)).next())}));import{_ as s}from"./index-rNRt1EuS.js";/* empty css                    *//* empty css                             *//* empty css                       *//* empty css                 *//* empty css                 *//* empty css                   *//* empty css                  */import{d as i,j as r,r as d,N as n,o as p,w as v,b as c,c as m,a as _,bt as f,bu as b,bv as y,e as x,y as g,u as h,bw as w,K as k,bp as V,a$ as j,Z as C,_ as P,aZ as B,a_ as O,aD as I,b1 as S,b2 as U,x as z,b8 as D,bx as F,aI as K,aJ as $,aX as E,H as M,Y as G,M as T}from"./vendor-CAPBtMef.js";import{F as Z}from"./flexiblePriceTableApi-BBM6dCg8.js";const A={key:0},H={key:1},J={key:2},L={key:0,class:"importing-status"},N={key:1},R={key:0},X={key:1},Y={key:0,style:{"text-align":"center",color:"#999","margin-top":"10px"}},q={class:"dialog-footer"},Q=s(i({__name:"ImportDialog",props:{visible:{type:Boolean}},emits:["update:visible","imported"],setup(e,{emit:s}){const i=e,Q=s,W=r({get:()=>i.visible,set:e=>Q("update:visible",e)}),ee=d(0),le=d([]),ae=d(null),te=d(!1),ue=d(null),oe=d({import_mode:"append",validate_only:!1,skip_duplicate:!0}),se=r((()=>(0===ee.value||1===ee.value)&&null!==ae.value)),ie=(e,l)=>{e.raw&&(ae.value=e.raw,le.value=l)},re=()=>o(this,null,(function*(){0===ee.value?ee.value=1:1===ee.value&&(yield ne())})),de=()=>{ee.value>0&&ee.value--},ne=()=>o(this,null,(function*(){if(ae.value)try{te.value=!0,ee.value=2;const e=((e,o)=>{for(var s in o||(o={}))a.call(o,s)&&u(e,s,o[s]);if(l)for(var s of l(o))t.call(o,s)&&u(e,s,o[s]);return e})({file:ae.value},oe.value),o=yield Z.importPriceData(e);o.success?(ue.value=o.data,!oe.value.validate_only&&o.data.success&&Q("imported")):T.error(o.message||"导入失败")}catch(e){T.error("导入失败")}finally{te.value=!1}else T.error("请选择文件")})),pe=()=>{W.value=!1,ee.value=0,le.value=[],ae.value=null,ue.value=null,oe.value={import_mode:"append",validate_only:!1,skip_duplicate:!0}},ve=e=>{var l;switch(null==(l=e.split(".").pop())?void 0:l.toLowerCase()){case"csv":return"CSV 文件";case"xlsx":return"Excel 文件 (xlsx)";case"xls":return"Excel 文件 (xls)";default:return"未知类型"}};return(e,l)=>{const a=b,t=f,u=g,o=y,s=V,i=j,r=O,d=B,T=P,Z=I,Q=U,ne=S,ce=C,me=D,_e=F,fe=$,be=K,ye=E,xe=M,ge=G;return p(),n(ge,{modelValue:W.value,"onUpdate:modelValue":l[3]||(l[3]=e=>W.value=e),title:"导入价格数据",width:"700px",onClose:pe},{footer:v((()=>[x("div",q,[c(xe,{onClick:pe},{default:v((()=>[k(z(2===ee.value?"关闭":"取消"),1)])),_:1}),ee.value>0&&ee.value<2?(p(),n(xe,{key:0,onClick:de},{default:v((()=>l[18]||(l[18]=[k(" 上一步 ")]))),_:1})):_("",!0),ee.value<2?(p(),n(xe,{key:1,type:"primary",onClick:re,disabled:!se.value},{default:v((()=>[k(z(1===ee.value?"开始导入":"下一步"),1)])),_:1},8,["disabled"])):_("",!0)])])),default:v((()=>[c(t,{active:ee.value,"finish-status":"success",style:{"margin-bottom":"30px"}},{default:v((()=>[c(a,{title:"选择文件"}),c(a,{title:"配置参数"}),c(a,{title:"导入结果"})])),_:1},8,["active"]),0===ee.value?(p(),m("div",A,[c(o,{ref:"uploadRef",class:"upload-demo",drag:"","auto-upload":!1,limit:1,accept:".csv,.xlsx,.xls","on-change":ie,"file-list":le.value},{tip:v((()=>l[4]||(l[4]=[x("div",{class:"el-upload__tip"}," 支持 CSV 格式文件，文件大小不超过 10MB ",-1)]))),default:v((()=>[c(u,{class:"el-icon--upload"},{default:v((()=>[c(h(w))])),_:1}),l[5]||(l[5]=x("div",{class:"el-upload__text"},[k(" 将文件拖到此处，或"),x("em",null,"点击上传")],-1))])),_:1},8,["file-list"]),c(s,{"content-position":"left"},{default:v((()=>l[6]||(l[6]=[k("文件格式说明")]))),_:1}),c(i,{title:"CSV文件格式要求",type:"info",closable:!1,style:{"margin-bottom":"15px"}},{default:v((()=>l[7]||(l[7]=[x("p",null,"请确保CSV文件包含以下列（顺序可以不同）：",-1),x("ul",null,[x("li",null,[x("strong",null,"必填字段："),k("供应商、快递公司代码、始发省份、目的省份、重量(KG)")]),x("li",null,[x("strong",null,"可选字段："),k("快递公司名称、始发城市、目的城市、价格(元)、币种等")]),x("li",null,[x("strong",null,"编码格式："),k("UTF-8（推荐）或 GBK")])],-1)]))),_:1})])):_("",!0),1===ee.value?(p(),m("div",H,[c(ce,{model:oe.value,"label-width":"120px"},{default:v((()=>[c(T,{label:"导入模式"},{default:v((()=>[c(d,{modelValue:oe.value.import_mode,"onUpdate:modelValue":l[0]||(l[0]=e=>oe.value.import_mode=e)},{default:v((()=>[c(r,{label:"append"},{default:v((()=>l[8]||(l[8]=[k("追加模式")]))),_:1}),c(r,{label:"update"},{default:v((()=>l[9]||(l[9]=[k("更新模式")]))),_:1}),c(r,{label:"replace"},{default:v((()=>l[10]||(l[10]=[k("替换模式")]))),_:1})])),_:1},8,["modelValue"]),l[11]||(l[11]=x("div",{class:"form-tip"},[x("p",null,[x("strong",null,"追加模式："),k("直接添加新数据，不检查重复")]),x("p",null,[x("strong",null,"更新模式："),k("存在则更新，不存在则插入")]),x("p",null,[x("strong",null,"替换模式："),k("清空现有数据，然后导入新数据")])],-1))])),_:1}),c(T,{label:"处理选项"},{default:v((()=>[c(Z,{modelValue:oe.value.validate_only,"onUpdate:modelValue":l[1]||(l[1]=e=>oe.value.validate_only=e)},{default:v((()=>l[12]||(l[12]=[k("仅验证数据（不实际导入）")]))),_:1},8,["modelValue"]),l[14]||(l[14]=x("br",null,null,-1)),c(Z,{modelValue:oe.value.skip_duplicate,"onUpdate:modelValue":l[2]||(l[2]=e=>oe.value.skip_duplicate=e)},{default:v((()=>l[13]||(l[13]=[k("跳过重复数据")]))),_:1},8,["modelValue"])])),_:1}),c(T,{label:"文件信息"},{default:v((()=>[c(ne,{column:2,border:""},{default:v((()=>[c(Q,{label:"文件名"},{default:v((()=>{var e;return[k(z(null==(e=ae.value)?void 0:e.name),1)]})),_:1}),c(Q,{label:"文件大小"},{default:v((()=>{var e,l;return[k(z((l=(null==(e=ae.value)?void 0:e.size)||0,l<1024?`${l} B`:l<1048576?`${(l/1024).toFixed(1)} KB`:`${(l/1048576).toFixed(1)} MB`)),1)]})),_:1}),c(Q,{label:"文件类型"},{default:v((()=>{var e;return[k(z(ve((null==(e=ae.value)?void 0:e.name)||"")),1)]})),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])):_("",!0),2===ee.value?(p(),m("div",J,[te.value?(p(),m("div",L,[c(me,{percentage:100,indeterminate:!0}),l[15]||(l[15]=x("p",{style:{"text-align":"center","margin-top":"10px"}},"正在导入数据，请稍候...",-1))])):ue.value?(p(),m("div",N,[c(_e,{icon:ue.value.success?"success":"error",title:ue.value.success?"导入成功":"导入失败","sub-title":ue.value.message},{extra:v((()=>[c(ne,{column:2,border:""},{default:v((()=>[c(Q,{label:"总行数"},{default:v((()=>[k(z(ue.value.total_rows),1)])),_:1}),c(Q,{label:"成功行数"},{default:v((()=>[k(z(ue.value.success_rows),1)])),_:1}),c(Q,{label:"失败行数"},{default:v((()=>[k(z(ue.value.failed_rows),1)])),_:1}),c(Q,{label:"跳过行数"},{default:v((()=>[k(z(ue.value.skipped_rows),1)])),_:1}),c(Q,{label:"执行时间"},{default:v((()=>[k(z(ue.value.execution_time)+"ms",1)])),_:1}),c(Q,{label:"任务ID"},{default:v((()=>[k(z(ue.value.task_id),1)])),_:1})])),_:1})])),_:1},8,["icon","title","sub-title"]),ue.value.validation_errors&&ue.value.validation_errors.length>0?(p(),m("div",R,[c(s,{"content-position":"left"},{default:v((()=>l[16]||(l[16]=[k("验证错误")]))),_:1}),c(be,{data:ue.value.validation_errors,size:"small","max-height":"200"},{default:v((()=>[c(fe,{prop:"row",label:"行号",width:"80"}),c(fe,{prop:"column",label:"字段",width:"120"}),c(fe,{prop:"value",label:"值",width:"120"}),c(fe,{prop:"message",label:"错误信息"})])),_:1},8,["data"])])):_("",!0),ue.value.imported_data&&ue.value.imported_data.length>0?(p(),m("div",X,[c(s,{"content-position":"left"},{default:v((()=>l[17]||(l[17]=[k("导入详情")]))),_:1}),c(be,{data:ue.value.imported_data.slice(0,100),size:"small","max-height":"200"},{default:v((()=>[c(fe,{prop:"row",label:"行号",width:"80"}),c(fe,{prop:"provider",label:"供应商",width:"80"}),c(fe,{prop:"express_code",label:"快递公司",width:"100"}),c(fe,{prop:"route",label:"路线",width:"150"}),c(fe,{prop:"action",label:"操作",width:"80"},{default:v((({row:e})=>[c(ye,{type:"inserted"===e.action?"success":"updated"===e.action?"warning":"info",size:"small"},{default:v((()=>[k(z("inserted"===e.action?"新增":"updated"===e.action?"更新":"跳过"),1)])),_:2},1032,["type"])])),_:1})])),_:1},8,["data"]),ue.value.imported_data.length>100?(p(),m("p",Y," 仅显示前100条记录 ")):_("",!0)])):_("",!0)])):_("",!0)])):_("",!0)])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-f129eb40"]]);export{Q as default};
