import{t as e,v as a,w as l,_ as s}from"./index-rNRt1EuS.js";/* empty css                             */import{d as t,r,s as i,c as d,o as u,e as o,b as n,w as c,K as v,H as b,u as f,b2 as m,b1 as _}from"./vendor-CAPBtMef.js";const p={class:"page-content"},k={class:"action-buttons"},g=s(t({__name:"Fireworks",setup(s){const t=r(null),g=r(!1),w=()=>{l.emit("triggerFireworks")},C=e=>{((e,a)=>{t.value&&(clearInterval(t.value),t.value=null),g.value=!0;let s=0;t.value=setInterval((()=>{l.emit("triggerFireworks",a),s++,s>=e&&(clearInterval(t.value),t.value=null,g.value=!1)}),1e3)})(10,e)},F=e=>{l.emit("triggerFireworks",e)};return i((()=>{t.value&&(clearInterval(t.value),t.value=null)})),(l,s)=>{const t=b,r=m,i=_;return u(),d("div",p,[o("div",k,[n(t,{disabled:g.value,onClick:w},{default:c((()=>s[4]||(s[4]=[v("✨ 放个小烟花")]))),_:1},8,["disabled"]),n(t,{disabled:g.value,onClick:s[0]||(s[0]=a=>F(f(e)))},{default:c((()=>s[5]||(s[5]=[v("🎉 打开幸运红包")]))),_:1},8,["disabled"]),n(t,{disabled:g.value,onClick:s[1]||(s[1]=e=>C(""))},{default:c((()=>s[6]||(s[6]=[v("🎆 璀璨烟火秀")]))),_:1},8,["disabled"]),n(t,{disabled:g.value,onClick:s[2]||(s[2]=e=>F(f(a)))},{default:c((()=>s[7]||(s[7]=[v("❄️ 飘点小雪花")]))),_:1},8,["disabled"]),n(t,{disabled:g.value,onClick:s[3]||(s[3]=e=>C(f(a)))},{default:c((()=>s[8]||(s[8]=[v("❄️ 浪漫暴风雪")]))),_:1},8,["disabled"])]),n(i,{title:"礼花组件说明",direction:"vertical",column:1,border:"",style:{"margin-top":"50px"}},{default:c((()=>[n(r,{label:"显示时机"},{default:c((()=>s[9]||(s[9]=[v(" 礼花效果组件全局注册了，在节假日的时候，会自动显示，你可以通过配置文件来控制显示时机 ")]))),_:1}),n(r,{label:"礼花样式"},{default:c((()=>s[10]||(s[10]=[v(" 默认显示几何图形，可以配置图片，图片需要提前在 components/Ceremony/Fireworks 文件预先定义 ")]))),_:1}),n(r,{label:"节日配置"},{default:c((()=>s[11]||(s[11]=[v(" 在 src/config/ceremony.ts 文件中，可以配置节日和对应的礼花样式 ")]))),_:1}),n(r,{label:"快捷键"},{default:c((()=>s[12]||(s[12]=[v(" command + shift + p 或者 ctrl + shift + p ")]))),_:1})])),_:1})])}}}),[["__scopeId","data-v-1f56106d"]]);export{g as default};
