import{_ as a}from"./index-rNRt1EuS.js";/* empty css                 *//* empty css                *//* empty css               */import{d as e,j as s,N as l,o as t,w as i,c,a as r,b as n,aV as p,aT as d,aN as o,e as u,aX as _,u as v,K as m,x as f,a$ as D,C as g,H as y,y as h,bq as $,Y as b,M as w}from"./vendor-CAPBtMef.js";import{g as x,a as k,b as j,f as q,c as N}from"./priceTable-dC3_lJoq.js";const V={key:0,class:"detail-content"},C={class:"detail-item"},I={class:"detail-item"},T={class:"value"},P={class:"detail-item"},U={class:"value"},B={class:"detail-item"},H={class:"value"},K={class:"detail-item"},M={class:"value"},X={class:"detail-item"},Y={class:"value"},z={key:0},A={class:"detail-item"},E={class:"value price-highlight"},F={class:"detail-item"},G={class:"value"},J={class:"detail-item"},L={class:"value"},O={class:"detail-item"},Q={class:"value"},R={class:"detail-item"},S={class:"value"},W={class:"detail-item"},Z={class:"value"},aa={class:"detail-item"},ea={class:"value volume-ratio"},sa={class:"detail-item"},la={class:"value"},ta={class:"detail-item"},ia={class:"value"},ca={class:"detail-item"},ra={class:"value weight-range"},na={key:1,class:"error-info"},pa={class:"detail-item"},da={class:"detail-item"},oa={class:"value"},ua={class:"detail-item"},_a={class:"value"},va={class:"detail-item"},ma={class:"value"},fa={class:"detail-item"},Da={class:"value"},ga={class:"detail-item"},ya={class:"value code"},ha={class:"error-detail"},$a={class:"dialog-footer"},ba=a(e({__name:"DetailDialog",props:{visible:{type:Boolean},priceData:{}},emits:["update:visible"],setup(a,{emit:e}){const ba=a,wa=e,xa=s({get:()=>ba.visible,set:a=>wa("update:visible",a)}),ka=()=>{wa("update:visible",!1)},ja=a=>null==a||isNaN(a)?"-":`${a}kg`,qa=a=>{const e=q(a);return"-"===e?"-":`${e}/kg`},Na=a=>null==a||isNaN(a)?"-":`1:${a}`,Va=a=>null==a||isNaN(a)?"-":`${a}天`,Ca=a=>`${a.first_weight||1}-${a.max_weight||50}kg`,Ia=()=>{return a=this,e=null,s=function*(){if(!ba.priceData)return;const a=ba.priceData,e=`\n价格数据详情\n===================\n供应商: ${k(a.provider)}\n快递公司: ${a.express_name} (${a.express_code})\n路线: ${a.from_province} ${a.from_city} → ${a.to_province} ${a.to_city}\n重量: ${a.weight}kg\n服务类型: ${j(a.service_type)}\n\n价格信息:\n总价格: ${q(a.price)}\n货币: ${a.currency||"-"}\n首重: ${ja(a.first_weight)}\n首重价格: ${q(a.first_weight_price)}\n续重: ${ja(a.continue_weight)}\n续重价格: ${qa(a.continue_weight_price)}\n体积比: ${Na(a.volume_ratio)}\n最大重量: ${ja(a.max_weight)}\n预计天数: ${Va(a.estimated_days)}\n重量范围: ${Ca(a)}\n\n查询信息:\n查询状态: ${a.query_success?"成功":"失败"}\n响应时间: ${a.response_time_ms}ms\n查询时间: ${N(a.query_time)}\n创建时间: ${N(a.created_at)}\n更新时间: ${N(a.updated_at)}\n记录ID: ${a.id}\n\n${a.error_message?`错误信息: ${a.error_message}`:""}\n  `.trim();try{yield navigator.clipboard.writeText(e),w.success("信息已复制到剪贴板")}catch(s){w.error("复制失败")}},new Promise(((l,t)=>{var i=a=>{try{r(s.next(a))}catch(e){t(e)}},c=a=>{try{r(s.throw(a))}catch(e){t(e)}},r=a=>a.done?l(a.value):Promise.resolve(a.value).then(i,c);r((s=s.apply(a,e)).next())}));var a,e,s};return(a,e)=>{const s=_,w=o,ba=d,wa=p,Ta=D,Pa=g,Ua=y,Ba=h,Ha=b;return t(),l(Ha,{modelValue:xa.value,"onUpdate:modelValue":e[1]||(e[1]=a=>xa.value=a),title:"价格数据详情",width:"800px",onClose:ka},{footer:i((()=>[u("div",$a,[n(Ua,{onClick:ka},{default:i((()=>e[28]||(e[28]=[m("关闭")]))),_:1}),n(Ua,{type:"primary",onClick:Ia},{default:i((()=>[n(Ba,null,{default:i((()=>[n(v($))])),_:1}),e[29]||(e[29]=m(" 复制信息 "))])),_:1})])])),default:i((()=>[a.priceData?(t(),c("div",V,[n(wa,{class:"detail-section"},{header:i((()=>e[2]||(e[2]=[u("div",{class:"section-header"},[u("i",{class:"el-icon-info"}),m(" 基本信息 ")],-1)]))),default:i((()=>[n(ba,{gutter:20},{default:i((()=>[n(w,{span:12},{default:i((()=>[u("div",C,[e[3]||(e[3]=u("span",{class:"label"},"供应商:",-1)),n(s,{type:v(x)(a.priceData.provider)},{default:i((()=>[m(f(v(k)(a.priceData.provider)),1)])),_:1},8,["type"])])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",I,[e[4]||(e[4]=u("span",{class:"label"},"快递公司:",-1)),u("span",T,f(a.priceData.express_name)+" ("+f(a.priceData.express_code)+")",1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",P,[e[5]||(e[5]=u("span",{class:"label"},"发货地址:",-1)),u("span",U,f(a.priceData.from_province)+" "+f(a.priceData.from_city),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",B,[e[6]||(e[6]=u("span",{class:"label"},"收货地址:",-1)),u("span",H,f(a.priceData.to_province)+" "+f(a.priceData.to_city),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",K,[e[7]||(e[7]=u("span",{class:"label"},"重量:",-1)),u("span",M,f(a.priceData.weight)+"kg",1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",X,[e[8]||(e[8]=u("span",{class:"label"},"服务类型:",-1)),u("span",Y,f(v(j)(a.priceData.service_type)),1)])])),_:1})])),_:1})])),_:1}),n(wa,{class:"detail-section"},{header:i((()=>e[9]||(e[9]=[u("div",{class:"section-header"},[u("i",{class:"el-icon-coin"}),m(" 价格信息 ")],-1)]))),default:i((()=>[a.priceData.query_success?(t(),c("div",z,[n(ba,{gutter:20},{default:i((()=>[n(w,{span:12},{default:i((()=>[u("div",A,[e[10]||(e[10]=u("span",{class:"label"},"总价格:",-1)),u("span",E,f(v(q)(a.priceData.price)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",F,[e[11]||(e[11]=u("span",{class:"label"},"货币:",-1)),u("span",G,f(a.priceData.currency||"-"),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",J,[e[12]||(e[12]=u("span",{class:"label"},"首重:",-1)),u("span",L,f(ja(a.priceData.first_weight)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",O,[e[13]||(e[13]=u("span",{class:"label"},"首重价格:",-1)),u("span",Q,f(v(q)(a.priceData.first_weight_price)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",R,[e[14]||(e[14]=u("span",{class:"label"},"续重:",-1)),u("span",S,f(ja(a.priceData.continue_weight)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",W,[e[15]||(e[15]=u("span",{class:"label"},"续重价格:",-1)),u("span",Z,f(qa(a.priceData.continue_weight_price)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",aa,[e[16]||(e[16]=u("span",{class:"label"},"体积比 (抛比):",-1)),u("span",ea,f(Na(a.priceData.volume_ratio)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",sa,[e[17]||(e[17]=u("span",{class:"label"},"最大重量:",-1)),u("span",la,f(ja(a.priceData.max_weight)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",ta,[e[18]||(e[18]=u("span",{class:"label"},"预计天数:",-1)),u("span",ia,f(Va(a.priceData.estimated_days)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",ca,[e[19]||(e[19]=u("span",{class:"label"},"重量范围:",-1)),u("span",ra,f(Ca(a.priceData)),1)])])),_:1})])),_:1})])):(t(),c("div",na,[n(Ta,{title:"查询失败",description:a.priceData.error_message||"未知错误",type:"error",closable:!1,"show-icon":""},null,8,["description"])]))])),_:1}),n(wa,{class:"detail-section"},{header:i((()=>e[20]||(e[20]=[u("div",{class:"section-header"},[u("i",{class:"el-icon-time"}),m(" 查询信息 ")],-1)]))),default:i((()=>[n(ba,{gutter:20},{default:i((()=>[n(w,{span:12},{default:i((()=>[u("div",pa,[e[21]||(e[21]=u("span",{class:"label"},"查询状态:",-1)),n(s,{type:a.priceData.query_success?"success":"danger"},{default:i((()=>[m(f(a.priceData.query_success?"成功":"失败"),1)])),_:1},8,["type"])])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",da,[e[22]||(e[22]=u("span",{class:"label"},"响应时间:",-1)),u("span",oa,f(a.priceData.response_time_ms)+"ms",1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",ua,[e[23]||(e[23]=u("span",{class:"label"},"查询时间:",-1)),u("span",_a,f(v(N)(a.priceData.query_time)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",va,[e[24]||(e[24]=u("span",{class:"label"},"创建时间:",-1)),u("span",ma,f(v(N)(a.priceData.created_at)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",fa,[e[25]||(e[25]=u("span",{class:"label"},"更新时间:",-1)),u("span",Da,f(v(N)(a.priceData.updated_at)),1)])])),_:1}),n(w,{span:12},{default:i((()=>[u("div",ga,[e[26]||(e[26]=u("span",{class:"label"},"记录ID:",-1)),u("span",ya,f(a.priceData.id),1)])])),_:1})])),_:1})])),_:1}),!a.priceData.query_success&&a.priceData.error_message?(t(),l(wa,{key:0,class:"detail-section"},{header:i((()=>e[27]||(e[27]=[u("div",{class:"section-header"},[u("i",{class:"el-icon-warning"}),m(" 错误信息 ")],-1)]))),default:i((()=>[u("div",ha,[n(Pa,{modelValue:a.priceData.error_message,"onUpdate:modelValue":e[0]||(e[0]=e=>a.priceData.error_message=e),type:"textarea",rows:4,readonly:"",placeholder:"无错误信息"},null,8,["modelValue"])])])),_:1})):r("",!0)])):r("",!0)])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-ec759179"]]);export{ba as default};
