var e=(e,a,l)=>new Promise(((s,t)=>{var d=e=>{try{u(l.next(e))}catch(a){t(a)}},i=e=>{try{u(l.throw(e))}catch(a){t(a)}},u=e=>e.done?s(e.value):Promise.resolve(e.value).then(d,i);u((l=l.apply(e,a)).next())}));import{_ as a}from"./index-rNRt1EuS.js";/* empty css                    *//* empty css                *//* empty css                       *//* empty css                 */import{d as l,r as s,X as t,j as d,N as i,o as u,w as o,e as r,b as c,a as n,aV as p,Z as m,_ as v,aZ as f,a_ as y,K as h,br as g,c as _,ap as b,aq as x,y as w,u as V,bs as k,F as R,A as j,x as U,aX as F,aY as T,b8 as q,bo as C,H as M,Y as O,M as z,ai as B}from"./vendor-CAPBtMef.js";const P={class:"enhanced-update-dialog"},X={class:"section-header"},Y={key:0,class:"retry-config"},Z={class:"section-header"},$={class:"preview-list"},A={class:"route-info"},D={key:0,class:"more-indicator"},E={class:"section-header"},H={class:"progress-content"},I={class:"progress-stats"},K={class:"stat-row"},N={class:"stat-row"},G={class:"success"},J={class:"failed"},L={key:0,class:"current-route"},Q={key:1,class:"estimated-time"},S={class:"dialog-footer"},W=a(l({__name:"EnhancedRouteUpdateDialog",props:{visible:{type:Boolean},provider:{},expressCode:{},selectedRoutes:{},filteredRoutes:{}},emits:["update:visible","update-completed"],setup(a,{emit:l}){const W=a,ee=l,ae=s(!1),le=s(!1),se=t({mode:"selected",concurrency:3,retryOnFailure:!0,maxRetries:2,delayMs:500}),te=s([]),de=t({visible:!1,percentage:0,status:"active",total:0,completed:0,success:0,failed:0,currentRoute:"",estimatedTime:""}),ie=d({get:()=>W.visible,set:e=>ee("update:visible",e)}),ue=()=>e(this,null,(function*(){try{ae.value=!0;let e=[];switch(se.mode){case"selected":e=W.selectedRoutes;break;case"filtered":case"all":e=W.filteredRoutes;break;case"failed":e=W.filteredRoutes.filter((e=>!e.query_success))}if(0===e.length)return void z.warning("没有找到符合条件的路线");te.value=e,z.success(`已生成 ${e.length} 条路线的更新预览`)}catch(e){z.error("生成预览失败")}finally{ae.value=!1}})),oe=()=>e(this,null,(function*(){if(0!==te.value.length)try{yield B.confirm(`确定要更新 ${te.value.length} 条路线吗？`,"确认更新",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),le.value=!0,de.visible=!0,de.percentage=0,de.status="active",de.total=te.value.length,de.completed=0,de.success=0,de.failed=0,z.success("更新功能开发中...")}catch(e){"cancel"!==e&&z.error("更新失败")}finally{le.value=!1}else z.warning("请先生成更新预览")}));return(e,a)=>{const l=w,s=y,t=f,d=v,z=g,B=b,W=x,ee=m,re=p,ce=F,ne=q,pe=M,me=O;return u(),i(me,{modelValue:ie.value,"onUpdate:modelValue":a[6]||(a[6]=e=>ie.value=e),title:"高级路线更新",width:"600px","close-on-click-modal":!1,"destroy-on-close":""},{footer:o((()=>[r("div",S,[c(pe,{onClick:a[5]||(a[5]=e=>ie.value=!1),disabled:le.value},{default:o((()=>a[17]||(a[17]=[h(" 取消 ")]))),_:1},8,["disabled"]),c(pe,{type:"primary",onClick:ue,loading:ae.value,disabled:le.value},{default:o((()=>a[18]||(a[18]=[h(" 生成预览 ")]))),_:1},8,["loading","disabled"]),c(pe,{type:"success",onClick:oe,loading:le.value,disabled:0===te.value.length},{default:o((()=>a[19]||(a[19]=[h(" 开始更新 ")]))),_:1},8,["loading","disabled"])])])),default:o((()=>[r("div",P,[c(re,{class:"update-options"},{header:o((()=>[r("div",X,[c(l,null,{default:o((()=>[c(V(k))])),_:1}),a[7]||(a[7]=h(" 更新选项 "))])])),default:o((()=>[c(ee,{model:se,"label-width":"120px"},{default:o((()=>[c(d,{label:"更新模式"},{default:o((()=>[c(t,{modelValue:se.mode,"onUpdate:modelValue":a[0]||(a[0]=e=>se.mode=e)},{default:o((()=>[c(s,{label:"selected"},{default:o((()=>a[8]||(a[8]=[h("仅选中路线")]))),_:1}),c(s,{label:"filtered"},{default:o((()=>a[9]||(a[9]=[h("当前筛选结果")]))),_:1}),c(s,{label:"failed"},{default:o((()=>a[10]||(a[10]=[h("仅失败路线")]))),_:1}),c(s,{label:"all"},{default:o((()=>a[11]||(a[11]=[h("全部路线")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),c(d,{label:"并发数量"},{default:o((()=>[c(z,{modelValue:se.concurrency,"onUpdate:modelValue":a[1]||(a[1]=e=>se.concurrency=e),min:1,max:10,step:1,"show-stops":"","show-input":"",style:{width:"300px"}},null,8,["modelValue"]),a[12]||(a[12]=r("div",{class:"form-tip"}," 并发数量越高更新越快，但可能增加服务器压力 ",-1))])),_:1}),c(d,{label:"失败重试"},{default:o((()=>[c(B,{modelValue:se.retryOnFailure,"onUpdate:modelValue":a[2]||(a[2]=e=>se.retryOnFailure=e),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"]),se.retryOnFailure?(u(),_("span",Y,[a[13]||(a[13]=h(" 最多重试 ")),c(W,{modelValue:se.maxRetries,"onUpdate:modelValue":a[3]||(a[3]=e=>se.maxRetries=e),min:1,max:5,size:"small",style:{width:"80px",margin:"0 8px"}},null,8,["modelValue"]),a[14]||(a[14]=h(" 次 "))])):n("",!0)])),_:1}),c(d,{label:"更新间隔"},{default:o((()=>[c(W,{modelValue:se.delayMs,"onUpdate:modelValue":a[4]||(a[4]=e=>se.delayMs=e),min:100,max:5e3,step:100,size:"default",style:{width:"150px"}},null,8,["modelValue"]),a[15]||(a[15]=r("span",{class:"form-tip"},"毫秒 (避免请求过快)",-1))])),_:1})])),_:1},8,["model"])])),_:1}),te.value.length>0?(u(),i(re,{key:0,class:"update-preview"},{header:o((()=>[r("div",Z,[c(l,null,{default:o((()=>[c(V(T))])),_:1}),h(" 更新预览 ("+U(te.value.length)+" 条路线) ",1)])])),default:o((()=>[r("div",$,[(u(!0),_(R,null,j(te.value.slice(0,10),((e,a)=>(u(),_("div",{key:a,class:"preview-item"},[r("span",A,U(e.from_province)+" → "+U(e.to_province),1),c(ce,{type:e.query_success?"success":"danger",size:"small"},{default:o((()=>[h(U(e.query_success?"成功":"失败"),1)])),_:2},1032,["type"])])))),128)),te.value.length>10?(u(),_("div",D," 还有 "+U(te.value.length-10)+" 条路线... ",1)):n("",!0)])])),_:1})):n("",!0),de.visible?(u(),i(re,{key:1,class:"update-progress"},{header:o((()=>[r("div",E,[c(l,null,{default:o((()=>[c(V(C))])),_:1}),a[16]||(a[16]=h(" 更新进度 "))])])),default:o((()=>[r("div",H,[c(ne,{percentage:de.percentage,status:de.status,"stroke-width":10},null,8,["percentage","status"]),r("div",I,[r("div",K,[r("span",null,"总计: "+U(de.total),1),r("span",null,"已完成: "+U(de.completed),1)]),r("div",N,[r("span",G,"成功: "+U(de.success),1),r("span",J,"失败: "+U(de.failed),1)])]),de.currentRoute?(u(),_("div",L," 正在更新: "+U(de.currentRoute),1)):n("",!0),de.estimatedTime?(u(),_("div",Q," 预计剩余时间: "+U(de.estimatedTime),1)):n("",!0)])])),_:1})):n("",!0)])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-ba3f6134"]]);export{W as default};
