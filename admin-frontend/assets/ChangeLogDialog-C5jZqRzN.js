var e=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,r=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t,n=(e,a,l)=>new Promise(((t,o)=>{var s=e=>{try{n(l.next(e))}catch(a){o(a)}},r=e=>{try{n(l.throw(e))}catch(a){o(a)}},n=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,r);n((l=l.apply(e,a)).next())}));import{_ as d}from"./index-rNRt1EuS.js";/* empty css                             *//* empty css                       */import{d as i,r as u,X as p,p as c,M as _,N as g,o as v,w as f,e as m,b,Z as h,_ as y,ar as w,c as C,F as j,A as k,as as V,C as z,D as x,b0 as O,H as P,u as D,a3 as U,K as Y,aB as I,a2 as S,aI as H,aJ as L,aX as M,x as B,aG as K,aH as A,Y as E,a as G,b1 as N,b2 as R}from"./vendor-CAPBtMef.js";import{S as X}from"./systemConfigApi-Cw3xPQeV.js";const $={class:"filter-section"},q={class:"table-section"},F={class:"change-content"},J={class:"old-value"},Z={class:"value old"},Q={class:"new-value"},T={class:"value new"},W={class:"pagination-wrapper"},ee={key:0,class:"detail-content"},ae={class:"value-comparison"},le={class:"comparison-content"},te={class:"old-section"},oe={class:"value-pre old"},se={class:"new-section"},re={class:"value-pre new"},ne={class:"user-agent-info"},de={class:"user-agent"},ie={class:"dialog-footer"},ue=d(i({__name:"ChangeLogDialog",props:{visible:{type:Boolean}},emits:["update:visible"],setup(e,{emit:d}){const i=e,ue=d,pe=u(!1),ce=u(!1),_e=u(!1),ge=u([]),ve=u([]),fe=u(null),me=p({page:1,page_size:20,config_group:"",config_key:"",operator_id:"",start_date:"",end_date:"",order_by:"created_at",order:"desc"}),be=p({page:1,page_size:20,total:0,total_pages:0}),he=u(!1),ye=u(null);c((()=>i.visible),(e=>{pe.value=e,e&&(we(),Ce())})),c(pe,(e=>{ue("update:visible",e)}));const we=()=>n(this,null,(function*(){try{ce.value=!0;const d=yield X.getChangeLogList((e=((e,a)=>{for(var l in a||(a={}))o.call(a,l)&&r(e,l,a[l]);if(t)for(var l of t(a))s.call(a,l)&&r(e,l,a[l]);return e})({},me),n={page:be.page,page_size:be.page_size},a(e,l(n))));d.success?(ge.value=d.data.logs,be.total=d.data.total,be.total_pages=d.data.total_pages):_.error(d.message||"获取变更日志失败")}catch(d){_.error("获取变更日志失败")}finally{ce.value=!1}var e,n})),Ce=()=>n(this,null,(function*(){try{const e=yield X.getConfigGroups();e.success&&(ve.value=e.data)}catch(e){}})),je=()=>{be.page=1,we()},ke=()=>{Object.assign(me,{page:1,page_size:20,config_group:"",config_key:"",operator_id:"",start_date:"",end_date:"",order_by:"created_at",order:"desc"}),fe.value=null,be.page=1,we()},Ve=e=>{e?(me.start_date=e[0],me.end_date=e[1]):(me.start_date="",me.end_date=""),je()},ze=e=>{be.page=e,we()},xe=e=>{be.page_size=e,be.page=1,we()},Oe=({prop:e,order:a})=>{me.order_by=e,me.order="ascending"===a?"asc":"desc",we()},Pe=()=>{try{_e.value=!0;const e=ge.value.map((e=>({"配置组":e.config_group,"配置键":e.config_key,"原值":e.old_value,"新值":e.new_value,"变更原因":e.change_reason,"操作员":e.operator_name||e.operator_id,"IP地址":e.ip_address,"变更时间":Ye(e.created_at)}))),a=[Object.keys(e[0]).join(","),...e.map((e=>Object.values(e).map((e=>`"${e}"`)).join(",")))].join("\n"),l=new Blob([a],{type:"text/csv;charset=utf-8;"}),t=document.createElement("a");t.href=URL.createObjectURL(l),t.download=`config_change_logs_${(new Date).toISOString().slice(0,10)}.csv`,t.click(),_.success("导出成功")}catch(e){_.error("导出失败")}finally{_e.value=!1}},De=()=>{pe.value=!1},Ue=e=>e?e.length>50?e.substring(0,50)+"...":e:"-",Ye=e=>e?new Date(e).toLocaleString("zh-CN"):"-";return(e,a)=>{const l=V,t=w,o=y,s=z,r=O,n=P,d=h,i=M,u=L,p=H,c=A,_=R,X=N,ue=E,we=K;return v(),g(ue,{modelValue:pe.value,"onUpdate:modelValue":a[7]||(a[7]=e=>pe.value=e),title:"配置变更日志",width:"1000px","before-close":De},{footer:f((()=>[m("div",ie,[b(n,{onClick:De},{default:f((()=>a[17]||(a[17]=[Y("关闭")]))),_:1}),b(n,{type:"primary",onClick:Pe,loading:_e.value},{default:f((()=>a[18]||(a[18]=[Y(" 导出日志 ")]))),_:1},8,["loading"])])])),default:f((()=>[m("div",$,[b(d,{model:me,inline:"",class:"search-form"},{default:f((()=>[b(o,{label:"配置组"},{default:f((()=>[b(t,{modelValue:me.config_group,"onUpdate:modelValue":a[0]||(a[0]=e=>me.config_group=e),placeholder:"选择配置组",clearable:"",style:{width:"150px"},onChange:je},{default:f((()=>[(v(!0),C(j,null,k(ve.value,(e=>(v(),g(l,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),b(o,{label:"配置键"},{default:f((()=>[b(s,{modelValue:me.config_key,"onUpdate:modelValue":a[1]||(a[1]=e=>me.config_key=e),placeholder:"输入配置键",clearable:"",style:{width:"180px"},onKeyup:x(je,["enter"]),onClear:je},null,8,["modelValue"])])),_:1}),b(o,{label:"操作员"},{default:f((()=>[b(s,{modelValue:me.operator_id,"onUpdate:modelValue":a[2]||(a[2]=e=>me.operator_id=e),placeholder:"输入操作员ID",clearable:"",style:{width:"150px"},onKeyup:x(je,["enter"]),onClear:je},null,8,["modelValue"])])),_:1}),b(o,{label:"时间范围"},{default:f((()=>[b(r,{modelValue:fe.value,"onUpdate:modelValue":a[3]||(a[3]=e=>fe.value=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"350px"},onChange:Ve},null,8,["modelValue"])])),_:1}),b(o,null,{default:f((()=>[b(n,{type:"primary",onClick:je,icon:D(U)},{default:f((()=>a[8]||(a[8]=[Y(" 搜索 ")]))),_:1},8,["icon"]),b(n,{onClick:ke,icon:D(I)},{default:f((()=>a[9]||(a[9]=[Y(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])]),m("div",q,[S((v(),g(p,{data:ge.value,onSortChange:Oe,stripe:"",style:{width:"100%"}},{default:f((()=>[b(u,{prop:"config_group",label:"配置组",width:"120"},{default:f((({row:e})=>[b(i,{type:"info",size:"small"},{default:f((()=>[Y(B(e.config_group),1)])),_:2},1024)])),_:1}),b(u,{prop:"config_key",label:"配置键",width:"180","show-overflow-tooltip":""}),b(u,{label:"变更内容","min-width":"300"},{default:f((({row:e})=>[m("div",F,[m("div",J,[a[10]||(a[10]=m("span",{class:"label"},"原值:",-1)),m("span",Z,B(Ue(e.old_value)),1)]),m("div",Q,[a[11]||(a[11]=m("span",{class:"label"},"新值:",-1)),m("span",T,B(Ue(e.new_value)),1)])])])),_:1}),b(u,{prop:"change_reason",label:"变更原因",width:"150","show-overflow-tooltip":""}),b(u,{prop:"operator_name",label:"操作员",width:"120"},{default:f((({row:e})=>[Y(B(e.operator_name||e.operator_id),1)])),_:1}),b(u,{prop:"ip_address",label:"IP地址",width:"120"}),b(u,{prop:"created_at",label:"变更时间",width:"160",sortable:"custom"},{default:f((({row:e})=>[Y(B(Ye(e.created_at)),1)])),_:1}),b(u,{label:"操作",width:"100",fixed:"right"},{default:f((({row:e})=>[b(n,{type:"primary",link:"",size:"small",onClick:a=>{return l=e,ye.value=l,void(he.value=!0);var l}},{default:f((()=>a[12]||(a[12]=[Y(" 详情 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[we,ce.value]]),m("div",W,[b(c,{"current-page":be.page,"onUpdate:currentPage":a[4]||(a[4]=e=>be.page=e),"page-size":be.page_size,"onUpdate:pageSize":a[5]||(a[5]=e=>be.page_size=e),total:be.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:xe,onCurrentChange:ze},null,8,["current-page","page-size","total"])])]),b(ue,{modelValue:he.value,"onUpdate:modelValue":a[6]||(a[6]=e=>he.value=e),title:"变更详情",width:"600px","append-to-body":""},{default:f((()=>[ye.value?(v(),C("div",ee,[b(X,{column:2,border:""},{default:f((()=>[b(_,{label:"配置组"},{default:f((()=>[Y(B(ye.value.config_group),1)])),_:1}),b(_,{label:"配置键"},{default:f((()=>[Y(B(ye.value.config_key),1)])),_:1}),b(_,{label:"操作员"},{default:f((()=>[Y(B(ye.value.operator_name||ye.value.operator_id),1)])),_:1}),b(_,{label:"IP地址"},{default:f((()=>[Y(B(ye.value.ip_address),1)])),_:1}),b(_,{label:"变更时间",span:2},{default:f((()=>[Y(B(Ye(ye.value.created_at)),1)])),_:1}),b(_,{label:"变更原因",span:2},{default:f((()=>[Y(B(ye.value.change_reason),1)])),_:1})])),_:1}),m("div",ae,[a[15]||(a[15]=m("h4",null,"值变更对比",-1)),m("div",le,[m("div",te,[a[13]||(a[13]=m("h5",null,"原值",-1)),m("pre",oe,B(ye.value.old_value),1)]),m("div",se,[a[14]||(a[14]=m("h5",null,"新值",-1)),m("pre",re,B(ye.value.new_value),1)])])]),m("div",ne,[a[16]||(a[16]=m("h4",null,"用户代理信息",-1)),m("p",de,B(ye.value.user_agent),1)])])):G("",!0)])),_:1},8,["modelValue"])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-d5ae3316"]]);export{ue as default};
