var e=Object.defineProperty,a=Object.getOwnPropertySymbols,t=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,o=(a,t,l)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):a[t]=l;import{_ as d}from"./index-rNRt1EuS.js";/* empty css                 *//* empty css                       *//* empty css                 */import{S as s}from"./systemConfigApi-Cw3xPQeV.js";import{d as n,r,X as i,j as u,p,N as c,o as _,w as m,e as v,c as f,a as h,b as g,x as b,aI as w,aJ as y,Z as V,_ as k,aZ as x,a_ as j,K as U,C,F as O,aq as B,a$ as P,H as S,Y as I,M as z,ai as E}from"./vendor-CAPBtMef.js";const T={class:"batch-update-content"},Z={class:"selected-configs"},$={class:"batch-operations"},q={key:0,class:"preview-section"},A={class:"change-preview"},D={class:"old-value"},F={class:"value old"},H={class:"new-value"},J={class:"value new"},K={class:"dialog-footer"},M=d(n({__name:"BatchUpdateDialog",props:{visible:{type:Boolean},configs:{}},emits:["update:visible","success"],setup(e,{emit:d}){const n=e,M=d,N=r(!1),X=r(!1),Y=i({operation_type:"update_value",new_value:"",new_status:!0,order_method:"increment",start_order:0,description_method:"replace",description_content:"",change_reason:""}),G=r([]),L=u((()=>{switch(Y.operation_type){case"update_value":return!!Y.new_value;case"update_status":return void 0!==Y.new_status;case"update_order":return!0;case"update_description":return!!Y.description_content;default:return!1}})),Q=u((()=>L.value&&!!Y.change_reason&&G.value.length>0));p((()=>n.visible),(e=>{N.value=e,e&&R()})),p(N,(e=>{M("update:visible",e)}));const R=()=>{Object.assign(Y,{operation_type:"update_value",new_value:"",new_status:!0,order_method:"increment",start_order:0,description_method:"replace",description_content:"",change_reason:""}),G.value=[]},W=()=>{G.value=[]},ee=()=>{const e=[];n.configs.forEach(((a,t)=>{let l="",o="";switch(Y.operation_type){case"update_value":o=a.config_value,l=Y.new_value;break;case"update_status":o=a.is_active?"启用":"禁用",l=Y.new_status?"启用":"禁用";break;case"update_order":o=String(a.display_order),l="increment"===Y.order_method?String(t+1):String(Y.start_order+t);break;case"update_description":o=a.description,"replace"===Y.description_method?l=Y.description_content:"append"===Y.description_method?l=a.description+" "+Y.description_content:"prepend"===Y.description_method&&(l=Y.description_content+" "+a.description)}e.push({config_key:a.config_key,old_value:o,new_value:l})})),G.value=e,z.success("预览生成成功")},ae=()=>{return e=this,d=null,r=function*(){try{yield E.confirm(`确认批量更新 ${n.configs.length} 个配置项吗？此操作不可撤销。`,"确认批量更新",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"}),X.value=!0;const e={configs:n.configs.map(((e,d)=>{const s=((e,d)=>{for(var s in d||(d={}))t.call(d,s)&&o(e,s,d[s]);if(a)for(var s of a(d))l.call(d,s)&&o(e,s,d[s]);return e})({},e);switch(Y.operation_type){case"update_value":s.config_value=Y.new_value;break;case"update_status":s.is_active=Y.new_status;break;case"update_order":"increment"===Y.order_method?s.display_order=d+1:s.display_order=Y.start_order+d;break;case"update_description":"replace"===Y.description_method?s.description=Y.description_content:"append"===Y.description_method?s.description=e.description+" "+Y.description_content:"prepend"===Y.description_method&&(s.description=Y.description_content+" "+e.description)}return s}))},d=yield s.batchUpdateConfigs(e);d.success?(z.success("批量更新成功"),M("success"),te()):z.error(d.message||"批量更新失败")}catch(e){"cancel"!==e&&z.error("批量更新失败")}finally{X.value=!1}},new Promise(((a,t)=>{var l=e=>{try{s(r.next(e))}catch(a){t(a)}},o=e=>{try{s(r.throw(e))}catch(a){t(a)}},s=e=>e.done?a(e.value):Promise.resolve(e.value).then(l,o);s((r=r.apply(e,d)).next())}));var e,d,r},te=()=>{N.value=!1};return(e,a)=>{const t=y,l=w,o=j,d=x,s=k,n=C,r=B,i=V,u=P,p=S,z=I;return _(),c(z,{modelValue:N.value,"onUpdate:modelValue":a[8]||(a[8]=e=>N.value=e),title:"批量更新配置",width:"800px","before-close":te},{footer:m((()=>[v("div",K,[g(p,{onClick:te},{default:m((()=>a[29]||(a[29]=[U("取消")]))),_:1}),g(p,{onClick:ee,disabled:!L.value},{default:m((()=>a[30]||(a[30]=[U(" 预览变更 ")]))),_:1},8,["disabled"]),g(p,{type:"primary",onClick:ae,loading:X.value,disabled:!Q.value},{default:m((()=>a[31]||(a[31]=[U(" 确认更新 ")]))),_:1},8,["loading","disabled"])])])),default:m((()=>[v("div",T,[v("div",Z,[v("h4",null,"选中的配置项 ("+b(e.configs.length)+")",1),g(l,{data:e.configs,size:"small","max-height":"200"},{default:m((()=>[g(t,{prop:"config_group",label:"配置组",width:"120"}),g(t,{prop:"config_key",label:"配置键",width:"180"}),g(t,{prop:"config_value",label:"当前值","min-width":"150","show-overflow-tooltip":""}),g(t,{prop:"config_type",label:"类型",width:"80"})])),_:1},8,["data"])]),v("div",$,[a[22]||(a[22]=v("h4",null,"批量操作",-1)),g(i,{model:Y,"label-width":"120px"},{default:m((()=>[g(s,{label:"操作类型"},{default:m((()=>[g(d,{modelValue:Y.operation_type,"onUpdate:modelValue":a[0]||(a[0]=e=>Y.operation_type=e),onChange:W},{default:m((()=>[g(o,{value:"update_value"},{default:m((()=>a[9]||(a[9]=[U("批量更新值")]))),_:1}),g(o,{value:"update_status"},{default:m((()=>a[10]||(a[10]=[U("批量更新状态")]))),_:1}),g(o,{value:"update_order"},{default:m((()=>a[11]||(a[11]=[U("批量更新排序")]))),_:1}),g(o,{value:"update_description"},{default:m((()=>a[12]||(a[12]=[U("批量更新描述")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),"update_value"===Y.operation_type?(_(),c(s,{key:0,label:"新值"},{default:m((()=>[g(n,{modelValue:Y.new_value,"onUpdate:modelValue":a[1]||(a[1]=e=>Y.new_value=e),placeholder:"请输入新的配置值",clearable:""},null,8,["modelValue"]),a[13]||(a[13]=v("div",{class:"form-tip"},"注意：不同类型的配置项请确保值的格式正确",-1))])),_:1})):h("",!0),"update_status"===Y.operation_type?(_(),c(s,{key:1,label:"新状态"},{default:m((()=>[g(d,{modelValue:Y.new_status,"onUpdate:modelValue":a[2]||(a[2]=e=>Y.new_status=e)},{default:m((()=>[g(o,{value:!0},{default:m((()=>a[14]||(a[14]=[U("启用")]))),_:1}),g(o,{value:!1},{default:m((()=>a[15]||(a[15]=[U("禁用")]))),_:1})])),_:1},8,["modelValue"])])),_:1})):h("",!0),"update_order"===Y.operation_type?(_(),f(O,{key:2},[g(s,{label:"排序方式"},{default:m((()=>[g(d,{modelValue:Y.order_method,"onUpdate:modelValue":a[3]||(a[3]=e=>Y.order_method=e)},{default:m((()=>[g(o,{value:"increment"},{default:m((()=>a[16]||(a[16]=[U("递增排序")]))),_:1}),g(o,{value:"custom"},{default:m((()=>a[17]||(a[17]=[U("自定义起始值")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),"custom"===Y.order_method?(_(),c(s,{key:0,label:"起始值"},{default:m((()=>[g(r,{modelValue:Y.start_order,"onUpdate:modelValue":a[4]||(a[4]=e=>Y.start_order=e),min:0,max:9999,style:{width:"200px"}},null,8,["modelValue"]),a[18]||(a[18]=v("div",{class:"form-tip"},"从此值开始，每个配置项递增1",-1))])),_:1})):h("",!0)],64)):h("",!0),"update_description"===Y.operation_type?(_(),f(O,{key:3},[g(s,{label:"操作方式"},{default:m((()=>[g(d,{modelValue:Y.description_method,"onUpdate:modelValue":a[5]||(a[5]=e=>Y.description_method=e)},{default:m((()=>[g(o,{value:"replace"},{default:m((()=>a[19]||(a[19]=[U("替换描述")]))),_:1}),g(o,{value:"append"},{default:m((()=>a[20]||(a[20]=[U("追加描述")]))),_:1}),g(o,{value:"prepend"},{default:m((()=>a[21]||(a[21]=[U("前置描述")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),g(s,{label:"描述内容"},{default:m((()=>[g(n,{modelValue:Y.description_content,"onUpdate:modelValue":a[6]||(a[6]=e=>Y.description_content=e),type:"textarea",rows:3,placeholder:"请输入描述内容",maxlength:"500","show-word-limit":""},null,8,["modelValue"])])),_:1})],64)):h("",!0),g(s,{label:"变更原因"},{default:m((()=>[g(n,{modelValue:Y.change_reason,"onUpdate:modelValue":a[7]||(a[7]=e=>Y.change_reason=e),placeholder:"请输入本次批量变更的原因",clearable:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])]),G.value.length>0?(_(),f("div",q,[a[25]||(a[25]=v("h4",null,"变更预览",-1)),g(l,{data:G.value,size:"small","max-height":"250"},{default:m((()=>[g(t,{prop:"config_key",label:"配置键",width:"180"}),g(t,{label:"变更内容","min-width":"300"},{default:m((({row:e})=>[v("div",A,[v("div",D,[a[23]||(a[23]=v("span",{class:"label"},"原值:",-1)),v("span",F,b(e.old_value),1)]),v("div",H,[a[24]||(a[24]=v("span",{class:"label"},"新值:",-1)),v("span",J,b(e.new_value),1)])])])),_:1})])),_:1},8,["data"])])):h("",!0),g(u,{title:"批量更新提醒",type:"warning",closable:!1,"show-icon":""},{default:m((()=>[v("div",null,[v("p",null,"• 批量更新将同时修改 "+b(e.configs.length)+" 个配置项",1),a[26]||(a[26]=v("p",null,"• 请仔细检查变更内容，确保操作正确",-1)),a[27]||(a[27]=v("p",null,"• 建议在操作前创建配置备份",-1)),a[28]||(a[28]=v("p",null,"• 操作完成后系统缓存将自动刷新",-1))])])),_:1})])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-4e2e7eab"]]);export{M as default};
