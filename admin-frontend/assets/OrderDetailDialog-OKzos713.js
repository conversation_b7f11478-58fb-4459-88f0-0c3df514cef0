import{_ as e}from"./index-rNRt1EuS.js";/* empty css                *//* empty css               *//* empty css                             */import{d as a,r as l,j as t,p as u,N as d,o as s,w as r,a2 as i,c as n,a as o,e as _,b as v,aT as f,aN as c,b1 as p,b2 as b,K as m,x as y,aX as g,v as h,aI as w,aJ as k,bC as x,F as j,A as I,bD as D,aV as F,y as z,u as C,bE as N,aK as O,aG as V,H as A,aQ as J,Y as P,M as S}from"./vendor-CAPBtMef.js";import{a as B,O as K,B as M}from"./orderApi-DbkZO4__.js";const E={class:"order-detail-dialog"},G={key:0,class:"order-detail-content"},H={class:"detail-section"},L={class:"detail-section"},Q={class:"detail-section"},T={class:"price-amount"},U={class:"price-amount"},X={class:"price-amount"},Y={class:"fee-amount overweight"},q={class:"fee-amount underweight"},R={class:"detail-section"},W={class:"address-info"},Z={class:"address-info"},$={class:"address-info"},ee={class:"detail-section"},ae={key:1},le={key:1},te={class:"detail-section"},ue={class:"tracking-event"},de={class:"event-header"},se={class:"event-source"},re={class:"event-content"},ie={class:"event-description"},ne={key:0,class:"event-location"},oe={key:0,class:"empty-tracking"},_e={class:"detail-section"},ve={class:"dialog-footer"},fe=e(a({__name:"OrderDetailDialog",props:{visible:{type:Boolean},orderId:{}},emits:["update:visible"],setup(e,{emit:a}){const fe=e,ce=a,pe=l(!1),be=l(null),me=t({get:()=>fe.visible,set:e=>ce("update:visible",e)}),ye=()=>{return e=this,a=null,l=function*(){if(fe.orderId)try{pe.value=!0,be.value=yield B.getAdminOrderDetail(fe.orderId)}catch(e){S.error("获取订单详情失败")}finally{pe.value=!1}},new Promise(((t,u)=>{var d=e=>{try{r(l.next(e))}catch(a){u(a)}},s=e=>{try{r(l.throw(e))}catch(a){u(a)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(d,s);r((l=l.apply(e,a)).next())}));var e,a,l},ge=()=>{ye()},he=()=>{be.value=null,me.value=!1},we=e=>({submitted:"primary",submit_failed:"danger",print_failed:"danger",assigned:"info",awaiting_pickup:"warning",picked_up:"primary",pickup_failed:"danger",in_transit:"info",out_for_delivery:"info",delivered:"success",delivered_abnormal:"warning",billed:"success",exception:"danger",returned:"warning",forwarded:"info",cancelled:"danger",voided:"danger",weight_updated:"info",revived:"primary"}[e]||"info"),ke=e=>{const a=K.find((a=>a.value===e));return(null==a?void 0:a.label)||e},xe=e=>{const a=M.find((a=>a.value===e));return(null==a?void 0:a.label)||e},je=e=>{if(!e)return"暂无信息";try{const a=JSON.parse(e);return JSON.stringify(a,null,2)}catch(a){return e}},Ie=e=>e?new Date(e).toLocaleString("zh-CN"):"-",De=e=>{if(!e)return 0;return(e.actual_fee||0)-(e.price||0)},Fe=e=>{if(!e)return!1;const a=e.actual_fee||0,l=e.price||0;return a>0&&a!==l};return u((()=>fe.visible),(e=>{e&&fe.orderId&&ye()})),(e,a)=>{const l=b,t=g,u=p,S=c,B=f,K=k,M=w,fe=z,ce=F,ye=D,ze=x,Ce=O,Ne=A,Oe=P,Ve=V;return s(),d(Oe,{modelValue:me.value,"onUpdate:modelValue":a[0]||(a[0]=e=>me.value=e),title:"订单详情",width:"1200px","close-on-click-modal":!1,onClose:he},{footer:r((()=>[_("div",ve,[v(Ne,{onClick:he},{default:r((()=>a[11]||(a[11]=[m("关闭")]))),_:1}),v(Ne,{type:"primary",onClick:ge,loading:pe.value},{default:r((()=>[v(fe,null,{default:r((()=>[v(C(J))])),_:1}),a[12]||(a[12]=m(" 刷新 "))])),_:1},8,["loading"])])])),default:r((()=>[i((s(),n("div",E,[be.value?(s(),n("div",G,[_("div",H,[a[1]||(a[1]=_("h3",null,"基础信息",-1)),v(B,{gutter:20},{default:r((()=>[v(S,{span:12},{default:r((()=>[v(u,{column:1,border:""},{default:r((()=>[v(l,{label:"订单ID"},{default:r((()=>[m(y(be.value.id),1)])),_:1}),v(l,{label:"订单号"},{default:r((()=>[m(y(be.value.order_no),1)])),_:1}),v(l,{label:"客户订单号"},{default:r((()=>[m(y(be.value.customer_order_no),1)])),_:1}),v(l,{label:"运单号"},{default:r((()=>[m(y(be.value.tracking_no||"-"),1)])),_:1}),v(l,{label:"订单状态"},{default:r((()=>[v(t,{type:we(be.value.status)},{default:r((()=>[m(y(ke(be.value.status)),1)])),_:1},8,["type"])])),_:1})])),_:1})])),_:1}),v(S,{span:12},{default:r((()=>[v(u,{column:1,border:""},{default:r((()=>[v(l,{label:"快递公司"},{default:r((()=>{var e;return[m(y((null==(e=be.value.express_company)?void 0:e.name)||be.value.express_type),1)]})),_:1}),v(l,{label:"产品类型"},{default:r((()=>[m(y(be.value.product_type),1)])),_:1}),v(l,{label:"供应商"},{default:r((()=>[m(y(be.value.provider),1)])),_:1}),v(l,{label:"创建时间"},{default:r((()=>[m(y(Ie(be.value.created_at)),1)])),_:1}),v(l,{label:"更新时间"},{default:r((()=>[m(y(Ie(be.value.updated_at)),1)])),_:1})])),_:1})])),_:1})])),_:1})]),_("div",L,[a[2]||(a[2]=_("h3",null,"用户信息",-1)),v(u,{column:2,border:""},{default:r((()=>[v(l,{label:"用户ID"},{default:r((()=>[m(y(be.value.user.id),1)])),_:1}),v(l,{label:"用户名"},{default:r((()=>[m(y(be.value.user.username),1)])),_:1}),v(l,{label:"邮箱"},{default:r((()=>[m(y(be.value.user.email),1)])),_:1}),v(l,{label:"账户状态"},{default:r((()=>[v(t,{type:be.value.user.is_active?"success":"danger"},{default:r((()=>[m(y(be.value.user.is_active?"活跃":"禁用"),1)])),_:1},8,["type"])])),_:1}),v(l,{label:"注册时间"},{default:r((()=>[m(y(Ie(be.value.user.created_at)),1)])),_:1}),v(l,{label:"默认角色"},{default:r((()=>[m(y(be.value.user.default_role),1)])),_:1})])),_:1})]),_("div",Q,[a[3]||(a[3]=_("h3",null,"费用信息",-1)),v(B,{gutter:20},{default:r((()=>[v(S,{span:12},{default:r((()=>[v(u,{column:1,border:""},{default:r((()=>[v(l,{label:"订单价格"},{default:r((()=>{var e;return[_("span",T,"¥"+y((null==(e=be.value.price)?void 0:e.toFixed(2))||"0.00"),1)]})),_:1}),v(l,{label:"实际费用"},{default:r((()=>{var e;return[_("span",U,"¥"+y((null==(e=be.value.actual_fee)?void 0:e.toFixed(2))||"0.00"),1)]})),_:1}),v(l,{label:"保价费"},{default:r((()=>{var e;return[_("span",X,"¥"+y((null==(e=be.value.insurance_fee)?void 0:e.toFixed(2))||"0.00"),1)]})),_:1}),be.value.overweight_fee>0?(s(),d(l,{key:0,label:"超重费用"},{default:r((()=>{var e;return[_("span",Y,"¥"+y((null==(e=be.value.overweight_fee)?void 0:e.toFixed(2))||"0.00"),1)]})),_:1})):o("",!0),be.value.underweight_fee>0?(s(),d(l,{key:1,label:"超轻费用"},{default:r((()=>{var e;return[_("span",q,"¥"+y((null==(e=be.value.underweight_fee)?void 0:e.toFixed(2))||"0.00"),1)]})),_:1})):o("",!0),Fe(be.value)?(s(),d(l,{key:2,label:"费用差额"},{default:r((()=>[_("span",{class:h(["fee-difference",De(be.value)>0?"positive":"negative"])},y(De(be.value)>0?"+":"")+"¥"+y(Math.abs(De(be.value)).toFixed(2)),3)])),_:1})):o("",!0),v(l,{label:"计费状态"},{default:r((()=>{return[v(t,{type:(e=be.value.billing_status,{pending:"warning",calculated:"primary",paid:"success",refunded:"info"}[e]||"info")},{default:r((()=>[m(y(xe(be.value.billing_status)),1)])),_:1},8,["type"])];var e})),_:1})])),_:1})])),_:1}),v(S,{span:12},{default:r((()=>[v(u,{column:1,border:""},{default:r((()=>[v(l,{label:"下单重量"},{default:r((()=>[m(y(be.value.weight)+" kg ",1)])),_:1}),v(l,{label:"实际重量"},{default:r((()=>[m(y(be.value.actual_weight||0)+" kg ",1)])),_:1}),v(l,{label:"计费重量"},{default:r((()=>[m(y(be.value.charged_weight||0)+" kg ",1)])),_:1}),v(l,{label:"下单体积"},{default:r((()=>[m(y(be.value.order_volume||0)+" m³ ",1)])),_:1}),v(l,{label:"实际体积"},{default:r((()=>[m(y(be.value.actual_volume||0)+" m³ ",1)])),_:1}),be.value.weight_adjustment_reason?(s(),d(l,{key:0,label:"重量调整原因"},{default:r((()=>[v(t,{type:"info",size:"small"},{default:r((()=>[m(y(be.value.weight_adjustment_reason),1)])),_:1})])),_:1})):o("",!0)])),_:1})])),_:1})])),_:1})]),_("div",R,[a[7]||(a[7]=_("h3",null,"地址信息",-1)),v(B,{gutter:20},{default:r((()=>[v(S,{span:12},{default:r((()=>[a[4]||(a[4]=_("h4",null,"寄件人信息",-1)),_("div",W,[_("pre",null,y(je(be.value.sender_info)),1)])])),_:1}),v(S,{span:12},{default:r((()=>[a[5]||(a[5]=_("h4",null,"收件人信息",-1)),_("div",Z,[_("pre",null,y(je(be.value.receiver_info)),1)])])),_:1})])),_:1}),v(B,null,{default:r((()=>[v(S,{span:24},{default:r((()=>[a[6]||(a[6]=_("h4",null,"包裹信息",-1)),_("div",$,[_("pre",null,y(je(be.value.package_info)),1)])])),_:1})])),_:1})]),_("div",ee,[a[8]||(a[8]=_("h3",null,"操作历史",-1)),v(M,{data:be.value.operation_history,border:"",stripe:"",style:{width:"100%"},"empty-text":"暂无操作记录"},{default:r((()=>[v(K,{prop:"created_at",label:"操作时间",width:"160"},{default:r((({row:e})=>[m(y(Ie(e.created_at)),1)])),_:1}),v(K,{prop:"operator",label:"操作人",width:"120"}),v(K,{prop:"operation",label:"操作类型",width:"120"}),v(K,{prop:"old_status",label:"原状态",width:"100"},{default:r((({row:e})=>[e.old_status?(s(),d(t,{key:0,size:"small",type:we(e.old_status)},{default:r((()=>[m(y(ke(e.old_status)),1)])),_:2},1032,["type"])):(s(),n("span",ae,"-"))])),_:1}),v(K,{prop:"new_status",label:"新状态",width:"100"},{default:r((({row:e})=>[e.new_status?(s(),d(t,{key:0,size:"small",type:we(e.new_status)},{default:r((()=>[m(y(ke(e.new_status)),1)])),_:2},1032,["type"])):(s(),n("span",le,"-"))])),_:1}),v(K,{prop:"reason",label:"操作原因","show-overflow-tooltip":""}),v(K,{prop:"ip_address",label:"IP地址",width:"120"})])),_:1},8,["data"])]),_("div",te,[a[9]||(a[9]=_("h3",null,"物流轨迹",-1)),v(ze,null,{default:r((()=>[(s(!0),n(j,null,I(be.value.tracking_history,(e=>(s(),d(ye,{key:e.id,timestamp:Ie(e.timestamp),placement:"top"},{default:r((()=>[v(ce,null,{default:r((()=>[_("div",ue,[_("div",de,[v(t,{type:we(e.status),size:"small"},{default:r((()=>[m(y(e.status),1)])),_:2},1032,["type"]),_("span",se,y(e.source),1)]),_("div",re,[_("p",ie,y(e.description),1),e.location?(s(),n("p",ne,[v(fe,null,{default:r((()=>[v(C(N))])),_:1}),m(" "+y(e.location),1)])):o("",!0)])])])),_:2},1024)])),_:2},1032,["timestamp"])))),128))])),_:1}),be.value.tracking_history&&0!==be.value.tracking_history.length?o("",!0):(s(),n("div",oe,[v(Ce,{description:"暂无物流轨迹信息"})]))]),_("div",_e,[a[10]||(a[10]=_("h3",null,"审计信息",-1)),v(u,{column:2,border:""},{default:r((()=>[v(l,{label:"创建人"},{default:r((()=>{var e;return[m(y((null==(e=be.value.audit_info)?void 0:e.created_by)||"-"),1)]})),_:1}),v(l,{label:"最后修改人"},{default:r((()=>{var e;return[m(y((null==(e=be.value.audit_info)?void 0:e.updated_by)||"-"),1)]})),_:1}),v(l,{label:"最后修改时间"},{default:r((()=>{var e;return[m(y((null==(e=be.value.audit_info)?void 0:e.last_modified)?Ie(be.value.audit_info.last_modified):"-"),1)]})),_:1}),v(l,{label:"修改次数"},{default:r((()=>{var e;return[m(y((null==(e=be.value.audit_info)?void 0:e.modification_count)||0),1)]})),_:1})])),_:1})])])):o("",!0)])),[[Ve,pe.value]])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-cf23080a"]]);export{fe as default};
