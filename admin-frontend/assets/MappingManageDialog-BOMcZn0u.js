var e=(e,l,i)=>new Promise(((a,t)=>{var s=e=>{try{o(i.next(e))}catch(l){t(l)}},d=e=>{try{o(i.throw(e))}catch(l){t(l)}},o=e=>e.done?a(e.value):Promise.resolve(e.value).then(s,d);o((i=i.apply(e,l)).next())}));import{_ as l}from"./index-rNRt1EuS.js";import{d as i,r as a,X as t,j as s,p as d,f as o,M as r,N as _,o as p,w as u,e as c,a2 as m,b as n,H as v,u as h,aR as g,K as y,aQ as f,aI as w,aJ as b,x as z,aX as V,c as x,a as k,F as C,A as U,aG as M,Y as j,Z as B,_ as q,ar as A,as as P,C as F,ap as I,aq as O,ai as R}from"./vendor-CAPBtMef.js";import{E as T}from"./expressCompanyApi-DSXUWAhW.js";const X={class:"action-bar"},$={key:1,class:"text-gray-400"},D={key:0},E={key:1,class:"text-gray-400"},G={key:0,class:"text-gray-400"},H={class:"dialog-footer"},J={class:"dialog-footer"},K=l(i({__name:"MappingManageDialog",props:{visible:{type:Boolean},company:{default:null}},emits:["update:visible","success"],setup(l,{emit:i}){const K=l,N=i,Q=a(!1),Y=a([]),Z=a([]),L=a(!1),S=a(),W=a(!1),ee=a(null),le=t({company_id:"",provider_id:"",provider_company_code:"",is_supported:!0,is_preferred:!1,weight_limit_kg:void 0,size_limit_cm_length:void 0,size_limit_cm_width:void 0,size_limit_cm_height:void 0,supported_services:[]}),ie={provider_id:[{required:!0,message:"请选择供应商",trigger:"change"}],provider_company_code:[{required:!0,message:"请输入供应商快递代码",trigger:"blur"},{max:50,message:"代码长度不能超过50个字符",trigger:"blur"}]},ae=s({get:()=>K.visible,set:e=>N("update:visible",e)}),te=s((()=>{var e;return!!(null==(e=ee.value)?void 0:e.id)}));d((()=>K.visible),(e=>{e&&K.company&&(se(),de())})),o((()=>{de()}));const se=()=>e(this,null,(function*(){var e;if(null==(e=K.company)?void 0:e.id)try{Q.value=!0;const e=yield T.getMappingsByCompany(K.company.id);e.success?Y.value=e.data:r.error(e.message||"获取映射关系失败")}catch(l){r.error("获取映射关系失败")}finally{Q.value=!1}})),de=()=>e(this,null,(function*(){try{const e=yield T.getActiveProviders();e.success&&(Z.value=e.data)}catch(e){}})),oe=()=>{ae.value=!1},re=()=>{ee.value=null,pe(),L.value=!0},_e=l=>e(this,null,(function*(){try{yield R.confirm(`确认删除与供应商 "${l.provider_name}" 的映射关系吗？`,"确认删除",{type:"warning",confirmButtonText:"确认",cancelButtonText:"取消"});const e=yield T.deleteMapping(l.id);e.success?(r.success("删除成功"),se(),N("success")):r.error(e.message||"删除失败")}catch(e){"cancel"!==e&&r.error("删除失败")}})),pe=()=>{var e,l;Object.assign(le,{company_id:(null==(e=K.company)?void 0:e.id)||"",provider_id:"",provider_company_code:"",is_supported:!0,is_preferred:!1,weight_limit_kg:void 0,size_limit_cm_length:void 0,size_limit_cm_width:void 0,size_limit_cm_height:void 0,supported_services:[]}),null==(l=S.value)||l.clearValidate()},ue=e=>{const l=Z.value.find((l=>l.id===e));return l?l.name:e},ce=()=>e(this,null,(function*(){if(S.value)try{if(!(yield S.value.validate()))return;if(W.value=!0,te.value){const e={provider_company_code:le.provider_company_code,is_supported:le.is_supported,is_preferred:le.is_preferred,weight_limit_kg:le.weight_limit_kg,size_limit_cm_length:le.size_limit_cm_length,size_limit_cm_width:le.size_limit_cm_width,size_limit_cm_height:le.size_limit_cm_height,supported_services:le.supported_services},l=yield T.updateMapping(ee.value.id,e);l.success?(r.success("映射关系更新成功"),L.value=!1,se(),N("success")):r.error(l.message||"更新失败")}else{const e=yield T.createMapping(le);e.success?(r.success("映射关系创建成功"),L.value=!1,se(),N("success")):r.error(e.message||"创建失败")}}catch(e){r.error("操作失败")}finally{W.value=!1}}));return(e,l)=>{var i;const a=v,t=b,s=V,d=w,o=P,r=A,R=q,T=F,K=I,N=O,de=B,pe=j,me=M;return p(),_(pe,{modelValue:ae.value,"onUpdate:modelValue":l[11]||(l[11]=e=>ae.value=e),title:`${null==(i=e.company)?void 0:i.name} - 供应商映射管理`,width:"1000px","close-on-click-modal":!1,onClose:oe},{footer:u((()=>[c("div",J,[n(a,{onClick:oe},{default:u((()=>l[20]||(l[20]=[y("关闭")]))),_:1})])])),default:u((()=>[c("div",X,[n(a,{type:"primary",icon:h(g),onClick:re},{default:u((()=>l[12]||(l[12]=[y(" 新增映射 ")]))),_:1},8,["icon"]),n(a,{icon:h(f),onClick:se},{default:u((()=>l[13]||(l[13]=[y(" 刷新 ")]))),_:1},8,["icon"])]),m((p(),_(d,{data:Y.value,stripe:"",border:"",style:{width:"100%"}},{default:u((()=>[n(t,{label:"供应商",width:"120"},{default:u((({row:e})=>[y(z(ue(e.provider_id)),1)])),_:1}),n(t,{prop:"provider_company_code",label:"供应商快递代码",width:"150"}),n(t,{label:"是否支持",width:"100"},{default:u((({row:e})=>[n(s,{type:e.is_supported?"success":"danger"},{default:u((()=>[y(z(e.is_supported?"支持":"不支持"),1)])),_:2},1032,["type"])])),_:1}),n(t,{label:"是否首选",width:"100"},{default:u((({row:e})=>[e.is_preferred?(p(),_(s,{key:0,type:"warning"},{default:u((()=>l[14]||(l[14]=[y("首选")]))),_:1})):(p(),x("span",$,"-"))])),_:1}),n(t,{prop:"weight_limit_kg",label:"重量限制(kg)",width:"120"},{default:u((({row:e})=>[y(z(e.weight_limit_kg||"-"),1)])),_:1}),n(t,{label:"尺寸限制(cm)",width:"150"},{default:u((({row:e})=>[e.size_limit_cm_length?(p(),x("span",D,z(e.size_limit_cm_length)+"×"+z(e.size_limit_cm_width)+"×"+z(e.size_limit_cm_height),1)):(p(),x("span",E,"-"))])),_:1}),n(t,{label:"支持服务","min-width":"150"},{default:u((({row:e})=>{var l;return[(p(!0),x(C,null,U(e.supported_services,(e=>(p(),_(s,{key:e,size:"small",style:{"margin-right":"4px","margin-bottom":"4px"}},{default:u((()=>[y(z(e),1)])),_:2},1024)))),128)),(null==(l=e.supported_services)?void 0:l.length)?k("",!0):(p(),x("span",G,"-"))]})),_:1}),n(t,{label:"操作",width:"150",fixed:"right"},{default:u((({row:e})=>[n(a,{type:"primary",size:"small",onClick:l=>{return i=e,ee.value=i,Object.assign(le,{company_id:i.company_id,provider_id:i.provider_id,provider_company_code:i.provider_company_code,is_supported:i.is_supported,is_preferred:i.is_preferred,weight_limit_kg:i.weight_limit_kg,size_limit_cm_length:i.size_limit_cm_length,size_limit_cm_width:i.size_limit_cm_width,size_limit_cm_height:i.size_limit_cm_height,supported_services:i.supported_services||[]}),void(L.value=!0);var i}},{default:u((()=>l[15]||(l[15]=[y(" 编辑 ")]))),_:2},1032,["onClick"]),n(a,{type:"danger",size:"small",onClick:l=>_e(e)},{default:u((()=>l[16]||(l[16]=[y(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[me,Q.value]]),n(pe,{modelValue:L.value,"onUpdate:modelValue":l[10]||(l[10]=e=>L.value=e),title:te.value?"编辑映射关系":"新增映射关系",width:"600px","close-on-click-modal":!1,"append-to-body":""},{footer:u((()=>[c("div",H,[n(a,{onClick:l[9]||(l[9]=e=>L.value=!1)},{default:u((()=>l[19]||(l[19]=[y("取消")]))),_:1}),n(a,{type:"primary",loading:W.value,onClick:ce},{default:u((()=>[y(z(te.value?"更新":"创建"),1)])),_:1},8,["loading"])])])),default:u((()=>[n(de,{ref_key:"mappingFormRef",ref:S,model:le,rules:ie,"label-width":"140px"},{default:u((()=>[n(R,{label:"供应商",prop:"provider_id"},{default:u((()=>[n(r,{modelValue:le.provider_id,"onUpdate:modelValue":l[0]||(l[0]=e=>le.provider_id=e),placeholder:"请选择供应商",style:{width:"100%"},disabled:te.value},{default:u((()=>[(p(!0),x(C,null,U(Z.value,(e=>(p(),_(o,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1}),n(R,{label:"供应商快递代码",prop:"provider_company_code"},{default:u((()=>[n(T,{modelValue:le.provider_company_code,"onUpdate:modelValue":l[1]||(l[1]=e=>le.provider_company_code=e),placeholder:"请输入供应商的快递公司代码",maxlength:"50"},null,8,["modelValue"]),l[17]||(l[17]=c("div",{class:"form-tip"},"供应商系统中对应的快递公司代码",-1))])),_:1}),n(R,{label:"是否支持",prop:"is_supported"},{default:u((()=>[n(K,{modelValue:le.is_supported,"onUpdate:modelValue":l[2]||(l[2]=e=>le.is_supported=e),"active-text":"支持","inactive-text":"不支持"},null,8,["modelValue"])])),_:1}),n(R,{label:"是否首选",prop:"is_preferred"},{default:u((()=>[n(K,{modelValue:le.is_preferred,"onUpdate:modelValue":l[3]||(l[3]=e=>le.is_preferred=e),"active-text":"首选","inactive-text":"普通"},null,8,["modelValue"]),l[18]||(l[18]=c("div",{class:"form-tip"},"首选供应商会优先使用",-1))])),_:1}),n(R,{label:"重量限制(kg)",prop:"weight_limit_kg"},{default:u((()=>[n(N,{modelValue:le.weight_limit_kg,"onUpdate:modelValue":l[4]||(l[4]=e=>le.weight_limit_kg=e),min:0,max:1e3,precision:2,placeholder:"重量限制",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),n(R,{label:"长度限制(cm)",prop:"size_limit_cm_length"},{default:u((()=>[n(N,{modelValue:le.size_limit_cm_length,"onUpdate:modelValue":l[5]||(l[5]=e=>le.size_limit_cm_length=e),min:0,max:1e3,placeholder:"长度",style:{width:"150px"}},null,8,["modelValue"])])),_:1}),n(R,{label:"宽度限制(cm)",prop:"size_limit_cm_width"},{default:u((()=>[n(N,{modelValue:le.size_limit_cm_width,"onUpdate:modelValue":l[6]||(l[6]=e=>le.size_limit_cm_width=e),min:0,max:1e3,placeholder:"宽度",style:{width:"150px"}},null,8,["modelValue"])])),_:1}),n(R,{label:"高度限制(cm)",prop:"size_limit_cm_height"},{default:u((()=>[n(N,{modelValue:le.size_limit_cm_height,"onUpdate:modelValue":l[7]||(l[7]=e=>le.size_limit_cm_height=e),min:0,max:1e3,placeholder:"高度",style:{width:"150px"}},null,8,["modelValue"])])),_:1}),n(R,{label:"支持服务",prop:"supported_services"},{default:u((()=>[n(r,{modelValue:le.supported_services,"onUpdate:modelValue":l[8]||(l[8]=e=>le.supported_services=e),multiple:"",placeholder:"请选择支持的服务",style:{width:"100%"}},{default:u((()=>[n(o,{label:"标准快递",value:"standard"}),n(o,{label:"特快专递",value:"express"}),n(o,{label:"次日达",value:"next_day"}),n(o,{label:"当日达",value:"same_day"}),n(o,{label:"代收货款",value:"cod"}),n(o,{label:"签收回单",value:"receipt"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-57d2c48b"]]);export{K as default};
