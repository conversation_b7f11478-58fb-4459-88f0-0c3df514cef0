var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,t=(l,a,o)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):l[a]=o;import{_ as n}from"./index-rNRt1EuS.js";/* empty css                 *//* empty css                       *//* empty css                 */import{d as u,r as s,X as d,j as c,p as g,N as f,o as p,w as _,b as m,Z as v,G as y,a as b,_ as h,ar as V,c as w,F as k,A as O,as as j,e as x,C as S,u as U,aq as N,aZ as C,a_ as J,K as E,H as q,a$ as P,x as $,Y as z,M as G}from"./vendor-CAPBtMef.js";import{C as M,S as A}from"./systemConfigApi-Cw3xPQeV.js";const B={key:4,class:"json-editor"},D={class:"json-actions"},I={key:5,class:"array-editor"},Z={class:"dialog-footer"},F=n(u({__name:"ConfigEditDialog",props:{visible:{type:Boolean},config:{},configGroups:{}},emits:["update:visible","success"],setup(e,{emit:n}){const u=e,F=n,H=s(!1),K=s(!1),R=s(),X=d({config_group:"",config_key:"",config_value:"",config_type:"string",description:"",validation_rule:"",default_value:"",display_order:0,change_reason:""}),Y=c((()=>{var e;return!!(null==(e=u.config)?void 0:e.id)})),L={config_group:[{required:!0,message:"请选择配置组",trigger:"change"}],config_key:[{required:!0,message:"请输入配置键",trigger:"blur"},{pattern:/^[a-z][a-z0-9_]*$/,message:"配置键只能包含小写字母、数字和下划线，且以字母开头",trigger:"blur"},{min:2,max:100,message:"配置键长度在 2 到 100 个字符",trigger:"blur"}],config_type:[{required:!0,message:"请选择配置类型",trigger:"change"}],config_value:[{required:!0,message:"请输入配置值",trigger:"blur"},{validator:function(e,l,a){if(!l&&0!==l&&!1!==l)return void a(new Error("请输入配置值"));const o=X.config_type;try{switch(o){case"integer":if(!/^-?\d+$/.test(String(l)))return void a(new Error("请输入有效的整数"));break;case"float":if(!/^-?\d+(\.\d+)?$/.test(String(l)))return void a(new Error("请输入有效的浮点数"));break;case"boolean":if(!["true","false"].includes(String(l)))return void a(new Error("布尔值只能是 true 或 false"));break;case"json":try{JSON.parse(String(l))}catch(r){return void a(new Error("请输入有效的JSON格式"))}break;case"array":if("string"==typeof l&&l.trim()){if(0===l.trim().split("\n").length)return void a(new Error("数组不能为空"))}}a()}catch(i){a(new Error("配置值格式不正确"))}},trigger:"blur"}],description:[{required:!0,message:"请输入配置描述",trigger:"blur"},{max:500,message:"描述长度不能超过 500 个字符",trigger:"blur"}],validation_rule:[{max:200,message:"验证规则长度不能超过 200 个字符",trigger:"blur"}],default_value:[{max:500,message:"默认值长度不能超过 500 个字符",trigger:"blur"}],display_order:[{type:"number",min:0,max:9999,message:"排序值必须在 0 到 9999 之间",trigger:"blur"}],change_reason:[{required:!0,message:"请输入变更原因",trigger:"blur",validator:(e,l,a)=>{Y.value&&!l?a(new Error("请输入变更原因")):a()}}]};g((()=>u.visible),(e=>{H.value=e,e&&(Q(),u.config&&T())})),g(H,(e=>{F("update:visible",e)}));const Q=()=>{var e;Object.assign(X,{config_group:"",config_key:"",config_value:"",config_type:"string",description:"",validation_rule:"",default_value:"",display_order:0,change_reason:""}),null==(e=R.value)||e.clearValidate()},T=()=>{u.config&&Object.assign(X,{config_group:u.config.config_group,config_key:u.config.config_key,config_value:u.config.config_value,config_type:u.config.config_type,description:u.config.description,validation_rule:u.config.validation_rule||"",default_value:u.config.default_value||"",display_order:u.config.display_order,change_reason:""})},W=e=>{switch(e){case"boolean":X.config_value="false";break;case"integer":X.config_value="0";break;case"float":X.config_value="0.0";break;case"json":X.config_value="{}";break;default:X.config_value=""}},ee=()=>{try{const e=JSON.parse(X.config_value);X.config_value=JSON.stringify(e,null,2),G.success("JSON格式化成功")}catch(e){G.error("JSON格式不正确")}},le=()=>{try{JSON.parse(X.config_value),G.success("JSON格式正确")}catch(e){G.error("JSON格式不正确")}},ae=()=>{H.value=!1},oe=()=>{return e=this,n=null,s=function*(){var e,n;if(R.value)try{if(!(yield R.value.validate()))return;K.value=!0;let s=X.config_value;if("array"===X.config_type&&"string"==typeof s){const e=s.trim().split("\n").filter((e=>e.trim()));s=JSON.stringify(e)}const d=(e=((e,l)=>{for(var a in l||(l={}))r.call(l,a)&&t(e,a,l[a]);if(o)for(var a of o(l))i.call(l,a)&&t(e,a,l[a]);return e})({},X),n={config_value:String(s)},l(e,a(n)));let c;c=Y.value&&u.config?yield A.updateConfig(u.config.id,d):yield A.createConfig(d),c.success?(G.success(Y.value?"更新成功":"创建成功"),F("success"),ae()):G.error(c.message||(Y.value?"更新失败":"创建失败"))}catch(s){G.error(Y.value?"更新失败":"创建失败")}finally{K.value=!1}},new Promise(((l,a)=>{var o=e=>{try{i(s.next(e))}catch(l){a(l)}},r=e=>{try{i(s.throw(e))}catch(l){a(l)}},i=e=>e.done?l(e.value):Promise.resolve(e.value).then(o,r);i((s=s.apply(e,n)).next())}));var e,n,s};return(e,l)=>{const a=j,o=V,r=h,i=S,t=N,n=J,u=C,s=q,d=P,c=v,g=z;return p(),f(g,{modelValue:H.value,"onUpdate:modelValue":l[16]||(l[16]=e=>H.value=e),title:Y.value?"编辑配置":"新增配置",width:"600px","before-close":ae},{footer:_((()=>[x("div",Z,[m(s,{onClick:ae},{default:_((()=>l[27]||(l[27]=[E("取消")]))),_:1}),m(s,{type:"primary",onClick:oe,loading:K.value},{default:_((()=>[E($(Y.value?"更新":"创建"),1)])),_:1},8,["loading"])])])),default:_((()=>[m(c,{ref_key:"formRef",ref:R,model:X,rules:L,"label-width":"100px",onSubmit:l[15]||(l[15]=y((()=>{}),["prevent"]))},{default:_((()=>[m(r,{label:"配置组",prop:"config_group"},{default:_((()=>[m(o,{modelValue:X.config_group,"onUpdate:modelValue":l[0]||(l[0]=e=>X.config_group=e),placeholder:"请选择或输入配置组",filterable:"","allow-create":"",style:{width:"100%"}},{default:_((()=>[(p(!0),w(k,null,O(e.configGroups,(e=>(p(),f(a,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),m(r,{label:"配置键",prop:"config_key"},{default:_((()=>[m(i,{modelValue:X.config_key,"onUpdate:modelValue":l[1]||(l[1]=e=>X.config_key=e),placeholder:"请输入配置键，如：max_connections",clearable:"",disabled:Y.value},null,8,["modelValue","disabled"]),l[17]||(l[17]=x("div",{class:"form-tip"},"配置键应使用小写字母和下划线，创建后不可修改",-1))])),_:1}),m(r,{label:"配置类型",prop:"config_type"},{default:_((()=>[m(o,{modelValue:X.config_type,"onUpdate:modelValue":l[2]||(l[2]=e=>X.config_type=e),placeholder:"请选择配置类型",style:{width:"100%"},onChange:W},{default:_((()=>[(p(!0),w(k,null,O(U(M),(e=>(p(),f(a,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),m(r,{label:"配置值",prop:"config_value"},{default:_((()=>["string"===X.config_type?(p(),f(i,{key:0,modelValue:X.config_value,"onUpdate:modelValue":l[3]||(l[3]=e=>X.config_value=e),placeholder:"请输入配置值",clearable:""},null,8,["modelValue"])):"integer"===X.config_type?(p(),f(t,{key:1,modelValue:X.config_value,"onUpdate:modelValue":l[4]||(l[4]=e=>X.config_value=e),modelModifiers:{number:!0},placeholder:"请输入整数",style:{width:"100%"},precision:0},null,8,["modelValue"])):"float"===X.config_type?(p(),f(t,{key:2,modelValue:X.config_value,"onUpdate:modelValue":l[5]||(l[5]=e=>X.config_value=e),modelModifiers:{number:!0},placeholder:"请输入浮点数",style:{width:"100%"},precision:2},null,8,["modelValue"])):"boolean"===X.config_type?(p(),f(u,{key:3,modelValue:X.config_value,"onUpdate:modelValue":l[6]||(l[6]=e=>X.config_value=e)},{default:_((()=>[m(n,{value:"true"},{default:_((()=>l[18]||(l[18]=[E("是")]))),_:1}),m(n,{value:"false"},{default:_((()=>l[19]||(l[19]=[E("否")]))),_:1})])),_:1},8,["modelValue"])):"json"===X.config_type?(p(),w("div",B,[m(i,{modelValue:X.config_value,"onUpdate:modelValue":l[7]||(l[7]=e=>X.config_value=e),type:"textarea",rows:6,placeholder:"请输入有效的JSON格式"},null,8,["modelValue"]),x("div",D,[m(s,{size:"small",onClick:ee},{default:_((()=>l[20]||(l[20]=[E("格式化")]))),_:1}),m(s,{size:"small",onClick:le},{default:_((()=>l[21]||(l[21]=[E("验证")]))),_:1})])])):"array"===X.config_type?(p(),w("div",I,[m(i,{modelValue:X.config_value,"onUpdate:modelValue":l[8]||(l[8]=e=>X.config_value=e),type:"textarea",rows:4,placeholder:"请输入数组，每行一个值"},null,8,["modelValue"]),l[22]||(l[22]=x("div",{class:"form-tip"},"每行输入一个数组元素",-1))])):(p(),f(i,{key:6,modelValue:X.config_value,"onUpdate:modelValue":l[9]||(l[9]=e=>X.config_value=e),placeholder:"请输入配置值",clearable:""},null,8,["modelValue"]))])),_:1}),m(r,{label:"默认值",prop:"default_value"},{default:_((()=>[m(i,{modelValue:X.default_value,"onUpdate:modelValue":l[10]||(l[10]=e=>X.default_value=e),placeholder:"请输入默认值（可选）",clearable:""},null,8,["modelValue"]),l[23]||(l[23]=x("div",{class:"form-tip"},"当配置值为空时使用的默认值",-1))])),_:1}),m(r,{label:"验证规则",prop:"validation_rule"},{default:_((()=>[m(i,{modelValue:X.validation_rule,"onUpdate:modelValue":l[11]||(l[11]=e=>X.validation_rule=e),placeholder:"请输入验证规则（可选）",clearable:""},null,8,["modelValue"]),l[24]||(l[24]=x("div",{class:"form-tip"}," 支持正则表达式或范围验证，如：^[0-9]+$ 或 1-100 ",-1))])),_:1}),m(r,{label:"排序",prop:"display_order"},{default:_((()=>[m(t,{modelValue:X.display_order,"onUpdate:modelValue":l[12]||(l[12]=e=>X.display_order=e),placeholder:"排序值",style:{width:"100%"},min:0,max:9999},null,8,["modelValue"]),l[25]||(l[25]=x("div",{class:"form-tip"},"数值越小排序越靠前",-1))])),_:1}),m(r,{label:"描述",prop:"description"},{default:_((()=>[m(i,{modelValue:X.description,"onUpdate:modelValue":l[13]||(l[13]=e=>X.description=e),type:"textarea",rows:3,placeholder:"请输入配置描述",maxlength:"500","show-word-limit":""},null,8,["modelValue"])])),_:1}),Y.value?(p(),f(r,{key:0,label:"变更原因",prop:"change_reason"},{default:_((()=>[m(i,{modelValue:X.change_reason,"onUpdate:modelValue":l[14]||(l[14]=e=>X.change_reason=e),placeholder:"请输入本次变更的原因",clearable:"",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])),_:1})):b("",!0),Y.value?(p(),f(d,{key:1,title:"注意",type:"warning",closable:!1,"show-icon":""},{default:_((()=>l[26]||(l[26]=[x("div",null,"修改系统配置可能会影响系统运行，请谨慎操作并填写变更原因。",-1)]))),_:1})):b("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-6bb260f7"]]);export{F as default};
