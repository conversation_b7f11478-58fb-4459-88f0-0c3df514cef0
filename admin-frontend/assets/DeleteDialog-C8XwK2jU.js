var e=Object.defineProperty,l=Object.defineProperties,a=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,u=(l,a,t)=>a in l?e(l,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[a]=t,r=(e,l)=>{for(var a in l||(l={}))o.call(l,a)&&u(e,a,l[a]);if(t)for(var a of t(l))s.call(l,a)&&u(e,a,l[a]);return e},d=(e,t)=>l(e,a(t)),i=(e,l,a)=>new Promise(((t,o)=>{var s=e=>{try{r(a.next(e))}catch(l){o(l)}},u=e=>{try{r(a.throw(e))}catch(l){o(l)}},r=e=>e.done?t(e.value):Promise.resolve(e.value).then(s,u);r((a=a.apply(e,l)).next())}));import{_ as n}from"./index-rNRt1EuS.js";/* empty css                   *//* empty css                       *//* empty css                 */import{P as v,E as p,a as c,F as m}from"./flexiblePriceTableApi-BBM6dCg8.js";import{d as _,j as f,r as b,p as y,N as h,o as w,w as g,b as V,c as x,a as j,Z as P,_ as k,a$ as D,ar as O,F as Y,A as U,u as q,as as C,b0 as H,bp as M,K as B,aI as $,aJ as I,e as T,H as A,Y as E,M as F,ai as z}from"./vendor-CAPBtMef.js";const J={key:0,class:"preview-result"},K={class:"dialog-footer"},N=n(_({__name:"DeleteDialog",props:{visible:{type:Boolean}},emits:["update:visible","deleted"],setup(e,{emit:l}){const a=e,t=l,o=f({get:()=>a.visible,set:e=>t("update:visible",e)}),s=b({providers:[],express_codes:[],from_provinces:[],to_provinces:[],query_success:void 0,start_time:void 0,end_time:void 0,dry_run:!1}),u=b(null),n=b(null),_=b(!1),N=b(!1);y(u,(e=>{e&&2===e.length?(s.value.start_time=e[0],s.value.end_time=e[1]):(s.value.start_time=void 0,s.value.end_time=void 0)}));const S=()=>i(this,null,(function*(){try{_.value=!0;const e=d(r({},s.value),{dry_run:!0}),l=yield m.deletePriceData(e);l.success?(n.value=l.data,F.success("预览完成")):F.error(l.message||"预览失败")}catch(e){F.error("预览失败")}finally{_.value=!1}})),Z=()=>i(this,null,(function*(){if(n.value)try{yield z.confirm(`确定要删除 ${n.value.affected_rows} 条记录吗？此操作不可撤销！`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}),N.value=!0;const e=d(r({},s.value),{dry_run:!1}),l=yield m.deletePriceData(e);l.success?(F.success(`删除成功：${l.data.message}`),t("deleted"),G()):F.error(l.message||"删除失败")}catch(e){"cancel"!==e&&F.error("删除失败")}finally{N.value=!1}else F.warning("请先预览删除结果")})),G=()=>{o.value=!1,s.value={providers:[],express_codes:[],from_provinces:[],to_provinces:[],query_success:void 0,start_time:void 0,end_time:void 0,dry_run:!1},u.value=null,n.value=null};return(e,l)=>{const a=D,t=k,r=C,d=O,i=H,m=P,f=M,b=I,y=$,F=A,z=E;return w(),h(z,{modelValue:o.value,"onUpdate:modelValue":l[6]||(l[6]=e=>o.value=e),title:"删除价格数据",width:"600px",onClose:G},{footer:g((()=>[T("div",K,[V(F,{onClick:G},{default:g((()=>l[8]||(l[8]=[B("取消")]))),_:1}),V(F,{type:"info",onClick:S,loading:_.value},{default:g((()=>l[9]||(l[9]=[B(" 预览删除 ")]))),_:1},8,["loading"]),V(F,{type:"danger",onClick:Z,loading:N.value,disabled:!n.value},{default:g((()=>l[10]||(l[10]=[B(" 确认删除 ")]))),_:1},8,["loading","disabled"])])])),default:g((()=>[V(m,{model:s.value,"label-width":"120px"},{default:g((()=>[V(t,{label:"删除条件"},{default:g((()=>[V(a,{title:"请谨慎操作！删除后的数据无法恢复",type:"warning",closable:!1,style:{"margin-bottom":"20px"}})])),_:1}),V(t,{label:"供应商"},{default:g((()=>[V(d,{modelValue:s.value.providers,"onUpdate:modelValue":l[0]||(l[0]=e=>s.value.providers=e),multiple:"",placeholder:"选择供应商（不选择表示所有）",style:{width:"100%"}},{default:g((()=>[(w(!0),x(Y,null,U(q(v),(e=>(w(),h(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),V(t,{label:"快递公司"},{default:g((()=>[V(d,{modelValue:s.value.express_codes,"onUpdate:modelValue":l[1]||(l[1]=e=>s.value.express_codes=e),multiple:"",placeholder:"选择快递公司（不选择表示所有）",style:{width:"100%"}},{default:g((()=>[(w(!0),x(Y,null,U(q(p),(e=>(w(),h(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),V(t,{label:"始发省份"},{default:g((()=>[V(d,{modelValue:s.value.from_provinces,"onUpdate:modelValue":l[2]||(l[2]=e=>s.value.from_provinces=e),multiple:"",placeholder:"选择始发省份（不选择表示所有）",style:{width:"100%"}},{default:g((()=>[(w(!0),x(Y,null,U(q(c),(e=>(w(),h(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),V(t,{label:"目的省份"},{default:g((()=>[V(d,{modelValue:s.value.to_provinces,"onUpdate:modelValue":l[3]||(l[3]=e=>s.value.to_provinces=e),multiple:"",placeholder:"选择目的省份（不选择表示所有）",style:{width:"100%"}},{default:g((()=>[(w(!0),x(Y,null,U(q(c),(e=>(w(),h(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),V(t,{label:"查询状态"},{default:g((()=>[V(d,{modelValue:s.value.query_success,"onUpdate:modelValue":l[4]||(l[4]=e=>s.value.query_success=e),placeholder:"选择查询状态（不选择表示所有）",style:{width:"100%"},clearable:""},{default:g((()=>[V(r,{label:"查询成功",value:!0}),V(r,{label:"查询失败",value:!1})])),_:1},8,["modelValue"])])),_:1}),V(t,{label:"时间范围"},{default:g((()=>[V(i,{modelValue:u.value,"onUpdate:modelValue":l[5]||(l[5]=e=>u.value=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),n.value?(w(),x("div",J,[V(f,{"content-position":"left"},{default:g((()=>l[7]||(l[7]=[B("删除预览")]))),_:1}),V(a,{title:`将删除 ${n.value.affected_rows} 条记录`,type:"info",closable:!1,style:{"margin-bottom":"15px"}},null,8,["title"]),n.value.deleted_data&&n.value.deleted_data.length>0?(w(),h(y,{key:0,data:n.value.deleted_data,size:"small","max-height":"200"},{default:g((()=>[V(b,{prop:"provider",label:"供应商",width:"80"}),V(b,{prop:"express_code",label:"快递公司",width:"80"}),V(b,{prop:"from_province",label:"始发省份",width:"80"}),V(b,{prop:"to_province",label:"目的省份",width:"80"}),V(b,{prop:"record_count",label:"记录数",width:"80"})])),_:1},8,["data"])):j("",!0)])):j("",!0)])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-559fc545"]]);export{N as default};
