import{d as a,X as s,c as e,b9 as t,e as r,F as c,A as d,u as n,x as p,o as i}from"./vendor-CAPBtMef.js";import{_ as l}from"./index-rNRt1EuS.js";const u={class:"region dynamic art-custom-card"},m={class:"list"},v={class:"user"},o={class:"type"},y={class:"target"},g=l(a({__name:"Dynamic",setup(a){const l=s([{username:"中小鱼",type:"关注了",target:"誶誶淰"},{username:"何小荷",type:"发表文章",target:"Vue3 + Typescript + Vite 项目实战笔记"},{username:"誶誶淰",type:"提出问题",target:"主题可以配置吗"},{username:"发呆草",type:"兑换了物品",target:"《奇特的一生》"},{username:"甜筒",type:"关闭了问题",target:"发呆草"},{username:"冷月呆呆",type:"兑换了物品",target:"《高效人士的七个习惯》"}]);return(a,s)=>(i(),e("div",u,[s[0]||(s[0]=t('<div class="card-header" data-v-25c4d2a6><div class="title" data-v-25c4d2a6><h4 class="box-title" data-v-25c4d2a6>动态</h4><p class="subtitle" data-v-25c4d2a6>新增<span class="text-success" data-v-25c4d2a6>+6</span></p></div></div>',1)),r("div",m,[(i(!0),e(c,null,d(n(l),((a,s)=>(i(),e("div",{key:s},[r("span",v,p(a.username),1),r("span",o,p(a.type),1),r("span",y,p(a.target),1)])))),128))])]))}}),[["__scopeId","data-v-25c4d2a6"]]);export{g as default};
