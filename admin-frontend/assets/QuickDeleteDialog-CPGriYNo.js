import{_ as e}from"./index-rNRt1EuS.js";/* empty css                 */import{d as s,j as l,r as a,N as o,o as t,w as i,e as r,b as n,y as d,u as p,bz as u,K as c,x as m,a$ as v,H as f,Y as g}from"./vendor-CAPBtMef.js";import{a as x}from"./priceTable-dC3_lJoq.js";const _={class:"delete-confirm-content"},b={class:"confirm-text"},C={class:"delete-info"},j={class:"dialog-footer"},w=e(s({__name:"QuickDeleteDialog",props:{visible:{type:Boolean},provider:{},expressCode:{},expressName:{},totalRoutes:{}},emits:["update:visible","confirm"],setup(e,{expose:s,emit:w}){const y=e,k=w,D=l({get:()=>y.visible,set:e=>k("update:visible",e)}),N=a(!1),V=l((()=>x(y.provider))),h=()=>{k("confirm",{provider:y.provider,expressCode:y.expressCode})},z=()=>{D.value=!1};return s({setDeleting:e=>{N.value=e}}),(e,s)=>{const l=d,a=v,x=f,w=g;return t(),o(w,{modelValue:D.value,"onUpdate:modelValue":s[0]||(s[0]=e=>D.value=e),title:"删除确认",width:"500px",onClose:z},{footer:i((()=>[r("div",j,[n(x,{onClick:z},{default:i((()=>s[5]||(s[5]=[c("取消")]))),_:1}),n(x,{type:"danger",onClick:h,loading:N.value},{default:i((()=>s[6]||(s[6]=[c(" 确认删除 ")]))),_:1},8,["loading"])])])),default:i((()=>[r("div",_,[n(l,{class:"warning-icon",size:"48",color:"#E6A23C"},{default:i((()=>[n(p(u))])),_:1}),r("div",b,[s[4]||(s[4]=r("h3",null,"确定要删除以下数据吗？",-1)),r("div",C,[r("p",null,[s[1]||(s[1]=r("strong",null,"供应商：",-1)),c(m(V.value),1)]),r("p",null,[s[2]||(s[2]=r("strong",null,"快递公司：",-1)),c(m(e.expressName),1)]),r("p",null,[s[3]||(s[3]=r("strong",null,"路线数量：",-1)),c(m(e.totalRoutes)+" 条",1)])]),n(a,{title:"此操作不可撤销，请谨慎操作！",type:"warning",closable:!1,style:{"margin-top":"15px"}})])])])),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-97935526"]]);export{w as default};
