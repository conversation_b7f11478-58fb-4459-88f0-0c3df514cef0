import{_ as e}from"./index-rNRt1EuS.js";/* empty css                */import{d as a,j as s,N as t,o as l,w as r,c as i,a2 as d,aI as o,b as n,aJ as p,e as c,x as u,aX as _,K as m,u as f,aF as g,H as v,aG as w,aK as h,y,aQ as b,aS as x,by as k,v as z,aV as C}from"./vendor-CAPBtMef.js";import{d as j,f as $,c as D,g as q}from"./priceTable-dC3_lJoq.js";const L={class:"provider-header"},I={class:"provider-info"},K={class:"provider-stats"},M={class:"stat-item"},R={class:"stat-item"},S={class:"stat-item"},B={class:"provider-actions"},F={key:0,class:"provider-table"},G={class:"express-info"},H={class:"express-name"},J={class:"express-code"},N={class:"route-count"},P={class:"price-value"},Q={class:"min-price-value"},T={class:"response-time"},V={class:"query-time"},X={key:1,class:"empty-data"},A={class:"empty-description"},E=e(a({__name:"ProviderDataSection",props:{provider:{},providerLabel:{},data:{},loading:{type:Boolean}},emits:["refresh","export","delete","showRouteDetails","rowDelete"],setup(e,{emit:a}){const E=e,O=s((()=>E.data.reduce(((e,a)=>e+(a.total_routes||0)),0))),U=s((()=>{if(0===E.data.length)return 0;const e=E.data.reduce(((e,a)=>e+(a.success_rate||0)),0);return Math.round(e/E.data.length)}));return(e,a)=>{const s=_,E=y,W=v,Y=p,Z=g,ee=o,ae=h,se=C,te=w;return l(),t(se,{class:z(["provider-section-card",`provider-${e.provider}`])},{header:r((()=>[c("div",L,[c("div",I,[n(s,{type:f(q)(e.provider),size:"large",class:"provider-tag"},{default:r((()=>[m(u(e.providerLabel),1)])),_:1},8,["type"]),c("div",K,[c("span",M,[c("strong",null,u(e.data.length),1),a[3]||(a[3]=m(" 个快递公司 "))]),c("span",R,[c("strong",null,u(O.value),1),a[4]||(a[4]=m(" 条路线 "))]),c("span",S,[a[5]||(a[5]=m(" 成功率: ")),c("strong",null,u(U.value)+"%",1)])])]),c("div",B,[n(W,{size:"small",onClick:a[0]||(a[0]=a=>e.$emit("refresh")),loading:e.loading},{default:r((()=>[n(E,null,{default:r((()=>[n(f(b))])),_:1}),a[6]||(a[6]=m(" 刷新 "))])),_:1},8,["loading"]),n(W,{type:"primary",size:"small",onClick:a[1]||(a[1]=a=>e.$emit("export"))},{default:r((()=>[n(E,null,{default:r((()=>[n(f(x))])),_:1}),a[7]||(a[7]=m(" 导出 "))])),_:1}),n(W,{type:"danger",size:"small",onClick:a[2]||(a[2]=a=>e.$emit("delete"))},{default:r((()=>[n(E,null,{default:r((()=>[n(f(k))])),_:1}),a[8]||(a[8]=m(" 删除全部 "))])),_:1})])])])),default:r((()=>[e.data.length>0?(l(),i("div",F,[d((l(),t(ee,{data:e.data,stripe:"",border:"",style:{width:"100%"},size:"default"},{default:r((()=>[n(Y,{prop:"express_name",label:"快递公司",width:"160"},{default:r((({row:e})=>[c("div",G,[c("span",H,u(e.express_name),1),c("span",J,"("+u(e.express_code)+")",1)])])),_:1}),n(Y,{prop:"total_routes",label:"路线数",width:"120",align:"center"},{default:r((({row:e})=>[c("span",N,u(e.total_routes),1)])),_:1}),n(Y,{prop:"success_rate",label:"成功率",width:"120",align:"center"},{default:r((({row:e})=>[n(s,{type:e.success_rate>=90?"success":e.success_rate>=70?"warning":"danger",size:"default"},{default:r((()=>[m(u(f(j)(e.success_rate)),1)])),_:2},1032,["type"])])),_:1}),n(Y,{prop:"avg_price",label:"平均价格",width:"120",align:"center"},{default:r((({row:e})=>[c("span",P,u(f($)(e.avg_price)),1)])),_:1}),n(Y,{prop:"min_price",label:"最低价格",width:"120",align:"center"},{default:r((({row:e})=>[c("span",Q,u(f($)(e.min_price)),1)])),_:1}),n(Y,{prop:"avg_response_time",label:"响应时间",width:"120",align:"center"},{default:r((({row:e})=>[c("span",T,u(Math.round(e.avg_response_time))+"ms",1)])),_:1}),n(Y,{prop:"latest_query_time",label:"最新查询",width:"160"},{default:r((({row:e})=>[c("span",V,u(f(D)(e.latest_query_time)),1)])),_:1}),n(Y,{label:"操作",width:"200",fixed:"right"},{default:r((({row:s})=>[n(Z,null,{default:r((()=>[n(W,{type:"primary",size:"default",onClick:a=>e.$emit("showRouteDetails",s)},{default:r((()=>a[9]||(a[9]=[m(" 价格明细 ")]))),_:2},1032,["onClick"]),n(W,{type:"danger",size:"default",onClick:a=>e.$emit("rowDelete",s),loading:s._deleting},{default:r((()=>a[10]||(a[10]=[m(" 删除 ")]))),_:2},1032,["onClick","loading"])])),_:2},1024)])),_:1})])),_:1},8,["data"])),[[te,e.loading]])])):(l(),i("div",X,[n(ae,{description:"暂无数据","image-size":80},{description:r((()=>[c("span",A,u(e.providerLabel)+" 暂无价格数据 ",1)])),_:1})]))])),_:1},8,["class"])}}}),[["__scopeId","data-v-c0f32584"]]);export{E as default};
