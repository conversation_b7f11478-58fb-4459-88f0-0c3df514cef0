import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import viteCompression from 'vite-plugin-compression'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { fileURLToPath } from 'url'

export default ({ mode }) => {
  const root = process.cwd()
  const env = loadEnv(mode, root)
  const { VITE_VERSION, VITE_PORT, VITE_BASE_URL, VITE_API_URL } = env

  return defineConfig({
    define: {
      __APP_VERSION__: JSON.stringify(VITE_VERSION),
      // 生产环境关闭调试
      __VUE_PROD_DEVTOOLS__: false,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    },
    base: VITE_BASE_URL,
    server: {
      port: parseInt(VITE_PORT) || 3007,
      strictPort: true,
      host: true,
      allowedHosts: [
        'lhnizjbonwll.sealosgzg.site',
        'sdbjgqtfpakl.sealosgzg.site',
        '.sealosgzg.site'
      ],
      proxy: {
        '/api': {
          target: 'http://***********:8081',
          changeOrigin: true,
          secure: false,
          timeout: 30000
        }
      }
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@views': resolvePath('src/views'),
        '@comps': resolvePath('src/components'),
        '@imgs': resolvePath('src/assets/img'),
        '@icons': resolvePath('src/assets/icons'),
        '@utils': resolvePath('src/utils'),
        '@stores': resolvePath('src/store'),
        '@plugins': resolvePath('src/plugins'),
        '@styles': resolvePath('src/assets/styles')
      }
    },
    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: false, // 生产环境不生成sourcemap
      chunkSizeWarningLimit: 1000, // 更严格的chunk大小限制
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // 移除console
          drop_debugger: true, // 移除debugger
          pure_funcs: ['console.log'], // 移除console.log
          dead_code: true // 移除死代码
        },
        mangle: {
          toplevel: true // 混淆顶级变量名
        }
      },
      rollupOptions: {
        output: {
          // 生产环境代码分割策略
          manualChunks: {
            'vendor-vue': ['vue', 'vue-router', 'pinia'],
            'vendor-ui': ['element-plus'],
            'vendor-utils': ['axios', '@vueuse/core']
          },
          // 文件命名策略
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const extType = assetInfo.name?.split('.').pop() || '';
            if (['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp'].includes(extType)) {
              return `img/[name]-[hash][extname]`;
            }
            if (['css'].includes(extType)) {
              return `css/[name]-[hash][extname]`;
            }
            return `assets/[name]-[hash][extname]`;
          }
        }
      },
      // 生产环境优化
      cssCodeSplit: true,
      reportCompressedSize: false // 关闭压缩大小报告以提升构建速度
    },
    plugins: [
      vue({
        template: {
          compilerOptions: {
            // 生产环境优化
            isCustomElement: (tag) => tag.startsWith('ion-')
          }
        }
      }),
      Components({
        deep: true,
        extensions: ['vue'],
        dirs: ['src/components'],
        resolvers: [ElementPlusResolver()],
        dts: false // 生产环境不生成类型文件
      }),
      AutoImport({
        imports: ['vue', 'vue-router', '@vueuse/core', 'pinia'],
        resolvers: [ElementPlusResolver()],
        dts: false // 生产环境不生成类型文件
      }),
      // Gzip压缩
      viteCompression({
        verbose: false,
        disable: false,
        algorithm: 'gzip',
        ext: '.gz',
        threshold: 1024, // 只压缩大于1KB的文件
        deleteOriginFile: false
      }),
      // Brotli压缩
      viteCompression({
        verbose: false,
        disable: false,
        algorithm: 'brotliCompress',
        ext: '.br',
        threshold: 1024,
        deleteOriginFile: false
      })
    ],
    // 生产环境依赖优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'element-plus',
        'axios'
      ],
      exclude: ['@vueuse/core']
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          additionalData: `@use "@styles/variables.scss" as *; @use "@styles/mixin.scss" as *;`
        }
      },
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove()
                }
              }
            }
          }
        ]
      }
    },
    // 生产环境缓存策略
    esbuild: {
      drop: ['console', 'debugger'], // 移除console和debugger
      legalComments: 'none' // 移除法律注释
    }
  })
}

function resolvePath(paths: string) {
  return path.resolve(__dirname, paths)
} 