{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "lib": ["esnext", "dom"], "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@views/*": ["src/views/*"], "@components/*": ["src/components/*"], "@imgs/*": ["src/assets/img/*"]}}, "include": ["src/**/*", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/types/auto-imports.d.ts", "src/types/components.d.ts", "src/types/global.d.ts", "src/types/vue-shims.d.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}