package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "swagger": "2.0",
    "info": {
        "description": "Go Kuaidi API Documentation",
        "title": "Go Kuaidi API",
        "contact": {},
        "version": "1.0"
    },
    "host": "localhost:8081",
    "basePath": "/api/v1",
    "paths": {}
}`

var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8081",
	BasePath:         "/api/v1",
	Schemes:          []string{"http"},
	Title:            "Go Kuaidi API",
	Description:      "Go Kuaidi API Documentation",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
