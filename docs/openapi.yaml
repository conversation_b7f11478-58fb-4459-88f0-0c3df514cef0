openapi: 3.0.3
info:
  title: 快递价格查询系统 API
  description: |
    快递价格查询系统提供统一的快递价格查询和订单创建服务，支持多家主流快递公司的价格比较和订单管理。
    
    ## 特性
    - 支持6家主流快递公司（YTO、ZTO、STO、YD、JT、JD）
    - OAuth 2.0 客户端凭证认证
    - 统一的价格查询和订单管理
    - 实时物流轨迹查询
    - 完善的错误处理和限流机制
    
    ## 认证
    使用OAuth 2.0客户端凭证模式，需要先获取访问令牌，然后在请求头中使用Bearer令牌。

    ## 签名验证（可选）
    系统支持可选的HMAC-SHA256签名验证机制，用于增强API安全性。
    当启用签名验证时，需要在请求头中包含签名相关参数：
    - X-Timestamp: Unix时间戳
    - X-Nonce: 随机字符串
    - X-Client-ID: 客户端ID
    - X-Signature: HMAC-SHA256签名
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.example.com
    description: 生产环境
  - url: https://test-api.example.com
    description: 测试环境

security:
  - BearerAuth: []
  - SignatureAuth: []

paths:
  /oauth/token:
    post:
      tags:
        - 认证
      summary: 获取访问令牌
      description: 使用客户端凭证获取访问令牌
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRequest'
            example:
              grant_type: client_credentials
              client_id: your_client_id
              client_secret: your_client_secret
              scope: "express:read express:write"
      responses:
        '200':
          description: 成功获取令牌
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/express/price:
    post:
      tags:
        - 价格查询
      summary: 查询快递价格
      description: 查询指定路线的快递价格，返回所有支持的快递公司报价
      parameters:
        - name: X-Timestamp
          in: header
          required: false
          description: Unix时间戳（启用签名验证时必填）
          schema:
            type: string
            example: "1640995200"
        - name: X-Nonce
          in: header
          required: false
          description: 随机字符串（启用签名验证时必填）
          schema:
            type: string
            example: "abc123def456"
        - name: X-Client-ID
          in: header
          required: false
          description: 客户端ID（启用签名验证时必填）
          schema:
            type: string
            example: "your_client_id"
        - name: X-Signature
          in: header
          required: false
          description: HMAC-SHA256签名（启用签名验证时必填）
          schema:
            type: string
            example: "generated_signature_base64"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PriceRequest'
            example:
              from_province: "广东省"
              from_city: "深圳市"
              from_district: "南山区"
              to_province: "北京市"
              to_city: "北京市"
              to_district: "朝阳区"
              weight: 2.5
              length: 20
              width: 15
              height: 10
              quantity: 1
              goods_name: "电子产品"
              pay_method: 0
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PriceResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/express/order:
    post:
      tags:
        - 订单管理
      summary: 创建快递订单
      description: 使用价格查询返回的order_code创建快递订单
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderRequest'
            example:
              order_code: "ORDER_20250628_ABC123"
              sender_name: "张三"
              sender_mobile: "13800138000"
              sender_province: "广东省"
              sender_city: "深圳市"
              sender_district: "南山区"
              sender_address: "科技园南区某某大厦A座1001室"
              receiver_name: "李四"
              receiver_mobile: "13900139000"
              receiver_province: "北京市"
              receiver_city: "北京市"
              receiver_district: "朝阳区"
              receiver_address: "某某小区1号楼2单元301室"
              weight: 2.5
              quantity: 1
              goods_name: "电子产品"
              pay_method: 0
              remark: "易碎物品，请轻拿轻放"
              insure_value: 1000.00
      responses:
        '200':
          description: 订单创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: 价格已过期
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/express/order/query:
    post:
      tags:
        - 订单管理
      summary: 查询订单详情
      description: 根据订单号查询订单详情和状态
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - order_no
              properties:
                order_no:
                  type: string
                  description: 订单号
                  example: "KD202506280001"
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderDetailResponse'
        '404':
          description: 订单不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/express/track:
    post:
      tags:
        - 物流轨迹
      summary: 查询物流轨迹
      description: 根据快递单号查询物流轨迹信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - tracking_no
                - express_code
              properties:
                tracking_no:
                  type: string
                  description: 快递单号
                  example: "75123456789012"
                express_code:
                  type: string
                  description: 快递公司代码
                  example: "ZTO"
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TrackResponse'
        '404':
          description: 快递单号不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/express/order/cancel:
    post:
      tags:
        - 订单管理
      summary: 取消订单
      description: 取消已创建的快递订单
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - order_no
              properties:
                order_no:
                  type: string
                  description: 订单号
                  example: "KD202506280001"
                cancel_reason:
                  type: string
                  description: 取消原因
                  example: "地址错误"
      responses:
        '200':
          description: 取消成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "订单取消成功"
                  data:
                    type: object
                    properties:
                      order_no:
                        type: string
                        example: "KD202506280001"
                      status:
                        type: string
                        example: "已取消"
                      cancel_time:
                        type: string
                        format: date-time
                        example: "2025-06-28T15:30:00Z"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

    SignatureAuth:
      type: apiKey
      in: header
      name: X-Signature
      description: |
        HMAC-SHA256签名验证（可选）

        需要在请求头中包含以下参数：
        - X-Timestamp: Unix时间戳（有效期300秒）
        - X-Nonce: 随机字符串（防重放攻击）
        - X-Client-ID: 客户端ID
        - X-Signature: HMAC-SHA256签名

        签名算法：
        1. 将所有参数（除sign外）按字典序排序
        2. 按key=value&key=value格式拼接
        3. 如有请求体，添加&body=请求体内容
        4. 使用客户端密钥进行HMAC-SHA256签名
        5. 对结果进行Base64编码

  schemas:
    TokenRequest:
      type: object
      required:
        - grant_type
        - client_id
        - client_secret
      properties:
        grant_type:
          type: string
          enum: [client_credentials]
          description: 授权类型
        client_id:
          type: string
          description: 客户端ID
        client_secret:
          type: string
          description: 客户端密钥
        scope:
          type: string
          description: 权限范围
          example: "express:read express:write"

    TokenResponse:
      type: object
      properties:
        access_token:
          type: string
          description: 访问令牌
        token_type:
          type: string
          enum: [bearer]
          description: 令牌类型
        expires_in:
          type: integer
          description: 过期时间（秒）
        scope:
          type: string
          description: 权限范围

    PriceRequest:
      type: object
      required:
        - from_province
        - from_city
        - to_province
        - to_city
        - weight
      properties:
        from_province:
          type: string
          description: 寄件省份
          example: "广东省"
        from_city:
          type: string
          description: 寄件城市
          example: "深圳市"
        from_district:
          type: string
          description: 寄件区县
          example: "南山区"
        to_province:
          type: string
          description: 收件省份
          example: "北京市"
        to_city:
          type: string
          description: 收件城市
          example: "北京市"
        to_district:
          type: string
          description: 收件区县
          example: "朝阳区"
        weight:
          type: number
          format: float
          minimum: 0.1
          maximum: 50
          description: 包裹重量(kg)
          example: 2.5
        length:
          type: number
          format: float
          minimum: 0
          description: 包裹长度(cm)
          example: 20
        width:
          type: number
          format: float
          minimum: 0
          description: 包裹宽度(cm)
          example: 15
        height:
          type: number
          format: float
          minimum: 0
          description: 包裹高度(cm)
          example: 10
        quantity:
          type: integer
          minimum: 1
          default: 1
          description: 包裹数量
          example: 1
        goods_name:
          type: string
          default: "物品"
          description: 物品名称
          example: "电子产品"
        pay_method:
          type: integer
          enum: [0, 1, 2]
          default: 0
          description: 支付方式：0-寄付，1-到付，2-月结
          example: 0

    PriceResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "查询成功"
        data:
          type: array
          items:
            $ref: '#/components/schemas/PriceItem'

    PriceItem:
      type: object
      properties:
        express_code:
          type: string
          description: 快递公司代码
          enum: [YTO, ZTO, STO, YD, JT, JD]
          example: "ZTO"
        express_name:
          type: string
          description: 快递公司名称
          example: "中通快递"
        product_code:
          type: string
          description: 产品代码
          example: "STANDARD"
        product_name:
          type: string
          description: 产品名称
          example: "标准快递"
        price:
          type: number
          format: float
          description: 运费价格(元)
          example: 15.80
        continued_weight_per_kg:
          type: number
          format: float
          description: 续重单价(元/kg)
          example: 2.50
        calc_weight:
          type: number
          format: float
          description: 计费重量(kg)
          example: 3.0
        order_code:
          type: string
          description: 下单代码，用于创建订单
          example: "ORDER_20250628_ABC123"
        expires_at:
          type: string
          format: date-time
          description: 价格过期时间
          example: "2025-06-28T18:00:00Z"

    OrderRequest:
      type: object
      required:
        - order_code
        - sender_name
        - sender_mobile
        - sender_province
        - sender_city
        - sender_district
        - sender_address
        - receiver_name
        - receiver_mobile
        - receiver_province
        - receiver_city
        - receiver_district
        - receiver_address
        - weight
      properties:
        order_code:
          type: string
          description: 价格查询返回的下单代码
          example: "ORDER_20250628_ABC123"
        sender_name:
          type: string
          description: 寄件人姓名
          example: "张三"
        sender_mobile:
          type: string
          pattern: '^1[3-9]\d{9}$'
          description: 寄件人手机号
          example: "13800138000"
        sender_province:
          type: string
          description: 寄件省份
          example: "广东省"
        sender_city:
          type: string
          description: 寄件城市
          example: "深圳市"
        sender_district:
          type: string
          description: 寄件区县
          example: "南山区"
        sender_address:
          type: string
          description: 寄件详细地址
          example: "科技园南区某某大厦A座1001室"
        receiver_name:
          type: string
          description: 收件人姓名
          example: "李四"
        receiver_mobile:
          type: string
          pattern: '^1[3-9]\d{9}$'
          description: 收件人手机号
          example: "13900139000"
        receiver_province:
          type: string
          description: 收件省份
          example: "北京市"
        receiver_city:
          type: string
          description: 收件城市
          example: "北京市"
        receiver_district:
          type: string
          description: 收件区县
          example: "朝阳区"
        receiver_address:
          type: string
          description: 收件详细地址
          example: "某某小区1号楼2单元301室"
        weight:
          type: number
          format: float
          minimum: 0.1
          maximum: 50
          description: 包裹重量(kg)
          example: 2.5
        quantity:
          type: integer
          minimum: 1
          default: 1
          description: 包裹数量
          example: 1
        goods_name:
          type: string
          description: 物品名称
          example: "电子产品"
        pay_method:
          type: integer
          enum: [0, 1, 2]
          default: 0
          description: 支付方式：0-寄付，1-到付，2-月结
          example: 0
        remark:
          type: string
          description: 订单备注
          example: "易碎物品，请轻拿轻放"
        insure_value:
          type: number
          format: float
          minimum: 0
          description: 保价金额(元)
          example: 1000.00

    OrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "订单创建成功"
        data:
          type: object
          properties:
            order_no:
              type: string
              description: 订单号
              example: "KD202506280001"
            express_code:
              type: string
              description: 快递公司代码
              example: "ZTO"
            express_name:
              type: string
              description: 快递公司名称
              example: "中通快递"
            tracking_no:
              type: string
              description: 快递单号
              example: "75123456789012"
            price:
              type: number
              format: float
              description: 实际价格
              example: 15.80
            status:
              type: string
              description: 订单状态
              example: "已下单"
            created_at:
              type: string
              format: date-time
              description: 创建时间
              example: "2025-06-28T10:30:00Z"

    OrderDetailResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "查询成功"
        data:
          type: object
          properties:
            order_no:
              type: string
              example: "KD202506280001"
            express_code:
              type: string
              example: "ZTO"
            express_name:
              type: string
              example: "中通快递"
            tracking_no:
              type: string
              example: "75123456789012"
            status:
              type: string
              example: "运输中"
            price:
              type: number
              format: float
              example: 15.80
            sender:
              type: object
              properties:
                name:
                  type: string
                  example: "张三"
                mobile:
                  type: string
                  example: "13800138000"
                address:
                  type: string
                  example: "广东省深圳市南山区科技园南区某某大厦A座1001室"
            receiver:
              type: object
              properties:
                name:
                  type: string
                  example: "李四"
                mobile:
                  type: string
                  example: "13900139000"
                address:
                  type: string
                  example: "北京市朝阳区某某小区1号楼2单元301室"
            created_at:
              type: string
              format: date-time
              example: "2025-06-28T10:30:00Z"
            updated_at:
              type: string
              format: date-time
              example: "2025-06-28T14:20:00Z"
