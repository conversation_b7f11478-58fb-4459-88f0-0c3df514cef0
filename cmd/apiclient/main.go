package main

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"sort"
	"strings"
	"time"
)

// 命令行参数
var (
	method       = flag.String("method", "GET", "HTTP方法")
	url          = flag.String("url", "http://localhost:8081/api/v1/express/price/query", "请求URL")
	clientID     = flag.String("client_id", "", "客户端ID")
	clientSecret = flag.String("client_secret", "", "客户端密钥")
	dataFile     = flag.String("data", "", "请求数据文件路径（JSON格式）")
)

func main() {
	flag.Parse()

	// 验证必要参数
	if *clientID == "" || *clientSecret == "" {
		fmt.Println("错误: 客户端ID和密钥是必需的")
		flag.Usage()
		os.Exit(1)
	}

	// 读取请求数据
	var requestBody []byte
	var err error
	if *dataFile != "" {
		requestBody, err = os.ReadFile(*dataFile)
		if err != nil {
			fmt.Printf("错误: 读取数据文件失败: %v\n", err)
			os.Exit(1)
		}
	}

	// 生成随机字符串
	nonce := generateNonce()

	// 生成时间戳
	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	// 解析URL获取路径
	parsedURL, err := http.NewRequest(*method, *url, nil)
	if err != nil {
		fmt.Printf("错误: 解析URL失败: %v\n", err)
		os.Exit(1)
	}
	path := parsedURL.URL.Path

	// 构建参数映射
	params := make(map[string]string)
	params["timestamp"] = timestamp
	params["nonce"] = nonce
	params["client_id"] = *clientID
	params["path"] = path

	// 生成签名
	signature, err := generateSignature(params, requestBody, *clientSecret)
	if err != nil {
		fmt.Printf("错误: 生成签名失败: %v\n", err)
		os.Exit(1)
	}

	// 创建HTTP客户端
	client := &http.Client{}

	// 创建请求
	req, err := http.NewRequest(*method, *url, bytes.NewBuffer(requestBody))
	if err != nil {
		fmt.Printf("错误: 创建请求失败: %v\n", err)
		os.Exit(1)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Timestamp", timestamp)
	req.Header.Set("X-Nonce", nonce)
	req.Header.Set("X-Signature", signature)
	req.Header.Set("X-Client-ID", *clientID)

	// 发送请求
	fmt.Println("发送请求...")
	fmt.Printf("URL: %s\n", *url)
	fmt.Printf("方法: %s\n", *method)
	fmt.Printf("时间戳: %s\n", timestamp)
	fmt.Printf("随机字符串: %s\n", nonce)
	fmt.Printf("客户端ID: %s\n", *clientID)
	fmt.Printf("签名: %s\n", signature)
	if len(requestBody) > 0 {
		fmt.Printf("请求体: %s\n", string(requestBody))
	}

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("错误: 发送请求失败: %v\n", err)
		os.Exit(1)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("错误: 读取响应失败: %v\n", err)
		os.Exit(1)
	}

	// 格式化JSON响应
	var prettyJSON bytes.Buffer
	if json.Valid(respBody) {
		err = json.Indent(&prettyJSON, respBody, "", "  ")
		if err != nil {
			fmt.Printf("错误: 格式化JSON失败: %v\n", err)
			fmt.Printf("响应状态码: %d\n", resp.StatusCode)
			fmt.Printf("响应体: %s\n", string(respBody))
			os.Exit(1)
		}

		fmt.Printf("响应状态码: %d\n", resp.StatusCode)
		fmt.Printf("响应体:\n%s\n", prettyJSON.String())
	} else {
		fmt.Printf("响应状态码: %d\n", resp.StatusCode)
		fmt.Printf("响应体: %s\n", string(respBody))
	}
}

// generateNonce 生成随机字符串
func generateNonce() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 16)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// generateSignature 生成签名
func generateSignature(params map[string]string, body []byte, secret string) (string, error) {
	// 按字典序排序参数
	var keys []string
	for k := range params {
		if k != "sign" { // 排除签名参数
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建待签名字符串
	var parts []string
	for _, k := range keys {
		parts = append(parts, fmt.Sprintf("%s=%s", k, params[k]))
	}
	stringToSign := strings.Join(parts, "&")

	// 如果有请求体，添加到待签名字符串
	if len(body) > 0 {
		stringToSign = stringToSign + "&body=" + string(body)
	}

	// 使用HMAC-SHA256计算签名
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return signature, nil
}
