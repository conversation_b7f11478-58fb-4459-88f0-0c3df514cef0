package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"github.com/your-org/go-kuaidi/internal/auth"
)

func main() {
	// 解析命令行参数
	var (
		keyDir  = flag.String("dir", "keys", "Directory to store keys")
		keyBits = flag.Int("bits", 2048, "RSA key size in bits")
	)
	flag.Parse()

	// 创建密钥目录
	if err := os.MkdirAll(*keyDir, 0700); err != nil {
		log.Fatalf("Failed to create key directory: %v", err)
	}

	// 生成密钥对
	privateKey, _, err := auth.GenerateRSAKeyPair(*keyBits)
	if err != nil {
		log.Fatalf("Failed to generate RSA key pair: %v", err)
	}

	// 保存密钥对
	privateKeyPath := filepath.Join(*keyDir, "private.pem")
	publicKeyPath := filepath.Join(*keyDir, "public.pem")

	if err := auth.SaveRSAKeyPair(privateKey, privateKeyPath, publicKeyPath); err != nil {
		log.Fatalf("Failed to save RSA key pair: %v", err)
	}

	fmt.Printf("RSA key pair generated successfully:\n")
	fmt.Printf("Private key: %s\n", privateKeyPath)
	fmt.Printf("Public key: %s\n", publicKeyPath)
}
