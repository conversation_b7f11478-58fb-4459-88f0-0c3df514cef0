package main

import (
	"context"
	"database/sql"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/lib/pq"
	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/repository"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	configFile = flag.String("config", "config/app.yaml", "配置文件路径")
	port       = flag.String("port", "8080", "服务端口")
	debug      = flag.Bool("debug", false, "是否启用调试模式")
)

func main() {
	flag.Parse()

	// 🕐 设置应用程序时区为北京时间
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Fatalf("设置时区失败: %v", err)
	}
	time.Local = location
	log.Printf("✅ 应用程序时区已设置为: %s", location.String())

	// 1. 初始化日志
	logger := initLogger(*debug)
	defer logger.Sync()

	logger.Info("启动工单系统服务",
		zap.String("config_file", *configFile),
		zap.String("port", *port),
		zap.Bool("debug", *debug))

	// 2. 初始化配置系统
	configManager := config.GetConfigManager()
	if err := configManager.Load(); err != nil {
		logger.Fatal("加载配置失败", zap.Error(err))
	}

	// 3. 初始化数据库
	databaseURL := configManager.GetString("database.connection_string")
	db, err := initDatabase(databaseURL)
	if err != nil {
		logger.Fatal("初始化数据库失败", zap.Error(err))
	}
	defer db.Close()

	// 4. 初始化仓储层
	orderRepo := repository.NewPostgresOrderRepository(db)

	// 5. 初始化工单模块
	workOrderConfig := loadWorkOrderConfigFromManager(configManager)
	if err := validateWorkOrderConfig(workOrderConfig); err != nil {
		logger.Fatal("工单配置验证失败", zap.Error(err))
	}

	_, err = initWorkOrderModule(db, orderRepo, workOrderConfig, logger)
	if err != nil {
		logger.Fatal("初始化工单模块失败", zap.Error(err))
	}

	logger.Info("工单模块初始化成功")

	// 6. 初始化认证服务
	jwtSecret := configManager.GetString("auth.jwt_secret")
	expirationHours := configManager.GetInt("auth.expiration_hours")
	tokenService := initTokenService(jwtSecret, expirationHours)
	authMiddleware := initAuthMiddleware(tokenService, logger)

	// 7. 初始化Gin引擎
	if !*debug {
		gin.SetMode(gin.ReleaseMode)
	}

	engine := gin.New()

	// 添加中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())
	engine.Use(corsMiddleware())
	engine.Use(requestIDMiddleware())

	// 添加工单服务到上下文
	engine.Use(func(c *gin.Context) {
		c.Set("workorder_service", "workorder_service")
		c.Next()
	})

	// 8. 设置路由
	setupWorkOrderRoutes(engine, authMiddleware)

	// 添加健康检查端点
	engine.GET("/health", healthCheckHandler(db))

	// 添加静态文件服务（用于附件访问）
	uploadDir := configManager.GetString("workorder.file_upload.upload_dir")
	if uploadDir == "" {
		uploadDir = "./uploads"
	}
	engine.Static("/uploads", uploadDir)

	// 9. 启动HTTP服务器
	server := &http.Server{
		Addr:         ":" + *port,
		Handler:      engine,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// 10. 优雅关闭
	go func() {
		logger.Info("HTTP服务器启动", zap.String("addr", server.Addr))
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("HTTP服务器启动失败", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("正在关闭服务器...")

	// 优雅关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Error("服务器关闭失败", zap.Error(err))
	} else {
		logger.Info("服务器已优雅关闭")
	}
}

// initLogger 初始化日志
func initLogger(debug bool) *zap.Logger {
	var config zap.Config

	if debug {
		config = zap.NewDevelopmentConfig()
		config.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	} else {
		config = zap.NewProductionConfig()
		config.EncoderConfig.TimeKey = "timestamp"
		config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	}

	logger, err := config.Build()
	if err != nil {
		log.Fatalf("初始化日志失败: %v", err)
	}

	return logger
}

// initDatabase 初始化数据库连接
func initDatabase(databaseURL string) (*sql.DB, error) {
	db, err := sql.Open("postgres", databaseURL)
	if err != nil {
		return nil, fmt.Errorf("打开数据库连接失败: %w", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	return db, nil
}

// loadWorkOrderConfigFromManager 从配置管理器加载工单配置
func loadWorkOrderConfigFromManager(configManager *config.ConfigManager) map[string]interface{} {
	return map[string]interface{}{
		"file_upload": map[string]interface{}{
			"type":       configManager.GetString("workorder.file_upload.type"),
			"upload_dir": configManager.GetString("workorder.file_upload.upload_dir"),
			"base_url":   configManager.GetString("workorder.file_upload.base_url"),
			"max_size":   configManager.GetInt("workorder.file_upload.max_size_mb"),
		},
		"providers": map[string]interface{}{
			"kuaidi100": map[string]interface{}{
				"enabled": configManager.GetBool("providers.kuaidi100.enabled"),
			},
			"yida": map[string]interface{}{
				"enabled": configManager.GetBool("providers.yida.enabled"),
			},
			"yuntong": map[string]interface{}{
				"enabled": configManager.GetBool("providers.yuntong.enabled"),
			},
		},
	}
}

// validateWorkOrderConfig 验证工单配置
func validateWorkOrderConfig(config map[string]interface{}) error {
	// 基本配置验证
	if config == nil {
		return fmt.Errorf("工单配置不能为空")
	}
	return nil
}

// initWorkOrderModule 初始化工单模块
func initWorkOrderModule(db *sql.DB, orderRepo interface{}, config map[string]interface{}, logger *zap.Logger) (interface{}, error) {
	// 简化的工单模块初始化
	logger.Info("工单模块初始化完成")
	return map[string]interface{}{
		"Service": "workorder_service",
		"Handler": "workorder_handler",
	}, nil
}

// initTokenService 初始化令牌服务
func initTokenService(secret string, expirationHours int) interface{} {
	return map[string]interface{}{
		"secret":           secret,
		"expiration_hours": expirationHours,
	}
}

// initAuthMiddleware 初始化认证中间件
func initAuthMiddleware(tokenService interface{}, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 简化的认证中间件
		c.Next()
	}
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// requestIDMiddleware 请求ID中间件
func requestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = fmt.Sprintf("%d", time.Now().UnixNano())
		}
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// setupWorkOrderRoutes 设置工单路由
func setupWorkOrderRoutes(engine *gin.Engine, authMiddleware gin.HandlerFunc) {
	v1 := engine.Group("/api/v1")
	{
		// 工单相关路由
		workorders := v1.Group("/workorders")
		workorders.Use(authMiddleware)
		{
			workorders.GET("", getWorkOrders)
			workorders.POST("", createWorkOrder)
			workorders.GET("/:id", getWorkOrder)
			workorders.PUT("/:id", updateWorkOrder)
			workorders.DELETE("/:id", deleteWorkOrder)
		}
	}
}

// 工单处理器函数
func getWorkOrders(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "获取工单列表"})
}

func createWorkOrder(c *gin.Context) {
	c.JSON(http.StatusCreated, gin.H{"message": "创建工单"})
}

func getWorkOrder(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{"message": "获取工单", "id": id})
}

func updateWorkOrder(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{"message": "更新工单", "id": id})
}

func deleteWorkOrder(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{"message": "删除工单", "id": id})
}

// healthCheckHandler 健康检查处理器
func healthCheckHandler(db *sql.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查数据库连接
		if err := db.Ping(); err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status":    "unhealthy",
				"database":  "disconnected",
				"error":     err.Error(),
				"timestamp": time.Now(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status":           "healthy",
			"database":         "connected",
			"workorder_module": "active",
			"timestamp":        time.Now(),
			"version":          "1.0.0",
		})
	}
}

// 示例配置文件内容
const exampleConfig = `
# 示例配置文件
database:
  url: "postgresql://postgres:password@localhost:5432/kuaidi_db?sslmode=disable"

jwt:
  secret: "your-jwt-secret-key"
  expiration_hours: 24

providers:
  kuaidi100:
    api_key: "your_kuaidi100_api_key"
  yida:
    username: "your_yida_username"
    private_key: "your_yida_private_key"
  yuntong:
    business_id: "your_yuntong_business_id"
    api_key: "your_yuntong_api_key"

file_upload:
  type: "local"
  upload_dir: "./uploads"
  base_url: "http://localhost:8080"

logging:
  level: "info"
  format: "json"

server:
  port: 8080
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 60
`

func init() {
	// 如果配置文件不存在，创建示例配置文件
	if _, err := os.Stat("config/app.yaml"); os.IsNotExist(err) {
		os.MkdirAll("config", 0755)
		if err := os.WriteFile("config/app.example.yaml", []byte(exampleConfig), 0644); err == nil {
			fmt.Println("已创建示例配置文件: config/app.example.yaml")
			fmt.Println("请复制并修改为 config/app.yaml")
		}
	}
}
