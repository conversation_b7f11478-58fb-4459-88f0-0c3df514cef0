package main

import (
	"bufio"
	"flag"
	"fmt"
	"os"
	"strings"

	"github.com/your-org/go-kuaidi/internal/security"
	"go.uber.org/zap"
)

func main() {
	var (
		envFile    = flag.String("env", "", "环境变量文件路径")
		output     = flag.String("output", "console", "输出格式: console, json, file")
		outputFile = flag.String("output-file", "security-report.txt", "输出文件路径")
		verbose    = flag.Bool("verbose", false, "详细输出")
	)
	flag.Parse()

	// 初始化日志
	var logger *zap.Logger
	var err error
	if *verbose {
		logger, err = zap.NewDevelopment()
	} else {
		logger, err = zap.NewProduction()
	}
	if err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Sync()

	// 加载环境变量文件
	if *envFile != "" {
		if err := loadEnvFile(*envFile); err != nil {
			logger.Error("加载环境变量文件失败", zap.Error(err))
			os.Exit(1)
		}
	}

	// 创建安全配置验证器
	validator := security.NewConfigValidator(logger)

	// 执行安全检查
	logger.Info("开始安全配置检查...")

	// 生成安全报告
	report := validator.GenerateSecurityReport()

	// 输出结果
	switch *output {
	case "console":
		fmt.Println(report)
	case "file":
		if err := writeToFile(*outputFile, report); err != nil {
			logger.Error("写入文件失败", zap.Error(err))
			os.Exit(1)
		}
		fmt.Printf("安全报告已保存到: %s\n", *outputFile)
	case "json":
		// TODO: 实现JSON格式输出
		fmt.Println("JSON格式输出暂未实现")
	default:
		fmt.Printf("不支持的输出格式: %s\n", *output)
		os.Exit(1)
	}

	// 检查是否有严重错误
	configResult := validator.ValidateProductionConfig()
	apiKeyResult := validator.ValidateAPIKeys()

	if !configResult.Valid || !apiKeyResult.Valid {
		logger.Error("安全检查发现严重问题，请查看报告")
		os.Exit(1)
	}

	logger.Info("安全检查完成")
}

// loadEnvFile 加载环境变量文件
func loadEnvFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("打开环境变量文件失败: %w", err)
	}
	defer file.Close()

	// 简单的环境变量文件解析
	// 在实际项目中，建议使用 godotenv 等库
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			// 移除引号
			if len(value) >= 2 &&
				((value[0] == '"' && value[len(value)-1] == '"') ||
					(value[0] == '\'' && value[len(value)-1] == '\'')) {
				value = value[1 : len(value)-1]
			}
			os.Setenv(key, value)
		}
	}

	return scanner.Err()
}

// writeToFile 写入文件
func writeToFile(filename, content string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	_, err = file.WriteString(content)
	if err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	return nil
}

// 使用示例:
// go run cmd/security-check/main.go -env config/production.env -output file -output-file security-report.txt -verbose
