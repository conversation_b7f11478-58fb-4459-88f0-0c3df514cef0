package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/your-org/go-kuaidi/internal/config"
	"go.uber.org/zap"
)

var (
	action     = flag.String("action", "", "操作类型: migrate, validate, status, backup, init, check")
	configPath = flag.String("config", "config/config.yaml", "配置文件路径")
	verbose    = flag.Bool("verbose", false, "详细输出")
	force      = flag.Bool("force", false, "强制执行操作")
)

func main() {
	flag.Parse()

	// 初始化日志
	logger := initLogger(*verbose)
	defer logger.Sync()

	// 创建配置初始化器
	initializer := config.NewConfigInitializer(logger)

	switch *action {
	case "migrate":
		if err := runMigration(initializer); err != nil {
			logger.Fatal("配置迁移失败", zap.Error(err))
		}
	case "validate":
		if err := runValidation(initializer); err != nil {
			logger.Fatal("配置验证失败", zap.Error(err))
		}
	case "status":
		runStatus(initializer)
	case "backup":
		if err := runBackup(initializer); err != nil {
			logger.Fatal("配置备份失败", zap.Error(err))
		}
	case "init":
		if err := runInit(initializer); err != nil {
			logger.Fatal("配置初始化失败", zap.Error(err))
		}
	case "check":
		if err := runCheck(initializer); err != nil {
			logger.Fatal("配置检查失败", zap.Error(err))
		}
	default:
		printUsage()
		os.Exit(1)
	}
}

// initLogger 初始化日志
func initLogger(verbose bool) *zap.Logger {
	var logger *zap.Logger
	var err error

	if verbose {
		config := zap.NewDevelopmentConfig()
		logger, err = config.Build()
	} else {
		config := zap.NewProductionConfig()
		config.Level = zap.NewAtomicLevelAt(zap.InfoLevel)
		logger, err = config.Build()
	}

	if err != nil {
		panic(fmt.Sprintf("初始化日志失败: %v", err))
	}

	return logger
}

// runMigration 执行配置迁移
func runMigration(initializer *config.ConfigInitializer) error {
	fmt.Println("🔄 开始配置迁移...")

	// 执行完整的初始化过程，包括迁移
	result, err := initializer.Initialize()
	if err != nil {
		return fmt.Errorf("配置初始化失败: %w", err)
	}

	// 显示结果
	if result.MigrationDone {
		fmt.Println("🎉 配置迁移完成！")
		fmt.Println("📝 请检查", result.ConfigPath, "文件并根据需要调整配置")
		fmt.Println("🗂️  旧配置文件已备份到 config/backups/ 目录")
	} else {
		fmt.Println("ℹ️  无需迁移，配置文件已是最新格式")
	}

	if len(result.Warnings) > 0 {
		fmt.Println("\n⚠️  警告:")
		for _, warning := range result.Warnings {
			fmt.Printf("  - %s\n", warning)
		}
	}

	return nil
}

// runValidation 执行配置验证
func runValidation(initializer *config.ConfigInitializer) error {
	fmt.Println("🔍 验证配置文件...")

	// 加载配置
	manager := initializer.GetManager()
	if err := manager.Load(); err != nil {
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 验证配置
	validator := initializer.GetValidator()
	result := validator.ValidateConfig(manager)

	// 显示验证结果
	summary := validator.GetValidationSummary(result)
	fmt.Println(summary)

	if !result.Valid {
		return fmt.Errorf("配置验证失败")
	}

	fmt.Println("✅ 配置文件验证通过！")
	return nil
}

// runStatus 显示配置状态
func runStatus(initializer *config.ConfigInitializer) {
	fmt.Println("📊 配置状态检查...")
	fmt.Println(repeat("=", 50))

	// 使用初始化器检查状态
	result, err := initializer.Initialize()
	if err != nil {
		fmt.Printf("❌ 配置系统状态: 异常 (%v)\n", err)
		fmt.Println("\n📋 详细信息:")
		if len(result.Errors) > 0 {
			fmt.Println("错误:")
			for _, e := range result.Errors {
				fmt.Printf("  - %s\n", e)
			}
		}
	} else {
		fmt.Printf("✅ 配置系统状态: 正常\n")
		fmt.Printf("� 配置文件路径: %s\n", result.ConfigPath)

		if result.MigrationDone {
			fmt.Println("� 已执行配置迁移")
		}

		if result.ValidationResult != nil && result.ValidationResult.Valid {
			fmt.Println("✅ 配置验证通过")
		}
	}

	// 显示警告信息
	if len(result.Warnings) > 0 {
		fmt.Println("\n⚠️  警告:")
		for _, warning := range result.Warnings {
			fmt.Printf("  - %s\n", warning)
		}
	}

	// 显示建议
	fmt.Println("\n💡 建议:")
	if err != nil {
		fmt.Println("  � 请运行配置初始化: go run cmd/config-tool/main.go -action=init")
	} else {
		fmt.Println("  ✅ 配置系统运行正常")
		fmt.Println("  � 可运行验证检查: go run cmd/config-tool/main.go -action=validate")
	}

	fmt.Println(repeat("=", 50))
}

// runBackup 执行配置备份
func runBackup(_ *config.ConfigInitializer) error {
	fmt.Println("📦 备份配置文件...")

	// 创建迁移器来执行备份
	logger, _ := zap.NewProduction()
	migrator := config.NewConfigMigrator(logger)

	if err := migrator.BackupOldConfigs(); err != nil {
		return fmt.Errorf("备份配置失败: %w", err)
	}

	fmt.Println("✅ 配置文件备份完成！")
	fmt.Println("📁 备份位置: config/backups/")

	return nil
}

// runInit 执行配置初始化
func runInit(initializer *config.ConfigInitializer) error {
	fmt.Println("🔧 初始化配置系统...")

	// 检查是否强制执行
	if !*force {
		// 检查是否已存在配置文件
		if _, err := os.Stat("config/config.yaml"); err == nil {
			fmt.Println("⚠️  配置文件已存在，使用 -force 参数强制重新创建")
			return nil
		}
	}

	// 创建默认配置
	if err := initializer.CreateDefaultConfig(); err != nil {
		return fmt.Errorf("创建默认配置失败: %w", err)
	}

	// 执行初始化
	result, err := initializer.Initialize()
	if err != nil {
		return fmt.Errorf("初始化配置失败: %w", err)
	}

	fmt.Println("🎉 配置系统初始化完成！")
	fmt.Printf("📁 配置文件: %s\n", result.ConfigPath)
	fmt.Println("📝 请根据需要修改配置文件中的参数")

	return nil
}

// runCheck 执行配置检查
func runCheck(initializer *config.ConfigInitializer) error {
	fmt.Println("🔍 执行配置全面检查...")

	// 1. 检查配置文件格式
	validator := initializer.GetValidator()
	fileResult := validator.ValidateConfigFile(*configPath)

	fmt.Println("📄 配置文件格式检查:")
	if fileResult.Valid {
		fmt.Println("  ✅ 格式正确")
	} else {
		fmt.Println("  ❌ 格式问题:")
		for _, err := range fileResult.Errors {
			fmt.Printf("    - %s\n", err)
		}
	}

	// 2. 检查环境变量
	envResult := validator.ValidateEnvironmentVariables()
	fmt.Println("\n🌍 环境变量检查:")
	if len(envResult.Warnings) == 0 {
		fmt.Println("  ✅ 环境变量配置正常")
	} else {
		fmt.Println("  ⚠️  环境变量警告:")
		for _, warning := range envResult.Warnings {
			fmt.Printf("    - %s\n", warning)
		}
	}

	// 3. 执行完整配置验证
	fmt.Println("\n⚙️  配置内容验证:")
	result, err := initializer.Initialize()
	if err != nil {
		fmt.Printf("  ❌ 配置验证失败: %v\n", err)
		return err
	}

	if result.ValidationResult.Valid {
		fmt.Println("  ✅ 配置内容验证通过")
	} else {
		fmt.Println("  ❌ 配置内容验证失败:")
		for _, err := range result.ValidationResult.Errors {
			fmt.Printf("    - %s\n", err)
		}
	}

	fmt.Println("\n🎯 检查完成！")
	return nil
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Println("Go-Kuaidi 配置管理工具")
	fmt.Println(repeat("=", 50))
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  go run cmd/config-tool/main.go -action=<操作> [选项]")
	fmt.Println()
	fmt.Println("操作:")
	fmt.Println("  migrate   - 迁移分散的配置文件到统一的YAML配置")
	fmt.Println("  validate  - 验证统一配置文件的正确性")
	fmt.Println("  status    - 显示配置系统的当前状态")
	fmt.Println("  backup    - 备份现有配置文件")
	fmt.Println("  init      - 初始化默认配置文件")
	fmt.Println("  check     - 执行全面的配置检查")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  -config   - 指定配置文件路径 (默认: config/config.yaml)")
	fmt.Println("  -verbose  - 启用详细输出")
	fmt.Println("  -force    - 强制执行操作（用于init等命令）")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 检查配置状态")
	fmt.Println("  go run cmd/config-tool/main.go -action=status")
	fmt.Println()
	fmt.Println("  # 执行配置迁移")
	fmt.Println("  go run cmd/config-tool/main.go -action=migrate -verbose")
	fmt.Println()
	fmt.Println("  # 验证配置文件")
	fmt.Println("  go run cmd/config-tool/main.go -action=validate")
	fmt.Println()
	fmt.Println("  # 备份配置文件")
	fmt.Println("  go run cmd/config-tool/main.go -action=backup")
	fmt.Println()
}

// 字符串重复函数（Go 1.18之前的版本兼容）
func repeat(s string, count int) string {
	if count <= 0 {
		return ""
	}
	result := ""
	for i := 0; i < count; i++ {
		result += s
	}
	return result
}

func init() {
	// 设置使用说明
	flag.Usage = printUsage
}
