package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
	"github.com/your-org/go-kuaidi/internal/auth"
)

func main() {
	// 解析命令行参数
	var (
		action     = flag.String("action", "", "Action to perform: create, list, delete")
		dbConnStr  = flag.String("db", "postgresql://postgres:<EMAIL>:5432/go_kuaidi", "Database connection string")
		clientName = flag.String("name", "", "Client name (for create)")
		clientID   = flag.String("id", "", "Client ID (for delete)")
		scopesStr  = flag.String("scopes", "express:read express:write", "Space-separated list of allowed scopes (for create)")
	)
	flag.Parse()

	// 验证操作
	if *action == "" {
		fmt.Println("Error: action is required")
		flag.Usage()
		os.Exit(1)
	}

	// 连接数据库
	db, err := sql.Open("postgres", *dbConnStr)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// 测试数据库连接
	if err := db.Ping(); err != nil {
		log.Fatalf("Failed to ping database: %v", err)
	}

	// 创建客户端存储
	clientRepo := auth.NewPostgresClientRepository(db)
	clientService := auth.NewClientService(clientRepo)

	// 执行操作
	switch *action {
	case "create":
		if *clientName == "" {
			fmt.Println("Error: name is required for create action")
			flag.Usage()
			os.Exit(1)
		}

		// 解析范围
		scopes := strings.Split(*scopesStr, " ")

		// 创建客户端
		client, secret, err := clientService.GenerateClientCredentials(*clientName, scopes)
		if err != nil {
			log.Fatalf("Failed to create client: %v", err)
		}

		fmt.Printf("Client created successfully:\n")
		fmt.Printf("ID: %s\n", client.ID)
		fmt.Printf("Secret: %s\n", secret)
		fmt.Printf("Name: %s\n", client.Name)
		fmt.Printf("Allowed Scopes: %s\n", strings.Join(client.AllowedScopes, ", "))

	case "list":
		fmt.Println("Error: list action not implemented yet")
		os.Exit(1)

	case "delete":
		if *clientID == "" {
			fmt.Println("Error: id is required for delete action")
			flag.Usage()
			os.Exit(1)
		}

		// 删除客户端
		if err := clientRepo.Delete(*clientID); err != nil {
			log.Fatalf("Failed to delete client: %v", err)
		}

		fmt.Printf("Client %s deleted successfully\n", *clientID)

	default:
		fmt.Printf("Error: unknown action %s\n", *action)
		flag.Usage()
		os.Exit(1)
	}
}
