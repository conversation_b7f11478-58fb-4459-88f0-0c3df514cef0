package main

import (
	"bufio"
	"context"
	"database/sql"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

func main() {
	// 数据库连接
	db, err := sql.Open("postgres", "postgresql://postgres:<EMAIL>:5432/go_kuaidi")
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	ctx := context.Background()

	// 读取961条路线
	routes, err := readRoutesFromFile("docs/961.txt")
	if err != nil {
		log.Fatal("读取路线文件失败:", err)
	}

	log.Printf("读取到 %d 条路线", len(routes))

	// 清空现有数据
	_, err = db.ExecContext(ctx, "DELETE FROM warmup_routes")
	if err != nil {
		log.Fatal("清空现有路线数据失败:", err)
	}

	// 批量插入路线数据
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		log.Fatal("开始事务失败:", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, `
		INSERT INTO warmup_routes (from_province, to_province, priority, warmup_frequency_hours)
		VALUES ($1, $2, $3, $4)
	`)
	if err != nil {
		log.Fatal("准备插入语句失败:", err)
	}
	defer stmt.Close()

	for i, route := range routes {
		priority := getPriority(route.FromProvince, route.ToProvince)
		frequency := getFrequency(priority)

		_, err = stmt.ExecContext(ctx, route.FromProvince, route.ToProvince, priority, frequency)
		if err != nil {
			log.Printf("插入路线失败 %s->%s: %v", route.FromProvince, route.ToProvince, err)
			continue
		}

		if (i+1)%100 == 0 {
			log.Printf("已插入 %d/%d 条路线", i+1, len(routes))
		}
	}

	if err = tx.Commit(); err != nil {
		log.Fatal("提交事务失败:", err)
	}

	log.Printf("成功初始化 %d 条预热路线", len(routes))
}

type Route struct {
	FromProvince string
	ToProvince   string
}

func readRoutesFromFile(filename string) ([]Route, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var routes []Route
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		// 解析路线格式：安徽北京 -> from: 安徽, to: 北京
		if len(line) < 4 {
			continue
		}

		// 找到省份分界点
		provinces := []string{
			"安徽", "北京", "福建", "甘肃", "广东", "广西", "贵州", "海南",
			"河北", "河南", "黑龙江", "湖北", "湖南", "吉林", "江苏", "江西",
			"辽宁", "内蒙古", "宁夏", "青海", "山东", "山西", "陕西", "上海",
			"四川", "天津", "西藏", "新疆", "云南", "浙江", "重庆",
		}

		var fromProvince, toProvince string
		for _, province := range provinces {
			if strings.HasPrefix(line, province) {
				fromProvince = province
				remaining := line[len(province):]
				for _, toProvinceCandidate := range provinces {
					if remaining == toProvinceCandidate {
						toProvince = toProvinceCandidate
						break
					}
				}
				break
			}
		}

		if fromProvince != "" && toProvince != "" {
			routes = append(routes, Route{
				FromProvince: fromProvince,
				ToProvince:   toProvince,
			})
		}
	}

	return routes, scanner.Err()
}

// 获取路线优先级
func getPriority(from, to string) int {
	// 主要经济区域优先级更高
	majorProvinces := map[string]bool{
		"北京": true, "上海": true, "广东": true, "江苏": true,
		"浙江": true, "山东": true, "河南": true, "四川": true,
	}

	if majorProvinces[from] || majorProvinces[to] {
		return 1 // 高优先级
	}

	if from == to {
		return 3 // 省内路线优先级较低
	}

	return 2 // 中优先级
}

// 获取预热频率（小时）
func getFrequency(priority int) int {
	switch priority {
	case 1:
		return 6 // 高优先级路线每6小时预热一次
	case 2:
		return 12 // 中优先级路线每12小时预热一次
	case 3:
		return 24 // 低优先级路线每24小时预热一次
	default:
		return 24
	}
}
