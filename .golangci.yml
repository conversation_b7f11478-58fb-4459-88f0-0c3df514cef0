# 🤖 AI代码生成专用golangci-lint配置 (2025年最佳实践)
# 专门针对AI写代码的常见问题进行优化
# 兼容 Go 1.24.4 + golangci-lint v1.64.8 (built with go1.23)

run:
  timeout: 15m
  issues-exit-code: 1
  tests: true
  go: "1.23"  # 使用golangci-lint构建时的Go版本

output:
  formats:
    - format: colored-line-number
  print-issued-lines: true
  print-linter-name: true
  sort-results: true

issues:
  # 🚨 新版本配置格式
  exclude-dirs:
    - vendor
    - node_modules
    - docs
    - scripts
    - .git
    - tmp
    - build

  exclude-files:
    - ".*\\.pb\\.go$"
    - ".*\\.gen\\.go$"
    - ".*_generated\\.go$"
    - ".*config.*"     # 临时跳过配置文件
    - ".*cache.*"      # 临时跳过缓存文件
    - ".*auth.*"       # 临时跳过认证文件

  uniq-by-line: true

# 🤖 AI代码生成专用linter设置
linters-settings:
  # 🚨 AI问题1: 函数过长 - 严格控制函数长度
  funlen:
    lines: 60        # AI容易生成长函数，严格限制
    statements: 30   # 限制语句数量，强制拆分逻辑

  # 🚨 AI问题2: 复杂度过高 - 多重复杂度检查
  gocyclo:
    min-complexity: 8  # 比标准更严格，AI容易生成复杂逻辑

  gocognit:
    min-complexity: 15 # 认知复杂度检查

  cyclop:
    max-complexity: 8
    package-average: 6.0

  # 🚨 AI问题3: 重复代码 - 严格检查重复
  dupl:
    threshold: 60    # 更严格的重复检查，AI容易复制粘贴

  # 🚨 AI问题4: 常量提取 - AI容易硬编码
  goconst:
    min-len: 2
    min-occurrences: 2
    ignore-tests: true
    match-constant: true
    numbers: true    # 检查数字常量

  # 🚨 AI问题5: 错误处理 - 已移动到下方统一配置

  # 🚨 AI问题6: 安全问题 - 严格安全检查
  gosec:
    severity: low    # 检查所有安全问题
    confidence: low  # 包括低置信度问题
    excludes:
      - G104 # 已在errcheck处理

  # 🚨 AI问题7: 命名规范 - 严格命名检查
  revive:
    rules:
      - name: var-naming
      - name: exported
      - name: argument-limit
        arguments: [5]  # AI容易生成过多参数
      - name: function-result-limit
        arguments: [3]  # 限制返回值数量
      - name: cyclomatic
        arguments: [8]
      - name: cognitive-complexity
        arguments: [15]
      - name: line-length-limit
        arguments: [120]
      - name: flag-parameter  # AI容易使用布尔参数
      - name: deep-exit      # 避免深层嵌套退出

  # 🚨 AI问题8: 性能问题 - 性能优化检查
  prealloc:
    simple: true
    range-loops: true
    for-loops: true

  # 🚨 AI问题9: 代码质量 - 全面质量检查
  gocritic:
    enabled-tags:
      - diagnostic   # 诊断问题
      - style       # 风格问题
      - performance # 性能问题
    disabled-checks:
      - dupImport    # 重复导入
      - commentedOutCode # 注释代码，AI可能需要

  # 🚨 AI问题10: 格式和导入
  gofmt:
    simplify: true

  goimports:
    local-prefixes: github.com/your-org/go-kuaidi

  # 🚨 AI问题11: 行长度控制
  lll:
    line-length: 120

  # 🚨 AI问题12: 返回值检查
  nakedret:
    max-func-lines: 15  # AI容易生成长函数的裸返回

  # 🚨 AI问题13: 未使用代码检查
  unused:
    check-exported: false
    go: "1.21"

  unparam:
    check-exported: false

  # 🚨 AI问题14: 拼写检查
  misspell:
    locale: US
    ignore-words:
      - kuaidi
      - yida
      - yuntong
      - golang

  # 🚨 AI问题15: 依赖管理
  depguard:
    rules:
      main:
        deny:
          - pkg: "github.com/sirupsen/logrus"
            desc: "使用go.uber.org/zap进行日志记录"
          - pkg: "log"
            desc: "使用go.uber.org/zap进行日志记录"
        allow:
          - $gostd
          - github.com/your-org/go-kuaidi

  # 🚨 AI问题16: Go静态分析 (新版本格式)
  govet:
    enable:
      - atomic
      - bools
      - buildtag
      - copylocks
      - errorsas
      - httpresponse
      - loopclosure
      - lostcancel
      - nilfunc
      - printf
      - shift
      - stdmethods
      - structtag
      - tests
      - unmarshal
      - unreachable
      - unsafeptr
      - unusedresult
      - shadow  # 替代check-shadowing

  # 🚨 AI问题17: 错误检查 (新版本格式)
  errcheck:
    check-type-assertions: true
    check-blank: true
    exclude-functions:  # 替代ignore
      - (*os.File).Close
      - (*database/sql.Rows).Close

# 🤖 AI代码生成专用linters配置
linters:
  disable-all: true
  enable:
    # 🚨 核心质量检查 (AI必须通过)
    - errcheck      # AI容易忽略错误处理
    - govet         # 静态分析，捕获AI逻辑错误
    - staticcheck   # 深度静态分析
    - typecheck     # 类型检查
    - gosimple      # 简化建议，AI代码常过于复杂
    - unused        # AI容易生成未使用代码
    - ineffassign   # AI容易生成无效赋值

    # 🚨 复杂度控制 (AI通病)
    - gocyclo       # 圈复杂度，AI容易生成复杂逻辑
    - funlen        # 函数长度，AI容易生成长函数
    - nakedret      # 裸返回检查
    # - gocognit      # 认知复杂度 (可能不兼容)
    # - cyclop        # 包复杂度 (可能不兼容)

    # 🚨 代码重复检查 (AI通病)
    - dupl          # AI容易复制粘贴代码
    - goconst       # AI容易硬编码常量

    # 🚨 代码质量和风格 (AI需要指导)
    - gocritic      # 全面代码质量检查
    - revive        # 代码风格，替代golint
    - stylecheck    # 风格检查
    - unconvert     # 不必要的类型转换

    # 🚨 格式化 (AI输出需要标准化)
    - gofmt         # 基础格式化
    - goimports     # import排序和格式化
    - whitespace    # 空白字符检查

    # 🚨 安全检查 (AI容易忽略安全)
    - gosec         # 安全漏洞检查

    # 🚨 性能优化 (AI不考虑性能)
    - prealloc      # 预分配检查
    - bodyclose     # HTTP body关闭

    # 🚨 命名和拼写 (AI命名不规范)
    - misspell      # 拼写检查

    # 🚨 参数和变量 (AI容易过度设计)
    - unparam       # 未使用参数
    - dogsled       # 过多空白标识符

    # 🚨 其他重要检查
    - lll           # 行长度控制
    - depguard      # 依赖管理

    # 🚨 2025年新增推荐linters (兼容性检查)
    # - errname       # 错误命名规范 (可能不兼容)
    # - nilnil        # nil检查 (可能不兼容)
    # - nolintlint    # nolint注释检查
    # - testpackage   # 测试包分离
    # - wastedassign  # 浪费的赋值
    # - contextcheck  # context使用检查

  # 🚨 AI代码专用排除规则
  exclude-rules:
    # 测试文件 - AI生成测试代码放宽要求
    - path: _test\.go
      linters:
        - funlen      # 测试函数可以更长
        - gocyclo     # 测试复杂度可以更高
        - gocognit    # 测试认知复杂度放宽
        - cyclop      # 测试包复杂度放宽
        - goconst     # 测试中允许重复常量
        - dupl        # 测试中允许重复代码
        - gosec       # 测试中放宽安全检查
        - errcheck    # 测试中某些错误可忽略
        - unparam     # 测试函数参数可能未使用

    # 命令行工具 - AI生成CLI代码放宽
    - path: cmd/
      linters:
        - goconst     # CLI中允许重复字符串
        - funlen      # main函数可能较长
        - gocyclo     # CLI逻辑可能复杂

    # 适配器代码 - AI生成适配器放宽
    - path: internal/adapter/
      linters:
        - goconst     # 适配器中允许重复常量
        - funlen      # 适配器函数可能较长
        - gocyclo     # 适配器逻辑复杂
        - dupl        # 适配器可能有相似代码

    # 模型定义 - AI生成模型放宽
    - path: internal/model/
      linters:
        - lll         # 模型定义可能有长行
        - unused      # 模型字段可能暂未使用
        - revive      # 模型命名可能特殊

    # 配置相关 - 暂时跳过
    - path: internal/config/
      linters:
        - goconst
        - funlen
        - gocyclo

    # 缓存相关 - 暂时跳过
    - path: internal/cache/
      linters:
        - goconst
        - funlen

    # 认证相关 - 暂时跳过
    - path: internal/auth/
      linters:
        - goconst
        - funlen

    # 🚨 AI常见误报排除
    - linters:
        - staticcheck
      text: "SA9003:" # 空分支，AI可能生成

    - linters:
        - govet
      text: 'shadow: declaration of "(err|ctx)" shadows declaration'

    - linters:
        - errcheck
      text: "Error return value of .*(Close|Flush).*is not checked"

    - linters:
        - revive
      text: "exported .* should have comment"

    - linters:
        - stylecheck
      text: "ST1000:" # 包注释

    - linters:
        - lll
      source: "^//go:generate"

    # 🚨 AI生成代码特殊处理
    - linters:
        - gocritic
      text: "commentedOutCode" # AI可能生成注释代码

    - linters:
        - unparam
      text: "parameter .* seems to be unused" # AI可能生成未使用参数

  # 🚨 全局排除 - AI常见问题
  exclude:
    - 'Error return value of .*\.Close` is not checked'
    - 'exported .* should have comment or be unexported'
    - 'comment on exported .* should be of the form'
    - 'package comment should be of the form'
    - 'should not use dot imports'
    - 'cyclomatic complexity .* of func .* is high'

  # 🚨 限制设置 - 适合AI代码审查
  max-issues-per-linter: 50   # 每个linter最多50个问题
  max-same-issues: 5          # 相同问题最多5个
  new: false                  # 检查所有代码，不只是新代码
  fix: false                  # 不自动修复，让AI学习

# 🚨 严重性设置 - AI代码分级处理
severity:
  default-severity: warning
  case-sensitive: false
  rules:
    # 🔴 错误级别 - AI必须修复
    - linters:
        - errcheck    # 错误处理必须正确
        - govet       # 静态分析错误必须修复
        - staticcheck # 静态检查错误必须修复
        - gosec       # 安全问题必须修复
        - typecheck   # 类型错误必须修复
      severity: error

    # 🟡 警告级别 - AI应该修复
    - linters:
        - funlen      # 函数长度警告
        - gocyclo     # 复杂度警告
        - gocognit    # 认知复杂度警告
        - cyclop      # 包复杂度警告
        - dupl        # 重复代码警告
        - gocritic    # 代码质量警告
        - revive      # 风格警告
      severity: warning

    # 🔵 信息级别 - AI可以忽略
    - linters:
        - goconst     # 常量提取建议
        - misspell    # 拼写错误
        - lll         # 行长度建议
        - whitespace  # 空白字符
        - unconvert   # 不必要转换
      severity: info
